stages:
  - scan
  - build
  - deploy
  - update_chart

blackduck check:
  stage: scan
  tags:
    - runner-1
  image: fis-gitlab-registry.fpt.com:5050/peoplex/devops/peoplex2024-devops-scanner-bd:latest
  script:
    - sh /home/<USER>
  only:
    - pre-staging
  when: manual

coverity check:
  stage: scan
  tags:
    - runner-1
  image: fis-gitlab-registry.fpt.com:5050/peoplex/devops/peoplex2024-devops-scanner-cov:latest
  # variables:
  #   DIR_TO_BUILD_FILE: /builds/hrdx/hrdx-fe
  script:
    - sh /home/<USER>
  only:
    - pre-staging
  when: manual

sonarqube-check-and-vulnerability-report:
  stage: scan
  tags:
    - runner-1
  image: fis-gitlab-registry.fpt.com:5050/peoplex/devops/peoplex2024-devops-scanner-sonarother:latest
  cache:
    key: "${CI_JOB_NAME}"
    paths:
      - .sonar/cache
  script:
    - source /home/<USER>
    - curl -u "${SONAR_TOKEN}:" "${SONAR_HOST_URL}/api/issues/gitlab_sast_export?projectKey=test&branch=${CI_COMMIT_BRANCH}&pullRequest=${CI_MERGE_REQUEST_IID}" -o gl-sast-sonar-report.json
  allow_failure: true
  artifacts:
    reports:
      sast: gl-sast-sonar-report.json
  rules:
    - if: $CI_COMMIT_BRANCH == "pre-staging"
      when: manual
    - if: $CI_COMMIT_BRANCH != "pre-staging"
      when: manual

build-docker auto:
  # image: rc.paas.ttgt.vn/public/nx-docker:20-alpine
  image: fis-gitlab-registry.fpt.com:5050/peoplex/devops/nx-docker:20-alpine
  stage: build
  needs: []
  tags:
    - runner-1
  services:
    # - rc.paas.ttgt.vn/public/docker:dind
    - fis-gitlab-registry.fpt.com:5050/peoplex/devops/docker:dind
  variables:
    # Docker config
    DOCKER_BUILDKIT: 1
    DOCKER_DRIVER: overlay2
    # Nx Container
    INPUT_PUSH: 'true' # To push your image to the registry
  before_script:
    - npm i
    - NX_HEAD=$CI_COMMIT_SHA
    - NX_BASE=${CI_MERGE_REQUEST_DIFF_BASE_SHA:-$CI_COMMIT_BEFORE_SHA}
    # Login to registry
    - echo "$CI_PB19_REGISTRY_PASSWORD" | docker login -u $CI_PB19_REGISTRY_USER --password-stdin $CI_PB19_REGISTRY
    - echo "$CI_REGISTRY_PASSWORD" | docker login -u $CI_REGISTRY_USER --password-stdin $CI_REGISTRY
    - echo "$FPT_CI_REGISTRY_PASSWORD" | docker login -u $FPT_CI_REGISTRY_USER --password-stdin $FPT_CI_REGISTRY
  script:
    - docker run --privileged --rm tonistiigi/binfmt --install all # required only for multi-platform build
    #- npx nx affected --base=$NX_BASE --head=$NX_HEAD --target=container --parallel=2
    - npx nx run-many --target=container --parallel=2 # force build all images for now, until we have a better way to detect changes
  only:
    - testing2
    - staging
    - staging2
    - staging3
    - staging4

build-docker:
  # image: rc.paas.ttgt.vn/public/nx-docker:20-alpine
  image: fis-gitlab-registry.fpt.com:5050/peoplex/devops/nx-docker:20-alpine
  stage: build
  needs: []
  tags:
    - runner-1
  services:
    # - rc.paas.ttgt.vn/public/docker:dind
    - fis-gitlab-registry.fpt.com:5050/peoplex/devops/docker:dind
  variables:
    # Docker config
    DOCKER_BUILDKIT: 1
    DOCKER_DRIVER: overlay2
    # Nx Container
    INPUT_PUSH: 'true' # To push your image to the registry
  before_script:
    - npm i
    - NX_HEAD=$CI_COMMIT_SHA
    - NX_BASE=${CI_MERGE_REQUEST_DIFF_BASE_SHA:-$CI_COMMIT_BEFORE_SHA}
    # Login to registry
    - echo "$CI_PB19_REGISTRY_PASSWORD" | docker login -u $CI_PB19_REGISTRY_USER --password-stdin $CI_PB19_REGISTRY
    - echo "$CI_REGISTRY_PASSWORD" | docker login -u $CI_REGISTRY_USER --password-stdin $CI_REGISTRY
    - echo "$FPT_CI_REGISTRY_PASSWORD" | docker login -u $FPT_CI_REGISTRY_USER --password-stdin $FPT_CI_REGISTRY
  script:
    - docker run --privileged --rm tonistiigi/binfmt --install all # required only for multi-platform build
    #- npx nx affected --base=$NX_BASE --head=$NX_HEAD --target=container --parallel=2
    - npx nx run-many --target=container --parallel=2 # force build all images for now, until we have a better way to detect changes
  only:
    - main
    - production
    - demo
    - testing
    - dev
  when: manual

deploy-demo:
  stage: deploy
  tags:
    - runner-1
  image: fis-gitlab-registry.fpt.com:5050/peoplex/devops/peoplex2024-devops-runner-worker:latest
  script: |
    curl -X POST https://manage.paas.ttgt.vn/api/stacks/webhooks/e5e7fb2a-8e60-40b0-8bc8-e95acd06183e
  only:
    - demo

# deploy-staging:
#   stage: deploy
#   tags:
#     - runner-1
#   image: fis-gitlab-registry.fpt.com:5050/peoplex/devops/peoplex2024-devops-runner-worker:latest
#   script: |
#     curl -X POST https://manage.paas.ttgt.vn/api/stacks/webhooks/fd8a6ca2-65bd-4b36-9d72-8a1ee8fd54c5
#   only:
#     - testing # Workaround. Revert ASAP

# deploy-dev:
#   stage: deploy
#   tags:
#     - runner-1
#   image: fis-gitlab-registry.fpt.com:5050/peoplex/devops/peoplex2024-devops-runner-worker:latest
#   script: |
#     curl -X POST https://manage.paas.ttgt.vn/api/stacks/webhooks/15e18c9b-490f-4ae6-8ece-1e835dda7862
#   only:
#     - dev

update_chart web dev:
  stage: update_chart
  needs: [build-docker]
  tags:
    - runner-1
  image: fis-gitlab-registry.fpt.com:5050/peoplex/devops/peoplex2024-devops-runner-worker:latest
  variables:
    PROCESS: web
    PORT: 8080 #Default is 80
    CPU: "0.1" #vCPU
    MEMORY: 256 #Mb
    POD_COUNT: 1
    EXTERNAL: true #Default is false
    DAPR_ENABLE: false #Default is false
    PROBE: true #Enable Liveness health check (Default is false)
    PROBE_PATH: / #Default is /healthz
    INITIAL_DELAY: 5 #Delay time in second before healthcheck (Default is 5 seconds)
  script: |
    sh /home/<USER>/deploy.sh
  only:
    - dev

update_chart web testing:
  stage: update_chart
  needs: [build-docker]
  tags:
    - runner-1
  image: fis-gitlab-registry.fpt.com:5050/peoplex/devops/peoplex2024-devops-runner-worker:latest
  variables:
    PROCESS: web
    PORT: 8080 #Default is 80
    CPU: "0.1" #vCPU
    MEMORY: 256 #Mb
    POD_COUNT: 1
    EXTERNAL: true #Default is false
    DAPR_ENABLE: false #Default is false
    PROBE: true #Enable Liveness health check (Default is false)
    PROBE_PATH: / #Default is /healthz
    INITIAL_DELAY: 5 #Delay time in second before healthcheck (Default is 5 seconds)
  script: |
    sh /home/<USER>/deploy.sh
  only:
    - testing

update_chart web testing2:
  stage: update_chart
  needs: [build-docker auto]
  tags:
    - runner-1
  image: fis-gitlab-registry.fpt.com:5050/peoplex/devops/peoplex2024-devops-runner-worker:latest
  variables:
    PROCESS: web
    PORT: 8080 #Default is 80
    CPU: "0.1" #vCPU
    MEMORY: 256 #Mb
    POD_COUNT: 1
    EXTERNAL: true #Default is false
    DAPR_ENABLE: false #Default is false
    PROBE: true #Enable Liveness health check (Default is false)
    PROBE_PATH: / #Default is /healthz
    INITIAL_DELAY: 5 #Delay time in second before healthcheck (Default is 5 seconds)
  script: |
    sh /home/<USER>/deploy.sh
  only:
    - testing2

update_chart web staging:
  stage: update_chart
  needs: [build-docker auto]
  tags:
    - runner-1
  image: fis-gitlab-registry.fpt.com:5050/peoplex/devops/peoplex2024-devops-runner-worker:latest
  variables:
    PROCESS: web
    PORT: 8080 #Default is 80
    CPU: "0.1" #vCPU
    MEMORY: 256 #Mb
    POD_COUNT: 2
    EXTERNAL: true #Default is false
    PROBE: true #Enable Liveness health check (Default is false)
    PROBE_PATH: / #Default is /healthz
    INITIAL_DELAY: 5 #Delay time in second before healthcheck (Default is 5 seconds)
  script: |
    sh /home/<USER>/deploy.sh
  only:
    - staging
  when: manual

update_chart web staging2:
  stage: update_chart
  needs: [build-docker auto]
  tags:
    - runner-1
  image: fis-gitlab-registry.fpt.com:5050/peoplex/devops/peoplex2024-devops-runner-worker:latest
  variables:
    PROCESS: web
    PORT: 8080 #Default is 80
    CPU: "0.1" #vCPU
    MEMORY: 256 #Mb
    POD_COUNT: 1
    EXTERNAL: true #Default is false
    LIVENESS_PATH: /
    INITIAL_DELAY: 5 #Delay time in second before healthcheck (Default is 5 seconds)
  script: |
    sh /home/<USER>/deploy.sh
  only:
    - staging2

update_chart web staging3:
  stage: update_chart
  needs: [build-docker auto]
  tags:
    - runner-1
  image: fis-gitlab-registry.fpt.com:5050/peoplex/devops/peoplex2024-devops-runner-worker:latest
  variables:
    PROCESS: web
    PORT: 8080 #Default is 80
    CPU: "0.1" #vCPU
    MEMORY: 256 #Mb
    POD_COUNT: 1
    EXTERNAL: true #Default is false
    LIVENESS_PATH: /
    INITIAL_DELAY: 5 #Delay time in second before healthcheck (Default is 5 seconds)
  script: |
    sh /home/<USER>/deploy.sh
  only:
    - staging3

update_chart web staging4:
  stage: update_chart
  needs: [build-docker auto]
  tags:
    - runner-1
  image: fis-gitlab-registry.fpt.com:5050/peoplex/devops/peoplex2024-devops-runner-worker:latest
  variables:
    PROCESS: web
    PORT: 8080 #Default is 80
    CPU: "0.1" #vCPU
    MEMORY: 256 #Mb
    POD_COUNT: 1
    EXTERNAL: true #Default is false
    LIVENESS_PATH: /
    INITIAL_DELAY: 5 #Delay time in second before healthcheck (Default is 5 seconds)
  script: |
    sh /home/<USER>/deploy.sh
  only:
    - staging4

update_chart web production:
  stage: update_chart
  tags:
    - runner-1
  image: fis-gitlab-registry.fpt.com:5050/peoplex/devops/peoplex2024-devops-runner-worker:latest
  variables:
    PROCESS: web
    PORT: 8080 #Default is 80
    CPU: "0.1" #vCPU
    MEMORY: 256 #Mb
    POD_COUNT: 0
    EXTERNAL: true #Default is false
    DAPR_ENABLE: false #Default is false
  script: |
    sh /home/<USER>/deploy.sh
  only:
    - production

update_chart bff dev:
  stage: update_chart
  needs: [build-docker]
  tags:
    - runner-1
  image: fis-gitlab-registry.fpt.com:5050/peoplex/devops/peoplex2024-devops-runner-worker:latest
  variables:
    PROCESS: bff
    PORT: 3000 #Default is 80
    CPU: "0.2" #vCPU
    MEMORY: 1024 #Mb
    POD_COUNT: 2
    EXTERNAL: true #Default is false
    DAPR_ENABLE: false #Default is false
    LIVENESS_PATH: /api/health
    READINESS_PATH: /api/health
    INITIAL_DELAY: 300 #Delay time in second before healthcheck (Default is 5 seconds)
  script: |
    sh /home/<USER>/deploy.sh
  only:
    - dev

update_chart bff testing:
  stage: update_chart
  needs: [build-docker]
  tags:
    - runner-1
  image: fis-gitlab-registry.fpt.com:5050/peoplex/devops/peoplex2024-devops-runner-worker:latest
  variables:
    PROCESS: bff
    PORT: 3000 #Default is 80
    CPU: "2" #vCPU
    MEMORY: 1024 #Mb
    POD_COUNT: 4
    EXTERNAL: true #Default is false
    DAPR_ENABLE: false #Default is false
    LIVENESS_PATH: /api/health
    READINESS_PATH: /api/health
    INITIAL_DELAY: 300 #Delay time in second before healthcheck (Default is 5 seconds)
  script: |
    sh /home/<USER>/deploy.sh
  only:
    - testing

update_chart bff testing2:
  stage: update_chart
  needs: [build-docker auto]
  tags:
    - runner-1
  image: fis-gitlab-registry.fpt.com:5050/peoplex/devops/peoplex2024-devops-runner-worker:latest
  variables:
    PROCESS: bff
    PORT: 3000 #Default is 80
    CPU: "2" #vCPU
    MEMORY: 1024 #Mb
    POD_COUNT: 1
    EXTERNAL: true #Default is false
    DAPR_ENABLE: false #Default is false
    LIVENESS_PATH: /api/health
    READINESS_PATH: /api/health
    INITIAL_DELAY: 300 #Delay time in second before healthcheck (Default is 5 seconds)
  script: |
    sh /home/<USER>/deploy.sh
  only:
    - testing2

update_chart bff staging:
  stage: update_chart
  needs: [build-docker auto]
  tags:
    - runner-1
  image: fis-gitlab-registry.fpt.com:5050/peoplex/devops/peoplex2024-devops-runner-worker:latest
  variables:
    PROCESS: bff
    PORT: 3000 #Default is 80
    CPU: "2" #vCPU
    MEMORY: 1024 #Mb
    POD_COUNT: 4
    EXTERNAL: true #Default is false
    LIVENESS_PATH: /api/health
    READINESS_PATH: /api/health
    INITIAL_DELAY: 30 #Delay time in second before healthcheck (Default is 5 seconds)
  script: |
    sh /home/<USER>/deploy.sh
  only:
    - staging
  when: manual

update_chart bff staging2:
  stage: update_chart
  needs: [build-docker auto]
  tags:
    - runner-1
  image: fis-gitlab-registry.fpt.com:5050/peoplex/devops/peoplex2024-devops-runner-worker:latest
  variables:
    PROCESS: bff
    PORT: 3000 #Default is 80
    CPU: "1.5" #vCPU
    MEMORY: 1024 #Mb
    POD_COUNT: 1
    EXTERNAL: true #Default is false
    LIVENESS_PATH: /api/health
    READINESS_PATH: /api/health
    INITIAL_DELAY: 300 #Delay time in second before healthcheck (Default is 5 seconds)
  script: |
    sh /home/<USER>/deploy.sh
  only:
    - staging2

update_chart bff staging3:
  stage: update_chart
  needs: [build-docker auto]
  tags:
    - runner-1
  image: fis-gitlab-registry.fpt.com:5050/peoplex/devops/peoplex2024-devops-runner-worker:latest
  variables:
    PROCESS: bff
    PORT: 3000 #Default is 80
    CPU: "1.5" #vCPU
    MEMORY: 1024 #Mb
    POD_COUNT: 1
    EXTERNAL: true #Default is false
    LIVENESS_PATH: /api/health
    READINESS_PATH: /api/health
    INITIAL_DELAY: 300 #Delay time in second before healthcheck (Default is 5 seconds)
  script: |
    sh /home/<USER>/deploy.sh
  only:
    - staging3

update_chart bff staging4:
  stage: update_chart
  needs: [build-docker auto]
  tags:
    - runner-1
  image: fis-gitlab-registry.fpt.com:5050/peoplex/devops/peoplex2024-devops-runner-worker:latest
  variables:
    PROCESS: bff
    PORT: 3000 #Default is 80
    CPU: "1.5" #vCPU
    MEMORY: 1024 #Mb
    POD_COUNT: 1
    EXTERNAL: true #Default is false
    LIVENESS_PATH: /api/health
    READINESS_PATH: /api/health
    INITIAL_DELAY: 300 #Delay time in second before healthcheck (Default is 5 seconds)
  script: |
    sh /home/<USER>/deploy.sh
  only:
    - staging4

update_chart bff production:
  stage: update_chart
  tags:
    - runner-1
  image: fis-gitlab-registry.fpt.com:5050/peoplex/devops/peoplex2024-devops-runner-worker:latest
  variables:
    PROCESS: bff
    PORT: 3000 #Default is 80
    CPU: "1" #vCPU
    MEMORY: 1024 #Mb
    POD_COUNT: 0
    EXTERNAL: true #Default is false
    DAPR_ENABLE: false #Default is false
  script: |
    sh /home/<USER>/deploy.sh
  only:
    - production
