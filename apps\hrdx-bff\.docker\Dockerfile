# Install dependencies only when needed
FROM docker.io/node:lts-alpine as deps
# Check https://github.com/nodejs/docker-node/tree/b4117f9333da4138b03a546ec926ef50a31506c3#nodealpine to understand why libc6-compat might be needed.
RUN apk add --no-cache libc6-compat
WORKDIR /usr/src/app

COPY dist/apps/hrdx-bff/package*.json ./
COPY .npmrc ./

# clone override for build fix security issue
COPY package.json ./package-template.json
COPY npm-copy-override.js ./

RUN node npm-copy-override.js
#

RUN npm install --only=production --force --omit=dev
RUN npm install --force blueimp-md5


# Production image, copy all the files and run nest
FROM docker.io/node:lts-alpine as runner
RUN apk add --no-cache dumb-init
ENV NODE_ENV production
ENV PORT 3000

ENV APP_CACHE_STORE memory
ENV APP_CACHE_TTL 10000
ENV APP_CACHE_MAX 200

WORKDIR /usr/src/app
COPY --from=deps /usr/src/app/node_modules ./node_modules
COPY --from=deps /usr/src/app/package.json ./package.json
COPY dist/apps/hrdx-bff .
# RUN chown -R node:node .
# USER node
EXPOSE 3000
CMD ["dumb-init", "node", "main.js"]

ENV APP_SERVICE=
ENV APP_NAME=
ENV APP_TENANT=
ENV APP_PREFIX=

ENV UPSTREAM_URL=

ENV OIDC_CLIENT_ID=
ENV OIDC_JWKS_URI=
ENV OIDC_ISSUER=

ENV MASTERDATA_HOST=
ENV MASTERDATA_TOKEN=

ENV APP_CORS_ORIGIN=

ENV OPENTELEMETRY_EXPORTER_OTLP_URL=
ENV OPENTELEMETRY_EXPORTER_JAEGER_URL=

# To enable security middleware
ENV ENABLE_SECURITY_MIDDLEWARE=

