{"name": "hrdx-bff", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/hrdx-bff/src", "projectType": "application", "tags": [], "targets": {"build": {"executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"target": "node", "compiler": "tsc", "outputPath": "dist/apps/hrdx-bff", "main": "apps/hrdx-bff/src/main.ts", "tsConfig": "apps/hrdx-bff/tsconfig.app.json", "assets": ["apps/hrdx-bff/src/assets", "apps/hrdx-bff/src/metadata", "apps/hrdx-bff/src/bootstraps", "apps/hrdx-bff/src/configs"], "isolatedConfig": true, "webpackConfig": "apps/hrdx-bff/webpack.config.js", "generatePackageJson": true}, "configurations": {"development": {}, "production": {}}}, "serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "options": {"buildTarget": "hrdx-bff:build"}, "configurations": {"development": {"buildTarget": "hrdx-bff:build:development"}, "production": {"buildTarget": "hrdx-bff:build:production"}}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/hrdx-bff/jest.config.ts"}}, "container": {"executor": "@nx-tools/nx-container:build", "dependsOn": ["build"], "options": {"push": true, "pull": true, "engine": "docker", "file": "apps/hrdx-bff/.docker/Dockerfile", "metadata": {"images": ["rc.paas.ttgt.vn/hrdx/hrdx-bff", "$CI_REGISTRY/$REGISTRY_PATH/$CI_PROJECT_PATH_SLUG-bff", "$FPT_CI_REGISTRY/$FPT_REGISTRY_PATH/$CI_PROJECT_PATH_SLUG-bff"], "load": true, "tags": ["$CI_PIPELINE_ID", "latest", "type=schedule", "type=ref,event=branch", "type=ref,event=tag", "type=ref,event=pr"]}}}}}