// always on top
import { DynamicCrudBackendModule } from './dynamic-crud/dynamic-crud.module';

import { PeoplexOpenTelemetryModule } from '@peoplex/open-telemetry';

//

import { BackendCommonModule } from '@hrdx-bff/backend-common';
import { Module, NestModule, MiddlewareConsumer } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { HealthController } from './health.controller';
import { MetadataModule } from './metadata/metadata.module';
import { Request, Response, NextFunction } from 'express';
import { decodeObject } from '../../../../libs/shared/src/lib/keyword-mapping';
import { SecurityMiddleware } from './security.middleware';


// a middleware to decode the query
export class DecodeQueryMiddleware {
  use(req: Request, res: Response, next: NextFunction) {
    req.query = decodeObject(req.query);
    next();
  }
}

@Module({
  imports: [
    BackendCommonModule,
    DynamicCrudBackendModule.forRoot(),
    MetadataModule,
    PeoplexOpenTelemetryModule.forRoot([AppModule]),
  ],
  controllers: [HealthController, AppController],
  providers: [AppService],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    const enableSecurity = process.env.ENABLE_SECURITY_MIDDLEWARE === 'true';
    if (enableSecurity) {
      consumer.apply(DecodeQueryMiddleware, SecurityMiddleware).forRoutes('*');
    } else {
      consumer.apply(DecodeQueryMiddleware).forRoutes('*');
    }
  }
}
