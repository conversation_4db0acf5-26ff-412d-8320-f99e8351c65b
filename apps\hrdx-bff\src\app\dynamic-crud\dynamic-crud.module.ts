import {
  DynamicConfig<PERSON>rud,
  DynamicCrudDelete,
  DynamicCrudDeleteById,
  DynamicCrudDownloadFile,
  DynamicCrudGet,
  DynamicCrudGetById,
  DynamicCrudPatch,
  DynamicCrudPatchById,
  DynamicCrudPost,
  DynamicCrudPostById,
  DynamicCrudPostFormData,
  DynamicCrudService,
  safeAny,
  SmcCrudConfigModule,
} from '@hrdx-bff/nestjs-dynamic-crud';

import { Response } from 'express';

import { Cache, CACHE_MANAGER, CacheModule } from '@nestjs/cache-manager';

import { Span, TraceService } from '@metinseylan/nestjs-opentelemetry';
import {
  Body,
  DynamicModule,
  Get,
  Inject,
  Injectable,
  Logger,
  Module,
  Req,
  Res,
} from '@nestjs/common';
import { CrudRequest, Override, ParsedRequest } from '@nestjsx/crud';
import { Boot } from '@smc/nestcloud-boot';
import { BOOT } from '@smc/nestcloud-common';
import fs from 'fs';
import { MemoryStoredFile, NestjsFormDataModule } from 'nestjs-form-data';
import path from 'path';

@Module({})
export class DynamicCrudBackendModule {
  static forRoot(): DynamicModule {
    const services = (process.env['APP_SERVICE'] ?? '')
      .replace(/['"]/g, '')
      .split(',');

    const route = this.getServices(services);
    const modules = this.getModules(route);
    for (const module of modules) {
      const name = module.name
        .split('-')
        .map((c) => c.charAt(0).toUpperCase() + c.slice(1))
        .join('-');

      Object.defineProperty(module.controller, 'name', {
        value: `${name}-Controller`,
      });
      Object.defineProperty(module.service, 'name', {
        value: `${name}-Service`,
      });
    }

    return {
      module: DynamicCrudBackendModule,
      providers: [
        ...modules.map((c) => {
          return c.service;
        }),
      ],
      exports: [
        ...modules.map((c) => {
          return c.service;
        }),
      ],
      imports: [
        ...modules.map((c) => {
          return c.import;
        }),
        NestjsFormDataModule.config({ storage: MemoryStoredFile }),
        CacheModule.registerAsync({
          inject: [BOOT],
          useFactory: async (boot: Boot) => {
            const storeCache = boot.get('cache.store', 'memory');
            const ttlCache = boot.get('cache.ttl', 500);
            const maxCache = boot.get('cache.max', 200);
            return {
              store: storeCache,
              ttl: +ttlCache,
              max: +maxCache,
            };
          },
        }),
      ],
      controllers: [
        ...modules.map((c) => {
          return c.controller;
        }),
      ],
    };
  }

  private static getServices(services: string[]) {
    const defaultPath = path.resolve(__dirname, 'configs');
    const route: {
      name: string;
      pathFile: string;
    }[] = [];

    for (const service of services) {
      if (service.endsWith(':')) {
        const pathFolder = path.resolve(defaultPath, service.replace(':', ''));

        const files = fs
          .readdirSync(pathFolder)
          .filter((f) => f.endsWith('.yaml'));

        for (const file of files) {
          const serviceName = file.split('.')[1];
          route.push({
            name: serviceName,
            pathFile: path.resolve(pathFolder, file),
          });
        }
      } else if (service.includes('/')) {
        const split = service.split('/');
        const pathFolder = path.resolve(
          defaultPath,
          ...split.slice(0, split.length - 1),
        );
        const serviceName = split[split.length - 1];

        route.push({
          name: serviceName,
          pathFile: path.resolve(pathFolder, `crud.${serviceName}.yaml`),
        });
      } else {
        route.push({
          name: service,
          pathFile: path.resolve(defaultPath, `crud.${service}.yaml`),
        });
      }
    }

    return route;
  }

  private static setForwardedHeader(request: CrudRequest, rawRequest: Request) {
    const clientIPAddress =
      (rawRequest.headers as any)?.['x-forwarded-for'] ??
      (rawRequest as any).ip ??
      (rawRequest as any).connection?.remoteAddress;

    if (clientIPAddress) {
      request.parsed.authPersist.headers['x-forwarded-for'] =
        clientIPAddress;
    }
  }

  private static getModules(
    routes: {
      name: string;
      pathFile: string;
    }[],
  ) {
    const modules = [];

    for (const r of routes) {
      Logger.debug(`File path: ${r.pathFile}`, 'DynamicCrudBackendModule');
      if (!fs.existsSync(r.pathFile)) {
        Logger.error(
          `File not found: ${r.pathFile}`,
          'DynamicCrudBackendModule',
        );
        continue;
      }

      /// Service
      @Injectable()
      class DynamicCrudBackendService extends DynamicCrudService {
        constructor(
          @Inject(CACHE_MANAGER)
          public readonly cacheManager: Cache,
          public readonly traceService: TraceService,
        ) {
          super(r.pathFile, cacheManager, traceService);
        }
      }
      ///

      /// Controller
      @DynamicConfigCrud({})
      class DynamicCrudController {
        constructor(
          private readonly service: DynamicCrudBackendService,
          public readonly traceService: TraceService,
        ) {}

        @Get('/_config')
        getConfigs() {
          return this.service.getConfigs();
        }

        @Span()
        @Override()
        getMany(@ParsedRequest() req: CrudRequest, @Req() rawReq: Request) {
          const rq = req.parsed.authPersist.request;
          this.traceService.getSpan().updateName(`${rq.method} ${rq.url}`);
          DynamicCrudBackendModule.setForwardedHeader(req, rawReq);
          return this.service.getMany(req);
        }

        @Span()
        @Override()
        createOne(
          @ParsedRequest() req: CrudRequest,
          @Body() body: safeAny,
          @Req() rawReq: Request,
        ) {
          const rq = req.parsed.authPersist.request;
          this.traceService.getSpan().updateName(`${rq.method} ${rq.url}`);
          DynamicCrudBackendModule.setForwardedHeader(req, rawReq);
          return this.service.createOne(req, body);
        }

        @Span()
        @DynamicCrudGet()
        customGet(@ParsedRequest() req: CrudRequest, @Req() rq: Request) {
          this.traceService.getSpan().updateName(`${rq.method} ${rq.url}`);
          DynamicCrudBackendModule.setForwardedHeader(req, rq);
          return this.service.customGet(req);
        }

        @Span()
        @DynamicCrudPatch()
        customPatch(
          @ParsedRequest() req: CrudRequest,
          @Req() rq: Request,
          @Body() body: safeAny,
        ) {
          this.traceService.getSpan().updateName(`${rq.method} ${rq.url}`);
          DynamicCrudBackendModule.setForwardedHeader(req, rq);
          return this.service.customPatch(req, body);
        }

        @Span()
        @DynamicCrudDelete()
        customDelete(
          @ParsedRequest() req: CrudRequest,
          @Req() rq: Request,
          @Body() body: safeAny,
        ) {
          this.traceService.getSpan().updateName(`${rq.method} ${rq.url}`);
          DynamicCrudBackendModule.setForwardedHeader(req, rq);
          return this.service.customDelete(req, body);
        }

        @Span()
        @DynamicCrudPost()
        customPost(
          @ParsedRequest() req: CrudRequest,
          @Req() rq: Request,
          @Body() body: safeAny,
        ) {
          this.traceService.getSpan().updateName(`${rq.method} ${rq.url}`);
          DynamicCrudBackendModule.setForwardedHeader(req, rq);
          return this.service.customPost(req, body);
        }

        @Span()
        @DynamicCrudDownloadFile()
        async dynamicCrudDownloadFile(
          @ParsedRequest() req: CrudRequest,
          @Body() body: safeAny,
          @Req() rq: Request,
          @Res() res: Response,
        ) {
          this.traceService.getSpan().updateName(`${rq.method} ${rq.url}`);
          DynamicCrudBackendModule.setForwardedHeader(req, rq);
          const rs = await this.service.customDownloadFile(req, body);
          res.setHeader('Content-Type', rs.headers['content-type']);
          res.setHeader(
            'Content-Disposition',
            rs.headers['content-disposition'],
          );
          res.setHeader('Content-Length', rs.headers['content-length']);
          res.send(rs.data);
          return;
        }

        @Span()
        @DynamicCrudPostFormData()
        customPostFile(
          @ParsedRequest() req: CrudRequest,
          @Req() rq: Request,
          @Body() body: safeAny,
        ) {
          this.traceService.getSpan().updateName(`${rq.method} ${rq.url}`);
          DynamicCrudBackendModule.setForwardedHeader(req, rq);
          return this.service.uploadFile(req, body);
        }

        @Span()
        @Override()
        getOne(@ParsedRequest() req: CrudRequest) {
          const rq = req.parsed.authPersist.request;
          this.traceService.getSpan().updateName(`${rq.method} ${rq.url}`);
          DynamicCrudBackendModule.setForwardedHeader(req, rq);
          return this.service.getOne(req);
        }

        @Span()
        @DynamicCrudGetById()
        customGetById(@ParsedRequest() req: CrudRequest, @Req() rq: Request) {
          this.traceService.getSpan().updateName(`${rq.method} ${rq.url}`);
          DynamicCrudBackendModule.setForwardedHeader(req, rq);
          return this.service.customGet(req);
        }

        @Span()
        @Override()
        updateOne(@ParsedRequest() req: CrudRequest, @Body() body: safeAny) {
          const rq = req.parsed.authPersist.request;
          this.traceService.getSpan().updateName(`${rq.method} ${rq.url}`);
          DynamicCrudBackendModule.setForwardedHeader(req, rq);
          return this.service.updateOne(req, body);
        }

        @Span()
        @DynamicCrudPatchById()
        customPatchById(
          @ParsedRequest() req: CrudRequest,
          @Body() body: safeAny,
          @Req() rq: Request,
        ) {
          this.traceService.getSpan().updateName(`${rq.method} ${rq.url}`);
          DynamicCrudBackendModule.setForwardedHeader(req, rq);
          return this.service.customPatch(req, body);
        }

        @Span()
        @Override()
        deleteOne(@ParsedRequest() req: CrudRequest) {
          const rq = req.parsed.authPersist.request;

          this.traceService.getSpan().updateName(`${rq.method} ${rq.url}`);
          DynamicCrudBackendModule.setForwardedHeader(req, rq);
          return this.service.deleteOne(req);
        }

        @Span()
        @DynamicCrudDeleteById()
        customDeleteById(
          @ParsedRequest() req: CrudRequest,
          @Body() body: safeAny,
          @Req() rq: Request,
        ) {
          this.traceService.getSpan().updateName(`${rq.method} ${rq.url}`);
          DynamicCrudBackendModule.setForwardedHeader(req, rq);
          return this.service.customDelete(req, body);
        }

        @Span()
        @DynamicCrudPostById()
        customPostById(
          @ParsedRequest() req: CrudRequest,
          @Body() body: safeAny,
          @Req() rq: Request,
        ) {
          this.traceService.getSpan().updateName(`${rq.method} ${rq.url}`);
          DynamicCrudBackendModule.setForwardedHeader(req, rq);
          return this.service.customPost(req, body);
        }
      }

      modules.push({
        controller: DynamicCrudController,
        service: DynamicCrudBackendService,
        name: r.name,
        import: SmcCrudConfigModule.forRoot(
          [DynamicCrudController],
          r.pathFile,
          r.name,
        ),
      });
    }

    return modules;
  }
}
