import { Request, Response, NextFunction } from 'express';

/**
 * Security Middleware for Input Validation and SQL Injection Prevention
 * 
 * This middleware supports Unicode characters and non-English text while protecting against:
 * - SQL injection attacks
 * - XSS attempts through malicious comments
 * - Dangerous control characters
 * - Overly deep nested objects
 * 
 * Unicode Support:
 * - Allows all Unicode letters (\p{L}) and numbers (\p{N})
 * - Supports non-English characters in business names and text fields
 * - Preserves legitimate Unicode content while filtering malicious patterns
 */

// Configuration object for easy maintenance
const CONFIG = {
  MAX_STRING_LENGTH: 10000,
  MAX_DEPTH: 10,
  ALLOWED_KEYS: [
    'fields', 'select', 's', 'filter', 'or', 'join', 'sort',
    'per_page', 'limit', 'offset', 'page', 'cache'
  ],
  ALLOWED_OPERATORS: [
    '$eq', '$ne', '$gt', '$lt', '$gte', '$lte',
    '$starts', '$ends', '$cont', '$excl',
    '$in', '$notin', '$isnull', '$notnull', '$between',
    '$eqL', '$neL', '$startsL', '$endsL', '$contL', '$exclL', '$inL', '$notinL',
    '$or', '$and'
  ]
};

// Pre-compiled regex patterns for better performance
const PATTERNS = {
  // SQL injection patterns
  SQL_COMMENT_LINE: /(?:--|#)/,
  SQL_COMMENT_BLOCK: /\/\*[\s\S]*?\*\//,
  QUOTE_EQUALITY: /(['"]).*?\1\s*=\s*\1.*?\1/,
  BOOLEAN_LOGIC: /\b(?:or|and)\b\s+[^\s]+\s*=\s*[^\s]+/i,
  ALWAYS_TRUE: /\b1\s*=\s*1\b/i,
  UNION_SELECT: /\bunion\s+(?:all\s+)?select\b/i,
  STACKED_QUERY: /;\s*(?:drop|select|insert|delete|update|create|alter|exec|union|sleep)\b/i,
  
  // Character patterns - only remove actual control characters, not Unicode
  NULL_BYTES: /[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g,
  UNICODE_ESCAPE: /\\u([0-9a-fA-F]{4})/g,
  HEX_ESCAPE: /\\x([0-9a-fA-F]{2})/g,
  
  // Validation patterns
  NUMERIC_ID: /^\d+$/,
  UUID: /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i,
  // Updated to support Unicode letters and numbers, not just ASCII
  BUSINESS_LIST: /^[\p{L}\p{N}\s\(\)\[\]_.-]+(?:[;,][\p{L}\p{N}\s\(\)\[\]_.-]+)*$/u,
  
  // Encoding detection
  URL_ENCODED: /%[0-9a-fA-F]{2}/,
  DOUBLE_ENCODED: /%25[0-9a-fA-F]{2}/,
  
  // Malicious block comment patterns - more specific
  MALICIOUS_BLOCK_COMMENT: /\/\*[\s\S]*?\*\/\s*(?:;|$|--|#|\/\*)/,
  SQL_COMMENT_AFTER_QUOTE: /['"`]\s*(?:--|#|\/\*)/,
  SQL_COMMENT_AFTER_SEMICOLON: /;\s*(?:--|#|\/\*)/,
};

// SQL keywords that are dangerous in certain contexts
const DANGEROUS_KEYWORDS = new Set([
  'select', 'insert', 'update', 'delete', 'drop', 'create', 'alter',
  'exec', 'execute', 'union', 'sleep', 'waitfor', 'benchmark',
  'pg_sleep', 'delay', 'information_schema', 'sys', 'master'
]);

interface ValidationResult {
  isValid: boolean;
  sanitizedValue?: any;
  reason?: string;
}

interface SecurityContext {
  depth: number;
  parentKey?: string;
  allowedKeys: Set<string>;
  allowedOperators: Set<string>;
}

class SecurityValidator {
  private readonly allowedKeys: Set<string>;
  private readonly allowedOperators: Set<string>;

  constructor() {
    this.allowedKeys = new Set(CONFIG.ALLOWED_KEYS);
    this.allowedOperators = new Set(CONFIG.ALLOWED_OPERATORS);
  }

  /**
   * Normalize and decode string input
   */
  private normalizeString(input: string): string[] {
    const variants: string[] = [input];
    
    // Remove null bytes and control characters
    const cleanInput = input.replace(PATTERNS.NULL_BYTES, '');
    if (cleanInput !== input) {
      variants.push(cleanInput);
    }

    // URL decode (single pass only to avoid decode bombs)
    if (PATTERNS.URL_ENCODED.test(input)) {
      try {
        const decoded = decodeURIComponent(input);
        variants.push(decoded);
      } catch (e) {
        // Invalid URL encoding - suspicious
        return [input];
      }
    }

    // Unicode decode
    if (PATTERNS.UNICODE_ESCAPE.test(input)) {
      const unicodeDecoded = input.replace(PATTERNS.UNICODE_ESCAPE, (_, hex) => 
        String.fromCharCode(parseInt(hex, 16))
      );
      variants.push(unicodeDecoded);
    }

    // Hex decode
    if (PATTERNS.HEX_ESCAPE.test(input)) {
      const hexDecoded = input.replace(PATTERNS.HEX_ESCAPE, (_, hex) => 
        String.fromCharCode(parseInt(hex, 16))
      );
      variants.push(hexDecoded);
    }

    // Remove duplicates
    return [...new Set(variants)];
  }

  /**
   * Check if string contains SQL injection patterns
   * Note: This method is Unicode-aware and should not flag legitimate non-English text
   */
  private containsSqlInjection(input: string): boolean {
    const normalized = input.toLowerCase().trim();
    
    // Check for SQL comments that are likely malicious
    if (PATTERNS.SQL_COMMENT_LINE.test(normalized) || PATTERNS.SQL_COMMENT_BLOCK.test(normalized)) {
      if (this.isLikelySqlComment(normalized)) {
        return true;
      }
    }

    // Check other patterns - these should be safe with Unicode as they look for specific SQL syntax
    return PATTERNS.QUOTE_EQUALITY.test(normalized) ||
           PATTERNS.BOOLEAN_LOGIC.test(normalized) ||
           PATTERNS.ALWAYS_TRUE.test(normalized) ||
           PATTERNS.UNION_SELECT.test(normalized) ||
           PATTERNS.STACKED_QUERY.test(normalized);
  }

  /**
   * Improved SQL comment detection - more precise to avoid false positives
   */
  private isLikelySqlComment(input: string): boolean {
    // Check for SQL comment patterns that are likely malicious
    const suspicious = [
      /^\s*--/,                    // Comment at start
      /--\s*$/,                    // Comment at end  
      /'\s*--/,                    // Comment after quote
      /;\s*--/,                    // Comment after semicolon
      /^\s*#[^a-zA-Z0-9]/,        // Hash comment at start (but not hashtag)
      /#\s*$/,                     // Hash comment at end
      /'\s*#/,                     // Hash comment after quote
      /;\s*#/,                     // Hash comment after semicolon
    ];

    // For block comments, be more restrictive
    if (PATTERNS.SQL_COMMENT_BLOCK.test(input)) {
      // Allow block comments only if they are:
      // 1. The entire string is just a comment with normal text
      // 2. Not mixed with other content that could be SQL
      
      // Block if comment is mixed with other content (potential SQL injection)
      if (!/^\s*\/\*[\s\S]*?\*\/\s*$/.test(input)) {
        return true; // Block comments that aren't standalone
      }
      
      // Block if the comment content itself looks suspicious
      const commentContent = input.match(/\/\*([\s\S]*?)\*\//)?.[1] || '';
      if (commentContent.match(/\b(select|insert|update|delete|drop|union|exec)\b/i)) {
        return true;
      }
      
      // Block if followed by suspicious patterns
      if (PATTERNS.SQL_COMMENT_AFTER_QUOTE.test(input) || 
          PATTERNS.SQL_COMMENT_AFTER_SEMICOLON.test(input) ||
          /\/\*[\s\S]*?\*\/\s*(?:;|--|#)/.test(input) ||
          /;\s*\/\*/.test(input) ||
          /'\s*\/\*/.test(input)) {
        return true;
      }
      
      // If it's just a standalone comment with normal text, allow it
      return false;
    }

    return suspicious.some(pattern => pattern.test(input));
  }

  /**
   * Validate ID fields - should be strict about format but still check for injection
   */
  private validateIdField(value: string): boolean {
    return PATTERNS.NUMERIC_ID.test(value) || PATTERNS.UUID.test(value);
  }

  /**
   * Check if string is a safe business list
   */
  private isSafeBusinessList(input: string): boolean {
    // Allow business lists with parentheses, brackets, common punctuation, and Unicode characters
    if (PATTERNS.BUSINESS_LIST.test(input)) {
      // Don't block if it's a legitimate business list format
      const semicolonParts = input.split(';');
      
      // If it starts with semicolon, it's likely malicious
      if (input.startsWith(';')) {
        return false;
      }
      
      // If it contains obvious SQL injection patterns, block it
      if (semicolonParts.length > 1) {
        const afterSemicolon = semicolonParts.slice(1).join(';').trim().toLowerCase();
        
        // Only check if the text after semicolon starts with a dangerous SQL keyword
        // This is more lenient for Unicode text that might coincidentally contain these words
        const firstWord = afterSemicolon.split(/\s+/)[0];
        if (DANGEROUS_KEYWORDS.has(firstWord)) {
          const restOfString = afterSemicolon.slice(firstWord.length).trim();
          
          // If it looks like "DROP TABLE" or "SELECT *" etc., block it
          // But be more careful with Unicode - only block if it really looks like SQL syntax
          if (restOfString.match(/^(table|from|into|\*|where|database|schema)\b/i) ||
              restOfString.match(/^\s*[*\w\s,()]+\s+from\s+/i)) {
            return false;
          }
        }
      }
      
      return true;
    }
    return false;
  }

  /**
   * Validate string input
   */
  private validateString(input: string, context: SecurityContext): ValidationResult {
    // Length check - temporarily skip validation for long strings
    if (input.length > CONFIG.MAX_STRING_LENGTH) {
      return { isValid: true, sanitizedValue: input };
    }

    // Check if it's a safe business list first
    if (this.isSafeBusinessList(input)) {
      return { isValid: true, sanitizedValue: input };
    }

    // Normalize and check all variants
    const variants = this.normalizeString(input);
    
    for (const variant of variants) {
      if (this.containsSqlInjection(variant)) {
        return { isValid: false, reason: 'Potentially malicious pattern detected' };
      }
    }

    return { isValid: true, sanitizedValue: input };
  }

  /**
   * Validate and sanitize any input
   */
  public validate(input: any, context: SecurityContext): ValidationResult {
    // Depth check to prevent stack overflow - temporarily skip validation for deep objects
    if (context.depth > CONFIG.MAX_DEPTH) {
      return { isValid: true, sanitizedValue: input };
    }

    if (typeof input === 'string') {
      return this.validateString(input, context);
    }

    if (typeof input === 'number' || typeof input === 'boolean') {
      return { isValid: true, sanitizedValue: input };
    }

    if (input === null || input === undefined) {
      return { isValid: true, sanitizedValue: input };
    }

    if (Array.isArray(input)) {
      const sanitizedArray: any[] = [];
      for (const item of input) {
        const result = this.validate(item, { ...context, depth: context.depth + 1 });
        if (!result.isValid) {
          return result;
        }
        sanitizedArray.push(result.sanitizedValue);
      }
      return { isValid: true, sanitizedValue: sanitizedArray };
    }

    if (typeof input === 'object') {
      const sanitizedObject: any = {};
      for (const [key, value] of Object.entries(input)) {
        // Skip validation for allowed keys/operators at root level only
        // BUT still validate their values if they're strings
        const shouldSkipKeyValidation = context.depth === 0 && 
          (context.allowedKeys.has(key) || context.allowedOperators.has(key));
        
        if (shouldSkipKeyValidation) {
          // Still validate the value, but don't treat it as strictly
          if (typeof value === 'string') {
            const relaxedContext = { ...context, depth: context.depth + 1, parentKey: key };
            // For allowed keys, we're more permissive but still check for obvious injection
            const variants = this.normalizeString(value);
            let hasInjection = false;
            
            for (const variant of variants) {
              // Only check for the most obvious injection patterns
              if (PATTERNS.STACKED_QUERY.test(variant.toLowerCase()) || 
                  PATTERNS.UNION_SELECT.test(variant.toLowerCase()) ||
                  (PATTERNS.ALWAYS_TRUE.test(variant.toLowerCase()) && /\bor\b/i.test(variant))) {
                hasInjection = true;
                break;
              }
            }
            
            if (hasInjection) {
              return { isValid: false, reason: 'Potentially malicious pattern detected' };
            }
          }
          sanitizedObject[key] = value;
          continue;
        }

        const result = this.validate(value, {
          ...context,
          depth: context.depth + 1,
          parentKey: key
        });
        
        if (!result.isValid) {
          return result;
        }
        
        sanitizedObject[key] = result.sanitizedValue;
      }
      return { isValid: true, sanitizedValue: sanitizedObject };
    }

    return { isValid: false, reason: 'Unsupported data type' };
  }
}

// Create singleton instance
const validator = new SecurityValidator();

/**
 * Security middleware for Express
 */
export function SecurityMiddleware(req: Request, res: Response, next: NextFunction): void {
  const context: SecurityContext = {
    depth: 0,
    allowedKeys: new Set(CONFIG.ALLOWED_KEYS),
    allowedOperators: new Set(CONFIG.ALLOWED_OPERATORS)
  };

  // Validate query parameters
  if (req.query && Object.keys(req.query).length > 0) {
    const queryResult = validator.validate(req.query, context);
    if (!queryResult.isValid) {
      res.status(400).json({ 
        message: 'Some special characters are not allowed.',
        details: queryResult.reason 
      });
      return;
    }
    req.query = queryResult.sanitizedValue;
  }

  // Validate request body
  if (req.body && Object.keys(req.body).length > 0) {
    const bodyResult = validator.validate(req.body, context);
    if (!bodyResult.isValid) {
      res.status(400).json({ 
        message: 'Some special characters are not allowed.',
        details: bodyResult.reason 
      });
      return;
    }
    req.body = bodyResult.sanitizedValue;
  }

  next();
}

// Export for testing
export { SecurityValidator, PATTERNS, CONFIG };