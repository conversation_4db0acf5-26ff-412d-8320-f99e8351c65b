controller: bands
upstream: ${{UPSTREAM_FO_URL}}
upstreamPath: bands

models:
  - name: _
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: count
    config:
      id:
        from: id
        type: string
      # name BFF
      code:
        # name BE
        from: code
        typeOptions:
          func: upperCase
        # type BE
        type: string
      effectiveDate:
        from: effectiveDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      status:
        from: enabled
        type: string
        typeOptions:
          func: YNToBoolean
      shortName:
        from: shortName
        type: string
        typeOptions:
          func: stringToMultiLang
      na_longName:
        from: na_longName
      na_name:
        from: na_name
      na_shortName:
        from: na_shortName
      longName:
        from: name
        type: string
        typeOptions:
          func: stringToMultiLang
      name:
        from: name
        type: string
        typeOptions:
          func: stringToMultiLang
      description:
        from: note
        type: string
        typeOptions:
          func: stringToMultiLang
      parentCareerStream:
        from: careerStreamName
      careerStreamCode:
        from: careerStreamCode
      careerStreamId:
        from: careerStreamId
      careerStreamObj:
        from: $
        objectChildren:
          id:
            from: careerStreamId
          code:
            from: careerStreamCode
      file:
        from: file
      createdBy:
        from: createdBy
        type: string
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
        type: string
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
  - name: _DELETE
    config:
      codes:
        from: codes
      ids:
        from: ids
auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: bands
crudConfig:
  query:
    sort:
      - field: code
        order: asc
  params:
    id:
      field: id
      type: string
      primary: true
    code:
      field: code
      type: string
  routes:
    exclude:
      - createManyBase
      - replaceOneBase
defaultQuery:

routes:
  - path: /api/bands
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: "bands"
      query:
        Page: ":{options.page}:"
        PageSize: ":{options.limit}:"
        OrderBy: ":{options.sort}:"
        Search: "::{search}::"
        Filter: "::{filter}::"
      transform: '$ ~> | $.data | {"parentCareerStream": $exists($.careerStreamId) ? $.parentCareerStream & " ("  & $.careerStreamCode & ")" : ""} |'

  - path: /api/bands/:id
    method: GET
    model: _

    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: "bands/:{id}:"
      transform: "$"

  - path: /api/bands
    method: POST
    model: _

    query:
    transform: "$"
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: "bands"
      transform: "$"

  - path: /api/bands/:id
    model: _
    method: PATCH
    query:
    transform: "$"
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: "bands/:{id}:"

  - path: /api/bands/:id
    method: DELETE
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: "bands/:{id}:"
customRoutes:
  - path: /api/bands/:id/histories
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: "bands/::{id}::/history"
      transform: "$"
  - path: /api/bands/by
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      query:
        enabled: "::{status}::"
        effectiveDate: "::{effectiveDate}::"
        code: "::{code}::"
        CareerStreamId: "::{careerStreamId}::"
      path: "bands/by"
      transform: "$"

  - path: /api/bands/import
    model: _
    method: POST
    dataType: "formData"
    query:
    transform: "$"
    upstreamConfig:
      method: POST
      path: "bands/:import"

  - path: /api/bands/export
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: binary
      path: "career-bands:export"
      query:
        Page: '1'
        PageSize: '1000'
        OrderBy: 'code asc'
        Search: "::{search}::"
        Filter: "::{filter}::"
      transform: "$"

  - path: /api/bands/template
    model: _
    method: GET
    query:
    transform: "$"
    upstreamConfig:
      method: GET
      response:
        dataType: binary
      path: "bands/template"
  - path: /api/bands/insert-new-record
    method: POST
    model: _

    query:
    transform: '$'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'bands/insert-new-record'
      transform: '$'

  - path: /api/bands/get-by
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: "bands/get-by"
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: 'name asc'
        Search: '::{search}::'
        EffectiveDate: ':{effectiveDate}:'
        CareerStreamId: "::{careerStreamId}::"
        CareerStreamCode: "::{careerStreamCode}::"
        Enabled: ':{status}:'
        Filter: '::{filter}::'
      transform: "$"
  - path: /api/${{controller}}/multidelete
    method: DELETE
    model: _DELETE
    upstreamConfig:
      method: DELETE
      path: 'bands'

