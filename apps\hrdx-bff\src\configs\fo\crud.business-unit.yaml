controller: business-units
upstream: ${{UPSTREAM_FO_URL}}

models:
  - name: _
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: count
    config:
      id:
        from: id
        type: string
      # name BFF
      code:
        # name BE
        from: code
        type: string
        typeOptions:
          func: upperCase
      IdFilter:
        from: id
      CodeFilter:
        from: code
      na_longName:
        from: na_longName
      na_name:
        from: na_name
      na_shortName:
        from: na_shortName
      effectiveDate:
        from: effectiveDateFrom
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      status:
        from: enabled
        type: string
        typeOptions:
          func: YNToBoolean
      shortName:
        from: shortName
        type: string
        typeOptions:
          func: stringToMultiLang
      longName:
        from: name
        type: string
        typeOptions:
          func: stringToMultiLang
      name:
        from: name
        type: string
        typeOptions:
          func: stringToMultiLang
      function:
        from: function
        type: string
      orgType:
        from: orgType
        type: string
      responsibility:
        from: responsibility
        type: string
      companyName:
        from: companyName
        type: string
      company:
        from: company
        objectChildren:
          id:
            from: id
          code:
            from: code
          status:
            from: enabled
            typeOptions:
              func: YNToBoolean
          effectiveDate:
            from: effectiveDateFrom
            type: timestamp
            typeOptions:
              func: timestampToDateTime
          shortName:
            from: shortName
            type: string
            typeOptions:
              func: stringToMultiLang
          longName:
            from: name
            type: string
            typeOptions:
              func: stringToMultiLang
          name:
            from: name
            type: string
            typeOptions:
              func: stringToMultiLang
      parentId:
        from: parentId
      parentCode:
        from: parentCode
      businessUnitParentCode:
        from: businessUnitParentCode
      businessUnitParentName:
        from: businessUnitParentName
      parent:
        from: parent
        objectChildren:
          id:
            from: id
          code:
            from: code
          status:
            from: enabled
            typeOptions:
              func: YNToBoolean
          effectiveDate:
            from: effectiveDateFrom
            type: timestamp
            typeOptions:
              func: timestampToDateTime
          shortName:
            from: shortName
            type: string
            typeOptions:
              func: stringToMultiLang
          longName:
            from: name
            type: string
            typeOptions:
              func: stringToMultiLang
          name:
            from: name
            type: string
            typeOptions:
              func: stringToMultiLang
      parentBusinessUnits:
        from: $
        objectChildren:
          id:
            from: parentId
          code:
            from: parentCode
      parentCode:
        from: parentCode
      parentId:
        from: parentId
      companyId:
        from: companyId
        type: int
      companyObj:
        from: $
        objectChildren:
          id:
            from: companyId
          code:
            from: companyCode
      companyCode:
        from: companyCode
      companyCodeFilter:
        from: company.code
      # orgObjectType:
      #   from: type
      # orgObjectId:
      #   from: orgObjectId
      orgObjects:
        from: orgObjects
      # orgObjectObj:
      #   from: $
      #   objectChildren:
      #     id:
      #       from: orgObjectId
      #     code:
      #       from: orgObjectCode
      managerType:
        from: managerType
      headOfBusinessUnit:
        from: headOfBusinessUnit
      headOfBusinessUnitData:
        from: headOfBusinessUnitData
      headOfBusinessUnitObj:
        from: $
        objectChildren:
          id:
            from: headOfBusinessUnit
          code:
            from: headOfBusinessUnitCode
      deputyManager:
        from: deputyManagers
      deputyManagerData:
        from: deputyManagerData
      deputyManagerObj:
        from: deputyManagerObj
      managerPosition:
        from: managerPosition
      deputyManagerPosition:
        from: deputyManagerPositions
      action:
        from: action
        type: string
      reason:
        from: reason
        type: string
      decisionNo:
        from: decisionNo
        type: string
      decisionName:
        from: decisionName
        type: string
      issueDate:
        from: issuanceDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      authorityForApproval:
        from: authorityForApproval
        type: string
      signatory:
        from: signatory
        type: string
      attachFile:
        from: attachFiles
        type: string
      file:
        from: file
      attachFileResults:
        from: attachFileResults
      createdBy:
        from: createdBy
        type: string
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
        type: string
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
  - name: tree_model
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: count
    config:
      id:
        from: id
        type: string
      # name BFF
      code:
        # name BE
        from: code
        type: string
        typeOptions:
          func: upperCase
      name:
        from: name
      status:
        from: enabled
        type: string
        typeOptions:
          func: YNToBoolean
      children:
        from: children
      effectiveDate:
        from: effectiveDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      parentCode:
        from: parentCode
      companyCode:
        from: companyCode
      legalEntityCode:
        from: legalEntityCode
  - name: _DELETE
    config:
      codes:
        from: codes
      ids:
        from: ids
auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: business-units
crudConfig:
  query:
    sort:
      - field: code
        order: asc
  params:
    id:
      field: id
      type: string
      primary: true
    code:
      field: code
      type: string
  routes:
    exclude:
      - createManyBase
      - replaceOneBase
defaultQuery:

routes:
  - path: /api/business-units
    method: GET
    model: _

    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'business-units'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ":{options.sort}:"
        Search: '::{search}::'
        Filter: '::{filter}::'
      transform: '$ ~> | $.data | {"parentName": $exists($.businessUnitParentCode) ? $.businessUnitParentName & " ("  & $.businessUnitParentCode & ")" : "","companyName": $exists($.companyId) ? $.companyName & " ("  & $.companyCode & ")" : ""} |'

  - path: /api/business-units/:id
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'business-units/:{id}:'
      transform: '$ ~> | $ | {
      "parentBusinessUnits": $exists(parent) ?
        {
          "label": $exists(parent) ? parent.name.default & " (" & parent.code & ")",
          "value": {"id": parent.id, "code": parent.code },
          "additionalData": parent
        } : null,
        "companyObj": $exists(company) ?
        {
          "label": $exists(company) ? company.name.default & " (" & company.code & ")",
          "value": {"id": company.id, "code": company.code },
          "additionalData": company
        } : null,
        "headOfBusinessUnitObj": $exists(headOfBusinessUnitData) ?
        {
          "label": $exists(headOfBusinessUnitData) ?  $boolean(headOfBusinessUnitData.userName) ? headOfBusinessUnitData.lastName & " " & headOfBusinessUnitData.middleName & " " & "" & headOfBusinessUnitData.firstName & "" & "(" & headOfBusinessUnitData.userName & ")" : headOfBusinessUnitData.lastName & " " & headOfBusinessUnitData.middleName & " " & "" & headOfBusinessUnitData.firstName & "",
          "value": {"id": headOfBusinessUnitData.employeeId, "code": headOfBusinessUnitData.employeeId }
        } : null,
        "deputyManagerObj": $map(deputyManagerData, function($value, $index) {
           {
              "label": $boolean($value.userName) ? $value.lastName & " " & $value.middleName & " " & "" & $value.firstName & "" & "(" & $value.userName & ")" : $value.lastName & " " & $value.middleName & " " & "" & $value.firstName & "",
              "value":{"id": $value.employeeId,
              "code": $value.employeeId}
            }
          })[],
        "orgObjects": orgObjects.{
          "id": {
            "label": $exists(modelView) ? modelView.name & " (" & modelView.code & ")",
            "value": {"id": modelView.id, "code": modelView.code },
            "additionalData": modelView
          },
          "objData": {
          "label": $exists(modelView) ? modelView.name & " (" & modelView.code & ")",
          "value": {"id": modelView.id, "code": modelView.code },
          "additionalData": modelView
          },
          "type": orgObjectType,
          "code": modelView.code
        }[]
      }|'
  - path: /api/business-units/:id
    method: DELETE
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'business-units/:{id}:'
customRoutes:
  - path: /api/business-units/upload
    method: POST
    dataType: 'formData'
    model: _
    query:
    transform: '$'
    bodyTransform: '$merge([$,{"deputyManagers": $map(deputyManagerObj, function($value) { $exists($value.value) ? $value.value.id : $value.id}) []}])'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'business-units'
      transform: '$'
  - path: /api/business-units/insert-new-record/upload
    method: POST
    dataType: 'formData'
    model: _
    query:
    transform: '$'
    bodyTransform: '$merge([$,{"deputyManagers": $map(deputyManagerObj, function($value) { $exists($value.value) ? $value.value.id : $value.id}) []}])'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'business-units/insert-new-record'
      transform: '$'

  - path: /api/business-units/:id/upload
    model: _
    method: POST
    dataType: 'formData'
    query:
    transform: '$'
    bodyTransform: '$merge([$,{"deputyManagers": $map(deputyManagerObj, function($value) { $exists($value.value) ? $value.value.id : $value.id}) []}])'
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: 'business-units/:{id}:'
  - path: /api/business-units/:id/histories
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'business-units/::{id}::/history'
      transform: '$ ~> | $ | {
        "parentBusinessUnits":
        {
          "label": $exists(parent) ? parent.name.default & " (" & parent.code & ")",
          "value": {"id": parent.id, "code": parent.code },
          "additionalData": parent
        },
        "companyObj":
        {
          "label": $exists(company) ? company.name.default & " (" & company.code & ")",
          "value": {"id": company.id, "code": company.code },
          "additionalData": company
        },
        "headOfBusinessUnitObj":
        {
          "label": $exists(headOfBusinessUnitData) ?  $boolean(headOfBusinessUnitData.userName) ? headOfBusinessUnitData.lastName & " " & headOfBusinessUnitData.middleName & " " & "" & headOfBusinessUnitData.firstName & "" & "(" & headOfBusinessUnitData.userName & ")" : headOfBusinessUnitData.lastName & " " & headOfBusinessUnitData.middleName & " " & "" & headOfBusinessUnitData.firstName & "",
          "value": {"id": headOfBusinessUnitData.employeeId, "code": headOfBusinessUnitData.employeeId }
        },
        "deputyManagerObj": $map(deputyManagerData, function($value, $index) {
           {
              "label": $boolean($value.userName) ? $value.lastName & " " & $value.middleName & " " & "" & $value.firstName & "" & "(" & $value.userName & ")" : $value.lastName & " " & $value.middleName & " " & "" & $value.firstName & "",
              "value":{"id": $value.employeeId,
              "code": $value.employeeId}
            }
          })[],
        "orgObjects": orgObjects.{
            "id": {
            "label": $exists(modelView) ? modelView.name & " (" & modelView.code & ")",
            "value": {"id": modelView.id, "code": modelView.code },
            "additionalData": modelView
          },
          "objData": {
            "label": $exists(modelView) ? modelView.name & " (" & modelView.code & ")",
            "value": {"id": modelView.id, "code": modelView.code },
            "additionalData": modelView
          },
          "type": orgObjectType,
          "code": modelView.code
        }[]
      }|'
  - path: /api/business-units/by
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'business-units/by'
      query:
        enabled: '::{status}::'
        effectiveDate: '::{effectiveDate}::'
        code: '::{code}::'
        companyId: '::{companyId}::'
      transform: '$'

  - path: /api/business-units/get-by
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'business-units/get-by'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: 'name asc'
        Search: '::{search}::'
        Filter: '::{filter}::'
        effectiveDate: '::{effectiveDate}::'
        code: '::{code}::'
      transform: '$'

  - path: /api/business-units/get-by-one-level
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'business-units/get-by-one-level'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Enabled: '::{status}::'
        Code: '::{code}::'
        Search: '::{search}::'
        Filter: '::{filter}::'
        GroupIds: '::{groupIds}::'
        CompanyIds: '::{companyIds}::'
        EffectiveDate: '::{effectiveDate}::'
      transform: '$'

  - path: /api/business-units/import
    model: _
    method: POST
    dataType: 'formData'
    query:
    transform: '$'
    upstreamConfig:
      method: POST
      path: 'business-units/:import'

  - path: /api/business-units/export
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: binary
      path: 'business-units/:export'
      query:
        Page: '1'
        PageSize: '1000'
        OrderBy: 'code asc'
        Search: '::{search}::'
        Filter: '::{filter}::'
      transform: '$'
  - path: /api/business-units/template
    model: _
    method: GET
    query:
    transform: '$'
    upstreamConfig:
      method: GET
      response:
        dataType: binary
      path: 'business-units/template'
  - path: /api/business-units/insert-new-record
    method: POST
    model: _
    query:
    transform: '$'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'business-units/insert-new-record'
      transform: '$'

  - path: /api/business-units/get-list
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'business-units/get-list'
      query:
        Enabled: '::{status}::'
        EffectiveDate: '::{effectiveDate}::'
        CompanyIds: '::{companyId}::'
        CompanyCodes: '::{companyCode}::'
      transform: '$'

  - path: /api/business-units/v2/get-list
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'business-units/v2/get-list'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: '::{search}::'
        Filter: '::{filter}::'
        Enabled: '::{status}::'
        EffectiveDate: '::{effectiveDate}::'
        CompanyIds: '::{companyId}::'
        CompanyCodes: '::{companyCode}::'
      transform: '$'

  - path: /api/business-units/v3/get-list
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'business-units/v3/get-list'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: '::{search}::'
        Filter: '::{filter}::'
        Enabled: '::{status}::'
        EffectiveDate: '::{effectiveDate}::'
        CompanyIds: '::{companyId}::'
        CompanyCodes: '::{companyCode}::'
        LegalEntityCodes: '::{legalEntityCode}::'
      transform: '$'

  - path: /api/business-units/get-by-children-with-tree
    method: GET
    model: tree_model
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'business-units/get-by-children-with-tree'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: 'name asc'
        Search: '::{search}::'
        CompanyIds: ':{companyId}:'
        CompanyCodes: ':{companyCode}:'
        LegalEntityCodes: ':{legalEntityCode}:'
        Filter: '::{filter}::'
        EffectiveDate: ':{effectiveDate}:'
        Enabled: ':{status}:'
      transform: '(  $transformChildren := function($child) {    {      "id": $child.id,      "key": $child.code,      "title": $child.name & " (" & $child.code & ")",      "isLeaf": $count($child.children) = 0,      "children": $count($child.children) > 0 ?        $map($child.children, function($subChild) {          $transformChildren($subChild)        })[]    }  };     $merge([$, {"data": $map($, function($item) {        $transformChildren($item)    })[]}]))'

  - path: /api/business-units/get-by-children-with-tree/raw
    method: GET
    model: tree_model
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'business-units/get-by-children-with-tree'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: 'name asc'
        Search: '::{search}::'
        CompanyIds: ':{companyId}:'
        CompanyCodes: ':{companyCode}:'
        LegalEntityCodes: ':{legalEntityCode}:'
        Filter: '::{filter}::'
        EffectiveDate: ':{effectiveDate}:'
        Enabled: ':{status}:'
      transform: '$'
  - path: /api/${{controller}}/multidelete
    method: DELETE
    model: _DELETE
    upstreamConfig:
      method: DELETE
      path: 'business-units'
