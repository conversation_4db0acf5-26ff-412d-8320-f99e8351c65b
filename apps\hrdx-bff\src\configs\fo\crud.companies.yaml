controller: companies
upstream: ${{UPSTREAM_FO_URL}}

models:
  - name: _
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: count
    config:
      id:
        from: id
        type: string
      # name BFF
      code:
        # name BE
        from: code
        type: string
        typeOptions:
          func: upperCase
      effectiveDate:
        from: effectiveDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      fromDate:
        from: fromDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      toDate:
        from: toDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      status:
        from: enabled
        type: string
        typeOptions:
          func: YNToBoolean
      shortName:
        from: shortName
        type: string
        typeOptions:
          func: stringToMultiLang
      # orgObjectType:
      #   from: orgObjectType
      #   type: string
      # orgObjectId:
      #   from: orgObjectId
      # orgObjectCode:
      #   from: orgObjectCode
      # orgObjectObj:
      #   from: $
      #   objectChildren:
      #     id:
      #       from: orgObjectId
      #     code:
      #       from: orgObjectCode
      orgObjects:
        from: orgObjects
      longName:
        from: name
        type: string
        typeOptions:
          func: stringToMultiLang
      na_longName:
        from: na_longName
      na_shortName:
        from: na_shortName
      name:
        from: name
        type: string
        typeOptions:
          func: stringToMultiLang
      locationId:
        from: locationId
      location:
        from: location
      locationObj:
        from: $
        objectChildren:
          id:
            from: locationId
          code:
            from: locationCode
      managerPositionObj:
        from: $
        objectChildren:
          id:
            from: managerPositionId
          code:
            from: managerPositionCode
      groupId:
        from: groupId
        type: int
      # group:
      #   from: group
      group:
        from: group
        objectChildren:
          id:
            from: id
          code:
            from: code
          status:
            from: enabled
            typeOptions:
              func: YNToBoolean
          effectiveDate:
            from: effectiveDate
            type: timestamp
            typeOptions:
              func: timestampToDateTime
          shortName:
            from: shortName
            type: string
            typeOptions:
              func: stringToMultiLang
          longName:
            from: name
            type: string
            typeOptions:
              func: stringToMultiLang
          name:
            from: name
            type: string
            typeOptions:
              func: stringToMultiLang
      groupObj:
        from: $
        objectChildren:
          id:
            from: groupId
          code:
            from: groupCode
      groupName:
        from: group.name
      groupCode:
        from: groupCode
      groupCodeFilter:
        from: group.code
      charterCapital:
        from: charterCapital
        type: int
      managerType:
        from: managerType
      headOfCompany:
        from: headOfCompany
      headOfCompanyData:
        from: headOfCompanyData
      headOfCompanyObj:
        from: $
        objectChildren:
          id:
            from: headOfCompany
          code:
            from: headOfCompanyCode
      managerPositionId:
        from: managerPositionId
      managerPosition:
        from: managerPosition
        type: string
      action:
        from: action
        type: string
      reason:
        from: reason
        type: string
      decisionNo:
        from: decisionNo
        type: string
      decisionName:
        from: decisionName
        type: string
      issueDate:
        from: issuanceDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      authorityForApproval:
        from: authorityForApproval
        type: in
      signatory:
        from: signatory
        type: string
      attachFile:
        from: attachFiles
        type: string
      file:
        from: file
      attachFileResults:
        from: attachFileResults
      createdBy:
        from: createdBy
        type: string
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
        type: string
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
  - name: _DELETE
    config:
      codes:
        from: codes
      ids:
        from: ids
auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: companies
crudConfig:
  query:
    sort:
      - field: code
        order: asc
  params:
    id:
      field: id
      type: string
      primary: true
    code:
      field: code
      type: string
  routes:
    exclude:
      - createManyBase
      - replaceOneBase
defaultQuery:

routes:
  - path: /api/companies
    method: GET
    model: _

    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
        dataKey: data
      path: 'companies'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ":{options.sort}:"
        Search: '::{search}::'
        Filter: '::{filter}::'
      transform: '$ ~> | $.data | {"groupName": $exists($.groupId) ? $.groupName & " ("  & $.groupCode & ")" : ""} |'

  - path: /api/companies/:id
    method: GET
    model: _

    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'companies/:{id}:'
      transform: '$ ~> | $ |
        {
        "locationObj": $exists(location) ? {
        "label": $exists(location) ? location.name & " (" & location.code & ")",
        "value": $exists(location) ? {"id": location.id, "code": location.code } : null
        } : null,
        "groupObj": $exists(group) ? {
            "label": $exists(group) ? group.name.default & " (" & group.code & ")",
            "value": {"id": group.id, "code": group.code },
            "id": group.id,
            "code": group.code,
            "additionalData": group
          } : null,
        "headOfCompanyObj":$exists(headOfCompanyData) ?
        {
        "label": $exists(headOfCompanyData) ?  $boolean(headOfCompanyData.userName) ? headOfCompanyData.lastName & " " & headOfCompanyData.middleName & " " & "" & headOfCompanyData.firstName & "" & "(" & headOfCompanyData.userName & ")" : headOfCompanyData.lastName & " " & headOfCompanyData.middleName & " " & "" & headOfCompanyData.firstName & "",
        "value": {"id": headOfCompanyData.employeeId, "code": headOfCompanyData.employeeId }
        } : null,
        "orgObjects": orgObjects.{
        "id": {
        "label": $exists(modelView) ? modelView.name & " (" & modelView.code & ")",
        "value": {"id": modelView.id, "code": modelView.code },
        "additionalData": modelView
        },
        "objData": {
        "label": $exists(modelView) ? modelView.name & " (" & modelView.code & ")",
        "value": {"id": modelView.id, "code": modelView.code },
        "additionalData": modelView
        },
        "type": orgObjectType,
        "code": modelView.code
        }[]

        }|'

  - path: /api/companies
    method: POST
    model: _

    query:
    transform: '$'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'companies'
      transform: '$'

  - path: /api/companies/:id
    model: _
    method: PATCH
    query:
    transform: '$'
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: 'companies/:{id}:'

  - path: /api/companies/:id
    method: DELETE
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'companies/:{id}:'
customRoutes:
  - path: /api/companies/upload
    method: POST
    dataType: 'formData'
    model: _
    query:
    transform: '$'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'companies'
      transform: '$'

  - path: /api/companies/insert-new-record/upload
    method: POST
    dataType: 'formData'
    model: _
    query:
    transform: '$'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'companies/insert-new-record'
      transform: '$'

  - path: /api/companies/:id/upload
    model: _
    method: POST
    dataType: 'formData'
    query:
    transform: '$'
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: 'companies/:{id}:'
  - path: /api/companies/:id/histories
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'companies/::{id}::/history'
      transform: '$ ~> | $ | {
        "locationObj": $exists(location) ? {
        "label": $exists(location) ? location.name & " (" & location.code & ")",
        "value": {"id": location.id, "code": location.code }
        } : null,
        "headOfCompanyObj":
        {
        "label": $exists(headOfCompanyData) ?  $boolean(headOfCompanyData.userName) ? headOfCompanyData.lastName & " " & headOfCompanyData.middleName & " " & "" & headOfCompanyData.firstName & "" & "(" & headOfCompanyData.userName & ")" : headOfCompanyData.lastName & " " & headOfCompanyData.middleName & " " & "" & headOfCompanyData.firstName & "",
        "value": {"id": headOfCompanyData.employeeId, "code": headOfCompanyData.employeeId }
        },
        "groupObj": {
            "id": groupId,
            "code": groupCode,
            "label": $exists(group) ? group.name.default & " (" & group.code & ")",
            "value": {"id": $exists(group) ? group.id : groupId, "code": $exists(group) ? group.code : groupCode },
            "additionalData": group
          },
          "orgObjects": orgObjects.{
          "id": {
            "label": $exists(modelView) ? modelView.name & " (" & modelView.code & ")",
            "value": {"id": modelView.id, "code": modelView.code },
            "additionalData": modelView
          },
          "objData": {
          "label": $exists(modelView) ? modelView.name & " (" & modelView.code & ")",
          "value": {"id": modelView.id, "code": modelView.code },
          "additionalData": modelView
          },
          "type": orgObjectType,
          "code": modelView.code
        }[]
        }|'
  - path: /api/companies/by
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'companies/by'
      query:
        Enabled: '::{status}::'
        Code: '::{code}::'
        EffectiveDate: ':{effectiveDate}:'
      transform: '$'
  - path: /api/companies/get-by
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'companies/get-by'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: 'name asc'
        Search: '::{search}::'
        EffectiveDate: ':{effectiveDate}:'
        Enabled: ':{status}:'
        Filter: '::{filter}::'
        # Code: '::{code}::'
        GroupCode: ':{groupCode}:'
      transform: '$'

  - path: /api/companies/import
    model: _
    method: POST
    dataType: 'formData'
    query:
    transform: '$'
    upstreamConfig:
      method: POST
      path: 'companies/:import'

  - path: /api/companies/export
    method: GET
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: binary
      path: 'companies/:export'
      query:
        Page: '1'
        PageSize: '1000'
        OrderBy: 'code asc'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$'
  - path: /api/companies/template
    model: _
    method: GET
    query:
    transform: '$'
    upstreamConfig:
      method: GET
      response:
        dataType: binary
      path: 'companies/template'
  - path: /api/companies/insert-new-record
    method: POST
    model: _
    query:
    transform: '$'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'companies/insert-new-record'
      transform: '$'

  - path: /api/companies/get-list
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'companies/get-list'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: '::{search}::'
        Filter: '::{filter}::'
      transform: '$'

  - path: /api/companies/get-by-filter
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'companies/get-by-filter'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: 'name asc'
        Search: '::{search}::'
        FromDate: ':{fromDate}:'
        ToDate: ':{toDate}:'
        Enabled: ':{status}:'
        Filter: '::{filter}::'
        # Code: '::{code}::'
        GroupCode: ':{groupCode}:'
      transform: '$'
  - path: /api/${{controller}}/multidelete
    method: DELETE
    model: _DELETE
    upstreamConfig:
      method: DELETE
      path: 'companies'
