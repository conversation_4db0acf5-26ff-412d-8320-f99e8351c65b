controller: departments
upstream: ${{UPSTREAM_FO_URL}}

models:
  - name: _
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: count
    config:
      id:
        from: id
        type: string
      # name BFF
      code:
        # name BE
        from: code
        type: string
        typeOptions:
          func: upperCase
      IdFilter:
        from: id
      codeFilter:
        from: code
      effectiveDate:
        from: effectiveDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      fromDate:
        from: fromDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      toDate:
        from: toDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      status:
        from: enabled
        type: string
        typeOptions:
          func: YNToBoolean
      shortName:
        from: shortName
        type: string
        typeOptions:
          func: stringToMultiLang
      longName:
        from: name
        type: string
        typeOptions:
          func: stringToMultiLang
      na_longName:
        from: na_longName
      na_name:
        from: na_name
      na_shortName:
        from: na_shortName
      displayName:
        from: displayName
        type: string
        typeOptions:
          func: stringToMultiLang
      parentDepartmentCode:
        from: parentDepartmentCode
      parentDepartments:
        from: $
        objectChildren:
          id:
            from: parentDepartmentId
          code:
            from: parentDepartmentCode
      name:
        from: name
        type: string
        typeOptions:
          func: stringToMultiLang
      legalEntityName:
        from: legalEntity.name
        type: string
      legalEntityCode:
        from: legalEntityCode
      legalEntityId:
        from: legalEntityId
        type: int
      legalEntityObj:
        from: $
        objectChildren:
          id:
            from: legalEntityId
          code:
            from: legalEntityCode
      legalEntity:
        from: legalEntity
        objectChildren:
          id:
            from: id
          code:
            from: code
          status:
            from: enabled
            typeOptions:
              func: YNToBoolean
          effectiveDate:
            from: effectiveDate
            type: timestamp
            typeOptions:
              func: timestampToDateTime
          shortName:
            from: shortName
            type: string
            typeOptions:
              func: stringToMultiLang
          longName:
            from: name
            type: string
            typeOptions:
              func: stringToMultiLang
          name:
            from: name
            type: string
            typeOptions:
              func: stringToMultiLang
      businessUnitName:
        from: businessUnit.name
        type: string
      businessUnit:
        from: businessUnit
        objectChildren:
          id:
            from: id
          code:
            from: code
          status:
            from: enabled
            typeOptions:
              func: YNToBoolean
          effectiveDate:
            from: effectiveDate
            type: timestamp
            typeOptions:
              func: timestampToDateTime
          shortName:
            from: shortName
            type: string
            typeOptions:
              func: stringToMultiLang
          longName:
            from: name
            type: string
            typeOptions:
              func: stringToMultiLang
          name:
            from: name
            type: string
            typeOptions:
              func: stringToMultiLang
      businessUnitCode:
        from: businessUnitCode
      businessUnitId:
        from: businessUnitId
        type: int
      businessUnitObj:
        from: $
        objectChildren:
          id:
            from: businessUnitId
          code:
            from: businessUnitCode
      divisionName:
        from: division.name
        type: string
      divisionCode:
        from: divisionCode
      divisionId:
        from: divisionId
        type: int
      divisionObj:
        from: $
        objectChildren:
          id:
            from: divisionId
          code:
            from: divisionCode
      division:
        from: division
        objectChildren:
          id:
            from: id
          code:
            from: code
          status:
            from: enabled
            typeOptions:
              func: YNToBoolean
          effectiveDate:
            from: effectiveDate
            type: timestamp
            typeOptions:
              func: timestampToDateTime
          shortName:
            from: shortName
            type: string
            typeOptions:
              func: stringToMultiLang
          longName:
            from: name
            type: string
            typeOptions:
              func: stringToMultiLang
          name:
            from: name
            type: string
            typeOptions:
              func: stringToMultiLang
      companyId:
        from: companyId
        type: int
      companyCodeFilter:
        from: companyCode
        type: string
      LegalEntityIds:
        from: LegalEntityIds
      parentDepartmentId:
        from: parentDepartmentId
      parentDepartment:
        from: parentDepartment
        objectChildren:
          id:
            from: id
          code:
            from: code
          status:
            from: enabled
            typeOptions:
              func: YNToBoolean
          effectiveDate:
            from: effectiveDateFrom
            type: timestamp
            typeOptions:
              func: timestampToDateTime
          shortName:
            from: shortName
            type: string
            typeOptions:
              func: stringToMultiLang
          longName:
            from: name
            type: string
            typeOptions:
              func: stringToMultiLang
          name:
            from: name
            type: string
            typeOptions:
              func: stringToMultiLang
      locationData:
        from: location
      depId:
        from: depId
      depName:
        from: depName
      depCode:
        from: depCode
      depObj:
        from: $
        objectChildren:
          id:
            from: depId
          code:
            from: depCode
      locationId:
        from: locationId
      locationCode:
        from: locationCode
      locationName:
        from: location.name
      location:
        from: location
        objectChildren:
          id:
            from: id
          code:
            from: code
          status:
            from: enabled
            typeOptions:
              func: YNToBoolean
          effectiveDate:
            from: effectiveDateFrom
            type: timestamp
            typeOptions:
              func: timestampToDateTime
          shortName:
            from: shortName
            type: string
            typeOptions:
              func: stringToMultiLang
          longName:
            from: name
            type: string
            typeOptions:
              func: stringToMultiLang
          name:
            from: name
      locationObj:
        from: $
        objectChildren:
          id:
            from: locationId
          code:
            from: locationCode
      # costCenter:
      #   from: costCenterId
      #   type: int
      costCenterId:
        from: costCenter.id
      costCenterCode:
        from: costCenter.code
      companyIdTmp:
        from: costCenter.companyId
      costCenter:
        from: $
        objectChildren:
          id:
            from: costCenterId
          code:
            from: costCenterCode
      function:
        from: functionId
        type: string
      functionCode:
        from: functionCode
      functionObj:
        from: $
        objectChildren:
          id:
            from: functionId
          code:
            from: functionCode
      orgTypeId:
        from: orgType
      orgTypeCode:
        from: orgTypeCode
      responsibility:
        from: responsibility
        type: string
      # orgObjectType:
      #   from: orgObjectType
      #   type: string
      # orgObjectId:
      #   from: orgObjectId
      # orgObjectObj:
      #   from: $
      #   objectChildren:
      #     id:
      #       from: orgObjectId
      #     code:
      #       from: orgObjectCode
      orgObjects:
        from: orgObjects
      managerType:
        from: managerType
      headOfDepartment:
        from: headOfDepartment
      headOfDepartmentData:
        from: headOfDepartmentData
      headOfDepartmentObj:
        from: $
        objectChildren:
          id:
            from: headOfDepartment
          code:
            from: headOfDepartmentCode
      deputyManagers:
        from: deputyManagers
      deputyManagersObj:
        from: deputyManagersObj
      deputyManagerData:
        from: deputyManagerData
      managerPositionId:
        from: managerPositionId
      managerPositionCode:
        from: managerPositionCode
      managerPositionObj:
        from: $
        objectChildren:
          id:
            from: managerPositionId
          code:
            from: managerPositionCode
      deputyPositionManagers:
        from: deputyPositionManagers
      action:
        from: action
        type: string
      reason:
        from: reason
        type: string
      decisionNo:
        from: decisionNo
        type: string
      decisionName:
        from: decisionName
        type: string
      issueDate:
        from: issuanceDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      authorityForApproval:
        from: authorityForApproval
      signatory:
        from: signatory
        type: string
      attachFile:
        from: attachFiles
        type: string
      file:
        from: file
      attachFileResults:
        from: attachFileResults
      createdBy:
        from: createdBy
        type: string
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
        type: string
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
  - name: list_by_childrents
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: count
    config:
      id:
        from: id
        type: string
      # name BFF
      code:
        # name BE
        from: code
        type: string
        typeOptions:
          func: upperCase
      name:
        from: name
      status:
        from: enabled
        type: string
        typeOptions:
          func: YNToBoolean
      childrens:
        from: childrens
      LegalEntityIds:
        from: LegalEntityIds

  - name: list_by_children_with_tree
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: count
    config:
      id:
        from: id
        type: string
      # name BFF
      code:
        # name BE
        from: code
        type: string
        typeOptions:
          func: upperCase
      name:
        from: name
      status:
        from: enabled
        type: string
        typeOptions:
          func: YNToBoolean
      children:
        from: children
      effectiveDate:
        from: WffectiveDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      LegalEntityIds:
        from: LegalEntityIds
      legalEntityCode:
        from: legalEntityCode
      bussinessUnitCode:
        from: bussinessUnitCode
      divisionCode:
        from: divisionCode
  - name: _DELETE
    config:
      codes:
        from: codes
      ids:
        from: ids
auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: departments
crudConfig:
  query:
    sort:
      - field: code
        order: asc
  params:
    id:
      field: id
      type: string
      primary: true
    code:
      field: code
      type: string

  routes:
    exclude:
      - createManyBase
      - replaceOneBase
defaultQuery:

routes:
  - path: /api/departments
    method: GET
    model: _

    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'departments'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: '::{search}::'
        Filter: '::{filter}::'
      transform: '$ ~> | $.data | {"legalEntityName": $exists($.legalEntityId) ? $.legalEntity.name.default & " ("  & $.legalEntityCode & ")" : "","parentDepartmentName": $exists($.parentDepartment) ? $.parentDepartment.name.default & " ("  & $.parentDepartment.code & ")" : "","businessUnitName": $exists($.businessUnitId) ? $.businessUnit.name.default & " ("  & $.businessUnitCode & ")" : "","divisionName": $exists($.divisionId) ? $.division.name.default & " ("  & $.divisionCode & ")" : ""} |'

  - path: /api/departments/:id
    method: GET
    model: _

    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'departments/:{id}:'
      transform: '$ ~> | $ |
        {
        "parentDepartments": {
        "label": $exists(parentDepartment) ? parentDepartment.name.default & " (" & parentDepartment.code & ")",
        "value": {"id":parentDepartment.id, "code": parentDepartment.code },
        "additionalData": parentDepartment
        },
        "locationObj": {
        "label": $exists(location) ? location.longName.default & " (" & location.code & ")",
        "value": {"id": location.id, "code": location.code },
        "additionalData": location
        },
        "divisionObj": {
        "label": $exists(division) ? division.longName.default & " (" & division.code & ")",
        "value": {"id": division.id, "code": division.code },
        "additionalData": division
        },
        "businessUnitObj": {
        "label": $exists(businessUnit) ? businessUnit.longName.default & " (" & businessUnit.code & ")",
        "value": {"id": businessUnit.id, "code": businessUnit.code },
        "additionalData": businessUnit
        },
        "legalEntityObj": {
        "label": $exists(legalEntity) ? legalEntity.longName.default & " (" & legalEntity.code & ")",
        "value": {"id": legalEntity.id, "code": legalEntity.code },
        "additionalData": legalEntity
        },
        "depObj": {
        "label": $exists(depName) ? depName & " (" & depCode & ")",
        "value": $exists(depCode) ? {"id": depId, "code": depCode }
        },
        "headOfDepartmentObj":
        {
        "label": $exists(headOfDepartmentData) ?  $boolean(headOfDepartmentData.userName) ? headOfDepartmentData.lastName & " " & headOfDepartmentData.middleName & " " & "" & headOfDepartmentData.firstName & "" & "(" & headOfDepartmentData.userName & ")" : headOfDepartmentData.lastName & " " & headOfDepartmentData.middleName & " " & "" & headOfDepartmentData.firstName & "",
        "value": {"id": headOfDepartmentData.employeeId, "code": headOfDepartmentData.employeeId }
        },
        "orgObjects": orgObjects.{
        "id": {
        "label": $exists(modelView) ? modelView.name & " (" & modelView.code & ")",
        "value": {"id": modelView.id, "code": modelView.code },
        "additionalData": modelView
        },
        "objData": {
        "label": $exists(modelView) ? modelView.name & " (" & modelView.code & ")",
        "value": {"id": modelView.id, "code": modelView.code },
        "additionalData": modelView
        },
        "type": orgObjectType,
        "code": modelView.code
        }[],
        "deputyManagersObj": $map(deputyManagerData, function($value, $index) {
        {
        "label": $boolean($value.userName) ? $value.lastName & " " & $value.middleName & " " & "" & $value.firstName & "" & "(" & $value.userName & ")" : $value.lastName & " " & $value.middleName & " " & "" & $value.firstName & "",
        "value":{"id": $value.employeeId,
        "code": $value.employeeId}
        }
        })[]
        }|'

  - path: /api/departments
    method: POST
    model: _

    query:
    transform: '$'
    bodyTransform: '$merge([$,{"deputyManagers": $map(deputyManagersObj, function($value) { $exists($value.value) ? $value.value.id : $value.id}) []}])'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'departments'
      transform: '$'

  - path: /api/departments/:id
    model: _
    method: PATCH
    query:
    transform: '$'
    bodyTransform: '$merge([$,{"deputyManagers": $map(deputyManagersObj, function($value) { $exists($value.value) ? $value.value.id : $value.id}) []}])'
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: 'departments/:{id}:'

  - path: /api/departments/:id
    method: DELETE
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'departments/:{id}:'
customRoutes:
  - path: /api/departments/upload
    method: POST
    dataType: 'formData'
    model: _
    query:
    transform: '$'
    bodyTransform: '$merge([$,{"deputyManagers": $map(deputyManagersObj, function($value) { $exists($value.value) ? $value.value.id : $value.id}) []}])'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'departments'
      transform: '$'
  - path: /api/departments/insert-new-record/upload
    method: POST
    dataType: 'formData'
    model: _
    query:
    transform: '$'
    bodyTransform: '$merge([$,{"deputyManagers": $map(deputyManagersObj, function($value) { $exists($value.value) ? $value.value.id : $value.id}) []}])'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'departments/insert-new-record'
      transform: '$'
  - path: /api/departments/:id/upload
    model: _
    method: POST
    dataType: 'formData'
    query:
    transform: '$'
    bodyTransform: '$merge([$,{"deputyManagers": $map(deputyManagersObj, function($value) { $exists($value.value) ? $value.value.id : $value.id}) []}])'
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: 'departments/:{id}:'
  - path: /api/departments/:id/histories
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'departments/::{id}::/history'
      transform: '$ ~> | $ | {
        "parentDepartments":
        {
        "label": $exists(parentDepartment) ? parentDepartment.name.default & " (" & parentDepartment.code & ")",
        "value": {"id":parentDepartment.id, "code": parentDepartment.code },
        "additionalData": parentDepartment
        },
        "locationObj": {
        "label": $exists(location) ? location.longName.default & " (" & location.code & ")",
        "value": {"id": location.id, "code": location.code },
        "additionalData": location
        },
        "divisionObj": {
        "label": $exists(division) ? division.longName.default & " (" & division.code & ")",
        "value": {"id": division.id, "code": division.code },
        "additionalData": division
        },
        "businessUnitObj": {
        "label": $exists(businessUnit) ? businessUnit.longName.default & " (" & businessUnit.code & ")",
        "value": {"id": businessUnit.id, "code": businessUnit.code },
        "additionalData": businessUnit
        },
        "legalEntityObj": {
        "label": $exists(legalEntity) ? legalEntity.longName.default & " (" & legalEntity.code & ")",
        "value": {"id": legalEntity.id, "code": legalEntity.code },
        "additionalData": legalEntity
        },
        "depObj": {
        "label": $exists(depName) ? depName & " (" & depCode & ")",
        "value": $exists(depCode) ? {"id": depId, "code": depCode }
        },
        "headOfDepartmentObj":
        {
        "label": $exists(headOfDepartmentData) ?  $boolean(headOfDepartmentData.userName) ? headOfDepartmentData.lastName & " " & headOfDepartmentData.middleName & " " & "" & headOfDepartmentData.firstName & "" & "(" & headOfDepartmentData.userName & ")" : headOfDepartmentData.lastName & " " & headOfDepartmentData.middleName & " " & "" & headOfDepartmentData.firstName & "",
        "value": {"id": headOfDepartmentData.employeeId, "code": headOfDepartmentData.employeeId }
        },
        "orgObjects": orgObjects.{
        "id": {
        "label": $exists(modelView) ? modelView.name & " (" & modelView.code & ")",
        "value": {"id": modelView.id, "code": modelView.code },
        "additionalData": modelView
        },
        "objData": {
            "label": $exists(modelView) ? modelView.name & " (" & modelView.code & ")",
            "value": {"id": modelView.id, "code": modelView.code },
            "additionalData": modelView
          },
        "type": orgObjectType,
        "code": modelView.code
        }[],
        "deputyManagersObj": $map(deputyManagerData, function($value, $index) {
        {
        "label": $boolean($value.userName) ? $value.lastName & " " & $value.middleName & " " & "" & $value.firstName & "" & "(" & $value.userName & ")" : $value.lastName & " " & $value.middleName & " " & "" & $value.firstName & "",
        "value":{"id": $value.employeeId,
        "code": $value.employeeId}
        }
        })[]
        }|'
  - path: /api/departments/by
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'departments/by'
      query:
        Enabled: '::{status}::'
        EffectiveDate: '::{effectiveDate}::'
        Code: '::{code}::'
      transform: '$'
  - path: /api/departments/get-by
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'departments/get-by'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: 'name asc'
        code: '::{code}::'
        Search: '::{search}::'
        CompanyId: ':{companyId}:'
        Filter: '::{filter}::'
        Enabled: ':{status}:'
        LocationId: ':{locationId}:'
        CostCenterId: ':{costCenterId}:'
        LegalEntityCode: ':{legalEntityCode}:'
        LegalEntityIds: ':{LegalEntityIds}:'
        EffectiveDate: ':{effectiveDate}:'
      transform: '$'

  - path: /api/departments/import
    model: _
    method: POST
    dataType: 'formData'
    query:
    transform: '$'
    upstreamConfig:
      method: POST
      path: 'departments/:import'

  - path: /api/departments/export
    method: GET
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: binary
      path: 'departments/:export'
      query:
        Page: '1'
        PageSize: '1000'
        OrderBy: 'code asc'
        Search: '::{search}::'
        Filter: '::{filter}::'
      transform: '$'
  - path: /api/departments/insert-new-record
    method: POST
    model: _
    query:
    transform: '$'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'departments/insert-new-record'
      transform: '$'

  - path: /api/departments/get-list
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'departments/get-list'
      query:
        Enabled: '::{status}::'
        EffectiveDate: '::{effectiveDate}::'
        CompanyIds: '::{companyId}::'
        CompanyCodes: '::{companyCode}::'
        LegalEntityIds: '::{legalEntityId}::'
        LegalEntityCodes: '::{legalEntityCode}::'
        BusinessUnitIds: '::{businessUnitId}::'
        BusinessUnitCodes: '::{businessUnitCode}::'
        DivisionIds: '::{divisionId}::'
        DivisionCodes: '::{divisionCode}::'
      transform: '$'

  - path: /api/departments/get-by-filter
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'departments/get-by-filter'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: 'name asc'
        # code: '::{code}::'
        Search: '::{search}::'
        CompanyId: ':{companyId}:'
        Filter: '::{filter}::'
        Enabled: ':{status}:'
        LocationId: ':{locationId}:'
        CostCenterId: ':{costCenterId}:'
        LegalEntityCode: ':{legalEntityCode}:'
        LegalEntityIds: ':{LegalEntityIds}:'
        FromDate: '::{fromDate}::'
        ToDate: '::{toDate}::'
      transform: '$'

  - path: /api/departments/get-dropdown-list-by
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'departments/get-dropdown-list-by'
      query:
        Enabled: '::{status}::'
        EffectiveDate: '::{effectiveDate}::'
        CompanyIds: '::{companyId}::'
        CompanyCodes: '::{companyCode}::'
        LegalEntityIds: '::{legalEntityId}::'
        LegalEntityCodes: '::{legalEntityCode}::'
        BusinessUnitIds: '::{businessUnitId}::'
        BusinessUnitCodes: '::{businessUnitCode}::'
        DivisionIds: '::{divisionId}::'
        DivisionCodes: '::{divisionCode}::'
      transform: '$'

  - path: /api/departments/get-by-childrens
    method: GET
    model: list_by_childrents
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'departments/get-by-childrens'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: 'name asc'
        Search: '::{search}::'
        CompanyId: ':{companyId}:'
        Filter: '::{filter}::'
        EffectiveDate: ':{effectiveDate}:'
        Enabled: ':{status}:'
        LocationId: ':{locationId}:'
        CostCenterId: ':{costCenterId}:'
        LegalEntityCode: ':{legalEntityCode}:'
        LegalEntityCodes: '::{LegalEntityCodes}::'
        LegalEntityIds: ':{LegalEntityIds}:'
      transform: '
        (   $transformChildren := function($child) {
        {
        "id": $child.id,
        "key": $child.code,
        "title": $child.name,
        "isLeaf": $count($child.childrens) > 0 ? false : true,
        "children": (
        $map($child.childrens, function($subChild) {
        $transformChildren($subChild)
        })[]
        )
        }
        };
        $merge([$, {"data": $map($.data, function($dept) {
        {
        "id": $dept.id,
        "key": $dept.code,
        "title": $dept.name,
        "isLeaf": $count($dept.childrens) > 0 ? false : true,
        "children": (
        $map($dept.childrens, function($child) {
        $transformChildren($child)
        })[]
        )
        }
        })[]}])
        )'

  - path: /api/departments/get-by-children-with-tree
    method: GET
    model: list_by_children_with_tree
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'departments/get-by-children-with-tree'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: 'name asc'
        Search: '::{search}::'
        CompanyId: ':{companyId}:'
        Filter: '::{filter}::'
        EffectiveDate: ':{effectiveDate}:'
        Enabled: ':{status}:'
        LocationId: ':{locationId}:'
        CostCenterId: ':{costCenterId}:'
        LegalEntityCode: ':{legalEntityCode}:'
        LegalEntityCodes: '::{LegalEntityCodes}::'
        LegalEntityIds: ':{LegalEntityIds}:'
        DivisionsCodes: ':{DivisionCodes}:'
        BusinessUnitCodes: ':{BusinessUnitCodes}:'
      transform: '(  $transformChildren := function($child) {    {      "id": $child.id,      "key": $child.code,      "title": $child.name & " (" & $child.code & ")",      "isLeaf": $count($child.children) = 0,      "legalEntityCode": $child.legalEntityCode ? $child.legalEntityCode : null,      "bussinessUnitCode": $child.bussinessUnitCode ? $child.bussinessUnitCode : null,      "divisionCode": $child.divisionCode ? $child.divisionCode : null,      "children": $count($child.children) > 0 ?        $map($child.children, function($subChild) {          $transformChildren($subChild)        })[]    }  };     $merge([$, {"data": $map($, function($dept) {        $transformChildren($dept)    })[]}]))'

  - path: /api/departments/v2/get-list
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'departments/v2/get-list'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: '::{search}::'
        Filter: '::{filter}::'
        Enabled: '::{status}::'
        EffectiveDate: '::{effectiveDate}::'
        CompanyIds: '::{companyId}::'
        CompanyCodes: '::{companyCode}::'
        LegalEntityIds: '::{legalEntityId}::'
        LegalEntityCodes: '::{legalEntityCode}::'
        BusinessUnitIds: '::{businessUnitId}::'
        BusinessUnitCodes: '::{businessUnitCode}::'
        DivisionIds: '::{divisionId}::'
        DivisionCodes: '::{divisionCode}::'
      transform: '$'
  - path: /api/${{controller}}/multidelete
    method: DELETE
    model: _DELETE
    upstreamConfig:
      method: DELETE
      path:  'departments'
