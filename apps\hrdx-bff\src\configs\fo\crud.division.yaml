controller: divisions
upstream: ${{UPSTREAM_FO_URL}}

models:
  - name: _
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: count
    config:
      id:
        from: id
        type: string
      # name BFF
      code:
        # name BE
        from: code
        type: string
        typeOptions:
          func: upperCase
      IdFilter:
        from: id
      codeFilter:
        from: code
      effectiveDate:
        from: effectiveDateFrom
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      status:
        from: enabled
        type: string
        typeOptions:
          func: YNToBoolean
      na_longName:
        from: na_longName
      na_name:
        from: na_name
      na_shortName:
        from: na_shortName
      shortName:
        from: shortName
        type: string
        typeOptions:
          func: stringToMultiLang
      longName:
        from: name
        type: string
        typeOptions:
          func: stringToMultiLang
      name:
        from: name
        type: string
        typeOptions:
          func: stringToMultiLang
      parentId:
        from: parentId
        type: int
      parentCode:
        from: parentCode
      parentObject:
        from: parentObject
        objectChildren:
          id:
            from: id
          code:
            from: code
          status:
            from: enabled
            typeOptions:
              func: YNToBoolean
          effectiveDate:
            from: effectiveDate
            type: timestamp
            typeOptions:
              func: timestampToDateTime
          shortName:
            from: shortName
            type: string
            typeOptions:
              func: stringToMultiLang
          longName:
            from: name
            type: string
            typeOptions:
              func: stringToMultiLang
          name:
            from: name
            type: string
            typeOptions:
              func: stringToMultiLang
      parentObj:
        from: $
        objectChildren:
          id:
            from: parentId
          code:
            from: parentCode
      headOfDivision:
        from: headOfDivision
        type: string
      headOfDivisionData:
        from: headOfDivisionData
      deputyManagerData:
        from: deputyManagerData
      deputyManagerObj:
        from: deputyManagerObj
      headOfDivisionObj:
        from: $
        objectChildren:
          id:
            from: headOfDivision
          code:
            from: headOfDivisionCode
      businessUnitName:
        from: businessUnitName
        type: string
      businessUnitCode:
        from: businessUnitCode
      businessUnitId:
        from: businessUnitId
        type: int
      businessUnitObj:
        from: $
        objectChildren:
          id:
            from: businessUnitId
          code:
            from: businessUnitCode
      businessUnit:
        from: businessUnit
        objectChildren:
          id:
            from: id
          code:
            from: code
          status:
            from: enabled
            typeOptions:
              func: YNToBoolean
          effectiveDate:
            from: effectiveDate
            type: timestamp
            typeOptions:
              func: timestampToDateTime
          shortName:
            from: shortName
            type: string
            typeOptions:
              func: stringToMultiLang
          longName:
            from: name
            type: string
            typeOptions:
              func: stringToMultiLang
          name:
            from: name
            type: string
            typeOptions:
              func: stringToMultiLang
      companyId:
        from: companyId
        type: int
      function:
        from: function
        type: string
      orgType:
        from: orgType
        type: string
      # orgObjectType:
      #   from: orgObjectType
      #   type: string
      # orgObjectId:
      #   from: orgObjectId
      #   type: int
      # orgObjectObj:
      #   from: $
      #   objectChildren:
      #     id:
      #       from: orgObjectId
      #     code:
      #       from: orgObjectCode
      orgObjects:
        from: orgObjects
      responsibility:
        from: responsibility
        type: string
      managerType:
        from: managerType
      headOfBusinessUnit:
        from: headOfBusinessUnit
        type: string
      deputyManager:
        from: deputyManagers
      managerPosition:
        from: managerPosition
        type: string
      managerPositionObj:
        from: $
        objectChildren:
          id:
            from: managerPosition
          code:
            from: managerPositionCode
      deputyManagerPosition:
        from: deputyManagerPositions
      action:
        from: action
        type: string
      reason:
        from: reason
        type: string
      decisionNo:
        from: decisionNo
        type: string
      decisionName:
        from: decisionName
        type: string
      issueDate:
        from: issuanceDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      authorityForApproval:
        from: authorityForApproval
        type: string
      signatory:
        from: signatory
        type: string
      attachFile:
        from: attachFiles
        type: string
      file:
        from: file
      attachFileResults:
        from: attachFileResults
      createdBy:
        from: createdBy
        type: string
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
        type: string
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      Code:
        from: Code
  - name: tree_model
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: count
    config:
      id:
        from: id
        type: string
      # name BFF
      code:
        # name BE
        from: code
        type: string
        typeOptions:
          func: upperCase
      name:
        from: name
      status:
        from: enabled
        type: string
        typeOptions:
          func: YNToBoolean
      children:
        from: children
      effectiveDate:
        from: effectiveDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      parentCode:
        from: parentCode
      companyCode:
        from: companyCode
      legalEntityCode:
        from: legalEntityCode
      businessUnitCode:
        from: businessUnitCode
  - name: _DELETE
    config:
      codes:
        from: codes
      ids:
        from: ids
auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: divisions
crudConfig:
  query:
    sort:
      - field: code
        order: asc
  params:
    id:
      field: id
      type: string
      primary: true
    code:
      field: code
      type: string
  routes:
    exclude:
      - createManyBase
      - replaceOneBase
defaultQuery:

routes:
  - path: /api/divisions
    method: GET
    model: _

    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'divisions'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ":{options.sort}:"
        Search: '::{search}::'
        Filter: '::{filter}::'
      transform: '$ ~> | $.data | {"parentName":$exists($.parentId) ? $.parentObject.name.default & " ("  & $.parentObject.code & ")" : "" ,"businessUnitName": $exists($.businessUnitName) ? $.businessUnitName & " ("  & $.businessUnitCode & ")" : ""} |'

  - path: /api/divisions/:id
    method: GET
    model: _

    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'divisions/:{id}:'
      transform: '$ ~> | $ | {
      "parentObj":
        {
          "label": $exists(parentObject) ? parentObject.name.default & " (" & parentObject.code & ")",
          "value": {"id": parentObject.id, "code": parentObject.code },
          "additionalData": parentObject
        },
      "businessUnitObj": {
        "label": $exists(businessUnit) ? businessUnit.longName.default & " (" & businessUnit.code & ")",
        "value": {"id": businessUnit.id, "code": businessUnit.code },
        "additionalData": businessUnit
        },
      "headOfDivisionObj":
        {
          "label": $exists(headOfDivisionData) ?  $boolean(headOfDivisionData.userName) ? headOfDivisionData.lastName & " " & headOfDivisionData.middleName & " " & "" & headOfDivisionData.firstName & "" & "(" & headOfDivisionData.userName & ")" : headOfDivisionData.lastName & " " & headOfDivisionData.middleName & " " & "" & headOfDivisionData.firstName & "",
          "value": {"id": headOfDivisionData.employeeId, "code": headOfDivisionData.employeeId }
        },
        "orgObjects": orgObjects.{
          "id": {
            "label": $exists(modelView) ? modelView.name & " (" & modelView.code & ")",
            "value": {"id": modelView.id, "code": modelView.code },
            "additionalData": modelView
          },
          "objData": {
            "label": $exists(modelView) ? modelView.name & " (" & modelView.code & ")",
            "value": {"id": modelView.id, "code": modelView.code },
            "additionalData": modelView
          },
          "type": orgObjectType,
          "code": modelView.code
        }[],
        "deputyManagerObj": $map(deputyManagerData, function($value, $index) {
           {
              "label": $boolean($value.userName) ? $value.lastName & " " & $value.middleName & " " & "" & $value.firstName & "" & "(" & $value.userName & ")" : $value.lastName & " " & $value.middleName & " " & "" & $value.firstName & "",
              "value":{"id": $value.employeeId,
              "code": $value.employeeId}
            }
          })[]
      }|'
customRoutes:
  - path: /api/divisions/upload
    method: POST
    dataType: 'formData'
    model: _
    query:
    transform: '$'
    bodyTransform: '$merge([$,{"deputyManagers": $map(deputyManagerObj, function($value) { $exists($value.value) ? $value.value.id : $value.id}) []}])'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'divisions'
      transform: '$'

  - path: /api/divisions/insert-new-record/upload
    method: POST
    dataType: 'formData'
    model: _
    query:
    transform: '$'
    bodyTransform: '$merge([$,{"deputyManagers": $map(deputyManagerObj, function($value) { $exists($value.value) ? $value.value.id : $value.id}) []}])'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'divisions/insert-new-record'
      transform: '$'

  - path: /api/divisions/:id/upload
    model: _
    method: POST
    dataType: 'formData'
    query:
    transform: '$'
    bodyTransform: '$merge([$,{"deputyManagers": $map(deputyManagerObj, function($value) { $exists($value.value) ? $value.value.id : $value.id}) []}])'
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: 'divisions/:{id}:'

  - path: /api/divisions/:id
    method: DELETE
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'divisions/:{id}:'
  - path: /api/divisions/:id/histories
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'divisions/::{id}::/history'
      transform: '$ ~> | $ | {
        "parentObj":
          {
            "label": $exists(parentObject) ? parentObject.name.default & " (" & parentObject.code & ")",
            "value": {"id": parentObject.id, "code": parentObject.code },
            "additionalData": parentObject
          },
        "headOfDivisionObj":
        {
          "label": $exists(headOfDivisionData) ?  $boolean(headOfDivisionData.userName) ? headOfDivisionData.lastName & " " & headOfDivisionData.middleName & " " & "" & headOfDivisionData.firstName & "" & "(" & headOfDivisionData.userName & ")" : headOfDivisionData.lastName & " " & headOfDivisionData.middleName & " " & "" & headOfDivisionData.firstName & "",
          "value": {"id": headOfDivisionData.employeeId, "code": headOfDivisionData.employeeId }
        },
         "businessUnitObj": {
        "label": $exists(businessUnit) ? businessUnit.longName.default & " (" & businessUnit.code & ")",
        "value": {"id": businessUnit.id, "code": businessUnit.code },
        "additionalData": businessUnit
        },
        "orgObjects": orgObjects.{
          "id": {
            "label": $exists(modelView) ? modelView.name & " (" & modelView.code & ")",
            "value": {"id": modelView.id, "code": modelView.code },
            "additionalData": modelView
          },
          "objData": {
            "label": $exists(modelView) ? modelView.name & " (" & modelView.code & ")",
            "value": {"id": modelView.id, "code": modelView.code },
            "additionalData": modelView
          },
          "type": orgObjectType,
          "code": modelView.code
        }[],
        "deputyManagerObj": $map(deputyManagerData, function($value, $index) {
           {
              "label": $boolean($value.userName) ? $value.lastName & " " & $value.middleName & " " & "" & $value.firstName & "" & "(" & $value.userName & ")" : $value.lastName & " " & $value.middleName & " " & "" & $value.firstName & "",
              "value":{"id": $value.employeeId,
              "code": $value.employeeId}
            }
          })[]
      }|'
  - path: /api/divisions/by
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'divisions/by'
      query:
        enabled: '::{status}::'
        effectiveDate: '::{effectiveDate}::'
        code: '::{code}::'
        businessUnitId: '::{businessUnitId}::'
        companyId: '::{companyId}::'
      transform: '$'

  - path: /api/divisions/get-by
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'divisions/get-by'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: 'name asc'
        Search: '::{search}::'
        CompanyId: ':{companyId}:'
        Code: '::{Code}::'
        Filter: '::{filter}::'
        EffectiveDate: ':{effectiveDate}:'
        Enabled: '::{status}::'
      transform: '$'

  - path: /api/divisions/get-by-one-level
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'divisions/get-by-one-level'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: '::{search}::'
        Code: '::{code}::'
        Filter: '::{filter}::'
        GroupIds: ':{groupIds}:'
        CompanyIds: ':{companyIds}:'
        EffectiveDate: ':{effectiveDate}:'
        BusinessUnitIds: ':{businessUnitIds}:'
      transform: '$'

  - path: /api/divisions/import
    model: _
    method: POST
    dataType: 'formData'
    query:
    transform: '$'
    upstreamConfig:
      method: POST
      path: 'divisions/:import'

  - path: /api/divisions/export
    method: GET
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: binary
      path: 'divisions/:export'
      query:
        Page: '1'
        PageSize: '1000'
        OrderBy: 'code asc'
        Search: '::{search}::'
        Filter: '::{filter}::'
      transform: '$'
  - path: /api/divisions/insert-new-record
    method: POST
    model: _
    query:
    transform: '$'
    bodyTransform: '$merge([$,{"deputyManagers": $map(deputyManagerObj, function($value) { $exists($value.value) ? $value.value.id : $value.id}) []}])'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'divisions/insert-new-record'
      transform: '$'

  - path: /api/divisions/get-list
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'divisions/get-list'
      query:
        CompanyIds: ':{companyId}:'
        CompanyCodes: '::{companyCode}::'
        BusinessUnitIds: '::{businessUnitId}::'
        BusinessUnitCodes: '::{businessUnitCode}::'
        EffectiveDate: '::{effectiveDate}::'
        Enabled: '::{status}::'
      transform: '$'

  - path: /api/divisions/v2/get-list
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'divisions/v2/get-list'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: '::{search}::'
        Filter: '::{filter}::'
        CompanyIds: ':{companyId}:'
        CompanyCodes: '::{companyCode}::'
        BusinessUnitIds: '::{businessUnitId}::'
        BusinessUnitCodes: '::{businessUnitCode}::'
        EffectiveDate: '::{effectiveDate}::'
        Enabled: '::{status}::'
      transform: '$'

  - path: /api/divisions/v3/get-list
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'divisions/v3/get-list'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: '::{search}::'
        Filter: '::{filter}::'
        CompanyIds: ':{companyId}:'
        CompanyCodes: '::{companyCode}::'
        BusinessUnitIds: '::{businessUnitId}::'
        BusinessUnitCodes: '::{businessUnitCode}::'
        LegalEntityCodes: '::{legalEntityCode}::'
        EffectiveDate: '::{effectiveDate}::'
        Enabled: '::{status}::'
      transform: '$'

  - path: /api/divisions/get-by-children-with-tree
    method: GET
    model: tree_model
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'divisions/get-by-children-with-tree'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: 'name asc'
        Search: '::{search}::'
        CompanyIds: ':{companyId}:'
        CompanyCodes: '::{companyCode}::'
        LegalEntityCodes: '::{legalEntityCode}::'
        BusinessUnitCodes: '::{businessUnitCode}::'
        Filter: '::{filter}::'
        EffectiveDate: ':{effectiveDate}:'
        Enabled: ':{status}:'
      transform: '(  $transformChildren := function($child) {    {      "id": $child.id,      "key": $child.code,      "title": $child.name & " (" & $child.code & ")",      "isLeaf": $count($child.children) = 0,      "children": $count($child.children) > 0 ?        $map($child.children, function($subChild) {          $transformChildren($subChild)        })[]    }  };     $merge([$, {"data": $map($, function($item) {        $transformChildren($item)    })[]}]))'
  - path: /api/${{controller}}/multidelete
    method: DELETE
    model: _DELETE
    upstreamConfig:
      method: DELETE
      path: 'divisions'
