controller: groupings
upstream: ${{UPSTREAM_FO_URL}}

models:
  - name: _
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: count
    config:
      id:
        from: id
        type: string
      # name BFF
      code:
        # name BE
        from: code
        # type BE
        type: string
        typeOptions:
          func: upperCase
      effectiveDate:
        from: effectiveDateFrom
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      status:
        from: enabled
        type: string
        typeOptions:
          func: YNToBoolean
      shortName:
        from: shortName
        type: string
        typeOptions:
          func: stringToMultiLang
      longName:
        from: name
        type: string
        typeOptions:
          func: stringToMultiLang
      name:
        from: name
        type: string
        typeOptions:
          func: stringToMultiLang
      na_longName:
        from: na_longName
      na_name:
        from: na_name
      na_shortName:
        from: na_shortName
      groupName:
        from: groupName
        type: string
      companyName:
        from: companyName
        type: string
      companyCode:
        from: companyCode
      group:
        from: $
        objectChildren:
          id:
            from: groupId
          code:
            from: groupCode
      groupCode:
        from: groupCode
        type: string
      companyId:
        from: companyId
      groupId:
        from: groupId
      company:
        from: $
        objectChildren:
          id:
            from: companyId
          code:
            from: companyCode

      file:
        from: file
      createdBy:
        from: createdBy
        type: string
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
        type: string
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
  - name: _DELETE
    config:
      codes:
        from: codes
      ids:
        from: ids

auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: groupings
crudConfig:
  query:
    sort:
      - field: code
        order: asc
  params:
    id:
      field: id
      type: string
      primary: true
    code:
      field: code
      type: string
  routes:
    exclude:
      - createManyBase
      - replaceOneBase
defaultQuery:

routes:
  - path: /api/groupings
    method: GET
    model: _

    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'groupings'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ":{options.sort}:"
        Search: '::{search}::'
        Filter: '::{filter}::'
      transform: '$ ~> | $.data | {"companyName": $.companyCode ? $.companyName & " ("  & $.companyCode & ")" : "--","groupName": $.groupCode ? $.groupName & " ("  & $.groupCode & ")" : "--" } |'


  - path: /api/groupings/:id
    method: GET
    model: _

    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'groupings/:{id}:'
      transform: '$merge([$, {"applyforLevel": $boolean($.companyId) = true}])'

  - path: /api/groupings
    method: POST
    model: _

    query:
    transform: '$'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'groupings'
      transform: '$'

  - path: /api/groupings/:id
    model: _
    method: PATCH
    query:
    transform: '$'
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: 'groupings/:{id}:'

  - path: /api/groupings/:id
    method: DELETE
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'groupings/:{id}:'
customRoutes:
  - path: /api/groupings/:id/histories
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      transform: '$append([], $map($, function($item) {$merge([$item, {"applyforLevel":  $boolean($item.companyId) ? true : false}])}))'
      path: 'groupings/::{id}::/history-v2'
      query:
        groupId: '::{groupId}::'
        groupCode: '::{groupCode}::'
        companyId: '::{companyId}::'
        companyCode: '::{companyCode}::'
  - path: /api/groupings/by
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      query:
        enabled: '::{status}::'
        effectiveDate: '::{effectiveDate}::'
        code: '::{code}::'
        companyId: '::{companyId}::'
        groupId: '::{groupId}::'
      path: 'groupings/by'
      transform: '$'
  - path: /api/groupings/get-by
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'groupings/get-by'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: 'name asc'
        Search: '::{search}::'
        Filter: '::{filter}::'
        Enabled: ':{status}:'
        code: '::{code}::'
        companyId: '::{companyId}::'
        groupId: '::{groupId}::'
        EffectiveDate: ':{effectiveDate}:'
      transform: '$'
  - path: /api/groupings/import
    model: _
    method: POST
    dataType: 'formData'
    query:
    transform: '$'
    upstreamConfig:
      method: POST
      path: 'groupings/import'

  - path: /api/groupings/export
    method: GET
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: binary
      path: 'groupings:export'
      query:
        Page: '1'
        PageSize: '1000'
        OrderBy: 'code asc'
        Search: '::{search}::'
        Filter: '::{filter}::'
      transform: '$'

  - path: /api/groupings/template
    model: _
    method: GET
    query:
    transform: '$'
    upstreamConfig:
      method: GET
      response:
        dataType: binary
      path: 'groupings/template'
  - path: /api/groupings/insert-new-record
    method: POST
    model: _
    query:
    transform: '$'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'groupings/insert-new-record'
      transform: '$'
  - path: /api/${{controller}}/multidelete
    method: DELETE
    model: _DELETE
    upstreamConfig:
      method: DELETE
      path: 'groupings'
