controller: job-codes
upstream: ${{UPSTREAM_FO_URL}}

models:
  - name: _
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: count
    config:
      id:
        from: id
        type: string
      # name BFF
      code:
        # name BE
        from: code
        type: string
        typeOptions:
          func: upperCase
      effectiveDate:
        from: effectiveDateFrom
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      effectiveDatesearch:
        from: effectiveDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      jobDescription:
        from: jobDescription
        typeOptions:
          func: stringToMultiLang
      jobFamilyId:
        from: jobFamilyId
      jobFamilyObj:
        from: $
        objectChildren:
          id:
            from: jobFamilyId
          code:
            from: jobFamilyCode
      jobFamily:
        from: jobFamily
        objectChildren:
          id:
            from: id
          code:
            from: code
          status:
            from: enabled
            typeOptions:
              func: YNToBoolean
          effectiveDate:
            from: effectiveDate
            type: timestamp
            typeOptions:
              func: timestampToDateTime
          shortName:
            from: shortName
            type: string
            typeOptions:
              func: stringToMultiLang
          longName:
            from: name
            type: string
            typeOptions:
              func: stringToMultiLang
          name:
            from: name
            type: string
            typeOptions:
              func: stringToMultiLang
          description:
            from: description
            type: string
            typeOptions:
              func: stringToMultiLang
      subFamily:
        from: subFamily
        objectChildren:
          id:
            from: id
          code:
            from: code
          status:
            from: enabled
            typeOptions:
              func: YNToBoolean
          effectiveDate:
            from: effectiveDate
            type: timestamp
            typeOptions:
              func: timestampToDateTime
          shortName:
            from: shortName
            type: string
            typeOptions:
              func: stringToMultiLang
          parentJobFamily:
            from: jobFamilyName
          parentJobFamilyCode:
            from: jobFamilyCode
          longName:
            from: name
            type: string
            typeOptions:
              func: stringToMultiLang
          name:
            from: name
            type: string
            typeOptions:
              func: stringToMultiLang
          description:
            from: description
            type: string
            typeOptions:
              func: stringToMultiLang
      jobSpecialization:
        from: jobSpecialization
        objectChildren:
          id:
            from: id
          code:
            from: code
          status:
            from: enabled
            typeOptions:
              func: YNToBoolean
          effectiveDate:
            from: effectiveDate
            type: timestamp
            typeOptions:
              func: timestampToDateTime
          shortName:
            from: shortName
            type: string
            typeOptions:
              func: stringToMultiLang
          description:
            from: description
            type: string
            typeOptions:
              func: stringToMultiLang
          parentJobSubFamily:
            from: jobSubFamilyName
          parentJobSubFamilyCode:
            from: jobSubFamilyCode
          parentJobFamily:
            from: jobFamilyName
          parentJobFamilyCode:
            from: jobFamilyCode
          longName:
            from: name
            type: string
            typeOptions:
              func: stringToMultiLang
          name:
            from: name
            type: string
            typeOptions:
              func: stringToMultiLang
      careerStream:
        from: careerStream
        objectChildren:
          id:
            from: id
          code:
            from: code
          status:
            from: enabled
            typeOptions:
              func: YNToBoolean
          effectiveDate:
            from: effectiveDateFrom
            type: timestamp
            typeOptions:
              func: timestampToDateTime
          shortName:
            from: shortName
            type: string
            typeOptions:
              func: stringToMultiLang
          longName:
            from: name
            type: string
            typeOptions:
              func: stringToMultiLang
          name:
            from: name
            type: string
            typeOptions:
              func: stringToMultiLang
      band:
        from: band
        objectChildren:
          id:
            from: id
          code:
            from: code
          status:
            from: enabled
            typeOptions:
              func: YNToBoolean
          effectiveDate:
            from: effectiveDate
            type: timestamp
            typeOptions:
              func: timestampToDateTime
          parentCareerStream:
            from: careerStreamName
          parentCareerStreamCode:
            from: careerStreamCode
          shortName:
            from: shortName
            type: string
            typeOptions:
              func: stringToMultiLang
          longName:
            from: name
            type: string
            typeOptions:
              func: stringToMultiLang
          name:
            from: name
            type: string
            typeOptions:
              func: stringToMultiLang
      limit:
        from: pageSize
      page:
        from: page
      filter:
        from: filter
        typeOptions:
          func: transformQueryFilter
      search:
        from: search
      companyObj:
        from: companyObj
      isQueryEffectiveDate:
        from: isQueryEffectiveDate
      companyName:
        from: companyName
      na_longName:
        from: na_longName
      na_name:
        from: na_name
      na_shortName:
        from: na_shortName
      jobFamilyName:
        from: jobFamilyName
      jobFamilyCode:
        from: jobFamilyCode
      jobSpecializationId:
        from: jobSpecializationId
      jobSpecializationObj:
        from: $
        objectChildren:
          id:
            from: jobSpecializationId
          code:
            from: jobSpecializationCode
      jobSpecializationName:
        from: jobSpecializationName
      jobSpecializationCode:
        from: jobSpecializationCode
      subFamilyId:
        from: subFamilyId
      jobSubFamilyObj:
        from: $
        objectChildren:
          id:
            from: subFamilyId
          code:
            from: subFamilyCode
      jobSubFamilyName:
        from: subFamilyName
      jobSubFamilyCode:
        from: subFamilyCode
      careerStreamId:
        from: careerStreamId
        type: int
      careerStreamObj:
        from: $
        objectChildren:
          id:
            from: careerStreamId
          code:
            from: careerStreamCode
      careerStreamName:
        from: careerStreamName
      careerStreamCode:
        from: careerStreamCode
      careerStream:
        from: careerStream
      bandObj:
        from: $
        objectChildren:
          id:
            from: bandId
          code:
            from: bandCode
      bandId:
        from: bandId
        type: int
      bandName:
        from: bandName
      bandCode:
        from: bandCode
      levelId:
        from: levelId
        type: int
      packageCode:
        from: packkageCodeId
        type: int
      companyDisplay:
        from: company
        arrayChildren:
          name:
            from: companyName
      company:
        from: company
      companyCode:
        from: companyCode
      companyId:
        from: companyId
      regularTemporary:
        from: regularOrTemporary
        type: string
      standardWeeklyHours:
        from: standardWeeklyHours
        type: int
      status:
        from: enabled
        type: string
        typeOptions:
          func: YNToBoolean
      keyJobCode:
        from: keyCode
        type: string
        typeOptions:
          func: YNToBoolean
      shortName:
        from: shortName
        type: string
        typeOptions:
          func: stringToMultiLang
      longName:
        from: name
        type: string
        typeOptions:
          func: stringToMultiLang
      name:
        from: name
        type: string
        typeOptions:
          func: stringToMultiLang
      file:
        from: file
      createdBy:
        from: createdBy
        type: string
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
        type: string
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      CompanyIds:
        from: CompanyIds
  - name: _DELETE
    config:
      codes:
        from: codes
      ids:
        from: ids
auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: job-codes
crudConfig:
  query:
    sort:
      - field: code
        order: asc
  params:
    id:
      field: id
      type: string
      primary: true
    code:
      field: code
      type: string
  routes:
    exclude:
      - createManyBase
      - replaceOneBase
defaultQuery:

routes:
  - path: /api/job-codes
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
        dataKey: data
      path: 'job-codes'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ":{options.sort}:"
        CompanyIds: '::{CompanyIds}::'
        Search: '::{search}::'

        Filter: '::{filter}::'
      transform: '$ ~> | $.data | {"companyDisplay": $map(company, function($value, $index) {
              {
                "name": $value.companyName & " (" & $value.companyCode & ")"
              }
            })[], "jobFamilyName": $exists($.jobFamilyName) ? $.jobFamilyName & " ("  & $.jobFamilyCode & ")" : "","jobSubFamilyName": $exists($.jobSubFamilyName) ? $.jobSubFamilyName & " ("  & $.jobSubFamilyCode & ")" : "","jobSpecializationName": $exists($.jobSpecializationName) ? $.jobSpecializationName & " ("  & $.jobSpecializationCode & ")" : "","careerStreamName": $exists($.careerStreamName) ? $.careerStreamName & " ("  & $.careerStreamCode & ")" : "","bandName": $exists($.bandName) ? $.bandName & " ("  & $.bandCode & ")" : ""}|'

  - path: /api/job-codes/:id
    method: GET
    model: _

    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'job-codes/:{id}:'
      transform: '$ ~> | $ | {
        "jobFamilyObj": jobFamily ? {
              "label": jobFamily.name.default & " (" & jobFamily.code & ")",
              "value": {"id": jobFamily.id, "code": jobFamily.code },
              "additionalData": jobFamily
            } : null,
          "jobSubFamilyObj": subFamily ? {
              "label": subFamily.name.default & " (" & subFamily.code & ")",
              "value": {"id": subFamily.id, "code": subFamily.code },
                "additionalData": subFamily
            } : null,
          "jobSpecializationObj": jobSpecialization ? {
              "label": jobSpecialization.name.default & " (" & jobSpecialization.code & ")",
              "value": {"id": jobSpecialization.id, "code": jobSpecialization.code },
              "additionalData": jobSpecialization
            } : null,
          "careerStreamObj": careerStream ? {
              "label": careerStream.name.default & " (" & careerStream.code & ")",
              "value": {"id": careerStream.id, "code": careerStream.code },
              "additionalData": careerStream
            } : null,
            "bandObj": band ? {
              "label": band.name.default & " (" & band.code & ")",
              "value": {"id": band.id, "code": band.code },
              "additionalData": band
              } : null,
        "companyObj": $map(companyCode, function($value, $index) {
              {
                  "label": companyName[$index] & " (" & $value & ")",
                  "value": {"id": companyId[$index],
                  "code": $value}
                }
              })[]
        } |'

  - path: /api/job-codes
    method: POST
    model: _
    query:
    transform: '$'
    bodyTransform: '$merge([$,{"companyId": $map(companyObj, function($value) { $exists($value.value) ? $value.value.id : $value.id}) []}])'

    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'job-codes'
      transform: '$'

  - path: /api/job-codes/:id
    model: _
    method: PATCH
    query:
    transform: '$'
    bodyTransform: '$merge([$,{"companyId": $map(companyObj, function($value) { $exists($value.value) ? $value.value.id : $value.id}) []}])'
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: 'job-codes/:{id}:'

  - path: /api/job-codes/:id
    method: DELETE
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'job-codes/:{id}:'
customRoutes:
  - path: /api/job-codes/:id/histories
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'job-codes/::{id}::/history'
      transform: '$ ~> | $ | {
          "keyJobCode":  $exists($.keyJobCode) ? $.keyJobCode = true ? true : false : false,
          "jobFamilyObj": jobFamily ? {
              "label": jobFamily.name.default & " (" & jobFamily.code & ")",
              "value": {"id": jobFamily.id, "code": jobFamily.code },
              "additionalData": jobFamily
            } : null,
          "jobSubFamilyObj": subFamily ? {
              "label": subFamily.name.default & " (" & subFamily.code & ")",
              "value": {"id": subFamily.id, "code": subFamily.code },
              "additionalData": subFamily
            } : null,
          "jobSpecializationObj": jobSpecialization ? {
              "label": jobSpecialization.name.default & " (" & jobSpecialization.code & ")",
              "value": {"id": jobSpecialization.id, "code": jobSpecialization.code },
              "additionalData": jobSpecialization
            } : null,
          "careerStreamObj": careerStream ? {
              "label": careerStream.name.default & " (" & careerStream.code & ")",
              "value": {"id": careerStream.id, "code": careerStream.code },
              "additionalData": careerStream
            } : null,
            "bandObj": band ? {
              "label": band.name.default & " (" & band.code & ")",
              "value": {"id": band.id, "code": band.code },
              "additionalData": band
              } : null,
          "companyObj": $map(companyCode, function($value, $index) {
              {
                  "label": companyName[$index] & " (" & $value & ")",
                  "value": {"id": companyId[$index],
                  "code": $value}
                }
              })[]
          } |'
  - path: /api/job-codes/by
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      query:
        Enabled: '::{status}::'
        EffectiveDate: '::{effectiveDate}::'
        Code: '::{code}::'
      path: 'job-codes/by'
      transform: '$'

  - path: /api/job-codes/get-by
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'job-codes/get-by'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: 'name asc'
        Search: '::{search}::'
        Filter: '::{filter}::'
        isQueryEffectiveDate: ':{isQueryEffectiveDate}:'
        CompanyIds: ':{CompanyIds}:'
        CompanyCodes: ':{companyCode}:'
        EffectiveDate: '::{effectiveDate}::'
        Enabled: ':{status}:'
      transform: '$'
  - path: /api/job-codes/get-by-post
    method: POST
    model: _
    query:
    dataType: object
    transform: '$'
    upstreamConfig:
      method: POST
      response:
        dataType: paginated
      path: 'job-codes/get-by'
      transform: '$'

  - path: /api/job-codes/get-dropdown-list-by
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'job-codes/get-dropdown-list-by'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: 'name asc'
        Search: '::{search}::'
        Filter: '::{filter}::'
        CompanyIds: ':{CompanyIds}:'
        CompanyCodes: ':{companyCode}:'
        EffectiveDate: '::{effectiveDate}::'
        Enabled: ':{status}:'
      transform: '$'

  - path: /api/job-codes/get-list
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'job-codes/get-list'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: '::{search}::'
        Filter: '::{filter}::'
        CompanyCodes: '::{companyCode}::'
      transform: '$'

  - path: /api/job-codes/import
    model: _
    method: POST
    dataType: 'formData'
    query:
    transform: '$'
    upstreamConfig:
      method: POST
      path: 'job-codes/:import'

  - path: /api/job-codes/export
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: binary
      path: 'job-codes/:export'
      query:
        Page: '1'
        PageSize: '1000'
        OrderBy: 'code asc'
        Search: ':{search}:'
        Filter: '::{filter}::'
        CompanyIds: '::{CompanyIds}::'
      transform: '$'

  - path: /api/job-codes/template
    model: _
    method: GET
    query:
    transform: '$'
    upstreamConfig:
      method: GET
      response:
        dataType: binary
      path: 'job-codes/template'
  - path: /api/job-codes/insert-new-record
    method: POST
    model: _
    query:
    transform: '$'
    bodyTransform: '$merge([$,{"companyId": $map(companyObj, function($value) { $exists($value.value) ? $value.value.id : $value.id}) []}])'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'job-codes/insert-new-record'
      transform: '$'
  - path: /api/${{controller}}/multidelete
    method: DELETE
    model: _DELETE
    upstreamConfig:
      method: DELETE
      path: 'job-codes'
