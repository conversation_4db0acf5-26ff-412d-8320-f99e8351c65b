controller: legal-entities
upstream: ${{UPSTREAM_FO_URL}}

models:
  - name: _
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: count
    config:
      id:
        from: id
        type: string
      IdFilter:
        from: id
      CodeFilter:
        from: code
      # name BFF
      code:
        # name BE
        from: code
        type: string
        typeOptions:
          func: upperCase
      effectiveDate:
        from: effectiveDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      fromDate:
        from: fromDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      toDate:
        from: toDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      status:
        from: enabled
        type: string
        typeOptions:
          func: YNToBoolean
      na_longName:
        from: na_longName
      na_name:
        from: na_name
      na_shortName:
        from: na_shortName
      shortName:
        from: shortName
        type: string
        typeOptions:
          func: stringToMultiLang
      longName:
        from: name
        type: string
        typeOptions:
          func: stringToMultiLang
      name:
        from: name
        type: string
        typeOptions:
          func: stringToMultiLang
      companyName:
        from: company.name
      companyCode:
        from: companyCode
      company:
        from: company
        objectChildren:
          id:
            from: id
          code:
            from: code
          status:
            from: enabled
            typeOptions:
              func: YNToBoolean
          effectiveDate:
            from: effectiveDate
            type: timestamp
            typeOptions:
              func: timestampToDateTime
          shortName:
            from: shortName
            type: string
            typeOptions:
              func: stringToMultiLang
          longName:
            from: name
            type: string
            typeOptions:
              func: stringToMultiLang
          name:
            from: name
            type: string
            typeOptions:
              func: stringToMultiLang
      locationId:
        from: locationId
      location:
        from: location
        objectChildren:
          id:
            from: id
          code:
            from: code
          status:
            from: enabled
            typeOptions:
              func: YNToBoolean
          effectiveDate:
            from: effectiveDateFrom
            type: timestamp
            typeOptions:
              func: timestampToDateTime
          shortName:
            from: shortName
            type: string
            typeOptions:
              func: stringToMultiLang
          longName:
            from: name
            type: string
            typeOptions:
              func: stringToMultiLang
          name:
            from: name
            type: string
            typeOptions:
              func: stringToMultiLang
      locationObj:
        from: $
        objectChildren:
          id:
            from: locationId
          code:
            from: locationCode
      locationCode:
        from: locationCode
      charterCapital:
        from: charterCapital
        type: string
      # orgObjectType:
      #   from: orgObjectType
      #   type: string
      # orgObjectId:
      #   from: orgObjectId
      #   type: int
      # orgObjectObj:
      #   from: $
      #   objectChildren:
      #     id:
      #       from: orgObjectId
      #     code:
      #       from: orgObjectCode
      companyId:
        from: companyId
        type: int
      companyObj:
        from: $
        objectChildren:
          id:
            from: companyId
          code:
            from: companyCode
      CompanyIds:
        from: CompanyIds
      parentLegalEntityId:
        from: parentLegalEntityId
        type: int
      parentLegalEntityCode:
        from: parentLegalEntityCode
      parentLegalEntity:
        from: parentLegalEntity
        objectChildren:
          id:
            from: id
          code:
            from: code
          status:
            from: enabled
            typeOptions:
              func: YNToBoolean
          effectiveDate:
            from: effectiveDateFrom
            type: timestamp
            typeOptions:
              func: timestampToDateTime
          shortName:
            from: shortName
            type: string
            typeOptions:
              func: stringToMultiLang
          longName:
            from: name
            type: string
            typeOptions:
              func: stringToMultiLang
          name:
            from: name
            type: string
            typeOptions:
              func: stringToMultiLang
      parentLegalEntitys:
        from: $
        objectChildren:
          id:
            from: parentLegalEntityId
          code:
            from: parentLegalEntityCode
      orgObjects:
        from: orgObjects
      managerType:
        from: managerType
      headOfLegalEntity:
        from: headOfLegalEntity
      headOfLegalEntityData:
        from: headOfLegalEntityData
      headOfLegalEntityObj:
        from: $
        objectChildren:
          id:
            from: headOfLegalEntity
          code:
            from: headOfLegalEntityCode
      managerPositionId:
        from: managerPositionId
        type: int
      managerPositionObj:
        from: $
        objectChildren:
          id:
            from: managerPositionId
          code:
            from: managerPositionCode
      managerPosition:
        from: managerPosition
        type: string
      action:
        from: action
        type: string
      reason:
        from: reason
        type: string
      decisionNo:
        from: decisionNumber
        type: string
      decisionName:
        from: nameOfDecision
        type: string
      issueDate:
        from: publishDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      authorityForApproval:
        from: authorityForApproval
        type: int
      signatory:
        from: signatory
        type: string
      attachFile:
        from: attachFiles
        type: string
      emailAddress:
        from: emailAddress
      telephone:
        from: telephone
      taxCode:
        from: taxCode
      tax:
        from: tax
      fax:
        from: fax
      file:
        from: file
      attachFileResults:
        from: attachFileResults
      createdBy:
        from: createdBy
        type: string
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
        type: string
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime

  - name: _legalEntiyBusinessTaxCode
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: count
    config:
      code:
        from: code
      name:
        from: name
      effectiveDate:
        from: effectiveDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      legalEntityId:
        from: legalEntityId
      taxCode:
        from: taxCode
      legalEntityCode:
        from: legalEntityCode
      legalEntityName:
        from: legalEntityName
      shortName:
        from: shortName

  - name: tree_model
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: count
    config:
      id:
        from: id
        type: string
      # name BFF
      code:
        # name BE
        from: code
        type: string
        typeOptions:
          func: upperCase
      name:
        from: name
      status:
        from: enabled
        type: string
        typeOptions:
          func: YNToBoolean
      children:
        from: children
      effectiveDate:
        from: effectiveDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      parentLegalEntityCode:
        from: parentLegalEntityCode
      parentLegalEntityId:
        from: parentLegalEntityId
      companyCode:
        from: companyCode
  - name: _DELETE
    config:
      codes:
        from: codes
      ids:
        from: ids

auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: legal-entities
crudConfig:
  query:
    sort:
      - field: code
        order: asc
  params:
    id:
      field: id
      type: string
      primary: true
    code:
      field: code
      type: string
  routes:
    exclude:
      - createManyBase
      - replaceOneBase
defaultQuery:

routes:
  - path: /api/legal-entities
    method: GET
    model: _

    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'legal-entities'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ":{options.sort}:"
        Search: '::{search}::'
        Filter: '::{filter}::'
      transform: '$ ~> | $.data | {"parentName": $exists($.parentLegalEntity) ? $.parentLegalEntity.name.default & " ("  & $.parentLegalEntity.code & ")" : "","companyName": $exists($.company) ? $.company.name.default & " ("  & $.company.code & ")" : ""} |'

  - path: /api/legal-entities/:id
    method: GET
    model: _

    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'legal-entities/:{id}:'
      transform: '$ ~> | $ | {
        "parentLegalEntitys":
          {
            "label": $exists(parentLegalEntity) ? parentLegalEntity.name.default & " (" & parentLegalEntity.code & ")",
            "value": {"id":parentLegalEntity.id, "code": parentLegalEntity.code },
            "additionalData": parentLegalEntity
          },
          "companyObj":
          {
            "label": $exists(company) ? company.name.default & " (" & company.code & ")",
            "value": {"id":company.id, "code": company.code },
            "additionalData": company
          },
          "headOfLegalEntityObj":
        {
                    "label": $exists(headOfLegalEntityData) ?  $boolean(headOfLegalEntityData.userName) ? headOfLegalEntityData.lastName & " " & headOfLegalEntityData.middleName & " " & "" & headOfLegalEntityData.firstName & "" & "(" & headOfLegalEntityData.userName & ")" : headOfLegalEntityData.lastName & " " & headOfLegalEntityData.middleName & " " & "" & headOfLegalEntityData.firstName & "",
          "value": {"id": headOfLegalEntityData.employeeId, "code": headOfLegalEntityData.employeeId }
        },
          "locationObj": $exists(location) ? {
            "label": $exists(location) ? location.name.default & " (" & location.code & ")",
            "value": $exists(location) ? {"id": location.id, "code": location.code } : null,
            "additionalData": location
          } : null,
          "orgObjects": orgObjects.{
          "id": {
            "label": $exists(modelView) ? modelView.name & " (" & modelView.code & ")",
            "value": {"id": modelView.id, "code": modelView.code },
            "additionalData": modelView
          },
          "objData": {
            "label": $exists(modelView) ? modelView.name & " (" & modelView.code & ")",
            "value": {"id": modelView.id, "code": modelView.code },
            "additionalData": modelView
          },
          "type": orgObjectType,
          "code": modelView.code
        }[]
        }|'

  - path: /api/legal-entities
    method: POST
    model: _

    query:
    transform: '$'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'legal-entities'
      transform: '$'

  - path: /api/legal-entities/:id
    model: _
    method: PATCH
    query:
    transform: '$'
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: 'legal-entities/:{id}:'

  - path: /api/legal-entities/:id
    method: DELETE
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'legal-entities/:{id}:'
customRoutes:
  - path: /api/legal-entities/upload
    method: POST
    dataType: 'formData'
    model: _
    query:
    transform: '$'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'legal-entities'
      transform: '$'

  - path: /api/legal-entities/insert-new-record/upload
    method: POST
    dataType: 'formData'
    model: _
    query:
    transform: '$'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'legal-entities/insert-new-record'
      transform: '$'

  - path: /api/legal-entities/:id/upload
    model: _
    method: POST
    dataType: 'formData'
    query:
    transform: '$'
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: 'legal-entities/:{id}:'
  - path: /api/legal-entities/:id/histories
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'legal-entities/::{id}::/history'
      transform: '$ ~> | $ | {
        "parentLegalEntitys":
          {
            "label": $exists(parentLegalEntity) ? parentLegalEntity.name.default & " (" & parentLegalEntity.code & ")",
            "value": {"id":parentLegalEntity.id, "code": parentLegalEntity.code },
            "additionalData": parentLegalEntity
          },
          "companyObj":
          {
            "label": $exists(company) ? company.name.default & " (" & company.code & ")",
            "value": {"id":company.id, "code": company.code },
            "additionalData": company
          },
          "headOfLegalEntityObj":
        {
                    "label": $exists(headOfLegalEntityData) ?  $boolean(headOfLegalEntityData.userName) ? headOfLegalEntityData.lastName & " " & headOfLegalEntityData.middleName & " " & "" & headOfLegalEntityData.firstName & "" & "(" & headOfLegalEntityData.userName & ")" : headOfLegalEntityData.lastName & " " & headOfLegalEntityData.middleName & " " & "" & headOfLegalEntityData.firstName & "",
          "value": {"id": headOfLegalEntityData.employeeId, "code": headOfLegalEntityData.employeeId }
        },
          "locationObj": $exists(location) ? {
            "label": $exists(location) ? location.name.default & " (" & location.code & ")",
            "value": {"id": location.id, "code": location.code },
            "additionalData": location
          } : null,
          "orgObjects": orgObjects.{
          "id": {
            "label": $exists(modelView) ? modelView.name & " (" & modelView.code & ")",
            "value": {"id": modelView.id, "code": modelView.code },
            "additionalData": modelView
          },
          "objData": {
            "label": $exists(modelView) ? modelView.name & " (" & modelView.code & ")",
            "value": {"id": modelView.id, "code": modelView.code },
            "additionalData": modelView
          },
          "type": orgObjectType,
          "code": modelView.code
        }[]
        }|'
  - path: /api/legal-entities/by
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'legal-entities/by'
      query:
        Enabled: '::{status}::'
        EffectiveDate: '::{effectiveDate}::'
        Code: '::{code}::'
      transform: '$'

  - path: /api/legal-entities/get-by
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'legal-entities/get-by'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: 'name asc'
        Search: '::{search}::'
        Filter: '::{filter}::'
        CompanyIds: ':{CompanyIds}:'
        CompanyCodes: '::{companyCodes}::'
        Enabled: ':{status}:'
        Code: '::{code}::'
        EffectiveDate: '::{effectiveDate}::'
      transform: '$'

  - path: /api/legal-entities/get-by-one-level
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'legal-entities/get-by-one-level'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: '::{search}::'
        Filter: '::{filter}::'
        GroupIds: ':{groupIds}:'
        CompanyCodes: ':{companyCodes}:'
        Enabled: ':{status}:'
        EffectiveDate: '::{effectiveDate}::'
      transform: '$'

  - path: /api/legal-entities/import
    model: _
    method: POST
    dataType: 'formData'
    query:
    transform: '$'
    upstreamConfig:
      method: POST
      path: 'legal-entities/:import'

  - path: /api/legal-entities/export
    method: GET
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: binary
      path: 'legal-entities/:export'
      query:
        Page: '1'
        PageSize: '1000'
        OrderBy: 'code asc'
        Search: '::{search}::'
        Filter: '::{filter}::'
      transform: '$'
  - path: /api/legal-entities/insert-new-record
    method: POST
    model: _
    query:
    transform: '$'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'legal-entities/insert-new-record'
      transform: '$'

  - path: /api/legal-entities/get-list
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'legal-entities/get-list'
      query:
        Enabled: '::{status}::'
        EffectiveDate: '::{effectiveDate}::'
        CompanyIds: '::{companyId}::'
        CompanyCodes: '::{companyCode}::'
      transform: '$'

  - path: /api/legal-entities/get-by-filter
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'legal-entities/get-by-filter'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: 'name asc'
        Search: '::{search}::'
        Filter: '::{filter}::'
        CompanyIds: ':{CompanyIds}:'
        CompanyCodes: '::{companyCodes}::'
        Enabled: ':{status}:'
        # Code: '::{code}::'
        FromDate: '::{fromDate}::'
        ToDate: '::{toDate}::'
      transform: '$'

  - path: /api/legal-entities/v2/get-list
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'legal-entities/v2/get-list'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: '::{search}::'
        Filter: '::{filter}::'
        Enabled: '::{status}::'
        EffectiveDate: '::{effectiveDate}::'
        CompanyIds: '::{companyId}::'
        CompanyCodes: '::{companyCode}::'
      transform: '$'

  - path: /api/legal-entities/get-by-business-tax-code
    method: GET
    model: _legalEntiyBusinessTaxCode
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'legal-entities/get-by-business-tax-code'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: '::{search}::'
        Filter: '::{filter}::'
        Enabled: '::{status}::'
        EffectiveDate: '::{effectiveDate}::'
      transform: '$'

  - path: /api/legal-entities/get-by-children-with-tree
    method: GET
    model: tree_model
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'legal-entities/get-by-children-with-tree'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: 'name asc'
        Search: '::{search}::'
        Filter: '::{filter}::'
        CompanyIds: ':{companyId}:'
        CompanyCodes: ':{companyCode}:'
        EffectiveDate: ':{effectiveDate}:'
        Enabled: ':{status}:'
      transform: '(  $transformChildren := function($child) {    {      "id": $child.id,      "key": $child.code,      "title": $child.name & " (" & $child.code & ")",      "isLeaf": $count($child.children) = 0,      "children": $count($child.children) > 0 ?        $map($child.children, function($subChild) {          $transformChildren($subChild)        })[]    }  };     $merge([$, {"data": $map($, function($item) {        $transformChildren($item)    })[]}]))'
  - path: /api/${{controller}}/multidelete
    method: DELETE
    model: _DELETE
    upstreamConfig:
      method: DELETE
      path: 'legal-entities'
