controller: sub-groupings
upstream: ${{UPSTREAM_FO_URL}}

models:
  - name: _
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: count
    config:
      id:
        from: id
        type: string
      # name BFF
      code:
        # name BE
        from: code
        # type BE
        type: string
        typeOptions:
          func: upperCase
      effectiveDate:
        from: effectiveDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      status:
        from: enabled
        type: string
        typeOptions:
          func: YNToBoolean

      shortName:
        from: shortName
        type: string
        typeOptions:
          func: stringToMultiLang
      longName:
        from: name
        type: string
        typeOptions:
          func: stringToMultiLang
      name:
        from: name
        type: string
        typeOptions:
          func: stringToMultiLang
      na_longName:
        from: na_longName
      na_name:
        from: na_name
      na_shortName:
        from: na_shortName
      companyName:
        from: companyName
        type: string
      parentJobGroup:
        from: groupingName
        type: string
      groupName:
        from: groupName
        type: string
      companyId:
        from: companyId
      companyObj:
        from: $
        objectChildren:
          id:
            from: companyId
          code:
            from: companyCode
      companyCode:
        from: companyCode
      groupCode:
        from: groupCode
      groupId:
        from: groupId
      group:
        from: group
        objectChildren:
          id:
            from: id
          code:
            from: code
          status:
            from: enabled
            typeOptions:
              func: YNToBoolean
          effectiveDate:
            from: effectiveDate
            type: timestamp
            typeOptions:
              func: timestampToDateTime
          shortName:
            from: shortName
            type: string
            typeOptions:
              func: stringToMultiLang
          longName:
            from: name
            type: string
            typeOptions:
              func: stringToMultiLang
          name:
            from: name
            type: string
            typeOptions:
              func: stringToMultiLang
      company:
        from: company
        objectChildren:
          id:
            from: id
          code:
            from: code
          status:
            from: enabled
            typeOptions:
              func: YNToBoolean
          effectiveDate:
            from: effectiveDate
            type: timestamp
            typeOptions:
              func: timestampToDateTime
          shortName:
            from: shortName
            type: string
            typeOptions:
              func: stringToMultiLang
          longName:
            from: name
            type: string
            typeOptions:
              func: stringToMultiLang
      groupObj:
        from: $
        objectChildren:
          id:
            from: groupId
          code:
            from: groupCode
      jobCodeCodes:
        from: jobCodeCodes
      groupingId:
        from: groupingId
      groupingName:
        from: groupingName
      grouping:
        from: $
        objectChildren:
          id:
            from: groupingId
          code:
            from: groupingCode
      groupingCode:
        from: groupingCode
      jobCodeIds:
        from: jobCodeIds
      jobCodeObj:
        from: jobCodeObj
      jobCodeNames:
        from: jobCodeNames
      file:
        from: file
      createdBy:
        from: createdBy
        type: string
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
        type: string
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
  - name: _DELETE
    config:
      codes:
        from: codes
      ids:
        from: ids
auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: sub-groupings
crudConfig:
  query:
    sort:
      - field: code
        order: asc
  params:
    id:
      field: id
      type: string
      primary: true
    code:
      field: code
      type: string
  routes:
    exclude:
      - createManyBase
      - replaceOneBase
defaultQuery:

routes:
  - path: /api/sub-groupings
    method: GET
    model: _

    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'sub-groupings'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ":{options.sort}:"
        Search: '::{search}::'
        Filter: '::{filter}::'
      transform: '$ ~> | $.data | {"companyName": $boolean($.companyId) ? $.companyName & " ("  & $.companyCode & ")" : "","groupName": $boolean($.groupId) ? $.groupName & " ("  & $.groupCode & ")" : "","parentJobGroup": $boolean($.groupingId) ? $.parentJobGroup & " ("  & $.groupingCode & ")" : "" } |'

  - path: /api/sub-groupings/:id
    method: GET
    model: _

    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'sub-groupings/:{id}:'
      transform: '$ ~> | $ |
        {
          "applyforLevel": $boolean($.companyId) = true,
          "jobCodeObj": $map(jobCodeIds, function($value, $index) {
            {
              "label": jobCodeNames[$index] & " (" & jobCodeCodes[$index] & ")",
              "value":{"id": $value,
              "code": jobCodeCodes[$index]}
            }
          })[],
          "groupObj":{
              "label": groupName & " (" & groupCode & ")",
              "value":{"id": groupId,
              "code": groupCode},
              "additionalData": group
          },
          "grouping":{
            "label": groupingName & " (" & groupingCode & ")",
            "value":{"id": groupingId,
              "code": groupingCode}
          },
          "companyObj":{
              "label": companyName & " (" & companyCode & ")",
              "value":{"id": companyId,
              "code": companyCode},
              "additionalData": company
          }
        } |
        '

  - path: /api/sub-groupings
    method: POST
    model: _
    query:
    transform: '$'
    bodyTransform: '$merge([$,{"jobCodeIds": $map(jobCodeObj, function($value) { $exists($value.value) ? $value.value.id : $value.id}) []}])'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'sub-groupings'
      transform: '$'

  - path: /api/sub-groupings/:id
    model: _
    method: PATCH
    query:
    transform: '$'
    bodyTransform: '$merge([$,{"jobCodeIds": $map(jobCodeObj, function($value) { $exists($value.value) ? $value.value.id : $value.id}) []}])'
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: 'sub-groupings/:{id}:'
      transform: '$'

  - path: /api/sub-groupings/:id
    method: DELETE
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'sub-groupings/:{id}:'
customRoutes:
  - path: /api/sub-groupings/:id/histories
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'sub-groupings/::{id}::/history-v2'
      query:
        groupId: '::{groupId}::'
        groupCode: '::{groupCode}::'
        companyId: '::{companyId}::'
        companyCode: '::{companyCode}::'
      transform: '$ ~> | $ |
        {
          "applyforLevel": $boolean($.companyId) = true,
          "jobCodeObj": $map(jobCodeIds, function($value, $index) {
            {
              "label": jobCodeNames[$index] & " (" & jobCodeCodes[$index] & ")",
              "value":{"id": $value,
              "code": jobCodeCodes[$index]}
            }
          })[],
          "groupObj":{
              "label": groupName & " (" & groupCode & ")",
              "value":{"id": groupId,
              "code": groupCode}
          },
          "grouping":{
            "label": groupingName & " (" & groupingCode & ")",
            "value":{"id": groupingId,
              "code": groupingCode}
          },
          "companyObj":{
              "label": companyName & " (" & companyCode & ")",
              "value":{"id": companyId,
              "code": companyCode}
          }
        } |'
  - path: /api/sub-groupings/by
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      query:
        enabled: '::{status}::'
        effectiveDate: '::{effectiveDate}::'
        code: '::{code}::'
      path: 'sub-groupings/by'
      transform: '$'
  - path: /api/sub-groupings/get-by
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'sub-groupings/get-by'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: 'name asc'
        Search: '::{search}::'
        Filter: '::{filter}::'
        Enabled: ':{status}:'
        code: '::{code}::'
        EffectiveDate: ':{effectiveDate}:'
      transform: '$'
  - path: /api/sub-groupings/import
    model: _
    method: POST
    dataType: 'formData'
    query:
    transform: '$'
    upstreamConfig:
      method: POST
      path: 'sub-groupings/import'

  - path: /api/sub-groupings/export
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: binary
      path: 'sub-groupings:export'
      query:
        Page: '1'
        PageSize: '1000'
        OrderBy: 'code asc'
        Search: '::{search}::'
        Filter: '::{filter}::'
      transform: '$'
  - path: /api/sub-groupings/insert-new-record
    method: POST
    model: _
    query:
    transform: '$'
    bodyTransform: '$merge([$,{"jobCodeIds": $map(jobCodeObj, function($value) { $exists($value.value) ? $value.value.id : $value.id}) []}])'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'sub-groupings/insert-new-record'
      transform: '$'
  - path: /api/sub-groupings/requirement
    method: GET
    model: _
    query:
    transform: '$'
    upstreamConfig:
      method: GET
      response:
        dataType: boolean
      path: 'requirement?ppxFunctionCode=da3bc0d2d5754285bc5b'
      transform: '$'
  - path: /api/${{controller}}/multidelete
    method: DELETE
    model: _DELETE
    upstreamConfig:
      method: DELETE
      path: 'sub-groupings'
