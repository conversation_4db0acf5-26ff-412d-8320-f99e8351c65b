controller: trees
upstream: ${{UPSTREAM_FO_URL}}

models:
  - name: _structureType
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: totalPages
    config:
      id:
        from: id
        type: string
      code:
        from: code
        type: string
      name:
        from: name
        type: string
      config:
        from: config
  - name: _organization
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: totalPages
    config:
      id:
        from: id
        type: string
      code:
        from: code
        type: string
      shortName:
        from: shortName
        type: string
      longName:
        from: longName
        type: string
      organizationType:
        from: organizationType
      effectiveDate:
        from: effectiveDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
  - name: _organizationTree
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: totalPages
    config:
      id:
        from: id
        type: string
      directPositionId:
        from: parentId
        type: string
      shortName:
        from: shortName
        type: string
      longName:
        from: longName
        type: string
      effectiveDate:
        from: effectiveDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      code:
        from: code
        type: string
      location:
        from: location
        type: string
      type:
        from: type
        type: string
      level:
        from: level
        type: string
      countChild:
        from: countChild
      color:
        from: color
        type: string
  - name: _organizationDetail
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: totalPages
    config:
      id:
        from: id
        type: string
      shortName:
        from: shortName
        type: string
        typeOptions:
          func: stringToMultiLang
      longName:
        from: longName
        type: string
        typeOptions:
          func: stringToMultiLang
      effectiveDate:
        from: effectiveDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      status:
        from: enabled
        type: string
        typeOptions:
          func: YNToBoolean
      location:
        from: location
        type: string
      locationCode:
        from: locationCode
      region:
        from: region
        type: string
      locationCountry:
        from: locationCountry
        type: string
      employeeCount:
        from: employeeCount
      termOfOffice:
        from: termOfOffice
  - name: _organizationUser
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: totalPages
    config:
      effectiveDate:
        from: effectiveDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      termOfOffice:
        from: termOfOffice
        type: string
      managerType:
        from: managerType
        type: string
      positionCode:
        from: positionCode
        type: string
      account:
        from: account
      email:
        from: email
      fullName:
        from: fullName
      appointmentDate:
        from: appointmentDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      jobCode:
        from: jobCode
      jobName:
        from: jobName
      jobTitle:
        from: jobTitle
      employeeId:
        from: employeeId
      avatarFile:
        from: avatarFile
  - name: _organizationPick
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: totalPages
    config:
      id:
        from: id
        type: string
      name:
        from: name
        type: string
      code:
        from: code
        type: string
      effectiveDate:
        from: effective
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      type:
        from: type
        type: string

auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: trees
crudConfig:
  query:
    sort:
      - field: code
        order: DESC
  params:
    id:
      field: id
      type: string
      primary: true
    code:
      field: code
      type: string
    structureType:
      field: structureType
      type: string
    organizationType:
      field: organizationType
      type: string
    organizationId:
      field: organizationId
      type: string
    pickType:
      field: pickType
      type: string
    effectiveDate:
      field: effectiveDate
      type: string
      typeOptions:
        func: timestampToDateTime
  routes:
    exclude:
      - createManyBase
      - replaceOneBase
defaultQuery:

routes:

customRoutes:
  - path: /api/trees/organization/:code
    method: GET
    model: _organization
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'trees/organization'
      query:
        page: ':{page}:'
        code: ':{code}:'
        search: '::{search}::'
        effectiveDate: ':{effectiveDate}:'
        OrderBy : ':{OrderBy }:'
      transform: '$'
  - path: /api/trees/organization/:structureType/:code/:organizationType
    method: GET
    model: _organizationTree
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'trees/organization/:{structureType}:/:{code}:/:{organizationType}:'
      query:
        effectiveDate: ':{effectiveDate}:'
      transform: '$'
  - path: /api/trees/organization/:organizationId/users
    method: GET
    model: _organizationUser
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'trees/organization/:{organizationId}:/users'
      query:
        organizationType: ':{organizationType}:'
        effectiveDate: ':{effectiveDate}:'
      transform: '$'
  - path: /api/trees/structure-type
    method: GET
    model: _structureType
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'trees/structure-type'
      query:
      transform: '$'
  - path: /api/trees/organization/:structureType/:id/parent/:organizationType
    method: GET
    model: _organizationTree
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object1
      path: 'trees/organization/:{structureType}:/:{id}:/:{organizationType}:/parent'
      query:
        effectiveDate: ':{effectiveDate}:'
        currentLevel: ':{currentLevel}:'
      transform: '$map($, function ($item) {
        $merge([
        $item,
        {"directPositionId": $item.parentId}
        ])
        })[]
        '
  - path: /api/trees/organization/:structureType/:id/child/:organizationType
    method: GET
    model: _organizationTree
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: '/trees/organization/:{structureType}:/:{id}:/:{organizationType}:/child'
      query:
        effectiveDate: ':{effectiveDate}:'
        currentLevel: ':{currentLevel}:'
      transform: '$'
  - path: /api/trees/organization/detail/:id
    method: GET
    model: _organizationDetail
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'trees/organization/:{id}:'
      query:
        organizationType: ':{organizationType}:'
        effectiveDate: ':{effectiveDate}:'
      transform: '$'

  - path: /api/trees/organization/pick/:pickType/:code/:effectiveDate
    method: GET
    model: _organizationPick
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'trees/organization/pick/:{pickType}:/::{code}::/:{effectiveDate}:'
      transform: '$'
