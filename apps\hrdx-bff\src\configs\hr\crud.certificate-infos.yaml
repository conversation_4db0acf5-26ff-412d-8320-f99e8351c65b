controller: personals/:empId/certificate-infos
upstream: ${{UPSTREAM_HR_URL}}

models:
  - name: _
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: count
    config:
      id:
        from: id
        type: string
      specializeName:
        from: specializeName
      specializeCode:
        from: specializeCode
        type: string
      certificateCategoryCode:
        from: certificateCategoryCode
      certificateCategoryName:
        from: certificateCategoryName
      specialize:
        from: specializeCode
        type: string
      certificateTypeCode:
        from: certificateTypeCode
        type: string
      type:
        from: certificateTypeName
        type: string
      codeCertificate:
        from: certificateCode
      certificateCode:
        from: certificateCode
        type: string
      name:
        from: certificateName
        type: string
      number:
        from: certificateNumber
      trainingPlaces:
        from: trainingPlaces
        type: string
      issuedDate:
        from: issuedDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      issuedBy:
        from: issuedBy
        type: string
      certificateLevelCode:
        from: certificateLevelCode
        type: string
      level:
        from: certificateLevelCode
        type: string
      certificateLevelName:
        from: certificateLevelName
        type: string
      note:
        from: note
        type: string
      scopeOfProfessionalPractice:
        from: scopeOfProfessionalPractice
        type: string
      startDate:
        from: startDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      endDate:
        from: endDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      createdBy:
        from: createdBy
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime

auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: certificate-infos
crudConfig:
  query:
  params:
    id:
      field: id
      type: string
      primary: true
    empId:
      field: empId
      type: string
  routes:
    exclude:
      - recoverOneBase
      - createManyBase
      - replaceOneBase

defaultQuery:

routes:
  - path: /api/personals/:empId/certificate-infos
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      path: personals/:{empId}:/certificate-infos
      response:
        dataType: array
        dataKey: data
      transform: $

  - path: /api/personals/:empId/certificate-infos/:id
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      path: 'personals/:{empId}:/certificate-infos/:{id}:'
      response:
        dataType: object
      transform: $

  - path: /api/personals/:empId/certificate-infos
    method: POST
    model: _
    query:
    bodyTransform: '$merge([$, {"cost": $.cost ? $.cost : 0}])'
    upstreamConfig:
      method: POST
      path: personals/:{empId}:/certificate-infos

  - path: /api/personals/:empId/certificate-infos/:id
    method: PATCH
    model: _
    query:
    bodyTransform: '$merge([$, {"cost": $.cost ? $.cost : 0}])'
    upstreamConfig:
      method: PUT
      path: 'personals/:{empId}:/certificate-infos/:{id}:'

  - path: /api/personals/:empId/certificate-infos/:id
    method: DELETE
    query:
    upstreamConfig:
      method: DELETE
      path: 'personals/:{empId}:/certificate-infos/:{id}:'

customRoutes:
  - path: /api/personals/:empId/certificate-infos/histories
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      path: 'personals/:{empId}:/certificate-infos/history'
      response:
        dataType: array
        dataKey: data
      transform: $
