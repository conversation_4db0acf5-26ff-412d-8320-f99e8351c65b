controller: personals/:empId/contracts
upstream: ${{UPSTREAM_HR_URL}}

models:
  - name: _
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: count
    config:
      id:
        from: id
      employeeId:
        from: employeeId
      startDate:
        from: startDate
        typeOptions:
          func: timestampToDateTime
      effectiveDate:
        from: effectiveDateFrom
        typeOptions:
          func: timestampToDateTime
      endDate:
        from: endDate
        typeOptions:
          func: timestampToDateTime
      expectedDateTo:
        from: expectedDateTo
        typeOptions:
          func: timestampToDateTime
      employeeRecordNumber:
        from: employeeRecordNumber
      contractTypeCode:
        from: contractTypeCode
      contractTypeName:
        from: contractTypeName
      contractNumber:
        from: contractNumber
      status:
        from: enabled
        typeOptions:
          func: YNToBoolean
      duration:
        from: duration
      unitCode:
        from: unitCode
      signDate:
        from: signDate
        typeOptions:
          func: timestampToDateTime
      signer:
        from: signer
      note:
        from: note
      includingProbationPeriod:
        from: includingProbationPeriod
        typeOptions:
          func: YNToBoolean
      probationDateFrom:
        from: probationDateFrom
        typeOptions:
          func: timestampToDateTime
      probationDateTo:
        from: probationDateTo
        typeOptions:
          func: timestampToDateTime
      probationDuration:
        from: probationDuration
      probationUnitCode:
        from: probationUnitCode
      probationUnitName:
        from: probationUnitName
      probationNote:
        from: probationNote
      attachFile:
        from: attachFile
      fileName:
        from: fileName
      file:
        from: file
      isModifyFile:
        from: isModifyFile
      contractAppendices:
        from: contractAppendices
        arrayChildren:
          id:
            from: id
          startDate:
            from: startDate
            typeOptions:
              func: timestampToDateTime
          endDate:
            from: endDate
            typeOptions:
              func: timestampToDateTime
          expectedDateTo:
            from: expectedDateTo
            typeOptions:
              func: timestampToDateTime
          appendixNumber:
            from: appendixNumber
          status:
            from: enabled
            typeOptions:
              func: YNToBoolean
          signDate:
            from: signDate
            typeOptions:
              func: timestampToDateTime
          signer:
            from: signer
          reason:
            from: reason
          note:
            from: note
          attachFile:
            from: attachFile
          fileName:
            from: fileName
          file:
            from: file
          isModifyFile:
            from: isModifyFile
          isDeleted:
            from: isDeleted
            typeOptions:
              func: YNToBoolean
      createdBy:
        from: createdBy
      createdAt:
        from: createdAt
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
      updatedAt:
        from: updatedAt
        typeOptions:
          func: timestampToDateTime
      updatedByTermination:
        from: updatedByTermination
        typeOptions:
          func: YNToBoolean
      accessType:
        from: accessType

  - name: firstRecord
    config:
      startDate:
        from: startDate
        typeOptions:
          func: timestampToDateTime
      isFirst:
        from: isFirst
  - name: checkDuration
    config:
      startDate:
        from: startDate
        typeOptions:
          func: timestampToDateTime
      endDate:
        from: endDate
        typeOptions:
          func: timestampToDateTime
      messageWarning:
        from: messageWarning
      messageError:
        from: messageError
      hasContractInDuration:
        from: hasContractInDuration
      hasInactiveJobData:
        from: hasInactiveJobData
      numberOfMappedJobDataRecords:
        from: numberOfMappedJobDataRecords

auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: contracts
crudConfig:
  query:
    sort:
      - field: employeeRecordNumber
        order: ASC
  params:
    id:
      field: id
      type: string
      primary: true
    empId:
      field: empId
      type: string
  routes:
    exclude:
      - recoverOneBase
      - createManyBase
      - replaceOneBase

defaultQuery:

routes:
  - path: /api/personals/:empId/contracts
    method: GET
    model: _
    query:
      $and:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      query:
        OrderBy: ':{options.sort}:'
        employeeRecordNumber: '::{employeeRecordNumber}::'
      path: personals/:{empId}:/contracts
      transform: '$count($) = 0 ? [] : $map($, function ($v,$i,$a){ $merge([$v,{"index": $i + 1, "includingAppendix": $exists($v.contractAppendices) and $count($v.contractAppendices) > 0 ? true : false }])})[] '

  - path: /api/personals/:empId/contracts/:id
    method: GET
    model: _
    query:
      $and:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'personals/:{empId}:/contracts/:{id}:'

  - path: /api/personals/:empId/contracts
    method: POST
    query:
    upstreamConfig:
      method: POST
      path: personals/:{empId}:/contracts

  - path: /api/personals/:empId/contracts/:id
    method: PATCH
    query:
      $and:
    upstreamConfig:
      method: PUT
      path: 'personals/:{empId}:/contracts/:{id}:'

  - path: /api/personals/:empId/contracts/:id
    method: DELETE
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'personals/:{empId}:/contracts/:{id}:'

customRoutes:
  - path: /api/personals/:empId/contracts/upload
    method: POST
    dataType: 'formData'
    model: _
    query:
    transform: '$'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'personals/:{empId}:/contracts'
      transform: '$'

  - path: /api/personals/:empId/contracts/check-first-record
    method: GET
    model: firstRecord
    query:
    transform: '$'
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'personals/:{empId}:/contracts/check-first-record'
      transform: '$'

  - path: /api/personals/:empId/contracts/check-contract-duration
    method: GET
    model: checkDuration
    query:
    transform: '$'
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'personals/:{empId}:/contracts/check-contract-duration'
      query:
        EmployeeRecordNumber: '::{employeeRecordNumber}::'
        StartDate: '::{startDate}::'
        EndDate: '::{endDate}::'
        ExcludeContractId: '::{excludeContractId}::'
      transform: '$'

  - path: /api/personals/:empId/contracts/:id/upload
    model: _
    method: POST
    dataType: 'formData'
    query:
    transform: '$'
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: 'personals/:{empId}:/contracts/:{id}:'
  - path: /api/personals/:empId/contracts/histories
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'personals/:{empId}:/contracts/history'
      query:
        employeeRecordNumber: '::{employeeRecordNumber}::'
      transform: '$'

  - path: /api/personals/:empId/contracts/get-contract
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'personals/:{empId}:/contracts/get-contract'
      query:
        EmployeeId: '::{employeeId}::'
        EmployeeRecordNumber: '::{employeeRecordNumber}::'
        JobDataId: '::{jobDataId}::'
        ActionCode: '::{actionCode}::'
        ViewEffectiveDate: '::{viewEffectiveDate}::'
        EffectiveDateFrom: '::{effectiveDate}::'
      transform: '$'
