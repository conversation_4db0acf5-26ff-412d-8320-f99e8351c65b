controller: personals/:empId/job-datas
upstream: ${{UPSTREAM_HR_URL}}

models:
  - name: _
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: count
    config:
      id:
        from: id
      employeeId:
        from: employeeId
      fullName:
        from: fullName
      processType:
        from: processType
      jobIndicatorName:
        from: jobIndicatorName
      jobIndicator:
        from: jobIndicatorCode
      effectiveDate:
        from: effectiveDateFrom
        typeOptions:
          func: timestampToDateTime
      effectiveDateTo:
        from: effectiveDateTo
        typeOptions:
          func: timestampToDateTime
      organizationalInstanceRcd:
        from: organizationalInstanceRcd
      employeeRecordNumber:
        from: employeeRecordNumber
      effectiveSequence:
        from: effectiveSequence
      actionCode:
        from: actionCode
      isPrimary:
        from: isPrimary
        typeOptions:
          func: YNToBoolean
      primaryJob3:
        from: previousJobDataId
      employeeGroup:
        from: employeeGroupCode
      employeeGroupName:
        from: employeeGroupName
      employeeSubGroup:
        from: employeeSubGroupCode
      employeeSubGroupName:
        from: employeeSubGroupName
      levelOfDecision:
        from: levelDecisionCode
      levelDecisionName:
        from: levelDecisionName
      decisionNumber:
        from: decisionNumber
      signDate:
        from: signDate
        typeOptions:
          func: timestampToDateTime
      expectedEndDate:
        from: expectedEndDate
        typeOptions:
          func: timestampToDateTime
      note:
        from: note
      fte:
        from: fte
      title:
        from: titleCode
      hrStatus:
        from: hrStatusCode
      hrStatusName:
        from: hrStatusName
      payrollStatus:
        from: prStatusCode
      payrollStatusName:
        from: prStatusName
      actionName:
        from: actionName
      actionReasonCode:
        from: actionReasonCode
      actionReasonName:
        from: actionReasonName
      isManagerName:
        from: isManagerName
      isManagerCode:
        from: isManagerCode
      position:
        from: positionCode
      positionName:
        from: positionName
      job:
        from: jobCode
      jobName:
        from: jobName
      company:
        from: companyCode
      companyCode:
        from: companyCode
      companyName:
        from: companyName
      legalEntityCode:
        from: legalEntityCode
      legalEntityName:
        from: legalEntityName
      businessUnit:
        from: businessUnitCode
      businessUnitName:
        from: businessUnitName
      division:
        from: divisionCode
      divisionName:
        from: divisionName
      department:
        from: departmentCode
      departmentName:
        from: departmentName
      businessTitle:
        from: businessTitleCode
      businessTitleName:
        from: businessTitleName
      careerStream:
        from: careerStreamCode
      careerStreamName:
        from: careerStreamName
      careerBand:
        from: careerBandCode
      careerBandName:
        from: careerBandName
      jobLevel:
        from: jobLevelCode
      jobLevelName:
        from: jobLevelName
      empLevel:
        from: empLevelCode
      empLevelName:
        from: empLevelName
      costCenter:
        from: costCenterCode
      costCenterName:
        from: costCenterName
      location:
        from: locationCode
      locationName:
        from: locationName
      region:
        from: regionCode
      regionName:
        from: regionName
      fullPartName:
        from: fullPartName
      fullPartCode:
        from: fullPartCode
      timeZoneName:
        from: timeZoneName
      timezone:
        from: timeZoneCode
      departmentEntryDate:
        from: departmentEntryDate
        typeOptions:
          func: timestampToDateTime
      matrixReportPositionCodes:
        from: matrixReportPositionCodes
      jobDataMatrixReports:
        from: jobDataMatrixReports
      reportToPos:
        from: reportPosition
      supervisor:
        from: supervisor
      supervisorFullName:
        from: supervisorFullName
      matrixManagers:
        from: matrixManagers
      status:
        from: enabled
        typeOptions:
          func: YNToBoolean
      groupFirstStartDate:
        from: groupFirstStartDate
        typeOptions:
          func: timestampToDateTime
      createdBy:
        from: createdBy
      attachFile:
        from: attachFile
      attachFiles:
        from: attachFiles
        arrayChildren:
          id:
            from: id
          attachFileName:
            from: attachFileName
          fileName:
            from: fileName
      attachFileName:
        from: attachFileName
      fileName:
        from: fileName
      file:
        from: file
      isFileModified:
        from: IsFileModified
        typeOptions:
          func: YNToBoolean
      createdAt:
        from: createdAt
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
      updatedAt:
        from: updatedAt
        typeOptions:
          func: timestampToDateTime
      jobSeniority:
        from: jobSeniority
      reportToPosName:
        from: reportPositionName
      contractNumber:
        from: contractNumber
      contractTypeCode:
        from: contractTypeCode
      contractTypeName:
        from: contractTypeName
      classificationOfTerminationCode:
        from: classificationOfTerminationCode
      classificationOfTerminationName:
        from: classificationOfTerminationName
      accessType:
        from: accessType

  - name: modelOverview
    config:
      id:
        from: id
      employeeId:
        from: employeeId
      company:
        from: companyName
      companyView:
        from: companyName,companyCode
        typeOptions:
          func: fieldsToNameCode
      legalEntity:
        from: legalEntityName
      legalEntityView:
        from: legalEntityName,legalEntityCode
        typeOptions:
          func: fieldsToNameCode
      businessUnit:
        from: businessUnitName
      businessUnitView:
        from: businessUnitName,businessUnitCode
        typeOptions:
          func: fieldsToNameCode
      division:
        from: divisionName
      divisionView:
        from: divisionName,divisionCode
        typeOptions:
          func: fieldsToNameCode
      department:
        from: departmentName
      departmentView:
        from: departmentName,departmentCode
        typeOptions:
          func: fieldsToNameCode
      jobTitle:
        from: jobName
      jobCode:
        from: jobCode
      jobCodeView:
        from: jobName,jobCode
        typeOptions:
          func: fieldsToNameCode
      contractType:
        from: contractType
      contractTypeName:
        from: contractTypeName
      contractTypeView:
        from: contractTypeName,contractType
        typeOptions:
          func: fieldsToNameCode
      location:
        from: locationName
      empLevel:
        from: empLevelName
      employeeGroup:
        from: employeeGroupName
      jobIndicator:
        from: jobIndicatorName
      jobIndicatorView:
        from: jobIndicatorName,jobIndicatorCode
        typeOptions:
          func: fieldsToNameCode
      companyCode:
        from: companyCode

  - name: postModal
    config:
      id:
        from: id
      employeeId:
        from: employeeId
      processType:
        from: processType
      jobIndicatorName:
        from: jobIndicatorName
      jobIndicator:
        from: jobIndicatorCode
      effectiveDate:
        from: effectiveDateFrom
        typeOptions:
          func: timestampToDateTime
      effectiveDateTo:
        from: effectiveDateTo
        typeOptions:
          func: timestampToDateTime
      organizationalInstanceRecord:
        from: organizationalInstanceRcd
      employeeRecordNumber:
        from: employeeRecordNumber
      effectiveSequence:
        from: effectiveSequence
      actionCode:
        from: actionCode
      isPrimary:
        from: isPrimary
        typeOptions:
          func: YNToBoolean
      primaryJob3:
        from: previousJobDataId
      employeeGroup:
        from: employeeGroupCode
      employeeGroupName:
        from: employeeGroupName
      employeeSubGroup:
        from: employeeSubGroupCode
      employeeSubGroupName:
        from: employeeSubGroupName
      levelOfDecision:
        from: levelDecisionCode
      levelDecisionName:
        from: levelDecisionName
      decisionNumber:
        from: decisionNumber
      signDate:
        from: signDate
        typeOptions:
          func: timestampToDateTime
      expectedEndDate:
        from: expectedEndDate
        typeOptions:
          func: timestampToDateTime
      note:
        from: note
      fte:
        from: fte
      title:
        from: titleCode
      hrStatus:
        from: hrStatusCode
      payrollStatus:
        from: prStatusCode
      actionName:
        from: actionName
      actionReasonCode:
        from: actionReasonCode
      actionReasonName:
        from: actionReasonName
      isManagerName:
        from: isManagerName
      isManagerCode:
        from: isManagerCode
      position:
        from: positionCode
      positionName:
        from: positionName
      job:
        from: jobCode
      jobName:
        from: jobName
      company:
        from: companyCode
      companyName:
        from: companyName
      legalEntityCode:
        from: legalEntityCode
      legalEntityName:
        from: legalEntityName
      businessUnit:
        from: businessUnitCode
      businessUnitName:
        from: businessUnitName
      division:
        from: divisionCode
      divisionName:
        from: divisionName
      department:
        from: departmentCode
      departmentName:
        from: departmentName
      businessTitle:
        from: businessTitleCode
      businessTitleName:
        from: businessTitleName
      careerStream:
        from: careerStreamCode
      careerStreamName:
        from: careerStreamName
      careerBand:
        from: careerBandCode
      careerBandName:
        from: careerBandName
      jobLevel:
        from: jobLevelCode
      jobLevelName:
        from: jobLevelName
      empLevel:
        from: empLevelCode
      empLevelName:
        from: empLevelName
      costCenter:
        from: costCenterCode
      costCenterName:
        from: costCenterName
      location:
        from: locationCode
      locationName:
        from: locationName
      region:
        from: regionCode
      regionName:
        from: regionName
      fullPartName:
        from: fullPartName
      fullPartCode:
        from: fullPartCode
      timeZoneName:
        from: timeZoneName
      timezone:
        from: timeZoneCode
      departmentEntryDate:
        from: departmentEntryDate
        typeOptions:
          func: timestampToDateTime
      matrixReportPositionCodes:
        from: matrixReportPositionCodes
      jobDataMatrixReports:
        from: jobDataMatrixReports
      reportToPos:
        from: reportPosition
      supervisor:
        from: supervisor
      matrixManagers:
        from: matrixManagers
      status:
        from: enabled
        typeOptions:
          func: YNToBoolean
      groupFirstStartDate:
        from: groupFirstStartDate
        typeOptions:
          func: timestampToDateTime
      createdBy:
        from: createdBy
      attachFile:
        from: attachFile
      file:
        from: files
      deleteFileIds:
        from: deleteFileIds
      isFileModified:
        from: IsFileModified
        typeOptions:
          func: YNToBoolean
      createdAt:
        from: createdAt
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
      updatedAt:
        from: updatedAt
        typeOptions:
          func: timestampToDateTime
      classificationOfTerminationCode:
        from: classificationOfTerminationCode
      classificationOfTerminationName:
        from: classificationOfTerminationName
      updateContractEndDate:
        from: updateContractEndDate
        typeOptions:
          func: YNToBoolean

  - name: totalFte
    config:
      employeeId:
        from: employeeId
      fte:
        from: fte
      fteStr:
        from: fteStr

  - name: _calculateJobSeniority
    config:
      employeeId:
        from: employeeId
      jobCode:
        from: jobCode
      jobSenioritySecond:
        from: jobSenioritySecond
      jobSeniority:
        from: jobSeniority
      effectiveDate:
        from: effectiveDate
        typeOptions:
          func: timestampToDateTime
      groupFirstStartDate:
        from: groupFirstStartDate
        typeOptions:
          func: timestampToDateTime

  - name: _validateJobData
    config:
      effectiveDate:
        from: effectiveDateFrom
        typeOptions:
          func: timestampToDateTime
      note:
        from: note
      code:
        from: code
      message:
        from: message
      inputError:
        from: inputError

  - name: _validateRelativeInfo
    config:
      effectiveDate:
        from: effectiveDateFrom
        typeOptions:
          func: timestampToDateTime
      hasError:
        from: hasError
      isActionError:
        from: isActionError

  - name: _haveContractUpdated
    config:
      effectiveDate:
        from: effectiveDateFrom
        typeOptions:
          func: timestampToDateTime
      updateContract:
        from: updateContract
        typeOptions:
          func: YNToBoolean

  - name: _relateiveInfoJobData
    config:
      effectiveDate:
        from: effectiveDateFrom
        typeOptions:
          func: timestampToDateTime
      isUniquePrimaryJob:
        from: isUniquePrimaryJob
        typeOptions:
          func: YNToBoolean
      hasOtherSecondaryJobInSameOIR:
        from: hasOtherSecondaryJobInSameOIR
        typeOptions:
          func: YNToBoolean
      numberOfSecondaryJobInSameOIR:
        from: numberOfSecondaryJobInSameOIR

  - name: _terminationRecordInSameOir
    config:
      terminationSecondaryJob:
        from: terminationSecondaryJob
        typeOptions:
          func: YNToBoolean

  - name: _CHECK_BLACK_BLOCK
    config:
      hasBlackBlockInfos:
        from: hasBlackBlockInfos
        typeOptions:
          func: YNToBoolean
      message:
        from: message

auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: job-datas
crudConfig:
  query:
    sort:
      - field: employeeRecordNumber
        order: ASC
  params:
    id:
      field: id
      type: string
      primary: true
    empId:
      field: empId
      type: string
    employeeGroupCode:
      field: employeeGroupCode
      type: string
    companyCode:
      field: companyCode
      type: string
    rehireDate:
      field: rehireDate
      type: string
    actionCode:
      field: actionCode
      type: string
  routes:
    exclude:
      - recoverOneBase
      - createManyBase
      - replaceOneBase

defaultQuery:

routes:
  - path: /api/personals/:empId/job-datas
    method: GET
    model: _
    upstreamConfig:
      method: GET
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        EffectiveDate: ':{EffectiveDate}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      response:
        dataType: paginated
      path: personals/:{empId}:/job-datas
      transform: '$ ~> | $.data | (
        $mapNameAndCode := function($name, $code) {
        $name and $code ? $name & " (" & $code & ")" : $name ? $name : $code ? $code : ""
        };
        $getListFiles := function($listData) {
        $reduce($listData, function($acc, $curr){
        $append($acc,{"name": $curr.fileName, "url": "/api/hr-files/" & $curr.id, "key": $curr.id})
        },[])
        };
        {
        "actionObj": $.actionCode ? {
        "label": $.actionName,
        "value": $.actionCode
        },
        "positionObj": $.position ? {
        "label": $mapNameAndCode($.positionName, $.position),
        "value": $.position
        },
        "companyObj": $.company ? {
        "label": $mapNameAndCode($.companyName, $.company),
        "value": $.company
        },
        "legalEntityObj": $.legalEntityCode ? {
        "label": $mapNameAndCode($.legalEntityName, $.legalEntityCode),
        "value": $.legalEntityCode
        },
        "businessUnitObj": $.businessUnit ? {
        "label": $mapNameAndCode($.businessUnitName, $.businessUnit),
        "value": $.businessUnit
        },
        "divisionObj": $.division ? {
        "label": $mapNameAndCode($.divisionName, $.division),
        "value": $.division
        },
        "departmentObj": $.department ? {
        "label": $mapNameAndCode($.departmentName, $.department),
        "value": $.department
        },
        "locationObj": $.location ? {
        "label": $mapNameAndCode($.locationName, $.location),
        "value": $.location
        },
        "regionObj": $.region ? {
        "label": $.regionName,
        "value": $.region
        },
        "jobObj": $.job ? {
        "label": $mapNameAndCode($.jobName, $.job),
        "value": $.job
        },
        "businessTitleObj": $.businessTitle ? {
        "label": $mapNameAndCode($.businessTitleName, $.businessTitle),
        "value": $.businessTitle
        },
        "careerStreamObj": $.careerStream ? {
        "label": $mapNameAndCode($.careerStreamName, $.careerStream),
        "value": $.careerStream
        },
        "careerBandObj": $.careerBand ? {
        "label": $mapNameAndCode($.careerBandName, $.careerBand),
        "value": $.careerBand
        },
        "costCenterObj": $.costCenter ? {
        "label": $mapNameAndCode($.costCenterName, $.costCenter),
        "value": $.costCenter
        },
        "supervisorObj": $.supervisor ? {
        "label": $mapNameAndCode($.supervisorFullName, $.supervisor),
        "value": $.supervisor
        },
        "reportToPosObj": $.reportToPos ? {
        "label": $mapNameAndCode($.reportToPosName, $.reportToPos),
        "value": $.reportToPos
        },
        "matrixManagerObjs": $map($filter($.jobDataMatrixReports, function ($v) {
        $boolean($v.matrixManager)
        }), function ($item) {
        {
        "label": $mapNameAndCode($item.matrixManagerName, $item.matrixManager),
        "value": $item.matrixManager
        }
        })[],
        "matrixReportPositionObjs": $map($filter($.jobDataMatrixReports, function ($v) {
        $boolean($v.matrixReportPositionCode)
        }), function ($item) {
        {
        "label": $mapNameAndCode($item.matrixReportPositionName, $item.matrixReportPositionCode),
        "value": $item.matrixReportPositionCode
        }
        })[],
        "organizationalInstanceRecord": $.organizationalInstanceRcd,
        "attachmentResults": $.attachFiles ? $getListFiles($.attachFiles) : null,
        "hrStatusDefault": $.hrStatus,
        "prStatusDefault": $.payrollStatus,
        "fteDefault": $.fte,
        "positionView": $mapNameAndCode($.positionName, $.position),
        "companyView": $mapNameAndCode($.companyName, $.company),
        "legalEntityView": $mapNameAndCode($.legalEntityName, $.legalEntityCode),
        "businessUnitView": $mapNameAndCode($.businessUnitName, $.businessUnit),
        "divisionView": $mapNameAndCode($.divisionName, $.division),
        "departmentView": $mapNameAndCode($.departmentName, $.department),
        "locationView": $mapNameAndCode($.locationName, $.location),
        "jobView": $mapNameAndCode($.jobName, $.job),
        "businessTitleView": $mapNameAndCode($.businessTitleName, $.businessTitle),
        "careerStreamView": $mapNameAndCode($.careerStreamName, $.careerStream),
        "careerBandView": $mapNameAndCode($.careerBandName, $.careerBand),
        "costCenterView": $mapNameAndCode($.costCenterName, $.costCenter),
        "empLevelView": $mapNameAndCode($.empLevelName, $.empLevel),
        "supervisorView": $mapNameAndCode($.supervisorFullName, $.supervisor),
        "reportToPosView": $mapNameAndCode($.reportToPosName, $.reportToPos),
        "matrixManagerView": $map($filter($.jobDataMatrixReports, function ($v) {
        $boolean($v.matrixManager)
        }), function ($item) {
        $mapNameAndCode($item.matrixManagerName, $item.matrixManager)
        })[],
        "matrixReportPositionView": $map($filter($.jobDataMatrixReports, function ($v) {
        $boolean($v.matrixReportPositionCode)
        }), function ($item) {
        $mapNameAndCode($item.matrixReportPositionName, $item.matrixReportPositionCode)
        })[]
        }
        ) |
        '

  - path: /api/personals/:empId/job-datas/:id
    method: GET
    model: _
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'personals/:{empId}:/job-datas/:{id}:'
      transform: '$ ~> | $ | (
        $mapNameAndCode := function($name, $code) {
        $name and $code ? $name & " (" & $code & ")" : $name ? $name : $code ? $code : ""
        };
        $getListFiles := function($listData) {
        $reduce($listData, function($acc, $curr){
        $append($acc,{"name": $curr.fileName, "url": "/api/hr-files/" & $curr.id, "key": $curr.id})
        },[])
        };
        {
        "actionObj": $.actionCode ? {
        "label": $.actionName,
        "value": $.actionCode
        },
        "positionObj": $.position ? {
        "label": $mapNameAndCode($.positionName, $.position),
        "value": $.position
        },
        "companyObj": $.company ? {
        "label": $mapNameAndCode($.companyName, $.company),
        "value": $.company
        },
        "legalEntityObj": $.legalEntityCode ? {
        "label": $mapNameAndCode($.legalEntityName, $.legalEntityCode),
        "value": $.legalEntityCode
        },
        "businessUnitObj": $.businessUnit ? {
        "label": $mapNameAndCode($.businessUnitName, $.businessUnit),
        "value": $.businessUnit
        },
        "divisionObj": $.division ? {
        "label": $mapNameAndCode($.divisionName, $.division),
        "value": $.division
        },
        "departmentObj": $.department ? {
        "label": $mapNameAndCode($.departmentName, $.department),
        "value": $.department
        },
        "locationObj": $.location ? {
        "label": $mapNameAndCode($.locationName, $.location),
        "value": $.location
        },
        "regionObj": $.region ? {
        "label": $.regionName,
        "value": $.region
        },
        "jobObj": $.job ? {
        "label": $mapNameAndCode($.jobName, $.job),
        "value": $.job
        },
        "businessTitleObj": $.businessTitle ? {
        "label": $mapNameAndCode($.businessTitleName, $.businessTitle),
        "value": $.businessTitle
        },
        "careerStreamObj": $.careerStream ? {
        "label": $mapNameAndCode($.careerStreamName, $.careerStream),
        "value": $.careerStream
        },
        "careerBandObj": $.careerBand ? {
        "label": $mapNameAndCode($.careerBandName, $.careerBand),
        "value": $.careerBand
        },
        "costCenterObj": $.costCenter ? {
        "label": $mapNameAndCode($.costCenterName, $.costCenter),
        "value": $.costCenter
        },
        "supervisorObj": $.supervisor ? {
        "label": $mapNameAndCode($.supervisorFullName, $.supervisor),
        "value": $.supervisor
        },
        "reportToPosObj": $.reportToPos ? {
        "label": $mapNameAndCode($.reportToPosName, $.reportToPos),
        "value": $.reportToPos
        },
        "matrixManagerObjs": $map($filter($.jobDataMatrixReports, function ($v) {
        $boolean($v.matrixManager)
        }), function ($item) {
        {
        "label": $mapNameAndCode($item.matrixManagerName, $item.matrixManager),
        "value": $item.matrixManager
        }
        })[],
        "matrixReportPositionObjs": $map($filter($.jobDataMatrixReports, function ($v) {
        $boolean($v.matrixReportPositionCode)
        }), function ($item) {
        {
        "label": $mapNameAndCode($item.matrixReportPositionName, $item.matrixReportPositionCode),
        "value": $item.matrixReportPositionCode
        }
        })[],
        "organizationalInstanceRecord": $.organizationalInstanceRcd,
        "attachmentResults": $.attachFiles ? $getListFiles($.attachFiles) : null,
        "hrStatusDefault": $.hrStatus,
        "prStatusDefault": $.payrollStatus,
        "fteDefault": $.fte,
        "positionView": $mapNameAndCode($.positionName, $.position),
        "companyView": $mapNameAndCode($.companyName, $.company),
        "legalEntityView": $mapNameAndCode($.legalEntityName, $.legalEntityCode),
        "businessUnitView": $mapNameAndCode($.businessUnitName, $.businessUnit),
        "divisionView": $mapNameAndCode($.divisionName, $.division),
        "departmentView": $mapNameAndCode($.departmentName, $.department),
        "locationView": $mapNameAndCode($.locationName, $.location),
        "jobView": $mapNameAndCode($.jobName, $.job),
        "businessTitleView": $mapNameAndCode($.businessTitleName, $.businessTitle),
        "careerStreamView": $mapNameAndCode($.careerStreamName, $.careerStream),
        "careerBandView": $mapNameAndCode($.careerBandName, $.careerBand),
        "costCenterView": $mapNameAndCode($.costCenterName, $.costCenter),
        "empLevelView": $mapNameAndCode($.empLevelName, $.empLevel),
        "supervisorView": $mapNameAndCode($.supervisorFullName, $.supervisor),
        "reportToPosView": $mapNameAndCode($.reportToPosName, $.reportToPos),
        "matrixManagerView": $map($filter($.jobDataMatrixReports, function ($v) {
        $boolean($v.matrixManager)
        }), function ($item) {
        $mapNameAndCode($item.matrixManagerName, $item.matrixManager)
        })[],
        "matrixReportPositionView": $map($filter($.jobDataMatrixReports, function ($v) {
        $boolean($v.matrixReportPositionCode)
        }), function ($item) {
        $mapNameAndCode($item.matrixReportPositionName, $item.matrixReportPositionCode)
        })[]
        }
        ) |
        '

  - path: /api/personals/:empId/job-datas
    method: POST
    model: postModal
    dataType: 'formData'
    query:
    transform: '$'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: personals/:{empId}:/job-datas
      transform: '$'

  - path: /api/personals/:empId/job-datas/:id
    method: PATCH
    model: postModal
    dataType: 'formData'
    query:
    transform: '$'
    upstreamConfig:
      method: PUT
      response:
        dataType: object
      path: 'personals/:{empId}:/job-datas/:{id}:'

  - path: /api/personals/:empId/job-datas/:id
    method: DELETE
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'personals/:{empId}:/job-datas/:{id}:'

customRoutes:
  - path: /api/personals/:empId/job-datas/lastest-records
    method: GET
    model: _
    upstreamConfig:
      method: GET
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      response:
        dataType: paginated
      path: personals/:{empId}:/job-datas/lastest-records
      transform: '$'
  - path: /api/personals/:empId/job-datas/upload
    method: POST
    dataType: 'formData'
    model: postModal
    query:
    transform: '$'
    upstreamConfig:
      method: POST
      path: 'personals/:{empId}:/job-datas'
      transform: '$'

  - path: /api/personals/:empId/job-datas/:id/upload
    model: postModal
    method: POST
    dataType: 'formData'
    query:
    transform: '$'
    upstreamConfig:
      method: PUT
      path: 'personals/:{empId}:/job-datas/:{id}:'

  - path: /api/personals/:empId/job-datas/histories
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'personals/:{empId}:/job-datas/history'
      query:
        JobDataId: '::{id}::'
        OrganizationalInstanceRcd: '::{organizationalInstanceRcd}::'
        EmployeeRecordNumber: '::{employeeRecordNumber}::'
      transform: '$ ~> | $ | (
        $mapNameAndCode := function($name, $code) {
        $name and $code ? $name & " (" & $code & ")" : $name ? $name : $code ? $code : ""
        };
        $getListFiles := function($listData) {
        $reduce($listData, function($acc, $curr){
        $append($acc,{"name": $curr.fileName, "url": "/api/hr-files/" & $curr.id, "key": $curr.id})
        },[])
        };
        {
        "actionObj": $.actionCode ? {
        "label": $.actionName,
        "value": $.actionCode
        },
        "positionObj": $.position ? {
        "label": $mapNameAndCode($.positionName, $.position),
        "value": $.position
        },
        "companyObj": $.company ? {
        "label": $mapNameAndCode($.companyName, $.company),
        "value": $.company
        },
        "legalEntityObj": $.legalEntityCode ? {
        "label": $mapNameAndCode($.legalEntityName, $.legalEntityCode),
        "value": $.legalEntityCode
        },
        "businessUnitObj": $.businessUnit ? {
        "label": $mapNameAndCode($.businessUnitName, $.businessUnit),
        "value": $.businessUnit
        },
        "divisionObj": $.division ? {
        "label": $mapNameAndCode($.divisionName, $.division),
        "value": $.division
        },
        "departmentObj": $.department ? {
        "label": $mapNameAndCode($.departmentName, $.department),
        "value": $.department
        },
        "locationObj": $.location ? {
        "label": $mapNameAndCode($.locationName, $.location),
        "value": $.location
        },
        "regionObj": $.region ? {
        "label": $.regionName,
        "value": $.region
        },
        "jobObj": $.job ? {
        "label": $mapNameAndCode($.jobName, $.job),
        "value": $.job
        },
        "businessTitleObj": $.businessTitle ? {
        "label": $mapNameAndCode($.businessTitleName, $.businessTitle),
        "value": $.businessTitle
        },
        "careerStreamObj": $.careerStream ? {
        "label": $mapNameAndCode($.careerStreamName, $.careerStream),
        "value": $.careerStream
        },
        "careerBandObj": $.careerBand ? {
        "label": $mapNameAndCode($.careerBandName, $.careerBand),
        "value": $.careerBand
        },
        "costCenterObj": $.costCenter ? {
        "label": $mapNameAndCode($.costCenterName, $.costCenter),
        "value": $.costCenter
        },
        "supervisorObj": $.supervisor ? {
        "label": $mapNameAndCode($.supervisorFullName, $.supervisor),
        "value": $.supervisor
        },
        "reportToPosObj": $.reportToPos ? {
        "label": $mapNameAndCode($.reportToPosName, $.reportToPos),
        "value": $.reportToPos
        },
        "matrixManagerObjs": $map($filter($.jobDataMatrixReports, function ($v) {
        $boolean($v.matrixManager)
        }), function ($item) {
        {
        "label": $mapNameAndCode($item.matrixManagerName, $item.matrixManager),
        "value": $item.matrixManager
        }
        })[],
        "matrixReportPositionObjs": $map($filter($.jobDataMatrixReports, function ($v) {
        $boolean($v.matrixReportPositionCode)
        }), function ($item) {
        {
        "label": $mapNameAndCode($item.matrixReportPositionName, $item.matrixReportPositionCode),
        "value": $item.matrixReportPositionCode
        }
        })[],
        "organizationalInstanceRecord": $.organizationalInstanceRcd,
        "attachmentResults": $.attachFiles ? $getListFiles($.attachFiles) : null,
        "hrStatusDefault": $.hrStatus,
        "prStatusDefault": $.payrollStatus,
        "fteDefault": $.fte,
        "positionView": $mapNameAndCode($.positionName, $.position),
        "companyView": $mapNameAndCode($.companyName, $.company),
        "legalEntityView": $mapNameAndCode($.legalEntityName, $.legalEntityCode),
        "businessUnitView": $mapNameAndCode($.businessUnitName, $.businessUnit),
        "divisionView": $mapNameAndCode($.divisionName, $.division),
        "departmentView": $mapNameAndCode($.departmentName, $.department),
        "locationView": $mapNameAndCode($.locationName, $.location),
        "jobView": $mapNameAndCode($.jobName, $.job),
        "businessTitleView": $mapNameAndCode($.businessTitleName, $.businessTitle),
        "careerStreamView": $mapNameAndCode($.careerStreamName, $.careerStream),
        "careerBandView": $mapNameAndCode($.careerBandName, $.careerBand),
        "costCenterView": $mapNameAndCode($.costCenterName, $.costCenter),
        "empLevelView": $mapNameAndCode($.empLevelName, $.empLevel),
        "supervisorView": $mapNameAndCode($.supervisorFullName, $.supervisor),
        "reportToPosView": $mapNameAndCode($.reportToPosName, $.reportToPos),
        "matrixManagerView": $map($filter($.jobDataMatrixReports, function ($v) {
        $boolean($v.matrixManager)
        }), function ($item) {
        $mapNameAndCode($item.matrixManagerName, $item.matrixManager)
        })[],
        "matrixReportPositionView": $map($filter($.jobDataMatrixReports, function ($v) {
        $boolean($v.matrixReportPositionCode)
        }), function ($item) {
        $mapNameAndCode($item.matrixReportPositionName, $item.matrixReportPositionCode)
        })[]
        }
        ) |
        '

  - path: /api/personals/:empId/job-datas/overview/:id
    method: GET
    model: modelOverview
    query:
      $and:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'personals/:{empId}:/job-datas/:{id}:'
      query:
        Filter: '::{filter}::'
      transform: '$'

  - path: /api/personals/:empId/job-datas/employee-unique-ern
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'personals/:{empId}:/job-datas/employee-unique-ern'
      query:
        effectiveDate: '::{effectiveDate}::'

  - path: /api/personals/:empId/job-datas/total-fte
    method: GET
    model: totalFte
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'personals/:{empId}:/job-datas/total-fte'
      query:
        inputFte: '::{inputFte}::'
        effectiveDate: '::{effectiveDate}::'
        employeeRecordNumber: '::{employeeRecordNumber}::'
        JobDataId: '::{jobDataId}::'
      transform: '$'

  - path: /api/personals/:empId/job-datas/calculate-job-seniority
    method: GET
    model: _calculateJobSeniority
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'personals/:{empId}:/job-datas/calculate-job-seniority'
      query:
        JobDataId: '::{jobDataId}::'
        EmployeeId: '::{employeeId}::'
        EmployeeRecordNumber: '::{employeeRecordNumber}::'
        OrganizationalInstanceRcd: '::{organizationalInstanceRcd}::'
        ActionCode: '::{actionCode}::'
        EffectiveDate: '::{effectiveDate}::'
        EmployeeSubGroupCode: '::{employeeSubGroupCode}::'
        JobCode: '::{jobCode}::'
        CompanyCode: '::{companyCode}::'
        HRStatusCode: '::{hrStatusCode}::'
        EffectiveSequence: '::{effectiveSequence}::'
      transform: '$'

  - path: /api/personals/:empId/job-datas/previous-record/:id
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'personals/job-datas/previous-record/:{id}:'
      transform: '$'

  - path: /api/personals/:empId/job-datas/validate-job-datas
    method: GET
    model: _validateJobData
    query:
    transfrom: '$'
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'personals/job-datas/validate-job-datas'
      query:
        EffectiveDateFrom: '::{effectiveDate}::'
        CompanyCode: '::{companyCode}::'
        LegalEntityCode: '::{legalEntityCode}::'
        BusinessUnitCode: '::{businessUnitCode}::'
        DivisionCode: '::{division}::'
        DepartmentCode: '::{department}::'
        CostCenterCode: '::{costCenter}::'
        LocationCode: '::{location}::'
        CareerStreamCode: '::{careerStream}::'
        CareerBandCode: '::{careerBand}::'
        ReportPosition: '::{reportPosition}::'
        JobCode: '::{jobCode}::'
        PositionCode: '::{positionCode}::'
        BusinessTitleCode: '::{businessTitleCode}::'
        ActionCode: '::{actionCode}::'
        ActionReasonCode: '::{actionReasonCode}::'
        EmployeeGroupCode: '::{employeeGroupCode}::'
        EmployeeSubGroupCode: '::{employeeSubGroupCode}::'
        MatrixManager: '::{matrixManager}::'
        MatrixReportPositionCodes: '::{matrixReportPositionCodes}::'
        Supervisor: '::{supervisor}::'
        RegionCode: '::{regionCode}::'
        EmployeeLevelCode: '::{employeeLevelCode}::'
        IgnoreNew: '::{ignoreNew}::'
        UseCache: 'true'

  - path: /api/personals/:empId/job-datas/have-contract-updated
    method: GET
    model: _haveContractUpdated
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'personals/:{empId}:/job-datas/have-contract-updated'
      query:
        EffectiveDateFrom: '::{effectiveDate}::'
        ActionCode: '::{actionCode}::'
        EmployeeRecordNumber: '::{employeeRecordNumber}::'
        JobDataId: '::{jobDataId}::'
        EmployeeId: '::{employeeId}::'
      transform: '$'

  - path: /api/personals/:empId/job-datas/get-relateive-info-job-data
    method: GET
    model: _relateiveInfoJobData
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'personals/:{empId}:/job-datas/get-relateive-info-job-data'
      query:
        EffectiveDateFrom: '::{effectiveDate}::'
        OrganizationalInstanceRecord: '::{organizationalInstanceRecord}::'
        EmployeeRecordNumber: '::{employeeRecordNumber}::'
        EmployeeId: '::{employeeId}::'
        JobDataId: '::{jobDataId}::'
        GetSubActiveJob: '::{getSubActiveJob}::'
      transform: '$'

  - path: /api/personals/:empId/job-datas/:id/clone-termination-record-in-same-oir
    method: POST
    model: _terminationRecordInSameOir
    query:
    transform: '$'
    upstreamConfig:
      method: POST
      path: 'personals/:{empId}:/job-datas/:{id}:/clone-termination-record-in-same-oir'
      transform: '$'

  - path: /api/personals/:empId/job-datas/:id/check-black-block
    method: GET
    model: _CHECK_BLACK_BLOCK
    query:
    transform: '$'
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'personals/:{empId}:/job-datas/:{id}:/check-black-block'
      transform: '$'

  - path: /api/personals/:empId/job-datas/validate-relative-infos
    method: GET
    model: _validateRelativeInfo
    query:
    transfrom: '$'
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'personals/job-datas/validate-relative-infos'
      query:
        EffectiveDate: '::{effectiveDate}::'
        EmployeeId: '::{employeeId}::'
        JobDataId: '::{jobDataId}::'
        ActionCode: '::{actionCode}::'

