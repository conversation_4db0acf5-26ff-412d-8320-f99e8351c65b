controller: jobdata-all
upstream: ${{UPSTREAM_HR_URL}}
upstreamPath: personals/job-datas/get-all

__auth: auth.default__auth
__crudConfig: crud-config.default__crudConfig

models:
  - name: _
    __pagination: pagination.default__pagination
    config:
      jobCode:
        from: jobCode
      jobIndicatorCode:
        from: jobIndicatorCode
      companyCode:
        from: companyCode
      departmentCode:
        from: departmentCode
      employeeId:
        from: employeeId
      effectiveDate:
        from: effectiveDateFrom
        typeOptions:
          func: timestampToDateTime
      fromDate:
        from: fromDate
        typeOptions:
          func: timestampToDateTime
      toDate:
        from: toDate
        typeOptions:
          func: timestampToDateTime
      jobName:
        from: jobName
      jobTitle:
        from: jobTitle

__routes: routes.default__routes
routes:
  - path: /api/${{controller}}
    uniq: GetManyBase
    method: GET
    model: _
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: '${{upstreamPath}}'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        Search: ':{search}:'
        OrderBy: ':{options.sort}:'
        Filter: '::{filter}::'
        FromDate: '::{fromDate}::'
        ToDate: '::{toDate}::'
        CompanyCode: '::{companyCode}::'
        DepartmentCode: '::{departmentCode}::'
        JobIndicatorCode: '::{jobIndicatorCode}::'
