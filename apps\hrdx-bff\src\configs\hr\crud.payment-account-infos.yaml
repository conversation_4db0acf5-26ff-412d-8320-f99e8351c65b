controller: personals/:empId/payment-account-infos
upstream: ${{UPSTREAM_HR_URL}}
upstreamPath: personals/:{empId}:/payment-account-infos

models:
  - name: _
    __pagination: pagination.default__pagination
    config:
      id:
        from: id
      employeeId:
        from: employeeId
      companyCode:
        from: companyCode
      companyName:
        from: companyName
      departmentCode:
        from: departmentCode
      departmentName:
        from: departmentName
      jobCode:
        from: jobCode
      jobName:
        from: jobName
      legalEntityCode:
        from: legalEntityCode
      legalEntityName:
        from: legalEntityName
      payee:
        from: payee
      effectiveDate:
        from: effectiveDateFrom
        typeOptions:
          func: timestampToDateTime
      jobDataId:
        from: jobDataId
      employeeRecordNumber:
        from: employeeRecordNumber
      note:
        from: note
      runTypeCode:
        from: runTimeCode
      runTypeName:
        from: runTimeName
      isDeleted:
        from: isDeleted
        typeOptions:
          func: YNToBoolean
      accessType:
        from: accessType
      createdBy:
        from: createdBy
      createdAt:
        from: createdAt
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
      updatedAt:
        from: updatedAt
        typeOptions:
          func: timestampToDateTime
      paymentAccountInfoDetails:
        from: paymentAccountInfoDetails
        arrayChildren:
          id:
            from: id
          bankAccountId:
            from: bankAccountId
          bankAccount:
            from: bankAccount
          bankCode:
            from: bankCode
          bankName:
            from: bankName
          currencyCode:
            from: currencyCode
          currencyName:
            from: currencyName
          paymentAccountInfoId:
            from: paymentAccountInfoId
          paymentMethodCode:
            from: paymentMethodCode
          paymentMethodName:
            from: paymentMethodName
          percent:
            from: percent
      commands:
        from: commands
        arrayChildren:
          id:
            from: id
          effectiveDate:
            from: effectiveDateFrom
            typeOptions:
              func: timestampToDateTime
          jobDataId:
            from: jobDataId
          note:
            from: note
          runTypeCode:
            from: runTimeCode
          isDeleted:
            from: isDeleted
            typeOptions:
              func: YNToBoolean
          paymentAccountInfoDetails:
            from: paymentAccountInfoDetails
            arrayChildren:
              id:
                from: id
              bankAccountId:
                from: bankAccountId
              paymentMethodCode:
                from: paymentMethodCode
              percent:
                from: percent

  - name: _CHECK
    config:
      isFirst:
        from: isFirst
      effectiveDate:
        from: effectiveDateFrom
        typeOptions:
          func: timestampToDateTime

  - name: _CHECK_DUPLICATE
    config:
      status:
        from: isDuplicated
      message:
        from: message

auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: payment-account-infos
crudConfig:
  query:
  params:
    id:
      field: id
      type: string
      primary: true
    empId:
      field: empId
      type: string
  routes:
    exclude:
      - recoverOneBase
      - createManyBase
      - replaceOneBase

defaultQuery:

routes:
  - path: /api/{{controller}}
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      path: '{{upstreamPath}}'
      response:
        dataType: array
      query:
        id: '::{id}::'
      transform: '$ ~> | $ | {"paymentAccountInfoDetails": $map($.paymentAccountInfoDetails, function($it, $indx){$merge([$it, {"stt": $indx + 1}]) })[]} |'

  - path: /api/{{controller}}/:id
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      path: '{{upstreamPath}}/:{id}:'
      response:
        dataType: object

  - path: /api/{{controller}}
    method: POST
    model: _
    query:
    upstreamConfig:
      method: POST
      path: '{{upstreamPath}}'

  - path: /api/{{controller}}/:id
    method: PATCH
    model: _
    query:
    upstreamConfig:
      method: PUT
      path: '{{upstreamPath}}'

  - path: /api/{{controller}}/:id
    method: DELETE
    query:
    upstreamConfig:
      method: DELETE
      path: '{{upstreamPath}}/:{id}:'

customRoutes:
  - path: /api/{{controller}}/histories
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: '{{upstreamPath}}/history'
      query:
        employeeRecordNumber: '::{employeeRecordNumber}::'
        runtypeCode: '::{runtypeCode}::'
      transform: '$ ~> | $ | {"paymentAccountInfoDetails": $map($.paymentAccountInfoDetails, function($it, $indx){$merge([$it, {"stt": $indx + 1}]) })[]} |'

  - path: /api/{{controller}}/dropdown
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: '{{upstreamPath}}/dropdown'
      transform: '$'

  - path: /api/{{controller}}/check-first-record
    method: GET
    model: _CHECK
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: '{{upstreamPath}}/check-first-record'
      query:
        employeeRecordNumber: '::{employeeRecordNumber}::'

  - path: /api/{{controller}}/check-duplicate-bank
    method: GET
    model: _CHECK_DUPLICATE
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: '{{upstreamPath}}/check-duplicate-bank'
      query:
        employeeRecordNumber: '::{employeeRecordNumber}::'
        bankAccountId: '::{bankAccountId}::'
