controller: personals
upstream: ${{UPSTREAM_HR_URL}}

models:
  - name: _
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: indexFrom
    config:
      id:
        from: id
      employeeId:
        from: employeeId
      employeeRecordNumber:
        from: employeeRecordNumber
        type: int
      name:
        from: fullName
        type: string
      company:
        from: companyCode
        type: string
      companyName:
        from: companyName
        type: string
      legalEntity:
        from: legalEntityCode
        type: string
      legalEntityName:
        from: legalEntityName
        type: string
      bussinessUnit:
        from: businessUnitCode
        type: string
      businessUnitName:
        from: businessUnitName
        type: string
      division:
        from: divisionCode
        type: string
      divisionName:
        from: divisionName
        type: string
      department:
        from: departmentCode
        type: string
      departmentName:
        from: departmentName
        type: string
      location:
        from: locationCode
        type: string
      locationName:
        from: locationName
        type: string
      employeeGroup:
        from: employeeGroupCode
        type: string
      employeeGroupName:
        from: employeeGroupName
        type: string
      employeeSubGroup:
        from: employeeSubGroupCode
      employeeSubGroupName:
        from: employeeSubGroupName
      job:
        from: jobCode
        type: string
      jobName:
        from: jobName
        type: string
      positionCode:
        from: positionCode
        type: string
      positionName:
        from: positionName
        type: string
      email:
        from: email
        type: string
      userName:
        from: userName
        type: string
      firstName:
        from: firstName
        type: string
      middleName:
        from: middleName
        type: string
      lastName:
        from: lastName
        type: string
      fullName:
        from: fullName
      fullName_Unsign:
        from: fullName_Unsign
      commonName:
        from: commonName
        type: string
      gender:
        from: genderCode
        type: string
      title:
        from: titleCode
        type: string
      age:
        from: age
        type: string
      effectiveDate:
        from: effectiveDateFrom
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      jobDataEffectiveDateFrom:
        from: jobDataEffectiveDateFrom
        typeOptions:
          func: timestampToDateTime
      jobDataEffectiveDateTo:
        from: jobDataEffectiveDateTo
        typeOptions:
          func: timestampToDateTime
      ignorequeryfilter:
        from: ignorequeryfilter
      dateOfBirth:
        from: birthDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      dateOfDeath:
        from: dateOfDeath
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      hireDate:
        from: hireDate
        typeOptions:
          func: timestampToDateTime
      hireDateFrom:
        from: hireDateFrom
        type: timestamp
        typeOptions:
          func: timestampToDateTime
          args: dateHour
      hireDateTo:
        from: hireDateTo
        type: timestamp
        typeOptions:
          func: timestampToDateTime
          args: dateHour
      nationalityName:
        from: nationalityName
      nationalityCode:
        from: nationalityCode
        type: string
      otherNationality:
        from: otherNationality
        type: string
      religionCode:
        from: religionCode
        type: string
      maritalStatusCode:
        from: maritalStatusCode
        type: string
      maritalDate:
        from: maritalDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      highestEducationLevel:
        from: highestEducationLevelCode
        type: string
      nationalId:
        from: nationalId
        type: int
      status:
        from: status
        type: string
        typeOptions:
          func: AIToBoolean
      townOfBirth:
        from: birthTown
        type: string
      countryOfBirth:
        from: birthCountryCode
        type: string
      socialName:
        from: socialName
      specialName:
        from: specialName
      avatarFile:
        from: avatarFile
      employeeLevelId:
        from: employeeLevelId
      nationCode:
        from: nationalityCode
      hrStatus:
        from: hrStatusCode
      jobDataId:
        from: jobDataId
      jobIndicatorCode:
        from: jobIndicatorCode
      jobIndicatorName:
        from: jobIndicatorName
      nationalIssueDate:
        from: nationalIssueDate
        typeOptions:
          func: timestampToDateTime
      nationalIssuePlaceName:
        from: nationalIssuePlaceName
      phoneNumber:
        from: phoneNumber
      fullEmail:
        from: fullEmail
      fullPhoneNumber:
        from: fullPhoneNumber
      fullNationID:
        from: fullNationID
      pitCode:
        from: pitCode
      fullPitCodeNumber:
        from: fullPitCodeNumber
      effectiveDateTo:
        from: effectiveDateTo
        typeOptions:
          func: timestampToDateTime
      foreigner:
        from: foreigner
        type: string
        typeOptions:
          func: YNToBoolean
      employeeLevelCode:
        from: employeeLevelCode
        type: string
      jobdataId:
        from: jobdataId

  - name: incumbentHistory
    config:
      employeeId:
        from: employeeId
      fullName:
        from: fullName
      email:
        from: email
      userName:
        from: userName
      jobCode:
        from: jobCode
      jobName:
        from: jobName
      departmentCode:
        from: departmentCode
      departmentName:
        from: departmentName
      companyCode:
        from: companyCode
      companyName:
        from: companyName
      startDate:
        from: startDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      endDate:
        from: endDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      avatar:
        from: avatar
      yearOfWork:
        from: yearOfWork
  - name: byPosition
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: indexFrom
    config:
      appointmentDate:
        from: appointmentDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      id:
        from: id
      employeeId:
        from: employeeId
      fullName:
        from: fullName
      attachFile:
        from: attachFile
      avatarFile:
        from: avatarFile
      email:
        from: email
      effectiveDate:
        from: effectiveDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime

  - name: lvInfo
    config:
      id:
        from: employeeId
      employeeId:
        from: employeeId
      fullName:
        from: fullName
      positionCode:
        from: positionCode
      positionName:
        from: positionName
      businessUnitCode:
        from: businessUnitCode
      jobCode:
        from: jobCode
      jobName:
        from: jobName
      departmentCode:
        from: departmentCode
      departmentName:
        from: departmentName
      departmentShortName:
        from: departmentShortName
      companyName:
        from: companyName
      companyShortName:
        from: companyShortName
      totalParent:
        from: totalParent
      totalChild:
        from: totoalChild
      directPositionId:
        from: directPositionId
      matrixManager:
        from: matrixManager
      effectiveDate:
        from: effectiveDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      totalParentMatrix:
        from: totalParentMatrix
      totalChildMatrix:
        from: totalChildMatrix
      totalChildDirect:
        from: totalChildDirect
      numberChildDirect:
        from: numberChildDirect
      numberChildMatrix:
        from: numberChildMatrix

  - name: positionInfo
    config:
      item:
        from: item
      childs:
        from: childs
      effectiveDate:
        from: effectiveDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      organizationId:
        from: organizationId
      organizationType:
        from: organizationType
  - name: ernOirEmp
    config:
      jobDataMatchOIR:
        from: jobDataMatchOIR
      employment:
        from: employment
      employeeRecordNumber:
        from: employeeRecordNumber
      organizationalInstanceRecord:
        from: organizationalInstanceRecord
      effectiveDate:
        from: effectiveDate
  - name: _treeLevel
    config:
      item:
        from: item
      childs:
        from: childs
      effectiveDate:
        from: effectiveDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime

  - name: UplevelOptions
    config:

  - name: calculateEmployment
    config:
      id:
        from: id
        type: string
      actionCode:
        from: actionCode
        type: string
      employeeGroupCode:
        from: employeeGroupCode
        type: string
      organizationalInstanceRcd:
        from: organizationalInstanceRcd
        type: string
      groupOriginalStartDate:
        from: groupOriginalStartDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      oirOriginalStartDate:
        from: oirOriginalStartDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      companyCode:
        from: companyCode
        type: string
      effectiveDate:
        from: effectiveDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      employeeSubGroupCode:
        from: employeeSubGroupCode
        type: string
      employeeRecordNumber:
        from: employeeRecordNumber
        type: string
      groupSeniority:
        from: groupSeniority
        type: string
      organizationalInstanceSeniority:
        from: organizationalInstanceSeniority
        type: string
      externalExperience:
        from: externalExperience
        type: string

  - name: _Access_Type
    config:
      accessType:
        from: accessType
        type: string

defaultQuery:
auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: personals
crudConfig:
  query:
  params:
    id:
      field: id
      type: string
      primary: true
    code:
      field: code
      type: string
    empId:
      field: empId
      type: string
    positionCode:
      field: positionCode
      type: string
    effectiveDate:
      field: effectiveDate
      type: string
    effectiveDateTo:
      field: effectiveDateTo
      type: string
  routes:
    exclude:
      - createManyBase
      - replaceOneBase

routes:
  - path: /api/personals
    method: GET
    model: _
    isExtendedFilter: true
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'personals'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        EffectiveDate: ':{effectiveDate}:'
        Search: ':{search}:'
        ignorequeryfilter: ':{ignorequeryfilter}:'
        Filter: '::{filter}::'
      transform: '$ ~> | $.data | ($mapNameAndCode := function($name,$code){ $name and $code ? $name & " - " & $code : $name ? $name : $code ? $code : ""} ;{ "companyView": $mapNameAndCode($.companyName , $.company) , "positionView": $mapNameAndCode($.positionName, $.position), "jobView": $mapNameAndCode($.jobName , $.job), "departmentView": $mapNameAndCode($.departmentName , $.department), "legalEntityView": $mapNameAndCode($.legalEntityName , $.legalEntity), "businessUnitView": $mapNameAndCode($.businessUnitName , $.bussinessUnit), "locationView": $mapNameAndCode($.locationName , $.location) , "divisionView": $mapNameAndCode($.divisionName , $.division), "email": $.email}) |'

  - path: /api/personals/:id
    method: GET
    model: _

    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'personals/code/:{id}:'
      transfrom: '$'

  - path: /api/personals
    method: POST
    model: _

    query:
    transfrom: '$'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'personals'
      transfrom: '$'

  - path: /api/personals/:id
    model: _
    method: PATCH
    query:
    transfrom: '$'
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: 'personals/:{id}:'

  - path: /api/personals/:id
    method: DELETE
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'personals/:{id}:'
customRoutes:
  - path: /api/personals/all-employees
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'personals/all-employees'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$append([], $map($.data, function($item) {$merge([$item, {"emailsplit": $item.email ? "(" & $substringBefore($item.email, "@") & ")" : ""}])}))'
  - path: /api/personals/rehire-employee
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'personals/rehire-employee'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$'
  - path: /api/personals/:id/histories
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'personals/:{id}:/history'
      transfrom: '$'

  - path: /api/personals/:empId/build-org-chart
    model: positionInfo
    method: GET
    query:
    transfrom: '$'
    upstreamConfig:
      response:
        dataType: object
      method: GET
      query:
        employeeRecordNumber: ':{employeeRecordNumber}:'
        positionCode: ':{positionCode}:'
      path: '/personals/:{empId}:/org-chart'
      transform: '{
        "item": $map($.item, function ($item) {
        $merge([
        $item,
        {"matrixPositionIds": [$item.matrixManager.matrixManager]},
        {"directPositionId": $item.directPositionId},
        {"id": $item.employeeId}
        ])
        }),
        "childs": $map($.childs, function ($item) {
        $merge([
        $item,
        {"matrixPositionIds": [$item.matrixManager.matrixManager]},
        {"directPositionId": $item.directPositionId},
        {"id": $item.employeeId}
        ])
        })[]
        }'

  - path: /api/personals/:empId/org-chart
    model: positionInfo
    method: GET
    query:
    transfrom: '$'
    upstreamConfig:
      response:
        dataType: object
      query:
        effectiveDate: ':{effectiveDate}:'
        employeeRecordNumber: ':{employeeRecordNumber}:'
        positionCode: ':{positionCode}:'
        organizationId: ':{organizationId}:'
        organizationType: ':{organizationType}:'
      method: GET
      path: '/personals/:{empId}:/org-chart'

  - path: /api/personals/show-incumbent-history/:positionCode/:effectiveDate/:effectiveDateTo
    model: incumbentHistory
    method: GET
    query:
    transfrom: '$'
    upstreamConfig:
      query:
        effectiveDate: ':{effectiveDate}:'
        effectiveDateto: ':{effectiveDateTo}:'
        positionCode: ':{positionCode}:'
      response:
        dataType: array
      method: GET
      path: 'personals/show-incumbent-history'
  - path: /api/personals/by-position
    model: byPosition
    method: GET
    query:
    transfrom: '$'
    upstreamConfig:
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        effectiveDate: ':{effectiveDate}:'
        Filter: '::{filter}::'
      response:
        dataType: paginated
      method: GET
      path: 'personals/by-position'

  - path: /api/personals/:empId/tree-level
    model: _treeLevel
    method: GET
    query:
    transfrom: '$'
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'personals/:{empId}:/tree-level'
      query:
        effectiveDate: ':{effectiveDate}:'
      transform: '$map($append($.item,$.childs), function ($item){
        $merge([
        $item,
        {"matrixPositionIds": [$item.matrixManager.matrixManager]},
        {"directPositionId": $item.directPositionId},
        {"id": $item.employeeId}
        ])
        })[]'

  - path: /api/personals/:empId/down-levels
    model: lvInfo
    method: GET
    query:
      Filter: '::{filter}::'
    transfrom: '$'
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'personals/:{empId}:/down-levels'
      query:
        effectiveDate: ':{effectiveDate}:'
      transform: '$map($, function ($item){
        $merge([
        $item,
        {"matrixPositionIds": [$item.matrixManager.matrixManager]},
        {"directPositionId": $item.directPositionId},
        {"id": $item.employeeId}
        ])
        })[]'

  - path: /api/personals/:empId/up-levels
    model: UplevelOptions
    method: GET
    query:
      Filter: '::{filter}::'
    transfrom: '$'
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'personals/:{empId}:/up-levels'
      query:
        effectiveDate: ':{effectiveDate}:'
        currentLevel: ':{currentLevel}:'
        showInactive: ':{showInactive}:'
        employeeRecordNumber: ':{employeeRecordNumber}:'
      transform: '$map($, function ($item){
        $merge([
        $item,
        {"matrixPositionIds": [$item.matrixManager.matrixManager]},
        {"directPositionId": $item.directPositionId},
        {"id": $item.employeeId}
        ])
        })[]'

  - path: /api/personals/:empId/job-datas/ern-oir-employment
    method: GET
    model: ernOirEmp
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'personals/:{empId}:/job-datas/ern-oir-employment'
      query:
        EmployeeGroupCode: ':{employeeGroupCode}:'
        CompanyCode: ':{companyCode}:'
        EffectiveDate: ':{rehireDate}:'
        ActionCode: ':{actionCode}:'
      transform: '$'

  - path: /api/personals/export
    method: GET
    model: _
    query:
    isExtendedFilter: true
    upstreamConfig:
      method: GET
      response:
        dataType: binary
      path: 'personals/export'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$'

  - path: /api/personals/personal-info
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'personals/personal-info'
      query:
        requestToken: ':{requestToken}:'
      transform: '$'

  - path: /api/personals/create-employee
    method: POST
    dataType: 'formData'
    model: _createEmployee
    query:
    transform: '$'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'personals/create-employee'
      transform: '$'

  - path: /api/personals/valid-create-full
    model: _
    method: GET
    query:
    transfrom: '$'
    upstreamConfig:
      response:
        dataType: object
      method: GET
      path: '/personals/valid-create-full'
      query:
        FirstName: '::{firstName}::'
        MiddleName: '::{middleName}::'
        LastName: '::{lastName}::'
        BirthDate: '::{birthDate}::'
        CountryCode: '::{countryCode}::'
        Type: '::{type}::'
        Number: '::{number}::'
        Enabled: '::{enabled}::'
        CheckValidCreateFull: '::{validationType}::'

  - path: /api/personals/all-employees-job-datas
    method: GET
    model: _
    isExtendedFilter: true
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'personals/all-employees-job-datas'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Search_Unsign: ':{search_unsign}:'
        Filter: '::{filter}::'
        effectiveDate: '::{effectiveDate}::'
      transform: '$append([], $map($.data, function($item) {$merge([$item, {"emailsplit": $item.email ? "(" & $substringBefore($item.email, "@") & ")" : ""}])}))'

  - path: /api/personals/all-employees-job-datas-dropdownlist
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'personals/all-employees-job-datas'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Search_Unsign: ':{search_unsign}:'
        Filter: '::{filter}::'
      transform: '$'

  - path: /api/personals/calculate-employment
    method: GET
    model: calculateEmployment
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'personals/employments/calculate-employment'
      query:
        ActionCode: '::{actionCode}::'
        EmployeeGroupCode: '::{employeeGroupCode}::'
        OrganizationalInstanceRcd: '::{organizationalInstanceRcd}::'
        GroupOriginalStartDate: '::{groupOriginalStartDate}::'
        OIROriginalStartDate: '::{oirOriginalStartDate}::'
        CompanyCode: '::{companyCode}::'
        EffectiveDate: '::{effectiveDate}::'
        EmployeeSubGroupCode: '::{employeeSubGroupCode}::'
        EmployeeRecordNumber: '::{employeeRecordNumber}::'
      transform: '$'

  - path: /api/personals/:empId/get-access-type
    method: GET
    model: _Access_Type
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'personals/:{empId}:/get-access-type'
      transform: '$'

  - path: /api/personals/:empId/allow-to-get-info
    method: GET
    query:
    upstreamConfig:
      method: GET
      path: 'personals/:{empId}:/allow-to-get-info'
      transform: '$'
