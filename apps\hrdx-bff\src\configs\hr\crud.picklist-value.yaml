controller: picklists-values/:pickListCode
upstream: ${{UPSTREAM_HR_URL}}

models:
  - name: _getValueInfo
    config:
      catalogTypeId:
        from: catalogTypeId
      code:
        from: code
      name:
        from: name
      effectiveDate:
        from: effectiveDate
        typeOptions:
          func: timestampToDateTime
      effectiveDateFrom:
        from: effectiveDateFrom
        typeOptions:
          func: timestampToDateTime
      effectiveDateTo:
        from: effectiveDateTo
        typeOptions:
          func: timestampToDateTime
  - name: _
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: totalPages
    config:
      id:
        from: id
      catalogTypeId:
        from: catalogTypeId
      code:
        from: code
        type: string
      name:
        from: name
        type: string
        typeOptions:
          func: stringToMultiLang
      status:
        from: enabled
        typeOptions:
          func: YNToBoolean
      defaultYN:
        from: defaultYN
        typeOptions:
          func: YNToBoolean
      picklistYN:
        from: picklistYN
        typeOptions:
          func: YNToBoolean
      effectiveDateQuery:
        from: effectiveDateQuery
        typeOptions:
          func: timestampToDateTime
      effectiveDate:
        from: effectiveDateFrom
        typeOptions:
          func: timestampToDateTime
      effectiveDateTo:
        from: effectiveDateTo
        typeOptions:
          func: timestampToDateTime
      linkCatalogTypeData:
        from: linkCatalogTypeData
        arrayChildren:
          name:
            from: name
            typeOptions:
              func: stringToMultiLang
      parentCode:
        from: parentCode
      parentName:
        from: parent.name
      linkCatalogDataName:
        from: linkCatalogDataName
      coL501:
        from: coL501
      codE501:
        from: codE501
      typE501:
        from: typE501
      coL201:
        from: coL201
        typeOptions:
          func: stringToMultiLang
      coL202:
        from: coL202
        typeOptions:
          func: stringToMultiLang
      coL203:
        from: coL203
        typeOptions:
          func: stringToMultiLang
      coL204:
        from: coL204
        typeOptions:
          func: stringToMultiLang
      coL205:
        from: coL205
        typeOptions:
          func: stringToMultiLang
      coL206:
        from: coL206
      coL207:
        from: coL207
      coL208:
        from: coL208
      coL209:
        from: coL209
      coL210:
        from: coL210
      coL211:
        from: coL211

auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: picklists-values/:pickListCode
crudConfig:
  query:
  params:
    id:
      field: id
      type: string
      primary: true
    code:
      field: code
      type: string
    pickListCode:
      field: pickListCode
      type: string
      configType: url
  routes:
    exclude:
      - recoverOneBase
      - createManyBase
      - replaceOneBase

defaultQuery:

routes:
  - path: /api/picklists-values/:pickListCode
    method: GET
    model: _
    query:
      $and:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: picklists/::{pickListCode}::/admin/values
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: '::{search}::'
        Filter: '::{filter}::'
        EffectiveDate: '::{effectiveDateQuery}::'
        NoFilterEffectiveDate: '::{noFilterEffectiveDate}::'
      transform: '$ ~>| $.data | {"effectiveDateTo": $exists($.effectiveDateTo) and $not($.effectiveDateTo = null) ? $fromMillis($toMillis($.effectiveDateTo) - 86400000)} |'

  - path: /api/picklists-values/:pickListCode/:id
    method: GET
    model: _
    query:
      $and:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'picklists/::{pickListCode}::/value/:{id}:'
      transform: '$ ~>|$|{"parentValueName": $.linkCatalogTypeData[0].name.default}|'

  - path: /api/picklists-values/:pickListCode
    method: POST
    query:
    upstreamConfig:
      method: POST
      path: picklists/::{pickListCode}::/values

  - path: /api/picklists-values/:pickListCode/:id
    model: _
    method: PATCH
    query:
    bodyTransform: "$not($boolean($.coL501)) or $not($boolean($.codE501)) or $not($boolean($.typE501))  ? $merge([$map($keys($), function ($key){  $not($key = 'coL501' or $key = 'codE501' or $key = 'typE501')  ? {$key: $lookup($,$key)}})]) : $"
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: 'picklists/::{pickListCode}::/values/:{id}:'

  - path: /api/picklists-values/:pickListCode/:id
    model: _
    method: DELETE
    query:
    upstreamConfig:
      response:
        dataType: object
      method: DELETE
      path: 'picklists/::{pickListCode}::/values/:{id}:'


customRoutes:

  - path: /api/picklists-values/:pickListCode/get-value-info/:id
    method: GET
    model: _getValueInfo
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'picklists/::{pickListCode}::/get-value-info/:{id}:'
      query:
        EffectiveDate: ':{effectiveDate}:'
        Id: ':{pickListId}:'
      transform: '$'
