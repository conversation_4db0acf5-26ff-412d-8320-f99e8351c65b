controller: search-dulicate-persons
upstream: ${{UPSTREAM_HR_URL}}

models:
  - name: _
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: count
    config:
      id:
        from: id
      code:
        from: code
      fullName:
        from: fullName
      firstName:
        from: firstName
      middleName:
        from: middleName
      lastName:
        from: lastName
      effectiveDate:
        from: effectiveDateFrom
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      effectiveDateTo:
        from: effectiveDateTo
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      dateOfBirth:
        from: dateOfBirth
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      terminateDate:
        from: terminateDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      employeeId:
        from: employeeId
      employeeGroupCode:
        from: employeeGroupCode
      employeeGroup:
        from: employeeGroupName
      jobDataId:
        from: jobDataId
      email:
        from: personalEmail
      personalEmails:
        from: personalEmails
      businessEmail:
        from: businessEmail
      businessEmails:
        from: businessEmails
      HRStatus:
        from: hrStatusName
      hrStatusCode:
        from: hrStatusCode
      gender:
        from: genderName
      genderCode:
        from: genderCode
      blacklistBlocklist:
        from: blacklist
      blacklistBlocklistNote:
        from: blacklistNote
      phone:
        from: phoneNumber
      phoneNumbers:
        from: phoneNumbers
      nationalityCode:
        from: nationalityCode
      otherNationalityCode:
        from: otherNationalityCode
      cccd:
        from: nationalityCCCD
      cmnd:
        from: nationalityCMND
      nationalitiesCCCD:
        from: nationalitiesCCCD
      nationalitiesCMND:
        from: nationalitiesCMND
      pitCode:
        from: pitCode
      pitCodes:
        from: pitCodes
      companyCode:
        from: companyCode
      company:
        from: companyName
      passport:
        from: passport
      nationalitiesPassport:
        from: nationalitiesPassport
      nationality:
        from: nationalityName
      nationalities:
        from: nationalities
      personType:
        from: personTypeName
      avatarFile:
        from: avatarFile

auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: properties/search-dulicate-persons
crudConfig:
  query:
  params:
    id:
      field: id
      type: string
      primary: true
    code:
      field: code
      type: string
  routes:
    exclude:
      - createManyBase
      - replaceOneBase
defaultQuery:

routes:
  - path: /api/search-dulicate-persons
    method: GET
    model: _
    isExtendedFilter: true
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
        dataKey: data
      path: "properties/search-dulicate-persons"
      query:
        Page: ":{options.page}:"
        PageSize: ":{options.limit}:"
        OrderBy: ":{options.sort}:"
        Search: ":{search}:"
        Filter: "::{filter}::"
      transform: '$ ~> | $.data | {"nationalIDInformation": $append($.nationalitiesCCCD, $.nationalitiesCMND)[]}|'

customRoutes:

