controller: report-parameter
upstream: ${{UPSTREAM_REP_URL}}

models:
  - name: _
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: count
    config:
      id:
        from: id
      scheduleName:
        from: scheduleName
        type: string
      author:
        from: author
        type: string
      occurrenceCode:
        from: occurrenceCode
        type: string
      firstOccurrence:
        from: firstOccurrence
        typeOptions:
          func: timestampToDateTime
          args: dateHour
          isNewVersion: true
      status:
        from: statusDesc
      fileId:
        from: fileId
      code:
        from: code
        type: string
      functionCode:
        from: functionCode
        type: string
      recurringPatternCode:
        from: recurringPatternCode
        type: string
      listDayCode:
        from: listDayCode
        type: string
      jsonParam:
        from: jsonParam
        type: string
      jsonParamDetailFE:
        from: jsonParamDetailFE
        type: string
      scheduleDate:
        from: scheduleDate
        typeOptions:
          func: timestampToDateTime
          isNewVersion: true
      endingOn:
        from: endingOn
        typeOptions:
          func: timestampToDateTime
          isNewVersion: true
          args: dateHour
      scheduleType:
        from: scheduleType
        type: string
      scheduleFormat:
        from: scheduleFormat
        type: string
      reportTypeCode:
        from: reportTypeCode
        type: string
      listEmail:
        from: listEmail
        type: string
      isSendNotiOnStart:
        from: isSendNotiOnStart
        type: boolean
        typeOptions:
          func: YNToBoolean
      isSendNotiOnCompletion:
        from: isSendNotiOnCompletion
        type: boolean
        typeOptions:
          func: YNToBoolean
      isRunNow:
        from: isRunNow
        type: boolean
        typeOptions:
          func: YNToBoolean
      statusCode:
        from: status
        type: number
      scheduleFormatDesc:
        from: scheduleFormatDesc
        type: string
      isLastPeriod:
        from: isLastPeriod
      isCancellation:
        from: isCancellation
      isBaseJob:
        from: isBaseJob
      reportParameterId:
        from: reportParameterId
      sourceJobId:
        from: sourceJobId
      triggerJobId:
        from: triggerJobId

auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: report-parameter
crudConfig:
  query:
    sort:
      - field: firstOccurrence
        order: DESC
  params:
    id:
      field: id
      type: string
      primary: true
    empId:
      field: empId
      type: string
  routes:
    exclude:
      - createManyBase
      - replaceOneBase

defaultQuery:

routes:
  - path: /api/report-parameter
    method: GET
    model: _
    query:
      $and:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
        dataKey: data
      path: report-parameter
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        Search: ":{search}:"
        OrderBy: ':{options.sort}:'
        Filter: "::{filter}::"
      transform: "$"

  - path: /api/report-parameter/:id
    method: GET
    query:
      $and:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'report-parameter/:{id}:'

  - path: /api/report-parameter
    method: POST
    model: _

    query:
    transform: '$'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'report-parameter'
      transform: '$'

  - path: /api/report-parameter/:id
    model: _
    method: PATCH
    query:
    transform: '$'
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: 'report-parameter/:{id}:'

  - path: /api/report-parameter/:id
    method: DELETE
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'report-parameter/:{id}:'

customRoutes:
  - path: /api/report-parameter/status
    method: PATCH
    model: _
    query:
    upstreamConfig:
      method: PUT
      response:
        dataType: object
      path: 'report-parameter/status'
      transform: '$'

  - path: /api/report-parameter/start-now-report-status
    method: POST
    model: _
    query:
    upstreamConfig:
      method: POST
      path: 'report-parameter/start-now-report-status'
      transform: '$'

  - path: /api/report-parameter/cancel-report-status
    method: POST
    model: _
    query:
    upstreamConfig:
      method: POST
      path: 'report-parameter/cancel-report-status'
      transform: '$'

  - path: /api/report-parameter/detail/:id
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'report-parameter-detail/:{id}:'
      query:
        ReportStatusId: '::{reportStatusId}::'
      transform: '$'
