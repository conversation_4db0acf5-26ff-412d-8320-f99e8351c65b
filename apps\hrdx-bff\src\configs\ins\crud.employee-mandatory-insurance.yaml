controller: employee-mandatory-insurance
upstream: ${{UPSTREAM_INS_URL}}

models:
  - name: _
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: totalPages
    config:
      id:
        from: id
        type: string
      code:
        from: employeeId
        type: string
      employeeId:
        from: employeeId
        type: string
      employeeRecordNumber:
        from: employeeRecordNumber
        type: string
      employeeName:
        from: fullName
        type: string
      contractTypeCode:
        from: contractTypeCode
        type: sting
      contractTypeName:
        from: contractTypeName
        type: string
      localExpatCode:
        from: localExpatCode
        type: string
      localExpatName:
        from: localExpatName
        type: string
      insuranceContributionRateCode:
        from: insuranceContributionRateCode
        type: string
      insuranceContributionRateName:
        from: insuranceContributionRateName
        type: string
      totalEmployerInsRate:
        from: totalEmployerInsRate
        type: string
      totalEmployeeInsRate:
        from: totalEmployeeInsRate
        type: string
      totalInsRate:
        from: totalInsRate
        type: string
      effectiveDate:
        from: effectiveDateFrom
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      status:
        from: enabled
        type: string
        typeOptions:
          func: YNToBoolean
      note:
        from: note
        type: string
        typeOptions:
          func: stringToMultiLang
      insuranceTypeCode:
        from: insTypes.insuranceTypeCode
        type: string
      insuranceTypeName:
        from: insTypes.insuranceTypeName
        type: string
      createdBy:
        from: createdBy
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
  - name: _getModel
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: totalPages
    config:
      id:
        from: id
        type: string
      code:
        from: employeeId
        type: string
      employeeId:
        from: employeeId
        type: string
      employeeRecordNumber:
        from: employeeRecordNumber
        type: string
      employeeName:
        from: fullName
        type: string
      contractTypeCode:
        from: contractTypeCode
        type: sting
      contractTypeName:
        from: contractTypeName
        type: string
      localExpatCode:
        from: localExpatCode
        type: string
      localExpatName:
        from: localExpatName
        type: string
      effectiveDate:
        from: effectiveDateFrom
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      status:
        from: enabled
        type: string
        typeOptions:
          func: YNToBoolean
      note:
        from: note
        type: string
        typeOptions:
          func: stringToMultiLang
      insTypes:
        from: insTypes
      insuranceTypeCode:
        from: insTypes.insuranceTypeCode
        type: string
      insuranceGroupCode:
        from: insTypes.insuranceGroupCode
        type: string
      insuranceTypeName:
        from: insTypes.insuranceTypeName
        type: string
      insuranceGroupName:
        from: insTypes
        arrayChildren:
          value:
            from: insuranceGroupName
            type: string
      createdBy:
        from: createdBy
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
  - name: _postModel
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: totalPages
    config:
      id:
        from: id
        type: string
      code:
        from: employeeId
        type: string
      employeeId:
        from: employeeId
        type: string
      employeeRecordNumber:
        from: employeeRecordNumber
        type: string
      employeeName:
        from: fullName
        type: string
      effectiveDate:
        from: effectiveDateFrom
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      status:
        from: enabled
        type: string
        typeOptions:
          func: YNToBoolean
      note:
        from: note
        type: string
        typeOptions:
          func: stringToMultiLang
      insTypes:
        from: insTypes
      createdBy:
        from: createdBy
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
  - name: _DELETE
    config:
      ids:
        from: ids
auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: employee-mandatory-insurance
crudConfig:
  query:
    sort:
      - field: employeeId
        order: ASC
      - field: effectiveDateFrom
        order: DESC
  params:
    id:
      field: id
      type: string
      primary: true
    code:
      field: code
      type: string
    employeeId:
      field: employeeId
      type: string
    insuranceType:
      type: string
      field: insuranceType
  routes:
    exclude:
      - createManyBase
      - replaceOneBase
defaultQuery:

routes:
  - path: /api/employee-mandatory-insurance
    method: GET
    model: _getModel
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'employee-mandatory-insurance'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
        insuranceType: '::{insuranceType}::'
      transform: '$ ~> | $.data | {
      "insuranceGroupName": $count($filter($.insuranceGroupName, function($item) {
        $count($keys($item)) > 0
      })) = 0 ? [] : $map($.insuranceGroupName, function($item) {$item.value})[],
      "insuranceTypeCode": $distinct($.insuranceTypeCode)[],
      "insuranceTypeName": $distinct($.insuranceTypeName)[]
      }|'

  - path: /api/employee-mandatory-insurance/:id
    method: GET
    model: _getModel

    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'employee-mandatory-insurance/:{id}:'
      transform: '$ ~> | $ | {
      "insuranceGroupCode": $filter($.insuranceGroupCode, function($item) { $item != null })[],
      "insuranceTypeCode": $distinct($.insuranceTypeCode)[]
      }|'

  - path: /api/employee-mandatory-insurance
    method: POST
    model: _postModel

    query:
    transform: '$'
    bodyTransform: '$ ~> | $ | {"insuranceGroupCode": "INSTYPE_BHXH" in $map($.insuranceTypeCode, function($item) {$item.value})[] ? [] : $.insuranceGroupCode }|'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'employee-mandatory-insurance'
      transform: '$'

  - path: /api/employee-mandatory-insurance/:id
    model: _postModel
    method: PATCH
    query:
    transform: '$'
    bodyTransform: '$ ~> | $ | {"insuranceGroupCode": "INSTYPE_BHXH" in $map($.insuranceTypeCode, function($item) {$item.value})[] ? [] : $.insuranceGroupCode }|'
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: 'employee-mandatory-insurance/:{id}:'

  - path: /api/employee-mandatory-insurance/:id
    method: DELETE
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'employee-mandatory-insurance/:{id}:'

customRoutes:
  - path: /api/employee-mandatory-insurance/:id/histories
    method: GET
    model: _getModel
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      query:
        employeeRecordNumber: '::{employeeRecordNumber}::'
      path: 'employee-mandatory-insurance/:{id}:/history'
      transform: '$ ~> | $.data | {
      "insuranceGroupName": $count($filter($.insuranceGroupName, function($item) {
        $count($keys($item)) > 0
      })) = 0 ? [] : $map($.insuranceGroupName, function($item) {$item.value})[],
      "insuranceTypeName": $distinct($.insuranceTypeName)[],
      "insuranceGroupCode": $filter($.insuranceGroupCode, function($item) { $item != null })[],
      "insuranceTypeCode": $distinct($.insuranceTypeCode)[]
      }|'

  # export
  - path: /api/employee-mandatory-insurance/export
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: binary
      path: 'employee-mandatory-insurance/:export'
      query:
        Page: '1'
        PageSize: '1000'
        OrderBy: 'employeeId asc, effectiveDateFrom desc'
        Search: '::{search}::'
        Filter: '::{filter}::'
      transform: '$'

  #delete multi
  - path: /api/${{controller}}/multidelete
    method: DELETE
    model: _DELETE
    upstreamConfig:
      method: DELETE
      path: 'employee-mandatory-insurance'

