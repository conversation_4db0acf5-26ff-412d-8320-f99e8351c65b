controller: insurance-base-salary
upstream: ${{UPSTREAM_INS_URL}}

models:
  - name: _
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: totalPages
    config:
      id:
        from: id
        type: string
      countryCode:
        from: countryCode
        type: string
      countryName:
        from: countryName
        type: string
      baseSalary:
        from: baseSalary
        typeOptions:
          func: toString
      maxSalaryMandatoryIns:
        from: maxSalaryMandatoryIns
        typeOptions:
          func: toString
      currencyCode:
        from: currencyCode
        type: string
      currencyName:
        from: currencyName
        type: string
      effectiveDateFrom:
        from: effectiveDateFrom
        typeOptions:
          func: timestampToDateTime
      enabled:
        from: enabled
        typeOptions:
          func: YNToBoolean
      note:
        from: note
        type: string
        typeOptions:
          func: stringToMultiLang

      createdAt:
        from: createdAt
        typeOptions:
          func: timestampToDateTime
      createdBy:
        from: createdBy
      updatedAt:
        from: updatedAt
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy

  - name: _countryCodeNameModel
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: totalPages
    config:
      id:
        from: id
        type: string
      countryCode:
        from: $
        objectChildren:
          value:
            from: countryCode
          label:
            from: countryName
      countryName:
        from: countryName
        type: string
      baseSalary:
        from: baseSalary
        typeOptions:
          func: toString
      maxSalaryMandatoryIns:
        from: maxSalaryMandatoryIns
        typeOptions:
          func: toString
      currencyCode:
        from: currencyCode
        type: string
      currencyName:
        from: currencyName
        type: string
      effectiveDateFrom:
        from: effectiveDateFrom
        typeOptions:
          func: timestampToDateTime
      enabled:
        from: enabled
        typeOptions:
          func: YNToBoolean
      note:
        from: note
        type: string
        typeOptions:
          func: stringToMultiLang

      createdAt:
        from: createdAt
        typeOptions:
          func: timestampToDateTime
      createdBy:
        from: createdBy
      updatedAt:
        from: updatedAt
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy

  - name: _DELETE
    config:
      ids:
        from: ids

auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: insurance-base-salary
crudConfig:
  query:
    sort:
      - field: countryName
        order: ASC
      - field: effectiveDateFrom
        order: DESC
  params:
    id:
      field: id
      type: string
      primary: true
    code:
      field: code
      type: string
  routes:
    exclude:
      - createManyBase
      - replaceOneBase
defaultQuery:

routes:
  - path: /api/insurance-base-salary
    method: GET
    model: _

    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'api/insurance-base-salary'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$'

  - path: /api/insurance-base-salary/:id
    method: GET
    model: _countryCodeNameModel

    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'api/insurance-base-salary/:{id}:'
      transform: '$'

  - path: /api/insurance-base-salary
    method: POST
    model: _countryCodeNameModel

    query:
    transform: '$'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'api/insurance-base-salary'
      transform: '$'

  - path: /api/insurance-base-salary/:id
    model: _countryCodeNameModel

    method: PATCH
    query:
    transform: '$'
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: 'api/insurance-base-salary/:{id}:'

  - path: /api/insurance-base-salary/:id
    method: DELETE
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'api/insurance-base-salary/:{id}:'

customRoutes:
  - path: /api/insurance-base-salary/export
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: binary
      path: 'insurance-base-salary/:export'
      query:
        Page: '1'
        PageSize: '1000'
        OrderBy: 'countryName asc, effectiveDateFrom desc'
        Search: '::{search}::'
        Filter: '::{filter}::'
      transform: '$'

  - path: /api/insurance-base-salary/multidelete
    method: DELETE
    model: _DELETE
    upstreamConfig:
      method: DELETE
      path: 'api/insurance-base-salary'
