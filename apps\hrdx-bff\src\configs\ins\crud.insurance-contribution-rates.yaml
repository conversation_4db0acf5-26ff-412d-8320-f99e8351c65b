controller: insurance-contribution-rates
upstream: ${{UPSTREAM_INS_URL}}

models:
  - name: _
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: count
    config:
      id:
        from: id
        type: string
      code:
        from: code
        type: string
      countryCode:
        from: $
        objectChildren:
          value:
            from: countryCode
          label:
            from: countryName
      countryName:
        from: countryName
        type: string
      shortName:
        from: shortName
        type: string
        typeOptions:
          func: stringToMultiLang
      name:
        from: name
        type: string
        typeOptions:
          func: stringToMultiLang
      totalEmployerInsRate:
        from: totalEmployerInsRate
        type: number
      totalEmployeeInsRate:
        from: totalEmployeeInsRate
        type: number
      totalInsRate:
        from: totalInsRate
        type: number
      effectiveDate:
        from: effectiveDateFrom
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      effectiveDateTo:
        from: effectiveDateTo
        typeOptions:
          func: timestampToDateTime
      note:
        from: note
        type: string
        typeOptions:
          func: stringToMultiLang
      status:
        from: enabled
        type: string
        typeOptions:
          func: YNToBoolean
      createdBy:
        from: createdBy
        type: string
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
        type: string
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      details:
        from: details
        arrayChildren:
          id:
            from: id
            type: string
          insuranceContributionRateId:
            from: insuranceContributionRateId
            type: string
          insuranceTypeCode:
            from: insuranceTypeCode
            type: string
          insuranceTypeName:
            from: insuranceTypeName
            type: string
          insuranceGroupCode:
            from: insuranceGroupCode
            type: string
          insuranceGroupName:
            from: insuranceGroupName
            type: string
          employerInsRate:
            from: employerInsRate
            type: number
          employeeInsRate:
            from: employeeInsRate
            type: number
          createdBy:
            from: createdBy
            type: string
          createdAt:
            from: createdAt
            type: timestamp
            typeOptions:
              func: timestampToDateTime
          updatedBy:
            from: updatedBy
            type: string
          updatedAt:
            from: updatedAt
            type: timestamp
            typeOptions:
              func: timestampToDateTime

      insuranceTypeCode:
        from: insuranceTypeCode
        type: string

  - name: _
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: indexFrom
    config:
      id:
        from: id
        type: string
      code:
        from: code
        type: string

  - name: _DELETE
    config:
      ids:
        from: ids
auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: insurance-contribution-rates
crudConfig:
  query:
    sort:
      - field: countryName
        order: ASC
      - field: effectiveDate
        order: DESC
      - field: code
        order: ASC
  params:
    id:
      field: id
      type: string
      primary: true
    code:
      field: code
      type: string
    InsuranceTypeCode:
      field: InsuranceTypeCode
      type: string
  routes:
    exclude:
      - createManyBase
      - replaceOneBase
defaultQuery:

routes:
  # list table
  - path: /api/insurance-contribution-rates
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
        dataKey: data
      path: 'insurance-contribution-rates'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$'

  # detail
  - path: /api/insurance-contribution-rates/:id
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'insurance-contribution-rates/:{id}:'
      transform: '$'

  # create
  - path: /api/insurance-contribution-rates
    method: POST
    model: _
    query:
    transform: '$'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'insurance-contribution-rates'
      transform: '$'

  # update
  - path: /api/insurance-contribution-rates/:id
    method: PATCH
    model: _
    query:
    transform: '$'
    upstreamConfig:
      method: PUT
      response:
        dataType: object
      path: 'insurance-contribution-rates/:{id}:'

  # delete
  - path: /api/insurance-contribution-rates/:id
    method: DELETE
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'insurance-contribution-rates/:{id}:'

customRoutes:
  # export
  - path: /api/insurance-contribution-rates/export
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: binary
      path: 'insurance-contribution-rates/:export'
      query:
        Page: '1'
        PageSize: '1000'
        OrderBy: 'countryName asc, effectiveDateFrom desc, code asc'
        Search: '::{search}::'
        Filter: '::{filter}::'
        Location: '::{locationCode}::'
      transform: '$'

  - path: /api/insurance-contribution-rates/ins-type
    method: POST
    model: _
    query:
    transform: '$'
    upstreamConfig:
      method: POST
      response:
        dataType: array
      path: 'insurance-contribution-rates/ins-type'
      transform: '$'

  - path: /api/insurance-contribution-rates/:id/histories
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'insurance-contribution-rates/:{id}:/history'
      transform: '$'

  - path: /api/${{controller}}/multidelete
    method: DELETE
    model: _DELETE
    upstreamConfig:
      method: DELETE
      path: 'insurance-contribution-rates'

