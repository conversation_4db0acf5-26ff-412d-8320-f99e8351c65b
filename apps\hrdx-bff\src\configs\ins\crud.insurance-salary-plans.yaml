controller: insurance-salary-plans
upstream: ${{UPSTREAM_INS_URL}}

models:
  - name: _
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: count
    config:
      id:
        from: id
        type: string
      effectiveDate:
        from: effectiveDateFrom
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      effectiveDateTo:
        from: effectiveDateTo
        typeOptions:
          func: timestampToDateTime
      code:
        from: code
        type: string
      name:
        from: name
        type: string
        typeOptions:
          func: stringToMultiLang
      version:
        from: version
        type: string
        typeOptions:
          func: stringToMultiLang
      countryCode:
        from: $
        objectChildren:
          value:
            from: countryCode
          label:
            from: countryName
      countryName:
        from: countryName
        type: string
      companyCode:
        from: $
        objectChildren:
          value:
            from: companyCode
          label:
            from: companyName,companyCode
            typeOptions:
              func: fieldsToNameCode
      companyName:
        from: companyName
        type: string
      legalEntityCode:
        from: $
        objectChildren:
          value:
            from: legalEntityCode
          label:
            from: legalEntityName,legalEntityCode
            typeOptions:
              func: fieldsToNameCode
      legalEntityName:
        from: legalEntityName
        type: string
      monthNextStepIncrement:
        from: monthNextStepIncrement
        type: string
      attachFile:
        from: attachFile
        type: string
      attachFileName:
        from: attachFileName
        type: string
      attachFileForm:
        from: attachFileForm
        type: string
      file:
        from: file
      attachFileResults:
        from: attachFileResults
      status:
        from: enabled
        type: string
        typeOptions:
          func: YNToBoolean
      note:
        from: note
        type: string
        typeOptions:
          func: stringToMultiLang
      createdBy:
        from: createdBy
        type: string
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
        type: string
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      viewDetails:
        from: details
        arrayChildren:
          salaryJobTitleCode:
            from: salaryJobTitleCode
            type: string
          insuranceSalaryClassCode:
            from: insuranceSalaryClassCode
            type: string
          insuranceSalaryClassName:
            from: insuranceSalaryClassName
            type: string
          insuranceRegionCode:
            from: insuranceRegionCode
            type: string
          insuranceRegionName:
            from: insuranceRegionName
            type: string
          insuranceSalaryLevelCode:
            from: insuranceSalaryLevelCode
            type: string
          insuranceSalaryLevelName:
            from: insuranceSalaryLevelName
            type: string
          insuranceSalaryStepCode:
            from: insuranceSalaryStepCode
            type: string
          insuranceSalaryStepName:
            from: insuranceSalaryStepName
            type: string
          classificationCode:
            from: classificationCode
            type: string
          classificationName:
            from: classificationName
            type: string
          insuranceSalary:
            from: insuranceSalary
            type: number
          currencyCode:
            from: currencyCode
            type: string
          currencyName:
            from: currencyName
            type: string
      details:
        from: details
        arrayChildren:
          salaryJobTitleCode:
            from: salaryJobTitleCode
            type: string
          insuranceSalaryClassCode:
            from: insuranceSalaryClassCode
            type: string
          insuranceSalaryClassName:
            from: insuranceSalaryClassName
            type: string
          insuranceRegionCode:
            from: insuranceRegionCode
            type: string
          insuranceRegionName:
            from: insuranceRegionName
            type: string
          insuranceSalaryLevelCode:
            from: insuranceSalaryLevelCode
            type: string
          insuranceSalaryLevelName:
            from: insuranceSalaryLevelName
            type: string
          insuranceSalaryStepCode:
            from: insuranceSalaryStepCode
            type: string
          insuranceSalaryStepName:
            from: insuranceSalaryStepName
            type: string
          classificationCode:
            from: classificationCode
            type: string
          classificationName:
            from: classificationName
            type: string
          insuranceSalary:
            from: insuranceSalary
            type: number
          currencyCode:
            from: currencyCode
            type: string
          currencyName:
            from: currencyName
            type: string
  - name: _1
    config:
      salaryJobTitleCode:
        from: salaryJobTitleCode
        type: string
      insuranceSalaryClassCode:
        from: insuranceSalaryClassCode
        type: string
      insuranceSalaryClassName:
        from: insuranceSalaryClassName
        type: string
      insuranceRegionCode:
        from: insuranceRegionCode
        type: string
      insuranceRegionName:
        from: insuranceRegionName
        type: string
      insuranceSalaryLevelCode:
        from: insuranceSalaryLevelCode
        type: string
      insuranceSalaryLevelName:
        from: insuranceSalaryLevelName
        type: string
      insuranceSalaryStepCode:
        from: insuranceSalaryStepCode
        type: string
      insuranceSalaryStepName:
        from: insuranceSalaryStepName
        type: string
      classificationCode:
        from: classificationCode
        type: string
      classificationName:
        from: classificationName
        type: string
      insuranceSalary:
        from: insuranceSalary
        type: number
      currencyCode:
        from: currencyCode
        type: string
      currencyName:
        from: currencyName
        type: string
  - name: _getAllModel
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: count
    config:
      id:
        from: id
        type: string
      effectiveDate:
        from: effectiveDateFrom
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      effectiveDateTo:
        from: effectiveDateTo
        typeOptions:
          func: timestampToDateTime
      code:
        from: code
        type: string
      name:
        from: name
        type: string
        typeOptions:
          func: stringToMultiLang
      version:
        from: version
        type: string
        typeOptions:
          func: stringToMultiLang
      countryCode:
        from: countryCode
        type: string
      countryName:
        from: countryName
        type: string
      companyCode:
        from: companyCode
        type: string
      companyName:
        from: companyName
        type: string
      legalEntityCode:
        from: legalEntityCode
        type: string
      legalEntityName:
        from: legalEntityName
        type: string
      monthNextStepIncrement:
        from: monthNextStepIncrement
        type: string
      attachFile:
        from: attachFile
        type: string
      attachFileName:
        from: attachFileName
        type: string
      attachFileForm:
        from: attachFileForm
        type: string
      file:
        from: file
      attachFileResults:
        from: attachFileResults
      status:
        from: enabled
        type: string
        typeOptions:
          func: YNToBoolean
      note:
        from: note
        type: string
        typeOptions:
          func: stringToMultiLang
      createdBy:
        from: createdBy
        type: string
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
        type: string
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      viewDetails:
        from: details
        arrayChildren:
          salaryJobTitleCode:
            from: salaryJobTitleCode
            type: string
          insuranceSalaryClassCode:
            from: insuranceSalaryClassCode
            type: string
          insuranceSalaryClassName:
            from: insuranceSalaryClassName
            type: string
          insuranceRegionCode:
            from: insuranceRegionCode
            type: string
          insuranceRegionName:
            from: insuranceRegionName
            type: string
          insuranceSalaryLevelCode:
            from: insuranceSalaryLevelCode
            type: string
          insuranceSalaryLevelName:
            from: insuranceSalaryLevelName
            type: string
          insuranceSalaryStepCode:
            from: insuranceSalaryStepCode
            type: string
          insuranceSalaryStepName:
            from: insuranceSalaryStepName
            type: string
          classificationCode:
            from: classificationCode
            type: string
          classificationName:
            from: classificationName
            type: string
          insuranceSalary:
            from: insuranceSalary
            type: number
          currencyCode:
            from: currencyCode
            type: string
          currencyName:
            from: currencyName
            type: string
      details:
        from: details
        arrayChildren:
          salaryJobTitleCode:
            from: salaryJobTitleCode
            type: string
          insuranceSalaryClassCode:
            from: insuranceSalaryClassCode
            type: string
          insuranceSalaryClassName:
            from: insuranceSalaryClassName
            type: string
          insuranceRegionCode:
            from: insuranceRegionCode
            type: string
          insuranceRegionName:
            from: insuranceRegionName
            type: string
          insuranceSalaryLevelCode:
            from: insuranceSalaryLevelCode
            type: string
          insuranceSalaryLevelName:
            from: insuranceSalaryLevelName
            type: string
          insuranceSalaryStepCode:
            from: insuranceSalaryStepCode
            type: string
          insuranceSalaryStepName:
            from: insuranceSalaryStepName
            type: string
          classificationCode:
            from: classificationCode
            type: string
          classificationName:
            from: classificationName
            type: string
          insuranceSalary:
            from: insuranceSalary
            type: number
          currencyCode:
            from: currencyCode
            type: string
          currencyName:
            from: currencyName
            type: string

  - name: _DELETE
    config:
      ids:
        from: ids

auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: insurance-salary-plans

crudConfig:
  query:
    sort:
      - field: companyName
        order: ASC
      - field: legalEntityName
        order: ASC
      - field: effectiveDate
        order: DESC
  params:
    id:
      field: id
      type: string
      primary: true
    code:
      field: code
      type: string
  routes:
    exclude:
      - createManyBase
      - replaceOneBase

defaultQuery:

routes:
  # list table
  - path: /api/insurance-salary-plans
    method: GET
    model: _getAllModel
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
        dataKey: data
      path: 'insurance-salary-plans'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$'

  # detail
  - path: /api/insurance-salary-plans/:id
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'insurance-salary-plans/:{id}:'
      transform: '$ ~> | $ | {"attachFileResults": [{"fileName": $.attachFileName, "attachFile": $.attachFile, "url": "/api/ins-files/download", "fileField": "attachFile", "fileValue": $.attachFile}], "viewDetails": $map($.viewDetails, function($item) {
        $merge([$item,
        {
          "insuranceSalaryStepName": $item.insuranceSalaryStepName & " (" & $item.insuranceSalaryStepCode & ")"
        }])
      })[]
      }|'

  # create
  - path: /api/insurance-salary-plans
    method: POST
    model: _
    query:
    transform: '$'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'insurance-salary-plans'
      transform: '$'

  # update
  - path: /api/insurance-salary-plans/:id
    method: PATCH
    model: _
    query:
    transform: '$'
    upstreamConfig:
      method: PUT
      response:
        dataType: object
      path: 'insurance-salary-plans/:{id}:'

  # delete
  - path: /api/insurance-salary-plans/:id
    method: DELETE
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'insurance-salary-plans/:{id}:'

customRoutes:
  # export
  - path: /api/insurance-salary-plans/export
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: binary
      path: 'insurance-salary-plans/:export'
      query:
        Page: '1'
        PageSize: '1000'
        OrderBy: 'companyName asc, legalEntityName asc, effectiveDateFrom desc, code asc'
        Search: '::{search}::'
        Filter: '::{filter}::'
      transform: '$'

  # create upload
  - path: /api/insurance-salary-plans/upload
    model: _
    method: POST
    dataType: 'formData'
    query:
    transform: '$'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'insurance-salary-plans'
      transform: '$'

  # edit upload
  - path: /api/insurance-salary-plans/:id/upload
    model: _
    method: POST
    dataType: 'formData'
    query:
    transform: '$'
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: 'insurance-salary-plans/:{id}:'
      transform: '$'

  - path: /api/insurance-salary-plans/info
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'insurance-salary-plans/info'
      query:
        CompanyCode: ':{companyCode}:'
        LegalEntityCode: ':{legalEntityCode}:'
        EffectiveDate: ':{effectiveDate}:'
      transform: '$'

  - path: /api/insurance-salary-plans/:id/details
    model: _1
    method: GET
    query:
    upstreamConfig:
      response:
        dataType: array
      method: GET
      path: 'insurance-salary-plans/:{id}:/details'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
        InsuranceRegionCode: '::{insuranceRegionCode}::'
      transform: '$'

    #multidelete
  - path: /api/${{controller}}/multidelete
    method: DELETE
    model: _DELETE
    upstreamConfig:
      method: DELETE
      path: 'insurance-salary-plans'
