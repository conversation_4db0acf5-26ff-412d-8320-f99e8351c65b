controller: assessment-period
upstream: ${{UPSTREAM_PIT_URL}}

models:
  - name: _
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: totalPages
    config:
      id:
        from: id
        type: string
      code:
        from: code
        type: string
      shortName:
        from: shortName
        type: string
        typeOptions:
          func: stringToMultiLang
      legalEntity:
        from: legalEntity
      longName:
        from: name
        type: string
        typeOptions:
          func: stringToMultiLang
      assessmentPeriodType:
        from: assessmentPeriodType.longName
        type: string
      assessmentPeriodTypeCode:
        from: assessmentPeriodTypeCode
        type: string
      period:
        from: period.longName
        type: string
      periodCode:
        from: periodCode
        type: string
      year:
        from: year
        typeOptions:
          func: intYearToDateTime
      yearName:
        from: year
      assessmentPeriodStartDate:
        from: startDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      assessmentPeriodEndDate:
        from: endDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      note:
        from: note
        type: string
        typeOptions:
          func: stringToMultiLang
      createdBy:
        from: createdBy
        type: string
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
        type: string
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
  - name: _legalEntities
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: totalPages
    config:
      id:
        from: id
        type: string
      code:
        from: code
        type: string
      shortName:
        from: shortName
        type: string
      longName:
        from: longName
        type: string
      effectiveDate:
        from: effectiveDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      taxCode:
        from: taxCode
        type: string
      address:
        from: address
        type: string
      telephone:
        from: telephone
        type: string
      assessmentPeriodCode:
        from: assessmentPeriodCode
        type: string
      isHeadOffice:
        from: isHeadOffice
  - name: multiDeleteModel
    config:
      ids:
        type: array
        from: ids

  - name: exportModel
    config:
      id:
        from: id
      code:
        from: code
      shortName:
        from: shortName
        typeOptions:
          func: stringToMultiLang
      longName:
        from: name
        typeOptions:
          func: stringToMultiLang
      assessmentPeriodType:
        from: assessmentPeriodType
      assessmentPeriodTypeCode:
        from: assessmentPeriodTypeCode
      period:
        from: period
      periodCode:
        from: periodCode
      yearName:
        from: year
      year:
        from: year
        typeOptions:
          func: intYearToDateTime
      assessmentPeriodStartDate:
        from: startDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      assessmentPeriodEndDate:
        from: endDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      note:
        from: note
        type: string
        typeOptions:
          func: stringToMultiLang
      updatedBy:
        from: updatedBy
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      createdBy:
        from: createdBy
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      columnNames:
        from: columnNames
        typeOptions:
          func: listKeyFields
      feColumnNames:
        from: feColumnNames
defaultQuery:
auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: assessment-period
crudConfig:
  query:
    sort:
      - field: createdAt
        order: DESC
  params:
    id:
      field: id
      type: string
      primary: true
    code:
      field: code
      type: string
  routes:
    exclude:
      - createManyBase
      - replaceOneBase

routes:
  - path: /api/assessment-period
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'assessment-period'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$merge([$,{"data": $map($.data, function($item) { $merge([$item,{"yearName":$item.yearName != 0? $item.yearName: null}]) })[] } ])'

  - path: /api/assessment-period/:id
    method: GET
    model: _
    transform: '$'
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'assessment-period/:{id}:'
      transform: '$'

  - path: /api/assessment-period
    method: POST
    model: _
    query:
    transform: '$'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'assessment-period'
      transform: '$'

  - path: /api/assessment-period/:id
    model: _
    method: PATCH
    query:
    transform: '$'
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: 'assessment-period/:{id}:'

  - path: /api/assessment-period/:id
    method: DELETE
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'assessment-period/:{id}:'

customRoutes:
  - path: /api/assessment-period/by
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'assessment-period/by'
      query:
        Filter: '::{filter}::'
      transform: '$'

  - path: /api/assessment-period/legal-entities
    method: GET
    model: _legalEntities
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'assessment-period/legal-entities'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
        assessmentPeriodCode: ':{assessmentPeriodCode}:'
        isHeadOffice: ':{isHeadOffice}:'
      transform: '$'

  - path: /api/assessment-period/by-finalize
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'assessment-period/by-finalize'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
        isCancel: '::{isCancel}::'
      transform: '$'

  - path: /api/assessment-period/multidelete
    method: DELETE
    model: multiDeleteModel
    bodyTransform: '{"ids": $.ids[]}'
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'assessment-periods'

  - path: /api/assessment-period/export
    method: POST
    model: exportModel
    query:
    upstreamConfig:
      method: POST
      response:
        dataType: binary
      path: 'assessment-period/export-assessment-period-report:export'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$'
