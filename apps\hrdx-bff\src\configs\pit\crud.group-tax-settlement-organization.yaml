controller: group-tax-settlement-organization
upstream: ${{UPSTREAM_PIT_URL}}

models:
  - name: _
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: totalPages
    config:
      id:
        from: id
        type: string
      code:
        from: code
        # type: string
      country:
        from: country.longName
        type: string
      countryCode:
        from: countryCode
        type: string
      groupLabel:
        from: group.longName
        type: string
      groupCode:
        from: groupCode
      group:
        from: $
        objectChildren:
          value:
            from: groupCode
          label:
            from: group.longName
      companyLabel:
        from: company.longName
        type: string
      companyCode:
        from: companyCode
      company:
        from: $
        objectChildren:
          value:
            from: companyCode
          label:
            from: company.longName
      legalEntities:
        from: legalEntities
      legalEntityCode:
        from: legalEntityCode
      legalEntitiesCode:
        from: legalEntitiesCode
      legalEntitiesName:
        from: legalEntities
      groupTaxSettlementCode:
        from: groupTaxSettlementCode
      groupTaxSettlement:
        from: $
        objectChildren:
          value:
            from: groupTaxSettlementCode
          label:
            from: groupTaxSettlement.longName
      groupTaxSettlementLabel:
        from: groupTaxSettlement.longName
        type: string
      effectiveDate:
        from: effectiveDateFrom
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      effectiveDateTo:
        from: effectiveDateTo
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      note:
        from: note
        type: string
      createdBy:
        from: createdBy
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      dataClosingDate:
        from: dataClosingDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
  - name: multiDeleteModel
    config:
      ids:
        type: array
        from: ids

  - name: _getReportGroupTaxSettlement
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: totalPages
    config:
      id:
        from: id
        type: string
      code:
        from: code
        type: string
      shortName:
        from: shortName
      longName:
        from: longName

  - name: exportModel
    config:
      id:
        from: id
      code:
        from: code
      effectiveDate:
        from: effectiveDateFrom
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      effectiveDateTo:
        from: effectiveDateTo
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      country:
        from: country
      countryCode:
        from: countryCode
      groupLabel:
        from: group
      groupCode:
        from: groupCode
      companyLabel:
        from: company
      companyCode:
        from: companyCode
      legalEntitiesName:
        from: legalEntities
      legalEntitiesCode:
        from: legalEntitiesCode
      groupTaxSettlementLabel:
        from: groupTaxSettlement
      groupTaxSettlementCode:
        from: groupTaxSettlementCode
      note:
        from: note
      createdBy:
        from: createdBy
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      columnNames:
        from: columnNames
        typeOptions:
          func: listKeyFields
      feColumnNames:
        from: feColumnNames
auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: group-tax-settlement-organization

crudConfig:
  query:
    sort:
      - field: createdAt
        order: DESC
  params:
    id:
      field: id
      type: string
      primary: true
    code:
      field: code
      type: string
  routes:
    exclude:
      - createManyBase
      - replaceOneBase
defaultQuery:

routes:
  - path: /api/group-tax-settlement-organization
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
        dataKey: data
      path: 'group-tax-settlement-organization'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$merge([$,{ "data": $map($.data,function($item){ $merge([$item, {"legalEntitiesName":$map($item.legalEntities,function($i){$i.longName})[]}]) })[] }])'

  # create
  - path: /api/group-tax-settlement-organization
    method: POST
    model: _
    query:
    bodyTransform: '$ ~> |$| { "legalEntitiesCode": $.legalEntitiesCode.value[]} |'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'group-tax-settlement-organization'
      transform: '$'

  # detail
  - path: /api/group-tax-settlement-organization/:id
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'group-tax-settlement-organization/:{id}:'
      transform: '$ ~> |$| { "legalEntitiesCode": $map($.legalEntities, function($item) { { "label": $item.longName & " (" & $item.code & ")", "value": $item.code} })[], "legalEntitiesName":$map($.legalEntities, function($item){ $item.longName })[] } |'

  # edit
  - path: /api/group-tax-settlement-organization/:id
    model: _
    method: PATCH
    query:
    bodyTransform: '$ ~> |$| { "legalEntitiesCode": $.legalEntitiesCode.value[]} |'
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: 'group-tax-settlement-organization/:{id}:'
      transform: '$'

  #delete
  - path: /api/group-tax-settlement-organization/:id
    method: DELETE
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'group-tax-settlement-organization/:{id}:'
customRoutes:
  - path: /api/group-tax-settlement-organization/multidelete
    method: DELETE
    model: multiDeleteModel
    bodyTransform: '{"ids": $.ids[]}'
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'group-tax-settlement-organizations'

  - path: /api/group-tax-settlement-organization/companies/by-tax-arrears-refund
    method: GET
    model: _getReportGroupTaxSettlement
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
        dataKey: data
      path: 'group-tax-settlement-organization/companies/by-tax-arrears-refund'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
        dataClosingDate: ':{dataClosingDate}:'
      transform: '$'

  - path: /api/group-tax-settlement-organization/legal-entities/by-tax-arrears-refund
    method: GET
    model: _getReportGroupTaxSettlement
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
        dataKey: data
      path: 'group-tax-settlement-organization/legal-entities/by-tax-arrears-refund'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
        groupTaxSettlementCode: ':{groupTaxSettlementCode}:'
        dataClosingDate: ':{dataClosingDate}:'
      transform: '$'

  - path: /api/group-tax-settlement-organization/export
    method: POST
    model: exportModel
    query:
    upstreamConfig:
      method: POST
      response:
        dataType: binary
      path: 'group-tax-settlement-organization/export-group-tax-settlement-organization-report:export'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$'
