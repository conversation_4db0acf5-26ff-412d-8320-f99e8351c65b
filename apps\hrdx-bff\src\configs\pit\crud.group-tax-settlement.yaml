controller: group-tax-settlement
upstream: ${{UPSTREAM_PIT_URL}}

models:
  - name: _
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: totalPages
    config:
      id:
        from: id
        type: string
      code:
        from: code
        type: string
      countryName:
        from: country.longName
        type: string
      countryCode:
        from: countryCode
        type: string
      country:
        from: $
        objectChildren:
          value:
            from: countryCode
          label:
            from: country.longName,countryCode
            typeOptions:
              func: fieldsToNameCode
      shortName:
        from: shortName
        type: string
        typeOptions:
          func: stringToMultiLang
      longName:
        from: name
        type: string
        typeOptions:
          func: stringToMultiLang
      groupCode:
        from: groupCode
      companyCode:
        from: companyCode
      companyName:
        from: company.longName,companyCode
        typeOptions:
          func: fieldsToNameCode
      company:
        from: $
        objectChildren:
          value:
            from: companyCode
          label:
            from: company.longName,companyCode
            typeOptions:
              func: fieldsToNameCode
      status:
        from: enabled
        type: string
        typeOptions:
          func: YNToBoolean
      updatedBy:
        from: updatedBy
        type: string
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      dataClosingDate:
        from: dataClosingDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      note:
        from: note
        typeOptions:
          func: stringToMultiLang
      effectiveDate:
        from: effectiveDateFrom
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      effectiveDateTo:
        from: effectiveDateTo
        type: timestamp
        typeOptions:
          func: timestampToDateTime

  - name: multiDeleteModel
    config:
      ids:
        type: array
        from: ids

  - name: exportModel
    config:
      id:
        from: id
      code:
        from: code
      countryName:
        from: country
      countryCode:
        from: countryCode
      companyName:
        from: company
      companyCode:
        from: companyCode
      status:
        from: enabled
        typeOptions:
          func: YNToBoolean
      shortName:
        from: shortName
        typeOptions:
          func: stringToMultiLang
      longName:
        from: name
        typeOptions:
          func: stringToMultiLang
      updatedBy:
        from: updatedBy
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      columnNames:
        from: columnNames
        typeOptions:
          func: listKeyFields
      feColumnNames:
        from: feColumnNames
defaultQuery:
auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: group-tax-settlement
crudConfig:
  query:
    sort:
      - field: createdAt
        order: DESC
  params:
    id:
      field: id
      type: string
      primary: true
    code:
      field: code
      type: string
  routes:
    exclude:
      - createManyBase
      - replaceOneBase

routes:
  - path: /api/group-tax-settlement
    method: GET
    model: _

    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'group-tax-settlement'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$'

  - path: /api/group-tax-settlement/:id
    method: GET
    model: _

    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'group-tax-settlement/:{id}:'
      transform: '$'

  - path: /api/group-tax-settlement
    method: POST
    model: _
    query:
    transform: '$'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'group-tax-settlement'
      transform: '$'

  - path: /api/group-tax-settlement/:id
    model: _
    method: PATCH
    query:
    transform: '$'
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: 'group-tax-settlement/:{id}:'

  - path: /api/group-tax-settlement/:id
    method: DELETE
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'group-tax-settlement/:{id}:'
customRoutes:
  - path: /api/group-tax-settlement/:id/histories
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'group-tax-settlement/history?code=:{id}:'
      query:
        # Page: ':{options.page}:'
        # PageSize: ':{options.limit}:'
        # OrderBy: ':{options.sort}:'
      transform: '$'

  - path: /api/group-tax-settlement/by
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'group-tax-settlement/by'
      query:
        Filter: '::{filter}::'
      transform: '$'

  #insert new for custom routes
  - path: /api/group-tax-settlement/custom-history/:code
    method: POST
    model: _
    query:
    transform: '$'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'group-tax-settlement/:{code}:'
      transform: '$'

  - path: /api/group-tax-settlement/by-establish
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
        dataKey: data
      path: 'group-tax-settlement/by-establish'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
        groupCode: ':{groupCode}:'
        companyCode: ':{companyCode}:'
        effectiveDate: ':{effectiveDate}:'
      transform: '$'

  - path: /api/group-tax-settlement/by-tax-arrears-refund
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
        dataKey: data
      path: 'group-tax-settlement/by-tax-arrears-refund'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
        companyCode: ':{companyCode}:'
        dataClosingDate: ':{dataClosingDate}:'
      transform: '$'

  - path: /api/group-tax-settlement/multidelete
    method: DELETE
    model: multiDeleteModel
    bodyTransform: '{"ids": $.ids[]}'
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'group-tax-settlements'

  - path: /api/group-tax-settlement/export
    method: POST
    model: exportModel
    query:
    upstreamConfig:
      method: POST
      response:
        dataType: binary
      path: 'group-tax-settlement/export-group-tax-settlement-report:export'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$'
