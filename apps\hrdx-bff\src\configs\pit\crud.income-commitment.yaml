controller: income-commitment
upstream: ${{UPSTREAM_PIT_URL}}

models:
  - name: _POST
    config:
      id:
        from: id
      employeeId:
        from: EmployeeId
      employeeRecordNumber:
        from: EmployeeRecordNumber
      assessmentPeriodCode:
        from: AssessmentPeriodCode
      effectiveDate:
        from: EffectiveDateFrom
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      cancelIncomeCommitment:
        from: CancelCommitment
        type: boolean
      cancelledOn:
        from: DateOfCancelCommitment
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      cancelledReason:
        from: ReasonForCancellation
      committedIncome:
        from: CommittedAmount
        type: number
      currency:
        from: CurrencyCode
        type: string
      note:
        from: Note
        type: string
      fileAttachmentName:
        from: File
      AttachFile:
        from: AttachFile

  - name: _
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: totalPages
    config:
      id:
        from: id
        type: string
      code:
        from: code
        type: string
      effectiveDate:
        from: effectiveDateFrom
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      employeeSearch:
        from: employeeSearch
      employeeId:
        from: employeeId
        type: string
      employeeRecordNumber:
        from: employeeRecordNumber
        type: string
      employeeName:
        from: employee.longName
        type: string
      jobIndicatorName:
        from: jobIndicator.longName
        type: string
      jobIndicatorCode:
        from: jobIndicatorCode
      jobIndicator:
        from: jobIndicator.longName,jobIndicator.code
        typeOptions:
          func: fieldsToNameCode
      group:
        from: group.longName,group.code
        typeOptions:
          func: fieldsToNameCode
      groupName:
        from: group.longName
        type: string
      companyName:
        from: company.longName
        type: string
      company:
        from: company.longName,company.code
        typeOptions:
          func: fieldsToNameCode
      businessUnitCode:
        from: businessUnitCode
      departmentCode:
        from: departmentCode
      department:
        from: department.longName,department.code
        typeOptions:
          func: fieldsToNameCode
      groupCode:
        from: groupCode
      companyCode:
        from: companyCode
      divisionCode:
        from: divisionCode
      division:
        from: division.longName,division.code
        typeOptions:
          func: fieldsToNameCode
      legalEntityCode:
        from: legalEntityCode
      legalEntityName:
        from: legalEntity.longName
        type: string
      legalEntity:
        from: legalEntity.longName,legalEntity.code
        typeOptions:
          func: fieldsToNameCode
      bussinessUnitCode:
        from: bussinessUnitCode
      businessUnitName:
        from: businessUnit.longName
        type: string
      businessUnit:
        from: businessUnit.longName,businessUnit.code
        typeOptions:
          func: fieldsToNameCode
      divisionName:
        from: division.longName
        type: string
      departmentName:
        from: department.longName
        type: string
      contractTypeCode:
        from: contractTypeCode
      contractTypeName:
        from: contractType.longName
        type: string
      contractType:
        from: contractType.longName,contractType.code
        typeOptions:
          func: fieldsToNameCode
      jobCode:
        from: jobCode
      jobName:
        from: job.longName
        type: string
      job:
        from: job.longName,job.code
        typeOptions:
          func: fieldsToNameCode
      taxCode:
        from: taxCode
        type: string
      assessmentPeriodCode:
        from: assessmentPeriodCode
      assessmentPeriodName:
        from: assessmentPeriod.longName
      assessmentPeriod:
        from: assessmentPeriod.longName,assessmentPeriod.code
        typeOptions:
          func: fieldsToNameCode
      settlementPeriod:
        from: yearSettlementPeriodCode
        type: string
      startDate:
        from: startDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      endDate:
        from: endDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      committedIncome:
        from: committedAmount
        type: number
      currencyName:
        from: currency.longName
      currency:
        from: currencyCode
        type: string
      cancelIncomeCommitment:
        from: cancelCommitment
      cancelledOn:
        from: dateOfCancelCommitment
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      cancelledReason:
        from: reasonForCancellation
        type: string
      note:
        from: note
        type: string
      fileAttachment:
        from: attachFile
      AttachFile:
        from: attachFile
      fileAttachmentName:
        from: attachFileName
      #
      createdBy:
        from: createdBy
        type: string
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
        type: string
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime

  - name: multiDeleteModel
    config:
      ids:
        type: array
        from: ids

  - name: exportModel
    config:
      id:
        from: id
      code:
        from: code
      effectiveDate:
        from: effectiveDateFrom
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      employeeId:
        from: employeeId
      employeeRecordNumber:
        from: employeeRecordNumber
      employeeName:
        from: employee
      employeeSearch:
        from: employeeSearch
      jobIndicatorName:
        from: jobIndicator
      jobIndicatorCode:
        from: jobIndicatorCode
      groupName:
        from: group
      groupCode:
        from: groupCode
      companyName:
        from: company
      legalEntityName:
        from: legalEntity
      legalEntityCode:
        from: legalEntityCode
      businessUnitName:
        from: businessUnit
      businessUnitCode:
        from: businessUnitCode
      divisionName:
        from: division
      divisionCode:
        from: divisionCode
      departmentName:
        from: department
      departmentCode:
        from: departmentCode
      contractTypeName:
        from: contractType
      contractTypeCode:
        from: contractTypeCode
      jobName:
        from: job
      jobCode:
        from: jobCode
      taxCode:
        from: taxCode
      assessmentPeriodCode:
        from: assessmentPeriodCode
      assessmentPeriodName:
        from: assessmentPeriod
      startDate:
        from: startDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      endDate:
        from: endDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      currencyName:
        from: currency
      committedIncome:
        from: committedAmount
      cancelIncomeCommitment:
        from: cancelCommitment
      cancelledOn:
        from: dateOfCancelCommitment
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      cancelledReason:
        from: reasonForCancellation
      note:
        from: note
      fileAttachmentName:
        from: attachFileName
      createdBy:
        from: createdBy
        type: string
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      columnNames:
        from: columnNames
        typeOptions:
          func: listKeyFields
      feColumnNames:
        from: feColumnNames
auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: income-commitment
crudConfig:
  query:
    sort:
      - field: assessmentPeriodCode
        order: DESC
  params:
    id:
      field: id
      type: string
      primary: true
    code:
      field: code
      type: string
  routes:
    exclude:
      - createManyBase
      - replaceOneBase
defaultQuery:

routes:
  - path: /api/income-commitment
    method: GET
    model: _

    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
        dataKey: data
      path: 'income-commitment'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$'

  - path: /api/income-commitment/:id
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'income-commitment/:{id}:'
      transform: '$ ~> | $ | {"attachmentResults": $.fileAttachmentName and  $.fileAttachment ? {"name": $.fileAttachmentName , "url": "/api/pit-files/download", "fileValue": $.AttachFile, "fileField": "AttachFile"  } : null} |'

  - path: /api/income-commitment/:id
    method: DELETE
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'income-commitment/:{id}:'

customRoutes:
  - path: /api/income-commitment/:id/histories
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'income-commitment'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
      transform: '$'

  - path: /api/income-commitment/upload
    method: POST
    model: _POST
    query:
    transform: '$'
    dataType: formData
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'income-commitment'
      transform: '$'

  - path: /api/income-commitment/:id/upload
    model: _POST
    method: POST
    query:
    transform: '$'
    dataType: formData
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: 'income-commitment/:{id}:'
      transform: '$'

  - path: /api/income-commitment/multidelete
    method: DELETE
    model: multiDeleteModel
    bodyTransform: '{"ids": $.ids[]}'
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'income-commitments'

  - path: /api/income-commitment/export
    method: POST
    model: exportModel
    query:
    upstreamConfig:
      method: POST
      response:
        dataType: binary
      path: 'income-commitment/export-income-commitment-report:export'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$'
