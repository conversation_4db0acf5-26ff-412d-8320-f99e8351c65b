controller: income-tax-summary
upstream: ${{UPSTREAM_PIT_URL}}

models:
  - name: _
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: totalPages
    config:
      id:
        from: id
      code:
        from: code
      assessmentPeriodCode:
        from: assessmentPeriodCode
      companyCode:
        from: companyCode
      legalEntityCode:
        from: legalEntityCode
      departmentCode:
        from: departmentCode
      longName:
        from: longName
      shortName:
        from: shortName
      employeeId:
        from: employeeId
      employee:
        from: employee
      employeeCode:
        from: employee.code
      employeeLongName:
        from: employee.longName
      employeeShortName:
        from: employee.shortName

auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: meta-data/income-tax-summary
crudConfig:
  query:
    sort:
      - field: createdAt
        order: DESC
  params:
    id:
      field: id
      type: string
      primary: true
    code:
      field: code
      type: string
  routes:
    exclude:
      - createManyBase
      - replaceOneBase
defaultQuery:
customRoutes:
  - path: /api/income-tax-summary/legal-entities
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
        dataKey: data
      path: 'meta-data/income-tax-summary/legal-entities'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
        assessmentPeriodCode: ':{assessmentPeriodCode}:'
        companyCode: ':{companyCode}:'
      transform: '$'

  - path: /api/income-tax-summary/departments
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
        dataKey: data
      path: 'meta-data/income-tax-summary/departments'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
        assessmentPeriodCode: ':{assessmentPeriodCode}:'
        companyCode: ':{companyCode}:'
      transform: '$'

  - path: /api/income-tax-summary/employees
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
        dataKey: data
      path: 'meta-data/income-tax-summary/employees'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
        assessmentPeriodCode: ':{assessmentPeriodCode}:'
        companyCode: ':{companyCode}:'
        legalEntityCode: ':{legalEntityCode}:'
      transform: '$'
