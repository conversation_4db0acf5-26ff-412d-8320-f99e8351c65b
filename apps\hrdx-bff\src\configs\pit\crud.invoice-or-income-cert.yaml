controller: invoice-or-income-cert
upstream: ${{UPSTREAM_PIT_URL}}

models:
  - name: _
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: totalPages
    config:
      id:
        from: id
      sid:
        from: sid
      code:
        from: code
      isCreate:
        from: isCreate
      businessTaxCode:
        from: businessTaxCode
      businessTaxCodeObject:
        from: $
        objectChildren:
          code:
            from: businessTaxCode
          name:
            from: businessName
      legalEntity:
        from: legalEntity
      legalEntityName:
        from: legalEntity.longName
      legalEntityShortName:
        from: legalEntity.shortName
      legalEntityCode:
        from: legalEntityCode
      formStepTwo:
        from: form
      seriStepTwo:
        from: seri
      form:
        from: form
      seri:
        from: seri
      no:
        from: no
      noIncomeCertification:
        from: noIncomeCertification
      onlyIncomeCertification:
        from: onlyIncomeCertification
      employeeTypeCode:
        from: employeeTypeCode
      employeeId:
        from: employeeId
      fullName:
        from: fullName
      taxCode:
        from: taxCode
      address:
        from: address
      status:
        from: status
      countryCode:
        from: country.code
      countryName:
        from: country.longName
      country:
        from: country
      countryObject:
        from: $
        objectChildren:
          label:
            from: country.longName
          value:
            from: country.code
      countryShortName:
        from: country.shortName
      residentCode:
        from: residentCode
      resident:
        from: resident
      nationId:
        from: nationId
      issuePlace:
        from: issuePlace
      issueDate:
        from: issueDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      phone:
        from: phone
      email:
        from: email
      sendMail:
        from: sendMail
      typeOfIncome:
        from: typeOfIncome
      startDate:
        from: startDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      endDate:
        from: endDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      totalIncomeTaxable:
        from: totalIncomeTaxable
      totalAssessableIncome:
        from: totalAssessableIncome
      totalInsurance:
        from: totalInsurance
      amountOfPersonalIncomeTax:
        from: amountOfPersonalIncomeTax
      totalDependent:
        from: totalDependent
      totalMonthlyDeductions:
        from: totalMonthlyDeductions
      totalIncomeTaxableValue:
        from: totalIncomeTaxable.stringValue
      totalAssessableIncomeValue:
        from: totalAssessableIncome.stringValue
      totalInsuranceValue:
        from: totalInsurance.stringValue
      amountOfPersonalIncomeTaxValue:
        from: amountOfPersonalIncomeTax.stringValue
      totalDependentValue:
        from: totalDependent.stringValue
      totalMonthlyDeductionsValue:
        from: totalMonthlyDeductions.stringValue
      currency:
        from: currency
      currencyName:
        from: currency.longName
      currencyObject:
        from: $
        objectChildren:
          label:
            from: currency.longName
          value:
            from: currencyCode
      currencyCode:
        from: currencyCode
      cancelledReason:
        from: cancelledReason
      cancelledComment:
        from: cancelledComment
      cancelledBy:
        from: cancelledBy
      cancelledAt:
        from: cancelledAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      pushedBy:
        from: pushedBy
      pushedAt:
        from: pushedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
          args: dateHour
      verifiedBy:
        from: verifiedBy
      verifiedLink:
        from: verifiedLink
      verifiedAt:
        from: verifiedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      createdBy:
        from: createdBy
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime

  - name: multiDeleteModel
    config:
      ids:
        type: array
        from: ids
  - name: approveCancelCertModel
    config:
      id:
        from: id
      ids:
        type: array
        from: ids
      reason:
        from: reason
      comment:
        from: comment

  - name: exportModel
    config:
      id:
        from: id
      businessTaxCode:
        from: businessTaxCode
      form:
        from: form
      seri:
        from: seri
      no:
        from: no
      employeeId:
        from: employeeId
      employeeTypeCode:
        from: employeeTypeCode
      onlyIncomeCertification:
        from: onlyIncomeCertification
      totalIncomeTaxableValue:
        from: totalIncomeTaxable
      totalInsuranceValue:
        from: totalInsurance
      amountOfPersonalIncomeTaxValue:
        from: amountOfPersonalIncomeTax
      fullName:
        from: fullName
      taxCode:
        from: taxCode
      status:
        from: status
      updatedBy:
        from: updatedBy
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      columnNames:
        from: columnNames
        typeOptions:
          func: listKeyFields
      feColumnNames:
        from: feColumnNames

defaultQuery:
auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: invoice-or-income-cert
crudConfig:
  query:
    sort:
      - field: createdAt
        order: DESC
  params:
    id:
      field: id
      type: string
      primary: true
  routes:
    exclude:
      - createManyBase
      - replaceOneBase
routes:
  # list table
  - path: /api/invoice-or-income-cert
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
        dataKey: data
      path: 'invoice-or-income-cert'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$'

  # create
  - path: /api/invoice-or-income-cert
    method: POST
    model: _
    query:
    transform: '$'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'invoice-or-income-cert'
      transform: '$ ~> | $ | $, ["employeeUnvisible","country","currency"] |'

  # edit
  - path: /api/invoice-or-income-cert/:id
    model: _
    method: PATCH
    query:
    transform: '$'
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: 'invoice-or-income-cert/:{id}:'
      transform: '$ ~> | $ | $, ["employeeUnvisible","country","currency"] |'

  # detail
  - path: /api/invoice-or-income-cert/:id
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'invoice-or-income-cert/:{id}:'
      transform: '$ ~> | $ |
        {
        "resident": $exists(residentCode) ? residentCode = 1 ? true : false,
        "businessTaxCodeObject": businessTaxCode ? {
        "label" : $join($filter([businessTaxCode,legalEntityShortName], $boolean), " - "),
        "value" : {
        "code": businessTaxCode,
        "name": $join($filter([businessTaxCode,legalEntityShortName], $boolean), " - "),
        "legalEntityCode": legalEntityCode
        }
        } : null
        } |'

  #delete
  - path: /api/invoice-or-income-cert/:id
    method: DELETE
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'invoice-or-income-cert/:{id}:'
customRoutes:
  # create save as draft
  - path: /api/invoice-or-income-cert/save-as-draft
    method: POST
    model: _
    query:
    bodyTransform: '$ ~> | $ | $merge([$ , {"isCreate" : false}]), ["employeeUnvisible","country","currency"] |'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'invoice-or-income-cert'
      transform: '$'

  # create push invoice
  - path: /api/invoice-or-income-cert/push-to-invoice
    method: POST
    model: _
    query:
    bodyTransform: '$ ~> | $ | $merge([$ , {"isCreate" : true}]), ["employeeUnvisible","country","currency"] |'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'invoice-or-income-cert'
      transform: '$'

  # edit save as draft
  - path: /api/invoice-or-income-cert/save-as-draft/:id
    method: PATCH
    model: _
    query:
    bodyTransform: '$ ~> | $ | $merge([$ , {"isCreate" : false}]), ["employeeUnvisible","country","currency","id"] |'
    upstreamConfig:
      method: PUT
      response:
        dataType: object
      path: 'invoice-or-income-cert/:{id}:'
      transform: '$'

  # edit push invoice
  - path: /api/invoice-or-income-cert/push-to-invoice/:id
    method: PATCH
    model: _
    query:
    bodyTransform: '$ ~> | $ | $merge([$ , {"isCreate" : true}]), ["employeeUnvisible","country","currency","id"] |'
    upstreamConfig:
      method: PUT
      response:
        dataType: object
      path: 'invoice-or-income-cert/:{id}:'
      transform: '$'

  # delete multi
  - path: /api/invoice-or-income-cert/multidelete
    method: DELETE
    model: multiDeleteModel
    bodyTransform: '$ ~> | $ | $merge([$ , {"ids": $.ids[]}]) |'
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'invoice-or-income-certs'

  # approve invoice/income cert detail
  - path: /api/invoice-or-income-cert/approve/:id
    method: PATCH
    model: approveCancelCertModel
    query:
    bodyTransform: '$ ~> | $ | $merge([$ , {"ids": $.id[]}]) |'
    upstreamConfig:
      method: PUT
      path: 'invoice-or-income-cert/approve'

  # cancel invoice/income cert detail
  - path: /api/invoice-or-income-cert/cancel/:id
    method: PATCH
    model: approveCancelCertModel
    query:
    bodyTransform: '$ ~> | $ | $merge([$ , {"ids": $.id[]}]) |'
    upstreamConfig:
      method: PUT
      path: 'invoice-or-income-cert/cancel'

  # approve invoice/income cert multi
  - path: /api/invoice-or-income-cert/approve
    method: PATCH
    model: approveCancelCertModel
    bodyTransform: '$ ~> | $ | $merge([$ , {"ids": $.ids[]}]) |'
    query:
      $and:
    upstreamConfig:
      method: PUT
      path: 'invoice-or-income-cert/approve'

  # cancel invoice/income cert multi
  - path: /api/invoice-or-income-cert/cancel
    method: PATCH
    model: approveCancelCertModel
    bodyTransform: '$ ~> | $ | $merge([$ , {"ids": $.ids[]}]) |'
    query:
      $and:
    upstreamConfig:
      method: PUT
      path: 'invoice-or-income-cert/cancel'

  - path: /api/invoice-or-income-cert/export
    method: POST
    model: exportModel
    query:
    upstreamConfig:
      method: POST
      response:
        dataType: binary
      path: 'invoice-or-income-cert/export-invoice-or-income-cert-report:export'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$'
