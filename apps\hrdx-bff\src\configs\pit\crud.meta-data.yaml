controller: meta-data
upstream: ${{UPSTREAM_PIT_URL}}

models:
  - name: _groupTaxSettlement
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: totalPages
    config:
      id:
        from: id
      code:
        from: code
      effectiveDate:
        from: effectiveDateFrom
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      effectiveDateTo:
        from: effectiveDateTo
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      status:
        from: enabled
        type: string
        typeOptions:
          func: YNToBoolean
      longName:
        from: name
        type: string
        typeOptions:
          func: stringToMultiLang
      shortName:
        from: shortName
        type: string
        typeOptions:
          func: stringToMultiLang
      note:
        from: note
        type: string
        typeOptions:
          func: stringToMultiLang
      companyCode:
        from: companyCode
      countryCode:
        from: countryCode
      createdBy:
        from: createdBy
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
defaultQuery:
auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: meta-data/synthesizing-income
crudConfig:
  query:
    sort:
      - field: createdAt
        order: DESC
  params:
    id:
      field: id
      type: string
      primary: true
    code:
      field: code
      type: string
  routes:
    exclude:
      - createManyBase
      - replaceOneBase

customRoutes:
  - path: /api/meta-data/synthesizing-income/group-tax-settlement
    method: GET
    model: _groupTaxSettlement
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
        dataKey: data
      path: 'meta-data/synthesizing-income/group-tax-settlement'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$'
