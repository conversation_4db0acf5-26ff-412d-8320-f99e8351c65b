controller: monthly-tax-declaration-period
upstream: ${{UPSTREAM_PIT_URL}}

models:
  - name: _
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: totalPages
    config:
      id:
        from: id
      # name BFF
      code:
        # name BE
        from: code
      shortName:
        from: shortName
        typeOptions:
          func: stringToMultiLang
      longName:
        from: name
        typeOptions:
          func: stringToMultiLang
      declarationPeriod:
        from: declarationPeriodCode
      declarationPeriodName:
        from: declarationPeriod.longName
      belongsToSettlementPeriod:
        from: settlementPeriodCode
      belongsToSettlementPeriodName:
        from: settlementPeriod.longName
      taxFormula:
        from: taxFormulaCode
      taxFormulaName:
        from: taxFormula.longName
      taxSettlementGroup:
        from: groupTaxSettlement
      taxSettlementGroupName:
        from: groupTaxSettlement.longName
      taxSettlementGroupNameCode:
        from: groupTaxSettlement.longName,groupTaxSettlement.code
        typeOptions:
          func: fieldsToNameCode
      groupTaxSettlementCode:
        from: groupTaxSettlementCode
      assessmentPeriodName:
        from: assessmentPeriod.longName
      assessmentPeriodCode:
        from: assessmentPeriodCode
      assessmentPeriodNameCode:
        from: assessmentPeriod.longName,assessmentPeriod.code
        typeOptions:
          func: fieldsToNameCode
      companyCode:
        from: companyCode
      groupCode:
        from: groupCode
      periodCode:
        from: periodCode
      countryCode:
        from: countryCode
      year:
        from: year
        typeOptions:
          func: intYearToDateTime
      yearName:
        from: year
      country:
        from: country
      countryName:
        from: country.longName
      countryNameCode:
        from: country.longName,country.code
        typeOptions:
          func: fieldsToNameCode
      company:
        from: company
      companyName:
        from: company.longName
      companyNameCode:
        from: company.longName,company.code
        typeOptions:
          func: fieldsToNameCode
      groupName:
        from: group.longName
      groupNameCode:
        from: group.longName,group.code
        typeOptions:
          func: fieldsToNameCode
      period:
        from: period
      periodName:
        from: period.longName
      periodNameCode:
        from: period.longName,period.code
        typeOptions:
          func: fieldsToNameCode
      currency:
        from: currency.longName
      currencyNameCode:
        from: currency.longName,currency.code
        typeOptions:
          func: fieldsToNameCode
      note:
        from: note
        typeOptions:
          func: stringToMultiLang
      startDate:
        from: startDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      endDate:
        from: endDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      createdBy:
        from: createdBy
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      currencyCode:
        from: currencyCode
      monthlyTaxDeclarationPeriodCode:
        from: monthlyTaxDeclarationPeriodCode

  - name: _monthlyDepartment
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: totalPages
    config:
      id:
        from: id
      code:
        from: code
      legalEntityCode:
        from: legalEntityCode
      longName:
        from: longName
      shortName:
        from: shortName
      enabled:
        from: enabled
        typeOptions:
          func: YNToBoolean

  - name: _monthlyLegalEntities
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: totalPages
    config:
      id:
        from: id
      code:
        from: code
      longName:
        from: longName
      shortName:
        from: shortName

  - name: multiDeleteModel
    config:
      ids:
        type: array
        from: ids

  - name: exportModel
    config:
      id:
        from: id
      code:
        from: code
      shortName:
        from: shortName
        typeOptions:
          func: stringToMultiLang
      longName:
        from: name
        typeOptions:
          func: stringToMultiLang
      countryName:
        from: country
      countryCode:
        from: countryCode
      groupName:
        from: group
      groupCode:
        from: groupCode
      companyName:
        from: company
      companyCode:
        from: companyCode
      taxSettlementGroupName:
        from: groupTaxSettlement
      taxSettlementGroupCode:
        from: groupTaxSettlementCode
      groupTaxSettlementCode:
        from: groupTaxSettlementCode
      periodName:
        from: period
      periodCode:
        from: periodCode
      yearName:
        from: year
      year:
        from: year
        typeOptions:
          func: intYearToDateTime
      assessmentPeriodName:
        from: assessmentPeriod
      assessmentPeriodCode:
        from: assessmentPeriodCode
      startDate:
        from: startDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      endDate:
        from: endDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      currency:
        from: currency
      currencyCode:
        from: currencyCode
      note:
        from: note
        typeOptions:
          func: stringToMultiLang
      createdBy:
        from: createdBy
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      columnNames:
        from: columnNames
        typeOptions:
          func: listKeyFields
      feColumnNames:
        from: feColumnNames
auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: monthly-tax-declaration-period
crudConfig:
  query:
    sort:
      - field: code
        order: DESC
  params:
    id:
      field: id
      type: string
      primary: true
  routes:
    exclude:
      - createManyBase
      - replaceOneBase
defaultQuery:

routes:
  # list table
  - path: /api/monthly-tax-declaration-period
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
        dataKey: data
      path: 'monthly-tax-declaration-period'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$merge([$,{"data": $map($.data, function($item) { $merge([$item,{"yearName":$item.yearName != 0? $item.yearName: null}]) })[] } ])'

  # create
  - path: /api/monthly-tax-declaration-period
    method: POST
    model: _
    query:
    transform: '$'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'monthly-tax-declaration-period'
      transform: '$'

  # detail
  - path: /api/monthly-tax-declaration-period/:id
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'monthly-tax-declaration-period/:{id}:'
      transform: '$ ~> |$| {"companyCode":$boolean($.companyCode)=true?{"label":$.companyName & " (" & $.companyCode & ")", "value":$.companyCode}, "groupCode":$boolean($.groupCode)=true?{"label":$.groupName & " (" & $.groupCode & ")", "value":$.groupCode}, "groupTaxSettlementCode":$boolean($.groupTaxSettlementCode)=true?{"label":$.taxSettlementGroupName & " (" & $.groupTaxSettlementCode & ")", "value":$.groupTaxSettlementCode}, "assessmentPeriodCode":$boolean($.assessmentPeriodCode)=true?{"label":$.assessmentPeriodName & " (" & $.assessmentPeriodCode & ")", "value":$.assessmentPeriodCode} }|'

  # edit
  - path: /api/monthly-tax-declaration-period/:id
    model: _
    method: PATCH
    query:
    transform: '$'
    bodyTransform: '$'
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: 'monthly-tax-declaration-period/:{id}:'
      transform: '$'

  #delete
  - path: /api/monthly-tax-declaration-period/:id
    method: DELETE
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'monthly-tax-declaration-period/:{id}:'

customRoutes:
  - path: /api/monthly-tax-declaration-period/legal-entities
    method: GET
    model: _monthlyLegalEntities
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
        dataKey: data
      path: 'monthly-tax-declaration-period/legal-entities'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
        monthlyDeclarationPeriodCode: '::{monthlyDeclarationPeriodCode}::'
      transform: '$'

  - path: /api/monthly-tax-declaration-period/departments
    method: GET
    model: _monthlyDepartment
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
        dataKey: data
      path: 'monthly-tax-declaration-period/departments'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
        monthlyDeclarationPeriodCode: '::{monthlyDeclarationPeriodCode}::'
      transform: '$'

  - path: /api/monthly-tax-declaration-period/by-finalize
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'monthly-tax-declaration-period/by-finalize'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
        isCancel: '::{isCancel}::'
      transform: '$'

  - path: /api/monthly-tax-declaration-period/multidelete
    method: DELETE
    model: multiDeleteModel
    bodyTransform: '{"ids": $.ids[]}'
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'monthly-tax-declaration-periods'

  - path: /api/monthly-tax-declaration-period/export
    method: POST
    model: exportModel
    query:
    upstreamConfig:
      method: POST
      response:
        dataType: binary
      path: 'monthly-tax-declaration-period/export-group-tax-settlement-organization-report:export'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$'
