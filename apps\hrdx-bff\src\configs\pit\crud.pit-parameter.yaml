controller: pit-parameter
upstream: ${{UPSTREAM_PIT_URL}}

models:
  - name: _
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: totalPages
    config:
      id:
        from: id
        type: string
      code:
        from: code
        type: string
      countryName:
        from: country.longName
        type: string
      countryCode:
        from: countryCode
        type: string
      country:
        from: $
        objectChildren:
          value:
            from: countryCode
          label:
            from: country.longName,countryCode
            typeOptions:
              func: fieldsToNameCode
      shortName:
        from: shortName
        type: string
        typeOptions:
          func: stringToMultiLang
      longName:
        from: name
        type: string
        typeOptions:
          func: stringToMultiLang
      status:
        from: enabled
        type: string
        typeOptions:
          func: YNToBoolean
      createdBy:
        from: createdBy
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
        type: string
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      note:
        from: note
        type: string
        typeOptions:
          func: stringToMultiLang
      effectiveDate:
        from: effectiveDateFrom
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      effectiveDateTo:
        from: effectiveDateTo
        type: timestamp
        typeOptions:
          func: timestampToDateTime

  - name: multiDeleteModel
    config:
      ids:
        type: array
        from: ids

  - name: exportModel
    config:
      id:
        from: id
      code:
        from: code
      countryName:
        from: country
      countryCode:
        from: countryCode
      status:
        from: enabled
        typeOptions:
          func: YNToBoolean
      shortName:
        from: shortName
        typeOptions:
          func: stringToMultiLang
      longName:
        from: name
        typeOptions:
          func: stringToMultiLang
      updatedBy:
        from: updatedBy
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      columnNames:
        from: columnNames
        typeOptions:
          func: listKeyFields
      feColumnNames:
        from: feColumnNames
defaultQuery:
auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: pit-parameter
crudConfig:
  query:
    sort:
      - field: code
        order: desc
  params:
    id:
      field: id
      type: string
      primary: true
    code:
      field: code
      type: string
  routes:
    exclude:
      - createManyBase
      - replaceOneBase

routes:
  - path: /api/pit-parameter
    method: GET
    model: _

    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'pit-parameter'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$'

  - path: /api/pit-parameter/:id
    method: GET
    model: _

    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'pit-parameter/:{id}:'
      transform: '$'

  - path: /api/pit-parameter
    method: POST
    model: _
    query:
    transform: '$'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'pit-parameter'
      transform: '$'

  - path: /api/pit-parameter/:id
    model: _
    method: PATCH
    query:
    transform: '$'
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: 'pit-parameter/:{id}:'

  - path: /api/pit-parameter/:id
    method: DELETE
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'pit-parameter/:{id}:'
customRoutes:
  - path: /api/pit-parameter/:id/histories
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'pit-parameter/history?code=:{id}:'
      # query:
      #   Page: ':{options.page}:'
      #   PageSize: ':{options.limit}:'
      #   OrderBy: ':{options.sort}:'
      transform: '$'

  - path: /api/pit-parameter/by
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'pit-parameter/by'
      query:
        Filter: '::{filter}::'
      transform: '$'

  # Insert
  - path: /api/pit-parameter/custom-history/:code
    method: POST
    model: _
    query:
    transform: '$'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'pit-parameter/:{code}:'
      transform: '$'

  - path: /api/pit-parameter/multidelete
    method: DELETE
    model: multiDeleteModel
    bodyTransform: '{"ids": $.ids[]}'
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'pit-parameters'

  - path: /api/pit-parameter/export
    method: POST
    model: exportModel
    query:
    upstreamConfig:
      method: POST
      response:
        dataType: binary
      path: 'pit-parameter/export-pit-parameter-report:export'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$'
