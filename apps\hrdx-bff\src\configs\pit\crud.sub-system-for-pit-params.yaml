controller: sub-system-for-pit-params
upstream: ${{UPSTREAM_PIT_URL}}

models:
  - name: _
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: totalPages
    config:
      id:
        from: id
        type: string
      country:
        from: country.longName
        type: string
      countryCode:
        from: countryCode
        type: string
      countryObject:
        from: $
        objectChildren:
          value:
            from: countryCode
          label:
            from: country.longName,countryCode
            typeOptions:
              func: fieldsToNameCode
      parameter:
        from: pitParameter.longName
        type: string
      parameterCode:
        from: pitParameterCode
        type: string
      parameterObject:
        from: $
        objectChildren:
          value:
            from: pitParameterCode
          label:
            from: pitParameter.longName,pitParameterCode
            typeOptions:
              func: fieldsToNameCode
      dateIssued:
        from: releasedDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      dateOfApplication:
        from: appliedDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      effectiveDate:
        from: effectiveDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      monthDate:
        from: monthDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      value:
        from: value
        type: string
      unit:
        from: unit.longName
        type: string
      unitCode:
        from: unitCode
        type: string
      unitObject:
        from: $
        objectChildren:
          value:
            from: unitCode
          label:
            from: unit.longName,unitCode
            typeOptions:
              func: fieldsToNameCode
      note:
        from: note
        type: string
      createdBy:
        from: createdBy
        type: string
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
        type: string
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      currency:
        from: currency
        type: string
      currencyCode:
        from: currencyCode
        type: string
      currencyObject:
        from: $
        objectChildren:
          value:
            from: currencyCode
          label:
            from: currency.longName,currencyCode
            typeOptions:
              func: fieldsToNameCode
  - name: multiDeleteModel
    config:
      ids:
        type: array
        from: ids

  - name: exportModel
    config:
      id:
        from: id
      code:
        from: code
      countryCode:
        from: countryCode
      country:
        from: country
      parameter:
        from: pitParameter
      parameterCode:
        from: pitParameterCode
      dateIssued:
        from: releasedDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      dateOfApplication:
        from: appliedDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      effectiveDate:
        from: effectiveDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      value:
        from: value
      # monthDate:
      #   from: monthDate
      #   type: timestamp
      #   typeOptions:
      #     func: timestampToDateTime
      unit:
        from: unit
      unitCode:
        from: unitCode
      note:
        from: note
      createdBy:
        from: createdBy
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      columnNames:
        from: columnNames
        typeOptions:
          func: listKeyFields
      feColumnNames:
        from: feColumnNames
defaultQuery:
auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: sub-system-for-pit-params
crudConfig:
  query:
    sort:
      - field: createdAt
        order: DESC
  params:
    id:
      field: id
      type: string
      primary: true
    code:
      field: code
      type: string
  routes:
    exclude:
      - createManyBase
      - replaceOneBase

routes:
  - path: /api/sub-system-for-pit-params
    method: GET
    model: _

    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'sub-system-for-pit-params'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      # transform: '$ ~> | $.data | {"value": $exists($.monthDate) ? $fromMillis($toMillis($.monthDate), "[D01]/[M01]/[Y0001]") : $.value} |'

  - path: /api/sub-system-for-pit-params/:id
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'sub-system-for-pit-params/:{id}:'
      transform: '$ ~> |$| {"parameterCode":$boolean($.parameterCode)=true?{"label":$.parameter & " (" & $.parameterCode & ")", "value":$.parameterCode} }|'

  - path: /api/sub-system-for-pit-params
    method: POST
    model: _
    query:
    transform: '$'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'sub-system-for-pit-params'
      transform: '$'

  - path: /api/sub-system-for-pit-params/:id
    model: _
    method: PATCH
    query:
    transform: '$'
    bodyTransform: '$ ~> | $ |{"monthDate": $exists($.monthDate) and $.unitCode = "UNIT_DATE"? $.monthDate: null, "currencyCode":$exists($.currencyCode) and $.unitCode = "UNIT_CURRENCY"? $.currencyCode: null, "value":$.unitCode = "UNIT_DATE"?null:$.value} |'
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: 'sub-system-for-pit-params/:{id}:'

  - path: /api/sub-system-for-pit-params/:id
    method: DELETE
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'sub-system-for-pit-params/:{id}:'
customRoutes:
  - path: /api/sub-system-for-pit-params/:id/histories
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'sub-system-for-pit-params/history?code=:{id}:'
      # query:
      #   Page: ':{options.page}:'
      #   PageSize: ':{options.limit}:'
      #   OrderBy: ':{options.sort}:'
      transform: '$'

  - path: /api/sub-system-for-pit-params/by
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'sub-system-for-pit-params/by'
      query:
        Filter: '::{filter}::'
      transform: '$'

  - path: /api/sub-system-for-pit-params/multidelete
    method: DELETE
    model: multiDeleteModel
    bodyTransform: '{"ids": $.ids[]}'
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'sub-system-for-pit-params'

  - path: /api/sub-system-for-pit-params/export
    method: POST
    model: exportModel
    query:
    upstreamConfig:
      method: POST
      response:
        dataType: binary
      path: 'sub-system-for-pit-params/export-sub-system-for-pit-param-report:export'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$'
