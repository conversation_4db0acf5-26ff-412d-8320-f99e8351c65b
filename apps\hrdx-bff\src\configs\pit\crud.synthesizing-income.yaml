controller: synthesizing-income
upstream: ${{UPSTREAM_PIT_URL}}

models:
  - name: _
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: totalPages
    config:
      id:
        from: id
      code:
        from: code
      calculationStatus:
        from: calculationStatus
      startDate:
        from: startDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      endDate:
        from: endDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      revision:
        from: revision
      legalEntityCode:
        from: legalEntityCode
      version:
        from: version
      isLastRevision:
        from: isLastRevision
      assessmentPeriodTypeName:
        from: assessmentPeriodType.longName
      assessmentPeriodTypeCode:
        from: assessmentPeriodTypeCode
      assessmentPeriodType:
        from: assessmentPeriodType
      assessmentPeriodName:
        from: assessmentPeriod.longName
      assessmentPeriodCode:
        from: assessmentPeriodCode
      assessmentPeriod:
        from: assessmentPeriod
      monthlyTaxDeclarationPeriodName:
        from: monthlyTaxDeclarationPeriod.longName
      monthlyTaxDeclarationPeriodCode:
        from: monthlyTaxDeclarationPeriodCode
      monthlyTaxDeclarationPeriod:
        from: monthlyTaxDeclarationPeriod
      groupTaxSettlementName:
        from: groupTaxSettlement.longName
      groupTaxSettlementCode:
        from: groupTaxSettlementCode
      groupTaxSettlement:
        from: groupTaxSettlement
      totalEmployee:
        from: totalEmployee
      totalEmployeeWarning:
        from: totalEmployeeWarning
      typeOfRun:
        from: typeOfRun
      typeOfRunCode:
        from: typeOfRunCode
      typeOfRunName:
        from: typeOfRun.longName

      createdBy:
        from: createdBy
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
  - name: _employees
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: totalPages
    config:
      id:
        from: id
      synthesizingIncomeId:
        from: synthesizingIncomeId
      code:
        from: code
      calculationStatus:
        from: calculationStatus
      employeeId:
        from: employeeId
      employeeName:
        from: employee.longName
      employeeRecordNumber:
        from: employeeRecordNumber
      departmentCode:
        from: departmentCode
      legalEntityCode:
        from: legalEntityCode
      taxCode:
        from: taxCode
      currencyCode:
        from: currencyCode
      totalIncome:
        from: totalIncome
      taxableIncomes:
        from: taxableIncomes
      taxFreeIncomes:
        from: taxFreeIncomes
      personalDeductionAmount:
        from: personalDeductionAmount
      dependentDeductionAmount:
        from: dependentDeductionAmount
      numberOfDependentDeduction:
        from: numberOfDependentDeduction
      insuranceDeductible:
        from: insuranceDeductible
      totalAmountOfDeduction:
        from: totalAmountOfDeduction
      totalAssessableIncome:
        from: totalAssessableIncome
      deductedPersonalIncomeTaxAmount:
        from: deductedPersonalIncomeTaxAmount
      totalTaxPayable:
        from: totalTaxPayable
      adjustedTaxAmount:
        from: adjustedTaxAmount
      remainingTaxAmountToPaid:
        from: remainingTaxAmountToPaid
      overpaidTaxAmount:
        from: overpaidTaxAmount
      recalculateMonthlyTax:
        from: recalculateMonthlyTax
        typeOptions:
          func: YNToBoolean
      legalEntityCodes:
        from: legalEntityCodes
      departmentCodes:
        from: departmentCodes
      locked:
        from: locked
        typeOptions:
          func: YNToBoolean
      lockedFilter:
        from: locked
      tariffCode:
        from: tariffCode
      contractTypeCode:
        from: contractTypeCode
      residentStatusCode:
        from: residentStatusCode
      terminationDate:
        from: terminationDate
        typeOptions:
          func: timestampToDateTime
      departmentName:
        from: department.longName
      legalEntityName:
        from: legalEntity.longName
      currencyName:
        from: currency.longName
      tariffName:
        from: tariff.longName
      contractTypeName:
        from: contractType.longName
      residentStatus:
        from: residentStatus
      isStaffRecalculatesMonthlyTax:
        from: isStaffRecalculatesMonthlyTax
      ids:
        from: ids
      terminationDateFrom:
        from: terminationDateFrom
        typeOptions:
          func: timestampToDateTime
      terminationDateTo:
        from: terminationDateTo
        typeOptions:
          func: timestampToDateTime
      employees:
        from: employees
      residentStatusName:
        from: residence.longName
      hrStatusCode:
        from: hrStatusCode
      hrStatusName:
        from: hrStatus.longName
      isCloneRequest:
        from: isCloneRequest
      incomeCommitment:
        from: incomeCommitment
      totalSalaryIncome:
        from: totalSalaryIncome
      totalOtherIncome:
        from: totalOtherIncome
      cancelCommitment:
        from: cancelCommitment
        typeOptions:
          func: YNToBoolean
      dateOfCancelCommitment:
        from: dateOfCancelCommitment
        typeOptions:
          func: timestampToDateTime
      totalTaxableSalaryIncome:
        from: totalTaxableSalaryIncome
      totalTaxableOtherIncome:
        from: totalTaxableOtherIncome
      taxGap:
        from: taxGap
      jobIndicatorCode:
        from: jobIndicatorCode
      jobIndicatorName:
        from: jobIndicator.longName
      typeOfRunCode:
        from: typeOfRunCode
      typeOfRun:
        from: typeOfRun
      typeOfRunName:
        from: typeOfRun.longName
      subStatus:
        from: subStatus
      totalEmployeeWarning:
        from: totalEmployeeWarning
      revision:
        from: revision
      
      monthlyTaxDeclarationPeriodCode:
        from: monthlyTaxDeclarationPeriodCode
      typeOfRunCode:
        from: typeOfRunCode
      errorMessage:
        from: customErrorMessage

      createdBy:
        from: createdBy
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime

  - name: _employeesIntegrate
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: totalPages
    config:
      employeeId:
        from: employeeId
      terminationDate:
        from: terminationDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      employeeRecordNumber:
        from: employeeRecordNumber
      legalEntityCode:
        from: legalEntityCode
      legalEntityCodes:
        from: legalEntityCodes
      departmentCode:
        from: departmentCode
      departmentCodes:
        from: departmentCodes
      hrStatusCode:
        from: hrStatusCode
      isLock:
        from: isLock
        typeOptions:
          func: YNToBoolean
      employee:
        from: employee
      employeeName:
        from: employee.longName
      employeeCode:
        from: employee.code
      paymentDate:
        from: paymentDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
  - name: exportModel
    config:
      id:
        from: id
      code:
        from: code
      monthlyTaxDeclarationPeriodName:
        from: monthlyTaxDeclarationPeriod
      monthlyTaxDeclarationPeriodCode:
        from: monthlyTaxDeclarationPeriodCode
      groupTaxSettlementName:
        from: groupTaxSettlement
      groupTaxSettlementCode:
        from: groupTaxSettlementCode
      startDate:
        from: startDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      endDate:
        from: endDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      revision:
        from: revision
      version:
        from: version
      typeOfRunName:
        from: typeOfRun
      typeOfRunCode:
        from: typeOfRunCode
      calculationStatus:
        from: calculationStatus
      columnNames:
        from: columnNames
        typeOptions:
          func: listKeyFields
      feColumnNames:
        from: feColumnNames
  - name: exportModelIntegrated
    config:
      id:
        from: id
      synthesizingIncomeId:
        from: synthesizingIncomeId
      code:
        from: code
      employeeId:
        from: employeeId
      employeeName:
        from: employee
      subStatus:
        from: subStatus
      employeeRecordNumber:
        from: employeeRecordNumber
      
      locked:
        from: locked
      lockedFilter:
        from: locked
      
      departmentName:
        from: department
      departmentCode:
        from: departmentCode

      legalEntityName:
        from: legalEntity
      legalEntityCode:
        from: legalEntityCode

      tariffName:
        from: tariff
      tariffCode:
        from: tariffCode
      
      taxCode:
        from: taxCode
      contractTypeName:
        from: contractType
      contractTypeCode:
        from: contractTypeCode
      
      residentStatusName:
        from: residence
      residentStatusCode:
        from: residentStatusCode
      
      terminationDate:
        from: terminationDate
        typeOptions:
          func: timestampToDateTime
      currencyName:
        from: currency
      totalIncome:
        from: totalIncome
      taxableIncomes:
        from: taxableIncomes
      taxFreeIncomes:
        from: taxFreeIncomes
      personalDeductionAmount:
        from: personalDeductionAmount
      numberOfDependentDeduction:
        from: numberOfDependentDeduction
      dependentDeductionAmount:
        from: dependentDeductionAmount
      insuranceDeductible:
        from: insuranceDeductible
      totalAssessableIncome:
        from: totalAssessableIncome
      jobIndicatorName:
        from: jobIndicator
      jobIndicatorCode:
        from: jobIndicatorCode

      totalAmountOfDeduction:
        from: totalAmountOfDeduction
      deductedPersonalIncomeTaxAmount:
        from: deductedPersonalIncomeTaxAmount
      totalTaxPayable:
        from: totalTaxPayable
      isStaffRecalculatesMonthlyTax:
        from: isStaffRecalculatesMonthlyTax
      adjustedTaxAmount:
        from: adjustedTaxAmount
      overpaidTaxAmount:
        from: overpaidTaxAmount
      remainingTaxAmountToPaid:
        from: remainingTaxAmountToPaid
      totalSalaryIncome:
        from: totalSalaryIncome
      totalOtherIncome:
        from: totalOtherIncome
      incomeCommitment:
        from: incomeCommitment
      cancelCommitment:
        from: cancelCommitment
      dateOfCancelCommitment:
        from: dateOfCancelCommitment
        typeOptions:
          func: timestampToDateTime
      totalTaxableSalaryIncome:
        from: totalTaxableSalaryIncome
      totalTaxableOtherIncome:
        from: totalTaxableOtherIncome
      taxGap: 
        from: taxGap
      errorMessage:
        from: customErrorMessage
      updatedBy:
        from: updatedBy
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      columnNames:
        from: columnNames
        typeOptions:
          func: listKeyFields
      feColumnNames:
        from: feColumnNames

  - name: progressingSynthesizingIncome
    config:
      id:
        from: id
      synthesizingIncomeId:
        from: synthesizingIncomeId
      revision:
        from: revision
      version:
        from: version
      isReCalc:
        from: isReCalc
      name:
        from: name
      monthlyTaxDeclarationPeriodCode:
        from: monthlyTaxDeclarationPeriodCode
      processing:
        from: processing
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
  - name: multiDeleteModel
    config:
      ids:
        type: array
        from: ids
defaultQuery:
auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: synthesizing-income
crudConfig:
  query:
    sort:
      - field: createdAt
        order: DESC
  params:
    id:
      field: id
      type: string
      primary: true
      configType: url
    id2:
      field: id2
      type: string
    code:
      field: code
      type: string
  routes:
    exclude:
      - createManyBase
      - replaceOneBase

routes:
  - path: /api/synthesizing-income
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'synthesizing-income'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: 'startDate desc'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$'

  - path: /api/synthesizing-income/:id
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'synthesizing-income/:{id}:'
      transform: '$'


customRoutes:
  - path: /api/synthesizing-income/calculate
    method: PATCH
    model: _employees
    query:
    transform: '$'
    bodyTransform: '{"monthlyTaxDeclarationPeriodCode": $.monthlyTaxDeclarationPeriodCode, "typeOfRunCode": $.typeOfRunCode}'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'synthesizing-income'
      transform: '$'
      
  # tab income summary
  - path: /api/synthesizing-income/:id/employees-income-summary
    method: GET
    model: _employees
    query:
    isExtendedFilter: true
    elemMatch: true
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'synthesizing-income/:{id}:/employees'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: 'EmployeeId asc'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$merge([$, {"data":$map($.data,function($item) { $merge([$item,{"residentStatusName":$item.residentStatus!=null? $item.residentStatus ?"Cư trú":"Không cư trú"}]) })[] }])[]'

  # tab calculate
  - path: /api/synthesizing-income/:id/employees/calculate-monthly-tax-declaration
    method: GET
    model: _employees
    query:
    isExtendedFilter: true
    elemMatch: true
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'synthesizing-income/:{id}:/employees/calculate-monthly-tax-declaration'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: 'EmployeeId asc'
        Filter: '::{filter}::'
        Search: ':{search}:'
      transform: '$merge([$, {"data":$map($.data,function($item) { $merge([$item,{"residentStatusName":$item.residentStatus!=null? $item.residentStatus ?"Cư trú":"Không cư trú"}]) })[] }])[]'

  # calculate tab calculate
  - path: /api/synthesizing-income/:id/employees/calculate-monthly-tax-declaration
    method: POST
    model: _employees
    query:
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'synthesizing-income/:{id}:/employees/calculate-monthly-tax-declaration'
      transform: '$'

  # export tab calc
  - path: /api/synthesizing-income/:id2/employees/calculate-monthly-tax-declaration/export
    method: POST
    model: exportModelIntegrated
    elemMatch: true
    isExtendedFilter: true
    query:
    upstreamConfig:
      method: POST
      response:
        dataType: binary
      path: 'synthesizing-income/export-synthesizing-income-for-employee-report:export'
      query:
        id: ':{id2}:'
        isCalculationTax: 'Y'
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: 'EmployeeId asc'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$'

  # tab integrate data synthetize employee integrate
  - path: /api/synthesizing-income/:id/raw-data-employees
    method: GET
    model: _employeesIntegrate
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'synthesizing-income/:{id}:/raw-data-employees'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$'

  # tab non-recalc
  - path: /api/synthesizing-income/:id/employees/non-recalculation
    method: GET
    model: _employees
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'synthesizing-income/:{id}:/employees/non-recalculation'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: 'EmployeeId asc'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$merge([$, {"data":$map($.data,function($item) { $merge([$item,{"residentStatusName":$item.residentStatus!=null? $item.residentStatus ?"Cư trú":"Không cư trú"}]) })[] }])[]'

  # tab recalc
  - path: /api/synthesizing-income/:id/employees/recalculation
    method: GET
    model: _employees
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'synthesizing-income/:{id}:/employees/recalculation'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: 'EmployeeId asc'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$merge([$, {"data":$map($.data,function($item) { $merge([$item,{"residentStatusName":$item.residentStatus!=null? $item.residentStatus ?"Cư trú":"Không cư trú"}]) })[] }])[]'

  # edit caret tab non calc
  - path: /api/synthesizing-income/:id/employees/caret
    method: PATCH
    model: _employees
    query:
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'synthesizing-income/:{id}:/employees/caret'
      transform: '$'

  # lock
  - path: /api/synthesizing-income/:id/employees/lock
    method: POST
    model: _employees
    query:
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'synthesizing-income/:{id}:/employees/lock'
      transform: '$'

  # unlock
  - path: /api/synthesizing-income/:id/employees/unlock
    method: POST
    model: _employees
    query:
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'synthesizing-income/:{id}:/employees/unlock'
      transform: '$'

  # tab detail income summary
  - path: /api/synthesizing-income/:id2/employees-income-summary/:id
    method: GET
    model: _employees
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'synthesizing-income/:{id2}:/employees/:{id}:'
      transform: '$ ~> |$| {"residentStatusName": $.residentStatus!=null? $.residentStatus?"Cư trú":"Không cư trú"} |'

  # export tab income summary
  - path: /api/synthesizing-income/:id2/employees-income-summary/export
    method: POST
    model: exportModelIntegrated
    elemMatch: true
    isExtendedFilter: true
    query:
    upstreamConfig:
      method: POST
      response:
        dataType: binary
      path: 'synthesizing-income/export-synthesizing-income-for-employee-report:export'
      query:
        id: ':{id2}:'
        isCalculationTax: 'N'
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: 'EmployeeId asc'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$'

  # tab detail calc
  - path: /api/synthesizing-income/:id2/employees/calculate-monthly-tax-declaration/:id
    method: GET
    model: _employees
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'synthesizing-income/:{id2}:/employees/:{id}:'
      transform: '$ ~> |$| {"residentStatusName": $.residentStatus!=null? $.residentStatus?"Cư trú":"Không cư trú"} |'

  # tab detail calc edit
  - path: /api/synthesizing-income/:id2/update-employees/:id
    method: PATCH
    model: _employees
    query:
    upstreamConfig:
      method: PUT
      response:
        dataType: object
      path: 'synthesizing-income/:{id2}:/employees/:{id}:'
      query:
        id: ':{id2}:'
        employeeSynthesizingIncomeId: ':{id}:'
      transform: '$'

  # export master
  - path: /api/synthesizing-income/export
    method: POST
    model: exportModel
    query:
    upstreamConfig:
      method: POST
      response:
        dataType: binary
      path: 'synthesizing-income/export-synthesizing-income-report:export'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$'

  # tab detail calc
  - path: /api/synthesizing-income/progressing
    method: GET
    model: progressingSynthesizingIncome
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'synthesizing-income/loading'
      query:
        isReCalc: ':{isReCalc}:'
      transform: '$'

  # tab detail calc
  - path: /api/synthesizing-income/with-next-revision
    method: GET
    model: _employees
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'synthesizing-income/with-next-revision'
      query:
        monthlyTaxDeclarationPeriodCode: ':{monthlyTaxDeclarationPeriodCode}:'
        typeOfRunCode: ':{typeOfRunCode}:'
      transform: '$'

  - path: /api/synthesizing-income/multidelete
    method: DELETE
    model: multiDeleteModel
    # bodyTransform: '{"ids": $.ids[]}'
    upstreamConfig:
      method: DELETE
      path: 'synthesizingIncomes'

  - path: /api/synthesizing-income/report-revisions
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'synthesizing-income/report-revisions'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
        assessmentPeriodCode: ':{assessmentPeriodCode}:'
        legalEntityCode: ':{legalEntityCode}:'
