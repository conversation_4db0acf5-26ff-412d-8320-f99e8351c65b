controller: tariff
upstream: ${{UPSTREAM_PIT_URL}}

models:
  - name: _
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: totalPages
    config:
      id:
        from: id
      # name BFF
      code:
        # name BE
        from: code
      taxCalculationMethod:
        from: taxForm.longName
      taxCalculationMethodCode:
        from: taxFormCode
      countryName:
        from: country.longName
      countryCode:
        from: countryCode
      country:
        from: $
        objectChildren:
          value:
            from: countryCode
          label:
            from: country.longName,countryCode
            typeOptions:
              func: fieldsToNameCode
      currency:
        from: currency.longName
      currencyCode:
        from: currencyCode
      tariffLevels:
        from: tariffLevels
      status:
        from: enabled
        typeOptions:
          func: YNToBoolean
      selfDeduction:
        from: selfDeduction
        typeOptions:
          func: YNToBoolean
      shortName:
        from: shortName
        typeOptions:
          func: stringToMultiLang
      longName:
        from: name
        typeOptions:
          func: stringToMultiLang
      effectiveDate:
        from: effectiveDateFrom
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      effectiveDateTo:
        from: effectiveDateTo
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      note:
        from: note
        typeOptions:
          func: stringToMultiLang
      createdBy:
        from: createdBy
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime

  - name: multiDeleteModel
    config:
      ids:
        type: array
        from: ids

  - name: exportModel
    config:
      id:
        from: id
      code:
        from: code
      taxCalculationMethod:
        from: taxForm
      taxCalculationMethodCode:
        from: taxFormCode
      countryName:
        from: country
      countryCode:
        from: countryCode
      status:
        from: enabled
        typeOptions:
          func: YNToBoolean
      shortName:
        from: shortName
        typeOptions:
          func: stringToMultiLang
      longName:
        from: name
        typeOptions:
          func: stringToMultiLang
      selfDeduction:
        from: selfDeduction
        typeOptions:
          func: YNToBoolean
      updatedBy:
        from: updatedBy
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      columnNames:
        from: columnNames
        typeOptions:
          func: listKeyFields
      feColumnNames:
        from: feColumnNames
auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: tariff
crudConfig:
  query:
    sort:
      - field: createdAt
        order: DESC
  params:
    id:
      field: id
      type: string
      primary: true
    code:
      field: code
      type: string
  routes:
    exclude:
      - createManyBase
      - replaceOneBase
defaultQuery:

routes:
  # list table
  - path: /api/tariff
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
        dataKey: data
      path: 'tariff'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$'

  # create
  - path: /api/tariff
    method: POST
    model: _
    query:
    transform: '$'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'tariff'
      transform: '$'

  # detail
  - path: /api/tariff/:id
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'tariff/:{id}:'
      transform: '$ ~> |$| {"taxCalculationMethodCode":$boolean($.taxCalculationMethodCode) = true? {"label":$.taxCalculationMethod & " (" & $.taxCalculationMethodCode & ")", "value":$.taxCalculationMethodCode} }|'

  # edit
  - path: /api/tariff/:id
    model: _
    method: PATCH
    query:
    transform: '$'
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: 'tariff/:{id}:'
      transform: '$'

  #delete
  - path: /api/tariff/:id
    method: DELETE
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'tariff/:{id}:'

customRoutes:
  - path: /api/tariff/:id/histories
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'tariff/history?code=:{id}:'
      transform: '$map($, function($item){$merge([$item,{"taxCalculationMethodCode":$boolean($item.taxCalculationMethodCode) = true? {"label":$item.taxCalculationMethod & " (" & $item.taxCalculationMethodCode & ")", "value":$item.taxCalculationMethodCode}}])})[]'

  - path: /api/tariff/export
    method: POST
    model: exportModel
    query:
    upstreamConfig:
      method: POST
      response:
        dataType: binary
      path: 'tariff/export-tariff-report:export'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$'

  #insert new for custom routes
  - path: /api/tariff/custom-history/:code
    method: POST
    model: _
    query:
    transform: '$'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'tariff/:{code}:'
      transform: '$'

  - path: /api/tariff/multidelete
    method: DELETE
    model: multiDeleteModel
    bodyTransform: '{"ids": $.ids[]}'
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'tariffs'
