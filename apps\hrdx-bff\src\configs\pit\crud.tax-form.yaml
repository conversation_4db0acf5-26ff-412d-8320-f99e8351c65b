controller: tax-form
upstream: ${{UPSTREAM_PIT_URL}}

models:
  - name: _
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: totalPages
    config:
      id:
        from: id
      # name BFF
      code:
        # name BE
        from: code
      longName:
        from: name
        typeOptions:
          func: stringToMultiLang
      shortName:
        from: shortName
        typeOptions:
          func: stringToMultiLang
      note:
        from: note
        typeOptions:
          func: stringToMultiLang
      status:
        from: enabled
        typeOptions:
          func: YNToBoolean
      effectiveDate:
        from: effectiveDateFrom
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      effectiveDateTo:
        from: effectiveDateTo
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      countryName:
        from: country.longName
      countryCode:
        from: countryCode
      country:
        from: $
        objectChildren:
          value:
            from: countryCode
          label:
            from: country.longName,countryCode
            typeOptions:
              func: fieldsToNameCode
      createdBy:
        from: createdBy
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
  - name: multiDeleteModel
    config:
      ids:
        type: array
        from: ids

  - name: exportModel
    config:
      id:
        from: id
      code:
        from: code
      countryName:
        from: country
      countryCode:
        from: countryCode
      status:
        from: enabled
        typeOptions:
          func: YNToBoolean
      shortName:
        from: shortName
        typeOptions:
          func: stringToMultiLang
      longName:
        from: name
        typeOptions:
          func: stringToMultiLang
      updatedBy:
        from: updatedBy
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      columnNames:
        from: columnNames
        typeOptions:
          func: listKeyFields
      feColumnNames:
        from: feColumnNames
auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: tax-form
crudConfig:
  query:
    sort:
      - field: code
        order: DESC
  params:
    id:
      field: id
      type: string
      primary: true
    code:
      field: code
      type: string
  routes:
    exclude:
      - createManyBase
      - replaceOneBase
defaultQuery:

routes:
  # list table
  - path: /api/tax-form
    method: GET
    model: _

    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
        dataKey: data
      path: 'tax-form'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$'

  # create
  - path: /api/tax-form
    method: POST
    model: _
    query:
    transform: '$'
    bodyTransform: '$merge([$, {"EffectiveDateTo": 0}])'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'tax-form'
      transform: '$'

  # detail
  - path: /api/tax-form/:id
    method: GET
    model: _

    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'tax-form/:{id}:'
      transform: '$'

  # edit
  - path: /api/tax-form/:id
    model: _
    method: PATCH
    query:
    transform: '$'
    bodyTransform: '$merge([$, {"EffectiveDateTo": 0}])'
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: 'tax-form/:{id}:'
      transform: '$'

  #delete
  - path: /api/tax-form/:id
    method: DELETE
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'tax-form/:{id}:'

customRoutes:
  - path: /api/tax-form/:id/histories
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'tax-form/history?code=:{id}:'
      transform: '$'

  - path: /api/tax-form/export
    method: POST
    model: exportModel
    query:
    upstreamConfig:
      method: POST
      response:
        dataType: binary
      path: 'tax-form/export-tax-form-report:export'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$'

  #insert new for custom routes
  - path: /api/tax-form/custom-history/:code
    method: POST
    model: _
    query:
    transform: '$'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'tax-form/:{code}:'
      transform: '$'

  - path: /api/tax-form/multidelete
    method: DELETE
    model: multiDeleteModel
    bodyTransform: '{"ids": $.ids[]}'
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'tax-forms'
