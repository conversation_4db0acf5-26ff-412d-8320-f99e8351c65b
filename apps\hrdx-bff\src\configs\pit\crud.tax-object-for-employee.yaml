controller: tax-object-for-employee
upstream: ${{UPSTREAM_PIT_URL}}

models:
  - name: _
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: totalPages
    config:
      id: #name-config
        from: id
        type: string
      code:
        from: code #name-BE
        type: string

      employeeId:
        from: employeeId
        type: string
      employeeRecordNumber:
        from: employeeRecordNumber
        type: string
      employeeName:
        from: employee.longName
        type: string

      jobIndicatorCode:
        from: jobIndicatorCode
        type: string
      jobIndicator:
        from: jobIndicator.longName
        type: string

      localForeigner:
        from: localForeigner
        type: string

      residenceStatus:
        from: residenceStatus
        type: boolean

      countryCode:
        from: countryCode
        type: string
      country:
        from: country.longName
        type: string

      groupCode:
        from: groupCode
        type: string
      group:
        from: group.longName
        type: string

      companyCode:
        from: companyCode
        type: string
      company:
        from: company.longName
        type: string

      legalEntityCode:
        from: legalEntityCode
        type: string
      legalEntity:
        from: legalEntity.longName
        type: string

      businessUnitCode:
        from: businessUnitCode
        type: string
      businessUnit:
        from: businessUnit.longName
        type: string

      divisionCode:
        from: divisionCode
        type: string
      division:
        from: division.longName
        type: string

      departmentCode:
        from: departmentCode
        type: string
      department:
        from: department.longName
        type: string

      employeeGroupCode:
        from: employeeGroupCode
        type: string
      employeeGroup:
        from: employeeGroup.longName
        type: string

      contractTypeCode:
        from: contractTypeCode
        type: string
      contractType:
        from: contractType.longName
        type: string

      tariffCode:
        from: tariffCode
        type: string
      tariffObject:
        from: $
        objectChildren:
          value:
            from: tariffCode
          label:
            from: tariff.longName,tariffCode
            typeOptions:
              func: fieldsToNameCode
      tariff:
        from: tariff.longName
        type: string

      effectiveDateFrom:
        from: effectiveDateFrom
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      effectiveDateTo:
        from: effectiveDateTo
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      employeeSearch:
        from: employeeSearch
      note:
        from: note
        type: string
      createdBy:
        from: createdBy
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
  - name: multiDeleteModel
    config:
      ids:
        type: array
        from: ids

  - name: exportModel
    config:
      id:
        from: id
      code:
        from: code
      employeeSearch:
        from: employeeSearch
      jobIndicatorCode:
        from: jobIndicatorCode
      residenceStatus:
        from: residenceStatus
      employeeId:
        from: employeeId
      employeeRecordNumber:
        from: employeeRecordNumber
      employeeName:
        from: employee
      jobIndicator:
        from: jobIndicator
      residenceStatusName:
        from: residenceStatus
      group:
        from: group
      groupCode:
        from: groupCode
      company:
        from: company
      companyCode:
        from: companyCode
      localForeigner:
        from: localForeigner
      legalEntity:
        from: legalEntity
      legalEntityCode:
        from: legalEntityCode
      businessUnit:
        from: businessUnit
      businessUnitCode:
        from: businessUnitCode
      division:
        from: division
      divisionCode:
        from: divisionCode
      department:
        from: department
      departmentCode:
        from: departmentCode
      employeeGroup:
        from: employeeGroup
      employeeGroupCode:
        from: employeeGroupCode
      contractType:
        from: contractType
      contractTypeCode:
        from: contractTypeCode
      tariff:
        from: tariff
      tariffCode:
        from: tariffCode
      effectiveDateFrom:
        from: effectiveDateFrom
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      effectiveDateTo:
        from: effectiveDateTo
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      note:
        from: note
      createdBy:
        from: createdBy
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      columnNames:
        from: columnNames
        typeOptions:
          func: listKeyFields
      feColumnNames:
        from: feColumnNames
auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: tax-object-for-employee
crudConfig:
  query:
    sort:
      - field: updatedAt
        order: DESC
  params:
    id:
      field: id
      type: string
      primary: true
  routes:
    exclude:
      - createManyBase
      - replaceOneBase
defaultQuery:

routes:
  - path: /api/tax-object-for-employee
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'tax-object-for-employee'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$ ~> |$| {"data": $map($.data, function($item) { $merge([$item, { "residenceStatusName":$item.residenceStatus!=null? $item.residenceStatus ?"Cư trú":"Không cư trú"} ]) })[] } |'

  - path: /api/tax-object-for-employee/:id
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'tax-object-for-employee/:{id}:'
      transform: '$merge([$, { "residenceStatusName":$.residenceStatus!=null? $.residenceStatus ?"Cư trú":"Không cư trú"} ])'

  - path: /api/tax-object-for-employee
    method: POST
    model: _
    query:
    transform: '$'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'tax-object-for-employee'
      transform: '$'

  - path: /api/tax-object-for-employee/:id
    model: _
    method: PATCH
    query:
    transform: '$'
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: 'tax-object-for-employee/:{id}:'

  - path: /api/tax-object-for-employee/:id
    method: DELETE
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'tax-object-for-employee/:{id}:'
customRoutes:
  - path: /api/tax-object-for-employee/multidelete
    method: DELETE
    model: multiDeleteModel
    bodyTransform: '{"ids": $.ids[]}'
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'tax-object-for-employees'

  - path: /api/tax-object-for-employee/export
    method: POST
    model: exportModel
    query:
    upstreamConfig:
      method: POST
      response:
        dataType: binary
      path: 'tax-object-for-employee/export-tax-object-for-employee-report:export'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$'
