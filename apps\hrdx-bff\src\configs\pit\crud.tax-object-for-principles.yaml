controller: tax-object-for-principles
upstream: ${{UPSTREAM_PIT_URL}}

models:
  - name: _
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: totalPages
    config:
      #FE
      id:
        from: id
        type: string
      country:
        from: country.longName
        type: string
      countryCode:
        from: countryCode
        type: string
      countryObject:
        from: $
        objectChildren:
          value:
            from: countryCode
          label:
            from: country.longName,countryCode
            typeOptions:
              func: fieldsToNameCode
      group:
        from: group.longName
        type: string
      groupCode:
        from: groupCode
        type: string
      company:
        from: company.longName
        type: string
      companyCode:
        from: companyCode
        type: string
      legalEntity:
        from: legalEntity.longName
        type: string
      legalEntityCode:
        from: legalEntityCode
        type: string
      payGroup:
        from: payGroup.longName
        type: string
      payGroupCode:
        from: payGroupCode
        type: string
      localForeigner:
        from: localForeigner.longName
        type: string
      localForeignerCode:
        from: isLocalsCode
        type: string
      residenceStatus:
        from: residenceStatus.longName
        type: string
      residenceStatusCode:
        from: residenceStatusCode
        type: string
      contractTypes:
        from: contractTypes
      contractTypesName:
        from: contractTypesName
      contractTypeCodes:
        from: contractTypeCodes
      contractTypesCode:
        from: contractTypesCode
      employeeGroup:
        from: employeeGroup.longName
        type: string
      employeeGroupCode:
        from: employeeGroupCode
        type: string
      effectiveDate:
        from: effectiveDateFrom
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      effectiveDateTo:
        from: effectiveDateTo
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      tariffObject:
        from: $
        objectChildren:
          value:
            from: tariffCode
          label:
            from: tariff.longName,tariffCode
            typeOptions:
              func: fieldsToNameCode
      tariff:
        from: tariff.longName
        type: string
      tariffCode:
        from: tariffCode
        type: string
      note:
        from: note
        type: string
      createdBy:
        from: createdBy
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
  - name: multiDeleteModel
    config:
      ids:
        type: array
        from: ids

  - name: exportModel
    config:
      id:
        from: id
      code:
        from: code
      country:
        from: country
      countryCode:
        from: countryCode
      group:
        from: group
      company:
        from: company
      legalEntity:
        from: legalEntity
      payGroup:
        from: payGroup
      localForeigner:
        from: localForeigner
      residenceStatus:
        from: residenceStatus
      residenceStatusCode:
        from: residenceStatusCode
      contractTypesName:
        from: contractTypes
      contractTypesCode:
        from: contractTypesCode
      employeeGroup:
        from: employeeGroup
      effectiveDate:
        from: effectiveDateFrom
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      effectiveDateTo:
        from: effectiveDateTo
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      tariff:
        from: tariff
      tariffCode:
        from: tariffCode
      note:
        from: note

      createdBy:
        from: createdBy
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      columnNames:
        from: columnNames
        typeOptions:
          func: listKeyFields
      feColumnNames:
        from: feColumnNames

auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: tax-object-for-principles
crudConfig:
  query:
    sort:
      - field: createdAt
        order: DESC
  params:
    id:
      field: id
      type: string
      primary: true
  routes:
    exclude:
      - createManyBase
      - replaceOneBase
defaultQuery:

routes:
  - path: /api/tax-object-for-principles
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'tax-object-for-principles'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$merge([$,{ "data": $map($.data,function($item){ $merge([$item, {"contractTypesName": $map($item.contractTypes, function($i) {$i.longName & " (" & $i.code & ")"})[] }]) })[] }])'

  - path: /api/tax-object-for-principles/:id
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'tax-object-for-principles/:{id}:'
      transform: '$~>|$| {"tariffCode":$.tariffCode?{"label":$.tariff & " (" & $.tariffCode & ")", "value":$.tariffCode}, "contractTypeCodes": $map($.contractTypes, function($item) { $item.code })[], "contractTypesName":$map($.contractTypes, function($item){ $item.longName & " (" & $item.code & ")" })[]} |'

  - path: /api/tax-object-for-principles
    method: POST
    model: _

    query:
    transform: '$'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'tax-object-for-principles'
      transform: '$'

  - path: /api/tax-object-for-principles/:id
    model: _
    method: PATCH
    query:
    transform: '$'
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: 'tax-object-for-principles/:{id}:'

  - path: /api/tax-object-for-principles/:id
    method: DELETE
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'tax-object-for-principles/:{id}:'
customRoutes:
  - path: /api/tax-object-for-principles/:id/histories
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'tax-object-for-principles'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
      transform: '$'

  - path: /api/tax-object-for-principles/multidelete
    method: DELETE
    model: multiDeleteModel
    bodyTransform: '{"ids": $.ids[]}'
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'tax-object-for-principles'

  - path: /api/tax-object-for-principles/export
    method: POST
    model: exportModel
    query:
    upstreamConfig:
      method: POST
      response:
        dataType: binary
      path: 'tax-object-for-principles/export-tax-object-for-principle-report:export'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$'
