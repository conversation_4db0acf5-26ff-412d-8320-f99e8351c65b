controller: deductions
upstream: ${{UPSTREAM_PR_URL}}

models:
  - name: _
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: count
    config:
      id:
        from: id
        type: string
      # name BFF
      code:
        # name BE
        from: code
        # type BE
        type: string
      shortName:
        from: shortName
        type: string
        typeOptions:
          func: stringToMultiLang
      longName:
        from: name
        type: string
        typeOptions:
          func: stringToMultiLang
      countryCode:
        from: countryCode
        type: string
      countryName:
        from: country.longName
        type: string
      countryId:
        from: country.id
      countryObj: 
        from: $
        objectChildren:
          id: 
            from: country.id
          code: 
            from: countryCode
      currency:
        from: currencyCode
        type: string
      currencyName:
        from: currency.longName
        type: string
      entryType:
        from: entryType
        type: string
      entryTypeName:
        from: entry.longName
        type: string
      deductTaxableIncome:
        from: deductTaxableIncome
        type: string
        typeOptions:
          func: YNToBoolean
      effectiveDate:
        from: effectiveDateFrom
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      status:
        from: enabled
        type: string
        typeOptions:
          func: YNToBoolean
      note:
        from: note
        type: string
        typeOptions:
          func: stringToMultiLang
      createdAt:
        from: createdAt
        type: string
        typeOptions:
          func: timestampToDateTime
      createdBy:
        from: createdBy
        type: string
      updatedBy:
        from: updatedBy
        type: string
      updatedAt:
        from: updatedAt
        type: string
        typeOptions:
          func: timestampToDateTime
      IsCombobox:
        from: IsCombobox
        typeOptions:
          func: YNToBoolean
auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: deductions
crudConfig:
  query:
    sort:
      - field: updatedAt
        order: DESC
  params:
    id:
      field: id
      type: string
      primary: true
    code:
      field: code
      type: string
  routes:
    exclude:
      - createManyBase
      - replaceOneBase
defaultQuery:

routes:
  - path: /api/deductions
    method: GET
    model: _

    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'deductions'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        IsCombobox: ':{IsCombobox}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$'

  - path: /api/deductions/:id
    method: GET
    model: _

    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'deductions/:{id}:'
      transform: '$merge([
        $,
        {
          "countryObj":countryCode ? 
          {
            "label": countryName & " (" & countryCode &")", 
            "value": {"id": countryId, "code": countryCode }
          }
        }
      ])'

  - path: /api/deductions
    method: POST
    model: _

    query:
    transform: '$'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'deductions'
      transform: '$'

  - path: /api/deductions/:id
    model: _
    method: PATCH
    query:
    transform: '$'
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: 'deductions/:{id}:'

  - path: /api/deductions/:id
    method: DELETE
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'deductions/:{id}:'
customRoutes:
  - path: /api/deductions/:id/histories
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'deductions/:{id}:/history'
      transform: '$map($ , function($item) {
        $merge([
        $item,
        {
          "countryObj":$item.countryCode ? 
          {
            "label": $item.countryName & " (" & $item.countryCode &")", 
            "value": {"id": $item.countryId, "code": $item.countryCode }
          }
        }
      ])
      })[]'
  - path: /api/deductions/by
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      query:
        Filter: '::{filter}::'
      path: 'deductions/by'
      transform: '$'
  - path: /api/deductions/:id/clone
    method: POST
    model: _
    query:
    transform: $
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'deductions/:{id}:/clone'
      transform: $
  - path: /api/deductions/export
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: binary
      path: 'deductions/export'
      query:
        PageSize: '1000'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$'
  - path: /api/deductions/multidelete
    method: DELETE
    model: multiDeleteModel
    bodyTransform: '$.ids[]'
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'deductions'