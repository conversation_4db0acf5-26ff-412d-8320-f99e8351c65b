controller: employee-pay-groups
upstream: ${{UPSTREAM_PR_URL}}

models:
  - name: _
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: totalPages
    config:
      id:
        from: id
        type: string

      employeeId:
        from: employeeId
      employeeIdFilter:
        from: employeeId
        type: string
      ern:
        from: ern
      employeeGroupName:
        from: employeeGroup.longName
        type: string
      employeeFullName:
        from: employeeFullName
        type: string
      employeeGroupCode:
        from: employeeGroupCode
      employeeRecordNumber:
        from: employeeRecordNumber
        type: number
      payGroup:
        from: payGroup
      payGroupCodeShow:
        from: payGroup.code
      payGroupCode:
        from: payGroupCode
      payGroupShowTable:
        from: payGroupCode
      payGroupName:
        from: payGroup.longName
        type: string
      company:
        from: company
      companyCode:
        from: companyCode
      companyName:
        from: company.longName
        type: string
      legalEntity:
        from: legalEntity
      legalEntityCode:
        from: legalEntityCode
      legalEntityName:
        from: legalEntity.longName
        type: string
      employeeName:
        from: employeeName
      fullName:
        from: fullName
      jobData:
        from: jobData
        type: object
        objectChildren:
          fullName:
            from: fullName
          companyCode:
            from: companyCode
          legalEntityCode:
            from: legalEntityCode
          businessUnitCode:
            from: businessUnitCode
          divisionCode:
            from: divisionCode
          departmentCode:
            from: departmentCode
          employeeGroupCode:
            from: employeeGroupCode
          jobLevelCode:
            from: jobLevelCode
          empLevelCode:
            from: empLevelCode
          contractTypeCode:
            from: contractTypeCode
          locationCode:
            from: locationCode
      employeeId_ern:
        from: employeeId_ern
      businessUnit:
        from: businessUnit
      businessUnitCode:
        from: businessUnitCode
      businessUnitName:
        from: businessUnit.longName
        type: string
      division:
        from: division
      divisionName:
        from: division.longName
        type: string
      divisionCode:
        from: divisionCode
      department:
        from: department
      departmentCode:
        from: departmentCode
      departmentName:
        from: department.longName
        type: string
      jobTitle:
        from: jobTitle
      jobTitleCode:
        from: jobTitleCode
      jobTitleName:
        from: jobTitle.longName
        type: string
      location:
        from: location
      locationCode:
        from: locationCode
      positionCode:
        from: positionCode
      locationName:
        from: location.longName
        type: string
      staffLevel:
        from: staffLevel
      staffLevelCode:
        from: staffLevelCode
      staffLevelName:
        from: staffLevel.longName
        type: string
      contractType:
        from: contractType
      contractTypeCode:
        from: contractTypeCode

      contractTypeName:
        from: contractType.longName
        type: string

      effectiveDate:
        from: effectiveDateFrom
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      effectiveDateForSort:
        from: effectiveDateFrom
      status:
        from: enabled
        type: string
        typeOptions:
          func: YNToBoolean
      note:
        from: note
        type: string
        typeOptions:
          func: stringToMultiLang
      createdBy:
        from: createdBy
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
  - name: multiDeleteModel
    config:
      ids:
        type: array
        from: ids
auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: employee-pay-groups
crudConfig:
  query:
    sort:
      - field: updatedAt
        order: DESC
  params:
    id:
      field: id
      type: string
      primary: true
    employeeId:
      field: employeeId
      type: string
    ern:
      field: ern
      type: string
    employeeGroupCode:
      field: employeeGroupCode
      type: string
    code:
      field: code
      type: string
    longDate:
      field: longDate
      type: number
  routes:
    exclude:
      - createManyBase
      - replaceOneBase
defaultQuery:

routes:
  - path: /api/employee-pay-groups
    method: GET
    model: _

    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'employee-pay-groups'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '(    $listEmp := $distinct($map($.data, function($v) { {         "employeeId": $v.employeeId,         "employeeGroupCode": ($v.jobData.employeeGroupCode ? $v.jobData.employeeGroupCode : ""),         "employeeRecordNumber": $v.employeeRecordNumber     } } ));    $items:= [$map($listEmp, function($group){ (        $employee := $filter($.data, function($v){$v.employeeId = $group.employeeId and         ($v.jobData.employeeGroupCode ? $v.jobData.employeeGroupCode : "") = $group.employeeGroupCode and         $v.employeeRecordNumber = $group.employeeRecordNumber })[0];        {            "employeeId": $employee.employeeId,            "employeeRecordNumber": $employee.employeeRecordNumber,            "fullName": $employee.fullName,   "employeeGroupCode": $employee.employeeGroupCode,         "children": $sort([$filter($.data, function($v){$v.employeeId = $group.employeeId and ($v.jobData.employeeGroupCode ? $v.jobData.employeeGroupCode : "") = $group.employeeGroupCode and $v.employeeRecordNumber = $group.employeeRecordNumber})], function($a, $b) {    $b.effectiveDateForSort > $a.effectiveDateForSort})        };    )})];    $merge([$, {"data": $items} ]))'

  - path: /api/employee-pay-groups/:id
    method: GET
    model: _

    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'employee-pay-groups/:{id}:'
      transform: '$ ~> | $ | {"payGroupCode": {"label": $.payGroupName & " (" & $.payGroupCodeShow & ")", "value": $.payGroup.code }, "employee": {"label": $join($filter([$.employeeId,$.employeeGroupCode,$string($.employeeRecordNumber), $.fullName], $boolean), " - "), "value": {"id": $.employeeId,"code": $.employeeId, "employeeRecordNumber": $.employeeRecordNumber, "jobDataId": $.jobDataId}}} |'

  - path: /api/employee-pay-groups
    method: POST
    model: _

    query:
    transform: '$'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'employee-pay-groups'
      transform: '$'

  - path: /api/employee-pay-groups/:id
    model: _
    method: PATCH
    query:
    transform: '$'
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: 'employee-pay-groups/:{id}:'

  - path: /api/employee-pay-groups/:id
    method: DELETE
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'employee-pay-groups/:{id}:'

customRoutes:
  - path: /api/employee-pay-groups/group
    method: GET
    isExtendedFilter: true
    elemMatch: true
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'employee-pay-groups:group'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$ ~> | $ | {"id": null} |'
  - path: /api/employee-pay-groups/group-detail/:employeeId/:ern/:employeeGroupCode
    method: GET
    isExtendedFilter: true
    elemMatch: true
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'employee-pay-groups:group-detail'
      query:
        Search: ':{search}:'
        Filter: '::{filter}::'
        OrderBy: ':{options.sort}:'
        employeeId: ':{employeeId}:'
        ern: ':{ern}:'
        employeeGroupCode: ':{employeeGroupCode}:'
      transform: '$'
  - path: /api/employee-pay-groups/group-detail/:employeeId/:ern
    method: GET
    isExtendedFilter: true
    elemMatch: true
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'employee-pay-groups:group-detail'
      query:
        Search: ':{search}:'
        Filter: '::{filter}::'
        OrderBy: ':{options.sort}:'
        employeeId: ':{employeeId}:'
        ern: ':{ern}:'
      transform: '$'

  - path: /api/employee-pay-groups/:id/histories
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'employee-pay-groups/:{id}:/history'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
      transform: '$'

  - path: /api/employee-pay-groups/:id/clone
    method: POST
    model: _
    query:
    transform: $
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'employee-pay-groups/:{id}:/clone'
      transform: $

  - path: /api/employee-pay-groups/export
    method: GET
    elemMatch: true
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: binary
      path: 'employee-pay-groups/export'
      query:
        PageSize: '1000'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$'

  - path: /api/employee-pay-groups/multidelete
    method: DELETE
    model: multiDeleteModel
    bodyTransform: '$.ids[]'
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'employee-pay-groups'
