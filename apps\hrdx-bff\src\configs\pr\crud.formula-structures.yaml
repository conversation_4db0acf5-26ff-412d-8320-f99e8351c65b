controller: formula-structures
upstream: ${{UPSTREAM_PR_URL}}

models:
  - name: _
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: totalPages
    config:
      id:
        from: id
        type: string
      code:
        from: code
        type: string
      readOnly:
        from: readOnly
        type: boolean
      name:
        from: name
        type: string
        typeOptions:
          func: stringToMultiLang
      shortName:
        from: shortName
        type: string
        typeOptions:
          func: stringToMultiLang
      scopes:
        from: scopes
      formulaCode:
        from: formulaCode
        type: string
      formulaShortName:
        from: formula.shortName
        type: string
      formulaLongName:
        from: formula.longName
        type: string
      elementGroupName:
        from: elementGroup.longName
        type: string
      elementGroupCode:
        from: elementGroupCode
        type: string
      elementGroupCodeFilter:
        from: elementGroupCode
        type: string
      elementTypeName:
        from: elementType.longName
        type: string
      elementTypeCode:
        from: elementTypeCode
        type: string
      elementTypeCodeFilter:
        from: elementTypeCode
        type: string
      country:
        from: country.longName
        type: string
      countryCode:
        from: countryCode
        type: string
      formula:
        from: formula.longName
        type: string
      companyObj:
        from: companyObj
      legalEntityObj:
        from: legalEntityObj
      businessUnitObj:
        from: businessUnitObj
      divisionObj:
        from: divisionObj
      departmentObj:
        from: departmentObj
      payGroupObj:
        from: payGroupObj
      object:
        from: typeCode
        type: string
      company:
        from: company.longName
        type: string
      companyCode:
        from: companyCode
        type: string
      
      legalEntity:
        from: legalEntity.longName
        type: string
      legalEntityCode:
        from: legalEntityCode
        type: string
      containsFullLegalEntityScope:
        from: containsFullLegalEntityScope
      businessUnit:
        from: businessUnit.longName
        type: string
      businessUnitCode:
        from: businessUnitCode
        type: string
      division:
        from: division.longName
        type: string
      divisionCode:
        from: divisionCode
        type: string
      department:
        from: department.longName
        type: string
      departmentCode:
        from: departmentCode
        type: string
      localExpat:
        from: residencyStatusCode
        type: string
      localExpatName:
        from: residencyStatus.longName
        type: string
      location:
        from: location.longName
        type: string
      locationCode:
        from: locationCode
        type: string
      payGroupName:
        from: payGroupNames
        type: string
      payGroupCode:
        from: payGroups
        arrayChildren:
          value:
            from: payGroupCode
      effectiveDate:
        from: effectiveDateFrom
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      status:
        from: enabled
        type: string
        typeOptions:
          func: YNToBoolean
      note:
        from: note
        type: string
        typeOptions:
          func: stringToMultiLang

      createdBy:
        from: createdBy
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      countryCodes:
        from: CountryCodes
      companyCodes:
        from: CompanyCodes
      legalEntityCodes:
        from: LegalEntityCodes
      businessUnitCodes:
        from: BusinessUnitCodes
      divisionCodes:
        from: DivisionCodes
      departmentCodes:
        from: DepartmentCodes
      payGroupCodes:
        from: PayGroupCodes
auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: formula-structures
crudConfig:
  query:
    sort:
      - field: updatedAt
        order: DESC
  params:
    id:
      field: id
      type: string
      primary: true
    code:
      field: code
      type: string
  routes:
    exclude:
      - createManyBase
      - replaceOneBase
defaultQuery:

routes:
  - path: /api/formula-structures
    method: GET
    model: _
    isExtendedFilter: true
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'formula-structures'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
        ContainsFullLegalEntityScope: '::{containsFullLegalEntityScope}::'
        ElementGroupCode: '::{elementGroupCode}::'
        CountryCode: '::{countryCode}::'
        CompanyCode: '::{companyCode}::'
        LegalEntityCode: '::{legalEntityCode}::'
        CountryCodes: '::{countryCodes}::'
        CompanyCodes: '::{companyCodes}::'
        LegalEntityCodes: '::{legalEntityCodes}::'
        BusinessUnitCodes: '::{businessUnitCodes}::'
        DivisionCodes: '::{divisionCodes}::'
        DepartmentCodes: '::{departmentCodes}::'
        PayGroupCodes: '::{payGroupCodes}::'
      transform: '$merge([$, {"data":$map($.data, function($item) {
                    $merge(
                        [$item,
                            {
                                "countryNames": $map($filter($item.scopes, function($v) {$v.level = "country"})[], function($_item) {$_item.nameObject.longName})[],
                                "countryCodes": $map($filter($item.scopes, function($v) {$v.level = "country"})[], function($_item) {$_item.nameObject.code})[],
                                "companyNames": $map($filter($item.scopes, function($v) {$v.level = "company"})[], function($_item) {$_item.nameObject.longName})[],
                                "companyCodes": $map($filter($item.scopes, function($v) {$v.level = "company"})[], function($_item) {$_item.nameObject.code})[],
                                "legalEntityNames": $map($filter($item.scopes, function($v) {$v.level = "legal_entity"})[], function($_item) {$_item.nameObject.longName})[],
                                "legalEntityCodes": $map($filter($item.scopes, function($v) {$v.level = "legal_entity"})[], function($_item) {$_item.nameObject.code})[],
                                "businessUnitNames": $map($filter($item.scopes, function($v) {$v.level = "business_unit"})[], function($_item) {$_item.nameObject.longName})[],
                                "businessUnitCodes": $map($filter($item.scopes, function($v) {$v.level = "business_unit"})[], function($_item) {$_item.nameObject.code})[],
                                "divisionNames": $map($filter($item.scopes, function($v) {$v.level = "division"})[], function($_item) {$_item.nameObject.longName})[],
                                "divisionCodes": $map($filter($item.scopes, function($v) {$v.level = "division"})[], function($_item) {$_item.nameObject.code})[],
                                "departmentNames": $map($filter($item.scopes, function($v) {$v.level = "department"})[], function($_item) {$_item.nameObject.longName})[],
                                "departmentCodes": $map($filter($item.scopes, function($v) {$v.level = "department"})[], function($_item) {$_item.nameObject.code})[],
                                "payGroupNames": $map($filter($item.scopes, function($v) {$v.level = "paygroup"})[], function($_item) {$_item.nameObject.longName})[],
                                "payGroupCodes": $map($filter($item.scopes, function($v) {$v.level = "paygroup"})[], function($_item) {$_item.nameObject.code})[]
                            }
                        ]
                    )
                    } )[]}])'

  - path: /api/formula-structures/:id
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'formula-structures/:{id}:'
      transform: '($filterFunction := function($scope,$key) {$filter($scope, function($v) {$v.level = $key})[]};
                    $countries := $filterFunction(scopes,"country")[];
                    $companies := $filterFunction(scopes,"company")[];
                    $legalEntities := $filterFunction(scopes,"legal_entity")[];
                    $businessUnits := $filterFunction(scopes,"business_unit")[];
                    $divisions := $filterFunction(scopes,"division")[];
                    $departments := $filterFunction(scopes,"department")[];
                    $payGroups := $filterFunction(scopes,"paygroup")[];
                    $ ~> | $ | {
                    "countries" : $countries,
                    "companies" : $companies,
                    "legalEntities" : $legalEntities,
                    "businessUnits" : $businessUnits,
                    "divisions" : $divisions,
                    "departments" : $departments,
                    "payGroups" : $payGroups,
                    "countryCode":$count($countries) > 0 ? $map($countries, function($value, $index) {
                        {
                            "label" : $value.nameObject.longName & " (" & $value.value & ")",
                            "value" : $value.value
                        }
                    })[] : null ,
                    "companyObj":$count($companies) > 0 ? $map($companies, function($value, $index) {
                            {
                            "label": $value.nameObject.longName & " (" & $value.value & ")",
                            "value":{"id": $value.nameObject.id,
                            "code": $value.value
                            }}
                        })[] : null ,
                    "legalEntityObj":$count($legalEntities) > 0 ? $map($legalEntities, function($value, $index) {
                            {
                            "label": $value.nameObject.longName & " (" & $value.value & ")",
                            "value":{"id": $value.nameObject.id,
                            "code": $value.value
                            }}
                        })[] : null ,
                    "businessUnitObj":$count($businessUnits) > 0 ? $map($businessUnits, function($value, $index) {
                            {
                            "label": $value.nameObject.longName & " (" & $value.value & ")",
                            "value":{"id": $value.nameObject.id,
                            "code": $value.value
                            }}
                        })[] : null ,
                    "divisionObj":$count($divisions) > 0 ? $map($divisions, function($value, $index) {
                            {
                            "label": $value.nameObject.longName & " (" & $value.value & ")",
                            "value":{"id": $value.nameObject.id,
                            "code": $value.value
                            }}
                        })[] : null ,
                    "departmentObj":$count($departments) > 0 ? $map($departments, function($value, $index) {
                            {
                            "label": $value.nameObject.longName & " (" & $value.value & ")",
                            "value":{"id": $value.nameObject.id,
                            "code": $value.value
                            }}
                        })[] : null ,
                    "payGroupObj":$count($payGroups) > 0 ? $map($payGroups, function($value, $index) {
                            {
                            "label": $value.nameObject.longName & " (" & $value.value & ")",
                            "value":{"id": $value.nameObject.id,
                            "code": $value.value
                            }}
                        })[] : null ,

                    "attachmentResults": $.attachFileName and  $.attachFile ?
                    {"name": $.attachFileName , "url": "/api/pr-files/download",
                    "fileValue": $.attachFile,
                    "fileField": "AttachFile"  }
                    : null} |)'

  - path: /api/formula-structures
    method: POST
    model: _
    bodyTransform: '$merge([$, {
                                  "scopes" : [
                                      $count($.countryCode) > 0 and $boolean(countryCode) = true ? $map($.countryCode, function($itemCountry)
                                      {
                                          {
                                              "level": "country","value" : $itemCountry.value ? $itemCountry.value : $itemCountry
                                          }
                                      })[],
                                      $count($.companyObj) > 0 and $boolean(companyObj) = true ? $map($.companyObj, function($itemCompany)
                                      {
                                          {
                                              "level": "company", "value" : $itemCompany.value ? $itemCompany.value.code : $itemCompany.code
                                          }
                                      })[],
                                      $count($.legalEntityObj) > 0 and $boolean(legalEntityObj) = true ? $map($.legalEntityObj, function($itemLegalEntity)
                                      {
                                          {
                                              "level": "legal_entity", "value" : $itemLegalEntity.value ? $itemLegalEntity.value.code : $itemLegalEntity.code
                                          }
                                      })[],
                                      $count($.businessUnitObj) > 0 and $boolean(businessUnitObj) = true ? $map($.businessUnitObj, function($itemBusinessUnit)
                                      {
                                          {
                                              "level": "business_unit", "value" : $itemBusinessUnit.value ? $itemBusinessUnit.value.code : $itemBusinessUnit.code
                                          }
                                      })[],
                                      $count($.divisionObj) > 0 and $boolean(divisionObj) = true ? $map($.divisionObj, function($itemDivision)
                                      {
                                          {
                                              "level": "division", "value" : $itemDivision.value ? $itemDivision.value.code : $itemDivision.code
                                          }
                                      })[],
                                      $count($.departmentObj) > 0 and $boolean(departmentObj) = true ? $map($.departmentObj, function($itemDepartment)
                                      {
                                          {
                                              "level": "department", "value" : $itemDepartment.value ? $itemDepartment.value.code : $itemDepartment.code
                                          }
                                      })[],
                                      $count($.payGroupObj) > 0 and $boolean(payGroupObj) = true ? $map($.payGroupObj, function($itemPayGroup)
                                      {
                                          {
                                              "level": "paygroup", "value" : $itemPayGroup.value ? $itemPayGroup.value.code : $itemPayGroup.code
                                          }
                                      })[]
                                  ]
                              }])'
    query:
    transform: '$'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'formula-structures'
      transform: '$'

  - path: /api/formula-structures/:id
    model: _
    method: PATCH
    query:
    bodyTransform: '$merge([$, {
                                  "scopes" : [
                                      $count($.countryCode) > 0 and $boolean(countryCode) = true ? $map($.countryCode, function($itemCountry)
                                      {
                                          {
                                              "level": "country","value" : $itemCountry.value ? $itemCountry.value : $itemCountry
                                          }
                                      })[],
                                      $count($.companyObj) > 0 and $boolean(companyObj) = true ? $map($.companyObj, function($itemCompany)
                                      {
                                          {
                                              "level": "company", "value" : $itemCompany.value ? $itemCompany.value.code : $itemCompany.code
                                          }
                                      })[],
                                      $count($.legalEntityObj) > 0 and $boolean(legalEntityObj) = true ? $map($.legalEntityObj, function($itemLegalEntity)
                                      {
                                          {
                                              "level": "legal_entity", "value" : $itemLegalEntity.value ? $itemLegalEntity.value.code : $itemLegalEntity.code
                                          }
                                      })[],
                                      $count($.businessUnitObj) > 0 and $boolean(businessUnitObj) = true ? $map($.businessUnitObj, function($itemBusinessUnit)
                                      {
                                          {
                                              "level": "business_unit", "value" : $itemBusinessUnit.value ? $itemBusinessUnit.value.code : $itemBusinessUnit.code
                                          }
                                      })[],
                                      $count($.divisionObj) > 0 and $boolean(divisionObj) = true ? $map($.divisionObj, function($itemDivision)
                                      {
                                          {
                                              "level": "division", "value" : $itemDivision.value ? $itemDivision.value.code : $itemDivision.code
                                          }
                                      })[],
                                      $count($.departmentObj) > 0 and $boolean(departmentObj) = true ? $map($.departmentObj, function($itemDepartment)
                                      {
                                          {
                                              "level": "department", "value" : $itemDepartment.value ? $itemDepartment.value.code : $itemDepartment.code
                                          }
                                      })[],
                                      $count($.payGroupObj) > 0 and $boolean(payGroupObj) = true ? $map($.payGroupObj, function($itemPayGroup)
                                      {
                                          {
                                              "level": "paygroup", "value" : $itemPayGroup.value ? $itemPayGroup.value.code : $itemPayGroup.code
                                          }
                                      })[]
                                  ]
                              }])'
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: 'formula-structures/:{id}:'

  - path: /api/formula-structures/:id
    method: DELETE
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'formula-structures/:{id}:'
customRoutes:
  - path: /api/formula-structures/:id/histories
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'formula-structures/:{id}:/history'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
      transform: '($map($, function($item) {
                      $merge([$item ,
                          (
                          $filterFunction := function($scope,$key) {$filter($scope, function($v) {$v.level = $key})[]};
                                      $countries := $filterFunction($item.scopes,"country")[];
                                      $companies := $filterFunction($item.scopes,"company")[];
                                      $legalEntities := $filterFunction($item.scopes,"legal_entity")[];
                                      $businessUnits := $filterFunction($item.scopes,"business_unit")[];
                                      $divisions := $filterFunction($item.scopes,"division")[];
                                      $departments := $filterFunction($item.scopes,"department")[];
                                      $payGroups := $filterFunction($item.scopes,"paygroup")[];
                                      {
                                      "countries" : $countries,
                                      "companies" : $companies,
                                      "legalEntities" : $legalEntities,
                                      "businessUnits" : $businessUnits,
                                      "divisions" : $divisions,
                                      "departments" : $departments,
                                      "payGroups" : $payGroups,
                                      "countryCode":$count($countries) > 0 ? $map($countries, function($value, $index) {
                                              {
                                                  "label" : $value.nameObject.longName & " (" & $value.value & ")",
                                                  "value" : $value.value
                                              }
                                          })[] : null ,
                                      "companyObj":$count($companies) > 0 ? $map($companies, function($value, $index) {
                                              {
                                              "label": $value.nameObject.longName & " (" & $value.value & ")",
                                              "value":{"id": $value.nameObject.id,
                                              "code": $value.value
                                              }}
                                          })[] : null ,
                                      "legalEntityObj":$count($legalEntities) > 0 ? $map($legalEntities, function($value, $index) {
                                              {
                                              "label": $value.nameObject.longName & " (" & $value.value & ")",
                                              "value":{"id": $value.nameObject.id,
                                              "code": $value.value
                                              }}
                                          })[] : null ,
                                      "businessUnitObj":$count($businessUnits) > 0 ? $map($businessUnits, function($value, $index) {
                                              {
                                              "label": $value.nameObject.longName & " (" & $value.value & ")",
                                              "value":{"id": $value.nameObject.id,
                                              "code": $value.value
                                              }}
                                          })[] : null ,
                                      "divisionObj":$count($divisions) > 0 ? $map($divisions, function($value, $index) {
                                              {
                                              "label": $value.nameObject.longName & " (" & $value.value & ")",
                                              "value":{"id": $value.nameObject.id,
                                              "code": $value.value
                                              }}
                                          })[] : null ,
                                      "departmentObj":$count($departments) > 0 ? $map($departments, function($value, $index) {
                                              {
                                              "label": $value.nameObject.longName & " (" & $value.value & ")",
                                              "value":{"id": $value.nameObject.id,
                                              "code": $value.value
                                              }}
                                          })[] : null ,
                                      "payGroupObj":$count($payGroups) > 0 ? $map($payGroups, function($value, $index) {
                                              {
                                              "label": $value.nameObject.longName & " (" & $value.value & ")",
                                              "value":{"id": $value.nameObject.id,
                                              "code": $value.value
                                              }}
                                          })[] : null}
                      )
                      ])
                  })[])'

  - path: /api/formula-structures/:id/clone
    method: POST
    model: _
    query:
    bodyTransform: '$merge([$, {
                                  "scopes" : [
                                      $count($.countryCode) > 0 and $boolean(countryCode) = true ? $map($.countryCode, function($itemCountry)
                                      {
                                          {
                                              "level": "country","value" : $itemCountry.value ? $itemCountry.value : $itemCountry
                                          }
                                      })[],
                                      $count($.companyObj) > 0 and $boolean(companyObj) = true ? $map($.companyObj, function($itemCompany)
                                      {
                                          {
                                              "level": "company", "value" : $itemCompany.value ? $itemCompany.value.code : $itemCompany.code
                                          }
                                      })[],
                                      $count($.legalEntityObj) > 0 and $boolean(legalEntityObj) = true ? $map($.legalEntityObj, function($itemLegalEntity)
                                      {
                                          {
                                              "level": "legal_entity", "value" : $itemLegalEntity.value ? $itemLegalEntity.value.code : $itemLegalEntity.code
                                          }
                                      })[],
                                      $count($.businessUnitObj) > 0 and $boolean(businessUnitObj) = true ? $map($.businessUnitObj, function($itemBusinessUnit)
                                      {
                                          {
                                              "level": "business_unit", "value" : $itemBusinessUnit.value ? $itemBusinessUnit.value.code : $itemBusinessUnit.code
                                          }
                                      })[],
                                      $count($.divisionObj) > 0 and $boolean(divisionObj) = true ? $map($.divisionObj, function($itemDivision)
                                      {
                                          {
                                              "level": "division", "value" : $itemDivision.value ? $itemDivision.value.code : $itemDivision.code
                                          }
                                      })[],
                                      $count($.departmentObj) > 0 and $boolean(departmentObj) = true ? $map($.departmentObj, function($itemDepartment)
                                      {
                                          {
                                              "level": "department", "value" : $itemDepartment.value ? $itemDepartment.value.code : $itemDepartment.code
                                          }
                                      })[],
                                      $count($.payGroupObj) > 0 and $boolean(payGroupObj) = true ? $map($.payGroupObj, function($itemPayGroup)
                                      {
                                          {
                                              "level": "paygroup", "value" : $itemPayGroup.value ? $itemPayGroup.value.code : $itemPayGroup.code
                                          }
                                      })[]
                                  ]
                              }])'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'formula-structures/:{id}:/clone'
      transform: $
  - path: /api/formula-structures/export
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: binary
      path: 'formula-structures/export'
      query:
        PageSize: '1000'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
        ContainsFullLegalEntityScope: '::{containsFullLegalEntityScope}::'
        ElementGroupCode: '::{elementGroupCode}::'
        CountryCode: '::{countryCode}::'
        CompanyCode: '::{companyCode}::'
        LegalEntityCode: '::{legalEntityCode}::'
        CountryCodes: '::{countryCodes}::'
        CompanyCodes: '::{companyCodes}::'
        LegalEntityCodes: '::{legalEntityCodes}::'
        BusinessUnitCodes: '::{businessUnitCodes}::'
        DivisionCodes: '::{divisionCodes}::'
        DepartmentCodes: '::{departmentCodes}::'
        PayGroupCodes: '::{payGroupCodes}::'
      transform: '$'
  - path: /api/formula-structures/multidelete
    method: DELETE
    model: multiDeleteModel
    bodyTransform: '$.ids[]'
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'formula-structures'
