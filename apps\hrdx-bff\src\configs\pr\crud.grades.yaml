controller: grades
upstream: ${{UPSTREAM_PR_URL}}

models:
  - name: _
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: indexFrom
    config:
      id:
        from: id
        type: string
      code:
        from: code
        type: string
      readOnly:
        from: readOnly
        type: boolean
      shortName:
        from: shortName
        type: string
        typeOptions:
          func: stringToMultiLang
      name:
        from: name
        type: string
        typeOptions:
          func: stringToMultiLang
      companyName:
        from: companyScopes.longName
      companyCode:
        from: companyScopes.companyCode
      CompanyCodes:
        from: CompanyCodes
      companyScopes:
        from: companyScopes
      companyObj:
        from: companyObj
      effectiveDate:
        from: effectiveDateFrom
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      effectiveDateTo:
        from: effectiveDateTo
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      status:
        from: enabled
        type: string
        typeOptions:
          func: YNToBoolean
      note:
        from: note
        type: string
        typeOptions:
          func: stringToMultiLang
      effectiveDateFrom:
        from: effectiveDateFrom
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      createdBy:
        from: createdBy
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
defaultQuery:
auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: grades
crudConfig:
  query:
    sort:
      - field: updatedAt
        order: DESC
  params:
    id:
      field: id
      type: string
      primary: true
    code:
      field: code
      type: string
  routes:
    exclude:
      - createManyBase
      - replaceOneBase

routes:
  - path: /api/grades
    method: GET
    model: _

    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'grades'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        CompanyCodes: ':{CompanyCodes}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$merge([$,
            {
            "data":$map($.data, function($item) {
              $merge([$item,
                {
                  "companyName":
                    $map($item.companyScopes, function($v) {$v.company.longName})
                }])
              })[]
            }
          ])'

  - path: /api/grades/:id
    method: GET
    model: _

    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'grades/:{id}:'
      transform: '$ ~> | $ |
        {
          "companyObj": $map(companyScopes, function($value, $index) {
            {
              "label": $value.company.longName & " (" & $value.company.code & ")",
              "value":{"id": $value.company.id,
              "code": $value.companyCode
            }}
          })[]
        } |
        '

  - path: /api/grades
    method: POST
    model: _
    query:
    bodyTransform: '$merge([$,{"companyScopes": $map($.companyObj, function($value) { $exists($value.code) ? {"companyCode": $value.code }}) []}])'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'grades'
      transform: '$'

  - path: /api/grades/:id
    model: _
    method: PATCH
    query:
    bodyTransform: '$merge([$,{"companyScopes": $map($.companyObj, function($value) {  {"companyCode": $exists($value.value) ? $value.value.code : $value.code }}) []}])'
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: 'grades/:{id}:'

  - path: /api/grades/:id
    method: DELETE
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'grades/:{id}:'
customRoutes:
  - path: /api/grades/:id/histories
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'grades/:{id}:/history'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
      transform: '$ ~> | $ |
        {
          "companyObj": $map(companyScopes, function($value, $index) {
            {
              "label": $value.company.longName & " (" & $value.company.code & ")",
              "value":{"id": $value.company.id,
              "code": $value.companyCode
            }}
          })[]
        } |
        '

  - path: /api/grades/:id/clone
    method: POST
    model: _
    query:
    bodyTransform: '$merge([$,{"companyScopes": $map($.companyObj, function($value) {  {"companyCode": $exists($value.value) ? $value.value.code : $value.code }}) []}])'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'grades/:{id}:/clone'
      transform: $

  - path: /api/grades/export
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: binary
      path: 'grades/export'
      query:
        PageSize: '1000'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        CompanyCodes: ':{CompanyCodes}:'
        Filter: '::{filter}::'
      transform: '$'

  - path: /api/grades/multidelete
    method: DELETE
    model: multiDeleteModel
    bodyTransform: '$.ids[]'
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'grades'
