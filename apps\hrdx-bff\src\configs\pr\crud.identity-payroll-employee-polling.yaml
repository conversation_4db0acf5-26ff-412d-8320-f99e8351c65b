controller: identity-payroll-employee-polling
upstream: ${{UPSTREAM_PR_URL}}
upstreamPath: identity-payroll-employee-polling

__auth: auth.default__auth
__crudConfig: crud-config.default__crudConfig
crudConfig:
  # query:
  #   sort:
  #     - field: code
  #       order: DESC
  params:
    # id:
    #   field: id
    #   type: string

models:
  - name: _
    __pagination: pagination.default__pagination
    config:
      payrollPeriodCode:
        from: payrollPeriodCode
        type: string
      payrollPeriodSettingCode:
        from: payrollPeriodSettingCode
        type: string
      pollId:
        from: pollId
      id:
        from: id
        type: string
      progressInPercent:
        from: progressInPercent
        type: number
      payrollPeriodCode:
        from: payrollPeriodCode
        type: string
      payrollPeriodName:
        from: payrollPeriodName
        type: string
      payrollPeriodSettingCode:
        from: payrollPeriodSettingCode
        type: string
      payrollPeriodSettingName:
        from: payrollPeriodSettingName
        type: string

      totalPaidEmployees:
        from: totalPaidEmployees
        type: number
      totalAddNew:
        from: totalAddNew
        type: number
      totalUpdate:
        from: totalUpdate
        type: number
      totalRemoved:
        from: totalRemoved
        type: number
      status:
        from: status
        type: string
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime



__routes: routes.default__routes
routes:
customRoutes:
  - path: /api/identity-payroll-employee-polling/register
    method: POST
    model: _
    options: 
      omitLanguages: true
    query:
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'identify-payroll-employee-proxy'
      query:
      transform: '$'

  - path: /api/identity-payroll-employee-polling/detail
    method: GET
    model: _

    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      query:
        pollId: ':{pollId}:'
      path: 'identity-payroll-employee-polling/get-by-poll-id'
      query:
      transform: '$'


