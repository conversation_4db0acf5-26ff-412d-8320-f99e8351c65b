controller: income-package-employees
upstream: ${{UPSTREAM_PR_URL}}

models:
  - name: _
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: indexFrom
    config:
      id:
        from: id
        type: string
      employeeIdErn:
        from: employeeIdErn
        type: string
      code:
        from: code
        type: string
      min:
        from: min
      max:
        from: max
      mid:
        from: mid
      employeeId:
        from: employeeId
      employeeRecordNumber:
        from: employeeRecordNumber
      fullName:
        from: fullName
      employeeGroupCode:
        from: employeeGroupCode
      employeeGroup:
        from: employeeGroup
      employeeGroupName:
        from: employeeGroup.longName
      incomePackageCode:
        from: incomePackageCode
      incomePackageId:
        from: incomePackage.id
      incomePackageName: 
        from: incomePackage.longName
      incomePackage: 
        from: incomePackage
      incomePackageObj:
        from: $
        objectChildren:
          code:
            from: incomePackageCode
          incomePackageSystemId:
            from: incomePackageSystemId
      incomePackageSystemCode:
        from: incomePackageSystem.code
      incomePackageSystemId:
        from: incomePackageSystemId
      incomePackageSystemName: 
        from: incomePackageSystem.longName
      incomePackageSystem: 
        from: incomePackageSystem
      incomePackageSystemObj:
        from: $
        objectChildren:
          id:
            from: incomePackageSystemId
      
      detailMin:
        from: incomePackageSystem.min
      detailMid:
        from: incomePackageSystem.mid
      detailMax:
        from: incomePackageSystem.max
      detailCurrencyName:
        from: currency.longName
      jobTitle:
        from: jobTitle
      jobTitleCode:
        from: jobTitleCode
      jobTitleName:
        from: jobTitle.longName
      jobCodeName: 
        from: jobCode.longName
      company:
        from: company
      companyCode:
        from: companyCode
      companyName:
        from: company.longName  
      legalEntity:
        from: legalEntity
      legalEntityCode:
        from: legalEntityCode
      legalEntityName:
        from: legalEntity.longName
      currency:
        from: currency
      currencyCode:
        from: currencyCode
      currencyName:
        from: currency.longName
      division:
        from: division
      divisionCode:
        from: divisionCode
      divisionName:
        from: division.longName
      department:
        from: department
      departmentCode:
        from: departmentCode
      departmentName:
        from: department.longName
      location:
        from: location
      locationCode:
        from: locationCode
      locationName:
        from: location.longName
      effectiveDate:
        from: effectiveDateFrom
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      status:
        from: enabled
        type: string
        typeOptions:
          func: YNToBoolean
      note:
        from: note
        type: string
        typeOptions:
          func: stringToMultiLang
      effectiveDateFrom:
        from: effectiveDateFrom
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      createdBy:
        from: createdBy
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      employeeId:
        from: employeeId
      ern:
        from: ern
  - name: packageList
    config:
      incomePackageCode:
        from: incomePackageCode
      incomePackageName:
        from: incomePackageName
      incomePackageSystemId:
        from: incomePackageSystemId
      effectiveDateFrom:
        from: effectiveDateFrom
        type: timestamp
        typeOptions:
          func: timestampToDateTime
defaultQuery:
auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: income-package-employees
crudConfig:
  query:
    sort:
      - field: updatedAt
        order: DESC
  params:
    id:
      field: id
      type: string
      primary: true
    code:
      field: code
      type: string
  routes:
    exclude:
      - createManyBase
      - replaceOneBase

routes:
  - path: /api/income-package-employees
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'income-package-employees'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        LegalEntities: ':{LegalEntities}:'
        Locations: ':{Locations}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$merge([$, {"data": $map($.data , function ($item) { $merge([$item , {"jobCodeName" : $item.jobTitleName}]) } )[] }])'

  - path: /api/income-package-employees/:id
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'income-package-employees/:{id}:'
      transform: '$ ~> | $ |
        {
        "employee": {"label": $join($filter([$.employeeId,$.employeeGroupCode,$string($.employeeRecordNumber), $.fullName], $boolean), " - "), "value": {"employeeId": $.employeeId,"code": $.employeeId, "employeeRecordNumber": $.employeeRecordNumber, "jobDataId": $.jobDataId}},
        "incomePackageObj": incomePackageCode ?
        {
        "label": incomePackageName & " (" & incomePackageCode & ")",
        "value": {"code": incomePackageCode}
        } : null
        []
        } |
        '

  - path: /api/income-package-employees
    method: POST
    model: _
    query:
    bodyTransform: '$merge([$,
      {
      "legalEntities": $map($.legalEntities, function($value) {
      $exists($value.code) ? {"legalEntityCode": $value.code }
      })[],
      "locations": $map($.locations, function($value) {
      $exists($value.code) ? {"locationCode": $value.code }
      })[]
      }
      ])'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'income-package-employees'
      transform: '$'

  - path: /api/income-package-employees/:id
    model: _
    method: PATCH
    query:
    bodyTransform: '$merge([$,{
      "legalEntities": $map($.legalEntities, function($value) {
      {
      "legalEntityCode": $exists($value.value) ? $value.value.code : $value.code
      }
      })[],
      "locations": $map($.locations, function($value) {
      {
      "locationCode": $exists($value.value) ? $value.value.code : $value.code
      }
      })[]
      }
      ])'
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: 'income-package-employees/:{id}:'

  - path: /api/income-package-employees/:id
    method: DELETE
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'income-package-employees/:{id}:'
customRoutes:
  - path: /api/income-package-employees/:id/histories
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'income-package-employees/:{id}:/history'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
      transform: '$ ~> | $ |
        {
        "employee": {"label": $join($filter([$.employeeId,$.employeeGroupCode,$string($.employeeRecordNumber), $.fullName], $boolean), " - "), "value": {"employeeId": $.employeeId,"code": $.employeeId, "employeeRecordNumber": $.employeeRecordNumber, "jobDataId": $.jobDataId}},
        "incomePackageObj": incomePackageCode ?
        {
        "label": incomePackageName & " (" & incomePackageCode & ")",
        "value": {"code": incomePackageCode}
        } : null,
        "companyObj": companyCode ?
        {
        "label": companyName & " (" & companyCode & ")",
        "value": {"code": companyCode}
        } : null,
        "currencyObj":currencyCode ?
        {
        "label": currencyName & " (" & currencyCode &")",
        "value": {"code": currencyCode }
        },
        "legalEntities": $map(legalEntities, function($value, $index) {
        {
        "label": $value.legalEntity.longName & " (" & $value.legalEntity.code & ")",
        "value":{"id": $value.legalEntity.id,
        "code": $value.legalEntityCode
        }}
        })[],
        "locations": $map(locations, function($value, $index) {
        {
        "label": $value.location.longName & " (" & $value.location.code & ")",
        "value":{"id": $value.location.id,
        "code": $value.locationCode
        }}
        })[]
        } |
        '

  - path: /api/income-package-employees/:id/clone
    method: POST
    model: _
    query:
    bodyTransform: '$'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'income-package-employees/:{id}:/clone'
      transform: $

  - path: /api/income-package-employees/income-package
    method: GET
    model: packageList
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'income-package-employees:income-package'
      query:
        employeeId: ':{employeeId}:'
        ern: ':{ern}:'
        effectiveDateFrom: ':{effectiveDateFrom}:'
      transform: '$'

  - path: /api/income-package-employees/export
    method: GET
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: binary
      path: 'income-package-employees:export'
      query:
        PageSize: '1000'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        LegalEntities: ':{LegalEntities}:'
        Locations: ':{Locations}:'
        Filter: '::{filter}::'
      transform: '$'

  - path: /api/income-package-employees/multidelete
    method: DELETE
    model: multiDeleteModel
    bodyTransform: '$.ids[]'
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'income-package-employees'
