controller: income-package-jobs
upstream: ${{UPSTREAM_PR_URL}}

models:
  - name: _
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: indexFrom
    config:
      id:
        from: id
        type: string
      code:
        from: code
        type: string
      job:
        from: jobCodeName
      jobCode:
        from: jobCode
      jobName:
        from: jobCodeName.longName
      jobId:
        from: jobCodeName.id
      jobObj:
        from: $
        objectChildren:
          code:
            from: jobCode
      company:
        from: company
      companyCode:
        from: companyCode
      companyName:
        from: company.longName
      companyId:
        from: company.id
      companyObj:
        from: $
        objectChildren:
          id:
            from: company.id
          code:
            from: companyCode
      effectiveDate:
        from: effectiveDateFrom
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      effectiveDateTo:
        from: effectiveDateTo
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      status:
        from: enabled
        type: string
        typeOptions:
          func: YNToBoolean
      note:
        from: note
        type: string
        typeOptions:
          func: stringToMultiLang
      effectiveDateFrom:
        from: effectiveDateFrom
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      incomePackageNames:
        from: incomePackageJobDetails.longName
      incomePackageJobDetails:
        from: incomePackageJobDetails
        arrayChildren:
          priority:
            from: priority
          incomePackageCode:
            from: incomePackageCode
          incomePackage:
            from: incomePackage
          incomePackageName:
            from: incomePackage.longName
          incomePackageObj:
            from: $
            objectChildren:
              code:
                from: incomePackageCode
          note:
            from: note
      incomePackageCodes:
        from: IncomePackageCodes
      createdBy:
        from: createdBy
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
defaultQuery:
auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: income-package-jobs
crudConfig:
  query:
    sort:
      - field: updatedAt
        order: DESC
  params:
    id:
      field: id
      type: string
      primary: true
    code:
      field: code
      type: string
  routes:
    exclude:
      - createManyBase
      - replaceOneBase

routes:
  - path: /api/income-package-jobs
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'income-package-jobs'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        IncomePackageCodes: ':{incomePackageCodes}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$merge([$, {"data":$map($.data, function($item) {$merge([$item,{"incomePackageNames": $map($item.incomePackageJobDetails, function($v) {$v.incomePackageName})}])} )[]}])'

  - path: /api/income-package-jobs/:id
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'income-package-jobs/:{id}:'
      transform: '$ ~> | $ |
        {
        "companyObj": companyCode ?
        {
        "label": companyName & " (" & companyCode & ")",
        "value": {"code": companyCode, "id": companyId}
        } : null,
        "dependantPackageField": companyCode & effectiveDate,
        "dependantCompanyPackageField": companyCode,
        "dependantEffectiveDatePackageField": effectiveDate,
        "jobObj": jobCode ?
        {
        "label": jobName & " (" & jobCode & ")",
        "value": {"code": jobCode}
        } : null,
        "incomePackageJobDetails": $count(incomePackageJobDetails) > 0 ? $map(incomePackageJobDetails, function($item) {
            $merge([$item, {
                "incomePackageObj": $item.incomePackageCode ? {
                "label" : $item.incomePackageName & " (" & $item.incomePackageCode & ")",
                "value" : {
                    "code": $item.incomePackageCode
                }
                } : null
            }])
        })[] : []
        } |
        '

  - path: /api/income-package-jobs
    method: POST
    model: _
    query:
    bodyTransform: '$'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'income-package-jobs'
      transform: '$'

  - path: /api/income-package-jobs/:id
    model: _
    method: PATCH
    query:
    bodyTransform: '$'
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: 'income-package-jobs/:{id}:'

  - path: /api/income-package-jobs/:id
    method: DELETE
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'income-package-jobs/:{id}:'
customRoutes:
  - path: /api/income-package-jobs/:id/histories
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'income-package-jobs/:{id}:/history'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
      transform: '$ ~> | $ |
        {
        "companyObj": companyCode ?
        {
        "label": companyName & " (" & companyCode & ")",
        "value": {"code": companyCode, "id": companyId}
        } : null,
        "jobObj": jobCode ?
        {
        "label": jobName & " (" & jobCode & ")",
        "value": {"code": jobCode}
        } : null,
        "incomePackageJobDetails": $count(incomePackageJobDetails) > 0 ? $map(incomePackageJobDetails, function($item) {
            $merge([$item, {
                "incomePackageObj": $item.incomePackageCode ? {
                "label" : $item.incomePackageName & " (" & $item.incomePackageCode & ")",
                "value" : {
                    "code": $item.incomePackageCode
                }
                } : null
            }])
        })[] : []
        } |
        '

  - path: /api/income-package-jobs/:id/clone
    method: POST
    model: _
    query:
    bodyTransform: '$'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'income-package-jobs/:{id}:/clone'
      transform: $

  - path: /api/income-package-jobs/export
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: binary
      path: 'income-package-jobs/export'
      query:
        PageSize: '1000'
        OrderBy: ':{options.sort}:'
        IncomePackageCodes: ':{incomePackageCodes}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$'

  - path: /api/income-package-jobs/multidelete
    method: DELETE
    model: multiDeleteModel
    bodyTransform: '$.ids[]'
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'income-package-jobs'
