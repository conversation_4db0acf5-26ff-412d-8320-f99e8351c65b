controller: income-package-systems
upstream: ${{UPSTREAM_PR_URL}}

models:
  - name: _
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: indexFrom
    config:
      id:
        from: id
        type: string
      code:
        from: code
        type: string
      min:
        from: min
      max:
        from: max
      mid:
        from: mid
      companyCodes:
        from: companyCodes
      incomePackageName:
        from: incomePackage.longName
      incomePackageCode:
        from: incomePackageCode
      incomePackageId:
        from: incomePackage.id
      incomePackageObj:
        from: $
        objectChildren:
          code:
            from: incomePackageCode
      company:
        from: company
      companyCode:
        from: companyCode
      companyName:
        from: company.longName
      companyId:
        from: company.id
      companyObj:
        from: $
        objectChildren:
          code:
            from: companyCode
          id:
            from: company.id
      legalEntities:
        from: legalEntities
      legalEntityNames:
        from: legalEntities.longName
      legalEntityListCodes:
        from: legalEntities.legalEntityCode
      locationListCodes:
        from: locations.locationCode
      legalEntityCodes:
        from: legalEntityCodes
      filterLegalEntityCodes:
        from: LegalEntityCodes
      locations:
        from: locations
      locationNames:
        from: locations.longName
      locationCodes:
        from: locationCodes
      filterLocationCodes:
        from: LocationCodes
      currencyCode:
        from: currencyCode
        type: string
      currencyId:
        from: currency.id
      currencyName:
        from: currency.longName
        type: string
      currencyObj:
        from: $
        objectChildren:
          code:
            from: currencyCode
      effectiveDate:
        from: effectiveDateFrom
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      status:
        from: enabled
        type: string
        typeOptions:
          func: YNToBoolean
      note:
        from: note
        type: string
        typeOptions:
          func: stringToMultiLang
      noteDefault:
        from: note
      effectiveDateFrom:
        from: effectiveDateFrom
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      createdBy:
        from: createdBy
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
defaultQuery:
auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: income-package-systems
crudConfig:
  query:
    sort:
      - field: updatedAt
        order: DESC
  params:
    id:
      field: id
      type: string
      primary: true
    code:
      field: code
      type: string
  routes:
    exclude:
      - createManyBase
      - replaceOneBase

routes:
  - path: /api/income-package-systems
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'income-package-systems'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        LegalEntities: ':{LegalEntities}:'
        LegalEntityCodes: ':{filterLegalEntityCodes}:'
        LocationCodes: ':{filterLocationCodes}:'
        Locations: ':{Locations}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$merge([$,
        {
          "data":$map($.data, function($item) {
          $merge([$item,
            {
              "legalEntityNames": $map($item.legalEntities, function($v) {$v.legalEntity.longName}),
              "locationNames": $map($item.locations, function($v) {$v.location.longName})
            }
          ])
          })[]
        }
        ])'

  - path: /api/income-package-systems/:id
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'income-package-systems/:{id}:'
      transform: '$ ~> | $ |
        {
        "incomePackageObj": incomePackageCode ?
        {
        "label": incomePackageName & " (" & incomePackageCode & ")",
        "value": {"code": incomePackageCode, "companyCodes": companyCodes}
        } : null,
        "companyObj": companyCode ?
        {
        "label": companyName & " (" & companyCode & ")",
        "value": {"code": companyCode, "id": companyId}
        } : null,
        "currencyObj":currencyCode ?
        {
        "label": currencyName & " (" & currencyCode &")",
        "value": {"code": currencyCode }
        },
        "legalEntities": $map(legalEntities, function($value, $index) {
        {
        "label": $value.legalEntity.longName & " (" & $value.legalEntity.code & ")",
        "value":{"id": $value.legalEntity.id,
        "code": $value.legalEntityCode
        }}
        })[],
        "locations": $map(locations, function($value, $index) {
        {
        "label": $value.location.longName & " (" & $value.location.code & ")",
        "value":{"id": $value.location.id,
        "code": $value.locationCode
        }}
        })[]
        } |
        '

  - path: /api/income-package-systems
    method: POST
    model: _
    query:
    bodyTransform: '$merge([$,
      {
      "legalEntities": $map($.legalEntities, function($value) {
      $exists($value.code) ? {"legalEntityCode": $value.code }
      })[],
      "locations": $map($.locations, function($value) {
      $exists($value.code) ? {"locationCode": $value.code }
      })[]
      }
      ])'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'income-package-systems'
      transform: '$'

  - path: /api/income-package-systems/:id
    model: _
    method: PATCH
    query:
    bodyTransform: '$merge([$,{
      "legalEntities": $map($.legalEntities, function($value) {
      {
      "legalEntityCode": $exists($value.value) ? $value.value.code : $value.code
      }
      })[],
      "locations": $map($.locations, function($value) {
      {
      "locationCode": $exists($value.value) ? $value.value.code : $value.code
      }
      })[]
      }
      ])'
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: 'income-package-systems/:{id}:'

  - path: /api/income-package-systems/:id
    method: DELETE
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'income-package-systems/:{id}:'
customRoutes:
  - path: /api/income-package-systems/:id/histories
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'income-package-systems/:{id}:/history'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
      transform: '$ ~> | $ |
        {
        "incomePackageObj": incomePackageCode ?
        {
        "label": incomePackageName & " (" & incomePackageCode & ")",
        "value": {"code": incomePackageCode}
        } : null,
        "companyObj": companyCode ?
        {
        "label": companyName & " (" & companyCode & ")",
        "value": {"code": companyCode, "id": companyId}
        } : null,
        "currencyObj":currencyCode ?
        {
        "label": currencyName & " (" & currencyCode &")",
        "value": {"code": currencyCode }
        },
        "legalEntities": $map(legalEntities, function($value, $index) {
        {
        "label": $value.legalEntity.longName & " (" & $value.legalEntity.code & ")",
        "value":{"id": $value.legalEntity.id,
        "code": $value.legalEntityCode
        }}
        })[],
        "locations": $map(locations, function($value, $index) {
        {
        "label": $value.location.longName & " (" & $value.location.code & ")",
        "value":{"id": $value.location.id,
        "code": $value.locationCode
        }}
        })[]
        } |
        '

  - path: /api/income-package-systems/:id/clone
    method: POST
    model: _
    query:
    bodyTransform: '$merge([$,{
      "legalEntities": $map($.legalEntities, function($value) {
      {
      "legalEntityCode": $exists($value.value) ? $value.value.code : $value.code
      }
      })[],
      "locations": $map($.locations, function($value) {
      {
      "locationCode": $exists($value.value) ? $value.value.code : $value.code
      }
      })[]
      }
      ])'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'income-package-systems/:{id}:/clone'
      transform: $

  - path: /api/income-package-systems/export
    method: GET
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: binary
      path: 'income-package-systems/export'
      query:
        PageSize: '1000'
        OrderBy: ':{options.sort}:'
        LegalEntities: ':{LegalEntities}:'
        LegalEntityCodes: ':{filterLegalEntityCodes}:'
        LocationCodes: ':{filterLocationCodes}:'
        Locations: ':{Locations}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$'

  - path: /api/income-package-systems/multidelete
    method: DELETE
    model: multiDeleteModel
    bodyTransform: '$.ids[]'
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'income-package-systems'
