controller: manage-external-payroll-imports
upstream: ${{UPSTREAM_PR_URL}}
upstreamPath: manage-external-payroll-imports

models:
  - name: _
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: totalPages
    config:
      id:
        from: id
        type: string
      reportType:
        from: reportType
      reportTypeCode:
        from: reportType.code
      reportTypeId:
        from: reportType.id
      reportTypeReportSourceId:
        from: reportType.reportSourceId
      reportTypeName:
        from: reportType.name
      reportTypeLongName:
        from: reportType.longName
      reportTypeElementGroupCode:
        from: reportType.elementGroupCode
      reportTypeElementGroupName:
        from: reportType.elementGroup.longName
      reportTypeElementGroup:
        from: reportType.elementGroup
      reportTypeCountryCode:
        from: reportType.countryCode
      reportTypeCountryName:
        from: reportType.country.longName
      reportTypeCountry:
        from: reportType.country
      reportTypeShortName:
        from: reportType.shortName
      reportTypeElementTypeCode:
        from: reportType.elementTypeCode
      reportTypeElementTypeName:
        from: reportType.elementType.longName
      reportTypeElementType:
        from: reportType.elementType
      reportTypeEffectiveDateFrom:
        from: reportType.effectiveDateFrom
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      reportTypeEffectiveDateTo:
        from: reportType.effectiveDateTo
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      reportTypeNote:
        from: reportType.note
      reportTypeEnable:
        from: reportType.enabled
        type: string
      reportTypeStatus:
        from: reportType.enabled
        type: string
        typeOptions:
          func: YNToBoolean
      reportColumns:
        from: reportType.reportColumns
        type: array
        arrayChildren:
          id:
            from: id
          nameInTemplate:
            from: nameInTemplate
          reportColumnCode:
            from: reportColumnCode
          reportColumnCalCode:
            from: reportColumnCalCode
          code:
            from: code
          codeMapping:
            from: codeMapping
          expressions:
            from: expressions
          countryCode:
            from: countryCode
          elementCode:
            from: elementCode
          name:
            from: name
          operatorCatalogId:
            from: operatorCatalogId
          operatorCode:
            from: operatorCode
          priority:
            from: priority
          encryption:
            from: encryption
            type: string
          effectiveDateFrom:
            from: effectiveDateFrom
            type: timestamp
            typeOptions:
              func: timestampToDateTime
          note:
            from: note
          status:
            from: enabled
            type: string
          enabled:
            from: enabled
            type: string
          filterYN:
            from: filterYN
            type: string
          isGeneralElement:
            from: isGeneralElement
            type: string
          listShowYN:
            from: listShowYN
            type: string
          inputShowYN:
            from: inputShowYN
            type: string
          InputMasterShowYN:
            from: inputMasterShowYN
            type: string
          InputDetailsShowYN:
            from: inputDetailsShowYN
            type: string
          inputRequireYN:
            from: inputRequireYN
            type: string
          reportColumnCalId:
            from: reportColumnCalId
          requiredSetting:
            from: requiredSetting
      code:
        from: code
        type: string
      shortName:
        from: shortName
        type: string
      name:
        from: name
        type: string
      reportTypeRowVersion:
        from: reportTypeRowVersion
      payrollPeriodShortName:
        from: payrollPeriod.shortName
      payrollPeriodName:
        from: payrollPeriod.longName
      payrollPeriodNameDetail:
        from: payrollPeriodName
      payrollPeriodCode:
        from: payrollPeriodCode
      payrollPeriod:
        from: payrollPeriod
      countryCode:
        from: countryCode
      countryName:
        from: country.longName
      country:
        from: country
      companyCode:
        from: companyCode
      companyName:
        from: company.longName
      company:
        from: company
      legalEntityCode:
        from: legalEntityCode
      legalEntityName:
        from: legalEntity.longName
      legalEntity:
        from: legalEntity
      payGroupCode:
        from: payGroupCode
      payGroupName:
        from: payGroup.longName
      payGroup:
        from: payGroup
      elementGroupCode:
        from: elementGroupCode
      elementGroupName:
        from: elementGroup.longName
      elementGroup:
        from: elementGroup
      salaryTypeCode:
        from: salaryTypeCode
      salaryTypeName:
        from: salaryType.longName
      salaryType:
        from: salaryType
      paymentDate:
        from: paymentDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      startDate:
        from: startDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      endDate:
        from: endDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      totalEmployee:
        from: totalEmployee
      totalCalculation:
        from: totalCalculation
      totalFail:
        from: totalFail
      totalNotCalculated:
        from: totalNotCalculated
      totalProcessing:
        from: totalProcessing
      totalCompleted:
        from: totalCompleted
      totalLocked:
        from: totalLocked
      currencyCode:
        from: currencyCode
      currencyName:
        from: currency.longName
      currency:
        from: currency
      version:
        from: version
      revision:
        from: revision
      calculationStatus:
        from: calculationStatus
      payrollStatus:
        from: payrollStatus
      notificationMessage:
        from: notificationMessage
      createdBy:
        from: createdBy
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      PayrollPeriodCode:
        from: PayrollPeriodCode
      PayrollPeriodSettingCode:
        from: PayrollPeriodSettingCode
  - name: elementModal
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: totalPages
    config:
      id:
        from: id
        type: string
      reportColumnCalCode:
        from: code
        type: string
      code:
        from: reportColumnCode
        type: string
      countryCode:
        from: countryCode
      elementCode:
        from: elementCode
      reportColumnCode:
        from: reportColumnCode
      name:
        from: name
      codeMapping:
        from: shortName
      operatorCatalogId:
        from: operatorCatalogId
      operatorCode:
        from: operatorCode
      operatorName:
        from: operator.longName
      priority:
        from: priority
      encryption:
        from: encryption
      effectiveDateFrom:
        from: effectiveDateFrom
      note:
        from: note
      status:
        from: enabled
        type: string
      enabled:
        from: enabled
        type: string
      filterYN:
        from: filterYN
      expressions:
        from: expressions
      listShowYN:
        from: listShowYN
      inputShowYN:
        from: inputShowYN
      InputMasterShowYN:
        from: InputMasterShowYN
      InputDetailsShowYN:
        from: InputDetailsShowYN
      inputRequireYN:
        from: inputRequireYN
      reportColumnCalId:
        from: reportColumnCalId
      operatorCatalogId:
        from: operatorCatalogId
      operatorCode:
        from: operatorCode
      effectiveDateTo:
        from: effectiveDateTo
      expressions:
        from: expressions
      dataType:
        from: dataType
      isGeneralElement:
        from: isGeneralElement
        type: string
      defaultValue:
        from: defaultValue
      operatorName:
        from: operatorName
      ElementGroupCode:
        from: ElementGroupCode
        type: string
      ElementTypeCode:
        from: ElementTypeCode
        type: string
      Code:
        from: Code
        type: string
      Name:
        from: Name
        type: string
      ShortName:
        from: ShortName
        type: string
  - name: patchModal
    config:
      id:
        from: id
        type: string
      code:
        from: payrollPeriodSettingCode
      reportColumns:
        from: reportImportColumns
        type: array
        arrayChildren:
          id:
            from: id
          nameInTemplate:
            from: nameInTemplate
          code:
            from: reportColumnCode
          requiredSetting:
            from: requiredSetting

auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: manage-external-payroll-imports
crudConfig:
  query:
    sort:
      - field: updatedAt
        order: DESC
  params:
    id:
      field: id
      type: string
      primary: true
    reportTypeCode:
      field: reportTypeCode
      type: string
    reportTypeRowVersion:
      field: reportTypeRowVersion
      type: string
    code:
      field: code
      type: string
    PayrollPeriodCode:
      field: PayrollPeriodCode
      type: string
    PayrollPeriodSettingCode:
      field: PayrollPeriodSettingCode
      type: string
  routes:
    exclude:
      - createManyBase
      - replaceOneBase
defaultQuery:

routes:
  - path: /api/manage-external-payroll-imports
    method: GET
    model: _

    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'manage-external-payroll-imports'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$'

  - path: /api/manage-external-payroll-imports/:id
    method: GET
    model: _

    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'manage-external-payroll-imports/:{id}:'
      transform: '$'

  - path: /api/manage-external-payroll-imports
    method: POST
    model: _

    query:
    transform: '$'
    # bodyTransform: '$ ~> | $ | {"expression": $.param.code} |'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'manage-external-payroll-imports'
      transform: '$'

  # - path: /api/manage-external-payroll-imports/:id
  #   model: patchModal
  #   method: PATCH
  #   query:
  #   bodyTransform: '$'
  #   dataType: 'base64Data'
  #   upstreamConfig:
  #     response:
  #       dataType: object
  #     method: PUT
  #     path: 'manage-external-payroll-imports/:{id}:'


  - path: /api/manage-external-payroll-imports/:id
    method: DELETE
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'manage-external-payroll-imports/:{id}:'
customRoutes:
  - path: /api/manage-external-payroll-imports/:id/histories
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'manage-external-payroll-imports/:{id}:/history'
      transform: '$'
  - path: /api/manage-external-payroll-imports/report-import-column
    model: patchModal
    method: PATCH
    query:
    bodyTransform: '$'
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: 'manage-external-payroll-imports:report-import-column'
  - path: /api/manage-external-payroll-imports/search-private-element
    method: GET
    model: elementModal
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      transform: '$merge([$, {"data":$map($.data, function($item) {$merge([$item,{"enabled": true, "filterYN": true,"listShowYN": true,"InputMasterShowYN":true,"InputDetailsShowYN":true,"inputShowYN": true,"inputRequireYN": true,"reportColumnCalId": $item.id, "disabled" : true }])[]} )[]}])'
      path: 'manage-external-payroll-imports:search-private-element'
      query:
        ElementGroupCode: ':{ElementGroupCode}:'
        ElementTypeCode: ':{ElementTypeCode}:'
        Code: ':{Code}:'
        Name: ':{Name}:'
        ShortName: ':{ShortName}:'
  - path: /api/manage-external-payroll-imports/search-element-calculations
    method: GET
    model: elementModal
    isExtendedFilter: true
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      transform: '$'
      path: 'manage-external-payroll-imports:search-element-calculations'
      query:
        ReportTypeId: ':{ReportTypeId}:'
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        Filter: '::{filter}::'
  - path: /api/manage-external-payroll-imports/:reportTypeCode/concurrency-check/:reportTypeRowVersion
    method: GET
    response:
        dataType: paginated
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'manage-external-payroll-imports/:{reportTypeCode}:/concurrency-check'
      query:
        Page: ":{options.page}:"
        PageSize: ":{options.limit}:"
        OrderBy: ":{options.sort}:"
        Search: ":{search}:"
        reportTypeCode: ":{reportTypeCode}:"
        version: ":{reportTypeRowVersion}:"
      transform: '$'

  - path: /api/manage-external-payroll-imports/export
    method: GET
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: binary
      path: 'manage-external-payroll-imports:export-formula'
      query:
        PageSize: '1000'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$'

  - path: /api/manage-external-payroll-imports/template/:PayrollPeriodCode/:PayrollPeriodSettingCode/export
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: binary
      path: 'manage-external-payroll-imports:export'
      query:
        PayrollPeriodCode: ':{PayrollPeriodCode}:'
        PayrollPeriodSettingCode: ':{PayrollPeriodSettingCode}:'
      transform: '$'

  - path: /api/manage-external-payroll-imports/modules
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      path: 'manage-external-payroll-imports:modules'
      transform: '$'
  - path: /api/manage-external-payroll-imports/multidelete
    method: DELETE
    model: multiDeleteModel
    bodyTransform: '{"ids": $.ids[]}'
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'manage-external-payroll-imports'
