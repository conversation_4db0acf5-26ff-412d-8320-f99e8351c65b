controller: other-income-details
upstream: ${{UPSTREAM_PR_URL}}

models:
  - name: _
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: count
    config:
      id:
        from: id
      # name BFF
      employeeId:
        # name BE
        from: employeeId
        # type BE
      employeeIdView:
        from: employeeId
        type: string
      fullName:
        from: fullName
        type: string
      employeeRecordNumber:
        from: employeeRecordNumber
      employeeRecordNumberView:
        from: employeeRecordNumber
      employeeGroupCode:
        from: employeeGroupCode
        type: string
      employeeGroup:
        from: employeeGroup.longName
        type: string
      taxPeriodCode:
        from: taxPeriodCode
      taxPeriod: 
        from: taxPeriod.longName
      effectiveDate:
        from: effectiveDateFrom
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      status:
        from: enabled
        typeOptions:
          func: YNToBoolean
      type:
        from: type
      typeCode:
        from: typeCode
      typeName:
        from: type.longName
      otherIncomeType:
        from: $
        objectChildren:
          code:
            from: otherIncomeCode
      otherIncomeCode:
        from: otherIncomeCode
      otherIncomeTypeName:
        from: otherIncome.longName
      exchangeRate:
        from: exchangeRate
      otherIncomeGroupCode:
        from: otherIncomeGroupCode
      otherIncomeGroup:
        from: otherIncomeGroup.longName
      otherIncomeAmount:
        from: otherIncomeAmount
      nonTaxIncome:
        from: nonTaxableIncome
      personalIncomeTax:
        from: personalIncomeTax
      otherPostTaxDeduction:
        from: otherPostTaxDeduction
      dataSource:
        from: dataSource
      netAmount:
        from: netAmount
      currencyCode:
        from: currencyCode
      currency:
        from: currency.longName
      personalIncomeTaxCurrencyCode:
        from: personalIncomeTaxCurrencyCode
      personalIncomeTaxCurrency:
        from: personalIncomeTaxCurrency.longName
      otherPostTaxDeductionCurrencyCode:
        from: otherPostTaxDeductionCurrencyCode
      otherPostTaxDeductionTaxCurrency:
        from: otherPostTaxDeductionCurrency.longName
      nonTaxableIncomeCurrencyCode:
        from: nonTaxableIncomeCurrencyCode
      nonTaxableIncomeCurrency:
        from: nonTaxableIncomeCurrency.longName
      netAmountCurrencyCode:
        from: netAmountCurrencyCode
      netAmountCurrency:
        from: netAmountCurrency.longName
      instanceId:
        from: instanceId
      declarationPeriod:
        from: declarationPeriod
      note:
        from: note
        typeOptions:
          func: stringToMultiLang
      company:
        from: company.longName
        type: string
      companyCode:
        from: companyCode
        type: string
      legalEntity:
        from: legalEntity.longName
        type: string
      legalEntityCode:
        from: legalEntityCode
        type: string
      businessUnit:
        from: businessUnit.longName
        type: string
      businessUnitCode:
        from: businessUnitCode
        type: string
      division:
        from: division.longName
        type: string
      divisionCode:
        from: divisionCode
        type: string
      department:
        from: department.longName
        type: string
      departmentCode:
        from: departmentCode
        type: string
      jobTitle:
        from: jobCode.longName
        type: string
      jobTitleCode:
        from: jobCodeCode
      level:
        from: careerStream.longName
        type: string
      levelCode:
        from: careerStreamCode
      contractType:
        from: contractType.longName
        type: string
      contractTypeCode:
        from: contractTypeCode
      location:
        from: location.longName
        type: string
      locationCode:
        from: locationCode
      paymentDate:
        from: paymentDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      createdBy:
        from: createdBy
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: other-income-details
crudConfig:
  query:
    sort:
      - field: updatedAt
        order: DESC
  params:
    id:
      field: id
      type: string
      primary: true
    code:
      field: employeeId
      type: string
  routes:
    exclude:
      - createManyBase
      - replaceOneBase
defaultQuery:

routes:
  - path: /api/other-income-details
    method: GET
    model: _

    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
        dataKey: data
      transform: '$merge([$, {"data":$map($.data, function($item) {$merge([$item,{"otherIncomeWithCurrency": $formatNumber($number($item.otherIncomeAmount ? $item.otherIncomeAmount : 0), "#,###" ) & " " & $item.currency,"nonTaxIncomeWithCurrency": $formatNumber($number($item.nonTaxIncome ? $item.nonTaxIncome : 0), "#,###") & " " & $item.nonTaxableIncomeCurrency,"personalIncomeTaxWithCurrency": $formatNumber($number($item.personalIncomeTax ? $item.personalIncomeTax : 0), "#,###") & " " & $item.personalIncomeTaxCurrency,"netAmountWithCurrency": $formatNumber($number($item.netAmount ? $item.netAmount : 0) , "#,###") & " " & $item.netAmountCurrency,"otherPostTaxDeductionWithCurrency": $formatNumber($number($item.otherPostTaxDeduction ? $item.otherPostTaxDeduction : 0) , "#,###") & " " & $item.otherPostTaxDeductionCurrencyCode }])} )[]}])'
      path: 'other-income-details'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'

  - path: /api/other-income-details/:id
    method: GET
    model: _

    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'other-income-details/:{id}:'
      transform: '$ ~> | $ | {"currencyCodeEdit": $.currencyCode , "personalIncomeTaxCurrencyCodeEdit": $.personalIncomeTaxCurrencyCode,"employee": {"label": $join($filter([$.employeeId,$.employeeGroupCode,$string($.employeeRecordNumber), $.fullName], $boolean), " - "), "value": {"employeeId": $.employeeId,"code": $.employeeId, "employeeRecordNumber": $.employeeRecordNumber, "jobDataId": $.jobDataId}}} |'

  - path: /api/other-income-details
    method: POST
    model: _

    query:
    transform: '$'
    # bodyTransform: '$merge([$, {"competencyRatingScaleId": $.competencyRatingScaleId.value}])'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'other-income-details'
      transform: '$'

  - path: /api/other-income-details/:id
    model: _
    method: PATCH
    query:
    transform: '$'
    bodyTransform: '$merge([$, {"competencyRatingScaleId": $.competencyRatingScaleId.value}])'
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: 'other-income-details/:{id}:'

  - path: /api/other-income-details/:id
    method: DELETE
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'other-income-details/:{id}:'
customRoutes:
  - path: /api/other-income-details/:id/histories
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'other-income-details/:{id}:/history'
      transform: '$'
  - path: /api/other-income-details/by
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'other-income-details/by'
      query:
        Filter: '::{filter}::'
      transform: '$'
  - path: /api/other-income-details/export
    method: GET
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: binary
      path: 'other-income-details:export'
      query:
        PageSize: '1000'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$'
  - path: /api/other-income-details/multidelete
    method: DELETE
    model: multiDeleteModel
    bodyTransform: '$.ids[]'
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'other-income-details'
