controller: payroll-employees
upstream: ${{UPSTREAM_PR_URL}}

models:
  - name: _
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: totalPages
    config:
      id:
        from: id
        type: string
      ids:
        from: ids
        type: string
      employeeId:
        from: employeeId
        type: string
      employeeRecordNumber:
        from: employeeRecordNumber
      personId:
        from: personId
      fullName:
        from: fullName
        type: string
      company:
        from: company.longName
        type: string
      companyCode:
        from: companyCode
        type: string
      genderCode:
        from: genderCode
        type: string
      genderName:
        from: gender.longName
        type: string
      action:
        from: action.longName
        type: string
      reason:
        from: actionReason.longName
        type: string
      legalEntityCode:
        from: legalEntityCode
        type: string
      legalEntity:
        from: legalEntity.longName
        type: string
      elementGroupCode:
        from: elementGroupCode
      elementGroup:
        from: elementGroup.longName
        type: string
      paymentDate:
        from: paymentDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      departmentCode:
        from: departmentCode
        type: string
      department:
        from: department.longName
        type: string
      payGroupCode:
        from: payGroupCode
        type: string
      payGroup:
        from: payGroup.longName
        type: string
      positionCode:
        from: positionCode
        type: string
      position:
        from: position.longName
        type: string
      prStatusCode:
        from: prStatusCode
        type: string
      pRStatusCode:
        from: pRStatusCode
      prStatus:
        from: prStatus.longName
        type: string
      reportTypeId:
        from: reportTypeId
        type: string
      monthSalary:
        from: monthSalary
        type: string
      payrollPeriodCode:
        from: payrollPeriodCode
        type: string
      payrollPeriod:
        from: payrollPeriod.longName
        type: string
      payrollPeriodSettingCode:
        from: payrollPeriodSettingCode
        type: string
      payrollPeriodSetting:
        from: payrollPeriodSetting.longName
        type: string
      companyName:
        from: company.longName
        type: string
      elementTypeCode:
        from: elementTypeCode
      elementTypeName:
        from: elementType.longName
        type: string
      startDate:
        from: startDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      endDate:
        from: endDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      businessUnitCode:
        from: businessUnitCode
        type: string
      divisionCode:
        from: divisionCode
        type: string
      jobTitleCode:
        from: jobTitleCode
        type: string
      jobIndicator:
        from: jobIndicator.longName
        type: string
      jobIndicatorCode:
        from: jobIndicatorCode
        type: string
      locationCode:
        from: locationCode
        type: string
      employeeGroupCode:
        from: employeeGroupCode
        type: string
      contractTypeCode:
        from: contractTypeCode
        type: string
      employeeLevelCode:
        from: employeeLevelCode
        type: string
      employeeGroup:
        from: employeeGroup.longName
        type: string
      businessUnit:
        from: businessUnit.longName
        type: string
      division:
        from: division.longName
        type: string
      isSalaried:
        from: isSalaried
        type: string
        typeOptions:
          func: YNToBoolean
      isLock:
        from: isLock
        type: string
        typeOptions:
          func: YNToBoolean
      calculationStatus:
        from: calculationStatus
        type: string
      executeCalErrorMessage:
        from: executeCalErrorMessage
        type: string
      executeCalErrorTime:
        from: executeCalErrorTime
      note:
        from: note
        type: string
      createdBy:
        from: createdBy
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      segment:
        from: segment
      version:
        from: version
      revision:
        from: revision
      formula:
        from: formular.longName
        type: string
      formulaStructure:
        from: formulaStructure
        type: string
      formulaStructureCode:
        from: formulaStructure.code
        type: string
      formulaStructureName:
        from: formulaStructure.longName
        type: string
      payrollEmployees:
        from: payrollEmployees
      isClearNote:
        from: isClearNote
        type: string
        typeOptions:
          func: YNToBoolean
      hasNote:
        from: hasNote
        type: string
        typeOptions:
          func: YNToBoolean
      isImport:
        from: IsImport
        typeOptions:
          func: YNToBoolean
      toolAction:
        from: action
      hasEmployee:
        from: hasEmployee
  - name: _export_static_tab
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: totalPages
    config:
      id:
        from: Id
        type: string
      ids:
        from: ids
        type: string
      employeeId:
        from: employeeId
        type: string
      employeeRecordNumber:
        from: employeeRecordNumber
      personId:
        from: personId
      fullName:
        from: fullName
        type: string
      company:
        from: company.longName
        type: string
      companyCode:
        from: companyCode
        type: string
      genderCode:
        from: genderCode
        type: string
      genderName:
        from: gender.longName
        type: string
      action:
        from: action.longName
        type: string
      reason:
        from: actionReason.longName
        type: string
      legalEntityCode:
        from: legalEntityCode
        type: string
      legalEntity:
        from: legalEntity.longName
        type: string
      elementGroupCode:
        from: elementGroupCode
      elementGroup:
        from: elementGroup.longName
        type: string
      paymentDate:
        from: paymentDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      departmentCode:
        from: departmentCode
        type: string
      department:
        from: department.longName
        type: string
      payGroupCode:
        from: payGroupCode
        type: string
      payGroup:
        from: payGroup.longName
        type: string
      positionCode:
        from: positionCode
        type: string
      position:
        from: position.longName
        type: string
      prStatusCode:
        from: prStatusCode
        type: string
      pRStatusCode:
        from: pRStatusCode
      prStatus:
        from: prStatus.longName
        type: string
      reportTypeId:
        from: reportTypeId
        type: string
      monthSalary:
        from: monthSalary
        type: string
      payrollPeriodCode:
        from: payrollPeriodCode
        type: string
      payrollPeriod:
        from: payrollPeriod.longName
        type: string
      payrollPeriodSettingCode:
        from: payrollPeriodSettingCode
        type: string
      payrollPeriodSetting:
        from: payrollPeriodSetting.longName
        type: string
      companyName:
        from: company.longName
        type: string
      elementTypeCode:
        from: elementTypeCode
      elementTypeName:
        from: elementType.longName
        type: string
      startDate:
        from: startDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      endDate:
        from: endDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      businessUnitCode:
        from: businessUnitCode
        type: string
      divisionCode:
        from: divisionCode
        type: string
      jobTitleCode:
        from: jobTitleCode
        type: string
      jobIndicator:
        from: jobIndicator.longName
        type: string
      jobIndicatorCode:
        from: jobIndicatorCode
        type: string
      locationCode:
        from: locationCode
        type: string
      employeeGroupCode:
        from: employeeGroupCode
        type: string
      contractTypeCode:
        from: contractTypeCode
        type: string
      employeeLevelCode:
        from: employeeLevelCode
        type: string
      employeeGroup:
        from: employeeGroup.longName
        type: string
      businessUnit:
        from: businessUnit.longName
        type: string
      division:
        from: division.longName
        type: string
      isSalaried:
        from: isSalaried
        type: string
        typeOptions:
          func: YNToBoolean
      isLock:
        from: isLock
        type: string
        typeOptions:
          func: YNToBoolean
      calculationStatus:
        from: calculationStatus
        type: string
      executeCalErrorMessage:
        from: executeCalErrorMessage
        type: string
      executeCalErrorTime:
        from: executeCalErrorTime
      note:
        from: note
        type: string
      createdBy:
        from: createdBy
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      segment:
        from: segment
      version:
        from: version
      revision:
        from: revision
      formula:
        from: formular.longName
        type: string
      formulaStructure:
        from: formulaStructure
        type: string
      formulaStructureCode:
        from: formulaStructure.code
        type: string
      formulaStructureName:
        from: formulaStructure.longName
        type: string
      payrollEmployees:
        from: payrollEmployees
      isClearNote:
        from: isClearNote
        type: string
        typeOptions:
          func: YNToBoolean
      hasNote:
        from: hasNote
        type: string
        typeOptions:
          func: YNToBoolean

  - name: calculate-model
    config:
      companyCode:
        from: companyCode
        type: string
      payrollPeriodCode:
        from: payrollPeriodCode
        type: string
      payrollPeriodSettingCode:
        from: payrollPeriodSettingCode
        type: string
      businessUnitCode:
        from: businessUnitCode
        type: string
      divisionCode:
        from: divisionCode
        type: string
      departmentCode:
        from: departmentCode
        type: string
      jobTitleCode:
        from: jobTitleCode
        type: string
      positionCode:
        from: positionCode
        type: string
      locationCode:
        from: locationCode
        type: string
      employeeGroupCode:
        from: employeeGroupCode
        type: string
      contractTypeCode:
        from: contractTypeCode
        type: string
      employeeLevelCode:
        from: employeeLevelCode
        type: string
      prStatusCode:
        from: prStatusCode
        type: string
      calculateStatus:
        from: calculateStatus
        type: string
      employeeIds:
        from: employeeIds
        type: string
      isClearNote:
        from: isClearNote
        type: string
        typeOptions:
          func: YNToBoolean
      hasNote:
        from: hasNote
        type: string
        typeOptions:
          func: YNToBoolean
auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: payroll-employees
crudConfig:
  query:
    sort:
      - field: employeeId
        order: ASC
  params:
    id:
      field: id
      type: string
      primary: true
    name:
      field: name
      type: string
  routes:
    exclude:
      - createManyBase
      - replaceOneBase
defaultQuery:

routes:
  - path: /api/payroll-employees
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'payroll-employees'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$merge([$, {"data":$map($.data, function($item) {$merge([$item,{"errorMessage": $item.executeCalErrorMessage & ( $item.executeCalErrorTime ? " - " &  $fromMillis($item.executeCalErrorTime * 1000, "[M01]/[D01]/[Y0001] [H01]:[m01]:[s01]") : "")     }])} )[]}])'
  - path: /api/payroll-employees/:id
    method: GET
    model: _

    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'payroll-employees/:{id}:'
      transform: '$'

  - path: /api/payroll-employees
    method: POST
    model: _

    query:
    transform: '$'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'payroll-employees'
      transform: '$'
  - path: /api/payroll-employees/:id
    model: _
    method: PATCH
    query:
    transform: '$'
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: 'payroll-employees/:{id}:'
  - path: /api/payroll-employees
    model: _
    method: PATCH
    query:
    transform: '$'
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: 'payroll-employees'
  - path: /api/payroll-employees/:id
    method: DELETE
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'payroll-employees/:{id}:'
customRoutes:
  - path: /api/payroll-employees/get-for-payroll-calculation
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'payroll-employees/get-for-payroll-calculation'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$merge([$, {"data":$map($.data, function($item) {$merge([$item,{"errorMessage": $item.executeCalErrorMessage & ( $item.executeCalErrorTime ? " - " &  $fromMillis($item.executeCalErrorTime * 1000, "[M01]/[D01]/[Y0001] [H01]:[m01]:[s01]") : "")     }])} )[]}])'
  - path: /api/payroll-employees/:id/histories
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'payroll-employees'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
      transform: '$'
  - path: /api/payroll-employees/has-employee-tab
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'payroll-employees/has-employee-tab'
      query:
        PayrollPeriodCode: ':{payrollPeriodCode}:'
        PayrollPeriodSettingCode: ':{payrollPeriodSettingCode}:'
        CalculationStatus: ':{calculationStatus}:'
        Action: ':{toolAction}:'
      transform: '$'
  - path: /api/payroll-employees
    model: _
    method: PATCH
    query:
    transform: '$'
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: 'payroll-employees'
  - path: /api/payroll-employees/:id
    model: _
    method: PATCH
    query:
    transform: '$'
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: 'payroll-employees/:{id}:'

  - path: /api/payroll-employees/process-payroll-employees
    model: calculate-model
    method: PATCH
    query:
    transform: '$'
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: 'payroll-employees/process-payroll-employees'

  - path: /api/payroll-employees/check-note-process-payroll-employees
    model: calculate-model
    method: POST
    query:
    transform: '$'
    upstreamConfig:
      response:
        dataType: object
      method: POST
      path: 'payroll-employees/check-note-process-payroll-employees'

  - path: /api/payroll-employees/lock
    model: _
    method: PATCH
    query:
    transform: '$'
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: 'payroll-employees/lock'

  - path: /api/payroll-employees/un-lock
    model: _
    method: PATCH
    query:
    transform: '$'
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: 'payroll-employees/un-lock'

  - path: /api/payroll-employees/lock-all
    model: _
    method: PATCH
    query:
    transform: '$'
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: 'payroll-employees/lock-all'

  - path: /api/payroll-employees/un-lock-all
    model: _
    method: PATCH
    query:
    transform: '$'
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: 'payroll-employees/un-lock-all'

  - path: /api/payroll-employees/info
    method: POST
    model: _

    query:
    transform: '$'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'payroll-employees/info'
      transform: '$'

  - path: /api/payroll-employees/set-note-paymentdate-all
    method: PATCH
    model: _

    query:
    transform: '$'
    upstreamConfig:
      method: PUT
      response:
        dataType: object
      path: 'payroll-employees/set-note-paymentdate-all'
      transform: '$'

  - path: /api/payroll-employees/re-calculate-salary
    method: PATCH
    model: _

    query:
    transform: '$'
    upstreamConfig:
      method: PUT
      response:
        dataType: object
      path: 'payroll-employees/re-calculate-salary'
      transform: '$'

  - path: /api/payroll-employees/check-note-re-calculate-salary
    method: POST
    model: _

    query:
    transform: '$'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'payroll-employees/check-note-re-calculate-salary'
      transform: '$'

  - path: /api/payroll-employees/check-note-re-calculate-salary-all
    method: POST
    model: _

    query:
    transform: '$'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'payroll-employees/check-note-re-calculate-salary-all'
      transform: '$'

  - path: /api/payroll-employees/re-calculate-salary-all
    method: PATCH
    model: _

    query:
    transform: '$'
    upstreamConfig:
      method: PUT
      response:
        dataType: object
      path: 'payroll-employees/re-calculate-salary-all'
      transform: '$'
  - path: /api/payroll-employees/export
    method: GET
    model: _export_static_tab
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: binary
      path: 'payroll-employees/export'
      query:
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
        exportAll: 'true'
      transform: '$'
  - path: /api/payroll-employees/export-static-tab/export
    method: GET
    model: _export_static_tab
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: binary
      path: 'salary-formulas/payroll-summaries/export-static-tab'
      query:
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
        exportAll: 'true'
      transform: '$'
  - path: /api/payroll-employees/set-note-paymentdate
    method: PATCH
    model: _

    query:
    transform: '$'
    upstreamConfig:
      method: PUT
      response:
        dataType: object
      path: 'payroll-employees/set-note-paymentdate'
      transform: '$'
