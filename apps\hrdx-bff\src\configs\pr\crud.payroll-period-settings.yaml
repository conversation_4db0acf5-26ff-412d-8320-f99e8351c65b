controller: payroll-period-settings
upstream: ${{UPSTREAM_PR_URL}}

models:
  - name: checkCompanyType
    config:
      companyType:
        from: companyType
        type: string
      companyCode:
        from: companyCode
        type: string
      payrollPeriodCode:
        from: payrollPeriodCode
        type: string
  - name: _
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: totalPages
    config:
      id:
        from: id
        type: string
      # name BFF
      payrollPeriodCode:
        # name BE
        from: payrollPeriodCode
        # type BE
        type: string
      payrollPeriodName:
        from: payrollPeriod.longName
        type: string
      tsPeriod:
        from: phase
        type: string
      tsPeriodCode:
        from: tsPeriodCode
        type: string
      tsPeriodName:
        from: tsPeriod.longName
        type: string
      tsPeriodGroup:
        from: $
        objectChildren: 
          id:
            from: tsPeriodCode
          code:
            from: tsPeriodCode
      code:
        from: code
        type: string
      shortName:
        from: shortName
        type: string
        typeOptions:
          func: stringToMultiLang
      longName:
        from: name
        type: string
        typeOptions:
          func: stringToMultiLang
      startDate:
        from: startDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      endDate:
        from: endDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      status:
        from: enabled
        type: string
        typeOptions:
          func: YNToBoolean
      paymentDay:
        from: paymentDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      company:
        from: companyCode
        type: string
      companyCode:
        from: companyCode
        type: string
      companyId:
        from: company.id
        type: string
      companyName:
        from: company.longName
        type: string
      companyObj:
        from: $
        objectChildren:
          id:
            from: company.id
          code:
            from: companyCode
      country:
        from: countryCode
        type: string
      countryName:
        from: country.longName
        type: string
      legalEntityCode:
        from: legalEntityCode
        type: string
      legalEntityName:
        from: legalEntity.longName
        type: string
      legalEntityId:
        from: legalEntity.id
      legalEntityObj:
        from: $
        objectChildren:
          id:
            from: legalEntity.id
          code:
            from: legalEntityCode
      payGroupCode:
        from: payGroupCode
        type: string
      payGroupCodeView:
        from: payGroupCode
        type: string
      payGroupName:
        from: payGroup.longName
        type: string
      elementGroup:
        from: elementGroupCode
        type: string
      elementGroupName:
        from: elementGroup.longName
        type: string
      salaryType:
        from: salaryTypeCode
        type: string
      salaryTypeName:
        from: salaryType.longName
        type: string
      currency:
        from: currencyCode
        type: string
      currencyName:
        from: currency.longName
        type: string
      prExtendPeriodCode:
        from: prExtendPeriodCode
        type: string
      prExtendPeriodName:
        from: prExtendPeriod.longName
        type: string
      note:
        from: note
        type: string
        typeOptions:
          func: stringToMultiLang
      createdBy:
        from: createdBy
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      payrollStatus:
        from: payrollStatus
          

auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: payroll-period-settings
crudConfig:
  query:
    sort:
      - field: updatedAt
        order: DESC
  params:
    id:
      field: id
      type: string
      primary: true
    code:
      field: code
      type: string
  routes:
    exclude:
      - createManyBase
      - replaceOneBase
defaultQuery:

routes:
  - path: /api/payroll-period-settings
    method: GET
    model: _

    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'payroll-period-settings'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ":{search}:"
        Filter: '::{filter}::'
      transform: '$'

  - path: /api/payroll-period-settings/:id
    method: GET
    model: _

    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'payroll-period-settings/:{id}:'
      transform: '$merge(
      [ $, 
      { 
      "legalEntityObj":legalEntityCode ? {
        "label": legalEntityName & " (" & legalEntityCode &")", 
        "value": {"id": legalEntityId, "code": legalEntityCode }
      } ,
      "payrollPeriodItem":payrollPeriodCode ? {
        "label": payrollPeriodName & " (" & payrollPeriodCode &")", 
        "value": payrollPeriodCode
      } ,
      "companyObj":companyCode ? {
        "label": companyName & " (" & companyCode &")", 
        "value": {"id": companyId, "code": companyCode }
      } ,
      "payGroupCode":payGroupCode ? {
        "label": payGroupName & " (" & payGroupCode &")", 
        "value": payGroupCode
      }
      }
      ])'

  - path: /api/payroll-period-settings
    method: POST
    model: _

    query:
    transform: '$'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'payroll-period-settings'
      transform: '$'

  - path: /api/payroll-period-settings/:id
    model: _
    method: PATCH
    query:
    transform: '$'
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: 'payroll-period-settings/:{id}:'

  - path: /api/payroll-period-settings/:id
    method: DELETE
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'payroll-period-settings/:{id}:'
customRoutes:
  - path: /api/payroll-period-settings/check-company-type
    method: GET
    model: checkCompanyType
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'payroll-period-settings/check-company-type'
      query:
        companyCode: ':{companyCode}:'
        payrollPeriodCode: ':{payrollPeriodCode}:'
      transform: '$'
  - path: /api/payroll-period-settings/:id/histories
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'payroll-period-settings'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
      transform: '$'
  - path: /api/payroll-period-settings/export
    method: GET
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: binary
      path: 'payroll-period-settings/export'
      query:
        PageSize: '1000'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$'
  - path: /api/payroll-period-settings/multidelete
    method: DELETE
    model: multiDeleteModel
    bodyTransform: '$.ids[]'
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'payroll-period-settings'
  - path: /api/payroll-period-settings/authorization-get
    method: GET
    model: _

    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'payroll-period-settings/authorization-get'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ":{search}:"
        Filter: '::{filter}::'
      transform: '$'