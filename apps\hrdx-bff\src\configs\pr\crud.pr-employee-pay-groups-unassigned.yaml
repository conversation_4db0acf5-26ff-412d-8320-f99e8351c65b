controller: pr-employee-pay-groups-unassigned
upstream: ${{UPSTREAM_PR_URL}}

models:
  - name: _
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: totalPages
    config:
      id:
        from: employeeId
        type: string
      employeeId_ern:
        from: employeeId_ern
      employeeId:
        from: employeeId
      employeeGroupCode:
        from: employeeGroupCode
      employeeGroupName:
        from: employeeGroup.longName
        type: string
      fullName:
        from: fullName
      employeeRecordNumber:
        from: employeeRecordNumber
        type: number
      payGroupCode:
        from: payGroupCode
        type: string
      payGroupName:
        from: payGroup.longName
        type: string
      company:
        from: company
      companyCode:
        from: companyCode
      companyName:
        from: company.longName
        type: string
      legalEntity:
        from: legalEntity
      legalEntityCode:
        from: legalEntityCode
      legalEntityName:
        from: legalEntity.longName
        type: string
      employeeName:
        from: employeeName
      jobData:
        from: jobData
        type: object
        objectChildren:
          fullName:
            from: fullName
          companyCode:
            from: companyCode
          legalEntityCode:
            from: legalEntityCode
          businessUnitCode:
            from: businessUnitCode
          divisionCode:
            from: divisionCode
          departmentCode:
            from: departmentCode
          employeeGroupCode:
            from: employeeGroupCode
          jobLevelCode:
            from: jobLevelCode
          empLevelCode:
            from: empLevelCode
          contractTypeCode:
            from: contractTypeCode
          locationCode:
            from: locationCode

      businessUnit:
        from: businessUnit
      businessUnitCode:
        from: businessUnitCode
      businessUnitName:
        from: businessUnit.longName
        type: string
      division:
        from: division
      divisionCode:
        from: divisionCode
      divisionName:
        from: division.longName
        type: string
      department:
        from: department
      departmentCode:
        from: departmentCode
      departmentName:
        from: department.longName
        type: string
      jobTitle:
        from: jobTitle
      jobTitleCode:
        from: jobTitleCode
      jobTitleName:
        from: jobTitle.longName
        type: string
      location:
        from: location
      locationCode:
        from: locationCode
      locationName:
        from: location.longName
        type: string
      staffLevel:
        from: staffLevel
      staffLevelCode:
        from: staffLevelCode
      staffLevelName:
        from: staffLevel.longName
        type: string
      contractType:
        from: contractType
      contractTypeCode:
        from: contractTypeCode
      contractTypeName:
        from: contractType.longName
        type: string
      positionCode:
        from: positionCode
      effectiveDate:
        from: effectiveDateFrom
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      effectiveDateForSort:
        from: effectiveDateFrom
      status:
        from: enabled
        type: string
        typeOptions:
          func: YNToBoolean
      note:
        from: note
        type: string
        typeOptions:
          func: stringToMultiLang
      createdBy:
        from: createdBy
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: pr-employee-pay-groups-unassigned
crudConfig:
  query:
  params:
    id:
      field: id
      type: string
      primary: true
    code:
      field: code
      type: string
    longDate:
      field: longDate
      type: number
  routes:
    exclude:
      - createManyBase
      - replaceOneBase
defaultQuery:

routes:
  - path: /api/pr-employee-pay-groups-unassigned/:id
    model: _
    method: PATCH
    query:
    transform: '$'
    upstreamConfig:
      response:
        dataType: object
      method: POST
      path: 'employee-pay-groups'
customRoutes:
  - path: /api/pr-employee-pay-groups-unassigned/:longDate/unassigned
    method: GET
    model: _
    elemMatch: true
    query:
    transform: '$'
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'employee-pay-groups/:{longDate}:/unassigned'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$'

  - path: /api/pr-employee-pay-groups-unassigned/:longDate/export
    method: GET
    elemMatch: true
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: binary
      path: 'employee-pay-groups/export/:{longDate}:/unassign'
      query:
        PageSize: '1000'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$'

  - path: /api/pr-employee-pay-groups-unassigned/:id
    model: _
    method: PATCH
    query:
    transform: '$'
    upstreamConfig:
      response:
        dataType: object
      method: POST
      path: 'employee-pay-groups'
