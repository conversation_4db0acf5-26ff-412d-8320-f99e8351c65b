controller: pr-employee-salaries
upstream: ${{UPSTREAM_PR_URL}}

models:
  - name: _
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: indexFrom
    config:
      id:
        from: id
        type: string
      employee:
        from: employee
      employeeId:
        from: employeeId
        type: string
      employeeIdFilter:
        from: employeeId
        type: string
      employeeIdView:
        from: employeeId
        type: string
      employeeRecordNumber:
        from: employeeRecordNumber
      employeeRecordNumberView:
        from: employeeRecordNumber
      fullName:
        from: fullName
        type: string
      company:
        from: company
      companyCode:
        from: companyCode
      companyName:
        from: company.longName
      legalEntity:
        from: legalEntity
      legalEntityCode:
        from: legalEntityCode
      legalEntityName:
        from: legalEntity.longName
        type: string
      businessUnit:
        from: businessUnit
      businessUnitCode:
        from: businessUnitCode
      businessUnitName:
        from: businessUnit.longName
      division:
        from: division
      divisionCode:
        from: divisionCode
      divisionName:
        from: division.longName
      department:
        from: department
      departmentCode:
        from: departmentCode
      departmentName:
        from: department.longName
      jobCode:
        from: jobCode
      jobCodeCode:
        from: jobCodeCode
      jobCodeName:
        from: jobCode.longName
      contractType:
        from: contractType
      contractTypeCode:
        from: contractTypeCode
      contractTypeName:
        from: contractType.longName
      location:
        from: location
      locationCode:
        from: locationCode
      locationName:
        from: location.longName
      employeeGroup:
        from: employeeGroup
      employeeGroupCode:
        from: employeeGroupCode
      employeeGroupName:
        from: employeeGroup.longName
      effectiveDateForSort:
        from: effectiveDateFrom
      effectiveDateFrom:
        from: effectiveDateFrom
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      expectedIncreaseDate:
        from: expectedIncreaseDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      salaryAdminPlan:
        from: salaryAdminPlan
      salaryAdminPlanCode:
        from: salaryAdminPlanCode
      salaryAdminPlanName:
        from: salaryAdminPlan.longName
      grade:
        from: grade
      gradeCode:
        from: gradeCode
      gradeName:
        from: grade.longName
      step:
        from: step
      stepCode:
        from: stepCode
      stepName:
        from: step.longName
      currency:
        from: currency
      currencyCode:
        from: currencyCode
      currencyName:
        from: currency.longName
      salaryType:
        from: salaryType
      salaryTypeCode:
        from: salaryTypeCode
      salaryTypeName:
        from: salaryType.longName
      employeeSalaryType:
        from: employeeSalaryType
      employeeSalaryTypeCode:
        from: employeeSalaryTypeCode
      employeeSalaryTypeName:
        from: employeeSalaryType.longName

      totalSalary:
        from: totalSalary
        type: number
      totalSalaryView:
        from: totalSalary
        type: number
      frequency:
        from: frequency
        type: number
      totalSalaryAfterConvert:
        from: totalSalaryAfterConvert
        type: number
      totalSalaryAfterConvertView:
        from: totalSalaryAfterConvert
        type: number
      decisionNo:
        from: decisionNo
      decisionNoView:
        from: decisionNo
      decisionSignerName:
        from: decisionSignerName
        type: string
      decisionSignerId:
        from: decisionSignerId
      isDecision:
        from: isDecision
      decisionSignDate:
        from: decisionSignDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      decisionSignDateView:
        from: decisionSignDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      attachFile:
        from: attachFile
        type: string
      attachFileResults:
        from: attachFileResults
      rateCode:
        from: rateCode
      wageClassificationModel:
        from: wageClassificationModel
      wageClassificationCode:
        from: wageClassificationModel.code
      wageClassificationName:
        from: wageClassificationModel.longName
      rateCodeView:
        from: rateCode
      amount:
        from: amount
      amountView:
        from: amount
      createdBy:
        from: createdBy
        type: string
      updatedBy:
        from: updatedBy
        type: string
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      details:
        from: details
        type: array
        arrayChildren:
          id:
            from: id
          employeeSalaryId:
            from: employeeSalaryId
          fixedAllowanceCode:
            from: fixedAllowanceCode
          fixedAllowance:
            from: fixedAllowance
          fixedAllowanceName:
            from: fixedAllowance.longName
          currencyCode:
            from: currencyCode
          currencyName:
            from: currency.longName
          amount:
            from: amount
          frequency:
            from: frequency
          instanceId:
            from: instanceId
          createdBy:
            from: createdBy
          createdAt:
            from: createdAt
          updatedBy:
            from: updatedBy
          updatedAt:
            from: updatedAt
      employeeId:
        from: employeeId
      ern:
        from: ern
      employeeGroupCode:
        from: employeeGroupCode
      incomePackageCode:
        from: incomePackageCode
      packageCode:
        from: packageCode
      incomePackage:
        from: incomePackage
      incomePackageName:
        from: incomePackage.longName
      incomePackageObj:
        from: $
        objectChildren:
          code:
            from: code
      reason:
        from: reason
      groupCode:
        from: groupCode
      payGroupCode:
        from: payGroupCode
      genderCode:
        from: genderCode
      localExpart:
        from: localExpart
      jobIndicatorCode:
        from: jobIndicatorCode
      hrStatusCode:
        from: hrStatusCode
      payrollStatusCode:
        from: payrollStatusCode
      salaryByTierMasterCode:
        from: salaryByTierMasterCode
  - name: postModal
    config:
      id:
        from: id
      employeeId:
        from: EmployeeId
      employeeRecordNumber:
        from: EmployeeRecordNumber
      effectiveDateFrom:
        from: effectiveDateFrom
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      expectedIncreaseDate:
        from: ExpectedIncreaseDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      isDecision:
        from: IsDecision
      decisionNo:
        from: DecisionNo
      decisionSignerId:
        from: DecisionSignerId
      decisionSignDate:
        from: DecisionSignDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      attachFile:
        from: AttachFile
      isNoChangeFile:
        from: IsNoChangeFile
      employeeSalaryTypeCode:
        from: EmployeeSalaryTypeCode
      salaryTypeCode:
        from: SalaryTypeCode
      rateCode:
        from: RateCode
      salaryAdminPlanCode:
        from: SalaryAdminPlanCode
      gradeCode:
        from: GradeCode
      stepCode:
        from: StepCode
      wageClassification:
        from: WageClassification
      wageClassificationModel:
        from: wageClassificationModel
      wageClassificationName:
        from: wageClassificationModel.longName
      amount:
        from: Amount
      currencyCode:
        from: CurrencyCode
      frequency:
        from: Frequency
      reason:
        from: Reason
      percent:
        from: Percent
      totalSalary:
        from: TotalSalary
      totalSalaryAfterConvert:
        from: TotalSalaryAfterConvert
      details:
        from: Details
        arrayChildren:
          fixedAllowanceCode:
            from: fixedAllowanceCode
            typeOptions:
              func: toValueByKey
              agrs: value
          currencyCode:
            from: currencyCode
          amount:
            from: amount
          frequency:
            from: frequency

      salaryByTierMasterCode:
        from: SalaryByTierMasterCode
      employee:
        from: $
        objectChildren:
          id:
            from: EmployeeId
          code:
            from: EmployeeIdCode
          employeeRecordNumber:
            from: EmployeeRecordNumber

      rateCodeGroup:
        from: $
        objectChildren:
          id:
            from: RateCode
          code:
            from: RateCode
          salaryAdminPlanCode:
            from: SalaryAdminPlanCode
          stepCode:
            from: StepCode
          gradeCode:
            from: GradeCode
          wageClassification:
            from: WageClassification
      incomePackageCode:
        from: incomePackageCode
      incomePackage:
        from: incomePackage
      incomePackageName:
        from: incomePackage.longName
      incomePackageObj:
        from: $
        objectChildren:
          code:
            from: code
  - name: viewDetail
    config:
      id:
        from: id
      employeeId:
        from: employeeId
      employeeGroupCode:
        from: employeeGroupCode
      employeeRecordNumber:
        from: employeeRecordNumber
      fullName:
        from: fullName
      companyName:
        from: company.longName
      legalEntityName:
        from: legalEntity.longName
      businessUnitName:
        from: businessUnit.longName
      divisionName:
        from: division.longName
      departmentName:
        from: department.longName
      jobCodeName:
        from: jobCode.longName
      contractTypeName:
        from: contractType.longName
      locationName:
        from: location.longName
      effectiveDateFrom:
        from: effectiveDateFrom
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      isDecision:
        from: isDecision
      decisionNo:
        from: decisionNo
      jobDataId:
        from: jobDataId
      decisionSignerName:
        from: decisionSignerName
      decisionSignerId:
        from: decisionSignerId
      decisionSignDate:
        from: decisionSignDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      attachFile:
        from: attachFile
      attachFileName:
        from: attachFileName
      attachFileResults:
        from: attachFileResults
      employeeSalaryTypeCode:
        from: employeeSalaryTypeCode
      employeeSalaryTypeName:
        from: employeeSalaryType.longName
      salaryByTierMasterCode:
        from: salaryByTierMasterCode
      salaryByTierMasterName:
        from: salaryByTierMaster.longName
      expectedIncreaseDate:
        from: expectedIncreaseDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      rateCode:
        from: rateCode
      rateCodeGroup:
        from: rateCodeGroup
      rateCodeView:
        from: rateCode
      salaryAdminPlanName:
        from: salaryAdminPlan.longName
      salaryAdminPlanCode:
        from: salaryAdminPlan.code
      gradeName:
        from: grade.longName
      gradeCode:
        from: grade.code
      stepName:
        from: step.longName
      stepCode:
        from: step.code
      currencyCode:
        from: currencyCode
      currencyName:
        from: currency.longName
      wageClassificationName:
        from: wageClassificationModel.longName
      wageClassificationModel:
        from: wageClassificationModel
      wageClassificationCode:
        from: wageClassificationModel.code
      amount:
        from: amount
      amountCurrency:
        from: amount
        typeOptions:
          func: numberToCurrency
      reason:
        from: reason
      salaryTypeName:
        from: salaryType.longName
      frequency:
        from: frequency
      totalSalaryAfterConvert:
        from: totalSalaryAfterConvert
      totalSalary:
        from: totalSalary
      details:
        from: details
        type: array
        arrayChildren:
          id:
            from: id
          employeeSalaryId:
            from: employeeSalaryId
          fixedAllowanceCode:
            from: fixedAllowanceCode
          fixedAllowance:
            from: fixedAllowance
          fixedAllowanceName:
            from: fixedAllowance.longName
          currencyCode:
            from: currencyCode
          currencyName:
            from: currency.longName
          amount:
            from: amount
          frequency:
            from: frequency
          instanceId:
            from: instanceId
          createdBy:
            from: createdBy
          createdAt:
            from: createdAt
          updatedBy:
            from: updatedBy
          updatedAt:
            from: updatedAt
      incomePackageCode:
        from: incomePackage.code
      incomePackage:
        from: incomePackage
      incomePackageName:
        from: incomePackage.longName
      incomePackageObj:
        from: $
        objectChildren:
          code:
            from: code
      historicalSalaryCode:
        from: historicalSalaryCode
      createdBy:
        from: createdBy
        type: string
      updatedBy:
        from: updatedBy
        type: string
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
  - name: packageIncome
    config:
      id:
        from: id
      code:
        from: code
      shortName:
        from: shortName
      longName:
        from: longName
      effectiveDate:
        from: effectiveDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      code:
        from: code
        type: string
        typeOptions:
          func: YNToBoolean
      min:
        from: min
      max:
        from: max
      mid:
        from: mid
      note:
        from: note
      currency:
        from: currency
      currencyCode:
        from: currencyCode


  - name: multiDeleteModel
    config:
      ids:
        type: array
        from: ids

defaultQuery:
auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: pr-employee-salaries
crudConfig:
  query:
    sort:
      - field: updatedAt
        order: DESC
  params:
    id:
      field: id
      type: string
      primary: true
    code:
      field: code
      type: string
    employeeId:
      field: employeeId
      type: string
    ern:
      field: ern
      type: string
    employeeGroupCode:
      field: employeeGroupCode
      type: string
  routes:
    exclude:
      - createManyBase
      - replaceOneBase

routes:
  - path: /api/pr-employee-salaries
    method: GET
    model: _

    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'employee-salaries'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '(    $listEmp := $distinct($map($.data, function($v) { {"employeeId": $v.employeeId, "employeeGroupCode": ($v.employeeGroupCode ? $v.employeeGroupCode : ""), "employeeRecordNumber": $v.employeeRecordNumber} } ));    $items:= [$map($listEmp, function($group){ (        $employee := $filter($.data, function($v){$v.employeeId = $group.employeeId and ($v.employeeGroupCode ? $v.employeeGroupCode : "") = $group.employeeGroupCode and $v.employeeRecordNumber = $group.employeeRecordNumber})[0];        {            "employeeId": $employee.employeeId,            "employeeRecordNumber": $employee.employeeRecordNumber,            "fullName": $employee.fullName, "employeeGroupCode": $employee.employeeGroupCode,            "children": $sort([$filter($.data, function($v){$v.employeeId = $group.employeeId and ($v.employeeGroupCode ? $v.employeeGroupCode : "") = $group.employeeGroupCode and $v.employeeRecordNumber = $group.employeeRecordNumber })], function($a, $b) {$b.effectiveDateForSort > $a.effectiveDateForSort})        };    )})];    $merge([$, {"data": $items} ]))'

  - path: /api/pr-employee-salaries/:id
    method: GET
    model: viewDetail

    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'employee-salaries/:{id}:'
      transform: '$ ~> | $ | {"decisionSignerId": {"label": decisionSignerId & "-" & decisionSignerName, "value": decisionSignerId},"attachmentResults": $.attachFile ? {"name": $.attachFile , "url": "/api/pr-files/download", "fileValue": $.attachFile, "fileField": "AttachFile"  } : null, "employee": {"label": $join($filter([$.employeeId,$.employeeGroupCode,$string($.employeeRecordNumber), $.fullName], $boolean), " - "), "value": {"id": $.employeeId,"code": $.employeeId, "employeeRecordNumber": $.employeeRecordNumber, "jobDataId": $.jobDataId}}, "details": $map($.details, function($a) {$merge([$a, {"fixedAllowanceName": $join($filter([$a.fixedAllowance.longName,$a.fixedAllowance.name,$a.fixedAllowance.code], $boolean), " - "),"fixedAllowanceCode": {"label": $a.fixedAllowance.longName & " - " & $a.fixedAllowanceCode, "value": $a.fixedAllowanceCode}} ])})[], "rateCodeGroup": {    "label": $.employeeSalaryTypeCode = "SLRT_00002" ? $.rateCode & " (" & (amountCurrency) & ")" : " ",    "value": $.employeeSalaryTypeCode = "SLRT_00002" ? {  "id": $.rateCode,  "code": $.rateCode,  "salaryAdminPlanName": $.salaryAdminPlanName,  "salaryAdminPlanCode": $.salaryAdminPlanCode,  "gradeName": $.gradeName,  "gradeCode": $.gradeCode,  "stepName": $.stepName,  "stepCode": $.stepCode,  "amount": $.amount,  "wageClassification": $.wageClassificationCode,  "wageClassificationName": $.wageClassificationName,  "currencyCode": $.currencyCode,  "currencyName": $.currency.longName}  }} |'

  - path: /api/pr-employee-salaries/:id
    method: DELETE
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'employee-salaries/:{id}:'

  - path: /api/pr-employee-salaries
    method: POST
    model: postModal
    query:
    bodyTransform: '$merge([$map($keys($), function ($key){$not($key = "SalaryByTierMasterCode" and $.EmployeeSalaryTypeCode != "SLRT_00002") ? {$key: $lookup($,$key)}})])'
    upstreamConfig:
      method: POST
      response:
      path: 'employee-salaries'

customRoutes:
  - path: /api/pr-employee-salaries/group
    method: GET
    isExtendedFilter: true
    elemMatch: true
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'employee-salaries:employee-salary-group'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$ ~> | $ | {"id": null} |'
  - path: /api/pr-employee-salaries/group-detail/:employeeId/:ern/:employeeGroupCode
    method: GET
    isExtendedFilter: true
    elemMatch: true
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'employee-salaries:employee-salary-group:detail'
      query:
        Filter: '::{filter}::'
        OrderBy: ':{options.sort}:'
        employeeId: ':{employeeId}:'
        ern: ':{ern}:'
        employeeGroupCode: ':{employeeGroupCode}:'
      transform: '$'

  - path: /api/pr-employee-salaries/group-detail/:employeeId/:ern
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'employee-salaries:employee-salary-group:detail'
      query:
        Search: ':{search}:'
        OrderBy: ':{options.sort}:'
        Filter: '::{filter}::'
        employeeId: ':{employeeId}:'
        ern: ':{ern}:'
      transform: '$'

  - path: /api/pr-employee-salaries/:id/histories
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'employee-salaries/:{id}:/history'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
      transform: '$'

  - path: /api/pr-employee-salaries/by
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'employee-salaries/by'
      query:
        Filter: '::{filter}::'
      transform: '$'

  - path: /api/pr-employee-salaries/upload
    method: POST
    model: postModal

    query:
    dataType: 'formData'
    bodyTransform: '$merge([$map($keys($), function ($key){$not($key = "SalaryByTierMasterCode" and $.EmployeeSalaryTypeCode != "SLRT_00002") ? {$key: $lookup($,$key)}})])'
    upstreamConfig:
      method: POST
      response:
      path: 'employee-salaries'

  - path: /api/pr-employee-salaries/:id/upload
    method: POST
    model: postModal
    bodyTransform: '$merge([$map($keys($), function ($key){$not($key = "SalaryByTierMasterCode" and $.EmployeeSalaryTypeCode != "SLRT_00002") ? {$key: $lookup($,$key)}})])'
    query:
    dataType: 'formData'
    transform: '$'
    upstreamConfig:
      method: PUT
      response:
      path: 'employee-salaries/:{id}:'

  - path: /api/pr-employee-salaries/income-package
    method: GET
    model: packageIncome
    query:
    upstreamConfig:
      method: GET
      path: 'employee-salaries:income-package'
      query:
        employeeId: ":{employeeId}:"
        ern: ":{ern}:"
        effectiveDateFrom: ":{effectiveDate}:"
      response:
        dataType: object
      transform: '$'

  - path: /api/pr-employee-salaries/export
    method: GET
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: binary
      path: 'employee-salaries/export-excel'
      query:
        PageSize: '1000'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$'

  - path: /api/pr-employee-salaries/multidelete
    method: DELETE
    model: multiDeleteModel
    bodyTransform: '$.ids[]'
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'employee-salaries'
