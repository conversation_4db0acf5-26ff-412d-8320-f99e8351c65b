controller: pr-employees
upstream: ${{UPSTREAM_PR_URL}}

models:
  - name: _
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: indexFrom
    config:
      id:
        from: id
      employeeId:
        from: employeeId
      employeeGroupCode:
        from: employeeGroupCode
      employeeRecordNumber:
        from: employeeRecordNumber
        type: int
      fullName:
        from: fullName
        type: string
      companyCode:
        from: companyCode
        type: string
      terminateDate:
        from: terminateDate
        typeOptions:
          func: timestampToDateTime
      firstName:
        from: firstName
        type: string
      middleName:
        from: middleName
        type: string
      lastName:
        from: lastName
        type: string
      IsCombobox: 
        from: IsCombobox
        typeOptions:
          func: YNToBoolean
      effectiveDate:
        from: effectiveDate
        typeOptions:
          func: timestampToDateTime
  - name: _jobDataDetail
    config:
      id:
        from: id
      employeeId:
        from: employeeId
      employeeRecordNumber:
        from: employeeRecordNumber
      jobDataId:
        from: jobDataId
      fullName:
        from: fullName
      companyCode:
        from: companyCode
      companyName:
        from: company.longName
        type: string
      company:
        from: company
      legalEntityCode:
        from: legalEntityCode
      legalEntityName:
        from: legalEntity.longName
        type: string
      legalEntity:
        from: legalEntity
      businessUnitCode:
        from: businessUnitCode
      businessUnitName:
        from: businessUnit.longName
        type: string
      businessUnit:
        from: businessUnit
      divisionCode:
        from: divisionCode
      divisionName:
        from: division.longName
        type: string
      division:
        from: division
      departmentCode:
        from: departmentCode
      departmentName:
        from: department.longName
        type: string
      department:
        from: department
      employeeGroupCode:
        from: employeeGroupCode
      employeeGroupName:
        from: employeeGroup.longName
        type: string
      employeeGroup:
        from: employeeGroup
      jobCode:
        from: jobCode
      jobTitleName:
        from: jobTitle.longName
        type: string
      jobTitle:
        from: jobTitle
        type: string
      contractTypeCode:
        from: contractTypeCode
      contractTypeName:
        from: contractType.longName
        type: string
      contractType:
        from: contractType
      locationCode:
        from: locationCode
      locationName:
        from: location.longName
        type: string
      location:
        from: location
defaultQuery:
auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: personals
crudConfig:
  query:
  params:
    id:
      field: id
      type: string
      primary: true
    effectiveDate:
      field: effectiveDate
      type: string
    effectiveDateFilter:
      field: effectiveDateFilter
      type: string
    employeeId:
      field: employeeId
      type: string
    employeeRecordNumber:
      field: employeeRecordNumber
      type: string
  routes:
    exclude:
      - createManyBase
      - replaceOneBase

routes:
  - path: /api/pr-employees
    method: GET
    model: _
    isExtendedFilter: true
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'employees'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
        effectiveDate: ':{effectiveDate}:'
      transform: '$'
customRoutes:
  - path: /api/pr-employees/:employeeId/employee-record-number/:employeeRecordNumber/effective-date/:effectiveDateFilter
    method: GET
    model: _jobDataDetail
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'employees/:{employeeId}:/employee-record-number/:{employeeRecordNumber}:/effective-date/:{effectiveDateFilter}:'
      query:
      transform: '$'
  - path: /api/pr-employees/terminated
    method: GET
    model: _
    isExtendedFilter: true
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'employees/terminated'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        IsCombobox: ':{IsCombobox}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
        effectiveDate: ':{effectiveDate}:'
      transform: '$'