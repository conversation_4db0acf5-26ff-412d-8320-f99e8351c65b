controller: report-column-cal
upstream: ${{UPSTREAM_PR_URL}}

models:
  - name: _
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: totalPages
    config:
      id:
        from: id
        type: string
      code:
        from: elementCode
        type: string
      elementCode:
        from: elementCode
        type: string
      shortName:
        from: codeMapping
        type: string
      shortNameNotMultiLang:
        from: codeMapping
        type: string
      longName:
        from: name
        type: string
        typeOptions:
          func: stringToMultiLang
      longNameNotMultiLang:
        from: name
        type: string
      country:
        from: country.longName
        type: string
      countryCode:
        from: countryCode
        type: string
      operation:
        from: operatorCode
        type: string
      operationName:
        from: operator.longName
        type: string
      dataType:
        from: dataType
        type: string
      dataTypeName:
        from: dataTypeName
        type: string
      # Duplicate

      elementGroup:
        from: elementGroupCode
      elementGroupName:
        from: elementGroup.longName
      elementTypeName:
        from: elementType.longName
      elementType:
        from: elementTypeCode
      elementGroupCode:
        from: elementGroupCode
      elementTypeCode:
        from: elementTypeCode
      # elementType:
      #   from: elementItems.elementTypeCode
      # elementGroup:
      #   from: elementItems.elementGroupCode
      elementItems:
        from: elementItems
        type: array
        arrayChildren:
          elementGroup:
            from: elementGroupCode
          elementGroupName:
            from: elementGroup.longName
          elementType:
            from: elementTypeCode
          elementTypeName:
            from: elementType.longName
      encryption:
        from: encryption
        typeOptions:
          func: YNToBoolean
      generalElement:
        from: isGeneralElement
        typeOptions:
          func: YNToBoolean
      effectiveDate:
        from: effectiveDateFrom
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      status:
        from: enabled
        type: string
        typeOptions:
          func: YNToBoolean
      note:
        from: note
        type: string
        typeOptions:
          func: stringToMultiLang
      createdBy:
        from: createdBy
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      executeType:
        from: executeType
      executeTypeName:
        from: executeTypeName
      executeTypeNameTable:
        from: executeType
      linkingElement:
        from: linkingElementCode
      linkingElementTable:
        from: linkingElement.longName
      linkingElementCode:
        from: linkingElementCode
      linkingElements:
        from: linkingElements
        type: array
        arrayChildren:
          linkingElement:
            from: linkingElementCode
          linkingElementName:
            from: linkingElement.longName
      label:
        from: label
      key:
        from: key
      value:
        from: value
      CountryCodes:
        from: CountryCodes
      ElementGroupCodes:
        from: ElementGroupCodes
      LinkingElementCodes:
        from: LinkingElementCodes
      OperationCodes:
        from: OperationCodes
      DataTypeCodes:
        from: DataTypeCodes
      ExecuteTypes:
        from: ExecuteTypes
      ElementTypeCodes:
        from: ElementTypeCodes
      
auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: report-column-cal
crudConfig:
  query:
    sort:
      - field: updatedAt
        order: DESC
  params:
    id:
      field: id
      type: string
      primary: true
    code:
      field: elementCode
      type: string
    elementGroupCodeFilter:
      field: elementGroupCodeFilter
      type: string
    elementTypeCodeFilter:
      field: elementTypeCodeFilter
      type: string
  routes:
    exclude:
      - createManyBase
      - replaceOneBase
defaultQuery:

routes:
  - path: /api/report-column-cal
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'report-column-cal/list-for-grid'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
        CountryCodes : ':{CountryCodes}:'
        ElementGroupCodes: ':{ElementGroupCodes}:'
        LinkingElementCodes: ':{LinkingElementCodes}:'
        OperationCodes: ':{OperationCodes}:'
        DataTypeCodes: ':{DataTypeCodes}:'
        ExecuteTypes: ':{ExecuteTypes}:'
        ElementTypeCodes: ':{ElementTypeCodes}:'
      transform: '$merge([$, {"data":$map($.data, function($item) {$merge([$item,{"executeTypeNameTable" : $item.executeTypeName, "linkingElementTable": $map($item.linkingElements, function($_item) { $_item.linkingElementName }),"linkingElement": $map($item.linkingElements, function($_item) { $_item.linkingElementName }),"elementGroup": $map($item.elementItems, function($_item) { $_item.elementGroupName }),"elementGroupName": $map($item.elementItems, function($_item) { $_item.elementGroupName }),"elementType": $item.elementItems[0].elementTypeName,"elementTypeName": $item.elementItems[0].elementTypeName }])} )[]}])'

  - path: /api/report-column-cal/:id
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'report-column-cal/:{id}:'
      transform: '$merge([ $, {"linkingElement": $map(linkingElements, function($item) { $item.linkingElement })[], "elementGroup":$count(elementItems) = 1 ? [elementItems[0].elementGroup]: $map(elementItems, function($item) { $item.elementGroup }),"elementType": elementItems[0].elementType}])'

  - path: /api/report-column-cal
    method: POST
    model: _
    query:
    bodyTransform: '$merge([ $, {"linkingElements": $count($.linkingElementCode) >= 0 ? $map(linkingElementCode, function($item) { {  "linkingElementCode": $item  }    })[] : [], "elementItems": $count(elementGroupCode) = 1 ? [{ "elementGroupCode": elementGroupCode[0], "elementTypeCode": elementTypeCode }] : $map(elementGroupCode, function($item) { {  "elementGroupCode": $item  }    }) }])'
    upstreamConfig:
      method: POST

      path: 'report-column-cal'

  - path: /api/report-column-cal/:id
    model: _
    method: PATCH
    query:
    bodyTransform: '$merge([ $, {"linkingElements": $count($.linkingElementCode) >= 0 ? $map(linkingElementCode, function($item) { {  "linkingElementCode": $item  }    })[] : [], "elementItems": $count(elementGroupCode) = 1 ? [{ "elementGroupCode": elementGroupCode[0], "elementTypeCode": elementTypeCode }] : $map(elementGroupCode, function($item) { {  "elementGroupCode": $item  }    }) }])'

    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: 'report-column-cal/:{id}:'

  - path: /api/report-column-cal/:id
    method: DELETE
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'report-column-cal/:{id}:'
customRoutes:
  - path: /api/report-column-cal/master-data
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'report-column-cal:master-data'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
        ElementGroupCode: ':{elementGroupCodeFilter}:'
        ElementTypeCode: ':{elementTypeCodeFilter}:'
      transform: '$'
  - path: /api/report-column-cal/month-salary-data
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'report-column-cal:month-salary-data'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
        ElementGroupCode: ':{elementGroupCodeFilter}:'
        ElementTypeCode: ':{elementTypeCodeFilter}:'
      transform: '$'
  - path: /api/report-column-cal/:id/histories
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'report-column-cal/:{id}:/history'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
      transform: '$'
  - path: /api/report-column-cal/:id/clone
    method: POST
    model: _
    query:
    bodyTransform: '$merge([ $, {"linkingElements": $count($.linkingElementCode) > 0 ? $map(linkingElementCode, function($item) { {  "linkingElementCode": $item  }    }) : [], "elementItems": $count(elementGroupCode) = 1 ? [{ "elementGroupCode": elementGroupCode[0], "elementTypeCode": elementTypeCode }] : $map(elementGroupCode, function($item) { {  "elementGroupCode": $item  }    }) }])'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'report-column-cal/:{id}:/clone'
      transform: $
  - path: /api/report-column-cal/execute-type
    method: GET
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'report-column-cal:execute-type'
      query:
      transform: '$'

  - path: /api/report-column-cal/export
    method: GET
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: binary
      path: 'report-column-cal/export'
      query:
        OrderBy: ':{options.sort}:'
        PageSize: '1000'
        Search: ':{search}:'
        Filter: '::{filter}::'
        CountryCodes : ':{CountryCodes}:'
        ElementGroupCodes: ':{ElementGroupCodes}:'
        LinkingElementCodes: ':{LinkingElementCodes}:'
        OperationCodes: ':{OperationCodes}:'
        DataTypeCodes: ':{DataTypeCodes}:'
        ExecuteTypes: ':{ExecuteTypes}:'
        ElementTypeCodes: ':{ElementTypeCodes}:'
      transform: '$'
  - path: /api/report-column-cal/operator
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'report-column-cal:operator'
      query:
      transform: '$'
  - path: /api/report-column-cal/multidelete
    method: DELETE
    model: multiDeleteModel
    bodyTransform: '{"ids": $.ids[]}'
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'report-column-cal'
