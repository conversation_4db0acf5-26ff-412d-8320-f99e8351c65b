controller: salary-admin-plans
upstream: ${{UPSTREAM_PR_URL}}

models:
  - name: _
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: indexFrom
    config:
      id:
        from: id
        type: string
      code:
        from: code
        type: string
      readOnly:
        from: readOnly
        type: boolean
      shortName:
        from: shortName
        type: string
        typeOptions:
          func: stringToMultiLang
      name:
        from: name
        type: string
        typeOptions:
          func: stringToMultiLang
      companyName:
        from: companyScopes.longName
      companyCode:
        from: companyScopes.companyCode
      CompanyCodes:
        from: CompanyCodes
      companyScopes:
        from: companyScopes
      companyObj:
        from: companyObj
      effectiveDate:
        from: effectiveDateFrom
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      effectiveDateTo:
        from: effectiveDateTo
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      status:
        from: enabled
        type: string
        typeOptions:
          func: YNToBoolean
      note:
        from: note
        type: string
        typeOptions:
          func: stringToMultiLang
      effectiveDateFrom:
        from: effectiveDateFrom
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      createdBy:
        from: createdBy
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
defaultQuery:
auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: salary-admin-plans
crudConfig:
  query:
    sort:
      - field: updatedAt
        order: DESC
  params:
    id:
      field: id
      type: string
      primary: true
    code:
      field: code
      type: string
  routes:
    exclude:
      - createManyBase
      - replaceOneBase

routes:
  - path: /api/salary-admin-plans
    method: GET
    model: _

    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'salary-admin-plans'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        CompanyCodes: ':{CompanyCodes}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$merge([$, {"data":$map($.data, function($item) {$merge([$item,{"companyName": $map($item.companyScopes, function($v) {$v.company.longName})}])} )[]}])'

  - path: /api/salary-admin-plans/:id
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'salary-admin-plans/:{id}:'
      transform: '$ ~> | $ |
        {
          "companyObj": $map(companyScopes, function($value, $index) {
            {
              "label": $value.company.longName & " (" & $value.company.code & ")",
              "value":{"id": $value.company.id,
              "code": $value.companyCode
            }}
          })[]
        } |
        '

  - path: /api/salary-admin-plans
    method: POST
    model: _
    query:
    bodyTransform: '$merge([$,{"companyScopes": $map($.companyObj, function($value) { $exists($value.code) ? {"companyCode": $value.code }}) []}])'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'salary-admin-plans'
      transform: '$'

  - path: /api/salary-admin-plans/:id
    model: _
    method: PATCH
    query:
    bodyTransform: '$merge([$,{"companyScopes": $map($.companyObj, function($value) {  {"companyCode": $exists($value.value) ? $value.value.code : $value.code }}) []}])'
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: 'salary-admin-plans/:{id}:'

  - path: /api/salary-admin-plans/:id
    method: DELETE
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'salary-admin-plans/:{id}:'
customRoutes:
  - path: /api/salary-admin-plans/:id/histories
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'salary-admin-plans/:{id}:/history'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
      transform: '$ ~> | $ |
        {
          "companyObj": $map(companyScopes, function($value, $index) {
            {
              "label": $value.company.longName & " (" & $value.company.code & ")",
              "value":{"id": $value.company.id,
              "code": $value.companyCode
            }}
          })[]
        } |
        '

  - path: /api/salary-admin-plans/:id/clone
    method: POST
    model: _
    query:
    bodyTransform: '$merge([$,{"companyScopes": $map($.companyObj, function($value) {  {"companyCode": $exists($value.value) ? $value.value.code : $value.code }}) []}])'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'salary-admin-plans/:{id}:/clone'
      transform: $

  - path: /api/salary-admin-plans/export
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: binary
      path: 'salary-admin-plans/export'
      query:
        PageSize: '1000'
        OrderBy: ':{options.sort}:'
        CompanyCodes: ':{CompanyCodes}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$'

  - path: /api/salary-admin-plans/multidelete
    method: DELETE
    model: multiDeleteModel
    bodyTransform: '$.ids[]'
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'salary-admin-plans'
