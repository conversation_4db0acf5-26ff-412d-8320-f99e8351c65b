controller: salary-by-tiers
upstream: ${{UPSTREAM_PR_URL}}

models:
  - name: _
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: totalPages
    config:
      #FE
      id:
        from: id
        type: string
      name:
        from: name
        type: string
        typeOptions:
          func: stringToMultiLang
      code:
        from: code
        type: string
      shortName:
        from: shortName
        type: string
        typeOptions:
          func: stringToMultiLang
      payrollName:
        from: name
        type: string
        typeOptions:
          func: stringToMultiLang
      country:
        from: country.longName
        type: string
      countryCode:
        from: countryCode
        type: string
      countryId:
        from: country.id
      countryObj:
        from: $
        objectChildren:
          id:
            from: country.id
          code:
            from: countryCode
      companies:
        from: companies

      companyCodes:
        from: companies.companyCode

      legalEntityCodes:
        from: legalEntities.legalEntityCode
        
      company:
        from: company.longName
        type: string
      companyCode:
        from: companyCode
        type: string
      companyId:
        from: company.id
        type: string
      companyObj:
        from: $
        objectChildren:
          id:
            from: company.id
          code:
            from: companyCode
      legalEntityNames:
        from: LEGALENTITIES.LONGNAME
      legalEntities:
        from: legalEntities
      legalEntity:
        from: legalEntity.longName
        type: string
      legalEntityCode:
        from: legalEntityCode
        type: string
      legalEntityId:
        from: legalEntity.id
      legalEntityObj:
        from: $
        objectChildren:
          id:
            from: legalEntity.id
          code:
            from: legalEntityCode
      expectedSalaryIncreasePeriod:
        from: salaryIncreaseMonths
        type: string
      effectiveDate:
        from: effectiveDateFrom
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      status:
        from: enabled
        type: string
        typeOptions:
          func: YNToBoolean
      attachFile:
        from: attachFile
      attachFileName:
        from: attachFileName
      note:
        from: note
        type: string
        typeOptions:
          func: stringToMultiLang
      createdBy:
        from: createdBy
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      salaryByTierMasterId:
        from: salaryByTierMasterId
      salaryByTierMasterName:
        from: salaryByTierMasterName
      salaryAdminPlanCode:
        from: salaryAdminPlanCode
      salaryAdminPlanName:
        from: salaryAdminPlanName
      gradeCode:
        from: gradeCode
      gradeName:
        from: gradeName
      stepCode:
        from: stepCode
      stepName:
        from: stepName
      rateCode:
        from: rateCode
      exportAll:
        from: exportAll
      amount:
        from: amount
      currencyCode:
        from: currencyCode
      currencyName:
        from: currencyName
      wageClassification:
        from: wageClassification
      wageClassificationName:
        from: wageClassificationName
      salaryByTierMasterCode:
        from: salaryByTierMasterCode
      jobDataId:
        from: jobDataId
      details:
        from: details
        arrayChildren:
          id:
            from: id
          salaryByTierMasterId:
            from: salaryByTierMasterId
          salaryByTierMasterName:
            from: salaryByTierMasterName
          salaryAdminPlanCode:
            from: salaryAdminPlanCode
          salaryAdminPlanName:
            from: salaryAdminPlanName
          gradeCode:
            from: gradeCode
          gradeName:
            from: gradeName
          stepCode:
            from: stepCode
          stepName:
            from: stepName
          currencyCode:
            from: currencyCode
          currencyName:
            from: currencyName
          rateCode:
            from: rateCode
          wageClassification:
            from: wageClassification
          amount:
            from: amount
          createdBy:
            from: createdBy
          createdAt:
            from: createdAt
          updatedBy:
            from: updatedBy
          updatedAt:
            from: updatedAt
      detailIds:
        from: detailIds
      companyNames:
        from: COMPANIES.LONGNAME
  - name: postModal
    config:
      #FE
      id:
        from: id
        type: string
      code:
        from: Code
        type: string
      shortName:
        from: shortName
        type: string
        typeOptions:
          func: stringToMultiLang
      country:
        from: country.longName
        type: string
      countryObj:
        from: countryObj
      countryCode:
        from: countryCode
        type: string
      countryId:
        from: country.id
      countryObj:
        from: $
        objectChildren:
          id:
            from: country.id
          code:
            from: countryCode
      companies:
        from: Companies
      companyObj:
        from: companyObj
      company:
        from: company.longName
        type: string
      companyCode:
        from: companyCode
        type: string
      companyId:
        from: company.id
        type: string
      companyObj:
        from: $
        objectChildren:
          id:
            from: company.id
          code:
            from: companyCode
      legalEntities:
        from: legalEntities
      legalEntityObj:
        from: legalEntityObj
      legalEntity:
        from: legalEntity.longName
        type: string
      legalEntityCode:
        from: legalEntityCode
        type: string
      legalEntityId:
        from: legalEntity.id
      legalEntityObj:
        from: $
        objectChildren:
          id:
            from: legalEntity.id
          code:
            from: legalEntityCode
      isNoChangeFile:
        from: IsNoChangeFile
      expectedSalaryIncreasePeriod:
        from: SalaryIncreaseMonths
        type: string
      effectiveDate:
        from: EffectiveDateFrom
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      status:
        from: Enabled
        type: string
        typeOptions:
          func: YNToBoolean
      file:
        from: File
      attachFileDuplicate:
        from: AttachFile
      attachFileName:
        from: attachFileName
      attachFile:
        from: AttachFile
      note:
        from: Note
        type: string
        typeOptions:
          func: stringToMultiLang
      payrollName:
        from: Name
        type: string
        typeOptions:
          func: stringToMultiLang
      details:
        from: Details
auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: salary-by-tiers
crudConfig:
  query:
    sort:
      - field: updatedAt
        order: DESC
  params:
    id:
      field: id
      type: string
      primary: true
    exportAll:
      field: exportAll
      type: string
  routes:
    exclude:
      - createManyBase
      - replaceOneBase
defaultQuery:

routes:
  - path: /api/salary-by-tiers
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'salary-by-tiers'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
        jobDataId: '::{jobDataId}::'
      transform: '$merge([$, {"data":$map($.data, function($item) {$merge([$item,{"companyNames": $map($item.companies, function($v) {$v.company.longName}), "legalEntityNames": $map($item.legalEntities, function($v) {$v.legalEntity.longName})}])} )[]}])'

  - path: /api/salary-by-tiers/:id
    method: GET
    model: _

    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'salary-by-tiers/:{id}:'
      transform: '$ ~> | $ | { 
          "countryObj":countryCode ? {
            "label": country & " (" & countryCode &")", 
            "value": {"id": countryId, "code": countryCode }
          } ,
          "legalEntityObj":$count(legalEntities) > 0 ?$map(legalEntities, function($value, $index) {
                {
                  "label": $value.legalEntity.longName & " (" & $value.legalEntity.code & ")",
                  "value":{"id": $value.legalEntity.id,
                  "code": $value.legalEntityCode
                }}
              })[] : null ,
          "companyObj":$count(companies) > 0 ? $map(companies, function($value, $index) {
                {
                  "label": $value.company.longName & " (" & $value.company.code & ")",
                  "value":{"id": $value.company.id,
                  "code": $value.companyCode
                }}
              })[] : null ,
          "attachmentResults": $.attachFileName and  $.attachFile ?
          {"name": $.attachFileName , "url": "/api/salary-by-tiers/" & $.id, 
          "fileValue": $.attachFile, 
          "fileField": "AttachFile"  } 
          : null} |'

  - path: /api/salary-by-tiers
    method: POST
    model: _

    query:
    transform: '$'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'salary-by-tiers'
      transform: '$'

  - path: /api/salary-by-tiers/:id
    model: _
    method: PATCH
    query:
    transform: '$'
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: 'salary-by-tiers/:{id}:'
  - path: /api/salary-by-tiers/:id
    method: DELETE
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'salary-by-tiers/:{id}:'

customRoutes:
  - path: /api/salary-by-tiers/rate-code
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'salary-by-tiers/rate-code'
      query:
        amount: ':{amount}:'
        jobDataId: '::{jobDataId}::'
        Filter: '::{filter}::'
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
      transform: '$'

  - path: /api/salary-by-tiers/:id/histories
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'salary-by-tiers'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
      transform: '$'

  - path: /api/salary-by-tiers/upload
    method: POST
    model: postModal
    query:
    dataType: 'formData'
    bodyTransform: '$merge([$,{"Details" : $string($.Details),"countryCode" : $exists($.countryObj.value) ? $.countryObj.value.code : $.countryObj.code, "companies": $map($.companyObj, function($value) { {"companyCode": $exists($value.value) ? $value.value.code : $value.code }}) [],"legalEntities": $map($.legalEntityObj, function($value) { {"legalEntityCode": $exists($value.value) ? $value.value.code : $value.code }}) [] }])'
    upstreamConfig:
      method: POST
      response:
      path: 'salary-by-tiers'
      transform: '$'

  - path: /api/salary-by-tiers/:id/upload
    model: postModal
    method: POST
    dataType: 'formData'
    query:
    bodyTransform: '$merge([$,{"Details" : $string($.Details), "countryCode": $exists($.countryObj.value) ? $.countryObj.value.code : $.countryObj.code,"companies": $map($.companyObj, function($value) { {"companyCode": $exists($value.value) ? $value.value.code : $value.code }}) [],"legalEntities": $map($.legalEntityObj, function($value) { {"legalEntityCode": $exists($value.value) ? $value.value.code : $value.code }}) [] }])'
    upstreamConfig:
      response:
      method: PUT
      path: 'salary-by-tiers/:{id}:'
  - path: /api/salary-by-tiers/export
    method: GET
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: binary
      path: 'salary-by-tiers/export'
      query:
        OrderBy: ':{options.sort}:'
        PageSize: '1000'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$'
  - path: /api/salary-by-tiers/download-attach-file/:id
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: binary
      path: 'salary-by-tiers/download-attach-file/:{id}:'
      transform: '$'
  - path: /api/salary-by-tiers/export-details
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: binary
      query:
        exportAll: ':{exportAll}:'
        Filter: '::{filter}::'
      path: 'salary-by-tiers/export-details'
      transform: '$'
  - path: /api/salary-by-tiers/detail
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      query:
        Filter: '::{filter}::'
        jobDataId: '::{jobDataId}::'
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'

      path: 'salary-by-tiers/detail'
      transform: '$'
  - path: /api/salary-by-tiers/check-details
    method: POST
    model: _
    bodyTransform: '$'
    query:
      $and:
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'salary-by-tiers/check-details'
      transform: $

  - path: /api/salary-by-tiers/multidelete
    method: DELETE
    model: multiDeleteModel
    bodyTransform: '$.ids[]'
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'salary-by-tiers'
