controller: salary-formula-payroll-period-settings
upstream: ${{UPSTREAM_PR_URL}}

models:
  - name: _
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: totalPages
    config:
      id:
        from: id
        type: string
      employees:
        from: employees
      reportTypeId:
        from: reportTypeId
        type: string
      code:
        from: code
        type: string
      payrollPeriodSettingCode:
        from: code
        type: string
      country:
        from: country.longName
        type: string
      countryCode:
        from: countryCode
        type: string
      payrollPeriod:
        from: payrollPeriod.longName
        type: string
      payrollPeriodCode:
        from: payrollPeriodCode
        type: string
      payrollSubPeriod:
        from: name
        type: string
      company:
        from: company.longName
        type: string
      companyCode:
        from: companyCode
        type: string
      legalEntity:
        from: legalEntity.longName
        type: string
      legalEntityCode:
        from: legalEntityCode
        type: string
      payGroup:
        from: payGroup.longName
        type: string
      payGroupCode:
        from: payGroupCode
        type: string
      elementGroup:
        from: elementGroup.longName
        type: string
      elementGroupCode:
        from: elementGroupCode
        type: string
      elementType:
        from: salaryType.longName
        type: string
      elementTypeCode:
        from: salaryTypeCode
        type: string
      paymentDate:
        from: paymentDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      startDate:
        from: startDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      endDate:
        from: endDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      currency:
        from: currency.longName
        type: string
      currencyCode:
        from: currencyCode
        type: string
      totalEmployee:
        from: totalEmployee
        type: number
      totalCalculation:
        from: totalCalculation
        type: number
      totalFail:
        from: totalFail
        type: number
      totalNotCalculated:
        from: totalNotCalculated
        type: number
      totalProcessing:
        from: totalProcessing
        type: number
      totalCompleted:
        from: totalCompleted
        type: number
      totalLocked:
        from: totalLocked
        type: number
      version:
        from: version
        type: string
      revision:
        from: revision
        type: string
      month:
        from: month
        typeOptions:
          func: YMDToDateTime
      periodStatus:
        from: calculationStatus
        type: string
      periodStatusTable:
        from: payrollStatus
        type: string
      PayrollStatus:
        from: PayrollStatus
        type: string
      grossSalary:
        from: total_GrossSalary
        type: string
      netSalary:
        from: total_NetSalary
        type: string
      createdBy:
        from: createdBy
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      isImport:
        from: isImport
        typeOptions:
          func: YNToBoolean
  - name: _export_static_tab
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: totalPages
    config:
      id:
        from: Id
        type: string
      reportTypeId:
        from: reportTypeId
        type: string
      code:
        from: code
        type: string
      payrollPeriodSettingCode:
        from: code
        type: string
      country:
        from: country.longName
        type: string
      countryCode:
        from: countryCode
        type: string
      payrollPeriod:
        from: payrollPeriod.longName
        type: string
      payrollPeriodCode:
        from: payrollPeriodCode
        type: string
      payrollSubPeriod:
        from: name
        type: string
      company:
        from: company.longName
        type: string
      companyCode:
        from: companyCode
        type: string
      legalEntity:
        from: legalEntity.longName
        type: string
      legalEntityCode:
        from: legalEntityCode
        type: string
      payGroup:
        from: payGroup.longName
        type: string
      payGroupCode:
        from: payGroupCode
        type: string
      elementGroup:
        from: elementGroup.longName
        type: string
      elementGroupCode:
        from: elementGroupCode
        type: string
      elementType:
        from: salaryType.longName
        type: string
      elementTypeCode:
        from: salaryTypeCode
        type: string
      paymentDate:
        from: paymentDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      startDate:
        from: startDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      endDate:
        from: endDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      currency:
        from: currency.longName
        type: string
      currencyCode:
        from: currencyCode
        type: string
      totalEmployee:
        from: totalEmployee
        type: number
      totalFail:
        from: totalFail
        type: number
      totalNotCalculated:
        from: totalNotCalculated
        type: number
      totalProcessing:
        from: totalProcessing
        type: number
      totalCompleted:
        from: totalCompleted
        type: number
      totalLocked:
        from: totalLocked
        type: number
      version:
        from: version
        type: string
      revision:
        from: revision
        type: string
      month:
        from: month
        typeOptions:
          func: YMDToDateTime
      periodStatus:
        from: calculationStatus
        type: string
      PayrollStatus:
        from: PayrollStatus
        type: string
      grossSalary:
        from: total_GrossSalary
        type: string
      netSalary:
        from: total_NetSalary
        type: string
      createdBy:
        from: createdBy
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime

  - name: _tabTable
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: totalPages
    config:
      id:
        from: id
        type: string
      reportTypeId:
        from: ReportTypeId
        type: string
      employeeId:
        from: EmployeeId
        type: string
      employeeRecordNumber:
        from: COL1100
        type: string
      company:
        from: companyCode
        type: string
      payrollPeriodCode:
        from: PayrollPeriodCode
        type: string
      payrollPeriodSettingCode:
        from: PayrollPeriodSettingCode
        type: string
      month:
        from: MonthSalary
        typeOptions:
          func: YMDToDateTime
      periodStatus:
        from: CalculationStatus
        type: string
      grossSalary:
        from: TT_GrossSalary
        type: string
      haveDecision:
        from: MasterYN
        type: string
      fullName:
        from: COL2101
        type: string
      department:
        from: COL2102
        type: string
      workingDays:
        from: COL1101
        type: string
      actualWorkingDays:
        from: COL1102
        type: string
      employeeSalaryTypeCode:
        from: COL6100
        type: string


auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: salary-formula
crudConfig:
  query:
    sort:
      - field: updatedAt
        order: DESC
  params:
    id:
      field: id
      type: string
      primary: true
    reportTypeId:
      field: reportTypeId
      type: string
    companyCode:
      field: companyCode
      type: string
    payrollPeriodSettingCode:
      field: payrollPeriodSettingCode
      type: string
    month:
      field: month
      type: string
  routes:
    exclude:
      - createManyBase
      - replaceOneBase
defaultQuery:

routes:
  - path: /api/salary-formula-payroll-period-settings
    method: GET
    model: _

    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: "salary-formulas:payroll-period-settings"
      query:
        Page: ":{options.page}:"
        PageSize: ":{options.limit}:"
        OrderBy: ":{options.sort}:"
        Search: ":{search}:"
        Filter: "::{filter}::"
      transform: '$ ~> | $ | { "data": $map($.data , function($v) {$merge([$v , {"employeeId": $map($v.employees, function($v) {$v.employeeId})[], "employeeRecordNumber": $map($v.employees, function($v) {$v.employeeRecordNumber})[] ,"payrollPeriodForm" : {"label" : $v.payrollPeriod & " (" & $v.payrollPeriodCode & ")" , "value" : $v.payrollPeriodCode }   }  ])}  )[] } |'

  - path: /api/salary-formula-payroll-period-settings/:id
    method: GET
    model: _

    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: "salary-formulas:payroll-summaries/:{id}:"
      query:
      transform: '$ ~> | $ | {"employeeId": $map($.employees, function($v) {$v.employeeId})[], "employeeRecordNumber": $map($.employees, function($v) {$v.employeeRecordNumber})[]} |'

customRoutes:
  - path: /api/salary-formula-payroll-period-settings/export
    method: GET
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: binary
      path: 'salary-formulas/payroll-summaries/export'
      query:
        PageSize: '1000'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$'
  - path: /api/salary-formula-payroll-period-settings-static-tab/export
    method: GET
    model: _export_static_tab
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: binary
      path: 'salary-formulas/payroll-summaries/export-static-tab'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$'
  # - path: /api/salary-formula-payroll-period-settings/:reportTypeId/calculate-employee
  #   method: GET
  #   model: _tabTable

  #   query:
  #   upstreamConfig:
  #     method: GET
  #     response:
  #       dataType: paginated
  #     path: "report-types/:{reportTypeId}:/salary-formula-result:calculate-employees"
  #     query:
  #       Page: ":{options.page}:"
  #       PageSize: ":{options.limit}:"
  #       OrderBy: ":{options.sort}:"
  #       Search: ":{search}:"
  #       CompanyCode: ":{companyCode}:"
  #       ReportTypeId: ":{reportTypeId}:"
  #       PayrollPeriodSettingCode: ":{payrollPeriodSettingCode}:"
  #       Month: ":{month}:"
  #       PayrollPeriodCode: ":{payrollPeriodCode}:"
  #       CalculationStatus: ":{calculateStatus}:"
  #     transform: '$'

  # - path: /api/salary-formula-payroll-period-settings/:reportTypeId/calculate-employee/:employeeId
  #   method: GET
  #   model: _

  #   query:
  #   upstreamConfig:
  #     method: GET
  #     response:
  #       dataType: paginated
  #     path: "report-types/:{reportTypeId}:/salary-formula-result:calculate-employees/:{employeeId}:"
  #     query:
  #       CompanyCode: ":{companyCode}:"
  #       ReportTypeId: ":{reportTypeId}:"
  #       PayrollPeriodSettingCode: ":{payrollPeriodSettingCode}:"
  #       Month: ":{month}:"
  #       PayrollPeriodCode: ":{payrollPeriodCode}:"
  #       CalculateStatus: ":{calculateStatus}:"
  #       EmployeeRecordNumber: ":{employeeRecordNumber}:"
  #       employeeId: ":{employeeId}:"
  #     transform: '$'

  # - path: /api/salary-formula-payroll-period-settings/:reportTypeId/result-setting
  #   method: GET
  #   model: _

  #   query:
  #   upstreamConfig:
  #     method: GET
  #     response:
  #       dataType: paginated
  #     path: "report-types/:{reportTypeId}:/salary-formula-result:setting"
  #     query:
  #       reportTypeId: ":{reportTypeId}:"
  #       id: ":{id}:"
  #     transform: '$'
