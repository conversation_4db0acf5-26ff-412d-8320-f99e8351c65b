controller: salary-standard-employees
upstream: ${{UPSTREAM_PR_URL}}

models:
  - name: _
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: totalPages
    config:
      #FE
      id:
        from: id
        type: string
      code:
        from: salaryStandardCode
        type: string
      codeObj:
        from: $
        objectChildren:
          code:
            from: salaryStandardCode
      shortName:
        from: salaryStandard.shortName
        type: string
        typeOptions:
          func: stringToMultiLang
      longName:
        from: salaryStandard.name
        type: string
        typeOptions:
          func: stringToMultiLang
      parameterName:
        from: salaryStandard.name
        type: string
      employeeId:
        from: employeeId
        type: string
      fullName:
        from: fullName
        type: string
      employeeGroupCode:
        from: employeeGroupCode
      employeeGroup:
        from: employeeGroup.longName
      employeeRecordNumber:
        from: employeeRecordNumber
      country:
        from: countryCode
        type: string
      countryCode:
        from: countryCode
        type: string

      companyName:
        from: company.longName
        type: string

      legalEntityName:
        from: legalEntity.longName
        type: string

      businessUnitName:
        from: businessUnit.longName
        type: string

      divisionName:
        from: division.longName
        type: string

      departmentName:
        from: department.longName
        type: string

      jobTitle:
        from: jobLevel.longName
        type: string

      level:
        from: levelCode
        type: string
      levelCode:
        from: levelCode
      contractTypeName:
        from: contractType.longName
        type: string
      contractTypeCode:
        from: contractCode
      location:
        from: location.longName
        type: string
      typeName:
        from: salaryStandard.wageClassificationName
        type: string
      typeCode:
        from: salaryStandard.wageClassification
        type: string
      typeNameList:
        from: wageClassification.longName
        type: string
      type:
        from: type
        type: string
      wageClassification:
        from: wageClassification
        type: string
      currency:
        from: salaryStandard.currencyName
        type: string
      currencyName:
        from: currency.longName
      currencyCode:
        from: salaryStandard.currencyCode
        type: string
      currencyCodeAddOn:
        from: currencyCode
        type: string
      amount:
        from: amount
      amountCurrency:
        from: amount
      amountNonCurrency:
        from: amount
      amountFilter:
        from: Amount
      startDate:
        from: startDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      endDate:
        from: endDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      status:
        from: enabled
        type: string
        typeOptions:
          func: YNToBoolean
      note:
        from: note
        type: string
        typeOptions:
          func: stringToMultiLang
      createdBy:
        from: createdBy
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      salaryStandardCodeFilter:
        from: SalaryStandard.Code
        type: string
      salaryStandardNameFilter:
        from: SalaryStandard.Name
        type: string
      currencyCodeFilter:
        from: Currency
        type: string
      updatedBy:
        from: updatedBy
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime

auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: salary-standard-employees
crudConfig:
  query:
    sort:
      - field: updatedAt
        order: DESC
  params:
    id:
      field: id
      type: string
      primary: true
  routes:
    exclude:
      - createManyBase
      - replaceOneBase
defaultQuery:

routes:
  - path: /api/salary-standard-employees
    method: GET
    model: _
    isExtendedFilter: true
    elemMatch: true
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'salary-standard-employees'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$merge([$, { "data" : $map($.data, function($v) { $merge([$v, { "currencyName": $v.currency, "typeNameList": $v.typeName }]) } )[] }])'

  - path: /api/salary-standard-employees/:id
    method: GET
    model: _

    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'salary-standard-employees/:{id}:'
      transform: '$merge([ $,{"currencyCodeEdit" : currencyCodeAddOn, "codeObj": code ? { "label": parameterName & " (" & code &")", "value": {"code": code , "typeCode": typeCode} },"employee": {"label": $join($filter([$.employeeId,$.employeeGroupCode,$string($.employeeRecordNumber), $.fullName], $boolean), " - "), "value": {"employeeId": $.employeeId,"code": $.employeeId, "employeeRecordNumber": $.employeeRecordNumber, "jobDataId": $.jobDataId}}  } ])'

  - path: /api/salary-standard-employees
    method: POST
    model: _

    query:
    transform: '$'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'salary-standard-employees'
      transform: '$'

  - path: /api/salary-standard-employees/:id
    model: _
    method: PATCH
    query:
    transform: '$'
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: 'salary-standard-employees/:{id}:'

  - path: /api/salary-standard-employees/:id
    method: DELETE
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'salary-standard-employees/:{id}:'
customRoutes:
  - path: /api/salary-standard-employees/:id/histories
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'salary-standard-employees'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
      transform: '$'
  - path: /api/salary-standard-employees/export
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: binary
      path: 'salary-standard-employees/export'
      query:
        PageSize: '1000'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$'
  - path: /api/salary-standard-employees/multidelete
    method: DELETE
    model: multiDeleteModel
    bodyTransform: '$.ids[]'
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'salary-standard-employees'