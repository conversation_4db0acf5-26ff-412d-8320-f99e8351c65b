controller: salary-standard-structures
upstream: ${{UPSTREAM_PR_URL}}

models:
  - name: _
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: totalPages
    config:
      #FE
      id:
        from: id
        type: string
      code:
        from: salaryStandardCode
        type: string
      codeObj:
        from: $
        objectChildren:
          code:
            from: salaryStandardCode
      parameterName:
        from: parameterName.longName
        type: string
      country:
        from: country.longName
        type: string
      countryCode:
        from: countryCode
        type: string
      company:
        from: company.longName
        type: string
      companyCode:
        from: companyCode
        type: string
      companyId:
        from: company.id
        type: string
      companyObj:
        from: $
        objectChildren:
          id:
            from: company.id
          code:
            from: companyCode
      legalEntity:
        from: legalEntity.longName
        type: string
      legalEntityCode:
        from: legalEntityCode
        type: string
      legalEntityId:
        from: legalEntity.id
      legalEntityObj:
        from: $
        objectChildren:
          id:
            from: legalEntity.id
          code:
            from: legalEntityCode
      businessUnit:
        from: businessUnit.longName
        type: string
      businessUnitCode:
        from: businessUnitCode
        type: string
      businessUnitId:
        from: businessUnit.id
      businessUnitObj:
        from: $
        objectChildren:
          id:
            from: businessUnit.id
          code:
            from: businessUnitCode
      division:
        from: division.longName
        type: string
      divisionCode:
        from: divisionCode
        type: string
      divisionId:
        from: division.id
      divisionObj:
        from: $
        objectChildren:
          id:
            from: division.id
          code:
            from: divisionCode
      department:
        from: department.longName
        type: string
      departmentCode:
        from: departmentCode
        type: string
      departmentId:
        from: department.id
      departmentObj:
        from: $
        objectChildren:
          id:
            from: department.id
          code:
            from: departmentCode
      localExpat:
        from: localExpat
        type: string
      localExpatName:
        from: localExpat.longName
        type: string
      residencyStatus:
        from: residencyStatus
        type: string
      employeeGroup:
        from: employeeGroup.longName
        type: string
      employeeGroupCode:
        from: employeeGroupCode
        type: string
      employeeSubGroup:
        from: employeeSubGroup.longName
        type: string
      employeeSubGroupCode:
        from: employeeSubGroupCode
        type: string
      jobTitle:
        from: jobTitle.longName
        type: string
      jobTitleCode:
        from: jobTitleCode
      jobTitleId:
        from: jobTitle.id
      jobTitleObj:
        from: $
        objectChildren:
          id:
            from: jobTitle.id
          code:
            from: jobTitleCode
      location:
        from: location.longName
        type: string
      locationCode:
        from: locationCode
      locationId:
        from: location.id
      locationObj:
        from: $
        objectChildren:
          id:
            from: location.id
          code:
            from: locationCode
      type:
        from: wageClassification
        type: string
      typeName:
        from: type.longName
      currency:
        from: currency.longName
        type: string
      currencyCode:
        from: currencyCode
        type: string
      amount:
        from: amount
      payGroups:
        from: payGroups
      payGroupNames:
        from: payGroups.longName
        type: string
      payGroupCodes:
        from: payGroups.payGroupCode
        type: string
      payGroupCodesFilter:
        from: PayGroupCodes
        type: string
      startDate:
        from: startDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      endDate:
        from: endDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      status:
        from: enabled
        type: string
        typeOptions:
          func: YNToBoolean
      note:
        from: note
        type: string
        typeOptions:
          func: stringToMultiLang
      createdBy:
        from: createdBy
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime

auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: salary-standard-structures
crudConfig:
  query:
    sort:
      - field: updatedAt
        order: DESC
  params:
    id:
      field: id
      type: string
      primary: true
  routes:
    exclude:
      - createManyBase
      - replaceOneBase
defaultQuery:

routes:
  - path: /api/salary-standard-structures
    method: GET
    model: _

    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'salary-standard-structures'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
        PayGroupCodes: ':{payGroupCodesFilter}:'
      transform: '$merge([$, {"data":$map($.data, function($item) {$merge([$item,{"payGroupNames":$count($item.payGroups) = 1?[$item.payGroups[0].longName]: $map($item.payGroups, function($_item) { $_item.longName })}])} )[]}])'

  - path: /api/salary-standard-structures/:id
    method: GET
    model: _

    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'salary-standard-structures/:{id}:'
      transform: '$merge(
            [ $, 
            { 
            "codeObj": code ? {
              "label": parameterName & " (" & code &")", 
              "value": {"code": code }
            },
            "legalEntityObj":legalEntityCode ? {
              "label": legalEntity & " (" & legalEntityCode &")", 
              "value": {"id": legalEntityId, "code": legalEntityCode }
            } ,
            "companyObj":companyCode ? {
              "label": company & " (" & companyCode &")", 
              "value": {"id": companyId, "code": companyCode }
            } ,
            "businessUnitObj": businessUnitCode ? {
              "label": businessUnit & " (" & businessUnitCode &")", 
              "value": {"id": businessUnitId, "code": businessUnitCode }
            } ,
            "divisionObj": divisionCode ? {
              "label": division & " (" & divisionCode &")", 
              "value": {"id": divisionId, "code": divisionCode }
            },
            "departmentObj": departmentCode ? {
              "label": department & " (" & departmentCode &")", 
              "value": {"id": departmentId, "code": departmentCode }
            },
            "jobTitleObj": jobTitleCode ? {
              "label": jobTitle & " (" & jobTitleCode &")", 
              "value": {"id": jobTitleId, "code": jobTitleCode }
            },
            "locationObj": locationCode ? {
              "label": location & " (" & locationCode &")", 
              "value": {"id": locationId, "code": locationCode }
            },
            "payGroups":$count(payGroups) = 1?[payGroups[0].payGroupCode]: $map(payGroups, function($item) { $item.payGroupCode })
            }
            ])'

  - path: /api/salary-standard-structures
    method: POST
    model: _
    query:
    bodyTransform: '$merge([ $, { "payGroups": $count(payGroups) = 1 ? [{ "payGroupCode": payGroups[0]}] : $map(payGroups, function($item) { {  "payGroupCode": $item  }    }) }])'

    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'salary-standard-structures'
      transform: '$'

  - path: /api/salary-standard-structures/:id
    model: _
    method: PATCH
    query:
    bodyTransform: '$merge([ $, { "payGroups": $count(payGroups) = 1 ? [{ "payGroupCode": payGroups[0]}] : $map(payGroups, function($item) { {  "payGroupCode": $item  }    }) }])'
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: 'salary-standard-structures/:{id}:'

  - path: /api/salary-standard-structures/:id
    method: DELETE
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'salary-standard-structures/:{id}:'
customRoutes:
  - path: /api/salary-standard-structures/:id/histories
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'salary-standard-structures'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
      transform: '$'
  - path: /api/salary-standard-structures/export
    method: GET
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: binary
      path: 'salary-standard-structures/export'
      query:
        OrderBy: ':{options.sort}:'
        PageSize: '1000'
        Search: ':{search}:'
        Filter: '::{filter}::'
        PayGroupCodes: ':{payGroupCodesFilter}:'
      transform: '$'
  - path: /api/salary-standard-structures/multidelete
    method: DELETE
    model: multiDeleteModel
    bodyTransform: '$.ids[]'
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'salary-standard-structures'