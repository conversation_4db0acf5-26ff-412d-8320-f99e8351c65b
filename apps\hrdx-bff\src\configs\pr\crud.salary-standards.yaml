controller: salary-standards
upstream: ${{UPSTREAM_PR_URL}}

models:
  - name: _
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: count
    config:
      id:
        from: id
        type: string
      # name BFF
      code:
        # name BE
        from: code
        # type BE
        type: string
      company:
        from: company
      effectiveDate:
        from: effectiveDateFrom
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      effectiveDateTo:
        from: effectiveDateTo
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      status:
        from: enabled
        type: string
        typeOptions:
          func: YNToBoolean
      shortName:
        from: shortName
        type: string
        typeOptions:
          func: stringToMultiLang
      longName:
        from: name
        type: string
        typeOptions:
          func: stringToMultiLang
      note:
        from: note
        type: string
        typeOptions:
          func: stringToMultiLang
      countryName:
        from: country.longName
        type: string
      countryCode:
        from: countryCode
      countryId:
        from: country.id
      countryObj:
        from: $
        objectChildren:
          id:
            from: country.id
          code:
            from: countryCode
      companyName:
        from: company.longName
        type: string
      companyCode:
        from: companyCode
      companyScopes:
        from: companyScopes
      companyNames:
        from: companyScopes.longName
      companyCodes:
        from: companyScopes.companyCode
      CompanyCodesFilter:
        from: CompanyCodesFilter
      companyObjs:
        from: companyObjs
      companyId:
        from: company.id
        type: string
      companyObj:
        from: $
        objectChildren:
          id:
            from: company.id
          code:
            from: companyCode
      type:
        from: type
      typeName:
        from: type.longName
        type: string
      typeCode:
        from: wageClassification
        type: string
      currency:
        from: currency.longName
        type: string
      currencyCode:
        from: currencyCode
      currencyObj:
        from: $
        objectChildren:
          code:
            from: currencyCode
      createdBy:
        from: createdBy
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      IsCombobox:
        from: IsCombobox
        typeOptions:
          func: YNToBoolean
      hasCompany:
        from: hasCompany
  - name: exportModel
    config:
      code:
        from: code
        type: string
      shortName:
        from: shortName
        typeOptions:
          func: stringToMultiLang
      longName:
        from: name
        typeOptions:
          func: stringToMultiLang
      countryName:
        from: country
      companyName:
        from: company
      typeName:
        from: type
      currency:
        from: currency
      status:
        from: enabled
        typeOptions:
          func: YNToBoolean
      note:
        from: note
        typeOptions:
          func: stringToMultiLang
      updatedBy:
        from: updatedBy
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      columnNames:
        from: columnNames
        typeOptions:
          func: listKeyFields
      feColumnNames:
        from: feColumnNames
      id:
        from: id
auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: salary-standards
crudConfig:
  query:
    sort:
      - field: updatedAt
        order: DESC
  params:
    id:
      field: id
      type: string
      primary: true
    code:
      field: code
      type: string
  routes:
    exclude:
      - createManyBase
      - replaceOneBase
defaultQuery:

routes:
  - path: /api/salary-standards
    method: GET
    model: _

    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'salary-standards'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        CompanyCodes: ':{CompanyCodesFilter}:'
        IsCombobox: ':{IsCombobox}:'
        hasCompany: ':{hasCompany}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: "$merge([$, {'data' : $map($.data, function($item) { $merge([$item, { 'companyNames': $map($item.companyScopes, function($v) {$v.company.longName})[],  'companyFullName': $item.company.longName ? $item.company.longName & ' (' & $item.company.shortName & ')' : ''}]) })[]}])"

  - path: /api/salary-standards/:id
    method: GET
    model: _

    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'salary-standards/:{id}:'
      transform: '$merge([
                      $,
                      {
                          "companyObjs": $map($.companyScopes, function($value, $index) {
                                              $value.companyCode ? {
                                                  "label": $value.company.longName & " (" & $value.companyCode &")",
                                                  "value": $value.companyCode
                                              }
                                          })[]
                      },
                      {
                          "companyObj":companyCode ?
                          {
                              "label": companyName & " (" & companyCode &")",
                              "value": {"id": companyId, "code": companyCode }
                          }
                      },
                      {
                          "countryObj":countryCode ?
                          {
                              "label": countryName & " (" & countryCode &")",
                              "value": {"id": countryId, "code": countryCode }
                          }
                      },
                      {
                          "currencyObj":currencyCode ?
                          {
                              "label": currency & " (" & currencyCode &")",
                              "value": {"code": currencyCode }
                          }
                      }
                  ])'

  - path: /api/salary-standards
    method: POST
    model: _

    query:
    bodyTransform: '$merge([$,{"companyScopes": $map($.companyObjs, function($value) { {"companyCode": $value.value ? $value.value : $value } }) []}])'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'salary-standards'
      transform: '$'

  - path: /api/salary-standards/:id
    model: _
    method: PATCH
    query:
    bodyTransform: '$merge([$,{"companyScopes": $map($.companyObjs, function($value) {  {"companyCode": $value.value ? $value.value : $value } }) []}])'
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: 'salary-standards/:{id}:'

  - path: /api/salary-standards/:id
    method: DELETE
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'salary-standards/:{id}:'


customRoutes:
  - path: /api/salary-standards/:id/histories
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'salary-standards/:{id}:/history'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
      transform: '$map($ , function($item) {
                    $merge([
                      $item,
                      {
                          "companyObjs": $map($item.companyScopes, function($value, $index) {
                              $value.companyCode ? {
                                  "label": $value.company.longName & " (" & $value.companyCode &")",
                                  "value": $value.companyCode
                              }
                          })[]
                      },
                      {
                          "companyObj": $item.companyCode ?
                          {
                              "label": $item.companyName & " (" & $item.companyCode &")",
                              "value": {"id": $item.companyId, "code": $item.companyCode }
                          }
                      },
                      {
                          "countryObj":$item.countryCode ?
                          {
                              "label": $item.countryName & " (" & $item.countryCode &")",
                              "value": {"id": $item.countryId, "code": $item.countryCode }
                          }
                      },
                      {
                          "currencyObj":$item.currencyCode ?
                          {
                              "label": $item.currency & " (" & $item.currencyCode &")",
                              "value": {"code": $item.currencyCode }
                          }
                      }
                    ])
                })[]'

  - path: /api/salary-standards/:id/clone
    method: POST
    model: _
    query:
    bodyTransform: '$merge([$,{"companyScopes": $map($.companyObjs, function($value) {  {"companyCode": $value.value ? $value.value : $value } }) []}])'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'salary-standards/:{id}:/clone'
      transform: $
  - path: /api/salary-standards/export
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: binary
      path: 'salary-standards/export'
      query:
        PageSize: '1000'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$'
  - path: /api/salary-standards/multidelete
    method: DELETE
    model: multiDeleteModel
    bodyTransform: '$.ids[]'
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'salary-standards'

  - path: /api/salary-standards/combobox
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'salary-standards/combobox'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        CompanyCodes: ':{CompanyCodesFilter}:'
        hasCompany: ':{hasCompany}:'
        IsCombobox: ':{IsCombobox}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: "$"
