controller: termination-settlement-statuses
upstream: ${{UPSTREAM_PR_URL}}

models:
  - name: _
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: totalPages
    config:
      #FE
      id:
        from: id
        type: string
      code:
        from: code
        type: string
      employeeId:
        from: employeeId
      jobDataId:
        from: jobDataId
      employeeRecordNumber:
        from: employeeRecordNumber
      terminationDate:
        from: terminationDate
        typeOptions:
          func: timestampToDateTime
      terminationSettlementDateGet:
        from: terminationSettlementDate
        typeOptions:
          func: timestampToDateTime
      terminationSettlementDate:
        from: terminationSettlementDate
        typeOptions:
          func: timestampToDateTime
      effectiveDateOfTermination:
        from: effectiveDateOfTermination
        typeOptions:
          func: timestampToDateTime
      actionReasonName:
        from: actionReason.longName
      actionReasonCode:
        from: actionReasonCode
      fullName:
        from: fullName
      employeeGroupCode:
        from: employeeGroupCode
      employeeGroup:
        from: employeeGroup
      employeeGroupName:
        from: employeeGroup.longName
      jobTitle:
        from: jobTitle
      jobTitleCode:
        from: jobTitleCode
      jobTitleName:
        from: jobTitle.longName
      jobCodeName:
        from: jobCode.longName
      company:
        from: company
      companyCode:
        from: companyCode
      companyName:
        from: company.longName
      legalEntity:
        from: legalEntity
      legalEntityCode:
        from: legalEntityCode
      legalEntityName:
        from: legalEntity.longName
      currency:
        from: currency
      currencyCode:
        from: currencyCode
      currencyName:
        from: currency.longName
      division:
        from: division
      divisionCode:
        from: divisionCode
      divisionName:
        from: division.longName
      department:
        from: department
      departmentCode:
        from: departmentCode
      departmentName:
        from: department.longName
      location:
        from: location
      locationCode:
        from: locationCode
      locationName:
        from: location.longName
      businessUnit:
        from: businessUnit
      businessUnitCode:
        from: businessUnitCode
      businessUnitName:
        from: businessUnit.longName
      status:
        from: status
        type: string
        typeOptions:
          func: YNToBoolean
      statusFilter:
        from: status
        type: string
      note:
        from: note
        type: string
        typeOptions:
          func: stringToMultiLang
      effectiveDateFrom:
        from: effectiveDateFrom
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      createdBy:
        from: createdBy
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      ern:
        from: ern
auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: termination-settlement-statuses
crudConfig:
  query:
    sort:
      - field: updatedAt
        order: DESC
  params:
    id:
      field: id
      type: string
      primary: true

  routes:
    exclude:
      - createManyBase
      - replaceOneBase
defaultQuery:

routes:
  - path: /api/termination-settlement-statuses
    method: GET
    model: _
    isExtendedFilter: true
    elemMatch: true
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'termination-settlement-statuses'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$'

  - path: /api/termination-settlement-statuses/:id
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'termination-settlement-statuses/:{id}:'
      transform: '$ ~> | $ | {"terminationDate": {"label": $fromMillis($toMillis($.terminationDate),"[D01]/[M01]/[Y0001]") , "value": $.terminationDate},"terminationSettlementDateGet" : $.effectiveDateOfTermination ,"employee": {"label": $join($filter([$.employeeId,$.employeeGroupCode,$string($.employeeRecordNumber), $.fullName], $boolean), " - "), "value": {"employeeId": $.employeeId,"code": $.employeeId, "employeeRecordNumber": $.employeeRecordNumber, "jobDataId": $.jobDataId}}} |'

  - path: /api/termination-settlement-statuses
    method: POST
    model: _
    query:
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'termination-settlement-statuses'
      transform: '$'

  - path: /api/termination-settlement-statuses/:id
    model: _
    method: PATCH
    query:
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: 'termination-settlement-statuses/:{id}:'

  - path: /api/termination-settlement-statuses/:id
    method: DELETE
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'termination-settlement-statuses/:{id}:'

customRoutes:
  - path: /api/termination-settlement-statuses/:id/histories
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'termination-settlement-statuses'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
      transform: '$'
  - path: /api/termination-settlement-statuses/export
    method: GET
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: binary
      path: 'termination-settlement-statuses/export'
      query:
        Page: ':{options.page}:'
        OrderBy: ':{options.sort}:'
        PageSize: ':{options.limit}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$'
  - path: /api/termination-settlement-statuses/multidelete
    method: DELETE
    model: multiDeleteModel
    bodyTransform: '$.ids[]'
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'termination-settlement-statuses'
