controller: un-fixed-allowance-and-deductions
upstream: ${{UPSTREAM_PR_URL}}

models:
  - name: _
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: totalPages
    config:
      #FE
      id:
        from: id
        type: string
      employeeId:
        from: employeeId
        type: string
      employeeIdView:
        from: employeeId
        type: string
      fullName:
        from: fullName
        type: string
      employeeRecordNumber:
        from: employeeRecordNumber
      employeeRecordNumberView:
        from: employeeRecordNumber
      elementName:
        from: element.longName
        type: string
      elementCode:
        from: code
        type: string
      deduction:
        from: code
        type: string
      earning:
        from: code
        type: string
      type:
        from: unFixedAllowanceAndDeductionType
        type: string
      typeName:
        from: unFixedAllowanceAndDeductionTypeModel.longName
        type: string
      instance:
        from: instanceId
        type: string
      haveDecision:
        from: hasDecision
      decisionNumber:
        from: decisionNumber
      signer:
        from: decisionSignerId
      signerName:
        from: decisionSignerName
      signDate:
        from: decisionSignDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      attachment:
        from: attachFile
      dataResource:
        from: dataResource
      company:
        from: company.longName
        type: string
      companyCode:
        from: companyCode
        type: string
      legalEntity:
        from: legalEntity.longName
        type: string
      legalEntityCode:
        from: legalEntityCode
        type: string
      businessUnit:
        from: businessUnit.longName
        type: string
      businessUnitCode:
        from: businessUnitCode
        type: string
      division:
        from: division.longName
        type: string
      divisionCode:
        from: divisionCode
        type: string
      department:
        from: department.longName
        type: string
      departmentCode:
        from: departmentCode
        type: string
      jobTitle:
        from: job.longName
        type: string
      jobTitleCode:
        from: jobCode
      level:
        from: careerStream.longName
        type: string
      levelCode:
        from: careerStreamCode
      contractType:
        from: contractType.longName
        type: string
      contractTypeCode:
        from: contractTypeCode
      location:
        from: location.longName
        type: string
      employeeGroupCode:
        from: employeeGroupCode
        type: string
      employeeGroup:
        from: employeeGroup.longName
        type: string
      locationCode:
        from: locationCode
      currency:
        from: currency
        type: string
      currencyName:
        from: currency.longName
        type: string
      currencyCode:
        from: currencyCode
        type: string
      amount:
        from: value
      startDate:
        from: startDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      endDate:
        from: endDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      note:
        from: note
        type: string
      createdBy:
        from: createdBy
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      creator:
        from: createdBy
      createdTime:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      lastEditor:
        from: updatedBy
      lastEdit:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      employeeId_ern:
        from: employeeId_ern
        type: string
  - name: postModal
    config:
      #FE
      id:
        from: id
        type: string
      employeeId:
        from: employeeId
        type: string
      fullName:
        from: fullName
        type: string
      employeeRecordNumber:
        from: employeeRecordNumber
      elementName:
        from: element.longName
        type: string
      elementCode:
        from: code
        type: string
      deduction:
        from: code
        type: string
      earning:
        from: code
        type: string
      type:
        from: unFixedAllowanceAndDeductionType
        type: string
      instance:
        from: instanceId
        type: string
      haveDecision:
        from: hasDecision
      decisionNumber:
        from: decisionNumber
      signer:
        from: decisionSignerId
      signerName:
        from: decisionSignerName
      signDate:
        from: decisionSignDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      attachment:
        from: AttachFile
      dataResource:
        from: dataResource
      currency:
        from: currency
        type: string
      currencyCode:
        from: currencyCode
        type: string
      amount:
        from: value
      startDate:
        from: startDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      endDate:
        from: endDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      note:
        from: note
        type: string
      createdBy:
        from: createdBy
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      creator:
        from: createdBy
      createdTime:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      lastEditor:
        from: updatedBy
      lastEdit:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime

auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: un-fixed-allowance-and-deductions
crudConfig:
  query:
    sort:
      - field: updatedAt
        order: DESC
  params:
    id:
      field: id
      type: string
      primary: true
  routes:
    exclude:
      - createManyBase
      - replaceOneBase
defaultQuery:

routes:
  - path: /api/un-fixed-allowance-and-deductions
    method: GET
    model: _
    isExtendedFilter: true
    elemMatch: true
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'un-fixed-allowance-and-deductions'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$'

  - path: /api/un-fixed-allowance-and-deductions/:id
    method: GET
    model: _

    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'un-fixed-allowance-and-deductions/:{id}:'
      transform: '$ ~> | $ | {"currencyCodeEdit" : currencyCode, "attachmentResults": $.attachment ? {"name": $.attachment , "url": "/api/pr-files/download", "fileValue": $.attachment, "fileField": "AttachFile"  } : null , "employeeDetail" : {"employeeId" : $.employeeId , "employeeRecordNumber" : $.employeeRecordNumber, "employeeGroupCode" : $.employeeGroupCode },"signer": {"label": signer & "-" & signerName, "value": signer}, "elementCode": {"label": elementName & " (" & elementCode & ")", "value": elementCode}, "employee": {"label": $join($filter([$.employeeId,$.employeeGroupCode,$string($.employeeRecordNumber), $.fullName], $boolean), " - "), "value": {"employeeId": $.employeeId,"code": $.employeeId, "employeeRecordNumber": $.employeeRecordNumber, "jobDataId": $.jobDataId}}} |'

  - path: /api/un-fixed-allowance-and-deductions
    method: POST
    model: postModal
    dataType: 'formData'

    query:
    transform: '$'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'un-fixed-allowance-and-deductions'
      transform: '$'

  - path: /api/un-fixed-allowance-and-deductions/:id
    model: _
    method: PATCH
    query:
    transform: '$'
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: 'un-fixed-allowance-and-deductions/:{id}:'

  - path: /api/un-fixed-allowance-and-deductions/:id
    method: DELETE
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'un-fixed-allowance-and-deductions/:{id}:'

customRoutes:
  - path: /api/un-fixed-allowance-and-deductions/:id/histories
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'un-fixed-allowance-and-deductions'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
      transform: '$'

  - path: /api/un-fixed-allowance-and-deductions/upload
    method: POST
    model: postModal
    dataType: 'formData'

    query:
    transform: '$'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'un-fixed-allowance-and-deductions'
      transform: '$'

  - path: /api/un-fixed-allowance-and-deductions/:id/upload
    model: postModal
    method: POST
    dataType: 'formData'
    query:
    transform: '$'
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: 'un-fixed-allowance-and-deductions/:{id}:'
  - path: /api/un-fixed-allowance-and-deductions/export
    method: GET
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: binary
      path: 'un-fixed-allowance-and-deductions/export'
      query:
        PageSize: '1000'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$'
  - path: /api/un-fixed-allowance-and-deductions/multidelete
    method: DELETE
    model: multiDeleteModel
    bodyTransform: '$.ids[]'
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'un-fixed-allowance-and-deductions'
