controller: account-email
upstream: ${{UPSTREAM_SYS_URL}}

models:
  - name: _
    pagination:
      data:
        from: items
      count:
        from: count
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: indexFrom
    config:
      id:
        from: id
        type: string
      employeeId:
        from: employeeId
        type: string
      code:
        from: code
        type: string
      fullName:
        from: fullName
        type: string
      firstName:
        from: firstName
        type: string
      middleName:
        from: middleName
        type: string
      lastName:
        from: lastName
        type: string
      company:
        from: companyCode
        type: string
      companyName:
        from: companyName
        type: string
      mailGroup:
        from: mailGroup
        type: string
      requestId:
        from: requestId
        type: string
      requestTypeCode:
        from: requestTypeCode
        type: string
      requestTypeValue:
        from: requestTypeValue
        type: string
      email:
        from: email
        type: string
      newCompanyName:
        from: newCompanyName
        type: string
      newCompanyCode:
        from: newCompanyCode
        type: string
      oldCompanyName:
        from: oldCompanyName
        type: string
      oldCompanyCode:
        from: oldCompanyCode
        type: string
      newDisplayName:
        from: newDisplayName
        type: string
      displayName:
        from: displayName
        type: string
      accountExpiredCode:
        from: accountExpiredCode
        type: string
      accountExpiredValue:
        from: accountExpiredValue
        type: string
      reason:
        from: reason
        type: string
      syncDate:
        from: syncDate
        ttype: timestamp
        typeOptions:
          func: timestampToDateTime
      endDate:
        from: endDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      cccd:
        from: cccd
        type: string
      note:
        from: note
        type: string
      mailboxTypeCode:
        from: mailboxTypeCode
        type: string
      mailboxTypeName:
        from: mailboxTypeName
        type: string
      status:
        from: enabled
        type: string
        typeOptions:
          func: YNToBoolean
      createdBy:
        from: createdBy
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
        from: updatedBy
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime

auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: account-email
crudConfig:
  query:
    sort:
      - field: updatedAt
        order: DESC
  params:
    id:
      field: id
      type: string
      primary: true
    code:
      field: code
      type: string
  routes:
    exclude:
      - createManyBase
      - replaceOneBase
defaultQuery:

routes:
  # list table
  - path: /api/account-email
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
        dataKey: data
      path: 'account-email'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'


  # detail
  - path: /api/account-email/:id
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'account-email/:{id}:'
      transform: '$'

  # create
  - path: /api/account-email
    method: POST
    model: _
    query:
    transform: '$'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'account-email'
      transform: '$'

  # update
  - path: /api/account-email/:id
    method: PATCH
    model: _
    query:
    transform: '$'
    upstreamConfig:
      method: PUT
      response:
        dataType: object
      path: 'account-email/:{id}:'

  # delete
  # - path: /api/account-email/:id
  #   method: DELETE
  #   query:
  #     $and:
  #   upstreamConfig:
  #     method: DELETE
  #     path: 'account-email/:{id}:'
customRoutes:
  - path: /api/account-email/export
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: binary
      path: 'account-email/export:export'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        Search: ':{search}:'
        OrderBy: ':{options.sort}:'
        Filter: '::{filter}::'
      transform: '$'
  # - path: /api/account-email/template
  #   model: _
  #   method: GET
  #   query:
  #   transform: '$'
  #   upstreamConfig:
  #     method: GET
  #     response:
  #       dataType: binary
  #     path: 'account-email/template'
  # - path: /api/account-email/validate
  #   model: _
  #   method: POST
  #   dataType: 'formData'
  #   query:
  #   transform: '$'
  #   upstreamConfig:
  #     method: POST
  #     path: 'account-email/validate'
