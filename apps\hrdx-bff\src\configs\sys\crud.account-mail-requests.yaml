controller: account-mail-requests
upstream: ${{UPSTREAM_SYS_URL}}

models:
  - name: _
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: totalPages
    config:
      id:
        from: id
        type: string
      code:
        from: requestId
        type: string
      requestId:
        from: requestId
        type: string
      requestSubject:
        from: requestSubject
        type: string
      region:
        from: region
      regionCode:
        from: regionCode
        type: string
      regionName:
        from: regionName
        type: string
      location:
        from: location.name
        type: string
      locationFilter:
        from: regionCode
        type: string
      requesterCode:
        from: requester
      requesterName:
        from: requesterName
      requesterUserName:
        from: requesterUser.fullName
      requesterUserEmail:
        from: requesterUser.email
      accountRequester:
        from: accountRequester
        type: string
      requestTypeCode:
        from: requestTypeCode
        type: string
      requestTypeName:
        from: requestTypeName
        type: string
      approver:
        from: approver
      accountApprover:
        from: accountApprover
        type: string
      userApprover:
        from: userApprover.fullName
      approverEmail:
        from: userApprover.email
      mailboxTypeCode:
        from: mailboxTypeCode
        type: string
      mailBoxType:
        from: mailboxTypeCode
        type: string
      mailboxTypeName:
        from: mailboxTypeName
        type: string
      attachFile:
        from: attachFile
      fileName:
        from: fileName
        type: string
      approvalStatusCode:
        from: approvalStatusCode
        type: string
      approvalStatus:
        from: approvalStatusCode
        type: string
      approvalStatusName:
        from: approvalStatusName
        type: string
      createdBy:
        from: createdBy
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
        type: string
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      details:
        from: details
        arrayChildren:
          employeeId:
            from: employeeId
          firstName:
            from: firstName
          lastName:
            from: lastName
          companyName:
            from: companyName
          mailGroup:
            from: mailGroup
          companyCode:
            from: companyCode
          CreateAccountType:
            from: CreateAccountType
          from:
            from: from
          idx:
            from: idx
          isDuplicate:
            from: isDuplicate
          companySlect:
            from: companySlect
          RequestTypeCode:
            from: RequestTypeCode
          email:
            from: email
          inactiveDateView:
            from: inactiveDateView
          inactiveDate:
            from: inActiveDate
            type: timestamp
            typeOptions:
              func: timestampToDateTime
          disableReason:
            from: disableReason
          inactiveDatePicker:
            from: inactiveDatePicker
          reason:
            from: reason
          middleName:
            from: middleName
          note:
            from: note
          fullName:
            from: fullName
          newDisplayName:
            from: newDisplayName
          mailboxTypeCode:
            from: mailboxTypeCode
          mailboxTypeName:
            from: mailboxTypeName
          accountExpired:
            from: accountExpired
          mailboxTypeSlect:
            from: mailboxTypeSlect
          newCompanyCode:
            from: newCompanyCode
          newCompanyName:
            from: newCompanyName
          newCompanySlect:
            from: newCompanySlect
          oldCompanyCode:
            from: oldCompanyCode
          oldCompanyName:
            from: oldCompanyName
          oldCompanySlect:
            from: oldCompanySlect
          reactiveEndDate:
            from: reActiveEndDate
            type: timestamp
            typeOptions:
              func: timestampToDateTime
          reactiveEndDatePicker:
            from: reactiveEndDatePicker
          accountExpriedCode:
            from: accountExpriedCode
          addMailGroup:
            from: addMailGroup
          reActiveNote:
            from: reActiveNote
          reactiveEndDateView:
            from: reactiveEndDateView
          displayName:
            from: displayName
          keepEndDateView:
            from: keepEndDateView
          keepEndDatePicker:
            from: keepEndDatePicker
          keepEndDate:
            from: keepEndDate
            type: timestamp
            typeOptions:
              func: timestampToDateTime
          keepReason:
            from: keepReason
          keepNote:
            from: keepNote
          reactiveNote:
            from: reActiveNote
      briefData:
        from: briefData
      isEdit:
        from: isEdit
        type: string
        typeOptions:
          func: YNToBoolean
      isDelete:
        from: isDelete
        type: string
        typeOptions:
          func: YNToBoolean

  - name: _modelPost
    config:
      id:
        from: id
        type: string
      code:
        from: requestId
        type: string
      requestId:
        from: requestId
        type: string
      requestSubject:
        from: requestSubject
        type: string
      regionCode:
        from: regionCode
        type: string
      location:
        from: location.name
        type: string
      requester:
        from: requester
      requesterUserName:
        from: requesterUser.fullName
      requesterUserEmail:
        from: requesterUser.email
      accountRequester:
        from: accountRequester
        type: string
      requestTypeCode:
        from: requestTypeCode
        type: string
      RequestTypeCode:
        from: RequestTypeCode
        type: string
      approver:
        from: approver
      accountApprover:
        from: accountApprover
        type: string
      userApprover:
        from: userApprover.fullName
      mailboxTypeCode:
        from: mailboxTypeCode
        type: string
      mailboxTypeName:
        from: mailboxTypeName
        type: string
      attachFile:
        from: file
      fileName:
        from: fileName
        type: string
      approvalStatusCode:
        from: approvalStatusCode
        type: string
      approvalStatusName:
        from: approvalStatusName
        type: string
      createdBy:
        from: createdBy
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
        type: string
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      details:
        from: details
        arrayChildren:
          employeeId:
            from: employeeId
          firstName:
            from: firstName
          lastName:
            from: lastName
          companyName:
            from: companyName
          mailGroup:
            from: mailGroup
          addMailGroup:
            from: addMailGroup
          reActiveNote:
            from: reActiveNote
          companyCode:
            from: companyCode
          CreateAccountType:
            from: CreateAccountType
          from:
            from: from
          idx:
            from: idx
          isDuplicate:
            from: isDuplicate
          companySlect:
            from: companySlect
          RequestTypeCode:
            from: RequestTypeCode
          email:
            from: email
          inactiveDateView:
            from: inactiveDateView
          inactiveDate:
            from: inactiveDate
            type: timestamp
            typeOptions:
              func: timestampToDateTime
          inactiveDatePicker:
            from: inactiveDatePicker
          reason:
            from: reason
          middleName:
            from: middleName
          note:
            from: note
          fullName:
            from: fullName
          newDisplayName:
            from: newDisplayName
          mailboxTypeCode:
            from: mailboxTypeCode
          mailboxTypeName:
            from: mailboxTypeName
          accountExpired:
            from: accountExpired
          mailboxTypeSlect:
            from: mailboxTypeSlect
          newCompanyCode:
            from: newCompanyCode
          newCompanyName:
            from: newCompanyName
          newCompanySlect:
            from: newCompanySlect
          oldCompanyCode:
            from: oldCompanyCode
          oldCompanyName:
            from: oldCompanyName
          oldCompanySlect:
            from: oldCompanySlect
          reactiveNote:
            from: ReactiveNote
          reactiveEndDate:
            from: reactiveEndDate
            type: timestamp
            typeOptions:
              func: timestampToDateTime
          reactiveEndDatePicker:
            from: reactiveEndDatePicker
          accountExpriedCode:
            from: accountExpriedCode
          reactiveEndDateView:
            from: reactiveEndDateView
          displayName:
            from: displayName
          keepEndDateView:
            from: keepEndDateView
          keepEndDatePicker:
            from: keepEndDatePicker
          keepEndDate:
            from: keepEndDate
            type: timestamp
            typeOptions:
              func: timestampToDateTime
          keepReason:
            from: keepReason
          keepNote:
            from: keepNote
      _tabActive:
        from: _tabActive
        type: string
      mailboxType:
        from: mailboxTypeCode
        type: string

  - name: _DELETE
    config:
      ids:
        from: ids
      requestIds:
        from: requestIds

auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: account-mail-requests
crudConfig:
  query:
    sort:
      - field: updatedAt
        order: DESC
  params:
    id:
      field: id
      type: string
      primary: true
    code:
      field: code
      type: string
  routes:
    exclude:
      - createManyBase
      - replaceOneBase
defaultQuery:

routes:
  - path: /api/account-mail-requests
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
        dataKey: data
      path: 'account-mail-requests'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$ ~> | $.data | {"approvalStatus": { "type": $.approvalStatusCode, "label": $.approvalStatusName }, "requesterUser": $.requesterUserName &" - "& $.requesterUserEmail, "regionName": $.region.name, "requesterName": $.requesterUserName &" - "& $.requesterUserEmail } |'

  - path: /api/account-mail-requests/:id
    method: GET
    model: _

    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'account-mail-requests/:{id}:'
      transform: '$ ~> | $ | { "approver": {"value": $.approver, "label": $.approver &" - "& $.approverEmail}, "requester": {"value": $.requesterCode, "label": $.requesterUserName &" - "& $.requesterUserEmail},"requesterUser": $.requesterUserName &" - "& $.requesterUserEmail, "mailboxType": $.mailboxTypeCode ,"details": $map($.details, function($d){$merge([$d, {"companyName": $d.companyName &" - "& $d.companyCode}])})[]} |'

  - path: /api/account-mail-requests
    method: POST
    model: _

    query:
    transform: '$'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'account-mail-requests'
      transform: '$'

  - path: /api/account-mail-requests/:id
    model: _
    method: PATCH
    query:
    transform: '$'
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: 'account-mail-requests/:{id}:'

  - path: /api/account-mail-requests/:id
    method: DELETE
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'account-mail-requests/:{id}:'

customRoutes:
  - path: /api/account-mail-requests/:code/histories
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'account-mail-requests/:{code}:/history'
      # query:
      #   requestId: ':{requestId}:'
      transform: '$ ~> | $ | { "mailboxType": $.mailboxTypeCode, "approvalStatus": $.approvalStatusCode, "approvalStatusDetail": $.approvalStatusCode = "CREATED" ? "Created" : $.approvalStatusCode = "DELIVERED" ? "Transferred UService" : $.approvalStatusCode = "REJECTED" ? "Rejected" : $.approvalStatusCode = "APPROVED" ? "Approved", "attachmentResults": $.fileName and  $.attachFile ? {"fileName": $.fileName , "url": "/api/sys-files/download", "key": $.attachFile } : null } |'

  - path: /api/account-mail-requests/upload
    method: POST
    model: _modelPost
    dataType: formData
    bodyTransform: '$ ~> | $ | { "requester": $.requester.empId ? $trim($.requester.empId) : $trim($.requester.value.empId), "accountRequester": $.requester.id, "createAccountType": $._tabActive = "hrList" ? "FromHRList" : "Manual", "details": $string($.details) } |'
    query:
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'account-mail-requests'
      transform: '$'

  - path: /api/account-mail-requests/disable-accounts/upload
    method: POST
    model: _modelPost
    dataType: formData
    query:
    bodyTransform: '$ ~> | $ | { "requester": $.requester.empId ? $trim($.requester.empId) : $trim($.requester.value.empId), "accountRequester": $.requester.id, "createAccountType": $._tabActive = "hrList" ? "FromHRList" : "Manual", "details": $string($.details) } |'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'account-mail-requests/disable-accounts'
      transform: '$'

  - path: /api/account-mail-requests/reactive-accounts/upload
    method: POST
    model: _modelPost
    dataType: formData
    query:
    bodyTransform: '$ ~> | $ | { "requester": $.requester.empId ? $trim($.requester.empId) : $trim($.requester.value.empId),"accountRequester": $.requester.id, "createAccountType": $._tabActive = "hrList" ? "FromHRList" : "Manual", "details": $string($.details) } |'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'account-mail-requests/reactive-accounts'
      transform: '$'

  - path: /api/account-mail-requests/keep-accounts/upload
    method: POST
    model: _modelPost
    dataType: formData
    query:
    bodyTransform: '$ ~> | $ | { "requester": $.requester.empId ? $trim($.requester.empId) : $trim($.requester.value.empId), "accountRequester": $.requester.id, "createAccountType": $._tabActive = "hrList" ? "FromHRList" : "Manual", "details": $string($.details) } |'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'account-mail-requests/keep-accounts'
      transform: '$'

  - path: /api/account-mail-requests/submit/upload
    method: POST
    model: _modelPost
    dataType: formData
    query:
    bodyTransform: '$ ~> | $ | { "requester": $.requester.empId ? $trim($.requester.empId) : $trim($.requester.value.empId), "accountRequester": $.requester.id, "createAccountType": $._tabActive = "hrList" ? "FromHRList" : "Manual", "details": $string($.details),  "approver": $.approver ? $.approver : $trim($.requester.empId)  } |'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'account-mail-requests/submit'
      transform: '$'

  - path: /api/account-mail-requests/:id/upload
    model: _modelPost
    method: POST
    dataType: formData
    query:
    bodyTransform: '$ ~> | $ | { "requester": $.requester.empId ? $trim($.requester.empId) : $trim($.requester.value.empId), "accountRequester": $.requester.id, "createAccountType": $._tabActive = "hrList" ? "FromHRList" : "Manual", "details": $string($.details) } |'
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: 'account-mail-requests/:{id}:'

  - path: /api/account-mail-requests/disable-accounts/:id/upload
    model: _modelPost
    method: POST
    dataType: formData
    query:
    bodyTransform: '$ ~> | $ | { "requester": $.requester.empId ? $trim($.requester.empId) : $trim($.requester.value.empId),  "createAccountType": $._tabActive = "hrList" ? "FromHRList" : "Manual", "details": $string($.details), "approver": $trim($.requester.empId) } |'
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: 'account-mail-requests/disable-accounts'

  - path: /api/account-mail-requests/reactive-accounts/:id/upload
    model: _modelPost
    method: POST
    dataType: formData
    query:
    bodyTransform: '$ ~> | $ | { "requester": $.requester.empId ? $trim($.requester.empId) : $trim($.requester.value.empId),  "createAccountType": $._tabActive = "hrList" ? "FromHRList" : "Manual", "details": $string($.details) } |'
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: 'account-mail-requests/reactive-accounts'

  - path: /api/account-mail-requests/keep-accounts/:id/upload
    model: _modelPost
    method: POST
    dataType: formData
    query:
    bodyTransform: '$ ~> | $ | { "requester": $.requester.empId ? $trim($.requester.empId) : $trim($.requester.value.empId),  "createAccountType": $._tabActive = "hrList" ? "FromHRList" : "Manual", "details": $string($.details) } |'
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: 'account-mail-requests/keep-accounts'

  - path: /api/account-mail-requests/export
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: binary
      path: 'account-mail-requests/export:export'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$'


  - path: /api/account-mail-requests/multidelete
    method: DELETE
    model: _DELETE
    request:
    upstreamConfig:
      method: DELETE
      path: 'account-mail-requests'
