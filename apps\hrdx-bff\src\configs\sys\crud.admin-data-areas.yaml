controller: admin-data-areas
upstream: ${{UPSTREAM_SYS_URL}}

models:
  - name: _
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: indexFrom
    config:
      id:
        from: id
        type: string
      code:
        from: code
        type: string
      name:
        from: name
        type: string
        typeOptions:
          func: stringToMultiLang
      companyCode:
        from: $
        objectChildren:
          value:
            from: companyCode
          label:
            from: companyName,companyCode
            typeOptions:
              func: fieldsToNameCode
      companyName:
        from: companyName
      structureLevel:
        from: structureLevel
      description:
        from: note
        type: string
        typeOptions:
          func: stringToMultiLang
      notAuthorizedDisplay:
        from: isNoPermission
        typeOptions:
          func: YNToBoolean
      notDecentralizeYourselfDisplay:
        from: isNoYourSelfPermission
        typeOptions:
          func: YNToBoolean
      notAuthorized:
        from: isNoPermission
        typeOptions:
          func: YNToBoolean
      notDecentralizeYourself:
        from: isNoYourSelfPermission
        typeOptions:
          func: YNToBoolean
      includesSubordinates:
        from: isIncludesSubordinates
        typeOptions:
          func: YNToBoolean
      status:
        from: enabled
        typeOptions:
          func: YNToBoolean
      isUsed:
        from: isUsed
        typeOptions:
          func: YNToBoolean
      creator:
        from: createdBy
      createdTime:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      editor:
        from: updatedBy
      editedTime:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      ruleCode:
        from: ruleCode
        type: string
      permissionWith:
        from: permissionWith
        type: string
      dataAreaDetails:
        from: dataAreaDetails
        type: array
      dataAreaParams:
        from: dataAreaParams
      permissionsWithLabel:
        from: ruleCode
      employee:
        from: employee
      file:
        from: File
      employees:
        from: employees
  - name: _DELETE
    config:
auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: admin-data-areas
crudConfig:
  query:
    sort:
      - field: editedTime
        order: DESC
  params:
    id:
      field: id
      type: string
      primary: true
    code:
      field: code
      type: string
  routes:
    exclude:
      - createManyBase
      - replaceOneBase
defaultQuery:

routes:
  # list table
  - path: /api/admin-data-areas
    method: GET
    model: _

    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
        dataKey: data
      path: 'admin-data-areas'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$ ~> | $.data | {    "permissionsWithLabel": $.ruleCode = "CN" ? "Individual" : $.ruleCode = "CTTT" ? "Direct superior" : $.ruleCode = "CTGT" ? "Indirect superior" : $.ruleCode = "ORG" ? "Structure" : "Employee list",    "notAuthorizedDisplay": {        "label": $.notAuthorized = true ? "Yes" : "No",         "type": $.notAuthorized ? "success" : "default"    },     "notDecentralizeYourselfDisplay": {        "label":$.notDecentralizeYourself = true ? "Yes" : "No",         "type": $.notDecentralizeYourself ? "success" : "default"    },    "criteria": ($newArray := $reduce(        $.dataAreaDetails,        function($acc, $item) {            $merge([                $acc,                $item.dataAreaParamName ?                {                $item.dataAreaParamName:( $value := $item.paramValueDisplay ? $item.paramValueDisplay : $item.dataAreaParamValue;                $lookup($acc, $item.dataAreaParamName) ? $lookup($acc, $item.dataAreaParamName) & "; " & $value  : $value)                } : {}            ])        }, {}     );     $filterArray := $filter($newArray, function($v){$boolean($v)});    $.includesSubordinates ? $append($filterArray, {"Include in direct staff": "Yes"}) : $filterArray )}|'

  # # detail
  - path: /api/admin-data-areas/:id
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'admin-data-areas/:{id}:'
      transform: '$ ~> | $ | { "companyName": $.companyName & ($.companyCode ? (" (" & $.companyCode.value & ")") : ""),     "notAuthorizedDisplay": {"label": $.notAuthorized = true ? "Yes" : "No", "type": $.notAuthorized ? "success" : "default"},     "notDecentralizeYourselfDisplay": {"label":$.notDecentralizeYourself = true ? "Yes" : "No", "type": $.notDecentralizeYourself ? "success" : "default"},     "permissionsWithLabel": $.ruleCode = "CN" ? "Individual" : $.ruleCode = "CTTT" ? "Direct superior" : $.ruleCode = "CTGT" ? "Indirect superior" : $.ruleCode = "ORG" ? "Structure" : "Employee list" } |'

  # create
  - path: /api/admin-data-areas
    method: POST
    model: _
    query:
    bodyTransform: '(
      $param_key := $.dataAreaParams.value;
      $nvs_Code := "employee" in $param_key ? $single($.dataAreaParams, function($v) { $v.value = "employee" }).id;
      $current_key := $keys($.dataAreaDetails);
      $rawDataAreaDetails := $merge(
      $map($current_key, function($key) {
      $key = "department" or $key = "company" or $key = "businessunit" or $key = "legal" ? { $key: $lookup($.dataAreaDetails, $key) } : { $key: $lookup($.dataAreaDetails, $key) }
      })
      );
      $dataAreaDetails := $.ruleCode = "NVS" ?
      [
      $map($.dataAreaDetails.userSelect, function($item) {
      { "dataAreaParamId": $nvs_Code, "dataAreaParamValue": $item.employeeId, "employeeRecordNumber":$item.employeeRecordNumber }
      })
      ] :
      [
      $reduce(
      [
      $map($current_key, function($key) {
      $key in $param_key ?
      [
      $map($lookup($rawDataAreaDetails, $key), function($item) {
      $item?
      {
      "dataAreaParamId": $single($.dataAreaParams, function($v) { $v.value = $lowercase($key) }).id,
      "dataAreaParamValue": $item.value ? $item.value : $item
      }
      })
      ] : []
      })
      ], $append, []
      )
      ];
      $ruleCodeUpdated := ($.isNoYourSelfPermission="Y" or $.isNoPermission="Y") and ($.ruleCode = "CN" or $.ruleCode = "CTTT" or $.ruleCode = "CTGT") ? null : $.ruleCode;

      $merge([
      $map($keys($), function($key) {
      $not($key = "dataAreaParams" or $key = "dataAreaDetails") ? { $key: $lookup($, $key) } : {}
      }),
      { "ruleCode": $ruleCodeUpdated , "structureLevel": $.dataAreaDetails._structureLevel, "dataAreaDetails": $dataAreaDetails }
      ]))'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'admin-data-areas'
      transform: '$'

  # update
  - path: /api/admin-data-areas/:id
    method: PATCH
    model: _
    query:
    bodyTransform: '(      $param_key := $.dataAreaParams.value;      $nvs_Code := "employee" in $param_key ? $single($.dataAreaParams, function($v) { $v.value = "employee" }).id;      $current_key := $keys($.dataAreaDetails);      $rawDataAreaDetails := $merge(      $map($current_key, function($key) {      $key = "department" or $key = "company" or $key = "businessunit" or $key = "legal" ? { $key: $lookup($.dataAreaDetails, $key) } : { $key: $lookup($.dataAreaDetails, $key) }      })      );      $dataAreaDetails := $.ruleCode = "NVS" ?      [      $map($.dataAreaDetails.userSelect, function($item) {      { "dataAreaParamId": $nvs_Code, "dataAreaParamValue": $item.employeeId, "employeeRecordNumber":$item.employeeRecordNumber }      })      ] :      [      $reduce(      [      $map($current_key, function($key) {      $key in $param_key ?      [      $map($lookup($rawDataAreaDetails, $key), function($item) {      $item?      {      "dataAreaParamId": $single($.dataAreaParams, function($v) { $v.value = $lowercase($key) }).id,      "dataAreaParamValue": $item.value ? $item.value : $item      }      })      ] : []      })      ], $append, []      )      ];      $ruleCodeUpdated := ($.isNoYourSelfPermission="Y" or $.isNoPermission="Y") and ($.ruleCode = "CN" or $.ruleCode = "CTTT" or $.ruleCode = "CTGT") ? null : $.ruleCode;      $merge([      $map($keys($), function($key) {      $not($key = "dataAreaParams" or $key = "dataAreaDetails") ? { $key: $lookup($, $key) } : {}      }),      { "ruleCode": $ruleCodeUpdated , "structureLevel": $.dataAreaDetails._structureLevel, "dataAreaDetails": $dataAreaDetails }      ]))'
    upstreamConfig:
      method: PUT
      response:
        dataType: object
      path: 'admin-data-areas/:{id}:'
      transform: '$'

  # delete
  - path: /api/admin-data-areas/:id
    method: DELETE
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'admin-data-areas/:{id}:'

customRoutes:
  - path: /api/data-area-params
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'data-area-params'
      transform: '$'
  #export
  - path: /api/admin-data-areas/export
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: binary
      path: 'admin-data-areas/export:export'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$'
  #import
  - path: /api/admin-data-areas/import
    model: _
    method: POST
    dataType: 'formData'
    query:
    transform: '$'
    upstreamConfig:
      method: POST
      path: 'admin-data-areas/import'
  - path: /api/admin-data-areas/template
    model: _
    method: GET
    query:
    transform: '$'
    upstreamConfig:
      method: GET
      response:
        dataType: binary
      path: 'admin-data-areas/template'

  - path: /api/admin-data-areas/infos
    model: _
    method: GET
    query:
    transform: '$'
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'admin-data-areas/infos'
      query:
        companyCode: '::{companyCode}::'
        enabled: '::{status}::'
      transform: '$ ~> | $ | { "criteria":      $.includesSubordinates = true        ? [{ "Include in direct staff": "Yes" }]        : [$reduce(            $.dataAreaDetails,            function($acc, $da) {              $merge([                $acc,                {                  $da.dataAreaParamName:                    $lookup($acc, $da.dataAreaParamName)                      ? $lookup($acc, $da.dataAreaParamName) & "; " & (                          $da.paramValueDisplay ? $da.paramValueDisplay : ""                        )                      : (                          $da.paramValueDisplay ? $da.paramValueDisplay : ""                        )                }              ])            },            {}          )] } |'

  - path: /api/admin-data-areas/short-list
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'admin-data-areas/short-list'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
        CompanyCode: '::{companyCode}::'
        enabled: '::{status}::'
      transform: '$'

  - path: /api/admin-data-areas/multidelete
    method: DELETE
    model: _DELETE
    request:
      dataType: array
    upstreamConfig:
      method: DELETE
      path: 'admin-data-areas'
