controller: admin-roles-infos
upstream: ${{UPSTREAM_SYS_URL}}

models:
  - name: _
    config:
      id:
        from: id
      code:
        from: code
      name:
        from: name
        typeOptions:
          func: stringToMultiLang
      shortName:
        from: shortName
        typeOptions:
          func: stringToMultiLang
      functionGroupPermission:
        from: roleDetails.functionGroupPermission.name
        typeOptions:
          func: arrayValueToBreaklineString
      dataArea:
        from: roleDetails.dataArea.name
        typeOptions:
          func: arrayValueToBreaklineString
      ruleCode:
        from: roleDetails.dataArea.ruleCode
        typeOptions:
          func: arrayValueToBreaklineString
      criteria:
        from: roleDetails.dataArea.criteria
        typeOptions:
          func: arrayValueToBreaklineString
      roleDetails:
        from: roleDetails
        arrayChildren:
          id:
            from: id
          dataArea:
            from: dataArea
            objectChildren:
              id:
                from: id
              code:
                from: code
              name:
                from: name
              companyCode:
                from: companyCode
              ruleCode:
                from: ruleCode
              criteria:
                from: criteria
              isIncludesSubordinates:
                from: isIncludesSubordinates
                typeOptions:
                  func: YNToBoolean
              dataAreaDetails:
                from: dataAreaDetails
          functionGroupPermission:
            from: functionGroupPermission
            objectChildren:
              id:
                from: id
              code:
                from: code
              name:
                from: name
                typeOptions:
                  func: stringToMultiLang
              companyCode:
                from: companyCode

auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: admin-roles-infos
crudConfig:
  query:
  params:
    id:
      field: id
      type: string
      primary: true
    code:
      field: userName
      type: string
    ids:
      field: ids
      type: string
  routes:
    exclude:
      - createManyBase
      - replaceOneBase
defaultQuery:

routes:
  - path: /api/admin-roles-infos
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'admin-roles/infos'
      query:
        ids: ':{ids}:'
      transform: '$ ~> | $ | { "roleDetails": $map($.roleDetails, function($roleDetail) {        $merge([$roleDetail, {          "criteria":            $roleDetail.dataArea.isIncludesSubordinates = true              ? [ { "Include in direct staff": "Yes" } ]              : $reduce(                  $roleDetail.dataArea.dataAreaDetails,                  function($acc, $item) {                    $merge([                      $acc,                      {                        $item.dataAreaParam.name:                          $lookup($acc, $item.dataAreaParam.name)                            ? $lookup($acc, $item.dataAreaParam.name) & "; " &                              (                                ($item.dataAreaParam.dataAreaParamValueDisplay                                  ? $item.dataAreaParam.dataAreaParamValueDisplay                                  : "") & "(" & $item.dataAreaParamValue & ")"                              )                            : (                                ($item.dataAreaParam.dataAreaParamValueDisplay                                  ? $item.dataAreaParam.dataAreaParamValueDisplay                                  : "") & "(" & $item.dataAreaParamValue & ")"                              )                      }                    ])                  },                  {}                )        }])      })[] } |'

customRoutes:
