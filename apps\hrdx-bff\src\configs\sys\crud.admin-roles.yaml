controller: admin-roles
upstream: ${{UPSTREAM_SYS_URL}}

models:
  - name: _
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: totalPages
    config:
      id:
        from: id
      code:
        from: code
      name:
        from: name
        typeOptions:
          func: stringToMultiLang
      shortName:
        from: shortName
      description:
        from: note
        typeOptions:
          func: stringToMultiLang
      companyCode:
        from: $
        objectChildren:
          value:
            from: companyCode
          label:
            from: company.name,companyCode
            typeOptions:
              func: fieldsToNameCode
      company:
        from: company
      companyName:
        from: companyName
      status:
        from: enabled
        typeOptions:
          func: YNToBoolean
      isAdminReport:
        from: isAdminReport
        typeOptions:
          func: YNToBoolean
      isAdminReportView:
        from: isAdminReport
        typeOptions:
          func: YNToBoolean
      isUsed:
        from: isUsed
        typeOptions:
          func: YNToBoolean
      roleDetails:
        from: roleDetails
      table:
        from: table
      createdBy:
        from: createdBy
      createdAt:
        from: createdAt
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
      updatedAt:
        from: updatedAt
        typeOptions:
          func: timestampToDateTime
      file:
        from: File
      isOnlyCompany:
        from: isOnlyCompanyCode
        typeOptions:
          func: YNToBoolean
  - name: _DELETE
    config:
auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: admin-roles
crudConfig:
  query:
    sort:
      - field: updatedAt
        order: DESC
  params:
    id:
      field: id
      type: string
      primary: true
    code:
      field: userName
      type: string
    ids:
      field: ids
      type: string
  routes:
    exclude:
      - createManyBase
      - replaceOneBase
defaultQuery:

routes:
  - path: /api/admin-roles
    method: GET
    model: _

    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'admin-roles'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$ ~> | $.data | { "companyName": $.company.name, "isAdminReportView": {"label": $.isAdminReport ? "Yes" : "No", "type": $.isAdminReport ? "success" : "default"} } |'

  - path: /api/admin-roles/:id
    method: GET
    model: _

    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'admin-roles/:{id}:'
      transform: '$'

  - path: /api/admin-roles
    method: POST
    model: _
    query:
    bodyTransform: "$.{'name': name, 'isAdminReport': isAdminReport, 'note': note, 'code': code, 'companyCode': companyCode, 'enabled': enabled, 'languages': languages, 'roleDetails': [$map(table, function($i){ {'functionGroupPermissionId' : $i.functionGroupPermissionId, 'dataAreaId': $i.dataAreaId} })]}"
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'admin-roles'
      transform: '$'

  - path: /api/admin-roles/:id
    model: _
    method: PATCH
    query:
    bodyTransform: "$.{'name': name, 'isAdminReport': isAdminReport, 'code': code,  'note': note, 'companyCode': companyCode, 'enabled': enabled, 'languages': languages, 'roleDetails': [$map(table, function($i){ {'functionGroupPermissionId' : $i.functionGroupPermissionId, 'dataAreaId': $i.dataAreaId} })]}"
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: 'admin-roles/:{id}:'

  - path: /api/admin-roles/:id
    method: DELETE
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'admin-roles/:{id}:'
customRoutes:
  - path: /api/admin-roles/:id/histories
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'admin-roles'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
      transform: '$'

  - path: /api/admin-roles/export
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: binary
      path: 'admin-roles/export:export'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$'

  - path: /api/admin-roles/import
    model: _
    method: POST
    dataType: 'formData'
    query:
    transform: '$'
    upstreamConfig:
      method: POST
      path: 'admin-roles/import'
  - path: /api/admin-roles/validate
    model: _
    method: POST
    dataType: 'formData'
    query:
    transform: '$'
    upstreamConfig:
      method: POST
      path: 'admin-roles/validate'
  - path: /api/admin-roles/template
    model: _
    method: GET
    query:
    transform: '$'
    upstreamConfig:
      method: GET
      response:
        dataType: binary
      path: 'admin-roles/template'

  - path: /api/admin-roles/dropdown
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'admin-roles/dropdown'
      query:
        companyCode: ':{companyCode}:'
        isOnlyCompanyCode: ':{isOnlyCompanyCode}:'
      transform: '$'

  - path: /api/admin-roles/short-list
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'admin-roles/short-list'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
        companyCode: ':{companyCode}:'
        isOnlyCompanyCode: ':{isOnlyCompany}:'
      transform: '$'

  - path: /api/admin-roles/multidelete
    method: DELETE
    model: _DELETE
    request:
      dataType: array
    upstreamConfig:
      method: DELETE
      path: 'admin-roles'
