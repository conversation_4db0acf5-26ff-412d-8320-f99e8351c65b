controller: admin-user-management
upstream: ${{UPSTREAM_SYS_URL}}

models:
  - name: _
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: totalPages
    config:
      id:
        from: id
        type: string
      code:
        from: id
        type: string
      account:
        from: userName
        type: string
      employeeCode:
        from: employeeId
        type: string
      employeeId:
        from: employeeId
        type: string
      recordId:
        from: employeeRecordNumber
        type: string
      employeeName:
        from: fullName
        type: string
      email:
        from: email
        type: string
      companyName:
        from: companyName
        type: string
      company:
        from: company.name
        type: string
      companyShortName:
        from: company.shortName
        type: string
      companyCode:
        from: companyCode
        type: string
      legalEntityName:
        from: legalEntityName
        type: string
      legalEntity:
        from: legalEntity.name
        type: string
      legalEntityCode:
        from: legalEntityCode
        type: string
      businessUnitName:
        from: businessUnitName
        type: string
      businessUnit:
        from: businessUnit.name
        type: string
      businessUnitCode:
        from: businessUnitCode
        type: string
      divisionName:
        from: divisionName
        type: string
      division:
        from: division.name
        type: string
      divisionCode:
        from: divisionCode
        type: string
      departmentName:
        from: departmentName
        type: string
      department:
        from: department.name
        type: string
      departmentCode:
        from: departmentCode
        type: string
      userGroupName:
        from: groupName
        type: string
      userGroup:
        from: userGroup.group.name
        type: string
      roleDefault:
        from: userGroup.group.roles
      userId:
        from: userId
        type: string
      role:
        from: userRoles.role.name
      roleDetail:
        from: userRoles.role
      roleId:
        from: roleId
        type: string
      securityInfoGroup:
        from: userDataSecurityGroups.dataSecurityGroup.name
        type: string
      status:
        from: enabled
        type: string
        typeOptions:
          func: YNToBoolean
      lockAccount:
        from: isLock
        type: string
        typeOptions:
          func: YNToBoolean
      isAdmin:
        from: isAdmin
        type: string
        typeOptions:
          func: YNToBoolean
      effectiveDate:
        from: effectiveDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      effectiveEndDate:
        from: expiredDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      syncDateHistory:
        from: syncDateHistory
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      endDateHistory:
        from: endDateHistory
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      renewalStartDate:
        from: renewEffectiveDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      renewalEndDate:
        from: renewExpiredDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      accessStartDate:
        from: accessEffectiveDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      accessEndDate:
        from: accessExpiredDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      accessStartHourfrom:
        from: accessStartHours
        type: string
        typeOptions:
          func: timeStringToDateTime
          args: 'HH:mm:ss'
      accessEndHourfrom:
        from: accessEndHours
        type: string
        typeOptions:
          func: timeStringToDateTime
          args: 'HH:mm:ss'
      createdBy:
        from: createdBy
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      defaultRoleCode:
        from: DfRoleCode
        type: string
      groupCode:
        from: GroupCode
        type: string
      roleCode:
        from: RoleCode
        type: string

auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: admin-user-management
crudConfig:
  query:
    sort:
      - field: updatedAt
        order: DESC
  params:
    id:
      field: id
      type: string
      primary: true
    code:
      field: id
      type: string
  routes:
    exclude:
      - createManyBase
      - replaceOneBase
defaultQuery:

routes:
  - path: /api/admin-user-management
    method: GET
    model: _

    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'admin-user-management'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$merge(
        [
        $,
        {
        "data": [
        $map($.data, function ($item){
        $merge([
        $item,
        {"companyName": $item.company, "legalEntityName": $item.legalEntity,  "businessUnitName": $item.businessUnit, "divisionName": $item.division, "departmentName": $item.department, "userGroupName": $item.userGroup, "lockAccount": $exists($item.lockAccount) ? $boolean($item.lockAccount) : false, "recordId": $number($item.recordId) <= 0 ? 0 : $item.recordId, "additionalRole": $distinct($item.role), "role": [$map($item.roleDefault, function($roleItem) {$roleItem.name})]}
        ])
        })
        ]
        }
        ]
        )'

  - path: /api/admin-user-management/:id
    method: GET
    model: _

    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'admin-user-management/:{id}:'
      transform: '$map($, function ($item){ $merge([ $item, {"lockAccount": $exists($item.lockAccount) ? $boolean($item.lockAccount) : false} ]) })'

  - path: /api/admin-user-management
    method: POST
    model: _

    query:
    transform: '$'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'admin-user-management'
      transform: '$'

  - path: /api/admin-user-management/:id
    model: _
    method: PATCH
    query:
    bodyTransform: '$map($, function ($item){
      $merge([
      $item,
      {"accessEffectiveHours": $length($item.accessStartHours) = 8 ? $number($split($item.accessStartHours,":")[0]) : null },
      {"accessEffectiveMinutes": $length($item.accessStartHours) = 8 ? $number($split($item.accessStartHours,":")[1]) : null },
      {"accessEffectiveSeconds": $length($item.accessStartHours) = 8 ? $number($split($item.accessStartHours,":")[2]) : null },
      {"accessExpiredHours": $length($item.accessEndHours) = 8 ? $number($split($item.accessEndHours,":")[0]) : null },
      {"accessExpiredMinutes": $length($item.accessEndHours) = 8 ? $number($split($item.accessEndHours,":")[1]) : null },
      {"accessExpiredSeconds": $length($item.accessEndHours) = 8 ? $number($split($item.accessEndHours,":")[2]) : null }
      ])
      })'
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: 'admin-user-management/:{id}:/access-times'

  - path: /api/admin-user-management/:id
    method: DELETE
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'admin-user-management/:{id}:'

customRoutes:
  - path: /api/admin-user-management/:id/lock
    method: PATCH
    model: _
    query:
    upstreamConfig:
      method: PUT
      response:
        dataType: object
      path: 'admin-user-management/:{id}:/lock'
      transform: '$'

  - path: /api/admin-user-management/:id/histories
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'admin-user-management/:{id}:/history'
      transform: '$ ~> | $ | {"roleDefault": $join($.roleDefault.name, ","), "lockAccount": $.lockAccount = true ? "Yes" : "No", "recordId": $number($.recordId) < 0 ? 0 : $.recordId, "role": $join($.roleDetail.name, ","), "securityInfoGroup": $join($.securityInfoGroup, ",") } |'

  - path: /api/admin-user-management/infos
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
        dataKey: data
      path: 'admin-user-management/infos'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        Filter: '::{filter}::'
        permission: '::{permission}::'
      transform: '$'

  - path: /api/admin-user-management/export
    method: GET
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: binary
      path: 'admin-user-management/export:export'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$'
