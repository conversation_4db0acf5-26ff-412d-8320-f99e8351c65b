controller: admin-users-permisions
upstream: ${{UPSTREAM_SYS_URL}}

models:
  - name: _
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: totalPages
    config:
      id:
        from: id
        type: string
      code:
        from: id
        type: string
      userId:
        from: id
        type: string
      userName:
        from: userName
        type: string
      employeeCode:
        from: employeeId
        type: string
      recordId:
        from: recordId
        type: string
      employeeName:
        from: fullName
        type: string
      email:
        from: email
        type: string
      companySelect:
        from: $
        objectChildren:
          value:
            from: companyCode
          label:
            from: company.name,companyCode
            typeOptions:
              func: fieldsToNameCode
      companyCode:
        from: companyCode
        type: string
      companyName:
        from: companyName
        type: string
      company:
        from: company
        type: string
      companyShortName:
        from: company.shortName
        type: string
      legalEntity:
        from: legalEntity.name
        type: string
      businessUnit:
        from: businessUnit.name
        type: string
      division:
        from: division.name
        type: string
      department:
        from: department.name
        type: string
      userGroupName:
        from: userGroup.group.name
      groupName:
        from: groupName
      role:
        from: userRoles.role.name
        type: string
      roleName:
        from: userRoles.role.name
        typeOptions:
          func: arrayValueToBreaklineString
      defaultRole:
        from: userRoles.roleId
        type: string
      securityInfoGroup:
        from: userDataSecurityGroups.dataSecurityGroup.name
        type: string
      dataSecurityGroupName:
        from: userDataSecurityGroups.dataSecurityGroup.name
        typeOptions:
          func: arrayValueToBreaklineString
      dataSecurityGroupIds:
        from: userDataSecurityGroups
        type: string
      lockAccount:
        from: isLock
        type: string
        typeOptions:
          func: YNToBoolean
      userGroup:
        from: userGroup
      userRoles:
        from: userRoles
      userDataSecurityGroups:
        from: userDataSecurityGroups
      effectiveDate:
        from: effectiveDatePermission
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      countryCode:
        from: $
        objectChildren:
          value:
            from: country.code
          label:
            from: country.name,country.code
            typeOptions:
              func: fieldsToNameCode
      effectiveDatePermissionTimestamp:
        from: effectiveDatePermission
        type: timestamp
      effectiveDateTimestamp:
        from: effectiveDate
        type: timestamp
      effectiveEndDate:
        from: expiredDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      renewalStartDate:
        from: renewEffectiveDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      renewalEndDate:
        from: renewExpiredDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      accessStartDate:
        from: accessEffectiveDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      accessEndDate:
        from: accessExpiredDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      accessStartHourfrom:
        from: accessStartHours
        type: string
        typeOptions:
          func: timeStringToDateTime
          args: 'HH:mm:ss'
      accessEndHourfrom:
        from: accessEndHours
        type: string
        typeOptions:
          func: timeStringToDateTime
          args: 'HH:mm:ss'
      createdBy:
        from: createdBy
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      file:
        from: File
      permissionEnabled:
        from: permissionEnabled
        typeOptions:
          func: YNToBoolean
      permissionCreatedBy:
        from: permissionCreatedBy
      permissionCreatedAt:
        from: permissionCreatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      permissionUpdatedBy:
        from: permissionUpdatedBy
      permissionUpdatedAt:
        from: permissionUpdatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      country:
        from: country
      groupCode:
        from: groupCode
      roleCode:
        from: roleCode
      dfRoleCode:
        from: dfRoleCode
      securityCode:
        from: securityCode
  - name: _POST
    config:
      userId:
        from: userId
      groupId:
        from: groupId
      companyCode:
        from: companyCode
      effectiveDate:
        from: effectiveDate
        typeOptions:
          func: timestampToDateTime
      permissionEnabled:
        from: enabled
        typeOptions:
          func: YNToBoolean
      countryCode:
        from: countryCode
      dataSecurityGroupIds:
        from: dataSecurityGroupIds
      permissionOnSecurityGroups:
        from: permissionOnSecurityGroups
      permissionOnRoles:
        from: permissionOnRoles
        objectChildren:
          defaultRole:
            from: defaultRole
          table:
            from: table
            arrayChildren:
              companyCode:
                from: companyCode
              componentsGroupValue:
                from: componentsGroupValue
              criteria:
                from: criteria
              company:
                from: company
              functionGroupPermissionName:
                from: functionGroupPermissionName
              permissionWith:
                from: permissionWith
              isDefault:
                from: isDefault
              roleId:
                from: roleId
              roleName:
                from: roleName
              isDuplicate:
                from: isDuplicate
              dataArea:
                from: $
                objectChildren:
                  value:
                    from: dataAreaId
                  label:
                    from: dataAreaName
      permissionOnUserGroups:
        from: permissionOnUserGroups
      selectedDefaultRole:
        from: selectedDefaultRole
      selectedAdditionalRole:
        from: selectedAdditionalRole
  - name: _DELETE
    config:
      userId:
        from: userId
      userName:
        from: userName
      effectiveDate:
        from: effectiveDate
        typeOptions:
          func: timestampToDateTime
auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: admin-users-permisions
crudConfig:
  query:
    sort:
      - field: permissionUpdatedAt
        order: DESC
  params:
    id:
      field: id
      type: string
      primary: true
    code:
      field: id
      type: string
  routes:
    exclude:
      - createManyBase
      - replaceOneBase
defaultQuery:

routes:
  - path: /api/admin-users-permisions
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'admin-users'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: '::{search}::'
        Filter: '::{filter}::'
      transform: '$ ~> | $.data | {
        "companyName": $.company.name,
        "groupName": $.userGroupName,
        "securityInfoGroupDisplayTooltip": $map($.userDataSecurityGroups, function($item) {$item.dataSecurityGroup.name}),
        "roleDisplayTooltip": $map($.userGroup.group.roles, function($item) {
        $item.name & " (" & $item.code & ")"
        }),
        "additionalRole": (
        $roleList := $.userGroup ?
        $filter($.userRoles, function ($roleItem){ $not($roleItem.role.id in $map($.userGroup.group.roles, function($groupRole){ $groupRole.id})) })
        :
        $.userRoles;
        $distinct(
        $map(
        $roleList,
        function($item) {
        $item.role.name & " (" & $item.role.code & ")"
        }
        ))
        )
        }|'

  - path: /api/admin-users-permisions/:id
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'admin-users/:{id}:'
      transform: '(    $_isCurrentRoleIdDefault:= function($itemRole, $currentUserGroup) { $itemRole in $map($currentUserGroup.group.roles, function($item) { $item.id }) };    $_mergeCurrentRole:= function($currentItem) { $append($map($currentItem.userGroup.group.roles, function($item) { $item.id }), $map($currentItem.userRoles, function($item) { $item.roleId }))[] };    $map($, function($i) { $merge([$i, {"userRoles": $map($i.userRoles, function($ur){$merge([$ur, {"action": $count($ur.userRoleDataAreas) > 0 ? [{ "id": "delete", "type": "ghost-gray", "icon": "trash-can", "disabled": true         }, { "id": "duplicate", "type": "ghost-gray", "icon": "copy", "appendObject": {     "isDefault": {         "label": "No",         "type": "default"     } }}] : [{ "id": "duplicate", "type": "ghost-gray", "icon": "copy", "appendObject": {     "isDefault": {         "label": "No",         "type": "default"    } } }]}])}), "permissionOnSecurityGroups":{"companyCode": $i.company.code, "dataSecurityGroupIds": $map($i.userDataSecurityGroups, function($item) { $item.dataSecurityGroupId })[] },"permissionOnRoles":{"defaultRole": $distinct($map($i.userRoles, function($item) { $item.roleId } )[])[], "roleCommands":$map($_mergeCurrentRole($i), function($item) { {"isDefault": $_isCurrentRoleIdDefault($item, $i.userGroup),"defaultRole": $item} })[] },"permissionOnUserGroups":{"userGroupId":$i.userGroup.id,"userGroupName":$i.userGroup.group.name} }])}))'

  - path: /api/admin-users-permisions
    method: POST
    model: _POST
    query:
    bodyTransform: '(  $__convertYN := function($i) {$i ="Yes" ? "Y": "N" };     $__convertDuplicate := function($i) {$i ? "Y": "N"}; $__roleCommands := $.permissionOnRoles.table;   $__dataSecurityGroupIds := $map($.permissionOnSecurityGroups.dataSecurityGroupIds, function($item) { $item.value })[];   $__countryCode := $.permissionOnSecurityGroups.countryCode;    $__convertData := function ($i) {         {        "isDefault": $__convertYN($i.isDefault.label),            "isDuplicate": $__convertDuplicate($i.isDuplicate),              "roleId": $i.roleId,                   "dataAreaId": $i.dataAreaId, "functionGroupId": $i.componentsGroupValue } };     $ ~> | $ | {     "roleCommands": $map($__roleCommands, function($item) {     $__convertData($item) })[],         "dataSecurityGroupIds": $exists($__dataSecurityGroupIds)?      $__dataSecurityGroupIds : [],         "countryCode": $exists($__countryCode)? $__countryCode : null       }, ["permissionOnRoles","permissionOnSecurityGroups","permissionOnUserGroups","languages", "selectedDefaultRole", "selectedAdditionalRole"] |)'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'admin-users'
      transform: '$'

  - path: /api/admin-users-permisions/:id
    model: _POST
    method: PATCH
    query:
    bodyTransform: '(  $__convertYN := function($i) {$i ="Yes" ? "Y": "N" };     $__convertDuplicate := function($i) {$i ? "Y": "N"}; $__roleCommands := $.permissionOnRoles.table;   $__dataSecurityGroupIds := $map($.permissionOnSecurityGroups.dataSecurityGroupIds, function($item) { $item.value })[];   $__countryCode := $.permissionOnSecurityGroups.countryCode;    $__convertData := function ($i) {         {        "isDefault": $__convertYN($i.isDefault.label),            "isDuplicate": $__convertDuplicate($i.isDuplicate),              "roleId": $i.roleId,                   "dataAreaId": $i.dataAreaId, "functionGroupId": $i.componentsGroupValue } };     $ ~> | $ | {     "roleCommands": $map($__roleCommands, function($item) {     $__convertData($item) })[],         "dataSecurityGroupIds": $exists($__dataSecurityGroupIds)?      $__dataSecurityGroupIds : [],         "countryCode": $exists($__countryCode)? $__countryCode : null       }, ["permissionOnRoles","permissionOnSecurityGroups","permissionOnUserGroups","languages", "selectedDefaultRole", "selectedAdditionalRole"] |)'
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: 'admin-users/:{id}:'

  - path: /api/admin-users-permisions/:id
    method: DELETE
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'admin-users/:{id}:'

customRoutes:
  - path: /api/admin-users-permisions/import
    model: _
    method: POST
    dataType: 'formData'
    query:
    transform: '$'
    upstreamConfig:
      method: POST
      path: 'admin-users/import'

  - path: /api/admin-users-permisions/validate
    model: _
    method: POST
    dataType: 'formData'
    query:
    transform: '$'
    upstreamConfig:
      method: POST
      path: 'admin-users/validate'

  - path: /api/admin-users-permisions/template
    model: _
    method: GET
    query:
    transform: '$'
    upstreamConfig:
      method: GET
      response:
        dataType: binary
      path: 'admin-users/template'

  - path: /api/admin-users-permisions/export
    method: POST
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: binary
      path: 'admin-users/user-permissions/:export'

  - path: /api/admin-users-permisions/:id/histories
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'admin-users/:{id}:/history-permission'
      transform: '(        $_isCurrentRoleIdDefault:= function($itemRole, $currentUserGroup) { $itemRole in $map($currentUserGroup.group.roles, function($item) { $item.id})};        $_mergeCurrentRole:= function($currentItem) { $append($map($currentItem.userGroup.group.roles, function($item) { $item.id}), $map($currentItem.userRoles, function($item) { $item.roleId}))[]};        $map($, function($i) {          $merge([            $i,            {              "groupId": $i.userGroup.groupId,              "permissionOnSecurityGroups": {                  "countryCode": $i.countryCode,                  "companyCode": $i.companyCode,                  "dataSecurityGroupIds": $map($i.userDataSecurityGroups, function($item) {        {"value":$item.dataSecurityGroupId,"label":$item.dataSecurityGroup.name}        })[]                  },                  "permissionOnRoles": {"defaultRole": $distinct($map($i.userRoles, function($item) { $item.roleId} )[])[],                  "roleCommands": $map($_mergeCurrentRole($i), function($item) { {"isDefault": $_isCurrentRoleIdDefault($item, $i.userGroup),"defaultRole": $item }})[]},                  "permissionOnUserGroups": {                      "userGroupId":$i.userGroup.id,"userGroupName":$i.userGroup.group.name},                      "effectiveDatePermissionTimestamp": $i.effectiveDateTimestamp                  }            ])          })[]        )'

  - path: /api/admin-users-permisions/infos
    model: _
    method: GET
    query:
    transform: '$'
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'admin-users/infos'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        Filter: '::{filter}::'
        permission: '::{permission}::'

  - path: /api/admin-users-permisions/export
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: binary
      path: 'admin-users/user-permissions:export'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$'

  - path: /api/admin-users-permisions/multidelete
    method: DELETE
    model: _DELETE
    request:
      dataType: array
    upstreamConfig:
      method: DELETE
      path: 'admin-users'
