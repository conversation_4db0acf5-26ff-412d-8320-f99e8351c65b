controller: auth-sessions
upstream: ${{UPSTREAM_SYS_URL}}

models:
  - name: _
    config:
      id:
        from: id
      expiredTime:
        from: expiredTime
      sessionTimeout:
        from: sessionTimeout
auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: auth-sessions
crudConfig:
  query:
  params:
  routes:
    exclude:
      - createManyBase
      - replaceOneBase
defaultQuery:

routes:
  - path: /api/auth-sessions
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'sessions'
      transform: '$'
  - path: /api/auth-sessions
    method: POST
    model: _
    query:
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'sessions'
      transform: '$'
customRoutes:
  - path: /api/auth-sessions
    method: PATCH
    model:
    query:
    upstreamConfig:
      method: PUT
      response:
        dataType: object
      path: 'sessions'
      transform: '$'
