controller: com-version
upstream: ${{UPSTREAM_COM_URL}}

models:
  - name: _
    config:

auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: com-version
crudConfig:
  query:
  params:
    id:
      field: id
      type: string
      primary: true
  routes:
    exclude:
      - createManyBase
      - replaceOneBase
defaultQuery:

routes:

customRoutes:
  - path: /api/com-version
    method: GET
    query:
    request:
      ignoreFunctionCode: true
    upstreamConfig:
      method: GET
      path: 'version'
      transform: '$'
