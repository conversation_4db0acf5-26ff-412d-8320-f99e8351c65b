controller: fo-version
upstream: ${{UPSTREAM_FO_URL}}

models:
  - name: _
    config:

auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: fo-version
crudConfig:
  query:
  params:
    id:
      field: id
      type: string
      primary: true
  routes:
    exclude:
      - createManyBase
      - replaceOneBase
defaultQuery:

routes:

customRoutes:
  - path: /api/fo-version
    method: GET
    query:
    request:
      ignoreFunctionCode: true
    upstreamConfig:
      method: GET
      path: 'version'
      transform: '$'
