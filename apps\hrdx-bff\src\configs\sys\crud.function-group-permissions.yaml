controller: function-group-permissions
upstream: ${{UPSTREAM_SYS_URL}}

models:
  - name: _
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: totalPages
    config:
      id:
        from: id
        type: string
      code:
        from: code
        type: string
      name:
        from: name
        type: string
        typeOptions:
          func: stringToMultiLang
      company:
        from: company.longName
        type: string
      companyCode:
        from: companyCode
        type: string
      status:
        from: enabled
        type: string
        typeOptions:
          func: YNToBoolean
      isUsed:
        from: isUsed
        typeOptions:
          func: YNToBoolean
      description:
        from: note
        type: string
        typeOptions:
          func: stringToMultiLang
      functionGroupPermissionDetails:
        from: functionGroupPermissionDetails
      createdOn:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      lastUpdatedOn:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      createdBy:
        from: createdBy
        type: string
      lastUpdatedBy:
        from: updatedBy
        type: string
      file:
        from: file
auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: function-group-permissions
crudConfig:
  query:
    sort:
      - field: updatedAt
        order: DESC
  params:
    id:
      field: id
      type: string
      primary: true
    code:
      field: code
      type: string
  routes:
    exclude:
      - createManyBase
      - replaceOneBase
defaultQuery:

routes:
  - path: /api/function-group-permissions
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'function-group-permissions'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$'

  - path: /api/function-group-permissions/:id
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'function-group-permissions/:{id}:'
      transform: '(  $functionActionLst := $map(    $.functionGroupPermissionDetails,    function($item) {      {        "moduleId": $string($item.functionAuth.function.moduleId),        "functionId": $string($item.functionAuth.function.functionId),        "functionGroupId": $string($item.functionAuth.function.functionGroupId),        "actionId": $string($item.functionAuth.actionId)      }    }  );  $sortedFunctionActionLst := $sort($functionActionLst, function($a, $b) {    $number($a.actionId) - $number($b.actionId)  });  $merge([    $map(      $keys($),      function($key) {        $not($key = "functionGroupPermissionDetails")          ? { $key: $lookup($, $key) }          : {}      }    ),    {      "rawfunctionGroupPermissionDetails": $sortedFunctionActionLst    }  ]))'

  - path: /api/function-group-permissions
    method: POST
    model: _
    query:
    bodyTransform: '(    $listByGroup:= [$map($keys($.functionGroupPermissionDetails), function($key) { { $key:  $lookup($lookup($.functionGroupPermissionDetails,$key).value ,$key)  }})];        $permissionGroupManagers:= $reduce([$map($listByGroup, function($group){ $count($lookup($group,$keys($group)[0])) ? [$map($lookup($group,$keys($group)[0]), function($actionId){ { "actionId" : $number($actionId), "functionId":$number( $keys( $group)[0] )}})]})], $append)   ;    $merge([$map($keys($), function ($key){ $not($key = "functionGroupPermissionDetails") ? {$key: $lookup($,$key)}}), {"permissionGroupManagers":$permissionGroupManagers }]))'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'function-group-permissions'
      transform: '$'

  - path: /api/function-group-permissions/:id
    model: _
    method: PATCH
    query:
    bodyTransform: '(    $listByGroup:= [$map($keys($.functionGroupPermissionDetails), function($key) { { $key:  $lookup($lookup($.functionGroupPermissionDetails,$key).value ,$key)  }})];        $permissionGroupManagers:= $reduce([$map($listByGroup, function($group){ $count($lookup($group,$keys($group)[0])) ? [$map($lookup($group,$keys($group)[0]), function($actionId){ { "actionId" : $number($actionId), "functionId":$number( $keys( $group)[0] )}})]})], $append)   ;    $merge([$map($keys($), function ($key){ $not($key = "functionGroupPermissionDetails") ? {$key: $lookup($,$key)}}), {"permissionGroupManagers":$permissionGroupManagers }]))'
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: 'function-group-permissions/:{id}:'
      transform: '$'

  - path: /api/function-group-permissions/:id
    method: DELETE
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'function-group-permissions/:{id}:'
customRoutes:
  - path: /api/function-group-permissions/import
    model: _
    method: POST
    dataType: 'formData'
    query:
    transform: '$'
    upstreamConfig:
      method: POST
      path: 'function-group-permissions/import'

  - path: /api/function-group-permissions/template
    model: _
    method: GET
    query:
    transform: '$'
    upstreamConfig:
      method: GET
      response:
        dataType: binary
      path: 'function-group-permissions/template'

  - path: /api/function-group-permissions/validate
    model: _
    method: POST
    dataType: 'formData'
    query:
    transform: '$'
    upstreamConfig:
      method: POST
      path: 'function-group-permissions/validate'

  - path: /api/function-group-permissions/:id/histories
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'function-group-permissions/:{id}:/history'
      transform: '$'
  - path: /api/function-group-permissions/infos
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'function-group-permissions/infos'
      query:
        companyCode: '::{companyCode}::'
      transform: '$'
