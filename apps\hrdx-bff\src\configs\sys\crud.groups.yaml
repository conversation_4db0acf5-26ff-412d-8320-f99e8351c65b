controller: groups-sys
upstream: ${{UPSTREAM_SYS_URL}}

models:
  - name: _
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: indexFrom
    config:
      id:
        from: id
        type: string
      code:
        from: code
        type: string
      name:
        from: name
        type: string
        typeOptions:
          func: stringToMultiLang
      company:
        from: companyCode
        type: string
      companySelect:
        from: $
        objectChildren:
          value:
            from: companyCode
          label:
            from: companyName,companyCode
            typeOptions:
              func: fieldsToNameCode
      companyName:
        from: companyName
        type: string
      nation:
        from: $
        objectChildren:
          value:
            from: countryCode
          label:
            from: countryName,countryCode
            typeOptions:
              func: fieldsToNameCode
      nationName:
        from: countryName
        type: string
      description:
        from: note
        type: string
        typeOptions:
          func: stringToMultiLang
      isMemberCompanyAdminDisplay:
        from: isCreatedByAdmin
        type: string
        typeOptions:
          func: YNToBoolean
      systemAdmin:
        from: isCreatedByAdmin
        type: string
        typeOptions:
          func: YNToBoolean
      status:
        from: enabled
        type: string
        typeOptions:
          func: YNToBoolean
      secureData:
        from: dataSecurityGroups.name
        type: array
        typeOptions:
          func: arrayValueToBreaklineString
      roles:
        from: roles
      dataSecurityGroups:
        from: dataSecurityGroups
      role:
        from: roles.name
        type: array
        typeOptions:
          func: arrayValueToBreaklineString
      roleIds:
        from: roles.id
      roleId:
        from: roleId
      securityGroup:
        from: dataSecurityGroups.id
      dataSecurityGroupId:
        from: dataSecurityGroupId
      createdOn:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      createdBy:
        from: createdBy
        type: string
      lastUpdatedOn:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      lastUpdatedBy:
        from: updatedBy
        type: string
      file:
        from: File
      isUsing:
        from: isUsing

  - name: postModal
    config:
      id:
        from: id
        type: string
      code:
        from: code
        type: string
      name:
        from: name
        typeOptions:
          func: stringToMultiLang
      systemAdmin:
        from: isMemberCompanyAdmin
        type: string
        typeOptions:
          func: YNToBoolean
      company:
        from: companyCode
      companySelect:
        from: $
        objectChildren:
          value:
            from: companyCode
          label:
            from: companyName,companyCode
            typeOptions:
              func: fieldsToNameCode
      nation:
        from: $
        objectChildren:
          value:
            from: countryCode
          label:
            from: countryName,countryCode
            typeOptions:
              func: fieldsToNameCode
      nationName:
        from: countryName
      roleIds:
        from: roleIds
      securityGroup:
        from: securityGroupIds
      status:
        from: enabled
        typeOptions:
          func: YNToBoolean
      description:
        from: note
        typeOptions:
          func: stringToMultiLang

  - name: _DELETE
    config:

auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: groups
crudConfig:
  query:
    sort:
      - field: lastUpdatedOn
        order: DESC
  params:
    id:
      field: id
      type: string
      primary: true
    code:
      field: code
      type: string
  routes:
    exclude:
      - createManyBase
      - replaceOneBase
defaultQuery:

routes:
  # list table
  - path: /api/groups-sys
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
        dataKey: data
      path: 'authz-groups'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$ ~> | $.data | { "isMemberCompanyAdminDisplay": { "label": $.systemAdmin ? "Yes" : "No", "type": $.systemAdmin ? "success" : "default"}, "roleDisplayTooltip": $map($.roles, function($item) { $item.name & " (" & $item.code & ")" }), "secureDataDisplayTooltip": $map($.dataSecurityGroups, function($item){     $item.name & "(" & $item.code & ")"}) } |'

  # detail
  - path: /api/groups-sys/:id
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'authz-groups/:{id}:'
      transform: '$ ~> | $ | {  "companyName": $exists($.companyName) ? $.companyName & " (" & $.company & ")" : "",  "securityGroup": $map($.dataSecurityGroups, function($item) {    {      "label": $item.name,      "value": $item.id    }  })[]} |'

  # create
  - path: /api/groups-sys
    method: POST
    model: postModal
    query:
    bodyTransform: '(  $__securityGroupIds := $map($.securityGroupIds, function($item) { $item.value })[];   $ ~> | $ | {     "securityGroupIds": $exists($__securityGroupIds)?      $__securityGroupIds : []       } |)'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'authz-groups'
      transform: '$'
  # update
  - path: /api/groups-sys/:id
    method: PATCH
    model: postModal
    query:
    bodyTransform: '(  $__securityGroupIds := $map($.securityGroupIds, function($item) { $item.value })[];   $ ~> | $ | {     "securityGroupIds": $exists($__securityGroupIds)?      $__securityGroupIds : []       } |)'
    upstreamConfig:
      method: PUT
      response:
        dataType: object
      path: 'authz-groups/:{id}:'

  # delete
  - path: /api/groups-sys/:id
    method: DELETE
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'authz-groups/:{id}:'
customRoutes:
  - path: /api/groups-sys/import
    model: _
    method: POST
    dataType: 'formData'
    query:
    transform: '$'
    upstreamConfig:
      method: POST
      path: 'authz-groups/import'
  - path: /api/groups-sys/template
    model: _
    method: GET
    query:
    transform: '$'
    upstreamConfig:
      method: GET
      response:
        dataType: binary
      path: 'authz-groups/template'
  - path: /api/groups-sys/validate
    model: _
    method: POST
    dataType: 'formData'
    query:
    transform: '$'
    upstreamConfig:
      method: POST
      path: 'authz-groups/validate'

  - path: /api/groups-sys/infos
    model: _
    method: GET
    query:
    transform: '$'
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'authz-groups/infos'

  - path: /api/groups-sys/dropdown
    model: _
    method: GET
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'authz-groups/dropdown'
      query:
        companyCode: ':{companyCode}:'
      transform: '$'

  - path: /api/groups-sys/noPagination
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
        dataKey: data
      path: 'authz-groups'
      query:
        Page: '1'
        PageSize: '50000'
        Filter: '::{filter}::'

  - path: /api/groups-sys/export
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: binary
      path: 'authz-groups/export:export'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$'

  - path: /api/groups-sys/multidelete
    method: DELETE
    model: _DELETE
    request:
      dataType: array
    upstreamConfig:
      method: DELETE
      path: 'authz-groups'
