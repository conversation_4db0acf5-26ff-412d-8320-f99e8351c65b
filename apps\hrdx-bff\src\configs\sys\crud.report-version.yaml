controller: report-version
upstream: ${{UPSTREAM_REP_URL}}

models:
  - name: _
    config:

auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: report-version
crudConfig:
  query:
  params:
    id:
      field: id
      type: string
      primary: true
  routes:
    exclude:
      - createManyBase
      - replaceOneBase
defaultQuery:

routes:

customRoutes:
  - path: /api/report-version
    method: GET
    query:
    request:
      ignoreFunctionCode: true
    upstreamConfig:
      method: GET
      path: 'version'
      transform: '$'
