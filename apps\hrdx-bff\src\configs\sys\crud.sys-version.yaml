controller: sys-version
upstream: ${{UPSTREAM_SYS_URL}}

models:
  - name: _
    config:

auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: sys-version
crudConfig:
  query:
  params:
    id:
      field: id
      type: string
      primary: true
  routes:
    exclude:
      - createManyBase
      - replaceOneBase
defaultQuery:

routes:

customRoutes:
  - path: /api/sys-version
    method: GET
    query:
    request:
      ignoreFunctionCode: true
    upstreamConfig:
      method: GET
      path: 'version'
      transform: '$'
