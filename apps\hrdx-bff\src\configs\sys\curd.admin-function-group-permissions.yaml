controller: admin-function-group-permissions
upstream: ${{UPSTREAM_SYS_URL}}

models:
  - name: _
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: totalPages
    config:
      id:
        from: id
        type: string
      code:
        from: code
        type: string
      name:
        from: name
        type: string
        typeOptions:
          func: stringToMultiLang
      company:
        from: company.name
        type: string
      companyCode:
        from: companyCode
        type: string
      companySelect:
        from: $
        objectChildren:
          value:
            from: companyCode
          label:
            from: company.name,companyCode
            typeOptions:
              func: fieldsToNameCode
      status:
        from: enabled
        type: string
        typeOptions:
          func: YNToBoolean
      isUsed:
        from: hasDataRole
        typeOptions:
          func: YNToBoolean
      description:
        from: note
        type: string
        typeOptions:
          func: stringToMultiLang
      functionGroupPermissionDetails:
        from: functionGroupPermissionDetails
      createdOn:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      lastUpdatedOn:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      createdBy:
        from: createdBy
        type: string
      lastUpdatedBy:
        from: updatedBy
        type: string
      file:
        from: file

  - name: _DELETE
    config:

auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: admin-function-group-permissions
crudConfig:
  query:
    sort:
      - field: lastUpdatedOn
        order: DESC
  params:
    id:
      field: id
      type: string
      primary: true
    code:
      field: code
      type: string
  routes:
    exclude:
      - createManyBase
      - replaceOneBase
defaultQuery:

routes:
  - path: /api/admin-function-group-permissions
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'admin-function-group-permissions'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$'

  - path: /api/admin-function-group-permissions/:id
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'admin-function-group-permissions/:{id}:'
      transform: '(     $functionActionLst := $map($.functionGroupPermissionDetails, function($item){         {            "moduleId": $string($item.functionAuth.moduleId),            "functionId": $string($item.functionAuth.functionId),            "functionGroupId": $string($item.functionGroupPermissionId),            "actionId": $string($item.functionAuth.actionId)        }});    $merge([$map($keys($), function ($key){$not($key = "functionGroupPermissionDetails") ? {$key: $lookup($,$key)}}), {"rawfunctionGroupPermissionDetails": $functionActionLst }]);)'

  - path: /api/admin-function-group-permissions
    method: POST
    model: _
    query:
    bodyTransform: '(       $listByGroup:= $map($.functionGroupPermissionDetails, function($item){        $map($item.permission.fields[0].value[], function($value){            {"functionId": $item.functionId, "actionId": $value}        })    })[];    $permissionGroupManagers := (  $listByGroup := $map($.functionGroupPermissionDetails, function($item){    $map($item.permission.fields[0].value[], function($value){      {        "functionId": $item.functionId,        "actionId": $value      }    })  })[];  $permissionGroupManagers := $distinct($reduce($listByGroup, $append));  $merge([    $map(      $keys($),      function($key){        $key != "functionGroupPermissionDetails" ? { $key: $lookup($, $key) } : {}      }    ),    { "permissionGroupManagers": $permissionGroupManagers }  ])))'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'admin-function-group-permissions'
      transform: '$'

  - path: /api/admin-function-group-permissions/:id
    model: _
    method: PATCH
    query:
    bodyTransform: '(       $listByGroup:= $map($.functionGroupPermissionDetails, function($item){        $map($item.permission.fields[0].value[], function($value){            {"functionId": $item.functionId, "actionId": $value}        })    })[];    $permissionGroupManagers := (  $listByGroup := $map($.functionGroupPermissionDetails, function($item){    $map($item.permission.fields[0].value[], function($value){      {        "functionId": $item.functionId,        "actionId": $value      }    })  })[];  $permissionGroupManagers := $distinct($reduce($listByGroup, $append));  $merge([    $map(      $keys($),      function($key){        $key != "functionGroupPermissionDetails" ? { $key: $lookup($, $key) } : {}      }    ),    { "permissionGroupManagers": $permissionGroupManagers }  ])))'
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: 'admin-function-group-permissions/:{id}:'
      transform: '$'

  - path: /api/admin-function-group-permissions/:id
    method: DELETE
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'admin-function-group-permissions/:{id}:'
customRoutes:
  - path: /api/admin-function-group-permissions/import
    model: _
    method: POST
    dataType: 'formData'
    query:
    transform: '$'
    upstreamConfig:
      method: POST
      path: 'admin-function-group-permissions/import'

  - path: /api/admin-function-group-permissions/template
    model: _
    method: GET
    query:
    transform: '$'
    upstreamConfig:
      method: GET
      response:
        dataType: binary
      path: 'admin-function-group-permissions/template'

  - path: /api/admin-function-group-permissions/validate
    model: _
    method: POST
    dataType: 'formData'
    query:
    transform: '$'
    upstreamConfig:
      method: POST
      path: 'admin-function-group-permissions/validate'

  - path: /api/admin-function-group-permissions/:id/histories
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'admin-function-group-permissions/:{id}:/history'
      transform: '$'

  - path: /api/admin-function-group-permissions/infos
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'admin-function-group-permissions/infos'
      query:
        companyCode: '::{companyCode}::'
        enabled: '::{status}::'
      transform: '$'

  - path: /api/admin-function-group-permissions/short-list
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'admin-function-group-permissions/short-list'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
        companyCode: '::{companyCode}::'
        enabled: '::{status}::'
      transform: '$'

  - path: /api/admin-function-group-permissions/export
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: binary
      path: 'admin-function-group-permissions/export:export'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$'

  - path: /api/admin-function-group-permissions/multidelete
    method: DELETE
    model: _DELETE
    request:
      dataType: array
    upstreamConfig:
      method: DELETE
      path: 'admin-function-group-permissions'
