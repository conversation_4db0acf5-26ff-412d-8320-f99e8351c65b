controller: ts-list-application-for-leaves
upstream: ${{UPSTREAM_TS_URL}}

models:
  - name: _
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: indexFrom
    config:
      id:
        from: Id
        type: string
      applicationForLeaveId:
        from: TsapplicationForLeaveId
      employeeId:
        from: employeeId
        type: string
      employeeCode:
        from: employeeCode
        type: string
      employeeRecord:
        from: employeeRecordNumber
        type: string
      employeeName:
        from: employeeName
        type: string
      groupId:
        from: groupId
      groupName:
        from: groupName
        type: string
      group:
        from: groupId
        type: string
      company:
        from: companyId
        type: string
      companyName:
        from: companyName
        type: string
      businessUnit:
        from: businessUnitId
        type: string
      businessUnitName:
        from: businessUnitName
        type: string
      division:
        from: divisionId
        type: string
      divisionName:
        from: divisionName
        type: string
      nation:
        from: nationId
        type: string
      nationName:
        from: nationName
        type: string
      legalEntity:
        from: legalEntityId
        type: string
      legalEntityName:
        from: legalEntityName
        type: string
      department:
        from: departmentId
        type: string
      departmentName:
        from: departmentName
        type: string
      jobName:
        from: jobName
        type: string
      jobCode:
        from: jobCode
        type: string
      leaveUnit:
        from: leaveUnit
        type: string
      leaveReason:
        from: leaveReason
        type: string
      leaveRequestStatus:
        from: leaveStatus
        type: string
      leaveRequestStatusName:
        from: leaveStatusName
        type: string
      dataSource:
        from: dataSource
        type: string
      dataSourceName:
        from: dataSourceName
        type: string
      daysOff:
        from: noDayOffs
        type: string
      hoursOff:
        from: noHoursOffs
        type: string
      totalNoDayOff:
        from: totalNoDayOff
        type: string
      totalNoHours:
        from: totalNoHours
        type: string
      catypeOfDayOffId:
        from: catypeOfDayOffId
        type: string
      catypeOfDayOffName:
        from: catypeOfDayOffName
        type: string
      startDate:
        from: dayOffStart
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      endDate:
        from: dayOffEnd
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      tsapplicationForLeaveTypes:
        from: tsapplicationForLeaveTypes
        arrayChildren:
          leaveUnit:
            from: leaveUnit
          leaveReason:
            from: leaveReason
          id:
            from: id
            type: string
          startDate:
            from: dayOffStart
            type: timestamp
            typeOptions:
              func: timestampToDateTime
          endDate:
            from: dayOffEnd
            type: timestamp
            typeOptions:
              func: timestampToDateTime
          fromTime:
            from: hourStart
            type: string
            typeOptions:
              func: timeStringToDateTime
          endTime:
            from: hourEnd
            type: string
            typeOptions:
              func: timeStringToDateTime
          absenceTypes:
            from: catypeOfDayOffId
            type: string
          cAtypeOfDayOffName:
            from: cAtypeOfDayOffName
            type: string
          daysOff002:
            from: noDayOffs
            type: number
          hoursOff002:
            from: noHours
            type: string
          leavePeriod:
            from: leavePeriod
            type: string
          tsapplicationForLeaveDetails:
            from: tsapplicationForLeaveDetails
            arrayChildren:
              leaveDate:
                from: date
                type: timestamp
                typeOptions:
                  func: timestampToDateTime
              dateDetails:
                from: dateDetail
                type: string
              leavePeriodDetail.value:
                from: leavePeriod
                type: string
              daysOff003:
                from: noDays
                type: string
              fromTime.value:
                from: fromHour
                type: string
                typeOptions:
                  func: timeStringToDateTime
                  args: 'HH:mm'
              endTime.value:
                from: toHour
                type: string
                typeOptions:
                  func: timeStringToDateTime
                  args: 'HH:mm'
              hoursOff003:
                from: noHours
                type: string
              iscalculation:
                from: iscalculation
              scheduleId:
                from: scheduleId
              scheduleType:
                from: scheduleType
              tssetDayOffTypeRuleId:
                from: tssetDayOffTypeRuleId
      note:
        from: note
        type: string
      naNote:
        from: naNote
        type: string
      status:
        from: enabled
        type: string
        typeOptions:
          func: YNToBoolean
      createdBy:
        from: createdBy
        type: string
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      lastUpdatedBy:
        from: updatedBy
        type: string
      lastUpdatedOn:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
        type: string
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
  - name: _dayDetail
    pagination:
    config:
      employeeCode:
        from: employeeCode
        type: string
      employeeRecordNumber:
        from: employeeRecordNumber
        type: string
      startDate:
        from: startDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      endDate:
        from: endDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      leavePeriod:
        from: leavePeriod
        type: string
      leaveDate:
        from: leaveDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      dateDetails:
        from: dateDetails
        type: string
      dayOff:
        from: dayOff
      iscalculation:
        from: iscalculation
      CAtypeOfDayOffId:
        from: CAtypeOfDayOffId
      scheduleId:
        from: scheduleId
      scheduleType:
        from: scheduleType
      tssetDayOffTypeRuleId:
        from: tssetDayOffTypeRuleId
  - name: _dayOffDetail
    pagination:
    config:
      employeeCode:
        from: employeeCode
        type: string
      employeeRecordNumber:
        from: employeeRecordNumber
        type: string
      absenceTypes:
        from: CATypeOfDaysCode
        type: string
      dayOffTypeNote:
        from: dayOffTypeNote
        type: string
      numberStandard:
        from: data.numberStandard
      fromDate:
        from: data.fromDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      toDate:
        from: data.toDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      startDate:
        from: startDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      endDate:
        from: endDate
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      leaveUnit:
        from: leaveUnit
        type: string
      remainDayOffs:
        from: data.remainDayOffs
        arrayChildren:
          typeDayOff:
            from: typeDayOff
          typeOfLeave:
            from: typeOfLeave
          expiryDate:
            from: expiryDate
            type: timestamp
            typeOptions:
              func: timestampToDateTime
          noHoursOffRemain:
            from: noHoursOffRemain
          noDaysOffRemain:
            from: noDaysOffRemain
          numberOfHoursOff:
            from: numberOfHoursOff
          numberOfDaysOff:
            from: numberOfDaysOff
          leavesPendingApproval:
            from: leavesPendingApproval
          hoursRemainingVacationFund:
            from: hoursRemainingVacationFund
          remainingVacationFund:
            from: remainingVacationFund

defaultQuery:
auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: ts-list-application-for-leaves
crudConfig:
  query:
    sort:
      - field: updatedAt
        order: DESC
  params:
    id:
      field: id
      type: string
      primary: true
    code:
      field: employeeCode
      type: string
  routes:
    exclude:
      - createManyBase
      - replaceOneBase

routes:
  - path: /api/ts-list-application-for-leaves
    method: GET
    model: _
    isExtendedFilter: true
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: "ts-list-application-for-leaves"
      query:
        Page: ":{options.page}:"
        PageSize: ":{options.limit}:"
        OrderBy: ":{options.sort}:"
        Search: ":{search}:"
        Filter: "::{filter}::"
      transform: '$merge(
                    [
                        $,
                        {
                            "data": [
                                $map($.data, function ($item){
                                    $merge([
                                        $item,
                                        {"leaveUnitName": $item.leaveUnit = "1" ? "Days" : "Hours" },
                                        {"daysOffName": $item.leaveUnit = "1" ? $item.daysOff & " Days" : null },
                                        {"hoursOffName": $item.leaveUnit = "2" ? $item.hoursOff & " h" : null }
                                    ])
                                })
                            ]
                        }
                    ]
                )'

  - path: /api/ts-list-application-for-leaves/:id
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: "ts-list-application-for-leaves/:{id}:"
      transform: '$map($, function ($item){
                        $merge([
                            $item,
                            {"leaveUnitName": $item.leaveUnit = "1" ? "Days" : "Hours" },
                            {"daysOffName": $item.leaveUnit = "1" ? $item.daysOff & " Days" : null },
                            {"hoursOffName": $item.leaveUnit = "2" ? $item.hoursOff & " h" : null },
                            {"tsapplicationForLeaveTypes": $map($item.tsapplicationForLeaveTypes, function ($item1){
                                        $merge([
                                            $item1,
                                            {"date": [$item1.startDate,$item1.endDate] },
                                            {"time": [$item1.fromTime,$item1.endTime] }
                                        ])
                                    })[]}
                        ])
                    })'

  - path: /api/ts-list-application-for-leaves
    method: POST
    model: _
    query:
    transform: "$"
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: "ts-list-application-for-leaves"
      transform: "$"

  - path: /api/ts-list-application-for-leaves/:id
    model: _
    method: PATCH
    query:
    transform: "$"
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: "ts-list-application-for-leaves/:{id}:"

  - path: /api/ts-list-application-for-leaves/:id
    method: DELETE
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: "ts-list-application-for-leaves/:{id}:"
customRoutes:
  - path: /api/ts-list-application-for-leaves/:id/histories
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: "ts-list-application-for-leaves/:{id}:/history"
      transform: "$"

  - path: /api/ts-list-application-for-leaves/day-detail
    method: GET
    model: _dayDetail
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: "ts-list-application-for-leaves/day-detail"
      query:
        EmployeeCode: ":{employeeCode}:"
        EmployeeRecordNumber: ":{employeeRecordNumber}:"
        StartDate: ":{startDate}:"
        EndDate: ":{endDate}:"
        LeavePeriod: ":{leavePeriod}:"
        CAtypeOfDayOffId: ":{CAtypeOfDayOffId}:"
      transform: "$"
  - path: /api/ts-list-application-for-leaves/get-detail-day-off
    method: POST
    model: _dayOffDetail
    query:
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: "ts-list-application-for-leaves/get-detail-day-off"
      transform: "$"
  - path: /api/ts-list-application-for-leaves/export
    method: GET
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: binary
      path: "ts-list-application-for-leaves/:export"
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: 'updatedAt desc'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$'
  - path: /api/ts-list-application-for-leaves/multidelete
    method: DELETE
    model: _DELETE
    upstreamConfig:
      method: DELETE
      path: 'ts-list-application-for-leaves'
