controller: ts-set-no-do-years
upstream: ${{UPSTREAM_TS_URL}}

models:
  - name: _
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: indexFrom
    config:
      id:
        from: id
      nationId:
        from: nationId
      nationName:
        from: nationName
      groupId:
        from: groupId
      groupName:
        from: groupName
      companyId:
        from: companyId
      companyName:
        from: companyName
      legalEntityId:
        from: legalEntityId
      legalEntityName:
        from: legalEntityName
      departmentId:
        from: deptId
      pxDeptId:
        from: PXDeptId
      departmentName:
        from: departmentName
      effectiveDate:
        from: effectiveDateFrom
        typeOptions:
          func: timestampToDateTime
      effectiveDateTo:
        from: effectiveDateTo
        typeOptions:
          func: timestampToDateTime
      period:
        from: period
      periodName:
        from: periodName
      vacationCycleFrom:
        from: vacationCycleFrom
        typeOptions:
          func: dayStringToDateTime
      vacationCycleDate:
        from: vacationCycleDate
        typeOptions:
          func: timestampToDateTime
      vacationCycleTo:
        from: vacationCycleTo
      unitType:
        from: unitType
      unitTypeName:
        from: unitType
        typeOptions:
          func: mapValueToLabel
          args:
            - value: 'D'
              label: 'Days'
            - value: 'H'
              label: 'Hours'
      startDateForCalLeave:
        from: startDateForCalLeave
      startDateForCalLeaveName:
        from: startDateForCalLeaveName
      startDateOfUse:
        from: startDateOfUse
      startDateOfUseName:
        from: startDateOfUseName
      note:
        from: note
      maxNoDayOffMove:
        from: MaxNODayOffMove
      expiredDateTranferredLeaveType:
        from: expiredDateTranferredLeaveType
      numberOfExpiredDate:
        from: numberOfExpiredDate
      setupLeaveAdvance:
        from: setupLeaveAdvance
      setupLeaveAdvanceName:
        from: setupLeaveAdvanceName
      startDateLeaveAdvanceType:
        from: startDateLeaveAdvanceType
      numberOfLeaveAdvance:
        from: numberOfLeaveAdvance
      advancedLeavePeriod:
        from: advancedLeavePeriod
      advancedLeavePeriodName:
        from: advancedLeavePeriodName
      principleOfLeaveCalculation:
        from: principleOfLeaveCalculation
      principleOfSubstractDayOff:
        from: principleOfSubstractDayOff
      tsSetPrincipleOfLeaveCalculationByDay:
        from: TSSetPrincipleOfLeaveCalculationByDay
      tsSetPrincipleOfLeaveCalculationByFunction:
        from: TSSetPrincipleOfLeaveCalculationByFunction
      tsSetPrincipleOfLeaveCalculationByStandardWork:
        from: TSSetPrincipleOfLeaveCalculationByStandardWork
      tsSetPrincipleOfSubstractDayOffByAbsenceGroup:
        from: TSSetPrincipleOfSubstractDayOffByAbsenceGroup
      tsSetPrincipleOfSubtractDayOffByFunction:
        from: TSSetPrincipleOfSubtractDayOffByFunction
      createdAt:
        from: createdAt
        typeOptions:
          func: timestampToDateTime
      createdBy:
        from: createdBy
      updatedAt:
        from: updatedAt
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
      tssetNodoyearId:
        from: tssetNodoyearId
      year:
        from: year
      name:
        from: name
      checkNationId:
        from: checkNationId
        typeOptions:
          func: YNToBoolean

defaultQuery:
auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: ts-set-no-do-years
crudConfig:
  query:
    sort:
      - field: updatedAt
        order: DESC
  params:
    id:
      field: id
      type: string
      primary: true
  routes:
    exclude:
      - createManyBase
      - replaceOneBase

routes:
  - path: /api/ts-set-no-do-years
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'ts-set-no-do-years'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$'

  - path: /api/ts-set-no-do-years/:id
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'ts-set-no-do-years/:{id}:'
      transform: '$'

  - path: /api/ts-set-no-do-years
    method: POST
    model: _
    query:
    transform: "$ ~> |$|{}, $._tabActive = 'leaveAccrual' ? ['principleOfLeaveCalculation', 'tsSetPrincipleOfLeaveCalculationByDay', 'tsSetPrincipleOfLeaveCalculationByStandardWork', 'tsSetPrincipleOfLeaveCalculationByFunction'] : ['tsSetPrincipleOfSubstractDayOffByAbsenceGroup', 'tsSetPrincipleOfSubtractDayOffByFunction', 'principleOfSubstractDayOff']|"
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'ts-set-no-do-years'
      transform: '$'

  - path: /api/ts-set-no-do-years/:id
    model: _
    method: PATCH
    query:
    transform: "$ ~> |$|{}, $._tabActive = 'leaveAccrual' ? ['principleOfLeaveCalculation', 'tsSetPrincipleOfLeaveCalculationByDay', 'tsSetPrincipleOfLeaveCalculationByStandardWork', 'tsSetPrincipleOfLeaveCalculationByFunction'] : ['tsSetPrincipleOfSubstractDayOffByAbsenceGroup', 'tsSetPrincipleOfSubtractDayOffByFunction', 'principleOfSubstractDayOff']|"
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: 'ts-set-no-do-years/:{id}:'

  - path: /api/ts-set-no-do-years/:id
    method: DELETE
    query:
    upstreamConfig:
      method: DELETE
      path: 'ts-set-no-do-years/:{id}:'

customRoutes:
  - path: /api/ts-set-no-do-years/:id/histories
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'ts-set-no-do-years/:{id}:/history'
      transform: '$'

  - path: /api/ts-set-no-do-years/by
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'ts-set-no-do-years/by'
      query:
        Filter: '::{filter}::'
      transform: '$'

  - path: /api/ts-set-no-do-years/export
    method: GET
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: binary
      path: 'ts-set-no-do-years/:export'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: 'updatedAt desc'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$'
  - path: /api/ts-set-no-do-years/list-data
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'ts-set-no-do-years/list-data'
      query:
        Page: ':{options.page}:'
        PageSize: '1000000'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
        nationId: '::{nationId}::'
        groupId: '::{groupId}::'
        companyId: '::{companyId}::'
        businessUnitId: '::{businessUnitId}::'
        divisionId: '::{divisionId}::'
        departmentId: '::{departmentId}::'
        effectiveDateFrom: '::{effectiveDate}::'
        checkNationId: '::{checkNationId}::'
        Enabled: 'Y'
      transform: '$'
  - path: /api/ts-set-no-do-years/multidelete
    method: DELETE
    model: _DELETE
    upstreamConfig:
      method: DELETE
      path: 'ts-set-no-do-years'
