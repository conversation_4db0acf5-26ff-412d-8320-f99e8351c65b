controller: ts-total-attendances
upstream: ${{UPSTREAM_TS_URL}}

models:
  - name: _
    pagination:
      data:
        from: items
      count:
        from: pageSize
      total:
        from: totalCount
      page:
        from: pageIndex
      pageCount:
        from: indexFrom
    config:
      id:
        from: id
        type: string
      code:
        from: code
        type: string
      country:
        from: nationName
        type: string
      countryCode:
        from: nationId
        type: string
      group:
        from: groupName
        type: string
      groupCode:
        from: groupId
        type: string
      company:
        from: companyName
        type: string
      companyCode:
        from: companyId
        type: string
      legalEntity:
        from: legalEntityName
        type: string
      legalEntityCode:
        from: legalEntityId
        type: string
      payGroup:
        from: payGroupName
        type: string
      payGroupCode:
        from: payGroupId
        type: string
      shortName:
        from: shortName
        type: string
      longName:
        from: fullName
        type: string
      tsmanagerTimekeepingCode:
        from: tsmanagerTimekeepingId
        type: string
      period:
        from: periodName
        type: string
      periodCode:
        from: periodId
        type: string
      startDatePeriod:
        from: startDatePeriod
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      endDatePeriod:
        from: endDatePeriod
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      status:
        from: status
        type: string
      statusName:
        from: statusName
        type: string
      revision:
        from: revision
        type: string
      note:
        from: note
        type: string
      createdBy:
        from: createdBy
        type: string
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
        type: string
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      pxdeptId:
        from: pxdeptId
        type: string
  - name: _summary
    config:
      createdBy:
        from: TSTotalAttendance.createdBy
        type: string
      createdAt:
        from: TSTotalAttendance.createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: TSTotalAttendance.updatedBy
        type: string
      updatedAt:
        from: TSTotalAttendance.updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      id:
        from: TSTotalAttendance.id
        type: string
      group:
        from: TSTotalAttendance.groupName
        type: string
      groupCode:
        from: TSTotalAttendance.groupId
      company:
        from: TSTotalAttendance.companyName
      companyCode:
        from: TSTotalAttendance.companyId
      legalEntity:
        from: TSTotalAttendance.legalEntityName
      legalEntityCode:
        from: TSTotalAttendance.legalEntityId
      payPeriod:
        from: TSTotalAttendance.periodName
      payGroup:
        from: TSTotalAttendance.payGroupName
      periodStartDate:
        from: TSTotalAttendance.startDatePeriod
        typeOptions:
          func: timestampToDateTime
      periodEndDate:
        from: TSTotalAttendance.endDatePeriod
        typeOptions:
          func: timestampToDateTime

      totalEmployees:
        from: tsSegmentInfoModel.total
      revision:
        from: tsSegmentInfoModel.revision
      version:
        from: tsSegmentInfoModel.version
      fails:
        from: tsSegmentInfoModel.countFail
      processing:
        from: tsSegmentInfoModel.countProcessing
      completed:
        from: tsSegmentInfoModel.countCompleted
      locked:
        from: tsSegmentInfoModel.countLocked
      status:
        from: TSTotalAttendance.status
      countryName:
        from: TSTotalAttendance.nationName
      countryCode:
        from: TSTotalAttendance.nationId
      # TSTotalAttendance:
      #   from: TSTotalAttendance
      # tsSegmentInfoModel:
      #   from: tsSegmentInfoModel
  - name: _synthesize
    config:
      id:
        from: tsTotalAttendanceId
        type: string
      country:
        from: nationName
        type: string
      countryCode:
        from: nationId
        type: string
      group:
        from: groupName
        type: string
      groupCode:
        from: groupId
        type: string
      company:
        from: companyName
        type: string
      companyCode:
        from: companyId
        type: string
      legalEntity:
        from: legalEntityName
        type: string
      legalEntityCode:
        from: legalEntityId
        type: string
      payGroup:
        from: payGroupName
        type: string
      payGroupCode:
        from: payGroupId
        type: string
      tsmanagerTimekeepingCode:
        from: tsmanagerTimekeepingId
        type: string
      period:
        from: periodName
        type: string
      periodCode:
        from: periodId
        type: string
      startDatePeriod:
        from: startDatePeriod
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      endDatePeriod:
        from: endDatePeriod
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      createdBy:
        from: createdBy
        type: string
      createdAt:
        from: createdAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      updatedBy:
        from: updatedBy
        type: string
      updatedAt:
        from: updatedAt
        type: timestamp
        typeOptions:
          func: timestampToDateTime
      pxdeptId:
        from: pxdeptId
        type: string
      unitCode:
        from: unitId
        type: string
      centerCode:
        from: centerId
        type: string
      employeeGroupCode:
        from: employeeGroupCode
        type: string
      businessUnitCode:
        from: businessUnitCode
      divisionCode:
        from: divisionCode
        type: string
      careerStreamCode:
        from: careerStreamId
        type: string
      employeeLevelCode:
        from: employeeLevelCode
        type: string
      contractTypeCode:
        from: contractTypeCode
        type: string
      jobTitleCode:
        from: jobTitleCode
        type: string
      status:
        from: status
        type: string
      listEmployeeCode:
        from: listEmployeeCode
        type: string
defaultQuery:
auth:
  enabled: false
  persist: false
  decorator:
    enabled: false
    feature: ts-total-attendances
crudConfig:
  query:
    sort:
      - field: updatedAt
        order: DESC
  params:
    id:
      field: id
      type: string
      primary: true
    code:
      field: code
      type: string
  routes:
    exclude:
      - createManyBase
      - replaceOneBase
defaultQuery:

routes:
  - path: /api/ts-total-attendances
    method: GET
    model: _

    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'ts-total-attendances'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ":{search}:"
        Filter: '::{filter}::'
      transform: '$merge(
                    [
                        $,
                        {
                            "data": [
                                $map($.data, function ($item){
                                    $merge([
                                        $item,
                                        {"statusName": $item.status = "1" ? "Đang thực hiện" : $item.status = "2" ? "Đã thực hiện" : $item.status = "3" ? "Đã khóa" : $item.status = "4" ? "Hoàn thành" : $item.status = "5" ? "Hủy" }
                                    ])
                                })
                            ]
                        }
                    ]
                )'

  - path: /api/ts-total-attendances/:id
    method: GET
    model: _summary

    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'ts-total-attendances/:{id}:'
      transform: '$'

  - path: /api/ts-total-attendances
    method: POST
    model: _synthesize
    query:
    transform: '$'
    upstreamConfig:
      method: POST
      response:
        dataType: object
      path: 'ts-so-ts-by-month-calculators/calc-ts-so-ts-by-month'
      transform: '$'

  - path: /api/ts-total-attendances/:id
    model: _
    method: PATCH
    query:
    transform: '$'
    upstreamConfig:
      response:
        dataType: object
      method: PUT
      path: 'ts-total-attendances/:{id}:'

  - path: /api/ts-total-attendances/:id
    method: DELETE
    query:
      $and:
    upstreamConfig:
      method: DELETE
      path: 'ts-total-attendances/:{id}:'
customRoutes:
  - path: /api/ts-total-attendances/:id/histories
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: 'ts-total-attendances/:{id}:/history'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
      transform: '$'

  - path: /api/ts-total-attendances/by
    method: GET
    model: _
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: array
      path: "ts-total-attendances/by"
      query:
        Filter: "::{filter}::"
      transform: '$'

  - path: /api/ts-total-attendances/export
    method: GET
    query:
    upstreamConfig:
      method: GET
      response:
        dataType: binary
      path: "ts-total-attendances/:export"
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: 'updatedAt desc'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$'

  - path: /api/ts-total-attendances/summary/:id
    method: GET
    model: _summary

    query:
    upstreamConfig:
      method: GET
      response:
        dataType: object
      path: 'ts-total-attendances/:{id}:'
      transform: '$'

  - path: /api/ts-total-attendances/ts-manager-timekeeping
    method: GET
    model: _

    query:
    upstreamConfig:
      method: GET
      response:
        dataType: paginated
      path: 'ts-total-attendances/ts-manager-timekeeping'
      query:
        Page: ':{options.page}:'
        PageSize: ':{options.limit}:'
        OrderBy: ':{options.sort}:'
        Search: ':{search}:'
        Filter: '::{filter}::'
      transform: '$'
