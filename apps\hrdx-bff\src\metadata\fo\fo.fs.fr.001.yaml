id: FO.FS.FR.001
status: draft
sort: 193
user_created: 43878dbb-aa6c-4ef4-8bd7-0ea811ffafa7
date_created: '2024-06-14T03:36:00.632Z'
user_updated: 35b4f590-f747-4e53-8fa5-76aba65555c2
date_updated: '2025-05-06T09:25:17.114Z'
title: Job Family
requirement:
  time: 1721035399767
  blocks:
    - id: SaJ8xe8Vyz
      type: paragraph
      data:
        text: Chức năng cho phép tạo mới/cập nhật thông tin Job Family
    - id: p2z6wORcbX
      type: paragraph
      data:
        text: Chức năng cho phép tìm kiếm danh mục Job Family
  version: 2.29.1
screen_design: null
module: FO
local_fields:
  - code: code
    pinned: true
    title: Job Family Code
    data_type:
      key: Increment ID
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    options__tabular__column_width: null
    options__tabular__align: left
    show_sort: true
    resizable: false
    dragable: false
  - code: shortName
    title: Short Name
    description: >-
      <PERSON><PERSON><PERSON> thị tên viết tắt của Job Family tương ứng với mã Job Family theo tiêu
      chí tìm kiếm
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    options__tabular__column_width: null
    options__tabular__align: left
    show_sort: true
    resizable: false
    dragable: false
  - code: longName
    title: Long Name
    description: >-
      Hiển thị tên đầy đủ của Job Family tương ứng với mã Job Family theo tiêu
      chí tìm kiếm
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    pinned: false
    options__tabular__align: left
    show_sort: true
    resizable: false
    dragable: false
  - code: status
    title: Status
    description: >
      Hiển thị trạng thái của các Job Family tương ứng với mã Job Family theo
      tiêu chí tìm kiếm
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: Boolean Tag
      collection: field_types
    options__tabular__column_width: null
    options__tabular__align: left
    show_sort: true
    resizable: false
    dragable: false
mock_data:
  - effectiveDate: 2024/01/01
    code: '00000001'
    status: true
    shortName:
      default: HRM
      vietnamese: HRM
      english: HRM
    longName:
      default: Human Resource Management
      vietnamese: Human Resource Management
      english: Human Resource Management
  - effectiveDate: 2024/01/02
    code: '00000002'
    status: false
    shortName:
      default: FIN
      vietnamese: FIN
      english: FIN
    longName:
      default: Financial Management
      vietnamese: Financial Management
      english: Financial Management
  - effectiveDate: 2024/01/03
    code: '00000003'
    status: false
    shortName:
      default: ENG
      vietnamese: ENG
      english: ENG
    longName:
      default: Engineering
      vietnamese: Engineering
      english: Engineering
  - effectiveDate: 2024/01/04
    code: '00000004'
    status: true
    shortName:
      default: MKT
      vietnamese: MKT
      english: MKT
    longName:
      default: Marketing
      vietnamese: Marketing
      english: Marketing
  - effectiveDate: 2024/01/05
    code: '00000005'
    status: true
    shortName:
      default: IT
      vietnamese: IT
      english: IT
    longName:
      default: Information Technology
      vietnamese: Information Technology
      english: Information Technology
  - effectiveDate: 2024/01/06
    code: '00000006'
    status: true
    shortName:
      default: PRD
      vietnamese: PRD
      english: PRD
    longName:
      default: Product Development
      vietnamese: Product Development
      english: Product Development
  - effectiveDate: 2024/01/07
    code: '00000007'
    status: true
    shortName:
      default: SALES
      vietnamese: SALES
      english: SALES
    longName:
      default: Sales
      vietnamese: Sales
      english: Sales
  - effectiveDate: 2024/01/08
    code: '00000008'
    status: true
    shortName:
      default: SUP
      vietnamese: SUP
      english: SUP
    longName:
      default: Supply Chain Management
      vietnamese: Supply Chain Management
      english: Supply Chain Management
local_buttons: null
layout: layout-table
form_config:
  fields:
    - type: group
      _condition:
        transform: $not($.extend.formType = 'view')
      n_cols: 2
      fields:
        - name: code
          label: Job Family Code
          type: text
          placeholder: Enter Job Family Code
          _disabled:
            transform: $not($.extend.formType = 'create')
          validators:
            - type: required
            - type: maxLength
              args: '8'
              text: Maximum 8 characters
            - type: pattern
              args: ^[a-zA-Z0-9_.&]+$
              text: The Code must not contain special characters.
        - type: dateRange
          label: Effective Date
          name: effectiveDate
          mode: date-picker
          _value:
            transform: >-
              $.extend.formType = 'create' and $not($.extend.isDuplicate) ?
              $not($exists($.fields.effectiveDate)) ?  $now()
          validators:
            - type: required
          setting:
            format: dd/MM/yyyy
            type: date
        - name: status
          label: Status
          type: radio
          _value:
            transform: $.extend.formType = 'create' and $not($.extend.isDuplicate) ? true
          validators:
            - type: required
          radio:
            - label: Active
              value: true
            - label: Inactive
              value: false
        - name: shortName
          label: Short Name
          placeholder: Enter Short Name
          type: translation
          validators:
            - type: maxLength
              args: '40'
              text: Maximum 40 characters
    - name: code
      label: Job Family Code
      type: text
      disabled: true
      placeholder: Automatic
      _condition:
        transform: $.extend.formType = 'view'
    - type: dateRange
      label: Effective Date
      name: effectiveDate
      mode: date-picker
      setting:
        format: dd/MM/yyyy
        type: date
      _condition:
        transform: $.extend.formType = 'view'
    - name: status
      label: Status
      type: radio
      readOnly: 'true'
      radio:
        - label: Active
          value: true
        - label: Inactive
          value: false
      _condition:
        transform: $.extend.formType = 'view'
    - name: shortName
      label: Short Name
      placeholder: Enter Short Name
      type: translation
      _condition:
        transform: $.extend.formType = 'view'
      validators:
        - type: maxLength
          args: '40'
          text: Maximum 40 characters
    - name: longName
      label: Long Name
      placeholder: Enter Long Name
      type: translation
      _condition:
        transform: >-
          $.extend.formType = 'create' or $.extend.formType = 'edit' or
          $.extend.formType = 'proceed'
      validators:
        - type: required
        - type: maxLength
          args: '120'
          text: Maximum 120 characters
    - name: longName
      label: Long Name
      placeholder: Enter Long Name
      type: translation
      _condition:
        transform: $.extend.formType = 'view'
    - name: description
      label: Description
      type: translationTextArea
      validators:
        - type: maxLength
          args: '4000'
          text: Maximum 4000 characters.
      textarea:
        autoSize:
          minRows: 3
          maxRows: 5
        maxCharCount: 4000
      placeholder: Enter Description
  historyHeaderTitle: '''View History Job Family '''
filter_config:
  fields:
    - name: code
      label: Job Family Code
      placeholder: Enter Job Family Code
      labelType: type-grid
      type: text
    - name: status
      label: Status
      type: radio
      labelType: type-grid
      value: ''
      radio:
        - label: All
          value: ''
        - label: Active
          value: true
        - label: Inactive
          value: false
    - name: shortName
      labelType: type-grid
      label: Short Name
      placeholder: Enter Short Name
      type: text
    - name: longName
      labelType: type-grid
      label: Long Name
      placeholder: Enter Long Name
      type: text
  filterMapping:
    - field: code
      operator: $cont
      valueField: code
    - field: status
      operator: $eq
      valueField: status
    - field: na_shortName
      operator: $cont
      valueField: shortName
    - field: na_name
      operator: $cont
      valueField: longName
  sources:
    jobFamilyList:
      uri: '"/api/job-families/by"'
      method: GET
      queryTransform: '{''filter'': [{''field'':''status'',''operator'': ''$eq'',''value'':true}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
layout_options:
  toolTable:
    export: true
    import: true
    adjustDisplay: true
  tool_table:
    - id: import
      icon: icon-upload-simple
      href: /GE/HR.FS.FR.092
    - id: export
      icon: icon-download-simple
  delete_multi_items: true
  view_after_updated: true
  view_history_after_created: true
  custom_history_backend_url: /api/job-families/insert-new-record
  hide_action_row: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    title: Edit
    icon: icon-pencil-simple
    type: ghost-gray
  - id: delete
    title: Delete
    icon: icon-trash
    group: null
    type: ghost-gray
backend_url: /api/job-families
screen_name: job-families
layout_options__actions_many: null
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key: null
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Job Family
  parent:
    title: Job Structure
