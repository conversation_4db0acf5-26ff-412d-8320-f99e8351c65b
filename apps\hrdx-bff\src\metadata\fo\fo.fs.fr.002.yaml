id: FO.FS.FR.002
status: draft
sort: 191
user_created: 43878dbb-aa6c-4ef4-8bd7-0ea811ffafa7
date_created: '2024-06-14T03:36:49.036Z'
user_updated: 35b4f590-f747-4e53-8fa5-76aba65555c2
date_updated: '2025-07-19T02:40:57.435Z'
title: Job SubFamily
requirement:
  time: 1745483158076
  blocks:
    - id: MzahThqCeL
      type: paragraph
      data:
        text: Chức năng cho phép tạo mới/cập nhật thông tin Job SubFamily
    - id: rxkYwao8j1
      type: paragraph
      data:
        text: Chức năng cho phép tìm kiếm danh mục Job SubFamily
  version: 2.30.7
screen_design: null
module: FO
local_fields:
  - code: code
    pinned: true
    title: Job SubFamily Code
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    options__tabular__column_width: null
    show_sort: true
  - code: shortName
    title: Short Name
    description: >-
      <PERSON><PERSON><PERSON> thị tên viết tắt của Job Family tương ứng với mã Job Family theo tiêu
      chí tìm kiếm
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    pinned: false
    options__tabular__column_width: null
    show_sort: true
  - code: longName
    title: Long Name
    description: >-
      Hiển thị tên đầy đủ của Job Family tương ứng với mã Job Family theo tiêu
      chí tìm kiếm
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: parentJobFamily
    title: Job Family
    description: >-
      Hiển thị Mã viết tắt (Short Name) của Job Family tương ứng với mã Job
      SubFamily theo tiêu chí tìm kiếm
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: null
    options__tabular__align: left
    show_sort: true
  - code: status
    title: Status
    description: >
      Hiển thị trạng thái của các Job Family tương ứng với mã Job Family theo
      tiêu chí tìm kiếm
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: Boolean Tag
      collection: field_types
    options__tabular__column_width: null
    options__tabular__align: left
    show_sort: true
mock_data:
  - code: '00000001'
    shortName:
      default: GMT
      vietnamese: GMT
      english: GMT
    longName:
      default: Genaral Management
      vietnamese: Genaral Management
      english: Genaral Management
    parentCode: HRM
    effectiveDate: 2024/03/06
    status: true
  - code: '00000002'
    shortName:
      default: PER
      vietnamese: PER
      english: PER
    longName:
      default: Personnel Administration
      vietnamese: Personnel Administration
      english: Personnel Administration
    parentCode: HRM
    effectiveDate: 2024/03/07
    status: false
  - code: '00000003'
    shortName:
      default: ADS
      vietnamese: ADS
      english: ADS
    longName:
      default: Administrative support
      vietnamese: Administrative support
      english: Administrative support
    parentCode: ENG
    effectiveDate: 2024/03/08
    status: true
local_buttons: null
layout: layout-table
form_config:
  fields:
    - type: group
      n_cols: 2
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - name: code
          label: Job SubFamily Code
          type: text
          placeholder: Enter Job SubFamily Code
          _disabled:
            transform: $not($.extend.formType = 'create')
          validators:
            - type: required
            - type: maxLength
              args: '8'
              text: Maximum 8 characters
            - type: pattern
              args: ^[a-zA-Z0-9_.&]+$
              text: The Code must not contain special characters.
        - type: dateRange
          label: Effective Date
          name: effectiveDate
          mode: date-picker
          _value:
            transform: >-
              $.extend.formType = 'create' and
              $not($exists($.fields.effectiveDate)) ? $now() :
              $.fields.effectiveDate
          setting:
            type: date
            format: dd/MM/yyyy
          validators:
            - type: required
        - name: status
          label: Status
          type: radio
          _condition:
            transform: $not($.extend.formType = 'view')
          _value:
            transform: $.extend.formType = 'create' and $not($.extend.isDuplicate) ? true
          radio:
            - label: Active
              value: true
            - label: Inactive
              value: false
        - name: shortName
          label: Short Name
          placeholder: Enter Short Name
          type: translation
          validators:
            - type: maxLength
              args: '40'
              text: Maximum 40 characters
    - name: code
      label: Job SubFamily Code
      type: text
      disabled: true
      _condition:
        transform: $.extend.formType = 'view'
    - type: dateRange
      label: Effective Date
      name: effectiveDate
      mode: date-picker
      setting:
        format: dd/MM/yyyy
        type: date
      _condition:
        transform: $.extend.formType = 'view'
    - name: status
      label: Status
      type: radio
      _condition:
        transform: $.extend.formType = 'view'
      radio:
        - label: Active
          value: true
        - label: Inactive
          value: false
    - name: shortName
      label: Short Name
      placeholder: Enter Short Name
      type: translation
      validators:
        - type: maxLength
          args: '40'
          text: Maximum 40 characters
      _condition:
        transform: $.extend.formType = 'view'
    - name: longName
      label: Long Name
      placeholder: Enter Long Name
      type: translation
      validators:
        - type: required
        - type: maxLength
          args: '120'
          text: Maximum 120 characters
      _condition:
        transform: $not($.extend.formType = 'view')
    - name: longName
      label: Long Name
      placeholder: Enter Long Name
      type: translation
      validators:
        - type: maxLength
          args: '120'
          text: Maximum 120 characters
      _condition:
        transform: $.extend.formType = 'view'
    - name: parentCode
      label: Job Family
      type: selectCustom
      placeholder: Select Job Family
      validators:
        - type: required
      actions:
        - view
      actionsConfig:
        view:
          formConfig:
            fields:
              - name: code
                label: Job Family Code
                type: text
              - type: dateRange
                label: Effective Date
                name: effectiveDate
                mode: date-picker
                setting:
                  format: dd/MM/yyyy
                  type: date
              - name: status
                label: Status
                type: radio
                radio:
                  - label: Active
                    value: true
                  - label: Inactive
                    value: false
              - name: shortName
                label: Short Name
                type: translation
              - name: longName
                label: Long Name
                type: translation
              - name: description
                label: Description
                type: translationTextArea
      outputValue: value
      _validateFn:
        transform: >-
          $exists($.value.code) ?
          ($jobFamilyList($.fields.effectiveDate,$.value.code,($not($.extend.formType
          = 'edit') or $not($DateFormat($.fields.effectiveDate, 'yyyy-MM-DD') =
          $DateFormat($.extend.defaultValue.effectiveDate, 'yyyy-MM-DD'))) ?
          true)[0] ?
          $jobFamilyList($.fields.effectiveDate,$.value.code,($not($.extend.formType
          = 'edit') or $not($DateFormat($.fields.effectiveDate, 'yyyy-MM-DD') =
          $DateFormat($.extend.defaultValue.effectiveDate, 'yyyy-MM-DD'))) ?
          true)[0] : '_setSelectValueNull')
      _select:
        transform: $jobFamilyList($.fields.effectiveDate,null,true)
      _condition:
        transform: $not($.extend.formType = 'view')
    - name: parentCode
      label: Job Family
      type: selectCustom
      placeholder: Select Job Family
      outputValue: value
      inputValue: code
      actions:
        - view
      actionsConfig:
        view:
          formConfig:
            fields:
              - name: code
                label: Job Family Code
                type: text
              - type: dateRange
                label: Effective Date
                name: effectiveDate
                mode: date-picker
                setting:
                  format: dd/MM/yyyy
                  type: date
              - name: status
                label: Status
                type: radio
                radio:
                  - label: Active
                    value: true
                  - label: Inactive
                    value: false
              - name: shortName
                label: Short Name
                type: translation
              - name: longName
                label: Long Name
                type: translation
              - name: description
                label: Description
                type: translationTextArea
      _select:
        transform: >-
          $exists($.fields.effectiveDate) ?
          $jobFamilyList($.fields.effectiveDate,$.extend.defaultValue.parentCode.code)
      _validateFn:
        transform: >-
          $exists($.extend.defaultValue.parentCode.code) ?
          ($jobFamilyList($.fields.effectiveDate,$.extend.defaultValue.parentCode.code)[0]
          ?
          $jobFamilyList($.fields.effectiveDate,$.extend.defaultValue.parentCode.code)[0]
          : '_setSelectValueNull')
      _condition:
        transform: $.extend.formType = 'view'
    - name: description
      label: Description
      type: translationTextArea
      validators:
        - type: maxLength
          args: '4000'
          text: Maximum 4000 characters.
      textarea:
        autoSize:
          minRows: 3
          maxRows: 5
        maxCharCount: 4000
      placeholder: Enter Description
  historyHeaderTitle: '''View History Job SubFamily '''
  sources:
    jobFamilyList:
      uri: '"/api/job-families/by"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':$.status},{'field':'code','operator':
        '$eq','value':$.code}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id': $item.id, 'code': $item.code},
        'additionalData': $item}})[]
      disabledCache: true
      params:
        - effectiveDate
        - code
        - status
filter_config:
  fields:
    - name: code
      label: Job SubFamily Code
      type: text
      labelType: type-grid
      placeholder: Enter Job SubFamily Code
    - name: status
      label: Status
      type: radio
      labelType: type-grid
      value: ''
      radio:
        - label: All
          value: ''
        - label: Active
          value: true
        - label: Inactive
          value: false
    - name: shortName
      label: Short Name
      labelType: type-grid
      placeholder: Enter Short Name
      type: text
    - name: longName
      label: Long Name
      labelType: type-grid
      placeholder: Enter Long Name
      type: text
    - type: selectAll
      label: Job Family
      isLazyLoad: true
      labelType: type-grid
      name: parentJobFamilyCode
      placeholder: Select Job Family
      _options:
        transform: $jobFamilyList($.extend.limit,$.extend.page,$.extend.search)
  filterMapping:
    - field: code
      operator: $cont
      valueField: code
    - field: status
      operator: $eq
      valueField: status
    - field: na_shortName
      operator: $cont
      valueField: shortName
    - field: na_name
      operator: $cont
      valueField: longName
    - field: parentJobFamilyCode
      operator: $in
      valueField: parentJobFamilyCode.(value)
  sources:
    jobFamilyList:
      uri: '"/api/job-families/get-by"'
      method: GET
      queryTransform: >-
        {'limit':$.limit,'page':$.page,'filter': [{'field':'search','operator':
        '$eq','value':$.search}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
layout_options:
  toolTable:
    export: true
    import: true
    adjustDisplay: true
  tool_table:
    - id: import
      icon: icon-upload-simple
      href: /GE/HR.FS.FR.092
    - id: export
      icon: icon-download-simple
  view_after_updated: true
  delete_multi_items: true
  view_history_after_created: true
  custom_history_backend_url: /api/job-sub-families/insert-new-record
  hide_action_row: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    title: Edit
    icon: icon-pencil-simple
    type: ghost-gray
  - id: delete
    title: Delete
    icon: icon-trash
    group: null
    type: ghost-gray
backend_url: /api/job-sub-families
screen_name: null
layout_options__actions_many: null
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key: null
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Job SubFamily
  parent:
    title: Job Structure
