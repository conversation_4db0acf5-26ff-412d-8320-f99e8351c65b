id: FO.FS.FR.003
status: draft
sort: 190
user_created: 43878dbb-aa6c-4ef4-8bd7-0ea811ffafa7
date_created: '2024-06-14T03:37:11.384Z'
user_updated: 35b4f590-f747-4e53-8fa5-76aba65555c2
date_updated: '2025-07-19T02:40:33.853Z'
title: Job Specialization
requirement:
  time: 1749438793198
  blocks:
    - id: VtYF4TBp8B
      type: paragraph
      data:
        text: Chức năng cho phép tìm kiếm danh mục Job Specialization
    - id: lKKwKcFzuV
      type: paragraph
      data:
        text: Chức năng cho phép tạo mới/cập nhật thông tin Job Specialization&nbsp;
  version: 2.30.7
screen_design: null
module: FO
local_fields:
  - code: code
    pinned: true
    title: Job Specialization Code
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    description: >-
      <PERSON><PERSON><PERSON> thị các mã Job Specialization đã được tạo trên hệ thống theo tiêu chí
      tìm kiếm
    options__tabular__column_width: null
    show_sort: true
  - code: shortName
    title: Short Name
    description: >-
      Hiển thị tên viết tắt của Job Specialization tương ứng với mã Job
      Specialization theo tiêu chí tìm kiếm
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    options__tabular__column_width: null
    show_sort: true
  - code: longName
    title: Long Name
    description: >-
      Hiển thị tên đầy đủ của Job Specialization tương ứng với mã Job
      Specialization theo tiêu chí tìm kiếm
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: jobFamiliesNew
    title: Job Family
    description: >-
      Hiển thị Mã viết tắt (Short Name) của Job Family tương ứng với mã Job
      SubFamily theo tiêu chí tìm kiếm
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: null
    show_sort: true
  - code: jobSubFamiliesNew
    title: Job SubFamily
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: null
    show_sort: true
  - code: status
    title: Status
    description: >
      Hiển thị trạng thái của các Job Specialization tương ứng với mã Job
      Specialization theo tiêu chí tìm kiếm
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: Boolean Tag
      collection: field_types
    options__tabular__column_width: null
    options__tabular__align: center
    show_sort: true
mock_data:
  - code: '00000001'
    shortName:
      default: EXDS
      vietnamese: EXDS
      english: EXDS
    effectiveDate: 08/05/2024
    longName:
      default: Employee Experience Design
      vietnamese: Employee Experience Design
      english: Employee Experience Design
    parentCode: HRM
    parentSubCode: ERD
    status: true
    parentJobSubFamily: Marketing Departmentt
    parentJobFamily: ''
  - code: '00000002'
    shortName:
      default: EXEX
      vietnamese: EXEX
      english: EXEX
    effectiveDate: 07/05/2024
    longName:
      default: Employee Experience Excutive
      vietnamese: Employee Experience Excutive
      english: Employee Experience Excutive
    parentCode: HRM
    parentSubCode: ERD
    status: true
    parentJobSubFamily: Marketing Departmentt
    parentJobFamily: ''
local_buttons: null
layout: layout-table
form_config:
  formSize:
    create: small
    edit: small
  fields:
    - type: group
      n_cols: 2
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - name: code
          label: Job Specialization Code
          type: text
          placeholder: Enter Job Specialization Code
          _disabled:
            transform: $not($.extend.formType = 'create')
          validators:
            - type: required
            - type: maxLength
              args: '8'
              text: Maximum 8 characters
            - type: pattern
              args: ^[a-zA-Z0-9_.&]+$
              text: The Code must not contain special characters.
        - type: dateRange
          label: Effective Date
          name: effectiveDate
          mode: date-picker
          _value:
            transform: $.extend.formType = 'create' ? $now()
          setting:
            format: dd/MM/yyyy
            type: date
          validators:
            - type: required
        - name: status
          label: Status
          type: radio
          _value:
            transform: $.extend.formType = 'create' and $not($.extend.isDuplicate) ? true
          radio:
            - label: Active
              value: true
            - label: Inactive
              value: false
        - name: shortName
          label: Short Name
          placeholder: Enter Short Name
          type: translation
          validators:
            - type: maxLength
              args: '40'
              text: Maximum 40 characters
    - name: code
      label: Job Specialization Code
      type: text
      disabled: true
      _condition:
        transform: $.extend.formType = 'view'
    - type: dateRange
      label: Effective Date
      name: effectiveDate
      mode: date-picker
      setting:
        format: dd/MM/yyyy
        type: date
      _condition:
        transform: $.extend.formType = 'view'
    - name: status
      label: Status
      type: radio
      _value:
        transform: $.extend.formType = 'create' and $not($.extend.isDuplicate) ? true
      radio:
        - label: Active
          value: true
        - label: Inactive
          value: false
      _condition:
        transform: $.extend.formType = 'view'
    - name: shortName
      label: Short Name
      placeholder: Enter Short Name
      type: translation
      validators:
        - type: maxLength
          args: '40'
          text: Maximum 40 characters
      _condition:
        transform: $.extend.formType = 'view'
    - name: longName
      label: Long Name
      placeholder: Enter Long Name
      type: translation
      validators:
        - type: required
        - type: maxLength
          args: '120'
          text: Maximum 120 characters
      _condition:
        transform: >-
          $.extend.formType = 'create' or $.extend.formType = 'edit' or
          $.extend.formType = 'proceed'
    - name: longName
      label: Long Name
      placeholder: Enter Long Name
      type: translation
      validators:
        - type: maxLength
          args: '120'
          text: Maximum 120 characters
      _condition:
        transform: $.extend.formType = 'view'
    - type: selectCustom
      label: Job Family
      validators:
        - type: required
      name: jobFamilyObj
      outputValue: value
      _condition:
        transform: $not($.extend.formType = 'view')
      placeholder: Select Job Family
      actions:
        - view
      actionsConfig:
        view:
          formConfig:
            fields:
              - name: code
                label: Job Family Code
                type: text
              - type: dateRange
                label: Effective Date
                name: effectiveDate
                mode: date-picker
                setting:
                  format: dd/MM/yyyy
                  type: date
              - name: status
                label: Status
                type: radio
                radio:
                  - label: Active
                    value: true
                  - label: Inactive
                    value: false
              - name: shortName
                label: Short Name
                type: translation
              - name: longName
                label: Long Name
                type: translation
              - name: description
                label: Description
                type: translationTextArea
      clearFieldsAfterChange:
        - jobSubFamilyObj
      _select:
        transform: $jobFamilyList($.fields.effectiveDate,null,true)
      _validateFn:
        transform: >-
          $exists($.value.code) ? ($exists($.fields.jobFamilyObj) ?
          $jobFamilyList($.fields.effectiveDate,$.value.code,($not($.extend.formType
          = 'edit') or $not($DateFormat($.fields.effectiveDate, 'yyyy-MM-DD') =
          $DateFormat($.extend.defaultValue.effectiveDate, 'yyyy-MM-DD'))) ?
          true)[0] ?
          $jobFamilyList($.fields.effectiveDate,$.value.code,($not($.extend.formType
          = 'edit') or $not($DateFormat($.fields.effectiveDate, 'yyyy-MM-DD') =
          $DateFormat($.extend.defaultValue.effectiveDate, 'yyyy-MM-DD'))) ?
          true)[0] : '_setSelectValueNull')
    - type: selectCustom
      label: Job Family
      _condition:
        transform: $.extend.formType = 'view'
      name: jobFamilyObj
      outputValue: value
      inputValue: code
      placeholder: Select Job Family
      actions:
        - view
      actionsConfig:
        view:
          formConfig:
            fields:
              - name: code
                label: Job Family Code
                type: text
              - type: dateRange
                label: Effective Date
                name: effectiveDate
                mode: date-picker
                setting:
                  format: dd/MM/yyyy
                  type: date
              - name: status
                label: Status
                type: radio
                radio:
                  - label: Active
                    value: true
                  - label: Inactive
                    value: false
              - name: shortName
                label: Short Name
                type: translation
              - name: longName
                label: Long Name
                type: translation
              - name: description
                label: Description
                type: translationTextArea
      _validateFn:
        transform: >-
          $exists($.extend.defaultValue.jobFamilyObj.code) ?
          ($exists($.fields.jobFamilyObj) ?
          $jobFamilyList($.fields.effectiveDate,$.extend.defaultValue.jobFamilyObj.code)[0]
          ?
          $jobFamilyList($.fields.effectiveDate,$.extend.defaultValue.jobFamilyObj.code)[0]
          : '_setSelectValueNull')
      _select:
        transform: >-
          $jobFamilyList($.fields.effectiveDate,$.extend.defaultValue.jobFamilyObj.code)
    - type: selectCustom
      label: Job SubFamily
      name: jobSubFamilyObj
      placeholder: 'Select Job SubFamily '
      outputValue: value
      _condition:
        transform: $not($.extend.formType = 'view')
      validators:
        - type: required
      actions:
        - view
      actionsConfig:
        view:
          formConfig:
            fields:
              - name: code
                label: Job SubFamily Code
                type: text
              - type: dateRange
                label: Effective Date
                name: effectiveDate
                mode: date-picker
                setting:
                  format: dd/MM/yyyy
                  type: date
              - name: status
                label: Status
                type: radio
                radio:
                  - label: Active
                    value: true
                  - label: Inactive
                    value: false
              - name: shortName
                label: Short Name
                type: translation
              - name: longName
                label: Long Name
                type: translation
              - name: parentJobFamily
                label: Job Family
                type: text
              - name: description
                label: Description
                type: translationTextArea
      _select:
        transform: >-
          $exists($.fields.jobFamilyObj) ?
          $jobSubFamilyList($.fields.effectiveDate,
          $test($.fields.jobFamilyObj.id),null,true)
      _validateFn:
        transform: >-
          $exists($test($.value.code)) ?
          ($jobSubFamilyList($.fields.effectiveDate,
          null,$.value.code,($not($.extend.formType = 'edit') or
          $not($DateFormat($.fields.effectiveDate, 'yyyy-MM-DD') =
          $DateFormat($.extend.defaultValue.effectiveDate, 'yyyy-MM-DD'))) ?
          true)[0] ?
          $jobSubFamilyList($.fields.effectiveDate,null,$.value.code,($not($.extend.formType
          = 'edit') or $not($DateFormat($.fields.effectiveDate, 'yyyy-MM-DD') =
          $DateFormat($.extend.defaultValue.effectiveDate, 'yyyy-MM-DD'))) ?
          true)[0] : '_setSelectValueNull')
    - type: selectCustom
      label: Job SubFamily
      name: jobSubFamilyObj
      placeholder: Select Job SubFamily
      outputValue: value
      inputValue: code
      _condition:
        transform: $.extend.formType = 'view'
      actions:
        - view
      actionsConfig:
        view:
          formConfig:
            fields:
              - name: code
                label: Job SubFamily Code
                type: text
              - type: dateRange
                label: Effective Date
                name: effectiveDate
                mode: date-picker
                setting:
                  format: dd/MM/yyyy
                  type: date
              - name: status
                label: Status
                type: radio
                radio:
                  - label: Active
                    value: true
                  - label: Inactive
                    value: false
              - name: shortName
                label: Short Name
                type: translation
              - name: longName
                label: Long Name
                type: translation
              - name: parentJobFamily
                label: Job Family
                type: text
              - name: description
                label: Description
                type: translationTextArea
      _validateFn:
        transform: >-
          $exists($test($.extend.defaultValue.jobSubFamilyObj.code)) ?
          ($jobSubFamilyList($.fields.effectiveDate,
          null,$.extend.defaultValue.jobSubFamilyObj.code)[0] ?
          $jobSubFamilyList($.fields.effectiveDate,null,$.extend.defaultValue.jobSubFamilyObj.code)[0]
          : '_setSelectValueNull')
      _select:
        transform: >-
          $exists($.fields.jobFamilyObj) ?
          $jobSubFamilyList($.fields.effectiveDate,null,$.extend.defaultValue.jobSubFamilyObj.code)
    - name: description
      label: Description
      type: translationTextArea
      validators:
        - type: maxLength
          args: '4000'
          text: Maximum 4000 characters.
      textarea:
        autoSize:
          minRows: 3
          maxRows: 5
        maxCharCount: 4000
      placeholder: Enter Description
  historyHeaderTitle: '''View History Job Specialization'''
  sources:
    jobFamilyList:
      uri: '"/api/job-families/by"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':$.status}, {'field':'code','operator': '$eq','value':
        $.code }]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id': $item.id, 'code': $item.code},
        'additionalData': $item}})[]
      disabledCache: true
      params:
        - effectiveDate
        - code
        - status
    jobSubFamilyList:
      uri: '"/api/job-sub-families/by"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':$.status},{'field':'jobFamilyId','operator':
        '$eq','value':$.jobFamilyId},{'field':'code','operator': '$eq','value':
        $.code }]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id': $item.id, 'code': $item.code},
        'additionalData': {'id': $item.id,'code': $item.code , 'effectiveDate':
        $item.effectiveDate, 'longName': $item.longName, 'shortName':
        $item.shortName, 'description': $item.description, 'status':
        $item.status, 'parentJobFamily': $item.parentJobFamily} ,'jobFamilyId':
        $item.jobFamilyId}})[]
      disabledCache: true
      params:
        - effectiveDate
        - jobFamilyId
        - code
        - status
filter_config:
  fields:
    - placeholder: Enter Job Specialization Code
      type: text
      label: Job Specialization Code
      labelType: type-grid
      name: code
    - type: radio
      label: Status
      name: status
      labelType: type-grid
      value: ''
      radio:
        - label: All
          value: ''
        - label: Active
          value: true
        - label: Inactive
          value: false
    - type: text
      label: Short Name
      labelType: type-grid
      placeholder: Enter Short Name
      name: shortName
    - type: text
      label: Long Name
      labelType: type-grid
      placeholder: Enter Long Name
      name: longName
    - type: selectAll
      label: Job Family
      isLazyLoad: true
      labelType: type-grid
      name: jobFamilyId
      placeholder: Select Job Family
      _options:
        transform: $jobFamilyList($.extend.limit,$.extend.page,$.extend.search)
    - type: selectAll
      label: Job SubFamily
      labelType: type-grid
      isLazyLoad: true
      name: jobSubFamilyId
      placeholder: Select Job SubFamily
      _options:
        transform: $jobSubFamilyList($.extend.limit,$.extend.page,$.extend.search)
  filterMapping:
    - field: code
      operator: $cont
      valueField: code
    - field: status
      operator: $eq
      valueField: status
    - field: na_name
      operator: $cont
      valueField: longName
    - field: na_shortName
      operator: $cont
      valueField: shortName
    - field: jobSubFamilyId
      operator: $in
      valueField: jobSubFamilyId.(value)
    - field: jobFamilyId
      operator: $in
      valueField: jobFamilyId.(value)
  sources:
    jobFamilyList:
      uri: '"/api/job-families/get-by"'
      method: GET
      queryTransform: >-
        {'limit':$.limit,'page':$.page,'filter': [{'field':'search','operator':
        '$eq','value':$.search}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.id}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    jobSubFamilyList:
      uri: '"/api/job-sub-families/get-by"'
      method: GET
      queryTransform: >-
        {'limit':$.limit,'page':$.page,'filter': [{'field':'search','operator':
        '$eq','value':$.search}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.id}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
layout_options:
  toolTable:
    export: true
    import: true
    adjustDisplay: true
  tool_table:
    - id: import
      icon: icon-upload-simple
    - id: export
      icon: icon-download-simple
  view_after_updated: true
  delete_multi_items: true
  view_history_after_created: true
  custom_history_backend_url: /api/job-specializations/insert-new-record
  hide_action_row: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    title: Edit
    icon: icon-pencil-simple
    type: ghost-gray
  - id: delete
    title: Delete
    icon: icon-trash
    group: null
    type: ghost-gray
backend_url: /api/job-specializations
screen_name: null
layout_options__actions_many: null
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key: null
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Job Specialization
  parent:
    title: Job Structure
