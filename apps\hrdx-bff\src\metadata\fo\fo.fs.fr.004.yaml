id: FO.FS.FR.004
status: draft
sort: 139
user_created: 59722cb8-f858-481b-97f1-c97dd2a8e4be
date_created: '2024-06-14T03:35:44.125Z'
user_updated: 35b4f590-f747-4e53-8fa5-76aba65555c2
date_updated: '2025-05-07T01:28:14.924Z'
title: Career Stream
requirement:
  time: 1743491487443
  blocks:
    - id: BikdhLQvNN
      type: paragraph
      data:
        text: >-
          &nbsp;Chức năng cho phép tạo mới/cập nhật thông tin Career
          Stream&nbsp;
  version: 2.30.7
screen_design: null
module: FO
local_fields:
  - code: code
    pinned: true
    title: Career Stream Code
    data_type:
      key: Increment ID
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    show_sort: true
  - code: shortName
    pinned: false
    title: Short Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: longName
    pinned: false
    title: Long Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: status
    pinned: false
    title: Status
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: Boolean Tag
      collection: field_types
    show_sort: true
mock_data:
  - index: 0
    code: '00000001'
    effectiveDate: 2024/06/07
    shortName:
      default: Para-Professionals
      vietnamese: Para-Professionals
      english: Para-Professionals
    longName:
      default: Professionals
      vietnamese: Professionals
      english: Professionals
    status: true
  - index: 1
    code: '00000002'
    effectiveDate: 2024/06/06
    shortName:
      default: Para-Professionals
      vietnamese: Para-Professionals
      english: Para-Professionals
    longName:
      default: Professionals
      vietnamese: Professionals
      english: Professionals
    status: true
  - index: 2
    code: '00000003'
    effectiveDate: 2024/06/05
    shortName:
      default: Para-Professionals
      vietnamese: Para-Professionals
      english: Para-Professionals
    longName:
      default: Professionals
      vietnamese: Professionals
      english: Professionals
    status: true
  - index: 3
    code: '00000004'
    effectiveDate: 2024/06/04
    shortName:
      default: Para-Professionals
      vietnamese: Para-Professionals
      english: Para-Professionals
    longName:
      default: Professionals
      vietnamese: Professionals
      english: Professionals
    status: true
  - index: 4
    code: '00000005'
    effectiveDate: 2024/06/03
    shortName:
      default: Para-Professionals
      vietnamese: Para-Professionals
      english: Para-Professionals
    longName:
      default: Professionals
      vietnamese: Professionals
      english: Professionals
    status: true
  - index: 5
    code: '00000006'
    effectiveDate: 2024/06/02
    shortName:
      default: Para-Professionals
      vietnamese: Para-Professionals
      english: Para-Professionals
    longName:
      default: Professionals
      vietnamese: Professionals
      english: Professionals
    status: true
  - index: 6
    code: '00000007'
    effectiveDate: 2024/06/01
    shortName:
      default: Para-Professionals
      vietnamese: Para-Professionals
      english: Para-Professionals
    longName:
      default: Professionals
      vietnamese: Professionals
      english: Professionals
    status: true
local_buttons: null
layout: layout-table
form_config:
  fields:
    - type: group
      n_cols: 2
      _condition:
        transform: >-
          $.extend.formType = 'create' or $.extend.formType = 'edit' or
          $.extend.formType = 'proceed'
      fields:
        - name: code
          label: Career Stream Code
          type: text
          placeholder: Enter Career Stream Code
          _disabled:
            transform: $not($.extend.formType = 'create')
          validators:
            - type: required
            - type: maxLength
              args: '8'
              text: Maximum 8 characters
            - type: pattern
              args: ^[a-zA-Z0-9_.&]+$
              text: The Code must not contain special characters.
        - type: dateRange
          label: Effective Date
          name: effectiveDate
          mode: date-picker
          _value:
            transform: $.extend.formType = 'create' ? $now()
          setting:
            format: dd/MM/yyyy
            type: date
          validators:
            - type: required
        - name: status
          label: Status
          type: radio
          _value:
            transform: $.extend.formType = 'create' and $not($.extend.isDuplicate) ? true
          radio:
            - label: Active
              value: true
            - label: Inactive
              value: false
        - name: shortName
          label: Short Name
          placeholder: Enter Short Name
          type: translation
          validators:
            - type: maxLength
              args: '40'
              text: Maximum 40 characters
    - name: code
      label: Career Stream Code
      type: text
      _condition:
        transform: $.extend.formType = 'view'
    - type: dateRange
      label: Effective Date
      name: effectiveDate
      mode: date-picker
      setting:
        format: dd/MM/yyyy
        type: date
      _condition:
        transform: $.extend.formType = 'view'
    - name: status
      label: Status
      type: radio
      _value:
        transform: $.extend.formType = 'create' ? true
      radio:
        - label: Active
          value: true
        - label: Inactive
          value: false
      _condition:
        transform: $.extend.formType = 'view'
    - name: shortName
      label: Short Name
      placeholder: Enter Short Name
      type: translation
      validators:
        - type: maxLength
          args: '40'
          text: Maximum 40 characters
      _condition:
        transform: $.extend.formType = 'view'
    - name: longName
      label: Long Name
      placeholder: Enter Long Name
      type: translation
      validators:
        - type: required
        - type: maxLength
          args: '120'
          text: Maximum 120 characters
      _condition:
        transform: >-
          $.extend.formType = 'create' or $.extend.formType = 'edit' or
          $.extend.formType = 'proceed'
    - name: longName
      label: Long Name
      placeholder: Enter Long Name
      type: translation
      validators:
        - type: maxLength
          args: '120'
          text: Maximum 120 characters
      _condition:
        transform: $.extend.formType = 'view'
  historyHeaderTitle: '''View History Career Stream'''
filter_config:
  fields:
    - type: text
      placeholder: Enter Career Stream Code
      label: Career Stream Code
      labelType: type-grid
      name: code
    - name: status
      label: Status
      type: radio
      labelType: type-grid
      value: ''
      radio:
        - label: All
          value: ''
        - label: Active
          value: true
        - label: Inactive
          value: false
    - type: text
      placeholder: Enter Short Name
      label: Short Name
      labelType: type-grid
      name: shortName
    - type: text
      label: Long Name
      labelType: type-grid
      placeholder: Enter Long Name
      name: longName
  filterMapping:
    - field: code
      operator: $cont
      valueField: code
    - field: status
      operator: $eq
      valueField: status
    - field: na_name
      operator: $cont
      valueField: longName
    - field: na_shortName
      operator: $cont
      valueField: shortName
layout_options:
  toolTable:
    export: true
    import: true
    adjustDisplay: true
  tool_table:
    - id: import
      icon: icon-upload-simple
    - id: export
      icon: icon-download-simple
  view_after_updated: true
  delete_multi_items: true
  view_history_after_created: true
  custom_history_backend_url: /api/career-streams/insert-new-record
  hide_action_row: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: view
    icon: icon-eye
    type: ghost-gray
  - id: edit
    icon: icon-pencil-simple
    type: ghost-gray
  - id: delete
    icon: icon-trash
    type: ghost-gray
backend_url: /api/career-streams
screen_name: null
layout_options__actions_many: null
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key: null
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Career Stream
  parent:
    title: Job Structure
