id: FO.FS.FR.005
status: draft
sort: 131
user_created: 43878dbb-aa6c-4ef4-8bd7-0ea811ffafa7
date_created: '2024-06-14T03:37:47.786Z'
user_updated: 35b4f590-f747-4e53-8fa5-76aba65555c2
date_updated: '2025-07-19T02:39:52.434Z'
title: Career Band
requirement:
  time: 1745483549322
  blocks:
    - id: gPe50IuYLh
      type: paragraph
      data:
        text: Chức năng cho phép tìm kiếm danh mục Career Band
    - id: OWAsCeINJn
      type: paragraph
      data:
        text: Chức năng cho phép tạo mới/cập nhật thông tin Career Band
  version: 2.30.7
screen_design: null
module: FO
local_fields:
  - code: code
    pinned: true
    title: Career Band Code
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    description: >-
      <PERSON><PERSON><PERSON> thị các mã Career Band đã được tạo trên hệ thống theo tiêu chí tìm
      kiếm
    show_sort: true
  - code: shortName
    title: Short Name
    description: >-
      <PERSON><PERSON><PERSON> thị tên viết tắt của Career Band tương ứng với mã Career Band theo
      tiêu chí tìm kiếm
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: longName
    title: Long Name
    description: >-
      Hiển thị tên đầy đủ của Career Band tương ứng với mã Career Band theo tiêu
      chí tìm kiếm
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: parentCareerStream
    title: Career Stream
    description: >-
      Hiển thị Mã viết tắt (Short Name) của Career Stream tương ứng với mã
      Career Band theo tiêu chí tìm kiếm
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: status
    title: Status
    description: >
      Hiển thị trạng thái của các Career Band tương ứng với mã Career Band theo
      tiêu chí tìm kiếm
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: Boolean Tag
      collection: field_types
    show_sort: true
mock_data:
  - effectiveDate: 07/01/2024
    parentCode: Professionals
    code: '0000000001'
    shortName:
      default: Nhân viên
      vietnamese: Nhân viên
      english: Staff
    longName:
      default: Nhân viên
      vietnamese: Nhân viên
      english: Staff
  - effectiveDate: 06/01/2024
    parentCode: Professionals
    code: '0000000002'
    shortName:
      default: Nhân viên
      vietnamese: Nhân viên
      english: Staff
    longName:
      default: Nhân viên
      vietnamese: Nhân viên
      english: Staff
  - effectiveDate: 05/01/2024
    parentCode: Professionals
    code: '0000000003'
    shortName:
      default: Chuyên viên
      vietnamese: Chuyên viên
      english: Chuyên viên
    longName:
      default: Chuyên viên
      vietnamese: Chuyên viên
      english: Chuyên viên
  - effectiveDate: 04/01/2024
    parentCode: Professionals
    code: '0000000004'
    shortName:
      default: Chuyên viên cao cấp
      vietnamese: Chuyên viên cao cấp
      english: Senior specialist
    longName:
      default: Chuyên viên cao cấp
      vietnamese: Chuyên viên cao cấp
      english: Senior specialist
  - effectiveDate: 03/01/2024
    parentCode: Professionals
    code: '0000000005'
    shortName:
      default: Chuyên gia
      vietnamese: Chuyên gia
      english: Professional
    longName:
      default: Chuyên gia
      vietnamese: Chuyên gia
      english: Professional
local_buttons: null
layout: layout-table
form_config:
  fields:
    - type: group
      n_cols: 2
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - name: code
          label: Career Band Code
          type: text
          placeholder: Enter Career Band Code
          _disabled:
            transform: $not($.extend.formType = 'create')
          validators:
            - type: required
            - type: maxLength
              args: '8'
              text: Maximum 8 characters
            - type: pattern
              args: ^[a-zA-Z0-9_.&]+$
              text: The Code must not contain special characters.
        - type: dateRange
          label: Effective Date
          name: effectiveDate
          mode: date-picker
          _value:
            transform: $.extend.formType = 'create' ? $now()
          setting:
            format: dd/MM/yyyy
            type: date
          validators:
            - type: required
        - name: status
          label: Status
          type: radio
          _value:
            transform: $.extend.formType = 'create' and $not($.extend.isDuplicate) ? true
          radio:
            - label: Active
              value: true
            - label: Inactive
              value: false
        - name: shortName
          label: Short Name
          placeholder: Enter Short Name
          type: translation
          validators:
            - type: maxLength
              args: '40'
              text: Maximum 40 characters
    - name: code
      label: Career Band Code
      type: text
      _condition:
        transform: $.extend.formType = 'view'
    - type: dateRange
      label: Effective Date
      name: effectiveDate
      mode: date-picker
      setting:
        format: dd/MM/yyyy
        type: date
      _condition:
        transform: $.extend.formType = 'view'
    - name: status
      label: Status
      type: radio
      radio:
        - label: Active
          value: true
        - label: Inactive
          value: false
      _condition:
        transform: $.extend.formType = 'view'
    - name: shortName
      label: Short Name
      placeholder: Enter Short Name
      type: translation
      validators:
        - type: maxLength
          args: '40'
          text: Maximum 40 characters
      _condition:
        transform: $.extend.formType = 'view'
    - name: longName
      label: Long Name
      placeholder: Enter Long Name
      type: translation
      validators:
        - type: required
        - type: maxLength
          args: '120'
          text: Maximum 120 characters
      _condition:
        transform: $not($.extend.formType = 'view')
    - name: longName
      label: Long Name
      placeholder: Enter Long Name
      type: translation
      validators:
        - type: maxLength
          args: '120'
          text: Maximum 120 characters
      _condition:
        transform: $.extend.formType = 'view'
    - name: careerStreamObj
      label: Career Stream
      type: selectCustom
      _condition:
        transform: $not($.extend.formType = 'view')
      outputValue: value
      placeholder: Select Career Stream
      validators:
        - type: required
      _validateFn:
        transform: >-
          $exists($.value.code) ? ($exists($.fields.careerStreamObj) ?
          $jobCareerStreamList($.fields.effectiveDate,$.value.code,($not($.extend.formType
          = 'edit') or $not($DateFormat($.fields.effectiveDate, 'yyyy-MM-DD') =
          $DateFormat($.extend.defaultValue.effectiveDate, 'yyyy-MM-DD'))) ?
          true)[0] ?
          $jobCareerStreamList($.fields.effectiveDate,$.value.code,($not($.extend.formType
          = 'edit') or $not($DateFormat($.fields.effectiveDate, 'yyyy-MM-DD') =
          $DateFormat($.extend.defaultValue.effectiveDate, 'yyyy-MM-DD'))) ?
          true)[0] : '_setSelectValueNull')
      actions:
        - view
      _select:
        transform: >-
          $exists($.fields.effectiveDate) ?
          $jobCareerStreamList($.fields.effectiveDate,null, true)
      actionsConfig:
        view:
          formConfig:
            fields:
              - name: code
                label: Career Stream Code
                type: text
              - type: dateRange
                label: Effective Date
                name: effectiveDate
                mode: date-picker
                setting:
                  format: dd/MM/yyyy
                  type: date
              - name: status
                label: Status
                type: radio
                radio:
                  - label: Active
                    value: true
                  - label: Inactive
                    value: false
              - name: shortName
                label: Short Name
                type: translation
              - name: longName
                label: Long Name
                type: translation
    - name: careerStreamObj
      label: Career Stream
      outputValue: value
      inputValue: code
      type: selectCustom
      _condition:
        transform: $.extend.formType = 'view'
      placeholder: Select Career Stream
      actions:
        - view
      _select:
        transform: >-
          $exists($.fields.effectiveDate) ?
          $jobCareerStreamList($.fields.effectiveDate,$.extend.defaultValue.careerStreamObj.code)
      _validateFn:
        transform: >-
          $exists($.extend.defaultValue.careerStreamObj.code) ?
          ($exists($.fields.careerStreamObj) ?
          $jobCareerStreamList($.fields.effectiveDate,$.extend.defaultValue.careerStreamObj.code)[0]
          ?
          $jobCareerStreamList($.fields.effectiveDate,$.extend.defaultValue.careerStreamObj.code)[0]
          : '_setSelectValueNull')
      actionsConfig:
        view:
          formConfig:
            fields:
              - name: code
                label: Career Stream Code
                type: text
              - type: dateRange
                label: Effective Date
                name: effectiveDate
                mode: date-picker
                setting:
                  format: dd/MM/yyyy
                  type: date
              - name: status
                label: Status
                type: radio
                radio:
                  - label: Active
                    value: true
                  - label: Inactive
                    value: false
              - name: shortName
                label: Short Name
                type: translation
              - name: longName
                label: Long Name
                type: translation
  historyHeaderTitle: '''View History Career Band'''
  sources:
    jobCareerStreamList:
      uri: '"/api/career-streams/by"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'effectiveDate','operator': '$eq','value':
        $.effectiveDate},{'field':'status','operator':
        '$eq','value':$.status},{'field':'code','operator':
        '$eq','value':$.code}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id': $item.id, 'code': $item.code},
        'additionalData': $item}})[]
      disabledCache: true
      params:
        - effectiveDate
        - code
        - status
filter_config:
  fields:
    - type: text
      placeholder: Enter Career Band Code
      label: Career Band Code
      labelType: type-grid
      name: code
    - type: radio
      label: Status
      labelType: type-grid
      name: status
      value: ''
      radio:
        - label: All
          value: ''
        - label: Active
          value: true
        - label: Inactive
          value: false
    - type: text
      label: Short Name
      labelType: type-grid
      placeholder: Enter Short Name
      name: shortName
    - type: text
      label: Long Name
      labelType: type-grid
      placeholder: Enter Long Name
      name: longName
    - type: selectAll
      label: Career Stream
      name: careerStreamId
      labelType: type-grid
      isLazyLoad: true
      placeholder: Select Career Stream
      _options:
        transform: $careerStreamsList($.extend.limit,$.extend.page,$.extend.search)
  filterMapping:
    - field: code
      operator: $cont
      valueField: code
    - field: status
      operator: $eq
      valueField: status
    - field: na_shortName
      operator: $cont
      valueField: shortName
    - field: na_name
      operator: $cont
      valueField: longName
    - field: careerStreamId
      operator: $in
      valueField: careerStreamId.(value)
  sources:
    careerStreamsList:
      uri: '"/api/career-streams/get-by"'
      method: GET
      queryTransform: >-
        {'limit':$.limit,'page':$.page,'filter': [{'field':'search','operator':
        '$eq','value':$.search}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.id}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
layout_options:
  toolTable:
    export: true
    import: true
    adjustDisplay: true
  tool_table:
    - id: import
      icon: icon-upload-simple
    - id: export
      icon: icon-download-simple
  view_after_updated: true
  delete_multi_items: true
  view_history_after_created: true
  custom_history_backend_url: /api/bands/insert-new-record
  hide_action_row: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    title: Edit
    icon: icon-pencil-simple
    type: ghost-gray
  - id: delete
    title: Delete
    icon: icon-trash
    group: null
    type: ghost-gray
backend_url: /api/bands
screen_name: bands
layout_options__actions_many: null
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key: null
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Career Band
  parent:
    title: Job Structure
