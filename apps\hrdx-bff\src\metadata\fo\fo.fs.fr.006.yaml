id: FO.FS.FR.006
status: draft
sort: 163
user_created: 8ad48761-02ef-4f69-bef6-ac3a6f107610
date_created: '2024-06-13T04:29:40.879Z'
user_updated: 230a0477-7252-4474-98f3-84e38bc47f2b
date_updated: '2024-09-18T08:01:57.111Z'
title: Career Level
requirement:
  time: 1718252888427
  blocks:
    - id: ixJW_S9oOQ
      type: paragraph
      data:
        text: <PERSON>hai báo mảng chức năng
  version: 2.29.1
screen_design: null
module: FO
local_fields:
  - code: code
    pinned: true
    title: Career Level Code
    description: Career Level Code
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
  - code: shortName
    title: Short Name
    description: Short Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
  - code: longName
    title: Long Name
    description: Long Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
  - code: status
    title: Status
    description: Status
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: Boolean
      collection: field_types
mock_data:
  - code: '00000001'
    shortName:
      default: LV1
      vietnamese: LV1
      english: LV1
    effectiveDate: 2024/06/01
    longName:
      default: Level 1
      vietnamese: Level 1
      english: Level 1
    status: true
  - code: '00000002'
    shortName:
      default: LV2
      vietnamese: LV2
      english: LV2
    effectiveDate: 2024/06/01
    longName:
      default: Level 2
      vietnamese: Level 2
      english: Level 2
    status: true
local_buttons: null
layout: layout-table
form_config:
  fields:
    - type: text
      label: Career Level Code
      name: code
      disabled: 'true'
      placeholder: Automatic
      scale: 1/2
      validators:
        - type: required
    - type: dateRange
      label: Effective Date
      name: effectiveDate
      scale: 1/2
      placeholder: dd/MM/yyyy
      mode: date-picker
      setting:
        format: dd/MM/YYYY
        type: date
      validators:
        - type: required
      _value:
        transform: $.extend.formType = 'create' ? $now()
    - type: radio
      label: Status
      name: status
      _value:
        transform: $.extend.formType = 'create' ? true
      radio:
        - label: Active
          value: true
        - label: Inactive
          value: false
      validators:
        - type: required
    - type: translation
      label: Short Name
      scale: 1/2
      placeholder: Enter Short Name
      name: shortName
    - type: translation
      label: Long Name
      placeholder: Enter Long Name
      name: longName
      validators:
        - type: required
  historyHeaderTitle: '''View History Career Level '''
filter_config:
  fields:
    - type: text
      label: Insurance Code
      name: insuranceCode
    - type: text
      label: Insurance Name
      name: insuranceName
    - type: text
      label: Social Insurance Code
      name: socialInsuranceCode
    - type: select
      label: Company
      name: company
      _select:
        transform: $companiesList()
    - type: select
      label: Legal Entity
      name: legalEntity
      _select:
        transform: $legalEntitiesList()
    - type: select
      label: Business Unit
      name: businessUnit
      _select:
        transform: $businessUnitList()
    - type: select
      label: Division
      name: division
      _select:
        transform: $divisionsList()
    - type: select
      label: Department
      name: department
      _select:
        transform: $departmentsList()
    - type: radio
      label: Local/Expat
      name: localExpat
      radio:
        - value: local
          label: Local
        - value: expat
          label: Expat
    - type: dateRange
      label: Effective Date
      name: effectiveDate
      setting:
        format: dd/MM/yyyy
      mode: date-picker
    - type: radio
      label: Status
      name: status
      radio:
        - value: ''
          label: All
        - value: true
          label: Active
        - value: false
          label: Inactive
    - type: text
      label: Note
      name: note
    - type: text
      label: Creator
      name: creator
    - type: dateRange
      label: Create Time
      name: createTime
      setting:
        format: dd/MM/yyyy
      mode: date-picker
    - type: text
      label: Last Editor
      name: lastEditor
    - type: dateRange
      label: Last Edit Time
      name: lastEditTime
      setting:
        format: dd/MM/yyyy
      mode: date-picker
  filterMapping:
    - field: insuranceCode
      operator: $cont
      valueField: insuranceCode
    - field: insuranceName
      operator: $cont
      valueField: insuranceName
    - field: socialInsuranceCode
      operator: $cont
      valueField: socialInsuranceCode
    - field: company
      operator: $eq
      valueField: company
    - field: legalEntity
      operator: $eq
      valueField: legalEntity
    - field: businessUnit
      operator: $eq
      valueField: businessUnit
    - field: division
      operator: $eq
      valueField: division
    - field: department
      operator: $eq
      valueField: department
    - field: localExpat
      operator: $eq
      valueField: localExpat
    - field: effectiveDate
      operator: $gte
      valueField: effectiveDateFrom
    - field: effectiveDate
      operator: $lte
      valueField: effectiveDateTo
    - field: status
      operator: $eq
      valueField: status
    - field: note
      operator: $cont
      valueField: note
    - field: creator
      operator: $cont
      valueField: creator
    - field: createTime
      operator: $gte
      valueField: createTimeFrom
    - field: createTime
      operator: $lte
      valueField: createTimeTo
    - field: lastEditor
      operator: $cont
      valueField: lastEditor
    - field: lastEditTime
      operator: $gte
      valueField: lastEditTimeFrom
    - field: lastEditTime
      operator: $lte
      valueField: lastEditTimeTo
layout_options: {}
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
  - id: export
    title: Export
    icon: file-arrow-down
    type: secondary
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions: null
backend_url: /api/levels
screen_name: null
layout_options__actions_many: null
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key: null
screen_designs: []
function_specs: []
fields: []
children: []
menu_item: null
