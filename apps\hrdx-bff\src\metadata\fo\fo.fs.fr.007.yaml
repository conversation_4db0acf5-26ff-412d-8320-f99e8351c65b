id: FO.FS.FR.007
status: draft
sort: 207
user_created: 8ad48761-02ef-4f69-bef6-ac3a6f107610
date_created: '2024-06-13T08:17:35.132Z'
user_updated: 35b4f590-f747-4e53-8fa5-76aba65555c2
date_updated: '2025-07-30T04:37:43.304Z'
title: Job
requirement:
  time: 1749007761401
  blocks:
    - id: RvRgbYHHka
      type: paragraph
      data:
        text: band&nbsp;
  version: 2.30.7
screen_design: null
module: FO
local_fields:
  - code: code
    title: Job Code
    description: Job Code
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    pinned: true
    show_sort: true
  - code: shortName
    title: Short Name
    description: Short Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: longName
    title: Job Title
    description: Job Title
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: jobFamilyName
    title: Job Family
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: jobSubFamilyName
    title: Job SubFamily
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: jobSpecializationName
    title: Job Specialization
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: careerStreamName
    title: Career Stream
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: bandName
    title: Career Band
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: companyDisplay
    title: Companies
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    extra_config:
      singular: Companies
      plural: Companies
  - code: status
    title: Status
    description: status
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: Boolean Tag
      collection: field_types
    show_sort: true
mock_data: null
local_buttons: null
layout: layout-table
form_config:
  formSize:
    create: large
    edit: large
    proceed: large
  fields:
    - type: group
      label: Basic Information
      collapse: false
      disableEventCollapse: true
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - type: group
          n_cols: 2
          fields:
            - name: code
              label: Job Code
              type: text
              placeholder: Enter Job Code
              _disabled:
                transform: $not($.extend.formType = 'create')
              validators:
                - type: required
                - type: maxLength
                  args: '8'
                  text: Maximum 8 characters
                - type: pattern
                  args: ^[a-zA-Z0-9_.&]+$
                  text: The Code must not contain special characters.
            - type: dateRange
              label: Effective Date
              name: effectiveDate
              placeholder: dd/MM/yyyy
              mode: date-picker
              setting:
                format: dd/MM/yyyy
                type: date
              _condition:
                transform: $not($.extend.formType = 'view')
              validators:
                - type: required
              _value:
                transform: $.extend.formType = 'create' ? $now()
            - type: dateRange
              label: Effective Date
              name: effectiveDate
              placeholder: dd/MM/yyyy
              mode: date-picker
              setting:
                format: dd/MM/YYYY
                type: date
              _condition:
                transform: $.extend.formType = 'view'
            - name: status
              label: Status
              type: radio
              _value:
                transform: $.extend.formType = 'create' ? true
              radio:
                - label: Active
                  value: true
                - label: Inactive
                  value: false
              _condition:
                transform: $not($.extend.formType = 'view')
            - name: status
              label: Status
              type: radio
              radio:
                - label: Active
                  value: true
                - label: Inactive
                  value: false
              _condition:
                transform: $.extend.formType = 'view'
            - type: checkbox
              name: keyJobCode
              label: Key Job Code
              labelType: type2
              hiddenLabel: false
              _condition:
                transform: $not($.extend.formType = 'view')
            - type: translation
              label: Short Name
              name: shortName
              placeholder: Enter Short Name
              validators:
                - type: maxLength
                  args: '40'
                  text: Maximum 40 characters
            - name: longName
              label: Job Title
              type: translation
              placeholder: Enter Job Title
              validators:
                - type: maxLength
                  args: '120'
                  text: Maximum 120 characters
                - type: required
              _condition:
                transform: $not($.extend.formType = 'view')
        - name: longName
          label: Job Title
          type: translation
          _condition:
            transform: $.extend.formType = 'view'
        - type: translationTextArea
          name: jobDescription
          label: Description
          placeholder: Enter Description
          textarea:
            autoSize:
              minRows: 3
            maxCharCount: 4000
          validators:
            - type: maxLength
              args: '4000'
              text: Maximum 4000 characters
        - type: group
          n_cols: 2
          fields:
            - name: standardWeeklyHours
              label: Standard Weekly Hours
              type: number
              placeholder: Enter Standard Weekly Hours
              validators:
                - type: pattern
                  args: ^\d{1,3}$
                  text: Maximum 3 characters
            - name: regularTemporary
              label: Regular/Temporary
              type: select
              outputValue: value
              placeholder: Select Regular/Temporary
              _value:
                transform: $.extend.formType = 'create' ? 'R'
              _select:
                transform: $regularTemporaryList()
    - type: group
      label: Basic Information
      collapse: false
      disableEventCollapse: false
      _condition:
        transform: $.extend.formType = 'view'
      fieldGroupTitleStyle:
        border: none
      fields:
        - type: group
          n_cols: 1
          fields:
            - name: code
              label: Job Code
              type: text
              placeholder: Automatic
              disabled: true
            - type: dateRange
              label: Effective Date
              name: effectiveDate
              placeholder: dd/MM/yyyy
              mode: date-picker
              setting:
                format: dd/MM/YYYY
                type: date
            - name: status
              label: Status
              type: radio
              value: true
              radio:
                - label: Active
                  value: true
                - label: Inactive
                  value: false
            - type: checkbox
              name: keyJobCode
              label: Key Job Code
              labelType: type2
              hiddenLabel: false
              disabled: true
              _condition:
                transform: $.extend.formType = 'view'
              showLabelCheckbox: false
            - type: translation
              label: Short Name
              name: shortName
              placeholder: Enter Short Name
        - name: longName
          label: Job Title
          type: translation
          _condition:
            transform: $.extend.formType = 'view'
          validators:
            - type: maxLength
              args: '120'
              text: Maximum 120 characters
        - type: translationTextArea
          name: jobDescription
          label: Description
          placeholder: Enter Description
          textarea:
            autoSize:
              minRows: 3
            maxCharCount: 4000
          validators:
            - type: maxLength
              args: '4000'
              text: Maximum 4000 characters
        - name: standardWeeklyHours
          label: Standard Weekly Hours
          type: number
          placeholder: Enter Standard Weekly Hours
          validators:
            - type: maxLength
              args: '3'
              text: Maximum 3 characters
        - name: regularTemporary
          label: Regular/Temporary
          type: select
          outputValue: value
          placeholder: select Regular/Temporary
          _select:
            transform: $regularTemporaryList()
    - type: group
      label: Category
      collapse: false
      disableEventCollapse: false
      fields:
        - type: group
          _n_cols:
            transform: '$not($.extend.formType = ''view'') ? 2 : 1'
          fields:
            - type: selectCustom
              label: Job Family
              name: jobFamilyObj
              isLazyLoad: true
              placeholder: Select Job Family
              clearFieldsAfterChange:
                - jobSubFamilyObj
                - jobSpecializationObj
              outputValue: value
              _validateFn:
                transform: >-
                  $exists($.value.code) ?
                  ($jobFamilyList($.extend.limit,$.extend.page,$.fields.effectiveDate,$.value.code,null,($not($.extend.formType
                  = 'edit') or $not($DateFormat($.fields.effectiveDate,
                  'yyyy-MM-DD') =
                  $DateFormat($.extend.defaultValue.effectiveDate,
                  'yyyy-MM-DD'))) ? true)[0] ?
                  $jobFamilyList($.extend.limit,$.extend.page,$.fields.effectiveDate,$.value.code,null,($not($.extend.formType
                  = 'edit') or $not($DateFormat($.fields.effectiveDate,
                  'yyyy-MM-DD') =
                  $DateFormat($.extend.defaultValue.effectiveDate,
                  'yyyy-MM-DD'))) ? true)[0] : '_setSelectValueNull')
              _detailData:
                transform: >-
                  $exists($.extend.defaultValue.jobFamilyObj.value.code) ?
                  ($info :=
                  $jobFamilyList(1,1,$.extend.defaultValue.effectiveDate,$.extend.defaultValue.jobFamilyObj.value.code,null,($not($.extend.formType
                  = 'edit') or $not($DateFormat($.fields.effectiveDate,
                  'yyyy-MM-DD') =
                  $DateFormat($.extend.defaultValue.effectiveDate,
                  'yyyy-MM-DD'))) ? true)[0]; $exists($info) ? $info )
              validators:
                - type: required
              _condition:
                transform: $not($.extend.formType = 'view')
              actions:
                - view
              _select:
                transform: >-
                  $jobFamilyList($.extend.limit,$.extend.page,$.fields.effectiveDate,null,$.extend.search,true)
              actionsConfig:
                view:
                  formConfig:
                    fields:
                      - name: code
                        label: Job Family Code
                        type: text
                      - type: dateRange
                        label: Effective Date
                        name: effectiveDate
                        mode: date-picker
                        setting:
                          format: dd/MM/yyyy
                          type: date
                      - name: status
                        label: Status
                        type: radio
                        radio:
                          - label: Active
                            value: true
                          - label: Inactive
                            value: false
                      - name: shortName
                        label: Short Name
                        type: translation
                      - name: longName
                        label: Long Name
                        type: translation
                      - name: description
                        label: Description
                        type: translationTextArea
            - type: text
              label: Job Family
              name: jobFamily
              _condition:
                transform: $.extend.formType = 'view'
              labelTransform:
                transform: $.name.default & ' ' & '(' & $.code & ')'
              formConfig:
                fields:
                  - name: code
                    label: Job Family Code
                    type: text
                  - type: dateRange
                    label: Effective Date
                    name: effectiveDate
                    mode: date-picker
                    setting:
                      format: dd/MM/yyyy
                      type: date
                  - name: status
                    label: Status
                    type: radio
                    radio:
                      - label: Active
                        value: true
                      - label: Inactive
                        value: false
                  - name: shortName
                    label: Short Name
                    type: translation
                  - name: longName
                    label: Long Name
                    type: translation
                  - name: description
                    label: Description
                    type: translation
            - type: selectCustom
              label: Job SubFamily
              name: jobSubFamilyObj
              isLazyLoad: true
              placeholder: Select Job SubFamily
              clearFieldsAfterChange:
                - jobSpecializationObj
              outputValue: value
              validators:
                - type: required
              _condition:
                transform: $not($.extend.formType = 'view')
              actions:
                - view
              actionsConfig:
                view:
                  formConfig:
                    fields:
                      - name: code
                        label: Job SubFamily Code
                        type: text
                      - type: dateRange
                        label: Effective Date
                        name: effectiveDate
                        mode: date-picker
                        setting:
                          format: dd/MM/yyyy
                          type: date
                      - name: status
                        label: Status
                        type: radio
                        radio:
                          - label: Active
                            value: true
                          - label: Inactive
                            value: false
                      - name: shortName
                        label: Short Name
                        type: translation
                      - name: longName
                        label: Long Name
                        type: translation
                      - name: parentJobFamily
                        label: Job Family
                        type: text
                      - name: description
                        label: Description
                        type: translationTextArea
              _select:
                transform: ' $.fields.jobFamilyObj ? $jobSubFamilyList($.extend.limit,$.extend.page,$.fields.effectiveDate,$.fields.jobFamilyObj.id,null,true,$.extend.search)'
              _validateFn:
                transform: >-
                  $exists($.value.code) ?
                  ($jobSubFamilyList($.extend.limit,$.extend.page,$.fields.effectiveDate,null,$.value.code,($not($.extend.formType
                  = 'edit') or $not($DateFormat($.fields.effectiveDate,
                  'yyyy-MM-DD') =
                  $DateFormat($.extend.defaultValue.effectiveDate,
                  'yyyy-MM-DD'))) ? true)[0] ?
                  $jobSubFamilyList($.extend.limit,$.extend.page,$.fields.effectiveDate,null,$.value.code,($not($.extend.formType
                  = 'edit') or $not($DateFormat($.fields.effectiveDate,
                  'yyyy-MM-DD') =
                  $DateFormat($.extend.defaultValue.effectiveDate,
                  'yyyy-MM-DD'))) ? true)[0] : '_setSelectValueNull')
            - type: text
              label: Job SubFamily
              name: subFamily
              _condition:
                transform: $.extend.formType = 'view'
              labelTransform:
                transform: $.name.default & ' ' & '(' & $.code & ')'
              formConfig:
                fields:
                  - name: code
                    label: Job SubFamily Code
                    type: text
                  - type: dateRange
                    label: Effective Date
                    name: effectiveDate
                    mode: date-picker
                    setting:
                      format: dd/MM/yyyy
                      type: date
                  - name: status
                    label: Status
                    type: radio
                    radio:
                      - label: Active
                        value: true
                      - label: Inactive
                        value: false
                  - name: shortName
                    label: Short Name
                    type: translation
                  - name: longName
                    label: Long Name
                    type: translation
                  - name: parentJobFamily
                    label: Job Family
                    type: text
                  - name: description
                    label: Description
                    type: translation
            - type: selectCustom
              label: Job Specialization
              name: jobSpecializationObj
              isLazyLoad: true
              validators:
                - type: required
              _condition:
                transform: $not($.extend.formType = 'view')
              outputValue: value
              _validateFn:
                transform: >-
                  $exists($.value.code) ?
                  ($jobSpeacializationsList($.extend.limit,$.extend.page,$.fields.effectiveDate,null,$.value.code,($not($.extend.formType
                  = 'edit') or $not($DateFormat($.fields.effectiveDate,
                  'yyyy-MM-DD') =
                  $DateFormat($.extend.defaultValue.effectiveDate,
                  'yyyy-MM-DD'))) ? true)[0] ?
                  $jobSpeacializationsList($.extend.limit,$.extend.page,$.fields.effectiveDate,null,$.value.code,($not($.extend.formType
                  = 'edit') or $not($DateFormat($.fields.effectiveDate,
                  'yyyy-MM-DD') =
                  $DateFormat($.extend.defaultValue.effectiveDate,
                  'yyyy-MM-DD'))) ? true)[0] : '_setSelectValueNull')
              actions:
                - view
              placeholder: Select Job Speacialization
              _select:
                transform: ' $.fields.jobSubFamilyObj ? $jobSpeacializationsList($.extend.limit,$.extend.page,$.fields.effectiveDate, $.fields.jobSubFamilyObj.id,null,true,$.extend.search)'
              actionsConfig:
                view:
                  formConfig:
                    fields:
                      - name: code
                        label: Job Specialization Code
                        type: text
                      - type: dateRange
                        label: Effective Date
                        name: effectiveDate
                        mode: date-picker
                        setting:
                          format: dd/MM/yyyy
                          type: date
                      - name: status
                        label: Status
                        type: radio
                        radio:
                          - label: Active
                            value: true
                          - label: Inactive
                            value: false
                      - name: shortName
                        label: Short Name
                        type: translation
                      - name: longName
                        label: Long Name
                        type: translation
                      - name: parentJobFamily
                        label: Job Family
                        type: text
                      - name: parentJobSubFamily
                        label: Job SubFamily
                        type: text
                      - name: description
                        label: Description
                        type: translationTextArea
            - type: text
              label: Job Specialization
              name: jobSpecialization
              _condition:
                transform: $.extend.formType = 'view'
              labelTransform:
                transform: $.name.default & ' ' & '(' & $.code & ')'
              formConfig:
                fields:
                  - name: code
                    label: Job Specialization Code
                    type: text
                  - type: dateRange
                    label: Effective Date
                    name: effectiveDate
                    mode: date-picker
                    setting:
                      format: dd/MM/yyyy
                      type: date
                  - name: status
                    label: Status
                    type: radio
                    radio:
                      - label: Active
                        value: true
                      - label: Inactive
                        value: false
                  - name: shortName
                    label: Short Name
                    type: translation
                  - name: longName
                    label: Long Name
                    type: translation
                  - name: parentJobFamily
                    label: Job Family
                    type: text
                  - name: parentJobSubFamily
                    label: Job SubFamily
                    type: text
                  - name: description
                    label: Description
                    type: translation
            - type: selectCustom
              label: Career Stream
              isLazyLoad: true
              name: careerStreamObj
              clearFieldsAfterChange:
                - bandObj
              validators:
                - type: required
              _condition:
                transform: $not($.extend.formType = 'view')
              outputValue: value
              actions:
                - view
              placeholder: Select Career Stream
              _select:
                transform: >-
                  $careerStreamList($.extend.limit,$.extend.page,$.fields.effectiveDate,null,true,$.extend.search)
              _validateFn:
                transform: >-
                  $exists($.value.code) ?
                  ($careerStreamList($.extend.limit,$.extend.page,$.fields.effectiveDate,
                  $.value.code,($not($.extend.formType = 'edit') or
                  $not($DateFormat($.fields.effectiveDate, 'yyyy-MM-DD') =
                  $DateFormat($.extend.defaultValue.effectiveDate,
                  'yyyy-MM-DD'))) ? true)[0] ?
                  $careerStreamList($.extend.limit,$.extend.page,$.fields.effectiveDate,
                  $.value.code,($not($.extend.formType = 'edit') or
                  $not($DateFormat($.fields.effectiveDate, 'yyyy-MM-DD') =
                  $DateFormat($.extend.defaultValue.effectiveDate,
                  'yyyy-MM-DD'))) ? true)[0] : '_setSelectValueNull')
              actionsConfig:
                view:
                  formConfig:
                    fields:
                      - name: code
                        label: Career Stream Code
                        type: text
                      - type: dateRange
                        label: Effective Date
                        name: effectiveDate
                        mode: date-picker
                        setting:
                          format: dd/MM/yyyy
                          type: date
                      - name: status
                        label: Status
                        type: radio
                        radio:
                          - label: Active
                            value: true
                          - label: Inactive
                            value: false
                      - name: shortName
                        label: Short Name
                        type: translation
                      - name: longName
                        label: Long Name
                        type: translation
            - type: text
              label: Career Stream
              name: careerStream
              _condition:
                transform: $.extend.formType = 'view'
              labelTransform:
                transform: $.name.default & ' ' & '(' & $.code & ')'
              formConfig:
                fields:
                  - name: code
                    label: Career Stream Code
                    type: text
                  - type: dateRange
                    label: Effective Date
                    name: effectiveDate
                    mode: date-picker
                    setting:
                      format: dd/MM/yyyy
                      type: date
                  - name: status
                    label: Status
                    type: radio
                    radio:
                      - label: Active
                        value: true
                      - label: Inactive
                        value: false
                  - name: shortName
                    label: Short Name
                    type: translation
                  - name: longName
                    label: Long Name
                    type: translation
            - type: selectCustom
              label: Career Band
              name: bandObj
              isLazyLoad: true
              outputValue: value
              _condition:
                transform: $not($.extend.formType = 'view')
              col: 2
              actions:
                - view
              placeholder: Select Career Band
              _select:
                transform: >-
                  $.fields.careerStreamObj ?
                  $careerBandList($.extend.limit,$.extend.page,$.fields.effectiveDate,
                  $.fields.careerStreamObj.id,null,true,$.extend.search)
              _validateFn:
                transform: >-
                  $exists($.value.code) ?
                  $careerBandList($.extend.limit,$.extend.page,$.fields.effectiveDate,null,
                  $.value.code,($not($.extend.formType = 'edit') or
                  $not($DateFormat($.fields.effectiveDate, 'yyyy-MM-DD') =
                  $DateFormat($.extend.defaultValue.effectiveDate,
                  'yyyy-MM-DD'))) ? true)[0] ?
                  $careerBandList($.extend.limit,$.extend.page,$.fields.effectiveDate,null,
                  $.value.code,($not($.extend.formType = 'edit') or
                  $not($DateFormat($.fields.effectiveDate, 'yyyy-MM-DD') =
                  $DateFormat($.extend.defaultValue.effectiveDate,
                  'yyyy-MM-DD'))) ? true)[0] : '_setSelectValueNull'
              actionsConfig:
                view:
                  formConfig:
                    fields:
                      - name: code
                        label: Career Band Code
                        type: text
                      - type: dateRange
                        label: Effective Date
                        name: effectiveDate
                        mode: date-picker
                        setting:
                          format: dd/MM/yyyy
                          type: date
                      - name: status
                        label: Status
                        type: radio
                        radio:
                          - label: Active
                            value: true
                          - label: Inactive
                            value: false
                      - name: shortName
                        label: Short Name
                        type: translation
                      - name: longName
                        label: Long Name
                        type: translation
                      - name: parentCareerStream
                        label: Career Stream
                        type: text
        - type: text
          label: Career Band
          name: band
          _condition:
            transform: $.extend.formType = 'view'
          labelTransform:
            transform: $.name.default & ' ' & '(' & $.code & ')'
          formConfig:
            fields:
              - name: code
                label: Career Band Code
                type: text
              - type: dateRange
                label: Effective Date
                name: effectiveDate
                mode: date-picker
                setting:
                  format: dd/MM/yyyy
                  type: date
              - name: status
                label: Status
                type: radio
                radio:
                  - label: Active
                    value: true
                  - label: Inactive
                    value: false
              - name: shortName
                label: Short Name
                type: translation
              - name: longName
                label: Long Name
                type: translation
              - name: parentCareerStream
                label: Career Stream
                type: text
    - type: group
      label: Job Company
      collapse: false
      disableEventCollapse: false
      fields:
        - type: selectAll
          label: Company
          name: companyObj
          placeholder: Select Company
          outputValue: value
          isLazyLoad: true
          _validateFn:
            transform: >-
              $exists($test($.fields.companyObj.value.code)) ?
              ($companyList(1000000,1,$.fields.effectiveDate,$.fields.companyObj.value.code,null,($not($.extend.formType
              = 'edit') or $not($DateFormat($.fields.effectiveDate,
              'yyyy-MM-DD') = $DateFormat($.extend.defaultValue.effectiveDate,
              'yyyy-MM-DD'))) ? true)[0] ?
              $companyList(1000000,1,$.fields.effectiveDate,$.fields.companyObj.value.code,null,($not($.extend.formType
              = 'edit') or $not($DateFormat($.fields.effectiveDate,
              'yyyy-MM-DD') = $DateFormat($.extend.defaultValue.effectiveDate,
              'yyyy-MM-DD'))) ? true) : '_setSelectValueNull')
          _options:
            transform: >-
              $companyList($.extend.limit,$.extend.page,$.fields.effectiveDate,null,$.extend.search,true)
          _condition:
            transform: $not($.extend.formType = 'view')
          validators:
            - type: required
        - type: selectCustom
          label: Company
          name: companyObj
          placeholder: Select Company
          outputValue: value
          inputValue: code
          mode: multiple
          _select:
            transform: >-
              $companyList(1000000,0,$.fields.effectiveDate,
              $.fields.companyObj.value.code)
          _condition:
            transform: $.extend.formType = 'view'
          actions:
            - view
          actionsConfig:
            view:
              formConfig:
                fields:
                  - name: code
                    label: Company Code
                    type: text
                  - type: dateRange
                    label: Effective Date
                    name: effectiveDate
                    mode: date-picker
                    setting:
                      format: dd/MM/yyyy
                      type: date
                  - name: status
                    label: Status
                    type: radio
                    radio:
                      - label: Active
                        value: true
                      - label: Inactive
                        value: false
                  - name: shortName
                    label: Short Name
                    type: translation
                  - name: longName
                    label: Long Name
                    type: translation
  historyHeaderTitle: '''View History Job'''
  sources:
    jobSubFamilyList:
      uri: '"/api/job-sub-families/get-by"'
      method: GET
      queryTransform: >-
        {'limit':$.limit,'page':$.page,'filter':
        [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':$.status},{'field':'jobFamilyId','operator':
        '$eq','value':$.jobFamilyId},{'field':'code','operator':
        '$eq','value':$.code},{'field':'search','operator':
        '$eq','value':$.search}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id': $item.id, 'code': $item.code},
        'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - effectiveDate
        - jobFamilyId
        - code
        - status
        - search
    jobSpeacializationsList:
      uri: '"/api/job-specializations/get-by"'
      method: GET
      queryTransform: >-
        {'limit':$.limit,'page':$.page,'filter':
        [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':$.status},{'field':'jobSubFamilyId','operator':
        '$eq','value':$.jobSubFamilyId},{'field':'code','operator':
        '$eq','value':$.code},{'field':'search','operator':
        '$eq','value':$.search}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id': $item.id, 'code': $item.code},
        'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - effectiveDate
        - jobSubFamilyId
        - code
        - status
        - search
    jobFamilyList:
      uri: '"/api/job-families/get-by"'
      method: GET
      queryTransform: >-
        {'limit':$.limit,'page':$.page,'filter':
        [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':$.status},{'field':'code','operator':
        '$eq','value':$.code},{'field':'search','operator':
        '$eq','value':$.search}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id': $item.id, 'code': $item.code},
        'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - effectiveDate
        - code
        - search
        - status
    careerStreamList:
      uri: '"/api/career-streams/get-by"'
      method: GET
      queryTransform: >-
        {'limit':$.limit,'page':$.page,'filter':
        [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':$.status},{'field':'code','operator':
        '$eq','value':$.code},{'field':'search','operator':
        '$eq','value':$.search}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id': $item.id, 'code': $item.code},
        'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - effectiveDate
        - code
        - status
        - search
    careerBandList:
      uri: '"/api/bands/get-by"'
      method: GET
      queryTransform: >-
        {'limit':$.limit,'page':$.page,'filter':
        [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':$.status}, {'field':'careerStreamId','operator':
        '$eq','value':$.careerStreamId},{'field':'code','operator':
        '$eq','value':$.code},{'field':'search','operator':
        '$eq','value':$.search}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id': $item.id, 'code': $item.code},
        'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - effectiveDate
        - careerStreamId
        - code
        - status
        - search
    companyList:
      uri: '"/api/companies/get-by"'
      method: GET
      queryTransform: >-
        {'limit':$.limit,'page':$.page,'filter': [{'field':'search','operator':
        '$eq','value':$.search},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':$.status},{'field':'code','operator':
        '$in','value':$.code}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': {'id': $item.id, 'code': $item.code},
        'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - effectiveDate
        - code
        - search
        - status
    jobGroupList:
      uri: '"/api/groupings/by"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.id}})[]
      disabledCache: true
      params:
        - effectiveDate
    jobSubGroupList:
      uri: '"/api/sub-groupings/by"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.id}})[]
      disabledCache: true
      params:
        - effectiveDate
    regularTemporaryList:
      uri: '"/api/picklists/JobRegularTemp/values/"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})
      disabledCache: true
  _mode:
    transform: $not($.extend.formType = 'view') ? 'mark-scroll'
filter_config:
  fields:
    - name: code
      label: Job Code
      placeholder: Enter Job Code
      labelType: type-grid
      type: text
    - type: radio
      label: Status
      labelType: type-grid
      name: status
      value: ''
      radio:
        - label: All
          value: ''
        - label: Active
          value: true
        - label: Inactive
          value: false
    - name: shortName
      labelType: type-grid
      label: Short Name
      placeholder: Enter Short Name
      type: text
    - name: longName
      label: Job Title
      labelType: type-grid
      placeholder: Enter Job Title
      type: text
    - type: selectAll
      label: Job Family
      isLazyLoad: true
      labelType: type-grid
      name: jobFamilyCode
      placeholder: Select Job Family
      _options:
        transform: $jobFamilyList($.extend.limit,$.extend.page,$.extend.search)
    - type: selectAll
      label: Job SubFamily
      labelType: type-grid
      isLazyLoad: true
      name: jobSubFamilyCode
      placeholder: Select Job SubFamily
      _options:
        transform: $jobSubFamilyList($.extend.limit,$.extend.page,$.extend.search)
    - type: selectAll
      label: Job Specialization
      labelType: type-grid
      isLazyLoad: true
      name: jobSpecializationCode
      placeholder: Select Job Specialization
      _options:
        transform: $jobSpecializationList($.extend.limit,$.extend.page,$.extend.search)
    - type: selectAll
      label: Career Stream
      name: careerStreamCode
      labelType: type-grid
      isLazyLoad: true
      placeholder: Select Career Stream
      _options:
        transform: $careerStreamsList($.extend.limit,$.extend.page,$.extend.search)
    - type: selectAll
      label: Career Band
      name: bandCode
      labelType: type-grid
      isLazyLoad: true
      placeholder: Select Career Band
      _options:
        transform: $careerBandsList($.extend.limit,$.extend.page,$.extend.search)
    - type: selectAll
      label: Company
      labelType: type-grid
      name: CompanyIds
      isLazyLoad: true
      placeholder: Select Company
      _options:
        transform: $companyList($.extend.limit,$.extend.page,$.extend.search)
  filterMapping:
    - field: code
      operator: $cont
      valueField: code
    - field: jobFamilyCode
      operator: $in
      valueField: jobFamilyCode.(value)
    - field: jobSubFamilyCode
      operator: $in
      valueField: jobSubFamilyCode.(value)
    - field: jobSpecializationCode
      operator: $in
      valueField: jobSpecializationCode.(value)
    - field: careerStreamCode
      operator: $in
      valueField: careerStreamCode.(value)
    - field: bandCode
      operator: $in
      valueField: bandCode.(value)
    - field: CompanyIds
      operator: $eq
      valueField: CompanyIds.(value)
    - field: na_shortName
      operator: $cont
      valueField: shortName
    - field: na_name
      operator: $cont
      valueField: longName
    - field: status
      operator: $eq
      valueField: status
  sources:
    jobFamilyList:
      uri: '"/api/job-families/get-by"'
      method: GET
      queryTransform: >-
        {'limit':$.limit,'page':$.page,'filter': [{'field':'search','operator':
        '$eq','value':$.search}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    jobSubFamilyList:
      uri: '"/api/job-sub-families/get-by"'
      method: GET
      queryTransform: >-
        {'limit':$.limit,'page':$.page,'filter': [{'field':'search','operator':
        '$eq','value':$.search}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    jobSpecializationList:
      uri: '"/api/job-specializations/get-by"'
      method: GET
      queryTransform: >-
        {'limit':$.limit,'page':$.page,'filter': [{'field':'search','operator':
        '$eq','value':$.search}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    careerStreamsList:
      uri: '"/api/career-streams/get-by"'
      method: GET
      queryTransform: >-
        {'limit':$.limit,'page':$.page,'filter': [{'field':'search','operator':
        '$eq','value':$.search}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    careerBandsList:
      uri: '"/api/bands/get-by"'
      method: GET
      queryTransform: >-
        {'limit':$.limit,'page':$.page,'filter': [{'field':'search','operator':
        '$eq','value':$.search}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    companyList:
      uri: '"/api/companies/get-by"'
      method: GET
      queryTransform: >-
        {'limit':$.limit,'page':$.page,'filter': [{'field':'search','operator':
        '$eq','value':$.search}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.id, 'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
layout_options:
  toolTable:
    export: true
    import: true
    adjustDisplay: true
  tool_table:
    - id: import
      icon: icon-upload-simple
    - id: export
      icon: icon-download-simple
  view_after_updated: true
  delete_multi_items: true
  view_history_after_created: true
  custom_history_backend_url: /api/job-codes/insert-new-record
  hide_action_row: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    title: Edit
    icon: icon-pencil-simple
    type: ghost-gray
  - id: delete
    title: Delete
    icon: icon-trash
    group: null
    type: ghost-gray
backend_url: /api/job-codes
screen_name: null
layout_options__actions_many: null
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: CompanyCode
    defaultName: CompanyCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Job
  parent:
    title: Job Structure
