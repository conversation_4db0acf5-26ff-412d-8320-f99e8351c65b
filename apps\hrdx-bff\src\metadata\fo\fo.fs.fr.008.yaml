id: FO.FS.FR.008
status: draft
sort: 149
user_created: 8ad48761-02ef-4f69-bef6-ac3a6f107610
date_created: '2024-06-13T09:52:40.795Z'
user_updated: 35b4f590-f747-4e53-8fa5-76aba65555c2
date_updated: '2025-07-19T02:37:18.564Z'
title: Job Group
requirement:
  time: 1743491772594
  blocks:
    - id: 6-STA3qVzq
      type: paragraph
      data:
        text: <PERSON><PERSON> báo mảng chức năng&nbsp;
  version: 2.30.7
screen_design: null
module: FO
local_fields:
  - code: code
    pinned: true
    title: Job Group Code
    description: Job Group Code
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    show_sort: true
  - code: shortName
    title: Short Name
    description: Short Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: longName
    title: Long Name
    description: Long Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: groupName
    title: Group
    description: Group
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: companyName
    title: Company
    description: Company
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: status
    title: Status
    description: Status
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: Boolean Tag
      collection: field_types
    show_sort: true
mock_data:
  - index: 0
    code: '00000001'
    effectiveDate: 2024/06/01
    shortName:
      default: FO
      vietnamese: FO
      english: FO
    longName:
      default: Front
      vietnamese: Front
      english: Front
    status: true
  - index: 1
    code: '00000002'
    effectiveDate: 2024/06/02
    shortName:
      default: MI
      vietnamese: MI
      english: MI
    longName:
      default: Middle
      vietnamese: Middle
      english: Middle
    status: true
  - index: 2
    code: '00000003'
    effectiveDate: 2024/06/03
    shortName:
      default: BA
      vietnamese: BA
      english: BA
    longName:
      default: Back
      vietnamese: Back
      english: Back
    status: true
local_buttons: null
layout: layout-table
form_config:
  fields:
    - type: group
      n_cols: 2
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - name: code
          label: Job Group Code
          type: text
          placeholder: Enter Job Group Code
          _disabled:
            transform: $not($.extend.formType = 'create')
          validators:
            - type: required
            - type: maxLength
              args: '8'
              text: Maxium 8 characters
            - type: pattern
              args: ^[a-zA-Z0-9_.&]+$
              text: The Code must not contain special characters.
        - type: dateRange
          label: Effective Date
          name: effectiveDate
          mode: date-picker
          _value:
            transform: $.extend.formType = 'create' ? $now()
          setting:
            format: dd/MM/yyyy
            type: date
          validators:
            - type: required
        - name: status
          label: Status
          type: radio
          _value:
            transform: $.extend.formType = 'create' and $not($.extend.isDuplicate) ? true
          radio:
            - label: Active
              value: true
            - label: Inactive
              value: false
        - name: shortName
          label: Short Name
          placeholder: Enter Short Name
          type: translation
          validators:
            - type: maxLength
              args: '40'
              text: Maxium 40 characters
    - name: code
      label: Job Group Code
      type: text
      _condition:
        transform: $.extend.formType = 'view'
    - type: dateRange
      label: Effective Date
      name: effectiveDate
      mode: date-picker
      setting:
        format: dd/MM/yyyy
        type: date
      _condition:
        transform: $.extend.formType = 'view'
    - name: status
      label: Status
      type: radio
      _value:
        transform: $.extend.formType = 'create' ? true
      radio:
        - label: Active
          value: true
        - label: Inactive
          value: false
      _condition:
        transform: $.extend.formType = 'view'
    - name: shortName
      label: Short Name
      placeholder: Enter Short Name
      type: translation
      validators:
        - type: maxLength
          args: '40'
          text: Maxium 40 characters
      _condition:
        transform: $.extend.formType = 'view'
    - name: longName
      label: Long Name
      placeholder: Enter Long Name
      type: translation
      validators:
        - type: required
        - type: maxLength
          args: '120'
          text: Maxium 120 characters
      _condition:
        transform: $not($.extend.formType = 'view')
    - name: longName
      label: Long Name
      placeholder: Enter Long Name
      type: translation
      validators:
        - type: maxLength
          args: '120'
          text: Maxium 120 characters
      _condition:
        transform: $.extend.formType = 'view'
    - name: applyforLevel
      label: Apply for Level
      placeholder: Select Apply for Level
      type: select
      select:
        - label: Company
          value: true
        - label: Group
          value: false
      _condition:
        transform: $.extend.formType = 'view'
    - name: group
      label: Group
      placeholder: Select Group
      type: selectCustom
      _select:
        transform: $groupList($.fields.effectiveDate)
      _condition:
        transform: $.fields.applyforLevel = false and $.extend.formType = 'view'
      outputValue: value
      inputValue: code
      actions:
        - view
      actionsConfig:
        view:
          formConfig:
            fields:
              - name: code
                label: Group Code
                type: text
              - type: dateRange
                label: Effective Date
                name: effectiveDate
                mode: date-picker
                setting:
                  format: dd/MM/yyyy
                  type: date
              - name: status
                label: Status
                type: radio
                radio:
                  - label: Active
                    value: true
                  - label: Inactive
                    value: false
              - name: shortName
                label: Short Name
                type: translation
              - name: longName
                label: Long Name
                type: translation
    - name: company
      label: Company
      placeholder: Select Company
      type: selectCustom
      _select:
        transform: $companiesList($.fields.effectiveDate)
      _condition:
        transform: $.fields.applyforLevel = true and $.extend.formType = 'view'
      _validateFn:
        transform: >-
          $exists($.value.code) ?
          ($companiesList($.fields.effectiveDate,$.value.code)[0] ?
          $companiesList($.fields.effectiveDate,$.value.code)[0] :
          '_setSelectValueNull')
      outputValue: value
      inputValue: code
      actions:
        - view
      actionsConfig:
        view:
          formConfig:
            fields:
              - name: code
                label: Company Code
                type: text
              - type: dateRange
                label: Effective Date
                name: effectiveDate
                mode: date-picker
                setting:
                  format: dd/MM/yyyy
                  type: date
              - name: status
                label: Status
                type: radio
                radio:
                  - label: Active
                    value: true
                  - label: Inactive
                    value: false
              - name: shortName
                label: Short Name
                type: translation
              - name: longName
                label: Long Name
                type: translation
    - type: group
      n_cols: 2
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - type: select
          label: Apply for Level
          name: applyforLevel
          _disabled:
            transform: $not($.extend.formType = 'create')
          outputValue: value
          placeholder: Select Apply for Level
          select:
            - label: Company
              value: true
            - label: Group
              value: false
          validators:
            - type: required
        - type: selectCustom
          label: Group
          name: group
          outputValue: value
          _validateFn:
            transform: >-
              $exists($.value.code) ? ($exists($.fields.group) ?
              $groupList($.fields.effectiveDate,$.value.code)[0] ?
              $groupList($.fields.effectiveDate,$.value.code)[0] :
              '_setSelectValueNull')
          dependantField: $.fields.applyforLevel
          clearValueIfRemoveControl: true
          _disabled:
            transform: $not($.extend.formType = 'create')
          placeholder: Select Group
          _condition:
            transform: $.fields.applyforLevel = false
          _value:
            transform: '$.fields.applyforLevel = true ? ''_setSelectValueNull'' '
          _select:
            transform: >-
              $.fields.applyforLevel = false ? $requirement() = false ?
              $groupList($.fields.effectiveDate)
          validators:
            - type: required
          actions:
            - view
          actionsConfig:
            view:
              formConfig:
                fields:
                  - name: code
                    label: Group Code
                    type: text
                  - type: dateRange
                    label: Effective Date
                    name: effectiveDate
                    mode: date-picker
                    setting:
                      format: dd/MM/yyyy
                      type: date
                  - name: status
                    label: Status
                    type: radio
                    radio:
                      - label: Active
                        value: true
                      - label: Inactive
                        value: false
                  - name: shortName
                    label: Short Name
                    type: translation
                  - name: longName
                    label: Long Name
                    type: translation
        - type: selectCustom
          label: Company
          name: company
          outputValue: value
          dependantField: $.fields.applyforLevel
          clearValueIfRemoveControl: true
          placeholder: Select Company
          _condition:
            transform: $.fields.applyforLevel = true
          _disabled:
            transform: $not($.extend.formType = 'create')
          _value:
            transform: '$.fields.applyforLevel = false ? ''_setSelectValueNull'' '
          _validateFn:
            transform: >-
              $exists($.value.code) ? ($exists($.fields.company) ?
              $companiesList($.fields.effectiveDate,$.value.code,($not($.extend.formType
              = 'edit') or $not($DateFormat($.fields.effectiveDate,
              'yyyy-MM-DD') = $DateFormat($.extend.defaultValue.effectiveDate,
              'yyyy-MM-DD'))) ? true)[0] ?
              $companiesList($.fields.effectiveDate,$.value.code,($not($.extend.formType
              = 'edit') or $not($DateFormat($.fields.effectiveDate,
              'yyyy-MM-DD') = $DateFormat($.extend.defaultValue.effectiveDate,
              'yyyy-MM-DD'))) ? true)[0] : '_setSelectValueNull')
          _select:
            transform: >-
              $.fields.applyforLevel = true ?
              $companiesList($.fields.effectiveDate,null,true)
          validators:
            - type: required
          actions:
            - view
          actionsConfig:
            view:
              formConfig:
                fields:
                  - name: code
                    label: Company Code
                    type: text
                  - type: dateRange
                    label: Effective Date
                    name: effectiveDate
                    mode: date-picker
                    setting:
                      format: dd/MM/yyyy
                      type: date
                  - name: status
                    label: Status
                    type: radio
                    radio:
                      - label: Active
                        value: true
                      - label: Inactive
                        value: false
                  - name: shortName
                    label: Short Name
                    type: translation
                  - name: longName
                    label: Long Name
                    type: translation
  historyHeaderTitle: '''View History Job Group'''
  sources:
    groupList:
      uri: '"/api/groups/by"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true},{'field':'code','operator': '$eq','value':$.code}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id': $item.id, 'code': $item.code},
        'additionalData': $item}})[]
      disabledCache: true
      params:
        - effectiveDate
        - code
    companiesList:
      uri: '"/api/companies/by"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':$.status},{'field':'code','operator':
        '$eq','value':$.code}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id': $item.id, 'code': $item.code},
        'additionalData': $item}})[]
      disabledCache: true
      params:
        - effectiveDate
        - code
        - status
    requirement:
      uri: '"/api/sub-groupings/requirement"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $
      disabledCache: true
filter_config:
  fields:
    - name: code
      label: Job Group Code
      labelType: type-grid
      placeholder: Enter Job Group code
      type: text
    - type: radio
      label: Status
      name: status
      labelType: type-grid
      value: ''
      radio:
        - label: All
          value: ''
        - label: Active
          value: true
        - label: Inactive
          value: false
    - name: shortName
      label: Short Name
      labelType: type-grid
      placeholder: Enter Short Name
      type: text
    - name: longName
      labelType: type-grid
      label: Long Name
      placeholder: Enter Long Name
      type: text
    - name: groupCode
      label: Group
      labelType: type-grid
      isLazyLoad: true
      type: selectAll
      placeholder: Select Group
      _options:
        transform: $groupList($.extend.limit,$.extend.page,$.extend.search)
    - type: selectAll
      label: Company
      name: companyCode
      labelType: type-grid
      placeholder: Select Company
      isLazyLoad: true
      _options:
        transform: $companyList($.extend.limit,$.extend.page,$.extend.search)
  filterMapping:
    - field: code
      operator: $cont
      valueField: code
    - field: status
      operator: $eq
      valueField: status
    - field: groupCode
      operator: $in
      valueField: groupCode.(value)
    - field: na_name
      operator: $cont
      valueField: longName
    - field: companyCode
      operator: $in
      valueField: companyCode.(value)
    - field: na_shortName
      operator: $cont
      valueField: shortName
  sources:
    groupList:
      uri: '"/api/groups/get-by"'
      method: GET
      queryTransform: >-
        {'limit':$.limit,'page':$.page,'filter': [{'field':'search','operator':
        '$eq','value':$.search}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    companyList:
      uri: '"/api/companies/get-by"'
      method: GET
      queryTransform: >-
        {'limit':$.limit,'page':$.page,'filter': [{'field':'search','operator':
        '$eq','value':$.search}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
layout_options:
  toolTable:
    export: true
    import: true
    adjustDisplay: true
  tool_table:
    - id: import
      icon: icon-upload-simple
    - id: export
      icon: icon-download-simple
  view_after_updated: true
  delete_multi_items: true
  view_history_after_created: true
  custom_history_backend_url: /api/groupings/insert-new-record
  hide_action_row: true
  historyFilterMapping:
    - field: groupCode
      operator: $eq
      valueField: groupCode
    - field: companyCode
      operator: $eq
      valueField: companyCode
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    title: Edit
    icon: pencil
  - id: delete
    title: Delete
    icon: trash
    group: null
backend_url: /api/groupings
screen_name: groupings
layout_options__actions_many: null
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: CompanyCode
    defaultName: CompanyCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Job Group
  parent:
    title: Job Structure
