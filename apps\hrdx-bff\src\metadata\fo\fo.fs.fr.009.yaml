id: FO.FS.FR.009
status: draft
sort: 188
user_created: 8ad48761-02ef-4f69-bef6-ac3a6f107610
date_created: '2024-06-13T10:15:30.662Z'
user_updated: 35b4f590-f747-4e53-8fa5-76aba65555c2
date_updated: '2025-07-30T04:40:56.342Z'
title: Job SubGroup
requirement:
  time: 1748920327961
  blocks:
    - id: lFlA8eyK4U
      type: paragraph
      data:
        text: '&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;'
  version: 2.30.7
screen_design: null
module: FO
local_fields:
  - code: code
    pinned: true
    title: Job SubGroup Code
    description: Job SubGroup Code
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    show_sort: true
  - code: shortName
    title: Short Name
    description: Short Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: longName
    title: Long Name
    description: Long Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: parentJobGroup
    title: Job Group
    description: Parent JobGroup
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: groupName
    title: Group
    description: Group
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: companyName
    title: Company
    description: Company
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: status
    title: Status
    description: Status
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: Boolean Tag
      collection: field_types
    show_sort: true
mock_data:
  - code: '00000001'
    short_name: FIS QA
    long_name: Đảm bảo chất lượng
    company: FIS
    parent_job_group: Back
    effectiveDate: 01/05/2024
    status: true
  - code: '00000002'
    short_name: FISHTBO
    long_name: Hỗ trợ BO
    company: FIS
    parent_job_group: Back
    effectiveDate: 02/05/2024
    status: true
  - code: '00000003'
    short_name: FISHTFO
    long_name: Hỗ trợ FO
    company: FIS
    parent_job_group: Back
    effectiveDate: 03/05/2024
    status: true
local_buttons: null
layout: layout-table
form_config:
  formSize:
    create: large
    edit: large
    proceed: large
  fields:
    - type: group
      label: Basic Information
      collapse: false
      disableCollapse: false
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - type: group
          n_cols: 2
          fields:
            - name: code
              label: Job  SubGroup Code
              type: text
              placeholder: Enter Job SubGroup Code
              _disabled:
                transform: $not($.extend.formType = 'create')
              validators:
                - type: required
                - type: maxLength
                  args: '8'
                  text: Maxium 8 characters
                - type: pattern
                  args: ^[a-zA-Z0-9_.&]+$
                  text: The Code must not contain special characters.
            - type: dateRange
              label: Effective Date
              name: effectiveDate
              mode: date-picker
              validators:
                - type: required
              setting:
                format: dd/MM/yyyy
                type: date
              _value:
                transform: >-
                  $.extend.formType = 'create' ?
                  $not($exists($.fields.effectiveDate)) ?  $now()
            - type: radio
              label: Status
              name: status
              _value:
                transform: >-
                  $.extend.formType = 'create' and $not($.extend.isDuplicate) ?
                  true
              radio:
                - label: Active
                  value: true
                - label: Inactive
                  value: false
            - type: translation
              label: Short Name
              name: shortName
              placeholder: Enter Short Name
              validators:
                - type: maxLength
                  args: '40'
                  text: Maxium 40 characters
        - type: translation
          label: Long Name
          name: longName
          placeholder: Enter Long Name
          validators:
            - type: maxLength
              args: '120'
              text: Maxium 120 characters
            - type: required
        - type: select
          label: Apply for Level
          name: applyforLevel
          outputValue: value
          _disabled:
            transform: $not($.extend.formType = 'create')
          placeholder: Select Apply for Level
          validators:
            - type: required
          clearFieldsAfterChange:
            - jobCodeObj
            - groupObj
            - companyObj
            - grouping
          select:
            - label: Company
              value: true
            - label: Group
              value: false
        - type: selectCustom
          label: Group
          name: groupObj
          _disabled:
            transform: $not($.extend.formType = 'create')
          outputValue: value
          placeholder: Select Group
          isLazyLoad: true
          _condition:
            transform: $.fields.applyforLevel = false
          clearFieldsAfterChange:
            - jobCodeObj
            - grouping
          clearValueIfRemoveControl: true
          _validateFn:
            transform: >-
              $exists($.fields.groupObj.code) ?
              ($groupList($.extend.limit,$.extend.page,$.fields.effectiveDate,$.fields.groupObj.code)[0]
              ?
              $groupList($.extend.limit,$.extend.page,$.fields.effectiveDate,$.fields.groupObj.code)[0]
              : '_setSelectValueNull')
          _select:
            transform: >-
              $requirement() = false ?
              $groupList($.extend.limit,$.extend.page,$.fields.effectiveDate,null,$.extend.search)
          validators:
            - type: required
          actions:
            - view
          actionsConfig:
            view:
              formConfig:
                fields:
                  - name: code
                    label: Group Code
                    type: text
                  - type: dateRange
                    label: Effective Date
                    name: effectiveDate
                    mode: date-picker
                    setting:
                      format: dd/MM/yyyy
                      type: date
                  - name: status
                    label: Status
                    type: radio
                    radio:
                      - label: Active
                        value: true
                      - label: Inactive
                        value: false
                  - name: shortName
                    label: Short Name
                    type: translation
                  - name: longName
                    label: Long Name
                    type: translation
        - type: selectCustom
          label: Company
          name: companyObj
          isLazyLoad: true
          _disabled:
            transform: $not($.extend.formType = 'create')
          outputValue: value
          _validateFn:
            transform: >-
              $exists($.value.code) ?
              ($companyList($.extend.limit,$.extend.page,$.fields.effectiveDate,$.value.code,null,($not($.extend.formType
              = 'edit') or $not($DateFormat($.fields.effectiveDate,
              'yyyy-MM-DD') = $DateFormat($.extend.defaultValue.effectiveDate,
              'yyyy-MM-DD'))) ? true)[0] ?
              $companyList($.extend.limit,$.extend.page,$.fields.effectiveDate,$.value.code,null,($not($.extend.formType
              = 'edit') or $not($DateFormat($.fields.effectiveDate,
              'yyyy-MM-DD') = $DateFormat($.extend.defaultValue.effectiveDate,
              'yyyy-MM-DD'))) ? true)[0] : '_setSelectValueNull')
          _detailData:
            transform: >-
              $exists($.extend.defaultValue.companyObj.value.code) ? ($info :=
              $companyList($.extend.limit,$.extend.page,$.fields.effectiveDate,$.extend.defaultValue.companyObj.value.code,null,($not($.extend.formType
              = 'edit') or $not($DateFormat($.fields.effectiveDate,
              'yyyy-MM-DD') = $DateFormat($.extend.defaultValue.effectiveDate,
              'yyyy-MM-DD'))) ? true)[0]; $exists($info) ? $info )
          clearFieldsAfterChange:
            - jobCodeObj
            - grouping
          clearValueIfRemoveControl: true
          placeholder: Select Company
          _condition:
            transform: $.fields.applyforLevel = true
          _select:
            transform: >-
              $.fields.applyforLevel ?
              $companyList($.extend.limit,$.extend.page,$.fields.effectiveDate,null,$.extend.search,true)
              : ''
          validators:
            - type: required
          actions:
            - view
          actionsConfig:
            view:
              formConfig:
                fields:
                  - name: code
                    label: Company Code
                    type: text
                  - type: dateRange
                    label: Effective Date
                    name: effectiveDate
                    mode: date-picker
                    setting:
                      format: dd/MM/yyyy
                      type: date
                  - name: status
                    label: Status
                    type: radio
                    radio:
                      - label: Active
                        value: true
                      - label: Inactive
                        value: false
                  - name: shortName
                    label: Short Name
                    type: translation
                  - name: longName
                    label: Long Name
                    type: translation
        - type: selectCustom
          label: Job Group
          name: grouping
          isLazyLoad: true
          _condition:
            transform: ($.fields.companyObj or $.fields.groupObj)
          placeholder: Select Job Group
          outputValue: value
          _select:
            transform: >-
              ($exists($.fields.companyObj) or $exists($.fields.groupObj)) ?
              $groupingList($.extend.limit,$.extend.page,$.fields.effectiveDate,$.fields.applyforLevel
              = true ? $.fields.companyObj.id : null,$.fields.applyforLevel =
              false ? $.fields.groupObj.id : null,null,true)
          _validateFn:
            transform: >-
              $exists($test($.value.code)) ?
              ($groupingList($.extend.limit,$.extend.page,$.fields.effectiveDate,$.fields.applyforLevel
              = true ? $.fields.companyObj.id : null,$.fields.applyforLevel =
              false ? $.fields.groupObj.id :
              null,$.value.code,($not($.extend.formType = 'edit') or
              $not($DateFormat($.fields.effectiveDate, 'yyyy-MM-DD') =
              $DateFormat($.extend.defaultValue.effectiveDate, 'yyyy-MM-DD'))) ?
              true)[0] ?
              $groupingList($.extend.limit,$.extend.page,$.fields.effectiveDate,$.fields.applyforLevel
              = true ? $.fields.companyObj.id : null,$.fields.applyforLevel =
              false ? $.fields.groupObj.id :
              null,$.value.code,($not($.extend.formType = 'edit') or
              $not($DateFormat($.fields.effectiveDate, 'yyyy-MM-DD') =
              $DateFormat($.extend.defaultValue.effectiveDate, 'yyyy-MM-DD'))) ?
              true)[0] : '_setSelectValueNull')
          _detailData:
            transform: >-
              ($exists($.extend.defaultValue.grouping.value.code) or
              $exists($.extend.defaultValue.grouping.code)) ? ($info :=
              $groupingList($.extend.limit,$.extend.page,$.fields.effectiveDate,$.fields.applyforLevel
              = true ? $.fields.companyObj.id : null,$.fields.applyforLevel =
              false ? $.fields.groupObj.id :
              null,$exists($.extend.defaultValue.grouping.value.code) ?
              $.extend.defaultValue.grouping.value.code :
              $.extend.defaultValue.grouping.code,($not($.extend.formType =
              'edit') or $not($DateFormat($.fields.effectiveDate, 'yyyy-MM-DD')
              = $DateFormat($.extend.defaultValue.effectiveDate, 'yyyy-MM-DD')))
              ? true)[0]; $exists($info) ? $info )
          validators:
            - type: required
          actions:
            - view
          actionsConfig:
            view:
              formConfig:
                fields:
                  - name: code
                    label: Job Group Code
                    type: text
                  - type: dateRange
                    label: Effective Date
                    name: effectiveDate
                    mode: date-picker
                    setting:
                      format: dd/MM/yyyy
                      type: date
                  - name: status
                    label: Status
                    type: radio
                    radio:
                      - label: Active
                        value: true
                      - label: Inactive
                        value: false
                  - name: shortName
                    label: Short Name
                    type: translation
                  - name: longName
                    label: Long Name
                    type: translation
    - type: group
      label: Basic Information
      collapse: false
      disableCollapse: false
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - type: group
          n_cols: 1
          fields:
            - name: code
              label: Job SubGroup Code
              type: text
              _condition:
                transform: $.extend.formType = 'view'
            - type: dateRange
              label: Effective Date
              name: effectiveDate
              mode: date-picker
              setting:
                format: dd/MM/yyyy
                type: date
              _value:
                transform: >-
                  $.extend.formType = 'create' ?
                  $not($exists($.fields.effectiveDate)) ?  $now()
        - type: radio
          label: Status
          name: status
          _value:
            transform: >-
              $.extend.formType = 'create' ? $not($exists($.fields.status)) ? 
              true
          radio:
            - label: Active
              value: true
            - label: Inactive
              value: false
        - type: group
          n_cols: 1
          fields:
            - type: translation
              label: Short Name
              name: shortName
              validators:
                - type: maxLength
                  args: '15'
                  text: Maxium 15 characters
            - type: translation
              label: Long Name
              name: longName
              validators:
                - type: maxLength
                  args: '100'
                  text: Maxium 100 characters
            - type: select
              label: Apply for Level
              name: applyforLevel
              outputValue: value
              placeholder: Select Apply for Level
              value: true
              select:
                - label: Company
                  value: true
                - label: Group
                  value: false
            - name: companyObj
              label: Company
              placeholder: Select Company
              type: selectCustom
              _select:
                transform: >-
                  $companyList(1,1,$.fields.effectiveDate,$.fields.companyObj.value.code)
              _condition:
                transform: $.fields.applyforLevel = true and $.extend.formType = 'view'
              outputValue: value
              inputValue: code
              actions:
                - view
              actionsConfig:
                view:
                  formConfig:
                    fields:
                      - name: code
                        label: Company Code
                        type: text
                      - type: dateRange
                        label: Effective Date
                        name: effectiveDate
                        mode: date-picker
                        setting:
                          format: dd/MM/yyyy
                          type: date
                      - name: status
                        label: Status
                        type: radio
                        radio:
                          - label: Active
                            value: true
                          - label: Inactive
                            value: false
                      - name: shortName
                        label: Short Name
                        type: translation
                      - name: longName
                        label: Long Name
                        type: translation
            - type: selectCustom
              label: Group
              name: groupObj
              placeholder: Select Group
              outputValue: value
              inputValue: code
              _condition:
                transform: $.fields.applyforLevel = false and $.extend.formType = 'view'
              _select:
                transform: >-
                  $exists($.fields.effectiveDate) ?
                  $groupList(1,1,$.fields.effectiveDate,$.fields.groupObj.value.code)
              actions:
                - view
              actionsConfig:
                view:
                  formConfig:
                    fields:
                      - name: code
                        label: Group Code
                        type: text
                      - type: dateRange
                        label: Effective Date
                        name: effectiveDate
                        mode: date-picker
                        setting:
                          format: dd/MM/yyyy
                          type: date
                      - name: status
                        label: Status
                        type: radio
                        radio:
                          - label: Active
                            value: true
                          - label: Inactive
                            value: false
                      - name: shortName
                        label: Short Name
                        type: translation
                      - name: longName
                        label: Long Name
                        type: translation
            - type: selectCustom
              label: Job Group
              name: grouping
              _condition:
                transform: $.extend.formType = 'view'
              outputValue: value
              inputValue: code
              _validateFn:
                transform: >-
                  $exists($test($.extend.defaultValue.grouping.value.code)) ?
                  $groupingList($.fields.effectiveDate,null,null,$.extend.defaultValue.grouping.value.code)[0]
                  : '_setSelectValueNull')
              _select:
                transform: >-
                  $groupingList($.extend.limit,$.extend.page,$.fields.effectiveDate,null,null,$.extend.defaultValue.grouping.value.code)
              actions:
                - view
              actionsConfig:
                view:
                  formConfig:
                    fields:
                      - name: code
                        label: Job Group Code
                        type: text
                      - type: dateRange
                        label: Effective Date
                        name: effectiveDate
                        mode: date-picker
                        setting:
                          format: dd/MM/yyyy
                          type: date
                      - name: status
                        label: Status
                        type: radio
                        radio:
                          - label: Active
                            value: true
                          - label: Inactive
                            value: false
                      - name: shortName
                        label: Short Name
                        type: translation
                      - name: longName
                        label: Long Name
                        type: translation
    - type: group
      label: Job
      collapse: false
      disableCollapse: false
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - type: selectAll
          name: jobCodeObj
          label: Job
          placeholder: Select Job
          isLazyLoad: true
          outputValue: value
          _options:
            transform: >-
              ($.fields.companyObj or $.fields.groupObj) ?
              $jobListCheck($.extend.limit,$.extend.page,$.fields.effectiveDate,null,$.extend.search,$.fields.companyObj.id,true)
          _validateFn:
            transform: >-
              ($not($isNilorEmpty($.fields.jobCodeObj.value.code)) or
              $not($isNilorEmpty($.fields.jobCodeObj.code))) ?
              ($jobListCheck(10000000,0,$.fields.effectiveDate,$not($isNilorEmpty($.fields.jobCodeObj.value.code))
              ? $.fields.jobCodeObj.value.code :
              $.fields.jobCodeObj.code,null,null,($not($.extend.formType =
              'edit') or $not($DateFormat($.fields.effectiveDate, 'yyyy-MM-DD')
              = $DateFormat($.extend.defaultValue.effectiveDate, 'yyyy-MM-DD')))
              ? true)[0] ?
              $jobListCheck(10000000,0,$.fields.effectiveDate,$not($isNilorEmpty($.fields.jobCodeObj.value.code))
              ? $.fields.jobCodeObj.value.code :
              $.fields.jobCodeObj.code,null,null,($not($.extend.formType =
              'edit') or $not($DateFormat($.fields.effectiveDate, 'yyyy-MM-DD')
              = $DateFormat($.extend.defaultValue.effectiveDate, 'yyyy-MM-DD')))
              ? true) : '_setSelectValueNull')
          validators:
            - type: required
    - type: group
      label: Job
      collapse: false
      disableCollapse: false
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - type: selectCustom
          label: Job
          name: jobCodeObj
          placeholder: Select Job
          outputValue: value
          inputValue: code
          mode: multiple
          _select:
            transform: >-
              $jobListCheck(10000000,0,$.fields.effectiveDate,
              $.fields.jobCodeObj.value.code)
          _validateFn:
            transform: >-
              $not($isNilorEmpty($.defaultValue.jobCodeObj.value.code)) ?
              ($jobListCheck(10000000,0,$.fields.effectiveDate,
              $.defaultValue.jobCodeObj.value.code)[0] ?
              $jobListCheck(10000000,0,$.fields.effectiveDate,
              $.defaultValue.jobCodeObj.value.code) : '_setSelectValueNull')
          actions:
            - view
          actionsConfig:
            view:
              formConfig:
                fields:
                  - name: code
                    label: Job Code
                    type: text
                  - type: dateRange
                    label: Effective Date
                    name: effectiveDate
                    mode: date-picker
                    setting:
                      format: dd/MM/yyyy
                      type: date
                  - name: status
                    label: Status
                    type: radio
                    radio:
                      - label: Active
                        value: true
                      - label: Inactive
                        value: false
                  - name: shortName
                    label: Short Name
                    type: translation
                  - name: longName
                    label: Long Name
                    type: translation
  historyHeaderTitle: '''View History Job SubGroup'''
  sources:
    groupList:
      uri: '"/api/groups/get-by"'
      method: GET
      queryTransform: >-
        {'limit':$.limit,'page':$.page,'filter': [{'field':'search','operator':
        '$eq','value':$.search},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true},{'field':'code','operator': '$eq','value':$.code}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id':$item.id,'code':$item.code},
        'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - effectiveDate
        - code
        - search
    companyList:
      uri: '"/api/companies/get-by"'
      method: GET
      queryTransform: >-
        {'limit':$.limit,'page':$.page,'filter': [{'field':'search','operator':
        '$eq','value':$.search},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':$.status},{'field':'code','operator':
        '$eq','value':$.code}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id':$item.id,'code':$item.code},
        'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - effectiveDate
        - code
        - search
        - status
    jobListCheck:
      uri: '"/api/job-codes/get-by-post"'
      method: POST
      queryTransform: ''
      bodyTransform: >-
        {'limit':$.limit,'page':$.page,'search': $.search,'effectiveDatesearch':
        $.effectiveDate,'filter':[{'field':'code','operator':'$in','value':$.code}],'CompanyIds':
        [$.companyId]}
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id':$item.id,'code':$item.code},
        'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - effectiveDate
        - code
        - search
        - companyId
        - status
    groupingList:
      uri: '"/api/groupings/get-by"'
      method: GET
      queryTransform: >-
        {'limit':$.limit,'page':$.page,'filter':
        [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':$.status}, {'field':'companyId','operator':
        '$eq','value':$.companyId}, {'field':'groupId','operator':
        '$eq','value':$.groupId},{'field':'code','operator':
        '$eq','value':$.code}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id':$item.id,'code':$item.code},
        'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - effectiveDate
        - companyId
        - groupId
        - code
        - status
    requirement:
      uri: '"/api/sub-groupings/requirement"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $
      disabledCache: true
  _mode:
    transform: $not($.extend.formType = 'view') ? 'mark-scroll'
filter_config:
  fields:
    - type: text
      label: Job SubGroup Code
      name: code
      labelType: type-grid
      placeholder: Enter Job SubGroup Code
    - type: radio
      label: Status
      name: status
      labelType: type-grid
      value: ''
      radio:
        - label: All
          value: ''
        - value: true
          label: Active
        - value: false
          label: Inactive
    - type: text
      label: Short Name
      labelType: type-grid
      placeholder: Enter Short Name
      name: shortName
    - type: text
      label: Long Name
      labelType: type-grid
      placeholder: Enter Long Name
      name: longName
    - type: selectAll
      label: Job Group
      isLazyLoad: true
      name: groupingCode
      labelType: type-grid
      placeholder: Select Job Group
      _options:
        transform: $jobGroupList($.extend.limit,$.extend.page,$.extend.search)
    - type: selectAll
      label: Company
      name: companyCode
      isLazyLoad: true
      labelType: type-grid
      placeholder: Select Company
      _options:
        transform: $companyList($.extend.limit,$.extend.page,$.extend.search)
    - type: selectAll
      label: Group
      labelType: type-grid
      name: groupCode
      isLazyLoad: true
      placeholder: Select Group
      _options:
        transform: $groupsList($.extend.limit,$.extend.page,$.extend.search)
  filterMapping:
    - field: code
      operator: $cont
      valueField: code
    - field: na_name
      operator: $cont
      valueField: longName
    - field: na_shortName
      operator: $cont
      valueField: shortName
    - field: status
      operator: $eq
      valueField: status
    - field: groupingCode
      operator: $in
      valueField: groupingCode.(value)
    - field: companyCode
      operator: $in
      valueField: companyCode.(value)
    - field: groupCode
      operator: $in
      valueField: groupCode.(value)
  sources:
    jobGroupList:
      uri: '"/api/groupings/get-by"'
      method: GET
      queryTransform: >-
        {'limit':$.limit,'page':$.page,'filter': [{'field':'search','operator':
        '$eq','value':$.search}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    companyList:
      uri: '"/api/companies/get-by"'
      method: GET
      queryTransform: >-
        {'limit':$.limit,'page':$.page,'filter': [{'field':'search','operator':
        '$eq','value':$.search}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    groupsList:
      uri: '"/api/groups/get-by"'
      method: GET
      queryTransform: >-
        {'limit':$.limit,'page':$.page,'filter': [{'field':'search','operator':
        '$eq','value':$.search}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
layout_options:
  toolTable:
    export: true
    import: true
    adjustDisplay: true
  tool_table:
    - id: import
      icon: icon-upload-simple
    - id: export
      icon: icon-download-simple
  view_after_updated: true
  delete_multi_items: true
  view_history_after_created: true
  custom_history_backend_url: /api/sub-groupings/insert-new-record
  hide_action_row: true
  historyFilterMapping:
    - field: groupCode
      operator: $eq
      valueField: groupCode
    - field: companyCode
      operator: $eq
      valueField: companyCode
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: view
    icon: icon-eye
    type: ghost-gray
  - id: edit
    icon: icon-pencil-simple
    type: ghost-gray
  - id: delete
    icon: icon-trash
    type: ghost-gray
backend_url: /api/sub-groupings
screen_name: null
layout_options__actions_many: null
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: CompanyCode
    defaultName: CompanyCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Job SubGroup
  parent:
    title: Job Structure
