id: FO.FS.FR.010
status: draft
sort: 188
user_created: 59722cb8-f858-481b-97f1-c97dd2a8e4be
date_created: '2024-06-14T03:37:24.221Z'
user_updated: 35b4f590-f747-4e53-8fa5-76aba65555c2
date_updated: '2025-07-19T02:35:58.240Z'
title: Position
requirement:
  time: 1744946584640
  blocks:
    - id: 5fIH6LwFyb
      type: paragraph
      data:
        text: Chức năng cho phép tạo mới/cập nhật thông tin Position
  version: 2.30.7
screen_design: null
module: FO
local_fields:
  - code: code
    pinned: true
    title: Position Code
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    description: Position Code
    show_sort: true
  - code: shortName
    pinned: false
    title: Short Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    description: Short Name
    show_sort: true
  - code: longName
    pinned: false
    title: Position Title
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    description: Position Title
    show_sort: true
  - code: jobcodeNames
    pinned: false
    title: Job
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: Job Code
    show_sort: true
  - code: department
    pinned: false
    title: Department
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: Department
    show_sort: true
  - code: status
    pinned: false
    title: Status
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: Boolean Tag
      collection: field_types
    description: Status
    show_sort: true
mock_data:
  - position: '00000001'
    short:
      default: CVNS_01
      vietnamese: CVNS_01
      english: CVNS_01
    positionTitle:
      default: Chuyên viên nhân sự FIS HN
      vietnamese: Chuyên viên nhân sự FIS HN
      english: Chuyên viên nhân sự FIS HN
    position_description: Chuyên viên nhân sự
    status: true
    job_code: Chuyên viên nhân sự (CVNS_00000001)
    job_title: Chuyên viên nhân sự
    FTE: '1'
    department: Ban nhân sự FIS HN(FISHNBNS-00000001)
    effectiveDate: 01/05/2024
  - position: '00000002'
    short:
      default: CVNS_02
      vietnamese: CVNS_02
      english: CVNS_02
    positionTitle:
      default: Chuyên viên nhân sự FIS HN
      vietnamese: Chuyên viên nhân sự FIS HN
      english: Chuyên viên nhân sự FIS HN
    position_description: Chuyên viên nhân sự
    status: true
    job_code: Chuyên viên nhân sự (CVNS_00000002)
    job_title: Chuyên viên nhân sự
    FTE: '1'
    department: Ban nhân sự FIS HN(FISHNBNS-00000002)
    effectiveDate: 01/05/2024
  - position: '00000003'
    short:
      default: CVNS_03
      vietnamese: CVNS_03
      english: CVNS_03
    positionTitle:
      default: Chuyên viên nhân sự FIS HN
      vietnamese: Chuyên viên nhân sự FIS HN
      english: Chuyên viên nhân sự FIS HN
    position_description: Chuyên viên nhân sự
    status: true
    job_code: Chuyên viên nhân sự (CVNS_00000003)
    job_title: Chuyên viên nhân sự
    FTE: '1'
    department: Ban nhân sự FIS HN(FISHNBNS-00000003)
    effectiveDate: 01/05/2024
local_buttons: null
layout: layout-table
form_config:
  formSize:
    create: large
    edit: large
    proceed: large
  fields:
    - type: group
      label: Position and Job Details
      collapse: false
      disableEventCollapse: true
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - type: group
          n_cols: 2
          fields:
            - name: code
              label: Position Code
              type: text
              placeholder: Automatic
              disabled: true
              _class:
                transform: '''required'''
            - type: dateRange
              label: Effective Date
              name: effectiveDate
              placeholder: dd/MM/yyyy
              mode: date-picker
              setting:
                format: dd/MM/yyyy
                type: date
              validators:
                - type: required
              _value:
                transform: >-
                  $.extend.formType = 'create' ?
                  $not($exists($.fields.effectiveDate)) ?  $now()
        - type: group
          n_cols: 2
          fields:
            - name: status
              label: Status
              type: radio
              _value:
                transform: >-
                  $.extend.formType = 'create' and $not($.extend.isDuplicate) ?
                  true
              radio:
                - label: Active
                  value: true
                - label: Inactive
                  value: false
            - type: checkbox
              name: isCriticalPosition
              label: Critical Position
              _value:
                transform: '$.fields.isCriticalPosition ? true : false'
              col: 1
              _condition:
                transform: $not($.extend.formType = 'view')
        - type: group
          n_cols: 2
          fields:
            - name: shortName
              label: Short Name
              placeholder: Enter Short Name
              type: translation
              validators:
                - type: maxLength
                  args: '40'
                  text: Maximum 40 characters
            - name: longName
              label: Position Title
              type: translation
              placeholder: Enter Position Title
              validators:
                - type: required
                - type: maxLength
                  args: '120'
                  text: Maximum 120 characters
        - type: translationTextArea
          name: description
          label: Position Description
          placeholder: Enter Description
          validators:
            - type: maxLength
              args: '4000'
              text: Maximum length is 4000 characters.
          textarea:
            autoSize:
              minRows: 3
              maxRows: 5
            maxCharCount: 4000
        - type: selectCustom
          label: Job
          name: jobCodeObjForm
          placeholder: Select Job
          isLazyLoad: true
          outputValue: value
          _validateFn:
            transform: >-
              $exists($.value.code) ?
              ($jobcodesList(0,0,$.fields.effectiveDate,null,$.value.code,true)[0]
              ?
              $jobcodesList(0,0,$.fields.effectiveDate,null,$.value.code,true)[0]
              : '_setSelectValueNull')
          _select:
            transform: >-
              $jobcodesList($.extend.limit,$.extend.page,$.fields.effectiveDate,$.extend.search,null,true)
          validators:
            - type: required
          actions:
            - view
          actionsConfig:
            view:
              formConfig:
                fields:
                  - name: code
                    label: Job Code
                    type: text
                  - type: dateRange
                    label: Effective Date
                    name: effectiveDate
                    mode: date-picker
                    setting:
                      format: dd/MM/yyyy
                      type: date
                  - name: status
                    label: Status
                    type: radio
                    radio:
                      - label: Active
                        value: true
                      - label: Inactive
                        value: false
                  - name: shortName
                    label: Short Name
                    type: translation
                  - name: name
                    label: Long Name
                    type: translation
        - type: group
          n_cols: 4
          fields:
            - name: isHired
              label: To Be Hired
              type: select
              col: 2
              placeholder: Select Item
              _value:
                transform: $.extend.formType = 'create' ? true
              outputValue: value
              select:
                - label: 'Yes'
                  value: true
                - label: 'No'
                  value: false
              validators:
                - type: required
            - name: fte
              label: FTE
              type: number
              col: 1
              _value:
                transform: $.extend.formType = 'create' ? 1
              placeholder: Enter FTE
              validators:
                - type: pattern
                  args: ^\d{1,3}(\.\d{1,2})?$
                  text: Field type must be number.
            - name: positionIncumbent
              label: Position Incumbent
              type: number
              placeholder: Enter Position Incumbent
              col: 1
              _value:
                transform: $.extend.formType = 'create' ? 1
              validators:
                - type: pattern
                  args: ^\d{1,5}$
                  text: Maximum 5 characters
                - type: required
    - type: group
      label: Position and Job Details
      fieldGroupTitleStyle:
        border: none
      collapse: false
      disableEventCollapse: false
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - type: group
          n_cols: 1
          fields:
            - name: code
              label: Position Code
              type: text
              placeholder: Automatic
              disabled: true
            - type: dateRange
              label: Effective Date
              name: effectiveDate
              placeholder: dd/MM/yyyy
              mode: date-picker
              setting:
                format: dd/MM/YYYY
                type: date
              _value:
                transform: >-
                  $.extend.formType = 'create' ?
                  $not($exists($.fields.effectiveDate)) ?  $now()
        - type: group
          n_cols: 1
          fields:
            - name: status
              label: Status
              type: radio
              value: true
              radio:
                - label: Active
                  value: true
                - label: Inactive
                  value: false
            - type: checkbox
              name: isCriticalPosition
              label: Critical Position
              labelType: type2
              showLabelCheckbox: false
              disabled: true
              _condition:
                transform: $.extend.formType = 'view'
        - type: group
          n_cols: 1
          fields:
            - name: shortName
              label: Short Name
              placeholder: Enter Short Name
              type: translation
              validators:
                - type: maxLength
                  args: '40'
                  text: Maximum 40 characters
            - name: longName
              label: Position Title
              type: translation
              placeholder: Enter Position Title
        - type: translationTextArea
          name: description
          label: Position Description
          placeholder: Enter Description
          validators:
            - type: maxLength
              args: '4000'
              text: Maximum length is 4000 characters.
          textarea:
            autoSize:
              minRows: 3
              maxRows: 5
            maxCharCount: 4000
        - type: group
          n_cols: 1
          fields:
            - type: selectCustom
              label: Job
              name: jobCodeObjForm
              placeholder: Select Job Code
              outputValue: value
              inputValue: code
              _select:
                transform: >-
                  $jobcodesList($.extend.limit,$.extend.page,$.fields.effectiveDate,null,$.fields.jobCodeObjForm.value.code)
              actions:
                - view
              actionsConfig:
                view:
                  formConfig:
                    fields:
                      - name: code
                        label: Job Code
                        type: text
                      - type: dateRange
                        label: Effective Date
                        name: effectiveDate
                        mode: date-picker
                        setting:
                          format: dd/MM/yyyy
                          type: date
                      - name: status
                        label: Status
                        type: radio
                        radio:
                          - label: Active
                            value: true
                          - label: Inactive
                            value: false
                      - name: shortName
                        label: Short Name
                        type: translation
                      - name: longName
                        label: Long Name
                        type: translation
            - name: fte
              label: FTE
              type: number
              placeholder: Enter FTE
              _value:
                transform: $.extend.formType = 'create' ? 1
              validators:
                - type: ppx-custom
                  args:
                    transform: $ParseInt($.value) > $ParseInt(3.2)
                  text: FTE cannot be greater than 3.2
            - name: positionIncumbent
              label: Position Incumbent
              type: number
              _value:
                transform: $.extend.formType = 'create' ? 1
              placeholder: Enter Position Incumbent
              validators:
                - type: maxLength
                  args: '5'
                  text: Maximum 5 characters
        - type: checkbox
          name: isHired
          label: To Be Hired
    - type: group
      label: Organization Information
      collapse: false
      _disableEventCollapse:
        transform: '$not($.extend.formType = ''view'') ? true : false'
      _n_cols:
        transform: $not($.extend.formType = 'view') ? 2
      fields:
        - type: selectCustom
          label: Department
          name: departmentObj
          isLazyLoad: true
          placeholder: Select Department
          outputValue: value
          validators:
            - type: required
          col: 2
          _select:
            transform: >-
              $departmentsList($.extend.limit,
              $.extend.page,$.fields.effectiveDate,$.extend.search,null,true)
          _validateFn:
            transform: >-
              $exists($.value.code) ? ($departmentsList(0,
              0,$.fields.effectiveDate,null,$.value.code,true)[0] ?
              $departmentsList(0,
              0,$.fields.effectiveDate,null,$.value.code,true)[0] :
              '_setSelectValueNull')
          _condition:
            transform: $not($.extend.formType = 'view')
          actions:
            - view
          actionsConfig:
            view:
              formConfig:
                fields:
                  - name: code
                    label: Deparment Code
                    type: text
                  - type: dateRange
                    label: Effective Date
                    name: effectiveDate
                    mode: date-picker
                    setting:
                      format: dd/MM/yyyy
                      type: date
                  - name: status
                    label: Status
                    type: radio
                    radio:
                      - label: Active
                        value: true
                      - label: Inactive
                        value: false
                  - name: shortName
                    label: Short Name
                    type: translation
                  - name: longName
                    label: Long Name
                    type: translation
        - type: text
          label: Department
          name: departmentText
          _condition:
            transform: $.extend.formType = 'view'
          labelTransform:
            transform: $.name.default & ' ' & '(' & $.code & ')'
          formConfig:
            fields:
              - name: code
                label: Department Code
                type: text
              - type: dateRange
                label: Effective Date
                name: effectiveDate
                mode: date-picker
                setting:
                  format: dd/MM/yyyy
                  type: date
              - name: status
                label: Status
                type: radio
                radio:
                  - label: Active
                    value: true
                  - label: Inactive
                    value: false
              - name: shortName
                label: Short Name
                type: translation
              - name: longName
                label: Long Name
                type: translation
        - type: selectCustom
          label: Location
          name: locationObj
          outputValue: value
          placeholder: Select Location
          _condition:
            transform: $not($.extend.formType = 'view')
          isLazyLoad: true
          _select:
            transform: >-
              $not($isNilorEmpty($.fields.departmentObj.code)) ?
              $locationsList($.extend.limit,$.extend.page,$.fields.effectiveDate,$.extend.search,null,$.fields.departmentObj.code,true)
          dependantField: $.fields.departmentObj
          _validateFn:
            transform: >-
              $isNilorEmpty($.fields.departmentObj.locationId) and
              $isNilorEmpty($.fields.locationObj) ?
              ($exists($.fields.departmentObj.code) ? ($locationsList(0,
              0,$.fields.effectiveDate,null,$.fields.locationObj.code,null,true)[0]
              ? $locationsList(0,
              0,$.fields.effectiveDate,null,$.fields.locationObj.code,null,true)[0]
              : '_setSelectValueNull'))
          _value:
            transform: >-
              $exists($.fields.departmentObj.code) and
              $exists($.fields.departmentObj.locationId) ? {'label':
              $.fields.departmentObj.locationId.label, 'value':
              $.fields.departmentObj.locationId.value, 'additionalData':
              $.fields.departmentObj.locationObj}
          actions:
            - view
          actionsConfig:
            view:
              formConfig:
                fields:
                  - name: code
                    label: Location Code
                    type: text
                  - type: dateRange
                    label: Effective Date
                    name: effectiveDate1
                    mode: date-picker
                    setting:
                      format: dd/MM/yyyy
                      type: date
                  - name: status
                    label: Status
                    type: radio
                    radio:
                      - label: Active
                        value: true
                      - label: Inactive
                        value: false
                  - name: shortName
                    label: Short Name
                    type: translation
                  - name: longName
                    label: Long Name
                    type: translation
        - type: selectCustom
          label: Location
          name: locationObj
          outputValue: value
          inputValue: code
          placeholder: Select Location
          _condition:
            transform: $.extend.formType = 'view'
          _select:
            transform: >-
              $locationsList($.extend.limit,$.extend.page,$.fields.effectiveDate,null,$.fields.locationObj.value.code)
          _validateFn:
            transform: >-
              $isNilorEmpty($.fields.locationObj.value.code) ?
              ($locationsList(0,
              0,$.fields.effectiveDate,null,$.fields.locationObj.value.code)[0]
              ? $locationsList(0,
              0,$.fields.effectiveDate,null,$.fields.locationObj.value.code)[0]
              : '_setSelectValueNull'))
          actions:
            - view
          actionsConfig:
            view:
              formConfig:
                fields:
                  - name: code
                    label: Location Code
                    type: text
                  - type: dateRange
                    label: Effective Date
                    name: effectiveDate1
                    mode: date-picker
                    setting:
                      format: dd/MM/yyyy
                      type: date
                  - name: status
                    label: Status
                    type: radio
                    radio:
                      - label: Active
                        value: true
                      - label: Inactive
                        value: false
                  - name: shortName
                    label: Short Name
                    type: translation
                  - name: longName
                    label: Long Name
                    type: translation
        - type: selectCustom
          label: Cost Center
          name: costCenterObj
          _condition:
            transform: $not($.extend.formType = 'view')
          isLazyLoad: true
          outputValue: value
          placeholder: Select Cost Center
          dependantField: $.fields.departmentObj
          _select:
            transform: >-
              $not($isNilorEmpty($.fields.departmentObj.code)) ?
              $costCentersList($.extend.limit,$.extend.page,$.fields.effectiveDate,$.extend.search,null,$.fields.departmentObj.code,true)
          _validateFn:
            transform: >-
              $isNilorEmpty($.fields.departmentObj.costCenterId) and
              $isNilorEmpty($.fields.costCenterObj) ?
              ($exists($.variables._costCenterId.id) ?
              ($costCentersList(0,0,$.fields.effectiveDate,
              $.variables._costCenterId.code)[0] ?
              $costCentersList(0,0,$.fields.effectiveDate,
              $.variables._costCenterId.code,null,null,true)[0]) :
              $exists($.fields.costCenterObj.code) ?
              ($costCentersList(0,0,$.fields.effectiveDate,
              $.fields.costCenterObj.code,null,null,true)[0] ?
              $costCentersList(0,0,$.fields.effectiveDate,
              $.fields.costCenterObj.code,null,null,true)[0]) :
              '_setSelectValueNull')
          _value:
            transform: >-
              $exists($.fields.departmentObj.code) and
              $exists($.fields.departmentObj.costCenterId) ? {'label':
              $.fields.departmentObj.costCenterId.label, 'value':
              $.fields.departmentObj.costCenterId.value}
          actions:
            - view
          actionsConfig:
            view:
              formConfig:
                fields:
                  - name: code
                    label: Cost Center Code
                    type: text
                  - type: dateRange
                    label: Effective Date
                    name: effectiveDate
                    mode: date-picker
                    setting:
                      format: dd/MM/yyyy
                      type: date
                  - name: status
                    label: Status
                    type: radio
                    radio:
                      - label: Active
                        value: true
                      - label: Inactive
                        value: false
                  - name: shortName
                    label: Short Name
                    type: translation
                  - name: longName
                    label: Long Name
                    type: translation
        - type: selectCustom
          label: Cost Center
          name: costCenterObj
          _condition:
            transform: $.extend.formType = 'view'
          outputValue: value
          inputValue: code
          placeholder: Select Cost Center
          _select:
            transform: >-
              $costCentersList(0,0,$.fields.effectiveDate,null,$.fields.costCenterObj.code)
          actions:
            - view
          actionsConfig:
            view:
              formConfig:
                fields:
                  - name: code
                    label: Cost Center Code
                    type: text
                  - type: dateRange
                    label: Effective Date
                    name: effectiveDate
                    mode: date-picker
                    setting:
                      format: dd/MM/yyyy
                      type: date
                  - name: status
                    label: Status
                    type: radio
                    radio:
                      - label: Active
                        value: true
                      - label: Inactive
                        value: false
                  - name: shortName
                    label: Short Name
                    type: translation
                  - name: longName
                    label: Long Name
                    type: translation
    - type: group
      label: Reporting Line
      collapse: false
      _disableEventCollapse:
        transform: '$not($.extend.formType = ''view'') ? true : false'
      fields:
        - type: selectCustom
          label: Direct Report
          name: directPositionObj
          placeholder: Select Direct Report
          outputValue: value
          isLazyLoad: true
          _condition:
            transform: $not($.extend.formType = 'view')
          _validateFn:
            transform: >-
              $exists($.value.code) ?
              ($positionsListView(0,0,$.fields.effectiveDate,$.value.code)[0] ?
              $positionsListView(0,0,$.fields.effectiveDate,$.value.code)[0] :
              '_setSelectValueNull')
          _select:
            transform: >-
              $positionsList($.extend.limit,
              $.extend.page,$.fields.effectiveDate,$.extend.defaultValue.id,$.extend.search)
          _disabled:
            transform: '$.fields.directPositionObj_extended ? true : false'
          actions:
            - view
          actionsConfig:
            view:
              formConfig:
                fields:
                  - name: code
                    label: Direct Report Code
                    type: text
                  - type: dateRange
                    label: Effective Date
                    name: effectiveDate
                    mode: date-picker
                    setting:
                      format: dd/MM/yyyy
                      type: date
                  - name: status
                    label: Status
                    type: radio
                    radio:
                      - label: Active
                        value: true
                      - label: Inactive
                        value: false
                  - name: shortName
                    label: Short Name
                    type: translation
                  - name: longName
                    label: Long Name
                    type: translation
        - name: directPositionObj_extended
          description: >-
            khi dung org chart create same level sẽ khởi tạo một form ở màn này,
            dựa vào biến này sẽ disabled fied directPositionObj
          type: text
          unvisible: true
          value: false
        - type: selectCustom
          label: Direct Report
          name: directPositionObj
          placeholder: Select Direct Report
          outputValue: value
          inputValue: code
          _condition:
            transform: $.extend.formType = 'view'
          _select:
            transform: >-
              $positionsListView($.extend.limit,
              $.extend.page,$.fields.effectiveDate,
              $.fields.directPositionObj.value.code)
          actions:
            - view
          actionsConfig:
            view:
              formConfig:
                fields:
                  - name: code
                    label: Direct Report Code
                    type: text
                  - type: dateRange
                    label: Effective Date
                    name: effectiveDate
                    mode: date-picker
                    setting:
                      format: dd/MM/yyyy
                      type: date
                  - name: status
                    label: Status
                    type: radio
                    radio:
                      - label: Active
                        value: true
                      - label: Inactive
                        value: false
                  - name: shortName
                    label: Short Name
                    type: translation
                  - name: longName
                    label: Long Name
                    type: translation
        - type: selectAll
          label: Matrix Report
          name: matrixPositionObj
          placeholder: Select Matrix Report
          outputValue: value
          _validateFn:
            transform: >-
              $exists($.value.code) ?
              ($positionsListViewMulti(0,0,$.fields.effectiveDate,$.value.code)[0]
              ?
              $positionsListViewMulti(0,0,$.fields.effectiveDate,$.value.code)[0]
              : '_setSelectValueNull')
          isLazyLoad: true
          _condition:
            transform: $not($.extend.formType = 'view')
          _options:
            transform: >-
              $positionsList($.extend.limit,
              $.extend.page,$.fields.effectiveDate,$.extend.defaultValue.id,$.extend.search)
          actions:
            - view
          actionsConfig:
            view:
              formConfig:
                fields:
                  - name: code
                    label: Matrix Report Code
                    type: text
                  - type: dateRange
                    label: Effective Date
                    name: effectiveDate
                    mode: date-picker
                    setting:
                      format: dd/MM/yyyy
                      type: date
                  - name: status
                    label: Status
                    type: radio
                    radio:
                      - label: Active
                        value: true
                      - label: Inactive
                        value: false
                  - name: shortName
                    label: Short Name
                    type: translation
                  - name: longName
                    label: Long Name
                    type: translation
        - type: selectCustom
          label: Matrix Report
          name: matrixPositionObj
          placeholder: Select Matrix Report
          outputValue: value
          inputValue: code
          mode: multiple
          _condition:
            transform: $.extend.formType = 'view'
          _validateFn:
            transform: >-
              $exists($.fields.matrixPositionObj.value.code) ?
              ($positionsListViewMulti(0,0,$.fields.effectiveDate,$.fields.matrixPositionObj.value.code)[0]
              ?
              $positionsListViewMulti(0,0,$.fields.effectiveDate,$.fields.matrixPositionObj.value.code)
              : '_setSelectValueNull')
          _select:
            transform: >-
              $positionsListViewMulti($.extend.limit,
              $.extend.page,$.fields.effectiveDate,$.fields.matrixPositionObj.value.code)
          actions:
            - view
          actionsConfig:
            view:
              formConfig:
                fields:
                  - name: code
                    label: Matrix Report Code
                    type: text
                  - type: dateRange
                    label: Effective Date
                    name: effectiveDate
                    mode: date-picker
                    setting:
                      format: dd/MM/yyyy
                      type: date
                  - name: status
                    label: Status
                    type: radio
                    radio:
                      - label: Active
                        value: true
                      - label: Inactive
                        value: false
                  - name: shortName
                    label: Short Name
                    type: translation
                  - name: longName
                    label: Long Name
                    type: translation
  historyHeaderTitle: '''View History Position'''
  sources:
    locationsList:
      uri: '"/api/locations/get-dropdown-list-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'filter':
        [{'field':'search','operator':
        '$eq','value':$.search},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':$.status},{'field':'code','operator': '$eq','value':
        $.codeFilter},{'field':'DepartmentCode','operator': '$eq','value':
        $.departmentCode}]}
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default  & ' (' &
        $item.code & ')', 'value': {'id':$item.id, 'code': $item.code},
        'additionalData': $item, 'departmentId': $item.departmentId}})[]
      disabledCache: true
      params:
        - limit
        - page
        - effectiveDate
        - search
        - codeFilter
        - departmentCode
        - status
    jobcodesList:
      uri: '"/api/job-codes/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'filter':
        [{'field':'search','operator':
        '$eq','value':$.search},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':$.status},{'field':'code','operator': '$eq','value':
        $.codeFilter}]}
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id':$item.id, 'code': $item.code},
        'packkageCodeId': $item.packageCode, 'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - effectiveDate
        - search
        - codeFilter
        - status
    departmentsList:
      uri: '"/api/departments/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'filter':
        [{'field':'search','operator':
        '$eq','value':$.search},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':$.status},{'field':'code','operator':
        '$eq','value':$.code}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value':{'id': $item.id, 'code': $item.code ,
        'locationObj': $item.location ,'locationId':  {'value': {'id':
        $item.location.id, 'code': $item.location.code},
        'label':$exists($item.location) ? ($item.location.name & ' (' &
        $item.location.code & ')')}, 'costCenterId': {'id': $item.costCenter.Id,
        'code': $item.costCenterCode}} , 'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - effectiveDate
        - search
        - code
        - status
    departmentsListbyId:
      uri: '"/api/departments/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'filter':
        [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':$.status},{'field':'IdFilter','operator': '$eq','value':
        $.IdFilter }]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id': $item.id, 'code': $item.code,
        'locationObj': $item.location ,'locationId': {'value': {'id':
        $item.location.id, 'code': $item.location.code},
        'label':$exists($item.location) ? ($item.location.name & ' (' &
        $item.location.code & ')')}, 'costCenterId': {'id': $item.costCenterId,
        'code': $item.costCenterCode}}, 'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - effectiveDate
        - IdFilter
        - status
    costCentersList:
      uri: '"/api/cost-centers/get-dropdown-list-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'filter':
        [{'field':'search','operator':
        '$eq','value':$.search},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':$.status},{'field':'code','operator':
        '$eq','value':$.code},{'field':'DepartmentCode','operator':
        '$eq','value':$.department}]}
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id': $item.id, 'code':
        $item.code},'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - effectiveDate
        - search
        - code
        - department
        - status
    positionsList:
      uri: '"/api/positions/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'filter':
        [{'field':'search','operator':
        '$eq','value':$.search},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true},{'field':'IdFilter','operator':
        '$ne','value':$.code}]}
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id': $item.id, 'code':
        $item.code},'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - effectiveDate
        - code
        - search
    positionsListView:
      uri: '"/api/positions/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'filter':
        [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true}, {'field':'code','operator': '$eq','value':
        $.codeFilter}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id':$item.id,'code':$item.code},
        'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - effectiveDate
        - codeFilter
    positionsListViewMulti:
      uri: '"/api/positions/get-by-multi"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'filter':
        [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true}, {'field':'code','operator': '$in','value':
        $.codeFilter}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id':$item.id,'code':$item.code},
        'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - effectiveDate
        - codeFilter
    positionsListMulti:
      uri: '"/api/positions/by"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true}]}
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.id,'additionalData': $item}})[]
      disabledCache: true
      params:
        - effectiveDate
  variables:
    _selectedDepart:
      transform: >-
        (
        $filter($departmentsListbyId(1,1,$.fields.effectiveDate,$.fields.departmentObj.id)
        , function($v, $i, $a) { $v.value.id = $.fields.departmentObj.id }))
    _locationObj:
      transform: $.variables._selectedDepart.locationObj
    _locationId:
      transform: $.variables._selectedDepart.value.locationId
    _costCenterId:
      transform: $.variables._selectedDepart.value.costCenterId
  _mode:
    transform: $not($.extend.formType = 'view') ? 'mark-scroll'
filter_config:
  fields:
    - name: code
      label: Position Code
      type: text
      labelType: type-grid
      placeholder: Enter Position Code
    - type: radio
      label: Status
      name: status
      labelType: type-grid
      value: ''
      radio:
        - label: All
          value: ''
        - label: Active
          value: true
        - label: Inactive
          value: false
    - name: shortName
      label: Short Name
      labelType: type-grid
      type: text
      placeholder: Enter Short Name
    - name: longName
      labelType: type-grid
      label: Position Title
      type: text
      placeholder: Enter Position Title
    - type: selectAll
      label: Job
      name: jobCodeCode
      labelType: type-grid
      placeholder: Select Job
      isLazyLoad: true
      _options:
        transform: $jobsList($.extend.limit,$.extend.page,$.extend.search)
    - type: selectAll
      label: Department
      labelType: type-grid
      name: departmentCode
      isLazyLoad: true
      placeholder: Select Department
      _options:
        transform: $departmentsList($.extend.limit,$.extend.page,$.extend.search)
  filterMapping:
    - field: code
      operator: $cont
      valueField: code
    - field: status
      operator: $eq
      valueField: status
    - field: na_shortName
      operator: $cont
      valueField: shortName
    - field: na_longName
      operator: $cont
      valueField: longName
    - field: jobCodeCode
      operator: $in
      valueField: jobCodeCode.(value)
    - field: departmentCode
      operator: $in
      valueField: departmentCode.(value)
  sources:
    jobsList:
      uri: '"/api/job-codes/get-by"'
      method: GET
      queryTransform: >-
        {'limit':$.limit,'page':$.page,'filter': [{'field':'search','operator':
        '$eq','value':$.search}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    departmentsList:
      uri: '"/api/departments/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page,'search': $.search, 'filter':
        [{'field':'search','operator': '$eq','value':$.search}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value':$item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
layout_options:
  view_history_after_created: true
  tool_table:
    - id: import
      icon: icon-upload-simple
    - id: export
      icon: icon-download-simple
  view_after_updated: true
  delete_multi_items: true
  hide_action_row: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    title: Edit
    icon: icon-pencil-simple
    type: ghost-gray
  - id: delete
    title: Delete
    icon: icon-trash
    group: null
    type: ghost-gray
backend_url: /api/positions
screen_name: null
layout_options__actions_many: null
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key: null
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Position
  parent:
    title: Job Structure
