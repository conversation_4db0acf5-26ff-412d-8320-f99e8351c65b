id: FO.FS.FR.011
status: draft
sort: 179
user_created: 59722cb8-f858-481b-97f1-c97dd2a8e4be
date_created: '2024-06-14T03:38:23.785Z'
user_updated: 35b4f590-f747-4e53-8fa5-76aba65555c2
date_updated: '2025-05-07T01:29:32.994Z'
title: Group
requirement:
  time: 1743491904985
  blocks:
    - id: gNgJ65JSPJ
      type: paragraph
      data:
        text: '&nbsp;Chức năng cho phép tạo mới/cập nhật thông tin Group'
  version: 2.30.7
screen_design: null
module: FO
local_fields:
  - code: code
    title: Group Code
    data_type:
      key: Increment ID
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    pinned: true
    show_sort: true
  - code: shortName
    title: Short Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: longName
    title: Long Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: status
    title: Status
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: Boolean Tag
      collection: field_types
    show_sort: true
mock_data:
  - code: '00000001'
    group: Technology Corporation
    basicInformation: multi-technology corporation
    effectiveDate: 06/13/2024
    shortName:
      default: FPT
      vietnamese: FPT
      english: FPT
    longName:
      default: FPT Corporation
      vietnamese: FPT Corporation
      english: FPT Corporation
    status: true
local_buttons: null
layout: layout-table
form_config:
  fields:
    - type: group
      n_cols: 2
      fields:
        - name: isNew
          type: radio
          _value:
            transform: '$.extend.formType = ''create'' ? true : false'
          unvisible: true
        - name: code
          label: Group Code
          type: text
          placeholder: Enter Group Code
          validators:
            - type: required
            - type: maxLength
              args: '8'
              text: Maximum 8 characters
            - type: pattern
              args: ^[a-zA-Z0-9_.&]+$
              text: The Code must not contain special characters.
          _condition:
            transform: $not($.extend.formType = 'view')
          _disabled:
            transform: $not($.extend.formType = 'create')
        - type: dateRange
          label: Effective Date
          name: effectiveDate
          mode: date-picker
          placeholder: dd/mm/yyyy
          setting:
            format: dd/MM/yyyy
            type: date
          _condition:
            transform: $not($.extend.formType = 'view')
          _value:
            transform: $.extend.formType = 'create' ? $now()
          validators:
            - type: required
        - name: status
          label: Status
          type: radio
          _value:
            transform: $.extend.formType = 'create' and $not($.extend.isDuplicate) ? true
          _condition:
            transform: $not($.extend.formType = 'view')
          radio:
            - label: Active
              value: true
            - label: Inactive
              value: false
        - name: shortName
          label: Short Name
          type: translation
          _condition:
            transform: $not($.extend.formType = 'view')
          validators:
            - type: maxLength
              args: '40'
              text: Maximum 40 characters
          placeholder: Enter Short Name
        - name: longName
          col: 2
          label: Long Name
          type: translation
          validators:
            - type: required
            - type: maxLength
              args: '120'
              text: Maximum 120 characters
          placeholder: Enter Long Name
          _condition:
            transform: $not($.extend.formType = 'view')
    - name: code
      label: Group Code
      type: text
      _condition:
        transform: $.extend.formType = 'view'
    - type: dateRange
      label: Effective Date
      name: effectiveDate
      mode: date-picker
      scale: 1/2
      placeholder: dd/mm/yyyy
      setting:
        format: dd/MM/yyyy
        type: date
      _condition:
        transform: $.extend.formType = 'view'
    - name: status
      label: Status
      type: radio
      value: true
      _condition:
        transform: $.extend.formType = 'view'
      radio:
        - label: Active
          value: true
        - label: Inactive
          value: false
    - name: shortName
      label: Short Name
      type: translation
      _condition:
        transform: $.extend.formType = 'view'
      placeholder: Enter Short Name
    - name: longName
      label: Long Name
      type: translation
      placeholder: Enter Long Name
      _condition:
        transform: $.extend.formType = 'view'
  historyHeaderTitle: '''View History Group'''
filter_config:
  fields:
    - name: code
      label: Group Code
      placeholder: Enter Group Code
      labelType: type-grid
      type: text
    - name: status
      label: Status
      labelType: type-grid
      type: radio
      value: ''
      radio:
        - label: All
          value: ''
        - label: Active
          value: true
        - label: Inactive
          value: false
    - name: shortName
      label: Short Name
      labelType: type-grid
      placeholder: Enter Short Name
      type: text
    - name: longName
      labelType: type-grid
      label: Long Name
      placeholder: Enter Long Name
      type: text
  filterMapping:
    - field: code
      operator: $cont
      valueField: code
    - field: na_shortName
      operator: $cont
      valueField: shortName
    - field: na_longName
      operator: $cont
      valueField: longName
    - field: status
      operator: $eq
      valueField: status
layout_options:
  toolTable:
    export: true
    import: true
    adjustDisplay: true
  tool_table:
    - id: import
      icon: icon-upload-simple
    - id: export
      icon: icon-download-simple
  view_after_updated: true
  delete_multi_items: true
  custom_create_api:
    url: /api/groups/insert
  view_history_after_created: true
  hide_action_row: true
  custom_history_backend_url: /api/groups/insert
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    title: Edit
    icon: pencil
  - id: delete
    title: Delete
    icon: trash
    group: null
backend_url: /api/groups
screen_name: null
layout_options__actions_many: null
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key: null
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Group
  parent:
    title: Organization Structure
