id: FO.FS.FR.012
status: draft
sort: 138
user_created: 56e71b91-dd39-4ebc-afd1-b4f00f38d4a5
date_created: '2024-06-13T06:26:13.099Z'
user_updated: 35b4f590-f747-4e53-8fa5-76aba65555c2
date_updated: '2025-07-19T02:33:57.217Z'
title: Company
requirement:
  time: 1748839020884
  blocks:
    - id: _SnZn64xZN
      type: paragraph
      data:
        text: '&nbsp; &nbsp;'
  version: 2.30.7
screen_design: null
module: FO
local_fields:
  - code: code
    pinned: true
    title: Company Code
    data_type:
      key: Employee Code
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    show_sort: true
  - code: shortName
    title: Short Name
    data_type:
      key: Organization-1
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: longName
    title: Long Name
    data_type:
      key: Organization-2
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: groupName
    title: Group
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: status
    title: Status
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: Boolean Tag
      collection: field_types
    show_sort: true
mock_data:
  - effectiveDate: 01/05/2024
    code: '00000001'
    shortName:
      default: FPT
      vietnamese: FPT
      english: FPT
    longName:
      default: FPT Corporation
      vietnamese: FPT Corporation
      english: FPT Corporation
    group: FPT
  - effectiveDate: 02/05/2024
    code: '00000002'
    shortName:
      default: FIS
      vietnamese: FIS
      english: FIS
    longName:
      default: FPT IS
      vietnamese: FPT IS
      english: FPT IS
    group: FPT
  - effectiveDate: 03/05/2024
    code: '00000003'
    shortName:
      default: FSOFT
      vietnamese: FSOFT
      english: FSOFT
    longName:
      default: FPT SOFTWARE
      vietnamese: FPT SOFTWARE
      english: FPT SOFTWARE
    group: FPT
local_buttons: null
layout: layout-table
form_config:
  formSize:
    create: large
    edit: large
    proceed: large
  fields:
    - type: group
      label: Basic Information
      collapse: false
      n_cols: 2
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - type: text
          label: Company Code
          name: code
          placeholder: Enter Company Code
          _condition:
            transform: $not($.extend.formType = 'view')
          _disabled:
            transform: $not($.extend.formType = 'create')
          validators:
            - type: required
            - type: maxLength
              args: '8'
              text: Maximum 8 characters
            - type: pattern
              args: ^[a-zA-Z0-9_.&]+$
              text: The Code must not contain special characters.
        - type: dateRange
          label: Effective Date
          name: effectiveDate
          mode: date-picker
          placeholder: Enter Effective Date
          _value:
            transform: >-
              $.extend.formType = 'create' ?
              $not($exists($.fields.effectiveDate)) ?  $now()
          setting:
            format: dd/MM/yyyy
            type: date
          validators:
            - type: required
          _condition:
            transform: >-
              $.extend.formType = 'create' or $.extend.formType = 'edit' or
              $.extend.formType = 'proceed'
        - name: status
          label: Status
          type: radio
          _condition:
            transform: >-
              $.extend.formType = 'create' or $.extend.formType = 'edit' or
              $.extend.formType = 'proceed'
          radio:
            - label: Active
              value: true
            - label: Inactive
              value: false
          _value:
            transform: $.extend.formType = 'create' and $not($.extend.isDuplicate) ? true
        - type: translation
          label: Short Name
          name: shortName
          placeholder: Enter Short Name
          _condition:
            transform: $not($.extend.formType = 'view')
          validators:
            - type: maxLength
              args: '40'
              text: Maximum 40 characters
        - type: translation
          label: Long Name
          name: longName
          col: 2
          placeholder: Enter Long Name
          validators:
            - type: required
            - type: maxLength
              args: '120'
              text: Maximum 120 characters
          _condition:
            transform: $not($.extend.formType = 'view')
        - type: text
          _condition:
            transform: $not($.extend.formType = 'view')
          col: 2
          label: Charter Capital
          name: charterCapital
          validators:
            - type: maxLength
              args: '30'
              text: Maximum 30 characters
          placeholder: Enter Charter Capital
    - type: group
      label: Basic Information
      collapse: false
      n_cols: 1
      _condition:
        transform: $.extend.formType = 'view'
      fieldGroupTitleStyle:
        border: none
      fields:
        - type: text
          label: Company Code
          name: code
          _condition:
            transform: $.extend.formType = 'view'
        - type: dateRange
          label: Effective Date
          name: effectiveDate
          mode: date-picker
          placeholder: Enter Effective Date
          setting:
            format: dd/MM/yyyy
            type: date
          _condition:
            transform: $.extend.formType = 'view'
        - name: status
          label: Status
          type: radio
          _condition:
            transform: $.extend.formType = 'view'
          radio:
            - label: Active
              value: true
            - label: Inactive
              value: false
        - name: shortName
          label: Short Name
          type: translation
          placeholder: Enter Short Name
          _condition:
            transform: $.extend.formType = 'view'
        - type: translation
          label: Long Name
          _condition:
            transform: $.extend.formType = 'view'
          name: longName
          placeholder: Enter Long Name
        - type: text
          _condition:
            transform: $.extend.formType = 'view'
          label: Charter Capital
          name: charterCapital
          placeholder: Enter Charter Capital
    - type: group
      label: Associations
      collapse: false
      _n_cols:
        transform: '$not($.extend.formType = ''view'') ? 2 : 1'
      fields:
        - type: selectCustom
          name: groupObj
          label: Group
          isLazyLoad: true
          placeholder: Select Group
          _validateFn:
            transform: >-
              $exists($.value.code) ?
              ($groupsList(0,0,$.fields.effectiveDate,$.value.code,null,true)[0]
              ?
              $groupsList(0,0,$.fields.effectiveDate,$.value.code,null,true)[0]
              : '_setSelectValueNull')
          _select:
            transform: >-
              $groupsList($.extend.limit,$.extend.page,$.fields.effectiveDate,null,$.extend.search,true)
          outputValue: value
          validators:
            - type: required
          _condition:
            transform: $not($.extend.formType = 'view')
          actions:
            - view
          actionsConfig:
            view:
              formConfig:
                fields:
                  - name: code
                    label: Group Code
                    type: text
                  - type: dateRange
                    label: Effective Date
                    name: effectiveDate
                    mode: date-picker
                    setting:
                      format: dd/MM/yyyy
                      type: date
                  - name: status
                    label: Status
                    type: radio
                    radio:
                      - label: Active
                        value: true
                      - label: Inactive
                        value: false
                  - name: shortName
                    label: Short Name
                    type: translation
                    placeholder: Enter Short Name
                  - name: longName
                    label: Long Name
                    type: translation
        - type: selectCustom
          name: groupObj
          label: Group
          placeholder: Select Group
          _select:
            transform: >-
              $groupsList($.extend.limit,$.extend.page,$.fields.effectiveDate,$.extend.defaultValue.groupObj.code)
          _validateFn:
            transform: >-
              $exists($.extend.defaultValue.groupObj.code) ?
              ($groupsList(0,0,$.fields.effectiveDate,
              $.extend.defaultValue.groupObj.code)[0] ?
              $groupsList(0,0,$.fields.effectiveDate,$.extend.defaultValue.groupObj.code)[0]
              : '_setSelectValueNull')
          _condition:
            transform: $.extend.formType = 'view'
          outputValue: value
          inputValue: code
          actions:
            - view
          actionsConfig:
            view:
              formConfig:
                fields:
                  - name: code
                    label: Group Code
                    type: text
                  - type: dateRange
                    label: Effective Date
                    name: effectiveDate
                    mode: date-picker
                    setting:
                      format: dd/MM/yyyy
                      type: date
                  - name: status
                    label: Status
                    type: radio
                    radio:
                      - label: Active
                        value: true
                      - label: Inactive
                        value: false
                  - name: shortName
                    label: Short Name
                    type: translation
                    placeholder: Enter Short Name
                  - name: longName
                    label: Long Name
                    type: translation
        - type: selectCustom
          label: Location
          name: locationObj
          placeholder: Select Location
          outputValue: value
          inputValue: code
          _condition:
            transform: $.extend.formType = 'view'
          _select:
            transform: >-
              $locationsList($.extend.limit,$.extend.page,$.fields.effectiveDate,null,$.extend.defaultValue.locationObj.value.code)
          actions:
            - view
          actionsConfig:
            view:
              formConfig:
                fields:
                  - name: code
                    label: Location Code
                    type: text
                  - type: dateRange
                    label: Effective Date
                    name: effectiveDate
                    mode: date-picker
                    setting:
                      format: dd/MM/yyyy
                      type: date
                  - name: status
                    label: Status
                    type: radio
                    radio:
                      - label: Active
                        value: true
                      - label: Inactive
                        value: false
                  - name: shortName
                    label: Short Name
                    type: translation
                    placeholder: Enter Short Name
                  - name: longName
                    label: Long Name
                    type: translation
        - type: select
          label: Location
          name: locationObj
          isLazyLoad: true
          placeholder: Select Location
          outputValue: value
          _validateFn:
            transform: >-
              $exists($.value.code) ?
              ($locationsList(0,0,$.fields.effectiveDate,null,$.value.code,null,true)[0]
              ?
              $locationsList(0,0,$.fields.effectiveDate,null,$.value.code,null,true)[0]
              : '_setSelectValueNull')
          _condition:
            transform: $not($.extend.formType = 'view')
          _select:
            transform: >-
              $not($.extend.formType = 'create') ?
              $locationsList($.extend.limit,$.extend.page,$.fields.effectiveDate,$.extend.search,null,$.fields.code,true)
              : []
          actions:
            - view
          actionsConfig:
            view:
              formConfig:
                fields:
                  - name: code
                    label: Location Code
                    type: text
                  - type: dateRange
                    label: Effective Date
                    name: effectiveDate
                    mode: date-picker
                    setting:
                      format: dd/MM/yyyy
                      type: date
                  - name: status
                    label: Status
                    type: radio
                    radio:
                      - label: Active
                        value: true
                      - label: Inactive
                        value: false
                  - name: shortName
                    label: Short Name
                    type: translation
                    placeholder: Enter Short Name
                  - name: longName
                    label: Long Name
                    type: translation
    - type: group
      label: Manager of Company
      collapse: false
      n_cols: 2
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - type: select
          name: managerType
          placeholder: Select Manager Type
          label: Manager Type
          _condition:
            transform: $not($.extend.formType = 'view')
          clearFieldsAfterChange:
            - headOfCompanyObj
            - managerPositionObj
          outputValue: value
          select:
            - label: Employee
              value: Employee
            - label: Position
              value: Position
        - type: select
          name: headOfCompanyObj
          dependantField: $.fields.managerType
          placeholder: Select Head of Company
          label: Head of Company
          isLazyLoad: true
          outputValue: value
          _condition:
            transform: >-
              $not($.extend.formType = 'view') and $.fields.managerType =
              'Employee'
          _validateFn:
            transform: >-
              $exists($.extend.defaultValue.headOfCompanyObj.value.code) ?
              ($personalsListView($.extend.limit,$.extend.page,null,$.fields.effectiveDate,$.extend.defaultValue.headOfCompanyObj.value.code)[0]
              ?
              $personalsListView($.extend.limit,$.extend.page,null,$.fields.effectiveDate,$.extend.defaultValue.headOfCompanyObj.value.code)[0]
              : '_setSelectValueNull')
          _select:
            transform: >-
              $personalsList($.extend.limit,$.extend.page,$.extend.search,$.fields.effectiveDate)
        - type: select
          name: managerPositionObj
          dependantField: $.fields.managerType
          placeholder: Select Manager Position
          label: Manager Position
          outputValue: value
          _condition:
            transform: >-
              $not($.extend.formType = 'view') and $.fields.managerType =
              'Position'
          _validateFn:
            transform: >-
              $exists($.value.code) ? ($positionsList($.fields.effectiveDate,
              $.value.code)[0] ?
              $positionsList($.fields.effectiveDate,$.value.code)[0] :
              '_setSelectValueNull')
          _select:
            transform: $positionsList($.fields.effectiveDate)
    - type: group
      label: Manager of Company
      collapse: false
      n_cols: 1
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - type: select
          name: managerType
          placeholder: Select Manager Type
          label: Manager Type
          _condition:
            transform: $.extend.formType = 'view'
          outputValue: value
          select:
            - label: Employee
              value: Employee
            - label: Position
              value: Position
        - type: select
          name: headOfCompanyObj
          placeholder: Select Head of Company
          label: Head of Company
          outputValue: value
          inputValue: code
          _condition:
            transform: $.fields.managerType = 'Employee' and $.extend.formType = 'view'
          _select:
            transform: >-
              $personalsListView($.extend.limit,$.extend.page,null,$.fields.effectiveDate,$.fields.headOfCompanyObj.value.code)
        - type: selectCustom
          name: managerPositionObj
          placeholder: Select Manager Position
          label: Manager Position
          outputValue: value.id
          inputValue: code
          _condition:
            transform: $.fields.managerType = 'Position' and $.extend.formType = 'view'
          _select:
            transform: $positionsList($.fields.effectiveDate)
          actions:
            - view
          actionsConfig:
            view:
              formConfig:
                fields:
                  - name: code
                    label: Position Code
                    type: text
                  - type: dateRange
                    label: Effective Date
                    name: effectiveDate
                    mode: date-picker
                    setting:
                      format: dd/MM/yyyy
                      type: date
                  - name: status
                    label: Status
                    type: radio
                    radio:
                      - label: Active
                        value: true
                      - label: Inactive
                        value: false
                  - name: shortName
                    label: Short Name
                    type: translation
                    placeholder: Enter Short Name
                  - name: longName
                    label: Long Name
                    type: translation
    - type: group
      label: Former Organization
      collapse: false
      _condition:
        transform: $not($.extend.formType = 'view')
      disableEventCollapse: false
      fields:
        - type: array
          mode: table
          name: orgObjects
          minSize: 1
          arrayOptions:
            canChangeSize: true
          field:
            type: group
            name: group
            fields:
              - type: select
                name: type
                placeholder: Org Object
                label: Former Organization
                outputValue: value
                width: 250px
                select:
                  - label: Group
                    value: '1'
                  - label: Company
                    value: '2'
                  - label: Legal Entity
                    value: '3'
                  - label: Business Unit
                    value: '4'
                  - label: Division
                    value: '5'
                  - label: Department
                    value: '6'
                clearFieldsAfterChange:
                  - id
                  - objData
                  - code
              - type: selectCustom
                name: objData
                placeholder: Select Org Object
                isLazyLoad: true
                outputValue: value
                width: 300px
                validators:
                  - id: check_duplicate
                    type: ppx-custom
                    args:
                      transform: >-
                        $not($isNilorEmpty($.value)) ?
                        $count($map($.fields.orgObjects, function($item, $index)
                        {($item.id = $.value and $item.type =
                        $.fields.orgObjects[$index].type) ? {}})) > 1
                    text: Former organiztion has been duplicated
                  - id: check_null
                    type: ppx-custom
                    args:
                      transform: >-
                        $not($isNilorEmpty($getFieldGroup($.extend.path,$.fields,1).type))
                        and $isNilorEmpty($.value)
                    text: Cannot be empty
                _select:
                  dependants:
                    - $.fields.orgObjects[$index].id
                    - $.fields.orgObjects[$index].type
                    - $.extend.page
                    - $.extend.search
                  params:
                    $index: $.extend.path[-1]
                  transform: >-
                    $getFieldGroup($.extend.path,$.fields,1).type = '1' ?
                    $groupsList($.extend.limit,$.extend.page,$.fields.effectiveDate,null,$.extend.search)
                    : $getFieldGroup($.extend.path,$.fields,1).type = '2' ?
                    $companyList($.extend.limit,$.extend.page,$.fields.effectiveDate,null,null,$.extend.search)
                    : $getFieldGroup($.extend.path,$.fields,1).type = '3' ?
                    $legalEntitiesList($.extend.limit,$.extend.page,$.fields.effectiveDate,null,$.extend.search)
                    : $getFieldGroup($.extend.path,$.fields,1).type = '4' ?
                    $businessUnitListLazy($.extend.limit,$.extend.page,$.fields.effectiveDate,$.extend.search)
                    : $getFieldGroup($.extend.path,$.fields,1).type = '5' ?
                    $divisionsList($.extend.limit,$.extend.page,$.fields.effectiveDate,null,$.extend.search)
                    : $getFieldGroup($.extend.path,$.fields,1).type = '6' ?
                    $departmentsList($.extend.limit,$.extend.page,$.fields.effectiveDate,null,$.extend.search)
                    : null
                actions:
                  - view
                actionsConfig:
                  view:
                    formConfig:
                      fields:
                        - name: code
                          label: Code
                          type: text
                        - type: dateRange
                          label: Effective Date
                          name: effectiveDate
                          mode: date-picker
                          setting:
                            format: dd/MM/yyyy
                            type: date
                        - name: status
                          label: Status
                          type: radio
                          radio:
                            - label: Active
                              value: true
                            - label: Inactive
                              value: false
                        - name: shortName
                          label: Short Name
                          type: translation
                          placeholder: Enter Short Name
                        - name: longName
                          label: Long Name
                          type: translation
              - type: text
                name: id
                unvisible: true
                _value:
                  transform: $getFieldGroup($.extend.path,$.fields,1).objData.id
              - type: text
                name: code
                unvisible: true
                _value:
                  transform: $getFieldGroup($.extend.path,$.fields,1).objData.code
    - type: group
      collapse: false
      label: Former Organization
      _condition:
        transform: $.extend.formType = 'view'
      disableEventCollapse: false
      fields:
        - type: array
          mode: table
          name: orgObjects
          arrayOptions:
            canChangeSize: true
          field:
            type: group
            name: group
            fields:
              - type: select
                name: type
                placeholder: Org Object
                label: Former Organization
                outputValue: value
                width: 192px
                select:
                  - label: Group
                    value: '1'
                  - label: Company
                    value: '2'
                  - label: Legal Entity
                    value: '3'
                  - label: Business Unit
                    value: '4'
                  - label: Division
                    value: '5'
                  - label: Department
                    value: '6'
                clearFieldsAfterChange:
                  - id
                  - code
              - type: selectCustom
                name: id
                placeholder: Select Org Object
                outputValue: value
                _select:
                  dependants:
                    - $.fields.orgObjects[$index].type
                  params:
                    $index: $.extend.path[-1]
                  transform: >-
                    $getFieldGroup($.extend.path,$.fields,1).type = '1' ?
                    $groupsList($.extend.limit,$.extend.page,$.fields.effectiveDate,$getFieldGroup($.extend.path,$.fields,1).code)
                    : $getFieldGroup($.extend.path,$.fields,1).type = '2' ?
                    $companyList($.extend.limit,$.extend.page,$.fields.effectiveDate,$getFieldGroup($.extend.path,$.fields,1).code)
                    : $getFieldGroup($.extend.path,$.fields,1).type = '3' ?
                    $legalEntitiesList($.extend.limit,$.extend.page,$.fields.effectiveDate,$getFieldGroup($.extend.path,$.fields,1).code)
                    : $getFieldGroup($.extend.path,$.fields,1).type = '4' ?
                    $businessUnitListViewLazy($.extend.limit,$.extend.page,$.fields.effectiveDate,$getFieldGroup($.extend.path,$.fields,1).code)
                    : $getFieldGroup($.extend.path,$.fields,1).type = '5' ?
                    $divisionsList($.extend.limit,$.extend.page,$.fields.effectiveDate,$getFieldGroup($.extend.path,$.fields,1).code)
                    : $getFieldGroup($.extend.path,$.fields,1).type = '6' ?
                    $departmentsList($.extend.limit,$.extend.page,$.fields.effectiveDate,$getFieldGroup($.extend.path,$.fields,1).code)
                    : '_setSelectValueNull'
                actions:
                  - view
                actionsConfig:
                  view:
                    formConfig:
                      fields:
                        - name: code
                          label: Code
                          type: text
                        - type: dateRange
                          label: Effective Date
                          name: effectiveDate
                          mode: date-picker
                          setting:
                            format: dd/MM/yyyy
                            type: date
                        - name: status
                          label: Status
                          type: radio
                          radio:
                            - label: Active
                              value: true
                            - label: Inactive
                              value: false
                        - name: shortName
                          label: Short Name
                          type: translation
                          placeholder: Enter Short Name
                        - name: longName
                          label: Long Name
                          type: translation
              - type: text
                name: code
                unvisible: true
    - type: group
      label: Decision Information
      collapse: false
      n_cols: 2
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - type: select
          name: action
          label: Action
          placeholder: Select Action
          outputValue: value
          _select:
            transform: >-
              $.extend.formType = 'create' ? $actionsList('ACTIONORG_001') :
              $actionsList()
          _condition:
            transform: $not($.extend.formType = 'view')
        - type: select
          name: reason
          label: Reason
          dependantField: $.fields.action
          dependantFieldSkip: 2
          _select:
            transform: ' $.fields.action ? $.variables._reasonList'
          outputValue: value
          placeholder: Select Reason
          _condition:
            transform: $not($.extend.formType = 'view')
        - type: text
          label: Decision No.
          name: decisionNo
          _condition:
            transform: $not($.extend.formType = 'view')
          validators:
            - type: maxLength
              args: '40'
              text: Maximum 40 characters
          placeholder: Enter Dicision No.
        - type: text
          label: Decision Name
          name: decisionName
          _condition:
            transform: $not($.extend.formType = 'view')
          validators:
            - type: maxLength
              args: '200'
              text: Maximum 200 characters
          placeholder: Enter Decision Name
        - type: dateRange
          label: Issue Date
          name: issueDate
          mode: date-picker
          _condition:
            transform: $not($.extend.formType = 'view')
          placeholder: dd/mm/yyyy
          setting:
            format: dd/MM/yyyy
            type: date
        - type: select
          name: authorityForApproval
          label: Approved By
          placeholder: Select Approved By
          outputValue: value
          _select:
            transform: $authoritiesList()
          _condition:
            transform: $not($.extend.formType = 'view')
        - type: text
          label: Signatory
          name: signatory
          col: 2
          validators:
            - type: maxLength
              args: '120'
              text: Maximum 120 characters
          placeholder: Enter Signatory
          _condition:
            transform: $not($.extend.formType = 'view')
        - type: upload
          label: Attachment
          name: attachFile
          col: 2
          upload:
            accept:
              - >-
                application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
              - application/pdf
              - >-
                application/vnd.openxmlformats-officedocument.wordprocessingml.document
            size: 100
          _condition:
            transform: $not($.extend.formType = 'view')
        - type: upload
          name: attachFileResults
          col: 2
          readOnly: true
          canAction: true
          hiddenLabel: true
          _condition:
            transform: $.extend.formType = 'edit'
    - type: group
      label: Decision Information
      collapse: false
      n_cols: 1
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - type: select
          name: action
          label: Action
          placeholder: Select Action
          outputValue: value
          _select:
            transform: $actionsList()
          _condition:
            transform: $.extend.formType = 'view'
        - type: select
          name: reason
          label: Reason
          _select:
            transform: $reasonsList()
          outputValue: value
          placeholder: Select Reason
          _condition:
            transform: $.extend.formType = 'view'
        - type: text
          label: Decision No.
          name: decisionNo
          _condition:
            transform: '$.extend.formType = ''view'' '
          placeholder: Enter Dicision No.
        - type: text
          label: Decision Name
          name: decisionName
          _condition:
            transform: '$.extend.formType = ''view'' '
          placeholder: Enter Decision Name
        - type: dateRange
          label: Issue Date
          name: issueDate
          mode: date-picker
          _condition:
            transform: '$.extend.formType = ''view'' '
          placeholder: dd/mm/yyyy
          setting:
            format: dd/MM/yyyy
            type: date
        - type: select
          name: authorityForApproval
          label: Approved By
          placeholder: Select Approved By
          outputValue: value
          _select:
            transform: $authoritiesList()
          _condition:
            transform: $.extend.formType = 'view'
        - type: text
          label: Signatory
          name: signatory
          placeholder: Enter Signatory
          _condition:
            transform: $.extend.formType = 'view'
        - type: upload
          label: Attachment
          name: attachFileResults
          readOnly: true
          _condition:
            transform: $.extend.formType = 'view'
  historyHeaderTitle: '''View History Company'''
  sources:
    locationsList:
      uri: '"/api/locations/get-dropdown-list-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'filter':
        [{'field':'search','operator':
        '$eq','value':$.search},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':$.status},{'field':'code','operator': '$eq','value':
        $.codeFilter},{'field':'CompanyCode','operator': '$eq','value':
        $.companyCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id': $item.id, 'code': $item.code},
        'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - effectiveDate
        - search
        - codeFilter
        - companyCode
        - status
    groupsList:
      uri: '"/api/groups/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page ,'filter':
        [{'field':'search','operator':
        '$eq','value':$.search},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'code','operator':
        '$eq','value':$.code},{'field':'status','operator':
        '$eq','value':$.status}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id': $item.id, 'code': $item.code},
        'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - effectiveDate
        - code
        - search
        - status
    personalsList:
      uri: '"/api/personals/all-employees-job-datas"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page,'filter':
        [{'field':'search','operator': '$eq','value':$.paramsName},
        {'field':'effectiveDate','operator': '$eq','value':$.effectiveDate},
        {'field':'hrStatus','operator': '$eq','value': 'A'}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.fullName  & ' ' &
        $item.emailsplit, 'value': {'id': $item.employeeId, 'code':
        $item.employeeId}}})[]
      disabledCache: true
      params:
        - limit
        - page
        - paramsName
        - effectiveDate
        - code
    personalsListView:
      uri: '"/api/personals/all-employees-job-datas"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page,'filter':
        [{'field':'search','operator':
        '$eq','value':$.paramsName},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}, {'field':'hrStatus','operator':
        '$eq','value': 'A'}, {'field':'employeeId','operator':
        '$in','value':$.code}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.fullName  & ' ' &
        $item.emailsplit, 'value': {'id': $item.employeeId, 'code':
        $item.employeeId}}})[]
      disabledCache: true
      params:
        - limit
        - page
        - paramsName
        - effectiveDate
        - code
    legalEntitiesList:
      uri: '"/api/legal-entities/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page ,'filter':
        [{'field':'search','operator':
        '$eq','value':$.search},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'code','operator':
        '$eq','value':$.code}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id': $item.id, 'code':
        $item.code},'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - effectiveDate
        - code
        - search
    departmentsList:
      uri: '"/api/departments/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'filter':
        [{'field':'search','operator':
        '$eq','value':$.search},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'code','operator':
        '$eq','value':$.code}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id': $item.id, 'code': $item.code} ,
        'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - effectiveDate
        - code
        - search
    divisionsList:
      uri: '"/api/divisions/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page ,'filter':
        [{'field':'search','operator':
        '$eq','value':$.search},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'code','operator':
        '$eq','value':$.code}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id': $item.id, 'code':
        $item.code},'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - effectiveDate
        - code
        - search
    companyList:
      uri: '"/api/companies/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page ,'filter':
        [{'field':'search','operator':
        '$eq','value':$.search},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':$.status},{'field':'code','operator':
        '$eq','value':$.code}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id': $item.id, 'code': $item.code},
        'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - effectiveDate
        - code
        - status
        - search
    businessUnitListLazy:
      uri: '"/api/business-units/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'filter':
        [{'field':'search','operator':
        '$eq','value':$.search},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':$.status}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id': $item.id, 'code': $item.code},
        'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - effectiveDate
        - search
        - status
    businessUnitListViewLazy:
      uri: '"/api/business-units/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'filter':
        [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':$.status}, {'field':'code','operator': '$eq','value':
        $.codeFilter}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id':$item.id,'code':$item.code},
        'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - effectiveDate
        - codeFilter
        - status
    authoritiesList:
      uri: '"/api/picklists/CAPQDORG/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
    actionsList:
      uri: '"/api/picklists-values/ACTIONORG"'
      method: GET
      queryTransform: '{''filter'': [{''field'':''code'',''operator'': ''$eq'',''value'':$.codeAction}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code, 'id':$item.id }})[]
      disabledCache: true
      params:
        - codeAction
    reasonsList:
      uri: '"/api/picklists/REASONORG/values"'
      method: GET
      queryTransform: '{''filter'': [{''field'':''col501'',''operator'': ''$eq'',''value'':$.codeAction}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - codeAction
    positionsList:
      uri: '"/api/positions/by"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true},{'field':'code','operator': '$eq','value':$.code}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item, 'additionalData': $item}})[]
      disabledCache: true
      params:
        - effectiveDate
        - code
  variables:
    _selectAction:
      transform: $filter($actionsList(),function ($v){ $v.value = $.fields.action })
    _actionId:
      transform: $.variables._selectAction.id
    _reasonList:
      transform: $reasonsList($.variables._actionId)
  _mode:
    transform: $not($.extend.formType = 'view') ? 'mark-scroll'
filter_config:
  fields:
    - type: text
      label: Company Code
      name: code
      labelType: type-grid
      placeholder: Enter Company Code
    - type: radio
      label: Status
      labelType: type-grid
      name: status
      value: ''
      radio:
        - value: ''
          label: All
        - value: true
          label: Active
        - value: false
          label: Inactive
    - type: text
      label: Short Name
      labelType: type-grid
      name: shortName
      placeholder: Enter Short Name
    - type: text
      label: Long Name
      labelType: type-grid
      name: longName
      placeholder: Enter Long Name
    - type: selectAll
      name: groupCode
      isLazyLoad: true
      label: Group
      labelType: type-grid
      placeholder: Select Group
      _options:
        transform: $groupsList($.extend.limit,$.extend.page,$.extend.search)
  filterMapping:
    - field: code
      operator: $cont
      valueField: code
    - field: na_longName
      operator: $cont
      valueField: longName
    - field: na_shortName
      operator: $cont
      valueField: shortName
    - field: status
      operator: $eq
      valueField: status
    - field: groupCode
      operator: $in
      valueField: groupCode.(value)
  sources:
    groupsList:
      uri: '"/api/groups/get-by"'
      method: GET
      queryTransform: >-
        {'limit':$.limit,'page':$.page,'filter': [{'field':'search','operator':
        '$eq','value':$.search}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
layout_options:
  toolTable:
    export: true
    import: true
    adjustDisplay: true
  tool_table:
    - id: import
      icon: icon-upload-simple
    - id: export
      icon: icon-download-simple
  is_upload_file: true
  delete_multi_items: true
  view_after_updated: true
  view_history_after_created: true
  hide_action_row: true
  custom_history_backend_url: /api/companies/insert-new-record
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    title: Edit
    icon: icon-pencil-simple
    type: ghost-gray
  - id: delete
    title: Delete
    icon: icon-trash
    group: null
    type: ghost-gray
backend_url: /api/companies
screen_name: null
layout_options__actions_many: null
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key: null
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Company
  parent:
    title: Organization Structure
