id: FO.FS.FR.013
status: draft
sort: 166
user_created: 56e71b91-dd39-4ebc-afd1-b4f00f38d4a5
date_created: '2024-06-13T06:24:10.834Z'
user_updated: 35b4f590-f747-4e53-8fa5-76aba65555c2
date_updated: '2025-07-19T02:27:56.869Z'
title: Legal Entity
requirement:
  time: 1750083322010
  blocks:
    - id: '-U7ysUNE6F'
      type: paragraph
      data:
        text: '&nbsp;'
  version: 2.29.1
screen_design: null
module: FO
local_fields:
  - code: code
    pinned: true
    title: Legal Entity Code
    data_type:
      key: Increment ID
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    show_sort: true
  - code: shortName
    title: Short Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: longName
    title: Long Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: companyName
    title: Company
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: parentName
    title: Parent Legal Entity
    description: Parent LegalEntity
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: status
    title: Status
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: Boolean Tag
      collection: field_types
    show_sort: true
mock_data:
  - code: '00000001'
    shortName:
      default: FIS HN
      vietnamese: FIS HN
      english: FIS HN
    effectiveDate: 06/22/2024
    longName:
      default: FPT IS Hà Nội
      vietnamese: FPT IS Hà Nội
      english: FPT IS Hà Nội
    status: true
    company: FIS
  - code: '00000002'
    shortName:
      default: FIS HCM
      vietnamese: FIS HCM
      english: FIS HCM
    longName:
      default: CN CtyTNHH HT T.tin FPT - HCM
      vietnamese: CN CtyTNHH HT T.tin FPT - HCM
      english: CN CtyTNHH HT T.tin FPT - HCM
    effectiveDate: 06/21/2024
    status: true
    company: FIS
  - code: '00000003'
    shortName:
      default: FIS CAM
      vietnamese: FIS CAM
      english: FIS CAM
    longName:
      default: Cty TNHH HTTT FPT Cambodia
      vietnamese: Cty TNHH HTTT FPT Cambodia
      english: Cty TNHH HTTT FPT Cambodia
    status: true
    company: FIS
  - code: '00000004'
    shortName:
      default: FIS MYA
      vietnamese: FIS MYA
      english: FIS MYA
    longName:
      default: VPĐD FIS tại Myanmar
      vietnamese: VPĐD FIS tại Myanmar
      english: VPĐD FIS tại Myanmar
    effectiveDate: 06/24/2024
    status: true
    company: FIS
  - code: '00000005'
    shortName:
      default: UTOP
      vietnamese: UTOP
      english: UTOP
    longName:
      default: Công ty Cổ phần Công nghệ UTOP
      vietnamese: Công ty Cổ phần Công nghệ UTOP
      english: Công ty Cổ phần Công nghệ UTOP
    status: true
    company: FIS
  - code: '00000006'
    shortName:
      default: FSEMI
      vietnamese: FSEMI
      english: FSEMI
    longName:
      default: Công ty Cổ phần Bán dẫn FPT
      vietnamese: Công ty Cổ phần Bán dẫn FPT
      english: Công ty Cổ phần Bán dẫn FPT
    status: true
    company: FIS
  - code: '00000007'
    shortName:
      default: TECHUP
      vietnamese: TECHUP
      english: TECHUP
    longName:
      default: CTCP Truyền thông TECHUP
      vietnamese: CTCP Truyền thông TECHUP
      english: CTCP Truyền thông TECHUP
    status: true
    company: FIS
  - code: '00000008'
    shortName:
      default: FSO HN
      vietnamese: FSO HN
      english: FSO HN
    longName:
      default: Cty TNHH Phần mềm FPT
      vietnamese: Cty TNHH Phần mềm FPT
      english: Cty TNHH Phần mềm FPT
    status: true
    company: FSOFT
  - code: '00000009'
    shortName:
      default: FSO HL
      vietnamese: FSO HL
      english: FSO HL
    longName:
      default: Cty TNHH Phần mềm FPT Hà Nội
      vietnamese: Cty TNHH Phần mềm FPT Hà Nội
      english: Cty TNHH Phần mềm FPT Hà Nội
    status: true
    company: FSOFT
  - code: '00000010'
    shortName:
      default: FSO DN
      vietnamese: FSO DN
      english: FSO DN
    longName:
      default: Cty TNHH Phần mềm FPT CN ĐN
      vietnamese: Cty TNHH Phần mềm FPT CN ĐN
      english: Cty TNHH Phần mềm FPT CN ĐN
    status: true
    company: FSOFT
local_buttons: null
layout: layout-table
form_config:
  formSize:
    create: large
    edit: large
    proceed: large
  fields:
    - type: group
      label: Basic Information
      collapse: false
      n_cols: 2
      _condition:
        transform: $not($.extend.formType = 'view')
      fieldGroupTitleStyle:
        border: none
      fields:
        - type: text
          label: Legal Entity Code
          name: code
          placeholder: Enter Legal Entity Code
          _disabled:
            transform: $not($.extend.formType = 'create')
          validators:
            - type: required
            - type: maxLength
              args: '20'
              text: Maximum 20 characters
            - type: pattern
              args: ^[a-zA-Z0-9_.&]+$
              text: The Code must not contain special characters.
          _condition:
            transform: $not($.extend.formType = 'view')
        - type: dateRange
          label: Effective Date
          name: effectiveDate
          mode: date-picker
          placeholder: dd/mm/yyyy
          _condition:
            transform: $not($.extend.formType = 'view')
          _value:
            transform: >-
              $.extend.formType = 'create' ?
              $not($exists($.fields.effectiveDate)) ?  $now()
          validators:
            - type: required
          setting:
            format: dd/MM/yyyy
            type: date
        - type: radio
          label: Status
          name: status
          _value:
            transform: $.extend.formType = 'create' and $not($.extend.isDuplicate) ? true
          _condition:
            transform: $not($.extend.formType = 'view')
          radio:
            - value: true
              label: Active
            - value: false
              label: Inactive
        - type: translation
          label: Short Name
          name: shortName
          _condition:
            transform: $not($.extend.formType = 'view')
          validators:
            - type: maxLength
              args: '40'
              text: Maximum 40 characters
          placeholder: Enter Short Name
        - type: translation
          label: Long Name
          name: longName
          col: 2
          placeholder: Enter Long Name
          _condition:
            transform: $not($.extend.formType = 'view')
          validators:
            - type: required
            - type: maxLength
              args: '120'
              text: Maximum 120 characters
        - type: text
          label: Charter Capital
          name: charterCapital
          placeholder: Enter Charter Capital
          col: 2
          _condition:
            transform: $not($.extend.formType = 'view')
          validators:
            - type: maxLength
              args: '30'
              text: Maximum 30 characters
    - type: group
      label: Basic Information
      collapse: false
      n_cols: 1
      _condition:
        transform: $.extend.formType = 'view'
      fieldGroupTitleStyle:
        border: none
      fields:
        - type: text
          label: Legal Entity Code
          name: code
          _template:
            transform: $.fields.code
          _condition:
            transform: $.extend.formType = 'view'
        - type: dateRange
          label: Effective Date
          name: effectiveDate
          mode: date-picker
          _condition:
            transform: $.extend.formType = 'view'
          setting:
            format: dd/MM/yyyy
            type: date
        - type: radio
          label: Status
          name: status
          _condition:
            transform: $.extend.formType = 'view'
          radio:
            - value: true
              label: Active
            - value: false
              label: Inactive
        - type: translation
          label: Short Name
          name: shortName
          placeholder: Enter Short Name
          _condition:
            transform: $.extend.formType = 'view'
        - type: translation
          label: Long Name
          name: longName
          _condition:
            transform: $.extend.formType = 'view'
        - type: text
          label: Charter Capital
          name: charterCapital
          _template:
            transform: $.fields.charterCapital
          _condition:
            transform: $.extend.formType = 'view'
    - type: group
      label: Associations
      collapse: false
      n_cols: 2
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - type: select
          name: AssociationsType1
          label: Legal Association Type
          placeholder: Select Legal Association Type
          outputValue: value
          _condition:
            transform: $not($.extend.formType = 'view')
          _value:
            transform: $isNilorEmpty($.extend.defaultValue.parentLegalEntitys.value.code)
          select:
            - label: Company
              value: true
            - label: Parent Legal Entity
              value: false
          validators:
            - type: required
        - type: selectCustom
          name: companyObj
          label: Company
          isLazyLoad: true
          outputValue: value
          clearFieldsAfterChange:
            - locationObj
          _condition:
            transform: >-
              $not($.extend.formType = 'view') and $.fields.AssociationsType1 =
              true
          _class:
            transform: '$boolean($.fields.parentLegalEntitys) ? ''unrequired'': ''required'''
          dependantField: $.fields.AssociationsType1
          placeholder: Select Company
          _validateFn:
            transform: >-
              $exists($.value.code) ?
              ($companyList(0,0,$.fields.effectiveDate,null,$.value.code,($not($.extend.formType
              = 'edit') or $not($DateFormat($.fields.effectiveDate,
              'yyyy-MM-DD') = $DateFormat($.extend.defaultValue.effectiveDate,
              'yyyy-MM-DD'))) ? true)[0] ?
              $companyList(0,0,$.fields.effectiveDate,null,$.value.code,($not($.extend.formType
              = 'edit') or $not($DateFormat($.fields.effectiveDate,
              'yyyy-MM-DD') = $DateFormat($.extend.defaultValue.effectiveDate,
              'yyyy-MM-DD'))) ? true)[0] : '_setSelectValueNull')
          _select:
            transform: >-
              $companyList($.extend.limit,$.extend.page,$.fields.effectiveDate,
              $.extend.search, null, true)
          actions:
            - view
          actionsConfig:
            view:
              formConfig:
                fields:
                  - name: code
                    label: Company Code
                    type: text
                  - type: dateRange
                    label: Effective Date
                    name: effectiveDate
                    mode: date-picker
                    setting:
                      format: dd/MM/yyyy
                      type: date
                  - name: status
                    label: Status
                    type: radio
                    radio:
                      - label: Active
                        value: true
                      - label: Inactive
                        value: false
                  - name: shortName
                    label: Short Name
                    type: translation
                  - name: longName
                    label: Long Name
                    type: translation
        - type: selectCustom
          name: parentLegalEntitys
          label: Parent Legal Entity
          isLazyLoad: true
          _condition:
            transform: >-
              $not($.extend.formType = 'view') and $.fields.AssociationsType1 =
              false
          dependantField: $.fields.AssociationsType1
          placeholder: Select Parent Legal Entity
          outputValue: value
          clearFieldsAfterChange:
            - locationObj
          _class:
            transform: >-
              $boolean($.fields.companyObj) or
              $not($isNilorEmpty($.fields.companyObj)) ? 'unrequired':
              'required'
          _select:
            transform: >-
              $legalEntitiesListLazy($.extend.limit,
              $.extend.page,$.fields.effectiveDate,$.extend.search,$.extend.defaultValue.id,true)
          _validateFn:
            transform: >-
              $exists($.value.code) ?
              ($legalEntitiesListViewLazy(0,0,$.fields.effectiveDate,$.value.code,($not($.extend.formType
              = 'edit') or $not($DateFormat($.fields.effectiveDate,
              'yyyy-MM-DD') = $DateFormat($.extend.defaultValue.effectiveDate,
              'yyyy-MM-DD'))) ? true)[0] ?
              $legalEntitiesListViewLazy(0,0,$.fields.effectiveDate,$.value.code,($not($.extend.formType
              = 'edit') or $not($DateFormat($.fields.effectiveDate,
              'yyyy-MM-DD') = $DateFormat($.extend.defaultValue.effectiveDate,
              'yyyy-MM-DD'))) ? true)[0] : '_setSelectValueNull')
          actions:
            - view
          actionsConfig:
            view:
              formConfig:
                fields:
                  - name: code
                    label: Legal Entity Code
                    type: text
                  - type: dateRange
                    label: Effective Date
                    name: effectiveDate
                    mode: date-picker
                    setting:
                      format: dd/MM/yyyy
                      type: date
                  - name: status
                    label: Status
                    type: radio
                    radio:
                      - label: Active
                        value: true
                      - label: Inactive
                        value: false
                  - name: shortName
                    label: Short Name
                    type: translation
                  - name: longName
                    label: Long Name
                    type: translation
        - type: selectCustom
          label: Location
          name: locationObj
          isLazyLoad: true
          placeholder: Select Location
          outputValue: value
          col: 2
          _validateFn:
            transform: >-
              $exists($.value.code) ?
              ($locationsList(0,0,$.fields.effectiveDate,null,$.value.code,null,null,true)[0]
              ?
              $locationsList(0,0,$.fields.effectiveDate,null,$.value.code,null,null,true)[0]
              : '_setSelectValueNull')
          _condition:
            transform: $not($.extend.formType = 'view')
          _select:
            transform: >-
              ($not($isNilorEmpty($.fields.companyObj)) or
              $not($isNilorEmpty($.fields.parentLegalEntitys))) ?
              $locationsList($.extend.limit,$.extend.page,$.fields.effectiveDate,$.extend.search,null,$.fields.companyObj.code,$.fields.parentLegalEntitys.code,true)
              : '_setSelectValueNull'
          actions:
            - view
          actionsConfig:
            view:
              formConfig:
                fields:
                  - name: code
                    label: Location Code
                    type: text
                  - type: dateRange
                    label: Effective Date
                    name: effectiveDate
                    mode: date-picker
                    setting:
                      format: dd/MM/yyyy
                      type: date
                  - name: status
                    label: Status
                    type: radio
                    radio:
                      - label: Active
                        value: true
                      - label: Inactive
                        value: false
                  - name: shortName
                    label: Short Name
                    type: translation
                  - name: longName
                    label: Long Name
                    type: translation
    - type: group
      label: Associations
      collapse: false
      n_cols: 1
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - type: selectCustom
          name: companyObj
          label: Company
          outputValue: value
          inputValue: code
          _condition:
            transform: $.extend.formType = 'view'
          placeholder: Select Company
          _select:
            transform: >-
              $companyList($.extend.limit,$.extend.page,$.fields.effectiveDate,null,$.extend.defaultValue.companyObj.value.code)
          _validateFn:
            transform: >-
              $exists($test($.extend.defaultValue.companyObj.value.code)) ?
              $companyList(0,0,$.fields.effectiveDate,null,$.extend.defaultValue.companyObj.value.code)[0]
              ?
              $companyList(0,0,$.fields.effectiveDate,null,$.extend.defaultValue.companyObj.value.code)[0]
              : '_setSelectValueNull'
          actions:
            - view
          actionsConfig:
            view:
              formConfig:
                fields:
                  - name: code
                    label: Company Code
                    type: text
                  - type: dateRange
                    label: Effective Date
                    name: effectiveDate
                    mode: date-picker
                    setting:
                      format: dd/MM/yyyy
                      type: date
                  - name: status
                    label: Status
                    type: radio
                    radio:
                      - label: Active
                        value: true
                      - label: Inactive
                        value: false
                  - name: shortName
                    label: Short Name
                    type: translation
                  - name: longName
                    label: Long Name
                    type: translation
        - type: selectCustom
          name: parentLegalEntitys
          label: Parent Legal Entity
          _condition:
            transform: $.extend.formType = 'view'
          placeholder: Select Parent Legal Entity
          outputValue: value
          inputValue: code
          _select:
            transform: >-
              $legalEntitiesListViewLazy($.extend.limit,
              $.extend.page,$.fields.effectiveDate,
              $.fields.parentLegalEntitys.value.code)
          actions:
            - view
          actionsConfig:
            view:
              formConfig:
                fields:
                  - name: code
                    label: Legal Entity Code
                    type: text
                  - type: dateRange
                    label: Effective Date
                    name: effectiveDate
                    mode: date-picker
                    setting:
                      format: dd/MM/yyyy
                      type: date
                  - name: status
                    label: Status
                    type: radio
                    radio:
                      - label: Active
                        value: true
                      - label: Inactive
                        value: false
                  - name: shortName
                    label: Short Name
                    type: translation
                  - name: longName
                    label: Long Name
                    type: translation
        - type: selectCustom
          label: Location
          name: locationObj
          placeholder: Select Location
          outputValue: value
          inputValue: code
          _condition:
            transform: $.extend.formType = 'view'
          _select:
            transform: >-
              $locationsList($.extend.limit,$.extend.page,$.fields.effectiveDate,null,$.extend.defaultValue.locationObj.value.code)
          actions:
            - view
          actionsConfig:
            view:
              formConfig:
                fields:
                  - name: code
                    label: Location Code
                    type: text
                  - type: dateRange
                    label: Effective Date
                    name: effectiveDate
                    mode: date-picker
                    setting:
                      format: dd/MM/yyyy
                      type: date
                  - name: status
                    label: Status
                    type: radio
                    radio:
                      - label: Active
                        value: true
                      - label: Inactive
                        value: false
                  - name: shortName
                    label: Short Name
                    type: translation
                  - name: longName
                    label: Long Name
                    type: translation
    - type: group
      label: Manager of Legal Entity
      collapse: false
      n_cols: 2
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - type: select
          name: managerType
          placeholder: Select Manager Type
          label: Manager Type
          outputValue: value
          clearFieldsAfterChange:
            - headOfLegalEntityObj
            - managerPositionObj
          _condition:
            transform: $not($.extend.formType = 'view')
          select:
            - label: Employee
              value: Employee
            - label: Position
              value: Position
        - type: select
          name: headOfLegalEntityObj
          dependantField: $.fields.managerType
          placeholder: Select Head of Legal Entity
          label: Head of Legal Entity
          isLazyLoad: true
          outputValue: value
          _condition:
            transform: >-
              $not($.extend.formType = 'view') and $.fields.managerType =
              'Employee'
          _validateFn:
            transform: >-
              $exists($.value.code) ?
              ($personalsListView($.extend.limit,$.extend.page,null,$.fields.effectiveDate,$.value.code)[0]
              ?
              $personalsListView($.extend.limit,$.extend.page,null,$.fields.effectiveDate,$.value.code)[0]
              : '_setSelectValueNull')
          _select:
            transform: >-
              $personalsList($.extend.limit,$.extend.page,$.extend.search,$.fields.effectiveDate)
        - type: select
          name: managerPositionObj
          dependantField: $.fields.managerType
          placeholder: Select Manager Position
          label: Manager Position
          outputValue: value
          _condition:
            transform: >-
              $not($.extend.formType = 'view') and $.fields.managerType =
              'Position'
          _validateFn:
            transform: >-
              $exists($.value.code) ?
              ($positionsList($.fields.effectiveDate,$.value.code,true)[0] ?
              $positionsList($.fields.effectiveDate,$.value.code,true)[0] :
              '_setSelectValueNull')
          _select:
            transform: $positionsList($.fields.effectiveDate,null,true)
    - type: group
      label: Manager of Legal Entity
      collapse: false
      n_cols: 1
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - type: select
          name: managerType
          placeholder: Select Manager Type
          label: Manager Type
          outputValue: value
          _condition:
            transform: $.extend.formType = 'view'
          select:
            - label: Employee
              value: Employee
            - label: Position
              value: Position
            - label: None
              value: None
        - type: select
          name: headOfLegalEntityObj
          placeholder: Select Head of Legal Entity
          label: Head of Legal Entity
          outputValue: value
          inputValue: code
          _condition:
            transform: $.fields.managerType = 'Employee' and $.extend.formType = 'view'
          _select:
            transform: >-
              $personalsListView($.extend.limit,$.extend.page,null,$.fields.effectiveDate,$.fields.headOfLegalEntityObj.value.code)
        - type: select
          name: managerPositionObj
          placeholder: Select Manager Position
          label: Manager Position
          outputValue: value
          inputValue: code
          _condition:
            transform: >-
              $.extend.formType = 'view' and $exists($.fields.managerType) and
              $.fields.managerType = 'Position' ? true : false
          _select:
            transform: $positionsList($.fields.effectiveDate,null,true)
    - type: group
      label: Former Organization
      collapse: false
      _condition:
        transform: $not($.extend.formType = 'view')
      disableEventCollapse: false
      fields:
        - type: array
          mode: table
          name: orgObjects
          minSize: 1
          arrayOptions:
            canChangeSize: true
          field:
            type: group
            name: group
            fields:
              - type: select
                name: type
                placeholder: Org Object
                label: Former Organization
                outputValue: value
                width: 250px
                select:
                  - label: Group
                    value: '1'
                  - label: Company
                    value: '2'
                  - label: Legal Entity
                    value: '3'
                  - label: Business Unit
                    value: '4'
                  - label: Division
                    value: '5'
                  - label: Department
                    value: '6'
                clearFieldsAfterChange:
                  - id
                  - objData
                  - code
              - type: selectCustom
                name: objData
                placeholder: Select Org Object
                isLazyLoad: true
                outputValue: value
                width: 300px
                _select:
                  dependants:
                    - $.fields.orgObjects[$index].type
                    - $.extend.page
                    - $.extend.search
                  params:
                    $index: $.extend.path[-1]
                  transform: >-
                    $getFieldGroup($.extend.path,$.fields,1).type = '1' ?
                    $groupsList($.extend.limit,$.extend.page,$.fields.effectiveDate,$.extend.search)
                    : $getFieldGroup($.extend.path,$.fields,1).type = '2' ?
                    $companyList($.extend.limit,$.extend.page,$.fields.effectiveDate,$.extend.search,$getFieldGroup($.extend.path,$.fields,1).code
                    ? $getFieldGroup($.extend.path,$.fields,1).code : null) :
                    $getFieldGroup($.extend.path,$.fields,1).type = '3' ?
                    $legalEntitiesListLazy($.extend.limit,$.extend.page,$.fields.effectiveDate,$.extend.search)
                    : $getFieldGroup($.extend.path,$.fields,1).type = '4' ?
                    $businessUnitListLazy($.extend.limit,$.extend.page,$.fields.effectiveDate,$.extend.search)
                    : $getFieldGroup($.extend.path,$.fields,1).type = '5' ?
                    $divisionsList($.extend.limit,$.extend.page,$.fields.effectiveDate,$.extend.search)
                    : $getFieldGroup($.extend.path,$.fields,1).type = '6' ?
                    $departmentsList($.extend.limit,$.extend.page,$.fields.effectiveDate,$.extend.search)
                    : null
                actions:
                  - view
                actionsConfig:
                  view:
                    formConfig:
                      fields:
                        - name: code
                          label: Code
                          type: text
                        - type: dateRange
                          label: Effective Date
                          name: effectiveDate
                          mode: date-picker
                          setting:
                            format: dd/MM/yyyy
                            type: date
                        - name: status
                          label: Status
                          type: radio
                          radio:
                            - label: Active
                              value: true
                            - label: Inactive
                              value: false
                        - name: shortName
                          label: Short Name
                          type: translation
                          placeholder: Enter Short Name
                        - name: longName
                          label: Long Name
                          type: translation
                validators:
                  - id: check_duplicate
                    type: ppx-custom
                    args:
                      transform: >-
                        $not($isNilorEmpty($.value)) ?
                        $count($map($.fields.orgObjects, function($item, $index)
                        {($item.id = $.value and $item.type =
                        $.fields.orgObjects[$index].type) ? {}})) > 1
                    text: Former organiztion has been duplicated
                  - id: check_null
                    type: ppx-custom
                    args:
                      transform: >-
                        $not($isNilorEmpty($getFieldGroup($.extend.path,$.fields,1).type))
                        and $isNilorEmpty($.value)
                    text: Cannot be empty
              - type: text
                name: id
                unvisible: true
                _value:
                  transform: $getFieldGroup($.extend.path,$.fields,1).objData.id
              - type: text
                name: code
                unvisible: true
                _value:
                  transform: $getFieldGroup($.extend.path,$.fields,1).objData.code
    - type: group
      collapse: false
      label: Former Organization
      _condition:
        transform: $.extend.formType = 'view'
      disableEventCollapse: false
      fields:
        - type: array
          mode: table
          name: orgObjects
          arrayOptions:
            canChangeSize: true
          field:
            type: group
            name: group
            fields:
              - type: select
                name: type
                placeholder: Org Object
                label: Former Organization
                width: 192px
                outputValue: value
                select:
                  - label: Group
                    value: '1'
                  - label: Company
                    value: '2'
                  - label: Legal Entity
                    value: '3'
                  - label: Business Unit
                    value: '4'
                  - label: Division
                    value: '5'
                  - label: Department
                    value: '6'
                clearFieldsAfterChange:
                  - id
              - type: selectCustom
                name: id
                placeholder: Select Org Object
                outputValue: value
                _select:
                  dependants:
                    - $.fields.orgObjects[$index].type
                  params:
                    $index: $.extend.path[-1]
                  transform: >-
                    $getFieldGroup($.extend.path,$.fields,1).type = '1' ?
                    $groupsList($.extend.limit,$.extend.page,$.fields.effectiveDate,$getFieldGroup($.extend.path,$.fields,1).code)
                    : $getFieldGroup($.extend.path,$.fields,1).type = '2' ?
                    $companyList($.extend.limit,$.extend.page,$.fields.effectiveDate,$getFieldGroup($.extend.path,$.fields,1).code)
                    : $getFieldGroup($.extend.path,$.fields,1).type = '3' ?
                    $legalEntitiesListViewLazy($.extend.limit,$.extend.page,$.fields.effectiveDate,$getFieldGroup($.extend.path,$.fields,1).code)
                    : $getFieldGroup($.extend.path,$.fields,1).type = '4' ?
                    $businessUnitListLazy($.extend.limit,$.extend.page,$.fields.effectiveDate,$getFieldGroup($.extend.path,$.fields,1).code)
                    : $getFieldGroup($.extend.path,$.fields,1).type = '5' ?
                    $divisionsList($.extend.limit,$.extend.page,$.fields.effectiveDate,$getFieldGroup($.extend.path,$.fields,1).code)
                    : $getFieldGroup($.extend.path,$.fields,1).type = '6' ?
                    $departmentsList($.extend.limit,$.extend.page,$.fields.effectiveDate,$getFieldGroup($.extend.path,$.fields,1).code)
                    : null
                actions:
                  - view
                actionsConfig:
                  view:
                    formConfig:
                      fields:
                        - name: code
                          label: Code
                          type: text
                        - type: dateRange
                          label: Effective Date
                          name: effectiveDate
                          mode: date-picker
                          setting:
                            format: dd/MM/yyyy
                            type: date
                        - name: status
                          label: Status
                          type: radio
                          radio:
                            - label: Active
                              value: true
                            - label: Inactive
                              value: false
                        - name: shortName
                          label: Short Name
                          type: translation
                          placeholder: Enter Short Name
                        - name: longName
                          label: Long Name
                          type: translation
              - type: text
                name: code
                unvisible: true
    - type: group
      label: Decision Information
      collapse: false
      n_cols: 2
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - type: select
          name: action
          label: Action
          placeholder: Select Action
          outputValue: value
          _select:
            transform: >-
              $.extend.formType = 'create' ? $actionsList('ACTIONORG_001') :
              $actionsList()
          _condition:
            transform: $not($.extend.formType = 'view')
        - type: select
          name: reason
          label: Reason
          dependantField: $.fields.action
          dependantFieldSkip: 2
          placeholder: Select Reason
          outputValue: value
          _condition:
            transform: $not($.extend.formType = 'view')
          _select:
            transform: $.fields.action ? $.variables._reasonsList
        - type: text
          label: Decision No.
          name: decisionNo
          placeholder: Enter Decision No.
          _condition:
            transform: $not($.extend.formType = 'view')
          validators:
            - type: maxLength
              args: '40'
              text: Maximum 40 characters
        - type: text
          label: Decision Name
          name: decisionName
          _condition:
            transform: $not($.extend.formType = 'view')
          placeholder: Enter Decision Name
          validators:
            - type: maxLength
              args: '200'
              text: Maximum 200 characters
        - type: dateRange
          label: Issuance Date
          name: issueDate
          _condition:
            transform: $not($.extend.formType = 'view')
          mode: date-picker
          placeholder: dd/mm/yyyy
        - type: select
          name: authorityForApproval
          label: Approved By
          outputValue: value
          placeholder: Select Approved By
          _condition:
            transform: $not($.extend.formType = 'view')
          _select:
            transform: $.variables._authorityForApprovalsList
        - type: text
          label: Signatory
          name: signatory
          placeholder: Enter Signatory
          col: 2
          _condition:
            transform: $not($.extend.formType = 'view')
          validators:
            - type: maxLength
              args: '120'
              text: Maximum 120 characters
        - type: upload
          label: Attachment
          name: attachFile
          col: 2
          upload:
            accept:
              - >-
                application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
              - application/pdf
              - application/vnd.ms-excel
            size: 5
          _condition:
            transform: $not($.extend.formType = 'view')
        - type: upload
          name: attachFileResults
          col: 2
          readOnly: true
          canAction: true
          hiddenLabel: true
          _condition:
            transform: $.extend.formType = 'edit'
    - type: group
      label: Decision Information
      collapse: false
      n_cols: 1
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - type: select
          name: action
          label: Action
          placeholder: Select Action
          outputValue: value
          _select:
            transform: $actionsList()
          _condition:
            transform: $.extend.formType = 'view'
        - type: select
          name: reason
          label: Reason
          _select:
            transform: $reasonsList()
          outputValue: value
          placeholder: Select Reason
          _condition:
            transform: $.extend.formType = 'view'
        - type: text
          label: Decision No.
          name: decisionNo
          placeholder: Enter Decision No.
          _condition:
            transform: '$$.extend.formType = ''view'' '
          validators:
            - type: maxLength
              args: '40'
              text: Maximum 40 characters
        - type: text
          label: Decision Name
          name: decisionName
          _condition:
            transform: '$$.extend.formType = ''view'' '
          placeholder: Enter Decision Name
          validators:
            - type: maxLength
              args: '200'
              text: Maximum 200 characters
        - type: dateRange
          label: Issuance Date
          name: issueDate
          mode: date-picker
          _condition:
            transform: '$$.extend.formType = ''view'' '
        - type: select
          name: authorityForApproval
          label: Approved By
          outputValue: value
          placeholder: Select Approved By
          _condition:
            transform: '$$.extend.formType = ''view'' '
          _select:
            transform: $.variables._authorityForApprovalsList
        - type: text
          label: Signatory
          name: signatory
          placeholder: Enter Signatory
          _condition:
            transform: '$$.extend.formType = ''view'' '
          validators:
            - type: maxLength
              args: '120'
              text: Maximum 120 characters
        - type: upload
          label: Attachment
          name: attachFileResults
          readOnly: true
          _condition:
            transform: $.extend.formType = 'view'
    - type: group
      label: Other Information
      collapse: false
      n_cols: 2
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - type: text
          label: Email Address
          name: emailAddress
          placeholder: Enter Email Address
          _condition:
            transform: $not($.extend.formType = 'view')
          validators:
            - type: maxLength
              args: '30'
              text: Maximum 30 characters
        - type: text
          label: Telephone
          placeholder: Enter Telephone
          name: telephone
          _condition:
            transform: $not($.extend.formType = 'view')
          validators:
            - type: maxLength
              args: '30'
              text: Maximum 30 characters
        - type: text
          label: Fax
          placeholder: Enter Fax
          name: fax
          _condition:
            transform: $not($.extend.formType = 'view')
          validators:
            - type: maxLength
              args: '30'
              text: Maximum 30 characters
        - type: text
          label: Tax Code
          placeholder: Enter Tax Code
          name: taxCode
          _condition:
            transform: $not($.extend.formType = 'view')
          validators:
            - type: maxLength
              args: '30'
              text: Maximum 30 characters
    - type: group
      label: Other Information
      collapse: false
      n_cols: 1
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - type: text
          label: Email Address
          name: emailAddress
          _condition:
            transform: $.extend.formType = 'view'
          validators:
            - type: maxLength
              args: '30'
              text: Maximum 30 characters
        - type: text
          label: Telephone
          name: telephone
          _condition:
            transform: $.extend.formType = 'view'
          validators:
            - type: maxLength
              args: '30'
              text: Maximum 30 characters
        - type: text
          label: Fax
          name: fax
          _condition:
            transform: $.extend.formType = 'view'
          validators:
            - type: maxLength
              args: '30'
              text: Maximum 30 characters
        - type: text
          label: Tax Code
          name: taxCode
          _condition:
            transform: $.extend.formType = 'view'
          validators:
            - type: maxLength
              args: '30'
              text: Maximum 30 characters
  historyHeaderTitle: '''View History Legal Entity'''
  sources:
    groupsList:
      uri: '"/api/groups/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page ,'filter':
        [{'field':'search','operator':
        '$eq','value':$.search},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'code','operator':
        '$eq','value':$.code}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id': $item.id, 'code': $item.code},
        'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - effectiveDate
        - search
        - code
    locationsList:
      uri: '"/api/locations/get-dropdown-list-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'filter':
        [{'field':'search','operator':
        '$eq','value':$.search},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':$.status},{'field':'code','operator': '$eq','value':
        $.codeFilter},{'field':'CompanyCode','operator': '$eq','value':
        $.companyCode},{'field':'LegalEntityCode','operator': '$eq','value':
        $.LegalEntityCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id': $item.id, 'code': $item.code},
        'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - effectiveDate
        - search
        - codeFilter
        - companyCode
        - LegalEntityCode
        - status
    departmentsList:
      uri: '"/api/departments/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'filter':
        [{'field':'search','operator':
        '$eq','value':$.search},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'code','operator':
        '$eq','value':$.code}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id': $item.id, 'code': $item.code} ,
        'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - effectiveDate
        - search
        - code
    divisionsList:
      uri: '"/api/divisions/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page ,'filter':
        [{'field':'search','operator':
        '$eq','value':$.search},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'code','operator':
        '$eq','value':$.code}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id': $item.id, 'code':
        $item.code},'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - effectiveDate
        - search
        - code
    legalEntitiesListLazy:
      uri: '"/api/legal-entities/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'filter':
        [{'field':'search','operator':
        '$eq','value':$.search},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':$.status}, {'field':'IdFilter','operator': '$ne','value':
        $.codeFilter}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value':  {'id': $item.id, 'code': $item.code},
        'CompanyCode': $item.companyCode,  'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - effectiveDate
        - search
        - codeFilter
        - status
    legalEntitiesListViewLazy:
      uri: '"/api/legal-entities/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'filter':
        [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':$.status}, {'field':'code','operator': '$eq','value':
        $.codeFilter}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id':$item.id,'code':$item.code},
        'additionalData': $item, 'CompanyCode': $item.companyCode}})[]
      disabledCache: true
      params:
        - limit
        - page
        - effectiveDate
        - codeFilter
        - status
    businessUnitList:
      uri: '"/api/business-units/by"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':$.status},{'field':'code','operator':
        '$eq','value':$.code}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id': $item.id, 'code': $item.code} ,
        'additionalData': $item}})[]
      disabledCache: true
      params:
        - effectiveDate
        - code
        - status
    businessUnitListLazy:
      uri: '"/api/business-units/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'filter':
        [{'field':'search','operator':
        '$eq','value':$.search},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'code','operator':
        '$eq','value': $.codeFilter}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id': $item.id, 'code': $item.code},
        'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - effectiveDate
        - search
        - codeFilter
    companyList:
      uri: '"/api/companies/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page ,'filter':
        [{'field':'search','operator':
        '$eq','value':$.search},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':$.status},{'field':'code','operator':
        '$eq','value':$.code}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id': $item.id, 'code': $item.code},
        'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - effectiveDate
        - search
        - code
        - status
    personalsList:
      uri: '"/api/personals/all-employees-job-datas"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page,'filter':
        [{'field':'search','operator': '$eq','value':$.paramsName},
        {'field':'effectiveDate','operator': '$eq','value':$.effectiveDate},
        {'field':'hrStatus','operator': '$eq','value': 'A'}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.fullName  & ' ' &
        $item.emailsplit, 'value': {'id': $item.employeeId, 'code':
        $item.employeeId}}})[]
      disabledCache: true
      params:
        - limit
        - page
        - paramsName
        - effectiveDate
        - code
    personalsListView:
      uri: '"/api/personals/all-employees-job-datas"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page,'filter':
        [{'field':'search','operator': '$eq','value':$.paramsName},
        {'field':'effectiveDate','operator': '$eq','value':$.effectiveDate},
        {'field':'hrStatus','operator': '$eq','value': 'A'},
        {'field':'employeeId','operator': '$in','value':$.code}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.fullName  & ' ' &
        $item.emailsplit, 'value': {'id': $item.employeeId, 'code':
        $item.employeeId}}})[]
      disabledCache: true
      params:
        - limit
        - page
        - paramsName
        - effectiveDate
        - code
    actionsList:
      uri: '"/api/picklists-values/ACTIONORG"'
      method: GET
      queryTransform: '{''filter'': [{''field'':''code'',''operator'': ''$eq'',''value'':$.codeAction}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code, 'id':$item.id}})[]
      disabledCache: true
      params:
        - codeAction
    positionsList:
      uri: '"/api/positions/by"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true},{'field':'code','operator': '$eq','value':$.code}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default  & ' (' &
        $item.code & ')', 'value': {'id': $item.id, 'code': $item.code},
        'additionalData': $item}})
      disabledCache: true
      params:
        - effectiveDate
        - code
    reasonsList:
      uri: '"/api/picklists/REASONORG/values"'
      method: GET
      queryTransform: '{''filter'': [{''field'':''col501'',''operator'': ''$eq'',''value'':$.codeAction}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - codeAction
    authorityForApprovalsList:
      uri: '"/api/picklists/CAPQDORG/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $string($item.code)}})
      disabledCache: true
  variables:
    _selectAction:
      transform: $filter($actionsList(),function ($v){ $v.value = $.fields.action })
    _actionId:
      transform: $.variables._selectAction.id
    _reasonsList:
      transform: $reasonsList($.variables._actionId)
    _authorityForApprovalsList:
      transform: $authorityForApprovalsList()
  _mode:
    transform: $not($.extend.formType = 'view') ? 'mark-scroll'
filter_config:
  fields:
    - type: text
      label: Legal Entity Code
      placeholder: Enter Legal Entity Code
      labelType: type-grid
      name: code
    - type: radio
      label: Status
      name: status
      labelType: type-grid
      value: ''
      radio:
        - value: ''
          label: All
        - value: true
          label: Active
        - value: false
          label: Inactive
    - type: text
      label: Short Name
      labelType: type-grid
      name: shortName
      placeholder: Enter Short Name
    - type: text
      label: Long Name
      labelType: type-grid
      name: longName
      placeholder: Enter Long Name
    - type: selectAll
      label: Company
      name: companyCode
      labelType: type-grid
      isLazyLoad: true
      placeholder: Select Company
      _options:
        transform: $companyList($.extend.limit,$.extend.page,$.extend.search)
    - type: selectAll
      label: Parent Legal Entity
      name: parentLegalEntityCode
      labelType: type-grid
      isLazyLoad: true
      placeholder: Select Parent Legal Entity
      _options:
        transform: $LegalEntityList($.extend.limit,$.extend.page,$.extend.search)
  filterMapping:
    - field: code
      operator: $cont
      valueField: code
    - field: na_shortName
      operator: $cont
      valueField: shortName
    - field: na_longName
      operator: $cont
      valueField: longName
    - field: companyCode
      operator: $in
      valueField: companyCode.(value)
    - field: parentLegalEntityCode
      operator: $in
      valueField: parentLegalEntityCode.(value)
    - field: status
      operator: $eq
      valueField: status
  sources:
    companyList:
      uri: '"/api/companies/get-list"'
      method: GET
      queryTransform: >-
        {'limit':$.limit,'page':$.page,'filter': [{'field':'search','operator':
        '$eq','value':$.search}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    LegalEntityList:
      uri: '"/api/legal-entities/v2/get-list"'
      method: GET
      queryTransform: >-
        {'limit':$.limit,'page':$.page,'filter': [{'field':'search','operator':
        '$eq','value':$.search}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
layout_options:
  is_upload_file: true
  view_history_after_created: true
  tool_table:
    - id: import
      icon: icon-upload-simple
    - id: export
      icon: icon-download-simple
  toolTable:
    export: true
    import: true
    adjustDisplay: true
  delete_multi_items: true
  custom_history_backend_url: /api/legal-entities/insert-new-record
  hide_action_row: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions: null
backend_url: /api/legal-entities
screen_name: null
layout_options__actions_many: null
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: companyCode
    defaultName: CompanyCode
  - name: code
    defaultName: LegalEntityCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Legal Entity
  parent:
    title: Organization Structure
