id: FO.FS.FR.014
status: draft
sort: 122
user_created: 56e71b91-dd39-4ebc-afd1-b4f00f38d4a5
date_created: '2024-06-13T06:40:38.559Z'
user_updated: 35b4f590-f747-4e53-8fa5-76aba65555c2
date_updated: '2025-07-19T02:27:31.790Z'
title: Department
requirement:
  time: 1749005156738
  blocks:
    - id: ItD0NJtBI7
      type: paragraph
      data:
        text: note
  version: 2.30.7
screen_design: null
module: FO
local_fields:
  - code: code
    pinned: true
    title: Department Code
    data_type:
      key: Increment ID
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    show_sort: true
    options__tabular__column_width: 12
  - code: shortName
    title: Short Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: longName
    title: Long Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: parentDepartmentName
    title: Parent Department
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
    options__tabular__column_width: 12
  - code: legalEntityName
    title: Legal Entity
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: businessUnitName
    title: Business Unit
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: divisionName
    title: Division
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: status
    title: Status
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: Boolean Tag
      collection: field_types
    show_sort: true
mock_data: null
local_buttons: null
layout: layout-table
form_config:
  formSize:
    create: large
    edit: large
    proceed: large
  fields:
    - type: group
      label: Basic Information
      collapse: false
      n_cols: 1
      fieldGroupTitleStyle:
        border: none
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - type: text
          label: Department Code
          name: code
          outputValue: value
          _condition:
            transform: $.extend.formType = 'view'
        - type: dateRange
          label: Effective Date
          name: effectiveDate
          mode: date-picker
          placeholder: DD/MM/YYYY
          _condition:
            transform: $.extend.formType = 'view'
        - type: radio
          label: Status
          name: status
          _condition:
            transform: $.extend.formType = 'view'
          radio:
            - value: true
              label: Active
            - value: false
              label: Inactive
        - type: translation
          label: Short Name
          name: shortName
          _condition:
            transform: $.extend.formType = 'view'
        - type: translation
          label: Long Name
          name: longName
          placeholder: Enter Long Name
          _condition:
            transform: $.extend.formType = 'view'
        - type: translation
          label: Display Name
          name: displayName
          placeholder: Enter Display Name
          _condition:
            transform: $.extend.formType = 'view'
        - type: select
          label: Dept ID (OU AD)
          name: depObj
          _condition:
            transform: $.extend.formType = 'view'
          placeholder: Select Dept ID (OU AD)
          outputValue: value
          inputValue: code
          _select:
            transform: >-
              $departmentsViewLazy($.extend.limit,
              $.extend.page,$.fields.effectiveDate,
              $.fields.depObj.depCode,true)
        - type: select
          label: Function
          name: functionObj
          _condition:
            transform: $.extend.formType = 'view'
          placeholder: Select Function
          outputValue: value
          inputValue: code
          _select:
            transform: $functionsList()
        - type: select
          label: Org Type
          _condition:
            transform: $.extend.formType = 'view'
          name: orgTypeId
          placeholder: Select Org Type
          outputValue: value
          _select:
            transform: $organizationTypesList()
        - type: textarea
          label: Responsibility
          name: responsibility
          placeholder: Enter Responsibility
          _condition:
            transform: $.extend.formType = 'view'
          validators:
            - type: maxLength
              args: '4000'
              text: Maximum 4000 characters
          textarea:
            autoSize:
              minRows: 3
    - type: group
      label: Basic Information
      collapse: false
      _condition:
        transform: $not($.extend.formType = 'view')
      n_cols: 2
      fields:
        - type: text
          label: Department Code
          name: code
          outputValue: value
          placeholder: Enter Department Code
          _condition:
            transform: $not($.extend.formType = 'view')
          _disabled:
            transform: $not($.extend.formType = 'create')
          validators:
            - type: required
            - type: maxLength
              args: '20'
              text: Maximum 20 characters
            - type: pattern
              args: ^[a-zA-Z0-9_.&]+$
              text: The Code must not contain special characters.
        - type: dateRange
          label: Effective Date
          name: effectiveDate
          mode: date-picker
          placeholder: dd/MM/yyyy
          _condition:
            transform: $not($.extend.formType = 'view')
          setting:
            format: dd/MM/yyyy
            type: date
          _value:
            transform: $.extend.formType = 'create' ? $now()
          validators:
            - type: required
        - type: radio
          label: Status
          name: status
          _condition:
            transform: $not($.extend.formType = 'view')
          _value:
            transform: $.extend.formType = 'create' and $not($.extend.isDuplicate) ? true
          radio:
            - value: true
              label: Active
            - value: false
              label: Inactive
        - type: translation
          label: Short Name
          name: shortName
          _condition:
            transform: $not($.extend.formType = 'view')
          validators:
            - type: maxLength
              args: '40'
              text: Maximum 40 characters
          placeholder: Enter Short Name
        - type: translation
          label: Long Name
          col: 2
          name: longName
          placeholder: Enter Long Name
          validators:
            - type: required
            - type: maxLength
              args: '120'
              text: Maximum 120 characters
          _condition:
            transform: $not($.extend.formType = 'view')
        - type: translation
          label: Display Name
          col: 2
          name: displayName
          placeholder: Enter Display Name
          validators:
            - type: maxLength
              args: '100'
              text: Maximum 100 characters
          _condition:
            transform: $not($.extend.formType = 'view')
        - type: select
          label: Function
          name: functionObj
          _condition:
            transform: $not($.extend.formType = 'view')
          placeholder: Select Function
          outputValue: value
          _select:
            transform: $functionsList()
        - type: select
          label: Org Type
          _condition:
            transform: $not($.extend.formType = 'view')
          name: orgTypeId
          placeholder: Select Org Type
          outputValue: value
          _select:
            transform: $organizationTypesList()
        - type: select
          name: depObj
          isLazyLoad: true
          col: 2
          label: Dept ID (OU AD)
          placeholder: Select Dept ID (OU AD)
          outputValue: value
          _select:
            transform: >-
              $departmentsListLazy($.extend.limit,
              $.extend.page,$.fields.effectiveDate,$.extend.search,$.extend.defaultValue.id,
              true)
          _validateFn:
            transform: >-
              $not($isNilorEmpty($.fields.depObj)) ?
              ($departmentsViewLazy(0,0,$.fields.effectiveDate,$.fields.depObj.code,true)[0]
              ?
              $departmentsViewLazy(0,0,$.fields.effectiveDate,$.fields.depObj.code,true)[0]
              : '_setSelectValueNull')
          _condition:
            transform: $not($.extend.formType = 'view')
        - type: textarea
          label: Responsibility
          name: responsibility
          col: 2
          _condition:
            transform: $not($.extend.formType = 'view')
          placeholder: Enter Responsibility
          validators:
            - type: maxLength
              args: '4000'
              text: Maximum 4000 characters
          textarea:
            autoSize:
              minRows: 3
              maxRows: 5
            maxCharCount: 4000
    - type: group
      label: Associations
      _condition:
        transform: $not($.extend.formType = 'view')
      n_cols: 2
      collapse: false
      fields:
        - type: text
          name: companyIdTmp
          unvisible: true
          _value:
            transform: $.extend.formType = 'edit' ? '_setValueNull'
        - type: select
          name: AssociationsType1
          label: Legal Association Type
          placeholder: Select Legal Association Type
          outputValue: value
          validators:
            - type: required
          _condition:
            transform: $not($.extend.formType = 'view')
          _value:
            transform: >-
              $not($isNilorEmpty($.extend.defaultValue.parentDepartments.value.code))
              ? true :
              $not($isNilorEmpty($.extend.defaultValue.legalEntityObj.value.code))
              ? false : '_setSelectValueNull'
          select:
            - label: Parent Department
              value: true
            - label: Legal Entity
              value: false
        - type: selectCustom
          name: parentDepartments
          isLazyLoad: true
          dependantField: $.fields.AssociationsType1
          label: Parent Department
          placeholder: Select Parent Department
          outputValue: value
          validators:
            - type: required
          _condition:
            transform: >-
              $not($.extend.formType = 'view') and $.fields.AssociationsType1 =
              true
          _select:
            transform: >-
              $departmentsListLazy($.extend.limit,
              $.extend.page,$.fields.effectiveDate,$.extend.search,$.extend.defaultValue.id,true)
          _validateFn:
            transform: >-
              $exists($.value.code) ?
              ($departmentsViewLazy(0,0,$.fields.effectiveDate,$.value.code,($not($.extend.formType
              = 'edit') or $not($DateFormat($.fields.effectiveDate,
              'yyyy-MM-DD') = $DateFormat($.extend.defaultValue.effectiveDate,
              'yyyy-MM-DD'))) ? true)[0] ?
              $departmentsViewLazy(0,0,$.fields.effectiveDate,$.value.code,($not($.extend.formType
              = 'edit') or $not($DateFormat($.fields.effectiveDate,
              'yyyy-MM-DD') = $DateFormat($.extend.defaultValue.effectiveDate,
              'yyyy-MM-DD'))) ? true)[0] : '_setSelectValueNull')
          actions:
            - view
          actionsConfig:
            view:
              formConfig:
                fields:
                  - name: code
                    label: Department Code
                    type: text
                  - type: dateRange
                    label: Effective Date
                    name: effectiveDate
                    mode: date-picker
                    setting:
                      format: dd/MM/yyyy
                      type: date
                  - name: status
                    label: Status
                    type: radio
                    radio:
                      - label: Active
                        value: true
                      - label: Inactive
                        value: false
                  - name: shortName
                    label: Short Name
                    type: translation
                  - name: longName
                    label: Long Name
                    type: translation
        - type: selectCustom
          name: legalEntityObj
          label: Legal Entity
          validators:
            - type: required
          dependantField: $.fields.AssociationsType1
          isLazyLoad: true
          _condition:
            transform: >-
              $not($.extend.formType = 'view') and $.fields.AssociationsType1 =
              false
          placeholder: Select Legal Entity
          outputValue: value
          _validateFn:
            transform: >-
              $exists($.value.code) ?
              ($legalEntitiesListLazyView($.fields.effectiveDate,$.value.code,($not($.extend.formType
              = 'edit') or $not($DateFormat($.fields.effectiveDate,
              'yyyy-MM-DD') = $DateFormat($.extend.defaultValue.effectiveDate,
              'yyyy-MM-DD'))) ? true)[0] ?
              $legalEntitiesListLazyView($.fields.effectiveDate,$.value.code,($not($.extend.formType
              = 'edit') or $not($DateFormat($.fields.effectiveDate,
              'yyyy-MM-DD') = $DateFormat($.extend.defaultValue.effectiveDate,
              'yyyy-MM-DD'))) ? true)[0] : '_setSelectValueNull')
          _select:
            transform: >-
              $legalEntitiesListLazy($.extend.limit,$.extend.page,$.fields.effectiveDate,$.extend.search,true)
          actions:
            - view
          actionsConfig:
            view:
              formConfig:
                fields:
                  - name: code
                    label: Legal Entity Code
                    type: text
                  - type: dateRange
                    label: Effective Date
                    name: effectiveDate
                    mode: date-picker
                    setting:
                      format: dd/MM/yyyy
                      type: date
                  - name: status
                    label: Status
                    type: radio
                    radio:
                      - label: Active
                        value: true
                      - label: Inactive
                        value: false
                  - name: shortName
                    label: Short Name
                    type: translation
                  - name: longName
                    label: Long Name
                    type: translation
        - type: select
          name: AssociationsType2
          label: Management Association Type
          placeholder: Select Management Association Type
          outputValue: value
          _value:
            transform: >-
              ($not($isNilorEmpty($.extend.defaultValue.divisionObj.value.code))
              or $not($isNilorEmpty($.fields.parentDepartments.division.code)))
              ? false :
              ($not($isNilorEmpty($.extend.defaultValue.businessUnitObj.value.code))
              or
              $not($isNilorEmpty($.fields.parentDepartments.businessUnit.code)))
              ? true : '_setSelectValueNull'
          _disabled:
            transform: >-
              $not($isNilorEmpty($.fields.parentDepartments.division.code)) or
              $not($isNilorEmpty($.fields.parentDepartments.businessUnit.code))
              ? true : false
          _condition:
            transform: $not($.extend.formType = 'view')
          select:
            - label: Business Unit
              value: true
            - label: Division
              value: false
        - type: selectCustom
          name: divisionObj
          label: Division
          dependantField: $.fields.AssociationsType2
          validators:
            - type: required
          isLazyLoad: true
          _condition:
            transform: >-
              $not($.extend.formType = 'view') and $.fields.AssociationsType2 =
              false
          placeholder: Select Division
          outputValue: value
          _validateFn:
            transform: >-
              $exists($.value.code) ?
              $divisionsListLazyView($.fields.effectiveDate,$.value.code,($not($.extend.formType
              = 'edit') or $not($DateFormat($.fields.effectiveDate,
              'yyyy-MM-DD') = $DateFormat($.extend.defaultValue.effectiveDate,
              'yyyy-MM-DD'))) ? true)[0] ?
              $divisionsListLazyView($.fields.effectiveDate,$.value.code,($not($.extend.formType
              = 'edit') or $not($DateFormat($.fields.effectiveDate,
              'yyyy-MM-DD') = $DateFormat($.extend.defaultValue.effectiveDate,
              'yyyy-MM-DD'))) ? true)[0] : '_setSelectValueNull'
          _value:
            transform: >-
              $exists($test($.fields.parentDepartments.division.code)) ?
              $divisionsListLazyView($.fields.effectiveDate,$.fields.parentDepartments.division.code,true)[0]
              : '_setSelectValueNull'
          _disabled:
            transform: >-
              $isNilorEmpty($.fields.parentDepartments.division.code) ? false :
              $divisionsListLazyView($.fields.effectiveDate,$.fields.parentDepartments.division.code,true)[0]
              ? true : false
          _select:
            transform: >-
              $divisionsListLazy($.extend.limit,$.extend.page,$.fields.effectiveDate,$.extend.search,true)
          actions:
            - view
          actionsConfig:
            view:
              formConfig:
                fields:
                  - name: code
                    label: Division Code
                    type: text
                  - type: dateRange
                    label: Effective Date
                    name: effectiveDate
                    mode: date-picker
                    setting:
                      format: dd/MM/yyyy
                      type: date
                  - name: status
                    label: Status
                    type: radio
                    radio:
                      - label: Active
                        value: true
                      - label: Inactive
                        value: false
                  - name: shortName
                    label: Short Name
                    type: translation
                  - name: longName
                    label: Long Name
                    type: translation
        - type: selectCustom
          name: businessUnitObj
          label: Business Unit
          dependantField: $.fields.AssociationsType2
          validators:
            - type: required
          isLazyLoad: true
          _condition:
            transform: >-
              $not($.extend.formType = 'view') and $.fields.AssociationsType2 =
              true
          placeholder: Select Business Unit
          outputValue: value
          _validateFn:
            transform: >-
              $exists($.value.code) ?
              ($businessUnitListLazyView($.fields.effectiveDate,$.value.code,($not($.extend.formType
              = 'edit') or $not($DateFormat($.fields.effectiveDate,
              'yyyy-MM-DD') = $DateFormat($.extend.defaultValue.effectiveDate,
              'yyyy-MM-DD'))) ? true)[0] ?
              $businessUnitListLazyView($.fields.effectiveDate,$.value.code,($not($.extend.formType
              = 'edit') or $not($DateFormat($.fields.effectiveDate,
              'yyyy-MM-DD') = $DateFormat($.extend.defaultValue.effectiveDate,
              'yyyy-MM-DD'))) ? true)[0] : '_setSelectValueNull')
          _select:
            transform: >-
              $businessUnitListLazy($.extend.limit,$.extend.page,$.fields.effectiveDate,$.extend.search,true)
          _value:
            transform: >-
              $exists($test($.fields.parentDepartments.businessUnit.code)) ?
              $businessUnitListLazyView($.fields.effectiveDate,$.fields.parentDepartments.businessUnit.code,true)[0]
              : '_setSelectValueNull'
          _disabled:
            transform: >-
              $isNilorEmpty($.fields.parentDepartments.businessUnit.code) ?
              false :
              $businessUnitListLazyView($.fields.effectiveDate,$.fields.parentDepartments.businessUnit.code,true)[0]
              ? true : false
          actions:
            - view
          actionsConfig:
            view:
              formConfig:
                fields:
                  - name: code
                    label: Business Unit Code
                    type: text
                  - type: dateRange
                    label: Effective Date
                    name: effectiveDate
                    mode: date-picker
                    setting:
                      format: dd/MM/yyyy
                      type: date
                  - name: status
                    label: Status
                    type: radio
                    radio:
                      - label: Active
                        value: true
                      - label: Inactive
                        value: false
                  - name: shortName
                    label: Short Name
                    type: translation
                  - name: longName
                    label: Long Name
                    type: translation
        - type: selectCustom
          label: Location
          dependantField: >-
            $.fields.parentDepartments;$.fields.legalEntityObj;$.fields.AssociationsType1
          dependantFieldSkip: 4
          name: locationObj
          isLazyLoad: true
          placeholder: Select Location
          outputValue: value
          _validateFn:
            transform: >-
              $exists($.value.code) ?
              ($locationsList(0,0,$.fields.effectiveDate,null,$.value.code,null,null,true)[0]
              ?
              $locationsList(0,0,$.fields.effectiveDate,null,$.value.code,null,null,true)[0]
              : '_setSelectValueNull')
          _condition:
            transform: $not($.extend.formType = 'view')
          _select:
            transform: ' ($not($isNilorEmpty($.fields.parentDepartments)) or $not($isNilorEmpty($.fields.legalEntityObj))) ? $locationsList($.extend.limit,$.extend.page,$.fields.effectiveDate,$.extend.search,null,$.fields.parentDepartments.code, $.fields.legalEntityObj.code,true) : []'
          actions:
            - view
          actionsConfig:
            view:
              formConfig:
                fields:
                  - name: code
                    label: Location Code
                    type: text
                  - type: dateRange
                    label: Effective Date
                    name: effectiveDate
                    mode: date-picker
                    setting:
                      format: dd/MM/yyyy
                      type: date
                  - name: status
                    label: Status
                    type: radio
                    radio:
                      - label: Active
                        value: true
                      - label: Inactive
                        value: false
                  - name: shortName
                    label: Short Name
                    type: translation
                  - name: longName
                    label: Long Name
                    type: translation
        - type: selectCustom
          label: Cost Center
          name: costCenter
          dependantField: >-
            $.fields.parentDepartments;$.fields.legalEntityObj;$.fields.AssociationsType1
          _condition:
            transform: $not($.extend.formType = 'view')
          placeholder: Select Cost Center
          outputValue: value
          _validateFn:
            transform: >-
              ($.fields.legalEntityObj.code; $exists($.value.code) ?
              ($costCenterList($.fields.effectiveDate,
              $.value.code,$.fields.parentDepartments.code,null,true)[0] ?
              $costCenterList($.fields.effectiveDate,$.value.code,$.fields.parentDepartments.code,null,true)[0]
              : '_setSelectValueNull'))
          _select:
            transform: ' ($not($isNilorEmpty($.fields.parentDepartments.code)) or $not($isNilorEmpty($.fields.legalEntityObj.code))) ? $costCenterList($.fields.effectiveDate,null, $.fields.parentDepartments.code, $.fields.legalEntityObj.code,true)'
          actions:
            - view
          actionsConfig:
            view:
              formConfig:
                fields:
                  - name: code
                    label: Cost Center Code
                    type: text
                  - type: dateRange
                    label: Effective Date
                    name: effectiveDate
                    mode: date-picker
                    setting:
                      format: dd/MM/yyyy
                      type: date
                  - name: status
                    label: Status
                    type: radio
                    radio:
                      - label: Active
                        value: true
                      - label: Inactive
                        value: false
                  - name: shortName
                    label: Short Name
                    type: translation
                  - name: longName
                    label: Long Name
                    type: translation
    - type: group
      label: Associations
      collapse: false
      n_cols: 1
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - type: selectCustom
          name: legalEntityObj
          label: Legal Entity
          _condition:
            transform: $.extend.formType = 'view'
          placeholder: Select Legal Entity
          outputValue: value
          inputValue: code
          _validateFn:
            transform: >-
              $exists($.extend.defaultValue.legalEntityObj.value.code) ?
              ($legalEntitiesListLazyView($.fields.effectiveDate,$.extend.defaultValue.legalEntityObj.value.code)[0]
              ?
              $legalEntitiesListLazyView($.fields.effectiveDate,$.extend.defaultValue.legalEntityObj.value.code)[0]
              : '_setSelectValueNull')
          _select:
            transform: >-
              $legalEntitiesListLazyView($.fields.effectiveDate,$.extend.defaultValue.legalEntityObj.value.code)
          actions:
            - view
          actionsConfig:
            view:
              formConfig:
                fields:
                  - name: code
                    label: Legal Entity Code
                    type: text
                  - type: dateRange
                    label: Effective Date
                    name: effectiveDate
                    mode: date-picker
                    setting:
                      format: dd/MM/yyyy
                      type: date
                  - name: status
                    label: Status
                    type: radio
                    radio:
                      - label: Active
                        value: true
                      - label: Inactive
                        value: false
                  - name: shortName
                    label: Short Name
                    type: translation
                  - name: longName
                    label: Long Name
                    type: translation
        - type: selectCustom
          name: parentDepartments
          label: Parent Department
          placeholder: Select Parent Department
          outputValue: value
          inputValue: code
          _condition:
            transform: $.extend.formType = 'view'
          _validateFn:
            transform: >-
              $exists($.extend.defaultValue.parentDepartments.value.code) ?
              ($departmentsViewLazy(0,0,$.fields.effectiveDate,$.extend.defaultValue.parentDepartments.value.code)[0]
              ?
              $departmentsViewLazy(0,0,$.fields.effectiveDate,$.extend.defaultValue.parentDepartments.value.code)[0]
              : '_setSelectValueNull')
          _select:
            transform: >-
              $departmentsViewLazy($.extend.limit,
              $.extend.page,$.fields.effectiveDate,
              $.extend.defaultValue.parentDepartments.value.code)
          actions:
            - view
          actionsConfig:
            view:
              formConfig:
                fields:
                  - name: code
                    label: Department Code
                    type: text
                  - type: dateRange
                    label: Effective Date
                    name: effectiveDate
                    mode: date-picker
                    setting:
                      format: dd/MM/yyyy
                      type: date
                  - name: status
                    label: Status
                    type: radio
                    radio:
                      - label: Active
                        value: true
                      - label: Inactive
                        value: false
                  - name: shortName
                    label: Short Name
                    type: translation
                  - name: longName
                    label: Long Name
                    type: translation
        - type: selectCustom
          name: businessUnitObj
          label: Business Unit
          _condition:
            transform: $.extend.formType = 'view'
          placeholder: Select Business Unit
          outputValue: value
          inputValue: code
          _validateFn:
            transform: >-
              $exists($.extend.defaultValue.businessUnitObj.value.code) ?
              ($businessUnitListLazyView($.fields.effectiveDate,$.extend.defaultValue.businessUnitObj.value.code)[0]
              ?
              $businessUnitListLazyView($.fields.effectiveDate,$.extend.defaultValue.businessUnitObj.value.code)[0]
              : '_setSelectValueNull')
          _select:
            transform: >-
              $businessUnitListLazyView($.fields.effectiveDate,$.extend.defaultValue.businessUnitObj.value.code)
          actions:
            - view
          actionsConfig:
            view:
              formConfig:
                fields:
                  - name: code
                    label: Business Unit Code
                    type: text
                  - type: dateRange
                    label: Effective Date
                    name: effectiveDate
                    mode: date-picker
                    setting:
                      format: dd/MM/yyyy
                      type: date
                  - name: status
                    label: Status
                    type: radio
                    radio:
                      - label: Active
                        value: true
                      - label: Inactive
                        value: false
                  - name: shortName
                    label: Short Name
                    type: translation
                  - name: longName
                    label: Long Name
                    type: translation
        - type: selectCustom
          name: divisionObj
          label: Division
          _condition:
            transform: $.extend.formType = 'view'
          placeholder: Select Division
          outputValue: value
          inputValue: code
          _validateFn:
            transform: >-
              $exists($.extend.defaultValue.divisionObj.value.code) ?
              ($divisionsListLazyView($.fields.effectiveDate,$.extend.defaultValue.divisionObj.value.code)[0]
              ?
              $divisionsListLazyView($.fields.effectiveDate,$.extend.defaultValue.divisionObj.value.code)[0]
              : '_setSelectValueNull')
          _select:
            transform: >-
              $divisionsListLazyView($.fields.effectiveDate,$.extend.defaultValue.divisionObj.value.code)
          actions:
            - view
          actionsConfig:
            view:
              formConfig:
                fields:
                  - name: code
                    label: Division Code
                    type: text
                  - type: dateRange
                    label: Effective Date
                    name: effectiveDate
                    mode: date-picker
                    setting:
                      format: dd/MM/yyyy
                      type: date
                  - name: status
                    label: Status
                    type: radio
                    radio:
                      - label: Active
                        value: true
                      - label: Inactive
                        value: false
                  - name: shortName
                    label: Short Name
                    type: translation
                  - name: longName
                    label: Long Name
                    type: translation
        - type: selectCustom
          label: Location
          name: locationObj
          placeholder: Select Location
          outputValue: value
          inputValue: code
          _condition:
            transform: $.extend.formType = 'view'
          _select:
            transform: >-
              $locationsList($.extend.limit,$.extend.page,$.fields.effectiveDate,null,$.extend.defaultValue.locationObj.value.code)
          actions:
            - view
          actionsConfig:
            view:
              formConfig:
                fields:
                  - name: code
                    label: Location Code
                    type: text
                  - type: dateRange
                    label: Effective Date
                    name: effectiveDate
                    mode: date-picker
                    setting:
                      format: dd/MM/yyyy
                      type: date
                  - name: status
                    label: Status
                    type: radio
                    radio:
                      - label: Active
                        value: true
                      - label: Inactive
                        value: false
                  - name: shortName
                    label: Short Name
                    type: translation
                  - name: longName
                    label: Long Name
                    type: translation
        - type: selectCustom
          label: Cost Center
          name: costCenter
          _condition:
            transform: $.extend.formType = 'view'
          placeholder: Select Cost Center
          outputValue: value
          inputValue: code
          _select:
            transform: >-
              $costCenterList($.fields.effectiveDate,$.extend.defaultValue.costCenter.code)
          actions:
            - view
          actionsConfig:
            view:
              formConfig:
                fields:
                  - name: code
                    label: Cost Center Code
                    type: text
                  - type: dateRange
                    label: Effective Date
                    name: effectiveDate
                    mode: date-picker
                    setting:
                      format: dd/MM/yyyy
                      type: date
                  - name: status
                    label: Status
                    type: radio
                    radio:
                      - label: Active
                        value: true
                      - label: Inactive
                        value: false
                  - name: shortName
                    label: Short Name
                    type: translation
                  - name: longName
                    label: Long Name
                    type: translation
    - type: group
      label: Manager of Department
      collapse: false
      n_cols: 2
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - type: select
          name: managerType
          label: Manager Type
          placeholder: Select Manager Type
          outputValue: value
          clearFieldsAfterChange:
            - headOfDepartmentObj
            - deputyManagersObj
            - managerPositionObj
            - deputyPositionManagers
          _condition:
            transform: $not($.extend.formType = 'view')
          select:
            - label: Employee
              value: Employee
            - label: Position
              value: Position
        - type: select
          name: headOfDepartmentObj
          dependantField: $.fields.managerType
          label: Head of Department
          isLazyLoad: true
          placeholder: Select Head of Department
          _condition:
            transform: >-
              $not($.extend.formType = 'view') and $.fields.managerType =
              'Employee'
          outputValue: value
          _validateFn:
            transform: >-
              $exists($.value.code) ?
              ($personalsListView($.extend.limit,$.extend.page,null,$.fields.effectiveDate,$.value.code)[0]
              ?
              $personalsListView($.extend.limit,$.extend.page,null,$.fields.effectiveDate,$.value.code)[0]
              : '_setSelectValueNull')
          _select:
            transform: >-
              $personalsList($.extend.limit,$.extend.page,$.extend.search,$.fields.effectiveDate)
        - type: selectAll
          name: deputyManagersObj
          dependantField: $.fields.managerType
          label: Deputy Manager
          isLazyLoad: true
          col: 2
          _condition:
            transform: >-
              $not($.extend.formType = 'view') and $.fields.managerType =
              'Employee'
          _validateFn:
            transform: >-
              $exists($.value.code) ?
              ($personalsListView($.extend.limit,$.extend.page,null,$.fields.effectiveDate,$.value.code)[0]
              ?
              $personalsListView($.extend.limit,$.extend.page,null,$.fields.effectiveDate,$.value.code)
              : '_setSelectValueNull')
          placeholder: Select Deputy Manager
          outputValue: value
          _options:
            transform: >-
              $personalsList($.extend.limit,$.extend.page,$.extend.search,
              $.fields.effectiveDate)
        - type: select
          name: managerPositionObj
          dependantField: $.fields.managerType
          label: Manager Position
          placeholder: Select Manager Position
          _condition:
            transform: >-
              $not($.extend.formType = 'view') and $.fields.managerType =
              'Position'
          outputValue: value
          _validateFn:
            transform: >-
              $exists($.value.code) ?
              ($positionsList($.fields.effectiveDate,$.value.code)[0] ?
              $positionsList($.fields.effectiveDate,$.value.code)[0] :
              '_setSelectValueNull')
          _select:
            transform: $positionsList($.fields.effectiveDate)
        - type: select
          name: deputyPositionManagers
          dependantField: $.fields.managerType
          label: Deputy Manager Position
          col: 2
          mode: multiple
          _condition:
            transform: >-
              $not($.extend.formType = 'view') and $.fields.managerType =
              'Position'
          placeholder: Select Deputy Manager Position
          outputValue: value
          _select:
            transform: $positionsListMutil($.fields.effectiveDate)
    - type: group
      label: Manager of Department
      collapse: false
      n_cols: 1
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - type: select
          name: managerType
          label: Manager Type
          outputValue: value
          _condition:
            transform: $.extend.formType = 'view'
          placeholder: Select Manager Type
          select:
            - label: Employee
              value: Employee
            - label: Position
              value: Position
        - type: select
          name: headOfDepartmentObj
          label: Head of Department
          _condition:
            transform: $.fields.managerType = 'Employee' and $.extend.formType = 'view'
          placeholder: Select Head of Department
          outputValue: value
          inputValue: value
          _select:
            transform: >-
              $personalsListView($.extend.limit,$.extend.page,null,$.fields.effectiveDate,$.fields.headOfDepartmentObj.value.code)
        - type: select
          name: deputyManagersObj
          label: Deputy Manager
          mode: multiple
          _condition:
            transform: $.fields.managerType = 'Employee' and $.extend.formType = 'view'
          placeholder: Select Deputy Manager
          outputValue: value
          inputValue: value
          _select:
            transform: >-
              $personalsListView($.extend.limit,$.extend.page,null,$.fields.effectiveDate,$.fields.deputyManagersObj.value.code)
        - type: select
          name: managerPositionObj
          label: Manager Position
          _condition:
            transform: $.fields.managerType = 'Position' and $.extend.formType = 'view'
          placeholder: Select Manager Position
          outputValue: value
          inputValue: code
          _select:
            transform: $positionsList($.fields.effectiveDate)
        - type: select
          name: deputyPositionManagers
          label: Deputy Manager Position
          _condition:
            transform: $.fields.managerType = 'Position' and $.extend.formType = 'view'
          placeholder: Select Deputy Manager Position
          outputValue: value
          mode: multiple
          _select:
            transform: $positionsListMutil($.fields.effectiveDate)
    - type: group
      label: Former Organization
      collapse: false
      _condition:
        transform: $not($.extend.formType = 'view')
      disableEventCollapse: false
      fields:
        - type: array
          mode: table
          name: orgObjects
          minSize: 1
          arrayOptions:
            canChangeSize: true
          field:
            type: group
            name: group
            fields:
              - type: select
                name: type
                placeholder: Org Object
                outputValue: value
                label: Former Organization
                width: 250px
                select:
                  - label: Group
                    value: '1'
                  - label: Company
                    value: '2'
                  - label: Legal Entity
                    value: '3'
                  - label: Business Unit
                    value: '4'
                  - label: Division
                    value: '5'
                  - label: Department
                    value: '6'
                clearFieldsAfterChange:
                  - id
                  - objData
                  - code
              - type: selectCustom
                name: objData
                placeholder: Select Org Object
                isLazyLoad: true
                outputValue: value
                _validateFn:
                  transform: >-
                    $exists($.fields.orgObjects[$index].code) ?
                    ($getFieldGroup($.extend.path,$.fields,1).type = '1' ?
                    $groupsList($.extend.limit,$.extend.page,$.fields.effectiveDate,$.fields.orgObjects[$index].code)[0]
                    : $getFieldGroup($.extend.path,$.fields,1).type = '2' ?
                    $companyList($.extend.limit,$.extend.page,$.fields.effectiveDate,null,$.fields.orgObjects[$index].code)[0]
                    : $getFieldGroup($.extend.path,$.fields,1).type = '3' ?
                    $legalEntitiesListLazyView($.fields.effectiveDate,$.fields.orgObjects[$index].code)[0]
                    : $getFieldGroup($.extend.path,$.fields,1).type = '4' ?
                    $businessUnitListLazyView($.fields.effectiveDate,$.fields.orgObjects[$index].code)[0]
                    :  $getFieldGroup($.extend.path,$.fields,1).type = '5' ?
                    $divisionsListLazyView($.fields.effectiveDate,$.fields.orgObjects[$index].code)[0]
                    : $getFieldGroup($.extend.path,$.fields,1).type = '6' ?
                    $departmentsViewLazy($.extend.limit,$.extend.page,$.fields.effectiveDate,$.fields.orgObjects[$index].code)[0]
                    : '_setSelectValueNull')
                  dependants:
                    - $.fields.orgObjects[$index].type
                  params:
                    $index: $.extend.path[-1]
                _select:
                  dependants:
                    - $.fields.orgObjects[$index].type
                    - $.extend.page
                    - $.extend.search
                  params:
                    $index: $.extend.path[-1]
                  transform: >-
                    $getFieldGroup($.extend.path,$.fields,1).type = '1' ?
                    $groupsList($.extend.limit,$.extend.page,$.fields.effectiveDate,$.extend.search)
                    : $getFieldGroup($.extend.path,$.fields,1).type = '2' ?
                    $companyList($.extend.limit,$.extend.page,$.fields.effectiveDate,$.extend.search)
                    : $getFieldGroup($.extend.path,$.fields,1).type = '3' ?
                    $legalEntitiesListLazy($.extend.limit,$.extend.page,$.fields.effectiveDate,$.extend.search)
                    : $getFieldGroup($.extend.path,$.fields,1).type = '4' ?
                    $businessUnitListLazy($.extend.limit,$.extend.page,$.fields.effectiveDate,$.extend.search)
                    : $getFieldGroup($.extend.path,$.fields,1).type = '5' ?
                    $divisionsListLazy($.extend.limit,$.extend.page,$.fields.effectiveDate,$.extend.search)
                    : $getFieldGroup($.extend.path,$.fields,1).type = '6' ?
                    $departmentsListLazy($.extend.limit,$.extend.page,$.fields.effectiveDate,$.extend.search)
                    : null
                actions:
                  - view
                actionsConfig:
                  view:
                    formConfig:
                      fields:
                        - name: code
                          label: Code
                          type: text
                        - type: dateRange
                          label: Effective Date
                          name: effectiveDate
                          mode: date-picker
                          setting:
                            format: dd/MM/yyyy
                            type: date
                        - name: status
                          label: Status
                          type: radio
                          radio:
                            - label: Active
                              value: true
                            - label: Inactive
                              value: false
                        - name: shortName
                          label: Short Name
                          type: translation
                          placeholder: Enter Short Name
                        - name: longName
                          label: Long Name
                          type: translation
                validators:
                  - id: check_duplicate
                    type: ppx-custom
                    args:
                      transform: >-
                        $not($isNilorEmpty($.value)) ?
                        $count($map($.fields.orgObjects, function($item, $index)
                        {($item.id = $.value and $item.type =
                        $.fields.orgObjects[$index].type) ? {}})) > 1
                    text: Former organiztion has been duplicated
                  - id: check_null
                    type: ppx-custom
                    args:
                      transform: >-
                        $not($isNilorEmpty($getFieldGroup($.extend.path,$.fields,1).type))
                        and $isNilorEmpty($.value)
                    text: Cannot be empty
              - type: text
                name: id
                unvisible: true
                _value:
                  transform: $getFieldGroup($.extend.path,$.fields,1).objData.id
              - type: text
                name: code
                unvisible: true
                _value:
                  transform: $getFieldGroup($.extend.path,$.fields,1).objData.code
    - type: group
      collapse: false
      label: Former Organization
      _condition:
        transform: $.extend.formType = 'view'
      disableEventCollapse: false
      fields:
        - type: array
          mode: table
          name: orgObjects
          arrayOptions:
            canChangeSize: true
          field:
            type: group
            name: group
            fields:
              - type: select
                name: type
                placeholder: Org Object
                label: Former Organization
                width: 192px
                outputValue: value
                select:
                  - label: Group
                    value: '1'
                  - label: Company
                    value: '2'
                  - label: Legal Entity
                    value: '3'
                  - label: Business Unit
                    value: '4'
                  - label: Division
                    value: '5'
                  - label: Department
                    value: '6'
                clearFieldsAfterChange:
                  - id
              - type: selectCustom
                name: id
                placeholder: Select Org Object
                outputValue: value
                _select:
                  dependants:
                    - $.fields.orgObjects[$index].type
                  params:
                    $index: $.extend.path[-1]
                  transform: >-
                    $getFieldGroup($.extend.path,$.fields,1).type = '1' ?
                    $groupsList($.extend.limit,$.extend.page,$.fields.effectiveDate,$getFieldGroup($.extend.path,$.fields,1).code)
                    : $getFieldGroup($.extend.path,$.fields,1).type = '2' ?
                    $companyList($.extend.limit,$.extend.page,$.fields.effectiveDate,null,$getFieldGroup($.extend.path,$.fields,1).code)
                    : $getFieldGroup($.extend.path,$.fields,1).type = '3' ?
                    $legalEntitiesListLazyView($.fields.effectiveDate,$getFieldGroup($.extend.path,$.fields,1).code)
                    : $getFieldGroup($.extend.path,$.fields,1).type = '4' ?
                    $businessUnitListLazyView($.fields.effectiveDate,$getFieldGroup($.extend.path,$.fields,1).code)
                    : $getFieldGroup($.extend.path,$.fields,1).type = '5' ?
                    $divisionsListLazyView($.fields.effectiveDate,$getFieldGroup($.extend.path,$.fields,1).code)
                    : $getFieldGroup($.extend.path,$.fields,1).type = '6' ?
                    $departmentsViewLazy($.extend.limit,$.extend.page,$.fields.effectiveDate,$getFieldGroup($.extend.path,$.fields,1).code)
                    : null
                actions:
                  - view
                actionsConfig:
                  view:
                    formConfig:
                      fields:
                        - name: code
                          label: Code
                          type: text
                        - type: dateRange
                          label: Effective Date
                          name: effectiveDate
                          mode: date-picker
                          setting:
                            format: dd/MM/yyyy
                            type: date
                        - name: status
                          label: Status
                          type: radio
                          radio:
                            - label: Active
                              value: true
                            - label: Inactive
                              value: false
                        - name: shortName
                          label: Short Name
                          type: translation
                          placeholder: Enter Short Name
                        - name: longName
                          label: Long Name
                          type: translation
              - type: text
                name: code
                unvisible: true
    - type: group
      label: Decision Information
      collapse: false
      _condition:
        transform: $not($.extend.formType = 'view')
      n_cols: 2
      fields:
        - type: select
          name: action
          label: Action
          placeholder: Select Action
          outputValue: value
          _select:
            transform: >-
              $.extend.formType = 'create' ? $actionsList('ACTIONORG_001') :
              $actionsList()
          _condition:
            transform: $not($.extend.formType = 'view')
        - type: select
          name: reason
          label: Reason
          dependantField: $.fields.action
          dependantFieldSkip: 2
          _select:
            transform: $.fields.action ? $.variables._reasonList
          outputValue: value
          placeholder: Select Reason
          _condition:
            transform: $not($.extend.formType = 'view')
        - type: text
          label: Decision No.
          name: decisionNo
          validators:
            - type: maxLength
              args: '40'
              text: Maximum 40 characters
          _condition:
            transform: $not($.extend.formType = 'view')
          placeholder: Enter Decision No.
        - type: text
          label: Decision Name
          validators:
            - type: maxLength
              args: '200'
              text: Maximum 200 characters
          _condition:
            transform: $not($.extend.formType = 'view')
          name: decisionName
          placeholder: Enter Decision Name
        - type: dateRange
          label: Issuance Date
          name: issueDate
          mode: date-picker
          placeholder: dd/mm/yyyy
          _condition:
            transform: $not($.extend.formType = 'view')
          setting:
            format: dd/MM/yyyy
            type: date
        - type: select
          name: authorityForApproval
          _condition:
            transform: $not($.extend.formType = 'view')
          label: Approved by
          placeholder: Select Approved by
          outputValue: value
          _select:
            transform: $authorityForApprovalsList()
        - type: text
          label: Signatory
          name: signatory
          _condition:
            transform: $not($.extend.formType = 'view')
          col: 2
          placeholder: Enter Signatory
          validators:
            - type: maxLength
              args: '120'
              text: Maximum 120 characters
        - type: upload
          label: Attachment
          name: attachFile
          col: 2
          upload:
            accept:
              - >-
                application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
              - application/pdf
              - application/vnd.ms-excel
            size: 5
          _condition:
            transform: $not($.extend.formType = 'view')
        - type: upload
          name: attachFileResults
          col: 2
          canAction: true
          hiddenLabel: true
          readOnly: true
          _condition:
            transform: $.extend.formType = 'edit'
    - type: group
      label: Decision Information
      collapse: false
      n_cols: 1
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - type: select
          name: action
          label: Action
          placeholder: Select Action
          outputValue: value
          _select:
            transform: $actionsList()
          _condition:
            transform: $.extend.formType = 'view'
        - type: select
          name: reason
          label: Reason
          _select:
            transform: $reasonsList()
          outputValue: value
          placeholder: Select Reason
          _condition:
            transform: $.extend.formType = 'view'
        - type: text
          label: Decision No.
          name: decisionNo
          _condition:
            transform: $.extend.formType = 'view'
          placeholder: Enter Decision No.
        - type: text
          label: Decision Name
          _condition:
            transform: $.extend.formType = 'view'
          name: decisionName
          placeholder: Enter Decision Name
        - type: dateRange
          label: Issuance Date
          name: issueDate
          mode: date-picker
          _condition:
            transform: $.extend.formType = 'view'
          setting:
            format: dd/MM/yyyy
            type: date
          placeholder: Enter Decision Name
        - type: select
          name: authorityForApproval
          _condition:
            transform: $.extend.formType = 'view'
          label: Approved by
          placeholder: Select Approved by
          outputValue: value
          _select:
            transform: $authorityForApprovalsList()
        - type: text
          label: Signatory
          name: signatory
          _condition:
            transform: $.extend.formType = 'view'
          placeholder: Enter Signatory
          validators:
            - type: maxLength
              args: '120'
              text: Maximum 120 characters
        - type: upload
          name: attachFileResults
          canAction: true
          hiddenLabel: true
          readOnly: true
          _condition:
            transform: $.extend.formType = 'edit'
        - type: upload
          label: Attachment
          name: attachFileResults
          readOnly: true
          _condition:
            transform: $.extend.formType = 'view'
  historyHeaderTitle: '''View History Department'''
  sources:
    groupsList:
      uri: '"/api/groups/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page ,'filter':
        [{'field':'search','operator':
        '$eq','value':$.search},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'code','operator':
        '$eq','value':$.code}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id': $item.id, 'code': $item.code},
        'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - effectiveDate
        - search
        - code
    locationsList:
      uri: '"/api/locations/get-dropdown-list-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'filter':
        [{'field':'search','operator':
        '$eq','value':$.search},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':$.status},{'field':'code','operator': '$eq','value':
        $.codeFilter},{'field':'DepartmentCode','operator': '$eq','value':
        $.departmentCode},{'field':'LegalEntityCode','operator': '$eq','value':
        $.LegalEntityCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id': $item.id, 'code': $item.code},
        'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - effectiveDate
        - search
        - codeFilter
        - departmentCode
        - LegalEntityCode
        - status
    departmentsListLazy:
      uri: '"/api/departments/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'filter':
        [{'field':'search','operator':
        '$eq','value':$.search},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':$.status}, {'field':'IdFilter','operator': '$ne','value':
        $.codeFilter}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value':
        {'id':$item.id,'code':$item.code,'businessUnit':{'id':$item.businessUnitId,'code':$item.businessUnitCode},'division':{'id':$item.divisionId,'code':$item.divisionCode}},
        'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - effectiveDate
        - search
        - codeFilter
        - status
    departmentsViewLazy:
      uri: '"/api/departments/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'filter':
        [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':$.status}, {'field':'codeFilter','operator':
        '$eq','value': $.codeFilter}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value':
        {'id':$item.id,'code':$item.code,'businessUnit':{'id':$item.businessUnitId,'code':$item.businessUnitCode},'division':{'id':$item.divisionId,'code':$item.divisionCode}},
        'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - effectiveDate
        - codeFilter
        - status
    divisionsListLazy:
      uri: '"/api/divisions/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'filter':
        [{'field':'search','operator':
        '$eq','value':$.search},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':$.status}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id':$item.id,'code':$item.code},
        'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - effectiveDate
        - search
        - status
    divisionsListLazyView:
      uri: '"/api/divisions/get-by"'
      method: GET
      queryTransform: >-
        {'limit': 0, 'page': 0, 'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':$.status},{'field':'code','operator':
        '$eq','value':$.code}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id':$item.id,'code':$item.code},
        'additionalData': $item}})[]
      disabledCache: true
      params:
        - effectiveDate
        - code
        - status
    legalEntitiesListLazy:
      uri: '"/api/legal-entities/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'filter':
        [{'field':'search','operator':
        '$eq','value':$.search},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':$.status}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id':$item.id,'code':$item.code},
        'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - effectiveDate
        - search
        - status
    legalEntitiesListLazyView:
      uri: '"/api/legal-entities/get-by"'
      method: GET
      queryTransform: >-
        {'limit': 0, 'page': 0, 'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':$.status},{'field':'code','operator':
        '$eq','value':$.code}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id':$item.id,'code':$item.code},
        'additionalData': $item}})[]
      disabledCache: true
      params:
        - effectiveDate
        - code
        - status
    businessUnitListLazy:
      uri: '"/api/business-units/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'filter':
        [{'field':'search','operator':
        '$eq','value':$.search},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':$.status}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id':$item.id,'code':$item.code},
        'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - effectiveDate
        - search
        - status
    businessUnitListLazyView:
      uri: '"/api/business-units/get-by"'
      method: GET
      queryTransform: >-
        {'limit': 0, 'page': 0, 'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':$.status},{'field':'code','operator':
        '$eq','value':$.code}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id':$item.id,'code':$item.code},
        'additionalData': $item}})[]
      disabledCache: true
      params:
        - effectiveDate
        - code
        - status
    costCenterList:
      uri: '"/api/cost-centers/get-dropdown-list-by"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':$.status},{'field':'code','operator': '$eq','value':
        $.code },{'field':'DepartmentCode','operator': '$eq','value':
        $.departmentCode},{'field':'LegalEntityCode','operator': '$eq','value':
        $.LegalEntityCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id':$item.id,'code':
        $item.code,'companyId':$item.companyId}, 'additionalData': $item}})[]
      disabledCache: true
      params:
        - effectiveDate
        - code
        - departmentCode
        - LegalEntityCode
        - status
    companyList:
      uri: '"/api/companies/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page ,'filter':
        [{'field':'search','operator':
        '$eq','value':$.search},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'code','operator':
        '$eq','value':$.code}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id': $item.id, 'code': $item.code},
        'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - effectiveDate
        - search
        - code
    functionsList:
      uri: '"/api/picklists/FUNCTIONORG/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        {'id':$item.id,'code': $item.code}}})
      disabledCache: true
    organizationTypesList:
      uri: '"/api/picklists/TYPEORG/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})
      disabledCache: true
    positionsList:
      uri: '"/api/positions/by"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true},{'field':'code','operator': '$eq','value':$.code}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')' , 'value': {'id':$item.id,'code': $item.code},
        'jobCode': $item.id, 'additionalData': $item}})[]
      disabledCache: true
      params:
        - effectiveDate
        - code
    positionsListMutil:
      uri: '"/api/positions/by"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default & ' (' &
        $item.department.name.default & ')' , 'value': $item.id, 'jobCode':
        $item.id, 'additionalData': $item}})[]
      disabledCache: true
      params:
        - effectiveDate
    personalsList:
      uri: '"/api/personals/all-employees-job-datas"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page,'filter':
        [{'field':'search','operator':
        '$eq','value':$.paramsName},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}, {'field':'hrStatus','operator':
        '$eq','value': 'A'}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.fullName  & ' ' &
        $item.emailsplit, 'value': {'id': $item.employeeId, 'code':
        $item.employeeId}}})[]
      disabledCache: true
      params:
        - limit
        - page
        - paramsName
        - effectiveDate
        - code
    personalsListView:
      uri: '"/api/personals/all-employees-job-datas"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page,'filter':
        [{'field':'search','operator': '$eq','value':$.paramsName},
        {'field':'effectiveDate','operator': '$eq','value':$.effectiveDate},
        {'field':'hrStatus','operator': '$eq','value': 'A'},
        {'field':'employeeId','operator': '$in','value':$.code}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.fullName  & ' ' &
        $item.emailsplit, 'value': {'id': $item.employeeId, 'code':
        $item.employeeId}}})[]
      disabledCache: true
      params:
        - limit
        - page
        - paramsName
        - effectiveDate
        - code
    actionsList:
      uri: '"/api/picklists-values/ACTIONORG"'
      method: GET
      queryTransform: '{''filter'': [{''field'':''code'',''operator'': ''$eq'',''value'':$.codeAction}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code, 'id':$item.id }})[]
      disabledCache: true
      params:
        - codeAction
    reasonsList:
      uri: '"/api/picklists/REASONORG/values"'
      method: GET
      queryTransform: '{''filter'': [{''field'':''col501'',''operator'': ''$eq'',''value'':$.codeAction}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - codeAction
    authorityForApprovalsList:
      uri: '"/api/picklists/CAPQDORG/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})
      disabledCache: true
  variables:
    _reasonList:
      transform: $reasonsList($.variables._actionId)
    _actionId:
      transform: $.variables._selectAction.id
    _selectAction:
      transform: $filter($actionsList(),function ($v){ $v.value = $.fields.action })
  _mode:
    transform: $not($.extend.formType = 'view') ? 'mark-scroll'
filter_config:
  fields:
    - type: text
      label: Department Code
      name: code
      labelType: type-grid
      placeholder: Enter Department Code
    - type: radio
      label: Status
      name: status
      labelType: type-grid
      value: ''
      radio:
        - value: ''
          label: All
        - value: true
          label: Active
        - value: false
          label: Inactive
    - type: text
      label: Short Name
      name: shortName
      labelType: type-grid
      placeholder: Enter Short Name
    - type: text
      label: Long Name
      name: longName
      labelType: type-grid
      placeholder: Enter Long Name
    - type: selectAll
      label: Parent Department
      labelType: type-grid
      name: parentDepartmentCode
      isLazyLoad: true
      placeholder: Select Parent Department
      _options:
        transform: $departmentsList($.extend.limit,$.extend.page,$.extend.search)
    - type: selectAll
      label: Legal Entity
      name: legalEntityCode
      isLazyLoad: true
      labelType: type-grid
      placeholder: Select Legal Entity
      _options:
        transform: $legalEntitiesList($.extend.limit,$.extend.page,$.extend.search)
    - type: selectAll
      name: businessUnitCode
      isLazyLoad: true
      label: Business Unit
      labelType: type-grid
      placeholder: Select Business Unit
      _options:
        transform: $businessUnitList($.extend.limit,$.extend.page,$.extend.search)
    - type: selectAll
      name: divisionCode
      isLazyLoad: true
      label: Division
      labelType: type-grid
      placeholder: Select Division
      _options:
        transform: $divisionsList($.extend.limit,$.extend.page,$.extend.search)
  filterMapping:
    - field: code
      operator: $cont
      valueField: code
    - field: na_shortName
      operator: $cont
      valueField: shortName
    - field: na_longName
      operator: $cont
      valueField: longName
    - field: legalEntityCode
      operator: $in
      valueField: legalEntityCode.(value)
    - field: businessUnitCode
      operator: $in
      valueField: businessUnitCode.(value)
    - field: divisionCode
      operator: $in
      valueField: divisionCode.(value)
    - field: parentDepartmentCode
      operator: $in
      valueField: parentDepartmentCode.(value)
    - field: status
      operator: $eq
      valueField: status
  sources:
    legalEntitiesList:
      uri: '"/api/legal-entities/v2/get-list"'
      method: GET
      queryTransform: >-
        {'limit':$.limit,'page':$.page,'filter': [{'field':'search','operator':
        '$eq','value':$.search}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    divisionsList:
      uri: '"/api/divisions/v2/get-list"'
      method: GET
      queryTransform: >-
        {'limit':$.limit,'page':$.page,'filter': [{'field':'search','operator':
        '$eq','value':$.search}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    businessUnitList:
      uri: '"/api/business-units/v2/get-list"'
      method: GET
      queryTransform: >-
        {'limit':$.limit,'page':$.page,'filter': [{'field':'search','operator':
        '$eq','value':$.search}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    departmentsList:
      uri: '"/api/departments/v2/get-list"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'filter':
        [{'field':'search','operator': '$eq','value':$.search}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value':$item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
layout_options:
  is_upload_file: true
  toolTable:
    export: true
    import: true
    adjustDisplay: true
  tool_table:
    - id: import
      icon: icon-download-simple-bold
      href: /GE/HR.FS.FR.092
    - id: export
      icon: icon-upload-simple-bold
  view_history_after_created: true
  delete_multi_items: true
  custom_history_backend_url: /api/departments/insert-new-record
  is_new_dynamic_form: false
  hide_action_row: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    title: Edit
    icon: icon-pencil-simple-bold
    type: ghost-gray
  - id: duplicate
    icon: icon-copy-bold
    type: ghost-gray
  - id: delete
    title: Delete
    icon: icon-trash-bold
    group: null
    type: ghost-gray
backend_url: /api/departments
screen_name: null
layout_options__actions_many: null
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key: null
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Department
  parent:
    title: Organization Structure
