id: FO.FS.FR.015
status: draft
sort: 185
user_created: 56e71b91-dd39-4ebc-afd1-b4f00f38d4a5
date_created: '2024-06-13T06:50:13.054Z'
user_updated: 35b4f590-f747-4e53-8fa5-76aba65555c2
date_updated: '2025-07-19T02:26:44.756Z'
title: Business Unit
requirement:
  time: 1749005245277
  blocks:
    - id: CAeDRg8a6R
      type: paragraph
      data:
        text: note
  version: 2.30.7
screen_design: null
module: FO
local_fields:
  - code: code
    pinned: true
    title: Business Unit Code
    data_type:
      key: Employee Code
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    show_sort: true
  - code: shortName
    title: Short Name
    data_type:
      key: Organization-1
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: longName
    title: Long Name
    data_type:
      key: Organization-2
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: companyName
    title: Company
    data_type:
      key: Organization-3
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: parentName
    title: Parent Business Unit
    description: Parent Business Unit
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: status
    title: Status
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: Boolean Tag
      collection: field_types
    show_sort: true
mock_data:
  - businessUnitCode: '00000001'
    shortName:
      default: GST
      vietnamese: GST
      english: GST
    longName:
      default: ĐV GP Công nghệ thông minh toàn cầu
      vietnamese: ĐV GP Công nghệ thông minh toàn cầu
      english: ĐV GP Công nghệ thông minh toàn cầu
    effectiveDate: 01/05/2024
    status: true
    company: FSOFT
  - businessUnitCode: '00000002'
    shortName:
      default: FGC
      vietnamese: FGC
      english: FGC
    longName:
      default: Ban chuyển đổi ngôn ngữ FSOFT
      vietnamese: Ban chuyển đổi ngôn ngữ FSOFT
      english: Ban chuyển đổi ngôn ngữ FSOFT
    effectiveDate: 01/05/2024
    status: true
    company: FSOFT
  - businessUnitCode: '00000003'
    shortName:
      default: FQC
      vietnamese: FQC
      english: FQC
    longName:
      default: Ban Đảm bảo Chất lượng
      vietnamese: Ban Đảm bảo Chất lượng
      english: Ban Đảm bảo Chất lượng
    effectiveDate: 01/05/2024
    status: true
    company: FSOFT
  - businessUnitCode: '00000004'
    shortName:
      default: SEPG
      vietnamese: SEPG
      english: SEPG
    longName:
      default: Ban Quy trình sản xuất PM
      vietnamese: Ban Quy trình sản xuất PM
      english: Ban Quy trình sản xuất PM
    effectiveDate: 01/05/2024
    status: true
    company: FSOFT
  - businessUnitCode: '00000005'
    shortName:
      default: FSG
      vietnamese: FSG
      english: FSG
    longName:
      default: ĐVKD Fin service Gr
      vietnamese: ĐVKD Fin service Gr
      english: ĐVKD Fin service Gr
    effectiveDate: 01/05/2024
    status: true
    company: FSOFT
  - businessUnitCode: '00000006'
    shortName:
      default: TDT
      vietnamese: TDT
      english: TDT
    longName:
      default: Đơn vị dữ liệu minh bạch
      vietnamese: Đơn vị dữ liệu minh bạch
      english: Đơn vị dữ liệu minh bạch
    effectiveDate: 01/05/2024
    status: true
    company: FSOFT
  - businessUnitCode: '00000007'
    shortName:
      default: FHO FWA
      vietnamese: FHO FWA
      english: FHO FWA
    longName:
      default: Ban Đảm bảo nguồn lực
      vietnamese: Ban Đảm bảo nguồn lực
      english: Ban Đảm bảo nguồn lực
    effectiveDate: 01/05/2024
    status: true
    company: FSOFT
  - businessUnitCode: '00000008'
    shortName:
      default: FHO MCP
      vietnamese: FHO MCP
      english: FHO MCP
    longName:
      default: Ban Marketing
      vietnamese: Ban Marketing
      english: Ban Marketing
    effectiveDate: 01/05/2024
    status: true
    company: FSOFT
  - businessUnitCode: '00000009'
    shortName:
      default: DXG
      vietnamese: DXG
      english: DXG
    longName:
      default: Đơn vị Chuyển đổi số toàn cầu
      vietnamese: Đơn vị Chuyển đổi số toàn cầu
      english: Đơn vị Chuyển đổi số toàn cầu
    effectiveDate: 01/05/2024
    status: true
    company: FSOFT
  - businessUnitCode: '00000010'
    shortName:
      default: IVS
      vietnamese: IVS
      english: IVS
    longName:
      default: ĐV GP và DV kiểm thử PM ĐL
      vietnamese: ĐV GP và DV kiểm thử PM ĐL
      english: ĐV GP và DV kiểm thử PM ĐL
    effectiveDate: 01/05/2024
    status: true
    company: FSOFT
local_buttons: null
layout: layout-table
form_config:
  formSize:
    create: large
    edit: large
    proceed: large
  fields:
    - type: group
      label: Basic Information
      n_cols: 2
      collapse: false
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - type: text
          label: Business Unit Code
          name: code
          placeholder: Enter BusinessUnit Code
          _disabled:
            transform: $not($.extend.formType = 'create')
          validators:
            - type: required
            - type: maxLength
              args: '20'
              text: Code should not exceed 20 characters
            - type: pattern
              args: ^[a-zA-Z0-9_.&]+$
              text: The Code must not contain special characters.
          _condition:
            transform: $not($.extend.formType = 'view')
        - type: dateRange
          label: Effective Date
          name: effectiveDate
          mode: date-picker
          placeholder: dd/mm/yyyy
          setting:
            format: dd/MM/yyyy
            type: date
          _value:
            transform: $.extend.formType = 'create' ? $now()
          validators:
            - type: required
          _condition:
            transform: $not($.extend.formType = 'view')
        - type: radio
          label: Status
          name: status
          _value:
            transform: $.extend.formType = 'create' and $not($.extend.isDuplicate) ? true
          radio:
            - value: true
              label: Active
            - value: false
              label: Inactive
          _condition:
            transform: $not($.extend.formType = 'view')
        - type: translation
          label: Short Name
          name: shortName
          placeholder: Enter Short Name
          _condition:
            transform: $not($.extend.formType = 'view')
          validators:
            - type: maxLength
              args: '120'
              text: Long Name should not exceed 120 characters
        - type: translation
          label: Long Name
          name: longName
          col: 2
          placeholder: Enter Long Name
          validators:
            - type: required
            - type: maxLength
              args: '120'
              text: Long Name should not exceed 120 characters
          _condition:
            transform: $not($.extend.formType = 'view')
        - type: select
          label: Function
          name: function
          outputValue: value
          placeholder: Select Function
          _condition:
            transform: $not($.extend.formType = 'view')
          _select:
            transform: $functionsList()
        - type: select
          label: Org Type
          name: orgType
          outputValue: value
          placeholder: Select Org Type
          _select:
            transform: $organizationTypesList()
          _condition:
            transform: $not($.extend.formType = 'view')
        - type: textarea
          label: Responsibility
          _condition:
            transform: $not($.extend.formType = 'view')
          name: responsibility
          col: 2
          placeholder: Enter Responsibility
          validators:
            - type: maxLength
              args: '4000'
              text: Maximum 4000 characters.
          textarea:
            autoSize:
              minRows: 3
              maxRows: 5
            maxCharCount: 4000
    - type: group
      label: Basic Information
      n_cols: 1
      collapse: false
      _condition:
        transform: $.extend.formType = 'view'
      fieldGroupTitleStyle:
        border: none
      fields:
        - type: text
          label: Business Unit Code
          name: code
          _condition:
            transform: $.extend.formType = 'view'
        - type: dateRange
          label: Effective Date
          name: effectiveDate
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
          _condition:
            transform: $.extend.formType = 'view'
        - type: radio
          label: Status
          name: status
          value: true
          radio:
            - value: true
              label: Active
            - value: false
              label: Inactive
          _condition:
            transform: $.extend.formType = 'view'
        - type: translation
          label: Short Name
          name: shortName
          placeholder: Enter Short Name
          _condition:
            transform: $.extend.formType = 'view'
        - type: translation
          label: Long Name
          name: longName
          placeholder: Enter Long Name
          _condition:
            transform: $.extend.formType = 'view'
        - type: select
          label: Org Type
          name: orgType
          outputValue: value
          placeholder: Select Org Type
          _select:
            transform: $organizationTypesList()
          _condition:
            transform: $.extend.formType = 'view'
        - type: select
          label: Function
          name: function
          outputValue: value
          placeholder: Select Function
          _condition:
            transform: $.extend.formType = 'view'
          _select:
            transform: $functionsList()
        - type: textarea
          label: Responsibility
          _condition:
            transform: $.extend.formType = 'view'
          name: responsibility
          placeholder: Responsibility
          textarea:
            autoSize:
              minRows: 3
            maxCharCount: 4000
    - type: group
      label: Associations
      n_cols: 2
      collapse: false
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - type: select
          name: AssociationsType1
          label: Management Association Type
          placeholder: Select Management Association Type
          outputValue: value
          _condition:
            transform: $not($.extend.formType = 'view')
          _value:
            transform: >-
              $isNilorEmpty($.extend.defaultValue.parentBusinessUnits.value.code)
          select:
            - label: Company
              value: true
            - label: Parent Business Unit
              value: false
        - type: select
          name: companyObj
          label: Company
          isLazyLoad: true
          dependantField: $.fields.AssociationsType1
          outputValue: value
          _condition:
            transform: >-
              $not($.extend.formType = 'view') and $.fields.AssociationsType1 =
              true
          _class:
            transform: '$.fields.AssociationsType1 = true ? ''required'': ''unrequired'''
          placeholder: Select Company
          _validateFn:
            transform: >-
              $exists($.value.code) ?
              ($companyList(0,0,$.fields.effectiveDate,$.value.code,($not($.extend.formType
              = 'edit') or $not($DateFormat($.fields.effectiveDate,
              'yyyy-MM-DD') = $DateFormat($.extend.defaultValue.effectiveDate,
              'yyyy-MM-DD'))) ? true)[0] ?
              $companyList(0,0,$.fields.effectiveDate,$.value.code,($not($.extend.formType
              = 'edit') or $not($DateFormat($.fields.effectiveDate,
              'yyyy-MM-DD') = $DateFormat($.extend.defaultValue.effectiveDate,
              'yyyy-MM-DD'))) ? true)[0] : '_setSelectValueNull')
          _select:
            transform: >-
              $companyList($.extend.limit,$.extend.page,$.fields.effectiveDate,null,
              true,$.extend.search)
        - type: select
          name: parentBusinessUnits
          dependantField: $.fields.AssociationsType1
          label: Parent Business Unit
          _condition:
            transform: >-
              $not($.extend.formType = 'view') and $.fields.AssociationsType1 =
              false
          _class:
            transform: '$.fields.AssociationsType1 = false ? ''required'': ''unrequired'''
          isLazyLoad: true
          placeholder: Select Parent Business Unit
          outputValue: value
          inputValue: code
          _validateFn:
            transform: >-
              $exists($.value.code) ?
              ($businessUnitListViewLazy(0,0,$.fields.effectiveDate,$.value.code,($not($.extend.formType
              = 'edit') or $not($DateFormat($.fields.effectiveDate,
              'yyyy-MM-DD') = $DateFormat($.extend.defaultValue.effectiveDate,
              'yyyy-MM-DD'))) ? true)[0] ?
              $businessUnitListViewLazy(0,0,$.fields.effectiveDate,$.value.code,($not($.extend.formType
              = 'edit') or $not($DateFormat($.fields.effectiveDate,
              'yyyy-MM-DD') = $DateFormat($.extend.defaultValue.effectiveDate,
              'yyyy-MM-DD'))) ? true)[0] : '_setSelectValueNull')
          _select:
            transform: >-
              $businessUnitListLazy($.extend.limit,
              $.extend.page,$.fields.effectiveDate,$.extend.defaultValue.id,$.extend.search,true)
    - type: group
      label: Associations
      n_cols: 1
      _condition:
        transform: $.extend.formType = 'view'
      collapse: false
      fields:
        - type: selectCustom
          name: companyObj
          label: Company
          outputValue: value
          inputValue: code
          _condition:
            transform: $.extend.formType = 'view'
          placeholder: Select Company
          _select:
            transform: >-
              $companyList($.extend.limit,$.extend.page,$.fields.effectiveDate,$.extend.defaultValue.companyObj.value.code)
          _validateFn:
            transform: >-
              $exists($test($.extend.defaultValue.companyObj.value.code)) ?
              ($companyList(0,0,$.fields.effectiveDate,$.extend.defaultValue.companyObj.value.code)[0]
              ?
              $companyList(0,0,$.fields.effectiveDate,$.extend.defaultValue.companyObj.value.code)[0]
              : '_setSelectValueNull')
          actions:
            - view
          actionsConfig:
            view:
              formConfig:
                fields:
                  - name: code
                    label: Company Code
                    type: text
                  - type: dateRange
                    label: Effective Date
                    name: effectiveDate
                    mode: date-picker
                    setting:
                      format: dd/MM/yyyy
                      type: date
                  - name: status
                    label: Status
                    type: radio
                    radio:
                      - label: Active
                        value: true
                      - label: Inactive
                        value: false
                  - name: shortName
                    label: Short Name
                    type: translation
                  - name: longName
                    label: Long Name
                    type: translation
        - type: selectCustom
          name: parentBusinessUnits
          label: Parent Business Unit
          _condition:
            transform: $.extend.formType = 'view'
          placeholder: Select Parent Business Unit
          outputValue: value
          inputValue: code
          _validateFn:
            transform: >-
              $exists($.extend.defaultValue.parentBusinessUnits.value.code) ?
              ($businessUnitListViewLazy(0,0,$.fields.effectiveDate,$.extend.defaultValue.parentBusinessUnits.value.code)[0]
              ?
              $businessUnitListViewLazy(0,0,$.fields.effectiveDate,$.extend.defaultValue.parentBusinessUnits.value.code)[0]
              : '_setSelectValueNull')
          _select:
            transform: >-
              $businessUnitListViewLazy($.extend.limit,
              $.extend.page,$.fields.effectiveDate,$.extend.defaultValue.parentBusinessUnits.value.code)
          actions:
            - view
          actionsConfig:
            view:
              formConfig:
                fields:
                  - name: code
                    label: Business Code
                    type: text
                  - type: dateRange
                    label: Effective Date
                    name: effectiveDate
                    mode: date-picker
                    setting:
                      format: dd/MM/yyyy
                      type: date
                  - name: status
                    label: Status
                    type: radio
                    radio:
                      - label: Active
                        value: true
                      - label: Inactive
                        value: false
                  - name: shortName
                    label: Short Name
                    type: translation
                  - name: longName
                    label: Long Name
                    type: translation
    - type: group
      label: Manager of Business Unit
      n_cols: 2
      collapse: false
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - type: select
          name: managerType
          label: Manager Type
          outputValue: value
          _condition:
            transform: $not($.extend.formType = 'view')
          clearFieldsAfterChange:
            - headOfBusinessUnitObj
            - deputyManagerObj
            - managerPosition
            - deputyManagerPosition
          placeholder: Select Manager Type
          select:
            - label: Employee
              value: Employee
            - label: Position
              value: Position
        - type: select
          name: headOfBusinessUnitObj
          dependantField: $.fields.managerType
          label: Head of Business Unit
          isLazyLoad: true
          placeholder: Select Head of Business Unit
          outputValue: value
          _validateFn:
            transform: >-
              $exists($.value.code) ?
              ($personalsListView($.extend.limit,$.extend.page,null,$.fields.effectiveDate,$.value.code)[0]
              ?
              $personalsListView($.extend.limit,$.extend.page,null,$.fields.effectiveDate,$.value.code)[0]
              : '_setSelectValueNull')
          _condition:
            transform: >-
              $not($.extend.formType = 'view') and $.fields.managerType =
              'Employee'
          _select:
            transform: >-
              $personalsList($.extend.limit,$.extend.page,$.extend.search,
              $.fields.effectiveDate)
        - type: selectAll
          name: deputyManagerObj
          label: Deputy Manager
          dependantField: $.fields.managerType
          isLazyLoad: true
          col: 2
          placeholder: Select Deputy Manager
          outputValue: value
          _condition:
            transform: >-
              $not($.extend.formType = 'view') and $.fields.managerType =
              'Employee'
          _validateFn:
            transform: >-
              $exists($.value.code) ?
              ($personalsListView($.extend.limit,$.extend.page,null,$.fields.effectiveDate,$.value.code)[0]
              ?
              $personalsListView($.extend.limit,$.extend.page,null,$.fields.effectiveDate,$.value.code)
              : '_setSelectValueNull')
          _options:
            transform: >-
              $personalsList($.extend.limit,$.extend.page,$.extend.search,$.fields.effectiveDate)
        - type: select
          name: managerPosition
          dependantField: $.fields.managerType
          label: Manager Position
          placeholder: Select Manager Position
          outputValue: value.id
          inputValue: code
          isClearValue: true
          isLazyLoad: true
          _condition:
            transform: >-
              $not($.extend.formType = 'view') and $.fields.managerType =
              'Position'
          _select:
            transform: >-
              $positionsList($.extend.limit,$.extend.page,$.fields.effectiveDate)
        - type: selectAll
          name: deputyManagerPosition
          dependantField: $.fields.managerType
          col: 2
          label: Deputy Manager Position
          placeholder: Select Deputy Manager Position
          outputValue: value.id
          inputValue: code
          isClearValue: true
          isLazyLoad: true
          _condition:
            transform: >-
              $not($.extend.formType = 'view') and $.fields.managerType =
              'Position'
          _options:
            transform: >-
              $positionsList($.extend.limit,$.extend.page,$.fields.effectiveDate)
    - type: group
      label: Manager of Business Unit
      n_cols: 1
      collapse: false
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - type: select
          name: managerType
          label: Manager Type
          outputValue: value
          _condition:
            transform: $.extend.formType = 'view'
          placeholder: Select Manager Type
          select:
            - label: Employee
              value: Employee
            - label: Position
              value: Position
        - type: select
          name: headOfBusinessUnitObj
          label: Head of Business Unit
          placeholder: Select Head of Business Unit
          outputValue: value
          inputValue: code
          _condition:
            transform: $.fields.managerType = 'Employee' and $.extend.formType = 'view'
          _select:
            transform: >-
              $personalsListView($.extend.limit,$.extend.page,null,$.fields.effectiveDate,$.fields.headOfBusinessUnitObj.value.code)
        - type: select
          name: deputyManagerObj
          label: Deputy Manager
          mode: multiple
          placeholder: Select Deputy Manager
          outputValue: value
          inputValue: code
          _condition:
            transform: $.fields.managerType = 'Employee'  and $.extend.formType = 'view'
          _options:
            transform: >-
              $personalsListView($.extend.limit,$.extend.page,null,$.fields.effectiveDate,$.fields.deputyManagerObj.value.code)
        - type: select
          name: managerPosition
          label: Manager Position
          placeholder: Select Manager Position
          outputValue: value.id
          inputValue: code
          isLazyLoad: true
          _condition:
            transform: $.fields.managerType = 'Position' and $.extend.formType = 'view'
          _select:
            transform: >-
              $positionsList($.extend.limit,$.extend.page,$.fields.effectiveDate)
        - type: select
          name: deputyManagerPosition
          label: Deputy Manager Position
          placeholder: Select Deputy Manager Position
          outputValue: value.id
          inputValue: code
          mode: multiple
          isLazyLoad: true
          _condition:
            transform: $.fields.managerType = 'Position' and $.extend.formType = 'view'
          _select:
            transform: >-
              $positionsList($.extend.limit,$.extend.page,$.fields.effectiveDate)
    - type: group
      label: Former Organization
      collapse: false
      _condition:
        transform: $not($.extend.formType = 'view')
      disableEventCollapse: false
      fields:
        - type: array
          mode: table
          name: orgObjects
          minSize: 1
          arrayOptions:
            canChangeSize: true
          field:
            type: group
            name: group
            fields:
              - type: select
                name: type
                placeholder: Org Object
                label: Former Organization
                outputValue: value
                width: 250px
                select:
                  - label: Group
                    value: '1'
                  - label: Company
                    value: '2'
                  - label: Legal Entity
                    value: '3'
                  - label: Business Unit
                    value: '4'
                  - label: Division
                    value: '5'
                  - label: Department
                    value: '6'
                clearFieldsAfterChange:
                  - id
                  - objData
                  - code
              - type: selectCustom
                name: objData
                placeholder: Select Org Object
                isLazyLoad: true
                outputValue: value
                width: 300px
                _validateFn:
                  transform: >-
                    $exists($.fields.orgObjects[$index].code) ?
                    ($getFieldGroup($.extend.path,$.fields,1).type = '1' ?
                    $groupsList($.extend.limit,$.extend.page,$.fields.effectiveDate,$.fields.orgObjects[$index].code)[0]
                    : $getFieldGroup($.extend.path,$.fields,1).type = '2' ?
                    $companyList($.extend.limit,$.extend.page,$.fields.effectiveDate,$.fields.orgObjects[$index].code)[0]
                    : $getFieldGroup($.extend.path,$.fields,1).type = '3' ?
                    $legalEntitiesList($.extend.limit,$.extend.page,$.fields.effectiveDate,$.fields.orgObjects[$index].code)[0]
                    : $getFieldGroup($.extend.path,$.fields,1).type = '4' ?
                    $businessUnitListViewLazy($.extend.limit,$.extend.page,$.fields.effectiveDate,$.fields.orgObjects[$index].code)[0]
                    :  $getFieldGroup($.extend.path,$.fields,1).type = '5' ?
                    $divisionsList($.extend.limit,$.extend.page,$.fields.effectiveDate,$.fields.orgObjects[$index].code)[0]
                    : $getFieldGroup($.extend.path,$.fields,1).type = '6' ?
                    $departmentsList($.extend.limit,$.extend.page,$.fields.effectiveDate,$.fields.orgObjects[$index].code)[0]
                    : '_setSelectValueNull')
                  dependants:
                    - $.fields.orgObjects[$index].type
                  params:
                    $index: $.extend.path[-1]
                _select:
                  dependants:
                    - $.fields.orgObjects[$index].type
                    - $.extend.page
                    - $.extend.search
                  params:
                    $index: $.extend.path[-1]
                  transform: >-
                    $getFieldGroup($.extend.path,$.fields,1).type = '1' ?
                    $groupsList($.extend.limit,$.extend.page,$.fields.effectiveDate,null,$.extend.search)
                    : $getFieldGroup($.extend.path,$.fields,1).type = '2' ?
                    $companyList($.extend.limit,$.extend.page,$.fields.effectiveDate,null,null,$.extend.search)
                    : $getFieldGroup($.extend.path,$.fields,1).type = '3' ?
                    $legalEntitiesList($.extend.limit,$.extend.page,$.fields.effectiveDate,null,$.extend.search)
                    : $getFieldGroup($.extend.path,$.fields,1).type = '4' ?
                    $businessUnitListLazy($.extend.limit,$.extend.page,$.fields.effectiveDate,null,$.extend.search)
                    : $getFieldGroup($.extend.path,$.fields,1).type = '5' ?
                    $divisionsList($.extend.limit,$.extend.page,$.fields.effectiveDate,null,$.extend.search)
                    : $getFieldGroup($.extend.path,$.fields,1).type = '6' ?
                    $departmentsList($.extend.limit,$.extend.page,$.fields.effectiveDate,null,$.extend.search)
                    : null
                actions:
                  - view
                actionsConfig:
                  view:
                    formConfig:
                      fields:
                        - name: code
                          label: Code
                          type: text
                        - type: dateRange
                          label: Effective Date
                          name: effectiveDate
                          mode: date-picker
                          setting:
                            format: dd/MM/yyyy
                            type: date
                        - name: status
                          label: Status
                          type: radio
                          radio:
                            - label: Active
                              value: true
                            - label: Inactive
                              value: false
                        - name: shortName
                          label: Short Name
                          type: translation
                          placeholder: Enter Short Name
                        - name: longName
                          label: Long Name
                          type: translation
                validators:
                  - id: check_duplicate
                    type: ppx-custom
                    args:
                      transform: >-
                        $not($isNilorEmpty($.value)) ?
                        $count($map($.fields.orgObjects, function($item, $index)
                        {($item.id = $.value and $item.type =
                        $.fields.orgObjects[$index].type) ? {}})) > 1
                    text: Former organiztion has been duplicated
                  - id: check_null
                    type: ppx-custom
                    args:
                      transform: >-
                        $not($isNilorEmpty($getFieldGroup($.extend.path,$.fields,1).type))
                        and $isNilorEmpty($.value)
                    text: Cannot be empty
              - type: text
                name: id
                unvisible: true
                _value:
                  transform: $getFieldGroup($.extend.path,$.fields,1).objData.id
              - type: text
                name: code
                unvisible: true
                _value:
                  transform: $getFieldGroup($.extend.path,$.fields,1).objData.code
    - type: group
      collapse: false
      label: Former Organization
      _condition:
        transform: $.extend.formType = 'view'
      disableEventCollapse: false
      fields:
        - type: array
          mode: table
          name: orgObjects
          arrayOptions:
            canChangeSize: true
          field:
            type: group
            name: group
            fields:
              - type: select
                name: type
                placeholder: Org Object
                label: Former Organization
                outputValue: value
                width: 192px
                select:
                  - label: Group
                    value: '1'
                  - label: Company
                    value: '2'
                  - label: Legal Entity
                    value: '3'
                  - label: Business Unit
                    value: '4'
                  - label: Division
                    value: '5'
                  - label: Department
                    value: '6'
                clearFieldsAfterChange:
                  - id
              - type: selectCustom
                name: id
                placeholder: Select Org Object
                outputValue: value
                _select:
                  dependants:
                    - $.fields.orgObjects[$index].type
                  params:
                    $index: $.extend.path[-1]
                  transform: >-
                    $getFieldGroup($.extend.path,$.fields,1).type = '1' ?
                    $groupsList($.extend.limit,$.extend.page,$.fields.effectiveDate,$getFieldGroup($.extend.path,$.fields,1).code)
                    : $getFieldGroup($.extend.path,$.fields,1).type = '2' ?
                    $companyList($.extend.limit,$.extend.page,$.fields.effectiveDate,$getFieldGroup($.extend.path,$.fields,1).code)
                    : $getFieldGroup($.extend.path,$.fields,1).type = '3' ?
                    $legalEntitiesList($.extend.limit,$.extend.page,$.fields.effectiveDate,$getFieldGroup($.extend.path,$.fields,1).code)
                    : $getFieldGroup($.extend.path,$.fields,1).type = '4' ?
                    $businessUnitListViewLazy($.extend.limit,$.extend.page,$.fields.effectiveDate,$getFieldGroup($.extend.path,$.fields,1).code)
                    :  $getFieldGroup($.extend.path,$.fields,1).type = '5' ?
                    $divisionsList($.extend.limit,$.extend.page,$.fields.effectiveDate,$getFieldGroup($.extend.path,$.fields,1).code)
                    : $getFieldGroup($.extend.path,$.fields,1).type = '6' ?
                    $departmentsList($.extend.limit,$.extend.page,$.fields.effectiveDate,$getFieldGroup($.extend.path,$.fields,1).code)
                    : '_setSelectValueNull'
                actions:
                  - view
                actionsConfig:
                  view:
                    formConfig:
                      fields:
                        - name: code
                          label: Code
                          type: text
                        - type: dateRange
                          label: Effective Date
                          name: effectiveDate
                          mode: date-picker
                          setting:
                            format: dd/MM/yyyy
                            type: date
                        - name: status
                          label: Status
                          type: radio
                          radio:
                            - label: Active
                              value: true
                            - label: Inactive
                              value: false
                        - name: shortName
                          label: Short Name
                          type: translation
                          placeholder: Enter Short Name
                        - name: longName
                          label: Long Name
                          type: translation
              - type: text
                name: code
                unvisible: true
    - type: group
      label: Decision Information
      collapse: false
      n_cols: 2
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - type: select
          name: action
          label: Action
          placeholder: Select Action
          outputValue: value
          _select:
            transform: >-
              $.extend.formType = 'create' ? $actionsList('ACTIONORG_001') :
              $actionsList()
          _condition:
            transform: $not($.extend.formType = 'view')
        - type: select
          name: reason
          label: Reason
          dependantField: $.fields.action
          dependantFieldSkip: 2
          _select:
            transform: $.fields.action ? $.variables._reasonList
          outputValue: value
          placeholder: Select Reason
          _condition:
            transform: $not($.extend.formType = 'view')
        - type: text
          label: Decision No.
          name: decisionNo
          placeholder: Enter Decision No.
          _condition:
            transform: $not($.extend.formType = 'view')
        - type: text
          label: Decision Name
          name: decisionName
          placeholder: Enter Decision Name
          _condition:
            transform: $not($.extend.formType = 'view')
        - type: dateRange
          label: Issuance Date
          name: issueDate
          placeholder: dd/mm/yyyy
          mode: date-picker
          _condition:
            transform: $not($.extend.formType = 'view')
        - type: select
          name: authorityForApproval
          label: Approved By
          placeholder: Select Approved By
          outputValue: value
          _condition:
            transform: $not($.extend.formType = 'view')
          _select:
            transform: $authorityForApprovalsList()
        - type: text
          label: Signatory
          name: signatory
          col: 2
          placeholder: Enter Signatory
          _condition:
            transform: $not($.extend.formType = 'view')
        - type: upload
          label: Attachment
          name: attachFile
          col: 2
          upload:
            accept:
              - >-
                application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
              - application/pdf
              - application/vnd.ms-excel
            size: 5
          _condition:
            transform: $not($.extend.formType = 'view')
        - type: upload
          name: attachFileResults
          col: 2
          readOnly: true
          canAction: true
          hiddenLabel: true
          _condition:
            transform: $.extend.formType = 'edit'
    - type: group
      label: Decision Information
      collapse: false
      n_cols: 1
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - type: select
          name: action
          label: Action
          placeholder: Select Action
          outputValue: value
          _select:
            transform: $actionsList()
          _condition:
            transform: $.extend.formType = 'view'
        - type: select
          name: reason
          label: Reason
          _select:
            transform: $reasonsList()
          outputValue: value
          placeholder: Select Reason
          _condition:
            transform: $.extend.formType = 'view'
        - type: text
          label: Decision No.
          name: decisionNo
          placeholder: Enter Decision No.
          _condition:
            transform: $.extend.formType = 'view'
        - type: text
          label: Decision Name
          name: decisionName
          placeholder: Enter Decision Name
          _condition:
            transform: $.extend.formType = 'view'
        - type: dateRange
          label: Issuance Date
          name: issueDate
          mode: date-picker
          _condition:
            transform: $.extend.formType = 'view'
        - type: select
          name: authorityForApproval
          label: Approved By
          placeholder: Select Approved By
          outputValue: value
          _condition:
            transform: $.extend.formType = 'view'
          _select:
            transform: $authorityForApprovalsList()
        - type: text
          label: Signatory
          name: signatory
          placeholder: Enter Signatory
          _condition:
            transform: $.extend.formType = 'view'
        - type: upload
          label: Attachment
          name: attachFileResults
          readOnly: true
          _condition:
            transform: $.extend.formType = 'view'
  historyHeaderTitle: '''View History Business Unit'''
  sources:
    companyList:
      uri: '"/api/companies/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page ,'filter':
        [{'field':'search','operator':
        '$eq','value':$.search},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':$.status},{'field':'code','operator':
        '$eq','value':$.code}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id': $item.id, 'code': $item.code},
        'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - effectiveDate
        - code
        - status
        - search
    groupsList:
      uri: '"/api/groups/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page ,'filter':
        [{'field':'search','operator':
        '$eq','value':$.search},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'code','operator':
        '$eq','value':$.code}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id': $item.id, 'code': $item.code},
        'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - effectiveDate
        - code
        - search
    departmentsList:
      uri: '"/api/departments/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'filter':
        [{'field':'search','operator':
        '$eq','value':$.search},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'code','operator':
        '$eq','value':$.code}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id': $item.id, 'code': $item.code} ,
        'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - effectiveDate
        - code
        - search
    divisionsList:
      uri: '"/api/divisions/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page ,'filter':
        [{'field':'search','operator':
        '$eq','value':$.search},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'code','operator':
        '$eq','value':$.code}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id': $item.id, 'code':
        $item.code},'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - effectiveDate
        - code
        - search
    legalEntitiesList:
      uri: '"/api/legal-entities/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page ,'filter':
        [{'field':'search','operator':
        '$eq','value':$.search},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'code','operator':
        '$eq','value':$.code}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id': $item.id, 'code':
        $item.code},'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - effectiveDate
        - code
        - search
    businessUnitListLazy:
      uri: '"/api/business-units/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'filter':
        [{'field':'search','operator':
        '$eq','value':$.search},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':$.status}, {'field':'IdFilter','operator': '$ne','value':
        $.codeFilter}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id': $item.id, 'code': $item.code},
        'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - effectiveDate
        - codeFilter
        - search
        - status
    businessUnitListViewLazy:
      uri: '"/api/business-units/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'filter':
        [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':$.status}, {'field':'code','operator': '$eq','value':
        $.codeFilter}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id':$item.id,'code':$item.code},
        'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - effectiveDate
        - codeFilter
        - status
    functionsList:
      uri: '"/api/picklists/FUNCTIONORG/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})
      disabledCache: true
    organizationTypesList:
      uri: '"/api/picklists/TYPEORG/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})
      disabledCache: true
    positionsList:
      uri: '"/api/positions/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'filter':
        [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item, 'jobCode': $item.id, 'additionalData':
        $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - effectiveDate
    personalsList:
      uri: '"/api/personals/all-employees-job-datas"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page,'filter':
        [{'field':'search','operator': '$eq','value':$.paramsName},
        {'field':'effectiveDate','operator': '$eq','value':$.effectiveDate},
        {'field':'hrStatus','operator': '$eq','value': 'A'}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.fullName  & ' ' &
        $item.emailsplit, 'value': {'id': $item.employeeId, 'code':
        $item.employeeId}}})[]
      disabledCache: true
      params:
        - limit
        - page
        - paramsName
        - effectiveDate
        - code
    personalsListView:
      uri: '"/api/personals/all-employees-job-datas"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page,'filter':
        [{'field':'search','operator': '$eq','value':$.paramsName},
        {'field':'effectiveDate','operator': '$eq','value':$.effectiveDate},
        {'field':'hrStatus','operator': '$eq','value': 'A'},
        {'field':'employeeId','operator': '$in','value':$.code}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.fullName  & ' ' &
        $item.emailsplit, 'value': {'id': $item.employeeId, 'code':
        $item.employeeId}}})[]
      disabledCache: true
      params:
        - limit
        - page
        - paramsName
        - effectiveDate
        - code
    actionsList:
      uri: '"/api/picklists-values/ACTIONORG"'
      method: GET
      queryTransform: '{''filter'': [{''field'':''code'',''operator'': ''$eq'',''value'':$.codeAction}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code, 'id':$item.id }})[]
      disabledCache: true
      params:
        - codeAction
    reasonsList:
      uri: '"/api/picklists/REASONORG/values"'
      method: GET
      queryTransform: '{''filter'': [{''field'':''col501'',''operator'': ''$eq'',''value'':$.codeAction}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - codeAction
    authorityForApprovalsList:
      uri: '"/api/picklists/CAPQDORG/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})
      disabledCache: true
  variables:
    _selectAction:
      transform: $filter($actionsList(),function ($v){ $v.value = $.fields.action })
    _actionId:
      transform: $.variables._selectAction.id
    _reasonList:
      transform: $reasonsList($.variables._actionId)
  _mode:
    transform: $not($.extend.formType = 'view') ? 'mark-scroll'
filter_config:
  fields:
    - type: text
      label: Business Unit Code
      placeholder: Enter Business Unit Code
      labelType: type-grid
      name: code
    - type: radio
      label: Status
      labelType: type-grid
      name: status
      value: ''
      radio:
        - value: ''
          label: All
        - value: true
          label: Active
        - value: false
          label: Inactive
    - name: shortName
      label: Short Name
      labelType: type-grid
      placeholder: Enter Short Name
      type: text
    - name: longName
      label: Long Name
      labelType: type-grid
      placeholder: Enter Long Name
      type: text
    - type: selectAll
      label: Company
      name: companyCode
      labelType: type-grid
      isLazyLoad: true
      placeholder: Select Company
      _options:
        transform: $companyList($.extend.limit,$.extend.page,$.extend.search)
    - name: parentCode
      label: Parent Business Unit
      placeholder: Select Parent Business Unit
      labelType: type-grid
      type: selectAll
      isLazyLoad: true
      _options:
        transform: $businessUnitList($.extend.limit,$.extend.page,$.extend.search)
  filterMapping:
    - field: code
      operator: $cont
      valueField: code
    - field: na_shortName
      operator: $cont
      valueField: shortName
    - field: na_longName
      operator: $cont
      valueField: longName
    - field: status
      operator: $eq
      valueField: status
    - field: companyCode
      operator: $in
      valueField: companyCode.(value)
    - field: parentCode
      operator: $in
      valueField: parentCode.(value)
  sources:
    companyList:
      uri: '"/api/companies/get-list"'
      method: GET
      queryTransform: >-
        {'limit':$.limit,'page':$.page,'filter': [{'field':'search','operator':
        '$eq','value':$.search}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    businessUnitList:
      uri: '"/api/business-units/v2/get-list"'
      method: GET
      queryTransform: >-
        {'limit':$.limit,'page':$.page,'filter': [{'field':'search','operator':
        '$eq','value':$.search}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
layout_options:
  toolTable:
    export: true
    import: true
    adjustDisplay: true
  tool_table:
    - id: import
      icon: icon-upload-simple
    - id: export
      icon: icon-download-simple
  view_after_updated: true
  view_history_after_created: true
  is_upload_file: true
  delete_multi_items: true
  hide_action_row: true
  custom_history_backend_url: /api/business-units/insert-new-record
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    title: Edit
    icon: icon-pencil-simple-bold
    type: ghost-gray
  - id: delete
    title: Delete
    icon: icon-trash-bold
    group: null
    type: ghost-gray
backend_url: /api/business-units
screen_name: null
layout_options__actions_many: null
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key: null
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Business Unit
  parent:
    title: Organization Structure
