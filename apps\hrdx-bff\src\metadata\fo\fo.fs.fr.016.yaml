id: FO.FS.FR.016
status: draft
sort: 186
user_created: 59722cb8-f858-481b-97f1-c97dd2a8e4be
date_created: '2024-06-14T03:39:22.626Z'
user_updated: 35b4f590-f747-4e53-8fa5-76aba65555c2
date_updated: '2025-07-19T02:23:25.203Z'
title: Division
requirement:
  time: 1749005281458
  blocks:
    - id: IeUdyqyzaN
      type: paragraph
      data:
        text: Quản lý thông tin Division
  version: 2.30.7
screen_design: null
module: FO
local_fields:
  - code: code
    title: Division Code
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    description: Division Code
    pinned: true
    show_sort: true
  - code: shortName
    title: Short Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    description: Short Name
    pinned: false
    show_sort: true
  - code: longName
    title: Long Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    description: Long Name
    pinned: false
    show_sort: true
  - code: businessUnitName
    title: Business Unit
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    pinned: false
    show_sort: true
  - code: parentName
    title: Parent Divison
    description: Parent Divison
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: status
    title: Status
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: Boolean Tag
      collection: field_types
    description: Status
    pinned: false
    show_sort: true
mock_data:
  - divisionCode: '00000001'
    shortName:
      default: RPM FIN
      vietnamese: RPM FIN
      english: RPM FIN
    longName:
      default: RPM FIN - Financial
      vietnamese: RPM FIN - Financial
      english: RPM FIN - Financial
    effectiveDate: '2023-07-15'
    status: true
    businessUnit: null
  - divisionCode: '00000002'
    shortName:
      default: RPM LOC
      vietnamese: RPM LOC
      english: RPM LOC
    longName:
      default: RPM LOC - Line of Competency
      vietnamese: RPM LOC - Line of Competency
      english: RPM LOC - Line of Competency
    effectiveDate: '2023-07-15'
    status: true
    businessUnit: null
  - divisionCode: '00000003'
    shortName:
      default: GST STU
      vietnamese: GST STU
      english: GST STU
    longName:
      default: Trung tâm thiết kế đồ họa
      vietnamese: Trung tâm thiết kế đồ họa
      english: Trung tâm thiết kế đồ họa
    EffectiveDate: '2023-07-15'
    status: true
    businessUnit: GST
  - divisionCode: '00000004'
    shortName:
      default: GST GMO
      vietnamese: GST GMO
      english: GST GMO
    longName:
      default: GST GMO - General Management
      vietnamese: GST GMO - General Management
      english: GST GMO - General Management
    effectiveDate: '2023-07-15'
    status: true
    businessUnit: GST
  - divisionCode: '00000005'
    shortName:
      default: FHM PMO
      vietnamese: FHM PMO
      english: FHM PMO
    longName:
      default: Phòng quản trị dự án - FHCMC
      vietnamese: Phòng quản trị dự án - FHCMC
      english: Phòng quản trị dự án - FHCMC
    effectiveDate: '2023-07-15'
    status: true
    businessUnit: FHM
  - divisionCode: '00000006'
    shortName:
      default: FHM AKAT
      vietnamese: FHM AKAT
      english: FHM AKAT
    longName:
      default: Đội chuyên gia
      vietnamese: Đội chuyên gia
      english: Đội chuyên gia
    effectiveDate: '2023-07-15'
    status: true
    businessUnit: FHM
  - divisionCode: '00000007'
    shortName:
      default: FHM META
      vietnamese: FHM META
      english: FHM META
    longName:
      default: Trung tâm Chuyên gia FHM
      vietnamese: Trung tâm Chuyên gia FHM
      english: Trung tâm Chuyên gia FHM
    effectiveDate: '2023-07-15'
    status: true
    businessUnit: FHM
  - divisionCode: '00000008'
    effectiveDate: '2023-07-15'
    status: true
    shortName:
      default: FHM QAI
      vietnamese: FHM QAI
      english: FHM QAI
    longName:
      default: FHM QAI - AI Business Unit
      vietnamese: FHM QAI - AI Business Unit
      english: FHM QAI - AI Business Unit
    businessUnit: FHM
  - divisionCode: '00000009'
    shortName:
      default: FHM Q9
      vietnamese: FHM Q9
      english: FHM Q9
    longName:
      default: FHM Q9 - Q9 Business Unit
      vietnamese: FHM Q9 - Q9 Business Unit
      english: FHM Q9 - Q9 Business Unit
    effectiveDate: '2023-07-15'
    status: true
    businessUnit: FHM
  - divisionCode: '00000010'
    shortName:
      default: FHM JFL
      vietnamese: FHM JFL
      english: FHM JFL
    longName:
      default: FHM JFL - Full Life Circle
      vietnamese: FHM JFL - Full Life Circle
      english: FHM JFL - Full Life Circle
    effectiveDate: '2023-07-15'
    status: true
    businessUnit: FHM
local_buttons: null
layout: layout-table
form_config:
  formSize:
    create: large
    edit: large
    proceed: large
  fields:
    - type: group
      label: Basic Information
      collapse: false
      n_cols: 2
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - type: text
          label: Division Code
          name: code
          _disabled:
            transform: $not($.extend.formType = 'create')
          placeholder: Enter Division Code
          _condition:
            transform: $not($.extend.formType = 'view')
          validators:
            - type: required
            - type: maxLength
              args: '20'
              text: Maximum 20 characters
            - type: pattern
              args: ^[a-zA-Z0-9_.&]+$
              text: The Code must not contain special characters.
        - type: dateRange
          label: Effective Date
          name: effectiveDate
          mode: date-picker
          placeholder: DD/MM/YYYY
          setting:
            format: dd/MM/yyyy
            type: date
          _value:
            transform: $.extend.formType = 'create' ? $now()
          validators:
            - type: required
          _condition:
            transform: $not($.extend.formType = 'view')
        - type: radio
          label: Status
          name: status
          _value:
            transform: $.extend.formType = 'create' and $not($.extend.isDuplicate) ? true
          radio:
            - value: true
              label: Active
            - value: false
              label: Inactive
          _condition:
            transform: $not($.extend.formType = 'view')
        - type: translation
          label: Short Name
          name: shortName
          placeholder: Enter Short Name
          _condition:
            transform: $not($.extend.formType = 'view')
          validators:
            - type: maxLength
              args: '40'
              text: Maximum 40 characters
        - type: translation
          label: Long Name
          name: longName
          col: 2
          placeholder: Enter Long Name
          validators:
            - type: required
            - type: maxLength
              args: '120'
              text: Maximum 120 characters
          _condition:
            transform: $not($.extend.formType = 'view')
        - type: select
          label: Function
          name: function
          placeholder: Select Function
          _condition:
            transform: $not($.extend.formType = 'view')
          outputValue: value
          _select:
            transform: $functionsList()
        - type: select
          label: Org Type
          _condition:
            transform: $not($.extend.formType = 'view')
          name: orgType
          placeholder: Select Org Type
          outputValue: value
          _select:
            transform: $organizationTypesList()
        - type: textarea
          label: Responsibility
          name: responsibility
          _condition:
            transform: $not($.extend.formType = 'view')
          col: 2
          placeholder: Enter Responsibility
          textarea:
            autoSize:
              minRows: 3
              maxRows: 5
            maxCharCount: 4000
    - type: group
      label: Basic Information
      collapse: false
      fieldGroupTitleStyle:
        border: none
      n_cols: 1
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - type: text
          label: Division Code
          name: code
        - type: dateRange
          label: Effective Date
          name: effectiveDate
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
          scale: 1/2
        - type: radio
          label: Status
          name: status
          radio:
            - value: true
              label: Active
            - value: false
              label: Inactive
        - type: translation
          label: Short Name
          name: shortName
          placeholder: Enter Short Name
        - type: translation
          label: Long Name
          name: longName
        - type: select
          label: Function
          name: function
          placeholder: Select Function
          outputValue: value
          _select:
            transform: $functionsList()
        - type: select
          label: Org Type
          name: orgType
          placeholder: Select Org Type
          outputValue: value
          _select:
            transform: $organizationTypesList()
        - type: textarea
          label: Responsibility
          name: responsibility
          placeholder: Responsibility
          textarea:
            autoSize:
              minRows: 3
    - type: group
      label: Associations
      n_cols: 2
      collapse: false
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - type: select
          name: AssociationsType1
          label: Management Association Type
          placeholder: Select Management Association Type
          outputValue: value
          clearFieldsAfterChange:
            - parentObj
            - businessUnitObj
          _condition:
            transform: $not($.extend.formType = 'view')
          _value:
            transform: $isNilorEmpty($.extend.defaultValue.parentObj.value.code)
          select:
            - label: Business Unit
              value: true
            - label: Parent Division
              value: false
        - type: text
          name: parentObjFromOrgChart
          unvisible: true
        - type: selectCustom
          name: businessUnitObj
          label: Business Unit
          outputValue: value
          dependantField: $.fields.AssociationsType1
          isLazyLoad: true
          _condition:
            transform: >-
              $not($.extend.formType = 'view') and $.fields.AssociationsType1 =
              true
          _class:
            transform: '$.fields.AssociationsType1 = true ? ''required'': ''unrequired'''
          placeholder: Select Business Unit
          _validateFn:
            transform: >-
              $exists($.value.code) ?
              ($businessUnitListLazyView($.fields.effectiveDate,$.value.code,($not($.extend.formType
              = 'edit') or $not($DateFormat($.fields.effectiveDate,
              'yyyy-MM-DD') = $DateFormat($.extend.defaultValue.effectiveDate,
              'yyyy-MM-DD'))) ? true)[0] ?
              $businessUnitListLazyView($.fields.effectiveDate,$.value.code,($not($.extend.formType
              = 'edit') or $not($DateFormat($.fields.effectiveDate,
              'yyyy-MM-DD') = $DateFormat($.extend.defaultValue.effectiveDate,
              'yyyy-MM-DD'))) ? true)[0] : '_setSelectValueNull')
          _select:
            transform: >-
              $businessUnitListLazy($.extend.limit,$.extend.page,$.fields.effectiveDate,$.extend.search,true)
          actions:
            - view
          actionsConfig:
            view:
              formConfig:
                fields:
                  - name: code
                    label: Business Unit Code
                    type: text
                  - type: dateRange
                    label: Effective Date
                    name: effectiveDate
                    mode: date-picker
                    setting:
                      format: dd/MM/yyyy
                      type: date
                  - name: status
                    label: Status
                    type: radio
                    radio:
                      - label: Active
                        value: true
                      - label: Inactive
                        value: false
                  - name: shortName
                    label: Short Name
                    type: translation
                  - name: longName
                    label: Long Name
                    type: translation
        - type: selectCustom
          name: parentObj
          dependantField: $.fields.AssociationsType1
          label: Parent Division
          _condition:
            transform: >-
              $not($.extend.formType = 'view') and $.fields.AssociationsType1 =
              false
          _class:
            transform: '$.fields.AssociationsType1 = false ? ''required'': ''unrequired'''
          placeholder: Select Parent Division
          isLazyLoad: true
          outputValue: value
          _validateFn:
            transform: >-
              $exists($.value.code) ?
              ($divisionsListLazyView(0,0,$.fields.effectiveDate,$.value.code,($not($.extend.formType
              = 'edit') or $not($DateFormat($.fields.effectiveDate,
              'yyyy-MM-DD') = $DateFormat($.extend.defaultValue.effectiveDate,
              'yyyy-MM-DD'))) ? true)[0] ?
              $divisionsListLazyView(0,0,$.fields.effectiveDate,$.value.code,($not($.extend.formType
              = 'edit') or $not($DateFormat($.fields.effectiveDate,
              'yyyy-MM-DD') = $DateFormat($.extend.defaultValue.effectiveDate,
              'yyyy-MM-DD'))) ? true)[0] : '_setSelectValueNull')
          _select:
            transform: >-
              $divisionsListLazy($.extend.limit,
              $.extend.page,$.fields.effectiveDate,$.extend.search,$.extend.defaultValue.id,true)
          actions:
            - view
          actionsConfig:
            view:
              formConfig:
                fields:
                  - name: code
                    label: Division Code
                    type: text
                  - type: dateRange
                    label: Effective Date
                    name: effectiveDate
                    mode: date-picker
                    setting:
                      format: dd/MM/yyyy
                      type: date
                  - name: status
                    label: Status
                    type: radio
                    radio:
                      - label: Active
                        value: true
                      - label: Inactive
                        value: false
                  - name: shortName
                    label: Short Name
                    type: translation
                  - name: longName
                    label: Long Name
                    type: translation
    - type: group
      label: Associations
      n_cols: 1
      _condition:
        transform: $.extend.formType = 'view'
      collapse: false
      fields:
        - type: selectCustom
          name: businessUnitObj
          label: Business Unit
          outputValue: value
          inputValue: code
          _condition:
            transform: $.extend.formType = 'view'
          placeholder: Select Business Unit
          _validateFn:
            transform: >-
              $exists($.extend.defaultValue.businessUnitObj.value.code) ?
              ($businessUnitListLazyView($.fields.effectiveDate,$.extend.defaultValue.businessUnitObj.value.code)[0]
              ?
              $businessUnitListLazyView($.fields.effectiveDate,$.extend.defaultValue.businessUnitObj.value.code)[0]
              : '_setSelectValueNull')
          _select:
            transform: >-
              $businessUnitListLazyView($.fields.effectiveDate,$.extend.defaultValue.businessUnitObj.value.code)
          actions:
            - view
          actionsConfig:
            view:
              formConfig:
                fields:
                  - name: code
                    label: Business Unit Code
                    type: text
                  - type: dateRange
                    label: Effective Date
                    name: effectiveDate
                    mode: date-picker
                    setting:
                      format: dd/MM/yyyy
                      type: date
                  - name: status
                    label: Status
                    type: radio
                    radio:
                      - label: Active
                        value: true
                      - label: Inactive
                        value: false
                  - name: shortName
                    label: Short Name
                    type: translation
                  - name: longName
                    label: Long Name
                    type: translation
        - type: selectCustom
          name: parentObj
          label: Parent Division
          _condition:
            transform: $.extend.formType = 'view'
          placeholder: Select Division
          outputValue: value
          inputValue: code
          _validateFn:
            transform: >-
              $exists($test($.extend.defaultValue.parentObj.value.code)) ?
              ($divisionsListLazyView(0,0,$.fields.effectiveDate,$.extend.defaultValue.parentObj.value.code)[0]
              ?
              $divisionsListLazyView(0,0,$.fields.effectiveDate,$.extend.defaultValue.parentObj.value.code)[0]
              : '_setSelectValueNull')
          _select:
            transform: >-
              $divisionsListLazyView($.extend.limit,
              $.extend.page,$.fields.effectiveDate,
              $.extend.defaultValue.parentObj.value.code)
          actions:
            - view
          actionsConfig:
            view:
              formConfig:
                fields:
                  - name: code
                    label: Division Code
                    type: text
                  - type: dateRange
                    label: Effective Date
                    name: effectiveDate
                    mode: date-picker
                    setting:
                      format: dd/MM/yyyy
                      type: date
                  - name: status
                    label: Status
                    type: radio
                    radio:
                      - label: Active
                        value: true
                      - label: Inactive
                        value: false
                  - name: shortName
                    label: Short Name
                    type: translation
                  - name: longName
                    label: Long Name
                    type: translation
    - type: group
      label: Manager of Division
      n_cols: 2
      collapse: false
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - type: select
          name: managerType
          label: Manager Type
          placeholder: Select Manager Type
          outputValue: value
          clearFieldsAfterChange:
            - headOfDivisionObj
            - deputyManagerObj
            - managerPosition
            - deputyManagerPosition
          _condition:
            transform: $not($.extend.formType = 'view')
          select:
            - label: Employee
              value: Employee
            - label: Position
              value: Position
        - type: select
          name: headOfDivisionObj
          dependantField: $.fields.managerType
          label: Head of Division
          isLazyLoad: true
          placeholder: Select Head of Division
          _condition:
            transform: >-
              $not($.extend.formType = 'view') and $.fields.managerType =
              'Employee'
          outputValue: value
          _validateFn:
            transform: >-
              $exists($.value.code) ?
              ($personalsListView($.extend.limit,$.extend.page,null,$.fields.effectiveDate,$.value.code)[0]
              ?
              $personalsListView($.extend.limit,$.extend.page,null,$.fields.effectiveDate,$.value.code)[0]
              : '_setSelectValueNull')
          _select:
            transform: >-
              $personalsList($.extend.limit,$.extend.page,$.extend.search,$.fields.effectiveDate)
        - type: selectAll
          name: deputyManagerObj
          dependantField: $.fields.managerType
          label: Deputy Manager
          col: 2
          isLazyLoad: true
          placeholder: Select Deputy Manager
          _condition:
            transform: >-
              $not($.extend.formType = 'view') and $.fields.managerType =
              'Employee'
          outputValue: value
          _validateFn:
            transform: >-
              $exists($.value.code) ?
              ($personalsListView($.extend.limit,$.extend.page,null,$.fields.effectiveDate,$.value.code)[0]
              ?
              $personalsListView($.extend.limit,$.extend.page,null,$.fields.effectiveDate,$.value.code)
              : '_setSelectValueNull')
          _options:
            transform: >-
              $personalsList($.extend.limit,$.extend.page,$.extend.search,$.fields.effectiveDate)
        - type: select
          name: managerPosition
          dependantField: $.fields.managerType
          label: Manager Position
          placeholder: Select Manager Position
          outputValue: value.id
          _condition:
            transform: >-
              $not($.extend.formType = 'view') and $.fields.managerType =
              'Position'
          _validateFn:
            transform: >-
              $exists($.value.code) ?
              ($positionsList($.fields.effectiveDate,$.value.code)[0] ?
              $positionsList($.fields.effectiveDate,$.value.code)[0] :
              '_setSelectValueNull')
          _select:
            transform: $positionsList($.fields.effectiveDate,$.extend.search)
        - type: selectAll
          name: deputyManagerPosition
          dependantField: $.fields.managerType
          col: 2
          label: Deputy Manager Position
          placeholder: Select Deputy Manager Position
          outputValue: value.id
          inputValue: code
          _condition:
            transform: >-
              $not($.extend.formType = 'view') and $.fields.managerType =
              'Position'
          _options:
            transform: $positionsList($.fields.effectiveDate,$.extend.search)
    - type: group
      label: Manager of Division
      n_cols: 1
      _condition:
        transform: $.extend.formType = 'view'
      collapse: false
      fields:
        - type: select
          name: managerType
          label: Manager Type
          outputValue: value
          _condition:
            transform: $.extend.formType = 'view'
          placeholder: Select Manager Type
          select:
            - label: Employee
              value: Employee
            - label: Position
              value: Position
        - type: select
          name: headOfDivisionObj
          label: Head of Division
          placeholder: Select Head of Division
          _condition:
            transform: $.fields.managerType = 'Employee' and $.extend.formType = 'view'
          outputValue: value
          inputValue: code
          _select:
            transform: >-
              $personalsListView($.extend.limit,$.extend.page,null,$.fields.effectiveDate,$.fields.headOfDivisionObj.value.code)
        - type: select
          name: deputyManagerObj
          label: Deputy Manager
          placeholder: Select Deputy Manager
          mode: multiple
          _condition:
            transform: $.fields.managerType = 'Employee'  and $.extend.formType = 'view'
          outputValue: value
          inputValue: code
          _select:
            transform: >-
              $personalsListView($.extend.limit,$.extend.page,null,$.fields.effectiveDate,$.fields.deputyManagerObj.value.code)
        - type: select
          name: managerPosition
          label: Manager Position
          placeholder: Select Manager Position
          outputValue: value.id
          inputValue: code
          _condition:
            transform: $.fields.managerType = 'Position' and $.extend.formType = 'view'
          _select:
            transform: $positionsList($.fields.effectiveDate,$.extend.search)
        - type: select
          name: deputyManagerPosition
          label: Deputy Manager Position
          placeholder: Select Deputy Manager Position
          outputValue: value.id
          inputValue: code
          mode: multiple
          _condition:
            transform: $.fields.managerType = 'Position' and $.extend.formType = 'view'
          _select:
            transform: $positionsList($.fields.effectiveDate,$.extend.search)
    - type: group
      label: Former Organization
      collapse: false
      _condition:
        transform: $not($.extend.formType = 'view')
      disableEventCollapse: false
      fields:
        - type: array
          mode: table
          name: orgObjects
          minSize: 1
          arrayOptions:
            canChangeSize: true
          field:
            type: group
            name: group
            fields:
              - type: select
                name: type
                placeholder: Org Object
                label: Former Organization
                width: 250px
                outputValue: value
                select:
                  - label: Group
                    value: '1'
                  - label: Company
                    value: '2'
                  - label: Legal Entity
                    value: '3'
                  - label: Business Unit
                    value: '4'
                  - label: Division
                    value: '5'
                  - label: Department
                    value: '6'
                clearFieldsAfterChange:
                  - id
                  - objData
                  - code
              - type: selectCustom
                name: objData
                placeholder: Select Org Object
                isLazyLoad: true
                outputValue: value
                _select:
                  dependants:
                    - $.fields.orgObjects[$index].type
                    - $.extend.page
                    - $.extend.search
                  params:
                    $index: $.extend.path[-1]
                  transform: >-
                    $getFieldGroup($.extend.path,$.fields,1).type = '1' ?
                    $groupsList($.extend.limit,$.extend.page,$.fields.effectiveDate,$.extend.search)
                    : $getFieldGroup($.extend.path,$.fields,1).type = '2' ?
                    $companyList($.extend.limit,$.extend.page,$.fields.effectiveDate,$.extend.search)
                    : $getFieldGroup($.extend.path,$.fields,1).type = '3' ?
                    $legalEntitiesList($.extend.limit,$.extend.page,$.fields.effectiveDate,$.extend.search)
                    : $getFieldGroup($.extend.path,$.fields,1).type = '4' ?
                    $businessUnitListLazy($.extend.limit,$.extend.page,$.fields.effectiveDate,$.extend.search)
                    : $getFieldGroup($.extend.path,$.fields,1).type = '5' ?
                    $divisionsListLazy($.extend.limit,$.extend.page,$.fields.effectiveDate,$.extend.search)
                    : $getFieldGroup($.extend.path,$.fields,1).type = '6' ?
                    $departmentsList($.extend.limit,$.extend.page,$.fields.effectiveDate,$.extend.search)
                    : null
                actions:
                  - view
                actionsConfig:
                  view:
                    formConfig:
                      fields:
                        - name: code
                          label: Code
                          type: text
                        - type: dateRange
                          label: Effective Date
                          name: effectiveDate
                          mode: date-picker
                          setting:
                            format: dd/MM/yyyy
                            type: date
                        - name: status
                          label: Status
                          type: radio
                          radio:
                            - label: Active
                              value: true
                            - label: Inactive
                              value: false
                        - name: shortName
                          label: Short Name
                          type: translation
                          placeholder: Enter Short Name
                        - name: longName
                          label: Long Name
                          type: translation
                validators:
                  - id: check_duplicate
                    type: ppx-custom
                    args:
                      transform: >-
                        $not($isNilorEmpty($.value)) ?
                        $count($map($.fields.orgObjects, function($item, $index)
                        {($item.id = $.value and $item.type =
                        $.fields.orgObjects[$index].type) ? {}})) > 1
                    text: Former organiztion has been duplicated
                  - id: check_null
                    type: ppx-custom
                    args:
                      transform: >-
                        $not($isNilorEmpty($getFieldGroup($.extend.path,$.fields,1).type))
                        and $isNilorEmpty($.value)
                    text: Cannot be empty
              - type: text
                name: id
                unvisible: true
                _value:
                  transform: $getFieldGroup($.extend.path,$.fields,1).objData.id
              - type: text
                name: code
                unvisible: true
                _value:
                  transform: $getFieldGroup($.extend.path,$.fields,1).objData.code
    - type: group
      collapse: false
      label: Former Organization
      _condition:
        transform: $.extend.formType = 'view'
      disableEventCollapse: false
      fields:
        - type: array
          mode: table
          name: orgObjects
          arrayOptions:
            canChangeSize: true
          field:
            type: group
            name: group
            fields:
              - type: select
                name: type
                placeholder: Org Object
                label: Former Organization
                width: 192px
                outputValue: value
                select:
                  - label: Group
                    value: '1'
                  - label: Company
                    value: '2'
                  - label: Legal Entity
                    value: '3'
                  - label: Business Unit
                    value: '4'
                  - label: Division
                    value: '5'
                  - label: Department
                    value: '6'
                clearFieldsAfterChange:
                  - id
              - type: selectCustom
                name: id
                placeholder: Select Org Object
                outputValue: value
                _select:
                  dependants:
                    - $.fields.orgObjects[$index].type
                  params:
                    $index: $.extend.path[-1]
                  transform: >-
                    $getFieldGroup($.extend.path,$.fields,1).type = '1' ?
                    $groupsList($.extend.limit,$.extend.page,$.fields.effectiveDate,null,$getFieldGroup($.extend.path,$.fields,1).code)
                    : $getFieldGroup($.extend.path,$.fields,1).type = '2' ?
                    $companyList($.extend.limit,$.extend.page,$.fields.effectiveDate,null,$getFieldGroup($.extend.path,$.fields,1).code)
                    : $getFieldGroup($.extend.path,$.fields,1).type = '3' ?
                    $legalEntitiesList($.extend.limit,$.extend.page,$.fields.effectiveDate,null,$getFieldGroup($.extend.path,$.fields,1).code)
                    : $getFieldGroup($.extend.path,$.fields,1).type = '4' ?
                    $businessUnitListLazyView($.fields.effectiveDate,$getFieldGroup($.extend.path,$.fields,1).code)
                    :  $getFieldGroup($.extend.path,$.fields,1).type = '5' ?
                    $divisionsListLazyView($.extend.limit,$.extend.page,$.fields.effectiveDate,$getFieldGroup($.extend.path,$.fields,1).code)
                    : $getFieldGroup($.extend.path,$.fields,1).type = '6' ?
                    $departmentsList($.extend.limit,$.extend.page,$.fields.effectiveDate,null,$getFieldGroup($.extend.path,$.fields,1).code)
                    : '_setSelectValueNull'
                actions:
                  - view
                actionsConfig:
                  view:
                    formConfig:
                      fields:
                        - name: code
                          label: Code
                          type: text
                        - type: dateRange
                          label: Effective Date
                          name: effectiveDate
                          mode: date-picker
                          setting:
                            format: dd/MM/yyyy
                            type: date
                        - name: status
                          label: Status
                          type: radio
                          radio:
                            - label: Active
                              value: true
                            - label: Inactive
                              value: false
                        - name: shortName
                          label: Short Name
                          type: translation
                          placeholder: Enter Short Name
                        - name: longName
                          label: Long Name
                          type: translation
              - type: text
                name: code
                unvisible: true
    - type: group
      label: Decision Information
      collapse: false
      _condition:
        transform: $not($.extend.formType = 'view')
      n_cols: 2
      fields:
        - type: select
          name: action
          label: Action
          placeholder: Select Action
          outputValue: value
          _select:
            transform: >-
              $.extend.formType = 'create' ? $actionsList('ACTIONORG_001') :
              $actionsList()
          _condition:
            transform: $not($.extend.formType = 'view')
        - type: select
          name: reason
          dependantField: $.fields.action
          dependantFieldSkip: 2
          label: Reason
          _select:
            transform: $.fields.action ? $.variables._reasonList
          outputValue: value
          placeholder: Select Reason
          _condition:
            transform: $not($.extend.formType = 'view')
        - type: text
          label: Decision No.
          name: decisionNo
          _condition:
            transform: $not($.extend.formType = 'view')
          placeholder: Enter Decision No.
        - type: text
          label: Decision Name
          name: decisionName
          _condition:
            transform: $not($.extend.formType = 'view')
          placeholder: Enter Decision Name
        - type: dateRange
          label: Issuance Date
          name: issueDate
          placeholder: dd/mm/yyyy
          _condition:
            transform: $not($.extend.formType = 'view')
          mode: date-picker
        - type: select
          name: authorityForApproval
          _condition:
            transform: $not($.extend.formType = 'view')
          label: Approved By
          placeholder: Select Approved By
          outputValue: value
          _select:
            transform: $authorityForApprovalsList()
        - type: text
          label: Signatory
          name: signatory
          col: 2
          _condition:
            transform: $not($.extend.formType = 'view')
          placeholder: Enter Signatory
        - type: upload
          label: Attachment
          col: 2
          name: attachFile
          upload:
            accept:
              - >-
                application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
              - application/pdf
              - application/vnd.ms-excel
            size: 5
          _condition:
            transform: $not($.extend.formType = 'view')
        - type: upload
          name: attachFileResults
          readOnly: true
          col: 2
          canAction: true
          hiddenLabel: true
          _condition:
            transform: $.extend.formType = 'edit'
    - type: group
      label: Decision Information
      collapse: false
      _condition:
        transform: $.extend.formType = 'view'
      n_cols: 1
      fields:
        - type: select
          name: action
          label: Action
          placeholder: Select Action
          outputValue: value
          _select:
            transform: $actionsList()
          _condition:
            transform: $.extend.formType = 'view'
        - type: select
          name: reason
          label: Reason
          _select:
            transform: $reasonsList()
          outputValue: value
          placeholder: Select Reason
          _condition:
            transform: $.extend.formType = 'view'
        - type: text
          label: Decision No.
          name: decisionNo
          _condition:
            transform: $.extend.formType = 'view'
          placeholder: Enter Decision No.
        - type: text
          label: Decision Name
          name: decisionName
          _condition:
            transform: $.extend.formType = 'view'
          placeholder: Enter Decision Name
        - type: dateRange
          label: Issuance Date
          name: issueDate
          _condition:
            transform: $.extend.formType = 'view'
          mode: date-picker
        - type: select
          name: authorityForApproval
          _condition:
            transform: $.extend.formType = 'view'
          label: Approved By
          placeholder: Select Approved By
          outputValue: value
          _select:
            transform: $authorityForApprovalsList()
        - type: text
          label: Signatory
          name: signatory
          _condition:
            transform: $.extend.formType = 'view'
          placeholder: Enter Signatory
        - type: upload
          label: Attachment
          name: attachFileResults
          readOnly: true
          _condition:
            transform: $.extend.formType = 'view'
  historyHeaderTitle: '''View History Division'''
  sources:
    businessUnitListLazy:
      uri: '"/api/business-units/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'filter':
        [{'field':'search','operator':
        '$eq','value':$.search},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':$.status}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id':$item.id,'code':$item.code},
        'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - effectiveDate
        - search
        - status
    businessUnitListLazyView:
      uri: '"/api/business-units/get-by"'
      method: GET
      queryTransform: >-
        {'limit': 0, 'page': 0, 'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':$.status},{'field':'code','operator':
        '$eq','value':$.code}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id':$item.id,'code':$item.code},
        'additionalData': $item}})[]
      disabledCache: true
      params:
        - effectiveDate
        - code
        - status
    groupsList:
      uri: '"/api/groups/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page ,'filter':
        [{'field':'search','operator':
        '$eq','value':$.search},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'code','operator':
        '$eq','value':$.code}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id': $item.id, 'code': $item.code},
        'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - effectiveDate
        - search
        - code
    companyList:
      uri: '"/api/companies/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page ,'filter':
        [{'field':'search','operator':
        '$eq','value':$.search},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'code','operator':
        '$eq','value':$.code}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id': $item.id, 'code': $item.code},
        'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - effectiveDate
        - search
        - code
        - status
    departmentsList:
      uri: '"/api/departments/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'filter':
        [{'field':'search','operator':
        '$eq','value':$.search},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'code','operator':
        '$eq','value':$.code}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id': $item.id, 'code': $item.code} ,
        'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - effectiveDate
        - search
        - code
    divisionsListLazy:
      uri: '"/api/divisions/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page,'filter':
        [{'field':'search','operator':
        '$eq','value':$.search},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':$.status}, {'field':'IdFilter','operator': '$ne','value':
        $.codeFilter}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id': $item.id, 'code': $item.code},
        'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - effectiveDate
        - search
        - codeFilter
        - status
    divisionsListLazyView:
      uri: '"/api/divisions/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'filter':
        [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':$.status}, {'field':'codeFilter','operator':
        '$eq','value': $.codeFilter}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id': $item.id, 'code': $item.code},
        'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - effectiveDate
        - codeFilter
        - status
    legalEntitiesList:
      uri: '"/api/legal-entities/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page ,'filter':
        [{'field':'search','operator':
        '$eq','value':$.search},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'code','operator':
        '$eq','value':$.code}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id': $item.id, 'code':
        $item.code},'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - effectiveDate
        - search
        - code
    functionsList:
      uri: '"/api/picklists/FUNCTIONORG/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})
      disabledCache: true
    organizationTypesList:
      uri: '"/api/picklists/TYPEORG/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})
      disabledCache: true
    positionsList:
      uri: '"/api/positions/by"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'code','operator':
        '$eq','value':$.code}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default  & ' (' &
        $item.code & ')', 'value': {'id': $item.id, 'code': $item.code},
        'jobCode': $item.id, 'additionalData': $item}})[]
      disabledCache: true
      params:
        - effectiveDate
        - code
    personalsList:
      uri: '"/api/personals/all-employees-job-datas"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page,'filter':
        [{'field':'search','operator': '$eq','value':$.paramsName},
        {'field':'effectiveDate','operator': '$eq','value':$.effectiveDate},
        {'field':'hrStatus','operator': '$eq','value': 'A'}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.fullName  & ' ' &
        $item.emailsplit, 'value': {'id': $item.employeeId, 'code':
        $item.employeeId}}})[]
      disabledCache: true
      params:
        - limit
        - page
        - paramsName
        - effectiveDate
        - code
    personalsListView:
      uri: '"/api/personals/all-employees-job-datas"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page,'filter':
        [{'field':'search','operator': '$eq','value':$.paramsName},
        {'field':'effectiveDate','operator': '$eq','value':$.effectiveDate},
        {'field':'hrStatus','operator': '$eq','value': 'A'},
        {'field':'employeeId','operator': '$in','value':$.code}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.fullName  & ' ' &
        $item.emailsplit, 'value': {'id': $item.employeeId, 'code':
        $item.employeeId}}})[]
      disabledCache: true
      params:
        - limit
        - page
        - paramsName
        - effectiveDate
        - code
    actionsList:
      uri: '"/api/picklists-values/ACTIONORG"'
      method: GET
      queryTransform: '{''filter'': [{''field'':''code'',''operator'': ''$eq'',''value'':$.codeAction}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code, 'id':$item.id }})[]
      disabledCache: true
      params:
        - codeAction
    reasonsList:
      uri: '"/api/picklists/REASONORG/values"'
      method: GET
      queryTransform: '{''filter'': [{''field'':''col501'',''operator'': ''$eq'',''value'':$.codeAction}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - codeAction
    authorityForApprovalsList:
      uri: '"/api/picklists/CAPQDORG/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})
      disabledCache: true
  variables:
    _selectAction:
      transform: $filter($actionsList(),function ($v){ $v.value = $.fields.action })
    _actionId:
      transform: $.variables._selectAction.id
    _reasonList:
      transform: $reasonsList($.variables._actionId)
  _mode:
    transform: $not($.extend.formType = 'view') ? 'mark-scroll'
filter_config:
  fields:
    - name: code
      label: Division Code
      placeholder: Enter Division Code
      labelType: type-grid
      type: text
    - name: status
      label: Status
      type: radio
      labelType: type-grid
      value: ''
      radio:
        - label: All
          value: ''
        - label: Active
          value: true
        - label: Inactive
          value: false
    - name: shortName
      label: Short Name
      placeholder: Enter Short Name
      labelType: type-grid
      type: text
    - name: longName
      label: Long Name
      placeholder: Enter Long Name
      labelType: type-grid
      type: text
    - name: businessUnitCode
      label: Business Unit
      placeholder: Select Business Unit
      labelType: type-grid
      type: selectAll
      isLazyLoad: true
      _options:
        transform: $businessUnitList($.extend.limit,$.extend.page,$.extend.search)
    - name: parentCode
      label: Parent Division
      placeholder: Select Parent Division
      labelType: type-grid
      type: selectAll
      isLazyLoad: true
      _options:
        transform: $divisionsListLazy($.extend.limit,$.extend.page,$.extend.search)
  filterMapping:
    - field: code
      operator: $cont
      valueField: code
    - field: status
      operator: $eq
      valueField: status
    - field: businessUnitCode
      operator: $in
      valueField: businessUnitCode.(value)
    - field: parentCode
      operator: $in
      valueField: parentCode.(value)
    - field: na_shortName
      operator: $cont
      valueField: shortName
    - field: na_longName
      operator: $cont
      valueField: longName
  sources:
    businessUnitList:
      uri: '"/api/business-units/v2/get-list"'
      method: GET
      queryTransform: >-
        {'limit':$.limit,'page':$.page,'filter': [{'field':'search','operator':
        '$eq','value':$.search}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    divisionsListLazy:
      uri: '"/api/divisions/v2/get-list"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page,'filter':
        [{'field':'search','operator': '$eq','value':$.search}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
layout_options:
  is_upload_file: true
  toolTable:
    export: true
    import: true
    adjustDisplay: true
  tool_table:
    - id: import
      icon: icon-upload-simple
    - id: export
      icon: icon-download-simple
  view_after_updated: true
  delete_multi_items: true
  view_history_after_created: true
  hide_action_row: true
  custom_history_backend_url: /api/divisions/insert-new-record
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    title: Edit
    icon: pencil
  - id: delete
    title: Delete
    icon: trash
    group: null
backend_url: /api/divisions
screen_name: null
layout_options__actions_many: null
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key: null
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Division
  parent:
    title: Organization Structure
