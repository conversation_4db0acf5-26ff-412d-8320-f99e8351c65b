id: FO.FS.FR.017
status: draft
sort: 171
user_created: 59722cb8-f858-481b-97f1-c97dd2a8e4be
date_created: '2024-06-14T03:40:04.062Z'
user_updated: 35b4f590-f747-4e53-8fa5-76aba65555c2
date_updated: '2025-07-29T03:50:02.000Z'
title: Location
requirement:
  time: 1747606544193
  blocks:
    - id: oSYMHwjAzj
      type: paragraph
      data:
        text: '&nbsp;Chức năng cho phép tạo mới/cập nhật thông tin Location&nbsp;'
  version: 2.30.7
screen_design: null
module: FO
local_fields:
  - code: code
    title: Location Code
    data_type:
      key: Employee Code
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    pinned: true
    show_sort: true
  - code: shortName
    title: Short Name
    description: Short Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: longName
    title: Long Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    description: Long Name
    show_sort: true
  - code: locationGroupName
    title: Location Group
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: companiesDisplay
    title: Companies
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    extra_config:
      singular: Company
      plural: Companies
  - code: status
    title: Status
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: Boolean Tag
      collection: field_types
    show_sort: true
mock_data:
  - code: '00000001'
    name:
      default: FPT Tower
      vietnamese: Tháp FPT
      english: FPT Tower
    description:
      default: FPT Tower 1, PVB
      vietnamese: Tháp FPT 1, PVB
      english: FPT Tower 1, PVB
    effectiveDate: 01/01/2023
    locationGroupName: Phạm Văn Bạch
    region: Miền Bắc
  - code: '00000002'
    name:
      default: FPTHCM
      vietnamese: FPTHCM
      english: FPTHCM
    description:
      default: Chi nhánh FPT HCM
      vietnamese: Chi nhánh FPT HCM
      english: FPT HCM Branch
    effectiveDate: 11/11/2023
    locationGroupName: Hồ Chí Minh
    region: Miền Nam
  - code: '00000003'
    name:
      default: FPTDNG
      vietnamese: FPT Đà Nẵng
      english: FPT Danang
    description:
      default: VPDD FPT Đà Nẵng
      vietnamese: Văn phòng đại diện FPT Đà Nẵng
      english: FPT Danang Office
    effectiveDate: 11/11/2023
    locationGroupName: Đà Nẵng
    region: Miền Trung
  - code: '00000004'
    name:
      default: FPTCT
      vietnamese: FPT Cần Thơ
      english: FPT Can Tho
    description:
      default: VFPT Chi nhánh Cần Thơ
      vietnamese: Chi nhánh FPT Cần Thơ
      english: FPT Can Tho Branch
    effectiveDate: 11/11/2023
    locationGroupName: Cần Thơ
    region: Miền Nam
  - code: '00000005'
    name:
      default: FSS HN
      vietnamese: FSS Hà Nội
      english: FSS Hanoi
    description:
      default: Trụ sở chính, HN
      vietnamese: Trụ sở chính, Hà Nội
      english: Headquarters, Hanoi
    effectiveDate: 11/11/2023
    locationGroupName: Hòa Lạc
    region: Miền Bắc
  - code: '00000006'
    name:
      default: FSS HCM
      vietnamese: FSS HCM
      english: FSS HCM
    description:
      default: Trụ sở chi nhánh, HCM
      vietnamese: Trụ sở chi nhánh, HCM
      english: Branch office, HCM
    effectiveDate: 11/11/2023
    locationGroupName: Phạm Văn Bạch
    region: Miền Nam
  - code: '00000007'
    name:
      default: FSS DN
      vietnamese: FSS Đà Nẵng
      english: FSS Danang
    description:
      default: Trụ sở chi nhánh, DN
      vietnamese: Trụ sở chi nhánh, Đà Nẵng
      english: Branch office, Danang
    effectiveDate: 11/11/2023
    locationGroupName: Phạm Văn Bạch
    region: Miền Trung
  - code: '00000008'
    name:
      default: FU HN
      vietnamese: FU Hà Nội
      english: FU Hanoi
    description:
      default: Khu CNC Hòa Lạc, Hà Nội
      vietnamese: Khu Công nghệ cao Hòa Lạc, Hà Nội
      english: Hoa Lac Hi-tech Park, Hanoi
    effectiveDate: 11/11/2023
    locationGroupName: Hòa Lạc
    region: Miền Bắc
  - code: '00000009'
    name:
      default: KNam Bld
      vietnamese: Tòa nhà Keangnam
      english: Keangnam Building
    description:
      default: KeangNam Building, HN
      vietnamese: Tòa nhà Keangnam, Hà Nội
      english: Keangnam Building, Hanoi
    effectiveDate: 11/11/2023
    locationGroupName: Keangnam
    region: Miền Bắc
  - code: '00000010'
    name:
      default: F-Ville-2
      vietnamese: F-Ville-2
      english: F-Ville-2
    description:
      default: F-Ville-2, HN
      vietnamese: F-Ville-2, Hà Nội
      english: F-Ville-2, Hanoi
    effectiveDate: 11/11/2023
    locationGroupName: Hòa Lạc
    region: Miền Bắc
local_buttons: null
layout: layout-table
form_config:
  fields:
    - type: group
      label: Basic Information
      collapse: false
      _disableEventCollapse:
        transform: '$not($.extend.formType = ''view'') ? true : false'
      fieldGroupTitleStyle:
        border: none
      fields:
        - type: group
          n_cols: 2
          _condition:
            transform: $not($.extend.formType = 'view')
          fields:
            - type: text
              label: Location Code
              name: code
              disabled: true
              placeholder: System – Generated
            - type: dateRange
              label: Effective Date
              name: effectiveDate
              mode: date-picker
              placeholder: dd/mm/yyyy
              setting:
                format: dd/MM/yyyy
                type: date
              _value:
                transform: $.extend.formType = 'create' ? $now()
              validators:
                - type: required
              _condition:
                transform: $not($.extend.formType = 'view')
            - type: radio
              label: Status
              _condition:
                transform: $not($.extend.formType = 'view')
              name: status
              _value:
                transform: >-
                  $.extend.formType = 'create' and $not($.extend.isDuplicate) ?
                  true
              radio:
                - value: true
                  label: Active
                - value: false
                  label: Inactive
            - name: shortName
              label: Short Name
              type: translation
              placeholder: Enter Short Name
              _condition:
                transform: $not($.extend.formType = 'view')
              validators:
                - type: maxLength
                  args: '40'
                  text: Maximum 40 characters
        - name: longName
          label: Long Name
          type: translation
          placeholder: Enter Long Name
          _condition:
            transform: $not($.extend.formType = 'view')
          validators:
            - type: required
            - type: maxLength
              args: '120'
              text: Maximum 120 characters.
        - type: group
          n_cols: 2
          _condition:
            transform: $not($.extend.formType = 'view')
          fields:
            - type: select
              label: Location Group
              name: locationGroupId
              placeholder: Select Location Group
              outputValue: value
              _select:
                transform: $locationGroupsList($.fields.effectiveDate)
            - type: select
              label: Region
              name: region
              placeholder: Select Region
              outputValue: value
              _select:
                transform: $regionsList($.fields.effectiveDate)
        - type: text
          label: Location Code
          name: code
          disabled: true
          placeholder: Automatic
          _condition:
            transform: $.extend.formType = 'view'
        - type: dateRange
          label: Effective Date
          name: effectiveDate
          mode: date-picker
          placeholder: dd/mm/yyyy
          setting:
            format: dd/MM/yyyy
            type: date
          _condition:
            transform: $.extend.formType = 'view'
        - type: radio
          label: Status
          name: status
          value: true
          _condition:
            transform: $.extend.formType = 'view'
          radio:
            - value: true
              label: Active
            - value: false
              label: Inactive
        - name: shortName
          label: Short Name
          type: translation
          placeholder: Enter Short Name
          _condition:
            transform: $.extend.formType = 'view'
        - name: longName
          label: Long Name
          type: translation
          _condition:
            transform: $.extend.formType = 'view'
        - type: select
          label: Location Group
          name: locationGroupId
          _condition:
            transform: $.extend.formType = 'view'
          outputValue: value
          _select:
            transform: $locationGroupsList($.fields.effectiveDate)
        - type: select
          label: Region
          name: region
          outputValue: value
          _condition:
            transform: $.extend.formType = 'view'
          _select:
            transform: $regionsList($.fields.effectiveDate)
    - type: group
      label: Address
      collapse: false
      n_cols: 2
      _condition:
        transform: $not($.extend.formType = 'view')
      disableEventCollapse: true
      fields:
        - type: select
          name: country
          label: Country
          _disabled:
            transform: $not($.extend.formType = 'create')
          placeholder: Select Country
          outputValue: value
          clearFieldsAfterChange:
            - provinceCity
            - district
            - ward
          validators:
            - type: required
          _select:
            transform: $nationsList($.fields.effectiveDate)
        - type: select
          name: provinceCity
          _disabled:
            transform: $not($.extend.formType = 'create')
          label: Province/City
          clearFieldsAfterChange:
            - district
            - ward
          placeholder: Select Province/City
          outputValue: value
          _select:
            transform: >-
              $.fields.country ?
              $provincesList($.fields.country,$.fields.effectiveDate)
          _condition:
            transform: $not($.extend.formType = 'view')
          validators:
            - type: required
        - type: select
          name: district
          label: District
          placeholder: Select District
          outputValue: value
          clearFieldsAfterChange:
            - ward
          _condition:
            transform: $not($.extend.formType = 'view')
          _select:
            transform: >-
              $.fields.provinceCity ?
              $districtsList($.fields.provinceCity,$.fields.effectiveDate)
        - type: select
          name: ward
          label: Ward
          placeholder: Select Ward
          _condition:
            transform: $not($.extend.formType = 'view')
          outputValue: value
          _select:
            transform: >-
              $.fields.district ?
              $wardsList($.fields.district,$.fields.effectiveDate)
        - type: text
          name: noStreet
          _condition:
            transform: $not($.extend.formType = 'view')
          validators:
            - type: maxLength
              args: '4000'
              text: Maximum 4000 characters
          col: 2
          label: No, Street
          placeholder: Enter No, Street
    - type: group
      label: Address
      collapse: false
      n_cols: 1
      _condition:
        transform: $.extend.formType = 'view'
      disableEventCollapse: false
      fields:
        - type: select
          name: country
          label: Country
          _condition:
            transform: $.extend.formType = 'view'
          _select:
            transform: $nationsList($.fields.effectiveDate)
        - type: select
          name: provinceCity
          label: Province/City
          _select:
            transform: $provincesList($.fields.country,$.fields.effectiveDate)
          _condition:
            transform: $.extend.formType = 'view'
        - type: select
          name: district
          label: District
          placeholder: Select District
          outputValue: value
          _condition:
            transform: $.extend.formType = 'view'
          _select:
            transform: >-
              $districtsList($.fields.provinceCity,$.fields.effectiveDate,$.extend.defaultValue.district)[]
        - type: select
          name: ward
          label: Ward
          _condition:
            transform: $.extend.formType = 'view'
          outputValue: value
          _select:
            transform: $wardsList($.fields.district,$.fields.effectiveDate)
        - type: text
          name: noStreet
          label: No, Street
          placeholder: Enter No, Street
    - type: text
      name: IsInsertNewRecord
      _value:
        transform: $.extend.formType = 'proceed' ? true
      unvisible: true
    - type: group
      label: Company
      collapse: false
      _disableEventCollapse:
        transform: '$not($.extend.formType = ''view'') ? true : false'
      fields:
        - type: selectAll
          label: Company
          name: companyObj
          placeholder: Select Company
          outputValue: value
          isLazyLoad: true
          _validateFn:
            transform: >-
              ($exists($.fields.companyObj.value.code) or
              $exists($.fields.companyObj.code)) ?
              ($companyList(0,0,$.fields.effectiveDate,$exists($.fields.companyObj.value.code)
              ? $.fields.companyObj.value.code :
              $.fields.companyObj.code,null,($not($.extend.formType = 'edit') or
              $not($DateFormat($.fields.effectiveDate, 'yyyy-MM-DD') =
              $DateFormat($.extend.defaultValue.effectiveDate, 'yyyy-MM-DD'))) ?
              true)[0] ?
              $companyList(0,0,$.fields.effectiveDate,$exists($.fields.companyObj.value.code)
              ? $.fields.companyObj.value.code :
              $.fields.companyObj.code,null,($not($.extend.formType = 'edit') or
              $not($DateFormat($.fields.effectiveDate, 'yyyy-MM-DD') =
              $DateFormat($.extend.defaultValue.effectiveDate, 'yyyy-MM-DD'))) ?
              true) : '_setSelectValueNull')
          _options:
            transform: >-
              $companyList($.extend.limit,$.extend.page,$.fields.effectiveDate,null,$.extend.search,true)
          _condition:
            transform: $not($.extend.formType = 'view')
          validators:
            - type: required
        - type: selectCustom
          label: Company
          name: companyObj
          placeholder: Select Company
          outputValue: value
          inputValue: code
          mode: multiple
          _select:
            transform: >-
              $companyList($.extend.limit,$.extend.page,$.fields.effectiveDate,
              $.extend.defaultValue.companyObj.value.code)
          _condition:
            transform: $.extend.formType = 'view'
          actions:
            - view
          actionsConfig:
            view:
              formConfig:
                fields:
                  - name: code
                    label: Company Code
                    type: text
                  - type: dateRange
                    label: Effective Date
                    name: effectiveDate
                    mode: date-picker
                    setting:
                      format: dd/MM/yyyy
                      type: date
                  - name: status
                    label: Status
                    type: radio
                    radio:
                      - label: Active
                        value: true
                      - label: Inactive
                        value: false
                  - name: shortName
                    label: Short Name
                    type: translation
                  - name: longName
                    label: Long Name
                    type: translation
  sources:
    companyList:
      uri: '"/api/companies/get-by"'
      method: GET
      queryTransform: >-
        {'limit':$.limit,'page':$.page,'filter': [{'field':'search','operator':
        '$eq','value':$.search},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':$.status},{'field':'code','operator':
        '$in','value':$.code}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': {'id': $item.id, 'code': $item.code},
        'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - effectiveDate
        - code
        - search
        - status
    locationGroupsList:
      uri: '"/api/picklists/LocationGroup/values"'
      method: GET
      queryTransform: ' {''filter'': [{''field'':''effectiveDate'',''operator'': ''$eq'',''value'':$.effectiveDate},{''field'':''status'',''operator'': ''$eq'',''value'':true}] }'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})
      params:
        - effectiveDate
      disabledCache: true
    nationsList:
      uri: '"/api/picklists/COUNTRIES/values"'
      method: GET
      queryTransform: ' {''filter'': [{''field'':''effectiveDate'',''operator'': ''$eq'',''value'':$.effectiveDate},{''field'':''status'',''operator'': ''$eq'',''value'':true}] }'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & '(' &
        $item.code & ')', 'value': $item.code}})
      params:
        - effectiveDate
      disabledCache: true
    regionsList:
      uri: '"/api/picklists/REGIONS/values"'
      method: GET
      queryTransform: ' {''filter'': [{''field'':''effectiveDate'',''operator'': ''$eq'',''value'':$.effectiveDate},{''field'':''status'',''operator'': ''$eq'',''value'':true}] }'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & '(' &
        $item.code & ')', 'value': $item.code}})
      params:
        - effectiveDate
      disabledCache: true
    provincesList:
      uri: '"/api/picklists/PROVINCE/values/" & $.country & ""'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'codE501','operator':
        '$eq','value':$.country},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & '(' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - country
        - effectiveDate
    districtsList:
      uri: '"/api/picklists/DISTRICT/values/" & $.provinceCity & ""'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'codE501','operator':
        '$eq','value':$.provinceCity},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true},{'field':'code','operator': '$eq','value':$.code}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & '(' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - provinceCity
        - effectiveDate
        - code
    wardsList:
      uri: '"/api/picklists/WARDS/values/" & $.district & ""'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'codE501','operator':
        '$eq','value':$.district},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & '(' &
        $item.code & ')', 'value': $item.code}})
      disabledCache: true
      params:
        - district
        - effectiveDate
  historyHeaderTitle: '''View History Location '''
  _mode:
    transform: $not($.extend.formType = 'view') ? 'mark-scroll'
filter_config:
  fields:
    - type: text
      label: Location Code
      name: code
      labelType: type-grid
      placeholder: Enter Location Code
    - type: radio
      label: Status
      name: status
      value: ''
      labelType: type-grid
      radio:
        - label: All
          value: ''
        - label: Active
          value: true
        - label: Inactive
          value: false
    - type: text
      label: Long Name
      name: name
      labelType: type-grid
      placeholder: Enter Long Name
    - name: shortName
      label: Short Name
      type: text
      labelType: type-grid
      placeholder: Enter Short Name
    - type: select
      label: Location Group
      name: locationGroupId
      labelType: type-grid
      placeholder: Select Location Group
      mode: multiple
      _select:
        transform: $locationGroupsList()
    - type: selectAll
      label: Company
      name: CompanyCode
      labelType: type-grid
      placeholder: Select Company
      isLazyLoad: true
      _options:
        transform: $companyList($.extend.limit,$.extend.page,$.extend.search)
  filterMapping:
    - field: code
      operator: $cont
      valueField: code
    - field: na_shortName
      operator: $cont
      valueField: shortName
    - field: na_longName
      operator: $cont
      valueField: name
    - field: locationGroupId
      operator: $in
      valueField: locationGroupId.(value)
    - field: CompanyCode
      operator: $in
      valueField: CompanyCode.(value)
    - field: status
      operator: $eq
      valueField: status
  sources:
    locationGroupsList:
      uri: '"/api/picklists/LocationGroup/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})
      disabledCache: true
    companyList:
      uri: '"/api/companies/get-list"'
      method: GET
      queryTransform: >-
        {'limit':$.limit,'page':$.page,'filter': [{'field':'search','operator':
        '$eq','value':$.search}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
layout_options:
  toolTable:
    export: true
    import: true
    adjustDisplay: true
  tool_table:
    - id: import
      icon: icon-upload-simple
    - id: export
      icon: icon-download-simple
  view_after_updated: true
  delete_multi_items: true
  view_history_after_created: true
  hide_action_row: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    title: Edit
    icon: pencil
  - id: delete
    title: Delete
    icon: trash
    group: null
backend_url: /api/locations
screen_name: null
layout_options__actions_many: null
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key: null
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Location
  parent:
    title: Organization Structure
