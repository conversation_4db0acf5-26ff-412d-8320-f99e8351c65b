id: FO.FS.FR.018
status: draft
sort: 199
user_created: 43878dbb-aa6c-4ef4-8bd7-0ea811ffafa7
date_created: '2024-06-14T03:38:28.008Z'
user_updated: 35b4f590-f747-4e53-8fa5-76aba65555c2
date_updated: '2025-07-19T02:34:24.300Z'
title: Cost Center
requirement:
  time: 1718272098579
  blocks:
    - id: 8jZnut9n8n
      type: paragraph
      data:
        text: Chức năng cho phép tạo mới/cập nhật thông tin Cost Center
    - id: fbEPvI5a1j
      type: paragraph
      data:
        text: '&nbsp;Chức năng cho phép tìm kiếm danh mục Cost Center'
  version: 2.29.1
screen_design: null
module: FO
local_fields:
  - code: code
    title: Cost Center Code
    description: >-
      Hiển thị các mã Cost Center đã được tạo trên hệ thống theo tiêu chí tìm
      kiếm
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    pinned: true
    show_sort: true
  - code: shortName
    title: Short Name
    description: >-
      <PERSON><PERSON><PERSON> thị tên viết tắt của Cost Center tương ứng với mã Cost Center theo
      tiêu chí tìm kiếm
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    pinned: false
    show_sort: true
  - code: longName
    title: Long Name
    description: >-
      Hiển thị tên đầy đủ của Cost Center tương ứng với mã Cost Center theo tiêu
      chí tìm kiếm
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: company
    title: Company
    description: >-
      Hiển thị short name của Company tương ứng với mã Cost Center theo tiêu chí
      tìm kiếm
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: status
    title: Status
    description: >-
      Hiển thị trạng thái của Cost Center tương ứng với mã Cost Center theo tiêu
      chí tìm kiếm
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Boolean Tag
      collection: field_types
    show_sort: true
mock_data:
  - status: true
    effectiveDate: 2024/01/01
    code: '00000001'
    shortName:
      default: ES PB8 DEV 1
      vietnamese: ES PB8 DEV 1
      english: ES PB8 DEV 1
    longName:
      default: TTCP PB8 DEV 1
      vietnamese: TTCP PB8 DEV 1
      english: TTCP PB8 DEV 1
    company: FIS
  - status: true
    effectiveDate: 2024/02/01
    code: '00000002'
    shortName:
      default: ES PB22PRE 1
      vietnamese: ES PB22PRE 1
      english: ES PB22PRE 1
    longName:
      default: TTCP ES PB22PRE 1
      vietnamese: TTCP ES PB22PRE 1
      english: TTCP ES PB22PRE 1
    company: FIS
  - status: true
    effectiveDate: 2024/03/01
    code: '00000003'
    shortName:
      default: ES PB22SDD 1
      vietnamese: ES PB22SDD 1
      english: ES PB22SDD 1
    longName:
      default: TTCP ES PB22SDD 1
      vietnamese: TTCP ES PB22SDD 1
      english: TTCP ES PB22SDD 1
    company: FIS
  - status: true
    effectiveDate: 2024/04/01
    code: '00000004'
    shortName:
      default: ES PB8 DEV 2
      vietnamese: ES PB8 DEV 2
      english: ES PB8 DEV 2
    longName:
      default: TTCP PB8 DEV 2
      vietnamese: TTCP PB8 DEV 2
      english: TTCP PB8 DEV 2
    company: FIS
  - status: true
    effectiveDate: 2024/05/01
    code: '00000005'
    shortName:
      default: ES PB22PRE 2
      vietnamese: ES PB22PRE 2
      english: ES PB22PRE 2
    longName:
      default: TTCP ES PB22PRE 2
      vietnamese: TTCP ES PB22PRE 2
      english: TTCP ES PB22PRE 2
    company: FIS
  - status: false
    effectiveDate: 2024/06/01
    code: '00000006'
    shortName:
      default: ES PB22SDD 2
      vietnamese: ES PB22SDD 2
      english: ES PB22SDD 2
    longName:
      default: TTCP ES PB22SDD 2
      vietnamese: TTCP ES PB22SDD 2
      english: TTCP ES PB22SDD 2
    company: FIS
  - status: false
    effectiveDate: 2024/07/01
    code: '00000007'
    shortName:
      default: ES PB8 DEV 3
      vietnamese: ES PB8 DEV 3
      english: ES PB8 DEV 3
    longName:
      default: TTCP PB8 DEV 3
      vietnamese: TTCP PB8 DEV 3
      english: TTCP PB8 DEV 3
    company: FIS
  - status: false
    effectiveDate: 2024/08/01
    code: '00000008'
    shortName:
      default: ES PB22PRE 3
      vietnamese: ES PB22PRE 3
      english: ES PB22PRE 3
    longName:
      default: TTCP ES PB22PRE 3
      vietnamese: TTCP ES PB22PRE 3
      english: TTCP ES PB22PRE 3
    company: FIS
  - status: true
    effectiveDate: 2024/09/01
    code: '00000009'
    shortName:
      default: ES PB22SDD 3
      vietnamese: ES PB22SDD 3
      english: ES PB22SDD 3
    longName:
      default: TTCP ES PB22SDD 3
      vietnamese: TTCP ES PB22SDD 3
      english: TTCP ES PB22SDD 3
    company: FIS
  - status: true
    effectiveDate: 2024/10/01
    code: '00000010'
    shortName:
      default: ES PB8 DEV 4
      vietnamese: ES PB8 DEV 4
      english: ES PB8 DEV 4
    longName:
      default: TTCP PB8 DEV 4
      vietnamese: TTCP PB8 DEV 4
      english: TTCP PB8 DEV 4
    company: FIS
local_buttons: null
layout: layout-table
form_config:
  fields:
    - type: group
      n_cols: 2
      fields:
        - name: code
          label: Cost Center Code
          type: text
          placeholder: Enter Cost Center Code
          _disabled:
            transform: $not($.extend.formType = 'create')
          _condition:
            transform: $not($.extend.formType = 'view')
          validators:
            - type: required
            - type: maxLength
              args: '8'
              text: Maximum 8 characters
            - type: pattern
              args: ^[a-zA-Z0-9_.&]+$
              text: The Code must not contain special characters.
        - type: dateRange
          label: Effective Date
          name: effectiveDate
          mode: date-picker
          placeholder: dd/mm/yyyy
          setting:
            format: dd/MM/yyyy
            type: date
          _value:
            transform: $.extend.formType = 'create' ? $now()
          validators:
            - type: required
              text: Effective date is required.
          _condition:
            transform: $not($.extend.formType = 'view')
        - name: status
          label: Status
          type: radio
          _value:
            transform: $.extend.formType = 'create' and $not($.extend.isDuplicate) ? true
          radio:
            - label: Active
              value: true
            - label: Inactive
              value: false
          _condition:
            transform: $not($.extend.formType = 'view')
        - name: shortName
          label: Short Name
          placeholder: Enter Short Name
          type: translation
          _condition:
            transform: $not($.extend.formType = 'view')
          validators:
            - type: maxLength
              args: '40'
              text: Maximum 40 characters
        - name: longName
          label: Long Name
          placeholder: Enter Long Name
          type: translation
          col: 2
          validators:
            - type: required
            - type: maxLength
              args: '120'
              text: Maximum 120 characters
          _condition:
            transform: $not($.extend.formType = 'view')
        - type: selectCustom
          name: companyObj
          label: Company
          placeholder: Select Company
          col: 2
          outputValue: value
          _validateFn:
            transform: >-
              $exists($.value.code) ?
              ($companyList($.fields.effectiveDate,$.value.code,true)[0] ?
              $companyList($.fields.effectiveDate,$.value.code,true)[0] :
              '_setSelectValueNull')
          _select:
            transform: $companyList($.fields.effectiveDate,null,true)
          _disabled:
            transform: $not($.extend.formType = 'create')
          validators:
            - type: required
          _condition:
            transform: $not($.extend.formType = 'view')
          actions:
            - view
          actionsConfig:
            view:
              formConfig:
                fields:
                  - name: code
                    label: Company Code
                    type: text
                  - type: dateRange
                    label: Effective Date
                    name: effectiveDate
                    mode: date-picker
                    setting:
                      format: dd/MM/yyyy
                      type: date
                  - name: status
                    label: Status
                    type: radio
                    radio:
                      - label: Active
                        value: true
                      - label: Inactive
                        value: false
                  - name: shortName
                    label: Short Name
                    type: translation
                  - name: longName
                    label: Long Name
                    type: translation
    - name: code
      label: Cost Center Code
      type: text
      _condition:
        transform: $.extend.formType = 'view'
    - type: dateRange
      label: Effective Date
      name: effectiveDate
      mode: date-picker
      setting:
        format: dd/MM/YYYY
        type: date
      _condition:
        transform: $.extend.formType = 'view'
    - name: status
      label: Status
      type: radio
      value: true
      radio:
        - label: Active
          value: true
        - label: Inactive
          value: false
      _condition:
        transform: $.extend.formType = 'view'
    - name: shortName
      label: Short Name
      placeholder: Enter Short Name
      type: translation
      _condition:
        transform: $.extend.formType = 'view'
    - name: longName
      label: Long Name
      placeholder: Enter long name
      type: translation
      _condition:
        transform: $.extend.formType = 'view'
    - type: selectCustom
      name: companyObj
      label: Company
      placeholder: Select Company
      outputValue: value
      inputValue: code
      _select:
        transform: $companyList($.fields.effectiveDate)
      _condition:
        transform: $.extend.formType = 'view'
      actions:
        - view
      actionsConfig:
        view:
          formConfig:
            fields:
              - name: code
                label: Company Code
                type: text
              - type: dateRange
                label: Effective Date
                name: effectiveDate
                mode: date-picker
                setting:
                  format: dd/MM/yyyy
                  type: date
              - name: status
                label: Status
                type: radio
                radio:
                  - label: Active
                    value: true
                  - label: Inactive
                    value: false
              - name: shortName
                label: Short Name
                type: translation
              - name: longName
                label: Long Name
                type: translation
  historyHeaderTitle: '''View History Cost Center'''
  sources:
    companyList:
      uri: '"/api/companies/by"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':$.status},{'field':'code','operator':
        '$eq','value':$.code}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id': $item.id, 'code': $item.code},
        'additionalData': $item}})[]
      disabledCache: true
      params:
        - effectiveDate
        - code
        - status
filter_config:
  fields:
    - name: code
      label: Cost Center Code
      type: text
      labelType: type-grid
      placeholder: Enter Cost Center Code
    - name: status
      label: Status
      type: radio
      value: ''
      labelType: type-grid
      radio:
        - label: All
          value: ''
        - label: Active
          value: true
        - label: Inactive
          value: false
    - name: shortName
      label: Short Name
      type: text
      labelType: type-grid
      placeholder: Enter Short Name
    - name: longName
      label: Long Name
      type: text
      labelType: type-grid
      placeholder: Enter Long Name
    - name: companyCode
      label: Company
      type: selectAll
      isLazyLoad: true
      labelType: type-grid
      placeholder: Select Company
      _options:
        transform: $companiesList($.extend.limit,$.extend.page,$.extend.search)
  filterMapping:
    - field: code
      operator: $cont
      valueField: code
    - field: na_shortName
      operator: $cont
      valueField: shortName
    - field: na_longName
      operator: $cont
      valueField: longName
    - field: status
      operator: $eq
      valueField: status
    - field: companyCode
      operator: $in
      valueField: companyCode.(value)
  sources:
    companiesList:
      uri: '"/api/companies/get-by"'
      method: GET
      queryTransform: >-
        {'limit':$.limit,'page':$.page,'filter': [{'field':'search','operator':
        '$eq','value':$.search}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
layout_options:
  toolTable:
    export: true
    import: true
    adjustDisplay: true
  tool_table:
    - id: import
    - id: export
  view_after_updated: true
  delete_multi_items: true
  view_history_after_created: true
  custom_history_backend_url: /api/cost-centers/insert-new-record
  hide_action_row: true
  historyFilterMapping:
    - field: companyCode
      operator: $eq
      valueField: companyCode
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    title: Edit
    icon: pencil
  - id: delete
    title: Delete
    icon: trash
    group: null
backend_url: /api/cost-centers
screen_name: null
layout_options__actions_many: null
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key: null
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Cost Center
  parent:
    title: Organization Structure
