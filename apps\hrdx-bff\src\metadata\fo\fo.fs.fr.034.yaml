id: FO.FS.FR.034
status: draft
sort: 272
user_created: 8ad48761-02ef-4f69-bef6-ac3a6f107610
date_created: '2024-07-15T11:57:21.068Z'
user_updated: 2aa5db12-fa54-46d6-8e05-9e421858de0c
date_updated: '2025-06-12T06:48:36.665Z'
title: Business Title
requirement:
  time: 1744943742041
  blocks:
    - id: AwtI5VKsw1
      type: paragraph
      data:
        text: Business Title&nbsp;
    - id: BiN6bqLwzr
      type: paragraph
      data:
        text: '&nbsp;'
  version: 2.30.7
screen_design: null
module: FO
local_fields:
  - code: code
    pinned: true
    title: Business Title Code
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    options__tabular__column_width: 15
    options__tabular__align: left
    description: |
      Business Title Code
    show_sort: true
  - code: shortName
    title: Short Name
    description: >-
      <PERSON><PERSON><PERSON> thị tên viết tắt của Job Family tương ứng với mã Job Family theo tiêu
      chí tìm kiếm
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    options__tabular__column_width: 15
    options__tabular__align: left
    show_sort: true
  - code: longName
    title: Long Name
    description: >-
      Hiển thị tên đầy đủ của Job Family tương ứng với mã Job Family theo tiêu
      chí tìm kiếm
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    pinned: false
    options__tabular__align: left
    show_sort: true
    options__tabular__column_width: 20
  - code: apply_for_company
    title: Apply For Company
    description: |
      Apply for company
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: status
    title: Status
    description: >
      Hiển thị trạng thái của các Job Family tương ứng với mã Job Family theo
      tiêu chí tìm kiếm
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: Boolean Tag
      collection: field_types
    options__tabular__column_width: 10
    options__tabular__align: center
    show_sort: true
mock_data:
  - code: '00000001'
    shortName:
      default: GĐKVMB
      vietnamese: GĐKVMB
      english: GĐKVMB
    longName:
      default: Giám đốc Khu vực Miền Bắc
      vietnamese: Giám đốc Khu vực Miền Bắc
      english: Giám đốc Khu vực Miền Bắc
    apply_for_company: Công ty TNHH FIS (00000001 - FIS)
    effectiveDate: 01/05/2024
    status: Active
  - code: '00000002'
    shortName:
      default: GĐKVMN
      vietnamese: GĐKVMN
      english: GĐKVMN
    longName:
      default: Giám đốc Khu vực Miền Nam
      vietnamese: Giám đốc Khu vực Miền Nam
      english: Giám đốc Khu vực Miền Nam
    apply_for_company: Công ty TNHH FIS (00000001 - FIS)
    effectiveDate: 02/05/2024
    status: Active
local_buttons: null
layout: layout-table
form_config:
  fields:
    - type: group
      _condition:
        transform: >-
          $.extend.formType = 'create' or $.extend.formType = 'edit' or
          $.extend.formType = 'proceed'
      n_cols: 2
      fields:
        - type: text
          name: code
          label: Business Title Code
          placeholder: Enter Business Title Code
          _disabled:
            transform: $not($.extend.formType = 'create')
          validators:
            - type: required
            - type: maxLength
              args: '8'
              text: Maximum 8 characters
            - type: pattern
              args: ^[a-zA-Z0-9_.&]+$
              text: The Code must not contain special characters.
          _condition:
            transform: >-
              $.extend.formType = 'create' or $.extend.formType = 'edit' or
              $.extend.formType = 'proceed'
        - type: text
          name: code
          label: Business Title Code
          _condition:
            transform: $.extend.formType = 'view'
        - type: dateRange
          label: Effective Date
          name: effectiveDate
          mode: date-picker
          placeholder: Enter Effective Date
          setting:
            format: dd/MM/yyyy
            type: date
          _condition:
            transform: >-
              $.extend.formType = 'edit' or $.extend.formType = 'proceed' or
              $.extend.formType = 'create'
          validators:
            - type: required
          _value:
            transform: $.extend.formType = 'create' ? $now()
        - type: dateRange
          label: Effective Date
          name: effectiveDate
          mode: date-picker
          placeholder: Enter Effective Date
          setting:
            format: dd/MM/yyyy
            type: date
          _condition:
            transform: $.extend.formType = 'view'
        - name: status
          label: Status
          type: radio
          placeholder: Enter Status
          radio:
            - label: Active
              value: true
            - label: Inactive
              value: false
          _condition:
            transform: >-
              $.extend.formType = 'edit' or $.extend.formType = 'proceed' or
              $.extend.formType = 'create'
          _value:
            transform: $.extend.formType = 'create' and $not($.extend.isDuplicate) ? true
        - name: shortName
          label: Short Name
          placeholder: Enter Short Name
          _condition:
            transform: $not($.extend.formType = 'view')
          type: translation
          validators:
            - type: maxLength
              args: '40'
              text: Maximum 40 characters
    - type: text
      name: code
      label: Business Title Code
      _condition:
        transform: $.extend.formType = 'view'
    - type: dateRange
      label: Effective Date
      name: effectiveDate
      mode: date-picker
      _condition:
        transform: $.extend.formType = 'view'
      placeholder: Enter Effective Date
      scale: 1/2
      setting:
        format: dd/MM/yyyy
        type: date
    - name: status
      label: Status
      type: radio
      placeholder: Enter Status
      radio:
        - label: Active
          value: true
        - label: Inactive
          value: false
      _condition:
        transform: $.extend.formType = 'view'
    - name: shortName
      label: Short Name
      placeholder: Enter Short Name
      _condition:
        transform: $.extend.formType = 'view'
      type: translation
      validators:
        - type: maxLength
          args: '40'
          text: Maximum 40 characters
    - name: longName
      label: Long Name
      placeholder: Enter Long Name
      type: translation
      validators:
        - type: required
        - type: maxLength
          args: '120'
          text: Maximum 120 characters
      _condition:
        transform: $not($.extend.formType = 'view')
    - name: longName
      label: Long Name
      placeholder: Enter Long Name
      type: translation
      validators:
        - type: maxLength
          args: '120'
          text: Maximum 120 characters
      _condition:
        transform: $.extend.formType = 'view'
    - type: selectCustom
      label: Apply For Company
      name: companyObj
      _disabled:
        transform: $not($.extend.formType = 'create')
      placeholder: Select Company
      outputValue: value
      actions:
        - view
      actionsConfig:
        view:
          formConfig:
            fields:
              - name: code
                label: Company Code
                type: text
              - type: dateRange
                label: Effective Date
                name: effectiveDate
                mode: date-picker
                setting:
                  format: dd/MM/yyyy
                  type: date
              - name: status
                label: Status
                type: radio
                radio:
                  - label: Active
                    value: true
                  - label: Inactive
                    value: false
              - name: shortName
                label: Short Name
                type: translation
              - name: longName
                label: Long Name
                type: translation
      _validateFn:
        transform: >-
          $exists($.value.code) ?
          ($companiesList($.fields.effectiveDate,$.value.code,true)[0] ?
          $companiesList($.fields.effectiveDate,$.value.code,true)[0] :
          '_setSelectValueNull')
      _select:
        transform: $companiesList($.fields.effectiveDate,null,true)
      validators:
        - type: required
      _condition:
        transform: >-
          $.extend.formType = 'create' or $.extend.formType = 'edit' or
          $.extend.formType = 'proceed'
    - type: selectCustom
      label: Apply For Company
      name: companyObj
      placeholder: Select Company
      outputValue: value
      inputValue: code
      _validateFn:
        transform: >-
          $exists($.value.code) ?
          ($companiesList($.fields.effectiveDate,$.value.code)[0] ?
          $companiesList($.fields.effectiveDate,$.value.code)[0] :
          '_setSelectValueNull')
      actions:
        - view
      actionsConfig:
        view:
          formConfig:
            fields:
              - name: code
                label: Company Code
                type: text
              - type: dateRange
                label: Effective Date
                name: effectiveDate
                mode: date-picker
                setting:
                  format: dd/MM/yyyy
                  type: date
              - name: status
                label: Status
                type: radio
                radio:
                  - label: Active
                    value: true
                  - label: Inactive
                    value: false
              - name: shortName
                label: Short Name
                type: translation
              - name: longName
                label: Long Name
                type: translation
      _select:
        transform: $companiesList($.fields.effectiveDate)
      _condition:
        transform: $.extend.formType = 'view'
  historyHeaderTitle: '''View History Business Title'''
  sources:
    companiesList:
      uri: '"/api/companies/by"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':$.status},{'field':'code','operator':
        '$eq','value':$.code}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id': $item.id, 'code':$item.code},
        'additionalData': $item}})[]
      disabledCache: true
      params:
        - effectiveDate
        - code
        - status
filter_config:
  fields:
    - name: code
      placeholder: Enter Business Title Code
      label: Business Title Code
      labelType: type-grid
      type: text
    - name: status
      label: Status
      type: radio
      value: ''
      labelType: type-grid
      radio:
        - label: All
          value: ''
        - label: Active
          value: true
        - label: Inactive
          value: false
    - name: shortName
      label: Short Name
      labelType: type-grid
      placeholder: Enter Short Name
      type: text
    - name: longName
      label: Long Name
      labelType: type-grid
      placeholder: Enter Long Name
      type: text
    - name: companyCode
      label: Apply For Company
      type: selectAll
      labelType: type-grid
      isLazyLoad: true
      placeholder: Select Company
      _options:
        transform: $companyList($.extend.limit,$.extend.page,$.extend.search)
  filterMapping:
    - field: code
      operator: $cont
      valueField: code
    - field: na_name
      operator: $cont
      valueField: longName
    - field: na_shortName
      operator: $cont
      valueField: shortName
    - field: status
      operator: $eq
      valueField: status
    - field: companyCode
      operator: $in
      valueField: companyCode.(value)
  historyHeaderTitle: '''View History Business Title'''
  sources:
    companyList:
      uri: '"/api/companies/get-by"'
      method: GET
      queryTransform: >-
        {'limit':$.limit,'page':$.page,'filter': [{'field':'status','operator':
        '$eq','value':true},{'field':'search','operator':
        '$eq','value':$.search}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
layout_options:
  toolTable:
    export: true
    import: true
    adjustDisplay: true
  tool_table:
    - id: import
      icon: icon-download-simple-bold
      href: /GE/HR.FS.FR.092
    - id: export
      icon: icon-upload-simple-bold
  view_after_updated: true
  delete_multi_items: true
  view_history_after_created: true
  custom_history_backend_url: /api/business-titles/insert-new-record
  hide_action_row: true
  historyFilterMapping:
    - field: companyCode
      operator: $eq
      valueField: companyCode
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    title: Edit
    icon: icon-pencil-simple-bold
    type: ghost-gray
  - id: delete
    title: Delete
    icon: icon-trash-bold
    group: null
    type: ghost-gray
backend_url: /api/business-titles
screen_name: null
layout_options__actions_many: null
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key: null
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Business Title
  parent:
    title: Job Structure
