id: HR.FR.018.05
status: draft
sort: 2
user_created: 78a455fe-2ca9-4621-b542-98e98ebfefe8
date_created: '2024-07-01T07:27:41.996Z'
user_updated: 2aa5db12-fa54-46d6-8e05-9e421858de0c
date_updated: '2025-07-07T09:38:35.521Z'
title: PIT Code Info
requirement:
  time: 1745381697770
  blocks:
    - id: YmUq6UY44j
      type: paragraph
      data:
        text: PIT Code Info&nbsp;
  version: 2.30.7
screen_design: null
module: HR
local_fields:
  - code: countryName
    title: Country
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: number
    title: Number
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: startDate
    title: Issue Date
    data_type:
      key: dd/mm/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    pinned: false
  - code: issueByName
    title: Issues By
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: status
    title: Status
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: Boolean Tag
      collection: field_types
  - code: isPrimary
    title: Priority
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: YesNo
      collection: field_types
  - code: note
    title: Note
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
mock_data:
  - startDate: '2024-12-03'
    endDate: '2026-12-02'
    country: Việt Nam
    number: '2354366'
    issueDate: '2026-12-02'
    placeOfIssue: Công An Lào Cai
    note: Note
    attachment: ''
  - startDate: '2022-12-03'
    endDate: '2024-12-02'
    country: Việt Nam
    number: '2354366'
    issueDate: '2026-12-02'
    placeOfIssue: Công An Lào Cai
    note: '23'
    attachment: ''
local_buttons: null
layout: layout-widget
form_config:
  formTitle:
    proceed: Add New PIT Code Info
    create: Add New PIT Code Info
    edit: Edit PIT Code Info
    view: PIT Code Info Details
  historyHeaderTitle: '''PIT Code Info Details'''
  fields:
    - type: group
      n_cols: 2
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - type: select
          name: countryCode
          label: Country
          outputValue: value
          placeholder: Enter Country
          isLazyLoad: true
          _defaultValue:
            transform: >-
              $.extend.formType = 'create' and
              $isNilorEmpty($.extend.defaultValue.countryCode) ? ($info :=
              $nationDefault($now(),'VNM') ; $exists($info) ? $info)
          _select:
            transform: >-
              $nationsList($.extend.limit, $.extend.page, $.extend.search,
              $.fields.startDate)
          _validateFn:
            transform: >-
              $.extend.defaultValue ? {'label':
              $.extend.defaultValue.countryName, 'value':
              $.extend.defaultValue.countryCode}
            params:
              updateLabelExistOption: true
          validators:
            - type: required
        - type: text
          label: Number
          name: number
          placeholder: Enter Number
          validators:
            - type: required
            - type: maxLength
              args: 13
              text: Number should not exceed 13 characters
            - type: pattern
              args: ^[0-9]+$
              text: Number must only be numbers
            - type: ppx-custom
              args:
                transform: >-
                  ($length($.fields.number) < 10 or $length($.fields.number) >
                  10) and $.fields.countryCode = 'VNM'
              text: Length must equal to 10 character for country is Viet Nam
        - type: dateRange
          label: Issue Date
          name: startDate
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
          _value:
            transform: $.extend.formType = 'create' ? $now()
          validators:
            - type: ppx-custom
              args:
                transform: >-
                  $exists($.fields.startDate) and
                  $DateDiff($DateFormat($.fields.startDate, 'yyyy-MM-DD'),
                  $DateFormat($now(), 'yyyy-MM-DD'), 'd') > 0
              text: Issue date must be less than now
        - type: select
          name: issueBy
          label: Issue By
          outputValue: value
          isLazyLoad: true
          _select:
            transform: >-
              $ISSUEAUTHORITY($.fields.startDate, $.extend.limit, $.extend.page,
              $.extend.search)
          _validateFn:
            transform: >-
              $.extend.defaultValue.issueByName ? {'label':
              $.extend.defaultValue.issueByName, 'value':
              $.extend.defaultValue.issueBy}
            params:
              updateLabelExistOption: true
          validators:
            - type: required
        - type: radio
          name: status
          label: Status
          value: true
          placeholder: Select Status
          radio:
            - label: Active
              value: true
            - label: Inactive
              value: false
        - type: checkbox
          name: isPrimary
          label: Priority
          customLabelCheckbox: Primary
          value: false
          _value:
            transform: >-
              $.extend.formType = 'create' and
              $exists($.variables._historiesData) ?
              ($not($count($.variables._historiesData) > 0) ? true : false)
    - type: group
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - type: text
          label: Country
          name: countryName
        - type: text
          label: Number
          name: number
        - type: text
          label: Issues By
          name: issueByName
        - type: dateRange
          label: Issue Date
          name: startDate
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
        - type: radio
          name: status
          label: Status
          radio:
            - label: Active
              value: true
            - label: Inactive
              value: false
        - type: radio
          name: isPrimary
          label: Priority
          radio:
            - label: 'Yes'
              value: true
              className: active
            - label: 'No'
              value: false
              className: inactive
    - type: textarea
      name: note
      label: Note
      placeholder: Enter note
      textarea:
        autoSize:
          minRows: 3
        maxCharCount: 1000
      validators:
        - type: maxLength
          args: '1000'
          text: Maximum 1000 characters
  historyTitle: >-
    $DateFormat($.startDate, 'DD/MM/YYYY') & ($.endDate ? ' - ' &
    $DateFormat($.endDate, 'DD/MM/YYYY') : '')
  historyDescription: '$.countryName & '' - '' & ($.status ? ''Active'' : ''Inactive'')'
  footer:
    create: false
    update: true
    createdOn: createdAt
    updatedOn: updatedAt
    createdBy: createdBy
    updatedBy: updatedBy
  sources:
    nationsList:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}], 'limit': $.limit,'page':
        $.page,'search': $.search}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
    nationDefault:
      uri: '"/api/picklists/COUNTRIES/values"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'code','operator':
        '$eq','value':$.code}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[0]
      disabledCache: true
      params:
        - effectiveDate
        - code
    ISSUEAUTHORITY:
      uri: '"/api/picklists/ISSUEAUTHORITYPIT/values/pagination"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page, 'search': $.search, 'filter':
        [{'field':'effectiveDate','operator': '$eq','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data,function($item) {{'label': $item.name.default,'value':
        $item.code}})[]
      disabledCache: true
      params:
        - effectiveDate
        - limit
        - page
        - search
    historiesData:
      uri: '"/api/personals/" & $.empId & "/personal-income-tax-infos/histories"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $[]
      disabledCache: true
      params:
        - empId
        - nationalIdType
  variables:
    _historiesData:
      transform: >-
        $.extend.formType = 'create' and $.extend.params.id1 ?
        $historiesData($.extend.params.id1)
filter_config: {}
layout_options:
  show_dialog_form_save_add_button: true
  is_upload_file: true
  widget_options:
    custom_data_transform: >-
      ($isPrimaryItem := $filter($,function($item){$item.isPrimary = true})[0];
      $lastEffDateItem := $reduce($, function($a, $b){$toMillis($a.startDate) >
      $toMillis($b.startDate) ? $a : $b}); $isPrimaryItem ? $isPrimaryItem :
      $lastEffDateItem )
layout_options__header_buttons:
  - id: create
    title: create
    icon: icon-plus-bold
  - id: history
    title: history
    icon: icon-clock-counter-clockwise-bold
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions: null
backend_url: api/personals/:id1/personal-income-tax-infos
screen_name: null
layout_options__actions_many: null
parent: HR.FS.FR.000
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key: null
screen_designs: []
function_specs: []
fields: []
children: []
menu_item: null
