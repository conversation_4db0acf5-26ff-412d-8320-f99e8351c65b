id: HR.FS.FR.000
status: draft
sort: 214
user_created: 9cfe47ce-3920-4eea-91ec-1d8471b048c5
date_created: '2024-06-19T07:00:52.415Z'
user_updated: 2aa5db12-fa54-46d6-8e05-9e421858de0c
date_updated: '2025-07-24T03:26:57.963Z'
title: Employee Profile
requirement:
  time: *************
  blocks:
    - id: qPgClqjYFP
      type: paragraph
      data:
        text: Employee Profile
  version: 2.30.7
screen_design: null
module: HR
local_fields: null
mock_data:
  - fullName: <PERSON><PERSON><PERSON><PERSON>n An
    portrait: https://picsum.photos/200
    employeeId: '0000001'
    account: Annv
    job: <PERSON><PERSON><PERSON><PERSON> vi<PERSON><PERSON> kế toán
    title: Maketing executive
    department: Phòng kế toán
    company: FIS DDC HN
    employeeType: Employee
    mobilePhone: '*********'
    email: <EMAIL>
    personalInfo: ''
    employment: ''
    capacityProfile: ''
    otherProfile: ''
local_buttons: null
layout: layout-profile
form_config: {}
filter_config: {}
layout_options:
  children_full_width:
    - HR.FS.FR.009
    - HR.FS.FR.002
    - HR.FS.FR.045
    - HR.FS.FR.035
  profile_image_fsId: HR_002
  is_layout_profile: true
  ignore_check_accessType:
    - HR.FS.FR.009
    - HR.FS.FR.045
    - HR.FS.FR.131
  show_employee_record_number: true
  employee_record_number_api_url: '"/api/personals/" & $.employeeId & "/job-datas/employee-unique-ern"'
  ern_transfrom_data: >-
    $map($,function($item){{'label':$item.jobName & ' - ' &
    $item.companyName,'value': $string($item.employeeRecordNumber),
    'organizationalInstanceRcd': $item.organizationalInstanceRcd}})[]
  tabset_menu:
    - title: Personal
      children:
        - id: HR.FS.FR.002
          title: Basic Information
        - id: HR.FS.FR.013
          title: National ID Information
        - id: HR.FR.018.05
          title: PIT Code Info
        - id: HR.FS.FR.022
          title: Email Information
        - id: HR.FS.FR.016
          title: Phone Information
        - id: HR.FS.FR.034
          title: Social Network Information
        - id: HR.FS.FR.003
          title: Address
        - id: HR.FS.FR.035
          title: Family/ Dependent Information
        - id: HR.FR.168
          title: Visa info
        - id: HR.FS.FR.015
          title: Work Permit Info
        - id: HR.FS.FR.031
          title: Permanent Resident Card
        - id: HR.FS.FR.027
          title: Emergency Contact Information
        - id: HR.FS.FR.052
          title: Vehicle Information
        - id: HR.FS.FR.030
          title: Health information
        - id: HR.FS.FR.103
          title: Academic Rank
        - id: HR.FS.FR.018
          title: Bank Details
        - id: HR.FS.FR.026
          title: Other Special Skills
        - id: HR.FS.FR.019
          title: Other Special Information
        - id: HR.FS.FR.097
          title: Retirement policy
        - id: HR.FS.FR.131
          title: Employee Payment Account
    - title: Employment
      children:
        - id: HR.FS.FR.009
          title: Job Data
        - id: HR.FS.FR.010
          title: Employment Seniority
        - id: HR.FS.FR.045
          title: Contract
        - id: HR.FS.FR.028
          title: Other/Previous Employer
    - title: Talent Profile
      children:
        - id: HR.FS.FR.020
          title: Education Infor
        - id: HR.FS.FR.021
          title: Certificate
        - id: HR.FS.FR.024
          title: Competency
        - id: HR.FS.FR.102
          title: Skill
        - id: HR.FS.FR.025
          title: Successor
        - id: HR.FS.FR.033
          title: Courses
        - id: HR.FS.FR.023
          title: Performance result
    - title: Reward and Penalty
      children:
        - id: PR.FS.FR.099
          title: Other Income
        - id: HR.FS.FR.041
          title: Disciplinary information
        - id: HR.FS.FR.040
          title: Reward information
    - title: Other Profile
      children:
        - id: HR.FS.FR.029
          title: Instructor's Teaching Time
        - id: HR.FS.FR.093
          title: Special Private Infor
        - id: HR.FS.FR.051
          title: Membership Card Information
        - id: HR.FS.FR.049
          title: Initiative Information
        - id: HR.FS.FR.050
          title: Social Organization Infor
layout_options__header_buttons: []
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions: null
backend_url: api/personals/:id1
screen_name: null
layout_options__actions_many: null
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: company
    defaultName: CompanyCode
  - name: department
    defaultName: DepartmentCode
  - name: employeeId
    defaultName: EmployeeId
  - name: legalEntity
    defaultName: LegalEntityCode
  - name: bussinessUnit
    defaultName: BusinessUnitCode
  - name: division
    defaultName: DivisionCode
  - name: employeeRecordNumber
    defaultName: EmployeeRecordNumber
  - name: location
    defaultName: LocationCode
  - name: job
    defaultName: JobCode
  - name: employeeLevelCode
    defaultName: EmployeeLevelCode
  - name: employeeSubGroup
    defaultName: EmployeeSubGroupCode
  - name: nationCode
    defaultName: CountryCode
screen_designs: []
function_specs: []
fields: []
children:
  - HR.FS.FR.002
  - HR.FS.FR.013
  - HR.FR.018.05
  - HR.FS.FR.022
  - HR.FS.FR.016
  - HR.FS.FR.034
  - HR.FS.FR.003
  - HR.FS.FR.035
  - HR.FR.168
  - HR.FS.FR.015
  - HR.FS.FR.031
  - HR.FS.FR.027
  - HR.FS.FR.052
  - HR.FS.FR.030
  - HR.FS.FR.103
  - HR.FS.FR.018
  - HR.FS.FR.026
  - HR.FS.FR.019
  - HR.FS.FR.097
  - HR.FS.FR.009
  - HR.FS.FR.045
  - HR.FS.FR.010
  - HR.FS.FR.028
  - HR.FS.FR.020
  - HR.FS.FR.021
  - HR.FS.FR.024
  - HR.FS.FR.025
  - HR.FS.FR.102
  - HR.FS.FR.033
  - HR.FS.FR.023
  - TS.FS.FR.073
  - HR.FS.FR.040
  - HR.FS.FR.029
  - HR.FS.FR.131
  - HR.FS.FR.093
  - HR.FS.FR.041
  - HR.FS.FR.051
  - HR.FS.FR.049
  - PR.FS.FR.099
  - HR.FS.FR.097_01
  - HR.FS.FR.050
  - INS.FR.013_03
  - PR.FS.FR.095
  - PR.FS.FR.093
  - INS.FS.FR.011_03
menu_item:
  title: Employee Profile
  parent:
    title: Manage People
