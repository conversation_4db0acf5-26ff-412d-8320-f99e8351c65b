id: HR.FS.FR.001
status: draft
sort: 140
user_created: 78a455fe-2ca9-4621-b542-98e98ebfefe8
date_created: '2024-06-30T07:01:55.202Z'
user_updated: 56e71b91-dd39-4ebc-afd1-b4f00f38d4a5
date_updated: '2025-06-16T08:23:43.896Z'
title: Manage Picklist
requirement:
  time: 1746774987695
  blocks:
    - id: vD92jMaYvn
      type: paragraph
      data:
        text: Quản lý danh mục chung&nbsp;
  version: 2.30.7
screen_design: null
module: COM
local_fields:
  - code: code
    pinned: true
    title: Picklist Code
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    show_sort: true
  - code: name
    title: Picklist Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: status
    title: Status
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Boolean Tag
      collection: field_types
    show_sort: true
  - code: pickListYN
    pinned: false
    title: Picklist System
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: YesNo
      collection: field_types
    show_sort: true
  - code: linkCatalogTypeName
    title: Parent Picklist
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: modules
    title: Module
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    show_sort: true
    extra_config:
      sortByCode: moduleCount
  - code: companies
    title: Company
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    show_sort: true
    extra_config:
      singular: Company
      plural: Companies
      sortByCode: companyCount
mock_data:
  - code: ActionHR
    name: ERD
    effectiveStartDate: 11/11/2022
    status: true
    parentPicklist: HR
  - code: Gender
    name: Gender
    effectiveStartDate: 11/11/2022
    status: true
    parentPicklist: ''
  - code: Major
    name: Ngành đào tạo
    effectiveStartDate: 11/11/2022
    status: false
    parentPicklist: ''
  - code: Minor
    name: Danh mục chuyên ngành đào tạo
    effectiveStartDate: 11/11/2022
    status: true
    parentPicklist: ''
  - code: Country
    name: Danh mục quốc gia
    effectiveStartDate: 11/11/2022
    status: true
    parentPicklist: ''
  - code: City
    name: Danh mục thành phố
    effectiveStartDate: 11/11/2022
    status: true
    parentPicklist: Country
  - code: School
    name: Trường đào tạo
    effectiveStartDate: 11/11/2022
    status: true
    parentPicklist: ''
local_buttons: null
layout: layout-table
form_config:
  formSize:
    view: largex
  formTitle:
    create: Add New Picklist
    edit: Edit Picklist
  _formTitle:
    view: $.code
  fields:
    - type: group
      n_cols: 2
      fields:
        - type: text
          name: code
          label: Picklist Code
          formatFn:
            - upperCase
          formatByKeydown: true
          placeholder: Enter Code
          _disabled:
            transform: $not($.extend.formType = 'create')
          validators:
            - type: required
            - type: maxLength
              args: '50'
              text: Code should not exceed 50 characters
            - type: pattern
              args: ^[a-zA-Z0-9_.&]*$
              text: The code must not contain spaces and special characters.
            - type: ppx-custom
              args:
                transform: >-
                  $.extend.formType = 'create' and $not($isNilorEmpty($.value))
                  and $isExsits($.value)  
              text: The code already exists.
        - type: text
          name: name
          placeholder: Enter Name
          label: Picklist Name
          validators:
            - type: maxLength
              args: '500'
              text: Picklist Name should not exceed 500 characters
        - type: radio
          label: Status
          name: status
          value: true
          _disabled:
            transform: $.fields.pickListYN
          radio:
            - label: Active
              value: true
            - label: Inactive
              value: false
        - type: text
          label: Parent Picklist
          name: linkCatalogType
          _condition:
            transform: $.extend.formType = 'view'
          _value:
            transform: ' $.fields.linkCatalogTypeName'
        - type: select
          label: Parent Picklist
          name: linkCatalogType
          col: 2
          isLazyLoad: true
          placeholder: Select Parent Picklist Value
          _defaultValue:
            transform: >-
              $exists($.extend.defaultValue.linkCatalogTypeCode) ? {'label':
              $.extend.defaultValue.linkCatalogTypeName, 'value':
              $.extend.defaultValue.linkCatalogTypeCode, 'code':
              $.extend.defaultValue.linkCatalogTypeCode, 'id':
              $.extend.defaultValue.linkCatalogTypeId} 
          _condition:
            transform: $not($.extend.formType = 'view')
          _select:
            transform: >-
              ($data:=$parentPicklist($.extend.search, $.extend.page, 25,
              $.extend.filter.code, $.extend.filter.label); $filter($data,
              function($item){$not($item.code = $.fields.code)})[] )
          _disabled:
            transform: >-
              $.variables._hasValue and
              $not($isNilorEmpty($.extend.defaultValue.linkCatalogTypeCode))
          selectSetting:
            filter:
              fields:
                - name: code
                  type: text
                  label: Code
                - name: label
                  type: text
                  label: Name
            tableFields:
              - name: code
                label: Code
              - name: label
                label: Name
        - type: checkbox
          label: Picklist System
          name: pickListYN
          disabled: true
          value: false
          customLabelCheckbox: 'Yes'
        - type: checkbox
          label: Deletable
          name: deleteYN
          customLabelCheckbox: 'Yes'
          value: true
          _disabled:
            transform: >-
              $.fields.pickListYN or
              $not($.extend.accountPermissions.permissionType = 1)
          _value:
            transform: $.fields.pickListYN ? false
        - type: checkbox
          label: isAllModule
          name: isAllModule
          prefixLabel: true
          unvisible: true
          _value:
            transform: >-
              $.fields.moduleCodes[0] = 'MODULE_ALL' or
              $isNilorEmpty($.fields.moduleCodes)
        - type: checkbox
          label: isAllCompany
          unvisible: true
          name: isAllCompany
          _value:
            transform: >-
              $.fields.companyCodes[0].value = 'COMPANY_ALL' or
              $isNilorEmpty($.fields.companyCodes) 
        - type: select
          name: moduleCodes
          label: Modules
          placeholder: Select Modules
          value:
            - MODULE_ALL
          mode: multiple
          outputValue: value
          valueAll: MODULE_ALL
          _select:
            transform: $.variables._adminModule
        - type: select
          name: companyCodes
          label: Company
          placeholder: Select Company
          mode: multiple
          value:
            - value: COMPANY_ALL
              label: ALL
          isLazyLoad: true
          valueAll: COMPANY_ALL
          _select:
            transform: '  $.extend.page = 1 ? $append({''value'': ''COMPANY_ALL'', ''label'': ''ALL''}, $companiesList($.extend.limit, $.extend.page, $.extend.search)) : $companiesList($.extend.limit, $.extend.page, $.extend.search)'
        - type: text
          name: linkCatalogTypeCode
          dependantField: $.fields.linkCatalogType
          unvisible: true
          _value:
            transform: $.fields.linkCatalogType.code
        - type: text
          name: linkCatalogTypeId
          dependantField: $.fields.linkCatalogType
          unvisible: true
          _value:
            transform: $.fields.linkCatalogType.id
        - type: text
          name: linkCatalogTypeName
          dependantField: $.fields.linkCatalogType
          unvisible: true
          _value:
            transform: $.fields.linkCatalogType.label
        - type: text
          name: id
          unvisible: true
  sources:
    parentPicklist:
      uri: '"/api/picklists-catalog-type"'
      method: GET
      queryTransform: >-
        {'search': $.search, 'limit': $.limit, 'page': $.page, 'filter':
        [{'field':'code','operator':
        '$cont','value':$.code},{'field':'name','operator': '$cont','value':
        $.label}, {'field':'noSearchWithParent','operator': '$eq','value':
        true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'code': $item.code, 'id': $item.id,
        'label': $item.label , 'value': $item.code}  })[]
      disabledCache: true
      params:
        - search
        - page
        - limit
        - code
        - label
    companiesList:
      uri: '"/api/companies"'
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'effectiveDate','operator': '$lte','value':
        $now()},{'field':'status','operator': '$eq','value':true}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    isHasValue:
      uri: '"/api/picklists-values/"& $.picklistCode '
      method: GET
      queryTransform: '{''limit'': 1}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $count($.data)>0
      disabledCache: true
      params:
        - picklistCode
    isExsits:
      uri: '"/api/picklists-catalog-type"'
      method: GET
      queryTransform: >-
        {'limit': 1,  'filter': [{'field':'code','operator':
        '$eq','value':$.picklistCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $count($.data)>0
      disabledCache: true
      params:
        - picklistCode
    adminModule:
      uri: '"/api/menus/permissions"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $distinct($map($, function($v) {{ 'value': $v.module, 'label': $v.title
        }}))[]
      disabledCache: true
  variables:
    _hasValue:
      transform: >-
        $exists($.fields.code) and $.extend.formType = 'edit' ?
        $isHasValue($.fields.code): false
    _adminModule:
      transform: '$append({''value'': ''MODULE_ALL'', ''label'': ''ALL''} , $adminModule())'
filter_config:
  fields:
    - type: text
      label: Picklist Code
      name: code
      placeholder: Enter Picklist Code
      labelType: type-grid
      code: code
    - type: text
      label: Picklist Name
      name: label
      placeholder: Enter Picklist Name
      labelType: type-grid
      code: label
    - type: radio
      label: Picklist System
      name: pickListYN
      labelType: type-grid
      value: ''
      radio:
        - value: ''
          label: All
        - value: true
          label: 'Yes'
        - value: false
          label: 'No'
    - type: radio
      label: Status
      name: status
      labelType: type-grid
      value: ''
      radio:
        - value: ''
          label: All
        - value: true
          label: Active
        - value: false
          label: Inactive
    - type: selectAll
      label: Parent Picklist
      name: parentPicklist
      outputValue: value.code
      labelType: type-grid
      placeholder: Select Parent Picklist Value
      mode: multiple
      isLazyLoad: true
      _options:
        transform: >-
          $parentPicklist($.extend.limit, $.extend.page, $.extend.search,
          $.extend.filter.code, $.extend.filter.label)
    - _disabled:
        transform: 'true'
      type: select
      name: modulesFilter
      label: Modules
      placeholder: Select Modules
      labelType: type-grid
      mode: multiple
      _options:
        transform: $adminModule()
    - _disabled:
        transform: 'true'
      type: select
      name: companiesFilter
      labelType: type-grid
      label: Company
      placeholder: Select Company
      mode: multiple
    - type: select
      name: isAllModule
      value: _setSelectValueNull
      _value:
        transform: >-
          ('MODULE_ALL' in $.variables.modules) ? true :
          $not($exists($.fields.modulesFilter)) ? '_setSelectValueNull' :
          $count($.variables.modules) = 0 ? '_setSelectValueNull' :
          ($count($.variables.modules) > 0 and $not('MODULE_ALL' in
          $.variables.modules)) ? false
      select:
        - label: 'True'
          value: true
        - label: 'False'
          value: false
        - label: 'Null'
          value: _setSelectValueNull
      unvisible: true
    - type: select
      name: isAllCompany
      value: _setSelectValueNull
      _value:
        transform: >-
          ('COMPANY_ALL' in $.variables.companies) ? true :
          $not($exists($.fields.companiesFilter)) ? '_setSelectValueNull' :
          $count($.variables.companies) = 0 ? '_setSelectValueNull' :
          ($count($.variables.companies) > 0 and $not('COMPANY_ALL' in
          $.variables.companies)) ? false
      select:
        - label: 'True'
          value: true
        - label: 'False'
          value: false
        - label: 'Null'
          value: _setSelectValueNull
      unvisible: true
  filterMapping:
    - field: code
      operator: $cont
      valueField: code
    - field: name
      operator: $cont
      valueField: label
    - field: pickListYN
      operator: $eq
      valueField: pickListYN
    - field: status
      operator: $eq
      valueField: status
    - field: linkCatalogTypeCode
      operator: $in
      valueField: parentPicklist
    - field: modulesFilter
      operator: $eq
      valueField: modulesFilter.(value)
    - field: isAllModule
      operator: $eq
      valueField: isAllModule
    - field: isAllCompany
      operator: $eq
      valueField: isAllCompany
    - field: companiesFilter
      operator: $eq
      valueField: companiesFilter.(value)
  sources:
    parentPicklist:
      uri: '"/api/picklists-catalog-type"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'code','operator':
        '$cont','value':$.code},{'field':'name','operator': '$cont','value':
        $.label}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.label, 'value':  {'code':
        $item.code, 'label': $item.label}}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - code
        - label
    companiesList:
      uri: '"/api/companies"'
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'effectiveDate','operator': '$lte','value':
        $now()},{'field':'status','operator': '$eq','value':true}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    adminModule:
      uri: '"/api/menus/permissions"'
      method: GET
      queryTransform: '{''filter'': [{''field'':''status'',''operator'': ''$eq'',''value'':true}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: '$distinct($.{''value'': module, ''label'': title}[])'
      disabledCache: true
  variables:
    companies:
      transform: >-
        $count($.fields.companiesFilter) > 0 ? $map($.fields.companiesFilter,
        function($v) { $v.value })[] : []
    modules:
      transform: >-
        $count($.fields.modulesFilter) > 0 ? $map($.fields.modulesFilter,
        function($v) { $v.value })[] : []
layout_options:
  show_detail_history: false
  show_detail_drawer: false
  show_table_checkbox: false
  show_table_filter: true
  show_table_group: false
  view_after_created: true
  view_after_updated: true
  skip_create_form: true
  hide_action_row: true
  layout_detail_modal_footer_buttons:
    - id: delete
      title: Delete
    - id: deactive
      title: Deactive
      condition_func: $.status = true
    - id: edit
      title: Edit
      type: primary
  precondition_filter:
    auto_filter_take: 1
    form_settings:
      submit_button:
        type: secondary
        title: Apply
      size: auto-fit
      border: true
      padding: 20px
    sync_filter_fields:
      - companiesFilter
      - modulesFilter
      - isAllModule
      - isAllCompany
layout_options__header_buttons:
  - id: create
    title: Create
    icon: icon-plus-bold
    type: primary
options: null
create_form:
  fields:
    - type: group
      n_cols: 2
      fields:
        - type: selectAll
          name: modulesFilter
          label: Modules
          mode: multiple
          placeholder: Select Modules
          _options:
            transform: '$append({''value'': ''MODULE_ALL'', ''label'': ''ALL''} , $adminModule())'
        - type: selectAll
          name: companiesFilter
          label: Company
          placeholder: Select Company
          mode: multiple
          isLazyLoad: true
          _options:
            transform: >-
              $.extend.page = 1 ? $append({'value': 'COMPANY_ALL', 'label':
              'ALL'}, $companiesList($.extend.limit, $.extend.page,
              $.extend.search)) : $companiesList($.extend.limit, $.extend.page,
              $.extend.search)
        - type: select
          name: isAllModule
          value: _setSelectValueNull
          _value:
            transform: >-
              ('MODULE_ALL' in $.variables.modules) ? true :
              $not($exists($.fields.modulesFilter)) ? '_setSelectValueNull' :
              $count($.variables.modules) = 0 ? '_setSelectValueNull' :
              ($count($.variables.modules) > 0 and $not('MODULE_ALL' in
              $.variables.modules)) ? false
          select:
            - label: 'True'
              value: true
            - label: 'False'
              value: false
            - label: 'Null'
              value: _setSelectValueNull
          unvisible: true
        - type: select
          name: isAllCompany
          value: _setSelectValueNull
          _value:
            transform: >-
              ('COMPANY_ALL' in $.variables.companies) ? true :
              $not($exists($.fields.companiesFilter)) ? '_setSelectValueNull' :
              $count($.variables.companies) = 0 ? '_setSelectValueNull' :
              ($count($.variables.companies) > 0 and $not('COMPANY_ALL' in
              $.variables.companies)) ? false
          select:
            - label: 'True'
              value: true
            - label: 'False'
              value: false
            - label: 'Null'
              value: _setSelectValueNull
          unvisible: true
  sources:
    parentPicklist:
      uri: '"/api/picklists-catalog-type"'
      method: GET
      queryTransform: >-
        {'search': $.search,'filter': [{'field':'code','operator':
        '$cont','value':$.code},{'field':'name','operator': '$cont','value':
        $.label}], 'limit': 1000}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.label, 'value':  {'code':
        $item.code, 'label': $item.label}}})[]
      disabledCache: true
      params:
        - search
        - code
        - label
    companiesList:
      uri: '"/api/companies"'
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'effectiveDate','operator': '$lte','value':
        $now()},{'field':'status','operator': '$eq','value':true}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    adminModule:
      uri: '"/api/menus/permissions"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: '$distinct($.{''value'': module, ''label'': title}[])'
      disabledCache: true
  variables:
    companies:
      transform: >-
        $count($.fields.companiesFilter) > 0 ? $map($.fields.companiesFilter,
        function($v) { $v.value })[] : []
    test:
      transform: $count($.variables.companies) = 0
    modules:
      transform: >-
        $count($.fields.modulesFilter) > 0 ? $map($.fields.modulesFilter,
        function($v) { $v.value })[] : []
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    icon: icon-pencil-simple
    type: ghost-gray
  - id: delete
    title: Delete
    icon: icon-trash
    type: ghost-gray
backend_url: api/picklists-catalog-type
screen_name: null
layout_options__actions_many: null
parent: null
detail_function_spec: HR.FS.FR.001_DETAIL
payment_config: {}
multi_typing_config: {}
inherited_default_detail: true
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key: null
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Manage Picklist
  parent:
    title: Manager Common
