id: HR.FS.FR.001_DETAIL
status: draft
sort: 520
user_created: 9cfe47ce-3920-4eea-91ec-1d8471b048c5
date_created: '2024-08-14T04:06:14.243Z'
user_updated: 56e71b91-dd39-4ebc-afd1-b4f00f38d4a5
date_updated: '2025-06-18T02:41:05.630Z'
title: Picklist Detail
requirement:
  time: 1747899408303
  blocks:
    - id: ppr3pf-8Tj
      type: paragraph
      data:
        text: Picklist Detail
  version: 2.30.7
screen_design: null
module: COM
local_fields:
  - code: code
    pinned: true
    title: Code
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: name
    title: Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: status
    title: Status
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Boolean Tag
      collection: field_types
    show_sort: true
  - code: effectiveDate
    title: Start Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    show_sort: true
  - code: effectiveDateTo
    title: End Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    show_sort: true
  - code: linkCatalogDataName
    title: Parent Picklist Value
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
mock_data: null
local_buttons: null
layout: layout-table
form_config:
  formTitle:
    create: Add New Value
    edit: Edit Value
  fields:
    - type: group
      n_cols: 2
      fields:
        - type: translation
          name: coL201
          unvisible: true
        - type: translation
          name: coL202
          unvisible: true
        - type: translation
          name: coL203
          unvisible: true
        - type: translation
          name: coL204
          unvisible: true
        - type: translation
          name: coL205
          unvisible: true
        - type: text
          name: catalogTypeId
          unvisible: true
          _value:
            transform: $.extend.addOnValue.detailId
        - type: text
          label: Code
          name: code
          placeholder: Enter Code
          code: code
          formatFn:
            - upperCase
          formatByKeydown: true
          _disabled:
            transform: $.extend.formType != 'create'
          validators:
            - type: required
            - type: pattern
              args: ^[a-zA-Z0-9_.&]*$
              text: The code must not contain spaces and special characters.
            - type: maxLength
              args: '50'
              text: Code should not exceed 50 characters
            - type: ppx-custom
              args:
                transform: >-
                  $.extend.formType = 'create' and $not($isNilorEmpty($.value))
                  and $not($isNilorEmpty($.fields.effectiveDate)) ?
                  $count($picklistValueByCode($.extend.addOnValue.dataDetail.code,
                  $.value, $.fields.effectiveDate)) > 0
              text: The code already exists.
        - type: translation
          label: Label
          name: name
          placeholder: Enter Label
          validators:
            - type: required
            - type: maxLength
              args: '500'
              text: Label should not exceed 500 characters
          code: name
        - type: select
          label: Parent Picklist Value
          name: parentPicklistValue
          code: parentPicklistValue
          placeholder: Select Parent Picklist Value
          col: 1
          isLazyLoad: true
          _disabled:
            transform: $isNilorEmpty($.fields.picklistCodeParent)
          _select:
            transform: >-
              $boolean($.fields.picklistCodeParent) = true ?
              $picklistValueList($.extend.limit, $.extend.page, $.extend.search,
              $.fields.picklistCodeParent, $.extend.filter.code,
              $.extend.filter.label, $.fields.effectiveDate, false)
          _defaultValue:
            transform: >-
              $exists($.extend.defaultValue.codE501) ? {'label':
              $.extend.defaultValue.linkCatalogTypeData[0].name.default,
              'value': $.extend.defaultValue.codE501, 'code':
              $.extend.defaultValue.codE501, 'id':
              $.extend.defaultValue.coL501} 
          clearFieldsAfterChange:
            - codE501
            - coL501
            - parentValueName
          selectSetting:
            filter:
              fields:
                - name: code
                  type: text
                  label: Code
                - name: label
                  type: text
                  label: Label
            tableFields:
              - name: code
                label: Code
              - name: label
                label: label
        - type: radio
          label: Status
          name: status
          value: true
          radio:
            - label: Active
              value: true
            - label: Inactive
              value: false
        - type: radio
          name: picklistYN
          label: picklistYN
          unvisible: true
          _value:
            transform: 'false'
          radio:
            - value: true
              label: Active
            - value: false
              label: Inactive
        - type: text
          label: PicklistValueCode
          name: codE501
          unvisible: true
          _value:
            transform: >-
              $exists($.fields.parentPicklistValue.code) ?
              $.fields.parentPicklistValue.code : '_setValueNull'
        - type: text
          label: PicklistValueId
          name: coL501
          unvisible: true
          _value:
            transform: >-
              $exists($.fields.parentPicklistValue.id) ?
              $.fields.parentPicklistValue.id : '_setValueNull'
        - type: text
          label: CatalogTypeId
          name: typE501
          unvisible: true
          _value:
            transform: >-
              $.extend.addOnValue.dataDetail.linkCatalogTypeId != '0' ? 
              $.extend.addOnValue.dataDetail.linkCatalogTypeId
        - type: text
          label: picklistCodeParent
          name: picklistCodeParent
          unvisible: true
          _value:
            transform: ' $.extend.addOnValue.dataDetail.linkCatalogTypeCode'
        - type: text
          label: parentValueName
          name: parentValueName
          unvisible: true
          _value:
            transform: >-
              $exists($boolean($.fields.parentPicklistValue)) ?
              $.fields.parentPicklistValue.label : '_setValueNull'
        - type: text
          label: picklistCode
          name: picklistCode
          unvisible: true
          _value:
            transform: $.extend.addOnValue.id
        - type: dateRange
          label: Start Date
          name: effectiveDate
          placeholder: dd/MM/yyyy
          _value:
            transform: $.extend.formType = 'create' ? $now()
          setting:
            format: dd/MM/yyyy
            type: date
          mode: date-picker
          code: effectiveDate
          validators:
            - type: required
            - type: ppx-custom
              args:
                transform: >-
                  ( $.extend.formType = 'edit' and $not($isNilorEmpty($.value))
                  ? ( $lst :=
                  $picklistValueByCode($.extend.addOnValue.dataDetail.code,
                  $.fields.code).from[]; $input :=
                  $.extend.defaultValue.effectiveDate ; $index :=
                  $filter($map($lst, function($v, $i) { $v = $input ? $i : null
                  }), function($v) { $v != null })[0]; $value :=  [ $index > 0 ?
                  $lst[$index - 1] : null, $index < ($count($lst) - 1) ?
                  $lst[$index + 1] : null ]; $DateDiff($.value, $value[0], 'd')
                  >= 0  or   $DateDiff($.value, $value[1], 'd') <= 0 ))
              text: The start date must be less than or equal to the end date.
        - type: dateRange
          label: End Date
          name: effectiveDateTo
          placeholder: dd/MM/yyyy
          setting:
            format: dd/MM/yyyy
            type: date
          mode: date-picker
          code: effectiveDate
          disabled: true
          _value:
            transform: >-
              $exists($.variables._getDateByCodePickList) and
              $exists($.variables._getDateByCodePickList.effectiveDateTo) ?
              $fromMillis($toMillis($.variables._getDateByCodePickList.effectiveDateTo)
              - 86400000) : $exists($.variables._getDateByCodePickList) and
              $not($exists($.variables._getDateByCodePickList.effectiveDateTo))
              ? '9999/12/31' : $not($exists($.variables._getDateByCodePickList))
              ? null
  sources:
    picklistValueList:
      uri: '"/api/picklists-values/" & $.picklistCode & ""'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'search': $.search, 'filter':
        [{'field':'code','operator':
        '$cont','value':$.code},{'field':'name','operator': '$cont','value':
        $.label},{'field':'status','operator': '$eq','value':
        true},{'field':'effectiveDateQuery','operator': '$eq','value':
        $.effectiveDateQuery},{'field':'noFilterEffectiveDate','operator':
        '$eq','value': $.noFilterEffectiveDate}] }
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        [$map($.data, function($item) { {'label': $item.name.default, 'value':
        $item.code, 'code': $item.code , 'id': $item.id }})]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - picklistCode
        - code
        - label
        - effectiveDateQuery
        - noFilterEffectiveDate
    getDateByCodePickList:
      uri: >-
        "/api/picklists-values/" & $.picklistCode & "/get-value-info/" & $.code
        & ""
      method: GET
      queryTransform: >-
        {'filter': [{'field':'effectiveDate','operator': '$eq','value':
        $.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $
      disabledCache: true
      params:
        - picklistCode
        - code
        - effectiveDate
    getDateByCodePickListEditScreen:
      uri: >-
        "/api/picklists-values/" & $.picklistCode & "/get-value-info/" & $.code
        & ""
      method: GET
      queryTransform: >-
        {'filter': [{'field':'effectiveDate','operator': '$eq','value':
        $.effectiveDate}, {'field': 'pickListId', 'operator': '$eq','value':
        $.pickListId}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $
      disabledCache: true
      params:
        - picklistCode
        - code
        - effectiveDate
        - pickListId
    picklistValueByCode:
      uri: '"/api/picklists-values/" & $.picklistCode & ""'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'code','operator':
        '$eq','value':$.code},{'field':'effectiveDate','operator':
        '$eq','value': $.effectiveDate},{'field':'status','operator':
        '$eq','value': true}], 'limit': 9999, 'sort': [{'field':
        'effectiveDate', 'order': 'desc'}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        ($map($.data, function($item){{'from': $item.effectiveDate, 'to': 
        $item.effectiveDateTo}}))[]
      disabledCache: false
      params:
        - picklistCode
        - code
        - effectiveDate
  variables:
    _fullValues:
      transform: >-
        $.extend.formType = 'create' ?
        $picklistValueList($.extend.addOnValue.dataDetail.code)[]
    _picklistValueList:
      transform: >-
        $boolean($.fields.picklistCodeParent) = true ?
        $picklistValueList($.extend.limit, $.extend.page, $.extend.search,
        $.fields.picklistCodeParent, $.extend.filter.code,
        $.extend.filter.label, $.fields.effectiveDate, false)
    _getDateByCodePickList:
      transform: >-
        $.extend.addOnValue.dataDetail.code and $.fields.code ?
        ($.extend.formType = 'create' ?
        $getDateByCodePickList($.extend.addOnValue.dataDetail.code,
        $.fields.code, $.fields.effectiveDate) : $.extend.formType = 'edit' ?
        $getDateByCodePickListEditScreen($.extend.addOnValue.dataDetail.code,
        $.fields.code, $.fields.effectiveDate, $.extend.defaultValue.id))
filter_config:
  fields:
    - type: text
      label: Code
      name: code
      placeholder: Enter Code
      code: code
    - type: text
      label: Name
      name: name
      placeholder: Enter Name
      code: label
    - type: radio
      label: Status
      name: status
      value: ''
      radio:
        - value: ''
          label: All
        - value: true
          label: Active
        - value: false
          label: Inactive
      code: status
    - type: select
      label: Parent Picklist Value
      name: parentPicklist
      code: parentPicklist
      placeholder: Select Parent Picklist Value
      _select:
        transform: >-
          $parentPicklist($.extend.search, $.extend.filter.code,
          $.extend.filter.label,
          $.extend.addOnValue.dataDetail.linkCatalogTypeCode[0])
      selectSetting:
        filter:
          fields:
            - name: code
              type: text
              label: Code
            - name: label
              type: text
              label: Name
        tableFields:
          - name: code
            label: Code
          - name: label
            label: Name
    - name: effectiveDate
      label: Effective Date
      type: dateRange
      mode: date-picker
      placeholder: dd/MM/yyyy
      setting:
        format: dd/MM/yyyy
        type: date
  filterMapping:
    - field: code
      operator: $cont
      valueField: code
    - field: name
      operator: $cont
      valueField: name
    - field: status
      operator: $eq
      valueField: status
    - field: effectiveDate
      operator: $eq
      valueField: effectiveDate
    - field: codE501
      operator: $eq
      valueField: parentPicklist.(code)
  sources:
    parentPicklist:
      uri: '"/api/picklists/"& $.picklistCode &"/admin/values"'
      method: GET
      queryTransform: >-
        {'search': $.search,'filter': [{'field':'code','operator':
        '$cont','value':$.code},{'field':'name','operator': '$cont','value':
        $.label}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'code':
        $item.code, 'id':$item.id, 'value': $item.code}})[]
      disabledCache: true
      params:
        - search
        - code
        - label
        - picklistCode
layout_options:
  disabled_click_row: true
  show_table_group: true
  show_table_checkbox: false
  view_after_updated: false
  view_after_created: false
  show_create_data_table: false
  tool_table:
    - id: create
      icon: icon-plus-bold
      title: Add new value
  page_header_options:
    visible: false
layout_options__header_buttons:
  - id: create
    title: Create
    icon: icon-plus-bold
    type: primary
options: null
create_form: null
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    icon: icon-pencil-simple
    type: ghost-gray
  - id: delete
    title: Delete
    icon: icon-trash
    type: ghost-gray
backend_url: /api/picklists-values/{{parent.code}}
screen_name: null
layout_options__actions_many: null
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key: null
screen_designs: []
function_specs: []
fields: []
children: []
menu_item: null
