id: HR.FS.FR.002
status: draft
sort: 3
user_created: 78a455fe-2ca9-4621-b542-98e98ebfefe8
date_created: '2024-06-13T06:10:50.202Z'
user_updated: 2aa5db12-fa54-46d6-8e05-9e421858de0c
date_updated: '2025-07-29T02:31:33.803Z'
title: Basic Information
requirement:
  time: 1748245525375
  blocks:
    - id: N8ddZ1PBb0
      type: paragraph
      data:
        text: Quản lý thông tin cá nhân của CBNV&nbsp;
  version: 2.30.7
screen_design: null
module: HR
local_fields:
  - code: effectiveDate
    title: Effective Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    options__tabular__column_width: 2
    group: null
  - code: dateOfBirth
    title: Date of birth
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    group: null
    options__tabular__column_width: 2
  - code: genderName
    title: Gender
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    group: null
    options__tabular__column_width: 2
  - code: nationalityName
    title: Nationality
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    group: null
    options__tabular__column_width: 2
  - code: maritalStatusName
    title: Married Status
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    group: null
    options__tabular__column_width: 2
  - code: highestEducationLevelName
    title: Highest Educational Level
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    group: null
    options__tabular__column_width: 2
mock_data:
  - effectiveDate: '2023-02-21'
    firstName: An
    lastName: Nguyễn
    middleName: Văn
    commonName: Nguyễn Văn An
    title: Ông
    gender: Nam
    dateOfBirth: '1998-09-26'
    age: '26'
    dateOfDead: null
    regionOfBirth: Giao Thủy
    townOfBirth: Hoàng Sơn
    ethnic: Kinh
    countryOfBirth: Nam Định
    nationality: Việt Nam
    religion: Không
    marriedStatus: Độc thân
    otherNationality: Nam Định
    highestEducationLevel: Đại học
    asOf: '1998-09-26'
    attachment: CV.docx
  - effectiveDate: '2023-06-18'
    firstName: An
    lastName: Nguyễn
    middleName: Văn
    commonName: Nguyễn Văn An
    title: Ông
    gender: Nam
    dateOfBirth: '1998-09-26'
    age: 26 Years 3 month
    dateOfDead: null
    regionOfBirth: Giao Thủy
    townOfBirth: Hoàng Sơn
    ethnic: Kinh
    countryOfBirth: Nam Định
    nationality: Viet Nam
    religion: Không
    marriedStatus: Độc thân
    otherNationality: ''
    highestEducationLevel: Đại học
    asOf: '1998-09-26'
    attachment: CV.docx
local_buttons: null
layout: layout-widget
form_config:
  _mode:
    transform: $not($.extend.formType = 'view') ? 'mark-scroll'
  formSize:
    create: large
    edit: large
    proceed: large
  formTitle:
    create: Add New Basic Information
    proceed: Add New Basic Information
  fields:
    - type: group
      fields:
        - type: dateRange
          scale: 1/2
          mode: date-picker
          label: Effective Date
          name: effectiveDate
          placeholder: dd/MM/yyyy
          setting:
            format: dd/MM/yyyy
          validators:
            - type: required
            - type: ppx-custom
              args:
                transform: >-
                  $exists($.fields.effectiveDate) ?
                  $DateDiff($DateFormat($now(), 'yyyy-MM-DD'),
                  $DateFormat($.fields.effectiveDate, 'yyyy-MM-DD'), 'd') < 0
              text: The effective date cannot be greater than the current date
          _class:
            transform: $.extend.formType = 'view' ? 'unrequired'
    - type: group
      label: Name Information
      collapse: false
      disableEventCollapse: true
      n_cols: 2
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - type: text
          label: employeeId
          name: employeeId
          _value:
            transform: $.extend.params.id1
          unvisible: true
        - type: text
          label: First Name
          name: firstName
          placeholder: Enter First Name
          _class:
            transform: $.extend.formType = 'view' ? 'unrequired'
          validators:
            - type: required
            - type: maxLength
              args: '40'
              text: First Name should not exceed 40 characters
        - type: text
          label: Middle Name
          name: middleName
          placeholder: Enter Middle Name
          validators:
            - type: maxLength
              args: '40'
              text: Middle Name should not exceed 40 characters
        - type: text
          label: Last Name
          name: lastName
          placeholder: Enter Last Name
          _class:
            transform: $.extend.formType = 'view' ? 'unrequired'
          validators:
            - type: required
            - type: maxLength
              args: '40'
              text: Last Name should not exceed 40 characters
        - type: text
          label: Full Name
          name: fullname
          placeholder: ' '
          unvisible: true
          _value:
            transform: >-
              $join($filter([$.fields.lastName,$.fields.middleName,$.fields.firstName],
              function($it){$boolean($it)}) ,' ')
        - type: text
          label: Full Name
          name: fullnameDisplay
          disabled: true
          placeholder: ' '
          _value:
            transform: $.fields.fullname
        - type: text
          label: Social Name
          name: socialName
          placeholder: Enter Social Name
          _condition:
            transform: $.extend.permission.SocialName.Read
          _disabled:
            transform: $not($.extend.permission.SocialName.Create = true)
          validators:
            - type: maxLength
              args: 120
              text: Social Name should not exceed 120 characters
        - type: text
          label: Special Name
          name: specialName
          placeholder: Enter Special Name
          _condition:
            transform: $.extend.permission.SpecialName.Read
          _disabled:
            transform: $not($.extend.permission.SpecialName.Create = true)
          validators:
            - type: maxLength
              args: 120
              text: Special Name should not exceed 120 characters
        - type: text
          label: Common Name
          name: commonName
          placeholder: Enter Common Name
          col: 2
          validators:
            - type: maxLength
              args: 120
              text: Common Name should not exceed 120 characters
    - type: group
      label: Biographic Information
      collapse: false
      disableEventCollapse: true
      n_cols: 2
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - type: dateRange
          mode: date-picker
          label: Date of Birth
          name: dateOfBirth
          placeholder: dd/MM/yyyy
          setting:
            format: dd/MM/yyyy
          _class:
            transform: $.extend.formType = 'view' ? 'unrequired'
          validators:
            - type: required
            - type: ppx-custom
              id: checkDoB
              args:
                transform: >-
                  $exists($.fields.dateOfBirth) and
                  $exists($.fields.effectiveDate) ?
                  $DateDiff($DateFormat($.fields.effectiveDate, 'YYYY-MM-DD'),
                  $DateFormat($.fields.dateOfBirth, 'YYYY-MM-DD'), 'd') < 0
              text: Date of Birth cannot be later than Effective Date
        - type: text
          label: Age
          name: age
          unvisible: true
          _value:
            transform: >-
              (  $days := $DateDiff($now(), $.fields.dateOfBirth, 'd');  $years
              := $floor($days / 365.25);  $remaining_days := $days - ($years *
              365.25);  $months := $floor($remaining_days / 30.44);  $years & '
              năm ' & $months & ' tháng')
        - type: text
          label: Age
          name: ageDisplay
          placeholder: ' '
          disabled: true
          _value:
            transform: $.fields.age
        - type: button
          name: warningAge
          col: 2
          _toast:
            transform: >-
              $exists($.fields.dateOfBirth) ? ($days := $DateDiff($now(),
              $.fields.dateOfBirth, 'd');  $years := $floor($days / 365.25);
              $years < 18 ?  {'position': 'top','type': 'warning', 'content':
              'Employee is under 18 years old'})
          _condition:
            transform: >-
              $exists($.fields.dateOfBirth) ? ($days := $DateDiff($now(),
              $.fields.dateOfBirth, 'd');  $years := $floor($days / 365.25);
              $years < 18)
        - type: dateRange
          mode: date-picker
          label: Date of Dead
          name: dateOfDeath
          setting:
            format: dd/MM/yyyy
          placeholder: dd/MM/yyyy
          validators:
            - type: ppx-custom
              id: checkBeforeBirthday
              args:
                transform: >-
                  $exists($.fields.dateOfDeath) ?
                  $DateDiff($DateFormat($.fields.dateOfDeath, 'yyyy-MM-DD'),
                  $DateFormat($.fields.dateOfBirth, 'yyyy-MM-DD'), 'd') < 0
              text: The Date Of Death must be later than the Date of birth
        - type: select
          label: Country of Birth
          name: countryOfBirth
          outputValue: value
          validators:
            - type: required
          isLazyLoad: true
          _select:
            transform: $nationsList($.extend.limit, $.extend.page, $.extend.search)
          placeholder: Select Country of birth
          _validateFn:
            transform: >-
              $.extend.defaultValue.birthCountryName ? {'label':
              $.extend.defaultValue.birthCountryName, 'value':
              $.extend.defaultValue.countryOfBirth}
            params:
              updateLabelExistOption: true
        - type: select
          label: City/Province of Birth
          name: regionOfBirth
          outputValue: value
          isLazyLoad: true
          _select:
            transform: >-
              $provincesList($.fields.countryOfBirth,$.extend.limit,
              $.extend.page, $.extend.search)
          placeholder: Select City/Province of Birth
          _validateFn:
            transform: >-
              $.extend.defaultValue.birthStateName ? {'label':
              $.extend.defaultValue.birthStateName, 'value':
              $.extend.defaultValue.regionOfBirth}
            params:
              updateLabelExistOption: true
        - type: text
          label: Birth Location
          name: townOfBirth
          validators:
            - type: maxLength
              args: '120'
              text: Birth Location should not exceed 120 characters
        - type: select
          name: religion
          label: Religion
          placeholder: Select Religion
          outputValue: value
          _select:
            transform: $religionList()
        - type: select
          name: ethnic
          label: Ethnic
          placeholder: Select Ethnic
          outputValue: value
          _select:
            transform: $ethnicList()
        - type: select
          name: nationality
          label: Nationality
          outputValue: value
          isLazyLoad: true
          _select:
            transform: $nationalityList($.extend.limit, $.extend.page, $.extend.search)
          placeholder: Select Nationality
          _class:
            transform: $.extend.formType = 'view' ? 'unrequired'
          validators:
            - type: required
          _validateFn:
            transform: >-
              $.extend.defaultValue.nationalityName ? {'label':
              $.extend.defaultValue.nationalityName, 'value':
              $.extend.defaultValue.nationality}
            params:
              updateLabelExistOption: true
        - type: select
          name: otherNationality
          label: Other Nationality
          placeholder: Select Other Nationality
          outputValue: value
          isLazyLoad: true
          _select:
            transform: >-
              ($data := $nationalityList($.extend.limit, $.extend.page,
              $.extend.search); $filter($data, function($item){ $not($item.value
              =  $.fields.nationality)})[])
          validators:
            - type: ppx-custom
              args:
                transform: >-
                  $boolean($.fields.nationality) and
                  $boolean($.fields.otherNationality) ? $.fields.nationality =
                  $.fields.otherNationality
              text: Other Nationality cannot be the same as Nationality!
          _validateFn:
            transform: >-
              $.extend.defaultValue.otherNationalityName ? {'label':
              $.extend.defaultValue.otherNationalityName, 'value':
              $.extend.defaultValue.otherNationality}
            params:
              updateLabelExistOption: true
    - type: group
      label: Biographical History
      collapse: false
      disableEventCollapse: true
      n_cols: 2
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - type: select
          name: genderCode
          label: Gender
          placeholder: Select Gender
          outputValue: value
          _select:
            transform: $genderList()
          _class:
            transform: $.extend.formType = 'view' ? 'unrequired'
          validators:
            - type: required
        - type: text
          name: highestEducationLevel
          label: Highest educational level
          unvisible: true
        - type: text
          name: highestEducationLevelName
          label: Highest educational level
          disabled: true
        - type: select
          name: marriedStatus
          label: Marrital Status
          placeholder: Select Marrital Status
          outputValue: value
          _select:
            transform: $marriedStatusList()
          validators:
            - type: required
        - type: dateRange
          mode: date-picker
          label: As of
          name: asOf
          setting:
            format: dd/MM/yyyy
          placeholder: dd/MM/yyyy
        - type: upload
          label: Attachment
          name: attachment
          col: 2
          status: true
          upload:
            accept:
              - application/pdf
              - >-
                application/vnd.openxmlformats-officedocument.wordprocessingml.document
              - application/msword
              - >-
                application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
              - application/vnd.ms-excel
              - image/jpeg
              - image/png
            size: 100
            isMultiple: true
            customContentUpload: >-
              or drop it here PDF, XLS, XLSX, DOC, DOCX, JPG, PNG only (Max
              100MB)
          _condition:
            transform: $not($.extend.formType = 'view')
        - type: upload
          name: attachmentResults
          readOnly: true
          canAction: true
          hiddenLabel: true
          status: true
          col: 2
          _condition:
            transform: $.extend.formType = 'edit'
        - name: deleteFileIds
          unvisible: true
          type: text
          _value:
            transform: >-
              $.extend.formType = 'edit' ? ($originalArray :=
              $map($.extend.defaultValue.attachmentResults,
              function($it){$string($it.key)})[]; $updatedArray :=
              $map($.fields.attachmentResults,
              function($it){$string($it.key)})[]; $difference($originalArray,
              $updatedArray))
        - type: upload
          label: Attachment
          name: attachmentResults
          status: true
          _condition:
            transform: $.extend.formType = 'view'
    - type: group
      label: Name Information
      collapse: false
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - type: text
          label: First Name
          name: firstName
        - type: text
          label: Middle Name
          name: middleName
        - type: text
          label: Last Name
          name: lastName
        - type: text
          label: Full Name
          name: fullname
          _value:
            transform: >-
              $.fields.lastName & ($.fields.middleName ? ' ' &
              $.fields.middleName : '') & ' ' & $.fields.firstName
        - type: text
          label: Social Name
          name: socialName
          _condition:
            transform: $.extend.permission.SocialName.Read
        - type: text
          label: Special Name
          name: specialName
          _condition:
            transform: $.extend.permission.SpecialName.Read
        - type: text
          label: Common Name
          name: commonName
    - type: group
      label: Biographic Information
      collapse: false
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - type: dateRange
          mode: date-picker
          label: Date of Birth
          name: dateOfBirth
          placeholder: dd/MM/yyyy
          setting:
            format: dd/MM/yyyy
        - type: text
          label: Age
          name: age
          _value:
            transform: >-
              (  $days := $DateDiff($now(), $.fields.dateOfBirth, 'd');  $years
              := $floor($days / 365.25);  $remaining_days := $days - ($years *
              365.25);  $months := $floor($remaining_days / 30.44);  $years & '
              năm ' & $months & ' tháng')
        - type: dateRange
          mode: date-picker
          label: Date of Dead
          name: dateOfDeath
          setting:
            format: dd/MM/yyyy
          placeholder: dd/MM/yyyy
        - type: text
          label: Country of Birth
          name: birthCountryName
        - type: text
          label: City/Province of Birth
          name: birthStateName
        - type: text
          label: Birth Location
          name: townOfBirth
        - type: text
          name: religionName
          label: Religion
        - type: text
          name: ethnicName
          label: Ethnic
          placeholder: Select Ethnic
        - type: text
          name: nationalityName
          label: Nationality
        - type: text
          name: otherNationalityName
          label: Other Nationality
    - type: group
      label: Biographical History
      collapse: false
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - type: text
          name: genderName
          label: Gender
          placeholder: Select Gender
        - type: text
          name: highestEducationLevelName
          label: Highest educational level
        - type: text
          name: maritalStatusName
          label: Marrital Status
          placeholder: Select Marrital Status
        - type: dateRange
          mode: date-picker
          label: As of
          name: asOf
          setting:
            format: dd/MM/yyyy
          placeholder: dd/MM/yyyy
        - type: upload
          label: Attachment
          name: attachmentResults
          status: true
          _condition:
            transform: $.extend.formType = 'view'
  sources:
    nationsList:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    nationalityList:
      uri: '"/api/picklists/NATIONALITY/values/pagination"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    educationInfos:
      uri: '"/api/personals/" & $.empId & "/basic-infomation"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: '[$]'
      disabledCache: true
      params:
        - empId
    provincesList:
      uri: '"/api/picklists/PROVINCE/values/" & $.country & "/pagination"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - country
        - limit
        - page
        - search
    marriedStatusList:
      uri: '"/api/picklists/MARITALSTATUS/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
    religionList:
      uri: '"/api/picklists/RELIGION/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
    genderList:
      uri: '"/api/picklists/GENDER/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
    genderTitleList:
      uri: '"/api/picklists/GenderTitle/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
    ethnicList:
      uri: '"/api/picklists/ETHNIC/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
  footer:
    create: false
    update: true
    createdOn: createdAt
    updatedOn: updatedAt
    createdBy: createdBy
    updatedBy: updatedBy
  historyTitle: >-
    $DateFormat($.effectiveDate, 'DD/MM/YYYY') & ($.effectiveDateTo ? ' - ' &
    $DateFormat($CalDate($.effectiveDateTo,-1,'d'), 'DD/MM/YYYY') : '')
  historyDescription: '$.isCreate ? ''Create'' : ''Modify'''
  not_submit_if_no_change: true
filter_config: {}
layout_options:
  is_upload_file: true
  widget_full_width: true
  n_cols: 1
  show_history_cancel_button: true
  widget_options:
    show_more_type: open-dialog
    refreshProfile: true
  is_new_dynamic_form: true
  get_children_action_permission: true
layout_options__header_buttons:
  - id: edit
    title: edit
    icon: icon-pencil-simple-bold
  - id: history
    title: history
    icon: icon-clock-counter-clockwise-bold
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions: null
backend_url: api/personals/:id1/basic-infomation
screen_name: basic-information
layout_options__actions_many: null
parent: HR.FS.FR.000
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: company
    defaultName: CompanyCode
  - name: department
    defaultName: DepartmentCode
  - name: employeeId
    defaultName: EmployeeId
  - name: legalEntityCode
    defaultName: LegalEntityCode
  - name: bussinessUnit
    defaultName: BusinessUnitCode
  - name: division
    defaultName: DivisionCode
  - name: employeeRecordNumber
    defaultName: EmployeeRecordNumber
  - name: location
    defaultName: LocationCode
  - name: job
    defaultName: JobCode
  - name: employeeLevelCode
    defaultName: EmployeeLevelCode
  - name: employeeSubGroup
    defaultName: EmployeeSubGroupCode
  - name: nationCode
    defaultName: CountryCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item: null
