id: HR.FS.FR.003
status: draft
sort: 6
user_created: 78a455fe-2ca9-4621-b542-98e98ebfefe8
date_created: '2024-06-13T07:15:35.217Z'
user_updated: 2aa5db12-fa54-46d6-8e05-9e421858de0c
date_updated: '2025-06-20T08:25:02.312Z'
title: Address
requirement:
  time: 1745381158504
  blocks:
    - id: _nPfWdTw6o
      type: paragraph
      data:
        text: Manage employee address information&nbsp;
  version: 2.30.7
screen_design: null
module: HR
local_fields:
  - code: startDate
    title: Effective date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
  - code: addressTypeName
    pinned: false
    title: Address Type
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: addressDetail
    title: Address
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: zipCode
    title: ZIP Code
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
mock_data: null
local_buttons: null
layout: layout-widget
form_config:
  isFilterInLeftSidebar: true
  not_submit_if_no_change: true
  formTitle:
    create: Add New Address
    view: Address Details
  fields:
    - type: group
      n_cols: 2
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - type: dateRange
          name: startDate
          mode: date-picker
          label: Effective Date
          validators:
            - type: required
          _condition:
            transform: $.extend.formType = 'create'
          _value:
            transform: >-
              ($count($.variables._addressList) = 0 ) ?
              $DateFormat($.variables._effectiveDate,'yyyy-MM-DD') : $now()
        - type: dateRange
          name: startDate
          mode: date-picker
          label: Effective Date
          validators:
            - type: required
          _condition:
            transform: $.extend.formType = 'edit'
        - type: select
          name: addressType
          label: Address Type
          placeholder: Select Address Type
          outputValue: value
          _select:
            transform: $addressTypesList($.fields.startDate)
          validators:
            - type: required
        - type: select
          name: country
          label: Country
          placeholder: Select Country
          outputValue: value
          isLazyLoad: true
          _select:
            transform: >-
              $nationsList($.extend.limit, $.extend.page, $.extend.search,
              $.fields.startDate)
          _validateFn:
            transform: >-
              $.extend.defaultValue.countryName ? {'label':
              $.extend.defaultValue.countryName, 'value':
              $.extend.defaultValue.country}
            params:
              updateLabelExistOption: true
          _value:
            transform: >-
              $.extend.formType = 'create' and
              $isNilorEmpty($.extend.defaultValue.country) ? ($info :=
              $nationDefault($now(),'VNM') ; $exists($info) ? $info)
          validators:
            - type: required
          clearFieldsAfterChange:
            - city
            - district
            - ward
        - type: select
          name: city
          label: City
          placeholder: Select City
          outputValue: value
          isLazyLoad: true
          _select:
            transform: >-
              $.fields.country ? $citiesList($.fields.country, $.extend.limit,
              $.extend.page, $.extend.search, $.fields.startDate)
          _validateFn:
            transform: >-
              $.extend.defaultValue.cityName ? {'label':
              $.extend.defaultValue.cityName, 'value':
              $.extend.defaultValue.city}
            params:
              updateLabelExistOption: true
          validators:
            - type: required
          clearFieldsAfterChange:
            - district
            - ward
        - type: select
          name: district
          label: District
          placeholder: Select District
          outputValue: value
          isLazyLoad: true
          _select:
            transform: >-
              $.fields.city ? $districtsList($.fields.city, $.extend.limit,
              $.extend.page, $.extend.search, $.fields.startDate) : null
          _validateFn:
            transform: >-
              $.extend.defaultValue.districtName ? {'label':
              $.extend.defaultValue.districtName, 'value':
              $.extend.defaultValue.district}
            params:
              updateLabelExistOption: true
          _value:
            transform: >-
              ($list:=$districtsList($.fields.city, $.extend.limit,
              $.extend.page, $.extend.search,
              $.fields.startDate);$.extend.formType = 'create' and $count($list)
              = 1 ? $list[0])
          clearFieldsAfterChange:
            - ward
        - type: select
          name: ward
          label: Ward
          placeholder: Select Ward
          outputValue: value
          isLazyLoad: true
          _select:
            transform: >-
              $.fields.district ? $wardsList($.fields.district, $.extend.limit,
              $.extend.page, $.extend.search, $.fields.startDate) : null
          _validateFn:
            transform: >-
              $.extend.defaultValue.wardName ? {'label':
              $.extend.defaultValue.wardName, 'value':
              $.extend.defaultValue.ward}
            params:
              updateLabelExistOption: true
          _value:
            transform: >-
              ($list:=$wardsList($.fields.district, $.extend.limit,
              $.extend.page, $.extend.search,
              $.fields.startDate);$.extend.formType = 'create' and $count($list)
              = 1 ? $list[0])
        - type: text
          label: Address
          name: address
          placeholder: Enter Address
          validators:
            - type: maxLength
              maxLength: 120
              text: Maximum 120 characters
        - type: text
          label: ZIP Code
          name: zipCode
          validators:
            - type: maxLength
              maxLength: 40
              text: Maximum 40 characters
          placeholder: Enter ZIP Code
    - type: dateRange
      name: startDate
      mode: date-picker
      label: Effective Date
      _condition:
        transform: $.extend.formType = 'view'
    - type: text
      name: addressTypeName
      label: Address Type
      _condition:
        transform: $.extend.formType = 'view'
    - type: text
      name: countryName
      label: Country
      _condition:
        transform: $.extend.formType = 'view'
    - type: text
      name: cityName
      label: City
      _condition:
        transform: $.extend.formType = 'view'
    - type: text
      name: districtName
      label: District
      _condition:
        transform: $.extend.formType = 'view'
    - type: text
      name: wardName
      label: Ward
      _condition:
        transform: $.extend.formType = 'view'
    - type: text
      label: Address
      name: address
      _condition:
        transform: $.extend.formType = 'view'
    - type: text
      label: ZIP Code
      name: zipCode
      _condition:
        transform: $.extend.formType = 'view'
      placeholder: Input ZIP Code
  sources:
    addressList:
      uri: '"/api/personals/" & $.empId & "/personal-addresses/"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $[]
      disabledCache: true
      params:
        - empId
    basicInformation:
      uri: '"/api/personals/" & $.empId'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $[]
      disabledCache: true
      params:
        - empId
    addressTypesList:
      uri: '"/api/picklists/ADDRESSTYPE/values"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - effectiveDate
    nationsList:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}], 'limit': $.limit,'page':
        $.page,'search': $.search}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
    nationDefault:
      uri: '"/api/picklists/COUNTRIES/values"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'code','operator':
        '$eq','value':$.code}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[0]
      disabledCache: true
      params:
        - effectiveDate
        - code
    citiesList:
      uri: '"/api/picklists/PROVINCE/values/" & $.country & "/pagination"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}], 'limit': $.limit,'page':
        $.page,'search': $.search}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - country
        - limit
        - page
        - search
        - effectiveDate
    districtsList:
      uri: '"/api/picklists/DISTRICT/values/"& $.city & "/pagination"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}], 'limit': $.limit,'page':
        $.page,'search': $.search}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - city
        - limit
        - page
        - search
        - effectiveDate
    wardsList:
      uri: '"/api/picklists/WARDS/values/"& $.district & "/pagination"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}], 'limit': $.limit,'page':
        $.page,'search': $.search}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - district
        - limit
        - page
        - search
        - effectiveDate
  historyTitle: $DateFormat($.startDate, 'DD/MM/YYYY')
  historyDescription: $.addressTypeName & ' - ' & $.cityName
  historyStatusName: $.statusName
  footer:
    create: false
    update: true
    createdOn: createdOn
    updatedOn: updatedAt
    createdBy: createdBy
    updatedBy: updatedBy
  variables:
    _addressList:
      transform: '$.extend.params.id1 ? $addressList($.extend.params.id1) : []'
    _effectiveDate:
      transform: >-
        $.extend.params.id1 ?
        $basicInformation($.extend.params.id1)[0].effectiveDate
filter_config:
  fields:
    - type: group
      padding: 12px 0 0 0
      fields:
        - type: select
          name: addressType
          outputValue: value
          placeholder: Select Address Type
          _select:
            transform: $.extend.params.id1 ? $addressTypesList($.extend.params.id1)
  filterMapping:
    - field: addressType
      operator: $eq
      valueField: addressType
  sources:
    addressTypesList:
      uri: '"/api/personals/" & $.empId & "/personal-addresses/histories"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $distinct($map($, function($item) {{'label': $item.addressTypeName,
        'value': $item.addressType}})[])[]
      disabledCache: true
      params:
        - empId
layout_options:
  filterType: history
  show_dialog_form_save_add_button: true
  view_history_after_created: false
  filter_history_method: manual
  show_history_cancel_button: true
layout_options__header_buttons:
  - id: create
    title: create
    icon: icon-plus-bold
  - id: history
    title: history
    icon: icon-clock-counter-clockwise-bold
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions: null
backend_url: api/personals/:id1/personal-addresses
screen_name: null
layout_options__actions_many: null
parent: HR.FS.FR.000
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key: null
screen_designs: []
function_specs: []
fields: []
children: []
menu_item: null
