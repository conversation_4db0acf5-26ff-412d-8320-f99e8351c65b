id: HR.FS.FR.006_01
status: draft
sort: 345
user_created: 2aa5db12-fa54-46d6-8e05-9e421858de0c
date_created: '2024-07-17T02:25:37.495Z'
user_updated: 56e71b91-dd39-4ebc-afd1-b4f00f38d4a5
date_updated: '2025-07-25T04:20:26.241Z'
title: Person Duplicate Check
requirement:
  time: 1743390061424
  blocks:
    - id: 9AIZ3Tptc6
      type: paragraph
      data:
        text: >-
          Chứ<PERSON> năng cho phép bộ phận nhân sự Tập đoàn/CTTV tìm kiếm kiểm tra
          nhân viên đã có trên hệ thống.
    - id: Ok0PWMXIv3
      type: paragraph
      data:
        text: >-
          Hỗ trợ việc kiểm tra trùng thông tin trong quá trình tuyển dụng nhân
          viên mới
  version: 2.30.7
screen_design: null
module: HR
local_fields:
  - code: employeeId
    title: Employee ID
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    pinned: false
    show_sort: true
  - code: fullName
    title: Full Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: AvatarWithLabel
      collection: field_types
    pinned: false
    options__tabular__column_width: 12.5
    show_sort: true
    extra_config:
      isShowAvatar: true
  - code: dateOfBirth
    title: Date of Birth
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    pinned: false
    show_sort: true
  - code: gender
    title: Gender
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    pinned: false
    show_sort: false
  - code: nationalities
    title: Nationality
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tags
      collection: field_types
    pinned: false
    show_sort: false
    options__tabular__column_width: null
  - code: personalEmails
    title: Personal email
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tags
      collection: field_types
    pinned: false
    show_sort: false
    options__tabular__column_width: 15
  - code: businessEmails
    title: Business email
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tags
      collection: field_types
    show_sort: false
    options__tabular__column_width: 15
  - code: nationalIDInformation
    title: National ID Information
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tags
      collection: field_types
    pinned: false
    options__tabular__column_width: 20
    show_sort: false
  - code: nationalitiesPassport
    title: Passport
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tags
      collection: field_types
    pinned: false
    options__tabular__column_width: 20
    options__tabular__align: center
    show_sort: false
  - code: pitCodes
    title: PIT Code
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tags
      collection: field_types
    pinned: false
    show_sort: false
    options__tabular__column_width: 15
  - code: phoneNumbers
    title: Phone Number
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tags
      collection: field_types
    pinned: false
    show_sort: false
  - code: company
    title: Company
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    pinned: false
    show_sort: false
    options__tabular__column_width: 20
  - code: employeeGroup
    title: Person Type
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    pinned: false
    show_sort: false
  - code: HRStatus
    title: HR Status
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    pinned: false
    show_sort: false
  - code: terminateDate
    title: Terminate Date
    data_type:
      key: Date
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    pinned: false
    show_sort: false
  - code: blacklistBlocklist
    title: Blacklist/Blocklist
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    pinned: false
    options__tabular__column_width: 10
    show_sort: false
  - code: blacklistBlocklistNote
    title: Blacklist/Blocklist Note
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    pinned: false
    options__tabular__column_width: 15
    show_sort: false
mock_data:
  - firstName: Nguyen
    middleName: Van
    lastName: A
    fullName: Nguyen Van A
    employeeID: E001
    dateOfBirth: 01/01/1990
    gender: Male
    nationality: Vietnamese
    email: <EMAIL>
    cccd: '*********'
    cmnd: '*********'
    passport: ********
    pitCode: '*********0'
    phone: '0*********'
    company: ABC Corp
    personType: Full-time
    HRStatus: Active
    terminateDate: 30/06/2023
    blacklistBlocklist: 'Yes'
    blacklistBlocklistNote: Violation of company policies
  - firstName: Tran
    middleName: Thi
    lastName: B
    fullName: Tran Thi B
    employeeID: E002
    dateOfBirth: 15/05/2015
    gender: Female
    nationality: Vietnamese
    email: <EMAIL>
    cccd: '*********'
    cmnd: '*********'
    passport: ********
    pitCode: '*********6'
    phone: '0*********'
    company: XYZ Ltd
    personType: Part-time
    HRStatus: Active
    terminateDate: 30/06/2023
    blacklistBlocklist: 'Yes'
    blacklistBlocklistNote: Violation of company policies
  - firstName: Le
    middleName: Thi
    lastName: C
    fullName: Le Van C
    employeeID: E003
    dateOfBirth: 20/09/2009
    gender: Male
    nationality: Vietnamese
    email: <EMAIL>
    cccd: '*********'
    cmnd: '*********'
    passport: ********
    pitCode: '*********7'
    phone: '0912345678'
    company: LMN Inc
    personType: Contractor
    HRStatus: Inactive
    terminateDate: 30/06/2023
    blacklistBlocklist: 'Yes'
    blacklistBlocklistNote: Violation of company policies
local_buttons: null
layout: layout-table
form_config:
  title: Job data
  fields:
    - label: Organizational Instance Rcd
      type: number
      name: organizationalInstanceRcd
      scale: 1/2
      disabled: true
      value: 0
      validators:
        - type: required
    - type: group
      label: Action
      collapse: false
      fields:
        - name: employeeNumberRecord
          label: Employee Number Record
          type: number
          disabled: true
          value: 1
          scale: 1/2
          validators:
            - type: required
        - name: jobIndicator
          label: Job Indicator
          type: select
          scale: 1/2
          validators:
            - type: required
          value: Primary
          disabled: true
          select:
            - label: Primary
              value: Primary
        - name: action
          label: Action
          type: select
          scale: 1/2
          validators:
            - type: required
          value: Rehire
          select:
            - label: Rehire
              value: Rehire
        - name: reason
          label: Reason
          type: select
          scale: 1/2
          validators:
            - type: required
          select:
            - label: Rehire in company
              value: Rehire in company
        - name: effectiveSequence
          label: Effective Sequence
          type: number
          value: 0
          scale: 1/2
          validators:
            - type: required
        - name: HRStatus
          label: HR Status
          type: select
          scale: 1/2
          validators:
            - type: required
          disabled: true
          value: true
          select:
            - label: Active
              value: true
        - name: payrollStatus
          label: Payroll Status
          type: select
          scale: 1/2
          validators:
            - type: required
          select:
            - label: Active
              value: true
            - label: Inactive
              value: false
        - name: employeeGroup
          label: Employee Group
          type: select
          scale: 1/2
          validators:
            - type: required
          value: Employee
          select:
            - label: Employee
              value: Employee
        - name: employeeSubGroup
          label: Employee Sub Group
          type: select
          scale: 1/2
          validators:
            - type: required
          select:
            - label: Manager
              value: Manager
        - name: levelOfDecision
          label: Level Of Decision
          type: select
          scale: 1/2
          select:
            - label: '1'
              value: '1'
        - type: dateRange
          name: signDate
          label: Sign Date
          mode: date-picker
          scale: 1/2
          setting:
            format: dd/MM/yyyy
            type: date
        - type: dateRange
          name: expectedEndDate
          label: Expected End Date
          scale: 1/2
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
        - name: note
          label: Note
          type: textarea
          placeholder: Enter Note
          textarea:
            autoSize:
              minRows: 3
    - type: group
      label: Assignment
      collapse: false
      fields:
        - name: position
          label: Position
          type: select
          select:
            - label: Manager
              value: Manager
        - name: company
          label: Company
          type: select
          scale: 1/2
          validators:
            - type: required
          select:
            - value: Công ty TNHH FPT IS(FIS)
              label: Công ty TNHH FPT IS(FIS)
        - name: legalEntity
          label: Legal Entity
          type: select
          scale: 1/2
          validators:
            - type: required
          select:
            - value: Công ty TNHH FPT IS(FIS)
              label: Công ty TNHH FPT IS(FIS)
        - name: businessUnit
          label: Business Unit
          type: select
          scale: 1/2
          validators:
            - type: required
          select:
            - value: Khối doanh nghiệp (FIS)
              label: Khối doanh nghiệp (FIS)
        - name: division
          label: Division
          type: select
          scale: 1/2
          validators:
            - type: required
          select:
            - value: PeopleX
              label: PeopleX
        - name: department
          label: Department
          type: select
          validators:
            - type: required
          select:
            - value: Trung tâm giải pháp NNL
              label: Trung tâm giải pháp NNL
        - name: job
          label: Job
          type: select
          scale: 1/2
          validators:
            - type: required
          select:
            - value: Trưởng phòng
              label: Trưởng phòng
        - name: FTE
          label: FTE
          type: text
          scale: 1/2
          validators:
            - type: required
        - name: businessTitle
          label: Business Title
          type: select
          scale: 1/2
          select:
            - value: Quản trị dự án
              label: Quản trị dự án
        - name: careerStream
          label: Career Stream
          type: select
          scale: 1/2
          select:
            - value: Management (M)
              label: Management (M)
        - name: careerBand
          label: Career Band
          type: select
          scale: 1/2
          select:
            - value: Quản lý (M2)
              label: Quản lý (M2)
        - name: jobLevel
          label: Job Level
          type: select
          scale: 1/2
          select:
            - value: '3'
              label: '3'
        - name: empLevel
          label: Emp level
          type: select
          scale: 1/2
          select:
            - value: 5.3M
              label: 5.3M
        - name: costCenter
          label: Cost Center
          type: select
          scale: 1/2
          select:
            - value: P3
              label: P3
        - name: location
          label: Location
          type: select
          scale: 1/2
          select:
            - value: Keangnam
              label: Keangnam
        - name: region
          label: Region
          type: select
          scale: 1/2
          select:
            - value: Miền Bắc
              label: Miền Bắc
        - name: timeZone
          label: Time Zone
          type: select
          scale: 1/2
          select:
            - value: HCM (GMT-7:00)
              label: HCM (GMT-7:00)
        - name: departmentEntryDate
          label: Department Entry Date
          type: dateRange
          scale: 1/2
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
        - name: reportToPos
          label: Report To Pos
          type: select
          scale: 1/2
          select:
            - value: Giám đôc trung tâm PB18 (50000000001)
              label: Giám đôc trung tâm PB18 (50000000001)
        - name: supervisor
          label: Supervisor
          type: select
          select:
            - value: Nguyễn Thị Hà (0000000001)
              label: Nguyễn Thị Hà (0000000001)
        - name: matrixReportTo
          label: Matrix Report To
          type: select
          scale: 1/2
          mode: multiple
          select:
            - value: Giám đốc trung tâm
              label: Giám đốc trung tâm
        - name: matrixManager
          label: Matrix Manager
          type: select
          scale: 1/2
          mode: multiple
          select:
            - value: Nguyễn Thị Hà (0000000001)
              label: Nguyễn Thị Hà (0000000001)
    - type: group
      label: Employment
      collapse: false
      fields:
        - name: organizationalInstanceRcd
          label: Organizational Instance Rcd
          type: text
          scale: 1/2
          disabled: true
          value: 0
          validators:
            - type: required
        - name: rehireDate
          label: Rehire Date
          type: dateRange
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
          scale: 1/2
          disabled: true
          validators:
            - type: required
        - name: groupFirstStartDate
          label: Group First Start Date
          type: text
          disabled: true
          scale: 1/2
          value: 01/01/2010
          validators:
            - type: required
        - name: rehireDate
          label: Rehire Date
          disabled: true
          scale: 1/2
          type: dateRange
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
          validators:
            - type: required
        - name: groupLastTerminate
          label: Group Last Terminate
          disabled: true
          type: text
          scale: 1/2
          value: 30/10/2015
          validators:
            - type: required
        - name: oirOriginalStartDate
          label: OIR Original Start Date
          type: dateRange
          scale: 1/2
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
          validators:
            - type: required
        - name: oirFirstStartDate
          label: OIR First Start Date
          type: dateRange
          scale: 1/2
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
          validators:
            - type: required
        - name: oirLastStartDate
          label: OIR Last Start Date
          type: dateRange
          scale: 1/2
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
          validators:
            - type: required
        - name: oirLastTerminate
          label: OIR Last Terminate
          type: dateRange
          scale: 1/2
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
          validators:
            - type: required
        - name: groupSeniority
          label: Group Seniority
          type: button
          button:
            - value: 4 Years
              label: 4 Years
            - value: 2 Months
              label: 2 Months
            - value: 2 Days
              label: 2 Days
        - name: organizationalInstanceSeniority
          label: Organizational Instance Seniority
          type: button
          button:
            - value: 4 Years
              label: 4 Years
            - value: 2 Months
              label: 2 Months
            - value: 2 Days
              label: 2 Days
        - name: externalExperience
          label: External Experience
          type: button
          button:
            - value: 4 Years
              label: 4 Years
            - value: 2 Months
              label: 2 Months
            - value: 2 Days
              label: 2 Days
        - name: note
          label: Note
          type: textarea
          placeholder: Enter Note
  actions:
    - id: save
      type: modal
      modal:
        type: warning
        title: Update Employee Profile?
        content: >-
          Employee has been rehired, would you like to navigate to Employee's
          profile to update the latest Personal information?
        onConfirm:
          type: navigate
          link: /HR/HR.FS.FR.000
filter_config:
  fields:
    - type: group
      lastGroupStyleOff: true
      n_cols: 4
      fields:
        - name: fullName
          label: Full Name
          placeholder: Enter Full Name
          type: text
          _class:
            transform: >-
              $not($queryInArray($.variables._config, {'code':'FullName'},
              'required')) ? 'unrequired' : 'required'
          _condition:
            transform: >-
              $filter($.variables._config, function($v, $i, $a) { $v.code =
              'FullName' })
        - name: firstName
          label: First Name
          placeholder: Enter First Name
          type: text
          _class:
            transform: >-
              $not($queryInArray($.variables._config, {'code':'FirstName'},
              'required')) ? 'unrequired' : 'required'
          _condition:
            transform: >-
              $filter($.variables._config, function($v, $i, $a) { $v.code =
              'FirstName' })
        - name: middleName
          label: Middle Name
          placeholder: Enter Middle Name
          type: text
          _class:
            transform: >-
              $not($queryInArray($.variables._config, {'code':'MiddleName'},
              'required')) ? 'unrequired' : 'required'
          _condition:
            transform: >-
              $filter($.variables._config, function($v, $i, $a) { $v.code =
              'MiddleName' })
        - name: lastName
          label: Last Name
          type: text
          placeholder: Enter Last Name
          _class:
            transform: >-
              $not($queryInArray($.variables._config, {'code':'LastName'},
              'required')) ? 'unrequired' : 'required'
          _condition:
            transform: >-
              $filter($.variables._config, function($v, $i, $a) { $v.code =
              'LastName' })
        - type: dateRange
          name: dateOfBirth
          label: Date of Birth
          mode: date-picker
          placeholder: dd/MM/yyyy
          _class:
            transform: >-
              $not($queryInArray($.variables._config, {'code':'BirthDate'},
              'required')) ? 'unrequired' : 'required'
          _condition:
            transform: >-
              $filter($.variables._config, function($v, $i, $a) { $v.code =
              'BirthDate' })
          setting:
            format: dd/MM/yyyy
        - name: genderCode
          label: Gender
          type: select
          outputValue: value
          placeholder: Select Gender
          _class:
            transform: >-
              $not($queryInArray($.variables._config, {'code':'Gender'},
              'required')) ? 'unrequired' : 'required'
          _condition:
            transform: >-
              $filter($.variables._config, function($v, $i, $a) { $v.code =
              'Gender' })
          _select:
            transform: $gendersList()
        - name: employeeGroupCode
          label: Employee Group
          type: select
          outputValue: value
          placeholder: Select Employee Group
          _class:
            transform: >-
              $not($queryInArray($.variables._config, {'code':'EmployeeGroup'},
              'required')) ? 'unrequired' : 'required'
          _condition:
            transform: >-
              $filter($.variables._config, function($v, $i, $a) { $v.code =
              'EmployeeGroup' })
          _select:
            transform: $.variables._employeeGroupList
        - name: nationality
          label: Nationality
          type: select
          outputValue: value
          placeholder: Select Nationality
          isLazyLoad: true
          _condition:
            transform: >-
              $filter($.variables._config, function($v, $i, $a) { $v.code =
              'Nationality' })
          _class:
            transform: >-
              $not($queryInArray($.variables._config, {'code':'Nationality'},
              'required')) ? 'unrequired' : 'required'
          _select:
            transform: $countriesList($.extend.limit, $.extend.page, $.extend.search)
        - name: nationalID
          label: National ID
          placeholder: Enter National ID/CCCD
          type: text
          _condition:
            transform: >-
              $filter($.variables._config, function($v, $i, $a) { $v.code =
              'NationalID' })
          _class:
            transform: >-
              $not($queryInArray($.variables._config, {'code':'NationalID'},
              'required')) ? 'unrequired' : 'required'
        - name: email
          label: Personal Email
          placeholder: Enter Personal Email
          type: text
          _class:
            transform: >-
              $not($queryInArray($.variables._config, {'code':'PersonalEmail'},
              'required')) ? 'unrequired' : 'required'
          _condition:
            transform: >-
              $filter($.variables._config, function($v, $i, $a) { $v.code =
              'PersonalEmail' })
        - name: businessEmail
          label: Business Email
          type: text
          placeholder: Enter Business Email
          _class:
            transform: >-
              $not($queryInArray($.variables._config, {'code':'BusinessEmail'},
              'required')) ? 'unrequired'
          _condition:
            transform: >-
              $filter($.variables._config, function($v, $i, $a) { $v.code =
              'BusinessEmail' })
        - name: phone
          label: Phone Number
          type: text
          placeholder: Enter Phone Number
          _class:
            transform: >-
              $not($queryInArray($.variables._config, {'code':'Phone'},
              'required')) ? 'unrequired' : 'required'
          _condition:
            transform: >-
              $filter($.variables._config, function($v, $i, $a) { $v.code =
              'Phone' })
        - name: passport
          label: Passport
          type: text
          placeholder: Enter Passport
          _class:
            transform: >-
              $not($queryInArray($.variables._config, {'code':'Passport'},
              'required')) ? 'unrequired' : 'required'
          _condition:
            transform: >-
              $filter($.variables._config, function($v, $i, $a) { $v.code =
              'Passport' })
        - name: pitCode
          label: PIT Code
          type: text
          placeholder: Enter PIT Code
          _class:
            transform: >-
              $not($queryInArray($.variables._config, {'code':'PITCode'},
              'required')) ? 'unrequired' : 'required'
          _condition:
            transform: >-
              $filter($.variables._config, function($v, $i, $a) { $v.code =
              'PITCode' })
  filterMapping:
    - field: fullName
      operator: $cont
      valueField: fullName
    - field: firstName
      operator: $cont
      valueField: firstName
    - field: middleName
      operator: $cont
      valueField: middleName
    - field: lastName
      operator: $cont
      valueField: lastName
    - field: dateOfBirth
      operator: $eq
      valueField: dateOfBirth
    - field: genderCode
      operator: $eq
      valueField: genderCode
    - field: employeeGroupCode
      operator: $eq
      valueField: employeeGroupCode
    - operator: $or
      valueField:
        - field: otherNationalityCode
          operator: $eq
          valueField: nationality
        - field: nationalityCode
          operator: $eq
          valueField: nationality
    - field: email
      operator: $cont
      valueField: email
    - field: businessEmail
      operator: $cont
      valueField: businessEmail
    - field: phone
      operator: $cont
      valueField: phone
    - field: cccd
      operator: $cont
      valueField: nationalID
    - field: pitCode
      operator: $cont
      valueField: pitCode
    - field: passport
      operator: $cont
      valueField: passport
  sources:
    countriesList:
      uri: '"/api/picklists/NATIONALITY/values/pagination"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    gendersList:
      uri: '"/api/picklists/GENDER/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
    employeeGroupList:
      uri: '"/api/employee-groups"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.employeeGroup.default,
        'value': $item.code}})
      disabledCache: true
    config:
      uri: '"/api/properties"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $
      disabledCache: true
  variables:
    _config:
      transform: $config()
    _employeeGroupList:
      transform: $employeeGroupList()
layout_options:
  expand_filter: true
  row_actions: []
  disabled_click_row: true
  show_table_filter: false
  show_table_search: false
  show_table_checkbox: false
  show_actions_many: false
  show_create_data_table: false
  is_new_dynamic_form: true
  table_actions:
    - id: rehire
      title: Rehire
      type: navigate
      link: /HR/HR.FS.FR.007_01
    - id: addJobData
      icon: plus
      title: Add Job Data
      type: navigate
      link: /HR/HR.FS.FR.000
  show_table_avatar: true
  pre_check_action_click_config:
    api:
      _url:
        transform: '"/api/personals/" & $.employeeId & "/allow-to-get-info"'
    expression: >-
      $.apiResponse = false ? {'type': 'info','title': 'Access
      Denied','message':'You do not have the required permissions to ' &
      ($.actionId = 'rehire' ? 'Rehire' : $.actionId = 'hire' ? 'Hire' :'add
      Job') & ' for Employee ID ' & $.rowData.employeeId & '. Please contact
      your system administrator for support.'} : true
layout_options__header_buttons: null
options: null
create_form:
  formSize:
    proceed: xsmall
  title: Hiring Information
  fields:
    - type: dateRange
      name: effectiveDate
      label: Effective Date
      mode: date-picker
      placeholder: dd/MM/yyyy
      _value:
        transform: $now()
      validators:
        - type: required
      setting:
        format: dd/MM/yyyy
    - name: employeeGroupCode
      label: Employee Group
      type: select
      outputValue: value
      placeholder: Choose Employee Group
      _select:
        transform: $employeeGroupList()
      validators:
        - type: required
    - name: companyCode
      label: Company
      type: select
      placeholder: Select Company
      isLazyLoad: true
      _select:
        transform: >-
          $companiesList($.fields.effectiveDate, $.extend.limit, $.extend.page,
          $.extend.search)
      validators:
        - type: required
  filterMapping:
    - field: companyCode
      operator: $eq
      valueField: companyCode.value
    - field: effectiveDate
      operator: $eq
      valueField: effectiveDate
    - field: employeeGroupCode
      operator: $eq
      valueField: employeeGroupCode
  sources:
    employeeGroupList:
      uri: '"/api/employee-groups"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.employeeGroup.default,
        'value': $item.code}})
      disabledCache: true
    companiesList:
      uri: '"/api/companies/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id}})[]
      disabledCache: true
      params:
        - effectiveDate
        - limit
        - page
        - search
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: rehire
    icon: icon-user-switch-bold
    type: ghost-gray
    title: Rehire
    href: /HR/HR.FS.FR.038
    condition_func: $.hrStatusCode= 'I'
  - id: addJob
    icon: icon-file-plus-bold
    type: ghost-gray
    title: Add Job
    href: /HR/employees/
    condition_func: $.hrStatusCode='A'
  - id: hire
    title: Hire
    icon: icon-user-circle-plus-bold
    type: ghost-gray
    condition_func: $.hrStatusCode= 'I'
backend_url: api/search-dulicate-persons
screen_name: null
layout_options__actions_many: null
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key: null
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Person Duplicate Check
  parent:
    title: Human Resource
