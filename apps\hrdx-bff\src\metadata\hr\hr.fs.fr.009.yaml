id: HR.FS.FR.009
status: draft
sort: 51
user_created: 78a455fe-2ca9-4621-b542-98e98ebfefe8
date_created: '2024-06-13T09:05:38.337Z'
user_updated: 56e71b91-dd39-4ebc-afd1-b4f00f38d4a5
date_updated: '2025-07-25T09:19:44.557Z'
title: Job Data
requirement:
  time: 1749203393321
  blocks:
    - id: 7M1im4ZXMu
      type: paragraph
      data:
        text: Quản lý thông tin Quyết định nhân sự
  version: 2.30.7
screen_design: null
module: HR
local_fields:
  - code: organizationalInstance
    title: Organizational Instance
  - code: organizationalInstance2
    title: ' '
  - code: organizationalInstanceRcd
    title: Organizational Instance Rcd
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    group: organizationalInstance
  - code: employeeRecordNumber
    title: Employee Number Record
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    group: organizationalInstance2
  - code: action
    title: Action
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: action2
    title: ' '
  - code: employeeGroupName
    title: Employee Group
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: |
      Emolpyee Group
    group: action
  - code: effectiveDate
    title: Effective Date
    description: Effective Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    group: action
  - code: jobIndicatorName
    title: Job Indicator
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    group: action
  - code: actionName
    title: Action
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    group: action
  - code: actionReasonName
    title: Reason
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    group: action
  - code: effectiveSequence
    title: Sequence No.
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    group: action
  - code: hrStatusName
    title: HR Status
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: |
      HR Status
    group: action
  - code: payrollStatusName
    title: Payroll Status
    description: Payroll Status
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    group: action2
  - code: employeeSubGroupName
    title: Employee Sub-Group
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    group: action2
  - code: isManagerName
    title: Manager?
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    group: action2
  - code: levelDecisionName
    title: Level of Decision
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: Level Of Decision
    group: action2
  - code: decisionNumber
    title: Decision Number
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    group: action2
  - code: signDate
    title: Sign Date
    data_type:
      key: YYYY/MM/DD
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    description: Sign Date
    group: action2
  - code: expectedEndDate
    title: Expected End Date
    data_type:
      key: YYYY/MM/DD
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    description: Expected End Date
    group: action2
  - code: note
    title: Note
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    group: action2
  - code: assignment
    title: Assignment
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: positionView
    title: Position
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    group: assignment
  - code: companyView
    title: Company
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    group: assignment
  - code: legalEntityView
    title: Legal Entity
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    group: assignment
  - code: businessUnitView
    title: Business Unit
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    group: assignment
  - code: divisionView
    title: Division
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    group: assignment
  - code: departmentView
    title: Department
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    group: assignment
  - code: locationView
    title: Location
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: Location
    group: assignment
  - code: regionName
    title: Region
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: Region
    group: assignment
  - code: departmentEntryDate
    title: Department Entry Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/YYYY
      collection: field_types
    group: assignment
  - code: jobView
    title: Job
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    group: assignment
  - code: businessTitleView
    title: Business Title
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    group: assignment
  - code: careerStreamView
    title: Career Stream
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: Career Stream
    group: assignment
  - code: careerBandView
    title: Career Band
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: Career Band
    group: assignment
  - code: fte
    title: FTE
    description: FTE
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    group: assignment
  - code: empLevelView
    title: Emp Level
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: Emp Level
    group: assignment
  - code: costCenterView
    title: Cost Center
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: Cost Center
    group: assignment
  - code: reportToPosView
    title: Report To Pos
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: Report To Pos
    group: assignment
  - code: supervisorView
    title: Supervisor
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    group: assignment
  - code: matrixReportPositionView
    title: ' Matrix Report to'
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Array
      collection: field_types
    group: assignment
  - code: matrixManagerView
    title: Matrix Manager
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Array
      collection: field_types
    group: assignment
  - code: contractNumber
    title: Contract Number
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    group: assignment
  - code: contractTypeName
    title: Contract Type
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    group: assignment
  - code: fullPartName
    title: Full/Part
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    group: assignment
  - code: groupFirstStartDate
    title: Group First Start Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    group: assignment
  - code: jobSeniority
    title: Job Seniority
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    group: assignment
  - code: timeZoneName
    title: Timezone
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: Timezone
    group: assignment
  - code: attachFileName
    title: Attachment
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    group: assignment
mock_data: []
local_buttons: null
layout: layout-widget
form_config:
  _mode:
    transform: $not($.extend.formType = 'view') ? 'mark-scroll'
  isFilterRight: false
  styleFilterForm:
    padding: 0
    borderBottom: none
  formSize:
    create: large
    edit: large
    proceed: large
  preCheckSubmit:
    source:
      uri: '"/api/personals/new/job-datas/validate-job-datas"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'companyCode','operator':
        '$eq','value':$.company},{'field':'legalEntityCode','operator':
        '$eq','value':$.legalEntityObj.value},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'businessUnitCode','operator':
        '$eq','value':$.businessUnitObj.value},{'field':'division','operator':
        '$eq','value':$.divisionObj.value},{'field':'department','operator':
        '$eq','value':$.departmentObj.value},{'field':'costCenter','operator':
        '$eq','value':$.costCenterObj.value},{'field':'location','operator':
        '$eq','value':$.locationObj.value},{'field':'careerStream','operator':
        '$eq','value':$.careerStreamObj.value},{'field':'careerBand','operator':
        '$eq','value':$.careerBandObj.value},{'field':'reportPosition','operator':
        '$eq','value':$.reportToPosObj.value},{'field':'jobCode','operator':
        '$eq','value':$.jobObj.value},{'field':'positionCode','operator':
        '$eq','value':$.positionObj.value},{'field':'businessTitleCode','operator':
        '$eq','value':$.businessTitleObj.value},{'field':'actionCode','operator':
        '$eq','value':$.actionObj.value},{'field':'actionReasonCode','operator':
        '$eq','value':$.actionReasonCode},{'field':'employeeGroupCode','operator':
        '$eq','value':$.employeeGroup},{'field':'employeeSubGroupCode','operator':
        '$eq','value':$.employeeSubGroup},{'field':'matrixManager','operator':
        '$eq','value':$map($.matrixManagerObjs,
        function($it){$it.value})[]},{'field':'supervisor','operator':
        '$eq','value':$.supervisorObj.value},{'field':'regionCode','operator':
        '$eq','value':$.regionObj.value},{'field':'employeeLevelCode','operator':
        '$eq','value':$.empLevel},{'field':'matrixReportPositionCodes','operator':
        '$eq','value':$map($.matrixReportPositionObjs,
        function($it){$it.value})[]}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $reduce($, function($acc, $item){ $merge([$acc,{ $item.note :
        $exists($item.inputError) ? $item.inputError : 'Null'}])},{})
      disabledCache: true
  nestedConfirmOnSubmit:
    name: checkTotalFte
    transform: >-
      $.variables._checkTotalFte.fte > 1 and $.currentValue.hrStatus = 'A' ?
      {'title': 'Total FTE exceeded 1.0','content': 'The sum of FTEs of all
      active jobs (with HR status Active) for this employee has exceeded 1.0.'}
    nestedSetting:
      name: have-contract-updated
      transform: >-
        $.variables._haveContractUpdated.updateContract and
        $.variables._checkTerminateAction.isTerminateAction ? {'title': 'End
        Contract?','content': 'Please confirm if you would like to update the
        contract end date for the resigned employee.'}
      transformValueAfterConfirm: '$merge([$.formValue,{''updateContractEndDate'': true}])'
      continuteWithCancel: true
      nestedSetting:
        name: isUniquePrimaryJob
        continuteWithCancel: true
        transformValueAfterConfirm: >-
          $merge([$.formValue,{'terminationSecondaryJob':
          $.variables._getRelateiveInfoJobData.isUniquePrimaryJob = true or
          ($.variables._getRelateiveInfoJobData.hasOtherSecondaryJobInSameOIR
          and $.variables._getRelateiveInfoJobData.numberOfSecondaryJobInSameOIR
          > 0)}])
        transform: >-
          $.variables._checkTerminateAction.isTerminateAction and
          ($.variables._getRelateiveInfoJobData.isUniquePrimaryJob or
          $.variables._getRelateiveInfoJobData.hasOtherSecondaryJobInSameOIR) ?
          ($options1 := {'title': 'Warning','content': 'Secondary job records
          within OIR and their related contracts will be terminated on the same
          Effective Date as the Primary job.','dialogOptions': {'nzOkText':
          'OK', 'nzCancelText': null, 'nzClassName': 'popup-got-it'}} ;
          $options2 := {'title': 'Warning','content': 'There are currently ' &
          $.variables._getRelateiveInfoJobData.numberOfSecondaryJobInSameOIR & '
          active secondary jobs within OIR as of the Effective Date. Do you want
          to terminate all of these secondary jobs and their related
          contracts?'}; $.variables._getRelateiveInfoJobData.isUniquePrimaryJob
          ? $options1 :
          $.variables._getRelateiveInfoJobData.hasOtherSecondaryJobInSameOIR ?
          $options2 : null )
        nestedSetting:
          name: validateRelateiveInfo
          transform: >-
            $.variables._messageValidateRelateiveInfo ? {'title':
            'Warning','content': $.variables._messageValidateRelateiveInfo,
            'dialogOptions':{'nzWrapClassName': 'popup popup-confirm
            content-left'}}
          nestedSetting:
            name: checkDayOffTsApplicationForLeave
            transform: >-
              $.variables._checkDayOffTsApplicationForLeave.checkResult = false
              and $.variables._checkTerminateAction.isTerminateAction ?
              {'title': 'Warning','content':
              $.variables._checkDayOffTsApplicationForLeave.message}
  requestAfterSubmitResponse:
    sourcesMapping:
      - key: updateDayOffTsApplicationForLeaves
        enabled: >-
          $.variables._checkDayOffTsApplicationForLeave.checkResult = false and
          $.variables._checkTerminateAction.isTerminateAction
      - key: terminationRecordInSameOir
        enabled: $.formData.terminationSecondaryJob
    sources:
      updateDayOffTsApplicationForLeaves:
        uri: >-
          "/api/ts-au-application-for-leaves/update-day-off-ts-application-for-leaves"
        method: POST
        queryTransform: ''
        bodyTransform: >-
          {'employeeCode': $._params.id1, 'employeeRecordNumber':
          $string($.employeeRecordNumber), 'resignationDate': $.effectiveDate}
        headerTransform: ''
        resultTransform: $
        disabledCache: true
      terminationRecordInSameOir:
        uri: >-
          "/api/personals/" & $._params.id1 & "/job-datas/" & ($._formType =
          'edit' ? $.id : $._responseData.jobDataModel.id) &
          "/clone-termination-record-in-same-oir"
        method: POST
        queryTransform: ''
        bodyTransform: '{''terminationSecondaryJob'': true}'
        headerTransform: ''
        resultTransform: $
        disabledCache: true
  fields:
    - type: group
      label: Basic Information
      n_cols: 2
      collapse: false
      disableEventCollapse: true
      _condition:
        transform: >-
          $not($.extend.formType = 'view') and $not($.extend.formType =
          'viewMore')
      fields:
        - type: text
          name: id
          unvisible: true
        - type: text
          name: matchJobDataId
          unvisible: true
        - type: text
          name: radioGroup
          unvisible: true
          value: '3'
        - type: text
          name: primaryJob3
          unvisible: true
          _value:
            transform: $.fields.radioGroup = '1' ? $.fields.matchJobDataId
        - type: number
          label: Organizational Instance Rcd
          name: organizationalInstanceRecord
          disabled: true
        - type: number
          name: employeeRecordNumber
          label: Employee Number Record
          disabled: true
        - type: number
          name: fteDefault
          unvisible: true
    - type: group
      label: Action
      collapse: false
      n_cols: 2
      disableEventCollapse: true
      _condition:
        transform: >-
          $not($.extend.formType = 'view') and $not($.extend.formType =
          'viewMore')
      fields:
        - type: text
          name: employeeGroup
          label: employeeGroup
          unvisible: true
        - type: dateRange
          label: Effective Date
          name: effectiveDate
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
          placeholder: dd/MM/yyyy
          validators:
            - type: required
        - type: text
          label: Employee Group
          name: employeeGroupName
          disabled: true
          validators:
            - type: required
        - type: select
          label: Action
          name: actionObj
          allowValueKey: value
          clearFieldsAfterChange:
            - actionReasonCode
          handleAfterChange:
            dataSource:
              transform: >-
                {'hrStatusCode': $.fieldValue.setStatusField ?
                $.fieldValue.hrStatusCode :
                $.fields.hrStatusDefault,'prStatusCode':
                $.fieldValue.setStatusField ? $.fieldValue.prStatusCode :
                $.fields.prStatusDefault}
            valueMapping:
              - field: hrStatus
                fieldValue: hrStatusCode
              - field: payrollStatus
                fieldValue: prStatusCode
          _allowValues:
            transform: >-
              $.variables._actionsList ?
              $map($.extend.optionList,function($item){$item.value})[]
          placeholder: Select Action
          _value:
            transform: $.extend.formType = 'create' ? '_setSelectValueNull'
          _select:
            transform: $.variables._actionsList
          validators:
            - type: required
            - type: ppx-custom
              args:
                transform: >-
                  $exists($.extend.responseError.ActionCode) and
                  ($.extend.responseError.ActionCode = $.fields.actionObj.value)
                  and $.variables._checkSameDate
              text: Action is not correct
        - type: select
          label: Job Indicator
          name: jobIndicator
          outputValue: value
          placeholder: Select Job Indicator
          _select:
            transform: >-
              $exists($.fields.effectiveDate) ?
              $jobIndicatorList($.fields.effectiveDate)
          validators:
            - type: required
          _validateFn:
            transform: >-
              $.extend.defaultValue.jobIndicatorName ? {'label':
              $.extend.defaultValue.jobIndicatorName, 'value':
              $.extend.defaultValue.jobIndicator}
            params:
              updateLabelExistOption: true
        - type: select
          label: Reason
          name: actionReasonCode
          outputValue: value
          placeholder: Select Reason
          isLazyLoad: true
          _value:
            transform: $.extend.formType = 'create' ? '_setSelectValueNull'
          _select:
            transform: >-
              ($actionReasons := $.variables._selectedAction.actionReasons;
              $count($actionReasons) > 0 ?
              $reasonsList($actionReasons,$.fields.effectiveDate,$count($actionReasons)))
          validators:
            - type: required
            - type: ppx-custom
              args:
                transform: >-
                  $exists($.extend.responseError.ActionReasonCode) and
                  ($.extend.responseError.ActionReasonCode =
                  $.fields.actionReasonCode) and $.variables._checkSameDate
              text: Reason is not correct
          _validateFn:
            transform: >-
              $not($.extend.formType = 'create') and
              $.extend.defaultValue.actionReasonName and
              ($.extend.defaultValue.actionReasonCode in
              $.variables._selectedAction.actionReasons)? {'label':
              $.extend.defaultValue.actionReasonName, 'value':
              $.extend.defaultValue.actionReasonCode}
            params:
              updateLabelExistOption: true
        - type: select
          label: Employee Sub Group
          name: employeeSubGroup
          outputValue: value
          isLazyLoad: true
          placeholder: Select Employee Sub Group
          _select:
            transform: >-
              $exists($.fields.employeeGroup) and
              $exists($.fields.effectiveDate) ?
              $employeeSubGroupsList($.fields.employeeGroup,
              $.fields.effectiveDate, $.extend.limit, $.extend.page,
              $.extend.search)
          _allowValues:
            transform: >-
              $exists($.fields.employeeGroup) and
              $exists($.fields.effectiveDate) and
              $exists($.fields.employeeSubGroup) ?
              $employeeSubGroupsListCheck($.fields.employeeGroup,
              $.fields.effectiveDate, $.fields.employeeSubGroup)
          _validateFn:
            transform: >-
              $.extend.defaultValue.employeeSubGroup ? ($info :=
              $employeeSubGroupInfo($.extend.defaultValue.employeeGroup,
              $.extend.defaultValue.effectiveDate,
              $.extend.defaultValue.employeeSubGroup); $exists($info) ? $info)
            params:
              updateLabelExistOption: true
          validators:
            - type: required
            - type: ppx-custom
              args:
                transform: >-
                  $exists($.extend.responseError.EmployeeSubGroupCode) and
                  ($.extend.responseError.EmployeeSubGroupCode =
                  $.fields.employeeSubGroup) and $.variables._checkSameDate
              text: Employee Sub Group is not correct
        - type: number
          name: effectiveSequence
          label: Sequence No.
          placeholder: Enter Sequence No.
          _value:
            transform: >-
              $.extend.formType = 'create' ? ($effectiveSequence :=
              $.fields.effectiveSequence
              ;$exists($.variables._getHistorySameEffectiveDate) ? ($newSequence
              := $.extend.defaultValue.isHistory and
              $DateFormat($.extend.defaultValue.effectiveDate, 'yyyy-MM-DD') =
              $DateFormat($.fields.effectiveDate, 'yyyy-MM-DD') ?
              $.extend.defaultValue.effectiveSequence + 1 :
              $.variables._getHistorySameEffectiveDate.effectiveSequence + 1;
              $effectiveSequence < $newSequence ? $newSequence :
              $effectiveSequence) : 0)
          validators:
            - type: required
            - type: ppx-custom
              args:
                transform: $length($string($.fields.effectiveSequence)) > 4
              text: Sequence No. should not exceed 4 characters
            - type: ppx-custom
              args:
                transform: >-
                  $.extend.formType = 'create' and
                  $exists($.variables._getHistorySameSequence) and
                  $.fields.effectiveSequence > 0
              text: >-
                Effective Sequence already exists. Please enter another
                sequence.
        - type: select
          label: Manager?
          name: isManagerCode
          outputValue: value
          placeholder: Select Manager?
          _select:
            transform: >-
              $exists($.fields.effectiveDate) ?
              $managerList($.fields.effectiveDate)
          validators:
            - type: required
        - type: text
          label: HR Status Default
          name: hrStatusDefault
          unvisible: true
          _value:
            transform: >-
              $.extend.formType = 'edit' and $.variables._previousJobdata ?
              $.variables._previousJobdata.hrStatus
        - type: radio
          label: HR Status
          name: hrStatus
          outputValue: value
          placeholder: Select HR Status
          _disabled:
            transform: >-
              ($setStatusField := $.variables._selectedAction.setStatusField;
              $setStatusField = true ? false : true)
          _radio:
            transform: >-
              $exists($.fields.effectiveDate) ?
              $hrStatusList($.fields.effectiveDate)
          _class:
            transform: '''required'''
        - type: text
          label: Payroll Status Default
          name: prStatusDefault
          unvisible: true
          _value:
            transform: >-
              $.extend.formType = 'edit' and $.variables._previousJobdata ?
              $.variables._previousJobdata.payrollStatus
        - type: select
          label: Payroll Status
          name: payrollStatus
          outputValue: value
          placeholder: Select Payroll Status
          _select:
            transform: >-
              $exists($.fields.effectiveDate) ?
              $payrollStatusList($.fields.effectiveDate)
          validators:
            - type: required
        - type: select
          label: Level of decision
          name: levelOfDecision
          placeholder: Select Level of decision
          outputValue: value
          _select:
            transform: >-
              $exists($.fields.effectiveDate) ?
              $decisionLevelList($.fields.effectiveDate)
        - type: text
          name: decisionNumber
          label: Decision Number
          placeholder: Enter Decision number
        - type: dateRange
          label: Sign date
          name: signDate
          placeholder: dd/MM/yyyy
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
        - type: dateRange
          label: Expected end date
          name: expectedEndDate
          placeholder: dd/MM/yyyy
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
          validators:
            - type: ppx-custom
              args:
                transform: >-
                  $exists($.fields.effectiveDate) and
                  $exists($.fields.expectedEndDate) ?
                  $DateDiff($DateFormat($.fields.effectiveDate, 'yyyy-MM-DD'),
                  $DateFormat($.fields.expectedEndDate, 'yyyy-MM-DD'), 'd') > 0
              text: >-
                Expected end date must be greater than or equal to the Effective
                date
        - type: select
          label: Classification of Termination
          name: classificationOfTerminationCode
          placeholder: Select Classification of Termination
          outputValue: value
          col: 2
          _select:
            transform: >-
              $exists($.fields.effectiveDate) ?
              $CLASSOFTERList($.fields.effectiveDate)
          _condition:
            transform: >-
              $.fields.hrStatus = 'I' and
              $.extend.permission.ClassificationOfTermination.Read
          _disabled:
            transform: >-
              $not($.extend.permission.ClassificationOfTermination.Create =
              true)
          _defaultValue:
            transform: >-
              $.extend.formType = 'edit' ?
              $.extend.defaultValue.classificationOfTerminationCode
        - type: textarea
          name: note
          label: Note
          placeholder: Enter note
          col: 2
          validators:
            - type: maxLength
              args: '500'
              text: Maximum length is 500 characters.
          textarea:
            autoSize:
              minRows: 3
            maxCharCount: 500
    - type: group
      label: Assignment
      collapse: false
      n_cols: 2
      disableEventCollapse: true
      _condition:
        transform: >-
          $not($.extend.formType = 'view') and $not($.extend.formType =
          'viewMore')
      fields:
        - type: select
          label: Position
          name: positionObj
          isLazyLoad: true
          options:
            enabledLoadMore: false
          placeholder: Select Position
          handleAfterChange:
            dataSource:
              transform: >-
                $organizationPick('Position', $.fieldValue.value,
                $DateToTimestampUTC($.fields.effectiveDate))
            valueMapping:
              - field: legalEntityObj
                fieldValue: LegalEntity
              - field: businessUnitObj
                fieldValue: BusinessUnit
                _setNullValue: $isNilorEmpty($.BusinessUnit)
              - field: divisionObj
                fieldValue: Division
                _setNullValue: $isNilorEmpty($.Division)
              - field: departmentObj
                fieldValue: Department
              - field: costCenterObj
                fieldValue: CostCenter
              - field: reportToPosObj
                fieldValue: DirectPosition
              - field: locationObj
                fieldValue: Location
              - field: jobObj
                fieldValue: JobCode
              - field: businessTitleObj
                fieldValue: BusinessTitle
              - field: careerStreamObj
                fieldValue: CareerStream
              - field: careerBandObj
                fieldValue: Band
              - field: regionObj
                fieldValue: Region
              - field: supervisorObj
                fieldValue: noName
                _setNullValue: $not($isNilorEmpty($.DirectPosition))
          clearFieldsAfterChange:
            - validationPosition
            - validationDepartment
            - validationDivision
            - validationBusiness
            - validationLegal
            - validationCost
            - validationBusinessTitle
            - validationReportToPos
            - validationLocation
            - validationJob
            - validationSupervisor
            - validationRegion
            - validationCareerStream
            - validationCareerBand
          _select:
            transform: >-
              $positionsList($.fields.company,$.fields.effectiveDate,$.fields.legalEntityObj.value,
              $.fields.departmentObj.value, $.fields.jobObj.value)
          validators:
            - type: ppx-custom
              args:
                transform: >-
                  $not($isNilorEmpty($.fields.validationPosition)) and
                  $.variables._checkSameDate
              text: Position is not correct
        - type: text
          name: validationPosition
          unvisible: true
          _value:
            transform: >-
              $exists($.extend.responseError.PositionCode) ?
              $.extend.responseError.PositionCode : '_setValueNull'
        - type: select
          label: Cost Center
          name: costCenterObj
          placeholder: Select Cost Center
          isLazyLoad: true
          _select:
            transform: >-
              $constCenterList($.fields.company, $.extend.limit, $.extend.page,
              $.extend.search, $.fields.effectiveDate)
          clearFieldsAfterChange:
            - validationCost
          isRemoveOptionNotExist: true
          dependantOptionList: $.fields.company
          validators:
            - type: ppx-custom
              args:
                transform: >-
                  $not($isNilorEmpty($.fields.validationCost)) and
                  $.variables._checkSameDate
              text: Cost Center is not correct
        - type: text
          name: validationCost
          unvisible: true
          _value:
            transform: >-
              $exists($.extend.responseError.CostCenterCode) ?
              $.extend.responseError.CostCenterCode : '_setValueNull'
        - type: select
          name: company
          label: Company
          disabled: true
          validateDisabled: true
          _select:
            transform: >-
              $exists($.fields.effectiveDate) ?
              $companiyInfo($.fields.company,$.fields.effectiveDate)
          validators:
            - type: required
            - type: ppx-custom
              args:
                transform: >-
                  $exists($.extend.responseError.CompanyCode) and
                  ($.extend.responseError.CompanyCode = $.fields.company) and
                  $.variables._checkSameDate
              text: Company is not correct
        - type: select
          label: Report To Pos
          name: reportToPosObj
          placeholder: Select Report To Pos
          isLazyLoad: true
          isRemoveOptionNotExist: true
          clearFieldsAfterChange:
            - supervisorObj
            - validationReportToPos
          _select:
            transform: >-
              $positionsListGetBy($.fields.effectiveDate, $.extend.limit,
              $.extend.page, $.extend.search)
          validators:
            - type: ppx-custom
              args:
                transform: >-
                  $not($isNilorEmpty($.fields.validationReportToPos)) and
                  $.variables._checkSameDate
              text: Report To Pos is not correct
        - type: text
          name: validationReportToPos
          unvisible: true
          _value:
            transform: >-
              $exists($.extend.responseError.ReportPosition) ?
              $.extend.responseError.ReportPosition : '_setValueNull'
        - type: select
          label: Legal Entity
          name: legalEntityObj
          isLazyLoad: true
          options:
            enabledLoadMore: false
          isRemoveOptionNotExist: true
          placeholder: Select Legal Entity
          clearFieldsAfterChange:
            - departmentObj
            - positionObj
            - validationLegal
          _select:
            transform: $legalEntitiesList($.fields.company,$.fields.effectiveDate)
          validators:
            - type: required
            - type: ppx-custom
              args:
                transform: >-
                  $not($isNilorEmpty($.fields.validationLegal)) and
                  $.variables._checkSameDate
              text: Legal Entity is not correct
        - type: text
          name: validationLegal
          unvisible: true
          _value:
            transform: >-
              $exists($.extend.responseError.LegalEntityCode) ?
              $.extend.responseError.LegalEntityCode : '_setValueNull'
        - type: select
          label: Supervisor
          name: supervisorObj
          placeholder: Select Supervisor
          isLazyLoad: true
          isRemoveOptionNotExist: true
          clearFieldsAfterChange:
            - reportToPosObj
            - validationSupervisor
          _select:
            transform: >-
              $personalsList($.extend.limit, $.extend.page, $.extend.search,
              $.fields.effectiveDate, 'A')
          validators:
            - type: ppx-custom
              args:
                transform: >-
                  $not($isNilorEmpty($.fields.validationSupervisor)) and
                  $.variables._checkSameDate
              text: Supervisor is not correct
        - type: text
          name: validationSupervisor
          unvisible: true
          _value:
            transform: >-
              $exists($.extend.responseError.Supervisor) ?
              $.extend.responseError.Supervisor : '_setValueNull'
        - type: select
          label: Business Unit
          name: businessUnitObj
          placeholder: Select Business Unit
          isLazyLoad: true
          options:
            enabledLoadMore: false
          isRemoveOptionNotExist: true
          isServerSearch: false
          clearFieldsAfterChange:
            - divisionObj
            - departmentObj
            - positionObj
            - validationBusiness
            - validationDivision
          _select:
            transform: $businessUnitList($.fields.company,$.fields.effectiveDate)
          validators:
            - type: ppx-custom
              args:
                transform: >-
                  $not($isNilorEmpty($.fields.validationBusiness)) and
                  $.variables._checkSameDate
              text: Business Unit is not correct
        - type: text
          name: validationBusiness
          unvisible: true
          _value:
            transform: >-
              $exists($.extend.responseError.BusinessUnitCode) ?
              $.extend.responseError.BusinessUnitCode : '_setValueNull'
        - type: selectAll
          label: Matrix Report To
          name: matrixReportPositionObjs
          mode: multiple
          isLazyLoad: true
          _value:
            transform: ' $exists($.variables._positionInfo.matrixPositionObj) ? $map($.variables._positionInfo.matrixPositionObj, function($it){{ ''label'': $it.label, ''value'': $it.value.code }})'
          setting:
            enabledLoadMore: false
          isRemoveOptionNotExist: true
          placeholder: Select Matrix Report To
          _options:
            transform: >-
              $positionsListGetBy($.fields.effectiveDate, $.extend.limit,
              $.extend.page, $.extend.search)
          validators:
            - type: ppx-custom
              args:
                transform: >-
                  $count($.fields.matrixReportPositionObjs) > 0 and
                  $boolean($.variables._validateMatrixReport)
              text: >-
                Matrix Report To is not correct:
                {{values.variables._validateMatrixReport}}
        - type: select
          label: Division
          name: divisionObj
          placeholder: Select Division
          value: null
          isLazyLoad: true
          isRemoveOptionNotExist: true
          handleAfterChange:
            dataSource:
              transform: $divisionDetail($.fieldValue.id)
            valueMapping:
              - field: businessUnitObj
                fieldValue: BusinessUnit
          options:
            enabledLoadMore: false
          clearFieldsAfterChange:
            - positionObj
            - departmentObj
            - validationDivision
            - validationBusiness
          _select:
            transform: >-
              $divisionsList($.fields.businessUnitObj.value,$.fields.effectiveDate,$.fields.company)
          validators:
            - type: ppx-custom
              args:
                transform: >-
                  $not($isNilorEmpty($.fields.validationDivision)) and
                  $.variables._checkSameDate
              text: Division is not correct
        - type: text
          name: validationDivision
          unvisible: true
          _value:
            transform: >-
              $exists($.extend.responseError.DivisionCode) ?
              $.extend.responseError.DivisionCode : '_setValueNull'
        - type: selectAll
          label: Matrix Manager
          name: matrixManagerObjs
          mode: multiple
          isLazyLoad: true
          isRemoveOptionNotExist: true
          placeholder: Select Matrix Manager
          _options:
            transform: >-
              $personalsList($.extend.limit, $.extend.page, $.extend.search,
              $.fields.effectiveDate, 'A')
          validators:
            - type: ppx-custom
              args:
                transform: >-
                  $count($.fields.matrixManagerObjs) > 0 and
                  $boolean($.variables._validateMatrixManager)
              text: >-
                Matrix Manager is not correct:
                {{values.variables._validateMatrixManager}}
        - type: select
          label: Department
          name: departmentObj
          placeholder: Select Department
          isLazyLoad: true
          isRemoveOptionNotExist: true
          options:
            enabledLoadMore: false
          clearFieldsAfterChange:
            - positionObj
            - validationDepartment
            - validationDivision
            - validationBusiness
            - validationLegal
            - validationLocation
            - validationSupervisor
            - validationPosition
            - validationCost
            - validationBusinessTitle
            - validationReportToPos
            - validationJob
            - validationRegion
          handleAfterChange:
            dataSource:
              transform: >-
                ($dataObj := {'organizationPickData':
                $organizationPick('Department', $.fieldValue.value,
                $DateToTimestampUTC($.fields.effectiveDate)), 'detail':
                $departmentInfo($.fieldValue.id)} ;
                $merge([$dataObj.organizationPickData,{'ManagerPosition':
                $not($dataObj.detail.managerType = 'Position') ? null :
                $dataObj.organizationPickData.ManagerPosition ,'Employee':
                $not($dataObj.detail.managerType = 'Employee') ? null :
                ($exists($dataObj.detail.headOfDepartmentObj.value.code) ?
                {'label': $dataObj.detail.headOfDepartmentObj.label & ' (' &
                $dataObj.detail.headOfDepartmentObj.value.code & ')', 'value':
                $dataObj.detail.headOfDepartmentObj.value.code} :
                $dataObj.organizationPickData.Employee) }]))
            valueMapping:
              - field: legalEntityObj
                fieldValue: LegalEntity
              - field: businessUnitObj
                fieldValue: BusinessUnit
                _setNullValue: $isNilorEmpty($.BusinessUnit)
              - field: divisionObj
                fieldValue: Division
                _setNullValue: $isNilorEmpty($.Division)
              - field: reportToPosObj
                fieldValue: ManagerPosition
                _setNullValue: $not($isNilorEmpty($.Employee))
              - field: supervisorObj
                fieldValue: Employee
                _setNullValue: $not($isNilorEmpty($.ManagerPosition))
              - field: locationObj
                fieldValue: Location
              - field: regionObj
                fieldValue: Region
              - field: costCenterObj
                fieldValue: CostCenter
          _select:
            transform: ' $departmentsList($.fields.company, $.fields.legalEntityObj.value,$.fields.businessUnitObj.value, $.fields.divisionObj.value, $.fields.effectiveDate)'
          validators:
            - type: required
            - type: ppx-custom
              args:
                transform: >-
                  $not($isNilorEmpty($.fields.validationDepartment)) and
                  $.variables._checkSameDate
              text: Department is not correct
        - type: text
          name: validationDepartment
          unvisible: true
          _value:
            transform: >-
              $exists($.extend.responseError.DepartmentCode) ?
              $.extend.responseError.DepartmentCode : '_setValueNull'
        - type: text
          label: Contract Number
          name: contractNumber
          placeholder: ' '
          disabled: true
          _value:
            transform: >-
              $.variables._getContract ? $.variables._getContract.contractNumber
              : '_setValueNull'
        - type: select
          label: Location
          name: locationObj
          placeholder: Select Location
          isLazyLoad: true
          isRemoveOptionNotExist: true
          _select:
            transform: >-
              $locationsList($.fields.company,$.extend.limit, $.extend.page,
              $.extend.search,$.fields.effectiveDate)
          clearFieldsAfterChange:
            - validationLocation
          validators:
            - type: ppx-custom
              args:
                transform: >-
                  $not($isNilorEmpty($.fields.validationLocation)) and
                  $.variables._checkSameDate
              text: Location is not correct
            - type: required
        - type: text
          name: validationLocation
          unvisible: true
          _value:
            transform: >-
              $exists($.extend.responseError.LocationCode) ?
              $.extend.responseError.LocationCode : '_setValueNull'
        - type: text
          label: Contract Type
          name: contractTypeName
          placeholder: ' '
          disabled: true
          _value:
            transform: >-
              $.variables._getContract ?
              $.variables._getContract.contractTypeName : '_setValueNull'
        - type: select
          label: Region
          name: regionObj
          isLazyLoad: true
          placeholder: Select Region
          isRemoveOptionNotExist: true
          _select:
            transform: $regionList($.fields.effectiveDate)
          clearFieldsAfterChange:
            - validationRegion
          validators:
            - type: ppx-custom
              args:
                transform: >-
                  $exists($.fields.validationRegion) and
                  ($.fields.validationRegion = $.fields.regionObj.value) and
                  $.variables._checkSameDate
              text: Region is not correct
        - type: text
          name: validationRegion
          unvisible: true
          _value:
            transform: >-
              $exists($.extend.responseError.RegionCode) ?
              $.extend.responseError.RegionCode : '_setValueNull'
        - type: select
          label: Full/Part
          name: fullPartCode
          placeholder: Select Full/Part
          outputValue: value
          _select:
            transform: >-
              $exists($.fields.effectiveDate) ?
              $fullPartList($.fields.effectiveDate)
          _value:
            transform: '$.variables._getJobdataPrimary = 0  ? ''F'' '
          validators:
            - type: required
        - type: dateRange
          label: Department Entry Date
          name: departmentEntryDate
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
          placeholder: dd/MM/yyyy
          _defaultValue:
            transform: >-
              ($.extend.formType = 'create' and $.fields.radioGroup = '3') ?
              $.fields.effectiveDate
        - type: dateRange
          label: Group First Start Date
          name: groupFirstStartDate
          mode: date-picker
          disabled: true
          _value:
            transform: >-
              $not($.extend.formType = 'view') and
              $.variables._calculateJobSeniority ?
              $.variables._calculateJobSeniority.groupFirstStartDate
        - type: select
          label: Job
          name: jobObj
          placeholder: Select Job
          isLazyLoad: true
          isRemoveOptionNotExist: true
          clearFieldsAfterChange:
            - blockCallApiJobCode
            - careerStreamObj
            - careerBandObj
            - positionObj
            - validationJob
          handleAfterChange:
            dataSource:
              transform: >-
                { 'band': $.fieldValue.bandId ?
                $careerBandDetail($.fieldValue.bandId).band : null, 'stream':
                $.fieldValue.careerStreamId ?
                $careerStreamDetail($.fieldValue.careerStreamId).careerStream :
                null }
            valueMapping:
              - field: careerStreamObj
                fieldValue: stream
              - field: careerBandObj
                fieldValue: band
          _select:
            transform: ' $jobCodesList($.fields.company, $.extend.limit, $.extend.page, $.extend.search,$.fields.effectiveDate)'
          dependantOptionList: $.fields.company,$.fields.effectiveDate
          validators:
            - type: required
            - type: ppx-custom
              args:
                transform: >-
                  $not($isNilorEmpty($.fields.validationJob)) and
                  $.variables._checkSameDate
              text: Job is not correct
        - type: text
          name: validationJob
          unvisible: true
          _value:
            transform: >-
              $exists($.extend.responseError.JobCode) ?
              $.extend.responseError.JobCode : '_setValueNull'
        - type: text
          label: Job Seniority
          name: jobSeniority
          disabled: true
          _value:
            transform: >-
              $not($.extend.formType = 'view') and
              $.variables._calculateJobSeniority ?
              $.variables._calculateJobSeniority.jobSeniority
        - type: select
          label: Business Title
          name: businessTitleObj
          isLazyLoad: true
          placeholder: Select Business Title
          isRemoveOptionNotExist: true
          _select:
            transform: ' $businessTitlesList($.fields.company, $.extend.limit, $.extend.page, $.extend.search,$.fields.effectiveDate)'
          clearFieldsAfterChange:
            - validationBusinessTitle
          dependantOptionList: $.fields.company,$.fields.effectiveDate
          validators:
            - type: ppx-custom
              args:
                transform: >-
                  $not($isNilorEmpty($.fields.validationBusinessTitle)) and
                  $.variables._checkSameDate
              text: Business Title is not correct
        - type: text
          name: validationBusinessTitle
          unvisible: true
          _value:
            transform: >-
              $exists($.extend.responseError.BusinessTitleCode) ?
              $.extend.responseError.BusinessTitleCode : '_setValueNull'
        - type: select
          label: Time Zone
          name: timezone
          placeholder: Select Time zone
          outputValue: value
          _select:
            transform: >-
              $exists($.fields.effectiveDate) ?
              $timezoneList($.fields.effectiveDate)
        - type: select
          label: Career Stream
          name: careerStreamObj
          isLazyLoad: true
          placeholder: Select Career Stream
          isRemoveOptionNotExist: true
          clearFieldsAfterChange:
            - careerBandObj
            - validationCareerStream
            - validationCareerBand
          _select:
            transform: >-
              $careerStreamsList($.extend.limit, $.extend.page,
              $.extend.search,$.fields.effectiveDate)
          validators:
            - type: ppx-custom
              args:
                transform: >-
                  $not($isNilorEmpty($.fields.validationCareerStream)) and
                  $.variables._checkSameDate
              text: Career Stream is not correct
        - type: text
          name: validationCareerStream
          unvisible: true
          _value:
            transform: >-
              $exists($.extend.responseError.CareerStreamCode) ?
              $.extend.responseError.CareerStreamCode : '_setValueNull'
        - type: select
          label: Career Band
          name: careerBandObj
          isLazyLoad: true
          isRemoveOptionNotExist: true
          placeholder: Select Career Band
          handleAfterChange:
            dataSource:
              transform: $careerStreamDetail( $.fieldValue.careerStreamId)
            valueMapping:
              - field: careerStreamObj
                fieldValue: careerStream
          clearFieldsAfterChange:
            - validationCareerStream
            - validationCareerBand
          _select:
            transform: >-
              $careerBandsList($.fields.careerStreamObj.value, $.extend.limit,
              $.extend.page, $.extend.search,$.fields.effectiveDate)
          validators:
            - type: ppx-custom
              args:
                transform: >-
                  $not($isNilorEmpty($.fields.validationCareerBand)) and
                  $.variables._checkSameDate
              text: Career Band is not correct
        - type: text
          name: validationCareerBand
          unvisible: true
          _value:
            transform: >-
              $exists($.extend.responseError.CareerBandCode) ?
              $.extend.responseError.CareerBandCode : '_setValueNull'
        - type: number
          label: FTE
          name: fte
          placeholder: Enter FTE
          value: '1'
          validators:
            - type: required
        - type: select
          label: Emp Level
          name: empLevel
          placeholder: Select Emp Level
          outputValue: value
          isLazyLoad: true
          _select:
            transform: >-
              $exists($.fields.effectiveDate) ? $empLevelList($.extend.limit,
              $.extend.page, $.extend.search, $.fields.effectiveDate)
          validators:
            - type: required
            - type: ppx-custom
              args:
                transform: >-
                  $exists($.extend.responseError.EmployeeLevelCode) and
                  ($.extend.responseError.EmployeeLevelCode = $.fields.empLevel)
                  and $.variables._checkSameDate
              text: Emp Level is not correct
          _validateFn:
            transform: >-
              $.extend.defaultValue.empLevelView ? {'label':
              $.extend.defaultValue.empLevelView, 'value':
              $.extend.defaultValue.empLevel}
            params:
              updateLabelExistOption: true
        - type: upload
          label: Attach File
          name: file
          col: 2
          status: true
          upload:
            size: 100
            isMultiple: true
            accept:
              - application/pdf
              - >-
                application/vnd.openxmlformats-officedocument.wordprocessingml.document
              - application/msword
              - >-
                application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
              - application/vnd.ms-excel
              - image/jpeg
              - image/png
            customContentUpload: >-
              or drop it here PDF, XLS, XLSX, DOC, DOCX, JPG, PNG only (Max
              100MB)
          _condition:
            transform: $.extend.formType = 'edit' or $.extend.formType = 'create'
          paddingBottomNone: true
        - type: upload
          name: attachmentResults
          readOnly: true
          col: 2
          canAction: true
          hiddenLabel: true
          status: true
          _condition:
            transform: $.extend.formType = 'edit'
        - name: deleteFileIds
          unvisible: true
          type: text
          _value:
            transform: >-
              $.extend.formType = 'edit' ? ($originalArray :=
              $map($.extend.defaultValue.attachmentResults,
              function($it){$string($it.key)})[]; $updatedArray :=
              $map($.fields.attachmentResults,
              function($it){$string($it.key)})[]; $difference($originalArray,
              $updatedArray))
    - type: group
      label: Organizational Instance
      collapse: false
      isBorderTopNone: true
      customPadding: 0px
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - type: number
          label: Organizational Instance Rcd
          name: organizationalInstanceRecord
          labelType: type-row-readOnly
          disabled: true
        - type: number
          name: employeeRecordNumber
          label: Employee Number Record
          labelType: type-row-readOnly
          disabled: true
    - type: group
      label: Action
      collapse: false
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - type: text
          label: Employee Group
          name: employeeGroupName
          labelType: type-row-readOnly
          _value:
            transform: >-
              $.extend.defaultValue.employeeGroupName ?
              $.extend.defaultValue.employeeGroupName :
              $.extend.defaultValue.employeeGroup
        - type: dateRange
          label: Effective Date
          name: effectiveDate
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
          placeholder: dd/MM/yyyy
          labelType: type-row-readOnly
        - type: text
          label: Job Indicator
          name: jobIndicatorName
          labelType: type-row-readOnly
          _value:
            transform: >-
              $.extend.defaultValue.jobIndicatorName ?
              $.extend.defaultValue.jobIndicatorName :
              $.extend.defaultValue.jobIndicator
        - type: text
          label: Action
          name: actionName
          labelType: type-row-readOnly
          _value:
            transform: >-
              $.extend.defaultValue.actionName ?
              $.extend.defaultValue.actionName :
              $.extend.defaultValue.actionCode
        - type: text
          label: Reason
          name: actionReasonName
          labelType: type-row-readOnly
          _value:
            transform: >-
              $.extend.defaultValue.actionReasonName ?
              $.extend.defaultValue.actionReasonName :
              $.extend.defaultValue.actionReasonCode
        - type: number
          name: effectiveSequence
          label: Sequence No.
          placeholder: Enter Sequence No.
          labelType: type-row-readOnly
        - type: radio
          label: HR Status
          name: hrStatus
          _radio:
            transform: >-
              [{'label': $.extend.defaultValue.hrStatusName, 'value':
              $.extend.defaultValue.hrStatus}]
          labelType: type-row-readOnly
        - type: radio
          label: Payroll Status
          name: payrollStatus
          _radio:
            transform: >-
              [{'label': $.extend.defaultValue.payrollStatusName, 'value':
              $.extend.defaultValue.payrollStatus}]
          labelType: type-row-readOnly
        - type: text
          label: Employee Sub Group
          name: employeeSubGroupName
          labelType: type-row-readOnly
          _value:
            transform: >-
              $.extend.defaultValue.employeeSubGroupName ?
              $.extend.defaultValue.employeeSubGroupName :
              $.extend.defaultValue.employeeSubGroup
        - type: text
          label: Manager?
          name: isManagerName
          labelType: type-row-readOnly
          _value:
            transform: >-
              $.extend.defaultValue.isManagerName ?
              $.extend.defaultValue.isManagerName :
              $.extend.defaultValue.isManagerCode
        - type: text
          label: Level of decision
          name: levelDecisionName
          labelType: type-row-readOnly
          _value:
            transform: >-
              $.extend.defaultValue.levelDecisionName ?
              $.extend.defaultValue.levelDecisionName :
              $.extend.defaultValue.levelOfDecision
        - type: text
          name: decisionNumber
          label: Decision Number
          placeholder: Enter Decision number
          labelType: type-row-readOnly
        - type: dateRange
          label: Sign date
          name: signDate
          placeholder: dd/MM/yyyy
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
          labelType: type-row-readOnly
        - type: dateRange
          label: Expected end date
          name: expectedEndDate
          placeholder: dd/MM/yyyy
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
          validators:
            - type: ppx-custom
              args:
                transform: >-
                  $exists($.fields.effectiveDate) and
                  $exists($.fields.expectedEndDate) ?
                  $DateDiff($DateFormat($.fields.effectiveDate, 'yyyy-MM-DD'),
                  $DateFormat($.fields.expectedEndDate, 'yyyy-MM-DD'), 'd') > 0
              text: >-
                Expected end date must be greater than or equal to the Effective
                date
          labelType: type-row-readOnly
        - type: text
          label: Classification of Termination
          name: classificationOfTerminationName
          labelType: type-row-readOnly
          _condition:
            transform: >-
              $.fields.hrStatus = 'I' and
              $.extend.permission.ClassificationOfTermination.Read
          _value:
            transform: >-
              $.extend.defaultValue.classificationOfTerminationName ?
              $.extend.defaultValue.classificationOfTerminationName :
              $.extend.defaultValue.classificationOfTerminationCode
        - type: textarea
          name: note
          label: Note
          placeholder: Enter note
          validators:
            - type: maxLength
              args: '500'
              text: Maximum length is 500 characters.
          textarea:
            autoSize:
              minRows: 3
          labelType: type-row-readOnly
    - type: group
      label: Assignment
      collapse: false
      customPadding: 0px
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - type: text
          name: positionView
          label: Position
        - type: text
          name: companyView
          label: Company
        - type: text
          label: Legal Entity
          name: legalEntityView
        - type: text
          label: Business Unit
          name: businessUnitView
        - type: text
          label: Division
          name: divisionView
        - type: text
          name: departmentView
          label: Department
        - type: text
          name: locationView
          label: Location
        - type: text
          name: regionName
          label: Region
          _value:
            transform: >-
              $.extend.defaultValue.regionName ?
              $.extend.defaultValue.regionName : $.extend.defaultValue.region
        - type: dateRange
          label: Department Entry Date
          name: departmentEntryDate
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
          placeholder: dd/MM/yyyy
        - type: text
          label: Job
          name: jobView
        - type: text
          name: businessTitleView
          label: Business Title
        - type: text
          name: careerStreamView
          label: Career Stream
        - type: text
          name: careerBandView
          label: Career Band
        - type: number
          label: FTE
          name: fte
          placeholder: Enter FTE
        - type: text
          name: empLevelView
          label: Emp Level
        - type: text
          name: costCenterView
          label: Cost Center
        - type: text
          name: reportToPosView
          label: Report To Pos
        - type: text
          name: supervisorView
          label: Supervisor
        - type: text
          name: matrixReportPositionView
          label: Matrix Report To
        - type: text
          name: matrixManagerView
          label: Matrix Manager
        - type: text
          label: Contract Number
          name: contractNumber
        - type: text
          label: Contract Type
          name: contractTypeName
        - type: text
          name: fullPartName
          label: Full/Part
          _value:
            transform: >-
              $.extend.defaultValue.fullPartName ?
              $.extend.defaultValue.fullPartName :
              $.extend.defaultValue.fullPartCode
        - type: dateRange
          label: Group First Start Date
          name: groupFirstStartDate
          mode: date-picker
        - type: text
          label: Job Seniority
          name: jobSeniority
        - type: text
          name: timeZoneName
          label: Time Zone
          _value:
            transform: >-
              $.extend.defaultValue.timeZoneName ?
              $.extend.defaultValue.timeZoneName :
              $.extend.defaultValue.timezone
        - type: upload
          label: Attach File
          name: attachmentResults
          status: true
    - type: group
      label: Organizational Instance
      collapse: false
      isBorderTopNone: true
      customPadding: 0px
      n_cols: 2
      _condition:
        transform: $.extend.formType = 'viewMore'
      fields:
        - type: number
          label: Organizational Instance Rcd
          name: organizationalInstanceRecord
          disabled: true
          labelType: type-row-readOnly
        - type: number
          name: employeeRecordNumber
          label: Employee Number Record
          disabled: true
          labelType: type-row-readOnly
    - type: group
      label: Action
      collapse: false
      _condition:
        transform: $.extend.formType = 'viewMore'
      n_cols: 2
      fields:
        - type: dateRange
          label: Effective Date
          name: effectiveDate
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
          placeholder: dd/MM/yyyy
          labelType: type-row-readOnly
        - type: text
          label: Employee Group
          name: employeeGroupName
          labelType: type-row-readOnly
          _value:
            transform: >-
              $.extend.defaultValue.employeeGroupName ?
              $.extend.defaultValue.employeeGroupName :
              $.extend.defaultValue.employeeGroup
        - type: text
          label: Job Indicator
          name: jobIndicatorName
          labelType: type-row-readOnly
          _value:
            transform: >-
              $.extend.defaultValue.jobIndicatorName ?
              $.extend.defaultValue.jobIndicatorName :
              $.extend.defaultValue.jobIndicator
        - type: text
          label: Action
          name: actionName
          labelType: type-row-readOnly
          _value:
            transform: >-
              $.extend.defaultValue.actionName ?
              $.extend.defaultValue.actionName :
              $.extend.defaultValue.actionCode
        - type: text
          label: Reason
          name: actionReasonName
          labelType: type-row-readOnly
          _value:
            transform: >-
              $.extend.defaultValue.actionReasonName ?
              $.extend.defaultValue.actionReasonName :
              $.extend.defaultValue.actionReasonCode
        - type: number
          name: effectiveSequence
          label: Sequence No.
          placeholder: Enter Sequence No.
          labelType: type-row-readOnly
        - type: radio
          label: HR Status
          name: hrStatus
          _radio:
            transform: >-
              [{'label': $.extend.defaultValue.hrStatusName, 'value':
              $.extend.defaultValue.hrStatus}]
          labelType: type-row-readOnly
        - type: text
          label: Payroll Status
          name: payrollStatusName
          placeholder: Select Payroll Status
          labelType: type-row-readOnly
          _value:
            transform: >-
              $.extend.defaultValue.payrollStatusName ?
              $.extend.defaultValue.payrollStatusName :
              $.extend.defaultValue.payrollStatus
        - type: text
          label: Employee Sub Group
          name: employeeSubGroupName
          labelType: type-row-readOnly
          _value:
            transform: >-
              $.extend.defaultValue.employeeSubGroupName ?
              $.extend.defaultValue.employeeSubGroupName :
              $.extend.defaultValue.employeeSubGroup
        - type: text
          label: Manager?
          name: isManagerName
          labelType: type-row-readOnly
          _value:
            transform: >-
              $.extend.defaultValue.isManagerName ?
              $.extend.defaultValue.isManagerName :
              $.extend.defaultValue.isManagerCode
        - type: text
          label: Level of decision
          name: levelDecisionName
          labelType: type-row-readOnly
          _value:
            transform: >-
              $.extend.defaultValue.levelDecisionName ?
              $.extend.defaultValue.levelDecisionName :
              $.extend.defaultValue.levelOfDecision
        - type: text
          name: decisionNumber
          label: Decision Number
          placeholder: Enter Decision number
          labelType: type-row-readOnly
        - type: dateRange
          label: Sign date
          name: signDate
          placeholder: dd/MM/yyyy
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
          labelType: type-row-readOnly
        - type: dateRange
          label: Expected end date
          name: expectedEndDate
          placeholder: dd/MM/yyyy
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
          validators:
            - type: ppx-custom
              args:
                transform: >-
                  $exists($.fields.effectiveDate) and
                  $exists($.fields.expectedEndDate) ?
                  $DateDiff($DateFormat($.fields.effectiveDate, 'yyyy-MM-DD'),
                  $DateFormat($.fields.expectedEndDate, 'yyyy-MM-DD'), 'd') > 0
              text: >-
                Expected end date must be greater than or equal to the Effective
                date
          labelType: type-row-readOnly
        - type: text
          label: Classification of Termination
          name: classificationOfTerminationName
          labelType: type-row-readOnly
          _condition:
            transform: >-
              $.fields.hrStatus = 'I' and
              $.extend.permission.ClassificationOfTermination.Read
          _value:
            transform: >-
              $.extend.defaultValue.classificationOfTerminationName ?
              $.extend.defaultValue.classificationOfTerminationName :
              $.extend.defaultValue.classificationOfTerminationCode
        - type: textarea
          name: note
          label: Note
          placeholder: Enter note
          validators:
            - type: maxLength
              args: '500'
              text: Maximum length is 500 characters.
          textarea:
            autoSize:
              minRows: 3
          labelType: type-row-readOnly
    - type: group
      label: Assignment
      collapse: false
      _condition:
        transform: $.extend.formType = 'viewMore'
      n_cols: 2
      fields:
        - type: text
          name: positionView
          label: Position
        - type: text
          name: companyView
          label: Company
        - type: text
          label: Legal Entity
          name: legalEntityView
        - type: text
          label: Business Unit
          name: businessUnitView
        - type: text
          label: Division
          name: divisionView
        - type: text
          name: departmentView
          label: Department
        - type: text
          name: locationView
          label: Location
        - type: text
          name: regionName
          label: Region
          _value:
            transform: >-
              $.extend.defaultValue.regionName ?
              $.extend.defaultValue.regionName : $.extend.defaultValue.region
        - type: dateRange
          label: Department Entry Date
          name: departmentEntryDate
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
          placeholder: dd/MM/yyyy
        - type: text
          label: Job
          name: jobView
        - type: text
          name: businessTitleView
          label: Business Title
        - type: text
          name: careerStreamView
          label: Career Stream
        - type: text
          name: careerBandView
          label: Career Band
        - type: number
          label: FTE
          name: fte
          placeholder: Enter FTE
        - type: text
          name: empLevelView
          label: Emp Level
        - type: text
          name: costCenterView
          label: Cost Center
        - type: text
          name: reportToPosView
          label: Report To Pos
        - type: text
          name: supervisorView
          label: Supervisor
        - type: text
          name: matrixReportPositionView
          label: Matrix Report To
        - type: text
          name: matrixManagerView
          label: Matrix Manager
        - type: text
          label: Contract Number
          name: contractNumber
        - type: text
          label: Contract Type
          name: contractTypeName
        - type: text
          name: fullPartName
          label: Full/Part
          _value:
            transform: >-
              $.extend.defaultValue.fullPartName ?
              $.extend.defaultValue.fullPartName :
              $.extend.defaultValue.fullPartCode
        - type: dateRange
          label: Group First Start Date
          name: groupFirstStartDate
          mode: date-picker
        - type: text
          label: Job Seniority
          name: jobSeniority
        - type: text
          name: timeZoneName
          label: Time Zone
          _value:
            transform: >-
              $.extend.defaultValue.timeZoneName ?
              $.extend.defaultValue.timeZoneName :
              $.extend.defaultValue.timezone
        - type: upload
          label: Attach File
          status: true
          name: attachmentResults
  sources:
    managerList:
      uri: '"/api/picklists/ISMANAGER/values"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - effectiveDate
    fullPartList:
      uri: '"/api/picklists/FULLPART/values"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - effectiveDate
    decisionLevelList:
      uri: '"/api/picklists/LEVELOFDECISION/values"'
      method: GET
      queryTransform: >-
        {'limit': 9999, 'filter': [{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - effectiveDate
    companiesList:
      uri: '"/api/companies/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
    companiyInfo:
      uri: '"/api/companies/get-by"'
      method: GET
      queryTransform: >-
        {'limit': 1,'filter': [{'field':'code','operator':
        '$eq','value':$.code},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id}})[]
      disabledCache: true
      params:
        - code
        - effectiveDate
    positionsList:
      uri: '"/api/positions/get-dropdown-list-by"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'companyCode','operator':
        '$eq','value':$.companyId},{'field':'legalEntityCode','operator':
        '$eq','value':$.legalEntityId},{'field':'departmentCode','operator':
        '$eq','value':$.departmentId},{'field':'jobCodeCode','operator':
        '$eq','value':$.jobCodeId}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code , 'id': $item.id }})[]
      disabledCache: true
      params:
        - companyId
        - effectiveDate
        - legalEntityId
        - departmentId
        - jobCodeId
    positionsListGetBy:
      uri: '"/api/positions/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code , 'id': $item.id}})[]
      disabledCache: true
      params:
        - effectiveDate
        - limit
        - page
        - search
    positionInfo:
      uri: '"/api/positions/" & $.id & ""'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $
      disabledCache: true
      params:
        - id
        - effectiveDate
    businessUnitList:
      uri: '"/api/business-units/get-list"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'status','operator':
        '$eq','value':true},{'field':'companyCode','operator':
        '$eq','value':$.companyId},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id}})[]
      disabledCache: true
      params:
        - companyId
        - effectiveDate
    legalEntitiesList:
      uri: '"/api/legal-entities/get-list"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'effectiveDate','operator': '$eq','value':
        $.effectiveDate},{'field':'status','operator':
        '$eq','value':true},{'field':'companyCode','operator':
        '$eq','value':$.companyId}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id}})[]
      disabledCache: true
      params:
        - companyId
        - effectiveDate
    departmentsList:
      uri: '"/api/departments/get-dropdown-list-by"'
      method: GET
      queryTransform: >-
        {'filter':
        [{'field':'companyCode','operator':'$eq','value':$.companyId},{'field':'legalEntityCode','operator':
        '$eq','value':$.legalEntityId},{'field':'businessUnitCode','operator':
        '$eq','value':$.businessUnitId},{'field':'divisionCode','operator':
        '$eq','value':$.divisionId},{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator': '$eq','value':
        $.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id}})[]
      disabledCache: true
      params:
        - companyId
        - legalEntityId
        - businessUnitId
        - divisionId
        - effectiveDate
    departmentInfo:
      uri: '"/api/departments/" & $.id & ""'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $
      disabledCache: true
      params:
        - id
    divisionsList:
      uri: '"/api/divisions/get-list"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'search','operator':
        '$eq','value':$.search},{'field':'status','operator':
        '$eq','value':true},{'field':'businessUnitCode','operator':
        '$eq','value':$.businessUnitId},{'field':'companyCode','operator':'$eq','value':$.companyId},{'field':'effectiveDate','operator':
        '$eq','value': $.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id}})[]
      disabledCache: true
      params:
        - businessUnitId
        - effectiveDate
        - companyId
    divisionDetail:
      uri: '"/api/divisions/" & $.id & ""'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        {'BusinessUnit': {'label': $.businessUnitName & ' (' &
        $.businessUnitCode & ')', 'value': $.businessUnitCode}}
      disabledCache: true
      params:
        - id
    locationsList:
      uri: '"/api/locations/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'companyCode','operator':
        '$eq','value':$.companyCode},{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator': '$eq','value':
        $.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id':$item.id}})[]
      disabledCache: true
      params:
        - companyCode
        - limit
        - page
        - search
        - effectiveDate
    constCenterList:
      uri: '"/api/cost-centers/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator':
        '$eq','value':true},{'field':'companyCode','operator':
        '$eq','value':$.companyId},{'field':'effectiveDate','operator':
        '$eq','value': $.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id':$item.id}})[]
      disabledCache: true
      params:
        - companyId
        - limit
        - page
        - search
        - effectiveDate
    employeeGroupsList:
      uri: '"/api/employee-groups"'
      method: GET
      queryTransform: '{''filter'': [{''field'':''status'',''operator'': ''$eq'',''value'':true}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.employeeGroup.default,
        'value': $item.code}})[]
      disabledCache: true
    employeeSubGroupsList:
      uri: '"/api/employee-sub-groups"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page, 'search': $.search, 'filter':
        [{'field':'linkCatalogDataCode','operator':
        '$eq','value':$.employeeGroupCode},{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator': '$eq','value':
        $.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - employeeGroupCode
        - effectiveDate
        - limit
        - page
        - search
    employeeSubGroupInfo:
      uri: '"/api/employee-sub-groups"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'linkCatalogDataCode','operator':
        '$eq','value':$.employeeGroupCode},{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator': '$eq','value':
        $.effectiveDate},{'field':'code','operator': '$eq','value': $.code}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default, 'value':
        $item.code}})[0]
      disabledCache: true
      params:
        - employeeGroupCode
        - effectiveDate
        - code
    employeeSubGroupsListCheck:
      uri: '"/api/employee-sub-groups"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'linkCatalogDataCode','operator':
        '$eq','value':$.employeeGroupCode},{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator': '$eq','value':
        $.effectiveDate},{'field':'code','operator': '$eq','value': $.code}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: '[$map($.data, function($item) {$item.code})]'
      disabledCache: true
      params:
        - employeeGroupCode
        - effectiveDate
        - code
    jobCodesList:
      uri: '"/api/job-codes/get-dropdown-list-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator':
        '$eq','value':true},{'field':'companyCode','operator':
        '$eq','value':$.companyId},{'field':'effectiveDate','operator':
        '$eq','value': $.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id, 'bandId':
        $item.bandId, 'careerStreamId': $item.careerStreamId}})[]
      disabledCache: true
      params:
        - companyId
        - limit
        - page
        - search
        - effectiveDate
    businessTitlesList:
      uri: '"/api/business-titles/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator': '$eq','value':
        $.effectiveDate},{'field':'companyCode','operator': '$eq','value':
        $.companyId}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - companyId
        - limit
        - page
        - search
        - effectiveDate
    careerStreamsList:
      uri: '"/api/career-streams/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator': '$eq','value':
        $.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code,'id': $item.id}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
    careerStreamDetail:
      uri: '"/api/career-streams/" & $.id & ""'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        {'careerStream': {'label': $.longName.default & ' (' & $.code & ')',
        'value': $.code, 'id': $.id}}
      disabledCache: true
      params:
        - id
    careerBandsList:
      uri: '"/api/bands/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator':
        '$eq','value':true},{'field':'careerStreamCode','operator':
        '$eq','value':$.careerStreamId},{'field':'effectiveDate','operator':
        '$eq','value': $.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($filter($.data, function ($item){ $exists($item.code) }) ,
        function($item) {{'label': $item.longName.default & ' (' & $item.code &
        ')', 'value': $item.code,'id': $item.id, 'careerStreamId':
        $item.careerStreamId}})[]
      disabledCache: true
      params:
        - careerStreamId
        - limit
        - page
        - search
        - effectiveDate
    careerBandDetail:
      uri: '"/api/bands/" & $.id & ""'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        {'band': {'label': $.longName.default & ' (' & $.code & ')', 'value':
        $.code, 'id': $.id}}
      disabledCache: true
      params:
        - id
    actionsList:
      uri: '"/api/actions/dropdown"'
      method: GET
      queryTransform: >-
        {'limit':10000 ,'filter': [{'field':'status','operator':
        '$eq','value':true},{'field':'employeeGroupCodes','operator':
        '$eq','value':$.employeeGroupCode},{'field':'processType','operator':
        '$eq','value': $.processType},{'field':'effectiveDateQuery','operator':
        '$eq','value': $.effectiveDate},{'field':'employeeId','operator':
        '$eq','value': $.employeeId},{'field':'employeeRecordNumber','operator':
        '$eq','value':
        $.employeeRecordNumber},{'field':'editJobDataId','operator':
        '$eq','value': $.editJobDataId},{'field':'effectiveSequence','operator':
        '$eq','value': $.effectiveSequence}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code,'setStatusField': $item.setStatusField,
        'prStatusCode':$item.prStatusCode, 'hrStatusCode':$item.hrStatusCode,
        'actionReasons': $item.actionReasons.actionReasonCode[]}})[]
      disabledCache: true
      params:
        - employeeGroupCode
        - processType
        - effectiveDate
        - employeeId
        - employeeRecordNumber
        - editJobDataId
        - effectiveSequence
    reasonsList:
      uri: '"/api/picklists/ACTIONREASON/values"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'filter':[{'field':'code','operator':'$in','value':
        $.reasonCodes},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - reasonCodes
        - effectiveDate
        - limit
    hrStatusList:
      uri: '"/api/picklists/HRSTATUS/values"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - effectiveDate
    payrollStatusList:
      uri: '"/api/picklists/PAYROLLSTATUS/values"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - effectiveDate
    regionList:
      uri: '"/api/picklists/REGIONS/values"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - effectiveDate
    jobIndicatorList:
      uri: '"/api/picklists/JOBINDICATOR/values"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - effectiveDate
    timezoneList:
      uri: '"/api/picklists/TIMEZONE/values"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - effectiveDate
    empLevelList:
      uri: '"/api/picklists/EMPLEVEL/values/pagination"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
    CLASSOFTERList:
      uri: '"/api/picklists/CLASSIFICATIONOFTERMINATION/values"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - effectiveDate
    personalsList:
      uri: '"/api/personals/all-employees-job-datas"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'effectiveDate','operator': '$eq','value':
        $.effectiveDate},{'field':'hrStatus','operator': '$eq','value':
        $.hrStatus}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.fullName &' ('&
        $item.employeeId & ')', 'value': $item.employeeId}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
        - hrStatus
    personalsCheckList:
      uri: '"/api/personals/all-employees-job-datas"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'filter': [{'field':'effectiveDate','operator':
        '$eq','value': $.effectiveDate},{'field':'hrStatus','operator':
        '$eq','value':
        $.hrStatus},{'field':'employeeId','operator':'$in','value':
        $.employeeId}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        [$map($, function($item) {{'label': $item.fullName &' ('&
        $item.employeeId & ')', 'value': $item.employeeId}})]
      disabledCache: true
      params:
        - effectiveDate
        - hrStatus
        - employeeId
        - limit
    organizationPick:
      uri: >-
        "/api/trees/organization/pick/" & $.pickType & "/" & $.code & "/" &
        $.effectiveDate & ""
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $reduce(     $,  function($acc, $item) {   $merge([     $acc,     {
        $item.type : ($origItem := $lookup($acc, $item.type); $validItem :=
        $item.effectiveDate ? $toMillis($item.effectiveDate) <=
        $toMillis($now()) : true ; $exists($origItem) and
        $toMillis($origItem.effectiveDate) > $toMillis($item.effectiveDate)  ?
        $origItem : ($validItem ? {'label': $item.name & ' (' & $item.code &
        ')', 'value': $item.code, 'type': $item.type, 'effectiveDate':
        $item.effectiveDate } : $origItem))    }    ])  }, {} )
      disabledCache: true
      params:
        - pickType
        - code
        - effectiveDate
    checkTotalFte:
      uri: '"/api/personals/" & $.employeeId & "/job-datas/total-fte"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'inputFte','operator':
        '$eq','value':$.fte},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'employeeRecordNumber','operator':
        '$eq','value':$.employeeRecordNumber},{'field':'jobDataId','operator':
        '$eq','value':$.jobDataId}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $
      disabledCache: true
      params:
        - employeeId
        - fte
        - effectiveDate
        - employeeRecordNumber
        - jobDataId
    getHistory:
      uri: '"/api/personals/" & $.employeeId & "/job-datas/histories"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'employeeRecordNumber','operator':
        '$eq','value':$.employeeRecordNumber}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $
      disabledCache: true
      params:
        - employeeId
        - employeeRecordNumber
    calculateJobSeniority:
      uri: '"/api/personals/" & $.employeeId & "/job-datas/calculate-job-seniority"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'employeeRecordNumber','operator':
        '$eq','value':$.employeeRecordNumber},{'field':'organizationalInstanceRcd','operator':
        '$eq','value':$.organizationalInstanceRcd},{'field':'actionCode','operator':
        '$eq','value':$.actionCode},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'employeeSubGroupCode','operator':
        '$eq','value':$.employeeSubGroupCode},{'field':'jobCode','operator':
        '$eq','value':$.jobCode},{'field':'companyCode','operator':
        '$eq','value':$.companyCode},{'field':'hrStatusCode','operator':
        '$eq','value':$.hrStatusCode},{'field':'jobDataId','operator':
        '$eq','value':$.jobDataId},{'field':'effectiveSequence','operator':
        '$eq','value':$.effectiveSequence}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $
      disabledCache: true
      params:
        - employeeId
        - employeeRecordNumber
        - organizationalInstanceRcd
        - actionCode
        - effectiveDate
        - employeeSubGroupCode
        - jobCode
        - companyCode
        - hrStatusCode
        - jobDataId
        - effectiveSequence
    previousJobdata:
      uri: >-
        "/api/personals/" & $.employeeId & "/job-datas/previous-record/" &
        $.jobdataId & ""
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $
      disabledCache: true
      params:
        - employeeId
        - jobdataId
    getContract:
      uri: '"/api/personals/" & $.employeeId & "/contracts/get-contract"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'employeeRecordNumber','operator':
        '$eq','value':$.employeeRecordNumber},{'field':'jobDataId','operator':
        '$eq','value':$.jobDataId},{'field':'actionCode','operator':
        '$eq','value':$.actionCode},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $
      disabledCache: true
      params:
        - employeeId
        - employeeRecordNumber
        - jobDataId
        - actionCode
        - effectiveDate
    checkTerminateAction:
      uri: '"/api/actions/check-terminate-action"'
      method: POST
      queryTransform: ''
      bodyTransform: '{''effectiveDate'': $.effectiveDate, ''actionCode'': $.actionCode}'
      headerTransform: ''
      resultTransform: $
      disabledCache: true
      params:
        - actionCode
        - effectiveDate
    haveContractUpdated:
      uri: '"/api/personals/" & $.employeeId & "/job-datas/have-contract-updated"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'employeeRecordNumber','operator':
        '$eq','value':$.employeeRecordNumber},{'field':'jobDataId','operator':
        '$eq','value':$.jobDataId},{'field':'actionCode','operator':
        '$eq','value':$.actionCode},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $
      disabledCache: true
      params:
        - employeeId
        - employeeRecordNumber
        - jobDataId
        - actionCode
        - effectiveDate
    checkDayOffTsApplicationForLeave:
      uri: >-
        "/api/ts-au-application-for-leaves/check-day-off-ts-application-for-leave"
      method: POST
      queryTransform: ''
      bodyTransform: >-
        {'employeeCode': $.employeeId,'resignationDate': $.effectiveDate,
        'employeeRecordNumber': $string($.employeeRecordNumber)}
      headerTransform: ''
      resultTransform: $
      disabledCache: true
      params:
        - employeeId
        - employeeRecordNumber
        - effectiveDate
    getRelateiveInfoJobData:
      uri: >-
        "/api/personals/" & $.employeeId &
        "/job-datas/get-relateive-info-job-data"
      method: GET
      queryTransform: >-
        {'filter': [{'field':'employeeRecordNumber','operator':
        '$eq','value':$.employeeRecordNumber},{'field':'organizationalInstanceRecord','operator':
        '$eq','value':$.organizationalInstanceRecord},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'jobDataId','operator':
        '$eq','value':$.jobDataId}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $
      disabledCache: true
      params:
        - employeeId
        - employeeRecordNumber
        - organizationalInstanceRecord
        - effectiveDate
        - jobDataId
    validateRelateiveInfo:
      uri: '"/api/personals/" & $.employeeId & "/job-datas/validate-relative-infos"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'jobDataId','operator':
        '$eq','value':$.jobDataId},{'field':'employeeId','operator':
        '$eq','value':$.employeeId},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'actionCode','operator':
        '$eq','value':$.actionCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $
      disabledCache: true
      params:
        - employeeId
        - jobDataId
        - effectiveDate
        - actionCode
    getJobdataPrimary:
      uri: '"/api/personals/" & $.employeeId & "/job-datas"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'jobIndicator','operator': '$eq','value':
        'P'},{'field':'hrStatus','operator': '$eq','value': 'A'}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: '$.data ? $.data[] : []'
      disabledCache: true
      params:
        - employeeId
  historyTitle: >-
    $DateFormat($.effectiveDate, 'DD/MM/YYYY') &  ($.isMultiSequence ? ' (' &
    $.effectiveSequence & ')' : '')
  historyDescription: $.actionName & ' - ' & $.departmentName & ' - ' & $.jobName
  skipInsertNewProceed: true
  customTypeEmitInsertNew: proceed
  customDataSelectedItem: >-
    $merge([$.selectedItem,{'isHistory': true, 'minDate':
    $.selectedItem.effectiveDate, 'maxDate': $exists($.nextItem) ?
    $.nextItem.effectiveDate  }])
  customDataHistory: >-
    ($checkIsFirst := function($date) {  $count($filter($, function($item) {
    $item.effectiveDate = $date })) > 1 };  $ ~> | $ | {     'isMultiSequence':
    $checkIsFirst($.effectiveDate) ,'currentId': $.id, 'radioGroup': '1' ,
    'empId': $.employeeId  } | )
  variables:
    _calculateJobSeniority:
      transform: >-
        $not($.extend.formType = 'view') and $.extend.params.id1 and
        $exists($.fields.organizationalInstanceRecord) and
        $exists($.fields.effectiveDate) and
        $exists($.fields.employeeRecordNumber) and $exists($.fields.actionObj)
        and $exists($.fields.employeeSubGroup) and $exists($.fields.jobObj) and
        $exists($.fields.company) and $exists($.fields.hrStatus) ?
        $calculateJobSeniority($.extend.params.id1,
        $.fields.employeeRecordNumber, $.fields.organizationalInstanceRecord,
        $.fields.actionObj.value, $.fields.effectiveDate,
        $.fields.employeeSubGroup, $.fields.jobObj.value, $.fields.company,
        $.fields.hrStatus, $.extend.formType = 'edit' ? $.fields.id : null,
        $.extend.formType = 'create' ? $.fields.effectiveSequence : null)
    _checkTotalFte:
      transform: >-
        $not($.extend.formType = 'view') and $.extend.params.id1 and
        $exists($.fields.fte) and $exists($.fields.effectiveDate) and
        $exists($.fields.employeeRecordNumber) ?
        $checkTotalFte($.extend.params.id1, $.fields.fte,
        $DateToTimestampUTC($.fields.effectiveDate),
        $.fields.employeeRecordNumber, $.extend.formType = 'edit' ? $.fields.id
        : null)
    _actionForCreate1:
      transform: >-
        $.extend.formType = 'create' and $.fields.radioGroup = '1' and
        $count($.fields.employeeGroup) > 0 and $exists($.fields.effectiveDate)
        and $exists($.fields.employeeRecordNumber) and
        $exists($.fields.effectiveSequence) ?
        $actionsList($.fields.employeeGroup,$.fields.radioGroup,$DateToTimestampUTC($.fields.effectiveDate),
        $.extend.params.id1, $.fields.employeeRecordNumber, null,
        $.fields.effectiveSequence)
    _actionForCreate2:
      transform: >-
        $.extend.formType = 'create' and $.fields.radioGroup = '3' and
        $exists($.fields.effectiveDate) and $count($.fields.employeeGroup) > 0 ?
        $actionsList($.fields.employeeGroup,$.fields.radioGroup,$DateToTimestampUTC($.fields.effectiveDate))
    _actionForEdit:
      transform: >-
        $.extend.formType = 'edit' and $count($.fields.employeeGroup) > 0 and
        $exists($.fields.effectiveDate) and $exists($.fields.id) ?
        $actionsList($.fields.employeeGroup,null,$DateToTimestampUTC($.fields.effectiveDate),null,null,$.fields.id)
    _actionsList:
      transform: >-
        $.extend.formType = 'create' and $.fields.radioGroup = '1' ?
        $.variables._actionForCreate1 : $.extend.formType = 'create' and
        $.fields.radioGroup = '3' ? $.variables._actionForCreate2 :
        $.extend.formType = 'edit' ? $.variables._actionForEdit
    _selectedAction:
      transform: >-
        $exists($.variables._actionsList) and $boolean($.fields.actionObj.value)
        ? $filter($.variables._actionsList, function($item) { $item.value =
        $.fields.actionObj.value })[0]
    _positionInfo:
      transform: >-
        $not($.extend.formType = 'view') and $exists($.fields.positionObj.id)
        and $exists($.fields.effectiveDate) ?
        $positionInfo($.fields.positionObj.id,$.fields.effectiveDate)
    _getHistory:
      transform: >-
        $.extend.formType = 'create' and $.extend.params.id1 and
        $exists($.fields.employeeRecordNumber) ?
        $getHistory($.extend.params.id1,$.fields.employeeRecordNumber)
    _getHistorySameEffectiveDate:
      transform: >-
        $.variables._getHistory ? ( $data := $filter($.variables._getHistory,
        function($item){$DateFormat($.fields.effectiveDate, 'yyyy-MM-DD') =
        $DateFormat($item.effectiveDate, 'yyyy-MM-DD')}) ; $result :=
        $reduce($data, function($acc,$item){ $exists($acc.effectiveSequence) and
        $acc.effectiveSequence > $item.effectiveSequence ? $acc : $item },{}))
    _getHistorySameSequence:
      transform: >-
        $.variables._getHistory ? ( $data := $filter($.variables._getHistory,
        function($item){$DateFormat($.fields.effectiveDate, 'yyyy-MM-DD') =
        $DateFormat($item.effectiveDate, 'yyyy-MM-DD') and
        $item.effectiveSequence = $.fields.effectiveSequence and
        $DateFormat($.fields.expectedEndDate, 'yyyy-MM-DD') =
        $DateFormat($item.expectedEndDate, 'yyyy-MM-DD') })[0])
    _previousJobdata:
      transform: >-
        $.extend.formType = 'edit' and $.fields.id ?
        $previousJobdata($.extend.params.id1, $.fields.id)
    _getContract:
      transform: >-
        $not($.extend.formType = 'view') and $.extend.params.id1 and
        $exists($.fields.employeeRecordNumber) and
        $exists($.fields.effectiveDate) ? $getContract($.extend.params.id1,
        $.fields.employeeRecordNumber, $.extend.formType = 'edit' ? $.fields.id
        : null, $.fields.actionObj.value, $.fields.effectiveDate)
    _checkTerminateAction:
      transform: >-
        (($.extend.formType = 'edit' and
        ($not($DateFormat($.extend.defaultValue.effectiveDate,'YYYY-MM-DD') =
        $DateFormat($.fields.effectiveDate,'YYYY-MM-DD')) or
        $not($.extend.defaultValue.actionObj.value = $.fields.actionObj.value)))
        or $.extend.formType = 'create') and $boolean($.fields.actionObj.value)
        and $exists($.fields.effectiveDate) ?
        $checkTerminateAction($.fields.actionObj.value, $.fields.effectiveDate)
    _checkDayOffTsApplicationForLeave:
      transform: >-
        $.variables._checkTerminateAction.isTerminateAction and
        ($.extend.formType = 'edit' or $.extend.formType = 'create') and
        $exists($.fields.effectiveDate) and $exists($.extend.params.id1) ?
        $checkDayOffTsApplicationForLeave($.extend.params.id1,
        $.fields.employeeRecordNumber, $.fields.effectiveDate)
    _haveContractUpdated:
      transform: >-
        $.variables._checkTerminateAction.isTerminateAction and
        $boolean($.fields.actionObj.value) and $exists($.fields.effectiveDate) ?
        $haveContractUpdated($.extend.params.id1, $.fields.employeeRecordNumber,
        $.extend.formType = 'edit' ? $.fields.id : null,
        $.fields.actionObj.value, $.fields.effectiveDate)
    _getRelateiveInfoJobData:
      transform: >-
        $.fields.jobIndicator = 'P' and
        $.variables._checkTerminateAction.isTerminateAction and
        $exists($.fields.organizationalInstanceRecord) and
        $exists($.fields.effectiveDate) ?
        $getRelateiveInfoJobData($.extend.params.id1,
        $.fields.employeeRecordNumber, $.fields.organizationalInstanceRecord,
        $.fields.effectiveDate, $.extend.formType = 'edit' ? $.fields.id : null)
    _checkSameDate:
      transform: >-
        $exists($.extend.responseError.errorValue.effectiveDate) ?
        $DateDiff($DateFormat($.extend.responseError.errorValue.effectiveDate,'YYYY-MM-DD'),
        $DateFormat($.fields.effectiveDate, 'YYYY-MM-DD')) = 0 : false
    _validateMatrixManager:
      transform: >-
        ($stringData := $split($.extend.responseError.MatrixManager, ',')[];
        $dataFilter := $filter($.fields.matrixManagerObjs, function($it){
        $it.value in $stringData })[] ; $count($dataFilter) > 0 and
        $.variables._checkSameDate ? ($dataNew := $map($dataFilter,
        function($it){$it.value})[] ; $join($dataNew, ', ')) : null )
    _validateMatrixReport:
      transform: >-
        ($stringData := $split($.extend.responseError.MatrixReportPositionCodes,
        ',')[]; $dataFilter := $filter($.fields.matrixReportPositionObjs,
        function($it){ $it.value in $stringData })[] ; $count($dataFilter) > 0
        and $.variables._checkSameDate ? ($dataNew := $map($dataFilter,
        function($it){$it.value})[] ; $join($dataNew, ', ')) : null )
    _validateRelateiveInfo:
      transform: >-
        $.extend.formType = 'edit' and
        $not($DateFormat($.extend.defaultValue.effectiveDate,'YYYY-MM-DD') =
        $DateFormat($.fields.effectiveDate,'YYYY-MM-DD')) and
        $exists($.fields.effectiveDate) and $exists($.extend.params.id1) ?
        $validateRelateiveInfo($.extend.params.id1, $.fields.id,
        $.fields.effectiveDate, $.fields.actionObj.value)
    _messageValidateRelateiveInfo:
      transform: >-
        $.extend.formType = 'edit' and
        $not($DateFormat($.extend.defaultValue.effectiveDate,'YYYY-MM-DD') =
        $DateFormat($.fields.effectiveDate,'YYYY-MM-DD')) and
        $exists($.variables._validateRelateiveInfo) and
        $not($.variables._validateRelateiveInfo.hasError = true) and
        $not($.variables._validateRelateiveInfo.isActionError = true) ? "Please
        Review and update the relevant staff profile information (if necessary)
        to ensure accuracy and completeness of the data: <br/> - Contract <br/>
        - Employee Payment Account <br/> - Employee's Payroll Data. <br/> -
        Employee's Timesheet. <br/> - Employee's Personal Income Tax Data. <br/>
        - Employee's Insurance Data"
    _getJobdataPrimary:
      transform: >-
        $.extend.formType = 'create' and $.fields.radioGroup = '3' ? ( $getData
        := $getJobdataPrimary($.extend.params.id1); $count($getData))
  form_value_transform: >-
    $merge([$,{'processType': $.radioGroup,'actionCode': $.actionObj.value,
    'position': $.positionObj.value, 'costCenter': $.costCenterObj.value,
    'reportToPos': $.reportToPosObj.value, 'legalEntityCode':
    $.legalEntityObj.value, 'supervisor': $.supervisorObj.value, 'businessUnit':
    $.businessUnitObj.value, 'matrixReportPositionCodes':
    $map($.matrixReportPositionObjs, function($it){$it.value})[] , 'division':
    $.divisionObj.value, 'matrixManagers': $map($.matrixManagerObjs,
    function($it){$it.value})[], 'department': $.departmentObj.value,
    'location': $.locationObj.value, 'job': $.jobObj.value, 'businessTitle':
    $.businessTitleObj.value, 'careerStream': $.careerStreamObj.value,
    'careerBand': $.careerBandObj.value, 'region': $.regionObj.value }])
filter_config:
  fields:
    - type: group
      padding: 10px 20px
      fieldBackground: '#F1F3F5'
      fields:
        - type: select
          label: Employee Record Number
          name: employeeRecordNumber
          outputValue: value
          labelType: type-row
          placeholder: Select Employee Record Number
          _select:
            transform: $.variables._jobDatasList
          _defaultValue:
            transform: >-
              $not($.extend.formType = 'filter') ? $.variables._jobDatasList ?
              $.variables._jobDatasList[0].value
          _allowValues:
            transform: $map($.variables._jobDatasList, function($it){$it.value})[]
  filterMapping:
    - field: employeeRecordNumber
      operator: $eq
      valueField: employeeRecordNumber
  sources:
    jobDatasList:
      uri: '"/api/personals/" & $.empId & "/job-datas/employee-unique-ern"'
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': { 'title': $item.employeeRecordNumber
        & ' - ' & $item.employeeGroup & ' - ' & $item.hrStatus  & ' - ' &
        $item.companyName & ' - ' & $item.departmentName & ' - ' &
        $item.jobName, 'iconColor': $item.jobIndicator = 'P' ? '#ebd847' :
        '#c0cbd83d', 'icon': 'icon-ic-star-action' } , 'value':
        $string($item.employeeRecordNumber), 'jobIndicator':
        $item.jobIndicator}})[]
      disabledCache: true
      params:
        - empId
        - effectiveDate
  variables:
    _jobDatasList:
      transform: >-
        $.extend.params.id1 ? $jobDatasList($.extend.params.id1,$CalDate($now(),
        1, 'd' ),$.extend.refresh) : []
  update_header_profile: $.employeeRecordNumber
layout_options:
  icon: database
  filterType: widget
  is_upload_file: true
  n_cols: 2
  show_navigate_to_contact_btn: true
  check_permission_record_history: true
  get_children_action_permission: true
  history_widget_header_options:
    duplicate: false
  is_check_permission_with_accessType: true
  widget_options:
    empty:
      is_proceed: true
    show_more_content_type: widget-drawer
    refreshHeader: true
    refreshProfile: true
    refeshDependent:
      - HR.FS.FR.010
      - HR.FS.FR.045
    condition_refresh_config: >-
      $string($.currentData.employeeRecordNumber) =
      $string($.receiptData.employeeRecordNumber)
  is_new_dynamic_form: true
  precheck_delete_api:
    source:
      uri: >-
        "/api/personals/" & $.employeeId & "/job-datas/" & $.id &
        "/check-black-block"
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: '$ ? $ : {''hasBlackBlockInfos'': false}'
      disabledCache: true
    confirm:
      _content:
        transform: >-
          $.precheckRes.hasBlackBlockInfos and $.precheckRes.message ?
          $.precheckRes.message
  custom_message_on_submit:
    update:
      success:
        _message: >-
          $exists($.variables._validateRelateiveInfo) and
          $not($.variables._validateRelateiveInfo.isActionError = true) ?
          "Employee's seniority information will be updated according to the new
          effective date."
        _type: >-
          $exists($.variables._validateRelateiveInfo) and
          $not($.variables._validateRelateiveInfo.isActionError = true) ?
          'dialog'
layout_options__header_buttons:
  - id: proceed
    title: Proceed
    icon: icon-plus-bold
  - id: history
    title: history
    icon: icon-clock-counter-clockwise-bold
options: null
create_form:
  formTitle:
    proceed: What would you like to Add?
  nestedConfirmOnSubmit:
    name: checkActionList
    skipConfirm: true
    transform: >-
      $.currentValue.radioGroup = '3' and $.variables._checkActionList = 0 ?
      {'title': 'Warning','content': 'No action is available to insert a new
      employee record number.','dialogOptions': {'nzOkText': 'OK',
      'nzCancelText': null, 'nzClassName': 'popup-got-it'}}
  fields:
    - type: text
      name: empId
      unvisible: true
      _value:
        transform: $.extend.params.id1
    - type: text
      name: currentId
      unvisible: true
      _value:
        transform: >-
          $.fields.radioGroup = '3' and $exists($.fields.primaryJob3) ?
          $.fields.primaryJob3 :
          $string($.extend.params.data.employeeRecordNumber)
    - name: radioGroup
      type: radio
      direction: column
      hasBackground: true
      radioGroupType: custom
      _radio:
        transform: >-
          ($array :=
          $getArrayValueOfRadioJobdata($exists($.extend.params.data.id),
          $.extend.permission); $filter($.variables._dataForRadio,
          function($it){ $it.value in $array })[])
      _value:
        transform: '$not($exists($.extend.params.data.id)) ? ''2'' : ''1'''
      sources:
        jobDatasList:
          uri: '"/api/personals/" & $.empId & "/job-datas"'
          method: GET
          queryTransform: '{''limit'':10000}'
          bodyTransform: ''
          headerTransform: ''
          resultTransform: >-
            ($list := $map($distinct($.data.organizationalInstanceRcd),
            function($rcd) { $reduce($filter($.data, function($v) {
            $v.organizationalInstanceRcd = $rcd }), function($a, $b) {
            $a.effectiveDate > $b.effectiveDate ? $a : $b })}) ; $map($list,
            function($item) {  {'label': $item.organizationalInstanceRcd & ' - '
            & $item.employeeGroup & ' - ' & $item.companyName , 'value':
            $item.id , 'employeeGroup': $item.employeeGroup
            ,'employeeGroupName':$item.employeeGroupName, 'jobIndicator' :'S'
            ,'effectiveDate':$item.effectiveDate,'organizationalInstanceRcd':$item.organizationalInstanceRcd,'employeeSubGroup':$item.employeeSubGroup,'company':$item.company,'companyCode':$item.companyCode,'companyView':$item.companyView,'isManagerCode':$item.isManagerCode,'hrStatusDefault':$item.hrStatusDefault
            , 'effectiveSequence': 0}})[])
          disabledCache: true
          params:
            - empId
            - effectiveDate
        employeeGroupsList:
          uri: '"/api/employee-groups"'
          method: GET
          queryTransform: ''
          bodyTransform: ''
          headerTransform: ''
          resultTransform: >-
            $map($.data, function($item) {{'label': $item.employeeGroup.default,
            'value': $item.code}})
          disabledCache: true
      variables:
        _employeeGroupsList:
          transform: >-
            ($data := $employeeGroupsList(); $filter($data, function($it){
            ($it.value = 'POI' and 'HR.FS.FR.046_03' in
            $.extend.permission.addOirJobdataList) or ($it.value = 'EMP' and
            'HR.FS.FR.046_01' in $.extend.permission.addOirJobdataList) or
            ($it.value = 'CWR' and 'HR.FS.FR.046_02' in
            $.extend.permission.addOirJobdataList)})[] )
    - type: text
      name: primaryJob3
      unvisible: true
      _value:
        transform: '$.fields.primaryJob3Obj ? $.fields.primaryJob3Obj.value '
    - name: effectiveDate
      label: When should these changes take effects
      type: dateRange
      mode: date-picker
      _value:
        transform: $now()
      validators:
        - type: required
  handleProceedConfig:
    proceedRedirect:
      _enabledRedirect: $.radioGroup = '2'
      _redirectTo: >-
        $.employeeGroup = 'EMP' ? '/HR/HR.FS.FR.046_01' : $.employeeGroup =
        'CWR' ? '/HR/HR.FS.FR.046_02' : $.employeeGroup = 'POI' ?
        '/HR/HR.FS.FR.046_03' : ''
      _paramsRedirect: >-
        {'hire': {'employeeId': $.empId, 'employeeGroup': $.employeeGroup,
        'primaryJob': $.currentId, 'effectiveDate': $.effectiveDate }}
    receiveResponse:
      _type:
        transform: ' $.responseData.isTerminated ? ''action'' : ( $.formData.radioGroup = ''3'' or $.formData.isHistory ) ? ''open-form'' : ''request'''
      actionConfig:
        title: Employee has Inactive
        content: >-
          You need to rehire employee to add new actions. Do you want to rehire
          employee?
        onConfirm:
          type: navigate
          link: /HR/HR.FS.FR.038
          _valueData:
            transform: >-
              ($valueData := $merge([$.responseData, $.formData]) ; {'rehire':
              $merge([$.selectedData, $valueData, {'employeeID':
              $.selectedData.employeeId, 'jobDataId': $valueData.matchJobDataId,
              'terminateDate': $.formData.effectiveDate }])} )
      _valueData:
        transform: >-
          $.formData.radioGroup = '3' or $.formData.isHistory ? ($valueData :=
          $merge([$.formData, $.responseData ]) ; $.formData.radioGroup = '3' ?
          $valueData := $merge([$.formData.primaryJob3Obj, $valueData ]) :
          $valueData := $merge([ $valueData, {'matchJobDataId': $.formData.id}
          ]) ) : $merge([$.currentResponseData, $.formData,{'employeeGroup':
          $.currentResponseData.employeeGroup}, $.responseData ])
      sourceRequest:
        uri: >-
          "/api/personals/" & $._params.id1 & "/job-datas/" &
          $.responseData.matchJobDataId & ""
        method: GET
        queryTransform: ''
        bodyTransform: ''
        headerTransform: ''
        resultTransform: $
        disabledCache: true
  backendUrl: /api/ern-oir/:empId
  filterObject: >-
    $filter([{'field':'effectiveDate','operator': '$eq','value':
    $.effectiveDate},{'field':'processType','operator': '$eq','value':
    $.radioGroup},{'field':'employeeRecordNumber','operator': '$eq','value':
    $.currentId },{'field':'jobDataId','operator': '$eq','value': $.currentId}],
    function($item){ $.radioGroup = '1' and $not($.isHistory = true) ?
    $not($item.field = 'jobDataId') : $not($item.field = 'employeeRecordNumber')
    })
  sources:
    checkActionList:
      uri: '"/api/actions/dropdown"'
      method: GET
      queryTransform: >-
        {'filter':[{'field':'effectiveDateQuery','operator': '$eq','value':
        $.effectiveDate},{'field':'processType','operator': '$eq','value':
        $.processType},{'field':'jobDataId','operator': '$eq','value':
        $.jobDataId},{'field':'employeeGroupCodes','operator': '$eq','value':
        $.employeeGroup}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: '$.total ? $.total : 0'
      disabledCache: true
      params:
        - effectiveDate
        - processType
        - jobDataId
        - employeeGroup
  variables:
    _dataForRadio:
      transform: >-
        [{"label":"New action for this Emp Record
        Number","value":"1"},{"label":"New Organizational Instance
        Rcd","value":"2","fields":[{"name":"employeeGroup","label":"Employee
        Group","type":"select","placeholder":"Select Employee
        Group","outputValue":"value","_select":{"transform":"$.variables._employeeGroupsList"},"validators":[{"type":"required"}]}]},{"label":"New
        Emp Record Number in Organizational Instance
        Rcd","value":"3","fields":[{"name":"primaryJob3Obj","placeholder":"Select
        Organizational Instance
        Rcd","type":"select","_select":{"transform":"$jobDatasList($.extend.params.id1,$CalDate($now(),
        1, 'd') )"},"label":"Organizational Instance
        Rcd","validators":[{"type":"required"}]},{"name":"employeeGroup","label":"Employee
        Group","type":"select","placeholder":"Select Employee
        Group","outputValue":"value","_select":{"transform":"$employeeGroupsList()"},"validators":[{"type":"required"}],"_value":{"transform":"$.fields.primaryJob3Obj
        ? $.fields.primaryJob3Obj.employeeGroup"},"disabled":true}]}]
    _checkActionList:
      transform: >-
        $.fields.radioGroup = '3' and $exists($.fields.effectiveDate) and
        $exists($.fields.primaryJob3Obj.value) and
        $exists($.fields.employeeGroup) ?
        $checkActionList($DateToTimestampUTC($.fields.effectiveDate),
        $.fields.radioGroup, $.fields.primaryJob3Obj.value,
        $.fields.employeeGroup)
layout_options__footer_buttons: null
layout_options__row_actions: null
backend_url: api/personals/:id1/job-datas
screen_name: null
layout_options__actions_many: null
parent: HR.FS.FR.000
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: company
    defaultName: CompanyCode
  - name: department
    defaultName: DepartmentCode
  - name: employeeId
    defaultName: EmployeeId
  - name: legalEntityCode
    defaultName: LegalEntityCode
  - name: bussinessUnit
    defaultName: BusinessUnitCode
  - name: division
    defaultName: DivisionCode
  - name: employeeRecordNumber
    defaultName: EmployeeRecordNumber
  - name: location
    defaultName: LocationCode
  - name: job
    defaultName: JobCode
  - name: employeeLevelCode
    defaultName: EmployeeLevelCode
  - name: employeeSubGroup
    defaultName: EmployeeSubGroupCode
  - name: nationCode
    defaultName: CountryCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item: null
