id: HR.FS.FR.010
status: draft
sort: 21
user_created: 78a455fe-2ca9-4621-b542-98e98ebfefe8
date_created: '2024-06-13T08:29:38.629Z'
user_updated: 56e71b91-dd39-4ebc-afd1-b4f00f38d4a5
date_updated: '2025-06-09T06:48:11.469Z'
title: Employment Seniority
requirement:
  time: 1746670803932
  blocks:
    - id: 08K0zqBp9h
      type: paragraph
      data:
        text: Quản lý thâm niên làm việc và thiết lập công thức tính thâm niên
  version: 2.30.7
screen_design: null
module: HR
local_fields:
  - code: groupEmployment
    title: Group Employment
  - code: groupOriginalStartDate
    title: Group Original Start Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    group: groupEmployment
  - code: groupFirstStartDate
    title: Group First Start Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    group: groupEmployment
  - code: groupLastStartDate
    title: Group Last Start Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    group: groupEmployment
  - code: groupLastTerminate
    title: Group Last Terminate
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    group: groupEmployment
  - code: OIREmployment
    title: OIR Employment
  - code: oirOriginalStartDate
    title: OIR Original Start Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    group: OIREmployment
  - code: oirFirstStartDate
    title: OIR First Start Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    group: OIREmployment
  - code: oirLastStartDate
    title: OIR Last Start Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    group: OIREmployment
  - code: oirLastTerminate
    title: OIR Last Terminate
    data_type:
      key: String
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    group: OIREmployment
  - code: seniority
    title: Seniority
  - code: groupSeniority
    title: Group Seniority
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    group: seniority
  - code: organizationalInstanceSeniority
    title: Organizational Instance Seniority
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    group: seniority
  - code: externalExperience
    title: External Experience
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    group: seniority
  - code: note
    title: Note
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
mock_data:
  - organizationalInstanceRcd: '0'
    groupOriginalStartDate: 01/02/2024
    groupFirstStartDate: 01/02/2024
    groupLastStartDate: 01/02/2024
    groupLastTerminate: 03/03/2024
    oirOriginalStartDate: 02/02/2024
    oirFirstStartDate: 02/02/2024
    oirLastStartDate: 02/02/2024
    oirLastTerminate: 03/03/2024
    groupSeniority: 4 Years 2 Months 2 Days
    organizationalInstanceSeniority: 4 Years 2 Months 2 Days
    externalExperience: 4 Years 2 Months 2 Days
    note: ''
  - organizationalInstanceRcd: '0'
    groupOriginalStartDate: 01/02/2024
    groupFirstStartDate: 01/02/2024
    groupLastStartDate: 01/02/2024
    groupLastTerminate: 30/03/2024
    oirOriginalStartDate: 02/02/2024
    oirFirstStartDate: 02/02/2024
    oirLastStartDate: 02/02/2024
    oirLastTerminate: 03/03/2024
    groupSeniority: 4 Years 2 Months 2 Days
    organizationalInstanceSeniority: 4 Years 2 Months 2 Days
    externalExperience: 4 Years 2 Months 2 Days
    note: ''
local_buttons: null
layout: layout-widget
form_config:
  _mode:
    transform: $not($.extend.formType = 'view') ? 'mark-scroll'
  formSize:
    create: large
    edit: large
    proceed: large
    view: small
  deleteButtonCondition: 'true'
  fields:
    - type: group
      _condition:
        transform: $not($.extend.formType = 'viewMore')
      fields:
        - type: text
          label: Organizational Instance Rcd
          name: organizationalInstanceRcdView
          disabled: true
          placeholder: Job Data
          _value:
            transform: >-
              $.fields.organizationalInstanceRcd & ' - ' &
              $.fields.employeeGroupName   & ' - ' & $.fields.companyName
        - type: text
          name: organizationalInstanceRcd
          unvisible: true
        - type: text
          name: employeeGroupName
          unvisible: true
        - type: text
          name: companyName
          unvisible: true
    - type: group
      collapse: false
      label: Group Employment
      _n_cols:
        transform: '$.extend.formType = ''view'' or $.extend.formType = ''viewMore'' ? 1 : 2'
      fields:
        - type: dateRange
          name: groupOriginalStartDate
          mode: date-picker
          label: Group Original Start Date
          setting:
            format: dd/MM/yyyy
            type: date
          validators:
            - type: required
        - type: dateRange
          name: groupFirstStartDate
          mode: date-picker
          label: Group First Start Date
          setting:
            format: dd/MM/yyyy
            type: date
          disabled: true
        - type: dateRange
          name: groupLastStartDate
          mode: date-picker
          label: Group Last Start Date
          setting:
            format: dd/MM/yyyy
            type: date
          disabled: true
        - type: dateRange
          name: groupLastTerminate
          mode: date-picker
          label: Group Last Terminate
          setting:
            format: dd/MM/yyyy
            type: date
          disabled: true
    - type: group
      collapse: false
      label: OIR Employment
      _n_cols:
        transform: '($.extend.formType = ''view'' or $.extend.formType = ''viewMore'') ? 1 : 2'
      fields:
        - type: dateRange
          name: oirOriginalStartDate
          mode: date-picker
          label: OIR Original Start Date
          setting:
            format: dd/MM/yyyy
            type: date
          validators:
            - type: required
        - type: dateRange
          name: oirFirstStartDate
          mode: date-picker
          label: OIR First Start Date
          setting:
            format: dd/MM/yyyy
            type: date
          disabled: true
        - type: dateRange
          name: oirLastStartDate
          mode: date-picker
          label: OIR Last Start Date
          setting:
            format: dd/MM/yyyy
            type: date
          disabled: true
        - type: dateRange
          name: oirLastTerminate
          mode: date-picker
          label: OIR Last Terminate
          setting:
            format: dd/MM/yyyy
            type: date
          disabled: true
    - type: group
      collapse: false
      label: Seniority
      n_cols: 2
      _condition:
        transform: >-
          $not($.extend.formType = 'view') and $not($.extend.formType =
          'viewMore')
      fields:
        - type: text
          name: groupSeniority
          label: Group Seniority
          disabled: true
        - type: text
          name: organizationalInstanceSeniority
          label: Organizational Instance Seniority
          disabled: true
        - type: text
          name: externalExperience
          label: External Experience
          disabled: true
        - type: textarea
          label: Note
          name: note
          col: 2
          placeholder: Enter Note
          textarea:
            autoSize:
              minRows: 3
    - type: group
      collapse: false
      label: Seniority
      _condition:
        transform: $.extend.formType = 'view' or $.extend.formType = 'viewMore'
      fields:
        - type: text
          name: groupSeniority
          label: Group Seniority
          disabled: true
        - type: text
          name: organizationalInstanceSeniority
          label: Organizational Instance Seniority
          disabled: true
        - type: text
          name: externalExperience
          label: External Experience
          disabled: true
        - type: textarea
          label: Note
          name: note
          placeholder: Enter Note
          textarea:
            autoSize:
              minRows: 3
filter_config:
  fields:
    - type: group
      padding: 10px 20px 0 20px
      fields:
        - type: select
          label: Organizational Instance Rcd
          name: organizationalInstanceRcd
          outputValue: value
          labelType: type-row-readOnly
          placeholder: Select
          options:
            allowClear: false
          _select:
            transform: $.variables._jobDatasList
          _defaultValue:
            transform: >-
              $not($.extend.formType = 'filter') and $.variables._jobDatasList ?
              $.variables._jobDatasList[0].value
          _allowValues:
            transform: $map($.variables._jobDatasList, function($it){$it.value})[]
  filterMapping:
    - field: oir
      operator: $eq
      valueField: organizationalInstanceRcd
  sources:
    jobDatasList:
      uri: '"/api/personals/" & $.empId & "/employments/dropdown"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.organizationalInstanceRcd & ' -
        ' & $item.employeeGroupName   & ' - ' & $item.companyName , 'value':
        $item.organizationalInstanceRcd}})[]
      disabledCache: true
      params:
        - empId
        - effectiveDate
  variables:
    _jobDatasList:
      transform: >-
        $.extend.params.id1 ? $jobDatasList($.extend.params.id1,$CalDate($now(),
        1, 'd' ),$.extend.refresh)
layout_options:
  filterType: widget
  widget_options:
    empty:
      show_add_information: false
    show_more_content_type: widget-drawer
layout_options__header_buttons:
  - id: edit
    icon: icon-pencil-simple-bold
    title: Edit
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions: null
backend_url: /api/personals/:id1/employments
screen_name: null
layout_options__actions_many: null
parent: HR.FS.FR.000
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key: null
screen_designs: []
function_specs: []
fields: []
children: []
menu_item: null
