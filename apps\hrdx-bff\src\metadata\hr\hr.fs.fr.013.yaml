id: HR.FS.FR.013
status: draft
sort: 8
user_created: 78a455fe-2ca9-4621-b542-98e98ebfefe8
date_created: '2024-06-13T08:41:23.464Z'
user_updated: 2aa5db12-fa54-46d6-8e05-9e421858de0c
date_updated: '2025-07-25T08:21:17.072Z'
title: National ID Information
requirement:
  time: 1749091000122
  blocks:
    - id: gnLmF8Evuc
      type: paragraph
      data:
        text: Manage employee identification information&nbsp;
  version: 2.30.7
screen_design: null
module: HR
local_fields:
  - code: countryName
    title: Country
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: nationalIdTypeName
    title: National ID Type
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: nationalId
    title: National ID
    data_type:
      key: Number
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: startDate
    title: Issue Date
    data_type:
      key: dd/mm/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    pinned: false
  - code: endDate
    title: End Date
    data_type:
      key: dd/mm/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
  - code: issueByName
    title: Issue By
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: status
    title: Status
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: Boolean Tag
      collection: field_types
  - code: priority
    title: Priority
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: Boolean
      collection: field_types
  - code: note
    title: Note
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
mock_data: null
local_buttons: null
layout: layout-widget
form_config:
  formTitle:
    proceed: Add New National ID Info
    create: Add New National ID Info
    edit: Edit National ID Info
    view: National ID Info Details
  historyHeaderTitle: '''National ID Info Details'''
  fields:
    - type: group
      n_cols: 2
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - type: select
          name: countryCode
          label: Country
          placeholder: Select Country
          isLazyLoad: true
          _defaultValue:
            transform: >-
              $.extend.formType = 'create' and
              $isNilorEmpty($.extend.defaultValue.countryCode) ? ($info :=
              $nationDefault($now(),'VNM') ; $exists($info) ? $info)
          _select:
            transform: >-
              $nationsList($.extend.limit, $.extend.page, $.extend.search,
              $.fields.startDate)
          _validateFn:
            transform: >-
              $.extend.defaultValue.countryName ? {'label':
              $.extend.defaultValue.countryName, 'value':
              $.extend.defaultValue.countryCode}
            params:
              updateLabelExistOption: true
          outputValue: value
          validators:
            - type: required
          _condition:
            transform: $not($.extend.formType = 'view')
          clearFieldsAfterChange:
            - nationalIdType
        - type: select
          name: nationalIdType
          label: National ID Type
          placeholder: Select National ID Type
          _select:
            transform: >-
              $exists($.fields.countryCode) and
              $not($exists($.fields.countryCode.value)) ?
              $identitydocumenttypeslist($.fields.countryCode,
              $.fields.startDate) : null
          outputValue: value
          validators:
            - type: required
          _condition:
            transform: $not($.extend.formType = 'view')
        - type: text
          label: National ID
          name: nationalId
          placeholder: Enter National ID
          validators:
            - type: required
            - type: pattern
              args: ^[a-zA-Z0-9]*$
              text: The National ID must not contain special characters.
            - type: ppx-custom
              args:
                transform: >-
                  ($.fields.nationalIdType in ['VNM_IC', 'VNM_IC2', 'VNM_IC3'])
                  and $not($length($.fields.nationalId) = 9 or
                  $length($.fields.nationalId) = 12)
              text: National ID only allows 9 or 12 characters
            - type: ppx-custom
              args:
                transform: >-
                  ($.fields.nationalIdType in ['VNM_IC05', 'VNM_IC4', 'CIC'])
                  and $not($length($.fields.nationalId) = 12)
              text: National ID only allows 12 characters
        - type: dateRange
          label: Issue Date
          name: startDate
          mode: date-picker
          placeholder: dd/MM/yyyy
          _value:
            transform: $.extend.formType = 'create' ? $now()
          setting:
            format: dd/MM/yyyy
            type: date
          validators:
            - type: required
            - type: ppx-custom
              args:
                transform: >-
                  $exists($.fields.startDate) and
                  $DateDiff($DateFormat($.fields.startDate, 'yyyy-MM-DD'),
                  $DateFormat($now(), 'yyyy-MM-DD'), 'd') > 0
              text: Issue date must be less than now
          _class:
            transform: $.extend.formType = 'view' ? 'unrequired'
        - type: dateRange
          label: End Date
          name: endDate
          mode: date-picker
          placeholder: dd/MM/yyyy
          setting:
            format: dd/MM/yyyy
            type: date
          validators:
            - type: ppx-custom
              args:
                transform: >-
                  $exists($.fields.startDate) and
                  $DateDiff($DateFormat($.fields.startDate, 'yyyy-MM-DD'),
                  $DateFormat($.fields.endDate, 'yyyy-MM-DD'), 'd') > 0
              text: End date must be greater than start date
          _class:
            transform: $.extend.formType = 'view' ? 'unrequired'
        - type: select
          name: issueBy
          label: Issue By
          outputValue: value
          _select:
            transform: >-
              $exists($.fields.countryCode) and
              $not($exists($.fields.countryCode.value)) ?
              $ISSUEAUTHORITY($.fields.countryCode, $.fields.startDate)
          validators:
            - type: required
        - type: radio
          name: status
          label: Status
          placeholder: Select Status
          value: true
          radio:
            - label: Active
              value: true
            - label: Inactive
              value: false
          _class:
            transform: $.extend.formType = 'view' ? 'unrequired'
        - type: checkbox
          name: priority
          label: Priority
          customLabelCheckbox: Primary
          value: false
          _value:
            transform: >-
              $.extend.formType = 'create' and $exists($.fields.nationalIdType)
              ? ($not($count($.variables._historiesData) > 0) ? true : false)
        - type: textarea
          name: note
          label: Note
          placeholder: Enter Note
          col: 2
          textarea:
            autoSize:
              minRows: 3
            maxCharCount: 1000
          validators:
            - type: maxLength
              args: '1000'
              text: Maximum 1000 characters
    - type: group
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - type: text
          name: countryName
          label: Country
        - type: text
          name: nationalIdTypeName
          label: National ID Type
        - type: text
          label: National ID
          name: nationalId
          placeholder: Enter National ID
        - type: dateRange
          label: Issue Date
          name: startDate
          mode: date-picker
          placeholder: dd/MM/yyyy
          setting:
            format: dd/MM/yyyy
            type: date
        - type: dateRange
          label: End Date
          name: endDate
          mode: date-picker
          placeholder: dd/MM/yyyy
          setting:
            format: dd/MM/yyyy
            type: date
        - type: text
          name: issueByName
          label: Issue By
        - type: radio
          name: status
          label: Status
          outputValue: value
          placeholder: Select Status
          radio:
            - label: Active
              value: true
            - label: Inactive
              value: false
        - type: radio
          name: priority
          label: Priority
          radio:
            - label: 'Yes'
              value: true
              className: active
            - label: 'No'
              value: false
              className: inactive
        - type: textarea
          name: note
          label: Note
          placeholder: Note
          textarea:
            autoSize:
              minRows: 3
  historyTitle: >-
    $DateFormat($.startDate, 'DD/MM/YYYY') & ' - ' & $DateFormat($.endDate,
    'DD/MM/YYYY')
  historyDescription: >-
    $.nationalIdTypeName & ' - ' & $.nationalId & ' - ' & ($.priority ?
    'Priority' : 'Non Priority')
  footer:
    create: false
    update: true
    createdOn: createdOn
    updatedOn: updatedAt
    createdBy: createdBy
    updatedBy: updatedBy
  sources:
    identitydocumenttypeslist:
      uri: '"/api/picklists/IDENTIFICATION/values/"& $.countryCode & ""'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($filter($.data, function($item) {$item.status =
        true}),function($item) {{'label': $item.name.default,'value':
        $item.code}})[]
      disabledCache: true
      params:
        - countryCode
        - effectiveDate
    ISSUEAUTHORITY:
      uri: '"/api/picklists/ISSUEAUTHORITY/values/"& $.countryCode & ""'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data,function($item) {{'label': $item.name.default,'value':
        $item.code}})[]
      disabledCache: true
      params:
        - countryCode
        - effectiveDate
    nationsList:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}], 'limit': $.limit,'page':
        $.page,'search': $.search}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
    nationDefault:
      uri: '"/api/picklists/COUNTRIES/values"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'code','operator':
        '$eq','value':$.code}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[0]
      disabledCache: true
      params:
        - effectiveDate
        - code
    historiesData:
      uri: '"/api/personals/" & $.empId & "/personal-identity-documents/histories"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'nationalIdType','operator':
        '$eq','value':$.nationalIdType}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $[]
      disabledCache: true
      params:
        - empId
        - nationalIdType
  variables:
    _historiesData:
      transform: >-
        $.extend.formType = 'create' and $.extend.params.id1 ?
        $historiesData($.extend.params.id1)
filter_config:
  fields:
    - type: group
      padding: 12px 0 0 0
      fields:
        - type: select
          name: nationalIdType
          outputValue: value
          label: National ID Type
          labelType: flex-row
          placeholder: National ID Type
          _select:
            transform: $identitydocumenttypeslist($.extend.params.id1)
  filterMapping:
    - field: nationalIdType
      operator: $eq
      valueField: nationalIdType
  sources:
    identitydocumenttypeslist:
      uri: '"/api/personals/" & $.empId & "/personal-identity-documents/dropdown"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.nationalIdTypeName, 'value':
        $item.nationalIdType}})[]
      disabledCache: true
      params:
        - empId
layout_options:
  widget_header_buttons:
    - id: create
      title: create
      icon: plus
    - id: history
      title: history
      icon: clock-rotate-left
  show_dialog_form_save_add_button: true
  is_upload_file: true
  filter_history_method: manual
layout_options__header_buttons:
  - id: create
    title: create
    icon: icon-plus-bold
  - id: history
    title: history
    icon: icon-clock-counter-clockwise-bold
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions: null
backend_url: api/personals/:id1/personal-identity-documents
screen_name: null
layout_options__actions_many: null
parent: HR.FS.FR.000
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key: null
screen_designs: []
function_specs: []
fields: []
children: []
menu_item: null
