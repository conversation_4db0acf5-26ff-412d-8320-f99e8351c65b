id: HR.FS.FR.016
status: draft
sort: 26
user_created: 56e71b91-dd39-4ebc-afd1-b4f00f38d4a5
date_created: '2024-09-27T15:26:16.446Z'
user_updated: 2aa5db12-fa54-46d6-8e05-9e421858de0c
date_updated: '2025-07-25T04:04:32.337Z'
title: Phone Information
requirement:
  time: 1745308524554
  blocks:
    - id: cbN8NzErLK
      type: paragraph
      data:
        text: >-
          Chứ<PERSON>  năng cho phép  bộ phận  nhân sự Tập đoàn / CTTV  xem thông tin 
          liên  hê  của  CBNV  đã  được tạo trên hệ thống&nbsp;
  version: 2.30.7
screen_design: null
module: HR
local_fields:
  - code: startDate
    pinned: true
    title: Effective Date
    data_type:
      key: YYYY/MM/DD
      collection: data_types
    display_type:
      key: DD/MM/YYYY
      collection: field_types
  - code: groupTypeStar
    title: Phone Type
    data_type:
      key: StringWithStar
      collection: data_types
    display_type: null
  - code: phoneNumber
    pinned: true
    title: Phone Number
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: extensionNumber
    pinned: true
    title: Extension Number
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: callingTimeName
    pinned: true
    title: Time To Call
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: phoneTypeName
    pinned: true
    title: ' '
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    group: groupTypeStar
  - code: isPrimary
    title: ' '
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: Star
      collection: field_types
    group: groupTypeStar
  - code: status
    title: Status
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: Boolean Tag
      collection: field_types
mock_data:
  - effectiveAsOf: 2024/06/13
    phoneType: Business
    phoneNumber: '0987132784'
    extensionNumber: '0987132784'
    timeToCall: Any
    isPrimary: true
    table:
      - effectiveAsOf: 2024/06/13
        phoneType: Business
        phoneNumber: '0987132784'
        extensionNumber: '0987132784'
        timeToCall: Any
        isPrimary: true
        test: Detail
      - effectiveAsOf: 2024/06/14
        phoneType: Personal
        phoneNumber: '0987132785'
        extensionNumber: '0987132785'
        timeToCall: Weekend
        isPrimary: false
        test: Detail
      - effectiveAsOf: 2024/06/15
        phoneType: Personal
        phoneNumber: '0987132786'
        extensionNumber: '0987132786'
        timeToCall: Any
        isPrimary: false
        test: Detail
      - effectiveAsOf: 2024/06/17
        phoneType: Business
        phoneNumber: '0987132787'
        extensionNumber: '0987132787'
        timeToCall: Weekend
        isPrimary: false
        test: Detail
      - effectiveAsOf: 2024/06/17
        phoneType: Business
        phoneNumber: '0987132787'
        extensionNumber: '0987132787'
        timeToCall: Weekend
        isPrimary: false
        test: Detail
      - effectiveAsOf: 2024/06/17
        phoneType: Business
        phoneNumber: '0987132787'
        extensionNumber: '0987132787'
        timeToCall: Weekend
        isPrimary: false
local_buttons: null
layout: layout-widget
form_config:
  formSize:
    create: largex
    edit: largex
  formTitle:
    proceed: Add New Phone Information
    create: Add New Phone Information
    edit: Edit Phone Information
    view: Phone Information
  fields:
    - type: array
      mode: table
      name: commands
      _size:
        transform: $.extend.formType = 'create' ? 1
      _minSize:
        transform: $.extend.formType = 'create' ? 1
      checkEmptyValue: true
      arrayOptions:
        canChangeSize: true
        showConfirmDelete: false
        uniqueField: isPrimary
        _canAddItem:
          transform: $.extend.permission.isCreate
        _canDeleteItem:
          transform: $.extend.formType = 'create' or $.extend.permission.isDelete
      field:
        type: group
        name: group
        fields:
          - type: text
            name: id
            unvisible: true
          - type: dateRange
            label: Effective Date
            name: startDate
            mode: date-picker
            _disabled:
              transform: >-
                $not($.extend.formType = 'create') and
                $not($.extend.permission.isEdit = true) and
                $exists($getFieldGroup($.extend.path, $.fields, 1).id)
            validators:
              - type: required
            _value:
              transform: $.extend.formType = 'create' ? $now()
            width: 200px
          - type: select
            label: Phone Type
            name: phoneTypeCode
            placeholder: Select Phone Type
            outputValue: value
            _disabled:
              transform: >-
                $not($.extend.formType = 'create') and
                $not($.extend.permission.isEdit = true) and
                $exists($getFieldGroup($.extend.path, $.fields, 1).id)
            _select:
              transform: $.variables._phoneTypesList
            _value:
              transform: $.extend.formType = 'create' ? 'CELL'
            validators:
              - type: required
              - type: maxLength
                args: '80'
                text: Phone Type should not exceed 80 characters
            _condition:
              transform: $not($.extend.formType = 'view')
            width: 200px
          - type: text
            label: Phone Type
            name: phoneTypeName
            placeholder: Select Phone Type
            _condition:
              transform: $.extend.formType = 'view'
            width: 200px
          - type: text
            label: Phone Number
            placeholder: Enter Phone Number
            name: phoneNumber
            _disabled:
              transform: >-
                $not($.extend.formType = 'create') and
                $not($.extend.permission.isEdit = true) and
                $exists($getFieldGroup($.extend.path, $.fields, 1).id)
            validators:
              - type: required
              - type: maxLength
                args: '20'
                text: Phone Number should not exceed 20 characters
            width: 200px
          - type: text
            label: Extension
            placeholder: Enter Extension
            name: extensionNumber
            _disabled:
              transform: >-
                $not($.extend.formType = 'create') and
                $not($.extend.permission.isEdit = true) and
                $exists($getFieldGroup($.extend.path, $.fields, 1).id)
            validators:
              - type: maxLength
                args: '20'
                text: Extension Number should not exceed 20 characters
            width: 200px
          - type: select
            label: Time to Call
            name: callingTimeCode
            placeholder: Select Time to Call
            outputValue: value
            _disabled:
              transform: >-
                $not($.extend.formType = 'create') and
                $not($.extend.permission.isEdit = true) and
                $exists($getFieldGroup($.extend.path, $.fields, 1).id)
            _select:
              transform: $.variables._callingTimeList
            validators:
              - type: maxLength
                args: '80'
                text: Time To Call should not exceed 80 characters
            _condition:
              transform: $not($.extend.formType = 'view')
            width: 200px
          - type: text
            label: Time to Call
            name: callingTimeName
            placeholder: Select Time to Call
            _condition:
              transform: $.extend.formType = 'view'
            width: 200px
          - type: radioTable
            label: Primary
            name: isPrimary
            _disabled:
              transform: >-
                $not($.extend.formType = 'create') and
                $not($.extend.permission.isEdit = true) and
                $exists($getFieldGroup($.extend.path, $.fields, 1).id)
            value: false
            width: 150px
          - type: select
            label: Status
            name: status
            outputValue: value
            placeholder: Select Status
            _disabled:
              transform: >-
                $not($.extend.formType = 'create') and
                $not($.extend.permission.isEdit = true) and
                $exists($getFieldGroup($.extend.path, $.fields, 1).id)
            value: true
            validators:
              - type: required
            width: 200px
            _condition:
              transform: $not($.extend.formType = 'view')
            select:
              - label: Active
                value: true
              - label: Inactive
                value: false
          - type: radio
            label: Status
            name: status
            _condition:
              transform: $.extend.formType = 'view'
            radio:
              - label: Active
                value: true
              - label: Inactive
                value: false
            width: 170px
  historyTitle: $DateFormat($.startDate, 'DD/MM/YYYY')
  historyDescription: '''Active'''
  sources:
    phoneTypesList:
      uri: '"/api/picklists-values/PHONETYPE"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
    callingTimeList:
      uri: '"/api/picklists/TimeToCall/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
  variables:
    _phoneTypesList:
      transform: $phoneTypesList()
    _callingTimeList:
      transform: $callingTimeList()
filter_config:
  fields:
    - type: group
      padding: 20px 0px 0px
      n_cols: 3
      fields:
        - type: select
          label: Phone Type
          name: phoneTypeCode
          labelType: type-grid
          placeholder: Select Phone Type
          outputValue: value
          col: 2
          _select:
            transform: $.extend.params.id1 ? $phoneTypesList($.extend.params.id1)
  filterMapping:
    - field: phoneTypeCode
      operator: $eq
      valueField: phoneTypeCode
  sources:
    phoneTypesList:
      uri: '"/api/personals/" & $.empId & "/phone-contacts/histories"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $distinct($map($, function($item) {{'label': $item.phoneTypeName,
        'value': $item.phoneTypeCode}})[])[]
      disabledCache: true
      params:
        - empId
layout_options:
  widget_options:
    show_more_type: hidden
    custom_data_transform: >-
      ($isPrimaryItem := $filter($,function($item){$item.isPrimary = true})[0];
      $lastEffDateItem := $reduce($, function($a, $b){$toMillis($a.startDate) >
      $toMillis($b.startDate) ? $a : $b}); $isPrimaryItem ? $isPrimaryItem :
      $lastEffDateItem )
  filterType: history
  show_dialog_form_save_add_button: false
  widget_history_type: table
  is_update_array: true
  group_data_history_by_key: commands
  filter_history_method: manual
  is_new_dynamic_form: false
layout_options__header_buttons:
  - id: create
    title: create
    icon: icon-plus-bold
  - id: history
    title: history
    icon: icon-clock-counter-clockwise-bold
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions: null
backend_url: api/personals/:id1/phone-contacts
screen_name: null
layout_options__actions_many: null
parent: HR.FS.FR.000
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key: null
screen_designs: []
function_specs: []
fields: []
children: []
menu_item: null
