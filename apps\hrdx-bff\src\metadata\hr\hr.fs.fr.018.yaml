id: HR.FS.FR.018
status: draft
sort: 15
user_created: 2aa5db12-fa54-46d6-8e05-9e421858de0c
date_created: '2024-08-08T02:50:14.066Z'
user_updated: 2aa5db12-fa54-46d6-8e05-9e421858de0c
date_updated: '2025-07-07T09:27:54.351Z'
title: Bank Details
requirement:
  time: *************
  blocks:
    - id: Z-nnjdsCBq
      type: paragraph
      data:
        text: "- Chứ<PERSON> năng cho phép bộ phận nhân sự Tập đoàn/<PERSON><PERSON> thêm mới, c<PERSON><PERSON> nh<PERSON><PERSON>, theo dõi thông tin ngân hàng của CBNV. \t\t\t\t\t"
    - id: _HekXpCN6x
      type: paragraph
      data:
        text: "- Chức năng cho phép thêm mới/cập nhật/xóa bản ghi\t\t\t\t\t"
  version: 2.29.1
screen_design: null
module: HR
local_fields:
  - code: effectiveDate
    pinned: false
    title: Effective Date
    description: Từ ngày
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
  - code: status
    pinned: false
    title: Status
    description: null
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: Boolean Tag
      collection: field_types
  - code: isMainBank
    title: Main Bank
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: Tag
      collection: field_types
    extra_config:
      tags:
        - label: 'Yes'
          value: true
          class: success
        - label: 'No'
          value: false
          class: default
  - code: bankName
    pinned: false
    title: Bank Name
    description: null
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: bankCountry
    title: Bank Country
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: bankAccount
    title: Bank Account
    data_type:
      key: Nmberic
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: payee
    title: Payee
    data_type:
      key: User
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: paymentCurrency
    title: Payment Currency
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Currency
      collection: field_types
  - code: note
    title: Note
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
mock_data:
  - effectiveDate: 01/01/2023
    status: true
    isMainBank: true
    bankName: Joint Stock Commercial Bank for Foreign Trade of Vietnam
    bankCountry: Vietnam
    bankAccount: '*********'
    payee: Hoang Khanh Linh
    paymentCurrency: VND
    paymentType: Transfer
    note: This is a sample note.
local_buttons: null
layout: layout-widget
form_config:
  _formTitle:
    create: '''Add New Bank Detail'''
  fields:
    - type: group
      n_cols: 2
      fields:
        - type: dateRange
          name: effectiveDate
          label: Effective Date
          mode: date-picker
          placeholder: dd/MM/yyyy
          validators:
            - type: required
          _value:
            transform: >-
              $.variables._checkFirst.isFirst ?
              $DateFormat($.variables._checkFirst.startDate, 'yyyy-MM-DD')
        - type: radio
          label: Status
          name: status
          placeholder: Choose status
          validators:
            - type: required
          value: true
          radio:
            - label: Active
              value: true
            - label: Inactive
              value: false
        - type: checkbox
          label: Main Bank
          name: isMainBank
          value: true
          outputValue: value
          hiddenLabel: true
      _condition:
        transform: $not($.extend.formType = 'view')
    - type: group
      n_cols: 2
      fields:
        - type: select
          name: bankCode
          label: Bank Name
          placeholder: Choose Bank Name
          validators:
            - type: required
          outputValue: value
          isLazyLoad: true
          _select:
            transform: $bankList($.extend.limit, $.extend.page, $.extend.search)
          _validateFn:
            transform: >-
              $.extend.defaultValue ? {'label': $.extend.defaultValue.bankName,
              'value': $.extend.defaultValue.bankCode}
            params:
              updateLabelExistOption: true
        - type: select
          name: countryCode
          label: Bank Country
          placeholder: Choose Bank Country
          validators:
            - type: required
          outputValue: value
          isLazyLoad: true
          _select:
            transform: $countryList($.extend.limit, $.extend.page, $.extend.search)
          _validateFn:
            transform: >-
              $.extend.defaultValue ? {'label':
              $.extend.defaultValue.bankCountry, 'value':
              $.extend.defaultValue.countryCode}
            params:
              updateLabelExistOption: true
          _value:
            transform: >-
              $.extend.formType = 'create' and $count($filterCountry('VNM')) > 0
              ? {'label': $filterCountry('VNM')[0].name.default, 'value': 'VNM'}
        - type: text
          name: bankAccount
          label: Bank Account
          placeholder: Enter Bank Account
          validators:
            - type: required
            - type: pattern
              args: ^[a-zA-Z0-9]*$
              text: >-
                The format is not correct.Please enter the account number
                excluding special characters such as !, @, #, $, %, ^, &, *, (),
                _, +, -, =, /,?, >, <,~, `, ”, :, ;
        - type: text
          name: payee
          label: Payee
          placeholder: Enter payee
          _value:
            transform: $.extend.formType = 'create' ? $.variables.fullName
        - type: select
          name: currencyCode
          label: Payment Currency
          placeholder: Choose Payment Currency
          col: 2
          validators:
            - type: required
          isLazyLoad: true
          _select:
            transform: $currencyList($.extend.limit, $.extend.page, $.extend.search)
          _validateFn:
            transform: >-
              $.extend.defaultValue ? {'label':
              $.extend.defaultValue.currencyCode, 'value':
              $.extend.defaultValue.currencyCode}
            params:
              updateLabelExistOption: true
          _value:
            transform: >-
              ($.extend.formType = 'create' and $.fields.countryCode = 'VNM') ?
              {'label': 'VND', 'value': 'VND'}
          outputValue: value
      _condition:
        transform: $not($.extend.formType = 'view')
    - type: dateRange
      name: effectiveDate
      label: Effective Date
      mode: date-picker
      placeholder: dd/MM/yyyy
      validators:
        - type: required
      _condition:
        transform: $.extend.formType = 'view'
      _value:
        transform: >-
          $.variables._checkFirst.isFirst ?
          $DateFormat($.variables._checkFirst.startDate, 'yyyy-MM-DD')
      _class:
        transform: '''unrequired'''
    - type: radio
      label: Status
      name: statusName
      radio:
        - label: Active
          value: Active
        - label: Inactive
          value: Inactive
      _condition:
        transform: $.extend.formType = 'view'
    - type: radio
      label: Main Bank
      name: isMainBank
      radio:
        - label: 'Yes'
          value: true
          className: active
        - label: 'No'
          value: false
          className: inactive
      _condition:
        transform: $.extend.formType = 'view'
    - type: text
      name: bankName
      label: Bank Name
      _condition:
        transform: $.extend.formType = 'view'
    - type: text
      name: bankCountry
      label: Bank Country
      _condition:
        transform: $.extend.formType = 'view'
    - type: text
      name: bankAccount
      label: Bank Account
      placeholder: Enter bank account
      _condition:
        transform: $.extend.formType = 'view'
    - type: text
      name: payee
      label: Payee
      placeholder: Enter payee
      _condition:
        transform: $.extend.formType = 'view'
    - type: text
      name: currencyCode
      label: Payment Currency
      _condition:
        transform: $.extend.formType = 'view'
    - type: textarea
      label: Note
      name: note
      placeholder: Enter Note
      textarea:
        autoSize:
          minRows: 3
        maxCharCount: 1000
      validators:
        - type: maxLength
          args: '1000'
          text: Maximum 1000 characters
  sources:
    checkFirst:
      uri: '"/api/personals/" & $.empId & "/bank-account-infos/check-first-record"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $
      disabledCache: true
      params:
        - empId
    getBasicInformation:
      uri: '"/api/personals/" & $.empId & "/basic-infomation"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $
      disabledCache: true
      params:
        - empId
    bankList:
      uri: '"/api/picklists/BANKNAME/values/pagination"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & '(' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    countryList:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    filterCountry:
      uri: '"/api/picklists/COUNTRIES/values"'
      method: GET
      queryTransform: '{''filter'': [{''field'':''code'',''operator'': ''$eq'',''value'':$.country}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $.data
      disabledCache: true
      params:
        - country
    currencyList:
      uri: '"/api/picklists/CURRENCY/values/pagination"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.code, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    paymentTypeList:
      uri: '"/api/picklists/PAYMENTTYPE/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
  variables:
    _checkFirst:
      transform: $.extend.params.id1 ? $checkFirst($.extend.params.id1)
    fullName:
      transform: $.extend.params.id1 ? $getBasicInformation($.extend.params.id1).fullName
  footer:
    create: false
    update: true
    createdOn: createdAt
    updatedOn: updatedAt
    createdBy: createdBy
    updatedBy: updatedBy
  historyTitle: $DateFormat($.effectiveDate, 'DD/MM/YYYY')
  historyDescription: $.bankName & ' - ' & $.bankAccount
filter_config: {}
layout_options:
  widget_header_buttons:
    - id: create
      title: create
      icon: plus
    - id: history
      title: history
      icon: clock-rotate-left
  show_dialog_form_save_add_button: true
layout_options__header_buttons:
  - id: create
    title: create
    icon: icon-plus-bold
  - id: history
    title: history
    icon: icon-clock-counter-clockwise-bold
options: null
create_form: null
layout_options__footer_buttons: null
layout_options__row_actions: null
backend_url: /api/personals/:id1/bank-account-infos
screen_name: null
layout_options__actions_many: null
parent: HR.FS.FR.000
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key: null
screen_designs: []
function_specs: []
fields: []
children: []
menu_item: null
