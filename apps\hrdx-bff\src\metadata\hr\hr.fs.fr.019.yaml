id: HR.FS.FR.019
status: draft
sort: 17
user_created: 56e71b91-dd39-4ebc-afd1-b4f00f38d4a5
date_created: '2024-10-08T07:27:46.714Z'
user_updated: 2aa5db12-fa54-46d6-8e05-9e421858de0c
date_updated: '2025-06-09T06:54:32.291Z'
title: Other Special Information
requirement:
  time: 1749452052457
  blocks:
    - id: JlBdGzxbLE
      type: paragraph
      data:
        text: Quản lý thông tin đặc biệt của CBNV&nbsp;
  version: 2.30.7
screen_design: null
module: HR
local_fields:
  - code: familyBackgroundComposition
    pinned: true
    title: Family Background
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    extra_config:
      mediumSize: true
  - code: beneficiaryOfSocialWelfare
    pinned: false
    title: Beneficiary Of Social Welfare
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    extra_config:
      hasBorderBottom: true
      mediumSize: true
  - code: startDate
    pinned: false
    title: Start Date
    data_type:
      key: YYYY/MM/DD
      collection: data_types
    display_type:
      key: DD/MM/YYYY
      collection: field_types
    extra_config:
      mediumSize: true
  - code: endDate
    pinned: false
    title: End Date
    data_type:
      key: YYYY/MM/DD
      collection: data_types
    display_type:
      key: DD/MM/YYYY
      collection: field_types
    extra_config:
      mediumSize: true
  - code: allowances
    pinned: false
    title: Allowances
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    extra_config:
      mediumSize: true
  - code: costWithPaymentCurrency
    pinned: false
    title: Cost
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Currency
      collection: field_types
    extra_config:
      mediumSize: true
  - code: note
    pinned: true
    title: Note
    data_type:
      key: Note
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    extra_config:
      mediumSize: true
mock_data: null
local_buttons: null
layout: layout-widget
form_config:
  formTitle:
    create: Add New Other Special Information
  fields:
    - type: group
      n_cols: 2
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - type: select
          name: familyBackgroundCode
          label: Family Background
          placeholder: Select Family Background
          outputValue: value
          _value:
            transform: >-
              $.extend.formType = 'create' and
              $.variables._getOtherSpecialByMaxCreatedDate.familyBackgroundCode
              ?
              $.variables._getOtherSpecialByMaxCreatedDate.familyBackgroundCode
          _select:
            transform: $familyBackgroundList(100)
        - type: select
          name: socialWelfareBeneficiaryCode
          label: Beneficiary Of Social Welfare
          placeholder: Select Beneficiary Of Social Welfare
          outputValue: value
          _value:
            transform: >-
              $.extend.formType = 'create' and
              $.variables._getOtherSpecialByMaxCreatedDate.socialWelfareBeneficiaryCode
              ?
              $.variables._getOtherSpecialByMaxCreatedDate.socialWelfareBeneficiaryCode
          _select:
            transform: $socialWelfareBeneficiaryList(100)
        - type: dateRange
          name: startDate
          label: Start Date
          mode: date-picker
          placeholder: dd/MM/yyyy
        - type: dateRange
          name: endDate
          label: End Date
          mode: date-picker
          placeholder: dd/MM/yyyy
          validators:
            - type: ppx-custom
              args:
                transform: >-
                  $exists($.fields.startDate) and
                  $DateDiff($DateFormat($.fields.startDate, 'yyyy-MM-DD'),
                  $DateFormat($.fields.endDate, 'yyyy-MM-DD'), 'd') > 0
              text: End Date must be greater than or equal Start Date
        - type: select
          name: allowanceCode
          label: Allowances
          placeholder: Select Allowances
          outputValue: value
          isLazyLoad: true
          _select:
            transform: $allowanceList($.extend.limit, $.extend.page, $.extend.search)
          _validateFn:
            transform: >-
              $.extend.defaultValue ? {'label':
              $.extend.defaultValue.allowances, 'value':
              $.extend.defaultValue.allowanceCode}
            params:
              updateLabelExistOption: true
        - type: number
          name: cost
          label: Cost
          placeholder: Enter Cost
          validators:
            - type: ppx-custom
              args:
                transform: $length($string($.fields.cost)) > 15
              text: Maximum length is 15 characters
          number:
            precision: 2
            format: currency
          addOnAfter:
            type: select
            name: currencyCode
            outputValue: value
            placeholder: Choose Currency
            width: 100px
            isLazyLoad: true
            _select:
              transform: $currencyList($.extend.limit, $.extend.page, $.extend.search)
            _validateFn:
              transform: >-
                $.extend.defaultValue ? {'label':
                $.extend.defaultValue.paymentCurrency, 'value':
                $.extend.defaultValue.currencyCode}
              params:
                updateLabelExistOption: true
            _value:
              transform: $.extend.formType = 'create' ? 'VND'
    - type: group
      n_cols: 1
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - type: text
          name: familyBackgroundComposition
          label: Family Background
        - type: text
          name: beneficiaryOfSocialWelfare
          label: Beneficiary Of Social Welfare
        - type: dateRange
          name: startDate
          label: Start Date
          mode: date-picker
        - type: dateRange
          name: endDate
          label: End Date
          mode: date-picker
        - type: text
          name: allowances
          label: Allowances
        - type: text
          name: costWithPaymentCurrency
          label: Cost
    - type: textarea
      name: note
      label: Note
      placeholder: Enter note
      textarea:
        autoSize:
          minRows: 3
        maxCharCount: 1000
      validators:
        - type: maxLength
          args: 1000
          text: Note should not exceed 1000 characters
  sources:
    otherSpecialHistory:
      uri: '"/api/personals/" & $.empId & "/other-special-infos/histories"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $
      disabledCache: true
      params:
        - empId
    familyBackgroundList:
      uri: '"/api/picklists/FAMILYBACKGROUND/values/pagination"'
      method: GET
      queryTransform: '{''limit'': $.limit}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $append([], $map($.data, function($item) {{'label': $item.name.default,
        'value': $item.code}}))[]
      disabledCache: true
      params:
        - limit
    socialWelfareBeneficiaryList:
      uri: '"/api/picklists/SOCIALWELFARE/values/pagination"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'filter': [{'field': 'status', 'operator': '$eq',
        'value': true }]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $append([], $map($.data, function($item) {{'label': $item.name.default,
        'value': $item.code}}))[]
      disabledCache: true
      params:
        - limit
    allowanceList:
      uri: '"/api/picklists/ALLOWANCESFPT/values/pagination"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'search': $.search, 'filter':
        [{'field': 'status', 'operator': '$eq', 'value': true }]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $append([], $map($.data, function($item) {{'label': $item.name.default,
        'value': $item.code}}))[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    currencyList:
      uri: '"/api/picklists/CURRENCY/values/pagination"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'search': $.search, 'filter':
        [{'field': 'status', 'operator': '$eq', 'value': true }]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $append([], $map($.data, function($item) {{'label': $item.code, 'value':
        $item.code}}))[]
      disabledCache: true
      params:
        - limit
        - page
        - search
  variables:
    _otherSpecialHistory:
      transform: $.extend.params.id1 ? $otherSpecialHistory($.extend.params.id1)
    _getOtherSpecialByMaxCreatedDate:
      transform: >-
        $count($.variables._otherSpecialHistory) > 0 ?
        $filter($.variables._otherSpecialHistory, function($item) {
        $toMillis($item.createdAt) = $max($map($.variables._otherSpecialHistory,
        function($i) { $toMillis($i.createdAt) }))}) : null
  buttons:
    - label: Cancel
      action: cancel
    - label: Save and Add new
      action: saveAndAddNew
    - label: Save
      action: save
  historyTitle: >-
    $DateFormat($.startDate, 'DD/MM/YYYY') & ' - ' & $DateFormat($.endDate,
    'DD/MM/YYYY')
  historyDescription: $.allowances & ' - ' & $.costWithPaymentCurrency
filter_config: {}
layout_options:
  widget_header_buttons:
    - id: create
      title: create
      icon: plus
    - id: history
      title: history
      icon: clock-rotate-left
  show_dialog_form_save_add_button: true
  is_copy_data_insert_new: false
layout_options__header_buttons:
  - id: create
    title: create
    icon: icon-plus-bold
  - id: history
    title: history
    icon: icon-clock-counter-clockwise-bold
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions: null
backend_url: /api/personals/:id1/other-special-infos
screen_name: null
layout_options__actions_many: null
parent: HR.FS.FR.000
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key: null
screen_designs: []
function_specs: []
fields: []
children: []
menu_item: null
