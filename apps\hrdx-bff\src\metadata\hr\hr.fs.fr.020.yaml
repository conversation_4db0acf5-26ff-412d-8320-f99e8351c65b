id: HR.FS.FR.020
status: draft
sort: 23
user_created: c712040f-6a4d-4c9b-86bb-1edd932ec5e0
date_created: '2024-09-09T09:32:57.681Z'
user_updated: 2aa5db12-fa54-46d6-8e05-9e421858de0c
date_updated: '2025-07-07T09:31:38.303Z'
title: Education Info
requirement:
  time: 1747995852708
  blocks:
    - id: VbUs-3SU5V
      type: paragraph
      data:
        text: >-
          - Chứ<PERSON> năng cho phép bộ phận nhân sự Tập đoàn/CTTV xem/cập nhật/thêm
          mới thông tin trình độ học vấn của nhân viên lên hệ thống.
    - id: eSYakToyu1
      type: paragraph
      data:
        text: >-
          - Chức năng cho phép thêm mới nhiều bản ghi Trình độ học vấn cho một
          cán bộ, không check trùng ngày hiệu lực
    - id: 2vQ3AKxdZm
      type: paragraph
      data:
        text: '- <PERSON> phép xóa bản ghi thừa/sai'
    - id: 40WcsJb6tw
      type: paragraph
      data:
        text: '- Trình độ học vấn cao nhất và hiển thị lên đầu trên màn hình chính'
  version: 2.30.7
screen_design: null
module: HR
local_fields:
  - code: startDate
    title: Start date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
  - code: endDate
    title: End date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
  - code: issueDate
    title: Issue date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
  - code: educationName
    title: Educational est.
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: countryName
    title: Country
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: cityName
    title: City/Province
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: instituteName
    title: School
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: educationTrainingName
    title: Major
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: branchStudyName1
    title: Branch of study 1
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: educationalMethodName
    title: Educational Method
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: languageName
    title: Languages
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: degreeName
    title: Degree
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: levelName
    title: Level
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: GPA
    title: GPA
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: note
    title: Note
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: fileName
    title: Attachment
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
mock_data: null
local_buttons: null
layout: layout-widget
form_config:
  formTitle:
    create: Add New Education Info
  fields:
    - type: group
      n_cols: 2
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - type: dateRange
          name: startDate
          label: Start Date
          mode: date-picker
          setting:
            format: dd/MM/yyyy
        - type: dateRange
          name: endDate
          label: End Date
          mode: date-picker
          setting:
            format: dd/MM/yyyy
          validators:
            - type: ppx-custom
              args:
                transform: >-
                  $not($isNilorEmpty($.fields.startDate)) and
                  $not($isNilorEmpty($.fields.endDate)) ?
                  $DateDiff($DateFormat($.fields.startDate, 'yyyy-MM-DD'),
                  $DateFormat($.fields.endDate, 'yyyy-MM-DD'), 'd') > 0
              text: End Date must be after Start Date
        - type: dateRange
          label: Issue Date
          name: issueDate
          mode: date-picker
          setting:
            format: dd/MM/yyyy
          validators:
            - type: ppx-custom
              id: validateWithEndDate
              args:
                transform: >-
                  $not($isNilorEmpty($.fields.startDate)) and
                  $not($isNilorEmpty($.fields.endDate)) and
                  $not($isNilorEmpty($.fields.issueDate)) ?
                  $DateDiff($DateFormat($.fields.endDate, 'yyyy-MM-DD'),
                  $DateFormat($.fields.issueDate, 'yyyy-MM-DD'), 'd') > 0
              text: Issue Date must be after End Date
            - type: ppx-custom
              id: validateWithStartDate
              args:
                transform: >-
                  $not($isNilorEmpty($.fields.startDate)) and
                  $isNilorEmpty($.fields.endDate) and
                  $not($isNilorEmpty($.fields.issueDate)) ?
                  $DateDiff($DateFormat($.fields.startDate, 'yyyy-MM-DD'),
                  $DateFormat($.fields.issueDate, 'yyyy-MM-DD'), 'd') > 0
              text: Issue Date must be after Start Date
        - type: select
          label: Educational Est.
          name: education
          placeholder: Select Educational Est.
          outputValue: value
          isLazyLoad: true
          _select:
            transform: $educationEstList($.extend.limit, $.extend.page, $.extend.search)
          _validateFn:
            transform: >-
              $.extend.defaultValue.educationName ? {'label':
              $.extend.defaultValue.educationName, 'value':
              $.extend.defaultValue.education}
            params:
              updateLabelExistOption: true
          validators:
            - type: required
          clearFieldsAfterChange:
            - education
            - degree
        - type: select
          label: Country
          name: country
          placeholder: Select Country
          outputValue: value
          isLazyLoad: true
          clearFieldsAfterChange:
            - city
            - institute
          _select:
            transform: $countryList($.extend.limit, $.extend.page, $.extend.search)
          _value:
            transform: >-
              $exists($.variables._selectedCountry) ? {'label':
              $.variables._selectedCountry[0].label, 'value':
              $.variables._selectedCountry[0].value} : $.extend.formType =
              'create' and $count($filterCountry('VNM')) > 0 ? {'label':
              $filterCountry('VNM').label, 'value': 'VNM'}
          _validateFn:
            transform: >-
              $.extend.defaultValue.countryName ? {'label':
              $.extend.defaultValue.countryName, 'value':
              $.extend.defaultValue.country}
            params:
              updateLabelExistOption: true
          validators:
            - type: required
        - type: select
          label: City/Province
          name: city
          placeholder: Select City/Province
          outputValue: value
          isLazyLoad: true
          clearFieldsAfterChange:
            - institute
          _validateFn:
            transform: >-
              $.extend.defaultValue.cityName ? {'label':
              $.extend.defaultValue.cityName, 'value':
              $.extend.defaultValue.city}
            params:
              updateLabelExistOption: true
          _select:
            transform: >-
              $provinceList($.extend.limit, $.extend.page, $.extend.search,
              $.fields.country)
          _value:
            transform: >-
              $exists($.variables._selectedSchool) ? {'label':
              $.variables._selectedProvince[0].name.default, 'value':
              $.variables._selectedProvince[0].code} : null
        - type: select
          label: School
          name: institute
          placeholder: Select School
          outputValue: value
          isLazyLoad: true
          _select:
            transform: >-
              ($cityInput:=$.fields.city;$countryInput:=$.fields.country;$countCity:=$.variables._countMultipleProvince;$provinceListCode:=$.variables._provinceListCode;$search:=$.extend.search;$page:=$.extend.page;$limit:=$.extend.limit;$not($isNilorEmpty($cityInput))
              ? $instituteList($.extend.limit, $.extend.page, $.extend.search,
              $cityInput) : ($not($isNilorEmpty($countryInput)) and $countCity >
              0) ? $instituteList($limit, $page, $search, $provinceListCode) :
              ($isNilorEmpty($countryInput) and $countCity > 0) ?
              $instituteList($limit, $page, $search) :
              ($not($isNilorEmpty($countryInput)) and $countCity > 0 and
              $isNilorEmpty($cityInput)) ? $instituteList($limit, $page,
              $search, $provinceListCode) : ($not($isNilorEmpty($countryInput))
              and $countCity = 0 and $isNilorEmpty($cityInput)) ? [] :
              $instituteList($limit, $page, $search))
          _validateFn:
            transform: >-
              $.extend.defaultValue.instituteName ? {'label':
              $.extend.defaultValue.instituteName, 'value':
              $.extend.defaultValue.institute}
            params:
              updateLabelExistOption: true
          validators:
            - type: required
          _condition:
            transform: $not($.extend.formType = 'view')
        - type: select
          label: Major
          name: category
          placeholder: Select Major
          outputValue: value
          isLazyLoad: true
          _select:
            transform: $majorList($.extend.limit, $.extend.page, $.extend.search)
          validators:
            - type: required
          _validateFn:
            transform: >-
              $.extend.defaultValue.educationTrainingName ? {'label':
              $.extend.defaultValue.educationTrainingName, 'value':
              $.extend.defaultValue.category}
            params:
              updateLabelExistOption: true
          clearFieldsAfterChange:
            - branchStudyCode1
        - type: select
          label: Branch of Study 1
          name: branchStudyCode1
          placeholder: Select Branch of Study 1
          outputValue: value
          _select:
            transform: $.fields.category ? $minorList($.fields.category)
          _validateFn:
            transform: >-
              $.extend.defaultValue.branchStudyName1 ? {'label':
              $.extend.defaultValue.branchStudyName1, 'value':
              $.extend.defaultValue.branchStudyCode1}
            params:
              updateLabelExistOption: true
        - type: select
          label: Educational Method
          name: educationMethod
          placeholder: Select Educational Method
          outputValue: value
          _select:
            transform: $educationMethodList()
          _validateFn:
            transform: >-
              $.extend.defaultValue.educationalMethodName ? {'label':
              $.extend.defaultValue.educationalMethodName, 'value':
              $.extend.defaultValue.educationMethod}
            params:
              updateLabelExistOption: true
        - type: select
          label: Language
          name: languages
          placeholder: Select Language
          outputValue: value
          _select:
            transform: $langugeList()
          _validateFn:
            transform: >-
              $.extend.defaultValue.languageName ? {'label':
              $.extend.defaultValue.languageName, 'value':
              $.extend.defaultValue.languages}
            params:
              updateLabelExistOption: true
        - type: select
          label: Degree
          name: degree
          placeholder: Select Degree
          outputValue: value
          _select:
            transform: >-
              $.fields.education = '50' ? $degreeList($.fields.education) :
              $educationEstOne($.fields.education)
          _value:
            transform: >-
              $not($.fields.education = '50') ? $.fields.education :
              $.fields.education = '50' ? $.fields.degree
          _disabled:
            transform: $not($.fields.education = '50')
          _validateFn:
            transform: >-
              $.extend.defaultValue.degreeName ? {'label':
              $.extend.defaultValue.degreeName, 'value':
              $.extend.defaultValue.degree}
            params:
              updateLabelExistOption: true
        - type: select
          label: Level
          name: level
          placeholder: Select Level
          outputValue: value
          _select:
            transform: $levelList()
          _validateFn:
            transform: >-
              $.extend.defaultValue.levelName ? {'label':
              $.extend.defaultValue.levelName, 'value':
              $.extend.defaultValue.level}
            params:
              updateLabelExistOption: true
        - type: text
          label: GPA
          name: GPA
          placeholder: Enter GPA
          outputValue: value
          validators:
            - type: maxLength
              args: '4'
              text: Maximum 4 characters
    - type: group
      n_cols: 1
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - type: dateRange
          label: Start Date
          name: startDate
          mode: date-picker
          setting:
            format: dd/MM/yyyy
        - type: dateRange
          label: End Date
          name: endDate
          mode: date-picker
          setting:
            format: dd/MM/yyyy
        - type: dateRange
          label: Issue Date
          name: issueDate
          mode: date-picker
          setting:
            format: dd/MM/yyyy
        - type: text
          label: Educational Est.
          name: educationName
        - type: text
          label: Country
          name: countryName
        - type: text
          label: City/Province
          name: cityName
        - type: text
          label: School
          name: instituteName
        - type: text
          label: Major
          name: educationTrainingName
        - type: text
          label: Branch of Study 1
          name: branchStudyName1
        - type: text
          label: Educational Method
          name: educationalMethodName
        - type: text
          label: Language
          name: languageName
        - type: text
          label: Degree
          name: degreeName
        - type: text
          label: Level
          name: levelName
        - type: text
          label: GPA
          name: GPA
    - type: group
      fields:
        - type: textarea
          label: Note
          name: note
          placeholder: Enter Note
          textarea:
            autoSize:
              minRows: 3
    - type: group
      _condition:
        transform: >-
          $not($.extend.formType = 'view') and $not($.extend.formType =
          'create') and $not($.extend.formType = 'edit')
      fields:
        - type: upload
          label: Attachment
          name: attachment
          outputValue: value
          upload:
            accept:
              - >-
                application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
              - application/pdf
              - application/vnd.ms-excel
            size: 5
            isMultiple: false
          _condition:
            transform: $not($.extend.formType = 'view')
        - type: upload
          name: attachmentResults
          readOnly: true
          canAction: true
          hiddenLabel: true
          _condition:
            transform: $.extend.formType = 'edit'
        - name: isFileModified
          unvisible: true
          type: text
          _value:
            transform: >-
              $count($.fields.attachmentResults) = 0 or $.fields.attachment !=
              null ? 'Y' : 'N'
        - type: upload
          label: Attachment
          name: attachmentResults
          readOnly: true
          _condition:
            transform: $.extend.formType = 'view'
  historyTitle: >-
    $DateFormat($.startDate, 'DD/MM/YYYY') & ' - ' & $DateFormat($.endDate,
    'DD/MM/YYYY')
  historyDescription: >-
    $.instituteName & ' - ' & $.educationTrainingName & ' - ' & ($.education !=
    ' ' and $.education != '50' ? $.educationName : $.degreeName)
  sources:
    educationEstList:
      uri: '"/api/picklists/EDUCATIONLEVEL/values/pagination"'
      method: GET
      queryTransform: '{''limit'': $.limit, ''page'': $.page, ''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code, 'name': $item.name.default}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    educationEstOne:
      uri: '"/api/picklists/EDUCATIONLEVEL/values"'
      method: GET
      queryTransform: '{''filter'': [{''field'':''code'',''operator'': ''$eq'',''value'':$.code}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code, 'name': $item.name.default}})[]
      disabledCache: true
      params:
        - code
    countryList:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      queryTransform: '{''limit'': $.limit, ''page'': $.page, ''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code, 'linkCatalogDataCode': $item.linkCatalogDataCode}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    provinceList:
      uri: '"/api/picklists/PROVINCE/values/pagination"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'search': $.search,'filter':
        [{'field':'codE501','operator': '$eq','value':$.country}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code, 'linkCatalogDataCode': $item.linkCatalogDataCode}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - country
    provinceListNotPagination:
      uri: '"/api/picklists/PROVINCE/values"'
      method: GET
      queryTransform: '{''filter'': [{''field'':''codE501'',''operator'': ''$eq'',''value'':$.country}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code, 'linkCatalogDataCode': $item.linkCatalogDataCode}})[]
      disabledCache: true
      params:
        - country
    instituteList:
      uri: '"/api/picklists/TRAININGSCHOOL/values/pagination"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'search': $.search,'filter':
        [{'field':'codE501','operator': '$in','value':$.city}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code , 'linkCatalogDataCode': $item.linkCatalogDataCode}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - city
    filterSchool:
      uri: '"/api/picklists/TRAININGSCHOOL/values"'
      method: GET
      queryTransform: '{''filter'': [{''field'':''code'',''operator'': ''$eq'',''value'':$.institute}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $.data
      disabledCache: true
      params:
        - institute
    filterProvince:
      uri: '"/api/picklists/PROVINCE/values"'
      method: GET
      queryTransform: '{''filter'': [{''field'':''code'',''operator'': ''$eq'',''value'':$.city}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $.data
      disabledCache: true
      params:
        - city
    filterCountry:
      uri: '"/api/picklists/COUNTRIES/values"'
      method: GET
      queryTransform: '{''filter'': [{''field'':''code'',''operator'': ''$eq'',''value'':$.country}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code, linkCatalogDataCode: $item.linkCatalogDataCode}})[]
      disabledCache: true
      params:
        - country
    majorList:
      uri: '"/api/picklists/MAJOR/values/pagination"'
      method: GET
      queryTransform: '{''limit'': $.limit, ''page'': $.page, ''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    minorList:
      uri: '"/api/picklists/MINOR/values/" & $.category & ""'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - category
    educationMethodList:
      uri: '"/api/picklists/FORMSOFTRAINING/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
    langugeList:
      uri: '"/api/picklists/TRAININGLANGUAGE/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
    degreeList:
      uri: '"/api/picklists/DEGREE/values/" & $.education & ""'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - education
    levelList:
      uri: '"/api/picklists/DEGREECLASSIFICATION/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
  footer:
    create: false
    update: true
    createdOn: createdAt
    updatedOn: updatedAt
    createdBy: createdBy
    updatedBy: updatedBy
  variables:
    _provinceListNotPagination:
      transform: $.fields.country ? $provinceListNotPagination($.fields.country)
    _provinceListCode:
      transform: >-
        $.variables._provinceListNotPagination ?
        $map($.variables._provinceListNotPagination, function ($v, $i, $a){
        $v.value })
    _countProvince:
      transform: $count($.fields.city)
    _countMultipleProvince:
      transform: >-
        $.variables._provinceListCode ? $count($.variables._provinceListCode) :
        0
    _selectedSchool:
      transform: $.fields.institute ? $filterSchool($.fields.institute)
    _selectedProvince:
      transform: >-
        $.variables._selectedSchool ?
        $filterProvince($.variables._selectedSchool[0].linkCatalogDataCode) :
        $.fields.city ? $filterProvince($.fields.city)
    _selectedCountry:
      transform: >-
        $.variables._selectedProvince ?
        $filterCountry($.variables._selectedProvince[0].linkCatalogDataCode)
filter_config: {}
layout_options:
  show_dialog_form_save_add_button: true
  is_upload_file: true
  is_copy_data_insert_new: false
  widget_options:
    refeshDependent:
      - HR.FS.FR.002
layout_options__header_buttons:
  - id: create
    title: create
    icon: icon-plus-bold
  - id: history
    title: history
    icon: icon-clock-counter-clockwise-bold
options: null
create_form: null
layout_options__footer_buttons: null
layout_options__row_actions: null
backend_url: api/personals/:id1/education-infos
screen_name: null
layout_options__actions_many: null
parent: HR.FS.FR.000
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key: null
screen_designs: []
function_specs: []
fields: []
children: []
menu_item: null
