id: HR.FS.FR.021
status: draft
sort: 57
user_created: 56e71b91-dd39-4ebc-afd1-b4f00f38d4a5
date_created: '2024-10-08T07:32:11.763Z'
user_updated: 56e71b91-dd39-4ebc-afd1-b4f00f38d4a5
date_updated: '2025-07-22T06:29:28.304Z'
title: Certificate
requirement:
  time: 1748398296928
  blocks:
    - id: tyovagRrqt
      type: paragraph
      data:
        text: Quản lý thông tin chứng chỉ
  version: 2.30.7
screen_design: null
module: HR
local_fields:
  - code: startDate
    pinned: true
    title: Start Date
    data_type:
      key: YYYY/MM/DD
      collection: data_types
    display_type:
      key: DD/MM/YYYY
      collection: field_types
  - code: endDate
    pinned: false
    title: End Date
    data_type:
      key: YYYY/MM/DD
      collection: data_types
    display_type:
      key: DD/MM/YYYY
      collection: field_types
  - code: certificateCategoryName
    title: Category
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: specializeName
    pinned: false
    title: Specialize
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: type
    pinned: false
    title: Type
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: certificateCode
    title: ID
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: name
    title: Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    pinned: false
  - code: number
    pinned: false
    title: Number
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: trainingPlaces
    pinned: false
    title: Training places
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: issuedDate
    pinned: false
    title: Issued date
    data_type:
      key: YYYY/MM/DD
      collection: data_types
    display_type:
      key: DD/MM/YYYY
      collection: field_types
  - code: issuedBy
    pinned: false
    title: Issued by
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: certificateLevelName
    pinned: false
    title: Level
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: note
    pinned: true
    title: Note
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: scopeOfProfessionalPractice
    pinned: false
    title: Scope Of Professional Practice
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
mock_data: null
local_buttons: null
layout: layout-widget
form_config:
  formTitle:
    proceed: Add New Certificate
    create: Add New Certificate
    edit: Edit Certificate
  historyHeaderTitle: '''Certificate Details'''
  defaultValidateError: Required fields have not been filled in completely (fields marked with *)
  fields:
    - type: group
      n_cols: 2
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - type: dateRange
          name: startDate
          label: Start Date
          mode: date-picker
          placeholder: dd/MM/yyyy
          validators:
            - type: required
            - type: ppx-custom
              args:
                transform: >-
                  $exists($.fields.startDate) and
                  $DateDiff($DateFormat($.fields.startDate, 'yyyy-MM-DD'),
                  $DateFormat($now(), 'yyyy-MM-DD'), 'd') > 0
              text: Start date must be less or equal than current date
          _class:
            transform: $.extend.formType = 'view' ? 'unrequired'
        - type: dateRange
          name: endDate
          label: End Date
          mode: date-picker
          placeholder: dd/MM/yyyy
          validators:
            - type: ppx-custom
              args:
                transform: >-
                  $exists($.fields.startDate) and $exists($.fields.endDate) and
                  $DateDiff($DateFormat($.fields.startDate, 'yyyy-MM-DD'),
                  $DateFormat($.fields.endDate, 'yyyy-MM-DD'), 'd') > 0
              text: End date must be greater than start date
    - type: select
      name: certificateCategoryCode
      label: Category
      placeholder: Select Category
      outputValue: value
      _select:
        transform: $.variables._categoryList
      clearFieldsAfterChange:
        - specializeCode
        - certificateTypeCode
        - certificate
        - certificateCode
        - certificateCodeView
        - codeCertificate
      _value:
        transform: >-
          $exists($.variables._selectedSpecialize.linkCatalogDataCode) ?
          $.variables._selectedSpecialize.linkCatalogDataCode
      validators:
        - type: required
      _condition:
        transform: $not($.extend.formType = 'view')
    - type: group
      n_cols: 2
      _condition:
        transform: $not($.extend.formType = 'view')
      borderBottom: true
      fields:
        - type: select
          name: specializeCode
          label: Specialize
          placeholder: Select Specialize
          outputValue: value
          clearFieldsAfterChange:
            - certificateTypeCode
            - certificate
            - certificateCode
            - certificateCodeView
            - codeCertificate
          _select:
            transform: >-
              $.fields.certificateCategoryCode ?
              $specializeList($.fields.certificateCategoryCode)
          _value:
            transform: >-
              $exists($.variables._selectedCertificateType.linkCatalogDataCode)
              ? $.variables._selectedCertificateType.linkCatalogDataCode
          validators:
            - type: required
          _condition:
            transform: $not($.extend.formType = 'view')
        - type: select
          label: Type
          name: certificateTypeCode
          placeholder: Select Type
          outputValue: value
          clearFieldsAfterChange:
            - certificate
            - certificateCode
            - certificateCodeView
            - codeCertificate
          _select:
            transform: >-
              $.fields.specializeCode ?
              $certificateTypeList($.fields.specializeCode)
          _disabled:
            transform: '$.fields.specializeCode = '' '' ? true : false'
          _value:
            transform: >-
              $exists($.variables._selectedCertificate[0].linkCatalogDataCode) ?
              $.variables._selectedCertificate[0].linkCatalogDataCode
          validators:
            - type: required
          _condition:
            transform: $not($.extend.formType = 'view')
    - type: group
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - type: dateRange
          name: startDate
          label: Start Date
          mode: date-picker
          placeholder: dd/MM/yyyy
        - type: dateRange
          name: endDate
          label: End Date
          mode: date-picker
          placeholder: dd/MM/yyyy
        - type: text
          name: certificateCategoryName
          label: Category
          _condition:
            transform: $.extend.formType = 'view'
        - type: text
          name: specializeName
          label: Specialize
          _condition:
            transform: $.extend.formType = 'view'
        - type: text
          label: Type
          name: type
          _condition:
            transform: $.extend.formType = 'view'
    - type: group
      label: Certification
      collapse: false
      disableEventCollapse: true
      fields:
        - type: group
          n_cols: 2
          _condition:
            transform: $not($.extend.formType = 'view')
          fields:
            - type: select
              name: certificate
              label: Certificate Name
              placeholder: Select Name
              clearFieldsAfterChange:
                - certificateCode
                - certificateCodeView
                - codeCertificate
              _select:
                transform: >-
                  $getCertification(null, null, null,
                  $.fields.certificateCategoryCode, $.fields.specializeCode,
                  $.fields.certificateTypeCode)
              validators:
                - type: required
              _value:
                transform: >-
                  $.extend.formType = 'edit' ? $.fields.codeCertificate :
                  $.fields.certificate.item.code
              _condition:
                transform: $not($.extend.formType = 'view')
            - type: text
              name: codeCertificate
              unvisible: true
            - type: text
              name: certificateCode
              _value:
                transform: >-
                  $exists($.fields.certificate) and
                  $not($isNilorEmpty($.fields.certificate)) ?
                  $.fields.certificate.item.code :
                  $not($isNilorEmpty($.fields.codeCertificate)) ?
                  $.fields.codeCertificate : '_setValueNull'
              unvisible: true
            - type: text
              label: Certificate ID
              placeholder: Auto
              name: certificateCodeView
              disabled: true
              validators:
                - type: required
              _value:
                transform: >-
                  $exists($.fields.certificateCode) and
                  $not($isNilorEmpty($.fields.certificateCode)) ?
                  $.fields.certificateCode :
                  $not($isNilorEmpty($.fields.codeCertificate)) ?
                  $.fields.codeCertificate : '_setValueNull'
              _condition:
                transform: $not($.extend.formType = 'view')
            - type: text
              label: Number
              name: number
              placeholder: Enter Number
              validators:
                - type: maxLength
                  args: '12'
                  text: Maximum 12 characters
            - type: text
              name: trainingPlaces
              label: Training places
              placeholder: Enter Training places
              validators:
                - type: maxLength
                  args: '140'
                  text: You are entering more than 140 characters
            - type: dateRange
              label: Issued date
              name: issuedDate
              placeholder: dd/MM/yyyy
              mode: date-picker
              validators:
                - type: ppx-custom
                  args:
                    transform: >-
                      $exists($.fields.issuedDate) and
                      $DateDiff($DateFormat($.fields.issuedDate, 'yyyy-MM-DD'),
                      $DateFormat($now(), 'yyyy-MM-DD'), 'd') > 0
                  text: Issued date must be less or equal than current date
              _value:
                transform: $DateFormat($.fields.startDate, 'MM-DD-yyyy')
            - type: text
              label: Issued by
              name: issuedBy
              placeholder: Enter Issued by
              _value:
                transform: $.fields.trainingPlaces
              validators:
                - type: maxLength
                  args: '140'
                  text: You are entering more than 140 characters
            - type: select
              name: certificateLevelCode
              label: Level
              placeholder: Select Level
              outputValue: value
              _select:
                transform: $.variables._certificateLevelList
              _condition:
                transform: $not($.extend.formType = 'view')
            - type: textarea
              label: Note
              name: note
              placeholder: Enter Note
              col: 2
              textarea:
                autoSize:
                  minRows: 3
                maxCharCount: 1000
              validators:
                - type: maxLength
                  args: '1000'
                  text: Maximum 1000 characters
            - type: textarea
              label: Scope Of Professional Practice
              name: scopeOfProfessionalPractice
              placeholder: Enter Scope Of Professional Practice
              col: 2
              textarea:
                autoSize:
                  minRows: 3
                maxCharCount: 1000
              validators:
                - type: maxLength
                  args: '1000'
                  text: Maximum 1000 characters
        - type: group
          _condition:
            transform: $.extend.formType = 'view'
          fields:
            - type: text
              label: Certificate Name
              name: name
            - type: text
              label: Certificate ID
              name: certificateCode
            - type: text
              label: Number
              name: number
              placeholder: Enter number
            - type: text
              name: trainingPlaces
              label: Training places
              placeholder: Enter Training places
            - type: dateRange
              label: Issued date
              name: issuedDate
              placeholder: dd/MM/yyyy
              mode: date-picker
            - type: text
              label: Issued by
              name: issuedBy
              placeholder: Enter data
            - type: text
              name: certificateLevelName
              label: Level
            - type: textarea
              label: Note
              name: note
              placeholder: Enter note
              textarea:
                autoSize:
                  minRows: 3
            - type: textarea
              label: Scope Of Professional Practice
              name: scopeOfProfessionalPractice
              placeholder: Enter Scope Of Professional Practice
              textarea:
                autoSize:
                  minRows: 3
  sources:
    categoryList:
      uri: '"/api/picklists/CERTCATEGORY/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $append([], $map($.data, function($item) {{'label': $item.name.default,
        'value': $item.code}}))[]
      disabledCache: true
    specializeList:
      uri: >-
        "/api/picklists/DEGREEEXPERTISE/values/" & $.certificateCategoryCode &
        ""
      method: GET
      queryTransform: >-
        {'filter': [{'field':'codE501','operator':
        '$eq','value':$.certificateCategoryCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $append([], $map($.data, function($item) {{'label': $item.name.default,
        'value': $item.code, 'linkCatalogDataCode':
        $item.linkCatalogDataCode}}))[]
      disabledCache: true
      params:
        - certificateCategoryCode
    getCertification:
      uri: '"/api/personals/get-certification"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'search': $.search,'filter':
        [{'field':'codE501','operator':
        '$eq','value':$.certificateTypeCode},{'field':'certificateCategoryCode','operator':
        '$eq','value':$.certificateCategoryCode},
        {'field':'degreeExpertiseCode','operator': '$eq','value':
        $.specializeCode}, {'field':'certificateTypeCode','operator':
        '$eq','value': $.certificateTypeCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $append([], $map($.data, function($item) {{'label': $item.name, 'value':
        $item.code, 'item': $item, 'linkCatalogDataCode': $item.codE501}}))[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - certificateCategoryCode
        - specializeCode
        - certificateTypeCode
    certificateTypeList:
      uri: '"/api/picklists/CERTIFICATETYPE/values/" & $.specializeCode & ""'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'codE501','operator':
        '$eq','value':$.specializeCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $append([], $map($.data, function($item) {{'label': $item.name.default,
        'value': $item.code, 'linkCatalogDataCode':
        $item.linkCatalogDataCode}}))[]
      disabledCache: true
      params:
        - specializeCode
    certificateList:
      uri: '"/api/picklists/CERTIFICATE/values/pagination"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'search': $.search,'filter':
        [{'field':'codE501','operator': '$eq','value':$.certificateTypeCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $append([], $map($.data, function($item) {{'label': $item.name.default,
        'value': $item.code, 'item': $item, 'linkCatalogDataCode':
        $item.linkCatalogDataCode}}))[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - certificateTypeCode
    filterCertificate:
      uri: '"/api/picklists/CERTIFICATE/values"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'code','operator':
        '$eq','value':$.certificateCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $.data
      disabledCache: true
      params:
        - certificateCode
    certificateLevelList:
      uri: '"/api/picklists/CERTIFICATELEVEL/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $append([], $map($.data, function($item) {{'label': $item.name.default,
        'value': $item.code}}))[]
      disabledCache: true
    basicInfo:
      uri: '"/api/personals/" & $.empId & "/basic-infomation"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: '[$]'
      disabledCache: true
      params:
        - empId
  variables:
    _specializeList:
      transform: $not($.extend.formType = 'view') ? $specializeList()
    _certificateTypeList:
      transform: $not($.extend.formType = 'view') ? $certificateTypeList()
    _categoryList:
      transform: $categoryList()
    _certificateList:
      transform: $not($.extend.formType = 'view') ? $certificateList()
    _certificateLevelList:
      transform: $certificateLevelList()
    _selectedSpecialize:
      transform: >-
        $filter($.variables._specializeList, function ($v, $i, $a){ $v.value =
        $.fields.specializeCode })[0]
    _selectedCertificateType:
      transform: >-
        $filter($.variables._certificateTypeList, function ($v, $i, $a){
        $v.value = $.fields.certificateTypeCode })[0]
    _selectedCertificate:
      transform: $.fields.certificateCode ? $filterCertificate($.fields.certificateCode)
    _basicInfo:
      transform: $.extend.params.id1 ? $basicInfo($.extend.params.id1)
  historyTitle: >-
    $DateFormat($.startDate, 'DD/MM/YYYY') & ' - ' & $DateFormat($.endDate,
    'DD/MM/YYYY')
  historyDescription: $.type & ' - ' & $.name
filter_config: {}
layout_options:
  widget_header_buttons:
    - id: create
      title: create
      icon: plus
    - id: history
      title: history
      icon: clock-rotate-left
  is_copy_data_insert_new: false
  show_dialog_form_save_add_button: true
layout_options__header_buttons:
  - id: create
    title: create
    icon: icon-plus-bold
  - id: history
    title: history
    icon: icon-clock-counter-clockwise-bold
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions: null
backend_url: /api/personals/:id1/certificate-infos
screen_name: null
layout_options__actions_many: null
parent: HR.FS.FR.000
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key: null
screen_designs: []
function_specs: []
fields: []
children: []
menu_item: null
