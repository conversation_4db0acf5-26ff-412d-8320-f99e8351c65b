id: HR.FS.FR.022
status: draft
sort: 23
user_created: 7ff5c796-6aaa-4ed5-a1e2-96f1c88e1fe9
date_created: '2024-07-01T03:22:01.854Z'
user_updated: 2aa5db12-fa54-46d6-8e05-9e421858de0c
date_updated: '2025-07-24T03:19:54.874Z'
title: Email Information
requirement:
  time: 1745382177078
  blocks:
    - id: ZRtDUhRswN
      type: paragraph
      data:
        text: >-
          <a
          href="https://fptsoftware362.sharepoint.com/:x:/r/sites/HRDXFETeam/Shared%20Documents/General/HR/HO%20Review/FPT_PEOPLEX_FSD_HR.FS.FR.022_HRM_Qua%CC%89n%20ly%CC%81%20tho%CC%82ng%20tin%20email_v1.0.xlsx?d=w7dbdd3b0a69f4af3949a6c80f896d270&amp;csf=1&amp;web=1&amp;e=gZwtYU">FPT_PEOPLEX_FSD_HR.FS.FR.022_HRM_Quản
          lý thông tin email_v1.0.xlsx</a>
  version: 2.30.7
screen_design: null
module: HR
local_fields:
  - code: emailTypeName
    title: Bussiness
    description: emailType
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Title
      collection: field_types
  - code: emailAddress
    title: Personal
    description: effectiveDate
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: isPrimary
    title: isPrimary
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: Star
      collection: field_types
mock_data:
  - bussiness:
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>
    personal:
      - <EMAIL>
      - <EMAIL>
    table:
      - effectiveDate: 2024/03/03
        emailAddress: <EMAIL>
        isPrimary: true
        emailType: Bussiness
      - effectiveDate: 2024/03/03
        emailAddress: <EMAIL>
        isPrimary: false
        emailType: Personal
local_buttons: null
layout: layout-widget
form_config:
  formSize:
    create: large
    edit: large
  formTitle:
    proceed: Add New Email Information
    create: Add New Email Information
    edit: Edit Email Information
    view: Email Information
  fields:
    - type: array
      mode: table
      name: commands
      arrayOptions:
        canChangeSize: true
        showConfirmDelete: false
        uniqueField: isPrimary
        _canAddItem:
          transform: $.extend.permission.isCreate
        _canDeleteItem:
          transform: $.extend.formType = 'create' or $.extend.permission.isDelete
      _size:
        transform: $.extend.formType = 'create' ? 1
      _minSize:
        transform: $.extend.formType = 'create' ? 1
      checkEmptyValue: true
      _condition:
        transform: $not($.extend.formType = 'view')
      field:
        type: group
        fields:
          - type: text
            name: id
            unvisible: true
            width: 100px
          - type: select
            label: Email Type
            name: emailTypeCode
            placeholder: Select Email Type
            outputValue: value
            _condition:
              transform: $not($.extend.formType = 'view')
            _disabled:
              transform: >-
                $not($.extend.formType = 'create') and
                $not($.extend.permission.isEdit = true) and
                $exists($getFieldGroup($.extend.path, $.fields, 1).id)
            _select:
              transform: >-
                $emailTypesList($getFieldGroup($.extend.path, $.fields,
                1).effectiveDate)
            validators:
              - type: required
            width: 170px
          - type: text
            label: Email Type
            name: emailTypeName
            _condition:
              transform: $.extend.formType = 'view'
            width: 170px
          - type: dateRange
            label: Effective Date
            name: effectiveDate
            mode: date-picker
            _disabled:
              transform: >-
                $not($.extend.formType = 'create') and
                $not($.extend.permission.isEdit = true) and
                $exists($getFieldGroup($.extend.path, $.fields, 1).id)
            validators:
              - type: required
            width: 170px
          - type: text
            label: Email Address
            name: emailAddress
            placeholder: Enter Email Address
            _disabled:
              transform: >-
                $not($.extend.formType = 'create') and
                $not($.extend.permission.isEdit = true) and
                $exists($getFieldGroup($.extend.path, $.fields, 1).id)
            validators:
              - type: required
              - type: maxLength
                args: '1000'
              - type: pattern
                args: ^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$
                text: Invalid email format
            width: 226px
          - type: radioTable
            label: Primary
            name: isPrimary
            value: false
            _disabled:
              transform: >-
                $not($.extend.formType = 'create') and
                $not($.extend.permission.isEdit = true) and
                $exists($getFieldGroup($.extend.path, $.fields, 1).id)
            width: 120px
          - type: select
            label: Status
            name: status
            placeholder: Select Status
            outputValue: value
            _condition:
              transform: $not($.extend.formType = 'view')
            _disabled:
              transform: >-
                $not($.extend.formType = 'create') and
                $not($.extend.permission.isEdit = true) and
                $exists($getFieldGroup($.extend.path, $.fields, 1).id)
            validators:
              - type: required
            _value:
              transform: $.extend.formType = 'create' ? true
            select:
              - label: Active
                value: true
              - label: Inactive
                value: false
            width: 170px
          - type: radio
            label: Status
            name: status
            _condition:
              transform: $.extend.formType = 'view'
            radio:
              - label: Active
                value: true
              - label: Inactive
                value: false
            width: 170px
    - type: table
      name: commands
      layout_option:
        tool_table:
          show_table_checkbox: false
          show_table_filter: false
          show_table_group: false
          hidden_header: false
          collapse: false
        show_pagination: false
        hide_action_row: true
      _condition:
        transform: $.extend.formType = 'view'
      columns:
        - code: emailTypeName
          title: Email Type
          align: start
          data_type:
            key: String
            collection: data_types
          display_type:
            key: Label
            collection: field_types
          width: 8.5
        - code: effectiveDate
          title: Effective Date
          align: start
          data_type:
            key: String
            collection: data_types
          display_type:
            key: DD/MM/yyyy
            collection: field_types
          width: 8.5
        - code: emailAddress
          title: Email Address
          align: start
          data_type:
            key: String
            collection: data_types
          display_type:
            key: Label
            collection: field_types
          width: 14.125
        - code: isPrimary
          title: Primary
          align: start
          data_type:
            key: String
            collection: data_types
          display_type:
            key: YesNo
            collection: field_types
          width: 7.5
        - code: status
          title: Status
          align: start
          data_type:
            key: String
            collection: data_types
          display_type:
            key: Boolean Tag
            collection: field_types
          width: 7.5
  historyTitle: $DateFormat($.effectiveDate, 'DD/MM/YYYY')
  historyDescription: $.status
  sources:
    emailTypesList:
      uri: '"/api/picklists/EMAILTYPE/values"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - effectiveDate
filter_config: {}
layout_options:
  widget_options:
    show_more_type: hidden
  show_dialog_form_save_add_button: false
  widget_history_type: table
  is_update_array: true
  widget_content_type: table-custom
  group_data_history_by_key: commands
  filter_history_method: manual
layout_options__header_buttons:
  - id: create
    title: create
    icon: icon-plus-bold
  - id: history
    title: history
    icon: icon-clock-counter-clockwise-bold
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions: null
backend_url: api/personals/:id1/email-contacts
screen_name: null
layout_options__actions_many: null
parent: HR.FS.FR.000
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key: null
screen_designs: []
function_specs: []
fields: []
children: []
menu_item: null
