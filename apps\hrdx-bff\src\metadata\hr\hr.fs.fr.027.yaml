id: HR.FS.FR.027
status: draft
sort: 11
user_created: 7ff5c796-6aaa-4ed5-a1e2-96f1c88e1fe9
date_created: '2024-07-01T07:54:02.354Z'
user_updated: 56e71b91-dd39-4ebc-afd1-b4f00f38d4a5
date_updated: '2025-06-10T04:35:03.018Z'
title: Emergency Contact Information
requirement:
  time: 1747995595110
  blocks:
    - id: W_HeZ2_cbH
      type: paragraph
      data:
        text: <PERSON><PERSON>n hệ khẩn cấp
  version: 2.30.7
screen_design: null
module: HR
local_fields:
  - code: effectiveDate
    title: Effective Date
    description: startDate
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
  - code: status
    title: Status
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: Boolean Tag
      collection: field_types
  - code: fullName
    title: Full Name
    description: fullName
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: mainContact
    title: Main Contact
    description: null
    data_type:
      key: String
      collection: data_types
    display_type:
      key: YesNo
      collection: field_types
  - code: relationship
    title: Relationship
    description: relationship
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: nation
    title: Nationality
    description: national
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: email
    title: Email
    description: email
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: mobilePhone
    title: Mobile Phone
    description: Mobile Phone
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: sameAddress
    title: Same Address
    description: sameAddress
    data_type:
      key: String
      collection: data_types
    display_type:
      key: YesNo
      collection: field_types
  - code: mainAddress
    title: Main Address
    description: mainAddress
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: secondaryAddress
    title: Secondary Address
    description: secondaryAddress
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: note
    title: Note
    description: note
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
mock_data: null
local_buttons: null
layout: layout-widget
form_config:
  formTitle:
    create: Add New Emergency Contact Information
  fields:
    - type: group
      _condition:
        transform: $not($.extend.formType = 'view')
      n_cols: 2
      fields:
        - type: dateRange
          name: effectiveDate
          label: Effective Date
          mode: date-picker
          placeholder: dd/MM/yyyy
          validators:
            - type: required
        - type: radio
          label: Status
          name: status
          placeholder: Choose Status
          validators:
            - type: required
          _value:
            transform: $.extend.formType = 'create' ? true
          outputValue: value
          radio:
            - label: Active
              value: true
            - label: Inactive
              value: false
        - type: text
          name: fullName
          label: Full Name
          placeholder: Enter Full Name
          validators:
            - type: required
            - type: maxLength
              args: '120'
              text: Maximum 120 characters
        - type: checkbox
          name: mainContact
          label: Main Contact
          value: false
        - type: select
          name: relationshipCode
          label: Relationship
          placeholder: Choose Relationship
          outputValue: value
          _select:
            transform: $.variables._relationshipList
        - type: text
          dependantFieldSkip: 2
          dependantField: $.fields.relationshipCode
          name: checkRelationshipCode
          unvisible: true
          value: true
        - type: select
          name: countryCode
          label: Nationality
          placeholder: Choose Nationality
          outputValue: value
          isLazyLoad: true
          _select:
            transform: $countryList($.extend.limit, $.extend.page, $.extend.search)
          _validateFn:
            transform: >-
              $.extend.defaultValue ? {'label': $.extend.defaultValue.nation,
              'value': $.extend.defaultValue.countryCode}
            params:
              updateLabelExistOption: true
          _condition:
            transform: $.extend.formType = 'create'
          _value:
            transform: >-
              $boolean($.fields.relationshipCode) and $.extend.formType =
              'create' ? $.variables._country
          _defaultValue:
            transform: >-
              $.extend.formType = 'create' and
              $isNilorEmpty($.extend.defaultValue.countryCode) ? ($info :=
              $nationDefault($now(),'VNM') ; $exists($info) ? $info)
        - type: select
          name: countryCode
          label: Nationality
          placeholder: Choose Nationality
          outputValue: value
          isLazyLoad: true
          _select:
            transform: $countryList($.extend.limit, $.extend.page, $.extend.search)
          _validateFn:
            transform: >-
              $.extend.defaultValue ? {'label': $.extend.defaultValue.nation,
              'value': $.extend.defaultValue.countryCode}
            params:
              updateLabelExistOption: true
          _condition:
            transform: >-
              $not($.extend.formType = 'create') and $not($.extend.formType =
              'view')
        - type: text
          name: email
          label: Email
          placeholder: Enter Email
          validators:
            - type: maxLength
              args: '256'
              text: Maximum 256 characters
          _condition:
            transform: $.extend.formType = 'create'
          _value:
            transform: >-
              $boolean($.fields.relationshipCode) and $.extend.formType =
              'create' ? $.variables._email : ''
        - type: text
          name: mobilePhone
          label: Mobile Phone
          placeholder: Enter Mobile Phone
          validators:
            - type: required
            - type: maxLength
              args: '12'
              text: Maximum 12 characters
          _condition:
            transform: $.extend.formType = 'create'
          _value:
            transform: >-
              $boolean($.fields.relationshipCode) and $.extend.formType =
              'create' ? $.variables._phone : ''
        - type: text
          name: email
          label: Email
          placeholder: Enter Email
          validators:
            - type: maxLength
              args: '256'
              text: Maximum 256 characters
          _condition:
            transform: $not($.extend.formType = 'create')
        - type: text
          name: mobilePhone
          label: Mobile Phone
          placeholder: Enter Mobile Phone
          validators:
            - type: required
            - type: maxLength
              args: '12'
              text: Maximum 12 characters
          _condition:
            transform: $not($.extend.formType = 'create')
    - type: group
      _condition:
        transform: $not($.extend.formType = 'view')
      n_cols: 1
      fields:
        - type: checkbox
          label: Same address as employee
          hiddenLabel: true
          name: sameAddress
          labelType: type-row
          value: false
        - type: text
          name: mainAddress
          label: Main Address
          placeholder: Enter Main Address
          _value:
            transform: >-
              $.fields.sameAddress = false and $not($.extend.formType = 'view')
              ? $.variables._address
          validators:
            - type: maxLength
              args: '500'
              text: Maximum 500 characters
          _condition:
            transform: >-
              $boolean($.fields.relationshipCode) and $not($.extend.formType =
              'view') and $.fields.sameAddress = false
        - type: text
          name: mainAddress
          label: Main Address
          placeholder: Enter Main Address
          _value:
            transform: $.variables._currentAddress[0].addressDetail
          validators:
            - type: maxLength
              args: '500'
              text: Maximum 500 characters
          _condition:
            transform: $not($.extend.formType = 'view') and $.fields.sameAddress
        - type: text
          name: secondaryAddress
          label: Secondary Address
          placeholder: Enter Secondary Address
          validators:
            - type: maxLength
              args: '500'
              text: Maximum 500 characters
    - type: group
      _condition:
        transform: $.extend.formType = 'view'
      n_cols: 1
      fields:
        - type: dateRange
          name: effectiveDate
          label: Effective Date
          mode: date-picker
        - type: radio
          name: status
          label: Status
          radio:
            - label: Active
              value: true
              className: active
            - label: Inactive
              value: false
              className: inactive
        - type: text
          name: fullName
          label: Full Name
          placeholder: Enter full name
        - type: radio
          label: Main Contact
          name: mainContact
          radio:
            - label: 'Yes'
              value: true
              className: active
            - label: 'No'
              value: false
              className: inactive
        - type: text
          name: relationship
          label: Relationship
        - type: text
          name: email
          label: Email
        - type: text
          name: mobilePhone
          label: Mobile Phone
        - type: text
          name: nation
          label: Nationality
        - type: radio
          label: Same address as employee
          name: sameAddress
          radio:
            - label: 'Yes'
              value: true
              className: active
            - label: 'No'
              value: false
              className: inactive
        - type: text
          name: mainAddress
          label: Main Address
        - type: text
          name: secondaryAddress
          label: Secondary Address
          placeholder: Enter secondary address
          validators:
            - type: maxLength
              args: '500'
              text: Maximum 500 characters
    - type: textarea
      label: Note
      placeholder: Enter Note
      name: note
      textarea:
        autoSize:
          minRows: 3
        maxCharCount: 1000
      validators:
        - type: maxLength
          args: '1000'
          text: Maximum 1000 characters
  sources:
    familyInfo:
      uri: '"/api/personals/" & $.empId & "/family-infos/histories"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $
      disabledCache: true
      params:
        - empId
    getFamilyByRelationship:
      uri: >-
        "/api/personals/" & $.empId &
        "/family-infos/dropdown-relationship-family"
      method: GET
      queryTransform: >-
        {'filter': [{'field':'relationshipCode','operator':
        '$eq','value':$.relationshipCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $
      disabledCache: true
      params:
        - empId
        - relationshipCode
    addressInfo:
      uri: '"/api/personals/" & $.empId & "/personal-addresses/history/"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'startDate','operator':
        '$eq','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $
      disabledCache: true
      params:
        - empId
    relationshipList:
      uri: '"/api/picklists/FAMILYRELATIONSHIP/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $append([], $map($.data, function($item) {{'label': $item.name.default,
        'value': $item.code}}))
      disabledCache: true
    countryList:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $append([], $map($.data, function($item) {{'label': $item.name.default,
        'value': $item.code}}))
      disabledCache: true
      params:
        - limit
        - page
        - search
    nationDefault:
      uri: '"/api/picklists/COUNTRIES/values"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'code','operator':
        '$eq','value':$.code}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[0]
      disabledCache: true
      params:
        - effectiveDate
        - code
  variables:
    _addressInfo:
      transform: >-
        $.extend.params.id1 ? $addressInfo($.extend.params.id1,
        $.fields.effectiveDate)
    _currentAddress:
      transform: >-
        $filter($.variables._addressInfo, function($v, $i, $a) {
        $v.addressTypeCode = 'CUR' and $v.startDate <=
        $DateToTimestampUTC($.fields.effectiveDate) })[]
    _getFamilyByRelationship:
      transform: >-
        ($.extend.params.id1 and $.fields.relationshipCode) ?
        $getFamilyByRelationship($.extend.params.id1,
        $.fields.relationshipCode): []
    _country:
      transform: >-
        $.fields.relationshipCode != null and
        $count($.variables._getFamilyByRelationship) > 0 and
        $.variables._getFamilyByRelationship[0].countryCode ?
        $.variables._getFamilyByRelationship[0].countryCode :
        $.extend.defaultValue.countryCode and
        $not($.fields.checkRelationshipCode = null) ?
        $.extend.defaultValue.countryCode : null
    _address:
      transform: >-
        $.fields.relationshipCode != null and
        $count($.variables._getFamilyByRelationship) > 0 and
        $.variables._getFamilyByRelationship[0].address ?
        $.variables._getFamilyByRelationship[0].address :
        $.extend.defaultValue.mainAddress and
        $not($.fields.checkRelationshipCode = null) ?
        $.extend.defaultValue.mainAddress : '_setValueNull'
    _email:
      transform: >-
        $.fields.relationshipCode != null and
        $count($.variables._getFamilyByRelationship) > 0 and
        $.variables._getFamilyByRelationship[0].emailAddress ?
        $.variables._getFamilyByRelationship[0].emailAddress :
        $.extend.defaultValue.email and $not($.fields.checkRelationshipCode =
        null) ? $.extend.defaultValue.email : '_setValueNull'
    _phone:
      transform: >-
        $.fields.relationshipCode != null and
        $count($.variables._getFamilyByRelationship) > 0 and
        $.variables._getFamilyByRelationship[0].phoneNumber ?
        $.variables._getFamilyByRelationship[0].phoneNumber :
        $.extend.defaultValue.mobilePhone and
        $not($.fields.checkRelationshipCode = null) ?
        $.extend.defaultValue.mobilePhone : '_setValueNull'
    _relationshipList:
      transform: $relationshipList()
    test:
      transform: >-
        $.extend.defaultValue.mobilePhone and
        $not($.fields.checkRelationshipCode = null)
  historyTitle: $DateFormat($.effectiveDate, 'DD/MM/YYYY')
  historyDescription: $.statusName & '-' & $.fullName & '-' & $.mobilePhone
filter_config: {}
layout_options:
  widget_header_buttons:
    - id: create
      title: create
      icon: plus
    - id: history
      title: history
      icon: clock-rotate-left
  show_dialog_form_save_add_button: true
layout_options__header_buttons:
  - id: create
    title: create
    icon: icon-plus-bold
  - id: history
    title: history
    icon: icon-clock-counter-clockwise-bold
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions: null
backend_url: /api/personals/:id1/emergency-contact-infos
screen_name: null
layout_options__actions_many: null
parent: HR.FS.FR.000
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key: null
screen_designs: []
function_specs: []
fields: []
children: []
menu_item: null
