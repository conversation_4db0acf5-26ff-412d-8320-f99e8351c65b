id: HR.FS.FR.028
status: draft
sort: 22
user_created: 43878dbb-aa6c-4ef4-8bd7-0ea811ffafa7
date_created: '2024-07-01T08:51:05.228Z'
user_updated: 2aa5db12-fa54-46d6-8e05-9e421858de0c
date_updated: '2025-06-10T06:45:11.771Z'
title: Other/Previous Employer
requirement:
  time: 1747995766892
  blocks:
    - id: lkppBefSEc
      type: paragraph
      data:
        text: Qu<PERSON>n lý thông tin kinh nghiệm của cán bộ&nbsp;
  version: 2.30.7
screen_design: null
module: HR
local_fields:
  - code: startDate
    title: Start Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
  - code: endDate
    title: End Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
  - code: employer
    title: Employer
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: department
    title: Department
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: location
    title: Location
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: cityProvinceName
    title: City/Province
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: countryName
    title: Country
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: industry
    title: Industry
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: job
    title: Job
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: contractType
    title: Contract Type
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: teamSizeManaged
    title: Team Size Managed
    data_type:
      key: Number
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: titleOfManager
    title: Title of Manager
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: reasonLeave
    title: Reason Leave
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: projects
    title: Projects
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: awards
    title: Awards
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: responsibility
    title: Responsibility
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: decisionToRecognizeProfessionalTitles
    title: Decision to recognize professional titles
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: Boolean
      collection: field_types
  - code: note
    title: Note
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: decisionNumber
    title: Decision Number
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: nameOfDecision
    title: Academic Title of Lecturer
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: effectiveStartDate
    title: Effective Start Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
  - code: effectiveEndDate
    title: Effective End Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
  - code: signDay
    title: Sign Day
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
  - code: signer
    title: Signer
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: decisionUnit
    title: Decision Unit
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
mock_data:
  - startDate: 2024/01/01
    endDate: 2024/05/14
    employer: FPT Information System of The South Company Limited
    department: SAP system implementation department
    industry: Technology
    job: Business analyst
    contractType: Indefinite - term labor contract hold; Indefinite term contract
    responsibility: >-
      Performing requirements analysis; Gathering critical information from
      meetings with various stakeholders and producing useful reports.
    location: >-
      22nd floor, Keangnam Landmark 72 building, E8 Pham Hung, Nam Tu Liem,
      Hanoi
    cityProvince: Hanoi
    country: Vietnam
    teamSizeManaged: 4
    titleOfManager: Manager
    reasonLeave: Inappropriate orientation
    projects: >-
      Project to deploy human resources software for Joint Stock Commercial Bank
      for Foreign Trade of Vietnam
    achievement: Employee of the year
    note: Inappropriate orientation
    decisionToRecognizeProfessionalTitles: 'true'
    decisionNumber: 6902/QĐ- FPT IS
    nameOfDecision: Decision to recognize professional titles
    effectiveStartDate: 2024/01/01
    effectiveEndDate: 2024/05/14
    signDay: 01/01/2024
    signer: Nguyễn Văn Ánh
    decisionUnit: Professional title promotion review council of FPT Information System
    attachment:
      - name: Achievement.pdf
        url: '123'
local_buttons: null
layout: layout-widget
form_config:
  formTitle:
    create: Add New Other/Previous Employer
  fields:
    - type: group
      n_cols: 2
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - type: dateRange
          name: startDate
          label: Start Date
          mode: date-picker
          placeholder: dd/MM/yyyy
          validators:
            - type: required
            - type: ppx-custom
              args:
                transform: >-
                  $exists($.fields.startDate) and
                  $DateDiff($DateFormat($.fields.startDate, 'yyyy-MM-DD'),
                  $DateFormat($.fields.endDate, 'yyyy-MM-DD'), 'd') > 0
              text: The start date must be less than or equal to the end date
            - type: ppx-custom
              args:
                transform: >-
                  $exists($.fields.dateOfBirth) and
                  $DateDiff($DateFormat($.fields.dateOfBirth, 'yyyy-MM-DD'),
                  $DateFormat($.fields.startDate, 'yyyy-MM-DD'), 'd') > 1
              text: The start date must be less than or equal to the date of birth
            - type: ppx-custom
              args:
                transform: >-
                  $exists($.fields.startDate) and
                  $DateDiff($DateFormat($.fields.startDate, 'yyyy-MM-DD'),
                  $DateFormat($now(), 'yyyy-MM-DD'), 'd') > 0
              text: Start date must be less than current date
        - type: dateRange
          name: endDate
          label: End Date
          validators:
            - type: ppx-custom
              args:
                transform: >-
                  $exists($.fields.startDate) and
                  $DateDiff($DateFormat($.fields.startDate, 'yyyy-MM-DD'),
                  $DateFormat($.fields.endDate, 'yyyy-MM-DD'), 'd') > 0
              text: The start date must be less than or equal to the end date
            - type: ppx-custom
              args:
                transform: >-
                  $exists($.fields.endDate) and
                  $DateDiff($DateFormat($.fields.endDate, 'yyyy-MM-DD'),
                  $DateFormat($now(), 'yyyy-MM-DD'), 'd') > 0
              text: End date must be less than current date
          mode: date-picker
          placeholder: dd/MM/yyyy
        - type: text
          name: employer
          label: Employer
          placeholder: Enter Employer
          validators:
            - type: required
            - type: maxLength
              args: '400'
              text: Maximum 400 characters
        - name: department
          label: Department
          type: text
          placeholder: Enter Department
          _value:
            transform: parent.businessUnit
        - type: select
          name: countryCode
          label: Country
          placeholder: Select Country
          outputValue: value
          isLazyLoad: true
          clearFieldsAfterChange:
            - cityProvince
          _select:
            transform: $nationsList($.extend.limit, $.extend.page, $.extend.search)
          _validateFn:
            transform: >-
              $.extend.defaultValue.countryName ? {'label':
              $.extend.defaultValue.countryName, 'value':
              $.extend.defaultValue.countryCode}
            params:
              updateLabelExistOption: true
          validators:
            - type: required
          _value:
            transform: >-
              $.extend.formType = 'create' and $count($filterCountry('VNM')) > 0
              ? {'label': 'Việt Nam', 'value': 'VNM'}
        - type: select
          name: cityProvince
          label: City Province
          placeholder: Select City Province
          outputValue: value
          isLazyLoad: true
          _select:
            transform: >-
              $.fields.countryCode ?
              $provincesList($.fields.countryCode,$.extend.limit, $.extend.page,
              $.extend.search)
          _validateFn:
            transform: >-
              $.extend.defaultValue.cityProvinceName ? {'label':
              $.extend.defaultValue.cityProvinceName, 'value':
              $.extend.defaultValue.cityProvince}
            params:
              updateLabelExistOption: true
          validators:
            - type: required
        - name: location
          label: Location
          type: text
          placeholder: Enter Location
          validators:
            - type: maxLength
              args: '400'
              text: Maximum 400 characters
        - type: select
          name: industryCode
          label: Industry
          outputValue: value
          placeholder: Select Industry
          _select:
            transform: $industryList()
        - name: job
          label: Job
          type: text
          placeholder: Enter Job
          validators:
            - type: required
            - type: maxLength
              args: '256'
              text: Maximum 256 characters
          _class:
            transform: $.extend.formType = 'view' ? 'unrequired'
        - type: select
          name: contractType
          label: Contract Type
          placeholder: Enter Contract Type
          outputValue: value
          _select:
            transform: $contractTypeList()
        - type: number
          name: teamSizeManaged
          label: Team Size Managed
          placeholder: Enter Team Size Managed
        - name: titleOfManager
          label: Title Of Manager
          type: text
          placeholder: Enter Title Of Manager
          validators:
            - type: maxLength
              args: '256'
              text: Maximum 256 characters
    - type: group
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - type: dateRange
          name: startDate
          label: Start Date
          mode: date-picker
          placeholder: dd/MM/yyyy
        - type: dateRange
          name: endDate
          label: End Date
          mode: date-picker
          placeholder: dd/MM/yyyy
        - type: text
          name: employer
          label: Employer
          placeholder: Enter employer
        - name: department
          label: Department
          type: text
          placeholder: Enter department
        - type: text
          name: countryName
          label: Country
        - type: text
          name: cityProvinceName
          label: City Province
        - name: location
          label: Location
          type: text
          placeholder: Enter location
        - type: text
          name: industry
          label: Industry
          placeholder: Select industry
        - name: job
          label: Job
          type: text
          placeholder: Enter Job
        - type: select
          name: contractType
          label: Contract Type
          placeholder: Enter contract type
          outputValue: value
          _select:
            transform: $contractTypeList()
        - type: number
          name: teamSizeManaged
          label: Team Size Managed
          placeholder: Enter team size managed
        - name: titleOfManager
          label: Title Of Manager
          type: text
          placeholder: Enter title of manager
    - type: group
      fields:
        - name: reasonLeave
          label: Reason Leave
          type: textarea
          placeholder: Enter Reason Leave
          textarea:
            autoSize:
              minRows: 3
            maxCharCount: 1000
          validators:
            - type: maxLength
              args: '1000'
              text: Maximum 1000 characters
        - name: projects
          label: Projects
          type: textarea
          placeholder: Enter Projects
          textarea:
            autoSize:
              minRows: 3
            maxCharCount: 1000
          validators:
            - type: maxLength
              args: '1000'
              text: Maximum 1000 characters
        - name: awards
          label: Awards
          type: textarea
          placeholder: Enter Awards
          textarea:
            autoSize:
              minRows: 3
            maxCharCount: 5000
          validators:
            - type: maxLength
              args: '5000'
              text: Maximum 5000 characters
        - name: responsibility
          label: Responsibility
          type: textarea
          placeholder: Enter Responsibility
          textarea:
            autoSize:
              minRows: 3
            maxCharCount: 5000
          validators:
            - type: maxLength
              args: '5000'
              text: Maximum 5000 characters
        - name: note
          label: Note
          type: textarea
          placeholder: Enter Note
          textarea:
            autoSize:
              minRows: 3
            maxCharCount: 1000
          validators:
            - type: maxLength
              args: '1000'
              text: Maximum 1000 characters
    - type: dateRange
      name: dateOfBirth
      unvisible: true
      mode: date-picker
      setting:
        format: dd/MM/yyyy
        type: date
      _value:
        transform: $.variables._basicInfo[0].dateOfBirth
    - type: checkbox
      name: decisionToRecognizeProfessionalTitles
      label: Decision To Recognize Professional Titles
      hiddenLabel: true
      value: true
      col: 1
      _condition:
        transform: $not($.extend.formType = 'view')
    - type: checkbox
      name: decisionToRecognizeProfessionalTitles
      label: Decision To Recognize Professional Titles
      col: 1
      disabled: true
      _condition:
        transform: $.extend.formType = 'view'
    - type: group
      _n_cols:
        transform: '$.extend.formType = ''view'' ? 1 : 2'
      _condition:
        transform: $.fields.decisionToRecognizeProfessionalTitles = true
      fields:
        - name: decisionNumber
          label: Decision Number
          type: text
          placeholder: Enter Decision Number
          validators:
            - type: required
          _class:
            transform: $.extend.formType = 'view' ? 'unrequired'
        - name: nameOfDecision
          label: Academic Title of Lecturer
          type: select
          outputValue: value
          placeholder: Enter Academic Title of Lecturer
          _select:
            transform: $nameOfDecision()
          validators:
            - type: required
            - type: maxLength
              args: '400'
              text: Maximum 400 characters
          _class:
            transform: $.extend.formType = 'view' ? 'unrequired'
        - type: dateRange
          label: Effective Start Date
          name: effectiveStartDate
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
          _class:
            transform: $.extend.formType = 'view' ? 'unrequired'
          validators:
            - type: required
            - type: ppx-custom
              args:
                transform: >-
                  $exists($.fields.effectiveStartDate) and
                  $exists($.fields.effectiveEndDate) ?
                  $DateDiff($DateFormat($.fields.effectiveStartDate,
                  'yyyy-MM-DD'), $DateFormat($.fields.effectiveEndDate,
                  'yyyy-MM-DD'), 'd') > 0
              text: The start date must be less than or equal to the end date
        - type: dateRange
          label: Effective End Date
          name: effectiveEndDate
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
          _class:
            transform: $.extend.formType = 'view' ? 'unrequired'
          validators:
            - type: ppx-custom
              args:
                transform: >-
                  $exists($.fields.effectiveStartDate) and
                  $exists($.fields.effectiveEndDate) ?
                  $DateDiff($DateFormat($.fields.effectiveStartDate,
                  'yyyy-MM-DD'), $DateFormat($.fields.effectiveEndDate,
                  'yyyy-MM-DD'), 'd') > 0
              text: The start date must be less than or equal to the end date
        - type: dateRange
          label: Sign Day
          name: signDay
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
        - type: text
          name: signer
          label: Signer
          placeholder: Enter Signer
          validators:
            - type: maxLength
              args: '120'
              text: Maximum 120 characters
    - name: decisionUnit
      label: Decision Unit
      type: text
      placeholder: Enter Decision Unit
      validators:
        - type: required
        - type: maxLength
          args: '400'
          text: Maximum 400 characters
      _condition:
        transform: $.fields.decisionToRecognizeProfessionalTitles = true
      _class:
        transform: $.extend.formType = 'view' ? 'unrequired'
  sources:
    basicInfo:
      uri: '"/api/personals/" & $.empId & "/basic-infomation"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: '[$]'
      disabledCache: true
      params:
        - empId
    nameOfDecision:
      uri: '"/api/picklists/ACADEMICTITLELECTURER/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
    contractTypeList:
      uri: '"/api/picklists/CONTRACTTYPE/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
    industryList:
      uri: '"/api/picklists/INDUSTRY/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
    nationsList:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    filterCountry:
      uri: '"/api/picklists/COUNTRIES/values"'
      method: GET
      queryTransform: '{''filter'': [{''field'':''code'',''operator'': ''$eq'',''value'':$.country}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - country
    provincesList:
      uri: '"/api/picklists/PROVINCE/values/" & $.countryCode & "/pagination"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - countryCode
        - limit
        - page
        - search
  variables:
    _basicInfo:
      transform: $.extend.params.id1 ? $basicInfo($.extend.params.id1)
  historyTitle: >-
    $DateFormat($.startDate, 'DD/MM/YYYY') & ' - ' & $DateFormat($.endDate,
    'DD/MM/YYYY')
  historyDescription: $.job & ' - ' & $.employer
filter_config: {}
layout_options:
  widget_header_buttons:
    - id: create
      title: create
      icon: plus
    - id: history
      title: history
      icon: clock-rotate-left
  is_upload_file: true
  show_dialog_form_save_add_button: true
  is_new_dynamic_form: true
layout_options__header_buttons:
  - id: create
    title: create
    icon: icon-plus-bold
  - id: history
    title: history
    icon: icon-clock-counter-clockwise-bold
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions: null
backend_url: /api/personals/:id1/work-experience-infos
screen_name: null
layout_options__actions_many: null
parent: HR.FS.FR.000
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key: null
screen_designs: []
function_specs: []
fields: []
children: []
menu_item: null
