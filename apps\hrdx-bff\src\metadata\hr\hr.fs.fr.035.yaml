id: HR.FS.FR.035
status: draft
sort: 38
user_created: 7ff5c796-6aaa-4ed5-a1e2-96f1c88e1fe9
date_created: '2024-06-27T07:41:09.126Z'
user_updated: 2aa5db12-fa54-46d6-8e05-9e421858de0c
date_updated: '2025-07-28T04:01:56.170Z'
title: Family/ Dependent Information
requirement:
  time: 1749110656696
  blocks:
    - id: FnY12miS5V
      type: paragraph
      data:
        text: Quản lý thông tin người thân
  version: 2.30.7
screen_design: null
module: HR
local_fields:
  - code: index
    title: STT
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    options__tabular__column_width: 5
  - code: fullName
    pinned: true
    title: Full Name
    description: Thông tin cơ bản của người thân
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    options__tabular__column_width: 15
  - code: genderName
    pinned: false
    title: Gender
    description: Giới tính
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    options__tabular__column_width: 10
  - code: relationshipName
    pinned: false
    title: Relationship
    description: Quan hệ
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    options__tabular__column_width: 10
  - code: dateOfBirth
    pinned: false
    title: Date of birth
    description: null
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    options__tabular__column_width: 10
  - code: isDependent
    pinned: false
    title: Is Dependent
    description: Có phụ thuộc không
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: YesNo
      collection: field_types
    options__tabular__column_width: 10
mock_data: null
local_buttons: null
layout: layout-table
form_config:
  formSize:
    create: large
    edit: large
  fields:
    - type: group
      label: Basic Information
      required: true
      styling_not_form_view:
        fieldGroupContentStyle:
          paddingTop: 0px
          gap: 0
      fieldGroupContentStyle:
        paddingTop: 12px
      space: 0
      fields:
        - type: group
          fields:
            - type: text
              name: code
              unvisible: true
            - type: text
              name: empId
              unvisible: true
        - type: array
          space: 12
          name: basicsInfo
          _size:
            transform: $.extend.formType = 'create' ? 1
          minSize: 1
          arrayOptions:
            canChangeSize: true
            add_btn_type: secondary
            add_btn_size: small
          field:
            type: group
            _label:
              transform: '''Basic Information '' & $string($.extend.path[-1] + 1)'
            collapse: false
            isBorderTopNone: true
            styling_not_form_view:
              fieldGroupContentStyle:
                padding: 20px
                paddingBottom: 0px
              fieldGroupTitleStyle:
                padding: 10px 12px
                fontSize: 14px
              fieldBackground: '#fff'
              borderRadius: 8px
              border: '1px solid #DFE3E8'
              borderBottomLabel: true
              lastGroupStyleOff: true
              isHideArrow: false
            fields:
              - type: dateRange
                mode: date-picker
                label: Effective Date
                name: effectiveDate
                placeholder: dd/MM/yyyy
                setting:
                  format: dd/MM/yyyy
                validators:
                  - type: required
                _value:
                  transform: $.extend.formType = 'create' ? $now()
              - type: group
                padding: '0'
                _n_cols:
                  transform: '$not($.extend.formType = ''view'') ? 2 : 1'
                fields:
                  - type: text
                    label: First Name
                    name: firstName
                    placeholder: Enter First Name
                    validators:
                      - type: required
                    _value:
                      transform: >-
                        ($.extend.path[-2] > $count($.fields.basicsInfo) - 1) ?
                        $trim($.fields.basicsInfo[0].firstName)
                  - type: text
                    label: Middle Name
                    name: middleName
                    placeholder: Enter Middle Name
                    _value:
                      transform: >-
                        ($.extend.path[-2] > $count($.fields.basicsInfo) - 1) ?
                        $trim($.fields.basicsInfo[0].middleName)
                  - type: text
                    label: Last Name
                    name: lastName
                    placeholder: Enter Last Name
                    validators:
                      - type: required
                    _value:
                      transform: >-
                        ($.extend.path[-2] > $count($.fields.basicsInfo) - 1) ?
                        $trim($.fields.basicsInfo[0].lastName)
                  - type: text
                    label: Full Name
                    name: fullName
                    placeholder: Enter Full Name
                    disabled: true
                    _value:
                      transform: >-
                        ($not($isNilorEmpty($getFieldGroup($.extend.path,
                        $.fields, 1).lastName)) ?
                        $trim($getFieldGroup($.extend.path, $.fields,
                        1).lastName) & ' ' : '') &
                        ($not($isNilorEmpty($getFieldGroup($.extend.path,
                        $.fields, 1).middleName)) ?
                        $trim($getFieldGroup($.extend.path, $.fields,
                        1).middleName) & ' ' : '') &
                        $trim($getFieldGroup($.extend.path, $.fields,
                        1).firstName)
                  - type: select
                    label: Relationship
                    name: relationship
                    outputValue: value
                    placeholder: Select Relationship
                    _select:
                      transform: $Relationships()
                    validators:
                      - type: required
                    _value:
                      transform: >-
                        ($.extend.path[-2] > $count($.fields.basicsInfo) - 1) ?
                        $.fields.basicsInfo[0].relationship
                  - type: select
                    label: Gender
                    name: gender
                    outputValue: value
                    placeholder: Gender
                    validators:
                      - type: required
                    _select:
                      transform: $Genders()
                    _value:
                      transform: >-
                        ($.extend.path[-2] > $count($.fields.basicsInfo) - 1) ?
                        $.fields.basicsInfo[0].gender
                  - type: dateRange
                    label: Date Of Birth
                    name: dateOfBirth
                    mode: date-picker
                    setting:
                      format: dd/MM/yyyy
                      type: date
                    validators:
                      - type: ppx-custom
                        args:
                          transform: >-
                            $exists($getFieldGroup($.extend.path, $.fields,
                            1).dateOfBirth) ? $DateDiff($DateFormat($now(),
                            'yyyy-MM-DD'),
                            $DateFormat($getFieldGroup($.extend.path, $.fields,
                            1).dateOfBirth, 'yyyy-MM-DD'), 'd') < 0
                        text: Date of birth cannot be greater than the current date
                    _value:
                      transform: >-
                        ($.extend.path[-2] > $count($.fields.basicsInfo) - 1) ?
                        $.fields.basicsInfo[0].dateOfBirth
                  - type: dateRange
                    label: Date Of Death
                    name: dateOfDeath
                    mode: date-picker
                    setting:
                      format: dd/MM/yyyy
                      type: date
                    validators:
                      - type: ppx-custom
                        id: check1
                        args:
                          transform: >-
                            $exists($getFieldGroup($.extend.path, $.fields,
                            1).dateOfDeath) ?
                            $DateDiff($DateFormat($getFieldGroup($.extend.path,
                            $.fields, 1).dateOfBirth, 'yyyy-MM-DD'),
                            $DateFormat($getFieldGroup($.extend.path, $.fields,
                            1).dateOfDeath, 'yyyy-MM-DD'), 'd') > 0
                        text: >-
                          The Date of Death must be more than or equal to
                          Employee's birth date
                      - type: ppx-custom
                        id: check2
                        args:
                          transform: >-
                            $exists($getFieldGroup($.extend.path, $.fields,
                            1).dateOfDeath) ? $DateDiff($DateFormat($now(),
                            'yyyy-MM-DD'),
                            $DateFormat($getFieldGroup($.extend.path, $.fields,
                            1).dateOfDeath, 'yyyy-MM-DD'), 'd') < 0
                        text: Date of death cannot be greater than the current date
                    _value:
                      transform: >-
                        ($.extend.path[-2] > $count($.fields.basicsInfo) - 1) ?
                        $.fields.basicsInfo[0].dateOfDeath
              - type: text
                name: nationalityName
                _value:
                  transform: >-
                    $NationalitiesByCode($getFieldGroup($.extend.path, $.fields,
                    1).nationality).label
                unvisible: true
              - type: select
                label: Nationality
                name: nationality
                outputValue: value
                placeholder: Select Nationality
                isLazyLoad: true
                _select:
                  transform: >-
                    $Nationalities($.extend.search, $.extend.page,
                    $.extend.limit)
                _value:
                  transform: >-
                    ($nationality:=$getFieldGroup($.extend.path, $.fields,
                    1).nationality;($.extend.path[-2] >
                    $count($.fields.basicsInfo) - 1) ? {'label':
                    $.fields.basicsInfo[0].nationalityName,'value':$.fields.basicsInfo[0].nationality}
                    : $count($.fields.basicsInfo) = 1 and $.extend.formType =
                    'create' ? $nationality ? $nationality :
                    $count($NationalitiesByCode('VNM')) > 0 ? {'label':
                    $NationalitiesByCode('VNM')[0].name.default, 'value':
                    'VNM'})
              - type: group
                _n_cols:
                  transform: '$not($.extend.formType = ''view'') ? 2 : 1'
                padding: '0'
                fields:
                  - type: dateRange
                    label: Relationship From
                    name: relationshipFrom
                    mode: date-picker
                    setting:
                      format: dd/MM/yyyy
                    validators:
                      - type: ppx-custom
                        args:
                          transform: >-
                            $exists($getFieldGroup($.extend.path, $.fields,
                            1).relationshipTo) and
                            $DateDiff($DateFormat($getFieldGroup($.extend.path,
                            $.fields, 1).relationshipTo, 'yyyy-MM-DD'),
                            $DateFormat($getFieldGroup($.extend.path, $.fields,
                            1).relationshipFrom, 'yyyy-MM-DD'), 'd') < 1
                        text: Relationship To must be greater than Relationship From
                    _value:
                      transform: >-
                        ($.extend.path[-2] > $count($.fields.basicsInfo) - 1) ?
                        $.fields.basicsInfo[0].relationshipFrom
                  - type: dateRange
                    label: Relationship To
                    name: relationshipTo
                    mode: date-picker
                    setting:
                      format: dd/MM/yyyy
                    validators:
                      - type: ppx-custom
                        args:
                          transform: >-
                            $exists($getFieldGroup($.extend.path, $.fields,
                            1).relationshipTo) and
                            $DateDiff($DateFormat($getFieldGroup($.extend.path,
                            $.fields, 1).relationshipTo, 'yyyy-MM-DD'),
                            $DateFormat($getFieldGroup($.extend.path, $.fields,
                            1).relationshipFrom, 'yyyy-MM-DD'), 'd') < 1
                        text: Relationship To must be greater than Relationship From
                    _value:
                      transform: >-
                        ($.extend.path[-2] > $count($.fields.basicsInfo) - 1) ?
                        $.fields.basicsInfo[0].relationshipTo
              - type: text
                label: Place Of Birth
                name: placeOfBirth
                placeholder: Enter Place of birth
                _value:
                  transform: >-
                    ($.extend.path[-2] > $count($.fields.basicsInfo) - 1) ?
                    $.fields.basicsInfo[0].placeOfBirth
              - type: group
                _n_cols:
                  transform: '$not($.extend.formType = ''view'') ? 2 : 1'
                padding: '0'
                fields:
                  - type: checkbox
                    label: Is Dependent
                    name: isDependent
                    value: false
                    hiddenLabel: true
                    _condition:
                      transform: $not($.extend.formType = 'view')
                  - type: checkbox
                    label: Is Dependent
                    name: isDependent
                    value: false
                    _condition:
                      transform: $.extend.formType = 'view'
                  - type: checkbox
                    label: Is Emergency
                    name: isEmergency
                    value: false
                    hiddenLabel: true
                    _condition:
                      transform: $not($.extend.formType = 'view')
                  - type: checkbox
                    label: Is Emergency
                    name: isEmergency
                    value: false
                    _condition:
                      transform: $.extend.formType = 'view'
    - type: group
      label: Dependent Information
      styling_not_form_view:
        fieldGroupContentStyle:
          paddingTop: 0px
          gap: 0
      fieldGroupContentStyle:
        paddingTop: 12px
      space: 0
      _required:
        transform: $.variables._selectedBasicInfo.isDependent
      fields:
        - type: group
          fields:
            - type: array
              space: 12
              name: biographies
              _size:
                transform: $.extend.formType = 'create' ? 1
              minSize: 1
              arrayOptions:
                canChangeSize: true
                add_btn_type: secondary
                add_btn_size: small
              field:
                type: group
                collapse: false
                _label:
                  transform: '''Dependent Information '' & $string($.extend.path[-1] + 1)'
                _unvisible:
                  transform: >-
                    $.extend.formType = 'view' and $map($.fields.biographies,
                    function($item) { $not($exists($item.taxCode)) and
                    $not($exists($item.registrationDate)) and
                    $not($exists($item.dependentFrom)) and
                    $not($exists($item.dependentTo)) and
                    $not($exists($item.deductionsCompanyCode)) and
                    $not($exists($item.deductionsLegalEntityCode))})
                isBorderTopNone: true
                styling_not_form_view:
                  fieldGroupContentStyle:
                    padding: 20px
                    paddingBottom: 0px
                  fieldGroupTitleStyle:
                    padding: 10px 12px
                    fontSize: 14px
                  fieldBackground: '#fff'
                  borderRadius: 8px
                  border: '1px solid #DFE3E8'
                  borderBottomLabel: true
                  lastGroupStyleOff: true
                  isHideArrow: false
                fields:
                  - type: group
                    _n_cols:
                      transform: '$not($.extend.formType = ''view'') ? 2 : 1'
                    padding: '0'
                    fields:
                      - type: text
                        label: Tax Code
                        name: taxCode
                        placeholder: Enter Tax Code
                        validators:
                          - type: maxLength
                            args: 120
                            text: Maximums 120 characters
                          - type: ppx-custom
                            args:
                              dependants:
                                - $.fields.biographies[$index].taxCode
                                - $.fields.biographies[$index].dependentFrom
                                - $.fields.biographies[$index].dependentTo
                              params:
                                $index: $.extend.path[-1]
                              transform: >-
                                $.extend.path[-1] ?
                                ($index:=$.extend.path[-1];$familyCode:=$.extend.defaultValue.basicsInfo[0].code
                                ? $.extend.defaultValue.basicsInfo[0].code :
                                null;$taxCode:=$.fields.biographies[$index].taxCode;$.extend.addOnValue.id1
                                ?
                                $test($not($checkDuplicateDependent($.extend.addOnValue.id1,
                                $taxCode, $familyCode,
                                $.fields.biographies[$index].dependentFrom,
                                $.fields.biographies[$index].dependentTo))))
                            text: >-
                              A dependent record with the same tax code already
                              exists.
                      - type: dateRange
                        mode: date-picker
                        label: Registration Date
                        name: registrationDate
                        placeholder: dd/MM/yyyy
                        clearFieldsAfterChange:
                          - deductionsCompanyCode
                          - deductionsLegalEntityCode
                        _class:
                          transform: >-
                            $.variables._selectedBasicInfo.isDependent ?
                            'required' : 'unrequired'
                      - type: dateRange
                        label: Dependent From
                        name: dependentFrom
                        mode: date-picker
                        _class:
                          transform: >-
                            $.variables._selectedBasicInfo.isDependent ?
                            'required' : 'unrequired'
                        validators:
                          - type: ppx-custom
                            args:
                              transform: >-
                                $exists($getFieldGroup($.extend.path, $.fields,
                                1).dependentTo) and
                                $DateDiff($DateFormat($getFieldGroup($.extend.path,
                                $.fields, 1).dependentTo, 'yyyy-MM-DD'),
                                $DateFormat($getFieldGroup($.extend.path,
                                $.fields, 1).dependentFrom, 'yyyy-MM-DD'), 'd')
                                < 1
                            text: Dependent To must be greater than Dependent From
                          - type: ppx-custom
                            id: checkDependentFrom
                            args:
                              params:
                                $index: $.extend.path[-2]
                              transform: >-
                                ($index := $.extend.path[-2];$biographies :=
                                $.fields.biographies;($count($biographies) > 1
                                and $index > 0) and ($current :=
                                $biographies[$index];$prev :=
                                $biographies[$index - 1];$currentFrom :=
                                $DateFormat($exists($current.dependentFrom) ?
                                $current.dependentFrom : '9999-12-31',
                                'yyyy-MM-DD');$prevTo :=
                                $DateFormat($exists($prev.dependentTo) ?
                                $prev.dependentTo : '9999-12-31',
                                'yyyy-MM-DD');$DateDiff($currentFrom, $prevTo,
                                'd') < 1))
                            text: >-
                              A record already exists for this dependent
                              registration time.
                      - type: dateRange
                        label: Dependent To
                        name: dependentTo
                        mode: date-picker
                        validators:
                          - type: ppx-custom
                            args:
                              transform: >-
                                $exists($getFieldGroup($.extend.path, $.fields,
                                1).dependentTo) and
                                $DateDiff($DateFormat($getFieldGroup($.extend.path,
                                $.fields, 1).dependentTo, 'yyyy-MM-DD'),
                                $DateFormat($getFieldGroup($.extend.path,
                                $.fields, 1).dependentFrom, 'yyyy-MM-DD'), 'd')
                                < 1
                            text: Dependent To must be greater than Dependent From
                      - type: select
                        label: Deductions at Company
                        name: deductionsCompanyCode
                        placeholder: Select Deductions at Company
                        outputValue: value
                        isLazyLoad: true
                        _select:
                          transform: >-
                            $Companies($.extend.search, $.extend.page,
                            $.extend.limit,$getFieldGroup($.extend.path,
                            $.fields, 1).registrationDate)
                        clearFieldsAfterChange:
                          - deductionsLegalEntityCode
                        _validateFn:
                          transform: >-
                            ($index:=$.extend.path[-2];$.extend.defaultValue.biographies
                            ? {'label':
                            $.extend.defaultValue.biographies[$index].deductionsCompanyName,
                            'value':
                            $.extend.defaultValue.biographies[$index].deductionsCompanyCode})
                          params:
                            updateLabelExistOption: true
                        _class:
                          transform: >-
                            $.variables._selectedBasicInfo.isDependent ?
                            'required' : 'unrequired'
                      - type: select
                        label: Deductions at Legal Entity
                        name: deductionsLegalEntityCode
                        placeholder: Select Deductions at Legal Entity
                        outputValue: value
                        _select:
                          transform: >-
                            $exists($getFieldGroup($.extend.path, $.fields,
                            1).deductionsCompanyCode) ?
                            $LegalEntity($getFieldGroup($.extend.path, $.fields,
                            1).deductionsCompanyCode,$getFieldGroup($.extend.path,
                            $.fields, 1).registrationDate)
                        _class:
                          transform: >-
                            $.variables._selectedBasicInfo.isDependent ?
                            'required' : 'unrequired'
                  - type: textarea
                    label: Note
                    name: note
                    placeholder: Enter Note
                    textarea:
                      autoSize:
                        minRows: 3
                      maxCharCount: 1000
                      validators:
                        type: maxLength
                        args: 1000
                        text: Maximum 1000 characters
    - type: group
      label: Phone
      styling_not_form_view:
        fieldGroupContentStyle:
          paddingTop: 0px
          gap: 0
      fieldGroupContentStyle:
        paddingTop: 12px
      space: 0
      _required:
        transform: $.variables._selectedBasicInfo.isEmergency
      fields:
        - type: array
          name: phone
          space: 12
          _size:
            transform: $.extend.formType = 'create' ? 1
          minSize: 1
          arrayOptions:
            canChangeSize: true
            add_btn_type: secondary
            add_btn_size: small
          field:
            type: group
            _label:
              transform: '''Phone '' & $string($.extend.path[-1] + 1)'
            _unvisible:
              transform: >-
                $.extend.formType = 'view' and $map($.fields.phone,
                function($item) { $not($exists($item.phoneType)) and
                $not($exists($item.phoneNumber))})
            collapse: false
            isBorderTopNone: true
            styling_not_form_view:
              fieldGroupContentStyle:
                padding: 20px
                paddingBottom: 0px
              fieldGroupTitleStyle:
                padding: 10px 12px
                fontSize: 14px
              fieldBackground: '#fff'
              borderRadius: 8px
              border: '1px solid #DFE3E8'
              borderBottomLabel: true
              lastGroupStyleOff: true
              isHideArrow: false
            fields:
              - type: radio
                label: Same Phone as Employee
                name: samePhoneAsEmployee
                value: false
                radio:
                  - label: 'Yes'
                    value: true
                  - label: 'No'
                    value: false
              - type: group
                padding: '0'
                _n_cols:
                  transform: '$not($.extend.formType = ''view'') ? 2 : 1 '
                fields:
                  - type: select
                    label: Phone Type
                    name: phoneType
                    outputValue: value
                    placeholder: Select Phone Type
                    _select:
                      transform: $PhoneTypes()
                  - type: text
                    label: Phone Number
                    name: phoneNumber
                    placeholder: Enter Phone Number
                    _value:
                      transform: >-
                        $getFieldGroup($.extend.path, $.fields,
                        1).samePhoneAsEmployee = true ?
                        $getFieldGroup($.extend.path, $.fields, 1).phoneNumber ?
                        $getFieldGroup($.extend.path, $.fields, 1).phoneNumber
                        :  $.variables._phoneInfo.value
                    _class:
                      transform: >-
                        $.variables._selectedBasicInfo.isEmergency ? 'required'
                        : 'unrequired'
              - type: checkbox
                label: Is Primary
                name: isPrimary
                hiddenLabel: true
                value: false
                _condition:
                  transform: $not($.extend.formType = 'view')
              - type: checkbox
                label: Is Primary
                name: isPrimary
                value: false
                _condition:
                  transform: $.extend.formType = 'view'
    - type: group
      label: National ID
      styling_not_form_view:
        fieldGroupContentStyle:
          paddingTop: 0px
          gap: 0
      fieldGroupContentStyle:
        paddingTop: 12px
      space: 0
      fields:
        - type: array
          space: 12
          name: nationalId
          _size:
            transform: $.extend.formType = 'create' ? 1
          minSize: 1
          arrayOptions:
            canChangeSize: true
            add_btn_type: secondary
            add_btn_size: small
            uniqueField: priority
          field:
            type: group
            _label:
              transform: '''National ID '' & $string($.extend.path[-1] + 1)'
            _unvisible:
              transform: >-
                $.extend.formType = 'view' and $map($.fields.nationalId,
                function($item) { $not($exists($item.endDate)) and
                $not($exists($item.nationalIdType)) and
                $not($exists($item.nationalIdNo))})
            padding: '0'
            collapse: false
            isBorderTopNone: true
            styling_not_form_view:
              fieldGroupContentStyle:
                padding: 20px
              fieldGroupTitleStyle:
                padding: 10px 12px
                fontSize: 14px
              fieldBackground: '#fff'
              borderRadius: 8px
              border: '1px solid #DFE3E8'
              borderBottomLabel: true
              lastGroupStyleOff: true
              isHideArrow: false
            fields:
              - type: group
                padding: '0'
                _n_cols:
                  transform: '$not($.extend.formType = ''view'') ? 2 : 1'
                fields:
                  - type: dateRange
                    mode: date-picker
                    label: Start Date
                    name: startDate
                    placeholder: dd/MM/yyyy
                  - type: dateRange
                    label: End Date
                    name: endDate
                    mode: date-picker
                    validators:
                      - type: ppx-custom
                        args:
                          transform: >-
                            $exists($getFieldGroup($.extend.path, $.fields,
                            1).endDate) and
                            $DateDiff($DateFormat($getFieldGroup($.extend.path,
                            $.fields, 1).endDate, 'yyyy-MM-DD'),
                            $DateFormat($getFieldGroup($.extend.path, $.fields,
                            1).startDate, 'yyyy-MM-DD'), 'd') < 1
                        text: End date must be greater than start date
                  - type: select
                    label: Country
                    name: nationality
                    outputValue: value
                    placeholder: Select Country
                    isLazyLoad: true
                    clearFieldsAfterChange:
                      - nationalIdType
                      - isChangedDefaultCountry
                    _select:
                      transform: >-
                        $Countries($.extend.search, $.extend.page,
                        $.extend.limit)
                    _value:
                      transform: >-
                        $.extend.formType = 'create' and
                        $count($CountriesByCode('VNM')) > 0 ? {'label':
                        $CountriesByCode('VNM')[0].name.default, 'value': 'VNM'}
                  - type: text
                    name: isChangedDefaultCountry
                    value: true
                    unvisible: true
                  - type: select
                    label: National ID Type
                    name: nationalIdType
                    outputValue: value
                    placeholder: Select National ID Type
                    _select:
                      transform: >-
                        ($national:=$getFieldGroup($.extend.path, $.fields,
                        1).nationality;$not($isNilorEmpty($national)) ?
                        $IdentityDocumentType($.extend.limit, $.extend.page,
                        $.extend.search, $national) :
                        $IdentityDocumentType($.extend.limit, $.extend.page,
                        $.extend.search))
              - type: text
                label: National ID Number
                name: nationalIdNo
                placeholder: Enter National ID Number
                validators:
                  - type: pattern
                    args: ^[a-zA-Z0-9]+$
                    text: The format is not correct
                  - type: ppx-custom
                    id: validateNationalIdNo1
                    args:
                      transform: >-
                        $getFieldGroup($.extend.path, $.fields, 1).nationality =
                        'VNM' and
                        $not($isNilorEmpty($getFieldGroup($.extend.path,
                        $.fields, 1).nationalIdNo)) and
                        $getFieldGroup($.extend.path, $.fields,
                        1).nationalIdType in ['VNM_IC', 'VNM_IC2', 'VNM_IC3']
                        and $not($length($getFieldGroup($.extend.path, $.fields,
                        1).nationalIdNo) = 9 or
                        $length($getFieldGroup($.extend.path, $.fields,
                        1).nationalIdNo) = 12)
                    text: National ID Number only allow 9 or 12 characters
                  - type: ppx-custom
                    id: validateNationalIdNo2
                    args:
                      transform: >-
                        $getFieldGroup($.extend.path, $.fields, 1).nationality =
                        'VNM' and
                        $not($isNilorEmpty($getFieldGroup($.extend.path,
                        $.fields, 1).nationalIdNo)) and
                        $getFieldGroup($.extend.path, $.fields,
                        1).nationalIdType in ['VNM_IC4', 'VNM_IC05', 'CIC'] and
                        $not($length($getFieldGroup($.extend.path, $.fields,
                        1).nationalIdNo) = 12)
                    text: National ID Number only allow 12 characters
              - type: checkbox
                label: Is Primary
                name: priority
                hiddenLabel: true
                value: false
                _condition:
                  transform: $not($.extend.formType = 'view')
              - type: checkbox
                label: Is Primary
                name: priority
                value: false
                _condition:
                  transform: $.extend.formType = 'view'
    - type: group
      label: Address
      styling_not_form_view:
        fieldGroupContentStyle:
          paddingTop: 0px
          gap: 0
      fieldGroupContentStyle:
        paddingTop: 12px
      space: 0
      fields:
        - type: array
          space: 12
          name: address
          _size:
            transform: $.extend.formType = 'create' ? 1
          minSize: 1
          arrayOptions:
            canChangeSize: true
            add_btn_type: secondary
            add_btn_size: small
          field:
            type: group
            _label:
              transform: '''Address '' & $string($.extend.path[-1] + 1)'
            _unvisible:
              transform: >-
                $.extend.formType = 'view' and $map($.fields.address,
                function($item) { $not($exists($item.addressType)) and
                ($exists($item.country) and $count($keys($item.country)) = 0)
                and $not($exists($item.provinceCity)) and
                $not($exists($item.district)) and $not($exists($item.ward)) and
                $not($exists($item.addressLine1)) and
                $not($exists($item.zipCode))})
            collapse: false
            isBorderTopNone: true
            styling_not_form_view:
              fieldGroupContentStyle:
                padding: 20px
                paddingBottom: 0px
              fieldGroupTitleStyle:
                padding: 10px 12px
                fontSize: 14px
              fieldBackground: '#fff'
              borderRadius: 8px
              border: '1px solid #DFE3E8'
              borderBottomLabel: true
              lastGroupStyleOff: true
              isHideArrow: false
            fields:
              - type: group
                padding: '0'
                _n_cols:
                  transform: '$not($.extend.formType = ''view'') ? 2 : 1'
                fields:
                  - type: dateRange
                    mode: date-picker
                    label: Effective Date
                    name: effectiveDate
                    placeholder: dd/MM/yyyy
                    setting:
                      format: dd/MM/yyyy
                    _class:
                      transform: >-
                        ($record := $getFieldGroup($.extend.path, $.fields, 0);
                        $addressType := $record.addressType; $country :=
                        $record.country ; $provinceCity := $record.provinceCity
                        ; $district := $record.district; $ward := $record.ward;
                        $addressLine1 := $record.addressLine1; $zipCode :=
                        $record.zipCode; $effectiveDate :=
                        $record.effectiveDate; $isChangedDefaultDate :=
                        $record.isChangedDefaultDate;
                        ($not($isNilorEmpty($effectiveDate)) and
                        $isNilorEmpty($isChangedDefaultDate)) or
                        ($not($isNilorEmpty($addressType)) or
                        $not($isNilorEmpty($country)) or
                        $not($isNilorEmpty($provinceCity)) or
                        $not($isNilorEmpty($district)) or
                        $not($isNilorEmpty($ward)) or
                        $not($isNilorEmpty($addressLine1)) or
                        $not($isNilorEmpty($zipCode)))  ? 'required' :
                        'fakeRequired')
                    clearFieldsAfterChange:
                      - isChangedDefaultDate
                    _value:
                      transform: $.extend.formType = 'create' ? $now()
                  - type: text
                    name: isChangedDefaultDate
                    value: true
                    unvisible: true
                  - type: select
                    label: Address Type
                    name: addressType
                    outputValue: value
                    placeholder: Select Address Type
                    _select:
                      transform: $AddressTypes()
              - type: radio
                label: Same Address as Employee
                name: samecontactAsEmployee
                value: false
                radio:
                  - label: 'Yes'
                    value: true
                  - label: 'No'
                    value: false
              - type: group
                padding: '0'
                _n_cols:
                  transform: '$not($.extend.formType = ''view'') ? 2 : 1'
                fields:
                  - type: select
                    label: Country
                    name: country
                    outputValue: value
                    placeholder: Select Country
                    clearFieldsAfterChange:
                      - checkChangeCountry
                      - provinceCity
                      - district
                      - ward
                    _disabled:
                      transform: >-
                        $getFieldGroup($.extend.path, $.fields,
                        1).samecontactAsEmployee
                    validateDisabled: true
                    isLazyLoad: true
                    _select:
                      transform: >-
                        $Countries($.extend.search, $.extend.page,
                        $.extend.limit)
                    _class:
                      transform: >-
                        ($record := $getFieldGroup($.extend.path, $.fields, 0);
                        $addressType := $record.addressType; $country :=
                        $record.country ; $provinceCity := $record.provinceCity
                        ; $district := $record.district; $ward := $record.ward;
                        $addressLine1 := $record.addressLine1; $zipCode :=
                        $record.zipCode; $effectiveDate :=
                        $record.effectiveDate; $isChangedDefaultDate :=
                        $record.isChangedDefaultDate;
                        ($not($isNilorEmpty($effectiveDate)) and
                        $isNilorEmpty($isChangedDefaultDate)) or
                        ($not($isNilorEmpty($addressType)) or
                        $not($isNilorEmpty($country)) or
                        $not($isNilorEmpty($provinceCity)) or
                        $not($isNilorEmpty($district)) or
                        $not($isNilorEmpty($ward)) or
                        $not($isNilorEmpty($addressLine1)) or
                        $not($isNilorEmpty($zipCode)) or
                        $record.samecontactAsEmployee)  ? 'required' :
                        'fakeRequired')
                    _value:
                      transform: >-
                        ($group := $getFieldGroup($.extend.path, $.fields,
                        1);$country := $group.country;$sameContact :=
                        $group.samecontactAsEmployee;$countryApi:=$addressInfo($.extend.addOnValue.id1,
                        $getFieldGroup($.extend.path, $.fields,
                        1).addressType);$sameContact = true ?
                        $exists($countryApi) ? {'label':
                        $countryApi.countryName, 'value': $countryApi.country} :
                        $exists($country) ? {'label':
                        $group.countryName,'value': $group.country})
                  - type: text
                    name: checkChangeCountry
                    unvisible: true
                    value: 'true'
                  - type: select
                    label: Province/City
                    name: provinceCity
                    outputValue: value
                    clearFieldsAfterChange:
                      - district
                      - ward
                    validateDisabled: true
                    _disabled:
                      transform: >-
                        $getFieldGroup($.extend.path, $.fields,
                        1).samecontactAsEmployee
                    placeholder: Select Province/City
                    _select:
                      transform: >-
                        $exists($getFieldGroup($.extend.path, $.fields,
                        1).country) ? $Provinces($getFieldGroup($.extend.path,
                        $.fields, 1).country)
                    _class:
                      transform: >-
                        ($record := $getFieldGroup($.extend.path, $.fields, 0);
                        $addressType := $record.addressType; $country :=
                        $record.country ; $provinceCity := $record.provinceCity
                        ; $district := $record.district; $ward := $record.ward;
                        $addressLine1 := $record.addressLine1; $zipCode :=
                        $record.zipCode; $effectiveDate :=
                        $record.effectiveDate; $isChangedDefaultDate :=
                        $record.isChangedDefaultDate;
                        ($not($isNilorEmpty($effectiveDate)) and
                        $isNilorEmpty($isChangedDefaultDate)) or
                        ($not($isNilorEmpty($addressType)) or
                        $not($isNilorEmpty($country)) or
                        $not($isNilorEmpty($provinceCity)) or
                        $not($isNilorEmpty($district)) or
                        $not($isNilorEmpty($ward)) or
                        $not($isNilorEmpty($addressLine1)) or
                        $not($isNilorEmpty($zipCode)) or
                        $record.samecontactAsEmployee)  ? 'required' :
                        'fakeRequired')
                    _value:
                      transform: >-
                        ($group := $getFieldGroup($.extend.path, $.fields,
                        1);$checkCountry:=$group.checkChangeCountry;$province :=
                        $group.provinceCity;$sameContact :=
                        $group.samecontactAsEmployee;$provinceApi :=
                        $addressInfo($.extend.addOnValue.id1,
                        $group.addressType).city;$sameContact = true ?
                        $exists($provinceApi) ? $provinceApi :
                        $not($exists($provinceApi)) ? null : $exists($province)
                        ? $province)
                  - type: select
                    label: District
                    name: district
                    outputValue: value
                    clearFieldsAfterChange:
                      - ward
                    placeholder: Select District
                    _select:
                      transform: >-
                        $exists($getFieldGroup($.extend.path, $.fields,
                        1).provinceCity) ?
                        $Districts($getFieldGroup($.extend.path, $.fields,
                        1).provinceCity)
                    _disabled:
                      transform: >-
                        $getFieldGroup($.extend.path, $.fields,
                        1).samecontactAsEmployee
                    _value:
                      transform: >-
                        ($group:=$getFieldGroup($.extend.path,$.fields,1);$checkCountry:=$group.checkChangeCountry;$district:=$group.district;$sameContact:=$group.samecontactAsEmployee;$districtApi:=$addressInfo($.extend.addOnValue.id1,$group.addressType).district;$sameContact
                        = true ? $exists($districtApi) ? $districtApi :
                        $not($exists($districtApi)) ? null : $exists($district)
                        ? $district)
                  - type: select
                    label: Ward
                    name: ward
                    outputValue: value
                    placeholder: Select Ward
                    _select:
                      transform: >-
                        $exists($getFieldGroup($.extend.path, $.fields,
                        1).district) ? $Wards($getFieldGroup($.extend.path,
                        $.fields, 1).district)
                    _disabled:
                      transform: >-
                        $getFieldGroup($.extend.path, $.fields,
                        1).samecontactAsEmployee
                    _value:
                      transform: >-
                        ($group:=$getFieldGroup($.extend.path,$.fields,1);$checkCountry:=$group.checkChangeCountry;$ward:=$group.ward;$sameContact:=$group.samecontactAsEmployee;$wardApi:=$addressInfo($.extend.addOnValue.id1,$group.addressType).ward;$sameContact
                        = true ? $exists($wardApi) ? $wardApi :
                        $not($exists($wardApi)) ? null : $exists($ward) ? $ward)
              - type: textarea
                label: Address
                name: addressLine1
                placeholder: Enter Address
                _disabled:
                  transform: >-
                    $getFieldGroup($.extend.path, $.fields,
                    1).samecontactAsEmployee
                textarea:
                  autoSize:
                    minRows: 3
                  maxCharCount: 1000
                  validators:
                    type: maxLength
                    args: 1000
                    text: Maximum 1000 characters
                _condition:
                  transform: $not($.extend.formType = 'view')
                _value:
                  transform: >-
                    ($group:=$getFieldGroup($.extend.path,$.fields,1);$checkCountry:=$group.checkChangeCountry;$address:=$group.addressLine1;$sameContact:=$group.samecontactAsEmployee;$addressApi:=$addressInfo($.extend.addOnValue.id1,$group.addressType).address;$sameContact
                    = true ? $exists($addressApi) ? $addressApi :
                    $not($exists($addressApi)) ? ' ' : $exists($address) ?
                    $address)
              - type: textarea
                label: Address
                name: addressLine1
                _condition:
                  transform: $.extend.formType = 'view'
              - type: text
                label: ZIP Code
                name: zipCode
                placeholder: Enter ZIP Code
                _disabled:
                  transform: >-
                    $getFieldGroup($.extend.path, $.fields,
                    1).samecontactAsEmployee
                _condition:
                  transform: $not($.extend.formType = 'view')
                validators:
                  - type: maxLength
                    args: '40'
                    text: Maximum 40 characters
                _value:
                  transform: >-
                    ($group:=$getFieldGroup($.extend.path,$.fields,1);$checkCountry:=$group.checkChangeCountry;$zipCode:=$group.zipCode;$sameContact:=$group.samecontactAsEmployee;$zipCodeApi:=$addressInfo($.extend.addOnValue.id1,$group.addressType).zipCode;$sameContact
                    = true ? $exists($zipCodeApi) ? $zipCodeApi :
                    $not($exists($zipCodeApi)) ? '_setValueNull' :
                    $exists($zipCode) ? $zipCode)
              - type: text
                label: ZIP Code
                name: zipCode
                _condition:
                  transform: $.extend.formType = 'view'
    - type: group
      label: Email
      styling_not_form_view:
        fieldGroupContentStyle:
          paddingTop: 0px
          gap: 0
      fieldGroupContentStyle:
        paddingTop: 12px
      space: 0
      fields:
        - type: array
          space: 12
          name: email
          _size:
            transform: $.extend.formType = 'create' ? 1
          minSize: 1
          arrayOptions:
            canChangeSize: true
            add_btn_type: secondary
            add_btn_size: small
            uniqueField: isPrimary
          field:
            type: group
            _label:
              transform: '''Email '' & $string($.extend.path[-1] + 1)'
            _unvisible:
              transform: >-
                $.extend.formType = 'view' and $map($.fields.email,
                function($item) { $not($exists($item.emailType)) and
                $not($exists($item.emailAddress))})
            collapse: false
            isBorderTopNone: true
            styling_not_form_view:
              fieldGroupContentStyle:
                padding: 20px
                paddingBottom: 0px
              fieldGroupTitleStyle:
                padding: 10px 12px
                fontSize: 14px
              fieldBackground: '#fff'
              borderRadius: 8px
              border: '1px solid #DFE3E8'
              borderBottomLabel: true
              lastGroupStyleOff: true
              isHideArrow: false
            _n_cols:
              transform: '$not($.extend.formType = ''view'') ? 2 : 1'
            fields:
              - type: select
                label: Email Type
                name: emailType
                outputValue: value
                placeholder: Select Email Type
                _select:
                  transform: $EmailTypes()
              - type: email
                label: Email Address
                name: emailAddress
                placeholder: <EMAIL>
              - type: checkbox
                label: Is Primary
                name: isPrimary
                hiddenLabel: true
                value: false
                _condition:
                  transform: $not($.extend.formType = 'view')
              - type: checkbox
                label: Is Primary
                name: isPrimary
                value: false
                _condition:
                  transform: $.extend.formType = 'view'
    - type: group
      label: Job
      styling_not_form_view:
        fieldGroupContentStyle:
          paddingTop: 0px
          gap: 0
      fieldGroupContentStyle:
        paddingTop: 12px
      space: 0
      fields:
        - type: array
          space: 12
          name: job
          _size:
            transform: $.extend.formType = 'create' ? 1
          minSize: 1
          arrayOptions:
            canChangeSize: true
            add_btn_type: secondary
            add_btn_size: small
          field:
            type: group
            _label:
              transform: '''Job '' & $string($.extend.path[-1] + 1)'
            _unvisible:
              transform: >-
                $.extend.formType = 'view' and $map($.fields.job,
                function($item) { $not($exists($item.effectiveDate)) and
                $not($exists($item.employeeId)) and $not($exists($item.job)) and
                $not($exists($item.position)) and $not($exists($item.company))
                and $not($exists($item.workStatusCode))})
            collapse: false
            isBorderTopNone: true
            styling_not_form_view:
              fieldGroupContentStyle:
                padding: 20px
                paddingBottom: 0px
              fieldGroupTitleStyle:
                padding: 10px 12px
                fontSize: 14px
              fieldBackground: '#fff'
              borderRadius: 8px
              border: '1px solid #DFE3E8'
              borderBottomLabel: true
              lastGroupStyleOff: true
              isHideArrow: false
            _n_cols:
              transform: '$not($.extend.formType = ''view'') ? 2 : 1'
            fields:
              - type: dateRange
                mode: date-picker
                label: Effective Date
                name: effectiveDate
                _value:
                  transform: >-
                    ($emp := $getFieldGroup($.extend.path, $.fields,
                    1).employeeSlect;$maxEffective :=
                    $.variables._getMaxEffectiveDate;
                    $not($isNilorEmpty($getFieldGroup($.extend.path, $.fields,
                    1).effectiveDate)) ? $getFieldGroup($.extend.path, $.fields,
                    1).effectiveDate : $not($isNilorEmpty($emp)) and
                    $not($isNilorEmpty($emp.jobDataEffectiveDateFrom)) ?
                    $emp.jobDataEffectiveDateFrom : $not($isNilorEmpty($emp))
                    and $not($exists($emp.jobDataEffectiveDateFrom)) ?
                    '_setValueNull' :
                    $exists($maxEffective.value.jobDataEffectiveDateFrom) ?
                    $maxEffective.value.jobDataEffectiveDateFrom :
                    '_setValueNull')
                _condition:
                  transform: $not($.extend.formType = 'view')
              - type: dateRange
                mode: date-picker
                label: Effective Date
                name: effectiveDate
                _condition:
                  transform: $.extend.formType = 'view'
              - type: select
                label: Employee ID
                name: employeeSlect
                outputValue: value
                placeholder: Select Employee ID
                _select:
                  transform: $.variables._getListPerson
                _value:
                  transform: >-
                    ($emp := $getFieldGroup($.extend.path, $.fields,
                    1).employeeSlect;$emp2 := $getFieldGroup($.extend.path,
                    $.fields, 1).employeeSlect2.value;$checkChangeJob :=
                    $getFieldGroup($.extend.path, $.fields,
                    1).checkChangeJob;$maxEffectiveDate :=
                    $.variables._getMaxEffectiveDate.value;$emp2 and
                    $not($checkChangeJob = null) ? $emp2 :
                    $not($isNilorEmpty($emp)) ? $emp : $not($isNilorEmpty($emp))
                    and $not($exists($emp)) ? '_setSelectValueNull' :
                    $exists($maxEffectiveDate) ? $maxEffectiveDate :
                    '_setSelectValueNull')
                _condition:
                  transform: $not($.extend.formType = 'view')
                clearFieldsAfterChange:
                  - checkChangeJob
                  - effectiveDate
                  - employeeRecordNumber
                  - employeeId1
                  - job
                  - company
                  - position
                handleAfterChange:
                  dataSource:
                    transform: '{data:false}'
                  valueMapping:
                    - field: workStatusCode
                      fieldValue: data
              - type: select
                label: Employee ID
                name: employeeSlect2
                outputValue: value
                unvisible: true
                placeholder: Select Employee ID
                _select:
                  transform: $.variables._getListPerson
                _value:
                  transform: >-
                    $filter($.variables._getListPerson, function($v) {
                    $count($.variables._getListPerson[value.employeeRecordNumber
                    = $v.value.employeeRecordNumber]) > 1 ? ($v.value.employeeId
                    = $getFieldGroup($.extend.path, $.fields, 1).employeeId1) :
                    ($v.value.employeeRecordNumber =
                    $getFieldGroup($.extend.path, $.fields,
                    1).employeeRecordNumber)})
              - type: text
                label: Employee ID
                name: employeeId
                _condition:
                  transform: $.extend.formType = 'view'
              - type: text
                label: Employee Record Number
                name: employeeRecordNumber
                unvisible: true
                _value:
                  transform: >-
                    ($emp := $getFieldGroup($.extend.path, $.fields,
                    1).employeeSlect;$maxEffective :=
                    $.variables._getMaxEffectiveDate;
                    $getFieldGroup($.extend.path, $.fields,
                    1).employeeRecordNumber ? $getFieldGroup($.extend.path,
                    $.fields, 1).employeeRecordNumber :
                    $not($isNilorEmpty($emp)) and
                    $not($isNilorEmpty($emp.employeeRecordNumber)) ?
                    $emp.employeeRecordNumber : $not($isNilorEmpty($emp)) and
                    $not($exists($emp.employeeRecordNumber)) ? '_setValueNull' :
                    $exists($maxEffective.value.employeeRecordNumber) ?
                    $maxEffective.value.employeeRecordNumber : '_setValueNull')
              - type: text
                label: Employee ID
                name: employeeId1
                unvisible: true
                _value:
                  transform: >-
                    ($emp := $getFieldGroup($.extend.path, $.fields,
                    1).employeeSlect;$maxEffective :=
                    $.variables._getMaxEffectiveDate;
                    $getFieldGroup($.extend.path, $.fields, 1).employeeId1 ?
                    $getFieldGroup($.extend.path, $.fields, 1).employeeId1 :
                    $not($isNilorEmpty($emp)) and
                    $not($isNilorEmpty($emp.employeeId)) ? $emp.employeeId :
                    $not($isNilorEmpty($emp)) and $not($exists($emp.employeeId))
                    ? '_setValueNull' : $exists($maxEffective.value.employeeId)
                    ? $maxEffective.value.employeeId : '_setValueNull')
              - type: text
                name: checkChangeJob
                unvisible: true
                value: 'true'
              - type: text
                label: Job
                name: job
                placeholder: Enter Job
                _value:
                  transform: >-
                    ($emp := $getFieldGroup($.extend.path, $.fields,
                    1).employeeSlect;$maxEffective :=
                    $.variables._getMaxEffectiveDate;
                    $getFieldGroup($.extend.path, $.fields, 1).job ?
                    $getFieldGroup($.extend.path, $.fields, 1).job :
                    $not($isNilorEmpty($emp)) and
                    $not($isNilorEmpty($emp.jobName)) ? $emp.jobName :
                    $not($isNilorEmpty($emp)) and $not($exists($emp.jobName)) ?
                    '_setValueNull' : $exists($maxEffective.value.jobName) ?
                    $maxEffective.value.jobName : '_setValueNull')
                _condition:
                  transform: $not($.extend.formType = 'view')
              - type: text
                label: Job
                name: job
                _condition:
                  transform: $.extend.formType = 'view'
              - type: text
                label: Position
                name: position
                placeholder: Enter Position
                _value:
                  transform: >-
                    ($emp := $getFieldGroup($.extend.path, $.fields,
                    1).employeeSlect;$maxEffective :=
                    $.variables._getMaxEffectiveDate;
                    $getFieldGroup($.extend.path, $.fields, 1).position ?
                    $getFieldGroup($.extend.path, $.fields, 1).position :
                    $not($isNilorEmpty($emp)) and
                    $not($isNilorEmpty($emp.positionName)) ? $emp.positionName :
                    $not($isNilorEmpty($emp)) and
                    $not($exists($emp.positionName)) ? '_setValueNull' :
                    $exists($.variables._getMaxEffectiveDate.value.positionName)
                    ? $.variables._getMaxEffectiveDate.value.positionName :
                    '_setValueNull')
                _condition:
                  transform: $not($.extend.formType = 'view')
              - type: text
                label: Position
                name: position
                _condition:
                  transform: $.extend.formType = 'view'
              - type: text
                label: Company
                name: company
                placeholder: Enter Company
                _value:
                  transform: >-
                    ($emp := $getFieldGroup($.extend.path, $.fields,
                    1).employeeSlect;$maxEffective :=
                    $.variables._getMaxEffectiveDate;
                    $getFieldGroup($.extend.path, $.fields, 1).company ?
                    $getFieldGroup($.extend.path, $.fields, 1).company :
                    $not($isNilorEmpty($emp)) and
                    $not($isNilorEmpty($emp.companyName)) ? $emp.companyName :
                    $not($isNilorEmpty($emp)) and
                    $not($exists($emp.companyName)) ? '_setValueNull' :
                    $exists($maxEffective.value.companyName) ?
                    $maxEffective.value.companyName : '_setValueNull')
                _condition:
                  transform: $not($.extend.formType = 'view')
              - type: text
                label: Company
                name: company
                _condition:
                  transform: $.extend.formType = 'view'
              - type: radio
                label: Working Status
                name: workStatusCode
                outputValue: value
                placeholder: Select Working Status
                radio:
                  - value: WS_Active
                    label: Active
                  - value: WS_Inactive
                    label: Inactive
                _value:
                  transform: >-
                    ($emp := $getFieldGroup($.extend.path, $.fields,
                    1).employeeSlect;$maxEffective :=
                    $.variables._getMaxEffectiveDate;$getFieldGroup($.extend.path,
                    $.fields, 1).workStatusCode and
                    $not($getFieldGroup($.extend.path, $.fields,
                    1).checkChangeJob = null) ? $getFieldGroup($.extend.path,
                    $.fields, 1).workStatusCode : $not($isNilorEmpty($emp)) and
                    $not($isNilorEmpty($emp.hrStatus)) and $emp.hrStatus = 'A' ?
                    'WS_Active' : ($not($isNilorEmpty($emp)) and
                    $not($isNilorEmpty($emp.hrStatus)) and $emp.hrStatus = 'I')
                    ? 'WS_Inactive' : $exists($maxEffective) and
                    $maxEffective.value.hrStatus = 'A' ? 'WS_Active' :
                    $exists($maxEffective) and  $maxEffective.value.hrStatus =
                    'I' ? 'WS_Inactive')
  footer:
    create: false
    update: true
    createdOn: createdOn
    updatedOn: updatedAt
    createdBy: createdBy
    updatedBy: updatedBy
  historyTitle: $.fullName
  historyDescription: >-
    $.relationshipName & ' - ' & ($.isDependent = 'Y' ? 'Là người phụ thuộc' :
    'Không là người phụ thuộc')
  _mode:
    transform: '$.extend.formType = ''view'' ? ''tabset'' : ''mark-scroll'''
  sources:
    searchPerson:
      uri: '"/api/personals"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,
        'filter':[{'field':'fullName','operator':'$eq','value':$.fullName},{'field':'pitCode','operator':'$eq','value':$.taxCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data,function($item){{'label':$item.fullName & ' - ' &
        $item.employeeId & ' - ' & $item.companyName & ' - ' & $item.jobName,
        'value':$item}})[]
      disabledCache: true
      params:
        - fullName
        - taxCode
        - limit
    getBasicInformation:
      uri: '"/api/personals/" & $.employeeId'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $
      disabledCache: true
      params:
        - employeeId
    checkDuplicateDependent:
      uri: >-
        "/api/personals/" & $.empId &
        "/family-infos/check-duplicate-family-dependent"
      method: POST
      queryTransform: ''
      bodyTransform: >-
        {'code': $.taxCode, 'familyCode': $.familyCode,'dependentDateFrom':
        $.dependentFrom, 'dependentDateTo': $.dependentTo}
      headerTransform: ''
      resultTransform: $
      disabledCache: true
      params:
        - empId
        - taxCode
        - familyCode
        - dependentFrom
        - dependentTo
    phoneInfo:
      uri: '"/api/personals/" & $.empId & "/phone-contacts"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($filter($, function($v, $i, $a) { $v.isPrimary = true }),
        function($item) { {'label': $item.phoneNumber, 'value':
        $item.phoneNumber} })
      disabledCache: true
      params:
        - empId
    addressInfo:
      uri: '"/api/personals/" & $.empId & "/personal-addresses/histories/"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'addressTypeCode','operator':
        '$eq','value':$.addressType}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $[0]
      disabledCache: true
      params:
        - empId
        - addressType
    Genders:
      uri: '"/api/picklists/GENDER/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
    CountriesNotPagination:
      uri: '"/api/picklists/COUNTRIES/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
    Countries:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      queryTransform: '{''search'': $.search,''page'': $.page,''limit'': $.limit}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - search
        - page
        - limit
    CountriesByCode:
      uri: '"/api/picklists/COUNTRIES/values"'
      method: GET
      queryTransform: '{''filter'': [{''field'':''code'',''operator'': ''$eq'',''value'':$.code}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $.data
      disabledCache: true
      params:
        - code
    Nationalities:
      uri: '"/api/picklists/NATIONALITY/values/pagination"'
      method: GET
      queryTransform: '{''search'': $.search,''page'': $.page,''limit'': $.limit}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - search
        - page
        - limit
    NationalitiesByCode:
      uri: '"/api/picklists/NATIONALITY/values"'
      method: GET
      queryTransform: '{''filter'': [{''field'':''code'',''operator'': ''$eq'',''value'':$.code}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $.data
      disabledCache: true
      params:
        - code
    Relationships:
      uri: '"/api/picklists/FAMILYRELATIONSHIP/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
    IdentityDocumentType:
      uri: '"/api/picklists/IDENTIFICATION/values/pagination/"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'codE501','operator': '$eq','value':$.nationality}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - nationality
    Provinces:
      uri: '"/api/picklists/PROVINCE/values/" & $.country & ""'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      params:
        - country
      disabledCache: true
    Districts:
      uri: '"/api/picklists/DISTRICT/values/" & $.provinceCity & ""'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      params:
        - provinceCity
      disabledCache: true
    Wards:
      uri: '"/api/picklists/WARDS/values/" & $.district & ""'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      params:
        - district
      disabledCache: true
    AddressTypes:
      uri: '"/api/picklists/ADDRESSTYPE/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
    EmailTypes:
      uri: '"/api/picklists/EMAILTYPE/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
    PhoneTypes:
      uri: '"/api/picklists/PHONETYPE/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
    workStatusList:
      uri: '"/api/picklists/WORKINGSTATUS/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
    Companies:
      uri: '"/api/companies/get-by"'
      method: GET
      queryTransform: >-
        {'search': $.search,'page': $.page,'limit': $.limit,'filter':
        [{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id}})[]
      disabledCache: true
      params:
        - search
        - page
        - limit
        - effectiveDate
    LegalEntity:
      uri: '"/api/legal-entities/get-list"'
      queryTransform: >-
        {'filter':[{'field':'status','operator':'$eq','value':true},{'field':'companyCode','operator':'$eq','value':$.companyCode},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($,function($item){{'label':$item.longName.default&'('&$item.code&')','value':$item.code,'id':$item.id}})[]
      disabledCache: true
      params:
        - companyCode
        - effectiveDate
  variables:
    _selectedBasicInfo:
      transform: >-
        $reduce($.fields.basicsInfo, function($acc, $current) {
        ($exists($current.effectiveDate) ? $string($current.effectiveDate): '' )
        > ($exists($acc.effectiveDate)  ?  $string($acc.effectiveDate) : '' ) ?
        $current : $acc})
    _selectedDependent:
      transform: >-
        $reduce($.fields.biographies, function($acc, $current) {
        ($exists($current.effectiveDate) ? $string($current.effectiveDate): '' )
        > ($exists($acc.effectiveDate)  ?  $string($acc.effectiveDate) : '' ) ?
        $current : $acc})
    _selectedNationalId:
      transform: >-
        $reduce($.fields.nationalId, function($acc, $current) {
        ($exists($current.effectiveDate) ? $string($current.effectiveDate): '' )
        > ($exists($acc.effectiveDate)  ?  $string($acc.effectiveDate) : '' ) ?
        $current : $acc})
    _phoneInfo:
      transform: $.extend.addOnValue.id1 ? $phoneInfo($.extend.addOnValue.id1)
    _getListPerson:
      transform: >-
        ($.variables._createFullName and $.variables._selectedDependent.taxCode)
        ? $searchPerson($.variables._createFullName,
        $.variables._selectedDependent.taxCode, 100)
    _getJobCodePrimary:
      transform: >-
        $filter($.variables._getListPerson, function($item) {
        $item.value.jobIndicatorCode = 'P' })
    _getJobCodeSecondary:
      transform: >-
        $filter($.variables._getListPerson, function( $item) {
        $item.value.jobIndicatorCode = 'S' })
    _getMaxEffectiveDate:
      transform: >-
        ($primary := $.variables._getJobCodePrimary;$secondary :=
        $.variables._getJobCodeSecondary;$getLatest := function($data)
        {$filter($data, function($item) {
        $toMillis($item.value.jobDataEffectiveDateFrom) = $max($map($data,
        function($i) { $toMillis($i.value.jobDataEffectiveDateFrom) }))
        })[0]};$count($primary) > 0 ? $getLatest($primary) :
        $getLatest($secondary))
    _createFullName:
      transform: >-
        ($not($isNilorEmpty($.variables._selectedBasicInfo.lastName)) ?
        $trim($.variables._selectedBasicInfo.lastName) & ' ' : '') &
        ($not($isNilorEmpty($.variables._selectedBasicInfo.middleName)) ?
        $trim($.variables._selectedBasicInfo.middleName) & ' ' : '') &
        $trim($.variables._selectedBasicInfo.firstName)
filter_config: {}
layout_options:
  show_table_search: false
  show_table_filter: false
  show_dialog_form_save_add_button: true
  show_table_checkbox: false
  paddingTab: false
  n_cols: 2
  history_widget_header_options:
    duplicate: false
  show_history_insert_button: false
  hide_action_row: true
  custom_delete_backend_url: /api/personals/:empId/family-infos/:code
  show_table_pagination: false
  show_tool_table: false
  history_dialog_options:
    sidebar:
      header:
        visible: false
  is_layout_widget: true
  widget_options:
    show_more_type: hidden
    is_full_height: true
  is_new_dynamic_form: true
  custom_rowId_for_view_detail: code
layout_options__header_buttons:
  - id: create
    icon: icon-plus
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions: null
backend_url: api/personals/:id1/family-infos
screen_name: null
layout_options__actions_many: null
parent: HR.FS.FR.000
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: code
default_filter: null
pre_condition_config: {}
permission_key: null
screen_designs: []
function_specs: []
fields: []
children: []
menu_item: null
