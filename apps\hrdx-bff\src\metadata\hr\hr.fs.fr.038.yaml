id: HR.FS.FR.038
status: draft
sort: 351
user_created: 56e71b91-dd39-4ebc-afd1-b4f00f38d4a5
date_created: '2024-08-28T06:46:24.854Z'
user_updated: 56e71b91-dd39-4ebc-afd1-b4f00f38d4a5
date_updated: '2025-07-28T04:01:32.478Z'
title: Rehire person
requirement:
  time: 1748251223656
  blocks:
    - id: aGbI4u9u6Q
      type: paragraph
      data:
        text: >-
          Chứ<PERSON> năng cho phép bộ phận nhân sự Tập đoàn bật/CTTV tái tuyển dụng
          các nhân viên đã nghỉ việc (Inactive) thuộc vùng dữ liệu được phân
          quyền của user
  version: 2.30.7
screen_design: null
module: HR
local_fields: []
mock_data: null
local_buttons: null
layout: layout-form
form_config:
  _mode:
    transform: $not($.extend.formType = 'proceed') ? 'mark-scroll'
  isFilterRight: true
  formSize:
    create: large
    edit: large
    proceed: large
  fields:
    - type: group
      label: Organizational Instance
      n_cols: 2
      collapse: false
      disableEventCollapse: true
      fields:
        - name: employeeID
          label: Employee ID
          type: text
          unvisible: true
        - name: employmentData
          label: employment
          type: text
          unvisible: true
        - name: employee
          label: Employee
          type: text
          unvisible: true
        - name: rehireDate
          type: dateRange
          placeholder: dd/MM/yyyy
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
          unvisible: true
        - name: organizationalInstanceRecord
          label: organizationalInstanceRecord
          type: number
          unvisible: true
        - name: employeeRecordNumber
          label: employeeRecordNumber
          type: number
          unvisible: true
        - name: actionCode
          label: actionCode
          type: text
          unvisible: true
        - name: companyObj
          label: companyObj
          type: text
          unvisible: true
        - name: employeeGroupCode
          label: employeeGroupCode
          type: text
          unvisible: true
        - name: previousJobData
          label: previousJobData
          type: text
          unvisible: true
        - label: Organizational Instance Rcd
          type: text
          name: organizationalInstanceRcd
          disabled: true
          _value:
            transform: >-
              $.fields.organizationalInstanceRecord ?
              $string($.fields.organizationalInstanceRecord) : '0'
        - name: employeeRecordNumber1
          label: Employee Number Record
          type: number
          disabled: true
          _value:
            transform: $.fields.employeeRecordNumber
    - type: group
      label: Action
      collapse: false
      n_cols: 2
      fields:
        - name: effectiveDate1
          label: Effective Date
          type: dateRange
          mode: date-picker
          disabled: true
          validators:
            - type: required
          _value:
            transform: >-
              $exists($.fields.rehireDate) ? $DateFormat($.fields.rehireDate,
              'YYYY/MM/DD') : $now()
        - name: employeeGroupCode1
          label: Employee Group
          type: select
          outputValue: value
          disabled: true
          validators:
            - type: required
          _select:
            transform: >-
              $exists($.fields.rehireDate) ?
              $employeeGroupList($.fields.rehireDate)
          _value:
            transform: $.fields.employeeGroupCode
          placeholder: Select Employee Group
        - name: actionCode1
          label: Action
          type: select
          placeholder: Select Action
          outputValue: value
          clearFieldsAfterChange:
            - actionReasonCode1
          validators:
            - type: required
          disabled: true
          _select:
            transform: $.variables._actionsList
          _value:
            transform: $.fields.actionCode
        - name: jobIndicator
          label: Job Indicator
          type: select
          placeholder: Select Job Indicator
          validators:
            - type: required
          value: P
          outputValue: value
          _select:
            transform: >-
              $exists($.fields.rehireDate) ?
              $jobIndicatorList($.fields.rehireDate)
        - name: actionReasonCode1
          label: Reason
          type: select
          placeholder: Select Reason
          outputValue: value
          _select:
            transform: >-
              (  $selectdAction:= $.variables._selectedAction; $actionReasons :=
              $selectdAction.actionReasons ; $exists($actionReasons) and
              $count($actionReasons) > 0 ?
              $actionReasonList($actionReasons,$.fields.effectiveDate,$count($actionReasons))
              ) 
          validators:
            - type: required
        - name: employeeSubGroupCode1
          label: Employee Sub Group
          type: select
          outputValue: value
          isLazyLoad: true
          placeholder: Select Employee Sub Group
          validators:
            - type: required
          _select:
            transform: >-
              $.fields.employeeGroupCode ?
              $employeeSubGroupsList($.fields.employeeGroupCode,
              $.fields.rehireDate)
        - name: effectiveSequence1
          label: Effective Sequence
          type: number
          disabled: true
          value: 0
          _value:
            transform: ' ($effectiveSequence := $.fields.jobData.effectiveSequence ;$exists($.variables._getHistorySameEffectiveDate) ? ($newSequence := $.variables._getHistorySameEffectiveDate.effectiveSequence + 1; $effectiveSequence < $newSequence ? $newSequence : $effectiveSequence) : 0)'
          validators:
            - type: required
        - type: select
          label: Manager?
          name: isManagerCode1
          outputValue: value
          placeholder: Select Manager?
          isLazyLoad: true
          _select:
            transform: $exists($.fields.rehireDate) ? $managerList($.fields.rehireDate)
          validators:
            - type: required
        - name: hrStatusCode1
          label: HR Status
          type: radio
          outputValue: value
          validators:
            - type: required
          disabled: true
          value: A
          _radio:
            transform: $exists($.fields.rehireDate) ? $hrStatusList($.fields.rehireDate)
          placeholder: Select HR Status
        - name: prStatusCode1
          label: Payroll Status
          type: select
          outputValue: value
          validators:
            - type: required
          _value:
            transform: >-
              $.variables._selectedAction.prStatusCode ?
              $.variables._selectedAction.prStatusCode
          _select:
            transform: $exists($.fields.rehireDate) ? $prStatusList($.fields.rehireDate)
          placeholder: Select Payroll Status
        - name: levelDecisionCode1
          label: Level Of Decision
          type: select
          outputValue: value
          placeholder: Select Level Of Decision
          isLazyLoad: true
          _select:
            transform: >-
              $exists($.fields.rehireDate) ?
              $levelOfDecisionList($.fields.rehireDate)
        - type: text
          label: Decision number
          name: decisionNumber1
          placeholder: Enter Decision Number
        - type: dateRange
          name: signDate1
          label: Sign Date
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
        - type: dateRange
          name: expectedEndDate1
          label: Expected End Date
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
          validators:
            - type: ppx-custom
              args:
                transform: >-
                  $exists($.fields.expectedEndDate1) ?
                  $DateDiff($DateFormat($.fields.rehireDate, 'yyyy-MM-DD'),
                  $DateFormat($.fields.expectedEndDate1, 'yyyy-MM-DD'), 'd') > 0
              text: >-
                The Expected End Date must be more than or equal to the
                Effective Date
        - name: note1
          label: Note
          type: textarea
          placeholder: Enter Note
          col: 2
          validators:
            - type: maxLength
              args: '1000'
              text: Maximum length is 1000 characters.
          textarea:
            autoSize:
              minRows: 3
            maxCharCount: 1000
    - type: group
      label: Assignment
      n_cols: 2
      collapse: false
      name: jobData
      fields:
        - type: text
          label: Employee Number Record
          name: employeeRecordNumber
          unvisible: true
          _value:
            transform: >-
              $.fields.employeeRecordNumber ?
              $string($.fields.employeeRecordNumber) : '0'
        - type: text
          name: employeeId
          unvisible: true
          _value:
            transform: $.fields.employeeID
        - type: text
          name: processType
          unvisible: true
          value: '1'
        - name: previousJobDataId
          label: previousJobDataId
          type: text
          unvisible: true
          _value:
            transform: $.fields.previousJobData.id
        - label: Organizational Instance Rcd
          type: text
          name: organizationalInstanceRcd
          unvisible: true
          _value:
            transform: >-
              $.fields.organizationalInstanceRecord ?
              $string($.fields.organizationalInstanceRecord) : '0'
        - type: dateRange
          mode: date-picker
          label: Effective Date
          name: effectiveDate
          unvisible: true
          _value:
            transform: >-
              $exists($.fields.rehireDate) ? $DateFormat($.fields.rehireDate,
              'YYYY/MM/DD') : $now()
        - name: jobIndicatorCode
          label: jobIndicatorCode
          type: text
          _value:
            transform: >-
              $exists($.fields.jobIndicator) ? $.fields.jobIndicator :
              '_setValueNull'
          unvisible: true
        - name: actionCode
          label: Action
          type: text
          unvisible: true
          _value:
            transform: >-
              $exists($.fields.actionCode) ? $.fields.actionCode :
              '_setValueNull'
        - name: actionReasonCode
          label: Reason
          type: text
          placeholder: Select Reason
          unvisible: true
          _value:
            transform: >-
              $exists($.fields.actionReasonCode1) ? $.fields.actionReasonCode1 :
              '_setValueNull'
        - name: effectiveSequence
          label: Effective Sequence
          type: number
          unvisible: true
          value: 0
          _value:
            transform: ' ($effectiveSequence := $.fields.jobData.effectiveSequence ;$exists($.variables._getHistorySameEffectiveDate) ? ($newSequence := $.variables._getHistorySameEffectiveDate.effectiveSequence + 1; $effectiveSequence < $newSequence ? $newSequence : $effectiveSequence) : 0)'
        - name: hrStatusCode
          label: hrStatusCode
          type: text
          unvisible: true
          value: A
        - name: isPrimary
          label: HR Status
          type: text
          unvisible: true
          value: true
        - name: prStatusCode
          label: prStatusCode
          type: text
          unvisible: true
          _value:
            transform: >-
              $exists($.fields.prStatusCode1) ? $.fields.prStatusCode1 :
              '_setValueNull'
        - type: text
          name: employeeGroupCode
          unvisible: true
          _value:
            transform: $.fields.employeeGroupCode
        - name: employeeSubGroupCode
          label: Employee Sub Group
          type: text
          unvisible: true
          _value:
            transform: $.fields.employeeSubGroupCode1
        - type: text
          label: Manager?
          name: isManagerCode
          unvisible: true
          _value:
            transform: >-
              $exists($.fields.isManagerCode1) ? $.fields.isManagerCode1 :
              '_setValueNull'
        - name: levelDecisionCode
          label: Level Of Decision
          type: text
          unvisible: true
          _value:
            transform: >-
              $exists($.fields.levelDecisionCode1) ? $.fields.levelDecisionCode1
              : '_setValueNull'
        - type: text
          label: Decision number
          name: decisionNumber
          unvisible: true
          _value:
            transform: >-
              $exists($.fields.decisionNumber1) ? $.fields.decisionNumber1 :
              '_setValueNull'
        - type: text
          name: signDate
          label: Sign Date
          unvisible: true
          _value:
            transform: '$exists($.fields.signDate1) ? $.fields.signDate1 : ''_setValueNull'''
        - type: text
          name: expectedEndDate
          label: Sign Date
          unvisible: true
          _value:
            transform: >-
              $exists($.fields.expectedEndDate1) ? $.fields.expectedEndDate1 :
              '_setValueNull'
        - type: text
          name: note
          unvisible: true
          _value:
            transform: '$exists($.fields.note1) ? $.fields.note1 : ''_setValueNull'''
        - type: select
          label: Position
          name: positionObj
          placeholder: Select Position
          isLazyLoad: true
          options:
            enabledLoadMore: false
          clearFieldsAfterChange:
            - positionCode
          handleAfterChange:
            dataSource:
              transform: >-
                $organizationPick('Position', $.fieldValue.value,
                $DateToTimestampUTC($.fields.rehireDate))
            valueMapping:
              - field: legalEntityObj
                fieldValue: LegalEntity
              - field: businessUnitObj
                fieldValue: BusinessUnit
                _setNullValue: $isNilorEmpty($.BusinessUnit)
              - field: divisionObj
                fieldValue: Division
                _setNullValue: $isNilorEmpty($.Division)
              - field: departmentObj
                fieldValue: Department
              - field: costCenterObj
                fieldValue: CostCenter
              - field: reportPositionObj
                fieldValue: DirectPosition
              - field: locationObj
                fieldValue: Location
              - field: jobObj
                fieldValue: JobCode
              - field: careerStreamObj
                fieldValue: CareerStream
              - field: careerBandObj
                fieldValue: Band
              - field: regionCode
                fieldValue: Region.value
              - field: businessTitleObj
                fieldValue: BusinessTitle
              - field: supervisorObj
                fieldValue: noName
                _setNullValue: $not($isNilorEmpty($.DirectPosition))
          _select:
            transform: >-
              $positionsList($.fields.companyObj.value,$.fields.rehireDate,$.fields.jobData.legalEntityObj.value,
              $.fields.jobData.departmentObj.value,
              $.fields.jobData.jobObj.value)
        - type: text
          name: positionCode
          label: Position
          unvisible: true
          _value:
            transform: $.fields.jobData.positionObj ? $.fields.jobData.positionObj.value
        - type: select
          label: Cost Center
          name: costCenterObj
          placeholder: Select Cost Center
          isRemoveOptionNotExist: true
          isLazyLoad: true
          _select:
            transform: >-
              $constCenterList($.fields.companyObj.value, $.extend.limit,
              $.extend.page, $.extend.search,$.fields.rehireDate)
        - type: text
          name: costCenterCode
          unvisible: true
          _value:
            transform: >-
              $.fields.jobData.costCenterObj ?
              $.fields.jobData.costCenterObj.value
        - type: select
          name: companyObj
          label: Company
          placeholder: Select Company
          disabled: true
          _value:
            transform: $.fields.companyObj
          _select:
            transform: $append([], $.fields.companyObj)[]
          validators:
            - type: required
        - type: text
          name: companyCode
          unvisible: true
          _value:
            transform: $exists($.fields.companyObj) ? $.fields.companyObj.value
        - name: reportPositionObj
          label: Report To Pos
          type: select
          isLazyLoad: true
          isRemoveOptionNotExist: true
          clearFieldsAfterChange:
            - supervisor
            - supervisorObj
            - reportPosition
          _select:
            transform: >-
              $positionsListGetBy($.fields.rehireDate, $.extend.limit,
              $.extend.page, $.extend.search)
          placeholder: Select Report To Pos
        - type: text
          name: reportPosition
          unvisible: true
          _value:
            transform: >-
              $.fields.jobData.reportPositionObj ?
              $.fields.jobData.reportPositionObj.value : '_setValueNull'
        - type: select
          label: Legal Entity
          name: legalEntityObj
          isRemoveOptionNotExist: true
          isLazyLoad: true
          placeholder: Select Legal Entity
          clearFieldsAfterChange:
            - departmentObj
            - departmentCode
            - positionObj
            - positionCode
            - legalEntityCode
          options:
            enabledLoadMore: false
          _select:
            transform: $legalEntityList( $.fields.companyObj.value,$.fields.rehireDate)
          validators:
            - type: required
        - type: text
          name: legalEntityCode
          unvisible: true
          _value:
            transform: >-
              $.fields.jobData.legalEntityObj ?
              $.fields.jobData.legalEntityObj.value : '_setValueNull'
        - name: supervisorObj
          label: Supervisor
          type: select
          isLazyLoad: true
          isRemoveOptionNotExist: true
          clearFieldsAfterChange:
            - reportPosition
            - reportPositionObj
            - supervisor
          _select:
            transform: >-
              $personalsList($.extend.limit, $.extend.page, $.extend.search,
              $.fields.rehireDate, 'A')
          placeholder: Select Supervisor
        - type: text
          name: supervisor
          unvisible: true
          _value:
            transform: >-
              $.fields.jobData.supervisorObj ?
              $.fields.jobData.supervisorObj.value : '_setValueNull'
        - type: select
          label: Business Unit
          name: businessUnitObj
          isRemoveOptionNotExist: true
          isLazyLoad: true
          options:
            enabledLoadMore: false
          placeholder: Select Business Unit
          clearFieldsAfterChange:
            - divisionObj
            - divisionCode
            - departmentObj
            - departmentCode
            - positionObj
            - positionCode
            - businessUnitCode
          _select:
            transform: $businessUnitList( $.fields.companyObj.value,$.fields.rehireDate)
        - type: text
          name: businessUnitCode
          unvisible: true
          _value:
            transform: >-
              $.fields.jobData.businessUnitObj ?
              $.fields.jobData.businessUnitObj.value : '_setValueNull'
        - name: matrixReportPositionCodes
          label: Matrix Report To
          type: selectAll
          mode: multiple
          isRemoveOptionNotExist: true
          isLazyLoad: true
          outputValue: value
          _value:
            transform: ' $exists($.variables._positionInfo.matrixPositionObj) ? $map($.variables._positionInfo.matrixPositionObj, function($it){{ ''label'': $it.label, ''value'': $it.value.code }})'
          _options:
            transform: >-
              $positionsListGetBy($.fields.rehireDate, $.extend.limit,
              $.extend.page, $.extend.search)
          placeholder: Select Matrix Report To
        - type: select
          label: Division
          name: divisionObj
          placeholder: Select Division
          isLazyLoad: true
          isRemoveOptionNotExist: true
          options:
            enabledLoadMore: false
          handleAfterChange:
            dataSource:
              transform: $divisionDetail($.fieldValue.id)
            valueMapping:
              - field: businessUnitObj
                fieldValue: BusinessUnit
          clearFieldsAfterChange:
            - positionObj
            - divisionCode
            - positionCode
          _select:
            transform: >-
              $divisionsList($.fields.companyObj.value,
              $.fields.jobData.businessUnitObj.value,$.fields.rehireDate)
        - type: text
          name: divisionCode
          unvisible: true
          _value:
            transform: >-
              $.fields.jobData.divisionObj ? $.fields.jobData.divisionObj.value
              : '_setValueNull'
        - name: matrixManager
          label: Matrix Manager
          type: selectAll
          mode: multiple
          outputValue: value
          isLazyLoad: true
          isRemoveOptionNotExist: true
          _options:
            transform: >-
              $personalsList($.extend.limit, $.extend.page, $.extend.search,
              $.fields.rehireDate, 'A')
          placeholder: Select Matrix Manager
        - type: select
          label: Department
          name: departmentObj
          placeholder: Select Department
          isRemoveOptionNotExist: true
          isLazyLoad: true
          options:
            enabledLoadMore: false
          clearFieldsAfterChange:
            - positionObj
            - blockCallApiDepartment
            - departmentCode
            - positionCode
          handleAfterChange:
            dataSource:
              transform: >-
                ($dataObj := {'organizationPickData':
                $organizationPick('Department', $.fieldValue.value,
                $DateToTimestampUTC($.fields.rehireDate)), 'detail':
                $departmentInfo($.fieldValue.id)} ;
                $merge([$dataObj.organizationPickData,{'ManagerPosition':
                $not($dataObj.detail.managerType = 'Position') ? null :
                $dataObj.organizationPickData.ManagerPosition ,'Employee':
                $not($dataObj.detail.managerType = 'Employee') ? null :
                ($exists($dataObj.detail.headOfDepartmentObj.value.code) ?
                {'label': $dataObj.detail.headOfDepartmentObj.label & ' (' &
                $dataObj.detail.headOfDepartmentObj.value.code & ')', 'value':
                $dataObj.detail.headOfDepartmentObj.value.code} :
                $dataObj.organizationPickData.Employee) }]))
            valueMapping:
              - field: legalEntityObj
                fieldValue: LegalEntity
              - field: businessUnitObj
                fieldValue: BusinessUnit
                _setNullValue: $isNilorEmpty($.BusinessUnit)
              - field: divisionObj
                fieldValue: Division
                _setNullValue: $isNilorEmpty($.Division)
              - field: locationObj
                fieldValue: Location
              - field: regionCode
                fieldValue: Region.value
              - field: costCenterObj
                fieldValue: CostCenter
              - field: reportPositionObj
                fieldValue: ManagerPosition
                _setNullValue: $not($isNilorEmpty($.Employee))
              - field: supervisorObj
                fieldValue: Employee
                _setNullValue: $not($isNilorEmpty($.ManagerPosition))
          _select:
            transform: >-
              $departmentsList($.fields.companyObj.value,
              $.fields.jobData.legalEntityObj.value,
              $.fields.jobData.businessUnitObj.value,
              $.fields.jobData.divisionObj.value, $.fields.rehireDate)
          validators:
            - type: required
        - type: text
          label: Department
          name: departmentCode
          _value:
            transform: >-
              $.fields.jobData.departmentObj ?
              $.fields.jobData.departmentObj.value : '_setValueNull'
          unvisible: true
        - name: departmentEntryDate
          label: Department Entry Date
          type: dateRange
          mode: date-picker
          _value:
            transform: >-
              $exists($.fields.rehireDate) ? $DateFormat($.fields.rehireDate,
              'YYYY/MM/DD')
          setting:
            format: dd/MM/yyyy
            type: date
        - type: select
          label: Location
          name: locationObj
          placeholder: Select Location
          isRemoveOptionNotExist: true
          isLazyLoad: true
          clearFieldsAfterChange:
            - locationCode
          _select:
            transform: >-
              $locationsList($.extend.limit, $.extend.page,
              $.extend.search,$.fields.rehireDate, $.fields.companyObj.value)
          validators:
            - type: required
        - type: text
          name: locationCode
          unvisible: true
          _value:
            transform: >-
              $.fields.jobData.locationObj ? $.fields.jobData.locationObj.value
              : '_setValueNull'
        - type: select
          label: Business Title
          name: businessTitleObj
          isRemoveOptionNotExist: true
          isLazyLoad: true
          placeholder: Select Business Title
          _select:
            transform: >-
              $businessTitlesList($.fields.companyObj.value, $.extend.limit,
              $.extend.page, $.extend.search,$.fields.rehireDate)
        - type: text
          name: businessTitleCode
          unvisible: true
          _value:
            transform: >-
              $.fields.jobData.businessTitleObj ?
              $.fields.jobData.businessTitleObj.value : '_setValueNull'
        - type: select
          label: Region
          name: regionCode
          placeholder: Select Region
          isRemoveOptionNotExist: true
          outputValue: value
          _select:
            transform: $regionList($.fields.rehireDate)
        - type: select
          label: Full/Part
          name: fullPartCode
          placeholder: Select Full/Part
          outputValue: value
          _select:
            transform: $exists($.fields.rehireDate) ? $fullPartList($.fields.rehireDate)
          _value:
            transform: '$.variables._getJobdataPrimary = 0  ? ''F'' '
          validators:
            - type: required
        - type: select
          label: Job
          name: jobObj
          placeholder: Select Job
          isRemoveOptionNotExist: true
          isLazyLoad: true
          clearFieldsAfterChange:
            - positionObj
            - careerStreamObj
            - careerBandObj
          _select:
            transform: >-
              $jobCodesList($.fields.companyObj.value, $.extend.limit,
              $.extend.page, $.extend.search,$.fields.rehireDate)
          handleAfterChange:
            dataSource:
              transform: >-
                { 'band': $.fieldValue.bandId ?
                $careerBandDetail($.fieldValue.bandId).band : null, 'stream':
                $.fieldValue.careerStreamId ?
                $careerStreamDetail($.fieldValue.careerStreamId).careerStream :
                null }
            valueMapping:
              - field: careerStreamObj
                fieldValue: stream
              - field: careerBandObj
                fieldValue: band
          validators:
            - type: required
        - type: text
          name: jobCode
          unvisible: true
          _value:
            transform: >-
              $.fields.jobData.jobObj ? $.fields.jobData.jobObj.value :
              '_setValueNull'
        - name: fte
          label: FTE
          type: text
          validators:
            - type: required
          value: '1.00000'
        - type: text
          name: totalFte
          unvisible: true
          _value:
            transform: $.variables._checkTotalFte.fte
        - type: select
          label: Career Stream
          name: careerStreamObj
          placeholder: Select Career Stream
          isRemoveOptionNotExist: true
          isLazyLoad: true
          clearFieldsAfterChange:
            - careerBandObj
          _select:
            transform: >-
              $careerStreamsList($.extend.limit, $.extend.page,
              $.extend.search,$.fields.rehireDate)
        - type: text
          name: careerStreamCode
          unvisible: true
          _value:
            transform: >-
              $.fields.jobData.careerStreamObj ?
              $.fields.jobData.careerStreamObj.value : '_setValueNull'
        - name: empLevelCode
          label: Emp level
          type: select
          outputValue: value
          isLazyLoad: true
          _select:
            transform: >-
              $exists($.fields.rehireDate) ? $empLevelList($.extend.limit,
              $.extend.page, $.extend.search, $.fields.rehireDate)
          placeholder: Select Emp level
          validators:
            - type: required
        - type: select
          label: Career Band
          name: careerBandObj
          placeholder: Select Career Band
          isRemoveOptionNotExist: true
          isLazyLoad: true
          handleAfterChange:
            dataSource:
              transform: $careerStreamDetail($.fieldValue.careerStreamId)
            valueMapping:
              - field: careerStreamObj
                fieldValue: careerStream
          _select:
            transform: >-
              $careerBandsList($.fields.jobData.careerStreamObj.value,
              $.extend.limit, $.extend.page,
              $.extend.search,$.fields.rehireDate)
        - type: text
          name: careerBandCode
          unvisible: true
          _value:
            transform: >-
              $.fields.jobData.careerBandObj ?
              $.fields.jobData.careerBandObj.value : '_setValueNull'
        - name: timeZoneCode
          label: Time Zone
          type: select
          outputValue: value
          isLazyLoad: true
          _select:
            transform: $exists($.fields.rehireDate) ? $timezoneList($.fields.rehireDate)
          placeholder: Select Time Zone
        - type: upload
          label: Attach File
          name: file
          status: true
          col: 2
          upload:
            size: 100
            isMultiple: true
            accept:
              - application/pdf
              - >-
                application/vnd.openxmlformats-officedocument.wordprocessingml.document
              - application/msword
              - >-
                application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
              - application/vnd.ms-excel
              - image/jpeg
              - image/png
            customContentUpload: >-
              or drop it here PDF, XLS, XLSX, DOC, DOCX, JPG, PNG only (Max
              100MB)
    - type: group
      label: Employment
      collapse: false
      name: employment
      n_cols: 2
      fields:
        - type: text
          name: employeeId
          unvisible: true
          _value:
            transform: $.fields.employeeID
        - name: groupOriginalStartDate
          label: Group Original Start Date
          type: dateRange
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
          _value:
            transform: $DateFormat($.fields.rehireDate, 'YYYY/MM/DD')
          validators:
            - type: required
        - name: groupFirstStartDate
          label: Group First Start Date
          type: dateRange
          mode: date-picker
          disabled: true
          _value:
            transform: >-
              $.variables._calculationEmployment.groupFirstStartDate ?
              $.variables._calculationEmployment.groupFirstStartDate :
              $.fields.employmentData.groupFirstStartDate
          setting:
            format: dd/MM/yyyy
            type: date
          validators:
            - type: required
        - name: groupLastStartDate
          label: Group Last Start Date
          type: dateRange
          mode: date-picker
          disabled: true
          _value:
            transform: >-
              $.variables._calculationEmployment.groupLastStartDate ?
              $.variables._calculationEmployment.groupLastStartDate :
              $.fields.employmentData.groupLastStartDate
          validators:
            - type: required
        - name: groupLastTerminate
          label: Group Last Terminate
          type: dateRange
          disabled: true
          mode: date-picker
          _value:
            transform: >-
              $.variables._calculationEmployment.groupLastTerminate ?
              $.variables._calculationEmployment.groupLastTerminate :
              $.fields.employmentData.groupLastTerminate ?
              $.fields.employmentData.groupLastTerminate :
              $DateFormat($.fields.rehireDate, 'YYYY/MM/DD')
          setting:
            format: dd/MM/yyyy
            type: date
          validators:
            - type: required
        - type: dateRange
          name: oirOriginalStartDate
          mode: date-picker
          label: OIR Original Start Date
          _value:
            transform: $DateFormat($.fields.rehireDate, 'YYYY/MM/DD')
          validators:
            - type: required
          setting:
            format: dd/MM/yyyy
            type: date
        - type: dateRange
          name: oirFirstStartDate
          mode: date-picker
          label: OIR First Start Date
          disabled: true
          _value:
            transform: >-
              $.variables._calculationEmployment.oirFirstStartDate ?
              $.variables._calculationEmployment.oirFirstStartDate :
              $.fields.employmentData.oirFirstStartDate
          validators:
            - type: required
          setting:
            format: dd/MM/yyyy
            type: date
        - type: dateRange
          name: oirLastStartDate
          mode: date-picker
          label: OIR Last Start Date
          disabled: true
          _value:
            transform: >-
              $.variables._calculationEmployment.oirLastStartDate ?
              $.variables._calculationEmployment.oirLastStartDate :
              $.fields.employmentData.oirLastStartDate
          validators:
            - type: required
          setting:
            format: dd/MM/yyyy
            type: date
        - type: dateRange
          name: OIRLastTerminate
          mode: date-picker
          label: OIR Last Terminate
          placeholder: dd/MM/yyyy
          disabled: true
          _value:
            transform: >-
              $.variables._calculationEmployment.oirLastTerminate ?
              $.variables._calculationEmployment.oirLastTerminate :
              $.fields.employmentData.oirLastTerminate ?
              $.fields.employmentData.oirLastTerminate :
              $DateFormat($.fields.rehireDate, 'YYYY/MM/DD')
          setting:
            format: dd/MM/yyyy
            type: date
        - name: groupSeniority
          label: Group Seniority
          type: text
          _value:
            transform: >-
              $.variables._calculationEmployment.groupSeniority ?
              $.variables._calculationEmployment.groupSeniority :
              $.fields.employmentData.groupSeniority
          _toast:
            transform: >-
              {'position': 'bottom','type': 'info', 'content': 'Based on ' &
              $.variables._getInfoSeniorityDefaultGroup.seniorityDateName}
          disabled: true
        - name: organizationalInstanceSeniority
          label: Organizational Instance Seniority
          type: text
          _value:
            transform: >-
              $.variables._calculationEmployment.organizationalInstanceSeniority
              ?
              $.variables._calculationEmployment.organizationalInstanceSeniority
              : $.fields.employmentData.organizationalInstanceSeniority
          _toast:
            transform: >-
              {'position': 'bottom','type': 'info', 'content': 'Based on ' &
              $.variables._getInfoSeniorityByCompany.seniorityDateName}
          disabled: true
        - name: externalExperience
          label: External Experience
          type: text
          _value:
            transform: >-
              $.variables._calculationEmployment.externalExperience ?
              $.variables._calculationEmployment.externalExperience :
              $.fields.employmentData.externalExperience
          disabled: true
        - type: textarea
          label: Note
          name: note
          placeholder: Enter Note
          col: 2
          textarea:
            autoSize:
              minRows: 3
  title: Job data
  backendUrl: /api/rehire/:employeeID
  _isActionWarning: '$.jobData.totalFte > 1 ? true : false'
  actions:
    - id: success
      type: modal
      modal:
        type: success
        title: Update Employee Profile?
        content: >-
          Employee has been rehired, would you like to navigate to Employee's
          profile to update the latest Personal information?
        onConfirm:
          type: navigate
          link: /HR/employees/:employeeID
          paramsUrl: '{''tab'': ''HR.FS.FR.009''}'
    - id: warning
      type: modal
      modal:
        type: success
        title: Total FTE exceeded 1.0
        content: >-
          The sum of FTEs of all active jobs (with HR status Active) for this
          employee has exceeded 1.0.
        onConfirm:
          type: confirm
  sources:
    managerList:
      uri: '"/api/picklists/ISMANAGER/values"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})
      disabledCache: true
      params:
        - effectiveDate
    fullPartList:
      uri: '"/api/picklists/FULLPART/values"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})
      disabledCache: true
      params:
        - effectiveDate
    actionsList:
      uri: '"/api/actions/dropdown"'
      method: GET
      queryTransform: >-
        {'limit':1000 ,'filter': [{'field':'status','operator':
        '$eq','value':true},{'field':'employeeGroupCodes','operator':
        '$eq','value':$.employeeGroupCode},{'field':'processType','operator':
        '$eq','value': 1},{'field':'effectiveDateQuery','operator':
        '$eq','value': $.rehireDate},{'field':'employeeId','operator':
        '$eq','value': $.employeeID},{'field':'employeeRecordNumber','operator':
        '$eq','value': $.employeeRecordNumber}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code,'hrStatusCode':$item.hrStatusCode, 'setStatusField':
        $item.setStatusField, 'prStatusCode':$item.prStatusCode,
        'actionReasons': $item.actionReasons.actionReasonCode[]}})[]
      disabledCache: true
      params:
        - rehireDate
        - employeeID
        - employeeGroupCode
        - employeeRecordNumber
    actionInfo:
      uri: '"/api/actions"'
      method: GET
      queryTransform: >-
        {'limit': 1,'filter': [{'field':'effectiveDate','operator':
        '$lte','value': $.rehireDate},{'field':'code','operator':
        '$eq','value':$.actionCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code, 'setStatusField': $item.setStatusField,
        'prStatusCode':$item.prStatusCode, 'hrStatusCode':$item.hrStatusCode,
        'actionReasons': $item.actionReasons.actionReasonCode[]}})[0]
      disabledCache: true
      params:
        - actionCode
        - rehireDate
    actionReasonList:
      uri: '"/api/picklists/ACTIONREASON/values"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'filter':[{'field':'code','operator':'$in','value':
        $.reasonCodes},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - reasonCodes
        - effectiveDate
        - limit
    hrStatusList:
      uri: '"/api/picklists/HRSTATUS/values"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - effectiveDate
    prStatusList:
      uri: '"/api/picklists/PAYROLLSTATUS/values"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - effectiveDate
    employeeGroupList:
      uri: '"/api/employee-groups"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.employeeGroup.default,
        'value': $item.code}})[]
      disabledCache: true
      params:
        - effectiveDate
    employeeSubGroupsList:
      uri: '"/api/employee-sub-groups"'
      method: GET
      queryTransform: >-
        {'limit': 9999,'page': $.page,'filter':
        [{'field':'linkCatalogDataCode','operator':
        '$eq','value':$.employeeGroupCode},{'field':'search','operator':
        '$eq','value':$.search},{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator': '$eq','value':
        $.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - employeeGroupCode
        - effectiveDate
        - limit
        - page
        - search
    levelOfDecisionList:
      uri: '"/api/picklists/LEVELOFDECISION/values"'
      method: GET
      queryTransform: >-
        {'limit': 9999, 'filter': [{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - effectiveDate
    positionsList:
      uri: '"/api/positions/get-dropdown-list-by"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator':
        '$eq','value':$.rehireDate},{'field':'companyCode','operator':
        '$eq','value':$.companyCode},{'field':'legalEntityCode','operator':
        '$eq','value':$.legalEntityCode},{'field':'departmentCode','operator':
        '$eq','value':$.departmentCode},{'field':'jobCodeCode','operator':
        '$eq','value':$.jobCodeCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code , 'departmentId':
        $item.departmentId, 'jobCodeId': $item.jobCodeId, 'locationId':
        $item.locationId, 'costCenterId': $item.costCenterId, 'matrixPositions':
        $item.matrixPositions[] }})[]
      disabledCache: true
      params:
        - companyCode
        - rehireDate
        - legalEntityCode
        - departmentCode
        - jobCodeCode
    positionsListGetBy:
      uri: '"/api/positions/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code , 'id': $item.id}})[]
      disabledCache: true
      params:
        - effectiveDate
        - limit
        - page
        - search
    positionInfo:
      uri: '"/api/positions/" & $.id & ""'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $
      disabledCache: true
      params:
        - id
    legalEntityList:
      uri: '"/api/legal-entities/get-list"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'effectiveDate','operator': '$eq','value':
        $.rehireDate},{'field':'status','operator':
        '$eq','value':true},{'field':'companyCode','operator':
        '$eq','value':$.companyCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id}})[]
      disabledCache: true
      params:
        - companyCode
        - rehireDate
    businessUnitList:
      uri: '"/api/business-units/get-list"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'status','operator':
        '$eq','value':true},{'field':'companyCode','operator':
        '$eq','value':$.companyCode},{'field':'effectiveDate','operator':
        '$eq','value':$.rehireDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id}})[]
      disabledCache: true
      params:
        - companyCode
        - rehireDate
    divisionsList:
      uri: '"/api/divisions/get-list"'
      method: GET
      queryTransform: >-
        {'filter':
        [{'field':'companyCode','operator':'$eq','value':$.companyCode},{'field':'status','operator':
        '$eq','value':true},{'field':'businessUnitCode','operator':
        '$eq','value':$.businessUnitCode},{'field':'effectiveDate','operator':
        '$eq','value': $.rehireDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id}})[]
      disabledCache: true
      params:
        - companyCode
        - businessUnitCode
        - rehireDate
    divisionDetail:
      uri: '"/api/divisions/" & $.id & ""'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        {'BusinessUnit': {'label': $.businessUnitName & ' (' &
        $.businessUnitCode & ')', 'value': $.businessUnitCode}}
      disabledCache: true
      params:
        - id
    departmentsList:
      uri: '"/api/departments/get-dropdown-list-by"'
      method: GET
      queryTransform: >-
        {'filter':
        [{'field':'companyCode','operator':'$eq','value':$.companyCode},{'field':'legalEntityCode','operator':
        '$eq','value':$.legalEntityCode},{'field':'businessUnitCode','operator':
        '$eq','value':$.businessUnitCode},{'field':'divisionCode','operator':
        '$eq','value':$.divisionCode},{'field':'status','operator':
        '$eq','value':true},{'field':'search','operator':
        '$eq','value':$.search},{'field':'effectiveDate','operator':
        '$eq','value': $.rehireDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id}})[]
      disabledCache: true
      params:
        - companyCode
        - legalEntityCode
        - businessUnitCode
        - divisionCode
        - rehireDate
    departmentInfo:
      uri: '"/api/departments/" & $.id & ""'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $
      disabledCache: true
      params:
        - id
    jobCodesList:
      uri: '"/api/job-codes/get-dropdown-list-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator':
        '$eq','value':true},{'field':'companyCode','operator':
        '$eq','value':$.companyCode},{'field':'effectiveDate','operator':
        '$eq','value': $.rehireDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id, 'bandId':
        $item.bandId, 'careerStreamId': $item.careerStreamId}})[]
      disabledCache: true
      params:
        - companyCode
        - limit
        - page
        - search
        - rehireDate
    businessTitlesList:
      uri: '"/api/business-titles/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator': '$eq','value':
        $.rehireDate},{'field':'companyCode','operator': '$eq','value':
        $.companyCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - companyCode
        - limit
        - page
        - search
        - rehireDate
    careerStreamsList:
      uri: '"/api/career-streams/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator': '$eq','value':
        $.rehireDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code,'id': $item.id}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - rehireDate
    careerStreamDetail:
      uri: '"/api/career-streams/" & $.id & ""'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        {'careerStream': {'label': $.longName.default & ' (' & $.code & ')',
        'value': $.code, 'id': $.id}}
      disabledCache: true
      params:
        - id
    careerBandsList:
      uri: '"/api/bands/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator':
        '$eq','value':true},{'field':'careerStreamCode','operator':
        '$eq','value':$.careerStreamCode},{'field':'effectiveDate','operator':
        '$eq','value': $.rehireDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($filter($.data, function ($item){ $exists($item.code) }) ,
        function($item) {{'label': $item.longName.default & ' (' & $item.code &
        ')', 'value': $item.code,'id': $item.id, 'careerStreamId':
        $item.careerStreamId}})[]
      disabledCache: true
      params:
        - careerStreamCode
        - limit
        - page
        - search
        - rehireDate
    careerBandDetail:
      uri: '"/api/bands/" & $.id & ""'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        {'band': {'label': $.longName.default & ' (' & $.code & ')', 'value':
        $.code, 'id': $.id}}
      disabledCache: true
      params:
        - id
    empLevelList:
      uri: '"/api/picklists/EMPLEVEL/values/pagination"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
    constCenterList:
      uri: '"/api/cost-centers/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator':
        '$eq','value':true},{'field':'companyCode','operator':
        '$eq','value':$.companyCode},{'field':'rehireDate','operator':
        '$eq','value': $.rehireDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id':$item.id}})[]
      disabledCache: true
      params:
        - companyCode
        - limit
        - page
        - search
        - rehireDate
    constCenterById:
      uri: '"/api/cost-centers/" & $.id & ""'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $.code ? {'label': $.longName.default & ' (' & $.code & ')', 'value':
        $.code}
      disabledCache: true
      params:
        - id
    locationsList:
      uri: '"/api/locations/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator': '$eq','value':
        $.rehireDate},{'field':'companyCode','operator':'$eq','value':$.companyCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id':$item.id}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - rehireDate
        - companyCode
    regionList:
      uri: '"/api/picklists/REGIONS/values"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})
      disabledCache: true
      params:
        - effectiveDate
    timezoneList:
      uri: '"/api/picklists/TIMEZONE/values"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - effectiveDate
    jobIndicatorList:
      uri: '"/api/picklists/JOBINDICATOR/values"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - effectiveDate
    personalsList:
      uri: '"/api/personals/all-employees-job-datas"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'effectiveDate','operator': '$eq','value':
        $.effectiveDate},{'field':'hrStatus','operator': '$eq','value':
        $.hrStatus}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.fullName &' ('&
        $item.employeeId & ')', 'value': $item.employeeId}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
        - hrStatus
    checkTotalFte:
      uri: '"/api/personals/" & $.employeeId & "/job-datas/total-fte"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'inputFte','operator':
        '$eq','value':$.fte},{'field':'effectiveDate','operator':
        '$eq','value':$.rehireDate},{'field':'employeeRecordNumber','operator':
        '$eq','value':$.employeeRecordNumber}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $
      disabledCache: true
      params:
        - employeeId
        - fte
        - rehireDate
        - employeeRecordNumber
    calculationEmployment:
      uri: '"/api/calculator-employment/" & $.employeeId & ""'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'companyCode','operator':
        '$eq','value':$.companyCode},{'field':'groupOriginalStartDate','operator':
        '$eq','value':$.groupOriginalStartDate},{'field':'employeeGroupCode','operator':
        '$eq','value':$.employeeGroupCode},{'field':'employeeSubGroupCode','operator':
        '$eq','value':$.employeeSubGroupCode},{'field':'effectiveDate','operator':
        '$eq','value':$.rehireDate},{'field':'oirOriginalStartDate','operator':
        '$eq','value':$.oirOriginalStartDate},{'field':'organizationalInstanceRcd','operator':
        '$eq','value':$.organizationalInstanceRcd},{'field':'employeeRecordNumber','operator':
        '$eq','value':$.employeeRecordNumber},{'field':'actionCode','operator':
        '$eq','value':$.actionCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $
      disabledCache: true
      params:
        - employeeId
        - companyCode
        - employeeGroupCode
        - employeeSubGroupCode
        - organizationalInstanceRcd
        - employeeRecordNumber
        - rehireDate
        - groupOriginalStartDate
        - oirOriginalStartDate
        - actionCode
    organizationPick:
      uri: >-
        "/api/trees/organization/pick/" & $.pickType & "/" & $.code & "/" &
        $.rehireDate & ""
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $reduce(     $,  function($acc, $item) {   $merge([     $acc,     {   
        $item.type : ($origItem := $lookup($acc, $item.type); $validItem :=
        $item.effectiveDate ? $toMillis($item.effectiveDate) <=
        $toMillis($now()) : true ; $exists($origItem) and
        $toMillis($origItem.effectiveDate) > $toMillis($item.effectiveDate)  ?
        $origItem : ($validItem ? {'label': $item.name & ' (' & $item.code &
        ')', 'value': $item.code, 'type': $item.type, 'effectiveDate':
        $item.effectiveDate } : $origItem))    }    ])  }, {} )
      disabledCache: true
      params:
        - pickType
        - code
        - rehireDate
    getHistory:
      uri: '"/api/personals/" & $.employeeId & "/job-datas/histories"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'employeeRecordNumber','operator':
        '$eq','value':$.employeeRecordNumber}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $
      disabledCache: true
      params:
        - employeeId
        - employeeRecordNumber
    getSeniority:
      uri: '"/api/seniority-calculators"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'companyCode','operator':
        '$in','value':$.companyCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: '[$.data]'
      disabledCache: true
      params:
        - companyCode
    getJobdataPrimary:
      uri: '"/api/personals/" & $.employeeId & "/job-datas"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'jobIndicator','operator': '$eq','value':
        'P'},{'field':'hrStatus','operator': '$eq','value': 'A'}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: '$.data ? $.data[] : []'
      disabledCache: true
      params:
        - employeeId
  variables:
    _calculationEmployment:
      transform: >-
        $.fields.employeeID and
        $exists($.fields.employment.groupOriginalStartDate) ?
        $calculationEmployment($.fields.employeeID,$.fields.companyObj.value,$.fields.employeeGroupCode,$.fields.jobData.employeeSubGroupCode,$.fields.organizationalInstanceRecord,$.fields.employeeRecordNumber,$DateToTimestampUTC($.fields.rehireDate),$.fields.employment.groupOriginalStartDate,$.fields.employment.oirOriginalStartDate,$.fields.actionCode)
    _actionsList:
      transform: >-
        $exists($.fields.rehireDate) and
        $exists($.fields.previousJobData.employeeRecordNumber) ?
        $actionsList($DateToTimestampUTC($.fields.rehireDate),$.fields.employeeID,$.fields.employeeGroupCode,$.fields.previousJobData.employeeRecordNumber)
    _checkTotalFte:
      transform: >-
        $.fields.employeeID ? $checkTotalFte($.fields.employeeID,
        $.fields.jobData.fte,
        $DateToTimestampUTC($.fields.rehireDate),$.fields.employeeRecordNumber)
    _selectedAction:
      transform: >-
        $exists($.fields.actionCode) ? ($actionCode:=  $.fields.actionCode;
        $selectdAction:= $.variables._actionsList[value=$actionCode])
    _positionInfo:
      transform: >-
        $exists($.fields.jobData.positionCode) and $exists($.fields.rehireDate)
        ? $positionInfo($.fields.jobData.positionCode,$.fields.rehireDate)
    _matrixPositionByPositionInfo:
      transform: >-
        ($value := $.variables._positionInfo[0].matrixPositions; $map($value,
        function($item) { $item.value }))
    _getHistory:
      transform: >-
        $.fields.employeeID and $exists($.fields.employeeRecordNumber) ?
        $getHistory($.fields.employeeID,$.fields.employeeRecordNumber)
    _getHistorySameEffectiveDate:
      transform: >-
        $.variables._getHistory ? ( $data := $filter($.variables._getHistory,
        function($item){$DateFormat($.fields.rehireDate, 'yyyy-MM-DD') =
        $DateFormat($item.effectiveDate, 'yyyy-MM-DD')}) ; $result :=
        $reduce($data, function($acc,$item){ $exists($acc.effectiveSequence) and
        $acc.effectiveSequence > $item.effectiveSequence ? $acc : $item },{}))
    _getHistorySameSequence:
      transform: >-
        $.variables._getHistory ? ( $data := $filter($.variables._getHistory,
        function($item){$DateFormat($.fields.rehireDate, 'yyyy-MM-DD') =
        $DateFormat($item.effectiveDate, 'yyyy-MM-DD') and
        $item.effectiveSequence = $.fields.effectiveSequence and
        $DateFormat($.fields.expectedEndDate, 'yyyy-MM-DD') =
        $DateFormat($item.expectedEndDate, 'yyyy-MM-DD') })[0])
    _getSeniority:
      transform: >-
        $exists($.fields.companyObj.value) ?
        $getSeniority([$.fields.companyObj.value,'DefaultCompany','DefaultGroup'])
    _getInfoSeniorityByCompany:
      transform: >-
        $exists($.variables._getSeniority) ? ($byCompany :=
        $filter($.variables._getSeniority, function($item){$item.companyCode =
        $.fields.companyObj.value})[0] ; $byCompany ? $byCompany :
        $filter($.variables._getSeniority, function($item){$item.companyCode =
        'DefaultCompany'})[0])
    _getInfoSeniorityDefaultGroup:
      transform: >-
        $exists($.variables._getSeniority) ? $filter($.variables._getSeniority,
        function($item){$item.companyCode = 'DefaultGroup'})[0]
    _getJobdataPrimary:
      transform: >-
        $.fields.employeeID ? ( $getData :=
        $getJobdataPrimary($.fields.employeeID); $count($getData))
filter_config: {}
layout_options:
  is_upload_file: true
  is_new_dynamic_form: true
  layout_data_service:
    data_key: rehire
    key_mapping:
      employeeID: employeeID
layout_options__header_buttons: null
options: null
create_form:
  fields:
    - type: group
      fields:
        - name: employee
          label: Employee
          type: text
          unvisible: true
        - type: text
          name: processType
          unvisible: true
          value: '1'
        - name: employeeRecordNumber
          label: employeeRecordNumber
          type: text
          unvisible: true
        - name: organizationalInstanceRecord
          label: organizationalInstanceRecord
          type: text
          unvisible: true
        - name: jobDataMatchOIR
          label: jobDataMatchOIR
          type: number
          unvisible: true
        - type: text
          name: fullName
          unvisible: true
          _value:
            transform: $.variables._basicInfor.fullName
        - type: text
          name: avatarFile
          unvisible: true
          _value:
            transform: $.variables._basicInfor.avatarFile
    - type: group
      n_cols: 3
      fields:
        - name: employeeID
          label: Employee ID
          type: select
          placeholder: Choose Employee ID
          outputValue: value
          isLazyLoad: true
          _select:
            transform: $personalsList($.extend.limit, $.extend.page, $.extend.search)
          _value:
            transform: >-
              $.extend.layoutData.rehire.employeeId ?
              $.extend.layoutData.rehire.employeeId
          _validateFn:
            transform: >-
              $.fields.isOriginEmployee and $exists($.fields.employeeID) and
              $not($isNilorEmpty($.extend.layoutData.rehire.employeeId)) ?
              {'label': $.variables._basicInfor.fullName & ' (' &
              $.fields.employeeID & ')', 'value': $.fields.employeeID }
            params:
              updateLabelExistOption: true
          validators:
            - type: required
          clearFieldsAfterChange:
            - isOriginEmployee
        - type: checkbox
          name: isOriginEmployee
          unvisible: true
          value: true
        - type: checkbox
          name: isBlackList
          unvisible: true
          _value:
            transform: $.variables._blackblockCheck.isBlackList
        - type: checkbox
          name: isBlockList
          unvisible: true
          _value:
            transform: $.variables._blackblockCheck.isBlockList
        - name: previousJobData
          label: Previous Job Data
          type: select
          placeholder: Choose Previous Job Data
          outputValue: value
          dependantField: $.fields.employeeID
          _select:
            transform: $.variables._jobDatasList
          _value:
            transform: $.extend.layoutData.rehire ? $.variables._jobDataSelected.value
          validators:
            - type: required
        - name: companyObj1
          label: Company
          type: select
          placeholder: Choose Company
          disabled: true
          validateDisabled: true
          dependantField: $.fields.employeeID
          _select:
            transform: $.variables._companiesList
          _value:
            transform: >-
              $.variables._companiesList and $.variables._companiesList[0] ?
              $.variables._companiesList[0] : '_setSelectValueNull'
          validators:
            - type: required
        - type: text
          name: companyObj
          unvisible: true
          _value:
            transform: >-
              $filter($.variables._companiesList, function($item) { $item.value
              = $.fields.previousJobData.company })[0]
        - name: employeeGroupCode1
          label: Employee Group
          type: select
          placeholder: Choose Employee Group
          outputValue: value
          disabled: true
          validateDisabled: true
          _select:
            transform: $employeeGroupList()
          _value:
            transform: >-
              $.fields.previousJobData ? $.fields.previousJobData.employeeGroup
              : '_setSelectValueNull'
          validators:
            - type: required
        - type: text
          name: employeeGroupCode
          unvisible: true
          _value:
            transform: $.fields.previousJobData ? $.fields.previousJobData.employeeGroup
        - type: dateRange
          name: rehireDate
          label: Rehire Date
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
          _value:
            transform: >-
              $exists($.extend.layoutData.rehire.terminateDate) ?
              $.extend.layoutData.rehire.terminateDate : $now()
          no_need_focus: true
          validators:
            - type: required
            - type: ppx-custom
              args:
                transform: >-
                  $not($isNilorEmpty($.fields.previousJobData)) and
                  $exists($.fields.rehireDate) and
                  $DateDiff($DateFormat($.fields.previousJobData.effectiveDate,
                  'yyyy-MM-DD'), $DateFormat($.fields.rehireDate, 'yyyy-MM-DD'),
                  'd') > 0
              text: Rehire Date must greater than previous JobData Effective Date
        - name: actionCode
          label: Action
          type: select
          placeholder: Choose Action
          outputValue: value
          dependantField: $.fields.employeeID,$.fields.previousJobData
          _select:
            transform: $.variables._actionsList
          _value:
            transform: $.variables._validRehireDate ? '_setSelectValueNull'
          validators:
            - type: required
  filterMapping:
    - field: companyCode
      operator: $eq
      valueField: companyObj.value
    - field: processType
      operator: $eq
      valueField: processType
    - field: actionCode
      operator: $eq
      valueField: actionCode
    - field: effectiveDate
      operator: $eq
      valueField: rehireDate
    - field: employeeGroupCode
      operator: $eq
      valueField: employeeGroupCode
    - field: jobDataId
      operator: $eq
      valueField: previousJobData.id
  backendUrl: /api/ern-oir/:employeeID
  _isActionProceed: '$not($.isTerminated) ? true : false'
  _isActionModalWarning: $.isBlackList
  _isActionToastError: $.isBlockList
  actions:
    - id: success
      type: modal
      modal:
        title: Warning
        content: Employee is not terminate
    - id: warning
      type: modal
      modal:
        type: warning
        title: Warning
        content: Blacklist employees are not encouraged to be recruited.
        onConfirm:
          type: confirm
    - id: error
      type: toast
      toast:
        type: error
        title: Error
        content: Blocklist employees are not recruited according to regulations.
        onConfirm:
          type: confirm
  sources:
    personalsList:
      uri: '"/api/personals/rehire-employee"'
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'hrStatus','operator': '$eq','value':'I'}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name & ' (' &
        $item.employeeId & ')', 'value': $item.employeeId, 'id': $item.id,
        'jobDataEffectiveDateFrom': $item.jobDataEffectiveDateFrom}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    employeeGroupList:
      uri: '"/api/employee-groups"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.employeeGroup.default,
        'value': $item.code}})[]
      disabledCache: true
    actionsList:
      uri: '"/api/actions/dropdown"'
      method: GET
      queryTransform: >-
        {'limit':1000 ,'filter': [{'field':'status','operator':
        '$eq','value':true},{'field':'employeeGroupCodes','operator':
        '$eq','value':$.employeeGroupCode},{'field':'processType','operator':
        '$eq','value': 1},{'field':'employeeId','operator': '$eq','value':
        $.employeeID},{'field':'employeeRecordNumber','operator': '$eq','value':
        $.employeeRecordNumber},{'field':'effectiveDateQuery','operator':
        '$eq','value': $.rehireDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code,'hrStatusCode':$item.hrStatusCode, 'actionReasons':
        $item.actionReasons.actionReasonCode[]}})[]
      disabledCache: true
      params:
        - employeeID
        - employeeGroupCode
        - employeeRecordNumber
        - rehireDate
    companiesList:
      uri: '"/api/companies/by"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator':
        '$eq','value':$.rehireDate},{'field':'code','operator':
        '$eq','value':$.code}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id}})[]
      disabledCache: true
      params:
        - rehireDate
        - code
    jobDatasList:
      uri: '"/api/personals/" & $.employeeID & "/job-datas/lastest-records"'
      method: GET
      queryTransform: >-
        {'limit':10000,'filter': [{'field':'hrStatus','operator':
        '$eq','value':'I'}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.employeeRecordNumber & ' -
        ' & $item.employeeGroup & ' - ' & $item.hrStatus & ' - ' &
        $item.departmentName & ' - ' & $item.jobName, 'value': $item,
        'jobIndicator': $item.jobIndicator}})[]
      disabledCache: true
      params:
        - employeeID
    basicInfor:
      uri: '"/api/personals/" & $.employeeId & ""'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $
      disabledCache: true
      params:
        - employeeId
    blackblockCheck:
      uri: '"/api/black-block-infos/check-black-block/" & $.employeeID & ""'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'effectiveDate','operator': '$eq','value':
        $.rehireDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $
      disabledCache: true
      params:
        - employeeID
        - rehireDate
  requestsOnProceed:
    sourcesMapping:
      - key: validateErnOir
        enabled: $not($.formData.isBlockList = true)
      - key: minimizedSingleDate
        enabled: $not($.formData.isBlockList = true)
    sources:
      validateErnOir:
        uri: '"/api/ern-oir/" & $.employeeID & ""'
        method: GET
        queryTransform: >-
          {'filter': [{'field':'actionCode','operator': '$eq','value':
          $.actionCode},{'field':'processType','operator': '$eq','value':
          $.processType},{'field':'employeeGroupCode','operator': '$eq','value':
          $.employeeGroupCode},{'field':'companyCode','operator': '$eq','value':
          $.companyObj.value},{'field':'effectiveDate','operator':
          '$eq','value': $.rehireDate},{'field':'jobDataId','operator':
          '$eq','value': $.previousJobData.id}]}
        bodyTransform: ''
        headerTransform: ''
        resultTransform: $
        disabledCache: true
  variables:
    _companiesList:
      transform: >-
        $.fields.previousJobData ?
        $companiesList($.fields.rehireDate,$.fields.previousJobData.company)
    _jobDatasList:
      transform: $.fields.employeeID ? $jobDatasList($.fields.employeeID)
    _jobDataSelected:
      transform: >-
        $.extend.layoutData.rehire ? $filter($.variables._jobDatasList,
        function($item) { $item.value.id = $.extend.layoutData.rehire.jobDataId
        })
    _validRehireDate:
      transform: >-
        $exists($.fields.previousJobData) and $exists($.fields.rehireDate) ?
        $DateDiff($DateFormat($.fields.previousJobData.effectiveDate,
        'yyyy-MM-DD'), $DateFormat($.fields.rehireDate, 'yyyy-MM-DD'), 'd') > 0
    _actionsList:
      transform: >-
        $not($.variables._validRehireDate) and $.fields.employeeID and
        $.fields.previousJobData ?
        $actionsList($.fields.employeeID,$.fields.employeeGroupCode,$.fields.previousJobData.employeeRecordNumber,$DateToTimestampUTC($.fields.rehireDate))
    _blackblockCheck:
      transform: >-
        $.fields.employeeID and $.fields.previousJobData ?
        $blackblockCheck($.fields.employeeID,$.fields.rehireDate)
    _basicInfor:
      transform: $.fields.employeeID ? $basicInfor($.fields.employeeID)
layout_options__footer_buttons:
  - id: cancel
    title: Cancel
    type: tertiary
  - id: proceed
    title: Proceed
    type: primary
layout_options__row_actions: null
backend_url: null
screen_name: null
layout_options__actions_many: null
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key: null
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Rehire Person
  parent:
    title: Human Resource
