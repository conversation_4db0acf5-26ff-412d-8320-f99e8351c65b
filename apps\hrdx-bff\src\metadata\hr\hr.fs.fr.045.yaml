id: HR.FS.FR.045
status: draft
sort: 52
user_created: 56e71b91-dd39-4ebc-afd1-b4f00f38d4a5
date_created: '2024-10-08T07:22:13.863Z'
user_updated: 56e71b91-dd39-4ebc-afd1-b4f00f38d4a5
date_updated: '2025-07-28T03:43:52.177Z'
title: Contract
requirement:
  time: 1748944237825
  blocks:
    - id: fzOyDcXm7x
      type: paragraph
      data:
        text: Qu<PERSON>n lý thông tin hợp đồng lao động&nbsp;
  version: 2.30.7
screen_design: null
module: HR
local_fields:
  - code: index
    title: STT
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    options__tabular__column_width: 5
    options__tabular__align: center
  - code: employeeRecordNumber
    title: Employee Record Number
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 15
  - code: contractNumber
    title: Contract Number
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
  - code: contractTypeName
    title: Contract Type
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
  - code: startDate
    title: Start Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
  - code: endDate
    title: Actual End Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
  - code: includingAppendix
    title: Include Appendix
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: Tag
      collection: field_types
    options__tabular__align: left
    extra_config:
      tags:
        - value: true
          label: 'Yes'
          class: success
        - value: false
          label: 'No'
          class: error
mock_data: null
local_buttons: null
layout: layout-table
form_config:
  formSize:
    create: large
    edit: large
    proceed: large
  styleFilterForm:
    padding: 0
    borderBottom: none
  nestedConfirmOnSubmit:
    name: check
    transform: >-
      $.variables._checkContractDuration.messageWarning ? {'title':
      'Warning','contents': $.variables._checkContractDuration.messageWarning}
  fields:
    - type: group
      label: Contract data
      collapse: false
      styling_not_form_view:
        fieldGroupContentStyle:
          paddingTop: 0px
      fieldGroupContentStyle:
        paddingTop: 12px
      fields:
        - type: group
          styling_not_form_view:
            fieldGroupTitleStyle:
              padding: 12px 0 0
            borderBottomLabel: true
            lastGroupStyleOff: true
            isHideArrow: false
          fields:
            - type: select
              label: Employee Record Number
              name: employeeRecordNumber
              placeholder: Enter Employee Record Number
              outputValue: value
              _select:
                transform: >-
                  ( $empId := $isNilorEmpty($.extend.addOnValue.id1 ) ?
                  $.extend.defaultValue.employeeId :$.extend.addOnValue.id1;
                  $jobDatasList(1000, $empId))
              _condition:
                transform: $not($.extend.formType = 'view')
              validators:
                - type: required
            - type: text
              label: Employee Record Number
              name: employeeRecordNumber
              _condition:
                transform: $.extend.formType = 'view'
            - type: group
              _n_cols:
                transform: '$not($.extend.formType = ''view'') ? 2 : 1'
              fields:
                - type: text
                  unvisible: true
                  name: id
                - type: select
                  label: Contract Type
                  name: contractTypeCode
                  placeholder: Select Contract Type
                  outputValue: value
                  _select:
                    transform: $contractTypeList($.fields.startDate)
                  validators:
                    - type: required
                  clearFieldsAfterChange:
                    - checkChangeStartDate
                - type: text
                  label: Contract Number
                  name: contractNumber
                  placeholder: Enter Contract Number
                  validators:
                    - type: required
                    - type: maxLength
                      args: '40'
                      text: Maximum 40 characters
                - type: dateRange
                  mode: date-picker
                  label: Start Date
                  name: startDate
                  placeholder: dd/MM/yyyy
                  setting:
                    type: day
                    format: dd/MM/yyyy
                  validators:
                    - type: required
                  _condition:
                    transform: $.extend.formType = 'create'
                  _value:
                    transform: >-
                      ($formType:=$.extend.formType;$contract:=$.variables._contractHistory;$data:=$.variables._getJobDataByMinEffectiveDate;($.extend.formType
                      = 'create' and $count($contract) = 0 and
                      $exists($data.effectiveDate) and $type($data) = 'object')
                      ? $data.effectiveDate : ($formType = 'create' and
                      $count($contract) = 0 and $exists($data) and $type($data)
                      = 'array' and $count($data) > 1) ? $data[0].effectiveDate
                      : $now())
                - type: dateRange
                  mode: date-picker
                  label: Start Date
                  name: startDate
                  placeholder: dd/MM/yyyy
                  setting:
                    type: day
                    format: dd/MM/yyyy
                  validators:
                    - type: required
                  _condition:
                    transform: $not($.extend.formType = 'create')
                - type: text
                  name: countAppendices
                  unvisible: true
                  _value:
                    transform: $count($.fields.contractAppendices)
                - type: text
                  unvisible: true
                  dependantField: $.fields.startDate
                  dependantFieldSkip: 2
                  name: checkChangeStartDate
                  value: 'true'
                - type: dateRange
                  mode: date-picker
                  label: Contract Expected End Date
                  name: expectedDateTo
                  placeholder: dd/MM/yyyy
                  _condition:
                    transform: $not($.extend.formType = 'view')
                  _value:
                    transform: >-
                      ($not($.extend.formType = 'view') and
                      $.variables._getMaxEffectiveDate and
                      $.variables._getMaxEffectiveDate.status and
                      $not($boolean($.fields.checkChangeStartDate))) ?
                      $.variables._caculationEndDate : ($not($.extend.formType =
                      'view') and $not($.variables._getMaxEffectiveDate) and
                      $not($boolean($.fields.checkChangeStartDate))) ?
                      '_setValueNull'
                  validators:
                    - type: ppx-custom
                      id: checkWithStartDate
                      args:
                        transform: >-
                          $exists($.fields.startDate) and
                          $exists($.fields.expectedDateTo) ?
                          $DateDiff($DateFormat($.fields.expectedDateTo,
                          'yyyy-MM-DD'), $DateFormat($.fields.startDate,
                          'yyyy-MM-DD'), 'd') < 1
                      text: Expected End Date must be greater than Start Date
                - type: dateRange
                  mode: date-picker
                  label: Contract Expected End Date
                  name: expectedDateTo
                  placeholder: dd/MM/yyyy
                  _condition:
                    transform: $.extend.formType = 'view'
                - type: dateRange
                  mode: date-picker
                  label: Contract End Date
                  name: endDate
                  placeholder: dd/MM/yyyy
                  validators:
                    - type: ppx-custom
                      args:
                        transform: >-
                          $exists($.fields.startDate) and
                          $exists($.fields.endDate) ?
                          $DateDiff($DateFormat($.fields.endDate, 'yyyy-MM-DD'),
                          $DateFormat($.fields.startDate, 'yyyy-MM-DD'), 'd') <
                          1
                      text: End Date must be greater than Start Date
                  _condition:
                    transform: $not($.extend.formType = 'view')
                  _value:
                    transform: >-
                      ($not($.extend.formType = 'view') and
                      $.variables._getMaxEffectiveDate and
                      $.variables._getMaxEffectiveDate.status and
                      $not($boolean($.fields.checkChangeStartDate))) ?
                      $.variables._caculationEndDate : ($not($.extend.formType =
                      'view') and $not($.variables._getMaxEffectiveDate) and
                      $not($boolean($.fields.checkChangeStartDate))) ?
                      '_setValueNull'
                - type: dateRange
                  mode: date-picker
                  label: Contract End Date
                  name: endDate
                  placeholder: dd/MM/yyyy
                  _condition:
                    transform: $.extend.formType = 'view'
                - type: dateRange
                  mode: date-picker
                  label: Sign Date
                  name: signDate
                  placeholder: dd/MM/yyyy
            - type: textarea
              label: Note
              name: note
              placeholder: Enter Note
              textarea:
                autoSize:
                  minRows: 3
                maxCharCount: 1000
              validators:
                - type: maxLength
                  args: '1000'
                  text: Maximum 1000 characters
            - type: group
              customPadding: 0 0 0 0
              fields:
                - type: radio
                  label: Including Probation Period
                  name: includingProbationPeriod
                  value: false
                  radio:
                    - label: 'Yes'
                      value: true
                    - label: 'No'
                      value: false
            - type: group
              n_cols: 2
              _condition:
                transform: >-
                  $boolean($.fields.includingProbationPeriod) and
                  $not($.extend.formType = 'view')
              fields:
                - type: dateRange
                  mode: date-picker
                  label: Probation Date From
                  name: probationDateFrom
                  placeholder: dd/MM/yyyy
                  validators:
                    - type: required
                  _value:
                    transform: >-
                      ($formType:=$.extend.formType;$contract:=$.variables._contractHistory;$data:=$.variables._getJobDataByMinEffectiveDate;($formType
                      = 'create' and $count($contract) = 0 and
                      $exists($data.effectiveDate) and $type($data) = 'object')
                      ? $data.effectiveDate : $formType = 'create' ?
                      $count($contract) = 0 and $exists($data) and $type($data)
                      = 'array' and $count($data) > 1 ? $data[0].effectiveDate :
                      $now())
                - type: dateRange
                  mode: date-picker
                  label: Probation Date To
                  validators:
                    - type: required
                    - type: ppx-custom
                      args:
                        transform: >-
                          $exists($.fields.probationDateFrom) and
                          $exists($.fields.probationDateTo) ?
                          $DateDiff($DateFormat($.fields.probationDateTo,
                          'yyyy-MM-DD'), $DateFormat($.fields.probationDateFrom,
                          'yyyy-MM-DD'), 'd') < 1
                      text: >-
                        Probation Date To must be greater than Probation Date
                        From
                  name: probationDateTo
                  placeholder: dd/MM/yyyy
            - type: group
              n_cols: 1
              _condition:
                transform: >-
                  $boolean($.fields.includingProbationPeriod) and
                  $.extend.formType = 'view'
              fieldGroupContentStyle:
                padding: 12px
                background: '#F8F9FA'
                borderRadius: 8px
              fields:
                - type: dateRange
                  mode: date-picker
                  label: Probation Date From
                  name: probationDateFrom
                  placeholder: dd/MM/yyyy
                - type: dateRange
                  mode: date-picker
                  label: Probation Date To
                  name: probationDateTo
                  placeholder: dd/MM/yyyy
                - type: textarea
                  label: Note
                  name: probationNote
                  placeholder: Enter Note
                  textarea:
                    autoSize:
                      minRows: 3
                    maxCharCount: 1000
                  validators:
                    - type: maxLength
                      args: '1000'
                      text: Maximum 1000 characters
            - type: textarea
              label: Note
              name: probationNote
              placeholder: Enter Note
              _condition:
                transform: >-
                  $not($.extend.formType = 'view') and
                  $boolean($.fields.includingProbationPeriod)
              textarea:
                autoSize:
                  minRows: 3
                maxCharCount: 1000
              validators:
                - type: maxLength
                  args: '1000'
                  text: Maximum 1000 characters
    - type: group
      label: Contract appendix
      collapse: false
      styling_not_form_view:
        fieldGroupContentStyle:
          paddingTop: 0px
      fieldGroupContentStyle:
        paddingTop: 12px
      fields:
        - type: array
          name: contractAppendices
          arrayOptions:
            canChangeSize: true
            _markAsDeletedByKey:
              transform: $.extend.formType = 'edit' ? 'isDeleted'
            markAsDeletedByKey: isDeleted
            add_btn_type: secondary
            add_btn_size: small
            _customFieldValueAddItem:
              transform: >-
                {'expectedDateTo': $.fields.expectedDateTo , 'endDate':
                $.fields.endDate}
          field:
            type: group
            _label:
              transform: '''Contract appendix '' & ''#'' & $string($.extend.path[-1] + 1)'
            fieldGroupContentStyle:
              padding: 16px
              paddingTop: 12px
              paddingBottom: 0px
            fieldGroupTitleStyle:
              padding: 10px 12px
              fontSize: 14px
            fieldBackground: '#fff'
            borderRadius: 8px
            border: '1px solid #DFE3E8'
            borderBottomLabel: true
            lastGroupStyleOff: true
            isHideArrow: false
            fields:
              - type: text
                name: id
                unvisible: true
                value: 0
              - type: checkbox
                name: isDeleted
                unvisible: true
                value: false
              - type: group
                _n_cols:
                  transform: '$not($.extend.formType = ''view'') ? 2 : 1'
                padding: '0'
                fields:
                  - type: text
                    label: Contract Appendix Number
                    name: appendixNumber
                    placeholder: Enter Contract Appendix Number
                    validators:
                      - type: required
                  - type: dateRange
                    label: Start Date
                    name: startDate
                    mode: date-picker
                    validators:
                      - type: required
                      - type: ppx-custom
                        args:
                          transform: >-
                            $exists($getFieldGroup($.extend.path, $.fields,
                            1).startDate) ?
                            $DateDiff($DateFormat($.fields.startDate,
                            'yyyy-MM-DD'),
                            $DateFormat($getFieldGroup($.extend.path, $.fields,
                            1).startDate, 'yyyy-MM-DD'), 'd') > 1
                        text: >-
                          Appendix Start Date must be the same as or later than
                          the Contract Start Date.
                  - type: dateRange
                    label: Expected End Date
                    name: expectedDateTo
                    mode: date-picker
                    validators:
                      - type: ppx-custom
                        id: checkWithStartDate
                        args:
                          transform: >-
                            $exists($getFieldGroup($.extend.path, $.fields,
                            1).startDate) and
                            $exists($getFieldGroup($.extend.path, $.fields,
                            1).expectedDateTo) ?
                            $DateDiff($DateFormat($getFieldGroup($.extend.path,
                            $.fields, 1).expectedDateTo, 'yyyy-MM-DD'),
                            $DateFormat($getFieldGroup($.extend.path, $.fields,
                            1).startDate, 'yyyy-MM-DD'), 'd') < 1
                        text: Expected End Date must be greater than Start Date
                    _condition:
                      transform: $not($.extend.formType = 'view')
                  - type: dateRange
                    label: Expected End Date
                    name: expectedDateTo
                    mode: date-picker
                    _condition:
                      transform: $.extend.formType = 'view'
                  - type: dateRange
                    mode: date-picker
                    label: End Date
                    name: endDate
                    placeholder: dd/MM/yyyy
                    validators:
                      - type: ppx-custom
                        args:
                          transform: >-
                            $exists($getFieldGroup($.extend.path, $.fields,
                            1).startDate) and
                            $exists($getFieldGroup($.extend.path, $.fields,
                            1).endDate) ?
                            $DateDiff($DateFormat($getFieldGroup($.extend.path,
                            $.fields, 1).endDate, 'yyyy-MM-DD'),
                            $DateFormat($getFieldGroup($.extend.path, $.fields,
                            1).startDate, 'yyyy-MM-DD'), 'd') < 0
                        text: End Date must be equal or greater than Start Date
                      - type: ppx-custom
                        id: validateEndDate
                        args:
                          transform: >-
                            $exists($.fields.endDate) ?
                            $DateDiff($DateFormat($.fields.endDate,
                            'yyyy-MM-DD'),
                            $DateFormat($getFieldGroup($.extend.path, $.fields,
                            1).endDate, 'yyyy-MM-DD'), 'd') < 0
                        text: End Date must be equal or less than Contract End Date
                      - type: ppx-custom
                        id: checkExistEndDate
                        args:
                          transform: >-
                            $exists($.fields.endDate) and
                            $not($exists($getFieldGroup($.extend.path, $.fields,
                            1).endDate))
                        text: End Date must be equal or less than Contract End Date
                    _condition:
                      transform: $not($.extend.formType = 'view')
                  - type: dateRange
                    label: End Date
                    name: endDate
                    mode: date-picker
                    _condition:
                      transform: $.extend.formType = 'view'
                  - type: dateRange
                    mode: date-picker
                    label: Sign Date
                    name: signDate
                    placeholder: dd/MM/yyyy
              - type: text
                label: Reason
                name: reason
                placeholder: Enter Reason
              - type: textarea
                label: Note
                name: note
                placeholder: Enter Note
                textarea:
                  autoSize:
                    minRows: 3
                  maxCharCount: 1000
                validators:
                  - type: maxLength
                    args: '1000'
                    text: Maximum 1000 characters
  historyTitle: >-
    $DateFormat($.startDate, 'DD/MM/YYYY') & ' - ' &  ($exists($.endDate) ?
    ($valueEndDate := $.endDate; $DateFormat($valueEndDate, 'DD/MM/YYYY')) :
    $exists($.expectedDateTo)? $DateFormat($.expectedDateTo, 'DD/MM/YYYY') :
    '31/12/9999' )
  historyDescription: $.contractNumber & ' - ' & $.contractTypeName
  _mode:
    transform: '$not($.extend.formType = ''view'') ? ''mark-scroll'' : ''tabset'''
  sources:
    contractHistory:
      uri: '"/api/personals/" & $.empId & "/contracts/histories"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'employeeRecordNumber','operator':
        '$eq','value':$.employeeRecordNumber}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $
      disabledCache: true
      params:
        - empId
        - employeeRecordNumber
    jobDataHistory:
      uri: '"/api/personals/" & $.empId & "/job-datas/histories"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'employeeRecordNumber','operator':
        '$eq','value':$.employeeRecordNumber}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $
      disabledCache: true
      params:
        - empId
        - employeeRecordNumber
    getContractDuration:
      uri: '"/api/contract-durations/" & $.contractTypeCode & "/histories"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $
      disabledCache: true
      params:
        - contractTypeCode
    jobDatasList:
      uri: '"/api/personals/" & $.empId & "/job-datas"'
      method: GET
      queryTransform: '{''limit'': $.limit}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $distinct($map($.data, function($item) {{'label':
        $item.employeeRecordNumber & ' - ' & $item.companyName & ' - ' &
        $item.departmentName & ' - ' & $item.jobName, 'value':
        $item.employeeRecordNumber}})[])[]
      disabledCache: true
      params:
        - limit
        - empId
    contractTypeList:
      uri: '"/api/picklists/CONTRACTTYPE/values"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - effectiveDate
    unitList:
      uri: '"/api/picklists/CONVERSIONUNIT/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
    isFirstContract:
      uri: '"/api/personals/" & $.empId & "/contracts/check-first-record"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $
      disabledCache: true
      params:
        - empId
    checkContractDuration:
      uri: '"/api/personals/" & $.empId & "/contracts/check-contract-duration"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'employeeRecordNumber','operator':
        '$eq','value':$.employeeRecordNumber},{'field':'startDate','operator':
        '$eq','value':$.startDate},{'field':'endDate','operator':
        '$eq','value':$.endDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $
      disabledCache: true
      params:
        - empId
        - employeeRecordNumber
        - startDate
        - endDate
    checkContractDurationEditScreen:
      uri: '"/api/personals/" & $.empId & "/contracts/check-contract-duration"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'excludeContractId','operator':
        '$eq','value':$.id},{'field':'employeeRecordNumber','operator':
        '$eq','value':$.employeeRecordNumber},{'field':'startDate','operator':
        '$eq','value':$.startDate},{'field':'endDate','operator':
        '$eq','value':$.endDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $
      disabledCache: true
      params:
        - empId
        - id
        - employeeRecordNumber
        - startDate
        - endDate
  variables:
    _unitList:
      transform: $unitList()
    _jobDataHistory:
      transform: >-
        $.extend.addOnValue.id1 and $exists($.fields.employeeRecordNumber) ?
        $jobDataHistory($.extend.addOnValue.id1, $.fields.employeeRecordNumber)
    _getJobDataByMinEffectiveDate:
      transform: >-
        $count($.variables._jobDataHistory) > 0 ?
        $filter($.variables._jobDataHistory, function($item) {
        $toMillis($item.effectiveDate) = $min($map($.variables._jobDataHistory,
        function($i) { $toMillis($i.effectiveDate) }))}) : null
    _getContractDuration:
      transform: >-
        $exists($.fields.contractTypeCode) ?
        $getContractDuration($.fields.contractTypeCode)
    _filterContractDuration:
      transform: >-
        $count($.variables._getContractDuration) > 0 ?
        $filter($.variables._getContractDuration, function($item) {
        $DateToTimestampUTC($item.effectiveDate) <=
        $DateToTimestampUTC($.fields.startDate)})[] : []
    _getMaxEffectiveDate:
      transform: >-
        $count($.variables._filterContractDuration) > 0 ?
        $filter($.variables._filterContractDuration, function($item) {
        $toMillis($item.effectiveDate) =
        $max($map($.variables._filterContractDuration, function($i) {
        $toMillis($i.effectiveDate) }))}) : null
    _isFirstContract:
      transform: >-
        $.extend.addOnValue.id1 and $not($.extend.formType = 'view') ?
        $isFirstContract($.extend.addOnValue.id1)
    _caculationEndDate:
      transform: >-
        ($unitCode := $.variables._getMaxEffectiveDate.unitCode; $test :=
        $unitCode = 'CU_N' ? 'y' : $unitCode = 'CU_T' ? 'M': 'd'; $duration :=
        $.variables._getMaxEffectiveDate.duration;
        $not($boolean($.fields.checkChangeStartDate)) and
        $exists($.fields.startDate) and $not($isNilorEmpty($test)) and
        $not($isNilorEmpty($duration)) ? $CalDate($CalDate($.fields.startDate,
        $duration, $test),-1,'d'))
    _contractHistory:
      transform: >-
        $.extend.addOnValue.id1 and $exists($.fields.employeeRecordNumber) ?
        $contractHistory($.extend.addOnValue.id1, $.fields.employeeRecordNumber)
    _filterContractById:
      transform: >-
        $filter($.variables._contractHistory, function($v) { $v.id = $.fields.id
        })
    _checkContractDuration:
      transform: >-
        ($.extend.addOnValue.id1 and $.extend.formType = 'create') ?
        $checkContractDuration($.extend.addOnValue.id1,
        $.fields.employeeRecordNumber, $.fields.startDate, $.fields.endDate) :
        ($.extend.addOnValue.id1 and $.extend.formType = 'edit') ?
        $checkContractDurationEditScreen($.extend.addOnValue.id1, $.fields.id,
        $.fields.employeeRecordNumber, $.fields.startDate, $.fields.endDate)
filter_config:
  fields:
    - type: select
      name: employeeRecordNumber
      label: Employee Record Number
      labelType: type-row
      outputValue: value
      placeholder: Select Employee Record Number
      _select:
        transform: >-
          ( $empId := $isNilorEmpty($.extend.addOnValue.id1 ) ?
          $.extend.defaultValue.employeeId :$.extend.addOnValue.id1;
          $jobDatasList(1000, $empId))
      _condition:
        transform: $.extend.formType = 'inlineFilter'
    - type: group
      padding: 10px 20px
      fieldBackground: '#F1F3F5'
      _condition:
        transform: $not($.extend.formType = 'inlineFilter')
      fields:
        - type: select
          name: employeeRecordNumber
          label: Employee Record Number
          labelType: flex-row
          outputValue: value
          placeholder: Select Employee Record Number
          _select:
            transform: >-
              ( $empId := $isNilorEmpty($.extend.addOnValue.id1 ) ?
              $.extend.defaultValue.employeeId :$.extend.addOnValue.id1;
              $jobDatasList(1000, $empId))
  filterMapping:
    - field: employeeRecordNumber
      operator: $eq
      valueField: employeeRecordNumber
  sources:
    jobDatasList:
      uri: '"/api/personals/" & $.empId & "/job-datas"'
      method: GET
      queryTransform: '{''limit'': $.limit}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $distinct($map($.data, function($item) {{'label':
        $item.employeeRecordNumber & ' - ' & $item.companyName & ' - ' &
        $item.departmentName & ' - ' & $item.jobName, 'value':
        $item.employeeRecordNumber}})[])[]
      disabledCache: true
      params:
        - limit
        - empId
layout_options:
  show_dialog_form_save_add_button: true
  show_table_search: false
  show_history_insert_button: true
  show_table_checkbox: false
  n_cols: 2
  is_upload_file: true
  history_widget_header_options:
    duplicate: false
    title: ''
  is_layout_widget: true
  precondition_filter:
    is_auto_filter: true
    is_auto_sync_filter: false
  is_copy_data_insert_new: false
  show_table_filter: false
  show_tool_table: false
  show_history_filter_form: true
  show_table_pagination: false
  view_history_after_created: true
  view_history_after_updated: true
  widget_options:
    show_more_type: hidden
    is_full_height: true
    refeshDependent:
      - HR.FS.FR.009
  is_new_dynamic_form: true
  hide_precondition_filter_if_empty: true
  is_check_permission_with_accessType: true
  check_permission_record_history: true
layout_options__header_buttons:
  - id: create
    icon: icon-plus-bold
    type: ghost-gray
    title: null
  - id: history
    title: History
    icon: icon-clock-counter-clockwise-bold
    type: tertiary
options: null
create_form: null
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: view
    title: Detail
    icon: icon-eye
    type: ghost-gray
backend_url: api/personals/:id1/contracts
screen_name: null
layout_options__actions_many: null
parent: HR.FS.FR.000
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key: null
screen_designs: []
function_specs: []
fields: []
children: []
menu_item: null
