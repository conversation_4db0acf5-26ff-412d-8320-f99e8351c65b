id: HR.FS.FR.046_03
status: draft
sort: 341
user_created: 56e71b91-dd39-4ebc-afd1-b4f00f38d4a5
date_created: '2024-09-27T15:32:52.364Z'
user_updated: 56e71b91-dd39-4ebc-afd1-b4f00f38d4a5
date_updated: '2025-07-28T04:07:47.108Z'
title: Add Person of Interest Instance
requirement:
  time: 1744859927560
  blocks:
    - id: afoVedzfOd
      type: paragraph
      data:
        text: >-
          - <PERSON><PERSON><PERSON> năng cho phép bộ phận nhân sự Tập đoàn/CTTV thêm mới hồ sơ tổ
          chức cho nhân viên (<PERSON><PERSON> sơ tổ chức cho nhóm nhân sự cấp 1 là Employee)
          thuộc vùng dữ liệu được phân quyền của user.
    - id: Q866MTm2WG
      type: paragraph
      data:
        text: >-
          - V<PERSON><PERSON><PERSON> thêm mới hồ sơ tổ chức (Organizational Instance Record) được
          thực hiện trong các trường hợp sau:
    - id: 3B8wxNsIc6
      type: paragraph
      data:
        text: >-
          + Nhân viên đang công tác tại 1 Công ty thành viên (Company) và tuyển
          mới tại công ty thành viên khác.
    - id: VBJllRFpLa
      type: paragraph
      data:
        text: >-
          + Nhân viên có thểm một mối quan hệ mới (Employee Group) với công ty.
          (VD: Nhân viên thuộc nhóm Employee có thêm mối quan hệ là Cộng tác
          viên với chính công ty đó hoặc với công ty khác).
  version: 2.30.7
screen_design: null
module: HR
local_fields:
  - code: code
    title: code
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
mock_data: []
local_buttons: null
layout: layout-form
form_config:
  _mode:
    transform: $not($.extend.formType = 'proceed') ? 'mark-scroll'
  isFilterRight: true
  formSize:
    create: large
    edit: large
    proceed: large
  fields:
    - type: group
      label: Organizational Instance
      n_cols: 2
      collapse: false
      disableEventCollapse: true
      fields:
        - type: text
          name: companyObj
          unvisible: true
        - type: text
          name: primaryJob
          unvisible: true
        - type: text
          name: employeeId
          unvisible: true
        - name: employmentData
          label: employment
          type: text
          unvisible: true
        - type: text
          name: employeeGroupCode
          unvisible: true
        - name: organizationalInstanceRecord
          label: organizationalInstanceRecord
          type: number
          unvisible: true
        - name: employeeRecordNumber
          label: employeeRecordNumber
          type: number
          unvisible: true
        - type: dateRange
          name: effectiveDate
          placeholder: dd/MM/yyyy
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
          unvisible: true
        - type: text
          label: Organizational Instance Rcd
          name: organizationalInstanceRcd
          disabled: true
          _value:
            transform: >-
              $.fields.organizationalInstanceRecord ?
              $string($.fields.organizationalInstanceRecord) : '0'
        - name: employeeRecordNumber1
          label: Employee Number Record
          type: number
          disabled: true
          _value:
            transform: $.fields.employeeRecordNumber
    - type: group
      label: Action
      n_cols: 2
      collapse: false
      fields:
        - type: dateRange
          mode: date-picker
          label: Effective Date
          name: effectiveDate1
          disabled: true
          _value:
            transform: >-
              $exists($.fields.effectiveDate) ?
              $DateFormat($.fields.effectiveDate, 'YYYY/MM/DD') : $now()
          validators:
            - type: required
        - type: select
          label: Employee Group
          name: employeeGroupView
          placeholder: Select Employee Group
          disabled: true
          _value:
            transform: $.fields.employeeGroupCode
          outputValue: value
          validators:
            - type: required
          _select:
            transform: >-
              $exists($.fields.effectiveDate) ?
              $employeeGroupsList($.fields.effectiveDate)
        - type: text
          name: employeeGroupCode
          unvisible: true
          _value:
            transform: $.fields.employeeGroupCode
        - type: select
          label: Action
          name: actionCode1
          placeholder: Select Action
          clearFieldsAfterChange:
            - actionReasonCode1
          _select:
            transform: $.variables._actionsList
          _value:
            transform: >-
              $count($.variables._actionsList) < 2 ?
              $.variables._actionsList[0].value
          outputValue: value
          validators:
            - type: required
        - type: select
          label: Job Indicator
          name: jobIndicatorCode
          placeholder: Select Job Indicator
          value: S
          outputValue: value
          _select:
            transform: >-
              $exists($.fields.effectiveDate) ?
              $jobIndicatorList($.fields.effectiveDate)
          validators:
            - type: required
        - type: select
          label: Reason
          name: actionReasonCode1
          placeholder: Select Reason
          outputValue: value
          _select:
            transform: >-
              ( $actionCode:=  $.fields.actionCode1; $selectdAction:=
              $.variables._actionsList[value=$actionCode]; $actionReasons :=
              $selectdAction.actionReasons ; $exists($actionReasons) and
              $count($actionReasons) > 0 ?
              $reasonsList($actionReasons,$.fields.effectiveDate,$count($actionReasons))
              ) 
          validators:
            - type: required
        - type: select
          label: Employee Sub Group
          name: employeeSubGroupCode1
          placeholder: Select Employee Sub Group
          outputValue: value
          isLazyLoad: true
          _select:
            transform: >-
              $exists($.fields.effectiveDate) ?
              $employeeSubGroupsList($.fields.employeeGroupCode,
              $.fields.effectiveDate)
          validators:
            - type: required
        - type: text
          label: Effective Sequence
          name: effectiveSequence1
          placeholder: Select Effective Sequence
          value: '0'
          disabled: true
          validators:
            - type: required
        - type: select
          label: Manager?
          name: isManagerCode1
          outputValue: value
          placeholder: Select Manager?
          isLazyLoad: true
          _select:
            transform: >-
              $exists($.fields.effectiveDate) ?
              $managerList($.fields.effectiveDate)
          validators:
            - type: required
        - type: radio
          label: HR Status
          name: hrStatusCode1
          outputValue: value
          _radio:
            transform: >-
              $exists($.fields.effectiveDate) ?
              $hrStatusList($.fields.effectiveDate)
          _value:
            transform: $.variables._selectedAction.hrStatusCode
          validators:
            - type: required
        - type: select
          label: Payroll Status
          name: prStatusCode1
          outputValue: value
          _select:
            transform: >-
              $exists($.fields.effectiveDate) ?
              $payrollStatusList($.fields.effectiveDate)
          _value:
            transform: $.variables._selectedAction.prStatusCode
          validators:
            - type: required
        - type: select
          label: Level of decision
          name: levelDecisionCode1
          placeholder: Select Level of decision
          outputValue: value
          isLazyLoad: true
          _select:
            transform: >-
              $exists($.fields.effectiveDate) ?
              $decisionLevelList($.fields.effectiveDate)
        - type: text
          label: Decision number
          name: decisionNumber1
          placeholder: Enter Decision Number
        - type: dateRange
          label: Sign date
          name: signDate1
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
        - type: dateRange
          label: Expected end date
          name: expectedEndDate1
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
          validators:
            - type: ppx-custom
              args:
                transform: >-
                  $exists($.fields.expectedEndDate1) ?
                  $DateDiff($DateFormat($.fields.effectiveDate, 'yyyy-MM-DD'),
                  $DateFormat($.fields.expectedEndDate1, 'yyyy-MM-DD'), 'd') > 0
              text: >-
                The Expected End Date must be more than or equal to the
                Effective Date
        - type: textarea
          label: Note
          name: note1
          placeholder: Enter Note
          col: 2
          validators:
            - type: maxLength
              args: '500'
              text: Maximum length is 500 characters.
          textarea:
            autoSize:
              minRows: 3
            maxCharCount: 500
    - type: group
      label: Assignment
      collapse: false
      name: jobData
      n_cols: 2
      fields:
        - type: text
          name: employeeId
          unvisible: true
          _value:
            transform: $.fields.employeeId
        - type: text
          name: previousJobDataId
          unvisible: true
          _value:
            transform: $.fields.primaryJob
        - type: text
          name: processType
          unvisible: true
          value: '2'
        - type: text
          name: primaryJob3
          unvisible: true
        - type: text
          label: Employee Number Record
          name: employeeRecordNumber
          placeholder: Select Employee Number Record
          unvisible: true
          _value:
            transform: >-
              $.fields.employeeRecordNumber ?
              $string($.fields.employeeRecordNumber) : '0'
        - type: text
          label: Organizational Instance Rcd
          name: organizationalInstanceRcd
          unvisible: true
          _value:
            transform: >-
              $.fields.organizationalInstanceRecord ?
              $string($.fields.organizationalInstanceRecord) : '0'
        - type: dateRange
          mode: date-picker
          label: Effective Date
          name: effectiveDate
          unvisible: true
          _value:
            transform: >-
              $exists($.fields.effectiveDate) ?
              $DateFormat($.fields.effectiveDate, 'YYYY/MM/DD') : $now()
        - name: jobIndicatorCode
          label: jobIndicatorCode
          type: text
          value: P
          unvisible: true
          _value:
            transform: $.fields.jobIndicatorCode
        - name: actionCode
          label: Action
          type: text
          unvisible: true
          _value:
            transform: $.fields.actionCode1
        - name: actionReasonCode
          label: Reason
          type: text
          placeholder: Select Reason
          unvisible: true
          _value:
            transform: >-
              $.fields.actionReasonCode1 ? $.fields.actionReasonCode1 :
              '_setValueNull'
        - name: effectiveSequence
          label: Effective Sequence
          type: number
          unvisible: true
          _value:
            transform: $.fields.effectiveSequence1
        - name: hrStatusCode
          label: hrStatusCode
          type: text
          unvisible: true
          _value:
            transform: '$.fields.hrStatusCode1 ? $.fields.hrStatusCode1 : ''_setValueNull'''
        - name: isPrimary
          label: HR Status
          type: text
          unvisible: true
          value: true
        - name: prStatusCode
          label: prStatusCode
          type: text
          unvisible: true
          _value:
            transform: '$.fields.prStatusCode1 ? $.fields.prStatusCode1 : ''_setValueNull'''
        - type: text
          name: employeeGroupCode
          unvisible: true
          _value:
            transform: >-
              $.fields.employeeGroupCode ? $.fields.employeeGroupCode :
              '_setValueNull'
        - name: employeeSubGroupCode
          label: Employee Sub Group
          type: text
          unvisible: true
          _value:
            transform: >-
              $.fields.employeeSubGroupCode1 ? $.fields.employeeSubGroupCode1 :
              '_setValueNull'
        - type: text
          label: Manager?
          name: isManagerCode
          unvisible: true
          _value:
            transform: >-
              $.fields.isManagerCode1 ? $.fields.isManagerCode1 :
              '_setValueNull'
        - name: levelDecisionCode
          label: Level Of Decision
          type: text
          unvisible: true
          _value:
            transform: >-
              $.fields.levelDecisionCode1 ? $.fields.levelDecisionCode1 :
              '_setValueNull'
        - type: text
          label: Decision number
          name: decisionNumber
          unvisible: true
          _value:
            transform: >-
              $.fields.decisionNumber1 ? $.fields.decisionNumber1 :
              '_setValueNull'
        - type: text
          name: signDate
          label: Sign Date
          unvisible: true
          _value:
            transform: '$exists($.fields.signDate1) ? $.fields.signDate1 : ''_setValueNull'''
        - type: text
          name: expectedEndDate
          label: Sign Date
          unvisible: true
          _value:
            transform: >-
              $exists($.fields.expectedEndDate1) ? $.fields.expectedEndDate1 :
              '_setValueNull'
        - type: text
          name: note
          unvisible: true
          _value:
            transform: '$.fields.note1 ? $.fields.note1 : ''_setValueNull'''
        - type: select
          label: Position
          name: positionObj
          isLazyLoad: true
          placeholder: Select Position
          clearFieldsAfterChange:
            - positionCode
          options:
            enabledLoadMore: false
          handleAfterChange:
            dataSource:
              transform: >-
                $organizationPick('Position', $.fieldValue.value,
                $DateToTimestampUTC($.fields.effectiveDate))
            valueMapping:
              - field: legalEntityObj
                fieldValue: LegalEntity
              - field: businessUnitObj
                fieldValue: BusinessUnit
                _setNullValue: $isNilorEmpty($.BusinessUnit)
              - field: divisionObj
                fieldValue: Division
                _setNullValue: $isNilorEmpty($.Division)
              - field: departmentObj
                fieldValue: Department
              - field: costCenterObj
                fieldValue: CostCenter
              - field: reportPositionObj
                fieldValue: DirectPosition
              - field: locationObj
                fieldValue: Location
              - field: jobObj
                fieldValue: JobCode
              - field: careerStreamObj
                fieldValue: CareerStream
              - field: careerBandObj
                fieldValue: Band
              - field: regionCode
                fieldValue: Region.value
              - field: businessTitleObj
                fieldValue: BusinessTitle
              - field: supervisorObj
                fieldValue: noName
                _setNullValue: $not($isNilorEmpty($.DirectPosition))
          _select:
            transform: >-
              $positionsList($.fields.companyObj.value,$.fields.effectiveDate,$.fields.jobData.legalEntityObj.value,
              $.fields.jobData.departmentObj.value,
              $.fields.jobData.jobObj.value)
        - type: text
          name: positionCode
          label: Position
          unvisible: true
          _value:
            transform: >-
              $.fields.jobData.positionObj ? $.fields.jobData.positionObj.value
              : '_setValueNull'
        - type: select
          label: Cost Center
          name: costCenterObj
          placeholder: Select Cost Center
          isLazyLoad: true
          isRemoveOptionNotExist: true
          _select:
            transform: >-
              ($companyCode := $.fields.companyObj.value ?
              $.fields.companyObj.value; $constCenterList($companyCode,
              $.extend.limit, $.extend.page,
              $.extend.search,$.fields.effectiveDate))
        - type: text
          name: costCenterCode
          unvisible: true
          _value:
            transform: >-
              $.fields.jobData.costCenterObj ?
              $.fields.jobData.costCenterObj.value : '_setValueNull'
        - type: select
          name: companyObj1
          label: Company
          placeholder: Select Company
          disabled: true
          _value:
            transform: $.fields.companyObj
          _select:
            transform: $append([], $.fields.companyObj)[]
          validators:
            - type: required
        - type: text
          name: companyCode
          unvisible: true
          _value:
            transform: $exists($.fields.companyObj) ? $.fields.companyObj.value
        - name: reportPositionObj
          label: Report To Pos
          type: select
          isLazyLoad: true
          isRemoveOptionNotExist: true
          clearFieldsAfterChange:
            - supervisor
            - supervisorObj
            - reportPosition
          _select:
            transform: >-
              $positionsListGetBy($.fields.effectiveDate, $.extend.limit,
              $.extend.page, $.extend.search)
          placeholder: Select Report To Pos
        - type: text
          name: reportPosition
          unvisible: true
          _value:
            transform: >-
              $.fields.jobData.reportPositionObj ?
              $.fields.jobData.reportPositionObj.value : '_setValueNull'
        - type: select
          label: Legal Entity
          name: legalEntityObj
          placeholder: Select Legal Entity
          isLazyLoad: true
          isRemoveOptionNotExist: true
          clearFieldsAfterChange:
            - departmentObj
            - positionObj
            - legalEntityCode
          options:
            enabledLoadMore: false
          _select:
            transform: $legalEntityList($.fields.companyObj.value,$.fields.effectiveDate)
          validators:
            - type: required
        - type: text
          name: legalEntityCode
          unvisible: true
          _value:
            transform: >-
              $.fields.jobData.legalEntityObj ?
              $.fields.jobData.legalEntityObj.value :'_setValueNull'
        - name: supervisorObj
          label: Supervisor
          type: select
          isLazyLoad: true
          isRemoveOptionNotExist: true
          clearFieldsAfterChange:
            - reportPosition
            - reportPositionObj
            - supervisor
          _select:
            transform: >-
              $personalsList($.extend.limit, $.extend.page, $.extend.search,
              $.fields.effectiveDate, 'A')
          placeholder: Select Supervisor
        - type: text
          name: supervisor
          unvisible: true
          _value:
            transform: >-
              $.fields.jobData.supervisorObj ?
              $.fields.jobData.supervisorObj.value : '_setValueNull'
        - type: select
          label: Business Unit
          name: businessUnitObj
          placeholder: Select Business Unit
          isRemoveOptionNotExist: true
          options:
            enabledLoadMore: false
          isLazyLoad: true
          clearFieldsAfterChange:
            - divisionObj
            - divisionCode
            - departmentObj
            - departmentCode
            - positionObj
            - positionCode
            - businessUnitCode
          _select:
            transform: >-
              $businessUnitList($.fields.companyObj.value,$.fields.effectiveDate)
        - type: text
          name: businessUnitCode
          unvisible: true
          _value:
            transform: >-
              $.fields.jobData.businessUnitObj ?
              $.fields.jobData.businessUnitObj.value : '_setValueNull'
        - name: matrixReportPositionCodes
          label: Matrix Report To
          type: selectAll
          mode: multiple
          outputValue: value
          isRemoveOptionNotExist: true
          isLazyLoad: true
          _value:
            transform: ' $exists($.variables._positionInfo.matrixPositionObj) ? $map($.variables._positionInfo.matrixPositionObj, function($it){{ ''label'': $it.label, ''value'': $it.value.code }})'
          _options:
            transform: >-
              $positionsListGetBy($.fields.effectiveDate, $.extend.limit,
              $.extend.page, $.extend.search)
          placeholder: Select Matrix Report To
        - type: select
          label: Division
          name: divisionObj
          placeholder: Select Division
          isRemoveOptionNotExist: true
          isLazyLoad: true
          options:
            enabledLoadMore: false
          handleAfterChange:
            dataSource:
              transform: $divisionDetail($.fieldValue.id)
            valueMapping:
              - field: businessUnitObj
                fieldValue: BusinessUnit
          clearFieldsAfterChange:
            - positionObj
            - divisionCode
            - positionCode
            - departmentObj
          _select:
            transform: >-
              $divisionsList($.fields.companyObj.value,$.fields.jobData.businessUnitObj.value,$.fields.effectiveDate)
        - type: text
          name: divisionCode
          unvisible: true
          _value:
            transform: >-
              $.fields.jobData.divisionObj ? $.fields.jobData.divisionObj.value
              : '_setValueNull'
        - name: matrixManagers
          label: Matrix Manager
          type: selectAll
          mode: multiple
          outputValue: value
          isRemoveOptionNotExist: true
          isLazyLoad: true
          _options:
            transform: >-
              $personalsList($.extend.limit, $.extend.page, $.extend.search,
              $.fields.effectiveDate, 'A')
          placeholder: Select Matrix Manager
        - type: select
          label: Department
          name: departmentObj
          placeholder: Select Department
          isRemoveOptionNotExist: true
          isLazyLoad: true
          clearFieldsAfterChange:
            - positionObj
            - departmentCode
          options:
            enabledLoadMore: false
          handleAfterChange:
            dataSource:
              transform: >-
                ($dataObj := {'organizationPickData':
                $organizationPick('Department', $.fieldValue.value,
                $DateToTimestampUTC($.fields.effectiveDate)), 'detail':
                $departmentInfo($.fieldValue.id)} ;
                $merge([$dataObj.organizationPickData,{'ManagerPosition':
                $not($dataObj.detail.managerType = 'Position') ? null :
                $dataObj.organizationPickData.ManagerPosition ,'Employee':
                $not($dataObj.detail.managerType = 'Employee') ? null :
                ($exists($dataObj.detail.headOfDepartmentObj.value.code) ?
                {'label': $dataObj.detail.headOfDepartmentObj.label & ' (' &
                $dataObj.detail.headOfDepartmentObj.value.code & ')', 'value':
                $dataObj.detail.headOfDepartmentObj.value.code} :
                $dataObj.organizationPickData.Employee) }]))
            valueMapping:
              - field: legalEntityObj
                fieldValue: LegalEntity
              - field: businessUnitObj
                fieldValue: BusinessUnit
                _setNullValue: $isNilorEmpty($.BusinessUnit)
              - field: divisionObj
                fieldValue: Division
                _setNullValue: $isNilorEmpty($.Division)
              - field: locationObj
                fieldValue: Location
              - field: regionCode
                fieldValue: Region.value
              - field: costCenterObj
                fieldValue: CostCenter
              - field: reportPositionObj
                fieldValue: ManagerPosition
                _setNullValue: $not($isNilorEmpty($.Employee))
              - field: supervisorObj
                fieldValue: Employee
                _setNullValue: $not($isNilorEmpty($.ManagerPosition))
          _select:
            transform: >-
              $departmentsList($.fields.companyObj.value,
              $.fields.jobData.legalEntityObj.value,
              $.fields.jobData.businessUnitObj.value,
              $.fields.jobData.divisionObj.value, $.fields.effectiveDate)
          validators:
            - type: required
        - type: text
          label: Department
          name: departmentCode
          _value:
            transform: >-
              $.fields.jobData.departmentObj ?
              $.fields.jobData.departmentObj.value : '_setValueNull'
          unvisible: true
        - type: dateRange
          label: Department Entry Date
          name: departmentEntryDate
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
          _value:
            transform: >-
              $exists($.fields.effectiveDate) ?
              $DateFormat($.fields.effectiveDate, 'YYYY/MM/DD') : $now()
          placeholder: dd/MM/yyyy
        - type: select
          label: Location
          name: locationObj
          placeholder: Select Location
          isLazyLoad: true
          isRemoveOptionNotExist: true
          _select:
            transform: >-
              $locationsList($.extend.limit, $.extend.page,
              $.extend.search,$.fields.effectiveDate, $.fields.companyObj.value)
          validators:
            - type: required
        - type: text
          name: locationCode
          unvisible: true
          _value:
            transform: >-
              $.fields.jobData.locationObj ? $.fields.jobData.locationObj.value
              : '_setValueNull'
        - type: select
          label: Business Title
          name: businessTitleObj
          isLazyLoad: true
          placeholder: Select Business Title
          isRemoveOptionNotExist: true
          _select:
            transform: >-
              $.fields.effectiveDate ? ($companyCode :=
              $.fields.companyObj.value ? $.fields.companyObj.value;
              $businessTitlesList($companyCode, $.extend.limit, $.extend.page,
              $.extend.search,$.fields.effectiveDate))
        - type: text
          name: businessTitleCode
          unvisible: true
          _value:
            transform: >-
              $.fields.jobData.businessTitleObj ?
              $.fields.jobData.businessTitleObj.value : '_setValueNull'
        - type: select
          label: Region
          name: regionCode
          placeholder: Select Region
          isRemoveOptionNotExist: true
          outputValue: value
          _select:
            transform: >-
              $exists($.fields.effectiveDate) ?
              $regionList($.fields.effectiveDate)
        - type: select
          label: Full/Part
          name: fullPartCode
          placeholder: Select Full/Part
          outputValue: value
          _select:
            transform: >-
              $exists($.fields.effectiveDate) ?
              $fullPartList($.fields.effectiveDate)
          _value:
            transform: '$.variables._getJobdataPrimary = 0  ? ''F'' '
          validators:
            - type: required
        - type: select
          label: Job
          name: jobObj
          placeholder: Select Job
          clearFieldsAfterChange:
            - positionObj
            - careerStreamObj
            - careerBandObj
          options:
            customSetValue: true
          isRemoveOptionNotExist: true
          isLazyLoad: true
          _select:
            transform: >-
              $jobCodesList($.fields.companyObj.value, $.extend.limit,
              $.extend.page, $.extend.search,$.fields.effectiveDate)
          handleAfterChange:
            dataSource:
              transform: >-
                { 'band': $.fieldValue.bandId ?
                $careerBandDetail($.fieldValue.bandId).band : null, 'stream':
                $.fieldValue.careerStreamId ?
                $careerStreamDetail($.fieldValue.careerStreamId).careerStream :
                null }
            valueMapping:
              - field: careerStreamObj
                fieldValue: stream
              - field: careerBandObj
                fieldValue: band
          validators:
            - type: required
        - type: text
          name: jobCode
          unvisible: true
          _value:
            transform: >-
              $.fields.jobData.jobObj ? $.fields.jobData.jobObj.value :
              '_setValueNull'
        - type: number
          label: FTE
          name: fte
          placeholder: Enter FTE
          value: '1.00000'
          validators:
            - type: required
        - type: text
          name: totalFte
          unvisible: true
          _value:
            transform: $.variables._checkTotalFte.fte
        - type: select
          label: Career Stream
          name: careerStreamObj
          isLazyLoad: true
          isRemoveOptionNotExist: true
          clearFieldsAfterChange:
            - careerBandObj
          placeholder: Select Career Stream
          _select:
            transform: >-
              $careerStreamsList($.extend.limit, $.extend.page,
              $.extend.search,$.fields.effectiveDate)
        - type: text
          name: careerStreamCode
          unvisible: true
          _value:
            transform: >-
              $.fields.jobData.careerStreamObj ?
              $.fields.jobData.careerStreamObj.value
        - type: select
          label: Emp Level
          name: empLevelCode
          placeholder: Select Emp Level
          outputValue: value
          isLazyLoad: true
          _select:
            transform: >-
              $exists($.fields.effectiveDate) ? $empLevelList($.extend.limit,
              $.extend.page, $.extend.search, $.fields.effectiveDate)
          validators:
            - type: required
        - type: selectCustom
          label: Career Band
          name: careerBandObj
          isLazyLoad: true
          isRemoveOptionNotExist: true
          placeholder: Select Career Band
          handleAfterChange:
            dataSource:
              transform: $careerStreamDetail($.fieldValue.careerStreamId)
            valueMapping:
              - field: careerStreamObj
                fieldValue: careerStream
          _select:
            transform: >-
              $careerBandsList($.fields.jobData.careerStreamObj.value,
              $.extend.limit, $.extend.page,
              $.extend.search,$.fields.effectiveDate)
        - type: text
          name: careerBandCode
          unvisible: true
          _value:
            transform: >-
              $.fields.jobData.careerBandObj ?
              $.fields.jobData.careerBandObj.value
        - type: select
          label: Time Zone
          name: timeZoneCode
          placeholder: Select Time zone
          outputValue: value
          _select:
            transform: >-
              $exists($.fields.effectiveDate) ?
              $timezoneList($.fields.effectiveDate)
        - type: upload
          label: Attach File
          name: file
          status: true
          col: 2
          upload:
            size: 100
            isMultiple: true
            accept:
              - application/pdf
              - >-
                application/vnd.openxmlformats-officedocument.wordprocessingml.document
              - application/msword
              - >-
                application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
              - application/vnd.ms-excel
              - image/jpeg
              - image/png
            customContentUpload: >-
              or drop it here PDF, XLS, XLSX, DOC, DOCX, JPG, PNG only (Max
              100MB)
    - type: group
      label: Employment
      name: employment
      n_cols: 2
      collapse: false
      fields:
        - type: text
          name: employeeId
          unvisible: true
          _value:
            transform: $.fields.employeeId
        - type: text
          label: Organizational Instance Rcd
          name: organizationalInstanceRcd
          col: 2
          disabled: true
          _value:
            transform: >-
              $.fields.organizationalInstanceRecord ?
              $string($.fields.organizationalInstanceRecord) : '0'
          validators:
            - type: required
        - type: dateRange
          name: groupOriginalStartDate
          mode: date-picker
          label: Group Original Start Date
          validators:
            - type: required
          _value:
            transform: >-
              $exists($.fields.employmentData.groupOriginalStartDate) ?
              $.fields.employmentData.groupOriginalStartDate :
              $.fields.effectiveDate
          setting:
            format: dd/MM/yyyy
            type: date
        - type: dateRange
          name: groupFirstStartDate
          mode: date-picker
          label: Group First Start Date
          _value:
            transform: >-
              $.variables._calculationEmployment.groupFirstStartDate ?
              $.variables._calculationEmployment.groupFirstStartDate :
              $.fields.employmentData.groupFirstStartDate
          validators:
            - type: required
          disabled: true
          setting:
            format: dd/MM/yyyy
            type: date
        - type: dateRange
          name: groupLastStartDate
          mode: date-picker
          label: Group Last Start Date
          _value:
            transform: >-
              $.variables._calculationEmployment.groupLastStartDate ?
              $.variables._calculationEmployment.groupLastStartDate :
              $.fields.employmentData.groupLastStartDate
          disabled: true
          validators:
            - type: required
          setting:
            format: dd/MM/yyyy
            type: date
        - type: dateRange
          name: groupLastTerminate
          mode: date-picker
          label: Group Last Terminate
          _value:
            transform: >-
              $.variables._calculationEmployment.groupLastTerminate ?
              $.variables._calculationEmployment.groupLastTerminate :
              $.fields.employmentData.groupLastTerminate
          disabled: true
          placeholder: dd/MM/yyyy
          setting:
            format: dd/MM/yyyy
            type: date
        - type: dateRange
          name: oirOriginalStartDate
          mode: date-picker
          label: OIR Original Start Date
          _value:
            transform: >-
              $exists($.fields.employmentData.oirOriginalStartDate) ?
              $.fields.employmentData.oirOriginalStartDate :
              $.fields.effectiveDate
          validators:
            - type: required
          setting:
            format: dd/MM/yyyy
            type: date
        - type: dateRange
          name: oirFirstStartDate
          mode: date-picker
          label: OIR First Start Date
          disabled: true
          _value:
            transform: >-
              $.variables._calculationEmployment.oirFirstStartDate ?
              $.variables._calculationEmployment.oirFirstStartDate :
              $.fields.employmentData.oirFirstStartDate
          validators:
            - type: required
          setting:
            format: dd/MM/yyyy
            type: date
        - type: dateRange
          name: oirLastStartDate
          mode: date-picker
          label: OIR Last Start Date
          disabled: true
          _value:
            transform: >-
              $.variables._calculationEmployment.oirLastStartDate ?
              $.variables._calculationEmployment.oirLastStartDate :
              $.fields.employmentData.oirLastStartDate
          validators:
            - type: required
          setting:
            format: dd/MM/yyyy
            type: date
        - type: dateRange
          name: oirLastTerminate
          mode: date-picker
          label: OIR Last Terminate
          placeholder: dd/MM/yyyy
          disabled: true
          _value:
            transform: >-
              $.variables._calculationEmployment.oirLastTerminate ?
              $.variables._calculationEmployment.oirLastTerminate :
              $.fields.employmentData.oirLastTerminate
          setting:
            format: dd/MM/yyyy
            type: date
        - name: groupSeniority
          label: Group Seniority
          type: text
          _value:
            transform: >-
              $.variables._calculationEmployment.groupSeniority ?
              $.variables._calculationEmployment.groupSeniority :
              $.fields.employmentData.groupSeniority
          _toast:
            transform: >-
              {'position': 'bottom','type': 'info', 'content': 'Based on ' &
              $.variables._getInfoSeniorityDefaultGroup.seniorityDateName}
          disabled: true
        - name: organizationalInstanceSeniority
          label: Organizational Instance Seniority
          type: text
          _value:
            transform: >-
              $.variables._calculationEmployment.organizationalInstanceSeniority
              ?
              $.variables._calculationEmployment.organizationalInstanceSeniority
              : $.fields.employmentData.organizationalInstanceSeniority
          _toast:
            transform: >-
              {'position': 'bottom','type': 'info', 'content': 'Based on ' &
              $.variables._getInfoSeniorityByCompany.seniorityDateName}
          disabled: true
        - name: externalExperience
          label: External Experience
          type: text
          _value:
            transform: >-
              $.variables._calculationEmployment.externalExperience ?
              $.variables._calculationEmployment.externalExperience :
              $.fields.employmentData.externalExperience
          disabled: true
        - type: textarea
          label: Note
          name: note
          placeholder: Enter Note
          col: 2
          textarea:
            autoSize:
              minRows: 3
  backendUrl: /api/personals/:employeeId/job-datas/hire-new-oir
  _isActionWarning: '$.jobData.totalFte > 1 ? true : false'
  actions:
    - id: success
      type: modal
      modal:
        type: success
        title: Go to Employee Job Data
        content: >-
          Organizational Instance has been created, would you like to navigate
          to Employee's Job Data?
        onConfirm:
          type: navigate
          link: /HR/employees/:employeeId
          paramsUrl: '{''tab'': ''HR.FS.FR.009''}'
    - id: warning
      type: modal
      modal:
        type: success
        title: Total FTE exceeded 1.0
        content: >-
          The sum of FTEs of all active jobs (with HR status Active) for this
          employee has exceeded 1.0.
        onConfirm:
          type: confirm
  sources:
    managerList:
      uri: '"/api/picklists/ISMANAGER/values"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})
      disabledCache: true
      params:
        - effectiveDate
    fullPartList:
      uri: '"/api/picklists/FULLPART/values"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})
      disabledCache: true
      params:
        - effectiveDate
    decisionLevelList:
      uri: '"/api/picklists/LEVELOFDECISION/values"'
      method: GET
      queryTransform: >-
        {'limit': 9999, 'filter': [{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})
      disabledCache: true
      params:
        - effectiveDate
    companiyInfo:
      uri: '"/api/companies/get-by"'
      method: GET
      queryTransform: >-
        {'limit': 1,'filter': [{'field':'code','operator':
        '$eq','value':$.code},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id}})[]
      disabledCache: true
      params:
        - code
        - effectiveDate
    positionsList:
      uri: '"/api/positions/get-dropdown-list-by"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'companyCode','operator':
        '$eq','value':$.companyCode},{'field':'legalEntityCode','operator':
        '$eq','value':$.legalEntityCode},{'field':'departmentCode','operator':
        '$eq','value':$.departmentCode},{'field':'jobCodeCode','operator':
        '$eq','value':$.jobCodeCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code , 'departmentId':
        $item.departmentId, 'jobCodeId': $item.jobCodeId, 'locationId':
        $item.locationId, 'costCenterId': $item.costCenterId, 'matrixPositions':
        $item.matrixPositions[] }})[]
      disabledCache: true
      params:
        - companyCode
        - effectiveDate
        - legalEntityCode
        - departmentCode
        - jobCodeCode
    positionsListGetBy:
      uri: '"/api/positions/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code , 'id': $item.id}})[]
      disabledCache: true
      params:
        - effectiveDate
        - limit
        - page
        - search
    positionInfo:
      uri: '"/api/positions/" & $.id & ""'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $
      disabledCache: true
      params:
        - id
    businessUnitList:
      uri: '"/api/business-units/get-list"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'status','operator':
        '$eq','value':true},{'field':'companyCode','operator':
        '$eq','value':$.companyCode},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id}})[]
      disabledCache: true
      params:
        - companyCode
        - effectiveDate
    legalEntityList:
      uri: '"/api/legal-entities/get-list"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'effectiveDate','operator': '$eq','value':
        $.effectiveDate},{'field':'status','operator':
        '$eq','value':true},{'field':'companyCode','operator':
        '$eq','value':$.companyCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id}})[]
      disabledCache: true
      params:
        - companyCode
        - effectiveDate
    divisionsList:
      uri: '"/api/divisions/get-list"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'search','operator':
        '$eq','value':$.search},{'field':'status','operator':
        '$eq','value':true},{'field':'businessUnitCode','operator':
        '$eq','value':$.businessUnitCode},{'field':'companyCode','operator':'$eq','value':$.companyCode},{'field':'effectiveDate','operator':
        '$eq','value': $.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id}})[]
      disabledCache: true
      params:
        - companyCode
        - businessUnitCode
        - effectiveDate
    divisionDetail:
      uri: '"/api/divisions/" & $.id & ""'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        {'BusinessUnit': {'label': $.businessUnitName & ' (' &
        $.businessUnitCode & ')', 'value': $.businessUnitCode}}
      disabledCache: true
      params:
        - id
    departmentsList:
      uri: '"/api/departments/get-dropdown-list-by"'
      method: GET
      queryTransform: >-
        {'filter':
        [{'field':'companyCode','operator':'$eq','value':$.companyCode},{'field':'legalEntityCode','operator':
        '$eq','value':$.legalEntityCode},{'field':'businessUnitCode','operator':
        '$eq','value':$.businessUnitCode},{'field':'divisionCode','operator':
        '$eq','value':$.divisionCode},{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator': '$eq','value':
        $.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id}})[]
      disabledCache: true
      params:
        - companyCode
        - legalEntityCode
        - businessUnitCode
        - divisionCode
        - effectiveDate
    departmentInfo:
      uri: '"/api/departments/" & $.id & ""'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $
      disabledCache: true
      params:
        - id
    locationsList:
      uri: '"/api/locations/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator': '$eq','value':
        $.effectiveDate},{'field':'companyCode','operator':'$eq','value':$.companyCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id':$item.id}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
        - companyCode
    locationById:
      uri: '"/api/locations/" & $.id & ""'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $.code ? {'label': $.longName.default & ' (' & $.code & ')', 'value':
        $.code}
      disabledCache: true
      params:
        - id
    constCenterList:
      uri: '"/api/cost-centers/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator':
        '$eq','value':true},{'field':'companyCode','operator':
        '$eq','value':$.companyCode},{'field':'effectiveDate','operator':
        '$eq','value': $.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id':$item.id}})[]
      disabledCache: true
      params:
        - companyCode
        - limit
        - page
        - search
        - effectiveDate
    constCenterById:
      uri: '"/api/cost-centers/" & $.id & ""'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $.code ? {'label': $.longName.default & ' (' & $.code & ')', 'value':
        $.code}
      disabledCache: true
      params:
        - id
    employeeGroupsList:
      uri: '"/api/employee-groups"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.employeeGroup.default,
        'value': $item.code}})
      disabledCache: true
    employeeSubGroupsList:
      uri: '"/api/employee-sub-groups"'
      method: GET
      queryTransform: >-
        {'limit': 9999,'page': $.page,'filter':
        [{'field':'linkCatalogDataCode','operator':
        '$eq','value':$.employeeGroupCode},{'field':'search','operator':
        '$eq','value':$.search},{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator': '$eq','value':
        $.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - employeeGroupCode
        - effectiveDate
        - limit
        - page
        - search
    jobCodesList:
      uri: '"/api/job-codes/get-dropdown-list-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator':
        '$eq','value':true},{'field':'companyCode','operator':
        '$eq','value':$.companyCode},{'field':'effectiveDate','operator':
        '$eq','value': $.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id, 'bandId':
        $item.bandId, 'careerStreamId': $item.careerStreamId}})[]
      disabledCache: true
      params:
        - companyCode
        - limit
        - page
        - search
        - effectiveDate
    jobCodeInfo:
      uri: '"/api/job-codes"'
      method: GET
      queryTransform: >-
        {'limit': 1,'filter': [{'field':'effectiveDate','operator':
        '$lte','value': $.effectiveDate},{'field':'code','operator':
        '$eq','value':$.code}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $.data[0]
      disabledCache: true
      params:
        - code
        - effectiveDate
    businessTitlesList:
      uri: '"/api/business-titles/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator': '$eq','value':
        $.effectiveDate},{'field':'companyCode','operator': '$eq','value':
        $.companyCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - companyCode
        - limit
        - page
        - search
        - effectiveDate
    careerStreamsList:
      uri: '"/api/career-streams/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator': '$eq','value':
        $.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code,'id': $item.id}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
    careerStreamById:
      uri: '"/api/career-streams/" & $.id & ""'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $.code ? {'label': $.longName.default & ' (' & $.code & ')', 'value':
        $.code, 'id': $.id}
      disabledCache: true
      params:
        - id
    careerStreamDetail:
      uri: '"/api/career-streams/" & $.id & ""'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        {'careerStream': {'label': $.longName.default & ' (' & $.code & ')',
        'value': $.code, 'id': $.id}}
      disabledCache: true
      params:
        - id
    careerBandsList:
      uri: '"/api/bands/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator':
        '$eq','value':true},{'field':'careerStreamCode','operator':
        '$eq','value':$.careerStreamCode},{'field':'effectiveDate','operator':
        '$eq','value': $.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($filter($.data, function ($item){ $exists($item.code) }) ,
        function($item) {{'label': $item.longName.default & ' (' & $item.code &
        ')', 'value': $item.code,'id': $item.id, 'careerStreamId':
        $item.careerStreamId}})[]
      disabledCache: true
      params:
        - careerStreamCode
        - limit
        - page
        - search
        - effectiveDate
    careerBandById:
      uri: '"/api/bands/" & $.id & ""'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $.code ? {'label': $.longName.default & ' (' & $.code & ')', 'value':
        $.code, 'id': $.id}
      disabledCache: true
      params:
        - id
    careerBandDetail:
      uri: '"/api/bands/" & $.id & ""'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        {'band': {'label': $.longName.default & ' (' & $.code & ')', 'value':
        $.code, 'id': $.id}}
      disabledCache: true
      params:
        - id
    actionsList:
      uri: '"/api/actions/dropdown"'
      method: GET
      queryTransform: >-
        {'limit':1000 ,'filter': [{'field':'status','operator':
        '$eq','value':true},{'field':'employeeGroupCodes','operator':
        '$eq','value':$.employeeGroupCode},{'field':'processType','operator':
        '$eq','value': 2},{'field':'effectiveDateQuery','operator':
        '$eq','value': $.effectiveDate},{'field':'effectiveSequence','operator':
        '$eq','value': $.effectiveSequence}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code,'hrStatusCode': $item.hrStatusCode, 'setStatusField':
        $item.setStatusField, 'prStatusCode': $item.prStatusCode ,
        'actionReasons': $item.actionReasons.actionReasonCode[]}})[]
      disabledCache: true
      params:
        - employeeGroupCode
        - effectiveDate
        - effectiveSequence
    actionInfo:
      uri: '"/api/actions"'
      method: GET
      queryTransform: >-
        {'limit': 1,'filter': [{'field':'effectiveDateQuery','operator':
        '$eq','value': $.effectiveDate},{'field':'code','operator':
        '$eq','value':$.actionCode1}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code, 'setStatusField': $item.setStatusField,
        'prStatusCode':$item.prStatusCode, 'hrStatusCode':$item.hrStatusCode,
        'actionReasons': $item.actionReasons.actionReasonCode[]}})[0]
      disabledCache: true
      params:
        - actionCode1
        - effectiveDate
    reasonsList:
      uri: '"/api/picklists/ACTIONREASON/values"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'filter':[{'field':'code','operator':'$in','value':
        $.reasonCodes},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - reasonCodes
        - effectiveDate
        - limit
    hrStatusList:
      uri: '"/api/picklists/HRSTATUS/values"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - effectiveDate
    payrollStatusList:
      uri: '"/api/picklists/PAYROLLSTATUS/values"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - effectiveDate
    regionList:
      uri: '"/api/picklists/REGIONS/values"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})
      disabledCache: true
      params:
        - effectiveDate
    jobIndicatorList:
      uri: '"/api/picklists/JOBINDICATOR/values"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})
      disabledCache: true
      params:
        - effectiveDate
    timezoneList:
      uri: '"/api/picklists/TIMEZONE/values"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - effectiveDate
    empLevelList:
      uri: '"/api/picklists/EMPLEVEL/values/pagination"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
    personalsList:
      uri: '"/api/personals/all-employees-job-datas"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'effectiveDate','operator': '$eq','value':
        $.effectiveDate},{'field':'hrStatus','operator': '$eq','value':
        $.hrStatus}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.fullName &' ('&
        $item.employeeId & ')', 'value': $item.employeeId}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
        - hrStatus
    checkTotalFte:
      uri: '"/api/personals/" & $.employeeId & "/job-datas/total-fte"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'inputFte','operator':
        '$eq','value':$.fte},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $
      disabledCache: true
      params:
        - employeeId
        - fte
        - effectiveDate
    calculationEmployment:
      uri: '"/api/calculator-employment/" & $.employeeId & ""'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'companyCode','operator':
        '$eq','value':$.companyCode},{'field':'groupOriginalStartDate','operator':
        '$eq','value':$.groupOriginalStartDate},{'field':'employeeGroupCode','operator':
        '$eq','value':$.employeeGroupCode},{'field':'employeeSubGroupCode','operator':
        '$eq','value':$.employeeSubGroupCode},{'field':'organizationalInstanceRcd','operator':
        '$eq','value':$.organizationalInstanceRcd},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'oirOriginalStartDate','operator':
        '$eq','value':$.oirOriginalStartDate},{'field':'actionCode','operator':
        '$eq','value':$.actionCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $
      disabledCache: true
      params:
        - employeeId
        - companyCode
        - employeeGroupCode
        - employeeSubGroupCode
        - organizationalInstanceRcd
        - effectiveDate
        - groupOriginalStartDate
        - oirOriginalStartDate
        - actionCode
    organizationPick:
      uri: >-
        "/api/trees/organization/pick/" & $.pickType & "/" & $.code & "/" &
        $.effectiveDate & ""
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $reduce(     $,  function($acc, $item) {   $merge([     $acc,     {   
        $item.type : ($origItem := $lookup($acc, $item.type); $validItem :=
        $item.effectiveDate ? $toMillis($item.effectiveDate) <=
        $toMillis($now()) : true ; $exists($origItem) and
        $toMillis($origItem.effectiveDate) > $toMillis($item.effectiveDate)  ?
        $origItem : ($validItem ? {'label': $item.name & ' (' & $item.code &
        ')', 'value': $item.code, 'type': $item.type, 'effectiveDate':
        $item.effectiveDate } : $origItem))    }    ])  }, {} )
      disabledCache: true
      params:
        - pickType
        - code
        - effectiveDate
    getSeniority:
      uri: '"/api/seniority-calculators"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'companyCode','operator':
        '$in','value':$.companyCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: '[$.data]'
      disabledCache: true
      params:
        - companyCode
    getJobdataPrimary:
      uri: '"/api/personals/" & $.employeeId & "/job-datas"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'jobIndicator','operator': '$eq','value':
        'P'},{'field':'hrStatus','operator': '$eq','value': 'A'}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: '$.data ? $.data[] : []'
      disabledCache: true
      params:
        - employeeId
  variables:
    _actionsList:
      transform: >-
        $exists($.fields.effectiveDate) ?
        $actionsList($.fields.employeeGroupCode,$DateToTimestampUTC($.fields.effectiveDate),$.fields.effectiveSequence1)
    _calculationEmployment:
      transform: >-
        $.fields.employeeId and
        $exists($.fields.employment.groupOriginalStartDate) ?
        $calculationEmployment($.fields.employeeId,$.fields.companyObj.value,$.fields.employeeGroupCode,$.fields.jobData.employeeSubGroupCode,$.fields.organizationalInstanceRecord,$DateToTimestamp($.fields.effectiveDate),$.fields.employment.groupOriginalStartDate,$.fields.employment.oirOriginalStartDate,$.fields.actionCode1)
    _checkTotalFte:
      transform: >-
        $.fields.employeeId ? $checkTotalFte($.fields.employeeId,
        $.fields.jobData.fte, $DateToTimestampUTC($.fields.effectiveDate))
    _selectedAction:
      transform: >-
        $exists($.fields.actionCode1) ? ($actionCode:=  $.fields.actionCode1;
        $selectdAction:= $.variables._actionsList[value=$actionCode])
    _positionInfo:
      transform: >-
        $exists($.fields.jobData.positionObj.id) and
        $exists($.fields.effectiveDate) ?
        $positionInfo($.fields.jobData.positionObj.id)
    _getSeniority:
      transform: >-
        $exists($.fields.companyObj.value) ?
        $getSeniority([$.fields.companyObj.value,'DefaultCompany','DefaultGroup'])
    _getInfoSeniorityByCompany:
      transform: >-
        $exists($.variables._getSeniority) ? ($byCompany :=
        $filter($.variables._getSeniority, function($item){$item.companyCode =
        $.fields.companyObj.value})[0] ; $byCompany ? $byCompany :
        $filter($.variables._getSeniority, function($item){$item.companyCode =
        'DefaultCompany'})[0])
    _getInfoSeniorityDefaultGroup:
      transform: >-
        $exists($.variables._getSeniority) ? $filter($.variables._getSeniority,
        function($item){$item.companyCode = 'DefaultGroup'})[0]
    _getJobdataPrimary:
      transform: >-
        $.fields.employeeId ? ( $getData :=
        $getJobdataPrimary($.fields.employeeId); $count($getData))
filter_config: {}
layout_options:
  is_upload_file: true
  layout_data_service:
    data_key: hire
    key_mapping:
      employeeId: employeeId
  is_new_dynamic_form: true
layout_options__header_buttons: null
options: null
create_form:
  fields:
    - type: text
      name: primaryJob
      unvisible: true
    - type: text
      name: fullName
      unvisible: true
      _value:
        transform: $.variables._basicInfor.fullName
    - type: text
      name: avatarFile
      unvisible: true
      _value:
        transform: $.variables._basicInfor.avatarFile
    - name: jobDataMatchOIR
      label: jobDataMatchOIR
      type: text
      unvisible: true
    - name: effectiveDateBasicInfo
      label: effectiveDateBasicInfo
      type: text
      unvisible: true
    - type: group
      n_cols: 4
      fields:
        - type: select
          label: Employee ID
          name: employeeId
          placeholder: Enter Employee ID
          isLazyLoad: true
          outputValue: value
          validators:
            - type: required
          _value:
            transform: >-
              $.extend.layoutData.hire.employeeId ?
              $.extend.layoutData.hire.employeeId
          _validateFn:
            transform: >-
              $exists($.fields.employeeId) and
              $not($isNilorEmpty($.extend.layoutData.hire.employeeId)) ?
              {'label': $.variables._basicInfor.fullName & ' (' &
              $.fields.employeeId & ')', 'value': $.fields.employeeId }
            params:
              updateLabelExistOption: true
          _select:
            transform: $personalsList($.extend.limit, $.extend.page, $.extend.search)
        - type: checkbox
          name: isBlackList
          unvisible: true
          _value:
            transform: $.variables._blackblockCheck.isBlackList
        - type: checkbox
          name: isBlockList
          unvisible: true
          _value:
            transform: $.variables._blackblockCheck.isBlockList
        - type: select
          label: Employee Group
          name: employeeGroupCode
          placeholder: Enter Employee Group
          disabled: true
          value: POI
          outputValue: value
          validators:
            - type: required
          _select:
            transform: $employeeGroupsList()
        - type: dateRange
          label: Effective Date
          name: effectiveDate
          mode: date-picker
          placeholder: dd/MM/yyyy
          setting:
            format: dd/MM/yyyy
            type: date
          _value:
            transform: >-
              $exists($.extend.layoutData.hire.effectiveDate) ?
              $DateFormat($.extend.layoutData.hire.effectiveDate, 'YYYY/MM/DD')
          validators:
            - type: required
        - type: checkbox
          name: isValidDateBacsicInfo
          unvisible: true
          _value:
            transform: >-
              $.fields.effectiveDateBasicInfo and
              $DateDiff($DateFormat($.fields.effectiveDateBasicInfo,
              'yyyy-MM-DD'), $DateFormat($.fields.effectiveDate, 'yyyy-MM-DD'),
              'd') > 0
        - type: select
          label: Company
          name: companyObj
          placeholder: Enter Company
          validators:
            - type: required
          isLazyLoad: true
          _value:
            transform: >-
              $.extend.layoutData.hire.companyCode ? {'label':
              $.extend.layoutData.hire.companyCode.label, 'value':
              $.extend.layoutData.hire.companyCode.value}
          _select:
            transform: >-
              $companiesList($.fields.effectiveDate, $.extend.limit,
              $.extend.page, $.extend.search)
  backendUrl: /api/ern-oir-employment/:employeeId
  _isActionProceed: '$.jobDataMatchOIR ? true : false'
  _isActionModalWarning: $.isBlackList and $isNilorEmpty($.jobDataMatchOIR)
  _isActionToastError: >-
    $.isBlockList or ($.effectiveDateBasicInfo and
    $DateDiff($DateFormat($.effectiveDateBasicInfo, 'yyyy-MM-DD'),
    $DateFormat($.effectiveDate, 'yyyy-MM-DD'), 'd') > 0)
  actions:
    - id: success
      type: modal
      modal:
        title: Update Existing OIR
        content: >-
          Job Data with Company and Employee Group already exist, would you like
          to navigate to Employee's Job Data?
        onConfirm:
          type: navigate
          link: /HR/employees/:employeeId
          paramsUrl: '{''tab'': ''HR.FS.FR.009''}'
    - id: warning
      type: modal
      modal:
        type: warning
        title: Warning
        content: Blacklist employees are not encouraged to be recruited.
        onConfirm:
          type: confirm
    - id: error
      type: toast
      toast:
        type: error
        title: Error
        content: Test
        _content: >-
          $.isBlockList ? 'Blocklist employees are not recruited according to
          regulations.' : $DateDiff($DateFormat($.effectiveDateBasicInfo,
          'yyyy-MM-DD'), $DateFormat($.effectiveDate, 'yyyy-MM-DD'), 'd') > 0 ?
          'The effective date must not be earlier than the first day of the
          Basic Infomation record.'
        onConfirm:
          type: confirm
  filterMapping:
    - field: companyCode
      operator: $eq
      valueField: companyObj.value
    - field: effectiveDate
      operator: $eq
      valueField: effectiveDate
    - field: employeeGroupCode
      operator: $eq
      valueField: employeeGroupCode
  sources:
    personalsList:
      uri: '"/api/personals/all-employees-job-datas"'
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.name & ' (' & $item.employeeId
        & ')', 'value': $item.employeeId, 'id': $item.id }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    employeeGroupsList:
      uri: '"/api/employee-groups"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.employeeGroup.default,
        'value': $item.code}})
      disabledCache: true
    basicInfor:
      uri: '"/api/personals/" & $.employeeId & ""'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $
      disabledCache: true
      params:
        - employeeId
    companiesList:
      uri: '"/api/companies/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id}})[]
      disabledCache: true
      params:
        - effectiveDate
        - limit
        - page
        - search
    blackblockCheck:
      uri: '"/api/black-block-infos/check-black-block/" & $.employeeId & ""'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'effectiveDate','operator': '$eq','value':
        $.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $
      disabledCache: true
      params:
        - employeeId
        - effectiveDate
  requestsOnProceed:
    sourcesMapping:
      - key: validateErnOir
        enabled: $not($.formData.isBlockList)
      - key: minimizedSingleDate
        enabled: $not($.formData.isBlockList)
    sources:
      validateErnOir:
        uri: '"/api/ern-oir-employment/" & $.employeeId & ""'
        method: GET
        queryTransform: >-
          {'filter': [{'field':'employeeGroupCode','operator': '$eq','value':
          $.employeeGroupCode},{'field':'companyCode','operator': '$eq','value':
          $.companyObj.value},{'field':'effectiveDate','operator':
          '$eq','value': $.effectiveDate}]}
        bodyTransform: ''
        headerTransform: ''
        resultTransform: $
        disabledCache: true
      minimizedSingleDate:
        uri: >-
          "/api/personals/" & $.employeeId &
          "/basic-infomation/get-minimized-single"
        method: GET
        queryTransform: ''
        bodyTransform: ''
        headerTransform: ''
        resultTransform: '{''effectiveDateBasicInfo'': $.effectiveDate}'
        disabledCache: true
  variables:
    _basicInfor:
      transform: $.fields.employeeId ? $basicInfor($.fields.employeeId)
    _blackblockCheck:
      transform: >-
        $.fields.employeeId ?
        $blackblockCheck($.fields.employeeId,$.fields.effectiveDate)
layout_options__footer_buttons:
  - id: cancel
    title: Cancel
    type: tertiary
  - id: proceed
    title: Proceed
    type: primary
layout_options__row_actions: null
backend_url: null
screen_name: null
layout_options__actions_many: null
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key: null
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Add Person of Interest Instance
  parent:
    title: Human Resource
