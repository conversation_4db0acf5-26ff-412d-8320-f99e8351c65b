id: HR.FS.FR.050
status: draft
sort: 77
user_created: 8ad48761-02ef-4f69-bef6-ac3a6f107610
date_created: '2024-07-01T09:54:54.774Z'
user_updated: 2aa5db12-fa54-46d6-8e05-9e421858de0c
date_updated: '2025-07-24T09:37:14.366Z'
title: Social Organization Infor
requirement:
  time: 1749014387677
  blocks:
    - id: XLAYTucuUA
      type: paragraph
      data:
        text: Quản lý thông về tổ chức xã hội&nbsp;&nbsp;
  version: 2.30.7
screen_design: null
module: HR
local_fields:
  - code: startDate
    title: Start Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
  - code: endDate
    title: End Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
  - code: organizationTypeName
    title: Type
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: organizationName
    title: Name of the Organization
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: cardNumber
    title: Card Number
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: activityLocation
    title: Activity Location
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: positionInOrg
    title: Position in the Organization
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: startDatePosition
    title: Start Date of the Position
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
  - code: endDatePosition
    title: End Date of the Position
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
  - code: note
    title: Note
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: attachment
    title: Attachment
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
mock_data:
  - startDate: 01/01/2010
    endDate: 01/01/9999
    type: Đoàn
    organizationName: Đoàn thanh niên
    cardNumber: '0101011'
    activityLocation: Công ty FPT
    note: ''
    attachment: The doan.pdf
    positionInOrg: Bí Thư
    startDatePosition: 01/01/2015
    endDatePosition: 01/01/9999
    positionOrgArray:
      - positionInOrg: Đoàn viên
        startDatePosition: 01/01/2010
        endDatePosition: 01/01/2015
      - positionInOrg: Bí Thư
        startDatePosition: 01/01/2015
        endDatePosition: 01/01/9999
  - startDate: 01/01/2000
    endDate: 12/31/2009
    type: Đội
    organizationName: Đội thiếu niên
    cardNumber: '0101011'
    activityLocation: Công ty FPT
    note: ''
    attachment: The doi vien.pdf
    positionInOrg: Đội viên
    startDatePosition: 01/01/2000
    endDatePosition: 01/01/2009
    positionOrgArray:
      - positionInOrg: Đội viên
        startDatePosition: 01/01/2000
        endDatePosition: 01/01/2009
local_buttons: null
layout: layout-widget
form_config:
  formSize:
    create: large
    edit: large
  formTitle:
    create: Add New Social Organization Infor
  fields:
    - type: group
      label: Organization
      collapse: false
      isBorderTopNone: true
      fieldGroupTitleStyle:
        padding: 0 0 12px 0
      styling_not_form_view:
        fieldGroupTitleStyle:
          padding: 12px 20px
      fields:
        - type: group
          n_cols: 2
          _condition:
            transform: $not($.extend.formType = 'view')
          fields:
            - type: dateRange
              mode: date-picker
              label: Start Date
              name: startDate
              placeholder: dd/MM/yyyy
              validators:
                - type: required
            - type: dateRange
              mode: date-picker
              label: End Date
              name: endDate
              placeholder: dd/MM/yyyy
              validators:
                - type: ppx-custom
                  args:
                    transform: >-
                      $exists($.fields.startDate) and $exists($.fields.endDate)
                      ? $DateDiff($DateFormat($.fields.startDate, 'yyyy-MM-DD'),
                      $DateFormat($.fields.endDate, 'yyyy-MM-DD'), 'd') > 0
                  text: End date must be greater than start date
            - type: select
              label: Type
              name: organizationTypeCode
              placeholder: Select Type
              outputValue: value
              _select:
                transform: $socialorgtypeList()
              validators:
                - type: required
            - type: text
              label: Name
              name: organizationName
              placeholder: Enter Name
              validators:
                - type: maxLength
                  args: 1000
                  text: Maximum 1000 characters
            - type: text
              label: Card Number
              name: cardNumber
              placeholder: Enter Card Number
              validators:
                - type: maxLength
                  args: 120
                  text: Maximum 120 characters
            - type: text
              label: Location
              name: activityLocation
              placeholder: Enter Location
        - type: group
          n_cols: 1
          _condition:
            transform: $.extend.formType = 'view'
          fields:
            - type: dateRange
              mode: date-picker
              label: Start Date
              name: startDate
              placeholder: dd/MM/yyyy
              validators:
                - type: required
            - type: dateRange
              mode: date-picker
              label: End Date
              name: endDate
              placeholder: dd/MM/yyyy
              validators:
                - type: ppx-custom
                  args:
                    transform: >-
                      $exists($.fields.startDate) and $exists($.fields.endDate)
                      ? $DateDiff($DateFormat($.fields.startDate, 'yyyy-MM-DD'),
                      $DateFormat($.fields.endDate, 'yyyy-MM-DD'), 'd') > 0
                  text: End date must be greater than start date
            - type: select
              label: Type
              name: organizationTypeCode
              placeholder: Select Type
              outputValue: value
              _select:
                transform: $socialorgtypeList()
              validators:
                - type: required
            - type: text
              label: Name
              name: organizationName
              placeholder: Enter Name
              validators:
                - type: maxLength
                  args: 1000
                  text: Maximum 1000 characters
            - type: text
              label: Card Number
              name: cardNumber
              placeholder: Enter Card Number
              validators:
                - type: maxLength
                  args: 120
                  text: Maximum 120 characters
            - type: text
              label: Location
              name: activityLocation
              placeholder: Enter Location
        - type: textarea
          name: note
          label: Note
          placeholder: Enter Note
          textarea:
            autoSize:
              minRows: 3
            maxCharCount: 1000
          validators:
            - type: maxLength
              args: 1000
              text: Maximum 1000 characters
        - type: group
          fields:
            - type: upload
              label: Attachment
              name: file
              upload:
                accept:
                  - >-
                    application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
                  - application/pdf
                  - application/vnd.ms-excel
                size: 5
                isMultiple: false
              _condition:
                transform: $.extend.formType = 'edit' or $.extend.formType = 'create'
            - name: isFileModified
              unvisible: true
              type: text
              _value:
                transform: >-
                  $exists($.fields.attachmentResults) and
                  $count($.fields.attachmentResults) = 0 ? 'Y' :
                  $not($exists($.fields.attachmentResults)) and
                  $not($isNilorEmpty($.fields.file)) ? 'Y' : 'N'
            - type: upload
              label: Attachment
              name: attachmentResults
              canAction: true
              hiddenLabel: true
              readOnly: true
              _condition:
                transform: $.extend.formType = 'edit' and $isNilorEmpty($.fields.file)
            - type: upload
              label: Attachment
              name: attachmentResults
              _condition:
                transform: $.extend.formType = 'view'
          _condition:
            transform: >-
              $not($.extend.formType = 'edit') and $not($.extend.formType =
              'view') and $not($.extend.formType = 'create')
    - type: group
      label: Position in the Organization
      padding: 12px 0 0
      collapse: false
      _disableEventCollapse:
        transform: '$.extend.formType = ''view'' ? false : true'
      borderBottomLabel: false
      fields:
        - type: array
          name: positionInOrg
          _size:
            transform: $.extend.formType = 'create' ? 1
          minSize: 1
          arrayOptions:
            canChangeSize: true
            add_btn_type: secondary
            add_btn_size: small
            _markAsDeletedByKey:
              transform: $.extend.formType = 'edit' ? 'isDeleted'
            markAsDeletedByKey: isDeleted
          field:
            type: group
            label: Position in the Organization
            _label:
              transform: >-
                'Position in the Organization #' & ($ParseInt($.extend.path[-1])
                + 1)
            fieldGroupTitleStyle:
              fontSize: 14px
              padding: 16px 16px 0 16px
            fieldGroupContentStyle:
              padding: 12px 16px 16px 16px
            padding: '0'
            collapse: false
            _disableEventCollapse:
              transform: '$.extend.formType = ''view'' ? true : false'
            fieldBackground: '#fff'
            borderRadius: 8px
            border: '1px solid #DFE3E8'
            isBorderTopNone: true
            styling_not_form_view:
              fieldGroupContentStyle:
                padding: 20px
              fieldGroupTitleStyle:
                padding: 12px 20px 0 20px
              borderBottomLabel: false
              lastGroupStyleOff: true
              isHideArrow: false
            fields:
              - type: text
                name: id
                unvisible: true
              - type: checkbox
                name: isDeleted
                unvisible: true
                value: false
              - type: select
                label: Position in the Organization
                name: organizationPositionCode
                placeholder: Select the Position
                outputValue: value
                _class:
                  transform: >-
                    ($startDate := $getFieldGroup($.extend.path, $.fields,
                    0).startDate;$endDate := $getFieldGroup($.extend.path,
                    $.fields, 0).endDate ; $not($isNilorEmpty($startDate)) or
                    $not($isNilorEmpty($endDate)) ? 'required' : 'fakeRequired')
                _select:
                  transform: $socialorgInSocialOrgList()
              - type: group
                padding: '0'
                _n_cols:
                  transform: '$not($.extend.formType = ''view'') ? 2 : 1'
                fields:
                  - type: dateRange
                    mode: date-picker
                    label: Start Date of the Position
                    name: startDate
                    placeholder: dd/MM/yyyy
                    validators:
                      - type: ppx-custom
                        args:
                          transform: >-
                            ($startDate := $getFieldGroup($.extend.path,
                            $.fields, 1).startDate ;
                            $not($isNilorEmpty($startDate)) and
                            $DateDiff($DateFormat($startDate, 'yyyy-MM-DD'),
                            $DateFormat($.fields.startDate, 'yyyy-MM-DD'), 'd')
                            < 0)
                        text: >-
                          Start Date of the Position must be greater than start
                          date
                  - type: dateRange
                    mode: date-picker
                    label: End Date of the Position
                    name: endDate
                    placeholder: dd/MM/yyyy
                    validators:
                      - type: ppx-custom
                        id: checkWithStartDate
                        args:
                          transform: >-
                            ($startDate := $getFieldGroup($.extend.path,
                            $.fields, 1).startDate;$endDate :=
                            $getFieldGroup($.extend.path, $.fields, 1).endDate ;
                            $not($isNilorEmpty($startDate)) and
                            $not($isNilorEmpty($endDate)) and
                            $DateDiff($DateFormat($endDate, 'yyyy-MM-DD'),
                            $DateFormat($startDate, 'yyyy-MM-DD'), 'd') < 0)
                        text: >-
                          End Date of the Position must be greater than start
                          date of the Position
                      - type: ppx-custom
                        id: checkWithEndDate
                        args:
                          transform: >-
                            ($endDate := $getFieldGroup($.extend.path, $.fields,
                            1).endDate ; $not($isNilorEmpty($endDate)) and
                            $not($isNilorEmpty($.fields.endDate)) and
                            $DateDiff($DateFormat($.fields.endDate,
                            'yyyy-MM-DD'), $DateFormat($endDate, 'yyyy-MM-DD'),
                            'd') < 0)
                        text: End Date of the Position must be less than End Date
  historyTitle: >-
    $DateFormat($.startDate, 'DD/MM/YYYY') & ' - ' & $DateFormat($.endDate,
    'DD/MM/YYYY')
  historyDescription: $.organizationTypeName & ' - ' & $.positionInOrg[0].organizationPositionName
  _mode:
    transform: $not($.extend.formType = 'view') ? 'mark-scroll'
  sources:
    socialorgtypeList:
      uri: '"/api/picklists/SOCIALORGTYPE/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
    socialorgInSocialOrgList:
      uri: '"/api/picklists/POSITIONINSOCIALORG/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
filter_config: {}
layout_options:
  show_dialog_form_save_add_button: true
  is_upload_file: true
  form_mode:
    view: tabset
    create: step
    edit: step
  is_copy_data_insert_new: false
layout_options__header_buttons:
  - id: create
    title: create
    icon: icon-plus-bold
  - id: history
    title: history
    icon: icon-clock-counter-clockwise-bold
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions: null
backend_url: api/personals/:id1/organization-infos
screen_name: null
layout_options__actions_many: null
parent: HR.FS.FR.000
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key: null
screen_designs: []
function_specs: []
fields: []
children: []
menu_item: null
