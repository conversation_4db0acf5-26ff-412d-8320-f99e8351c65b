id: HR.FS.FR.089
status: draft
sort: 239
user_created: 7b005132-8c47-469d-87f8-c72f7305edb7
date_created: '2024-07-18T09:02:19.014Z'
user_updated: 56e71b91-dd39-4ebc-afd1-b4f00f38d4a5
date_updated: '2025-07-10T09:44:40.624Z'
title: Scheduled Job Manager
requirement:
  time: 1746501122798
  blocks:
    - id: N4_85e4kku
      type: paragraph
      data:
        text: >-
          Chứ<PERSON> năng cho phép quản lý và xem thông tin chi tiết của tiến trình
          đang thực hiện
  version: 2.30.7
screen_design: null
module: COM
local_fields:
  - code: jobId
    title: Job ID
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    options__tabular__column_width: 8
    show_sort: true
  - code: id
    title: Job Request ID
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 10
    show_sort: true
  - code: jobTitle
    title: Job Tile
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 18
    show_sort: true
  - code: jobType
    title: Job Type
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 12
    show_sort: true
  - code: statusJobScheduler
    title: Status
    description: Status
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tag
      collection: field_types
    show_sort: true
    extra_config:
      sortByCode: statusJobRequest
  - code: ownerBy
    title: Owned By
    data_type:
      key: User
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 8
    show_sort: true
  - code: createdBy
    title: Created By
    data_type:
      key: User
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 8
    show_sort: true
  - code: statusJobExecution
    title: Status
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tag
      collection: field_types
    options__tabular__column_width: 8
    extra_config:
      tags:
        - label: Waiting
          value: 0
          class: violet
        - label: Waiting
          value: 1
          class: violet
        - label: In Progress
          value: 2
          class: warning
        - label: Completed
          value: 3
          class: success
        - label: Failed
          value: 4
          class: error
        - label: Recovered
          value: 5
          class: infor
        - label: Cancelled
          value: 6
          class: default
        - label: Skipped
          value: 7
          class: violet
    show_sort: true
  - code: createdAt
    title: Started Time (Local Time)
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss UTC
      collection: field_types
    options__tabular__column_width: 17
    show_sort: true
  - code: processResult
    title: Progress
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 10
    options__tabular__align: left
    show_sort: true
  - code: scheduleType
    title: Schedule Type
    description: Schedule Type
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
    extra_config:
      sortByCode: jobOccurrenceType
  - code: lastRun
    title: Last Run (Local Time)
    description: lastRun
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss UTC
      collection: field_types
    show_sort: true
  - code: nextRun
    title: Next Run (Local Time)
    description: Next Run (Local Time)
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss UTC
      collection: field_types
    show_sort: true
  - code: updatedBy
    title: Last Modified By
    description: Last Modified By
    data_type:
      key: User
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
mock_data:
  - jobId: '00000001'
    jobRequestId: '00000001'
    jobTitle: Import Foundation Object_02024
    jobType: ImportFoundation Object
    ownedBy: ChiNY2
    createdBy: ChiNY2
    status: In Progress
    startedTime: 01/01/2023 13:33
    progess: ''
local_buttons: null
layout: layout-table
form_config:
  formSize:
    create: large
    edit: large
    view: large
  formTitle:
    view: Job Request Detail
  fields:
    - type: group
      label: Job Definition
      collapse: false
      fields:
        - type: text
          label: Job Request ID
          disabled: true
          name: id
          _value:
            transform: $.extend.formType = 'create' ? 'Automatic'
          validators:
            - type: required
          placeholder: Enter Job Request ID
        - type: text
          label: Job Title
          name: jobTitle
          validators:
            - type: required
          placeholder: Enter Job Title
        - type: select
          label: Job Type
          name: jobType
          _select:
            transform: $jobTypesList()
          validators:
            - type: required
          placeholder: Enter Job Type
        - type: select
          label: Job Owner
          name: ownerBy
          outputValue: value
          isLazyLoad: true
          _select:
            transform: $usersList($.extend.limit, $.extend.page, $.extend.search)
          validators:
            - type: required
          placeholder: Enter Job Owner
          _condition:
            transform: $.extend.formType = 'create' or $.extend.formType = 'edit'
        - type: text
          label: Job Owner
          name: ownerBy
          _condition:
            transform: $.extend.formType = 'view'
        - type: text
          label: Created By
          name: createdBy
          _condition:
            transform: $.extend.formType = 'view'
        - type: text
          label: Job Parameters
          name: jobParameters
          placeholder: Enter Job Parameters
    - type: group
      label: Job Occurrence
      collapse: false
      fields:
        - type: radio
          label: jobOccurrenceType
          name: jobOccurrenceType
          validators:
            - type: required
          radio:
            - label: One-Time
              value: 1
            - label: Recurring
              value: 2
            - label: Dependent On
              value: 3
        - type: dateRange
          mode: date-picker
          label: Effective On
          name: effectiveOn
          _condition:
            transform: $.fields.jobOccurrenceType = 1
          validators:
            - type: required
          placeholder: Enter Effective On
        - type: group
          n_cols: 2
          _condition:
            transform: $.fields.jobOccurrenceType = 2
          fields:
            - type: select
              label: Job Type
              name: jobType2
              outputValue: value
              col: 2
              placeholder: Select Job Type
              validators:
                - type: required
              select:
                - label: Yearly
                  value: 1
                - label: Monthly
                  value: 2
                - label: Weekly
                  value: 3
                - label: Hourly
                  value: 4
                - label: Daily
                  value: 5
            - type: select
              label: Months
              name: months
              mode: multiple
              placeholder: Select Months
              outputValue: value
              _condition:
                transform: $.fields.jobType2 = 1
              validators:
                - type: required
              select:
                - label: June
                  value: 6
                - label: July
                  value: 7
            - type: number
              name: numberHours
              label: Every Number of Hours
              _condition:
                transform: $.fields.jobType2 = 4
            - type: select
              label: Day
              name: days
              mode: multiple
              placeholder: Select Day
              outputValue: value
              _condition:
                transform: $.fields.jobType2 = 4
              validators:
                - type: required
              select:
                - label: Monday
                  value: 1
                - label: Tuesday
                  value: 2
                - label: Wenesday
                  value: 3
                - label: Thurday
                  value: 4
                - label: Friday
                  value: 5
                - label: Satuday
                  value: 6
                - label: Sunday
                  value: 7
            - type: radio
              label: ''
              name: setWeek
              direction: column
              _condition:
                transform: $.fields.jobType2 = 3
              col: 2
              radio:
                - label: One-Time
                  value: 1
                - label: Recurring
                  value: 2
            - type: select
              label: ''
              name: weekOption
              col: 2
              placeholder: Select item
              _condition:
                transform: $.fields.setWeek = 2
              select:
                - label: Second
                  value: 1
                - label: Minute
                  value: 2
            - type: select
              label: Day
              name: days
              mode: multiple
              col: 2
              placeholder: Select Day
              outputValue: value
              _condition:
                transform: $.fields.setWeek = 2
              validators:
                - type: required
              select:
                - label: Monday
                  value: 1
                - label: Tuesday
                  value: 2
                - label: Wenesday
                  value: 3
                - label: Thurday
                  value: 4
                - label: Friday
                  value: 5
                - label: Satuday
                  value: 6
                - label: Sunday
                  value: 7
            - type: dateRange
              mode: date-picker
              label: Effective On
              name: effectiveOn1
              _condition:
                transform: $.fields.setWeek = 2 or $.fields.jobType2 = 5
              validators:
                - type: required
              placeholder: Enter Effective On
            - type: dateRange
              mode: date-picker
              label: End
              name: endDate
              _condition:
                transform: $.fields.setWeek = 2 or $.fields.jobType2 = 5
              validators:
                - type: required
              placeholder: Enter Effective On
        - type: group
          _condition:
            transform: $.fields.jobOccurrenceType = 3
          fields:
            - type: select
              label: Choose Dependent Job
              name: dependentJob
              placeholder: Select Dependent Job
              _condition:
                transform: $.fields.jobOccurrenceType = 3
              validators:
                - type: required
              select:
                - label: Foundation Object Export (00000010)
                  value: '00000010'
                - label: Foundation Object Export (00000011)
                  value: '00000011'
            - type: radio
              label: Day
              name: day
              validators:
                - type: required
              radio:
                - label: Immediately
                  value: 1
                - label: After a certain number of minutes
                  value: 2
                - label: At
                  value: 3
    - type: group
      label: Notification
      collapse: false
      fields:
        - type: select
          label: Recipients
          name: recipients
          mode: multiple
          placeholder: Select Recipients
          isLazyLoad: true
          validators:
            - type: required
          _select:
            transform: $usersList($.extend.limit, $.extend.page, $.extend.search)
        - type: checkbox
          label: Send Email When Job Starts
          hiddenLabel: true
          name: sendWhenJobStart
          _condition:
            transform: $.extend.formType != 'view'
        - type: checkbox
          label: Send Email When Job Completes
          hiddenLabel: true
          name: sendWhenJobComplete
          _condition:
            transform: $.extend.formType != 'view'
        - type: checkbox
          label: Send Email When Job Fails
          hiddenLabel: true
          name: sendJobFail
          _condition:
            transform: $.extend.formType != 'view'
        - mode: table
          type: array
          name: notificationGroup
          field:
            type: group
            fields:
              - type: text
                label: Description
                name: description
                align: start
                readOnly: true
                width: 150px
              - type: text
                label: Status
                name: status
                align: start
                readOnly: true
                width: 150px
          _condition:
            transform: $not($.extend.formType != 'view')
          _value:
            transform: >-
              [{'description': 'Send Email When Job Starts', 'status':
              $.extend.defaultValue.sendWhenJobStart ? 'Yes':
              'No'},{'description': 'Send Email When Job Completes', 'status':
              $.extend.defaultValue.sendWhenJobComplete ? 'Yes':
              'No'},{'description': 'Send Email When Job Fails', 'status':
              $.extend.defaultValue.sendJobFail ? 'Yes': 'No'}]
  sources:
    employeesList:
      uri: '"/api/personals"'
      method: GET
      queryTransform: '{''filter'': [{''field'':''search'',''operator'': ''$eq'',''value'':$.search}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.fullName ,
        'value':$item.id }})[]
      disabledCache: true
      params:
        - search
    jobTypesList:
      uri: '"/api/job-type"'
      method: GET
      queryTransform: '{''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.name.default}})[]
      disabledCache: true
    usersList:
      uri: '"/api/admin-users/infos"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.account, 'value':
        $item.account}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
filter_config:
  fields:
    - type: text
      placeholder: Enter Job Request ID
      label: Job Request ID
      name: id
    - type: text
      placeholder: Enter Job Title
      label: Job Title
      name: jobTitle
    - type: select
      placeholder: Select Job Type
      label: Job Type
      name: jobType
      isLazyLoad: true
      mode: multiple
      _select:
        transform: $jobTypesList($.extend.limit, $.extend.page, $.extend.search)
    - type: select
      label: Status
      name: status
      mode: multiple
      select:
        - label: Waiting
          value: 1
        - label: In Progress
          value: 2
        - label: Completed
          value: 3
        - label: Failed
          value: 4
        - label: Cancelled
          value: 6
    - type: select
      placeholder: Created By
      label: Created By
      name: createdBy
      mode: multiple
      isLazyLoad: true
      _select:
        transform: $usersList($.extend.limit, $.extend.page, $.extend.search)
    - type: dateRange
      name: createdAt
      label: Create On
    - type: select
      label: Last Modified By
      name: updateBy
      mode: multiple
      isLazyLoad: true
      placeholder: Select Last Modified By
      _select:
        transform: $usersList($.extend.limit, $.extend.page, $.extend.search)
  filterMapping:
    - field: strId
      operator: $cont
      valueField: id
    - field: jobTitle
      operator: $cont
      valueField: jobTitle
    - field: jobType
      operator: $eq
      valueField: jobType.(value)
    - field: statusJobExecution
      operator: $in
      valueField: status.(value)
    - field: na_shortName
      operator: $cont
      valueField: shortName
    - field: createdAt
      operator: $between
      valueField: createdAt
    - field: createdBy
      operator: $in
      valueField: createdBy.(value)
    - field: updatedBy
      operator: $in
      valueField: updatedBy.(value)
  sources:
    jobTypesList:
      uri: '"/api/picklists/JOBTYPE/values/pagination"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.name.default}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    usersList:
      uri: '"/api/admin-users/infos"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.account, 'value':
        $item.account}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
layout_options:
  show_dialog_form_save_add_button: true
  show_detail_history: false
  show_table_checkbox: false
  show_detail_drawer: true
  disabled_click_row: false
  skip_create_form: true
  _show_dialog_footer:
    transform: '$.extend.formType = ''filter'' ? true : false'
  tabset:
    - id: JobMonitor
      title: Job Monitor
      filter:
        filterValue:
          jobRequestScreenType: JobMonitor
        filterMapping:
          - field: jobRequestScreenType
            operator: $eq
            valueField: jobRequestScreenType
      pick_local_fields:
        - jobId
        - jobTitle
        - id
        - jobType
        - ownedBy
        - createdBy
        - statusJobExecution
        - createdAt
        - processResult
    - id: UpcomingJobs
      title: Upcoming Jobs
      filter:
        filterValue:
          jobRequestScreenType: UpcomingJobs
        filterMapping:
          - field: jobRequestScreenType
            operator: $eq
            valueField: jobRequestScreenType
      pick_local_fields:
        - jobTitle
        - id
        - jobType
        - lastRun
        - nextRun
        - scheduleType
    - id: JobScheduler
      title: Job Scheduler
      filter:
        filterValue:
          jobRequestScreenType: JobScheduler
        filterMapping:
          - field: jobRequestScreenType
            operator: $eq
            valueField: jobRequestScreenType
      pick_local_fields:
        - jobTitle
        - id
        - jobType
        - statusJobScheduler
        - createdBy
        - createdAt
        - updatedBy
layout_options__header_buttons: null
options: null
create_form:
  formSize:
    create: large
    edit: large
    view: large
  formTitle:
    view: Job Execution Details
  fields:
    - type: text
      _label:
        transform: $.extend.addOnValue.dataDetail.jobTitle
      name: '111'
      value: ' '
    - type: text
      label: ''
      name: jobType
    - type: table
      name: jobExecutionDetails
      rowIdName: id
      layout_option:
        hide_action_row: true
        show_pagination: false
      columns:
        - code: description
          title: Description
          align: start
          data_type:
            key: String
            collection: data_types
          display_type:
            key: Label
            collection: field_types
            conditionKey: typeOfLink
            condition: 1
            customType: Hyperlink
          actionRow:
            action: download
            baseUrl: /api/export-common/download-file-export
          options__tabular__column_width: 12
        - code: time
          title: Time
          align: start
          data_type:
            key: dd/MM/yyyy
            collection: data_types
          display_type:
            key: DD/MM/yyyy HH:mm:ss UTC
            collection: field_types
        - code: status
          title: Status
          align: start
          data_type:
            key: String
            collection: data_types
          display_type:
            key: Tag
            collection: field_types
      _dataSource:
        transform: $.variables._dataRequestDetail
    - type: text
      name: cloneJobExecutionDetails
      unvisible: true
  variables:
    _dataRequestDetail:
      transform: >-
        $map($.fields.cloneJobExecutionDetails, function($item) {$merge([$item,
        {'time': ($item.time * 1000),'status': ({'label':$item.status,'type':
        $item.status = 'Started' ? 'warning' : $item.status = 'Completed' ?
        'success' : 'infor'})}, {'jobRequestId': $.extend.defaultValue.id}])})[]
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: sub-detail
    title: View Details
    icon: icon-eye-bold
    type: ghost-gray
    href: null
    condition_func: $._selectedTab = 'JobMonitor'
  - id: cancel-job-requests
    title: Cancel Job
    icon: icon-file-x
    type: ghost-gray
    condition_func: $.statusJobExecution < 3
backend_url: api/job-request
screen_name: null
layout_options__actions_many: null
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key: null
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Scheduled Job Manager
  parent:
    title: Manager Common
