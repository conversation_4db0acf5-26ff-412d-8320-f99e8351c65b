id: HR.FS.FR.092_CM.002-02
status: draft
sort: 58
user_created: b403d5da-d684-45e4-ae6d-e25223baedc5
date_created: '2024-08-07T14:16:10.621Z'
user_updated: 56e71b91-dd39-4ebc-afd1-b4f00f38d4a5
date_updated: '2025-06-16T02:03:49.735Z'
title: Export Data
requirement:
  time: 1747306358818
  blocks:
    - id: z2lH2W2HNZ
      type: paragraph
      data:
        text: '&nbsp;Export Data.'
  version: 2.30.7
screen_design: null
module: COM
local_fields: []
mock_data: {}
local_buttons: null
layout: layout-finalize
form_config:
  fields:
    - type: group
      n_cols: 4
      space: 24
      fieldGroupContentStyle:
        gap: 16px
      fieldGroupTitleStyle:
        padding: 0 0 24px 0
      collapse: false
      disableEventCollapse: true
      label: File Management
      fields:
        - type: select
          name: typeSelect
          label: Type
          placeholder: Select Type
          outputValue: value
          _select:
            transform: $.variables._typeList
          _value:
            transform: $.variables._typeListDefault.value
          validators:
            - type: required
        - type: text
          name: type
          unvisible: true
          _value:
            transform: $.fields.typeSelect.code
        - type: select
          name: entityOrObjSelect
          label: Select entity/Select object
          dependantField: $.fields.typeSelect
          placeholder: Select entity/Select object
          _select:
            transform: $.variables._entityObjectList
          _value:
            transform: $.variables._entityObjectDefault
          validators:
            - type: required
        - type: text
          name: entityOrObj
          dependantField: $.fields.typeSelect
          unvisible: true
          _value:
            transform: $.fields.entityOrObjSelect.value
        - type: text
          name: isFisrtInit
          dependantFieldSkip: 2
          dependantField: $.fields.typeSelect
          unvisible: true
          value: true
        - type: text
          name: controllerName
          unvisible: true
          _value:
            transform: >-
              $.fields.type = 'FO_OBJECT' ? 'ExportingController' :
              $.fields.entityOrObjSelect.controller
        - type: select
          name: fileEncoding
          label: File Encoding
          placeholder: Select File Encoding
          outputValue: value
          value: Unicode (UTF-8)
          _select:
            transform: $.variables._fileEncodingList
          validators:
            - type: required
        - type: select
          name: fileLocale
          label: File Locale
          placeholder: Select File Locale
          outputValue: value
          value: Vietnam
          _select:
            transform: $.variables._fileLocaleList
          validators:
            - type: required
    - type: switch
      name: parameters
      label: Parameters
      hiddenLabel: true
      showSwitchLabel: true
      _condition:
        transform: >-
          $.fields.type = 'EMPLOYEE_DATA' or $.fields.type = 'PR_OBJECT' or
          $.fields.type = 'SYS_OBJECT' or $.fields.type = 'INS_OBJECT'
    - type: group
      name: employeeDataOptions
      _condition:
        transform: >-
          $.fields.type = 'EMPLOYEE_DATA' or $.fields.type = 'PR_OBJECT' or
          $.fields.type = 'SYS_OBJECT' or $.fields.type = 'INS_OBJECT'
      fields:
        - type: group
          n_cols: 3
          space: 24
          fieldGroupContentStyle:
            gap: 16px
          _condition:
            transform: $.fields.type = 'EMPLOYEE_DATA' and $.fields.parameters = true
          fields:
            - type: selectAll
              name: companies1
              label: Company
              mode: multiple
              placeholder: Select Company
              isLazyLoad: true
              _options:
                transform: $companiesList($.extend.limit, $.extend.page, $.extend.search)
            - type: selectAll
              name: legalEntities1
              label: Legal entity
              placeholder: Select Legal Entity
              isLazyLoad: true
              mode: multiple
              dependantField: $.fields.employeeDataOptions.companies1
              _options:
                transform: >-
                  $legalEntitiesList($.variables._companyCodesSelected,
                  $.extend.limit, $.extend.page, $.extend.search)
            - type: selectAll
              name: departments
              label: Department
              placeholder: Select Department
              isLazyLoad: true
              mode: multiple
              outputValue: value
              dependantField: >-
                $.fields.employeeDataOptions.companies1,$.fields.employeeDataOptions.legalEntities1
              _options:
                transform: >-
                  $departmentsList($.variables._companyCodesSelected,
                  $.variables._legalEntityCodesSelected, $.extend.limit,
                  $.extend.page, $.extend.search)
        - type: group
          n_cols: 3
          space: 24
          fieldGroupContentStyle:
            gap: 16px
          _condition:
            transform: >-
              $.fields.parameters = true and ($.fields.type = 'SYS_OBJECT' or
              $.fields.type = 'INS_OBJECT' or $.fields.type = 'PR_OBJECT')
          fields:
            - type: selectAll
              name: companies2
              label: Company
              mode: multiple
              placeholder: Select Company
              isLazyLoad: true
              _condition:
                transform: >-
                  $not($.fields.entityOrObjSelect.functionCode = 'INS_020') and
                  $not($.fields.entityOrObjSelect.functionCode = 'INS_025') and
                  $not($.fields.entityOrObjSelect.functionCode = 'INS_010')
              _options:
                transform: $companiesList($.extend.limit, $.extend.page, $.extend.search)
            - type: selectAll
              name: legalEntities2
              label: Legal entity
              placeholder: Select Legal Entity
              _condition:
                transform: >-
                  $.fields.type = 'PR_OBJECT' or ($.fields.type = 'INS_OBJECT'
                  and $not($.fields.entityOrObjSelect.functionCode = 'INS_020')
                  and $not($.fields.entityOrObjSelect.functionCode = 'INS_025')
                  and $not($.fields.entityOrObjSelect.functionCode = 'INS_010'))
              isLazyLoad: true
              dependantField: $.fields.companies2
              mode: multiple
              _options:
                transform: >-
                  $legalEntitiesList($.variables._companyCodesSelected,
                  $.extend.limit, $.extend.page, $.extend.search)
            - type: selectAll
              name: departments
              label: Department
              placeholder: Select Department
              outputValue: value
              mode: multiple
              isLazyLoad: true
              dependantField: $.fields.companies2,$.fields.legalEntities2
              _condition:
                transform: >-
                  $not($.fields.type = 'SYS_OBJECT') and ($.fields.type =
                  'PR_OBJECT' or ($.fields.type = 'INS_OBJECT' and
                  ($.fields.entityOrObjSelect.functionCode = 'INS_006' or
                  $.fields.entityOrObjSelect.functionCode = 'INS_008')))
              _options:
                transform: >-
                  $departmentsList($.variables._companyCodesSelected,
                  $.variables._legalEntityCodesSelected, $.extend.limit,
                  $.extend.page, $.extend.search)
            - type: radio
              label: Status
              name: status1
              value: ''
              _condition:
                transform: $.fields.type = 'SYS_OBJECT'
              radio:
                - label: All
                  value: ''
                - label: Active
                  value: 'Y'
                - label: Inactive
                  value: 'N'
        - type: text
          name: companies
          unvisible: true
          _value:
            transform: >-
              ($companyIds:= $.fields.type = 'EMPLOYEE_DATA' ?
              $.fields.employeeDataOptions.companies1 :
              $.fields.employeeDataOptions.companies2; $map($companyIds,
              function($item){ $item.value })[])
        - type: text
          name: legalEntities
          unvisible: true
          _value:
            transform: >-
              ($legalEntityIds:= $.fields.type = 'EMPLOYEE_DATA' ?
              $.fields.employeeDataOptions.legalEntities1 :
              $.fields.employeeDataOptions.legalEntities2; $map($legalEntityIds,
              function($item){ $item.value })[])
        - name: availableDataFields
          type: checkboxPicklist
          dependantField: $.fields.entityOrObj
          _checkboxGroup:
            transform: $.variables._propertiesList
          _condition:
            transform: $.fields.type = 'EMPLOYEE_DATA'
          checkboxSection:
            title: Available Data Fields
            searchConfig:
              placeholder: Search by name
              visible: false
            checkAllConfig:
              label: Select all/Deselect all
              visible: true
          bindingSection:
            title: Selected Data Fields
            showSelectedNumber: true
            showClearAll: true
            emptyText: No data selected
          validators:
            - type: required
    - type: group
      name: filter
      fields:
        - type: text
          name: status
          unvisible: true
          _value:
            transform: $.fields.employeeDataOptions.status1
        - type: text
          name: companies
          unvisible: true
          _value:
            transform: $.fields.employeeDataOptions.companies
    - type: group
      name: foundationObjectOptions
      _condition:
        transform: $.fields.type = 'FO_OBJECT'
      fields:
        - type: checkbox
          name: includeInActiveRecord
          label: Include inactive records
          hiddenLabel: true
          value: false
        - type: checkbox
          name: selectAllRecord
          label: Select all data records
          hiddenLabel: true
          value: false
    - type: group
      name: pickListOptions
      _condition:
        transform: $.fields.type = 'PICKLIST'
      fields:
        - type: checkbox
          name: includeInActiveRecord
          label: Include inactive records
          hiddenLabel: true
          value: false
        - type: checkbox
          name: selectAllRecord
          label: Select all data records
          hiddenLabel: true
          value: false
  sources:
    entityObjectList:
      uri: '"/api/picklists/IMPORTEXPORTENTITYOBJ/values"'
      method: GET
      queryTransform: '{''filter'': [{''field'':''search'',''operator'': ''$eq'',''value'': $.code}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $.data
      disabledCache: true
      params:
        - code
    fileEncodingList:
      uri: '"/api/picklists/ENCODING/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.name.default}})[]
      disabledCache: true
    fileLocaleList:
      uri: '"/api/picklists/LOCALE/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.name.default}})[]
      disabledCache: true
    typeList:
      uri: '"/api/picklists/IMPORTEXPORTTYPE/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item}})[]
      disabledCache: true
    typesList:
      uri: '"/api/import-export-type"'
      method: GET
      queryTransform: '{''filter'': [{''field'':''typeQuery'',''operator'': ''$eq'',''value'': 1}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: '$map($.data, function($item) {{''label'': $item.name, ''value'': $item}})[]'
      disabledCache: true
    companiesList:
      uri: '"/api/companies/get-by"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    legalEntitiesList:
      uri: '"/api/legal-entities/v2/get-list"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator':
        '$eq','value':true},{'field':'companyCode','operator':
        '$eq','value':$.companyCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id}})[]
      disabledCache: true
      params:
        - companyCode
        - limit
        - page
        - search
    departmentsList:
      uri: '"/api/departments/v2/get-list"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'companyCode','operator':'$eq','value':$.companyCode},{'field':'legalEntityCode','operator':
        '$eq','value':$.legalEntityCode},{'field':'status','operator':
        '$eq','value':true},{'field':'search','operator':
        '$eq','value':$.search}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id}})[]
      disabledCache: true
      params:
        - companyCode
        - legalEntityCode
        - limit
        - page
        - search
    propertiesList:
      uri: '"/api/export-common/get-properties-of-entity"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'type','operator':
        '$eq','value':$.typeSelect.code},{'field':'entityOrObj','operator':
        '$eq','value':$.entityOrObj},{'field':'module','operator':
        '$eq','value':$.typeSelect.module}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.fieldName, 'value':
        $item.fieldName, 'checked': $item.isRequired, 'disabled':
        $item.isRequired}})[]
      disabledCache: true
      params:
        - typeSelect
        - entityOrObj
  variables:
    _typeList:
      transform: $typesList()
    _typeListDefault:
      transform: >-
        ($typeCode := $.extend.dataRedirect.type and $.extend.dataRedirect.type
        = 'PR_OBJECT' and $not($.extend.dataRedirect.entityOrObj =
        'ManageExternalPayrollImports') ? $.extend.dataRedirect.type :
        'EMPLOYEE_DATA'; $filter($.variables._typeList , function($v, $i, $a) {
        $v.value.code = $typeCode }))
    _entityObjectList:
      transform: >-
        $.variables._typeList ? $map($filter($.variables._typeList, function($v,
        $i, $a) {$v.value.code = $.fields.type}).value.details, function($item)
        {{'label': $item.name.'en-US' ? $item.name.'en-US' : $item.name.default,
        'value': $item.entities, 'controller': $item.controller, 'functionCode':
        $item.functionCode }})[]
    _entityObjectDefault:
      transform: >-
        $.fields.typeSelect.code = 'PR_OBJECT' and
        $.extend.dataRedirect.entityOrObj and $not($.fields.isFisrtInit = null)
        ? $filter($.variables._entityObjectList, function($v, $i, $a) {$v.value
        = $.extend.dataRedirect.entityOrObj})
    _fileEncodingList:
      transform: $fileEncodingList()
    _fileLocaleList:
      transform: $fileLocaleList()
    _companyCodesSelected:
      transform: >-
        ($companyCodes:= $.fields.type = 'EMPLOYEE_DATA' ?
        $.fields.employeeDataOptions.companies1 :
        $.fields.employeeDataOptions.companies2; $map($companyCodes,
        function($item){ $item.value })[])
    _legalEntityCodesSelected:
      transform: >-
        ($legalEntityCodes:= $.fields.type = 'EMPLOYEE_DATA' ?
        $.fields.employeeDataOptions.legalEntities1 :
        $.fields.employeeDataOptions.legalEntities2; $map($legalEntityCodes,
        function($item){ $item.value })[])
    _propertiesList:
      transform: >-
        $exists($.fields.type) and $.fields.type = 'EMPLOYEE_DATA' and
        $.fields.entityOrObj ?
        $propertiesList($.fields.typeSelect,$.fields.entityOrObj)
filter_config: {}
layout_options:
  show_detail_history: false
  is_reload_form: false
  page_header_options:
    visible: true
    breadcrumb: false
    border: false
  header_buttons_condition:
    export-data:
      _disabled: $not($boolean($.isValidData))
layout_options__header_buttons:
  - id: export-data
    title: Export
    icon: icon-upload-simple-bold
    type: primary
    size: small
options: null
create_form: null
layout_options__footer_buttons: null
layout_options__row_actions: null
backend_url: api/common-02
screen_name: export-data
layout_options__actions_many: null
parent: HR.FS.FR.092
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key: null
screen_designs: []
function_specs: []
fields: []
children: []
menu_item: null
