id: HR.FS.FR.096_1
status: draft
sort: 447
user_created: d63712b1-cfe5-41d3-874d-e394fe1215f5
date_created: '2024-08-08T09:26:36.389Z'
user_updated: 56e71b91-dd39-4ebc-afd1-b4f00f38d4a5
date_updated: '2025-06-19T02:15:01.977Z'
title: View Schedule
requirement:
  time: 1747627187461
  blocks:
    - id: DhjRUXOCEF
      type: paragraph
      data:
        text: View Schedule Manage Report&nbsp;
  version: 2.30.7
screen_design: null
module: HR
local_fields:
  - code: scheduleName
    pinned: true
    title: Title
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    options__tabular__column_width: 25
    show_sort: true
  - code: author
    title: Author
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    options__tabular__column_width: 7
  - code: occurrenceCode
    title: Occurence
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    options__tabular__column_width: 7
  - code: firstOccurrence
    title: Start Date
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy hh:mm:ss AM/PM
      collection: field_types
    options__tabular__column_width: 15
    show_sort: true
  - code: status
    title: Status
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tag
      collection: field_types
    extra_config:
      tags:
        - value: Scheduled
          class: infor
        - value: Waiting
          class: violet
        - value: In Progress
          class: warning
        - value: Cancel
          class: default
        - value: Completed
          class: success
        - value: Failed
          class: error
    options__tabular__column_width: 10
  - code: sourceJobId
    title: 'Source Job ID '
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 15
    show_sort: true
  - code: triggerJobId
    title: Triggered Job ID
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 20
    show_sort: true
mock_data: null
local_buttons: null
layout: layout-table
form_config:
  fields:
    - type: group
      label: Schedule Defination
      collapse: false
      n_cols: 1
      fields:
        - type: group
          collapsed: false
          disableEventCollapse: true
          label: 1. Schedule Defination
          n_cols: 1
          fields:
            - type: group
              collapsed: false
              disableEventCollapse: true
              label: ''
              n_cols: 2
              fields:
                - name: scheduleName
                  label: Schedule Name
                  type: text
                  display_type:
                    key: tooltip
                    collection: field_types
                  validators:
                    - type: required
                  placeholder: Enter schedule name
                - name: scheduleFormat
                  label: Report Format
                  type: select
                  validators:
                    - type: required
                  placeholder: Select Report Format
                  outputValue: value
                  _select:
                    transform: >-
                      ($defaultList := [{'label': 'Excel', 'value': 0},{'label':
                      'CSV', 'value': 1},{'label': 'PDF', 'value': 2}];
                      $exists($.extend.extraData.downloadOptionConfiguration) ?
                      $.extend.extraData.downloadOptionConfiguration :
                      $defaultList)
            - type: textarea
              label: Send Notification To
              name: listEmail
              placeholder: Enter Email
              description: >-
                Enter additional Email addresses, separated by commas, for all
                the users who want to receive the notifications.
              textarea:
                autoSize:
                  minRows: 2
            - type: group
              collapsed: false
              disableEventCollapse: true
              n_cols: 5
              fields:
                - type: button
                  label: Send Notification On
                  readOnly: true
                  description: >-
                    Email will be sent on Job Start and Job Completion to the
                    Schedule Creator and additional “Send Notification to” email
                    addresses.
                  col: 5
                - type: checkbox
                  label: Schedule Start
                  name: isSendNotiOnStart
                  value: false
                  prefixLabel: false
                  hiddenLabel: true
                - type: checkbox
                  label: Schedule Completion
                  name: isSendNotiOnCompletion
                  value: false
                  prefixLabel: false
                  hiddenLabel: true
    - type: group
      label: Schedule Occurrence
      collapse: false
      fields:
        - type: group
          label: 3. Schedule Occurrence
          collapsed: false
          disableEventCollapse: true
          n_cols: 2
          fields:
            - type: radio
              label: Occurs
              name: occurrenceCode
              value: Recurring
              validators:
                - type: required
              radio:
                - label: Recurring
                  value: Recurring
                - label: Once
                  value: Once
            - name: recurringPatternCode
              label: Recurring Pattern
              type: select
              outputValue: value
              placeholder: Select Recurring Pattern
              select:
                - label: Daily
                  value: Daily
                - label: Weekly
                  value: Weekly
                - label: Monthly
                  value: Monthly
                - label: Yearly
                  value: Yearly
              validators:
                - type: required
              _condition:
                transform: $.fields.occurrenceCode = 'Recurring'
        - type: group
          collapsed: false
          disableEventCollapse: true
          n_cols: 2
          fields:
            - type: dateRange
              mode: date-picker
              label: First Occurrence
              name: firstOccurrence
              description: >-
                Choose the date and time for the first occurrence of this job.
                Subsequent occurrences will use the same time.
              placeholder: dd/MM/yyyy HH:mm
              validators:
                - type: required
                - type: ppx-custom
                  args:
                    transform: >-
                      $exists($.fields.firstOccurrence) and
                      $DateToTimestamp($.fields.firstOccurrence) <
                      $DateToTimestamp($now())
                  text: First Occurrence must be greater than to the current time
              setting:
                format: dd/MM/yyyy HH:mm
                type: minute
                hasTimePicker: true
                disabledDate:
                  value: today
                  operator: $lt
            - type: dateRange
              mode: date-picker
              label: Ending On
              name: endingOn
              placeholder: dd/MM/yyyy
              validators:
                - type: required
                - type: ppx-custom
                  args:
                    transform: >-
                      $exists($.fields.firstOccurrence) and
                      $exists($.fields.endingOn) ?
                      $DateDiff($DateFormat($.fields.firstOccurrence,
                      'yyyy-MM-DD'), $DateFormat($.fields.endingOn,
                      'yyyy-MM-DD'), 'd') > 0
                  text: >-
                    Ending On must be greater than or equal to the First
                    Occurrence
              setting:
                format: dd/MM/yyyy
                autoFill: end-of
                disabledDate:
                  value: today
                  operator: $lt
              _condition:
                transform: $.fields.occurrenceCode = 'Recurring'
        - type: group
          collapsed: false
          disableEventCollapse: true
          fields:
            - type: checkbox
              label: Last of period
              name: isLastPeriod
              value: false
              prefixLabel: false
              hiddenLabel: true
              _condition:
                transform: >-
                  $.fields.occurrenceCode = 'Recurring' and
                  ($.fields.recurringPatternCode = 'Weekly' or
                  $.fields.recurringPatternCode = 'Monthly')
            - type: text
              label: isBaseJob
              name: isBaseJob
              value: false
              unvisible: true
  _mode:
    transform: '''tabset'''
filter_config:
  fields:
    - name: title
      label: Title
      type: text
      placeholder: Enter title
    - name: author
      label: Author
      type: text
      placeholder: Enter Author
    - name: occurrence
      label: Occurence
      type: select
      placeholder: Select Occurence
      mode: multiple
      select:
        - label: Once
          value: Once
        - label: Recurring
          value: Recurring
    - name: startDate
      label: Start Date
      type: dateRange
      placeholder: dd/MM/yyyy
      setting:
        format: dd/MM/yyyy
        type: date
      validators:
        - type: ppx-custom
          args:
            transform: >-
              $count($.fields.startDate) > 0 ?
              $DateDiff($DateFormat($.fields.startDate[0], 'yyyy-MM-DD'),
              $DateFormat($.fields.startDate[1], 'yyyy-MM-DD'), 'd') > 0
          text: To date must be greater than start date
    - name: status
      label: Status
      type: select
      placeholder: Select Status
      mode: multiple
      select:
        - label: Scheduled
          value: '0'
        - label: Waiting
          value: '1'
        - label: In Progress
          value: '2'
        - label: Cancel
          value: '3'
        - label: Completed
          value: '4'
        - label: Failed
          value: '5'
    - name: sourceJobId
      label: Source Job ID
      type: select
      placeholder: Select Source Job ID
      isLazyLoad: true
      _select:
        transform: >-
          $reportPrameterList($.extend.limit, $.extend.page, $.extend.search,
          true)
    - name: triggerJobId
      label: Triggered  Job ID
      type: select
      placeholder: Select Triggered  Job ID
      isLazyLoad: true
      _select:
        transform: >-
          $reportPrameterList($.extend.limit, $.extend.page, $.extend.search,
          'false')
  filterMapping:
    - field: scheduleName
      operator: $cont
      valueField: title
    - field: author
      operator: $cont
      valueField: author
    - field: occurrenceCode
      operator: $in
      valueField: occurrence.(value)
    - field: firstOccurrence
      operator: $between
      valueField: startDate
    - field: statusCode
      operator: $in
      valueField: status.(value)
    - field: sourceJobId
      operator: $eq
      valueField: sourceJobId.value
    - field: triggerJobId
      operator: $eq
      valueField: triggerJobId.value
  sources:
    reportPrameterList:
      uri: '"/api/report-parameter"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'search': $.search,
        'filter':[{'field':'isBaseJob','operator': '$eq','value':$.isBaseJob}] }
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.id & ' (' &
        $item.scheduleName & ')', 'value': $item.id}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - isBaseJob
layout_options:
  show_detail_drawer: false
  toolTable:
    adjustDisplay: 'true'
  disabled_click_row: true
  show_detail_history: false
  show_table_header_action: true
  show_table_checkbox: false
  is_popup: true
  edit_step_layout: true
  mapping_row_actions_state: true
layout_options__header_buttons:
  - id: refresh
    title: Refresh
    icon: icon-repeat-bold
    type: primary
options: null
create_form:
  fields:
    - type: group
      label: Schedule Information
      fields:
        - name: code
          type: text
          label: Code
        - name: scheduleName
          type: text
          label: Name
        - name: status
          type: text
          label: Status
    - type: group
      label: Schedule Defination
      fields:
        - name: scheduleFormat
          label: Report Format
          type: select
          placeholder: Select Report Format
          outputValue: value
          _select:
            transform: >-
              ($defaultList := [{'label': 'Excel', 'value': 0},{'label': 'CSV',
              'value': 1},{'label': 'PDF', 'value': 2}];
              $exists($.extend.extraData.downloadOptionConfiguration) ?
              $.extend.extraData.downloadOptionConfiguration : $defaultList)
        - name: listEmail
          type: text
          label: Send Notification To
        - name: sendNotificationOn
          type: text
          label: Send Notification On
          _value:
            transform: >-
              ($listString := []; $.extend.defaultValue.isSendNotiOnStart ?
              $listString := $append($listString,'Schedule Start');
              $.extend.defaultValue.isSendNotiOnCompletion ? $listString :=
              $append($listString,'Schedule Completion'); $join($listString, ';
              '))
    - type: group
      label: Schedule Settings
      fields:
        - type: text
          name: occurrenceCode
          label: Schedule Type
        - type: text
          name: recurringPatternCode
          label: Frequency
          _value:
            transform: >-
              $.extend.defaultValue.recurringPatternCode &
              ($.extend.defaultValue.isLastPeriod = true ? ' ( Last of period )'
              : '')
        - type: dateRange
          name: firstOccurrence
          label: Start Date
          mode: date-picker
          setting:
            format: dd/MM/yyyy HH:mm
            type: date
        - type: dateRange
          name: endingOn
          label: End Date
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
          _condition:
            transform: >-
              $.fields.occurrenceCode = 'Recurring' or
              $not($.extend.defaultValue.isBaseJob = true)
        - type: text
          name: reportParameterId
          label: Source Job ID
        - type: text
          name: triggerJobId
          label: Triggered Job ID
    - type: group
      label: Report Parameters
      isDynamicConfig: true
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: run-schedule
    icon: icon-play-circle-bold
    type: ghost-gray
    condition_func: null
    _disabled: >-
      $.occurrenceCode = 'Once' or ($.occurrenceCode = 'Recurring' and
      $.statusCode = 3 ) or ($.occurrenceCode = 'Recurring' and $.statusCode = 4
      )
    title: Run Now
  - id: view-schedule
    icon: icon-info-bold
    type: ghost-gray
    condition_func: null
    title: View Details
  - id: edit
    icon: icon-pencil-simple-bold
    type: ghost-gray
    condition_func: null
    _disabled: '($not($.statusCode = 0) and $not($.statusCode = 3)) '
    title: Edit
  - id: download-schedule
    icon: icon-download-simple-bold
    type: ghost-gray
    group: expand
    title: Download
    condition_func: null
    _disabled: $not($.statusCode = 4)
  - id: cancel-schedule
    icon: icon-x-circle-bold
    type: ghost-gray
    group: expand
    title: Cancel
    condition_func: null
    _disabled: >-
      $not($.statusCode = 0) and $not($.statusCode = 1) and $not($.statusCode =
      2)
  - id: delete
    icon: icon-trash-bold
    type: ghost-gray
    group: expand
    title: Delete
    condition_func: null
    _disabled: $not($.statusCode = 3)
backend_url: /api/report-parameter
screen_name: null
layout_options__actions_many: null
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key: null
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: View Schedule Manage Report
  parent: null
