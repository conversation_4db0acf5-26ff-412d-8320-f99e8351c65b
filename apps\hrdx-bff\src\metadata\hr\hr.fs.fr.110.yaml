id: HR.FS.FR.110
status: draft
sort: 548
user_created: 2aa5db12-fa54-46d6-8e05-9e421858de0c
date_created: '2024-08-15T07:19:10.810Z'
user_updated: 56e71b91-dd39-4ebc-afd1-b4f00f38d4a5
date_updated: '2025-06-24T04:32:21.225Z'
title: Manage Black/Block List
requirement:
  time: 1747735249144
  blocks:
    - id: LsmMzLzDEV
      type: paragraph
      data:
        text: >-
          - Chứ<PERSON> năng cho phép tìm kiếm danh sách nhân viên thuộc Black
          list/Block list
  version: 2.30.7
screen_design: null
module: HR
local_fields:
  - code: employeeId
    title: Employee ID
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    show_sort: true
  - code: fullName
    title: Full Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    options__tabular__column_width: 15
    show_sort: true
  - code: isBlackList
    title: Black List
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: Tag
      collection: field_types
    options__tabular__align: left
    extra_config:
      tags:
        - value: true
          label: 'Yes'
          class: success
        - value: false
          label: 'No'
          class: error
  - code: isBlockList
    title: Block List
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: Tag
      collection: field_types
    options__tabular__align: left
    extra_config:
      tags:
        - value: true
          label: 'Yes'
          class: success
        - value: false
          label: 'No'
          class: error
  - code: reason
    title: Block/Black List Reason
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 15
  - code: userName
    title: User name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: employeeRecordNumber
    title: Employee Number Record
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 15
    show_sort: true
  - code: companyName
    title: Company
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    pinned: false
  - code: jobName
    title: Job
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
  - code: departmentName
    title: Department
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
  - code: hrStatusName
    title: HR Status
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
    extra_config:
      sortByCode: hrStatusCode
  - code: updatedAt
    title: Last Modified
    data_type:
      key: String
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
    options__tabular__column_width: 15
    show_sort: true
  - code: updatedBy
    title: Update By
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
mock_data:
  - employeeId: '00000001'
    fullName: Nguyễn Văn An
    blackList: true
    blockList: true
    effectiveDate: 2024/08/15 10:30:00
    blockBlackListReason: Sử dụng bằng giả
    userName: annv
    employeeNumberRecord: '0'
    company: FPT IS
    job: BVN
    department: Admin
    hrStatus: Active
    lastModified: 2024/08/15 10:30:00
    updatedBy: Nguyễn Văn Anh
local_buttons: null
layout: layout-table
form_config:
  formTitle:
    proceed: Add Employee to Black/Block List
    create: Add Employee to Black/Block List
  _formTitle:
    proceed: '''Add '' & $.employeeView & '' to Black/Block List'''
    create: '''Add '' & $.employeeView & '' to Black/Block List'''
  historyHeaderTitle: $.employeeView
  isNotViewBageLeftSidebar: true
  fields:
    - type: group
      label: Block/Black List Information
      collapse: false
      n_cols: 2
      fieldGroupTitleStyle:
        padding: 0 0 12px 0
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - type: text
          unvisible: true
          name: jobDataId
        - type: text
          name: code
          unvisible: true
          _value:
            transform: $exists($.fields.employeeId) ? $.fields.employeeId
        - name: startDate
          label: Effective Date
          type: dateRange
          mode: date-picker
          _disabled:
            transform: $not($.extend.formType = 'edit')
          setting:
            format: dd/MM/yyyy
            type: date
          validators:
            - type: required
        - type: text
          label: Employee ID
          name: employeeId
          unvisible: true
        - type: text
          label: Employee ID
          placeholder: Select Employee ID
          name: employeeView
          disabled: true
          _customDisabledTitle:
            transform: $.fields.employeeView
        - type: checkbox
          label: Block List
          hiddenLabel: true
          name: isBlockList
          value: false
        - type: checkbox
          label: Black List
          hiddenLabel: true
          name: isBlackList
          value: false
        - type: textarea
          label: Block/Black List Reason
          name: reason
          col: 2
          textarea:
            autoSize:
              minRows: 3
            maxCharCount: 1000
          value: ''
          _class:
            transform: >-
              $.extend.formType = 'view' ? 'unrequired' : ($.fields.isBlackList
              = true or $.fields.isBlockList = true ) ? 'required' :
              'unrequired'
          validators:
            - type: maxLength
              args: 1000
              text: Block/Black List Reason should not exceed 1000 characters
    - type: group
      label: Job Data
      collapse: false
      isBorderTopLabel: true
      n_cols: 2
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - name: employeeRecordNumber
          label: Employee Record Number
          type: text
          disabled: true
          _class:
            transform: $.extend.formType = 'view' ? 'unrequired'
          validators:
            - type: required
        - name: companyCode
          label: Company
          type: text
          unvisible: true
        - name: companyView
          label: Company
          type: text
          disabled: true
          _customDisabledTitle:
            transform: $.fields.companyView
        - name: departmentCode
          label: Department
          type: text
          unvisible: true
        - name: departmentView
          label: Department
          type: text
          disabled: true
          _customDisabledTitle:
            transform: $.fields.departmentView
        - name: jobCode
          label: Job
          type: text
          unvisible: true
        - name: jobView
          label: Job
          type: text
          disabled: true
          _customDisabledTitle:
            transform: $.fields.jobView
        - name: terminateDate
          label: Terminate Date
          type: dateRange
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
          disabled: true
        - name: actionName
          label: Action
          type: text
          disabled: true
          _customDisabledTitle:
            transform: $.fields.actionName
        - name: reasonName
          label: Reason
          type: text
          disabled: true
          col: 2
          _customDisabledTitle:
            transform: $.fields.reasonName
    - type: group
      label: Block/Black List Information
      collapse: false
      isBorderTopNone: true
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - type: text
          unvisible: true
          name: jobDataId
        - type: text
          name: code
          unvisible: true
          _value:
            transform: $exists($.fields.employeeId) ? $.fields.employeeId
        - name: startDate
          label: Effective Date
          type: dateRange
          mode: date-picker
          _disabled:
            transform: $not($.extend.formType = 'edit')
          _class:
            transform: $.extend.formType = 'view' ? 'unrequired'
          setting:
            format: dd/MM/yyyy
            type: date
          validators:
            - type: required
        - type: text
          label: Employee ID
          name: employeeId
          unvisible: true
        - type: text
          label: Employee ID
          placeholder: Select Employee ID
          name: employeeView
          disabled: true
        - type: checkbox
          label: Block List
          hiddenLabel: true
          name: isBlockList
          value: false
          _unvisible:
            transform: $.extend.formType = 'view'
        - type: text
          label: Block List
          name: isBlockListView
          _value:
            transform: '$.fields.isBlockList ? ''Yes'' : ''No'''
        - type: checkbox
          label: Black List
          hiddenLabel: true
          name: isBlackList
          value: false
          _unvisible:
            transform: $.extend.formType = 'view'
        - type: text
          label: Black List
          name: isBlackListView
          _value:
            transform: '$.fields.isBlackList ? ''Yes'' : ''No'''
        - type: textarea
          label: Block/Black List Reason
          name: reason
          textarea:
            autoSize:
              minRows: 3
            maxCharCount: 1000
          value: ''
          _class:
            transform: >-
              $.extend.formType = 'view' ? 'unrequired' : ($.fields.isBlackList
              = true or $.fields.isBlockList = true )? 'required' : 'unrequired'
          validators:
            - type: ppx-custom
              args:
                transform: >-
                  ($.fields.isBlackList = true or $.fields.isBlockList = true)
                  and $not($length($.fields.reason) > 0)
              text: Black/Black List Reason is required
            - type: maxLength
              args: 1000
              text: Block/Black List Reason should not exceed 1000 characters
    - type: group
      label: Job Data
      collapse: false
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - name: employeeRecordNumber
          label: Employee Record Number
          type: text
          disabled: true
          _class:
            transform: $.extend.formType = 'view' ? 'unrequired'
          validators:
            - type: required
        - name: companyCode
          label: Company
          type: text
          unvisible: true
        - name: companyView
          label: Company
          type: text
          disabled: true
          _class:
            transform: $.extend.formType = 'view' ? 'unrequired'
        - name: departmentCode
          label: Department
          type: text
          unvisible: true
        - name: departmentView
          label: Department
          type: text
          disabled: true
          _class:
            transform: $.extend.formType = 'view' ? 'unrequired'
        - name: jobCode
          label: Job
          type: text
          unvisible: true
        - name: jobView
          label: Job
          type: text
          disabled: true
        - name: terminateDate
          label: Terminate Date
          type: dateRange
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
          disabled: true
        - name: actionName
          label: Action
          type: text
          disabled: true
        - name: reasonName
          label: Reason
          type: text
          disabled: true
  historyTitle: $DateFormat($.startDate, 'DD/MM/YYYY')
  historyDescription: >-
    ($isBlackList := $.isBlackList ? 'Black list' : ''; $isBlockList :=
    $.isBlockList ? 'Block list' : ''; $label := $isBlackList and $isBlockList ?
    $isBlackList & ' - ' & $isBlockList : $isBlackList ? 'Black list' :
    $isBlockList ? 'Block list' : '' ; $label & ($.reason ? ' - ' & $.reason :
    ''))
  sources:
    personalsList:
      uri: '"/api/black-block-infos/dropdown"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data,function($item){{'label':$item.fullName & ' (' &
        $item.employeeId & ')','value':$item.employeeId ,'employeeRecordNumber':
        $item.employeeRecordNumber, 'companyCode': $item.companyCode,
        'departmentCode': $item.departmentCode, 'jobCode': $item.jobCode,
        'companyName': $item.companyName, 'departmentName':
        $item.departmentName, 'jobName': $item.jobName}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    jobDatasList:
      uri: '"/api/personals/" & $.empId & "/job-datas"'
      method: GET
      queryTransform: >-
        {'filter':[{'field':'employeeRecordNumber','operator':'$eq','value':
        $.employeeRecordNumber},{'field':'hrStatus','operator':'$eq','value':
        'I'}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'terminateDate': $item.terminateDate,
        'actionName': $item.actionName, 'regionName': $item.regionName,
        'company': $item.company, 'department': $item.department, 'job':
        $item.job}})[]
      disabledCache: true
      params:
        - empId
        - employeeRecordNumber
filter_config:
  fields:
    - type: text
      label: User Name
      name: userName
      placeholder: Enter User Name
      labelType: type-grid
    - type: text
      label: Employee Number Record
      name: employeeRecordNumber
      placeholder: Enter Employee Number Record
      labelType: type-grid
    - name: employee
      type: select
      label: Employee
      placeholder: Select Employee
      isLazyLoad: true
      mode: multiple
      _select:
        transform: $personalsList($.extend.limit, $.extend.page, $.extend.search)
      labelType: type-grid
    - type: select
      name: company
      label: Company
      placeholder: Select Company
      mode: multiple
      isLazyLoad: true
      _select:
        transform: $companiesList($.extend.limit, $.extend.page, $.extend.search)
      labelType: type-grid
    - type: select
      name: jobCode
      label: Job
      placeholder: Select Job
      mode: multiple
      outputValue: value
      isLazyLoad: true
      _select:
        transform: $jobsList($.extend.limit, $.extend.page, $.extend.search, $now())
      labelType: type-grid
    - type: select
      name: departmentCode
      label: Department
      placeholder: Select Department
      mode: multiple
      outputValue: value
      isLazyLoad: true
      _select:
        transform: >-
          $departmentsList($.extend.limit, $.extend.page, $.extend.search,
          $now())
      labelType: type-grid
    - type: select
      name: hrStatusCode
      label: HR Status
      placeholder: Select HR Status
      outputValue: value
      isLazyLoad: true
      _select:
        transform: $hrStatusList()
      labelType: type-grid
    - type: radio
      name: blockList
      value: ''
      label: Block List
      radio:
        - label: All
          value: ''
        - label: 'Yes'
          value: true
        - label: 'No'
          value: false
      labelType: type-grid
    - type: radio
      name: blackList
      value: ''
      label: Black List
      radio:
        - label: All
          value: ''
        - label: 'Yes'
          value: true
        - label: 'No'
          value: false
      labelType: type-grid
    - type: textarea
      label: Black/Block List Reason
      name: reason
      textarea:
        autoSize:
          minRows: 3
        maxCharCount: 1024
      labelType: type-grid
    - type: dateRange
      label: Last Modified
      name: updatedAt
      labelType: type-grid
    - type: text
      label: Update By
      name: updatedBy
      placeholder: Enter Update By
      labelType: type-grid
  filterMapping:
    - field: userName
      operator: $cont
      valueField: userName
    - field: employeeRecordNumber
      operator: $eq
      valueField: employeeRecordNumber
    - field: employeeId
      operator: $in
      valueField: employee.(value)
    - field: companyCode
      operator: $in
      valueField: company.(value)
    - field: jobCode
      operator: $in
      valueField: jobCode
    - field: departmentCode
      operator: $in
      valueField: departmentCode
    - field: hrStatusCode
      operator: $eq
      valueField: hrStatusCode
    - field: effectiveDate
      operator: $beetween
      valueField: effectiveDate
    - field: isBlockList
      operator: $eq
      valueField: blockList
    - field: isBlackList
      operator: $eq
      valueField: blackList
    - field: reason
      operator: $cont
      valueField: reason
    - field: updatedAt
      operator: $between
      valueField: updatedAt
    - field: updatedBy
      operator: $cont
      valueField: updatedBy
  sources:
    personalsList:
      uri: '"/api/personals/all-employees"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.name & ' (' & $item.employeeId
        & ')', 'value': $item.employeeId}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    companiesList:
      uri: '"/api/companies/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator':
        '$lte','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
    jobsList:
      uri: '"/api/job-codes"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator':
        '$lte','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
    departmentsList:
      uri: '"/api/departments"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator':
        '$lte','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
    hrStatusList:
      uri: '"/api/picklists/HRSTATUS/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $append([{'label': 'All', 'value': ''}],$map($.data, function($item)
        {{'label': $item.name.default, 'value': $item.code}})[])
      disabledCache: true
layout_options:
  delete_multi_items: true
  history_widget_header_options:
    duplicate: false
  is_custom_insert_new_proceed: true
  tool_table:
    - id: export
      icon: icon-download-simple
  custom_delete_body: >-
    $.isHistory ? {'ids': $map($.data,function($item){$item.id})[]} :
    {'employeeIds': $map($.data,function($item){$item.employeeId})[]}
  apply_delete_multi_items_to_delete_one: true
  is_check_permission_with_accessType: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form:
  formTitle:
    proceed: Add Employee to Black/Block List
    proceedCustom: Add Employee to Black/Block List
  fields:
    - type: group
      n_cols: 2
      fields:
        - type: text
          name: code
          unvisible: true
          _value:
            transform: $exists($.fields.employeeId) ? $.fields.employeeId
        - name: startDate
          label: Effective Date
          type: dateRange
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
          _value:
            transform: $now()
          validators:
            - type: required
        - type: select
          label: Employee ID
          placeholder: Select Employee ID
          isLazyLoad: true
          _select:
            transform: >-
              ($effectiveDate  := $DateToTimestamp($now()) <
              $DateToTimestamp($.fields.startDate) ? $.fields.startDate;
              $personalsList($.extend.limit, $.extend.page, $.extend.search,
              $effectiveDate ))
          validators:
            - type: required
          name: employeeObj
          _condition:
            transform: $not($.extend.formType = 'proceedCustom')
          handleAfterChange:
            dataSource:
              transform: $.fieldValue
            valueMapping:
              - field: employeeId
                fieldValue: employeeId
              - field: employeeRecordNumber
                fieldValue: employeeRecordNumber
                _setNullValue: 'true'
              - field: jobDataId
                fieldValue: id
              - field: employeeRecordNumberView
                fieldValue: employeeRecordNumber
                _setNullValue: 'true'
              - field: companyCode
                fieldValue: companyCode
              - field: companyView
                fieldValue: companyView
              - field: companyDisplay
                fieldValue: companyView
              - field: departmentCode
                fieldValue: departmentCode
              - field: departmentView
                fieldValue: departmentView
              - field: departmentDisplay
                fieldValue: departmentView
              - field: jobCode
                fieldValue: jobCode
              - field: jobView
                fieldValue: jobView
              - field: jobDisplay
                fieldValue: jobView
              - field: terminateDate
                fieldValue: terminateDate
              - field: actionName
                fieldValue: actionName
              - field: reasonName
                fieldValue: reasonName
          clearFieldsAfterChange:
            - employeeId
            - employeeRecordNumber
            - jobDataId
            - employeeRecordNumberView
            - companyCode
            - companyView
            - companyDisplay
            - departmentCode
            - departmentView
            - departmentDisplay
            - jobCode
            - jobView
            - jobDisplay
            - terminateDate
            - actionName
            - reasonName
        - type: text
          label: Employee ID
          placeholder: Select Employee ID
          _disabled:
            transform: $.extend.formType = 'proceedCustom'
          name: employeeView
          _value:
            transform: >-
              $.fields.employeeObj.viewFullName ?
              $.fields.employeeObj.viewFullName
          _unvisible:
            transform: $not($.extend.formType = 'proceedCustom')
          _customDisabledTitle:
            transform: $.fields.employeeView
        - type: text
          unvisible: true
          name: fullName
        - type: text
          unvisible: true
          name: employeeId
        - type: select
          label: Employee Record Number
          placeholder: Select Employee Record Number
          isLazyLoad: true
          _select:
            transform: >-
              $ErnBypersonalsList( $.extend.limit, $.extend.page,
              $.extend.search, $.fields.employeeId, $.fields.startDate)
          validators:
            - type: required
          name: employeeObj
          _condition:
            transform: $boolean($.extend.formType = 'proceedCustom')
          handleAfterChange:
            dataSource:
              transform: $.fieldValue
            valueMapping:
              - field: employeeId
                fieldValue: employeeId
              - field: employeeRecordNumber
                fieldValue: employeeRecordNumber
                _setNullValue: 'true'
              - field: jobDataId
                fieldValue: id
              - field: employeeRecordNumberView
                fieldValue: employeeRecordNumber
                _setNullValue: 'true'
              - field: companyCode
                fieldValue: companyCode
              - field: companyView
                fieldValue: companyView
              - field: companyDisplay
                fieldValue: companyView
              - field: departmentCode
                fieldValue: departmentCode
              - field: departmentView
                fieldValue: departmentView
              - field: departmentDisplay
                fieldValue: departmentView
              - field: jobCode
                fieldValue: jobCode
              - field: jobView
                fieldValue: jobView
              - field: jobDisplay
                fieldValue: jobView
              - field: terminateDate
                fieldValue: effectiveDate
              - field: actionName
                fieldValue: actionName
              - field: reasonName
                fieldValue: actionReasonName
          clearFieldsAfterChange:
            - employeeId
            - employeeRecordNumber
            - jobDataId
            - employeeRecordNumberView
            - companyCode
            - companyView
            - companyDisplay
            - departmentCode
            - departmentView
            - departmentDisplay
            - jobCode
            - jobView
            - jobDisplay
            - terminateDate
            - actionName
            - reasonName
        - type: number
          unvisible: true
          label: Employee Record Number
          name: employeeRecordNumber
        - type: text
          unvisible: true
          name: jobDataId
        - type: number
          disabled: true
          label: Employee Record Number
          validators:
            - type: required
          name: employeeRecordNumberView
          _condition:
            transform: $not($.extend.formType = 'proceedCustom')
        - type: text
          unvisible: true
          name: companyCode
        - type: text
          unvisible: true
          label: Company
          placeholder: ' '
          name: companyView
        - type: text
          disabled: true
          label: Company
          placeholder: ' '
          validators:
            - type: required
          name: companyDisplay
          _customDisabledTitle:
            transform: $.fields.companyView
        - type: text
          unvisible: true
          name: departmentCode
        - type: text
          unvisible: true
          label: Department
          placeholder: ' '
          name: departmentView
        - type: text
          disabled: true
          label: Department
          placeholder: ' '
          validators:
            - type: required
          name: departmentDisplay
          _customDisabledTitle:
            transform: $.fields.departmentView
        - type: text
          unvisible: true
          name: jobCode
        - type: text
          unvisible: true
          label: Job
          placeholder: ' '
          name: jobView
        - type: text
          disabled: true
          label: Job
          placeholder: ' '
          name: jobDisplay
          _customDisabledTitle:
            transform: $.fields.jobView
        - name: terminateDate
          label: Terminate Date
          type: dateRange
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
          unvisible: true
        - name: actionName
          label: Action
          type: text
          unvisible: true
        - name: reasonName
          label: Reason
          type: text
          unvisible: true
  sources:
    personalsList:
      uri: '"/api/black-block-infos/dropdown"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'effectiveDate','operator': '$eq','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        ( $mapNameAndCode := function($name, $code) {  $name and $code ? $name &
        ' (' & $code & ')' :   $name ? $name :  $code ? $code : '' };
        $map($.data, function($item) {   $item ~> | $ | { 'label':$item.fullName
        & ' (' & $item.employeeId & ')' & ' - ' & $item.employeeRecordNumber & '
        - ' & $item.companyName & ' - ' & $item.departmentName, 'value':
        $item.employeeRecordNumber & '-' & $item.employeeId,   'viewFullName':
        $mapNameAndCode($item.fullName, $item.employeeId),     'companyView':
        $mapNameAndCode($item.companyName, $item.companyCode),    
        'departmentView': $mapNameAndCode($item.departmentName,
        $item.departmentCode),   'jobView': $mapNameAndCode($item.jobName,
        $item.jobCode) , 'jobDataId': $item.id, 'employeeRecordNumber' :
        $item.employeeRecordNumber   } |  })[] )
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
    ErnBypersonalsList:
      uri: '"/api/personals/" & $.employeeId & "/job-datas/employee-unique-ern"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search':
        $.search,'filter':[{'field':'hrStatus','operator':'$eq','value': 'I'},
        {'field':'effectiveDate','operator': '$eq','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        ( $mapNameAndCode := function($name, $code) {  $name and $code ? $name &
        ' (' & $code & ')' :   $name ? $name :  $code ? $code : '' };
        $map($filter($,function($it){$it.hrStatus = 'I'}), function($item) {  
        $item ~> | $ | { 'label': $string($item.employeeRecordNumber), 'value':
        $item.employeeRecordNumber, 'viewFullName':
        $mapNameAndCode($item.fullName, $item.employeeId),   'companyView':
        $mapNameAndCode($item.companyName, $item.companyCode),    
        'departmentView': $mapNameAndCode($item.departmentName,
        $item.department),   'jobView': $mapNameAndCode($item.jobName,
        $item.job) ,'jobDataId': $item.id ,'employeeRecordNumber' :
        $item.employeeRecordNumber  } |  })[] )
      disabledCache: true
      params:
        - limit
        - page
        - search
        - employeeId
        - effectiveDate
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    title: Edit
    icon: icon-pencil-simple
    type: ghost-gray
    condition_func: 'false'
  - id: delete
    title: Delete
    icon: icon-trash
    group: null
    type: ghost-gray
    condition_func: 'false'
backend_url: /api/black-block-infos
screen_name: null
layout_options__actions_many: null
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key: null
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Manage Black/Block List
  parent:
    title: Human Resource
