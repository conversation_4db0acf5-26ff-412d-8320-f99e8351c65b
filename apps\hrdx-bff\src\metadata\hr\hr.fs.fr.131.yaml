id: HR.FS.FR.131
status: draft
sort: 69
user_created: 56e71b91-dd39-4ebc-afd1-b4f00f38d4a5
date_created: '2025-01-20T03:11:06.480Z'
user_updated: 56e71b91-dd39-4ebc-afd1-b4f00f38d4a5
date_updated: '2025-07-28T03:16:14.507Z'
title: Employee Payment Account
requirement:
  time: *************
  blocks:
    - id: EJy508QaWF
      type: paragraph
      data:
        text: >-
          - <PERSON><PERSON><PERSON> tài khoản ngân hàng có thể gắn với nhiề<PERSON> bản ghi employee number
          record.&nbsp;
  version: 2.30.7
screen_design: null
module: HR
local_fields:
  - code: runTypeName
    title: Run Type Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 5
mock_data: null
local_buttons: null
layout: layout-widget
form_config:
  formTitle:
    proceed: Add New Employee Payment Account
    create: Add New Employee Payment Account
    edit: Edit Employee Payment Account
  formSize:
    create: large
    edit: large
    proceed: large
    view: middle
  isFilterRight: false
  styleFilterForm:
    padding: 0
    borderBottom: none
  fields:
    - type: group
      _condition:
        transform: >-
          $not($.extend.formType = 'view') and $not($.extend.formType =
          'viewMore')
      n_cols: 2
      fields:
        - type: select
          label: Run Type Name
          name: runTypeCode
          placeholder: Select Run Type
          _select:
            transform: $runTypeList()
          outputValue: value
          validators:
            - type: required
        - type: select
          label: Employee Record Number
          name: employeeRecordNumberView
          outputValue: value
          placeholder: Select Employee Record Number
          _select:
            transform: $.variables._ernList
          validators:
            - type: required
    - type: array
      name: commands
      minSize: 1
      _condition:
        transform: >-
          $not($.extend.formType = 'view') and $not($.extend.formType =
          'viewMore')
      arrayOptions:
        canChangeSize: true
        add_btn_type: secondary
        add_btn_size: small
      field:
        type: group
        label: Record
        _label:
          transform: '''Record '' & ($ParseInt($.extend.path[-1]) + 1)'
        collapse: false
        disableEventCollapse: true
        fieldGroupContentStyle:
          padding: 20px
        fieldGroupTitleStyle:
          padding: 10px 20px
          borderTop: none
        fieldBackground: '#fff'
        borderRadius: 8px
        border: '1px solid #DFE3E8'
        borderBottomLabel: true
        lastGroupStyleOff: true
        isHideArrow: true
        n_cols: 2
        fields:
          - type: text
            name: id
            unvisible: true
            value: 0
          - type: text
            name: runTypeCode
            unvisible: true
            _value:
              transform: $.fields.runTypeCode
          - type: text
            name: jobDataId
            unvisible: true
            _value:
              transform: $.variables._selectedErn.jobDataId
          - type: dateRange
            mode: date-picker
            label: Effective Date
            name: effectiveDate
            placeholder: dd/MM/yyyy
            scale: 1/2
            col: 2
            setting:
              format: dd/MM/yyyy
            validators:
              - type: required
            _value:
              transform: >-
                $isNilorEmpty($getFieldGroup($.extend.path, $.fields,
                1).effectiveDate) ? ($exists($.variables._selectedErn.value) ?
                ($checkFirst := $checkFirstRecord($.extend.params.id1,
                $.variables._selectedErn.value); $checkFirst.isFirst ?
                $.variables._selectedErn.effectiveDate : $now()))
          - type: text
            label: Company
            name: companyName
            placeholder: Enter Company
            disabled: true
            _value:
              transform: $.variables._selectedErn.companyName
          - type: text
            label: Legal Entity
            name: legalEntityName
            placeholder: Enter Legal Entity
            disabled: true
            _value:
              transform: $.variables._selectedErn.legalEntityName
          - type: text
            label: Department
            name: departmentName
            placeholder: Enter Department
            disabled: true
            _value:
              transform: $.variables._selectedErn.departmentName
          - type: text
            label: Job
            name: jobName
            placeholder: Enter Job
            disabled: true
            _value:
              transform: $.variables._selectedErn.jobName
          - type: array
            mode: table
            name: paymentAccountInfoDetails
            col: 2
            arrayOptions:
              canChangeSize: true
            minSize: 1
            checkEmptyValue: true
            field:
              type: group
              padding: 0
              fields:
                - type: text
                  name: id
                  unvisible: true
                - type: text
                  label: No.
                  name: stt
                  readOnly: true
                  width: 80px
                  _value:
                    transform: $ParseInt($.extend.path[-2]) + 1
                - type: select
                  label: Bank account
                  name: bankAccount
                  placeholder: Select Bank account
                  _select:
                    transform: >-
                      ($effectiveDate := $getFieldGroup($.extend.path, $.fields,
                      3).effectiveDate; $bankAccountId :=
                      $getFieldGroup($.extend.path, $.fields, 1).bankAccountId;
                      $paymentAccountInfoDetails :=
                      $map($getFieldGroup($.extend.path, $.fields,
                      3).paymentAccountInfoDetails,
                      function($it){$it.bankAccountId}) ;
                      $filter($uniqBy($.variables._bankdetailList, 'uniqKey'),
                      function($it) {
                      $DateToTimestamp($DateFormat($it.effectiveDate,'YYYY-MM-DD'))
                      <=
                      $DateToTimestamp($DateFormat($effectiveDate,'YYYY-MM-DD'))
                      and $it.status = true and ($it.id = $bankAccountId or
                      $not( $it.id in $paymentAccountInfoDetails ))})[] )
                  width: 200px
                  validators:
                    - type: required
                  confirmPopup:
                    _enabled:
                      transform: >-
                        $exists($.variables._selectedErn.value) ? ($check :=
                        $checkDuplicateBank($.extend.params.id1,
                        $.variables._selectedErn.value, $.fieldValue.id);
                        $check.status ? $check.message : false )
                    _content:
                      transform: $._enabled
                    listFieldsDependantName:
                      - employeeRecordNumberView
                  handleAfterChange:
                    dataSource:
                      transform: $.fieldValue
                    valueMapping:
                      - field: bankCode
                        fieldValue: bankCode
                      - field: bankAccountId
                        fieldValue: id
                      - field: currencyName
                        fieldValue: paymentCurrency
                  clearFieldsAfterChange:
                    - bankCode
                    - bankAccountId
                    - currencyName
                  allowValueKey: value
                  _allowValues:
                    transform: >-
                      $.variables._bankdetailList ? ($effectiveDate :=
                      $getFieldGroup($.extend.path, $.fields, 3).effectiveDate;
                      $bankAccountId := $getFieldGroup($.extend.path, $.fields,
                      1).bankAccountId; $paymentAccountInfoDetails :=
                      $map($getFieldGroup($.extend.path, $.fields,
                      3).paymentAccountInfoDetails,
                      function($it){$it.bankAccountId}) ; $newData :=
                      $filter($uniqBy($.variables._bankdetailList, 'uniqKey'),
                      function($it) {
                      $DateToTimestamp($DateFormat($it.effectiveDate,'YYYY-MM-DD'))
                      <=
                      $DateToTimestamp($DateFormat($effectiveDate,'YYYY-MM-DD'))
                      and $it.status = true and ($it.id = $bankAccountId or
                      $not( $it.id in $paymentAccountInfoDetails ))})[];
                      [$map($newData, function($it){$it.value})] )
                - type: text
                  label: Bank Name
                  name: bankCode
                  disabled: true
                  width: 150px
                  _customDisabledTitle:
                    transform: >-
                      ($bankNameDefault := $getFieldGroup($.extend.path,
                      $.extend.defaultValue, 0).bankName; $currentBankName :=
                      $getFieldGroup($.extend.path,
                      $.fields,0).bankAccount.bankName ; $currentBankName ?
                      $currentBankName : $bankNameDefault)
                - type: text
                  name: bankAccountId
                  unvisible: true
                - type: text
                  label: Currency
                  name: currencyName
                  disabled: true
                  width: 150px
                - type: number
                  label: Percent
                  name: percent
                  width: 120px
                  value: 0
                  _value:
                    transform: >-
                      $.extend.formType = 'create' ?
                      (($ParseInt($.extend.path[-2]) + 1) = 1 ? 100 : 0)
                  number:
                    format: currency
                    min: 0
                    max: 99999
                  validators:
                    - type: required
                - type: select
                  label: Payment Method
                  name: paymentMethodCode
                  outputValue: value
                  placeholder: Select Payment Method
                  _select:
                    transform: $.variables._paymentTypeList
                  value: T
                  width: 200px
                  validators:
                    - type: required
          - type: textarea
            name: note
            label: Note
            placeholder: Enter note
            col: 2
            validators:
              - type: maxLength
                args: '1000'
                text: Maximum 1000 characters.
            textarea:
              autoSize:
                minRows: 3
              maxCharCount: 1000
    - type: group
      _condition:
        transform: $.extend.formType = 'view' or $.extend.formType = 'viewMore'
      fields:
        - type: text
          label: Run Type Name
          name: runTypeName
          labelType: type-row-readOnly
        - type: text
          label: Employee Record Number
          name: employeeRecordNumber
          labelType: type-row-readOnly
    - type: group
      _condition:
        transform: $.extend.formType = 'view' or $.extend.formType = 'viewMore'
      fieldGroupContentStyle:
        padding: 16px 20px
      fieldBackground: '#fff'
      borderRadius: 8px
      border: '1px solid #DFE3E8'
      margin: 0 0 16px 0
      fields:
        - type: dateRange
          mode: date-picker
          label: Effective Date
          name: effectiveDate
          placeholder: dd/MM/yyyy
          scale: 1/2
          setting:
            format: dd/MM/yyyy
          validators:
            - type: required
        - type: text
          label: Company
          name: companyName
          placeholder: Enter Company
        - type: text
          label: Legal Entity
          name: legalEntityName
          placeholder: Enter Legal Entity
        - type: text
          label: Department
          name: departmentName
          placeholder: Enter Department
        - type: text
          label: Job
          name: jobName
          placeholder: Enter Job
        - type: textarea
          name: note
          label: Note
          placeholder: Enter note
        - type: table
          name: paymentAccountInfoDetails
          layout_option:
            tool_table:
              show_table_checkbox: false
              show_table_filter: false
              show_table_group: false
              hidden_header: false
              collapse: false
            show_pagination: false
            hide_action_row: true
          columns:
            - code: stt
              title: No.
              align: start
              data_type:
                key: String
                collection: data_types
              display_type:
                key: Label
                collection: field_types
              width: 3.2
            - code: bankAccount
              title: Bank Account
              align: start
              data_type:
                key: String
                collection: data_types
              display_type:
                key: Label
                collection: field_types
              width: 12
            - code: bankCode
              title: Bank Name
              align: start
              data_type:
                key: String
                collection: data_types
              display_type:
                key: Label
                collection: field_types
              width: 7.5
            - code: currencyName
              title: Currency
              align: start
              data_type:
                key: String
                collection: data_types
              display_type:
                key: Label
                collection: field_types
              width: 6.25
            - code: percent
              title: Percent
              align: start
              data_type:
                key: String
                collection: data_types
              display_type:
                key: Label
                collection: field_types
              width: 5
            - code: paymentMethodName
              title: Payment Method
              align: start
              data_type:
                key: String
                collection: data_types
              display_type:
                key: Label
                collection: field_types
              width: 10
  historyTitle: $.employeeRecordNumber & ' - ' & $.runTypeName
  historyDescription: $DateFormat($.effectiveDate, 'DD/MM/YYYY')
  sources:
    runTypeList:
      uri: '"/api/picklists/RUNTYPE/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})
      disabledCache: true
    paymentTypeList:
      uri: '"/api/picklists/PAYMENTTYPE/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})
      disabledCache: true
    ernList:
      uri: '"/api/personals/" & $.empId & "/job-datas/employee-unique-ern"'
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.employeeRecordNumber & ' - '  &
        $item.companyName & ' - ' & $item.departmentName & ' - ' & $item.jobName
        , 'value': $string($item.employeeRecordNumber), 'jobDataId': $item.id
        ,'companyName': $item.companyName, 'legalEntityName':
        $item.legalEntityName, 'departmentName': $item.departmentName,
        'jobName': $item.jobName, 'effectiveDate': $item.effectiveDate,
        'employeeRecordNumber': $item.employeeRecordNumber}})[]
      disabledCache: true
      params:
        - empId
    bankdetailList:
      uri: '"/api/personals/" & $.empId & "/bank-account-infos/histories"'
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {$merge([$item,{'label': $item.bankAccount & ' -
        '  & $item.bankName & ' - ' & $item.payee & ' - ' &
        $item.paymentCurrency , 'value': $item.id, 'uniqKey': $item.bankAccount
        & '-'  & $item.bankCode}]) })[]
      disabledCache: true
      params:
        - empId
    checkFirstRecord:
      uri: >-
        "/api/personals/" & $.empId &
        "/payment-account-infos/check-first-record"
      method: GET
      queryTransform: >-
        {'filter':[{'field':'employeeRecordNumber','operator':'$eq','value':
        $.employeeRecordNumber}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $
      disabledCache: true
      params:
        - empId
        - employeeRecordNumber
    checkDuplicateBank:
      uri: >-
        "/api/personals/" & $.empId &
        "/payment-account-infos/check-duplicate-bank"
      method: GET
      queryTransform: >-
        {'filter':[{'field':'employeeRecordNumber','operator':'$eq','value':
        $.employeeRecordNumber},{'field':'bankAccountId','operator':'$eq','value':
        $.bankAccountId}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $
      disabledCache: true
      params:
        - empId
        - employeeRecordNumber
        - bankAccountId
  variables:
    _bankdetailList:
      transform: '$.extend.params.id1 ? $bankdetailList($.extend.params.id1) : []'
    _paymentTypeList:
      transform: $paymentTypeList()
    _paymentAccountInfoDetails:
      transform: >-
        $reduce($.fields.commands, function($acc, $item) { $append($acc,
        $map($item.paymentAccountInfoDetails, function($it) { $it.bankAccountId
        })) }, [])
    _ernList:
      transform: >-
        $not($.extend.formType = 'view') and $not($.extend.formType =
        'viewMore') and $.extend.params.id1 ? $ernList($.extend.params.id1)
    _selectedErn:
      transform: >-
        $.variables._ernList ? $filter($.variables._ernList,
        function($it){$it.value = $.fields.employeeRecordNumberView or $it.value
        = $.fields.employeeRecordNumberView.value})[0]
filter_config:
  fields:
    - type: group
      padding: 10px 20px
      fieldBackground: '#F1F3F5'
      fields:
        - type: select
          label: Employee Record Number
          name: ernData
          labelType: type-row
          placeholder: Select Employee Record Number
          _select:
            transform: $.variables._ernListTransform
          _defaultValue:
            transform: >-
              $not($.extend.formType = 'filter') ? $.variables._ernListTransform
              ? $.variables._ernListTransform[0]
          allowValueKey: value
          _allowValues:
            transform: >-
              $exists($.variables._ernListTransform) ?
              $map($.variables._ernListTransform, function($it){$it.value})[]
  filterMapping:
    - field: id
      operator: $eq
      valueField: ernData.value
    - field: runtypeCode
      operator: $eq
      valueField: ernData.runTypeCode
    - field: employeeRecordNumber
      operator: $eq
      valueField: ernData.employeeRecordNumber
  sources:
    ernList:
      uri: '"/api/personals/" & $.empId & "/payment-account-infos/dropdown"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: '[$]'
      disabledCache: true
      params:
        - empId
  variables:
    _ernList:
      transform: $.extend.params.id1 ? $ernList($.extend.params.id1,$.extend.refresh)
    _ernListTransform:
      transform: >-
        $.variables._ernList ? $map($.variables._ernList,
        function($it){{'label': $it.employeeRecordNumber & ' - ' &
        $it.runTypeName , 'value': $it.id, 'employeeRecordNumber':
        $string($it.employeeRecordNumber), 'runTypeCode': $it.runTypeCode}})[]
layout_options:
  filterType: widget
  widget_options:
    show_more_content_type: widget-drawer
    hyperlinkInForm:
      type: scrollToBlock
      route: HR.FS.FR.018
      title: Maintain Bank Details
  widget_content_type: readonly-form
  is_check_permission_with_accessType: true
  check_permission_record_history: true
  custom_value_before_edit: >-
    {'commands': $append([],$merge([$,{'paymentAccountInfoDetails':
    $map($.paymentAccountInfoDetails, function($it){$merge([$it,{'bankAccount':
    {'value':$it.bankAccountId}}])})[]}])), 'runTypeCode': $.runTypeCode,
    'employeeRecordNumberView': {'label': $.employeeRecordNumber & ' - '  &
    $.companyName & ' - ' & $.departmentName & ' - ' & $.jobName,'value':
    $string($.employeeRecordNumber)}, 'id': $.id}
layout_options__header_buttons:
  - id: create
    title: Create
    icon: icon-plus-bold
  - id: history
    title: history
    icon: icon-clock-counter-clockwise-bold
options: null
create_form: null
layout_options__footer_buttons: null
layout_options__row_actions: null
backend_url: api/personals/:id1/payment-account-infos
screen_name: null
layout_options__actions_many: null
parent: HR.FS.FR.000
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: company
    defaultName: CompanyCode
  - name: department
    defaultName: DepartmentCode
  - name: employeeId
    defaultName: EmployeeId
  - name: legalEntityCode
    defaultName: LegalEntityCode
  - name: bussinessUnit
    defaultName: BusinessUnitCode
  - name: division
    defaultName: DivisionCode
  - name: employeeRecordNumber
    defaultName: EmployeeRecordNumber
  - name: location
    defaultName: LocationCode
  - name: job
    defaultName: JobCode
  - name: employeeLevelCode
    defaultName: EmployeeLevelCode
  - name: employeeSubGroup
    defaultName: EmployeeSubGroupCode
  - name: nationCode
    defaultName: CountryCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item: null
