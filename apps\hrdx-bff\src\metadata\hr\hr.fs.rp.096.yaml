id: HR.FS.RP.096
status: draft
sort: 530
user_created: 56e71b91-dd39-4ebc-afd1-b4f00f38d4a5
date_created: '2024-10-08T23:58:58.800Z'
user_updated: 56e71b91-dd39-4ebc-afd1-b4f00f38d4a5
date_updated: '2025-06-19T02:15:33.043Z'
title: Manage Report
requirement:
  time: 1749111444065
  blocks:
    - id: 91KCL_breY
      type: paragraph
      data:
        text: >-
          Ch<PERSON><PERSON> năng cho phép người dùng tạo hoặc xem kết quả báo cáo được tạo
          theo phân quyền chức năng tại màn hình quản lý báo cáo
  version: 2.30.7
screen_design: null
module: HR
local_fields:
  - code: star
    title: Favourite
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: Star
      collection: field_types
    options__tabular__column_width: 3
    extra_config:
      hidden_column_title: true
      add_favourite: true
      not_set_min_width: true
    pinned: true
    resizable: false
    show_sort: false
    dragable: false
  - code: code
    pinned: true
    title: Code
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    show_sort: true
    options__tabular__column_width: 10
    extra_config:
      generate_report: true
  - code: name
    pinned: false
    title: Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    show_sort: true
    options__tabular__column_width: 15
  - code: category
    title: Category
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tags
      collection: field_types
    show_sort: false
    extra_config:
      singular: Category
      plural:
        group: Categories
        item: Category
    options__tabular__column_width: 15
  - code: author
    title: Author
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    show_sort: true
    options__tabular__column_width: 15
  - code: reportTypeCode
    title: Type
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    show_sort: true
    options__tabular__column_width: 15
mock_data: []
local_buttons: null
layout: layout-table
form_config:
  fields:
    - type: text
      name: code
      unvisible: true
    - type: group
      collapsed: false
      disableEventCollapse: true
      n_cols: 2
      fields:
        - name: run
          label: Run
          type: select
          validators:
            - type: required
          value: online
          _condition:
            transform: >-
              $.extend.formType = 'create' or $.extend.formType = 'edit' or
              $.extend.formType = 'view'
          placeholder: Select Run
          select:
            - label: Online
              value: online
            - label: Offline
              value: offline
        - name: fileType
          label: ' '
          type: select
          _condition:
            transform: $.fields.run.value = 'offline'
          placeholder: Select file type
          select:
            - label: Excel
              value: Excel
            - label: CSV
              value: CSV
            - label: PDF
              value: PDF
    - type: group
      label: Require Parameters
      collapsed: false
      fields:
        - type: group
          collapsed: false
          disableEventCollapse: true
          name: ' '
          n_cols: 1
          fields:
            - name: paramenter1
              label: Paramenter 1
              type: text
              _condition:
                transform: >-
                  $.extend.formType = 'create' or $.extend.formType = 'edit' or
                  $.extend.formType = 'view'
              validators:
                - type: required
              placeholder: Enter value
            - name: paramenter2
              label: Paramenter 2
              type: text
              _condition:
                transform: >-
                  $.extend.formType = 'create' or $.extend.formType = 'edit' or
                  $.extend.formType = 'view'
              validators:
                - type: required
              placeholder: Enter value
            - name: paramenter3
              label: Paramenter 3
              type: text
              _condition:
                transform: >-
                  $.extend.formType = 'create' or $.extend.formType = 'edit' or
                  $.extend.formType = 'view'
              validators:
                - type: required
              placeholder: Enter value
            - name: paramenter4
              label: Paramenter 4
              type: text
              _condition:
                transform: >-
                  $.extend.formType = 'create' or $.extend.formType = 'edit' or
                  $.extend.formType = 'view'
              validators:
                - type: required
              placeholder: Enter value
filter_config:
  fields:
    - name: code
      label: Code
      type: selectAll
      mode: multiple
      labelType: type-grid
      placeholder: Select Code
      outputValue: value
      isLazyLoad: true
      _options:
        transform: $reportStoragesList($.extend.limit, $.extend.page, $.extend.search)
    - name: name
      label: Name
      type: text
      labelType: type-grid
      placeholder: Enter Name
    - name: category
      label: Category
      type: selectAll
      mode: multiple
      labelType: type-grid
      placeholder: Select Category
      isLazyLoad: true
      _options:
        transform: $reportCategoriesList($.extend.limit, $.extend.page, $.extend.search)
    - name: author
      label: Author
      type: text
      labelType: type-grid
      placeholder: Enter Author
    - name: type
      label: Type
      type: selectAll
      labelType: type-grid
      outputValue: value
      placeholder: Select Type
      _options:
        transform: '[{ ''label'': ''Table'', ''value'': ''Table'' } ]'
  filterMapping:
    - field: code
      operator: $in
      valueField: code
    - field: name
      operator: $cont
      valueField: name
    - field: reportCategories
      operator: $eq
      valueField: category.(value)
    - field: author
      operator: $cont
      valueField: author
    - field: reportTypeCode
      operator: $eq
      valueField: type
  sources:
    reportStoragesList:
      uri: '"/api/report-storages"'
      method: GET
      queryTransform: '{''limit'': $.limit, ''page'': $.page, ''search'': $.search }'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        [$map($.data, function($item) {{'label': $item.code & ' - ' &
        $item.name, 'value': $item.code}})]
      disabledCache: true
      params:
        - limit
        - page
        - search
    reportCategoriesList:
      uri: '"/api/report-categories"'
      method: GET
      queryTransform: '{''limit'': $.limit, ''page'': $.page, ''search'': $.search }'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        [$map($.data, function($item) {{'label': $item.categoryName.default,
        'value': $item.id}})]
      disabledCache: true
      params:
        - limit
        - page
        - search
layout_options:
  header_buttons_condition:
    create:
      _disabled: 'true'
  show_actions_many: true
  disabled_click_row: true
  show_create_data_table: false
  defaultTabsetIndex: 1
  export_report_method: POST
  customStyleTabset: true
  tabset:
    - id: myReport
      title: My report
      filter:
        filterValue:
          star: true
        filterMapping:
          - field: star
            operator: $eq
            valueField: star
    - id: allReport
      title: All report
  tool_table:
    - id: folder
      icon: icon-folders-bold
      disabled: false
  folderDisplayForm:
    fields:
      - name: categoryName
        label: Category Name
        placeholder: Enter Category name
        type: translation
        validators:
          - type: required
  link_redirect: /GE/HR.FS.FR.096_1
  modal_footer_buttons:
    - id: cancel
      title: Return to Report
      type: tertiary
    - id: generateReport
      title: Generate Report
      type: primary
  no_need_confirm: true
  dialogTable:
    title: Manage Category
    columns:
      - code: categoryName
        title: Category Name
        align: start
        width: 200
    show_table_checkbox: false
    show_pagination: false
    api_url: /api/report-categories
    actions:
      add: true
      edit: true
      delete: true
    form:
      fields:
        - name: categoryName
          label: Category Name
          placeholder: Enter Category name
          type: translation
          validators:
            - type: required
layout_options__header_buttons:
  - id: redirect
    title: View Schedules
    icon: icon-queue-bold
    type: secondary
    href: /GE/HR.FS.FR.096_1
  - id: create
    title: Create
    icon: icon-plus-bold
    type: primary
options: null
create_form:
  fields:
    - type: group
      label: Schedule Defination
      collapse: false
      n_cols: 1
      fields:
        - type: group
          collapsed: false
          disableEventCollapse: true
          label: 1. Schedule Defination
          n_cols: 1
          fields:
            - type: group
              collapsed: false
              disableEventCollapse: true
              label: ''
              n_cols: 2
              fields:
                - name: scheduleName
                  label: Schedule Name
                  type: text
                  display_type:
                    key: tooltip
                    collection: field_types
                  validators:
                    - type: required
                  placeholder: Enter schedule name
                - name: scheduleFormat
                  label: Report Format
                  type: select
                  validators:
                    - type: required
                  placeholder: Select Report Format
                  outputValue: value
                  _select:
                    transform: >-
                      ($defaultList := [{'label': 'Excel', 'value': 0},{'label':
                      'CSV', 'value': 1},{'label': 'PDF', 'value': 2}];
                      $exists($.extend.extraData.downloadOptionConfiguration) ?
                      $.extend.extraData.downloadOptionConfiguration :
                      $defaultList)
            - type: textarea
              label: Send Notification To
              name: listEmail
              placeholder: Enter Email
              description: >-
                Enter additional Email addresses, separated by commas, for all
                the users who want to receive the notifications.
              textarea:
                autoSize:
                  minRows: 2
            - type: group
              collapsed: false
              disableEventCollapse: true
              n_cols: 5
              fields:
                - type: button
                  label: Send Notification On
                  readOnly: true
                  description: >-
                    Email will be sent on Job Start and Job Completion to the
                    Schedule Creator and additional “Send Notification to” email
                    addresses.
                  col: 5
                - type: checkbox
                  label: Schedule Start
                  name: isSendNotiOnStart
                  value: false
                  prefixLabel: false
                  hiddenLabel: true
                - type: checkbox
                  label: Schedule Completion
                  name: isSendNotiOnCompletion
                  value: false
                  prefixLabel: false
                  hiddenLabel: true
    - type: group
      label: Schedule Occurrence
      collapse: false
      fields:
        - type: group
          label: 3. Schedule Occurrence
          collapsed: false
          disableEventCollapse: true
          n_cols: 2
          fields:
            - type: radio
              label: Occurs
              name: occurrenceCode
              value: Recurring
              validators:
                - type: required
              radio:
                - label: Recurring
                  value: Recurring
                - label: Once
                  value: Once
            - name: recurringPatternCode
              label: Recurring Pattern
              type: select
              outputValue: value
              placeholder: Select Recurring Pattern
              select:
                - label: Daily
                  value: Daily
                - label: Weekly
                  value: Weekly
                - label: Monthly
                  value: Monthly
                - label: Yearly
                  value: Yearly
              validators:
                - type: required
              _condition:
                transform: $.fields.occurrenceCode = 'Recurring'
        - type: group
          collapsed: false
          disableEventCollapse: true
          n_cols: 2
          fields:
            - type: dateRange
              mode: date-picker
              label: First Occurrence
              name: firstOccurrence
              description: >-
                Choose the date and time for the first occurrence of this job.
                Subsequent occurrences will use the same time.
              placeholder: dd/MM/yyyy HH:mm
              validators:
                - type: required
                - type: ppx-custom
                  args:
                    transform: >-
                      $exists($.fields.firstOccurrence) and
                      $DateToTimestamp($.fields.firstOccurrence) <
                      $DateToTimestamp($now())
                  text: First Occurrence must be greater than to the current time
              setting:
                format: dd/MM/yyyy HH:mm
                type: minute
                hasTimePicker: true
                disabledDate:
                  value: today
                  operator: $lt
            - type: dateRange
              mode: date-picker
              label: Ending On
              name: endingOn
              placeholder: dd/MM/yyyy
              validators:
                - type: required
                - type: ppx-custom
                  args:
                    transform: >-
                      $exists($.fields.firstOccurrence) and
                      $exists($.fields.endingOn) ?
                      $DateDiff($DateFormat($.fields.firstOccurrence,
                      'yyyy-MM-DD'), $DateFormat($.fields.endingOn,
                      'yyyy-MM-DD'), 'd') > 0
                  text: >-
                    Ending On must be greater than or equal to the First
                    Occurrence
              setting:
                format: dd/MM/yyyy
                autoFill: end-of
                disabledDate:
                  value: today
                  operator: $lt
              _condition:
                transform: $.fields.occurrenceCode = 'Recurring'
        - type: group
          collapsed: false
          disableEventCollapse: true
          fields:
            - type: checkbox
              label: Last of period
              name: isLastPeriod
              value: false
              prefixLabel: false
              hiddenLabel: true
              _condition:
                transform: >-
                  $.fields.occurrenceCode = 'Recurring' and
                  ($.fields.recurringPatternCode = 'Weekly' or
                  $.fields.recurringPatternCode = 'Monthly')
            - type: text
              label: isBaseJob
              name: isBaseJob
              value: false
              unvisible: true
  _mode:
    transform: '''tabset'''
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: generate-report
    title: Generate report
    icon: icon-play-circle-bold
    type: ghost-gray
  - id: add-category
    title: Category As
    icon: icon-folder-bold
    type: ghost-gray
  - id: new-schedule
    title: New Schedule
    icon: icon-clock-countdown-bold
    type: ghost-gray
    group: null
backend_url: /api/report-storages
screen_name: null
layout_options__actions_many:
  - id: addCategory
    title: Category As
    icon: icon-folder-simple-plus-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key: null
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Manage Report
  parent:
    title: Manage Report
