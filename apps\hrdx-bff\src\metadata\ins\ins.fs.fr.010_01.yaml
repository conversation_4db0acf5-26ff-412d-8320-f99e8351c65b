id: INS.FS.FR.010_01
status: draft
sort: 559
user_created: 11767dbb-19f9-4795-8331-850f94c3c6e3
date_created: '2024-10-05T08:58:02.651Z'
user_updated: 1029d146-842f-41b9-9cb4-06b6253ce435
date_updated: '2025-07-21T02:06:11.495Z'
title: Set up Salary Plan by Job Title
requirement:
  time: 1748403427896
  blocks:
    - id: RocrQMxIGT
      type: paragraph
      data:
        text: Set Up Insurance Salary Plan
  version: 2.30.7
screen_design: null
module: INS
local_fields:
  - code: code
    title: Insurance Salary Plan Code
    description: Mã bảng lương
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: name
    title: Insurance Salary Plan Name
    description: Tên bảng lương
    show_sort: true
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
  - code: version
    title: Version
    description: Phiên bản
    show_sort: true
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
  - code: countryName
    title: Country
    description: Quốc gia
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: companyName
    title: Company
    description: Công ty
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: legalEntityName
    title: Legal Entity
    description: Pháp nhân
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: effectiveDate
    title: ' Effective Date'
    description: Ngày hiệu lực
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/YYYY
      collection: field_types
    show_sort: true
  - code: status
    title: Status
    description: Trạng thái
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Boolean Tag
      collection: field_types
    show_sort: true
  - code: note
    title: Note
    description: Ghi chú
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: updatedBy
    title: Last Updated By
    description: Người cập nhật mới nhất
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
    options__tabular__column_width: 11
  - code: updatedAt
    title: Last Updated On
    description: Ngày cập nhật mới nhất
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
    show_sort: true
    options__tabular__column_width: 11
mock_data: null
local_buttons: null
layout: layout-table
form_config:
  formTitle:
    create: Add New Salary Plan by Job Title
    duplicate: Duplicate Salary Plan by Job Title
    edit: Edit Salary Plan by Job Title
    view: Details of Salary Plan by Job Title
  _formTitle:
    duplicate: '''Duplicate Salary Plan by Job Title'''
  defaultValidateError: Required fields have not been filled in completely (fields marked with *)
  formSize:
    create: largex
    edit: largex
    view: largex
  _mode:
    transform: >-
      $.extend.formType != 'view' ? {'name': 'mark-scroll',
      'showCollapseSection': true}
  fields:
    - type: group
      label: General Information
      disableEventCollapse: true
      fields:
        - type: group
          n_cols: 2
          collapsed: false
          disableEventCollapse: true
          fields:
            - type: text
              label: Insurance Salary Plan Code
              name: code
              validators:
                - type: required
              placeholder: Enter Insurance Salary Plan Code
            - type: translation
              label: Version
              name: version
              placeholder: Enter Version
              validators:
                - type: required
        - type: translation
          label: Insurance Salary Plan Name
          name: name
          placeholder: Enter Insurance Salary Plan Name
          validators:
            - type: required
            - type: maxLength
              args: '500'
              text: Insurance Salary Plan Name should not exceed 500 characters
        - type: group
          n_cols: 2
          collapsed: false
          disableEventCollapse: true
          fields:
            - type: number
              label: Month to Next Step
              name: monthNextStepIncrement
              placeholder: Enter Month to Next Step
              number:
                min: 0
            - type: select
              label: Country
              name: countryCode
              placeholder: Select Country
              isLazyLoad: true
              _select:
                transform: >-
                  $countryList($.extend.limit, $.extend.page,
                  $.extend.search,$.fields.effectiveDate)
        - type: group
          n_cols: 2
          collapsed: false
          disableEventCollapse: true
          fields:
            - type: select
              label: Company
              name: companyCode
              placeholder: Select Company
              _select:
                transform: >-
                  $companyList($.extend.limit, $.extend.page,
                  $.extend.search,$.fields.effectiveDate)
              isLazyLoad: true
              clearFieldsAfterChange:
                - legalEntityCode
            - type: select
              label: Legal Entity
              name: legalEntityCode
              placeholder: Select Legal Entity
              isLazyLoad: true
              _select:
                transform: ' $exists($.fields.companyCode.id)? $legalEntityList($.fields.effectiveDate, $.fields.companyCode.id) : $legalEntityList($.fields.effectiveDate, $.variables._selectedCompany.id)'
        - type: group
          n_cols: 2
          collapsed: false
          disableEventCollapse: true
          fields:
            - type: dateRange
              label: Effective Date
              name: effectiveDate
              mode: date-picker
              placeholder: Enter Effective Date
              setting:
                format: dd/MM/yyyy
                type: date
              validators:
                - type: required
              _value:
                transform: $.extend.formType = 'create' ? $now()
            - type: radio
              label: Status
              name: status
              _value:
                transform: $.extend.formType = 'create' ? true
              radio:
                - value: true
                  label: Active
                - value: false
                  label: InActive
        - type: group
          n_cols: 2
          collapsed: false
          disableEventCollapse: true
          fields:
            - type: text
              label: Attach File
              name: attachFileForm
              unvisible: true
              _value:
                transform: >-
                  $.extend.formType = 'create' ? $.fields.attachFile :
                  $.fields.attachFileName
            - type: upload
              label: Attach File
              name: attachFileName
              upload:
                accept:
                  - >-
                    application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
                  - application/pdf
                  - application/vnd.ms-excel
                isMultiple: false
              _condition:
                transform: $.extend.formType = 'edit'
            - type: upload
              name: attachFile
              label: Attach File
              upload:
                accept:
                  - >-
                    application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
                  - application/pdf
                  - application/vnd.ms-excel
                isMultiple: false
              _condition:
                transform: $.extend.formType = 'create'
            - type: translationTextArea
              name: note
              label: Note
              placeholder: Enter Note
              textarea:
                autoSize:
                  minRows: 4
                  maxRows: 5
                maxCharCount: 1000
      _condition:
        transform: $.extend.formType = 'create' or $.extend.formType = 'edit'
    - type: group
      label: General information
      disableEventCollapse: true
      fields:
        - type: text
          label: Insurance Salary Plan Code
          name: code
        - type: translation
          label: Insurance Salary Plan Name
          name: name
        - type: translation
          label: Version
          name: version
        - type: upload
          label: Attach File
          name: attachFileResults
          upload:
            accept:
              - >-
                application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
              - application/pdf
              - application/vnd.ms-excel
            isMultiple: false
        - type: text
          label: Country
          name: countryName
        - type: text
          label: Company
          name: companyName
        - type: text
          label: Legal Entity
          name: legalEntityName
        - type: number
          label: Month to Next Step
          name: monthNextStepIncrement
        - type: dateRange
          label: Effective Date
          name: effectiveDate
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
          _value:
            transform: $.extend.formType = 'create' ? $now()
        - type: radio
          label: Status
          name: status
          radio:
            - value: true
              label: Active
            - value: false
              label: InActive
        - type: translation
          label: Note
          name: note
      _condition:
        transform: $.extend.formType = 'view'
    - type: group
      label: Set Up Detail
      disableEventCollapse: true
      fields:
        - type: text
          label: View Details
          name: viewDetails
          unvisible: true
        - type: treeTable
          _condition:
            transform: $.extend.formType = 'create' and $not($.extend.isDuplicate)
          treeForm:
            form:
              - type: group
                label: Set up detail
                mode: multiple
                collapse: false
                fields:
                  - title: No.
                    align: left
                    width: '5'
                    fixedLeft: true
                    autoIndex: true
                  - title: Insurance Region
                    align: left
                    code: insuranceRegionName
                  - title: Insurance Salary Level
                    align: left
                    code: insuranceSalaryLevelName
                    unvisible: true
                  - title: Insurance Salary Step
                    align: left
                    code: insuranceSalaryStepName
                    fixedLeft: true
                  - title: Salary Code by Job Title
                    display_type: input
                    align: left
                    code: salaryJobTitleCode
                    fixedLeft: true
                  - title: Insured Salary
                    display_type: Currency
                    align: left
                    code: insuranceSalary
                    fixedLeft: true
                  - title: Currency
                    align: left
                    code: currencyName
                    fixedLeft: true
          group:
            keyGroup: insuranceSalaryClassCode
            rowExpandTitle:
              - insuranceSalaryClassName
            keyTitle: insuranceSalaryClassName
          _dataSource:
            transform: $.variables._resultData
          rowActions:
            delete: {}
          config:
            addSetup: true
            filter: true
          actions:
            search: {}
            addSetup:
              title: Set Up Detail
              uniqueFields:
                - insuranceSalaryClassCode
                - insuranceSalaryLevelCode
                - insuranceSalaryStepCode
                - insuranceRegionCode
              addValidate:
                type: noDataAdded
                errorMessage: >-
                  The record you want to add already exist. Please check the
                  data
              form:
                - type: group
                  collapse: false
                  n_cols: 2
                  fields:
                    - type: select
                      label: Insurance Salary Class
                      name: insuranceSalaryClass
                      isLazyLoad: true
                      placeholder: Select Insurance Salary Class
                      _select:
                        transform: >-
                          $insuranceSalaryClassList($.extend.limit,
                          $.extend.page, $.extend.search,
                          $.variables._effectiveDate)
                      _class:
                        transform: >-
                          $count($.variables._resultData) > 0 ? 'unrequired':
                          'required'
                    - type: text
                      name: insuranceSalaryClassName
                      unvisible: true
                      _value:
                        transform: $.fields.insuranceSalaryClass.label
                    - type: text
                      name: insuranceSalaryClassCode
                      unvisible: true
                      _value:
                        transform: $.fields.insuranceSalaryClass.value
                    - type: select
                      label: Insurance Region
                      name: insuranceRegion
                      isLazyLoad: true
                      placeholder: Select Insurance Region
                      _select:
                        transform: $insuranceRegionList($.variables._effectiveDate)
                    - type: text
                      name: insuranceRegionName
                      unvisible: true
                      _value:
                        transform: $.fields.insuranceRegion.label
                    - type: text
                      name: insuranceRegionCode
                      unvisible: true
                      _value:
                        transform: $.fields.insuranceRegion.value
                    - type: select
                      label: Insurance Salary Level
                      name: insuranceSalaryLevel
                      isLazyLoad: true
                      placeholder: Select Insurance Salary Level
                      _select:
                        transform: $insuranceSalaryLevelList($.variables._effectiveDate)
                    - type: selectAll
                      label: Insurance Salary Step
                      name: insuranceSalaryStep
                      isLazyLoad: true
                      mode: multiple
                      placeholder: Select Insurance Salary Step
                      options:
                        maxTag: 7
                      _options:
                        transform: >-
                          $insuranceSalaryStepList($.extend.limit,
                          $.extend.page,
                          $.extend.search,$.variables._effectiveDate)
                      _class:
                        transform: ' $count($.variables._resultData) > 0 ?  ''unrequired'': ''required'''
                - type: group
                  collapse: false
                  n_cols: 4
                  fields:
                    - type: select
                      label: Insured Salary Type
                      name: classification
                      isLazyLoad: true
                      placeholder: Select Insured Salary Type
                      _class:
                        transform: >-
                          $count($.variables._resultData) > 0 ? 'unrequired':
                          'required'
                      _value:
                        transform: $classificationList()[1]
                      _select:
                        transform: $classificationList()
                    - type: number
                      label: Initial Value
                      name: initialValue
                      isLazyLoad: true
                      placeholder: Enter Initial Value
                      number:
                        min: 0
                        format: currency
                        max: '999999999999999'
                        precision: 4
                    - type: number
                      label: Step
                      name: step
                      isLazyLoad: true
                      placeholder: Enter Step
                      number:
                        min: 0
                        format: currency
                        max: '999999999999999'
                        precision: 4
                    - type: select
                      label: Currency
                      name: currency
                      dependantField: $.fields.countryCode
                      placeholder: Select Currency
                      _value:
                        transform: >-
                          $.fields.countryCode and
                          $count($currencyList($.fields.countryCode)) = 1 ?
                          $currencyList($.fields.countryCode)[0].value
                      _select:
                        transform: $currencyList($.fields.countryCode)
                      _condition:
                        transform: $.fields.classification.value = 'WGCSFT_00001'
                      validators:
                        - type: required
              specificData: $.variables._data
              sources:
                countryList:
                  uri: '"/api/picklists/COUNTRIES/values/pagination"'
                  method: GET
                  queryTransform: >-
                    {'limit': $.limit,'page': $.page,'search':
                    $.search,'filter': [{'field':'status','operator':
                    '$eq','value':true}, {'field':'effectiveDate','operator':
                    '$lte','value': $.effectiveDate}]}
                  bodyTransform: ''
                  headerTransform: ''
                  resultTransform: >-
                    $map($.data, function($item) {{'label': $item.name.default,
                    'value': $item.code}})
                  disabledCache: true
                  params:
                    - limit
                    - page
                    - search
                    - effectiveDate
                companyList:
                  uri: '"/api/companies"'
                  queryTransform: >-
                    {'limit': $.limit,'page': $.page, 'search': $.search,
                    'filter': [{'field':'status','operator':
                    '$eq','value':true}, {'field':'effectiveDate','operator':
                    '$lte','value': $.effectiveDate}]}
                  method: GET
                  bodyTransform: ''
                  headerTransform: ''
                  resultTransform: >-
                    $map($.data, function($item) {{'label':
                    $item.longName.default & ' (' & $item.code & ')', 'value':
                    $item.code, 'id': $item.id}})[]
                  disabledCache: true
                  params:
                    - limit
                    - page
                    - search
                    - effectiveDate
                legalEntityList:
                  uri: '"/api/legal-entities"'
                  queryTransform: >-
                    {'filter': [{'field':'status','operator':
                    '$eq','value':true},{'field':'companyId','operator':
                    '$eq','value': $.companyId },
                    {'field':'effectiveDate','operator': '$lte','value':
                    $.effectiveDate}]}
                  method: GET
                  bodyTransform: ''
                  headerTransform: ''
                  resultTransform: >-
                    $map($.data, function($item) {{'label':
                    $item.longName.default & ' (' & $item.code & ')', 'value':
                    $item.code, 'id': $item.id}})[]
                  disabledCache: true
                  params:
                    - effectiveDate
                    - companyId
                insuranceSalaryClassList:
                  uri: '"/api/picklists/INSURANCESALARYCLASS/values/pagination"'
                  method: GET
                  queryTransform: >-
                    {'limit': $.limit,'page': $.page,'search':
                    $.search,'filter': [{'field':'status','operator':
                    '$eq','value':true},{'field':'effectiveDate','operator':
                    '$eq','value':$.effectiveDate}]}
                  bodyTransform: ''
                  headerTransform: ''
                  resultTransform: >-
                    $map($map($distinct($map($.data,function($i){$i.code})),function($c){$.data[code=$c][-1]})[],
                    function($item) {{'label': $item.name.default & ' (' &
                    $item.code & ')', 'value': $item.code, 'id': $item.id}})[]
                  disabledCache: true
                  params:
                    - limit
                    - page
                    - search
                    - effectiveDate
                insuranceRegionList:
                  uri: '"/api/picklists/INSREGION/values"'
                  method: GET
                  queryTransform: >-
                    {'filter': [{'field':'status','operator':
                    '$eq','value':true},{'field':'effectiveDate','operator':
                    '$eq','value':$.effectiveDate}]}
                  bodyTransform: ''
                  headerTransform: ''
                  resultTransform: >-
                    $map($.data, function($item) {{'label': $item.name.default,
                    'value': $item.code}})[]
                  disabledCache: true
                  params:
                    - effectiveDate
                insuranceSalaryLevelList:
                  uri: '"/api/picklists/InsLevel/values"'
                  method: GET
                  queryTransform: >-
                    {'filter': [{'field':'status','operator':
                    '$eq','value':true},{'field':'effectiveDate','operator':
                    '$eq','value':$.effectiveDate}]}
                  bodyTransform: ''
                  headerTransform: ''
                  resultTransform: >-
                    $map($.data, function($item) {{'label': $item.name.default,
                    'value': $item.code}})[]
                  disabledCache: true
                  params:
                    - effectiveDate
                insuranceSalaryStepList:
                  uri: '"/api/picklists/INSURANCESALARYSTEP/values/pagination"'
                  method: GET
                  queryTransform: >-
                    {'limit': $.limit,'page': $.page,'search':
                    $.search,'filter': [{'field':'status','operator':
                    '$eq','value':true},{'field':'effectiveDate','operator':
                    '$eq','value':$.effectiveDate}]}
                  bodyTransform: ''
                  headerTransform: ''
                  resultTransform: >-
                    $map($.data, function($item) {{'label': $item.name.default &
                    ' (' & $item.code & ')', 'value': $item.code}})[]
                  disabledCache: true
                  params:
                    - limit
                    - page
                    - search
                    - effectiveDate
                classificationList:
                  uri: '"/api/picklists/wageclassification/values"'
                  method: GET
                  queryTransform: >-
                    {'filter': [{'field':'status','operator':
                    '$eq','value':true}]}
                  bodyTransform: ''
                  headerTransform: ''
                  resultTransform: >-
                    $map($filter($.data,function($v){$v.code !=
                    'WGCSFT_00002'}), function($item) {{'label':
                    $item.name.default, 'value': $item.code}})[]
                  disabledCache: true
                salaryJobTitleList:
                  uri: '"/api/picklists/salaryJobTitleList/values"'
                  method: GET
                  queryTransform: >-
                    {'filter': [{'field':'status','operator':
                    '$eq','value':true}]}
                  bodyTransform: ''
                  headerTransform: ''
                  resultTransform: >-
                    $map($.data, function($item) {{'label': $item.name.default,
                    'value': $item.code}})[]
                  disabledCache: true
                currencyList:
                  uri: '"/api/picklists/currency/values"'
                  method: GET
                  queryTransform: >-
                    {'filter': [{'field':'status','operator':
                    '$eq','value':true},{'field':'linkCatalogDataCode','operator':
                    '$eq','value':$.countryCode.value}]}
                  bodyTransform: ''
                  headerTransform: ''
                  resultTransform: >-
                    $map($.data, function($item) {{'label': $item.name.default,
                    'value': $item.code}})[]
                  disabledCache: true
                  params:
                    - countryCode
              variables:
                _data:
                  transform: >-
                    $map($.fields.insuranceSalaryStep, function($item,$i1) {
                    {'insuranceSalaryClassCode':
                    $.fields.insuranceSalaryClass.value,
                    'insuranceSalaryClassName':
                    $.fields.insuranceSalaryClass.name,'insuranceRegionCode':
                    $.fields.insuranceRegion.value,'insuranceRegionName':
                    $.fields.insuranceRegion.label,'insuranceSalaryLevelCode':
                    $.fields.insuranceSalaryLevel.value,'insuranceSalaryLevelName':
                    $.fields.insuranceSalaryLevel.label,'insuranceSalaryStepCode':
                    $item.value,'insuranceSalaryStepName':
                    $item.label,'classificationCode':
                    $.fields.classification.value,'currencyCode':
                    $.fields.currency.value,'currencyName':
                    $.fields.currency.label,'insuranceSalary':
                    ($.fields.initialValue and $.fields.step ?
                    $round($.fields.initialValue + $.fields.step*$i1,3) :
                    $.fields.initialValue),'salaryJobTitleCode':
                    $.fields.insuranceSalaryClass.value &
                    ($.fields.insuranceRegion ? ('_' &
                    $.fields.insuranceRegion.value)) &
                    ($.fields.insuranceSalaryLevel ? ('_' &
                    $.fields.insuranceSalaryLevel.value)) & '_' & $item.value  }
                    })[]
                _effectiveDate:
                  transform: $.extend.extendFormValues.effectiveDate
          name: details
          columns:
            - title: No.
              align: left
              width: '5'
              fixedLeft: true
              autoIndex: true
            - title: Insurance Region
              align: left
              code: insuranceRegionName
            - title: Insurance Salary Level
              align: left
              code: insuranceSalaryLevelName
              unvisible: true
            - title: Insurance Salary Step
              align: left
              code: insuranceSalaryStepName
              fixedLeft: true
            - title: Salary Code by Job Title
              display_type: input
              align: left
              code: salaryJobTitleCode
              fixedLeft: true
            - title: Insured Salary
              display_type: Currency
              align: left
              code: insuranceSalary
              fixedLeft: true
            - title: Currency
              align: left
              code: currencyName
              fixedLeft: true
          layout_option:
            tool_table:
              expand_filter: true
              show_table_checkbox: true
              collapse: true
          showPagination: true
        - type: treeTable
          _condition:
            transform: $.extend.formType = 'edit' or $.extend.isDuplicate
          treeForm:
            form:
              - type: group
                label: Set up detail
                mode: multiple
                collapse: false
                fields:
                  - title: No.
                    align: left
                    width: '5'
                    fixedLeft: true
                    autoIndex: true
                  - title: Insurance Region
                    align: left
                    code: insuranceRegionName
                  - title: Insurance Salary Level
                    align: left
                    code: insuranceSalaryLevelName
                    unvisible: true
                  - title: Insurance Salary Step
                    align: left
                    code: insuranceSalaryStepName
                    fixedLeft: true
                  - title: Salary Code by Job Title
                    display_type: input
                    align: left
                    code: salaryJobTitleCode
                    fixedLeft: true
                  - title: Insured Salary
                    display_type: Currency
                    align: left
                    code: insuranceSalary
                    fixedLeft: true
                  - title: Currency
                    align: left
                    code: currencyName
                    fixedLeft: true
          group:
            keyGroup: insuranceSalaryClassCode
            rowExpandTitle:
              - insuranceSalaryClassName
            keyTitle: insuranceSalaryClassName
          _dataSource:
            transform: $.fields.viewDetails
          rowActions:
            delete: {}
          actions:
            search: {}
            addSetup:
              title: Set Up Detail
              specificData: $.variables._data
              uniqueFields:
                - insuranceSalaryClassCode
                - insuranceSalaryLevelCode
                - insuranceSalaryStepCode
                - insuranceRegionCode
              addValidate:
                type: noDataAdded
                errorMessage: >-
                  The record you want to add already exist. Please check the
                  data
              form:
                - type: group
                  collapse: false
                  n_cols: 2
                  fields:
                    - type: select
                      label: Insurance Salary Class
                      name: insuranceSalaryClass
                      isLazyLoad: true
                      placeholder: Select Insurance Salary Class
                      _select:
                        transform: >-
                          $insuranceSalaryClassList($.extend.limit,
                          $.extend.page, $.extend.search,
                          $.variables._effectiveDate)
                      _class:
                        transform: >-
                          $count($.variables._resultData) > 0 ? 'unrequired':
                          'required'
                    - type: text
                      name: insuranceSalaryClassName
                      unvisible: true
                      _value:
                        transform: $.fields.insuranceSalaryClass.label
                    - type: text
                      name: insuranceSalaryClassCode
                      unvisible: true
                      _value:
                        transform: $.fields.insuranceSalaryClass.value
                    - type: select
                      label: Insurance Region
                      name: insuranceRegion
                      isLazyLoad: true
                      placeholder: Select Insurance Region
                      _select:
                        transform: $insuranceRegionList($.variables._effectiveDate)
                    - type: text
                      name: insuranceRegionName
                      unvisible: true
                      _value:
                        transform: $.fields.insuranceRegion.label
                    - type: text
                      name: insuranceRegionCode
                      unvisible: true
                      _value:
                        transform: $.fields.insuranceRegion.value
                    - type: select
                      label: Insurance Salary Level
                      name: insuranceSalaryLevel
                      isLazyLoad: true
                      placeholder: Select Insurance Salary Level
                      _select:
                        transform: $insuranceSalaryLevelList($.variables._effectiveDate)
                    - type: selectAll
                      label: Insurance Salary Step
                      name: insuranceSalaryStep
                      isLazyLoad: true
                      mode: multiple
                      placeholder: Select Insurance Salary Step
                      options:
                        maxTag: 7
                      _options:
                        transform: >-
                          $insuranceSalaryStepList($.extend.limit,
                          $.extend.page, $.extend.search,
                          $.variables._effectiveDate)
                      _class:
                        transform: ' $count($.variables._resultData) > 0 ?  ''unrequired'': ''required'''
                - type: group
                  collapse: false
                  n_cols: 4
                  fields:
                    - type: select
                      label: Insured Salary Type
                      name: classification
                      isLazyLoad: true
                      placeholder: Select Insured Salary Type
                      _class:
                        transform: >-
                          $count($.variables._resultData) > 0 ? 'unrequired':
                          'required'
                      _value:
                        transform: $classificationList()[1]
                      _select:
                        transform: $classificationList()
                    - type: number
                      label: Initial Value
                      name: initialValue
                      isLazyLoad: true
                      placeholder: Enter Initial Value
                      number:
                        min: 0
                        format: currency
                        max: '999999999999999'
                        precision: 4
                    - type: number
                      label: Step
                      name: step
                      isLazyLoad: true
                      placeholder: Enter Step
                      number:
                        min: 0
                        format: currency
                        max: '999999999999999'
                        precision: 4
                    - type: select
                      label: Currency
                      name: currency
                      dependantField: $.fields.countryCode
                      placeholder: Select Currency
                      _value:
                        transform: >-
                          $.fields.countryCode and
                          $count($currencyList($.fields.countryCode)) = 1 ?
                          $currencyList($.fields.countryCode)[0].value
                      _select:
                        transform: $currencyList($.fields.countryCode)
                      _condition:
                        transform: ' $.fields.classification.value = ''WGCSFT_00001'''
                      validators:
                        - type: required
              sources:
                countryList:
                  uri: '"/api/picklists/COUNTRIES/values/pagination"'
                  method: GET
                  queryTransform: >-
                    {'limit': $.limit,'page': $.page,'search':
                    $.search,'filter': [{'field':'status','operator':
                    '$eq','value':true}, {'field':'effectiveDate','operator':
                    '$lte','value': $.effectiveDate}]}
                  bodyTransform: ''
                  headerTransform: ''
                  resultTransform: >-
                    $map($.data, function($item) {{'label': $item.name.default,
                    'value': $item.code}})
                  disabledCache: true
                  params:
                    - limit
                    - page
                    - search
                    - effectiveDate
                companyList:
                  uri: '"/api/companies"'
                  queryTransform: >-
                    {'limit': $.limit,'page': $.page, 'search': $.search,
                    'filter': [{'field':'status','operator':
                    '$eq','value':true}, {'field':'effectiveDate','operator':
                    '$lte','value': $.effectiveDate}]}
                  method: GET
                  bodyTransform: ''
                  headerTransform: ''
                  resultTransform: >-
                    $map($.data, function($item) {{'label':
                    $item.longName.default & ' (' & $item.code & ')', 'value':
                    $item.code, 'id': $item.id}})[]
                  disabledCache: true
                  params:
                    - limit
                    - page
                    - search
                    - effectiveDate
                legalEntityList:
                  uri: '"/api/legal-entities"'
                  queryTransform: >-
                    {'filter': [{'field':'status','operator':
                    '$eq','value':true},{'field':'companyId','operator':
                    '$eq','value': $.companyId },
                    {'field':'effectiveDate','operator': '$lte','value':
                    $.effectiveDate}]}
                  method: GET
                  bodyTransform: ''
                  headerTransform: ''
                  resultTransform: >-
                    $map($.data, function($item) {{'label':
                    $item.longName.default & ' (' & $item.code & ')', 'value':
                    $item.code, 'id': $item.id}})[]
                  disabledCache: true
                  params:
                    - effectiveDate
                    - companyId
                insuranceSalaryClassList:
                  uri: '"/api/picklists/INSURANCESALARYCLASS/values/pagination"'
                  method: GET
                  queryTransform: >-
                    {'limit': $.limit,'page': $.page,'search':
                    $.search,'filter': [{'field':'status','operator':
                    '$eq','value':true},{'field':'effectiveDate','operator':
                    '$eq','value':$.effectiveDate}]}
                  bodyTransform: ''
                  headerTransform: ''
                  resultTransform: >-
                    $map($map($distinct($map($.data,function($i){$i.code})),function($c){$.data[code=$c][-1]})[],
                    function($item) {{'label': $item.name.default & ' (' &
                    $item.code & ')', 'value': $item.code, 'id': $item.id}})[]
                  disabledCache: true
                  params:
                    - limit
                    - page
                    - search
                    - effectiveDate
                insuranceRegionList:
                  uri: '"/api/picklists/INSREGION/values"'
                  method: GET
                  queryTransform: >-
                    {'filter': [{'field':'status','operator':
                    '$eq','value':true},{'field':'effectiveDate','operator':
                    '$eq','value':$.effectiveDate}]}
                  bodyTransform: ''
                  headerTransform: ''
                  resultTransform: >-
                    $map($.data, function($item) {{'label': $item.name.default,
                    'value': $item.code}})[]
                  disabledCache: true
                  params:
                    - effectiveDate
                insuranceSalaryLevelList:
                  uri: '"/api/picklists/InsLevel/values"'
                  method: GET
                  queryTransform: >-
                    {'filter': [{'field':'status','operator':
                    '$eq','value':true},{'field':'effectiveDate','operator':
                    '$eq','value':$.effectiveDate}]}
                  bodyTransform: ''
                  headerTransform: ''
                  resultTransform: >-
                    $map($.data, function($item) {{'label': $item.name.default,
                    'value': $item.code}})[]
                  disabledCache: true
                  params:
                    - effectiveDate
                insuranceSalaryStepList:
                  uri: '"/api/picklists/INSURANCESALARYSTEP/values/pagination"'
                  method: GET
                  queryTransform: >-
                    {'limit': $.limit,'page': $.page,'search':
                    $.search,'filter': [{'field':'status','operator':
                    '$eq','value':true},{'field':'effectiveDate','operator':
                    '$eq','value':$.effectiveDate}]}
                  bodyTransform: ''
                  headerTransform: ''
                  resultTransform: >-
                    $map($.data, function($item) {{'label': $item.name.default &
                    ' (' & $item.code & ')', 'value': $item.code}})[]
                  disabledCache: true
                  params:
                    - limit
                    - page
                    - search
                    - effectiveDate
                classificationList:
                  uri: '"/api/picklists/wageclassification/values"'
                  method: GET
                  queryTransform: >-
                    {'filter': [{'field':'status','operator':
                    '$eq','value':true}]}
                  bodyTransform: ''
                  headerTransform: ''
                  resultTransform: >-
                    $map($filter($.data,function($v){$v.code !=
                    'WGCSFT_00002'}), function($item) {{'label':
                    $item.name.default, 'value': $item.code}})[]
                  disabledCache: true
                salaryJobTitleList:
                  uri: '"/api/picklists/salaryJobTitleList/values"'
                  method: GET
                  queryTransform: >-
                    {'filter': [{'field':'status','operator':
                    '$eq','value':true}]}
                  bodyTransform: ''
                  headerTransform: ''
                  resultTransform: >-
                    $map($.data, function($item) {{'label': $item.name.default,
                    'value': $item.code}})[]
                  disabledCache: true
                currencyList:
                  uri: '"/api/picklists/currency/values"'
                  method: GET
                  queryTransform: >-
                    {'filter': [{'field':'status','operator':
                    '$eq','value':true},{'field':'linkCatalogDataCode','operator':
                    '$eq','value':$.countryCode.value}]}
                  bodyTransform: ''
                  headerTransform: ''
                  resultTransform: >-
                    $map($.data, function($item) {{'label': $item.name.default,
                    'value': $item.code}})[]
                  disabledCache: true
                  params:
                    - countryCode
              variables:
                _data:
                  transform: >-
                    $map($.fields.insuranceSalaryStep, function($item,$i1) {
                    {'insuranceSalaryClassCode':
                    $.fields.insuranceSalaryClass.value,
                    'insuranceSalaryClassName':
                    $.fields.insuranceSalaryClass.name,'insuranceRegionCode':
                    $.fields.insuranceRegion.value,'insuranceRegionName':
                    $.fields.insuranceRegion.label,'insuranceSalaryLevelCode':
                    $.fields.insuranceSalaryLevel.value,'insuranceSalaryLevelName':
                    $.fields.insuranceSalaryLevel.label,'insuranceSalaryStepCode':
                    $item.value,'insuranceSalaryStepName':
                    $item.label,'classificationCode':
                    $.fields.classification.value,'currencyCode':
                    $.fields.currency.value,'currencyName':
                    $.fields.currency.label,'insuranceSalary':
                    ($.fields.initialValue and $.fields.step ?
                    $round($.fields.initialValue + $.fields.step*$i1,3) :
                    $.fields.initialValue),'salaryJobTitleCode':
                    $.fields.insuranceSalaryClass.value &
                    ($.fields.insuranceRegion ? ('_' &
                    $.fields.insuranceRegion.value)) &
                    ($.fields.insuranceSalaryLevel ? ('_' &
                    $.fields.insuranceSalaryLevel.value)) & '_' & $item.value  }
                    })[]
                _effectiveDate:
                  transform: $.extend.extendFormValues.effectiveDate
          config:
            addSetup: true
            filter: true
          name: details
          columns:
            - title: No.
              align: left
              width: '5'
              fixedLeft: true
              autoIndex: true
            - title: Insurance Region
              align: left
              code: insuranceRegionName
            - title: Insurance Salary Level
              align: left
              code: insuranceSalaryLevelName
              unvisible: true
            - title: Insurance Salary Step
              align: left
              code: insuranceSalaryStepName
              fixedLeft: true
            - title: Salary Code by Job Title
              display_type: input
              align: left
              code: salaryJobTitleCode
              fixedLeft: true
            - title: Insured Salary
              display_type: Currency
              align: left
              code: insuranceSalary
              fixedLeft: true
            - title: Currency
              align: left
              code: currencyName
              fixedLeft: true
          layout_option:
            tool_table:
              expand_filter: true
              show_table_checkbox: true
              collapse: true
          showPagination: true
      _condition:
        transform: $.extend.formType = 'create' or $.extend.formType = 'edit'
    - type: group
      label: Set Up Detail
      disableEventCollapse: true
      fields:
        - type: text
          label: View Details
          name: viewDetails
          unvisible: true
        - type: treeTable
          treeForm:
            form:
              - type: group
                label: Set up detail
                mode: multiple
                collapse: false
                fields:
                  - title: No.
                    align: left
                    width: '5'
                    fixedLeft: true
                    autoIndex: true
                  - title: Insurance Region
                    align: left
                    code: insuranceRegionName
                  - title: Insurance Salary Level
                    align: left
                    code: insuranceSalaryLevelName
                    unvisible: true
                  - title: Insurance Salary Step
                    align: left
                    code: insuranceSalaryStepName
                    fixedLeft: true
                  - title: Salary Code by Job Title
                    display_type: input
                    align: left
                    code: salaryJobTitleCode
                    fixedLeft: true
                  - title: Insured Salary
                    display_type: Currency
                    align: left
                    code: insuranceSalary
                    fixedLeft: true
                  - title: Currency
                    align: left
                    code: currencyName
                    fixedLeft: true
          group:
            keyGroup: insuranceSalaryClassCode
            rowExpandTitle:
              - insuranceSalaryClassName
            keyTitle: insuranceSalaryClassName
          _dataSource:
            transform: $.fields.viewDetails
          actions:
            search: {}
          config:
            filter: true
          name: details
          columns:
            - title: No.
              align: left
              width: '5'
              fixedLeft: true
              autoIndex: true
            - title: Insurance Region
              align: left
              code: insuranceRegionName
            - title: Insurance Salary Level
              align: left
              code: insuranceSalaryLevelName
              unvisible: true
            - title: Insurance Salary Step
              align: left
              code: insuranceSalaryStepName
              fixedLeft: true
            - title: Salary Code by Job Title
              align: left
              code: salaryJobTitleCode
              fixedLeft: true
            - title: Insured Salary
              align: left
              code: insuranceSalary
              fixedLeft: true
            - title: Currency
              align: left
              code: currencyName
              fixedLeft: true
          layout_option:
            tool_table:
              expand_filter: true
              show_table_checkbox: true
              collapse: true
          showPagination: true
      _condition:
        transform: $.extend.formType = 'view'
  footer:
    create: true
    update: true
    createdOn: createdAt
    updatedOn: updatedAt
    createdBy: createdBy
    updatedBy: updatedBy
  sources:
    countryList:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true},
        {'field':'effectiveDate','operator': '$eq','value': $.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
    companyList:
      uri: '"/api/companies"'
      queryTransform: >-
        {'limit': $.limit,'page': $.page, 'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true},
        {'field':'effectiveDate','operator': '$lte','value': $.effectiveDate}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
    legalEntityList:
      uri: '"/api/legal-entities"'
      queryTransform: >-
        {'filter': [{'field':'status','operator':
        '$eq','value':true},{'field':'companyId','operator': '$eq','value':
        $.companyId }, {'field':'effectiveDate','operator': '$lte','value':
        $.effectiveDate}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id}})[]
      disabledCache: true
      params:
        - effectiveDate
        - companyId
  variables:
    _selectedCompany:
      transform: >-
        ($filter($companyList(10000) , function($v, $i, $a) { $v.value =
        $.extend.defaultValue.companyCode.value}))
filter_config:
  fields:
    - type: text
      label: Insurance Salary Plan Code
      name: code
      placeholder: Enter Insurance Salary Plan Code
      labelType: type-grid
    - type: text
      label: Insurance Salary Plan Name
      name: name
      placeholder: Enter Insurance Salary Plan Name
      labelType: type-grid
    - type: text
      label: Version
      name: version
      placeholder: Enter Version
      labelType: type-grid
    - type: selectAll
      label: Country
      name: country
      mode: multiple
      placeholder: Select Country
      labelType: type-grid
      _options:
        transform: $.variables.$countryList()
    - type: selectAll
      label: Company
      name: company
      placeholder: Select Company
      labelType: type-grid
      _options:
        transform: $companyList()
    - type: selectAll
      label: Legal Entity
      name: legalEntity
      mode: multiple
      isLazyLoad: true
      placeholder: Select Legal Entity
      labelType: type-grid
      _options:
        transform: $legalEntityList($.extend.limit, $.extend.page, $.extend.search)
    - type: dateRange
      label: Effective Date
      name: effectiveDate
      labelType: type-grid
      setting:
        format: dd/MM/yyyy
        type: date
    - type: radio
      label: Status
      name: status
      labelType: type-grid
      value: ''
      radio:
        - label: All
          value: ''
        - label: Active
          value: true
        - label: Inactive
          value: false
    - type: textarea
      label: Note
      name: note
      labelType: type-grid
      placeholder: Enter Note
      textarea:
        autoSize:
          minRows: 3
        maxCharCount: 1000
    - type: text
      name: updatedBy
      labelType: type-grid
      label: Last Updated By
      placeholder: Enter Editor
    - type: dateRange
      label: Last Updated On
      name: updatedAt
      labelType: type-grid
      setting:
        format: dd/MM/yyyy
        type: date
  filterMapping:
    - field: code
      operator: $cont
      valueField: code
    - field: shortName
      operator: $cont
      valueField: shortName
    - field: version
      operator: $cont
      valueField: version
    - field: name
      operator: $cont
      valueField: name
    - field: countryCode
      operator: $in
      valueField: country.(value)
    - field: companyCode
      operator: $in
      valueField: company.(value)
    - field: legalEntityCode
      operator: $in
      valueField: legalEntity.(value)
    - field: effectiveDate
      operator: $between
      valueField: effectiveDate
    - field: status
      operator: $eq
      valueField: status
    - field: note
      operator: $cont
      valueField: note
    - field: updatedBy
      operator: $eq
      valueField: updatedBy
    - field: updatedAt
      operator: $between
      valueField: updatedAt
  sources:
    countryList:
      uri: '"/api/picklists/COUNTRIES/values"'
      method: GET
      queryTransform: '{''filter'': [{''field'':''status'',''operator'': ''$eq'',''value'':true}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
    companyList:
      uri: '"/api/companies/by"'
      queryTransform: '{''filter'': [{''field'':''status'',''operator'': ''$eq'',''value'':true}]}'
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id}})[]
      disabledCache: true
    legalEntityList:
      uri: '"/api/legal-entities/get-by"'
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
layout_options:
  show_detail_history: false
  is_popup: true
  is_upload_file: true
  show_dialog_form_save_add_button: true
  support_search_date: true
  toolTable:
    export: true
    import: true
    adjustDisplay: true
  tool_table:
    - id: export
  duplicate_value_transform:
    fields:
      - code
    transform: ''''''
  hide_action_row: true
  delete_multi_items: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: null
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    title: null
    icon: pencil
    type: ghost-gray
  - id: duplicate
    icon: icon-copy-bold
    type: ghost-gray
  - id: delete
    icon: trash
    type: ghost-gray
backend_url: /api/insurance-salary-plans
screen_name: null
layout_options__actions_many: null
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: legalEntityCode
    defaultName: LegalEntityCode
  - name: countryCode
    defaultName: CountryCode
  - name: companyCode
    defaultName: CompanyCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Set up Salary Plan by Job Title
  parent:
    title: INS Setting
