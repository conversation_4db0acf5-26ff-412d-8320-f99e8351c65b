id: INS.FS.FR.011_01
status: draft
sort: 606
user_created: 230a0477-7252-4474-98f3-84e38bc47f2b
date_created: '2024-09-14T08:21:18.586Z'
user_updated: 1029d146-842f-41b9-9cb4-06b6253ce435
date_updated: '2025-07-25T03:02:49.828Z'
title: Manage Salary by Job Title
requirement:
  time: 1748336366976
  blocks:
    - id: wblNrfk1DU
      type: paragraph
      data:
        text: >-
          - Chức năng đáp ứng các yêu cầu thêm mới/điều chỉnh/tra cứu thông tin
          lương chức danh công việc của CBNV. 
    - id: DXEDIValaN
      type: paragraph
      data:
        text: >

          - <PERSON> phép bộ phận Nhân sự quản lý thông tin lương chức danh công việc
          của CBNV làm cơ sở trích nộp bảo hiểm bắt buộc theo quy định của Nhà
          nước.
    - id: M8g6qW6JbO
      type: paragraph
      data:
        text: >-
          - <PERSON><PERSON> thống kiểm tra thông tin và không cho thiết lập mới nếu thông tin
          thiết lập trùng (Employee ID, Employee Record Number, Effective Date)
          đã lưu trước đó.
  version: 2.30.7
screen_design: null
module: INS
local_fields:
  - code: effectiveDateFrom
    title: Effective Date
    data_type:
      key: dd/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/YYYY
      collection: field_types
    show_sort: true
  - code: insuranceRegionName
    title: Insurance Region
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
    extra_config:
      sortByCode: insuranceRegionCode
  - code: salarySetUpDesc
    title: Salary Set Up
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: insuranceSalaryPlanName
    title: Insurance Salary Plan Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: salaryCategoryByJobTitleCode
    title: Salary Code by Job Title
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: classificationName
    title: Insured Salary Type
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: false
  - code: insuranceSalary
    title: Amount
    data_type:
      key: Money Amount
      collection: data_types
    display_type:
      key: Currency
      collection: field_types
    show_sort: false
  - code: currencyCode
    title: Currency
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: nextPromotionDate
    title: Next Promotion Date
    data_type:
      key: dd/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/YYYY
      collection: field_types
    show_sort: true
    options__tabular__column_width: 13
  - code: note
    title: Note
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: updatedBy
    title: Last Update By
    display_type:
      key: Label
      collection: field_types
    data_type:
      key: User
      collection: data_types
    show_sort: true
  - code: updatedAt
    title: Last Update On
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
    data_type:
      key: dd/MM/yyyy
      collection: data_types
    show_sort: true
mock_data: null
local_buttons: null
layout: layout-table
form_config:
  defaultValidateError: Required fields have not been filled in completely (fields marked with *)
  formTitle:
    create: Add New Salary by Job Title
    edit: Edit Salary by Job Title
    view: Details of Salary by Job Title
  formSize:
    create: largex
    edit: largex
    view: large
  fields:
    - type: group
      label: Basic Information
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - type: select
          name: employee
          label: Employee
          placeholder: Select Employee
          validators:
            - type: required
          isLazyLoad: true
          clearFieldsAfterChange:
            - code
            - companyCode
            - currencyCode
          _condition:
            transform: $.extend.formType = 'create'
          _select:
            transform: >-
              $employeesList($.extend.limit, $.extend.page, $.extend.search,
              $.fields.effectiveDateFrom)
          outputValue: value
        - type: text
          label: Employee
          disabled: true
          _value:
            transform: >-
              $.fields.employeeId &' - '& $.fields.employeeGroupCode &' - '&
              $.fields.employeeRecordNumber &' - '& $.fields.fullName
          _condition:
            transform: $.extend.formType = 'edit'
        - type: text
          name: employeeId
          unvisible: true
          _value:
            transform: $.fields.employee.employeeId
        - type: text
          name: employeeGroupCode
          unvisible: true
          label: Employee Group
          _value:
            transform: $.variables._dataEmpSelected.employeeGroupCode
        - type: number
          name: employeeRecordNumber
          label: Employee Record Number
          unvisible: true
          _value:
            transform: $.fields.employee.employeeRecordNumber
        - type: text
          name: fullName
          label: Employee Name
          unvisible: true
          _value:
            transform: $.fields.employee.fullName
        - type: text
          name: companyCode
          label: Company ID
          unvisible: true
          _value:
            transform: $.variables._dataEmpSelected.companyCode
        - type: text
          name: legalEntityCode
          label: legal ID
          unvisible: true
          _value:
            transform: $.variables._dataEmpSelected.legalEntityCode
        - type: text
          name: baseSalary
          label: salaary ID
          unvisible: true
          _value:
            transform: $.variables._dataEmpSelected.baseSalary
        - type: number
          name: nextMonth
          label: nextMonth ID
          unvisible: true
          _value:
            transform: $.variables._selectedsalaryPlan.nextMonth
        - type: text
          name: employeeIdObj
          unvisible: true
          _value:
            transform: $.variables._employeeId
    - type: group
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - type: text
          name: employeeId
          label: Employee ID
        - type: text
          name: employeeRecordNumber
          label: Employee Record Number
        - type: text
          name: fullName
          label: Full Name
        - type: text
          name: companyName
          label: Company
        - type: text
          name: legalEntityName
          label: Legal Entity
        - type: text
          name: jobTitle
          label: Job Title
        - type: text
          name: emplLevelCode
          label: Employee Level
        - type: group
          label: Insurance Salary Information
          hostStyle:
            marginTop: 8px
          fieldGroupTitleStyle:
            fontSize: 16px
          fields:
            - name: effectiveDateFrom
              label: Effective Date
              type: dateRange
              mode: date-picker
              placeholder: dd/mm/yyyy
              setting:
                format: dd/MM/yyyy
                type: date
            - name: salarySetUp
              label: Salary Set Up
              type: radio
              radio:
                - value: 0
                  label: According to Class & Step
                - value: 1
                  label: According to Agreement
              unvisible: true
            - name: salarySetUpDesc
              label: Salary Set Up
              type: text
            - name: insuranceSalaryPlanName
              label: Insurance Salary Plan Name
              type: text
              _condition:
                transform: $.fields.salarySetUp = 0
            - type: text
              name: salaryCategoryByJobTitleCode
              label: Salary Code by Job Title
              _condition:
                transform: $.fields.salarySetUp = 0
            - label: Insurance Salary Class
              type: text
              name: insuranceSalaryClassName
              _condition:
                transform: $.fields.salarySetUp = 0
            - type: text
              label: Insurance Region
              name: insuranceRegionName
              _condition:
                transform: $.fields.salarySetUp = 0
            - type: text
              label: Insurance Salary Level
              name: insuranceSalaryLevelName
              _condition:
                transform: $.fields.salarySetUp = 0
            - type: text
              label: Insurance Salary Step
              name: insuranceSalaryStepName
              _condition:
                transform: $.fields.salarySetUp = 0
            - type: text
              label: Insured Salary Type
              name: classificationName
            - type: number
              label: Amount
              name: insuranceSalary
              number:
                format: currency
                max: '999999999999999'
            - type: text
              label: Currency
              name: currencyCode
            - name: nextPromotionDate
              label: Next Promotion Date
              type: dateRange
              mode: date-picker
              placeholder: dd/mm/yyyy
              setting:
                format: dd/MM/yyyy
                type: date
            - name: note
              label: Note
              type: translationTextArea
    - type: group
      label: Salary Information
      disableEventCollapse: true
      fields:
        - type: group
          n_cols: 2
          fields:
            - name: effectiveDateFrom
              label: Effective Date
              type: dateRange
              mode: date-picker
              placeholder: dd/mm/yyyy
              setting:
                format: dd/MM/yyyy
                type: date
              _disabled:
                transform: $.extend.formType = 'view'
              validators:
                - type: required
              _toast:
                transform: >-
                  $.fields.effectiveDateFrom ? null : {'position':
                  'bottom','type': 'warning','content': 'Deleting this will also
                  effect related data'}
            - name: effectiveDate
              label: Effective Date
              type: dateRange
              mode: date-picker
              placeholder: dd/mm/yyyy
              setting:
                format: dd/MM/yyyy
                type: date
              _value:
                transform: $.fields.effectiveDateFrom
              unvisible: true
            - type: select
              name: insuranceRegionCode
              label: Insurance Region
              placeholder: Select Insurance Region
              outputValue: value
              _select:
                transform: $regionList($.fields.effectiveDateFrom)
              validators:
                - type: required
              clearFieldsAfterChange:
                - salaryCategoryByJobTitleCode_01
        - name: salarySetUp
          label: Salary Set Up
          type: radio
          radio:
            - value: 0
              label: According to Class & Step
            - value: 1
              label: According to Agreement
          value: 0
        - name: insuranceSalaryPlanCode
          label: Insurance Salary Plan Name
          type: select
          placeholder: Select Salary Plan Name
          _select:
            transform: >-
              $salaryPlanNameList($.fields.companyCode,
              $.fields.legalEntityCode, $.fields.effectiveDateFrom ?
              $.fields.effectiveDateFrom : $now())
          _condition:
            transform: $.fields.salarySetUp = 0
          validators:
            - type: required
          outputValue: value
          dependantField: $.fields.salarySetUp, $.fields.effectiveDateFrom
          clearFieldsAfterChange:
            - nextPromotionDate
          _toast:
            transform: >-
              $.fields.insuranceSalaryPlanCode = null ?  {'position':
              'bottom','type': 'warning','content': 'Deleting this will also
              effect related data'} : null
        - type: selectCustom
          name: salaryCategoryByJobTitleCode_01
          label: Salary Code by Job Title
          placeholder: Select Salary Code by Job Title
          _condition:
            transform: $.fields.salarySetUp = 0
          _toast:
            transform: ' $.fields.salaryCategoryByJobTitleCode_01 = null ?  {''position'': ''bottom'',''type'': ''warning'',''content'': ''Deleting this will also effect related data''} : null '
          validators:
            - type: required
          actions:
            - id: search
              title: Search Salary Category By Job Title Code
          _select:
            transform: >-
              $exists($.variables._selectedsalaryPlan.slrPlanId) ?
              $salaryCategoryDetailList($.variables._selectedsalaryPlan.slrPlanId,$.extend.filter.tblinsuranceSalaryClass,
              $exists($.fields.insuranceRegionCode) ?
              $.fields.insuranceRegionCode :
              $.extend.filter.tblinsuranceRegionCode,
              $.extend.filter.tblinsuranceSalaryLevel,
              $.extend.filter.tblinsuranceSalaryStep)[]
          actionsConfig:
            search:
              options:
                okButton:
                  display: true
                cancelButton:
                  display: false
                autoSubmitSelection: false
                searchButton:
                  size: small
                clearSearchButton:
                  size: small
                  type: ghost-color
                showSearchResultTitle: true
              formConfig:
                fields:
                  - type: group
                    collapsed: false
                    key: salaryCategoryByJobTitleCode1
                    disableEventCollapse: true
                    n_cols: 3
                    fields:
                      - type: select
                        label: Insurance Salary Class
                        name: tblinsuranceSalaryClass
                        key: rateinsuranceSalaryClass
                        placeholder: Select Insurance Salary Class
                        outputValue: value
                        _select:
                          transform: $plans()
                      - type: select
                        key: rateInsuranceSalaryLevel
                        label: Insurance Level
                        name: tblinsuranceSalaryLevel
                        placeholder: Select Insurance Salary Level
                        outputValue: value
                        _select:
                          transform: $levels()
                      - type: select
                        label: Insurance Step
                        key: rate
                        name: tblinsuranceSalaryStep
                        placeholder: Select Insurance Salary Step
                        mode: multiple
                        outputValue: value
                        _select:
                          transform: $steps()
                sources:
                  plans:
                    uri: '"/api/picklists/INSURANCESALARYCLASS/values"'
                    method: GET
                    queryTransform: ''
                    bodyTransform: ''
                    headerTransform: ''
                    resultTransform: >-
                      $map($.data, function($item) {{'label': $item.name.default
                      & ' (' & $item.code & ')' , 'value': $item.code}})[]
                    disabledCache: true
                  grades:
                    uri: '"/api/picklists/INSREGION/values"'
                    method: GET
                    queryTransform: ''
                    bodyTransform: ''
                    headerTransform: ''
                    resultTransform: >-
                      $map($.data, function($item) {{'label': $item.name.default
                      & ' (' & $item.code & ')', 'value': $item.code}})[]
                    disabledCache: true
                  levels:
                    uri: '"/api/picklists/InsLevel/values"'
                    method: GET
                    queryTransform: ''
                    bodyTransform: ''
                    headerTransform: ''
                    resultTransform: >-
                      $map($.data, function($item) {{'label': $item.name.default
                      & ' (' & $item.code & ')', 'value': $item.code}})[]
                    disabledCache: true
                  steps:
                    uri: '"/api/picklists/INSURANCESALARYSTEP/values"'
                    method: GET
                    queryTransform: ''
                    bodyTransform: ''
                    headerTransform: ''
                    resultTransform: >-
                      $map($.data, function($item) {{'label': $item.name.default
                      & ' (' & $item.code & ')', 'value': $item.code}})[]
                    disabledCache: true
              table:
                fields:
                  - name: salaryJobTitleCode
                    key: salaryJobTitleCode
                    label: Salary By Job Title Code
                    type: text
                    displayType: Hyperlink
                  - name: insuranceSalaryClassName
                    key: insuranceSalaryClassCode
                    label: Insurance Salary Class
                    displayType: Label
                  - name: insuranceRegionName
                    key: insuranceRegionCode
                    label: Insurance Region
                    displayType: Label
                  - name: insuranceSalaryLevelName
                    key: insuranceSalaryLevelCode
                    label: Insurance Level
                    displayType: Label
                  - name: insuranceSalaryStepNameCode
                    key: insuranceSalaryStepCode
                    label: Insurance Step
                    displayType: Label
                  - name: classificationName
                    key: classificationCode
                    label: Insured Salary Type
                    displayType: Label
                  - name: insuranceSalary
                    key: insuranceSalary
                    label: Amount
                    displayType: Currency
        - name: salaryCategoryByJobTitleCode
          label: Salary Code by Job Title
          outputValue: value
          type: text
          unvisible: true
          dependantField: $.fields.salaryCategoryByJobTitleCode_01, $.fields.salarySetUp
          _value:
            transform: >-
              $type($.fields.salaryCategoryByJobTitleCode_01) = 'object' ?
              $.fields.salaryCategoryByJobTitleCode_01.additionalData.salaryJobTitleCode
              : $.fields.salaryCategoryByJobTitleCode_01
        - type: group
          n_cols: 6
          _condition:
            transform: >-
              $.fields.salarySetUp = 0 and
              ($not($isNilorEmpty($.fields.salaryCategoryByJobTitleCode)) or
              $not($isNilorEmpty($.variables._selectedSalaryCategoryByJobTitle)))
          disabled: true
          fields:
            - label: Insurance Salary Class
              type: text
              placeholder: Select Insurance Salary Class
              name: insuranceSalaryClassName
              outputValue: value
              col: 6
              _value:
                transform: >-
                  $type($.fields.salaryCategoryByJobTitleCode_01) != 'string' ?
                  $isNilorEmpty($.fields.salaryCategoryByJobTitleCode_01.additionalData.insuranceSalaryClassName)
                  ? '_setValueNull' :
                  $.fields.salaryCategoryByJobTitleCode_01.additionalData.insuranceSalaryClassName
            - type: text
              label: Insurance Level
              name: insuranceSalaryLevelName
              placeholder: Select Insurance Salary Level
              outputValue: value
              col: 3
              dependantField: $.fields.salaryCategoryByJobTitleCode_01;$.fields.salarySetUp
              _value:
                transform: >-
                  $type($.fields.salaryCategoryByJobTitleCode_01) = 'string' ?
                  $.variables._selectedSalaryCategoryByJobTitle.additionalData.insuranceSalaryLevelName
                  :
                  $isNilorEmpty($.fields.salaryCategoryByJobTitleCode_01.additionalData.insuranceSalaryLevelName)
                  ? '_setValueNull' :
                  $.fields.salaryCategoryByJobTitleCode_01.additionalData.insuranceSalaryLevelName
            - type: text
              label: Insurance Step
              name: insuranceSalaryStepName
              placeholder: Select Insurance Salary Step
              outputValue: value
              dependantField: $.fields.salaryCategoryByJobTitleCode_01;$.fields.salarySetUp
              dependantFieldSkip: 5
              col: 3
              _value:
                transform: >-
                  $type($.fields.salaryCategoryByJobTitleCode_01) = 'string' ?
                  $.variables._selectedSalaryCategoryByJobTitle.additionalData.insuranceSalaryStepName
                  :
                  $isNilorEmpty($.fields.salaryCategoryByJobTitleCode_01.additionalData.insuranceSalaryStepName)
                  ? '_setValueNull' :
                  $.fields.salaryCategoryByJobTitleCode_01.additionalData.insuranceSalaryStepName
            - type: text
              label: Insured Salary Type
              name: classificationName
              placeholder: Select Insured Salary Type
              outputValue: value
              dependantField: $.fields.salaryCategoryByJobTitleCode_01
              col: 2
              _value:
                transform: >-
                  $type($.fields.salaryCategoryByJobTitleCode_01) = 'string' ?
                  $.variables._selectedSalaryCategoryByJobTitle.additionalData.classificationName
                  :
                  $isNilorEmpty($.fields.salaryCategoryByJobTitleCode_01.additionalData.classificationName)
                  ? '_setValueNull' :
                  $.fields.salaryCategoryByJobTitleCode_01.additionalData.classificationName
            - type: text
              label: Insured Salary Type1
              name: classificationCode
              placeholder: Select Insured Salary Type
              outputValue: value
              dependantField: $.fields.salaryCategoryByJobTitleCode_01
              unvisible: true
              _value:
                transform: >-
                  $type($.fields.salaryCategoryByJobTitleCode_01) = 'string' ?
                  $.variables._selectedSalaryCategoryByJobTitle.additionalData.classificationCode
                  :
                  $isNilorEmpty($.fields.salaryCategoryByJobTitleCode_01.additionalData.classificationCode)
                  ? '_setValueNull' :
                  $.fields.salaryCategoryByJobTitleCode_01.additionalData.classificationCode
            - type: number
              label: Amount
              name: insuranceSalary
              placeholder: Enter Amount
              dependantField: $.fields.salaryCategoryByJobTitleCode_01
              outputValue: value
              col: 2
              _value:
                transform: >-
                  $type($.fields.salaryCategoryByJobTitleCode_01) = 'string' ?
                  $.variables._selectedSalaryCategoryByJobTitle.additionalData.insuranceSalary
                  :
                  $isNilorEmpty($.fields.salaryCategoryByJobTitleCode_01.additionalData.insuranceSalary)
                  ? '' :
                  $.fields.salaryCategoryByJobTitleCode_01.additionalData.insuranceSalary
              number:
                format: currency
                max: '999999999999999'
            - type: text
              label: Currency
              name: currencyName
              placeholder: Select Currency
              col: 2
              _value:
                transform: >-
                  $type($.fields.salaryCategoryByJobTitleCode_01) = 'string' ?
                  $.variables._selectedSalaryCategoryByJobTitle.additionalData.currencyName
                  :
                  $isNilorEmpty($.fields.salaryCategoryByJobTitleCode_01.additionalData.currencyName)
                  ? '_setValueNull' :
                  $.fields.salaryCategoryByJobTitleCode_01.additionalData.currencyName
              dependantField: $.fields.salaryCategoryByJobTitleCode_01
              _condition:
                transform: $.fields.classificationCode = 'WGCSFT_00001'
            - type: text
              label: Currency
              name: currencyCode
              placeholder: Select Currency
              outputValue: value
              dependantField: $.fields.salaryCategoryByJobTitleCode_01
              _value:
                transform: >-
                  $type($.fields.salaryCategoryByJobTitleCode_01) = 'string' ?
                  $.variables._selectedSalaryCategoryByJobTitle.additionalData.currencyCode
                  :
                  $isNilorEmpty($.fields.salaryCategoryByJobTitleCode_01.additionalData.currencyCode)
                  ? '_setValueNull' :
                  $.fields.salaryCategoryByJobTitleCode_01.additionalData.currencyCode
              unvisible: true
        - type: group
          n_cols: 3
          _condition:
            transform: $.fields.salarySetUp = 1
          formValueAfterReset: {}
          dependantField: $.fields.salarySetUp
          dependantFieldSkip: 2
          fields:
            - type: select
              label: Insured Salary Type
              name: classificationCode
              placeholder: Select Insured Salary Type
              outputValue: value
              _select:
                transform: $classificationList()
              dependantField: $.fields.salarySetUp, $.fields.effectiveDateFrom
              validators:
                - type: required
              _defaultValue:
                transform: ' $exists($.fields.classificationCode) ? $.fields.classificationCode : ''WGCSFT_00001'''
              clearFieldsAfterChange:
                - currencyCode
            - type: number
              label: Amount
              name: insuranceSalary
              placeholder: Enter Amount
              outputValue: value
              validators:
                - type: required
              defaultValue: ''
              number:
                format: currency
                max: '999999999999999'
            - type: select
              label: Currency
              name: currencyCode
              placeholder: Select Currency
              outputValue: value
              _select:
                transform: $currencyList()[]
              _condition:
                transform: $.fields.classificationCode = 'WGCSFT_00001'
              validators:
                - type: required
              _value:
                transform: $.fields.defaultCurrencyCode
            - type: text
              name: defaultCurrencyCode
              unvisible: true
              _value:
                transform: >-
                  $exists($.fields.employee) and $.fields.classificationCode =
                  'WGCSFT_00001' ?
                  $currencyList($.variables._dataEmpSelected.countryCode)[0].value
                  : '_setValueNull'
        - name: nextPromotionDate
          label: Next Promotion Date
          type: dateRange
          mode: date-picker
          placeholder: dd/mm/yyyy
          setting:
            format: dd/MM/yyyy
            type: date
          dependantField: $.fields.insuranceSalaryPlanCode, $.fields.salarySetUp
          _value:
            transform: >-
              $.fields.nextPromotionDate ? $.fields.nextPromotionDate :
              $.variables._datanextpromotiondate_date.nextMonthres
        - name: note
          label: Note
          type: translationTextArea
          placeholder: Enter Note
          textarea:
            autoSize:
              minRows: 3
            maxCharCount: 1000
          validators:
            - type: maxLength
              args: '1000'
              text: Note should not exceed 1000 characters
        - name: status
          label: Status
          type: radio
          _value:
            transform: $.extend.formType = 'create' ? true
          unvisible: true
          radio:
            - label: Active
              value: true
            - label: Inactive
              value: false
          validators:
            - type: required
        - name: totalSalaryAndAllowance
          label: Total Salary And Allowance
          type: number
          unvisible: true
          _value:
            transform: >-
              $.fields.classificationCode = 'WGCSFT_00001' ?
              $.variables._total_allow_1 : $.variables._total_allow_2
          number:
            precision: 0
            _suffix:
              transform: $.fields.currencyCode
            format: currency
        - name: totalSalaryAndAllowance
          label: Total Salary And Allowance
          type: number
          placeholder: ''
          unvisible: true
          dependantField: $.fields.insuranceSalary
          _value:
            transform: >-
              $.fields.classificationCode = 'WGCSFT_00001' ?
              $.variables._total_allow_3 : $.variables._total_allow_4
          number:
            precision: 0
            _suffix:
              transform: $.fields.currencyCode
            format: currency
      _condition:
        transform: $not($.extend.formType = 'view')
  _mode:
    transform: >-
      $.extend.formType != 'view' ? {'name': 'mark-scroll',
      'showCollapseSection': true}
  overview:
    dependentField: employeeIdObj
    title: Employee Detail
    noDataMessages: Select employee information to display data
    uri: >-
      /api/employee-insurance-regions/employee?filter%5B0%5D=EmployeeId%7C%7C%24eq%7C%7C:{employeeId}:&filter%5B1%5D=EmployeeRecordNumber%7C%7C%24eq%7C%7C:{employeeRecordNumber}:&filter%5B2%5D=effectiveDate%7C%7C%24eq%7C%7C:{effectiveDate}:
    collapsed: false
    display:
      - key: companyName
        label: Company
      - key: legalEntityName
        label: Legal Entity
      - key: jobIndicatorName
        label: Job Indicator
      - key: employeeGroupName
        label: Employee Group
      - key: actionName
        label: Action
      - key: jobTitle
        label: Job Title
      - key: employeeLevelName
        label: Emp Level
      - key: locationName
        label: Location
      - key: provinceCity
        label: Province/City
  footer:
    create: true
    update: true
    createdOn: createTime
    updatedOn: lastEditTime
    createdBy: creator
    updatedBy: lastEditor
  sources:
    salaryPlanNameList:
      uri: '"/api/insurance-salary-plans/info"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'companyCode','operator':
        '$eq','value':$.companyCode},{'field':'legalEntityCode','operator':
        '$eq','value':$.legalEntityCode}, {'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}]}
      resultTransform: >-
        $map($, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code, 'slrPlanId': $item.id,
        'nextMonth': $item.monthNextStepIncrement}})[]
      params:
        - companyCode
        - legalEntityCode
        - effectiveDate
      disabledCache: true
    salaryCategoryDetailList:
      uri: '"/api/insurance-salary-plans/" & $.id &  "/details/"'
      method: GET
      queryTransform: >-
        {'filter':[{'field': 'insuranceSalaryClassCode', 'operator': '$eq',
        'value': $.insuranceSalaryClassCode}, {'field':
        'insuranceSalaryLevelCode', 'operator': '$eq', 'value':
        $.insuranceSalaryLevelCode}, {'field': 'insuranceSalaryStepCode',
        'operator': '$in', 'value': $.insuranceSalaryStepCode},
        {'field':'insuranceRegionCode','operator': '$eq','value':
        $.insuranceRegionCode}] }
      resultTransform: >-
        $test($count($) = 0 ? [] : $map($, function($item) {{'label':
        $item.salaryJobTitleCode &' (' &
        $formatNumber($number($item.insuranceSalary), '#,##0') & ')', 'value':
        $item.salaryJobTitleCode, 'additionalData':
        $merge([$item,{'insuranceSalaryStepNameCode':
        $item.insuranceSalaryStepName &' (' & $item.insuranceSalaryStepCode &
        ')' }])}})[])
      disabledCache: true
      params:
        - id
        - insuranceSalaryClassCode
        - insuranceRegionCode
        - insuranceSalaryLevelCode
        - insuranceSalaryStepCode
    classificationList:
      uri: '"/api/picklists/wageclassification/values"'
      method: GET
      queryTransform: '{''filter'': [{''field'':''status'',''operator'': ''$eq'',''value'':true}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($filter($.data,function($v){$v.code != 'WGCSFT_00002'}),
        function($item) {{'label': $item.name.default, 'value': $item.code}})[]
      disabledCache: true
    currencyList:
      uri: '"/api/picklists/currency/values"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'status','operator':
        '$eq','value':true},{'field':'linkCatalogDataCode','operator':
        '$eq','value':$.countryCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code, 'additionalData': $item ,'countryCode':
        $item.countryCode}})[]
      disabledCache: true
      params:
        - countryCode
    employeesList:
      uri: '"/api/personals/"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page, 'search': $.search, 'filter':
        [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDateFrom}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label':
        $join($filter([$item.employeeId,$item.employeeGroup,$string($item.employeeRecordNumber),$item.fullName],
        $boolean), ' - ')  , 'value': {'employeeId': $item.employeeId,
        'employeeRecordNumber': $string($item.employeeRecordNumber),
        'jobDataId': $item.jobDataId}, 'employeeId': $item.employeeId,
        'employeeRecordNumber': $string($item.employeeRecordNumber),'jobDataId':
        $item.jobDataId}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDateFrom
    allowancies:
      uri: '"/api/fixed-allowances"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'status','operator':
        '$eq','value':true},{'field':'insuranceAllowance','operator':
        '$eq','value':true}], 'limit': 1000}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.code & ' -' &
        $item.longName.default , 'value': $item.code, 'currency': $item.currency
        , 'currencyName': $item.currencyName}})[]
      disabledCache: true
    dataEmployee:
      uri: '"/api/employee-insurance-regions/employee"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'EmployeeId','operator':
        '$eq','value':$.employeeId},{'field':'EmployeeRecordNumber','operator':
        '$eq','value':$.employeeRecordNumber},
        {'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDateFrom}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $
      disabledCache: true
      params:
        - employeeId
        - employeeRecordNumber
        - effectiveDateFrom
    nextMonthData:
      uri: '"/api/employee-ins-salary-job/cal-month-next-step"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'effectiveDateFrom','operator':
        '$eq','value':$.effectiveDateFrom},{'field':'nextMonth','operator':
        '$eq','value':$.nextMonth}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $
      disabledCache: true
      params:
        - effectiveDateFrom
        - nextMonth
    regionList:
      uri: '"/api/picklists/InsRegion/values"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})
      disabledCache: true
      params:
        - effectiveDate
  variables:
    _salaryPlan:
      transform: >-
        $salaryPlanNameList($.fields.companyCode, $.fields.legalEntityCode,
        $.fields.effectiveDateFrom ? $.fields.effectiveDateFrom : $now())
    _selectedsalaryPlan:
      transform: >-
        ($.fields.insuranceSalaryPlanCode; $filter($.variables._salaryPlan ,
        function($v, $i, $a) { $v.value = $.fields.insuranceSalaryPlanCode }))
    _allowancies:
      transform: $allowancies()
    _totalamount:
      transform: >-
        ($.fields.allowances; $sum($map($.fields.allowances[], function($item)
        {$number($item.amount)})[]))
    _total_allow_1:
      transform: >-
        ($.fields.insuranceSalary ? $number($.fields.insuranceSalary) : 0) +
        ($.variables._totalamount ? $.variables._totalamount : 0)
    _total_allow_2:
      transform: >-
        ($.fields.insuranceSalary ? $number($.fields.insuranceSalary) : 0)  *
        $.variables._dataEmpSelected.baseSalary + ($.variables._totalamount ?
        $.variables._totalamount : 0)
    _total_allow_3:
      transform: >-
        ($.fields.insuranceSalary ? $number($.fields.insuranceSalary) : 0)  +
        ($.variables._totalamount ? $.variables._totalamount : 0)
    _total_allow_4:
      transform: >-
        ($.fields.insuranceSalary ? $number($.fields.insuranceSalary) : 0)  *
        $.variables._dataEmpSelected.baseSalary + ($.variables._totalamount ?
        $.variables._totalamount : 0)
    _dataEmpSelected:
      transform: >-
        $dataEmployee($.fields.employeeId,$.fields.employeeRecordNumber,$exists($.fields.effectiveDateFrom)
        ? $.fields.effectiveDateFrom : $now())
    _datanextpromotiondate_date:
      transform: $nextMonthData($.fields.effectiveDateFrom,$.fields.nextMonth)
    _employeeId:
      transform: >-
        $.fields.employeeId != '' ? {'employeeId': $.fields.employeeId,
        'employeeRecordNumber': $.fields.employeeRecordNumber, 'effectiveDate':
        $exists($.fields.effectiveDateFrom) ? $.fields.effectiveDateFrom :
        $now()}
    _selectedSalarySetUp:
      transform: $.fields.salarySetUp
    _selectedClassificationCode:
      transform: >-
        $.variables._selectedSalarySetUp = 0 ? '_setSelectValueNull' :
        $.fields.classificationCode
    _selectedSalaryCategoryByJobTitle:
      transform: >-
        $exists($.variables._selectedsalaryPlan.slrPlanId) ?
        $filter($salaryCategoryDetailList($.variables._selectedsalaryPlan.slrPlanId,$.extend.filter.tblinsuranceSalaryClass,
        $.extend.filter.tblinsuranceRegionCode,
        $.extend.filter.tblinsuranceSalaryLevel,
        $.extend.filter.tblinsuranceSalaryStep) , function($v) { $v.value =
        $.fields.salaryCategoryByJobTitleCode })
    _insuranceSalaryPlanCodeList:
      transform: >-
        $exists($.variables._selectedsalaryPlan.slrPlanId) ?
        $salaryCategoryDetailList($.variables._selectedsalaryPlan.slrPlanId,$.extend.filter.tblinsuranceSalaryClass,
        $.extend.filter.tblinsuranceRegionCode,
        $.extend.filter.tblinsuranceSalaryLevel,
        $.extend.filter.tblinsuranceSalaryStep)
filter_config:
  fields:
    - type: selectAll
      name: employeeId
      label: Employee ID
      placeholder: Select Employee
      isLazyLoad: true
      clearFieldsAfterChange:
        - code
        - companyCode
      _options:
        transform: $distinct($employeesList(50, $.extend.page, $.extend.search))[]
      outputValue: value
      labelType: type-grid
    - type: group
      label: Employee Record Number
      n_cols: 2
      name: employeeRecordNumber
      fields:
        - name: from
          type: number
          placeholder: From
        - name: to
          type: number
          placeholder: To
      labelType: type-row
    - type: text
      name: fullName
      label: Full Name
      unvisible: true
      placeholder: Enter Full Name
      _value:
        transform: $.variables._dataEmpSelected.companyCode
      labelType: type-grid
    - type: selectAll
      key: rateinsuranceRegionCode
      label: Insurance Region
      name: tblinsuranceRegionCode
      placeholder: Select Insurance Region
      mode: multiple
      outputValue: value
      _options:
        transform: $insuranceRegionList()
      labelType: type-grid
    - type: selectAll
      label: Salary Set Up
      mode: multiple
      name: salarySetUp
      placeholder: Select Salary Set Up
      labelType: type-grid
      options:
        - label: According to Class & Step
          value: 0
        - label: According to Agreement
          value: 1
    - name: insuranceSalaryPlanCode
      label: Insurance Salary Plan Name
      type: select
      placeholder: Select Salary Plan Name
      _select:
        transform: >-
          $salaryPlanNameList($.fields.effectiveDateFrom ?
          $.fields.effectiveDateFrom : $now())
      labelType: type-grid
      clearFieldsAfterChange:
        - salaryCategoryByJobTitleCode
    - type: selectAll
      name: salaryCategoryByJobTitleCode
      label: Salary Code by Job Title
      placeholder: Select Salary Code by Job Title
      _options:
        transform: $.variables._salaryCategoryDetailList
      disabledCache: true
      outputValue: value
      mode: multiple
      labelType: type-grid
    - type: dateRange
      name: effectiveDateFrom
      label: Effective Date
      labelType: type-grid
      setting:
        format: dd/MM/yyyy
        type: date
    - label: Next Promotion Date
      name: nextPromotionDate
      type: dateRange
      labelType: type-grid
    - type: radio
      label: Insured Salary Type
      name: classificationCode
      placeholder: Select Insured Salary Type
      outputValue: value
      _radio:
        transform: $classificationList()
      labelType: type-grid
    - type: selectAll
      label: Currency
      name: currency
      mode: multiple
      labelType: type-grid
      placeholder: Select Currency
      _options:
        transform: $currencyList()
    - type: text
      name: updatedBy
      labelType: type-grid
      label: Last Updated By
      placeholder: Enter Editor
    - type: dateRange
      name: updatedAt
      labelType: type-grid
      label: Last Updated On
      setting:
        format: dd/MM/yyyy
        type: date
  filterMapping:
    - field: employeeId
      operator: $eq
      valueField: employeeId
    - field: employeeRecordNumber
      operator: $gte
      valueField: employeeRecordNumber.from
    - field: employeeRecordNumber
      operator: $lte
      valueField: employeeRecordNumber.to
    - field: companyCode
      operator: $eq
      valueField: companyCode
    - field: insuranceRegionCode
      operator: $in
      valueField: tblinsuranceRegionCode
    - field: salarySetUp
      operator: $in
      valueField: salarySetUp.(value)
    - field: insuranceSalaryPlanCode
      operator: $eq
      valueField: insuranceSalaryPlanCode.(value)
    - field: salaryCategoryByJobTitleCode
      operator: $eq
      valueField: salaryCategoryByJobTitleCode
    - field: currencyCode
      operator: $in
      valueField: currency.(value)
    - field: insuranceSalary
      operator: $eq
      valueField: insuranceSalary
    - field: totalSalaryAndAllowance
      operator: $eq
      valueField: totalSalaryAndAllowance
    - field: effectiveDateFrom
      operator: $between
      valueField: effectiveDateFrom
    - field: nextPromotionDate
      operator: $between
      valueField: nextPromotionDate
    - field: classificationCode
      operator: $eq
      valueField: classificationCode
    - field: updatedBy
      operator: $eq
      valueField: updatedBy
    - field: updatedAt
      operator: $between
      valueField: updatedAt
    - field: fullName
      operator: $cont
      valueField: fullName
  sources:
    insuranceRegionList:
      uri: '"/api/picklists/INSREGION/values"'
      method: GET
      queryTransform: '{''filter'': [{''field'':''status'',''operator'': ''$eq'',''value'':true}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
    classificationList:
      uri: '"/api/picklists/wageclassification/values"'
      method: GET
      queryTransform: '{''filter'': [{''field'':''status'',''operator'': ''$eq'',''value'':true}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($filter($.data,function($v){$v.code != 'WGCSFT_00002'}),
        function($item) {{'label': $item.name.default, 'value': $item.code}})[]
      disabledCache: true
    salaryPlanNameList:
      uri: '"/api/insurance-salary-plans/info"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}]}
      resultTransform: >-
        $map($, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code, 'slrPlanId': $item.id,
        'nextMonth': $item.monthNextStepIncrement}})[]
      params:
        - effectiveDate
    salaryCategoryDetailList:
      uri: '"/api/insurance-salary-plans/" & $.id &  "/details/"'
      method: GET
      queryTransform: ''
      disabledCache: true
      resultTransform: >-
        $map($, function($item) {{'label': $item.salaryJobTitleCode, 'value':
        $item.salaryJobTitleCode}})[]
      params:
        - id
    employeesList:
      uri: '"/api/personals/"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page, 'search': $.search, 'filter':
        [{'field':'employeeId','operator':
        '$eq','value':$.empId},{'field':'employeeGroupCode','operator':
        '$cont','value':$.grpCode},{'field':'employeeRecordNumber','operator':
        '$eq','value':$.ern}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label':
        $join($filter([$item.employeeId,$item.fullName], $boolean), ' - ')  ,
        'value': $item.employeeId}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - empId
        - grpCode
        - ern
    currencyList:
      uri: '"/api/picklists/currency/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})
      disabledCache: true
  variables:
    _salaryCategoryDetailList:
      transform: >-
        $exists($.variables._selectedPlanId) ?
        $salaryCategoryDetailList($.variables._selectedPlanId)
    _selectedPlanId:
      transform: $.fields.insuranceSalaryPlanCode.slrPlanId
layout_options:
  tool_table:
    - id: import
      href: /GE/HR.FS.FR.092
      paramsRedirect:
        type: INS_OBJECT
        entityOrObj: EmployeeInsuranceSalaryJob
    - id: export
  show_detail_history: false
  show_dialog_form_save_add_button: true
  show_dialog_duplicate_button: false
  nested_row_type: group
  transform_group_row_of_nested_row: >-
    $.employeeId & ' - ' & $.employeeGroupCode & ' - ' & $.employeeRecordNumber
    & ' - ' & $.fullName
  support_search_date: true
  row_type: expand
  row_data_combine:
    - employeeId
    - employeeGroupCode
    - employeeRecordNumber
    - fullName
  hide_action_row: true
  delete_multi_items: true
layout_options__header_buttons:
  - id: create
    title: Create
    type: primary
    icon: plus
options: null
create_form: null
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    title: Edit
    icon: icon-pencil-simple-bold
    type: ghost-gray
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: ghost-gray
backend_url: /api/employee-ins-salary-job
screen_name: null
layout_options__actions_many: null
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: employeeId
    defaultName: EmployeeId
  - name: employeeRecordNumber
    defaultName: EmployeeRecordNumber
  - name: companyCode
    defaultName: CompanyCode
  - name: legalEntityCode
    defaultName: LegalEntityCode
  - name: emplLevelCode
    defaultName: EmployeeLevelCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Manage Salary by Job Title
  parent:
    title: Manage Data Insurance
