id: INS.FS.FR.013_01
status: draft
sort: 577
user_created: 27c6db8f-5244-4827-bfd9-df7fa8d185f0
date_created: '2024-09-15T11:34:28.223Z'
user_updated: 1029d146-842f-41b9-9cb4-06b6253ce435
date_updated: '2025-07-24T03:37:13.452Z'
title: Manage Compulsory Insurance for Employees
requirement:
  time: 1747990874072
  blocks:
    - id: hzCMF7S3Je
      type: paragraph
      data:
        text: Mandatory Insurance Participation Information Of Employees
  version: 2.30.7
screen_design: null
module: INS
local_fields:
  - code: employeeId
    title: Employee ID
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    pinned: true
    show_sort: true
  - code: employeeRecordNumber
    title: Employee Record Number
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    pinned: true
    show_sort: true
  - code: employeeName
    title: Full Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
    pinned: true
  - code: contractTypeName
    title: Contract Type
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: localExpatName
    title: Local/Expat
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: insuranceTypeName
    title: Insurance Type
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tags
      collection: field_types
    show_sort: false
    options__tabular__column_width: 12
  - code: insuranceGroupName
    title: Insurance Type Detail by Employer
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tags
      collection: field_types
  - code: effectiveDate
    title: Effective Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    show_sort: true
  - code: status
    title: Status
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: Boolean Tag
      collection: field_types
    show_sort: true
  - code: note
    title: Note
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: updatedBy
    title: Last Updated By
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
    options__tabular__column_width: 11
  - code: updatedAt
    title: Last Updated On
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
    show_sort: true
    options__tabular__column_width: 11
mock_data: []
local_buttons: null
layout: layout-table
form_config:
  effectiveDateValidations:
    - sameDate
    - greaterThanLastest
  defaultValidateError: Required fields have not been filled in completely (fields marked with *)
  formTitle:
    create: Add New Compulsory Insurance for Employees
    edit: Edit Compulsory Insurance for Employees
    view: Details of Compulsory Insurance for Employees
    proceed: Add new Compulsory Insurance for Employees
  historyHeaderTitle: '''Details of Compulsory Insurance for Employees'''
  formSize:
    create: large
    edit: large
    proceed: large
    history: large
  fields:
    - type: group
      _condition:
        transform: $.extend.formType != 'view'
      fields:
        - type: group
          fields:
            - type: select
              name: employee
              label: Employee
              clearFieldsAfterChange:
                - code
                - companyCode
              placeholder: Select Employee
              isLazyLoad: true
              validators:
                - type: required
              _select:
                transform: >-
                  $employeesList($.extend.limit, $.extend.page, $.extend.search,
                  $.fields.effectiveDate)
              _value:
                transform: $.variables._employee[0].value
              _condition:
                transform: $.extend.formType = 'create'
              outputValue: value
            - type: text
              label: Employee
              disabled: true
              _value:
                transform: >-
                  $.fields.employeeId &' - '& $.fields.employeeGroupCode &' - '&
                  $.fields.employeeRecordNumber &' - '& $.fields.employeeName
              _condition:
                transform: $.extend.formType = 'edit' or $.extend.formType = 'proceed'
            - type: text
              name: employeeId
              unvisible: true
              _value:
                transform: $.fields.employee.employeeId
            - type: text
              name: employeeGroupCode
              unvisible: true
              _value:
                transform: $.variables._dataEmpSelected.employeeGroupCode
            - type: number
              name: employeeRecordNumber
              unvisible: true
              _value:
                transform: $.fields.employee.employeeRecordNumber
            - type: text
              name: employeeName
              unvisible: true
            - type: text
              name: employeeIdObj
              unvisible: true
              _value:
                transform: $.variables._employeeId
            - type: dateRange
              label: Effective Date
              name: effectiveDate
              mode: date-picker
              _value:
                transform: $.extend.formType = 'create' ?  $now()
              setting:
                format: dd/MM/yyyy
                type: date
              validators:
                - type: required
        - name: insuranceTypeCode
          label: Insurance Type
          type: selectAll
          outputValue: value
          placeholder: Select Insurance Type
          _options:
            transform: $insTypeList()
          validators:
            - type: required
          clearFieldsAfterChange:
            - insuranceGroupCode
        - name: insuranceGroupCode
          type: groupCheckbox
          label: Insurance Type Detail by Employer
          _checkboxGroup:
            transform: $insuranceDetailList()
          customWrapperStyle: column
          validators:
            - type: required
          _condition:
            transform: $boolean($.variables._insTypeCodeContainBHXH)
          _value:
            transform: >-
              $not($.variables._insTypeCodeContainBHXH) ? [] :
              $count($.extend.defaultValue.insuranceGroupCode) > 0 ?
              $.extend.defaultValue.insuranceGroupCode :
              $map($insuranceDetailList(), function($item) {$item.value})[]
        - type: text
          name: insTypes
          unvisible: true
          _value:
            transform: $.variables._insTypes
    - type: group
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - name: employeeId
          label: Employee ID
          type: text
        - name: employeeRecordNumber
          label: Employee Record Number
          type: text
          _value:
            transform: $string($.fields.employeeRecordNumber)
        - name: employeeName
          label: Employee Name
          type: text
        - name: contractTypeName
          label: Contract Type
          type: text
        - name: localExpatName
          label: Local/Expat
          type: text
        - name: insuranceTypeName
          label: Insurance Type
          type: text
          displaySetting:
            type: Tags
            extraConfig:
              resize: false
        - name: insuranceGroupName
          label: Insurance Type Detail by Employer
          type: text
          displaySetting:
            type: Tags
            extraConfig:
              resize: false
    - type: dateRange
      label: Effective Date
      name: effectiveDate
      mode: date-picker
      setting:
        format: dd/MM/yyyy
        type: date
      _condition:
        transform: $.extend.formType = 'view'
    - name: status
      label: Status
      type: radio
      value: true
      radio:
        - label: Active
          value: true
        - label: Inactive
          value: false
    - name: note
      label: Note
      type: translationTextArea
      textarea:
        autoSize:
          minRows: 3
        maxCharCount: 1000
      placeholder: Enter Note
  overview:
    dependentField: employeeIdObj
    title: Employee Detail
    noDataMessages: Select employee information to display data
    uri: >-
      /api/employee-insurance-regions/employee?filter%5B0%5D=EmployeeId%7C%7C%24eq%7C%7C:{employeeId}:&filter%5B1%5D=EmployeeRecordNumber%7C%7C%24eq%7C%7C:{employeeRecordNumber}:&filter%5B2%5D=effectiveDate%7C%7C%24eq%7C%7C:{effectiveDate}:
    border: true
    display:
      - key: companyName
        label: Company
      - key: legalEntityName
        label: Legal Entity
      - key: jobIndicatorName
        label: Job Indicator
      - key: employeeGroupName
        label: Employee Group
      - key: actionName
        label: Action
      - key: jobTitle
        label: Job
      - key: employeeLevelName
        label: Emp Level
      - key: contractTypeName
        label: Contract Type
      - key: localExpatName
        label: Local/Expat
  footer:
    create: true
    update: true
    createdOn: createdAt
    updatedOn: updatedAt
    createdBy: createdBy
    updatedBy: updatedBy
  sources:
    employeesList:
      uri: '"/api/personals/"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page, 'search': $.search,'filter':
        [{'field':'effectiveDate','operator': '$eq','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label':
        $join($filter([$item.employeeId,$item.employeeGroup,$string($item.employeeRecordNumber),$item.fullName],
        $boolean), ' - ')  , 'value': {'employeeId': $item.employeeId,
        'employeeRecordNumber': $string($item.employeeRecordNumber),
        'jobDataId': $item.jobDataId}, 'employeeId': $item.employeeId,
        'employeeRecordNumber': $string($item.employeeRecordNumber),'jobDataId':
        $item.jobDataId}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
    insTypeList:
      uri: '"/api/picklists/INSURANCETYPE/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})
      disabledCache: true
    insuranceDetailList:
      uri: '"/api/picklists/INSURANCETYPEDETAIL/values"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'linkCatalogDataCode','operator':
        '$eq','value':'INSTYPE_BHXH'}, {'field':'status','operator':
        '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
    dataEmployee:
      uri: '"/api/employee-insurance-regions/employee"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'EmployeeId','operator':
        '$eq','value':$.employeeId},{'field':'EmployeeRecordNumber','operator':
        '$eq','value':$.employeeRecordNumber},
        {'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDateFrom}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $
      disabledCache: true
      params:
        - employeeId
        - employeeRecordNumber
        - effectiveDateFrom
  variables:
    _employeeId:
      transform: >-
        $.fields.employeeId != '' ? {'employeeId': $.fields.employeeId,
        'employeeRecordNumber': $.fields.employeeRecordNumber, 'effectiveDate':
        $.fields.effectiveDate}
    _insTypeList:
      transform: $insTypeList()
    _dataEmpSelected:
      transform: >-
        $dataEmployee($.fields.employeeId,$.fields.employeeRecordNumber,$exists($.fields.effectiveDateFrom)
        ? $.fields.effectiveDateFrom : $now())
    _insTypeCodeContainBHXH:
      transform: '''INSTYPE_BHXH'' in $.fields.insuranceTypeCode'
    _insuranceTypeCodes:
      transform: >-
        $map($filter($.fields.insuranceTypeCode, function($type) { $type !=
        'INSTYPE_BHXH' }), function($type) { { 'insuranceTypeCode': $type } })[]
    _countInsGroupCodes:
      transform: $isNilorEmpty($.fields.insuranceGroupCode)
    _insuranceGroupCodes:
      transform: >-
        $not('INSTYPE_BHXH' in $.fields.insuranceTypeCode) ? [] :
        $map($.fields.insuranceGroupCode, function($group)
        {{'insuranceTypeCode': 'INSTYPE_BHXH','insuranceGroupCode': $group}})[]
    _insTypes:
      transform: >-
        $append($.variables._insuranceTypeCodes,
        $.variables._insuranceGroupCodes)
    _testInsuranceGroupCode:
      transform: >-
        $count($.extend.defaultValue.insuranceGroupCode) > 0 ?
        $.extend.defaultValue.insuranceGroupCode :  $map($insuranceDetailList(),
        function($item) {$item.value})[]
filter_config:
  fields:
    - type: selectAll
      name: employee
      label: Employee ID
      labelType: type-grid
      placeholder: Select Employee
      isLazyLoad: true
      mode: multiple
      _options:
        transform: $employeesList(20, $.extend.page, $.extend.search)
    - type: group
      label: Employee Record Number
      labelType: type-row
      n_cols: 2
      name: employeeRecordNumber
      fields:
        - name: from
          type: number
          placeholder: From
        - name: to
          type: number
          placeholder: To
    - type: text
      name: employeeName
      label: Full Name
      placeholder: Enter Full Name
      labelType: type-grid
    - type: selectAll
      name: contractTypeCode
      label: Contract Type
      labelType: type-grid
      placeholder: Select Contract Type
      mode: multiple
      _options:
        transform: $.variables._contractTypeList
    - type: selectAll
      name: localExpatCode
      label: Local/Expat
      labelType: type-grid
      placeholder: Select Local/Expat
      mode: multiple
      _options:
        transform: $.variables._localExpatList
    - type: selectAll
      name: insuranceType
      label: Insurance Type
      labelType: type-grid
      placeholder: Select Insurance Type
      mode: multiple
      _options:
        transform: $.variables._insTypeList
    - type: dateRange
      label: Effective Date
      labelType: type-grid
      name: effectiveDate
      setting:
        format: dd/MM/yyyy
        type: date
    - name: status
      label: Status
      labelType: type-grid
      type: radio
      value: ''
      radio:
        - label: All
          value: ''
        - label: Active
          value: true
        - label: Inactive
          value: false
    - type: textarea
      label: Note
      name: note
      labelType: type-grid
      placeholder: Enter Note
      textarea:
        autoSize:
          minRows: 3
        maxcharCount: 1000
    - type: text
      name: updatedBy
      labelType: type-grid
      label: Last Updated By
      placeholder: Enter Editor
    - type: dateRange
      label: Last Updated On
      labelType: type-grid
      name: updatedAt
  filterMapping:
    - field: employeeId
      operator: $in
      valueField: employee.(value)
    - field: employeeRecordNumber
      operator: $gte
      valueField: employeeRecordNumber.from
    - field: employeeRecordNumber
      operator: $lte
      valueField: employeeRecordNumber.to
    - field: employeeName
      operator: $cont
      valueField: employeeName
    - field: localExpatCode
      operator: $in
      valueField: localExpatCode.(value)
    - field: insuranceType
      operator: $eq
      valueField: insuranceType.(value)
    - field: contractTypeCode
      operator: $in
      valueField: contractTypeCode.(value)
    - field: effectiveDate
      operator: $between
      valueField: effectiveDate
    - field: status
      operator: $in
      valueField: status
    - field: note
      operator: $cont
      valueField: note
    - field: updatedBy
      operator: $eq
      valueField: updatedBy
    - field: updatedAt
      operator: $between
      valueField: updatedAt
  sources:
    employeesList:
      uri: '"/api/personals/"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page, 'search': $.search, 'filter':
        [{'field':'employeeId','operator': '$eq','value':$.empId}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $distinct($map($.data, function($item) {{'label':
        $join($filter([$item.employeeId,$item.fullName], $boolean), ' - '),
        'value': $item.employeeId}})[])[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - empId
    contractTypeList:
      uri: '"/api/picklists/CONTRACTTYPE/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})
      disabledCache: true
    localExpatList:
      uri: '"/api/picklists/LOCALFOREIGNERS/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})
      disabledCache: true
    insContriRateList:
      uri: '"/api/insurance-contribution-rates"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
    insTypeList:
      uri: '"/api/picklists/INSURANCETYPE/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})
      disabledCache: true
    userList:
      uri: '"/api/admin-users"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.account, 'value':
        $item.id}})
      disabledCache: true
  variables:
    _employeesList:
      transform: $employeesList()
    _contractTypeList:
      transform: $contractTypeList()
    _localExpatList:
      transform: $localExpatList()
    _insContriRateList:
      transform: $insContriRateList()
    _insTypeList:
      transform: $insTypeList()
    _userList:
      transform: $userList()
layout_options:
  toolTable:
    export: true
    import: true
    adjustDisplay: true
  tool_table:
    - id: import
      href: /GE/HR.FS.FR.092
      paramsRedirect:
        type: INS_OBJECT
        entityOrObj: EmployeeMandatoryInsurance
    - id: export
  history_widget_header_options:
    duplicate: false
  show_dialog_form_save_add_button: true
  support_search_date: true
  historyFilterMapping:
    - field: employeeRecordNumber
      operator: $eq
      valueField: employeeRecordNumber
  delete_multi_items: true
  hide_action_row: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: add
    type: primary
options: null
create_form: null
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    title: Edit
    icon: icon-pencil-simple
    type: ghost-gray
  - id: delete
    title: Delete
    icon: icon-trash
    group: null
    type: ghost-gray
backend_url: /api/employee-mandatory-insurance
screen_name: null
layout_options__actions_many: null
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: employeeId
    defaultName: EmployeeId
  - name: employeeRecordNumber
    defaultName: EmployeeRecordNumber
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Manage Compulsory Insurance for Employees
  parent:
    title: Manage Data Insurance
