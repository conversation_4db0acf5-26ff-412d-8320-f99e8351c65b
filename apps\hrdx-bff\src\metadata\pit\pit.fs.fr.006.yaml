id: PIT.FS.FR.006
status: draft
sort: 80
user_created: 43878dbb-aa6c-4ef4-8bd7-0ea811ffafa7
date_created: '2024-07-27T02:42:40.034Z'
user_updated: cdfee544-f2ba-4ed2-ace8-5adc1ae0ed61
date_updated: '2025-06-20T08:28:48.042Z'
title: Tax Bracket Rule
requirement:
  time: 1748402940212
  blocks:
    - id: t3nqFLz6CS
      type: paragraph
      data:
        text: .
    - id: T5SifJrIwu
      type: paragraph
      data:
        text: ' <PERSON><PERSON> thống cho phép admin thực hiện cấu hình, chỉnh sửa caption của các trường thông tin trên giao diện, tiêu đề của các cột dưới lưới và các nút chức năng tại MH chính và các popup'
    - id: IfmnAHhrHj
      type: paragraph
      data:
        text: >-
          &nbsp;<PERSON><PERSON> thống cho phép admin sắp xếp thứ tự của các trường thông tin
          hiển thị trên giao diện và thứ tự các cột dữ liệu dưới lưới.
    - id: 40Ibsrl0aB
      type: paragraph
      data:
        text: >-
          Với các trường thông tin cho phép nhập liệu, hệ thống thực hiện valid
          và cảnh báo ngay khi người dùng nhập sai định dạng cơ bản về kiểu dữ
          liệu (MessageID=10)
    - id: SbOSZyMSrp
      type: paragraph
      data:
        text: >-
          Với các valid dữ liệu về định dạng chi tiết (Không cho nhập ký tự đặc
          biệt, không cho nhập khoảng trắng), valid về logic thì khi người dùng
          bấm lưu thông tin thì hệ thống sẽ cảnh báo lỗi và hiển thị thông tin
          lỗi ngay dưới trường thông tin xảy ra lỗi
  version: 2.30.7
screen_design: null
module: PIT
local_fields:
  - code: country
    title: Country
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: Load data for the corresponding country
    pinned: false
    show_sort: true
  - code: residenceStatus
    title: Residence Status
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: Load data for the corresponding residence status
  - code: contractTypesName
    title: Contract Type
    data_type: null
    display_type:
      key: Tooltip
      collection: field_types
    description: Load data for the corresponding contract type
  - code: effectiveDate
    title: Effective Start Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    default_sort: Sort Ascending
    description: Load data for the corresponding effective date
    pinned: false
  - code: effectiveDateTo
    title: Effective End Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
  - code: tariff
    title: Tax Bracket
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: Load data for the corresponding tariff
  - code: note
    title: Note
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    description: Load data for the corresponding note
  - code: updatedBy
    title: Last Updated By
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: updatedAt
    title: Last Updated On
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
mock_data:
  - country: Viet Nam
    group: FPT
    company: FPT Telecom
    legalEntity: FPT Telecom
    payGroup: Chăm sóc khách hàng (CS)
    localForeigner: Người bản địa
    residenceStatus: Cư trú
    employeeGroup: Nhân viên chính thức
    contractType: Hợp đồng XĐTH 12 tháng
    effectiveDate: '2024-01-01'
    tariff: Lũy tiến VN 2023
    note: Thông tư 111/2013/TT-BTC
    createdBy: AnhTT1
    createdOn: '2024-01-01 10:00:02'
    lastUpdatedBy: AnhTT1
    lastUpdatedOn: '2024-01-01 10:00:02'
  - country: Viet Nam
    group: FPT
    company: FPT Retail
    legalEntity: FPT Shop
    payGroup: Khối cửa hàng
    localForeigner: Người bản địa
    residenceStatus: Cư trú
    employeeGroup: Nhân viên chính thức
    contractType: Hợp đồng XĐTH 12 tháng
    effectiveDate: '2020-01-01'
    tariff: Lũy tiến VN 2023
    note: Thông tư 111/2013/TT-BTC
    createdBy: AnhTT1
    createdOn: '2024-01-01 10:00:02'
    lastUpdatedBy: AnhTT1
    lastUpdatedOn: '2024-01-01 10:00:02'
  - country: Viet Nam
    group: FPT
    company: FPT IS
    legalEntity: ''
    payGroup: ''
    localForeigner: Người bản địa
    residenceStatus: Cư trú
    employeeGroup: Nhân viên thử việc
    contractType: Hợp đồng khoán
    effectiveDate: '2024-01-01'
    tariff: Toàn phần 10%
    note: Thông tư 111/2013/TT-BTC
    createdBy: AnhTT1
    createdOn: '2024-01-01 10:00:02'
    lastUpdatedBy: AnhTT1
    lastUpdatedOn: '2024-01-01 10:00:02'
  - country: Viet Nam
    group: FPT
    company: FPT IS
    legalEntity: ''
    payGroup: ''
    localForeigner: Người bản địa
    residenceStatus: Cư trú
    employeeGroup: Nhân viên thử việc
    contractType: Hợp đồng XĐTH 12 tháng
    effectiveDate: '2021-01-01'
    tariff: Toàn phần 10%
    note: Thông tư 111/2013/TT-BTC
    createdBy: AnhTT1
    createdOn: '2024-01-01 10:00:02'
    lastUpdatedBy: AnhTT1
    lastUpdatedOn: '2024-01-01 10:00:02'
local_buttons: null
layout: layout-table
form_config:
  formTitle:
    create: Add New Tax Bracket Rule
    edit: Edit Tax Bracket Rule
    view: Tax Bracket Rule Detail
  formSize:
    view: small
  fields:
    - type: group
      _condition:
        transform: $not($.extend.formType = 'view')
      n_cols: 2
      fields:
        - name: effectiveDate
          label: Effective Start Date
          type: dateRange
          setting:
            format: dd/MM/yyyy
            type: date
          _value:
            transform: $isNilorEmpty($.extend.defaultValue.effectiveDate) ? $now()
          validators:
            - type: required
          mode: date-picker
        - name: effectiveDateTo
          label: Effective End Date
          type: dateRange
          setting:
            format: dd/MM/yyyy
            type: date
          mode: date-picker
        - name: countryObject
          label: Country
          type: select
          placeholder: Select Country
          clearFieldsAfterChange:
            - tariffObject
          isLazyLoad: true
          _select:
            transform: $nationsList($.extend.page,$.extend.limit,$.extend.search)
          validators:
            - type: required
        - name: residenceStatusCode
          label: Residence Status
          type: select
          placeholder: Select Residence Status
          outputValue: value
          _select:
            transform: $residenceStatusList()
          validators:
            - type: required
        - name: contractTypeCodes
          label: Contract Type
          type: selectAll
          placeholder: Select Contract Type
          outputValue: value
          mode: multiple
          _options:
            transform: $contractTypesList($.fields.effectiveDate)
          validators:
            - type: required
        - name: tariffObject
          label: Tax Bracket
          type: select
          placeholder: Select Tax Bracket
          validators:
            - type: required
          isLazyLoad: true
          _select:
            transform: >-
              $tariffList($.fields.effectiveDate,$.extend.page,$.extend.limit,$.extend.search,$.fields.countryObject.value)
    - type: group
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - name: effectiveDate
          label: Effective Start Date
          type: dateRange
          setting:
            format: dd/MM/yyyy
            type: date
          mode: date-picker
        - name: effectiveDateTo
          label: Effective End Date
          type: dateRange
          setting:
            format: dd/MM/yyyy
            type: date
          mode: date-picker
        - name: countryNameCode
          label: Country
          type: text
          _value:
            transform: >-
              $.extend.defaultValue.country? $.extend.defaultValue.country & '
              (' & $.extend.defaultValue.countryCode & ')'
        - name: residenceStatusNameCode
          label: Residence Status
          type: text
          _value:
            transform: >-
              $.extend.defaultValue.residenceStatus?$.extend.defaultValue.residenceStatus
              & ' (' & $.extend.defaultValue.residenceStatusCode & ')'
        - name: contractTypeCodes
          label: Contract Type
          type: selectAll
          placeholder: Select Contract Type
          outputValue: value
          mode: multiple
          _options:
            transform: $contractTypesList($.fields.effectiveDate)
        - name: tariffNameCode
          label: Tax Bracket
          type: text
          _value:
            transform: >-
              $.extend.defaultValue.tariffCode?$.extend.defaultValue.tariffCode.label
    - name: note
      label: Note
      type: textarea
      placeholder: Enter Note
      textarea:
        autoSize:
          minRows: 3
        maxCharCount: 1000
      validators:
        - type: maxLength
          args: 1000
          text: Maximum 1000 characters
  footer:
    create: true
    update: true
    createdOn: createdAt
    updatedOn: updatedAt
    createdBy: createdBy
    updatedBy: updatedBy
  sources:
    groupsList:
      uri: '"/api/groups/get-by"'
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator': '$eq','value':
        $.effectiveDate}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id}})[]
      disabledCache: true
      params:
        - page
        - limit
        - search
        - effectiveDate
    tariffList:
      uri: '"/api/tariff/"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page, 'search': $.search, 'filter':
        [{'field':'status','operator':'$eq','value':true},{'field':'countryCode','operator':'$eq','value':$.countryCode},{'field':'code','operator':'$eq','value':
        $.code},{'field':'effectiveDate','operator':'$lte','value':$.effectiveDate},{'field':'effectiveDateTo','operator':'$eq','value':'NULL'},{'field':'effectiveDateTo','operator':'$gt','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - effectiveDate
        - page
        - limit
        - search
        - countryCode
    nationsList:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      queryTransform: ' {''limit'': $.limit,''search'':$.search , ''page'': $.page, ''filter'':[{''field'':''status'',''operator'':''$eq'',''value'':true}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data[], function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - page
        - limit
        - search
    contractTypesList:
      uri: '"/api/picklists/CONTRACTTYPE/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
    residenceStatusList:
      uri: '"/api/picklists/RESIDENCESTATUS/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
filter_config:
  fields:
    - name: country
      labelType: type-grid
      label: Country
      type: selectAll
      mode: multiple
      placeholder: Select Country
      _options:
        transform: $nationsList($.extend.limit, $.extend.page, $.extend.search)
      isLazyLoad: true
    - name: residenceStatus
      labelType: type-grid
      label: Residence Status
      type: selectAll
      placeholder: Select Residence Status
      mode: multiple
      _options:
        transform: $residenceStatusList()
    - name: contractType
      labelType: type-grid
      label: Contract Type
      type: selectAll
      placeholder: Select Contract Type
      mode: multiple
      _options:
        transform: $contractTypesList($DateFormat($now(),'YYYY-MM-DDTHH:mm:ss'))
    - name: effectiveDate
      labelType: type-grid
      label: Effective Start Date
      type: dateRange
      setting:
        format: dd/MM/yyyy
        type: date
    - name: effectiveDateTo
      labelType: type-grid
      label: Effective End Date
      type: dateRange
      setting:
        format: dd/MM/yyyy
        type: date
    - name: tariff
      labelType: type-grid
      label: Tax Bracket
      type: selectAll
      placeholder: Select Tax Bracket
      mode: multiple
      isLazyLoad: true
      _options:
        transform: >-
          $tariffList($DateFormat($now(),'YYYY-MM-DDTHH:mm:ss'),$.extend.limit,
          $.extend.page, $.extend.search)
    - name: note
      labelType: type-grid
      label: Note
      type: text
      placeholder: Enter Note
    - name: updatedBy
      label: Last Updated By
      type: selectAll
      mode: multiple
      labelType: type-grid
      placeholder: Select Last Updated By
      _options:
        transform: $userList()
    - name: updatedAt
      label: Last Updated On
      type: dateRange
      labelType: type-grid
      setting:
        format: dd/MM/yyyy
        type: date
  filterMapping:
    - field: countryCode
      operator: $in
      valueField: country.(value)
    - field: effectiveDate
      operator: $between
      valueField: effectiveDate
    - field: effectiveDateTo
      operator: $between
      valueField: effectiveDateTo
    - field: contractTypesCode
      operator: $in
      valueField: contractType.(value)
    - field: residenceStatusCode
      operator: $in
      valueField: residenceStatus.(value)
    - field: note
      operator: $cont
      valueField: note
    - field: tariffCode
      operator: $in
      valueField: tariff.(value)
    - field: updatedBy
      operator: $in
      valueField: updatedBy.(value)
    - field: updatedAt
      operator: $between
      valueField: updatedAt
  sources:
    nationsList:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      queryTransform: ' {''limit'': $.limit,''search'':$.search , ''page'': $.page, ''filter'':[{''field'':''status'',''operator'':''$eq'',''value'':true}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    contractTypesList:
      uri: '"/api/picklists/CONTRACTTYPE/values/pagination"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
    tariffList:
      uri: '"/api/tariff/"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'filter':
        [{'field':'search','operator':'$eq','value':$.search},{'field':'effectiveDate','operator':
        '$lte','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - effectiveDate
        - limit
        - page
        - search
    residenceStatusList:
      uri: '"/api/picklists/RESIDENCESTATUS/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
    userList:
      uri: '"/api/internal-users"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.userName, 'value':
        $item.userName}})
      disabledCache: true
layout_options:
  show_dialog_form_save_add_button: true
  toolTable:
    adjustDisplay: 'true'
  tool_table:
    - id: export
  show_detail_history: false
  view_after_created: false
  delete_multi_items: true
  is_export_grid: true
  hide_action_row: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    title: Edit
    icon: icon-pencil-simple-bold
    type: ghost-gray
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: ghost-gray
backend_url: /api/tax-object-for-principles
screen_name: tax-object-for-principles
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: countryCode
    defaultName: CountryCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Tax Bracket Rule
  parent:
    title: General Setting
