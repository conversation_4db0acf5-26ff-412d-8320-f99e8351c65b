id: PIT.FS.FR.007
status: draft
sort: 98
user_created: c712040f-6a4d-4c9b-86bb-1edd932ec5e0
date_created: '2024-09-24T10:20:16.082Z'
user_updated: cdfee544-f2ba-4ed2-ace8-5adc1ae0ed61
date_updated: '2025-07-22T02:16:52.260Z'
title: Employee-Specific Tax Bracket
requirement:
  time: 1748340627353
  blocks:
    - id: t3nqFLz6CS
      type: paragraph
      data:
        text: >-
          <PERSON><PERSON> thống có hỗ trợ chức năng tự động xác định đối tượng tính thuế dựa
          trên thông tin hợp đồng lao động của phân hệ HR.
    - id: YrVAVwllCH
      type: paragraph
      data:
        text: >-
          Tr<PERSON><PERSON><PERSON> hợp trong một kỳ tính thuế có cả thuế toàn phần và lũy tiến th<PERSON>
          hệ thống sẽ tính thuế lũy tiến cho nhân viên.
  version: 2.30.7
screen_design: null
module: PIT
local_fields:
  - code: employeeId
    title: Employee ID
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    pinned: false
    show_sort: true
  - code: employeeRecordNumber
    title: Employee Record Number (ERN)
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: employeeName
    title: Employee Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: jobIndicator
    title: Job Indicator
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: localForeigner
    title: Local/Foreigner
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: residenceStatusName
    title: Residence Status
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: group
    title: Group
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: company
    title: Company
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: legalEntity
    title: Legal Entity
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: businessUnit
    title: Business Unit
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: division
    title: Division
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: department
    title: Department
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: employeeGroup
    title: Employee Group
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: contractType
    title: Contract Type
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: effectiveDateFrom
    title: Effective Start Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
  - code: effectiveDateTo
    title: Effective End Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
  - code: tariff
    title: Tax Bracket
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: note
    title: Note
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
  - code: updatedBy
    title: Last Updated By
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: updatedAt
    title: Last Updated On
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
mock_data:
  - code: '123456'
    employeeRecordNumber: '1'
    employeeName: Nguyen Van A
    jobIndicator: Việc chính
    localForeigner: Người bản địa
    residenceStatus: Cư trú
    country: Viet Nam
    group: FPT
    company: FPT IS
    legalEntity: FPT IS HCM
    businessUnit: ES HCM
    division: PB22
    department: Phòng triển khai miền Nam
    employeeGroup1: Nhân viên chính thức
    contractType: Hợp đồng XĐTH 12 tháng
    effectiveDate: '2020-01-01'
    tariff: Lũy tiến VN 2023
    note: Thông tư 111/2013/TT-BTC
    createdBy: AnhTT1
    createdOn: '2024-01-01 10:00:02'
    lastUpdatedBy: AnhTT1
    lastUpdatedOn: '2024-01-01 10:00:02'
  - code: '123457'
    employeeRecordNumber: '1'
    employeeName: Nguyen Van B
    jobIndicator: Việc chính
    localForeigner: Người bản địa
    residenceStatus: Cư trú
    country: Viet Nam
    group: FPT
    company: FPT IS
    legalEntity: FPT IS HN
    businessUnit: ES HN
    division: PB5
    department: Phòng triển khai miền Bắc
    employeeGroup1: Nhân viên chính thức
    contractType: Hợp đồng XĐTH 12 tháng
    effectiveDate: '2020-01-01'
    tariff: Lũy tiến VN 2023
    note: Thông tư 111/2013/TT-BTC
    createdBy: AnhTT1
    createdOn: '2024-01-01 10:00:02'
    lastUpdatedBy: AnhTT1
    lastUpdatedOn: '2024-01-01 10:00:02'
  - code: '123458'
    employeeRecordNumber: '1'
    employeeName: Nguyen Van C
    jobIndicator: Việc chính
    localForeigner: Người bản địa
    residenceStatus: Cư trú
    country: Viet Nam
    group: FPT
    company: FPT IS
    legalEntity: FPT IS HN
    businessUnit: ES HN
    division: PB8
    department: Phòng triển khai miền Nam
    employeeGroup1: Nhân viên thử việc
    contractType: Hợp đồng khoán
    effectiveDate: '2020-01-01'
    tariff: Toàn phần VN 10%
    note: Thông tư 111/2013/TT-BTC
    createdBy: AnhTT1
    createdOn: '2024-01-01 10:00:02'
    lastUpdatedBy: AnhTT1
    lastUpdatedOn: '2024-01-01 10:00:02'
local_buttons: null
layout: layout-table
form_config:
  formTitle:
    create: Add New Employee-Specific Tax Bracket
    view: Employee-Specific Tax Bracket Detail
  formSize:
    view: small
    create: largex
    edit: largex
  fields:
    - type: group
      n_cols: 2
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - name: effectiveDateFrom
          label: Effective Start Date
          type: dateRange
          _condition:
            transform: $.extend.formType != 'view'
          validators:
            - type: required
          _value:
            transform: >-
              $.extend.formType = 'create' and
              $not($exists($.fields.effectiveDate)) ? $now() :
              $.fields.effectiveDate
          clearFieldsAfterChange:
            - employee
            - tariffObject
          placeholder: Select Effective Start Date
          setting:
            format: dd/MM/yyyy
            type: date
          mode: date-picker
        - name: effectiveDateTo
          label: Effective End Date
          type: dateRange
          _condition:
            transform: $.extend.formType != 'view'
          placeholder: Select Effective End Date
          setting:
            format: dd/MM/yyyy
            type: date
          mode: date-picker
    - type: select
      name: employee
      label: Employee
      placeholder: Select Employee
      isLazyLoad: true
      validators:
        - type: required
      _select:
        transform: >-
          $employeesList($.extend.limit, $.extend.page,
          $.extend.search,'','','', $.fields.effectiveDateFrom)
      _condition:
        transform: $.extend.formType = 'create'
      outputValue: value
    - type: select
      name: employee
      label: Employee
      placeholder: Select Employee
      validators:
        - type: required
      _select:
        transform: $.variables._employee
      _value:
        transform: $.variables._dataEdit
      disabled: true
      _condition:
        transform: $.extend.formType = 'edit'
      outputValue: value
    - type: text
      name: employeeId
      label: Employee
      unvisible: true
      _value:
        transform: $.fields.employee.employeeId
    - type: text
      name: employeeRecordNumber
      unvisible: true
      _value:
        transform: $.fields.employee.employeeRecordNumber
    - dependantField: $.fields.employee
      type: select
      name: employeeIdInvisible
      unvisible: true
      _value:
        transform: >-
          $exists($.variables._totalOverview) and
          $boolean($.variables._totalOverview)?$.variables._totalOverview:undefined
    - type: text
      name: jobDataId
      label: jobDataId
      unvisible: true
      _value:
        transform: $.fields.employee.jobDataId
    - name: tariffObject
      label: Tax Bracket
      _condition:
        transform: $.extend.formType = 'create' or $.extend.formType = 'edit'
      type: select
      validators:
        - type: required
      isLazyLoad: true
      placeholder: Select Tax Bracket
      _select:
        transform: >-
          $tariffList($.fields.effectiveDateFrom,$.extend.limit, $.extend.page,
          $.extend.search)
    - type: group
      lastGroupStyleOff: true
      _condition:
        transform: $.extend.formType = 'view'
      readOnly: true
      fields:
        - name: employeeId
          type: text
          label: Employee Id
        - name: employeeRecordNumber
          type: text
          label: Employee Record Number
          _value:
            transform: $string($.fields.employeeRecordNumber)
        - name: employeeName
          type: text
          label: Name
        - name: jobIndicator
          label: Job Indicator
          type: text
        - name: localForeigner
          type: text
          label: Local/Foreign Employees
        - name: residenceStatusName
          type: text
          label: Residence Status
        - name: group
          label: Group
          type: text
        - name: company
          label: Company
          type: text
        - name: legalEntity
          label: Legal Entity
          type: text
        - name: businessUnit
          label: Business Unit
          type: text
        - name: division
          label: Division
          type: text
        - name: department
          label: Department
          type: text
        - name: contractType
          label: Contract Type
          type: text
        - name: employeeGroup
          type: text
          label: Employee Group
        - name: effectiveDateFrom
          type: dateRange
          label: Effective Start Date
          setting:
            format: dd/MM/yyyy
            type: date
          mode: date-picker
        - name: effectiveDateTo
          type: dateRange
          label: Effective End Date
          setting:
            format: dd/MM/yyyy
            type: date
          mode: date-picker
        - name: tariffObject
          label: Tax Bracket
          type: select
          placeholder: Select Tax Bracket
          _select:
            transform: >-
              $tariffList($.fields.effectiveDateFrom,$.extend.limit,
              $.extend.page, $.extend.search)
    - type: textarea
      label: Note
      name: note
      placeholder: Enter Note
      textarea:
        autoSize:
          minRows: 3
        maxCharCount: 1000
      validators:
        - type: maxLength
          args: 1000
          text: Maximum 1000 characters
  overview:
    dependentField: employeeIdInvisible
    _condition:
      transform: $.extend.formType = 'create' or $.extend.formType = 'edit'
    title: Employee Detail
    border: true
    uri: ''
    display:
      - label: Job Indicator
        _value:
          transform: >-
            $exists($.variables._totalOverview.jobIndicator)?$.variables._totalOverview.jobIndicator:'--'
      - label: Group
        _value:
          transform: >-
            $exists($.variables._totalOverview.groupName)?$.variables._totalOverview.groupName:'--'
      - label: Company
        _value:
          transform: >-
            $exists($.variables._totalOverview.company)?$.variables._totalOverview.company:'--'
      - label: Legal Entity
        _value:
          transform: >-
            $exists($.variables._totalOverview.legalEntity)?$.variables._totalOverview.legalEntity:'--'
      - label: Business Unit
        _value:
          transform: >-
            $exists($.variables._totalOverview.businessUnit)?$.variables._totalOverview.businessUnit:'--'
      - label: Division
        _value:
          transform: >-
            $exists($.variables._totalOverview.division)?$.variables._totalOverview.division:'--'
      - label: Department
        _value:
          transform: >-
            $exists($.variables._totalOverview.department)?$.variables._totalOverview.department:'--'
      - label: Employee Group
        _value:
          transform: >-
            $exists($.variables._totalOverview.employeeGroup)?$.variables._totalOverview.employeeGroup:'--'
      - label: Local/Foreign Employees
        _value:
          transform: >-
            $exists($.variables._totalOverview.localForeigner)?$.variables._totalOverview.localForeigner:'--'
      - label: Residence Status
        _value:
          transform: >-
            $exists($.variables._totalOverview.residenceStatusName)?$.variables._totalOverview.residenceStatusName:'--'
      - label: Contract Type
        _value:
          transform: >-
            $exists($.variables._totalOverview.contractTypeName)?$.variables._totalOverview.contractTypeName:'--'
  footer:
    create: true
    update: true
    createdOn: createdAt
    updatedOn: updatedAt
    createdBy: createdBy
    updatedBy: updatedBy
  sources:
    employeesList:
      uri: '"/api/personals"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page, 'search': $.search, 'filter':
        [{'field':'employeeId','operator':
        '$eq','value':$.empId},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'jobDataEffectiveDateFrom','operator':
        '$lte','value':$.effectiveDate},{'operator': '$or',
        'value':[{'field':'jobDataEffectiveDateTo','operator': '$eq','value':
        'NULL'},{'field':'jobDataEffectiveDateTo','operator':
        '$gt','value':$.effectiveDate}]},{'field':'employeeGroupCode','operator':
        '$cont','value':$.grpCode},{'field':'employeeRecordNumber','operator':
        '$eq','value':$.ern}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label':
        $join($filter([$item.employeeId,$item.employeeGroupCode,$string($item.employeeRecordNumber),$item.fullName,$item.jobIndicatorName],
        $boolean), ' - ')  , 'value': {'employeeId': $item.employeeId,
        'employeeRecordNumber': $string($item.employeeRecordNumber),
        'jobDataId': $item.jobDataId}, 'employeeId': $item.employeeId,
        'employeeRecordNumber': $string($item.employeeRecordNumber),'jobDataId':
        $item.jobDataId}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - empId
        - grpCode
        - ern
        - effectiveDate
    tariffList:
      uri: '"/api/tariff"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator':'$eq','value':true},{'field':'effectiveDate','operator':'$lte','value':$.effectiveDateFrom},{'field':'effectiveDateTo','operator':'$eq','value':'NULL'},{'field':'effectiveDateTo','operator':'$gt','value':$.effectiveDateFrom}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - effectiveDateFrom
        - limit
        - page
        - search
    residenceStatusList:
      uri: '"/api/manage-residence-status"'
      method: GET
      queryTransform: >-
        {'limit':1,'filter':[{'field':'employeeId', 'operator':'$eq',
        'value':$.employeeId}, {'field':'employeeRecordNumber',
        'operator':'$eq',
        'value':$.ern},{'field':'effectiveDateFrom','operator':'$lte','value':$.effectiveDateStart},{'field':'effectiveDateTo','operator':'$eq','value':'NULL'},{'field':'effectiveDateTo','operator':'$gt','value':$.effectiveDateStart}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $.data[]
      disabledCache: true
      params:
        - employeeId
        - ern
        - effectiveDateStart
    detailLocalExpat:
      uri: '"/api/employee-related-info/local-foreigner"'
      method: GET
      queryTransform: >-
        {'filter':[{'field': 'employeeId', 'operator': '$eq', 'value':
        $.employeeId}, {'field': 'employeeRecordNumber', 'operator': '$eq',
        'value': $.ern}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $
      disabledCache: true
      params:
        - employeeId
        - ern
    overviewDetail:
      uri: '"/api/personals/"  & $.employeeId & "/job-datas/overview/" & $.jobDataId'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $
      disabledCache: true
      params:
        - employeeId
        - jobDataId
    companiesDetail:
      uri: '"/api/companies"'
      method: GET
      queryTransform: >-
        {'limit': 1, 'filter': [{'field':'code','operator':
        '$eq','value':$.companyCode},{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator': '$lte','value':
        $exists($.effectiveDate)?$.effectiveDate:$now()}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $.data[]
      disabledCache: true
      params:
        - effectiveDate
        - companyCode
    personalIdenDetail:
      uri: '"/api/personals/"  & $.employeeId & "/personal-identity-documents/"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $[]
      disabledCache: true
      params:
        - employeeId
  variables:
    _employee:
      transform: >-
        $employeesList(1,1,'',$.extend.defaultValue.employeeId,$.extend.defaultValue.employeeGroupCode,$.extend.defaultValue.employeeRecordNumber,$.extend.defaultValue.effectiveDateFrom)
    _dataEdit:
      transform: $.variables._employee[0].value
    _foundedResidenceStatus:
      transform: >-
        ($.fields.employee; $exists($.fields.employee) and
        $boolean($.fields.employee)?$residenceStatusList($.fields.employee.employeeId,$.fields.employee.employeeRecordNumber,$.fields.effectiveDateFrom))
    _selectedResidenceStatus:
      transform: >-
        ($.variables._foundedResidenceStatus;
        $count($.variables._foundedResidenceStatus)>0?$.variables._foundedResidenceStatus[0]:{})
    _selectedLocalExpat:
      transform: >-
        ($.fields.employee; $exists($.fields.employee) and
        $boolean($.fields.employee)?$detailLocalExpat($.fields.employee.employeeId,
        $.fields.employee.employeeRecordNumber):{})
    _overviewDetail:
      transform: >-
        ($.fields.employee; $exists($.fields.employee) and
        $boolean($.fields.employee)?$overviewDetail($.fields.employee.employeeId,
        $.fields.employee.jobDataId):{})
    _foundedCompaniesDetail:
      transform: >-
        ($.variables._overviewDetail; $exists($.variables._overviewDetail) and
        $boolean($.variables._overviewDetail.companyCode)?$companiesDetail($.fields.effectiveDateFrom,
        $.variables._overviewDetail.companyCode))
    _getFirstCompaniesDetail:
      transform: >-
        ($.variables._getCompaniesDetail;
        $count($.variables._foundedCompaniesDetail)>0?$.variables._foundedCompaniesDetail[0]:{})
    _foundedPersonalIdenDetail:
      transform: >-
        ($.fields.employee; $exists($.fields.employee) and
        $boolean($.fields.employee)?$personalIdenDetail($.fields.employee.employeeId))
    _getFirstPersonalIdenDetail:
      transform: >-
        ($.variables._foundedPersonalIdenDetail;
        $count($.variables._foundedPersonalIdenDetail)>0?$.variables._foundedPersonalIdenDetail[0]:{})
    _totalOverview:
      transform: >-
        $exists($.fields.employee) and
        $boolean($.fields.employee)?$merge([$.variables._overviewDetail,$.variables._selectedResidenceStatus,{'localForeigner':$.variables._selectedLocalExpat.localForeigner,'groupName':
        $.variables._getFirstCompaniesDetail.groupName,'countryName':$.variables._getFirstPersonalIdenDetail.countryName}]):null
filter_config:
  fields:
    - type: selectAll
      name: employee
      label: Employee
      placeholder: Select Employee
      isLazyLoad: true
      labelType: type-grid
      _options:
        transform: $employeesList($.extend.limit, $.extend.page, $.extend.search)
    - labelType: type-grid
      name: jobIndicator
      label: Job Indicator
      type: radio
      _radio:
        transform: $.variables._jobIndicatorList
      value: ''
      placeholder: Select Job Indicator
    - labelType: type-grid
      name: localForeigner
      label: Local/Foreigner
      type: selectAll
      mode: multiple
      _options:
        transform: >-
          [{'label': 'Local','value': 'Local'},{'label': 'Foreigner','value':
          'Foreigner'}]
      placeholder: Select Local/Foreigner
    - labelType: type-grid
      name: residenceStatus
      label: Residence Status
      type: selectAll
      mode: multiple
      _options:
        transform: >-
          [{'label': 'Cư trú','value': 'true'},{'label': 'Không cư trú','value':
          'false'}]
      placeholder: Select Residence Status
    - type: selectAll
      labelType: type-grid
      name: group
      label: Group
      placeholder: Select Group
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $groupsList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      labelType: type-grid
      name: company
      label: Company
      placeholder: Select Company
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $companiesList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      labelType: type-grid
      name: legalEntity
      label: Legal Entity
      placeholder: Select Legal Entity
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $legalEntitiesList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      labelType: type-grid
      name: businessUnit
      label: Business Unit
      placeholder: Select Business Unit
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $businessUnitList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      labelType: type-grid
      name: division
      label: Division
      placeholder: Select Division
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $divisionList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      labelType: type-grid
      name: department
      label: Department
      placeholder: Select Department
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $departmentsList($.extend.limit, $.extend.page, $.extend.search)
    - labelType: type-grid
      name: employeeGroup
      label: Employee Group
      type: selectAll
      mode: multiple
      _options:
        transform: $employeeGroupList()
      placeholder: Select Employee Group
    - labelType: type-grid
      name: contractType
      label: Contract Type
      type: selectAll
      mode: multiple
      _options:
        transform: $contractTypeList()
      placeholder: Select Contract Type
    - type: dateRange
      label: Effective Start Date
      labelType: type-grid
      name: effectiveDateFrom
    - type: dateRange
      label: Effective End Date
      labelType: type-grid
      name: effectiveDateTo
    - labelType: type-grid
      name: tariff
      label: Tax Bracket
      type: selectAll
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $tariffList($.extend.limit, $.extend.page, $.extend.search)
      placeholder: Select Tax Bracket
    - name: updatedBy
      label: Last Updated By
      type: selectAll
      mode: multiple
      labelType: type-grid
      placeholder: Select Last Updated By
      _options:
        transform: $userList()
    - name: updatedAt
      label: Last Updated On
      type: dateRange
      labelType: type-grid
      setting:
        format: dd/MM/yyyy
        type: date
  filterMapping:
    - field: employeeSearch
      operator: $in
      valueField: employee.(value)
    - field: jobIndicatorCode
      operator: $eq
      valueField: jobIndicator
    - field: localForeigner
      operator: $in
      valueField: localForeigner.(value)
    - field: residenceStatus
      operator: $in
      valueField: residenceStatus.(value)
    - field: groupCode
      operator: $in
      valueField: group.(value)
    - field: companyCode
      operator: $in
      valueField: company.(value)
    - field: legalEntityCode
      operator: $in
      valueField: legalEntity.(value)
    - field: divisionCode
      operator: $in
      valueField: division.(value)
    - field: businessUnitCode
      operator: $in
      valueField: businessUnit.(value)
    - field: departmentCode
      operator: $in
      valueField: department.(value)
    - field: employeeGroupCode
      operator: $in
      valueField: employeeGroup.(value)
    - field: contractTypeCode
      operator: $in
      valueField: contractType.(value)
    - field: effectiveDateFrom
      operator: $between
      valueField: effectiveDateFrom
    - field: effectiveDateTo
      operator: $between
      valueField: effectiveDateTo
    - field: tariffCode
      operator: $in
      valueField: tariff.(value)
    - field: createdBy
      operator: $cont
      valueField: createdBy
    - field: createdAt
      operator: $between
      valueField: createdAt
    - field: updatedBy
      operator: $in
      valueField: updatedBy.(value)
    - field: updatedAt
      operator: $between
      valueField: updatedAt
  sources:
    groupsList:
      uri: '"/api/groups"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'filter':
        [{'field':'search','operator': '$eq','value':
        $.search},{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    divisionList:
      uri: '"/api/divisions"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    businessUnitList:
      uri: '"/api/business-units"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    companiesList:
      uri: '"/api/companies"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    departmentsList:
      uri: '"/api/departments"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''status'': true}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    legalEntitiesList:
      uri: '"/api/legal-entities"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    tariffList:
      uri: '"/api/tariff"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default& ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    employeesList:
      uri: '"/api/personals/"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page, 'search': $.search, 'filter':
        [{'field':'employeeId','operator':
        '$eq','value':$.empId},{'field':'employeeGroupCode','operator':
        '$cont','value':$.grpCode},{'field':'employeeRecordNumber','operator':
        '$eq','value':$.ern}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label':
        $join($filter([$item.employeeId,$item.employeeGroupCode,$string($item.employeeRecordNumber),$item.fullName,$item.jobIndicatorName],
        $boolean), ' - ')  , 'value':  $item.employeeId & '_' &
        $item.employeeRecordNumber}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    nationsList:
      uri: '"/api/picklists/COUNTRIES/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default& ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
    employeeGroupList:
      uri: '"/api/picklists/EMPLOYEEGROUP/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default& ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
    contractTypeList:
      uri: '"/api/picklists/CONTRACTTYPE/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default& ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
    JobIndicatorList:
      uri: '"/api/picklists/JOBINDICATOR/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $append({'label': 'All', 'value': ''},$map($.data, function($item)
        {{'label': $item.name.default, 'value': $item.code}}))[]
      disabledCache: true
    residenceStatusList:
      uri: '"/api/picklists/RESIDENCESTATUS/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
    localForeignersList:
      uri: '"/api/picklists/LOCALFOREIGNERS/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
    userList:
      uri: '"/api/internal-users"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.userName, 'value':
        $item.userName}})
      disabledCache: true
  variables:
    _jobIndicatorList:
      transform: $JobIndicatorList()
layout_options:
  show_dialog_form_save_add_button: true
  show_detail_history: false
  toolTable:
    export: true
    import: true
    adjustDisplay: true
  tool_table:
    - id: import
      href: /GE/HR.FS.FR.092
    - id: export
  delete_multi_items: true
  is_export_grid: true
  hide_action_row: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    title: Edit
    icon: icon-pencil-simple-bold
    type: ghost-gray
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: ghost-gray
backend_url: /api/tax-object-for-employee
screen_name: tax-object-for-employee
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: employeeId
    defaultName: EmployeeId
  - name: employeeRecordNumber
    defaultName: EmployeeRecordNumber
  - name: companyCode
    defaultName: CompanyCode
  - name: legalEntityCode
    defaultName: LegalEntityCode
  - name: businessUnitCode
    defaultName: BusinessUnitCode
  - name: departmentCode
    defaultName: DepartmentCode
  - name: divisionCode
    defaultName: DivisionCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Employee-Specific Tax Bracket
  parent:
    title: Manage Employee Infor
