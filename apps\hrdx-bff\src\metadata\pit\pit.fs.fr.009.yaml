id: PIT.FS.FR.009
status: draft
sort: 101
user_created: 43878dbb-aa6c-4ef4-8bd7-0ea811ffafa7
date_created: '2024-07-27T06:21:11.636Z'
user_updated: cdfee544-f2ba-4ed2-ace8-5adc1ae0ed61
date_updated: '2025-07-22T02:19:06.104Z'
title: Manage Income Commitment
requirement:
  time: 1748416760115
  blocks:
    - id: tZ1QS0_YAt
      type: paragraph
      data:
        text: >-
          <PERSON><PERSON><PERSON> năng cho phép bộ phận nhân sự CTTV thiết lập, quản lý danh sách
          cam kết thu nhập của CBNV
  version: 2.30.7
screen_design: null
module: PIT
local_fields:
  - code: effectiveDate
    title: Effective Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
  - code: employeeId
    title: Employee ID
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: employeeR<PERSON>ordNumber
    title: Employee Record Number (ERN)
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: employeeName
    title: Employee Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: jobIndicatorName
    title: Job Indicator
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: groupName
    title: Group
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: companyName
    title: Company
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: legalEntityName
    title: Legal Entity
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: businessUnitName
    title: Business Unit
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: divisionName
    title: Division
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: departmentName
    title: Department
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: contractTypeName
    title: Contract Type
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: jobName
    title: Job Title
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: taxCode
    title: PIT Code
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: assessmentPeriodName
    title: Tax Period
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    show_sort: false
  - code: startDate
    title: Start Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
  - code: endDate
    title: End Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
  - code: committedIncome
    title: Committed Income
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Currency
      collection: field_types
  - code: currencyName
    title: Currency
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: cancelIncomeCommitment
    title: Cancel Income Commitment
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: Custom Boolean
      collection: field_types
  - code: cancelledOn
    title: Cancelled On
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
  - code: cancelledReason
    title: Cancelled Reason
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
  - code: note
    title: Note
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
  - code: updatedBy
    title: Last Updated By
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: updatedAt
    title: Last Updated On
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
mock_data:
  - employeeId: '123456'
    employeeRecordNumber: '1'
    employeeName: Nguyen Van A
    jobIndicator: Việc chính
    group: FPT
    company: FPT IS
    legalEntity: FPT IS HCM
    businessUnit: ESHCM
    division: PB22
    department: Phòng triển khai miền Bắc
    contractType: Hợp đồng thử việc
    job: CB Phân tích nghiệp vụ
    taxCode: '*********'
    effectiveDate: '2024-01-01'
    settlementPeriod: '2024'
    startDate: '2024-01-01'
    endDate: '2024-12-31'
    committedIncome: 132,000,000
    currency: VND
    fileAttachment:
      - name: 123456_NguyenVanA.pdf
        url: '123'
    cancelIncomeCommitment: true
    cancelledOn: '2024-06-01'
    cancelledReason: Vượt quá thu nhập cho phép
    note: Dữ liệu test
    createdBy: AnhTT1
    createdOn: '2024-01-01 10:00:25'
    lastUpdatedBy: AnhTT1
    lastUpdatedOn: '2024-06-01 10:00:25'
  - employeeId: '112233'
    employeeRecordNumber: '2'
    employeeName: Hoang Minh B
    jobIndicator: Việc chính
    group: FPT
    company: FPT IS
    legalEntity: FPT IS HCM
    businessUnit: ESHCM
    division: PB22
    department: Phòng triển khai miền Bắc
    contractType: Hợp đồng đào tạo nghề
    job: CB Phân tích nghiệp vụ
    taxCode: '*********'
    effectiveDate: '2024-01-01'
    settlementPeriod: '2024'
    startDate: '2024-01-01'
    endDate: '2024-12-31'
    committedIncome: 132,000,000
    currency: VND
    fileAttachment: []
    cancelIncomeCommitment: false
    cancelledOn: ''
    cancelledReason: ''
    note: Dữ liệu test
    createdBy: AnhTT1
    createdOn: '2024-01-01 10:00:25'
    lastUpdatedBy: AnhTT1
    lastUpdatedOn: '2024-06-01 10:00:25'
local_buttons: null
layout: layout-table
form_config:
  formTitle:
    view: Income Commitment Detail
  formSize:
    view: small
    edit: largex
    create: largex
  fields:
    - type: group
      _condition:
        transform: $not($.extend.formType = 'view')
      n_cols: 2
      fields:
        - name: effectiveDate
          label: Effective Date
          type: dateRange
          setting:
            format: dd/MM/yyyy
            type: date
          validators:
            - type: required
          mode: date-picker
          _value:
            transform: $isNilorEmpty($.extend.defaultValue.effectiveDate) ? $now()
        - type: select
          name: employee
          label: Employee
          placeholder: Select Employee
          isLazyLoad: true
          validators:
            - type: required
          _select:
            transform: >-
              $employeesList($.extend.limit, $.extend.page,
              $.extend.search,'','','', $.fields.effectiveDate)
          _value:
            transform: $.variables._employee[0].value
          _condition:
            transform: $boolean($.extend.formType = 'create')
          outputValue: value
        - type: select
          name: employee
          label: Employee
          placeholder: Select Employee
          validators:
            - type: required
          _select:
            transform: $.variables._employee
          _value:
            transform: $.variables._dataEdit
          _disabled:
            transform: 'true'
          _condition:
            transform: $.extend.formType = 'edit'
          outputValue: value
        - type: text
          name: employeeId
          unvisible: true
          _value:
            transform: $.fields.employee.employeeId
        - type: text
          name: employeeRecordNumber
          unvisible: true
          _value:
            transform: $string($.fields.employee.employeeRecordNumber)
        - name: assessmentPeriodCode
          label: Tax Period
          type: select
          validators:
            - type: required
          outputValue: value
          placeholder: Select Tax Period
          _select:
            transform: $.variables._settlementPeriodList
        - name: committedIncome
          label: Committed Income
          type: number
          number:
            format: currency
            max: '99999999999999'
            precision: 4
          validators:
            - type: required
          placeholder: Enter Committed Income
        - name: currency
          label: Currency
          type: select
          outputValue: value
          validators:
            - type: required
          placeholder: Select Currency
          _select:
            transform: $currenciesList()
        - name: cancelIncomeCommitment
          label: Cancel Income Commitment
          type: checkbox
          placeholder: Cancel Income Commitment
        - col: 2
          name: cancelledOn
          label: Cancelled On
          type: dateRange
          _condition:
            transform: $.fields.cancelIncomeCommitment = true
          setting:
            format: dd/MM/yyyy
            type: date
          mode: date-picker
          validators:
            - type: required
        - col: 2
          name: cancelledReason
          label: Cancelled Reason
          type: textarea
          _condition:
            transform: $.fields.cancelIncomeCommitment = true
          placeholder: Enter Cancelled Reason
          textarea:
            autoSize:
              minRows: 3
            maxCharCount: 1000
          validators:
            - type: required
            - type: maxLength
              args: 1000
              text: Maximum 1000 characters
        - col: 2
          name: note
          label: Note
          type: textarea
          placeholder: Enter Note
          textarea:
            autoSize:
              minRows: 3
            maxCharCount: 1000
          validators:
            - type: maxLength
              args: 1000
              text: Maximum 1000 characters
        - name: employeeIdInvisible
          type: select
          dependantField: $.fields.employee
          unvisible: true
          _value:
            transform: >-
              $exists($.variables._totalOverview) and
              $boolean($.variables._totalOverview)?$.variables._totalOverview:undefined
        - name: settlementPeriodInvisible
          type: select
          dependantField: $.fields.assessmentPeriodCode
          unvisible: true
          _value:
            transform: >-
              $exists($.variables._selectedAssementPeriod) and
              $boolean($.variables._selectedAssementPeriod)?$.variables._selectedAssementPeriod:undefined
    - type: group
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - isBorderTopNone: true
          fieldGroupTitleStyle:
            paddingTop: 0px
          type: group
          label: Job Data
          collapse: false
          fields:
            - name: effectiveDate
              label: Effective Date
              type: dateRange
              setting:
                format: dd/MM/yyyy
                type: date
              mode: date-picker
            - name: employeeId
              label: Employee ID
              type: text
            - name: employeeName
              label: Employee Name
              type: text
            - name: employeeRecordNumber
              label: Employee Record Number (ERN)
              type: text
            - name: jobIndicator
              label: Job Indicator
              type: text
            - name: group
              label: Group
              type: text
            - name: company
              label: Company
              type: text
            - name: legalEntity
              label: Legal Entity
              type: text
            - name: businessUnit
              label: Business Unit
              type: text
            - name: division
              label: Division
              type: text
            - name: department
              label: Department
              type: text
            - name: contractType
              label: Contract Type
              type: text
            - name: job
              label: Job Title
              type: text
        - type: group
          label: Commited Information
          collapse: false
          fields:
            - name: taxCode
              label: PIT Code
              type: text
            - name: assessmentPeriod
              label: Tax Period
              type: text
            - name: startDate
              label: Start Date
              type: dateRange
              setting:
                format: dd/MM/yyyy
                type: date
              _value:
                transform: $.variables._selectedAssementPeriod.assessmentPeriodStartDate
              mode: date-picker
            - name: endDate
              label: End Date
              type: dateRange
              setting:
                format: dd/MM/yyyy
                type: date
              _value:
                transform: $.variables._selectedAssementPeriod.assessmentPeriodEndDate
              mode: date-picker
            - name: committedIncome
              label: Committed Income
              type: number
              number:
                format: currency
              placeholder: Enter Committed Income
            - name: currencyNameCode
              label: Currency
              type: text
              _value:
                transform: >-
                  $.extend.defaultValue.currencyName & ' (' &
                  $.extend.defaultValue.currency & ')'
            - name: cancelIncomeCommitment
              label: Cancel Income Commitment
              type: checkbox
              placeholder: Cancel Income Commitment
              disabled: 'true'
            - name: cancelledOn
              label: Cancelled On
              type: dateRange
              _condition:
                transform: $.fields.cancelIncomeCommitment = true
              setting:
                format: dd/MM/yyyy
                type: date
              mode: date-picker
            - name: cancelledReason
              label: Cancelled Reason
              type: text
              _condition:
                transform: $.fields.cancelIncomeCommitment = true
            - name: note
              label: Note
              type: textarea
              placeholder: Enter Note
              textarea:
                autoSize:
                  minRows: 3
                maxCharCount: 1000
              validators:
                - type: maxLength
                  args: 1000
  overviewGroup:
    - dependentField: employeeIdInvisible
      _condition:
        transform: $.extend.formType = 'create' or $.extend.formType = 'edit'
      title: Employee Detail
      noDataMessages: Select Employee to display data
      uri: ''
      border: true
      display:
        - label: Group
          _value:
            transform: >-
              $exists($.variables._totalOverview.groupCompanyName)?
              $.variables._totalOverview.groupCompanyName: '--'
        - label: Company
          _value:
            transform: >-
              $exists($.variables._totalOverview.companyView)?
              $.variables._totalOverview.companyView: '--'
        - label: Legal Entity
          _value:
            transform: >-
              $exists($.variables._totalOverview.legalEntityView)?
              $.variables._totalOverview.legalEntityView: '--'
        - label: Business Unit
          _value:
            transform: >-
              $exists($.variables._totalOverview.businessUnitView)?$.variables._totalOverview.businessUnitView:'--'
        - label: Division
          _value:
            transform: >-
              $exists($.variables._totalOverview.divisionView)?
              $.variables._totalOverview.divisionView: '--'
        - label: Department
          _value:
            transform: >-
              $exists($.variables._totalOverview.departmentView)?
              $.variables._totalOverview.departmentView: '--'
        - label: Contract Type
          _value:
            transform: >-
              $exists($.variables._totalOverview.contractTypeView)?
              $.variables._totalOverview.contractTypeView : '--'
        - label: Job Title
          _value:
            transform: >-
              $exists($.variables._totalOverview.jobCodeView)?
              $.variables._totalOverview.jobCodeView: '--'
        - label: PIT Code
          _value:
            transform: >-
              $exists($.variables._totalOverview.taxCode)?
              $.variables._totalOverview.taxCode : '--'
    - dependentField: settlementPeriodInvisible
      _condition:
        transform: $.extend.formType = 'create' or $.extend.formType = 'edit'
      title: Tax Period
      uri: ''
      display:
        - label: Tax Period
          _value:
            transform: >-
              $exists($.variables._selectedAssementPeriod.label)?
              $.variables._selectedAssementPeriod.label : '--'
        - label: Start Date
          _value:
            transform: >-
              $exists($.variables._selectedAssementPeriod.assessmentPeriodStartDate)?
              $DateFormat($.variables._selectedAssementPeriod.assessmentPeriodStartDate,'DD/MM/YYYY')
              : '--'
        - label: End Date
          _value:
            transform: >-
              $exists($.variables._selectedAssementPeriod.assessmentPeriodEndDate)?
              $DateFormat($.variables._selectedAssementPeriod.assessmentPeriodEndDate,'DD/MM/YYYY')
              : '--'
  footer:
    create: true
    update: true
    createdOn: createdAt
    updatedOn: updatedAt
    createdBy: createdBy
    updatedBy: updatedBy
  sources:
    assessmentPeriodList:
      uri: '"/api/assessment-period"'
      method: GET
      queryTransform: >-
        { 'limit':5000,'filter':[{'field':'assessmentPeriodTypeCode',
        'operator':'$eq', 'value':'APT_TAX_SETTLEMENT'}] }
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code,
        'assessmentPeriodEndDate':$item.assessmentPeriodEndDate,
        'assessmentPeriodStartDate':$item.assessmentPeriodStartDate }})[]
      disabledCache: true
    currenciesList:
      uri: '"/api/picklists/CURRENCY/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
    employeesList:
      uri: '"/api/personals/"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page, 'search': $.search, 'filter':
        [{'field':'employeeId','operator':
        '$eq','value':$.empId},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'jobDataEffectiveDateFrom','operator':
        '$lte','value':$.effectiveDate},{'operator': '$or',
        'value':[{'field':'jobDataEffectiveDateTo','operator': '$eq','value':
        'NULL'},{'field':'jobDataEffectiveDateTo','operator':
        '$gt','value':$.effectiveDate}]},{'field':'employeeGroupCode','operator':
        '$cont','value':$.grpCode},{'field':'employeeRecordNumber','operator':
        '$eq','value':$.ern}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label':
        $join($filter([$item.employeeId,$item.employeeGroupCode,$string($item.employeeRecordNumber),$item.fullName,$item.jobIndicatorName],
        $boolean), ' - ')  , 'value': {'employeeId': $item.employeeId,
        'employeeRecordNumber': $string($item.employeeRecordNumber),
        'jobDataId': $item.jobDataId}, 'employeeId': $item.employeeId,
        'employeeRecordNumber': $string($item.employeeRecordNumber),'jobDataId':
        $item.jobDataId}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - empId
        - grpCode
        - ern
        - effectiveDate
    detailTaxCode:
      uri: '"/api/employee-related-info/tax-code"'
      method: GET
      queryTransform: >-
        {'filter':[{'field': 'employeeId', 'operator': '$eq', 'value':
        $.employeeId}, {'field': 'employeeRecordNumber', 'operator': '$eq',
        'value': $.ern}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $
      disabledCache: true
      params:
        - employeeId
        - ern
    overviewDetail:
      uri: '"/api/personals/"  & $.employeeId & "/job-datas/overview/" & $.jobDataId'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $
      disabledCache: true
      params:
        - employeeId
        - jobDataId
    jobDetail:
      uri: '"/api/job-codes"'
      method: GET
      queryTransform: >-
        {'limit': 1, 'filter': [{'field':'code','operator':
        '$eq','value':$.jobCode},{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator': '$lte','value':
        $exists($.effectiveDate) and
        $boolean($.effectiveDate)?$.effectiveDate:$now()}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $.data[]
      disabledCache: true
      params:
        - effectiveDate
        - jobCode
    companyDetail:
      uri: '"/api/companies"'
      method: GET
      queryTransform: >-
        {'limit': 1, 'filter': [{'field':'code','operator':
        '$eq','value':$.companyCode},{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator': '$lte','value':
        $exists($.effectiveDate) and
        $boolean($.effectiveDate)?$.effectiveDate:$now()}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $.data[]
      disabledCache: true
      params:
        - effectiveDate
        - companyCode
  variables:
    _settlementPeriodList:
      transform: $assessmentPeriodList()
    _employee:
      transform: >-
        $employeesList(1,1,'',$.extend.defaultValue.employeeId,$.extend.defaultValue.employeeGroupCode,$.extend.defaultValue.employeeRecordNumber,$.extend.defaultValue.effectiveDate)
    _dataEdit:
      transform: $.variables._employee[0].value
    _overviewDetail:
      transform: >-
        ($.fields.employee; $exists($.fields.employee) and
        $boolean($.fields.employee)?$overviewDetail($.fields.employee.employeeId,
        $.fields.employee.jobDataId):{})
    _selectedAssementPeriod:
      transform: >-
        ($.fields.assessmentPeriodCode; $exists($.fields.assessmentPeriodCode) ?
        $filter($.variables._settlementPeriodList, function($item) { $item.value
        = $.fields.assessmentPeriodCode }) : null)
    _selectedTaxCode:
      transform: >-
        ($.fields.employee; $exists($.fields.employee) and
        $boolean($.fields.employee)?$detailTaxCode($.fields.employee.employeeId,
        $.fields.employee.employeeRecordNumber))
    _foundedJobDetail:
      transform: >-
        ($.variables._overviewDetail; $exists($.variables._overviewDetail) and
        $boolean($.variables._overviewDetail.companyCode)?$jobDetail($.fields.effectiveDate,
        $.variables._overviewDetail.jobCode))
    _getFirstJobDetail:
      transform: >-
        ($.variables._foundedJobDetail;
        $count($.variables._foundedJobDetail)>0?$.variables._foundedJobDetail[0]:{})
    _foundedCompanyDetail:
      transform: >-
        ($.variables._overviewDetail; $exists($.variables._overviewDetail) and
        $boolean($.variables._overviewDetail.companyCode)?$companyDetail($.fields.effectiveDate,
        $.variables._overviewDetail.companyCode))
    _getFirstCompanyDetail:
      transform: >-
        ($.variables._foundedCompanyDetail;
        $count($.variables._foundedCompanyDetail)>0?$.variables._foundedCompanyDetail[0]:{})
    _taxCodeName:
      transform: >-
        ($.variables._selectedTaxCode;
        $count($.variables._selectedTaxCode)>0?$.variables._selectedTaxCode.taxCode:'--')
    _totalOverview:
      transform: >-
        $exists($.fields.employee) and
        $boolean($.fields.employee)?$merge([$.variables._overviewDetail,{'taxCode':
        $.variables._taxCodeName,
        'jobLongNameTitle':$.variables._getFirstJobDetail.longName.default,'groupCompanyName':$.variables._getFirstCompanyDetail.groupName}]):null
filter_config:
  fields:
    - name: effectiveDate
      label: Effective Date
      type: dateRange
      labelType: type-grid
      setting:
        format: dd/MM/yyyy
        type: date
    - type: selectAll
      name: employee
      label: Employee
      placeholder: Select Employee
      isLazyLoad: true
      labelType: type-grid
      _options:
        transform: $employeesList($.extend.limit, $.extend.page, $.extend.search)
    - name: jobIndicator
      label: Job Indicator
      type: selectAll
      mode: multiple
      placeholder: Select Job Indicator
      labelType: type-grid
      _options:
        transform: $jobIndicatorList()
    - name: group
      label: Group
      type: selectAll
      mode: multiple
      placeholder: Select Group
      labelType: type-grid
      isLazyLoad: true
      _options:
        transform: $groupsList($.extend.limit, $.extend.page, $.extend.search)
    - name: company
      label: Company
      type: selectAll
      mode: multiple
      placeholder: Select Company
      labelType: type-grid
      isLazyLoad: true
      _options:
        transform: $companiesList($.extend.limit, $.extend.page, $.extend.search)
    - name: legalEntity
      label: Legal Entity
      type: selectAll
      mode: multiple
      placeholder: Select Legal Entity
      labelType: type-grid
      isLazyLoad: true
      _options:
        transform: $legalEntitiesList($.extend.limit, $.extend.page, $.extend.search)
    - name: businessUnit
      label: Business Unit
      type: selectAll
      mode: multiple
      placeholder: Select Business Unit
      labelType: type-grid
      isLazyLoad: true
      _options:
        transform: $businessUnitList($.extend.limit, $.extend.page, $.extend.search)
    - name: division
      label: Division
      type: selectAll
      mode: multiple
      placeholder: Select Division
      labelType: type-grid
      isLazyLoad: true
      _options:
        transform: $divisionList($.extend.limit, $.extend.page, $.extend.search)
    - name: department
      label: Department
      type: selectAll
      mode: multiple
      placeholder: Select Department
      labelType: type-grid
      isLazyLoad: true
      _options:
        transform: $departmentsList($.extend.limit, $.extend.page, $.extend.search)
    - name: contractType
      label: Contract Type
      type: selectAll
      mode: multiple
      placeholder: Select Contract Type
      labelType: type-grid
      _options:
        transform: $contractTypeList()
    - name: job
      label: Job Title
      type: selectAll
      mode: multiple
      placeholder: Select Job Title
      labelType: type-grid
      isLazyLoad: true
      _options:
        transform: $jobList($.extend.limit, $.extend.page, $.extend.search)
    - name: settlementPeriod
      label: Tax Period
      labelType: type-grid
      type: selectAll
      mode: multiple
      placeholder: Select Tax Period
      isLazyLoad: true
      _options:
        transform: $assessmentPeriodList($.extend.limit, $.extend.page, $.extend.search)
    - name: cancelIncomeCommitment
      label: Cancel Income Commitment
      labelType: type-grid
      type: checkbox
      placeholder: Cancel Commitment
    - name: cancelledOn
      labelType: type-grid
      label: Cancelled On
      type: dateRange
      setting:
        format: dd/MM/yyyy
        type: date
    - name: updatedBy
      label: Last Updated By
      type: selectAll
      mode: multiple
      labelType: type-grid
      placeholder: Select Editor
      _options:
        transform: $userList()
    - name: updatedAt
      label: Last Updated On
      labelType: type-grid
      type: dateRange
      setting:
        format: dd/MM/yyyy
        type: date
  filterMapping:
    - field: employeeSearch
      operator: $in
      valueField: employee.(value)
    - field: jobIndicatorCode
      operator: $in
      valueField: jobIndicator.(value)
    - field: groupCode
      operator: $in
      valueField: group.(value)
    - field: companyCode
      operator: $in
      valueField: company.(value)
    - field: legalEntityCode
      operator: $in
      valueField: legalEntity.(value)
    - field: businessUnitCode
      operator: $in
      valueField: businessUnit.(value)
    - field: divisionCode
      operator: $in
      valueField: division.(value)
    - field: departmentCode
      operator: $in
      valueField: department.(value)
    - field: contractTypeCode
      operator: $in
      valueField: contractType.(value)
    - field: jobCode
      operator: $in
      valueField: job.(value)
    - field: effectiveDate
      operator: $between
      valueField: effectiveDate
    - field: assessmentPeriodCode
      operator: $in
      valueField: settlementPeriod.(value)
    - field: cancelIncomeCommitment
      operator: $eq
      valueField: cancelIncomeCommitment
    - field: cancelledOn
      operator: $between
      valueField: cancelledOn
    - field: updatedBy
      operator: $in
      valueField: updatedBy.(value)
    - field: updatedAt
      operator: $between
      valueField: updatedAt
  sources:
    groupsList:
      uri: '"/api/groups/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'filter':
        [{'field':'search','operator': '$eq','value':
        $.search},{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    companiesList:
      uri: '"/api/companies/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    legalEntitiesList:
      uri: '"/api/legal-entities/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    businessUnitList:
      uri: '"/api/business-units/get-by"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    divisionList:
      uri: '"/api/divisions/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    departmentsList:
      uri: '"/api/departments/get-by"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''status'': true}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    assessmentPeriodList:
      uri: '"/api/assessment-period"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''status'': true}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code,
        'assessmentPeriodEndDate':$item.assessmentPeriodEndDate,
        'assessmentPeriodStartDate':$item.assessmentPeriodStartDate }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    employeesList:
      uri: '"/api/personals/"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page, 'search': $.search, 'filter':
        [{'field':'employeeId','operator':
        '$eq','value':$.empId},{'field':'employeeGroupCode','operator':
        '$cont','value':$.grpCode},{'field':'employeeRecordNumber','operator':
        '$eq','value':$.ern}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label':
        $join($filter([$item.employeeId,$item.employeeGroupCode,$string($item.employeeRecordNumber),$item.fullName,$item.jobIndicatorName],
        $boolean), ' - ')  , 'value':  $item.employeeId & '_' &
        $item.employeeRecordNumber}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    contractTypeList:
      uri: '"/api/picklists/CONTRACTTYPE/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
    jobIndicatorList:
      uri: '"/api/picklists/JOBINDICATOR/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
    jobList:
      uri: '"/api/job-codes/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page, 'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    userList:
      uri: '"/api/internal-users"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.userName, 'value':
        $item.userName}})
      disabledCache: true
layout_options:
  show_dialog_form_save_add_button: true
  toolTable:
    adjustDisplay: 'true'
  is_upload_file: true
  tool_table:
    - id: import
      href: /GE/HR.FS.FR.092
    - id: export
  show_detail_history: false
  delete_multi_items: true
  is_export_grid: true
  hide_action_row: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    title: Edit
    icon: edit
    type: ghost-gray
  - id: delete
    title: Delete
    icon: trash
    type: ghost-gray
    _disabled: null
backend_url: api/income-commitment
screen_name: income-commitment
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: employeeId
    defaultName: EmployeeId
  - name: employeeRecordNumber
    defaultName: EmployeeRecordNumber
  - name: jobCode
    defaultName: JobCode
  - name: departmentCode
    defaultName: DepartmentCode
  - name: companyCode
    defaultName: CompanyCode
  - name: businessUnitCode
    defaultName: BusinessUnitCode
  - name: legalEntityCode
    defaultName: LegalEntityCode
  - name: divisionCode
    defaultName: DivisionCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Manage Income Commitment
  parent:
    title: Manage Employee Infor
