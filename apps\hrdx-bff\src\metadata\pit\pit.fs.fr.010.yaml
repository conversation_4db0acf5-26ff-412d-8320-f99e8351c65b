id: PIT.FS.FR.010
status: draft
sort: 87
user_created: 43878dbb-aa6c-4ef4-8bd7-0ea811ffafa7
date_created: '2024-07-05T07:58:44.843Z'
user_updated: cdfee544-f2ba-4ed2-ace8-5adc1ae0ed61
date_updated: '2025-06-13T06:52:21.202Z'
title: Parameter Value
requirement:
  time: 1748402554864
  blocks:
    - id: GTK8fk7CaG
      type: paragraph
      data:
        text: >-
          &nbsp;Chức năng đáp ứng các yêu cầu thêm mới, chỉnh sửa, x<PERSON>a và tìm
          kiếm danh sách thiếp lập các tham số chung của phân hệ thuế
  version: 2.30.7
screen_design: null
module: PIT
local_fields:
  - code: country
    title: Country
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    pinned: false
  - code: parameter
    title: Parameter
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
  - code: dateIssued
    title: Released Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
  - code: effectiveDate
    title: Effective Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
  - code: unit
    title: Unit
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: value
    title: Value
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Currency
      collection: field_types
  - code: note
    title: Note
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
  - code: updatedBy
    title: Last Updated By
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: updatedAt
    title: Last Updated On
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
mock_data:
  - country: Việt Nam
    parameter: Giảm trừ cho bản thân
    dateIssued: '2020-07-01'
    dateOfApplication: '2020-01-01'
    value: 11,000,000
    unit: Tiền tệ
    note: Theo nghị quyết 954/2020/UBTVQH14
    currency: VND
    createdBy: ChauPV
    createdOn: '2024-01-01 14:05:00'
    lastUpdatedBy: ChauPV
    lastUpdatedOn: '2024-01-01 14:05:00'
  - country: Việt Nam
    parameter: Giảm trừ cho người phụ thuộc
    dateIssued: '2020-07-01'
    dateOfApplication: '2020-01-01'
    value: 4,400,000
    unit: Tiền tệ
    note: Theo nghị quyết 954/2020/UBTVQH14
    currency: VND
    createdBy: ChauPV
    createdOn: '2024-01-01 14:05:00'
    lastUpdatedBy: ChauPV
    lastUpdatedOn: '2024-01-01 14:05:00'
local_buttons: null
layout: layout-table
form_config:
  formTitle:
    create: Add New Parameter Value
    edit: Edit Parameter Value
    view: Parameter Value Detail
  formSize:
    view: small
  fields:
    - type: group
      space: 12
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - name: countryObject
          label: Country
          type: select
          placeholder: Select Country
          _select:
            transform: $nationsList($.extend.limit, $.extend.page, $.extend.search)
          validators:
            - type: required
          isLazyLoad: true
        - name: parameterObject
          label: Parameter
          type: select
          placeholder: Select Parameter
          _select:
            transform: $parametersList($.extend.limit, $.extend.page, $.extend.search)
          _condition:
            transform: $.extend.formType = 'view'
          isLazyLoad: true
        - name: dateIssued
          label: Released Date
          type: dateRange
          mode: date-picker
          placeholder: Select Released Date
          _condition:
            transform: $.extend.formType = 'view'
          setting:
            format: dd/MM/yyyy
            type: date
        - name: effectiveDate
          label: Effective Date
          type: dateRange
          mode: date-picker
          placeholder: Select Effective Date
          _condition:
            transform: $.extend.formType = 'view'
          setting:
            format: dd/MM/yyyy
            type: date
        - name: unitObject
          label: Unit
          type: select
          placeholder: Select Unit
          _condition:
            transform: $.extend.formType = 'view'
          _select:
            transform: $unitsList($.extend.limit, $.extend.page, $.extend.search)
          isLazyLoad: true
        - name: value
          label: Value
          type: number
          number:
            format: currency
          _condition:
            transform: $.extend.formType = 'view'
        - name: monthDate
          label: Date
          type: dateRange
          mode: date-picker
          _condition:
            transform: >-
              $.extend.formType = 'view' and $.fields.unitObject.value
              ='UNIT_DATE'
          setting:
            format: dd/MM/yyyy
            type: date
        - type: select
          name: currencyObject
          label: Currency
          placeholder: Select Currency
          _condition:
            transform: >-
              $.extend.formType = 'view' and $.fields.unitObject.value
              ='UNIT_CURRENCY'
          _select:
            transform: $currencyList($.extend.limit, $.extend.page, $.extend.search)
          isLazyLoad: true
        - name: note
          label: Note
          type: textarea
          placeholder: Enter Note
          textarea:
            autoSize:
              minRows: 3
            maxCharCount: 1000
          validators:
            - type: maxLength
              args: 1000
              text: Maximum 1000 characters
    - type: group
      n_cols: 12
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - name: countryObject
          label: Country
          type: select
          col: 6
          _condition:
            transform: $not($.extend.formType = 'view')
          placeholder: Select Country
          _select:
            transform: $nationsList($.extend.limit, $.extend.page, $.extend.search)
          validators:
            - type: required
          isLazyLoad: true
          clearFieldsAfterChange:
            - parameterObject
        - name: parameterObject
          label: Parameter
          type: select
          col: 6
          placeholder: Select Parameter
          validators:
            - type: required
          _select:
            transform: >-
              $parametersList($.extend.limit, $.extend.page,
              $.extend.search,$.fields.countryObject.value)
          isLazyLoad: true
          _condition:
            transform: $.extend.formType = 'create' or $.extend.formType = 'edit'
        - name: dateIssued
          col: 6
          label: Released Date
          type: dateRange
          placeholder: Select Released Date
          _condition:
            transform: $.extend.formType = 'create' or $.extend.formType = 'edit'
          validators:
            - type: required
          setting:
            format: dd/MM/yyyy
            type: date
          mode: date-picker
        - name: effectiveDate
          label: Effective Date
          col: 6
          type: dateRange
          mode: date-picker
          placeholder: Select Effective Date
          _condition:
            transform: $.extend.formType = 'create' or $.extend.formType = 'edit'
          validators:
            - type: required
          setting:
            format: dd/MM/yyyy
            type: date
        - type: group
          col: 12
          fields:
            - type: group
              n_cols: 3
              _n_cols:
                transform: '$.fields.unitObject.value = ''UNIT_CURRENCY'' ? 3 : 2'
              fields:
                - name: unitObject
                  label: Unit
                  col: 1
                  type: select
                  validators:
                    - type: required
                  placeholder: Select Unit
                  _select:
                    transform: $unitsList($.extend.limit, $.extend.page, $.extend.search)
                  isLazyLoad: true
                - type: select
                  col: 1
                  name: currencyObject
                  label: Currency
                  placeholder: Select Currency
                  validators:
                    - type: required
                  _select:
                    transform: >-
                      $currencyList($.extend.limit, $.extend.page,
                      $.extend.search)
                  _condition:
                    transform: $.fields.unitObject.value ='UNIT_CURRENCY'
                  isLazyLoad: true
                - col: 1
                  name: value
                  label: Value
                  type: number
                  validators:
                    - type: required
                  number:
                    format: currency
                    max: '99999999999999'
                    precision: 4
                  placeholder: Enter Value
                  _condition:
                    transform: >-
                      $isNilorEmpty($.fields.unitObject)?true:$.fields.unitObject.value
                      !='UNIT_DATE'
                - col: 1
                  name: monthDate
                  label: Date
                  validators:
                    - type: required
                  type: dateRange
                  mode: date-picker
                  placeholder: Select Date
                  setting:
                    format: dd/MM/yyyy
                    type: date
                  _condition:
                    transform: $.fields.unitObject.value ='UNIT_DATE'
        - name: note
          label: Note
          col: 12
          type: textarea
          placeholder: Enter Note
          _condition:
            transform: $not($.extend.formType = 'view')
          textarea:
            autoSize:
              minRows: 3
            maxCharCount: 1000
          validators:
            - type: maxLength
              args: '1000'
              text: Maximum 1000 characters
  footer:
    create: true
    update: true
    createdOn: createdAt
    updatedOn: updatedAt
    createdBy: createdBy
    updatedBy: updatedBy
  sources:
    nationsList:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      queryTransform: ' {''limit'': $.limit,''search'':$.search ,  ''page'': $.page, ''filter'':[{''field'':''status'',''operator'':''$eq'',''value'':true}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    parametersList:
      uri: '"/api/pit-parameter"'
      method: GET
      queryTransform: >-
        {'limit':$.limit,'page':$.page,'search':$.search,'filter':
        [{'field':'status','operator':
        '$eq','value':true},{'field':'countryCode','operator':
        '$eq','value':$.countryCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - countryCode
    unitsList:
      uri: '"/api/picklists/UNIT/values/pagination"'
      method: GET
      queryTransform: ' {''limit'': $.limit, ''page'': $.page, ''filter'':[{''field'':''search'',''operator'':''$eq'',''value'':$.search},{''field'':''status'',''operator'':''$eq'',''value'':true}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $string($item.code)}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    currencyList:
      uri: '"/api/picklists/CURRENCY/values/pagination"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page,
        'filter':[{'field':'search','operator':'$eq','value':$.search},{'field':'status','operator':'$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
filter_config:
  fields:
    - name: country
      labelType: type-grid
      label: Country
      type: selectAll
      mode: multiple
      placeholder: Select Country
      _options:
        transform: $nationsList($.extend.limit, $.extend.page, $.extend.search)
      isLazyLoad: true
    - name: parameter
      labelType: type-grid
      label: Parameter
      type: selectAll
      mode: multiple
      placeholder: Select Parameter
      _options:
        transform: $parametersList($.extend.limit, $.extend.page, $.extend.search)
      isLazyLoad: true
    - name: dateIssued
      labelType: type-grid
      label: Released Date
      type: dateRange
      placeholder: Select Released Date
      setting:
        format: dd/MM/yyyy
        type: date
    - name: dateOfApplication
      labelType: type-grid
      label: Applied Date
      type: dateRange
      placeholder: Select Applied Date
      setting:
        format: dd/MM/yyyy
        type: date
    - name: value
      labelType: type-grid
      label: Value
      type: text
      placeholder: Enter Value
    - name: unit
      labelType: type-grid
      label: Unit
      type: selectAll
      mode: multiple
      placeholder: Select Unit
      _options:
        transform: $unitsList($.extend.limit, $.extend.page, $.extend.search)
      isLazyLoad: true
    - name: updatedBy
      label: Last Updated By
      type: selectAll
      mode: multiple
      labelType: type-grid
      placeholder: Select Editor
      _options:
        transform: $userList()
    - name: updatedAt
      labelType: type-grid
      label: Last Updated On
      type: dateRange
      placeholder: Select Last Updated On
      setting:
        format: dd/MM/yyyy
        type: date
  filterMapping:
    - field: countryCode
      operator: $in
      valueField: country.(value)
    - field: parameterCode
      operator: $in
      valueField: parameter.(value)
    - field: dateIssued
      operator: $between
      valueField: dateIssued
    - field: dateOfApplication
      operator: $between
      valueField: dateOfApplication
    - field: value
      operator: $eq
      valueField: value
    - field: unitCode
      operator: $in
      valueField: unit.(value)
    - field: updatedBy
      operator: $in
      valueField: updatedBy.(value)
    - field: updatedAt
      operator: $between
      valueField: updatedAt
  sources:
    nationsList:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      queryTransform: ' {''limit'': $.limit, ''page'': $.page, ''search'':$.search,  ''filter'':[{''field'':''status'',''operator'':''$eq'',''value'':true}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    parametersList:
      uri: '"/api/pit-parameter"'
      method: GET
      queryTransform: >-
        {'limit':$.limit,'page':$.page,'search':$.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    unitsList:
      uri: '"/api/picklists/UNIT/values/pagination"'
      method: GET
      queryTransform: ' {''limit'': $.limit, ''page'': $.page, ''filter'':[{''field'':''search'',''operator'':''$eq'',''value'':$.search},{''field'':''status'',''operator'':''$eq'',''value'':true}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $string($item.code)}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    currencyList:
      uri: '"/api/picklists/CURRENCY/values/pagination"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page,
        'filter':[{'field':'search','operator':'$eq','value':$.search},{'field':'status','operator':'$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    userList:
      uri: '"/api/internal-users"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.userName, 'value':
        $item.userName}})
      disabledCache: true
layout_options:
  show_dialog_form_save_add_button: true
  toolTable:
    adjustDisplay: 'true'
  tool_table:
    - id: export
  show_detail_history: false
  view_after_created: false
  delete_multi_items: true
  is_export_grid: true
  hide_action_row: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    title: Edit
    icon: icon-pencil-simple-bold
    type: ghost-gray
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: ghost-gray
backend_url: /api/sub-system-for-pit-params
screen_name: sub-system-for-pit-params
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: countryCode
    defaultName: CountryCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Parameter Value
  parent:
    title: General Setting
