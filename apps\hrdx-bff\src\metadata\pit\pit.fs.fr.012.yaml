id: PIT.FS.FR.012
status: draft
sort: 122
user_created: 43878dbb-aa6c-4ef4-8bd7-0ea811ffafa7
date_created: '2024-07-27T06:56:38.476Z'
user_updated: cdfee544-f2ba-4ed2-ace8-5adc1ae0ed61
date_updated: '2025-07-24T02:10:30.167Z'
title: Organizational Tax Group
requirement:
  time: 1747369354406
  blocks:
    - id: 5h_ncc8i76
      type: paragraph
      data:
        text: >-
          Chứ<PERSON> năng cho phép bộ phận nhân sự tập đoàn/CTTV thiết lập xác định
          nhóm QTT cho các pháp nhân thuộc đơn vị
  version: 2.30.7
screen_design: null
module: PIT
local_fields:
  - code: effectiveDate
    title: Effective Start Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DateHyperlink
      collection: field_types
    pinned: true
  - code: effectiveDateTo
    title: Effective End Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DateHyperlink
      collection: field_types
  - code: groupLabel
    title: Group
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: companyLabel
    title: Company
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: legalEntitiesName
    title: Legal Entity
    data_type: null
    display_type:
      key: Tooltip
      collection: field_types
    extra_config:
      singular: Legal Entity
      plural: Legal Entities
  - code: groupTaxSettlementLabel
    title: Tax Settlement Group
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: note
    title: Note
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
  - code: updatedBy
    title: Last Updated By
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: updatedAt
    title: Last Updated On
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
mock_data:
  - country: Vietnam
    effectiveDate: '2024-01-01'
    group: FPT
    company: FPT IS
    legalEntity: FPT IS HCM
    taxSettlementGroup: FPT IS HCM
    note: Create data test
    createdOn: '2024-01-01 12:00:00'
    createdBy: Phuong Bui
    lastUpdatedBy: Khanh Vy
    lastUpdatedOn: '2024-01-01 12:00:00'
  - country: Vietnam
    effectiveDate: '2024-01-01'
    group: FPT
    company: FSOFT
    legalEntity: FSOFT HN
    taxSettlementGroup: FSOFT HN
    note: Create data test
    createdOn: '2023-04-05 12:00:00'
    createdBy: Phuong Bui
    lastUpdatedBy: Khanh Vy
    lastUpdatedOn: '2024-01-05 12:00:00'
local_buttons: null
layout: layout-table
form_config:
  formTitle:
    create: Add New Organizational Tax Group
    edit: Edit Organizational Tax Group
    view: Organizational Tax Group Detail
  formSize:
    create: large
    edit: large
    view: small
  fields:
    - type: group
      _condition:
        transform: $not($.extend.formType = 'view')
      n_cols: 2
      fields:
        - name: effectiveDate
          label: Effective Start Date
          type: dateRange
          setting:
            format: dd/MM/yyyy
            type: date
          validators:
            - type: required
          mode: date-picker
          _value:
            transform: $isNilorEmpty($.extend.defaultValue.effectiveDate) ? $now()
        - name: effectiveDateTo
          label: Effective End Date
          type: dateRange
          setting:
            format: dd/MM/yyyy
            type: date
          mode: date-picker
        - type: ppx-custom
          args:
            transform: >-
              $exists($.fields.endDate) and
              $DateDiff($DateFormat($.fields.endDate, 'yyyy-MM-DD'),
              $DateFormat($.fields.startDate, 'yyyy-MM-DD'), 'd') < 1
          text: End date must be greater than start date
        - name: groupTaxSettlement
          label: Tax Settlement Group
          type: select
          placeholder: Select Tax Settlement Group
          clearFieldsAfterChange:
            - group
            - company
            - legalEntitiesCode
          validators:
            - type: required
          isLazyLoad: true
          _select:
            transform: >-
              $taxSettlementList($.extend.limit, $.extend.page, $.extend.search,
              $.fields.effectiveDate)
        - name: group
          label: Group
          type: select
          placeholder: Select Group
          clearFieldsAfterChange:
            - company
            - legalEntitiesCode
          validators:
            - type: required
          _value:
            transform: >-
              $not($isNilorEmpty($.fields.groupTaxSettlement))?
              $getCompaniesByCode($.fields.groupTaxSettlement.companyCode,
              $.fields.effectiveDate)[0].groupObject
          _select:
            transform: >-
              $groupsList($.extend.limit, $.extend.page, $.extend.search,
              $.fields.effectiveDate)
          _condition:
            transform: $.extend.formType = 'create'
          isLazyLoad: true
        - name: group
          label: Group
          type: select
          placeholder: Select Group
          clearFieldsAfterChange:
            - company
            - legalEntitiesCode
          validators:
            - type: required
          _select:
            transform: >-
              $groupsList($.extend.limit, $.extend.page, $.extend.search,
              $.fields.effectiveDate)
          _condition:
            transform: $.extend.formType = 'edit'
          isLazyLoad: true
        - name: company
          label: Company
          type: select
          placeholder: Select Company
          clearFieldsAfterChange:
            - legalEntitiesCode
          validators:
            - type: required
          _value:
            transform: >-
              $not($isNilorEmpty($.fields.groupTaxSettlement))?
              $.fields.groupTaxSettlement.companyObject
          _select:
            transform: >-
              $isNilorEmpty( $.fields.group) ? [] :
              $companiesList($.extend.limit, $.extend.page, $.extend.search,
              $.fields.effectiveDate, $.fields.group.value)
          isLazyLoad: true
        - name: legalEntitiesCode
          label: Legal Entity
          type: selectAll
          placeholder: Select Legal Entity
          mode: multiple
          validators:
            - type: required
          _options:
            transform: >-
              ($.fields.company; $isNilorEmpty( $.variables._selectedCompany.id)
              ?  [] :  $legalEntitiesList($.fields.effectiveDate,
              $.variables._selectedCompany.id))
    - type: group
      _condition:
        transform: $.extend.formType = 'view'
      space: 12
      fields:
        - name: effectiveDate
          label: Effective Start Date
          type: dateRange
          placeholder: Select Effective Date
          setting:
            format: dd/MM/yyyy
            type: date
          _condition:
            transform: $.extend.formType = 'view'
          mode: date-picker
        - name: effectiveDateTo
          label: Effective End Date
          type: dateRange
          setting:
            format: dd/MM/yyyy
            type: date
          _condition:
            transform: $.extend.formType = 'view'
          mode: date-picker
        - name: groupTaxSettlement
          label: Tax Settlement Group
          type: select
          placeholder: Select Tax Settlement Group
          _condition:
            transform: $.extend.formType = 'view'
          _select:
            transform: >-
              $taxSettlementList($.extend.limit, $.extend.page, $.extend.search,
              $.fields.effectiveDate)
        - name: group
          label: Group
          type: select
          placeholder: Select Group
          clearFieldsAfterChange:
            - company
            - legalEntitiesCode
          _select:
            transform: >-
              $groupsList($.extend.limit, $.extend.page, $.extend.search,
              $.fields.effectiveDate)
          isLazyLoad: true
          _condition:
            transform: $.extend.formType = 'view'
        - name: company
          label: Company
          type: select
          clearFieldsAfterChange:
            - legalEntitiesCode
          placeholder: Select Company
          _select:
            transform: >-
              $isNilorEmpty( $.fields.group) ? [] :
              $companiesList($.extend.limit, $.extend.page, $.extend.search,
              $.fields.effectiveDate, $.fields.group.value)
          isLazyLoad: true
          _condition:
            transform: $.extend.formType = 'view'
        - name: legalEntitiesCode
          label: Legal Entity
          type: selectAll
          placeholder: Select Legal Entity
          mode: multiple
          _condition:
            transform: $.extend.formType = 'view'
          validators:
            - type: required
          _options:
            transform: >-
              ($.fields.company; $isNilorEmpty( $.variables._selectedCompany.id)
              ?  [] :  $legalEntitiesList($.fields.effectiveDate,
              $.variables._selectedCompany.id))
    - name: note
      label: Note
      type: textarea
      placeholder: Enter Note
      textarea:
        autoSize:
          minRows: 3
        maxCharCount: 1000
      validators:
        - type: maxLength
          args: 1000
          text: Maximum 1000 characters
  sources:
    taxSettlementList:
      uri: '"/api/group-tax-settlement"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field': 'status', 'operator': '$eq', 'value': true },
        {'field':'effectiveDate','operator': '$lte','value':
        $.effectiveDate},{'field':'effectiveDateTo','operator':'$eq','value':'NULL'},{'field':'effectiveDateTo','operator':'$gt','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code,'companyCode':$item.companyCode,
        'companyObject':{'label':$item.companyName, 'value':$item.companyCode},
        'id': $item.id}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
    groupsList:
      uri: '"/api/groups/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page ,'filter':
        [{'field':'search','operator':
        '$eq','value':$.search},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
    companiesList:
      uri: '"/api/companies/get-by"'
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'effectiveDate','operator': '$eq','value':
        $exists($.effectiveDate)? $.effectiveDate :
        $now()},{'field':'groupCode','operator':
        '$eq','value':$.groupCode},{'field':'status','operator':
        '$eq','value':true}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
        - groupCode
    getCompaniesByCode:
      uri: '"/api/companies/get-by"'
      queryTransform: >-
        {'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$exists($.effectiveDate)?$.effectiveDate :
        $now()},{'field':'code','operator':
        '$eq','value':$.code},{'field':'status','operator':
        '$eq','value':true}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id,
        'groupObject':{'label':$item.groupName, 'value':$item.groupCode}  }})[]
      disabledCache: true
      params:
        - code
        - effectiveDate
    legalEntitiesList:
      uri: '"/api/legal-entities/get-list"'
      queryTransform: >-
        {'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$exists($.effectiveDate) and
        $boolean($.effectiveDate)?$.effectiveDate:$now()},{'field':'companyId','operator':
        '$eq','value':[$.companyId]},{'field':'status','operator':
        '$eq','value':true}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - effectiveDate
        - companyId
  variables:
    _selectedCompany:
      transform: >-
        $isNilorEmpty( $.fields.company.value) ? null :
        $getCompaniesByCode($.fields.company.value, $.fields.effectiveDate)[0]
  footer:
    create: true
    update: true
    createdOn: createdAt
    updatedOn: updatedAt
    createdBy: createdBy
    updatedBy: updatedBy
filter_config:
  fields:
    - name: effectiveDate
      labelType: type-grid
      label: Effective Start Date
      type: dateRange
      setting:
        format: dd/MM/yyyy
        type: date
    - name: effectiveDateTo
      labelType: type-grid
      label: Effective End Date
      type: dateRange
      setting:
        format: dd/MM/yyyy
        type: date
    - name: group
      labelType: type-grid
      label: Group
      type: selectAll
      mode: multiple
      placeholder: Select Group
      _options:
        transform: $groupsList($.extend.limit, $.extend.page, $.extend.search)
      isLazyLoad: true
    - name: company
      labelType: type-grid
      label: Company
      type: selectAll
      mode: multiple
      placeholder: Select Company
      _options:
        transform: $companiesList($.extend.limit, $.extend.page, $.extend.search)
      isLazyLoad: true
    - name: legalEntity
      labelType: type-grid
      label: Legal Entity
      mode: multiple
      placeholder: Select Legal Entity
      type: selectAll
      _options:
        transform: $legalEntitiesList($.extend.limit, $.extend.page, $.extend.search)
      isLazyLoad: true
    - name: groupTaxSettlement
      labelType: type-grid
      label: Tax Settlement Group
      mode: multiple
      placeholder: Select Tax Settlement Group
      type: selectAll
      _options:
        transform: $taxSettlementList($.extend.limit, $.extend.page, $.extend.search)
      isLazyLoad: true
    - name: updatedBy
      label: Last Updated By
      type: selectAll
      mode: multiple
      labelType: type-grid
      placeholder: Select Editor
      _options:
        transform: $userList()
    - name: updatedAt
      labelType: type-grid
      label: Last Updated On
      type: dateRange
      setting:
        format: dd/MM/yyyy
        type: date
  filterMapping:
    - field: countryCode
      operator: $in
      valueField: country.(value)
    - field: effectiveDate
      operator: $between
      valueField: effectiveDate
    - field: effectiveDateTo
      operator: $between
      valueField: effectiveDateTo
    - field: groupCode
      operator: $in
      valueField: group.(value)
    - field: companyCode
      operator: $in
      valueField: company.(value)
    - field: legalEntitiesCode
      operator: $in
      valueField: legalEntity.(value)
    - field: groupTaxSettlementCode
      operator: $in
      valueField: groupTaxSettlement.(value)
    - field: updatedBy
      operator: $in
      valueField: updatedBy.(value)
    - field: updatedAt
      operator: $between
      valueField: updatedAt
  sources:
    nationsList:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      queryTransform: ' {''limit'': $.limit, ''page'': $.page, ''search'': $.search, ''filter'':[{''field'':''status'',''operator'':''$eq'',''value'':true}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    taxSettlementList:
      uri: '"/api/group-tax-settlement"'
      method: GET
      queryTransform: ' {''limit'': $.limit, ''page'': $.page, ''filter'':[{''field'':''search'',''operator'':''$eq'',''value'':$.search},{''field'':''status'',''operator'':''$eq'',''value'':true}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    groupsList:
      uri: '"/api/groups"'
      queryTransform: ' {''limit'': $.limit, ''page'': $.page, ''filter'':[{''field'':''search'',''operator'':''$eq'',''value'':$.search},{''field'':''status'',''operator'':''$eq'',''value'':true}]}'
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    companiesList:
      uri: '"/api/companies"'
      queryTransform: ' {''limit'': $.limit, ''page'': $.page, ''filter'':[{''field'':''search'',''operator'':''$eq'',''value'':$.search},{''field'':''status'',''operator'':''$eq'',''value'':true}]}'
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    legalEntitiesList:
      uri: '"/api/legal-entities"'
      queryTransform: ' {''limit'': $.limit, ''page'': $.page, ''filter'':[{''field'':''search'',''operator'':''$eq'',''value'':$.search},{''field'':''status'',''operator'':''$eq'',''value'':true}]}'
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    userList:
      uri: '"/api/internal-users"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.userName, 'value':
        $item.userName}})
      disabledCache: true
layout_options:
  show_dialog_form_save_add_button: true
  toolTable:
    adjustDisplay: 'true'
  tool_table:
    - id: export
  show_detail_history: false
  delete_multi_items: true
  is_export_grid: true
  hide_action_row: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    icon: icon-pencil-simple
    type: ghost-gray
  - id: delete
    icon: icon-trash
    type: ghost-gray
backend_url: /api/group-tax-settlement-organization
screen_name: set-up-structure-based-principles-for-tax-settlement-group
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: companyCode
    defaultName: CompanyCode
  - name: groupTaxSettlementCode
    defaultName: GroupTaxSettlementCode
  - name: legalEntityCode
    defaultName: LegalEntityCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Organizational Tax Group
  parent:
    title: Entity Setting
