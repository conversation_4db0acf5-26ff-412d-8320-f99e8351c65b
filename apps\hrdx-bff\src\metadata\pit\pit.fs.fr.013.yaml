id: PIT.FS.FR.013
status: draft
sort: 123
user_created: 43878dbb-aa6c-4ef4-8bd7-0ea811ffafa7
date_created: '2024-07-27T07:13:17.829Z'
user_updated: cdfee544-f2ba-4ed2-ace8-5adc1ae0ed61
date_updated: '2025-07-17T10:23:02.751Z'
title: Tax Calculation Calendar
requirement:
  time: 1748343143488
  blocks:
    - id: 8Oynlp7lNN
      type: paragraph
      data:
        text: >-
          Chức năng đáp ứng các yêu cầu thêm mới, chỉnh sửa, xóa và tìm kiếm
          danh sách kỳ tính thuế tương ứng với nghiệp vụ của từng CTTV
    - id: ag6CKqn09e
      type: paragraph
      data:
        text: >-
          <PERSON><PERSON> thống cho phép admin thực hiện c<PERSON><PERSON>, chỉnh sửa caption của các
          trường thông tin trên gia<PERSON>, ti<PERSON><PERSON> đề của các cột dưới lưới và các
          nút chức năng tại MH chính và các popup
  version: 2.30.7
screen_design: null
module: PIT
local_fields:
  - code: code
    title: Code
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    pinned: true
    show_sort: true
  - code: shortName
    title: Short Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
  - code: longName
    title: Long Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
  - code: countryName
    title: Country
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: groupName
    title: Group
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: companyName
    title: Company
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: taxSettlementGroupName
    title: Tax Settlement Group
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: periodName
    title: Period
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: yearName
    title: Year
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: assessmentPeriodName
    title: Tax Period
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
  - code: startDate
    title: Start Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
  - code: endDate
    title: End Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
  - code: currency
    title: Currency
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: note
    title: Note
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
  - code: updatedBy
    title: Last Updated By
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: updatedAt
    title: Last Updated On
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
mock_data:
  - declarationPeriod: FPT IS HN_01/2023
    period: Tháng
    startDate: '2023-01-01'
    endDate: '2023-01-31'
    country: Viet Nam
    company: FPT
    taxSettlementGroup: FPT IS HN
    belongsToSettlementPeriod: FPT IS HN_2023
    taxFormula: Công thức tiêu chuẩn
    note: Dữ liệu test
    createdBy: Phuong Bui
    createdOn: '2023-01-01 10:24:12'
    lastUpdatedBy: Khanh Vy
    lastUpdatedOn: '2023-01-01 10:24:12'
  - declarationPeriod: FPT IS HN_02/2023
    period: Tháng
    startDate: '2023-02-01'
    endDate: '2023-02-28'
    country: Viet Nam
    company: FPT
    taxSettlementGroup: FPT IS HN
    belongsToSettlementPeriod: FPT IS HN_2023
    taxFormula: Công thức tiêu chuẩn
    note: Dữ liệu test
    createdBy: Phuong Bui
    createdOn: '2023-01-01 10:24:12'
    lastUpdatedBy: Khanh Vy
    lastUpdatedOn: '2023-01-01 10:24:12'
  - declarationPeriod: FPT IS HN_03/2023
    period: Tháng
    startDate: '2023-03-01'
    endDate: '2023-03-31'
    country: Viet Nam
    company: FPT
    taxSettlementGroup: FPT IS HN
    belongsToSettlementPeriod: FPT IS HN_2023
    taxFormula: Công thức tiêu chuẩn
    note: Dữ liệu test
    createdBy: Phuong Bui
    createdOn: '2023-01-01 10:24:12'
    lastUpdatedBy: Khanh Vy
    lastUpdatedOn: '2023-01-01 10:24:12'
  - declarationPeriod: FPT IS HN_04/2023
    period: Tháng
    startDate: '2023-04-01'
    endDate: '2023-04-30'
    country: Viet Nam
    company: FPT
    taxSettlementGroup: FPT IS HN
    belongsToSettlementPeriod: FPT IS HN_2023
    taxFormula: Công thức tiêu chuẩn
    note: Dữ liệu test
    createdBy: Phuong Bui
    createdOn: '2023-01-01 10:24:12'
    lastUpdatedBy: Khanh Vy
    lastUpdatedOn: '2023-01-01 10:24:12'
  - declarationPeriod: FPT IS HN_05/2023
    period: Tháng
    startDate: '2023-05-01'
    endDate: '2023-05-31'
    country: Viet Nam
    company: FPT
    taxSettlementGroup: FPT IS HN
    belongsToSettlementPeriod: FPT IS HN_2023
    taxFormula: Công thức tiêu chuẩn
    note: Dữ liệu test
    createdBy: Phuong Bui
    createdOn: '2023-01-01 10:24:12'
    lastUpdatedBy: Khanh Vy
    lastUpdatedOn: '2023-01-01 10:24:12'
  - declarationPeriod: FPT IS HN_06/2023
    period: Tháng
    startDate: '2023-06-01'
    endDate: '2023-06-30'
    country: Viet Nam
    company: FPT
    taxSettlementGroup: FPT IS HN
    belongsToSettlementPeriod: FPT IS HN_2023
    taxFormula: Công thức tiêu chuẩn
    note: Dữ liệu test
    createdBy: Phuong Bui
    createdOn: '2023-01-01 10:24:12'
    lastUpdatedBy: Khanh Vy
    lastUpdatedOn: '2023-01-01 10:24:12'
  - declarationPeriod: FPT IS HN_07/2023
    period: Tháng
    startDate: '2023-07-01'
    endDate: '2023-07-31'
    country: Viet Nam
    company: FPT
    taxSettlementGroup: FPT IS HN
    belongsToSettlementPeriod: FPT IS HN_2023
    taxFormula: Công thức tiêu chuẩn
    note: Dữ liệu test
    createdBy: Phuong Bui
    createdOn: '2023-01-01 10:24:12'
    lastUpdatedBy: Khanh Vy
    lastUpdatedOn: '2023-01-01 10:24:12'
  - declarationPeriod: FPT IS HN_08/2023
    period: Tháng
    startDate: '2023-08-01'
    endDate: '2023-08-31'
    country: Viet Nam
    company: FPT
    taxSettlementGroup: FPT IS HN
    belongsToSettlementPeriod: FPT IS HN_2023
    taxFormula: Công thức tiêu chuẩn
    note: Dữ liệu test
    createdBy: Phuong Bui
    createdOn: '2023-01-01 10:24:12'
    lastUpdatedBy: Khanh Vy
    lastUpdatedOn: '2023-01-01 10:24:12'
  - declarationPeriod: FPT IS HN_09/2023
    period: Tháng
    startDate: '2023-09-01'
    endDate: '2023-09-30'
    country: Viet Nam
    company: FPT
    taxSettlementGroup: FPT IS HN
    belongsToSettlementPeriod: FPT IS HN_2023
    taxFormula: Công thức tiêu chuẩn
    note: Dữ liệu test
    createdBy: Phuong Bui
    createdOn: '2023-01-01 10:24:12'
    lastUpdatedBy: Khanh Vy
    lastUpdatedOn: '2023-01-01 10:24:12'
  - declarationPeriod: FPT IS HN_10/2023
    period: Tháng
    startDate: '2023-10-01'
    endDate: '2023-10-31'
    country: Viet Nam
    company: FPT
    taxSettlementGroup: FPT IS HN
    belongsToSettlementPeriod: FPT IS HN_2023
    taxFormula: Công thức tiêu chuẩn
    note: Dữ liệu test
    createdBy: Phuong Bui
    createdOn: '2023-01-01 10:24:12'
    lastUpdatedBy: Khanh Vy
    lastUpdatedOn: '2023-01-01 10:24:12'
local_buttons: null
layout: layout-table
form_config:
  formTitle:
    create: Add New Tax Calculation Calendar
    edit: Edit Tax Calculation Calendar
    view: Tax Calculation Calendar Detail
  formSize:
    view: small
    create: largex
    edit: largex
  fields:
    - type: group
      space: 12
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - name: code
          type: text
          label: Code
          disabled: true
          _condition:
            transform: >-
              $.extend.formType = 'proceed' or $.extend.formType = 'view' or
              $.extend.formType = 'edit'
        - name: shortName
          label: Short Name
          type: translation
          placeholder: Enter Short Name
          _condition:
            transform: $.extend.formType = 'view'
        - name: longName
          label: Long Name
          type: translation
          placeholder: Enter Long Name
          _condition:
            transform: $.extend.formType = 'view'
        - name: countryNameCode
          label: Country
          type: text
        - name: groupNameCode
          label: Group
          type: text
        - name: companyNameCode
          label: Company
          type: text
        - name: taxSettlementGroupNameCode
          label: Tax Settlement Group
          type: text
        - name: periodNameCode
          label: Period
          type: text
        - type: dateRange
          label: Year
          name: year
          mode: date-picker
          setting:
            format: YYYY
            type: date
        - type: dateRange
          label: Start Date
          name: startDate
          mode: date-picker
        - type: dateRange
          label: End Date
          name: endDate
          mode: date-picker
        - label: Tax Period
          name: assessmentPeriodNameCode
          type: text
        - label: Currency
          name: currencyNameCode
          type: text
    - name: codeAutoGenerate
      label: Code
      type: text
      disabled: true
      _value:
        transform: '''System – Generated'''
      _condition:
        transform: $.extend.formType = 'create'
    - name: code
      type: text
      label: Code
      disabled: true
      _condition:
        transform: $.extend.formType = 'proceed' or $.extend.formType = 'edit'
    - name: shortName
      label: Short Name
      type: translation
      placeholder: Enter Short Name
      validators:
        - type: required
        - type: maxLength
          args: '300'
          text: Maximum 300 characters
      _condition:
        transform: $not($.extend.formType = 'view')
    - name: longName
      label: Long Name
      type: translation
      placeholder: Enter Long Name
      validators:
        - type: required
        - type: maxLength
          args: '500'
          text: Maximum 500 characters
      _condition:
        transform: $not($.extend.formType = 'view')
    - type: group
      n_cols: 2
      fields:
        - name: countryCode
          label: Country
          type: select
          outputValue: value
          placeholder: Select Country
          _condition:
            transform: $not($.extend.formType = 'view')
          clearFieldsAfterChange:
            - groupTaxSettlementCode
          _select:
            transform: $countryList()
          validators:
            - type: required
        - type: select
          name: groupCode
          label: Group
          placeholder: Select Group
          clearFieldsAfterChange:
            - companyCode
            - groupTaxSettlementCode
          _select:
            transform: $groupsList($.extend.page,$.extend.limit,$.extend.search)
          validators:
            - type: required
          outputValue: value
          isLazyLoad: true
          _condition:
            transform: $not($.extend.formType = 'view')
        - type: select
          name: companyCode
          label: Company
          outputValue: value
          clearFieldsAfterChange:
            - groupTaxSettlementCode
          _condition:
            transform: $not($.extend.formType = 'view')
          validators:
            - type: required
          placeholder: Select Company
          isLazyLoad: true
          _select:
            transform: >-
              $boolean($.fields.groupCode)?$companiesList($.fields.effectiveDate,
              $.fields.groupCode,$.extend.page,$.extend.limit,$.extend.search):[]
        - type: select
          name: groupTaxSettlementCode
          label: Tax Settlement Group
          outputValue: value
          _condition:
            transform: $not($.extend.formType = 'view')
          validators:
            - type: required
          placeholder: Select Tax Settlement Group
          isLazyLoad: true
          _select:
            transform: >-
              $not($isNilorEmpty($.fields.companyCode) or
              $isNilorEmpty($.fields.groupCode))? $tsgList($.fields.groupCode,
              $.fields.companyCode,
              $.fields.countryCode,$.extend.page,$.extend.limit,$.extend.search)
              : []
        - type: select
          label: Tax Period
          name: assessmentPeriodCode
          placeholder: Select Tax Period
          outputValue: value
          _condition:
            transform: $not($.extend.formType = 'view')
          validators:
            - type: required
          clearFieldsAfterChange:
            - assessmentPeriodCodeInvisible
          isLazyLoad: true
          _select:
            transform: >-
              $assessmentPeriodList($.extend.page,$.extend.limit,$.extend.search)
        - name: assessmentPeriodCodeInvisible
          type: select
          unvisible: true
          _value:
            transform: >-
              $exists($.variables.selectedAssessmentPeriod) and
              $boolean($.variables.selectedAssessmentPeriod)?$.variables.selectedAssessmentPeriod:undefined
        - type: select
          label: Currency
          name: currencyCode
          placeholder: Select Currency
          outputValue: value
          validators:
            - type: required
          _select:
            transform: $currencyList()
          _condition:
            transform: $not($.extend.formType = 'view')
    - type: translationTextArea
      label: Note
      name: note
      placeholder: Enter Note
      textarea:
        autoSize:
          minRows: 3
        maxCharCount: 1000
      validators:
        - type: maxLength
          args: '1000'
          text: Maximum 1000 characters
  overview:
    dependentField: assessmentPeriodCodeInvisible
    _condition:
      transform: $.extend.formType = 'create' or $.extend.formType = 'edit'
    border: true
    _title:
      transform: >-
        ($selectedItem := $.variables.selectedAssessmentPeriod[0]; $title :=
        $selectedItem.label; $title ? $title : 'Tax Period Detail')
    noDataMessages: Choose Tax Period getting data
    uri: ''
    display:
      - label: Period
        _value:
          transform: >-
            $exists($.variables.selectedAssessmentPeriod)?$.variables.selectedAssessmentPeriod.period:'--'
      - label: Year
        _value:
          transform: >-
            $exists($.variables.selectedAssessmentPeriod)?$.variables.selectedAssessmentPeriod.yearName:'--'
      - label: Start Date
        _value:
          transform: >-
            $exists($.variables.selectedAssessmentPeriod)?$DateFormat($.variables.selectedAssessmentPeriod.assessmentPeriodStartDate,'DD/MM/YYYY'):'--'
      - label: End Date
        _value:
          transform: >-
            $exists($.variables.selectedAssessmentPeriod)?$DateFormat($.variables.selectedAssessmentPeriod.assessmentPeriodEndDate,'DD/MM/YYYY'):'--'
  sources:
    assessmentPeriodList:
      uri: '"/api/assessment-period"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator':
        '$eq','value':true},{'field':'code','operator':
        '$eq','value':$.assessmentPeriodCode},{'field':'year','operator':
        '$eq','value':$.year},{'field':'assessmentPeriodTypeCode','operator':
        '$eq','value':'APT_TAX_RETURN'},{'field':'periodCode','operator':
        '$eq','value':$.periodCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value':
        $item.code,'assessmentPeriodStartDate':$item.assessmentPeriodStartDate,'assessmentPeriodEndDate':$item.assessmentPeriodEndDate,'period':$item.period,'yearName':$item.yearName
        }})[]
      disabledCache: true
      params:
        - page
        - limit
        - search
        - assessmentPeriodCode
    countryList:
      uri: '"/api/picklists/COUNTRIES/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
    groupsList:
      uri: '"/api/groups/get-by"'
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id}})[]
      disabledCache: true
      params:
        - page
        - limit
        - search
        - effectiveDate
    companiesList:
      uri: '"/api/companies/get-by"'
      queryTransform: >-
        {'limit': $.limit,'page': $.page, 'search': $.search, 'filter':
        [{'field':'status','operator':
        '$eq','value':true},{'field':'groupCode','operator': '$eq','value':
        $.groupCode }, {'field':'effectiveDate','operator': '$eq','value':
        $.effectiveDate}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id}})[]
      disabledCache: true
      params:
        - effectiveDate
        - groupCode
        - page
        - limit
        - search
    periodCodesList:
      uri: '"/api/picklists/PERIOD/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $string($item.code)}})[]
      disabledCache: true
    currencyList:
      uri: '"/api/picklists/CURRENCY/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $string($item.code) & ')', 'value': $string($item.code)}})[]
      disabledCache: true
    tsgList:
      uri: '"/api/group-tax-settlement/by-establish"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page, 'search': $.search,
        'filter':[{'field':'status','operator':
        '$eq','value':true},{'field':'groupCode','operator':
        '$eq','value':$.groupCode},{'field':'countryCode','operator':
        '$eq','value':$.countryCode},{'field':'companyCode','operator':
        '$eq','value':$.companyCode}]}
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - groupCode
        - companyCode
        - countryCode
        - page
        - limit
        - search
  variables:
    selectedAssessmentPeriod:
      transform: >-
        ($not($isNilorEmpty($.fields.assessmentPeriodCode))
        ?$assessmentPeriodList(1,1,'',$.fields.assessmentPeriodCode))
  footer:
    create: true
    update: true
    createdOn: createdAt
    updatedOn: updatedAt
    createdBy: createdBy
    updatedBy: updatedBy
filter_config:
  fields:
    - name: code
      labelType: type-grid
      label: Code
      type: text
      placeholder: Enter Code
    - name: shortName
      labelType: type-grid
      label: Short Name
      type: text
      placeholder: Enter Short Name
    - name: longName
      labelType: type-grid
      label: Long Name
      type: text
      placeholder: Enter Long Name
    - name: country
      labelType: type-grid
      label: Country
      type: selectAll
      placeholder: Select Country
      _options:
        transform: $countriesList()
    - name: group
      labelType: type-grid
      label: Group
      type: selectAll
      placeholder: Select Group
      _options:
        transform: $groupsList()
    - name: company
      labelType: type-grid
      label: Company
      type: selectAll
      placeholder: Select Company
      _options:
        transform: $companyList()
    - name: tax
      labelType: type-grid
      label: Tax Settlement Group
      type: selectAll
      placeholder: Select Tax Settlement Group
      isLazyLoad: true
      _options:
        transform: $tsgList($.extend.limit, $.extend.page, $.extend.search)
    - name: period
      labelType: type-grid
      label: Period
      type: selectAll
      placeholder: Select Period
      _options:
        transform: $periodCodesList()
    - type: dateRange
      label: Year
      name: year
      labelType: type-grid
      placeholder: Select Year
      setting:
        format: yyyy
        type: year
    - name: assessmentPeriod
      labelType: type-grid
      label: Tax Period
      type: selectAll
      placeholder: Select Tax Period
      isLazyLoad: true
      _options:
        transform: $assessmentPeriodList($.extend.limit, $.extend.page, $.extend.search)
    - name: startDate
      labelType: type-grid
      label: Start Date
      type: dateRange
      setting:
        format: dd/MM/yyyy
        type: date
    - name: endDate
      labelType: type-grid
      label: End Date
      type: dateRange
      setting:
        format: dd/MM/yyyy
        type: date
    - name: currencyCode
      labelType: type-grid
      label: Currency
      type: selectAll
      placeholder: Select Currency
      _options:
        transform: $currencyList()
    - labelType: type-grid
      name: updatedBy
      label: Last Updated By
      type: selectAll
      placeholder: Select Last Updated By
      _options:
        transform: $userList()
    - type: dateRange
      label: Last Updated On
      name: updatedAt
      labelType: type-grid
  filterMapping:
    - field: code
      operator: $cont
      valueField: code
    - field: shortName
      operator: $cont
      valueField: shortName
    - field: longName
      operator: $cont
      valueField: longName
    - field: countryCode
      operator: $in
      valueField: country.(value)
    - field: groupCode
      operator: $in
      valueField: group.(value)
    - field: companyCode
      operator: $in
      valueField: company.(value)
    - field: groupTaxSettlementCode
      operator: $in
      valueField: tax.(value)
    - field: currencyCode
      operator: $in
      valueField: currencyCode.(value)
    - field: periodCode
      operator: $in
      valueField: period.(value)
    - field: year
      operator: $between
      valueField: year
    - field: assessmentPeriodCode
      operator: $in
      valueField: assessmentPeriod.(value)
    - field: startDate
      operator: $between
      valueField: startDate
    - field: endDate
      operator: $between
      valueField: endDate
    - field: updatedBy
      operator: $in
      valueField: updatedBy.(value)
    - field: updatedAt
      operator: $between
      valueField: updatedAt
  sources:
    currencyList:
      uri: '"/api/picklists/CURRENCY/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $string($item.code) & ')', 'value': $string($item.code)}})[]
      disabledCache: true
    assessmentPeriodList:
      uri: '"/api/assessment-period"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page, 'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')','value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    countriesList:
      uri: '"/api/picklists/COUNTRIES/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
    groupsList:
      uri: '"/api/groups/by"'
      method: GET
      queryTransform: '{''filter'': [{''field'':''status'',''operator'': ''$eq'',''value'':true}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'additionalData': $item}})[]
      disabledCache: true
    companyList:
      uri: '"/api/companies/by"'
      method: GET
      queryTransform: '{''filter'': [{''field'':''status'',''operator'': ''$eq'',''value'':true}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code,  'additionalData': $item}})[]
      disabledCache: true
    tsgList:
      uri: '"/api/group-tax-settlement"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page, 'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    periodCodesList:
      uri: '"/api/picklists/PERIOD/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $string($item.code)}})[]
      disabledCache: true
    userList:
      uri: '"/api/internal-users"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.userName, 'value':
        $item.userName}})
      disabledCache: true
layout_options:
  show_dialog_form_save_add_button: true
  toolTable:
    export: true
    adjustDisplay: 'true'
  show_detail_history: false
  tool_table:
    - id: export
  view_after_created: true
  delete_multi_items: true
  is_export_grid: true
  hide_action_row: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    title: Edit
    icon: icon-pencil-simple-bold
    type: ghost-gray
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: ghost-gray
backend_url: /api/monthly-tax-declaration-period
screen_name: monthly-tax-declaration-period
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: companyCode
    defaultName: CompanyCode
  - name: countryCode
    defaultName: CountryCode
  - name: groupTaxSettlementCode
    defaultName: GroupTaxSettlementCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Tax Calculation Calendar
  parent:
    title: Entity Setting
