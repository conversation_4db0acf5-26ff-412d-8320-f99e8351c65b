id: PIT.FS.FR.014_
status: draft
sort: 359
user_created: 43878dbb-aa6c-4ef4-8bd7-0ea811ffafa7
date_created: '2024-08-02T03:10:32.140Z'
user_updated: cdfee544-f2ba-4ed2-ace8-5adc1ae0ed61
date_updated: '2025-07-11T08:45:43.636Z'
title: Run Monthly Tax Calculation
requirement:
  time: 1748234145445
  blocks:
    - id: IIWaFKAZct
      type: paragraph
      data:
        text: >-
          Chứ<PERSON> năng cho phép bộ phận nhân sự tổng hợp dữ liệu theo kỳ kê khai
          thuế tháng
    - id: dyUqJ6CUli
      type: paragraph
      data:
        text: >-
          'Chứ<PERSON> năng cho phép bộ phận nhân sự tổng hợp các dữ liệu theo kỳ kê
          khai thuế tháng, các thông tin tổng hợp như sau:
    - id: 1IGOyYv8mr
      type: paragraph
      data:
        text: >-
          - <PERSON><PERSON> sách nhân viên thỏa điều kiện kê khai thuế: toàn bộ nhân viên
          và đối tượng vãng lai có phát sinh thu nhập trong tháng tính thuế
    - id: WJVSnS1A1B
      type: paragraph
      data:
        text: '- Thông tin người phụ thuộc trong kỳ kê khai'
    - id: jGKWDtLyh_
      type: paragraph
      data:
        text: >-
          - Tất cả các khoản trên bảng lương tháng và thu nhập kê khai bổ sung
          trong tháng (Thu nhập trước thuế, các khoản miễn thuế, các khoản giảm
          trừ, thuế TNCN đã khấu trừ)
    - id: IC-ybaj6Qv
      type: paragraph
      data:
        text: >-
          Ngoài ra, chức năng cho phép bộ phận nhân sự thực hiện tính lại thuế,
          khóa và mở khóa dữ liệu cho CBNV
  version: 2.30.7
screen_design: null
module: PIT
local_fields:
  - code: monthlyTaxDeclarationPeriodName
    title: Declaration Period
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    description: Load declaration period information
  - code: groupTaxSettlementName
    title: Tax Settlement Group
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: Load tax settlement group information
  - code: startDate
    title: Start Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    description: Load start date information
  - code: endDate
    title: End Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    description: Load end date information
  - code: revision
    title: Revision
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: typeOfRunName
    title: Type Of Run
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: calculationStatus
    title: Status
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tag
      collection: field_types
    extra_config:
      tags:
        - value: NotCalculated
          label: Not Calculated
          style:
            background_color: '#F1F3F5'
        - value: Processing
          label: Processing
          style:
            background_color: '#E6F2FF'
        - value: Completed
          label: Completed
          style:
            background_color: '#E0FAE9'
        - value: Locked
          label: Locked
          style:
            background_color: '#F1F3F5'
        - value: Finalized
          label: Finalized
          style:
            background_color: '#F3EAFB'
        - value: Failed
          label: Failed
mock_data: []
local_buttons: null
layout: layout-table-progressing
form_config:
  formSize:
    create: largex
    view: largex
    edit: largex
  formTitle:
    view: Income Summary Parameter
  btnModalDialogFooter:
    - id: calculate
      title: Run
      type: primary
  fields:
    - type: group
      n_cols: 2
      fields:
        - type: text
          name: unvisibleRevisionChecking
          unvisible: true
          _value:
            transform: $string($.variables._checkingRevision)
        - type: text
          name: monthlyTaxDeclarationPeriodCode
          unvisible: true
          dependantField: $.fields.monthlyTaxDeclarationPeriod
          _value:
            transform: $.fields.monthlyTaxDeclarationPeriod.value
        - type: text
          name: typeOfRunCode
          unvisible: true
          dependantField: $.fields.typeOfRun
          _value:
            transform: $.fields.typeOfRun.value
        - type: select
          label: Declaration Period
          name: monthlyTaxDeclarationPeriod
          placeholder: Select Declaration Period
          clearFieldsAfterChange:
            - typeOfRun
            - typeOfRunCode
          _select:
            transform: >-
              $declarationPeriodList($.extend.limit, $.extend.page,
              $.extend.search)
          isLazyLoad: true
          validators:
            - type: required
        - type: select
          label: Type of Run
          name: typeOfRun
          placeholder: Select Type of Run
          _select:
            transform: $typeOfRunList($.extend.limit, $.extend.page, $.extend.search)
          isLazyLoad: true
          validators:
            - type: required
        - type: text
          label: Tax Settlement Group
          name: taxSettlementGroupNameCode
          _value:
            transform: >-
              $not($isNilorEmpty($.fields.monthlyTaxDeclarationPeriod.taxSettlementGroupNameCode))
              ?  $.fields.monthlyTaxDeclarationPeriod.taxSettlementGroupNameCode
          dependantField: $.fields.monthlyTaxDeclarationPeriod
          placeholder: Auto-Generated
          _disabled:
            transform: 'true'
        - type: text
          label: Revision
          name: revision
          _value:
            transform: $.variables._checkingRevision
          dependantField: $.fields.monthlyTaxDeclarationPeriod;$.fields.typeOfRun
          placeholder: Auto-Generated
          _disabled:
            transform: 'true'
        - type: dateRange
          label: Start Date
          name: startDate
          mode: date-picker
          placeholder: Auto-Generated
          _value:
            transform: $.fields.monthlyTaxDeclarationPeriod.startDate
          dependantField: $.fields.monthlyTaxDeclarationPeriod
          _disabled:
            transform: 'true'
        - type: dateRange
          label: End Date
          name: endDate
          mode: date-picker
          placeholder: Auto-Generated
          _value:
            transform: $.fields.monthlyTaxDeclarationPeriod.endDate
          dependantField: $.fields.monthlyTaxDeclarationPeriod
          _disabled:
            transform: 'true'
  sources:
    declarationPeriodList:
      uri: '"/api/monthly-tax-declaration-period"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search':
        $.search,'filter':[{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'startDate': $item.startDate,
        'endDate': $item.endDate,
        'taxSettlementGroupNameCode':$item.taxSettlementGroupNameCode }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    typeOfRunList:
      uri: '"/api/picklists/PIT_TYPE_RUN/values/pagination"'
      method: GET
      queryTransform: ' {''limit'': $.limit,''search'':$.search, ''page'': $.page,''sort'':[{''field'':''code'',''order'':''ascend''}], ''filter'':[{''field'':''status'',''operator'':''$eq'',''value'':true}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    isCheckNextRevision:
      uri: '"/api/synthesizing-income/with-next-revision"'
      method: GET
      queryTransform: ' {''filter'':[{''field'':''monthlyTaxDeclarationPeriodCode'',''operator'':''$eq'',''value'':$.monthlyTaxDeclarationPeriodCode},{''field'':''typeOfRunCode'',''operator'':''$eq'',''value'':$.typeOfRunCode}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $exists($.revision)?$string($.revision):null
      disabledCache: true
      showError: true
      params:
        - monthlyTaxDeclarationPeriodCode
        - typeOfRunCode
  variables:
    _checkingRevision:
      transform: >-
        $not($isNilorEmpty($.fields.monthlyTaxDeclarationPeriod.value)) and
        $not($isNilorEmpty($.fields.typeOfRun))?$isCheckNextRevision($.fields.monthlyTaxDeclarationPeriod.value,$.fields.typeOfRun.value):'_setValueNull'
  calculateBackendUrl: /api/synthesizing-income/calculate
  codeCalculateKey: monthlyTaxDeclarationPeriodCode
filter_config:
  fields:
    - type: selectAll
      label: Declaration Period
      name: monthlyTaxDeclarationPeriod
      placeholder: Select Declaration Period
      isLazyLoad: true
      _options:
        transform: >-
          $monthlyTaxDeclarationPeriodList($.extend.limit, $.extend.page,
          $.extend.search)
      labelType: type-grid
    - type: dateRange
      label: Start Date
      name: startDate
      labelType: type-grid
    - type: dateRange
      name: endDate
      label: End Date
      labelType: type-grid
    - type: selectAll
      label: Tax Settlement Group
      placeholder: Select Tax Settlement Group
      name: groupTaxSettlement
      isLazyLoad: true
      _options:
        transform: $tsgList($.extend.limit, $.extend.page, $.extend.search)
      labelType: type-grid
    - type: text
      label: Revision
      placeholder: Enter Revision
      name: revision
      labelType: type-grid
    - type: selectAll
      label: Type Of Run
      placeholder: Select Type Of Run
      name: typeOfRunCode
      _options:
        transform: $typeOfRunList($.extend.limit, $.extend.page, $.extend.search)
      labelType: type-grid
    - type: selectAll
      label: Status
      placeholder: Select Status
      name: calculationStatus
      _options:
        transform: >-
          [{'label':'Not
          Calculated','value':'NotCalculated'},{'label':'Processing','value':'Processing'},{'label':'Completed','value':'Completed'},{'label':'Locked','value':'Locked'},{'label':'Finalized','value':'Finalized'}]
      labelType: type-grid
  filterMapping:
    - field: groupTaxSettlementCode
      operator: $in
      valueField: groupTaxSettlement.(value)
    - field: monthlyTaxDeclarationPeriodCode
      operator: $in
      valueField: monthlyTaxDeclarationPeriod.(value)
    - field: revision
      operator: $eq
      valueField: revision
    - field: calculationStatus
      operator: $in
      valueField: calculationStatus.(value)
    - field: typeOfRunCode
      operator: $in
      valueField: typeOfRunCode.(value)
    - field: endDate
      operator: $between
      valueField: endDate
    - field: startDate
      operator: $between
      valueField: startDate
  sources:
    tsgList:
      uri: '"/api/meta-data/synthesizing-income/group-tax-settlement"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    typeOfRunList:
      uri: '"/api/picklists/PIT_TYPE_RUN/values/pagination"'
      method: GET
      queryTransform: ' {''limit'': $.limit,''search'':$.search , ''page'': $.page, ''filter'':[{''field'':''status'',''operator'':''$eq'',''value'':true}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    monthlyTaxDeclarationPeriodList:
      uri: '"/api/monthly-tax-declaration-period"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code,
        'assessmentPeriodEndDate':$item.assessmentPeriodEndDate,
        'assessmentPeriodStartDate':$item.assessmentPeriodStartDate }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
layout_options:
  show_detail_history: false
  toolTable:
    export: true
    adjustDisplay: 'true'
  tool_table:
    - id: export
  action_click_row:
    actionId: routing
    params: id
    default_query_params:
      tab: 1
  hide_action_row: true
  show_create_data_table: true
  delete_multi_items: true
  is_export_grid: true
  fsdIdProgressingPath: /PIT/PIT.FS.FR.014
  progressing_info:
    title_progressing: Synthesizing data
    body_progressing:
      id: id
      title: name
      progress: processing
      createdAt: createdAt
    is_cancel: false
    is_view_result: false
    get_progressing_list_api: /api/synthesizing-income/progressing
    update_progressing_list_api: /api/synthesizing-income/progressing
    filter_mapping_progressing: []
    no_filter_needed: true
    unique_by: version
layout_options__header_buttons:
  - id: calculate
    title: Start
    icon: icon-monitor-play-bold
    type: primary
options: null
create_form: null
layout_options__footer_buttons: null
layout_options__row_actions: null
backend_url: /api/synthesizing-income
screen_name: null
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key: null
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Integrate Gross Income Arising  During Declaration Period
  parent:
    title: Monthly tax declaration
