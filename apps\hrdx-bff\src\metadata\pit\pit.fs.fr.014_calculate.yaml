id: PIT.FS.FR.014_calculate
status: draft
sort: 2
user_created: cdfee544-f2ba-4ed2-ace8-5adc1ae0ed61
date_created: '2024-10-22T08:59:21.445Z'
user_updated: cdfee544-f2ba-4ed2-ace8-5adc1ae0ed61
date_updated: '2025-07-16T04:22:11.109Z'
title: Calculate Tax
requirement:
  time: 1748419902685
  blocks:
    - id: gg1bE9ikuA
      type: paragraph
      data:
        text: >-
          Chứ<PERSON> năng cho phép bộ phận nhân sự tính thuế cho kỳ kê khai thuế tháng
          như sau:
    - id: b-OlUXEfnc
      type: paragraph
      data:
        text: >-
          - <PERSON><PERSON> thống sẽ tổng hợp dữ liệu tính thuế cho tất cả nhân viên thỏa
          điều kiện trong kỳ kê khai thuế tháng
    - id: GHPxv61GRI
      type: paragraph
      data:
        text: >-
          - <PERSON><PERSON> thống sẽ tổng hợp thu nhập theo tháng cho các <PERSON>V không cần tính
          lại thuế và tính toán chính xác số thuế người lao động phải tạm khấu
          trừ trên tổng thu nhập của kỳ tính thuế, trừ giảm trừ và áp dụng biểu
          thuế lũy tiến tính ra khoản thừa thiếu cho các CBNV cần tính lại thuế
    - id: bwo670ObBj
      type: paragraph
      data:
        text: >-
          - Sau khi tính thuế, các khoản thuế thừa / thiếu sẽ được chuyển vào kỳ
          lương được CBNS xác định.
  version: 2.30.7
screen_design: null
module: PIT
local_fields:
  - code: subStatus
    title: Sub Status
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tag
      collection: field_types
    extra_config:
      tags:
        - value: Warning
          label: Warning
          style:
            background_color: '#FEF9CC'
        - value: Successful
          label: Successful
          class: success
  - code: locked
    title: Lock Status
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tag
      collection: field_types
    extra_config:
      tags:
        - value: true
          label: Locked
          class: default
        - value: false
          label: Unlocked
          class: success
  - code: employeeId
    title: Employee ID
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: employeeRecordNumber
    title: Employee Record Number (ERN)
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: employeeName
    title: Employee Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: taxCode
    title: PIT Code
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: legalEntityName
    title: Legal Entity
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: departmentName
    title: Department
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: contractTypeName
    title: Contract Type
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: residentStatusName
    title: Residence Status
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: tariffName
    title: Tax Bracket
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: incomeCommitment
    title: Income Commitment
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Currency
      collection: field_types
    extra_config:
      precision: 0
  - code: cancelCommitment
    title: Cancel Commitment
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: dateOfCancelCommitment
    title: Cancelled On
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
  - code: currencyName
    title: Currency
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    extra_config:
      precision: 0
  - code: totalSalaryIncome
    title: Total Monthly Salary Income
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Currency
      collection: field_types
    extra_config:
      precision: 0
  - code: totalOtherIncome
    title: Total Other Income
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Currency
      collection: field_types
    extra_config:
      precision: 0
  - code: totalTaxableSalaryIncome
    title: Taxable Monthly Salary Income
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Currency
      collection: field_types
    extra_config:
      precision: 0
  - code: totalTaxableOtherIncome
    title: Taxable Other Income
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Currency
      collection: field_types
    extra_config:
      precision: 0
  - code: personalDeductionAmount
    title: Self Deduction
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Currency
      collection: field_types
    extra_config:
      precision: 0
  - code: dependentDeductionAmount
    title: Dependent Deduction
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Currency
      collection: field_types
    extra_config:
      precision: 0
  - code: insuranceDeductible
    title: Insurance Deduction
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Currency
      collection: field_types
    extra_config:
      precision: 0
  - code: totalIncome
    title: Total Income
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Currency
      collection: field_types
    extra_config:
      precision: 0
  - code: taxableIncomes
    title: Total Taxable Income
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Currency
      collection: field_types
    extra_config:
      precision: 0
  - code: totalAmountOfDeduction
    title: Total Deduction
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Currency
      collection: field_types
    extra_config:
      precision: 0
  - code: taxFreeIncomes
    title: Total Income Tax Exemptions
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Currency
      collection: field_types
    extra_config:
      precision: 0
  - code: totalAssessableIncome
    title: Assessable income
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Currency
      collection: field_types
    extra_config:
      precision: 0
  - code: totalTaxPayable
    title: Total Tax Payable
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Currency
      collection: field_types
    extra_config:
      precision: 0
  - code: deductedPersonalIncomeTaxAmount
    title: Total PIT Deducted
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Currency
      collection: field_types
    extra_config:
      precision: 0
  - code: taxGap
    title: Tax Gap
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Currency
      collection: field_types
    extra_config:
      precision: 0
  - code: overpaidTaxAmount
    title: Overpaid Tax Amount
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Currency
      collection: field_types
    extra_config:
      precision: 0
  - code: remainingTaxAmountToPaid
    title: Remaining Tax Amount Owed
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Currency
      collection: field_types
    extra_config:
      precision: 0
  - code: errorMessage
    title: Error Message
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: updatedBy
    title: Last Modified By
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: updatedAt
    title: Last Modified On
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
mock_data:
  - lockStatus: false
local_buttons: null
layout: layout-table-progressing
form_config:
  fields:
    - type: group
      collapse: false
      label: Employee Information
      fieldGroupTitleStyle:
        border: none
        padding: 0 0 12px 0
      fields:
        - type: text
          unvisible: true
          name: id
        - type: text
          unvisible: true
          name: synthesizingIncomeId
        - type: text
          label: Sub Status
          name: subStatus
          readOnly: true
          displaySetting:
            type: Tag
            extraConfig:
              tags:
                - value: Warning
                  label: Warning
                  style:
                    background_color: '#FEF9CC'
                - value: Successful
                  label: Successful
                  class: success
            _value:
              transform: $.extend.defaultValue.subStatus
        - type: text
          label: Lock Status
          name: lockedName
          readOnly: true
          displaySetting:
            type: Tag
            extraConfig:
              tags:
                - value: true
                  label: Locked
                  class: default
                - value: false
                  label: Unlocked
                  class: success
            _value:
              transform: $.extend.defaultValue.locked
        - type: text
          readOnly: true
          label: Employee ID
          name: employeeId
        - type: text
          readOnly: true
          label: Employee ERN
          name: employeeRecordNumber
        - type: text
          readOnly: true
          label: Employee Name
          name: employeeName
        - type: text
          readOnly: true
          label: PIT Code
          name: taxCode
        - type: text
          readOnly: true
          label: Department
          name: departmentName
        - type: text
          readOnly: true
          label: Legal Entity
          name: legalEntityName
        - type: text
          readOnly: true
          label: Contract Type
          name: contractTypeName
        - type: text
          label: Residence Status
          name: residentStatusName
          readOnly: true
        - type: text
          readOnly: true
          label: Tax Bracket
          name: tariffName
        - type: text
          readOnly: true
          label: Income Commitment
          name: incomeCommitment
          number:
            format: currency
            max: '99999999999999'
            precision: 0
        - type: text
          label: Cancel Commitment
          name: cancelCommitment
          readOnly: true
          displaySetting:
            type: Tag
            extraConfig:
              tags:
                - value: false
                  label: 'No'
                  class: default
                - value: true
                  label: 'Yes'
                  class: success
            _value:
              transform: >-
                $isNilorEmpty($.extend.defaultValue.cancelCommitment) ? false :
                $.extend.defaultValue.cancelCommitment
        - type: dateRange
          label: Cancelled On
          name: dateOfCancelCommitment
          mode: date-picker
          readOnly: true
        - type: text
          readOnly: true
          label: Error Message
          name: errorMessage
    - type: group
      collapse: false
      label: Detail Income Summary
      fieldGroupContentStyle:
        gap: unset
        padding: unset
      fields:
        - type: text
          readOnly: true
          name: currencyName
          label: Currency
          fieldFlexEnd: true
        - type: number
          number:
            format: currency
            max: '99999999999999'
            precision: 0
          readOnly: true
          name: totalSalaryIncome
          label: Total Monthly Salary Income (1)
          fieldFlexEnd: true
        - type: number
          number:
            format: currency
            max: '99999999999999'
            precision: 0
          readOnly: true
          name: totalOtherIncome
          label: Total Other Income (2)
          fieldFlexEnd: true
        - type: number
          number:
            format: currency
            max: '99999999999999'
            precision: 0
          readOnly: true
          name: totalTaxableSalaryIncome
          label: Taxable Monthly Salary Income (3)
          fieldFlexEnd: true
        - type: number
          number:
            format: currency
            max: '99999999999999'
            precision: 0
          readOnly: true
          name: totalTaxableOtherIncome
          label: Taxable Other Income (4)
          fieldFlexEnd: true
        - type: number
          number:
            format: currency
            max: '99999999999999'
            precision: 0
          readOnly: true
          name: personalDeductionAmount
          label: Self Deduction (5)
          fieldFlexEnd: true
        - type: number
          number:
            format: currency
            max: '99999999999999'
            precision: 0
          readOnly: true
          name: dependentDeductionAmount
          label: Dependent Deduction (6)
          fieldFlexEnd: true
        - type: number
          number:
            format: currency
            max: '99999999999999'
            precision: 0
          readOnly: true
          name: insuranceDeductible
          label: Insurance Deduction (7)
          fieldFlexEnd: true
        - type: number
          number:
            format: currency
            max: '99999999999999'
            precision: 0
          readOnly: true
          name: totalIncome
          label: Total Income (8) = (1) + (2)
          fieldFlexEnd: true
        - type: number
          number:
            format: currency
            max: '99999999999999'
            precision: 0
          readOnly: true
          name: taxableIncomes
          label: Total Taxable Income (9) = (3) + (4)
          fieldFlexEnd: true
        - type: number
          number:
            format: currency
            max: '99999999999999'
            precision: 0
          readOnly: true
          name: totalAmountOfDeduction
          label: Total Deduction (10) = (5) + (6)
          fieldFlexEnd: true
        - type: number
          number:
            format: currency
            max: '99999999999999'
            precision: 0
          readOnly: true
          name: taxFreeIncomes
          label: Total Income Tax Exemptions (11)
          fieldFlexEnd: true
        - type: number
          number:
            format: currency
            max: '99999999999999'
            precision: 0
          readOnly: true
          name: totalAssessableIncome
          label: Assessable income (12) = (9) - (10) - (11)
          fieldFlexEnd: true
        - type: number
          number:
            format: currency
            max: '99999999999999'
            precision: 0
          readOnly: true
          name: totalTaxPayable
          label: Total Tax Payable (13)
          fieldFlexEnd: true
        - type: number
          number:
            format: currency
            max: '99999999999999'
            precision: 0
          readOnly: true
          name: deductedPersonalIncomeTaxAmount
          label: Total PIT Deducted (14)
          fieldFlexEnd: true
        - type: number
          number:
            format: currency
            max: '99999999999999'
            precision: 0
          readOnly: true
          name: overpaidTaxAmount
          label: Overpaid Tax Amount (15.1) = (13) - (14)
          fieldFlexEnd: true
        - type: number
          number:
            format: currency
            max: '99999999999999'
            precision: 0
          readOnly: true
          name: remainingTaxAmountToPaid
          label: Remaining Tax Amount Owed (15.2) = (14) - (13)
          fieldFlexEnd: true
  footer:
    update: true
    updatedOn: updatedAt
    updatedBy: updatedBy
filter_config:
  fields:
    - type: selectAll
      label: Sub Status
      name: subStatus
      placeholder: Select Sub Status
      _options:
        transform: >-
          [{'value':'Successful','label':'Successful'},{'value':'Warning','label':'Warning'}]
      labelType: type-grid
    - type: selectAll
      label: Lock Status
      name: lockedStatus
      placeholder: Select Lock Status
      _options:
        transform: '[{''value'':''Y'',''label'':''Locked''},{''value'':''N'',''label'':''Unlocked''}]'
      labelType: type-grid
    - type: selectAll
      name: employees
      label: Employee
      placeholder: Select Employee
      isLazyLoad: true
      _options:
        transform: >-
          $employeesPayrollList($.extend.limit, $.extend.page,
          $.extend.search,$.extend.addOnValue.id1)
      labelType: type-grid
    - type: text
      label: PIT Code
      placeholder: Enter PIT Code
      name: taxCode
      labelType: type-grid
    - name: legalEntityCodes
      label: Legal Entity
      type: selectAll
      placeholder: Select Legal Entity
      isLazyLoad: true
      _options:
        transform: >-
          $legalListOfGroupTaxSettlementOrg($.extend.limit, $.extend.page,
          $.extend.search,$.extend.addOnValue.dataDetail.monthlyTaxDeclarationPeriodCode)
      labelType: type-grid
    - name: departmentCodes
      label: Department
      type: selectAll
      placeholder: Select Department
      isLazyLoad: true
      _options:
        transform: >-
          $departmentList($.extend.limit, $.extend.page,
          $.extend.search,$.extend.addOnValue.dataDetail.monthlyTaxDeclarationPeriodCode)
      labelType: type-grid
    - name: contractTypes
      label: Contract Type
      type: selectAll
      placeholder: Select Contract Type
      isLazyLoad: true
      _options:
        transform: $contractTypeList($.extend.limit, $.extend.page, $.extend.search)
      labelType: type-grid
    - name: residenceStatuses
      label: Residence Status
      type: selectAll
      placeholder: Select Residence Status
      isLazyLoad: true
      _options:
        transform: $residenceStatusList($.extend.limit, $.extend.page, $.extend.search)
      labelType: type-grid
    - name: jobIndicators
      label: Job Indicator
      type: selectAll
      placeholder: Select Job Indicator
      isLazyLoad: true
      _options:
        transform: $jobIndicatorList($.extend.limit, $.extend.page, $.extend.search)
      labelType: type-grid
    - name: tariffs
      label: Tax Bracket
      type: selectAll
      placeholder: Select Tax Bracket
      isLazyLoad: true
      _options:
        transform: $tariffList($.extend.limit, $.extend.page, $.extend.search)
      labelType: type-grid
    - type: number
      number:
        format: currency
        max: '99999999999999'
        precision: 4
      label: Income Commitment
      placeholder: Enter Income Commitment
      name: incomeCommitment
      labelType: type-grid
    - type: text
      label: Cancel Commitment
      placeholder: Enter Cancel Commitment
      name: cancelCommitment
      labelType: type-grid
    - name: dateOfCancelCommitment
      labelType: type-grid
      label: Cancelled On
      type: dateRange
    - name: updatedBy
      label: Last Modified By
      type: selectAll
      mode: multiple
      labelType: type-grid
      placeholder: Select Last Updated By
      _options:
        transform: $userList()
    - name: lastUpdatedOn
      labelType: type-grid
      label: Last Modified On
      type: dateRange
  filterMapping:
    - field: taxCode
      operator: $cont
      valueField: taxCode
    - operator: $or
      valueField:
        - field: employeeId
          operator: $elemMatch
          valueField: employees.(employeeId)
        - field: employeeRecordNumber
          operator: $elemMatch
          valueField: employees.(employeeRecordNumber)
    - field: lockedFilter
      operator: $in
      valueField: lockedStatus.(value)
    - field: subStatus
      operator: $in
      valueField: subStatus.(value)
    - field: legalEntityCode
      operator: $in
      valueField: legalEntityCodes.(value)
    - field: departmentCode
      operator: $in
      valueField: departmentCodes.(value)
    - field: contractTypeCode
      operator: $in
      valueField: contractTypes.(value)
    - field: residentStatusCode
      operator: $in
      valueField: residenceStatuses.(value)
    - field: jobIndicatorCode
      operator: $in
      valueField: jobIndicators.(value)
    - field: tariffCode
      operator: $in
      valueField: tariffs.(value)
    - field: currencyCode
      operator: $in
      valueField: currencies.(value)
    - field: incomeCommitment
      operator: $cont
      valueField: incomeCommitment
    - field: totalSalaryIncome
      operator: $cont
      valueField: totalSalaryIncome
    - field: totalOtherIncome
      operator: $cont
      valueField: totalOtherIncome
    - field: totalTaxableSalaryIncome
      operator: $cont
      valueField: totalTaxableSalaryIncome
    - field: totalTaxableOtherIncome
      operator: $cont
      valueField: totalTaxableOtherIncome
    - field: personalDeductionAmount
      operator: $cont
      valueField: personalDeductionAmount
    - field: dependentDeductionAmount
      operator: $cont
      valueField: dependentDeductionAmount
    - field: insuranceDeductible
      operator: $cont
      valueField: insuranceDeductible
    - field: totalIncome
      operator: $cont
      valueField: totalIncome
    - field: taxableIncomes
      operator: $cont
      valueField: taxableIncomes
    - field: totalAmountOfDeduction
      operator: $cont
      valueField: totalAmountOfDeduction
    - field: taxFreeIncomes
      operator: $cont
      valueField: taxFreeIncomes
    - field: totalTaxPayable
      operator: $cont
      valueField: totalTaxPayable
    - field: deductedPersonalIncomeTaxAmount
      operator: $cont
      valueField: deductedPersonalIncomeTaxAmount
    - field: taxGap
      operator: $cont
      valueField: taxGap
    - field: overpaidTaxAmount
      operator: $cont
      valueField: overpaidTaxAmount
    - field: remainingTaxAmountToPaid
      operator: $cont
      valueField: remainingTaxAmountToPaid
    - field: updatedBy
      operator: $in
      valueField: updatedBy.(value)
    - field: updatedAt
      operator: $between
      valueField: lastUpdatedOn
  sources:
    tsgList:
      uri: '"/api/group-tax-settlement"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    typeOfRunList:
      uri: '"/api/picklists/PIT_TYPE_RUN/values/pagination"'
      method: GET
      queryTransform: ' {''limit'': $.limit,''search'':$.search , ''page'': $.page, ''filter'':[{''field'':''status'',''operator'':''$eq'',''value'':true}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    contractTypeList:
      uri: '"/api/picklists/CONTRACTTYPE/values/pagination"'
      method: GET
      queryTransform: ' {''limit'': $.limit,''search'':$.search , ''page'': $.page, ''filter'':[{''field'':''status'',''operator'':''$eq'',''value'':true}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    residenceStatusList:
      uri: '"/api/picklists/RESIDENCESTATUS/values/pagination"'
      method: GET
      queryTransform: ' {''limit'': $.limit,''search'':$.search , ''page'': $.page, ''filter'':[{''field'':''status'',''operator'':''$eq'',''value'':true}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    jobIndicatorList:
      uri: '"/api/picklists/JOBINDICATOR/values/pagination"'
      method: GET
      queryTransform: ' {''limit'': $.limit,''search'':$.search , ''page'': $.page, ''filter'':[{''field'':''status'',''operator'':''$eq'',''value'':true}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    currencyList:
      uri: '"/api/picklists/CURRENCY/values/pagination"'
      method: GET
      queryTransform: ' {''limit'': $.limit,''search'':$.search , ''page'': $.page, ''filter'':[{''field'':''status'',''operator'':''$eq'',''value'':true}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    tariffList:
      uri: '"/api/tariff"'
      method: GET
      queryTransform: ' {''limit'': $.limit,''search'':$.search , ''page'': $.page, ''filter'':[{''field'':''status'',''operator'':''$eq'',''value'':true}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    monthlyTaxDeclarationPeriodList:
      uri: '"/api/monthly-tax-declaration-period"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code,
        'assessmentPeriodEndDate':$item.assessmentPeriodEndDate,
        'assessmentPeriodStartDate':$item.assessmentPeriodStartDate }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    employeesPayrollList:
      uri: '"/api/synthesizing-income/"  & $.recordId & "/raw-data-employees"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page, ''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label':
        $join($filter([$item.employeeId,$string($item.employeeRecordNumber),$item.employeeName],
        $boolean), ' - ')  , 'value': {'employeeId': $item.employeeId,
        'employeeRecordNumber': $string($item.employeeRecordNumber)},
        'employeeId': $item.employeeId, 'employeeRecordNumber':
        $string($item.employeeRecordNumber)}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - recordId
    legalListOfGroupTaxSettlementOrg:
      uri: '"/api/monthly-tax-declaration-period/legal-entities"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page, 'search': $.search,
        'sort':[{'field':'effectiveDate', 'order':'DESC'}],'filter':
        [{'field':'monthlyDeclarationPeriodCode','operator':'$eq','value':$.monthlyDeclarationPeriodCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($entity) {{'value': $entity.code,'label':
        $entity.longName & ' (' & $entity.code & ')'}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - monthlyDeclarationPeriodCode
    departmentList:
      uri: '"/api/monthly-tax-declaration-period/departments"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page, 'search': $.search,
        'sort':[{'field':'effectiveDate', 'order':'DESC'}],'filter':
        [{'field':'monthlyDeclarationPeriodCode','operator':'$eq','value':$.monthlyDeclarationPeriodCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($entity) {{'value': $entity.code,'label':
        $entity.longName & ' (' & $entity.code & ')'}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - monthlyDeclarationPeriodCode
    userList:
      uri: '"/api/internal-users"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.userName, 'value':
        $item.userName}})
      disabledCache: true
layout_options:
  page_header_options:
    visible: false
  toolTable:
    adjustDisplay: 'true'
  tool_table:
    - id: integrate
      icon: icon-calculator-bold
      title: Re-Calculate Tax
      _disabled:
        transform: $.parentData.calculationStatus = 'Finalized'
    - id: export
  actions_many_handler:
    lock:
      action: lock
      confirm:
        title: Lock
        icon: lock
        content: >-
          This will not be accessible after activating Lock. Do you want to
          lock?
        type: popup-confirm
      url: /api/synthesizing-income/:id/employees/lock
    unlock:
      action: unlock
      confirm:
        title: Unlock
        icon: lock-open
        content: Do you want to unlock?
        type: popup-confirm
      url: /api/synthesizing-income/:id/employees/unlock
  _show_dialog_footer: false
  row_actions_handler:
    lock:
      action: lock
      confirm:
        title: Lock
        icon: lock
        content: >-
          This will not be accessible after activating Lock. Do you want to
          lock?
        type: popup-confirm
      _backendUrl: /api/synthesizing-income/:id/employees/lock
      _update_fields: '{''ids'': [$.id]}'
      method: POST
    unlock:
      action: unlock
      confirm:
        title: Unlock
        icon: lock-open
        content: Do you want to unlock?
        type: popup-confirm
      _backendUrl: /api/synthesizing-income/:id/employees/unlock
      _update_fields: '{''ids'': [$.id]}'
      method: POST
  show_detail_history: false
  show_create_data_table: false
  modal_footer_buttons:
    - id: cancel
      title: Close
      type: primary
  edit_modal_footer_buttons:
    - id: cancel
      title: Cancel
      type: tertiary
    - id: save
      title: Save
      type: primary
  custom_update_api:
    _url:
      transform: >-
        '/api/synthesizing-income/' & $.synthesizingIncomeId &
        '/update-employees/' & $.id
  is_export_grid: true
  progressing_info:
    title_progressing: Synthesizing data
    body_progressing:
      id: synthesizingIncomeId
      title: name
      progress: processing
      createdAt: createdAt
    is_cancel: false
    is_view_result: false
    get_progressing_list_api: /api/synthesizing-income/progressing
    update_progressing_list_api: /api/synthesizing-income/progressing
    unique_by: version
layout_options__header_buttons: null
options: null
create_form:
  dialog_title: Tax Calculation Parameter
  btnModalDialogFooter:
    - id: integrate
      title: Run
      type: primary
  mappingValueFromParent:
    groupTaxSettlementCode: groupTaxSettlementCode
    monthlyDeclarationPeriodCode: monthlyTaxDeclarationPeriodCode
    recordId: id
  integrateBackendUrl: >-
    '/api/synthesizing-income/' & $.id &
    '/employees/calculate-monthly-tax-declaration'
  fields:
    - name: legalEntityCodes
      label: Legal Entity
      type: selectAll
      clearFieldsAfterChange:
        - employees
        - departmentCodes
      outputValue: value
      placeholder: Select Legal Entity
      isLazyLoad: true
      _options:
        transform: >-
          $legalListOfGroupTaxSettlementOrg($.extend.limit, $.extend.page,
          $.extend.search,$.extend.defaultValue.monthlyDeclarationPeriodCode)
    - name: departmentCodes
      label: Department
      type: selectAll
      clearFieldsAfterChange:
        - employees
      outputValue: value
      placeholder: Select Department
      isLazyLoad: true
      _options:
        transform: >-
          $departmentList($.extend.limit, $.extend.page,
          $.extend.search,$.extend.defaultValue.monthlyDeclarationPeriodCode,$.fields.legalEntityCodes)
    - type: selectAll
      name: employees
      label: Employee
      placeholder: Select Employee
      isLazyLoad: true
      _options:
        transform: >-
          $employeesPayrollList($.extend.limit, $.extend.page,
          $.extend.search,$.extend.defaultValue.recordId,$.fields.legalEntityCodes,$.fields.departmentCodes)
      outputValue: value
  sources:
    employeesPayrollList:
      uri: '"/api/synthesizing-income/"  & $.recordId & "/raw-data-employees"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page, 'search': $.search, 'filter':
        [{'field':'legalEntityCode','operator':'$in','value':$.legalEntityCode},{'field':'departmentCode','operator':'$in','value':$.departmentCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label':
        $join($filter([$item.employeeId,$string($item.employeeRecordNumber),$item.employeeName],
        $boolean), ' - ')  , 'value': {'employeeId': $item.employeeId,
        'employeeRecordNumber': $string($item.employeeRecordNumber)},
        'employeeId': $item.employeeId, 'employeeRecordNumber':
        $string($item.employeeRecordNumber)}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - recordId
        - legalEntityCode
        - departmentCode
    legalListOfGroupTaxSettlementOrg:
      uri: '"/api/monthly-tax-declaration-period/legal-entities"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page, 'search': $.search,
        'sort':[{'field':'effectiveDate', 'order':'DESC'}],'filter':
        [{'field':'monthlyDeclarationPeriodCode','operator':'$eq','value':$.monthlyDeclarationPeriodCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($entity) {{'value': $entity.code,'label':
        $entity.longName & ' (' & $entity.code & ')'}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - monthlyDeclarationPeriodCode
    departmentList:
      uri: '"/api/monthly-tax-declaration-period/departments"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page, 'search': $.search,
        'sort':[{'field':'effectiveDate', 'order':'DESC'}],'filter':
        [{'field':'monthlyDeclarationPeriodCode','operator':'$eq','value':$.monthlyDeclarationPeriodCode},{'field':'legalEntityCode','operator':'$in','value':$.legalEntityCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($entity) {{'value': $entity.code,'label':
        $entity.longName & ' (' & $entity.code & ')'}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - monthlyDeclarationPeriodCode
        - legalEntityCode
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: lock
    title: Lock
    icon: icon-lock-bold
    type: ghost-gray
    condition_func: ' $.locked = false and $.subStatus = ''Warning'''
  - id: unlock
    title: Unlock
    icon: icon-lock-open-bold
    type: ghost-gray
    condition_func: $.locked = true and $.subStatus = 'Warning'
backend_url: >-
  /api/synthesizing-income/{{parent.id}}/employees/calculate-monthly-tax-declaration
screen_name: null
layout_options__actions_many:
  - id: lock
    title: Lock
    icon: icon-lock-bold
    type: tertiary
    condition_func: $.parentData.calculationStatus != 'Finalized'
  - id: unlock
    title: Unlock
    icon: icon-lock-open-bold
    type: tertiary
    condition_func: $.parentData.calculationStatus != 'Finalized'
  - id: export
    title: Export Selected
    icon: icon-upload-simple-bold
    type: secondary
parent: PIT.FS.FR.014_detail
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key: null
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Calculate Monthly Tax Declaration
  parent:
    title: Monthly tax declaration
