id: PIT.FS.FR.014_detail
status: draft
sort: null
user_created: cdfee544-f2ba-4ed2-ace8-5adc1ae0ed61
date_created: '2024-10-22T08:30:56.905Z'
user_updated: cdfee544-f2ba-4ed2-ace8-5adc1ae0ed61
date_updated: '2025-06-18T03:02:59.052Z'
title: Declaration Period Result
requirement:
  time: 1748339533024
  blocks:
    - id: pHQW_OIEQH
      type: paragraph
      data:
        text: '&nbsp; &nbsp;detail'
  version: 2.30.7
screen_design: null
module: PIT
local_fields: []
mock_data: null
local_buttons: null
layout: layout-tabs
form_config:
  formSize:
    create: largex
    view: largex
    edit: largex
  fields:
    - type: group
      n_cols: 2
      fields:
        - type: text
          label: Tax Calculation Calendar
          name: monthlyTaxDeclarationPeriodNameCode
          _value:
            transform: >-
              $.extend.defaultValue.monthlyTaxDeclarationPeriodName & ' (' &
              $.extend.defaultValue.monthlyTaxDeclarationPeriodCode & ')'
        - type: text
          label: Tax Settlement Group
          name: groupTaxSettlementNameCode
          _value:
            transform: >-
              $.extend.defaultValue.groupTaxSettlementName & ' (' &
              $.extend.defaultValue.groupTaxSettlementCode & ')'
        - type: dateRange
          label: Start Date
          name: startDate
          setting:
            format: dd/MM/yyyy
            type: date
          mode: date-picker
          _value:
            transform: $.extend.defaultValue.startDate
        - type: dateRange
          label: End Date
          name: endDate
          setting:
            format: dd/MM/yyyy
            type: date
          mode: date-picker
          _value:
            transform: $.extend.defaultValue.endDate
        - type: text
          label: Type Of Run
          name: typeOfRunName
          _value:
            transform: $.extend.defaultValue.typeOfRunName
        - name: calculationStatus
          type: select
          select:
            - value: NotCalculated
              label: Not Calculated
            - value: Processing
              label: Processing
            - value: Completed
              label: Completed
            - value: Locked
              label: Locked
            - value: Finalized
              label: Finalized
          label: Status
          readOnly: true
          displaySetting:
            type: Tag
            extraConfig:
              tags:
                - value: NotCalculated
                  label: Not Calculated
                  style:
                    background_color: '#E6F2FF'
                - value: Processing
                  label: Processing
                  style:
                    background_color: '#E0FAE9'
                - value: Completed
                  label: Completed
                  style:
                    background_color: '#F1F3F5'
                - value: Locked
                  label: Locked
                  style:
                    background_color: '#FEF9CC'
                - value: Finalized
                  label: Finalized
                  style:
                    background_color: '#F3EAFB'
                - value: Failed
                  label: Failed
              size: small
        - type: text
          label: Revision
          name: revision
          _value:
            transform: $.extend.defaultValue.revision
        - type: text
          label: Total of Employee
          name: totalEmployee
          _value:
            transform: $.extend.defaultValue.totalEmployee
          displaySetting:
            type: Label
            style:
              display: flex
              gap: 4px
            elements:
              - name: totalEmployeeWarning
                type: Tag
                _value:
                  transform: >-
                    {'type': 'warning', 'icon': 'icon-warning-bold', 'label':
                    $.extend.defaultValue.totalEmployeeWarning & ' Employees',
                    'size': 'small'}
filter_config: {}
layout_options:
  tabs_title:
    - Income Summary
    - Calculate Tax
  show_detail_history: false
  page_header_options:
    visible: true
  custom_title:
    transform: '''Declaration Period Result: '' & $.monthlyTaxDeclarationPeriodName'
  default_selected_tab_index: 1
  show_details_form: true
  child_event_subjects:
    - type: lock
      action: refresh
layout_options__header_buttons: null
options: null
create_form: null
layout_options__footer_buttons: null
layout_options__row_actions: null
backend_url: /api/synthesizing-income/:id1
screen_name: null
layout_options__actions_many: null
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key: null
screen_designs: []
function_specs: []
fields: []
children:
  - PIT.FS.FR.014_integrated
  - PIT.FS.FR.014_calculate
menu_item: null
