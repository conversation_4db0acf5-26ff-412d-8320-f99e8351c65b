id: PIT.FS.FR.014_integrated
status: draft
sort: 0
user_created: cdfee544-f2ba-4ed2-ace8-5adc1ae0ed61
date_created: '2024-10-22T09:26:21.279Z'
user_updated: cdfee544-f2ba-4ed2-ace8-5adc1ae0ed61
date_updated: '2025-07-16T09:25:11.633Z'
title: Income Summary
requirement:
  time: 1748420344781
  blocks:
    - id: hBICocWwc-
      type: paragraph
      data:
        text: >-
          Chứ<PERSON> năng cho phép bộ phận nhân sự tổng hợp các dữ liệu theo kỳ kê
          khai thuế tháng, các thông tin tổng hợp như sau:
    - id: H03JTR-TPA
      type: paragraph
      data:
        text: >-
          - Danh sách nhân viên thỏa điều kiện kê khai thuế: toàn bộ nhân viên
          và đối tượng vãng lai có phát sinh thu nhập trong tháng tính thuế
    - id: vp6zWxiBlH
      type: paragraph
      data:
        text: '- Thông tin người phụ thuộc trong kỳ kê khai'
    - id: BZ57FMdEjc
      type: paragraph
      data:
        text: >-
          - Tất cả các khoản trên bảng lương tháng và thu nhập kê khai bổ sung
          trong tháng (Thu nhập trước thuế, các khoản miễn thuế, các khoản giảm
          trừ, thuế TNCN đã khấu trừ)
  version: 2.30.7
screen_design: null
module: PIT
local_fields:
  - code: locked
    title: Lock Status
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tag
      collection: field_types
    extra_config:
      tags:
        - value: true
          label: Locked
          class: default
        - value: false
          label: Unlocked
          class: success
  - code: employeeId
    title: Employee ID
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: employeeRecordNumber
    title: Employee Record Number (ERN)
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: employeeName
    title: Employee Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: taxCode
    title: PIT Code
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: legalEntityName
    title: Legal Entity
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: departmentName
    title: Department
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: contractTypeName
    title: Contract Type
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: residentStatusName
    title: Residence Status
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: jobIndicatorName
    title: Job Indicator
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: tariffName
    title: Tax Bracket
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: currencyName
    title: Currency
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: totalSalaryIncome
    title: Total Monthly Salary Income
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Currency
      collection: field_types
    extra_config:
      precision: 0
  - code: totalOtherIncome
    title: Other Income Amount
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Currency
      collection: field_types
    extra_config:
      precision: 0
  - code: personalDeductionAmount
    title: Self Deduction
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Currency
      collection: field_types
    extra_config:
      precision: 0
  - code: dependentDeductionAmount
    title: Dependent Deduction
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Currency
      collection: field_types
    extra_config:
      precision: 0
  - code: insuranceDeductible
    title: Insurance Deduction
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Currency
      collection: field_types
  - code: totalIncome
    title: Total Income
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Currency
      collection: field_types
    extra_config:
      precision: 0
  - code: totalTaxableSalaryIncome
    title: Taxable Monthly Salary Income
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Currency
      collection: field_types
    extra_config:
      precision: 0
  - code: totalTaxableOtherIncome
    title: Taxable Other Income
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Currency
      collection: field_types
    extra_config:
      precision: 0
  - code: totalAmountOfDeduction
    title: Total Deduction
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Currency
      collection: field_types
    extra_config:
      precision: 0
  - code: taxFreeIncomes
    title: Total Income Tax Exemptions
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Currency
      collection: field_types
  - code: totalAssessableIncome
    title: Assessable income
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Currency
      collection: field_types
    extra_config:
      precision: 0
  - code: deductedPersonalIncomeTaxAmount
    title: Personal Income Tax Amount Deducted
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Currency
      collection: field_types
    extra_config:
      precision: 0
  - code: updatedBy
    title: Last Modified By
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: updatedAt
    title: Last Modified On
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
mock_data:
  - lockStatus: false
local_buttons: null
layout: layout-table-progressing
form_config:
  fields:
    - type: group
      collapse: false
      label: Basic Information
      fields:
        - type: text
          label: Lock Status
          name: lockedName
          displaySetting:
            type: Tag
            extraConfig:
              tags:
                - value: true
                  label: Locked
                  class: default
                - value: false
                  label: Unlocked
                  class: success
            _value:
              transform: $.extend.defaultValue.locked
        - type: text
          label: Employee ID
          name: employeeId
        - type: text
          label: Employee Record Number (ERN)
          name: employeeRecordNumber
        - type: text
          label: Employee Name
          name: employeeName
        - type: text
          label: Legal Entity
          name: legalEntityName
        - type: text
          label: Tariff
          name: tariffName
        - type: text
          label: Department
          name: departmentName
        - type: text
          label: Tax Code
          name: taxCode
        - type: text
          label: Contract Type
          name: contractTypeName
        - type: text
          label: Residence Status
          name: residentStatusName
        - type: dateRange
          label: Termination Date
          name: terminationDate
          setting:
            format: dd/MM/yyyy
            type: date
          mode: date-picker
    - type: group
      collapse: false
      label: Detail declaration period
      fields:
        - type: text
          readOnly: true
          name: currencyName
          label: Currency
          fieldFlexEnd: true
        - type: number
          number:
            format: currency
            max: '99999999999999'
            precision: 0
          readOnly: true
          name: totalIncome
          label: Total Income
          fieldFlexEnd: true
        - type: number
          number:
            format: currency
            max: '99999999999999'
            precision: 0
          readOnly: true
          name: taxableIncomes
          label: Taxable Income
          fieldFlexEnd: true
        - type: number
          number:
            format: currency
            max: '99999999999999'
            precision: 0
          readOnly: true
          name: taxFreeIncomes
          label: Tax Free Income
          fieldFlexEnd: true
        - type: number
          number:
            format: currency
            max: '99999999999999'
            precision: 0
          readOnly: true
          name: personalDeductionAmount
          label: Personal Deduction Amount
          fieldFlexEnd: true
        - type: number
          number:
            format: currency
            max: '99999999999999'
            precision: 0
          readOnly: true
          name: numberOfDependentDeduction
          label: The number of dependants
          fieldFlexEnd: true
        - type: number
          number:
            format: currency
            max: '99999999999999'
            precision: 0
          readOnly: true
          name: dependentDeductionAmount
          label: Dependent Deduction Amount
          fieldFlexEnd: true
        - type: number
          number:
            format: currency
            max: '99999999999999'
            precision: 0
          readOnly: true
          name: insuranceDeductible
          label: Deducted Insurance
          fieldFlexEnd: true
        - type: number
          number:
            format: currency
            max: '99999999999999'
            precision: 0
          readOnly: true
          name: totalAmountOfDeduction
          label: Total Amount Of Deduction
          fieldFlexEnd: true
        - type: number
          number:
            format: currency
            max: '99999999999999'
            precision: 0
          readOnly: true
          name: totalAssessableIncome
          label: Total Amount Of Assessable Income
          fieldFlexEnd: true
        - type: number
          number:
            format: currency
            max: '99999999999999'
            precision: 0
          readOnly: true
          name: deductedPersonalIncomeTaxAmount
          label: Personal Income Tax Amount Deducted
          fieldFlexEnd: true
        - type: number
          number:
            format: currency
            max: '99999999999999'
            precision: 0
          readOnly: true
          name: totalTaxPayable
          label: Total Tax Payable
          fieldFlexEnd: true
  footer:
    update: true
    updatedOn: updatedAt
    updatedBy: updatedBy
filter_config:
  fields:
    - type: selectAll
      label: Lock Status
      name: lockedStatus
      placeholder: Select Lock Status
      _options:
        transform: '[{''value'':''Y'',''label'':''Locked''},{''value'':''N'',''label'':''Unlocked''}]'
      labelType: type-grid
    - type: selectAll
      name: employees
      label: Employee
      placeholder: Select Employee
      isLazyLoad: true
      _options:
        transform: >-
          $employeesPayrollList($.extend.limit, $.extend.page,
          $.extend.search,$.extend.addOnValue.id1)
      labelType: type-grid
    - type: text
      label: PIT Code
      placeholder: Enter PIT Code
      name: taxCode
      labelType: type-grid
    - name: legalEntityCodes
      label: Legal Entity
      type: selectAll
      placeholder: Select Legal Entity
      isLazyLoad: true
      _options:
        transform: >-
          $legalListOfGroupTaxSettlementOrg($.extend.limit, $.extend.page,
          $.extend.search,$.extend.addOnValue.dataDetail.monthlyTaxDeclarationPeriodCode)
      labelType: type-grid
    - name: departmentCodes
      label: Department
      type: selectAll
      placeholder: Select Department
      isLazyLoad: true
      _options:
        transform: >-
          $departmentList($.extend.limit, $.extend.page,
          $.extend.search,$.extend.addOnValue.dataDetail.monthlyTaxDeclarationPeriodCode)
      labelType: type-grid
    - name: contractTypes
      label: Contract Type
      type: selectAll
      placeholder: Select Contract Type
      isLazyLoad: true
      _options:
        transform: $contractTypeList($.extend.limit, $.extend.page, $.extend.search)
      labelType: type-grid
    - name: residenceStatuses
      label: Residence Status
      type: selectAll
      placeholder: Select Residence Status
      isLazyLoad: true
      _options:
        transform: $residenceStatusList($.extend.limit, $.extend.page, $.extend.search)
      labelType: type-grid
    - name: jobIndicators
      label: Job Indicator
      type: selectAll
      placeholder: Select Job Indicator
      isLazyLoad: true
      _options:
        transform: $jobIndicatorList($.extend.limit, $.extend.page, $.extend.search)
      labelType: type-grid
    - name: tariffs
      label: Tax Bracket
      type: selectAll
      placeholder: Select Tax Bracket
      isLazyLoad: true
      _options:
        transform: $tariffList($.extend.limit, $.extend.page, $.extend.search)
      labelType: type-grid
    - name: updatedBy
      label: Last Modified By
      type: selectAll
      mode: multiple
      labelType: type-grid
      placeholder: Select Last Updated By
      _options:
        transform: $userList()
    - name: lastUpdatedOn
      labelType: type-grid
      label: Last Modified On
      type: dateRange
  filterMapping:
    - operator: $or
      valueField:
        - field: employeeId
          operator: $elemMatch
          valueField: employees.(employeeId)
        - field: employeeRecordNumber
          operator: $elemMatch
          valueField: employees.(employeeRecordNumber)
    - field: taxCode
      operator: $cont
      valueField: taxCode
    - field: lockedFilter
      operator: $in
      valueField: lockedStatus.(value)
    - field: legalEntityCode
      operator: $in
      valueField: legalEntityCodes.(value)
    - field: departmentCode
      operator: $in
      valueField: departmentCodes.(value)
    - field: contractTypeCode
      operator: $in
      valueField: contractTypes.(value)
    - field: residentStatusCode
      operator: $in
      valueField: residenceStatuses.(value)
    - field: jobIndicatorCode
      operator: $in
      valueField: jobIndicators.(value)
    - field: tariffCode
      operator: $in
      valueField: tariffs.(value)
    - field: currencyCode
      operator: $in
      valueField: currencies.(value)
    - field: totalSalaryIncome
      operator: $cont
      valueField: totalSalaryIncome
    - field: totalOtherIncome
      operator: $cont
      valueField: totalOtherIncome
    - field: personalDeductionAmount
      operator: $cont
      valueField: personalDeductionAmount
    - field: dependentDeductionAmount
      operator: $cont
      valueField: dependentDeductionAmount
    - field: insuranceDeductible
      operator: $cont
      valueField: insuranceDeductible
    - field: totalIncome
      operator: $cont
      valueField: totalIncome
    - field: totalTaxableSalaryIncome
      operator: $cont
      valueField: totalTaxableSalaryIncome
    - field: totalTaxableOtherIncome
      operator: $cont
      valueField: totalTaxableOtherIncome
    - field: totalAmountOfDeduction
      operator: $cont
      valueField: totalAmountOfDeduction
    - field: taxFreeIncomes
      operator: $cont
      valueField: taxFreeIncomes
    - field: totalAssessableIncome
      operator: $cont
      valueField: totalAssessableIncome
    - field: deductedPersonalIncomeTaxAmount
      operator: $cont
      valueField: deductedPersonalIncomeTaxAmount
    - field: updatedBy
      operator: $in
      valueField: updatedBy.(value)
    - field: updatedAt
      operator: $between
      valueField: lastUpdatedOn
  sources:
    contractTypeList:
      uri: '"/api/picklists/CONTRACTTYPE/values/pagination"'
      method: GET
      queryTransform: ' {''limit'': $.limit,''search'':$.search , ''page'': $.page, ''filter'':[{''field'':''status'',''operator'':''$eq'',''value'':true}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    residenceStatusList:
      uri: '"/api/picklists/RESIDENCESTATUS/values/pagination"'
      method: GET
      queryTransform: ' {''limit'': $.limit,''search'':$.search , ''page'': $.page, ''filter'':[{''field'':''status'',''operator'':''$eq'',''value'':true}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    jobIndicatorList:
      uri: '"/api/picklists/JOBINDICATOR/values/pagination"'
      method: GET
      queryTransform: ' {''limit'': $.limit,''search'':$.search , ''page'': $.page, ''filter'':[{''field'':''status'',''operator'':''$eq'',''value'':true}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    currencyList:
      uri: '"/api/picklists/CURRENCY/values/pagination"'
      method: GET
      queryTransform: ' {''limit'': $.limit,''search'':$.search , ''page'': $.page, ''filter'':[{''field'':''status'',''operator'':''$eq'',''value'':true}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    tariffList:
      uri: '"/api/tariff"'
      method: GET
      queryTransform: ' {''limit'': $.limit,''search'':$.search , ''page'': $.page, ''filter'':[{''field'':''status'',''operator'':''$eq'',''value'':true}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    employeesPayrollList:
      uri: '"/api/synthesizing-income/"  & $.recordId & "/raw-data-employees"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page, ''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label':
        $join($filter([$item.employeeId,$string($item.employeeRecordNumber),$item.employeeName],
        $boolean), ' - ')  , 'value': {'employeeId': $item.employeeId,
        'employeeRecordNumber': $string($item.employeeRecordNumber)},
        'employeeId': $item.employeeId, 'employeeRecordNumber':
        $string($item.employeeRecordNumber)}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - recordId
    legalListOfGroupTaxSettlementOrg:
      uri: '"/api/monthly-tax-declaration-period/legal-entities"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page, 'search': $.search,
        'sort':[{'field':'effectiveDate', 'order':'DESC'}],'filter':
        [{'field':'monthlyDeclarationPeriodCode','operator':'$eq','value':$.monthlyDeclarationPeriodCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($entity) {{'value': $entity.code,'label':
        $entity.longName & ' (' & $entity.code & ')'}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - monthlyDeclarationPeriodCode
    departmentList:
      uri: '"/api/monthly-tax-declaration-period/departments"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page, 'search': $.search,
        'sort':[{'field':'effectiveDate', 'order':'DESC'}],'filter':
        [{'field':'monthlyDeclarationPeriodCode','operator':'$eq','value':$.monthlyDeclarationPeriodCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($entity) {{'value': $entity.code,'label':
        $entity.longName & ' (' & $entity.code & ')'}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - monthlyDeclarationPeriodCode
    userList:
      uri: '"/api/internal-users"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.userName, 'value':
        $item.userName}})
      disabledCache: true
layout_options:
  page_header_options:
    visible: false
  toolTable:
    adjustDisplay: 'true'
  tool_table:
    - id: export
  show_detail_history: false
  show_actions_delete: false
  hide_action_row: true
  show_dialog_footer: true
  show_table_filter: true
  show_create_data_table: false
  is_export_grid: true
  progressing_info:
    title_progressing: Synthesizing data
    body_progressing:
      id: synthesizingIncomeId
      title: name
      progress: processing
      createdAt: createdAt
    is_cancel: false
    is_view_result: false
    get_progressing_list_api: /api/synthesizing-income/progressing
    update_progressing_list_api: /api/synthesizing-income/progressing
    unique_by: version
  disabled_click_row: true
layout_options__header_buttons: null
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions: null
backend_url: /api/synthesizing-income/{{parent.id}}/employees-income-summary
screen_name: null
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
parent: PIT.FS.FR.014_detail
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key: null
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Income Summary
  parent:
    title: Monthly tax declaration
