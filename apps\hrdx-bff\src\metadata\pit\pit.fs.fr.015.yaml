id: PIT.FS.FR.015
status: draft
sort: 124
user_created: 43878dbb-aa6c-4ef4-8bd7-0ea811ffafa7
date_created: '2024-08-09T09:51:08.701Z'
user_updated: cdfee544-f2ba-4ed2-ace8-5adc1ae0ed61
date_updated: '2025-07-17T10:21:43.446Z'
title: Regional Tax Allocation
requirement:
  time: 1748343255506
  blocks:
    - id: Hixd2PLYAl
      type: paragraph
      data:
        text: >-
          Chứ<PERSON> năng cho phép bộ phận nhân sự tập đoàn/CTTV thiết lập nguyên tắc
          phân bổ thuế của từng đơn vị theo từng location tương ứng
  version: 2.30.7
screen_design: null
module: PIT
local_fields:
  - code: effectiveDate
    title: Effective Start Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    sort: Descending
    pinned: true
    options__tabular__column_width: 12
  - code: effectiveDateTo
    title: Effective End Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    options__tabular__column_width: 12
  - code: country
    title: Country
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: group
    title: Group
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: company
    title: Company
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: legalEntity
    title: Legal Entity
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: taxCode
    title: Business Tax Code
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: department
    title: Department
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: managementTaxAgency
    title: Regional Tax Departments
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: note
    title: Note
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
  - code: updatedBy
    title: Last Updated By
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: updatedAt
    title: Last Updated On
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
mock_data:
  - effectiveDate: '2023-01-01'
    country: Việt Nam
    group: FPT
    company: FRT
    legalEntity: FPT Shop
    taxCode: '0311609355'
    department: Cửa hàng FPT Shop - Thành phố Bắc Giang
    provinceCity: Bắc Giang
    district: Thành phố Bắc Giang
    managementTaxAgency: Chi cục thuế Thành phố Bắc Giang
    note: Dữ liệu test
    createdBy: ChauPV
    createdOn: '2023-01-01 14:05:00'
    lastUpdatedBy: ChauPV
    lastUpdatedOn: '2023-01-01 14:05:00'
  - effectiveDate: '2023-01-01'
    country: Việt Nam
    group: FPT
    company: FRT
    legalEntity: FPT Shop
    taxCode: '0311609355'
    department: Cửa hàng FPT Shop - Hiệp Hòa
    provinceCity: Bắc Giang
    district: Hiệp Hòa
    managementTaxAgency: Chi cục thuế huyện Hiệp Hòa
    note: Dữ liệu test
    createdBy: ChauPV
    createdOn: '2023-01-01 14:05:00'
    lastUpdatedBy: ChauPV
    lastUpdatedOn: '2023-01-01 14:05:00'
  - effectiveDate: '2023-01-01'
    country: Việt Nam
    group: FPT
    company: FRT
    legalEntity: FPT Shop
    taxCode: '0311609355'
    department: Cửa hàng FPT Shop - Việt Yên
    provinceCity: Bắc Giang
    district: Việt Yên
    managementTaxAgency: Chi cục thuế huyện Việt Yên
    note: Dữ liệu test
    createdBy: ChauPV
    createdOn: '2023-01-01 14:05:00'
    lastUpdatedBy: ChauPV
    lastUpdatedOn: '2023-01-01 14:05:00'
local_buttons: null
layout: layout-table
form_config:
  formTitle:
    create: Add New Regional Tax Allocation
    edit: Edit Regional Tax Allocation
    view: Regional Tax Allocation Detail
  fields:
    - type: group
      _condition:
        transform: $not($.extend.formType = 'view')
      n_cols: 2
      fields:
        - name: effectiveDate
          label: Effective Start Date
          type: dateRange
          setting:
            format: dd/MM/yyyy
            type: date
          no_need_focus: true
          validators:
            - type: required
          mode: date-picker
          _value:
            transform: $isNilorEmpty($.extend.defaultValue.effectiveDate) ? $now()
          _condition:
            transform: $not($.extend.formType = 'view')
        - name: effectiveDateTo
          label: Effective End Date
          type: dateRange
          setting:
            format: dd/MM/yyyy
            type: date
          no_need_focus: true
          mode: date-picker
        - name: countryObject
          label: Country
          type: select
          placeholder: Select Country
          isLazyLoad: true
          _select:
            transform: $nationsList($.extend.limit, $.extend.page, $.extend.search)
          validators:
            - type: required
        - name: groupObject
          label: Group
          type: select
          placeholder: Select Group
          clearFieldsAfterChange:
            - companyObject
            - legalEntityObject
            - departmentObject
          isLazyLoad: true
          _select:
            transform: >-
              $groupsList($.extend.page,$.extend.limit,$.extend.search,$.fields.effectiveDate)
          validators:
            - type: required
        - col: 2
          name: companyObject
          label: Company
          type: select
          clearFieldsAfterChange:
            - legalEntityObject
            - departmentObject
          placeholder: Select Company
          validators:
            - type: required
          isLazyLoad: true
          _select:
            transform: >-
              $isNilorEmpty($.fields.groupObject)?[]:$companiesList($.fields.effectiveDate,
              $.fields.groupObject.value,$.extend.page,$.extend.limit,$.extend.search)
        - col: 2
          name: legalEntityObject
          label: Legal Entity
          type: select
          clearFieldsAfterChange:
            - departmentObject
          placeholder: Select Legal Entity
          _select:
            transform: >-
              $isNilorEmpty($.fields.companyObject)?[]:$legalEntitiesList($.fields.effectiveDate,
              $.fields.companyObject.value)
          validators:
            - type: required
        - col: 2
          type: group
          readOnly: true
          fieldBackground: rgba(192, 203, 216, 0.12)
          padding: 16px 24px
          borderRadius: 8px
          _condition:
            transform: $isNilorEmpty($.fields.legalEntityObject) = false
          fields:
            - name: taxCode
              label: Business Tax Code
              type: text
              _value:
                transform: $.variables._selectedLegalEntity.taxCode
        - col: 2
          name: departmentObject
          label: Department
          type: select
          placeholder: Select Department
          _select:
            transform: >-
              $isNilorEmpty($.fields.legalEntityObject) = false
              ?$departmentsList($.fields.effectiveDate,
              $.fields.legalEntityObject.value):[]
        - type: text
          name: provinceCityName
          label: Province/City
          _disabled:
            transform: 'true'
          placeholder: ''
          _value:
            transform: $.variables._currentProvinceCity.label
        - type: text
          name: districtName
          label: District
          _disabled:
            transform: 'true'
          placeholder: ''
          _value:
            transform: $.variables._currentDistrict.label
        - col: 2
          name: organizationTaxTypeObject
          label: Organization Tax Type
          type: select
          validators:
            - type: required
          placeholder: Select Organization Tax Type
          isLazyLoad: true
          _select:
            transform: $orgTaxTypeList($.extend.limit, $.extend.page, $.extend.search)
        - col: 2
          name: managementTaxAgencyObject
          label: Regional Tax Departments
          type: select
          validators:
            - type: required
          placeholder: Select Regional Tax Departments
          isLazyLoad: true
          _select:
            transform: $taxAgencyList($.extend.limit, $.extend.page, $.extend.search)
    - type: group
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - name: effectiveDate
          label: Effective Start Date
          type: dateRange
          setting:
            format: dd/MM/yyyy
            type: date
          mode: date-picker
        - name: effectiveDateTo
          label: Effective End Date
          type: dateRange
          setting:
            format: dd/MM/yyyy
            type: date
          mode: date-picker
        - name: countryObject
          label: Country
          type: select
          placeholder: Select Country
          _select:
            transform: $nationsList($.extend.limit, $.extend.page, $.extend.search)
          isLazyLoad: true
        - name: groupObject
          label: Group
          type: select
          placeholder: Select Group
          _select:
            transform: >-
              $groupsList($.extend.page,$.extend.limit,$.extend.search,$.fields.effectiveDate)
          isLazyLoad: true
        - name: companyObject
          label: Company
          type: select
          placeholder: Select Company
          isLazyLoad: true
          _select:
            transform: >-
              $companiesList($.fields.effectiveDate,
              $.fields.groupObject.value,$.extend.page,$.extend.limit,$.extend.search)
        - name: legalEntityObject
          label: Legal Entity
          type: select
          placeholder: Select Legal Entity
          _select:
            transform: >-
              $legalEntitiesList($.fields.effectiveDate,
              $.fields.companyObject.value)
          isLazyLoad: true
        - name: taxCode
          label: Business Tax Code
          type: text
        - name: departmentObject
          label: Department
          type: select
          placeholder: Select Department
          _select:
            transform: >-
              $departmentsList($.fields.effectiveDate,
              $.fields.legalEntityObject.value)
        - type: text
          name: provinceCityName
          label: Province/City
          _value:
            transform: $.variables._currentProvinceCity.label
        - type: select
          name: districtName
          label: District
          _value:
            transform: $.variables._currentDistrict.label
        - name: organizationTaxTypeName
          label: Organization Tax Type
          type: text
        - name: managementTaxAgencyObject
          label: Regional Tax Departments
          type: select
          placeholder: Select Regional Tax Departments
          _select:
            transform: $taxAgencyList($.extend.limit, $.extend.page, $.extend.search)
    - name: note
      label: Note
      type: textarea
      placeholder: Enter Note
      textarea:
        autoSize:
          minRows: 3
        maxCharCount: 1000
      validators:
        - type: maxLength
          args: 1000
          text: Maximum 1000 characters
  footer:
    create: true
    update: true
    createdOn: createdAt
    updatedOn: updatedAt
    createdBy: createdBy
    updatedBy: updatedBy
  sources:
    groupsList:
      uri: '"/api/groups/get-by"'
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator': '$eq','value':
        $.effectiveDate}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id}})[]
      disabledCache: true
      params:
        - page
        - limit
        - search
        - effectiveDate
    companiesList:
      uri: '"/api/companies/get-by"'
      queryTransform: >-
        {'limit': $.limit,'page': $.page, 'search': $.search, 'filter':
        [{'field':'status','operator':
        '$eq','value':true},{'field':'groupCode','operator': '$eq','value':
        $.groupCode }, {'field':'effectiveDate','operator': '$eq','value':
        $.effectiveDate}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id}})[]
      disabledCache: true
      params:
        - effectiveDate
        - groupCode
        - page
        - limit
        - search
    legalEntitiesList:
      uri: '"/api/legal-entities/get-list"'
      queryTransform: >-
        {'filter': [{'field':'status','operator':
        '$eq','value':true},{'field':'companyCode','operator': '$eq','value':
        $.companyCode }, {'field':'effectiveDate','operator': '$eq','value':
        $.effectiveDate}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - effectiveDate
        - companyCode
    legalEntitiesDetail:
      uri: '"/api/legal-entities/get-by"'
      queryTransform: >-
        {'limit': 1,'page': 1,'filter': [{'field':'status','operator':
        '$eq','value':true},{'field':'code','operator': '$eq','value':
        $.legalEntityCode }, {'field':'effectiveDate','operator': '$eq','value':
        $.effectiveDate}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id':
        $item.id,'locationCode':$item.locationCode?$item.locationCode,
        'taxCode':$item.taxCode?$item.taxCode:' '}})[]
      disabledCache: true
      params:
        - effectiveDate
        - legalEntityCode
    departmentsList:
      uri: '"/api/departments/get-list"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'status','operator':
        '$eq','value':true},{'field':'legalEntityCode','operator':
        '$eq','value': $.legalEntityCode },{'field':'effectiveDate','operator':
        '$eq','value': $.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id}})[]
      disabledCache: true
      params:
        - effectiveDate
        - legalEntityCode
    departmentDetail:
      uri: '"/api/departments/get-by"'
      queryTransform: >-
        {'limit': 1,'page': 1,'filter': [{'field':'status','operator':
        '$eq','value':true},{'field':'code','operator': '$eq','value':
        $.departmentCode }, {'field':'effectiveDate','operator': '$eq','value':
        $.effectiveDate}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $isNilorEmpty($.data)?{}:{'locationCode':$.data[0].locationCode}
      disabledCache: true
      params:
        - effectiveDate
        - departmentCode
    nationsList:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      queryTransform: '{''limit'': $.limit, ''page'': $.page, ''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    provincesList:
      uri: '"/api/picklists/PROVINCE/values/pagination"'
      method: GET
      queryTransform: >-
        {'limit': 1,'page': 1, 'search': $.search,
        'filter':[{'field':'code','operator': '$eq','value':$.code}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $isNilorEmpty($.data)?'NULL':{'label': $.data[0].name.default & '(' &
        $.data[0].code & ')', 'value': $.data[0].code}
      disabledCache: true
      params:
        - search
        - code
    districtsList:
      uri: '"/api/picklists/DISTRICT/values/pagination"'
      method: GET
      queryTransform: >-
        {'limit': 1,'page': 1, 'search': $.search,
        'filter':[{'field':'code','operator': '$eq','value':$.code}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $isNilorEmpty($.data)?'NULL':{'label': $.data[0].name.default & '(' &
        $.data[0].code & ')', 'value': $.data[0].code}
      disabledCache: true
      params:
        - search
        - code
    taxAgencyList:
      uri: '"/api/picklists/TAXAGENCY/values/pagination"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page, 'search': $.search, 'sort':
        [{'field':'code', 'order':'desc'} ]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    orgTaxTypeList:
      uri: '"/api/picklists/ORGTAXTYPE/values/pagination"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page, 'search': $.search, 'sort':
        [{'field':'code', 'order':'desc'} ]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    locationDetail:
      uri: '"/api/locations/get-by"'
      method: GET
      queryTransform: >-
        {'limit': 1,'page': 1, 'filter': [{'field':'code','operator':
        '$eq','value':$.code}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $isNilorEmpty($.data)?{}:{'provinceCityCode':$.data[0].provinceCity,'districtCode':$.data[0].district}
      disabledCache: true
      params:
        - code
  variables:
    _selectedLegalEntity:
      transform: >-
        $isNilorEmpty($.fields.legalEntityObject)?{}:
        $legalEntitiesDetail($.fields.effectiveDate,$.fields.legalEntityObject.value)[0]
    _selectedDepartment:
      transform: >-
        $isNilorEmpty($.fields.departmentObject)?{}:
        $departmentDetail($fields.effectiveDate,$.fields.departmentObject.value)
    _locationDetail:
      transform: >-
        $isNilorEmpty($.variables._selectedDepartment)?$isNilorEmpty($.variables._selectedLegalEntity.locationCode)?{}:$locationDetail($.variables._selectedLegalEntity.locationCode):
        $locationDetail($.variables._selectedDepartment.locationCode)
    _currentDistrict:
      transform: >-
        $isNilorEmpty($.variables._locationDetail)?{}:
        $districtsList($.variables._locationDetail.districtCode,$.variables._locationDetail.districtCode)
    _currentProvinceCity:
      transform: >-
        $isNilorEmpty($.variables._locationDetail)?{}:
        $provincesList($.variables._locationDetail.provinceCityCode,$.variables._locationDetail.provinceCityCode)
filter_config:
  fields:
    - labelType: type-grid
      name: effectiveDate
      label: Effective Date
      type: dateRange
    - labelType: type-grid
      name: country
      label: Country
      type: selectAll
      placeholder: Select Country
      isLazyLoad: true
      _options:
        transform: $nationsList($.extend.limit, $.extend.page, $.extend.search)
    - labelType: type-grid
      name: group
      label: Group
      type: selectAll
      placeholder: Select Group
      isLazyLoad: true
      _options:
        transform: $groupsList($.extend.limit, $.extend.page, $.extend.search)
    - labelType: type-grid
      name: company
      label: Company
      type: selectAll
      placeholder: Select Company
      isLazyLoad: true
      _options:
        transform: $companiesList($.extend.limit, $.extend.page, $.extend.search)
    - labelType: type-grid
      name: legalEntity
      label: Legal Entity
      type: selectAll
      placeholder: Select Legal Entity
      isLazyLoad: true
      _options:
        transform: $legalEntitiesList($.extend.limit, $.extend.page, $.extend.search)
    - labelType: type-grid
      name: taxCode
      label: Business Tax Code
      type: text
      placeholder: Enter Business Tax Code
    - labelType: type-grid
      name: department
      label: Department
      type: selectAll
      placeholder: Select Department
      isLazyLoad: true
      _options:
        transform: $departmentsList($.extend.limit, $.extend.page, $.extend.search)
    - labelType: type-grid
      name: managementTaxAgency
      label: Regional Tax Departments
      type: selectAll
      placeholder: Select Regional Tax Departments
      isLazyLoad: true
      _options:
        transform: $taxAgencyList($.extend.limit, $.extend.page, $.extend.search)
    - labelType: type-grid
      name: organizationTaxType
      label: Organization Tax Type
      type: selectAll
      placeholder: Select Organization Tax Type
      isLazyLoad: true
      _options:
        transform: $orgTaxTypeList($.extend.limit, $.extend.page, $.extend.search)
    - labelType: type-grid
      name: updatedBy
      label: Last Updated By
      type: selectAll
      mode: multiple
      placeholder: Select Last Updated By
      _options:
        transform: $userList()
    - labelType: type-grid
      name: updatedAt
      label: Last Updated On
      type: dateRange
  filterMapping:
    - field: code
      operator: $cont
      valueField: code
    - field: taxCode
      operator: $cont
      valueField: taxCode
    - field: groupCode
      operator: $in
      valueField: group.(value)
    - field: countryCode
      operator: $in
      valueField: country.(value)
    - field: companyCode
      operator: $in
      valueField: company.(value)
    - field: legalEntityCode
      operator: $in
      valueField: legalEntity.(value)
    - field: departmentCode
      operator: $in
      valueField: department.(value)
    - field: managementTaxAgencyCode
      operator: $in
      valueField: managementTaxAgency.(value)
    - field: organizationTaxTypeCode
      operator: $in
      valueField: organizationTaxType.(value)
    - field: effectiveDate
      operator: $between
      valueField: effectiveDate
    - field: updatedBy
      operator: $in
      valueField: updatedBy.(value)
    - field: updatedAt
      operator: $between
      valueField: updatedAt
  sources:
    groupsList:
      uri: '"/api/groups"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'sort': [{'field':'code',
        'order':'desc'} ] ,'filter': [{'field':'search','operator':
        '$eq','value': $.search},{'field':'status','operator':
        '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    companiesList:
      uri: '"/api/companies"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    legalEntitiesList:
      uri: '"/api/legal-entities"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    departmentsList:
      uri: '"/api/departments"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''status'': true}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    nationsList:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    taxAgencyList:
      uri: '"/api/picklists/TAXAGENCY/values/pagination"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'sort':
        [{'field':'code', 'order':'desc'} ]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    districtsList:
      uri: '"/api/picklists/DISTRICT/values/pagination"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    businessUnitList:
      uri: '"/api/business-units"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id':$item.id }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    divisionsList:
      uri: '"/api/divisions"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id':$item.id}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    citiesList:
      uri: '"/api/picklists/PROVINCE/values/pagination"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})
      disabledCache: true
      params:
        - limit
        - page
        - search
    userList:
      uri: '"/api/internal-users"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.userName, 'value':
        $item.userName}})
      disabledCache: true
    orgTaxTypeList:
      uri: '"/api/picklists/ORGTAXTYPE/values/pagination"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page, 'search': $.search, 'sort':
        [{'field':'code', 'order':'desc'} ]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
layout_options:
  show_dialog_form_save_add_button: true
  toolTable:
    adjustDisplay: 'true'
  show_detail_history: false
  tool_table:
    - id: export
  delete_multi_items: true
  is_export_grid: true
  hide_action_row: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    icon: icon-pencil-simple
    type: ghost-gray
  - id: delete
    icon: icon-trash
    type: ghost-gray
backend_url: /api/principles-tax-location
screen_name: principles-tax-location
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: companyCode
    defaultName: CompanyCode
  - name: legalEntityCode
    defaultName: LegalEntityCode
  - name: countryCode
    defaultName: CountryCode
  - name: departmentCode
    defaultName: DepartmentCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Regional Tax Allocation
  parent:
    title: Entity Setting
