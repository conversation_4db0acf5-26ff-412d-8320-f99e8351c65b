id: PIT.FS.FR.037
status: draft
sort: 616
user_created: c712040f-6a4d-4c9b-86bb-1edd932ec5e0
date_created: '2024-09-23T09:39:27.296Z'
user_updated: cdfee544-f2ba-4ed2-ace8-5adc1ae0ed61
date_updated: '2025-07-22T02:20:30.811Z'
title: Manage Residence Status
requirement:
  time: 1747370044004
  blocks:
    - id: ZR8IH1VSu9
      type: paragraph
      data:
        text: >-
          - <PERSON><PERSON><PERSON> năng cho phép bộ phận nhân sự CTTV thiết lập, quản lý thông tin
          tình trạng cư trú của CBNV&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
  version: 2.30.7
screen_design: null
module: PIT
local_fields:
  - code: employeeId
    title: Employee ID
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    pinned: true
    show_sort: true
  - code: employee<PERSON><PERSON>ordNumber
    title: Employee Record Number (ERN)
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: name
    title: Employee Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: residenceStatusName
    title: Residence Status
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: effectiveDate
    title: Effective Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
  - code: jobIndicatorName
    title: Job Indicator
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: groupName
    title: Group
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: companyName
    title: Company
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: legalEntityName
    title: Legal Entity
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: businessUnitName
    title: Business Unit
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: divisionName
    title: Division
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: departmentName
    title: Department
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: jobName
    title: Job Title
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: note
    title: Note
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
  - code: updatedBy
    title: Last Updated By
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: updatedAt
    title: Last Updated On
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
mock_data: []
local_buttons: null
layout: layout-table
form_config:
  formTitle:
    create: Add New Manage Residence Status
    edit: Edit Manage Residence Status
    view: Manage Residence Status Detail
  formSize:
    view: small
    create: largex
    edit: largex
  fields:
    - name: effectiveDate
      label: Effective Date
      type: dateRange
      setting:
        format: dd/MM/yyyy
        type: date
      validators:
        - type: required
      mode: date-picker
      _value:
        transform: $isNilorEmpty($.extend.defaultValue.effectiveDate) ? $now()
      _condition:
        transform: $not($.extend.formType = 'view')
    - type: select
      name: employee
      label: Employee
      placeholder: Select Employee
      isLazyLoad: true
      validators:
        - type: required
      _select:
        transform: >-
          $employeesList($.extend.limit, $.extend.page,
          $.extend.search,'','','', $.fields.effectiveDate)
      _value:
        transform: $.variables._employee[0].value
      _condition:
        transform: $boolean($.extend.formType = 'create')
      outputValue: value
    - type: select
      name: employee
      label: Employee
      placeholder: Select Employee
      validators:
        - type: required
      _select:
        transform: $.variables._employee
      _value:
        transform: $.variables._dataEdit
      disabled: true
      _condition:
        transform: $.extend.formType = 'edit'
      outputValue: value
    - type: text
      name: employeeId
      label: Employee ID
      unvisible: true
      _value:
        transform: $.fields.employee.employeeId
    - type: text
      name: employeeRecordNumber
      label: Employee Record Number (ERN)
      unvisible: true
      _value:
        transform: $string($.fields.employee.employeeRecordNumber)
    - type: group
      _condition:
        transform: $.extend.formType = 'view'
      space: 12
      fields:
        - type: dateRange
          label: Effective Date
          name: effectiveDate
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
        - type: text
          name: employeeId
          label: Employee ID
        - type: text
          name: employeeRecordNumber
          label: Employee Record Number (ERN)
        - type: text
          name: name
          label: Employee Name
        - type: text
          name: jobIndicatorName
          label: Job Indicator
        - type: text
          name: groupName
          label: Group
        - type: text
          name: companyName
          label: Company
        - type: text
          name: legalEntityName
          label: Legal Entity
        - type: text
          name: divisionName
          label: Division
        - type: text
          name: businessUnitName
          label: Business Unit
        - type: text
          name: departmentName
          label: Department
        - type: text
          name: jobName
          label: Job Title
        - name: residenceStatus
          label: Residence Status
          type: radio
          radio:
            - label: Cư trú
              value: true
            - label: Không cư trú
              value: false
    - name: residenceStatus
      label: Residence Status
      type: radio
      value: true
      outputValue: value
      _condition:
        transform: $.extend.formType = 'edit' or $.extend.formType = 'create'
      radio:
        - label: Cư trú
          value: true
        - label: Không cư trú
          value: false
      validators:
        - type: required
    - type: textarea
      label: Note
      name: note
      placeholder: Enter Note
      textarea:
        autoSize:
          minRows: 3
        maxCharCount: 1000
      validators:
        - type: maxLength
          args: 1000
          text: Maximum 1000 characters
    - name: employeeIdInvisible
      type: select
      dependantField: $.fields.employee
      unvisible: true
      _value:
        transform: >-
          $exists($.variables._totalOverview) and
          $boolean($.variables._totalOverview)?$.variables._totalOverview:undefined
  overview:
    dependentField: employeeIdInvisible
    border: true
    _condition:
      transform: $.extend.formType = 'create' or $.extend.formType = 'edit'
    title: Employee Detail
    uri: ''
    display:
      - label: Group
        _value:
          transform: >-
            $exists($.variables._totalOverview.groupName)?$.variables._totalOverview.groupName:'--'
      - label: Company
        _value:
          transform: >-
            $exists($.variables._totalOverview.company)?$.variables._totalOverview.company:'--'
      - label: Legal Entity
        _value:
          transform: >-
            $exists($.variables._totalOverview.legalEntity)?$.variables._totalOverview.legalEntity:'--'
      - label: Business Unit
        _value:
          transform: >-
            $exists($.variables._totalOverview.businessUnit)?$.variables._totalOverview.businessUnit:'--'
      - label: Division
        _value:
          transform: >-
            $exists($.variables._totalOverview.division)?$.variables._totalOverview.division:'--'
      - label: Department
        _value:
          transform: >-
            $exists($.variables._totalOverview.department)?$.variables._totalOverview.department:'--'
      - label: Job Title
        _value:
          transform: >-
            $exists($.variables._totalOverview.jobLongNameTitle)?$.variables._totalOverview.jobLongNameTitle:'--'
  footer:
    create: true
    update: true
    createdOn: createdAt
    updatedOn: updatedAt
    createdBy: createdBy
    updatedBy: updatedBy
  sources:
    employeesList:
      uri: '"/api/personals/"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page, 'search': $.search, 'filter':
        [{'field':'employeeId','operator':
        '$eq','value':$.empId},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'jobDataEffectiveDateFrom','operator':
        '$lte','value':$.effectiveDate},{'operator': '$or',
        'value':[{'field':'jobDataEffectiveDateTo','operator': '$eq','value':
        'NULL'},{'field':'jobDataEffectiveDateTo','operator':
        '$gt','value':$.effectiveDate}]},{'field':'employeeGroupCode','operator':
        '$cont','value':$.grpCode},{'field':'employeeRecordNumber','operator':
        '$eq','value':$.ern}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label':
        $join($filter([$item.employeeId,$item.employeeGroupCode,$string($item.employeeRecordNumber),$item.fullName,$item.jobIndicatorName],
        $boolean), ' - ')  , 'value': {'employeeId': $item.employeeId,
        'employeeRecordNumber': $string($item.employeeRecordNumber),
        'jobDataId': $item.jobDataId}, 'employeeId': $item.employeeId,
        'employeeRecordNumber': $string($item.employeeRecordNumber),'jobDataId':
        $item.jobDataId}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - empId
        - grpCode
        - ern
        - effectiveDate
    companiesDetail:
      uri: '"/api/companies"'
      method: GET
      queryTransform: >-
        {'limit': 1, 'filter': [{'field':'code','operator':
        '$eq','value':$.companyCode},{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator': '$lte','value':
        $exists($.effectiveDate) and
        $boolean($.effectiveDate)?$.effectiveDate:$now()}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $.data[]
      disabledCache: true
      params:
        - effectiveDate
        - companyCode
    jobDetail:
      uri: '"/api/job-codes"'
      method: GET
      queryTransform: >-
        {'limit': 1, 'filter': [{'field':'code','operator':
        '$eq','value':$.jobCode},{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator': '$lte','value':
        $exists($.effectiveDate) and
        $boolean($.effectiveDate)?$.effectiveDate:$now()}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $.data[]
      disabledCache: true
      params:
        - effectiveDate
        - jobCode
    overviewDetail:
      uri: '"/api/personals/"  & $.employeeId & "/job-datas/overview/" & $.jobDataId'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $
      disabledCache: true
      params:
        - employeeId
        - jobDataId
  variables:
    _employee:
      transform: >-
        $employeesList(1,1,'',$.extend.defaultValue.employeeId,$.extend.defaultValue.employeeGroupCode,$.extend.defaultValue.employeeRecordNumber,$.extend.defaultValue.effectiveDate)
    _dataEdit:
      transform: $.variables._employee[0].value
    _overviewDetail:
      transform: >-
        ($.fields.employee; $exists($.fields.employee) and
        $boolean($.fields.employee)?$overviewDetail($.fields.employee.employeeId,
        $.fields.employee.jobDataId):{})
    _foundedCompaniesDetail:
      transform: >-
        ($.variables._overviewDetail; $exists($.variables._overviewDetail) and
        $boolean($.variables._overviewDetail.companyCode)?$companiesDetail($.fields.effectiveDate,
        $.variables._overviewDetail.companyCode))
    _getFirstCompaniesDetail:
      transform: >-
        ($.variables._foundedCompaniesDetail;
        $count($.variables._foundedCompaniesDetail)>0?$.variables._foundedCompaniesDetail[0]:{})
    _foundedJobDetail:
      transform: >-
        ($.variables._overviewDetail; $exists($.variables._overviewDetail) and
        $boolean($.variables._overviewDetail.companyCode)?$jobDetail($.fields.effectiveDate,
        $.variables._overviewDetail.jobCode))
    _getFirstJobDetail:
      transform: >-
        ($.variables._foundedJobDetail;
        $count($.variables._foundedJobDetail)>0?$.variables._foundedJobDetail[0]:{})
    _totalOverview:
      transform: >-
        $exists($.fields.employee) and
        $boolean($.fields.employee)?$merge([$.variables._overviewDetail,{'groupName':
        $.variables._getFirstCompaniesDetail.groupName,'jobLongNameTitle':$.variables._getFirstJobDetail.longName.default}]):null
filter_config:
  fields:
    - type: selectAll
      name: employee
      label: Employee
      placeholder: Select Employee
      isLazyLoad: true
      labelType: type-grid
      _options:
        transform: $employeesList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      name: groupCode
      label: Group
      placeholder: Select Group
      isLazyLoad: true
      labelType: type-grid
      _options:
        transform: $groupsList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      name: companyCode
      label: Company
      placeholder: Select Company
      isLazyLoad: true
      labelType: type-grid
      _options:
        transform: $companiesList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      name: legalEntityCode
      label: Legal Entity
      placeholder: Select Legal Entity
      labelType: type-grid
      isLazyLoad: true
      _options:
        transform: $legalEntitiesList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      name: businessUnitCode
      label: Business Unit
      placeholder: Select Business Unit
      isLazyLoad: true
      labelType: type-grid
      _options:
        transform: $businessUnitList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      name: divisionCode
      label: Division
      placeholder: Select Division
      labelType: type-grid
      isLazyLoad: true
      _options:
        transform: $divisionList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      name: departmentCode
      label: Department
      labelType: type-grid
      placeholder: Select Department
      isLazyLoad: true
      _options:
        transform: $departmentsList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      name: jobCode
      label: Job Title
      labelType: type-grid
      placeholder: Select Job
      isLazyLoad: true
      _options:
        transform: $jobCodesList($.extend.limit, $.extend.page, $.extend.search)
    - name: residenceStatus
      label: Residence Status
      type: radio
      radio:
        - label: All
          value: ''
        - label: Cư trú
          value: 'true'
        - label: Không cư trú
          value: 'false'
      labelType: type-grid
      value: ''
    - type: dateRange
      label: Effective Date
      name: effectiveDate
      labelType: type-grid
    - name: updatedBy
      label: Last Updated By
      type: selectAll
      mode: multiple
      labelType: type-grid
      placeholder: Select Last Updated By
      _options:
        transform: $userList()
    - type: dateRange
      label: Last Updated On
      labelType: type-grid
      name: updatedAt
  filterMapping:
    - field: employeeSearch
      operator: $in
      valueField: employee.(value)
    - field: jobIndicatorCode
      operator: $in
      valueField: jobIndicatorCode.(value)
    - field: jobCode
      operator: $in
      valueField: jobCode.(value)
    - field: residenceStatus
      operator: $eq
      valueField: residenceStatus
    - field: groupCode
      operator: $in
      valueField: groupCode.(value)
    - field: companyCode
      operator: $in
      valueField: companyCode.(value)
    - field: legalEntityCode
      operator: $in
      valueField: legalEntityCode.(value)
    - field: divisionCode
      operator: $in
      valueField: divisionCode.(value)
    - field: businessUnitCode
      operator: $in
      valueField: businessUnitCode.(value)
    - field: departmentCode
      operator: $in
      valueField: departmentCode.(value)
    - field: effectiveDate
      operator: $between
      valueField: effectiveDate
    - field: updatedBy
      operator: $in
      valueField: updatedBy.(value)
    - field: updatedAt
      operator: $between
      valueField: updatedAt
  sources:
    employeesList:
      uri: '"/api/personals/"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page, 'search': $.search, 'filter':
        [{'field':'employeeId','operator':
        '$eq','value':$.empId},{'field':'employeeGroupCode','operator':
        '$cont','value':$.grpCode},{'field':'employeeRecordNumber','operator':
        '$eq','value':$.ern}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label':
        $join($filter([$item.employeeId,$item.employeeGroupCode,$string($item.employeeRecordNumber),$item.fullName,$item.jobIndicatorName],
        $boolean), ' - ')  , 'value':  $item.employeeId & '_' &
        $item.employeeRecordNumber}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - empId
        - grpCode
        - ern
    groupsList:
      uri: '"/api/groups"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'filter':
        [{'field':'search','operator': '$eq','value':
        $.search},{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    divisionList:
      uri: '"/api/divisions"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    businessUnitList:
      uri: '"/api/business-units"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    companiesList:
      uri: '"/api/companies"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    legalEntitiesList:
      uri: '"/api/legal-entities"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    departmentsList:
      uri: '"/api/departments"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''status'': true}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    jobCodesList:
      uri: '"/api/job-codes"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    userList:
      uri: '"/api/internal-users"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.userName, 'value':
        $item.userName}})
      disabledCache: true
    residenceStatusList:
      uri: '"/api/picklists/RESIDENCESTATUS/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $append({'label': 'All', 'value': ''},$map($.data, function($item)
        {{'label': $item.name.default & ' (' & $item.code & ')', 'value':
        $item.code}}))[]
      disabledCache: true
  variables:
    _userList:
      transform: $userList()
layout_options:
  toolTable:
    adjustDisplay: true
  tool_table:
    - id: export
  show_dialog_form_save_add_button: true
  show_detail_history: false
  delete_multi_items: true
  is_export_grid: true
  hide_action_row: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: null
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    title: Edit
    icon: edit
    type: ghost-gray
  - id: delete
    title: Delete
    icon: trash
    type: ghost-gray
backend_url: /api/manage-residence-status
screen_name: manage-residence-status
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: employeeRecordNumber
    defaultName: EmployeeRecordNumber
  - name: employeeId
    defaultName: EmployeeId
  - name: companyCode
    defaultName: CompanyCode
  - name: legalEntityCode
    defaultName: LegalEntityCode
  - name: jobCode
    defaultName: JobCode
  - name: departmentCode
    defaultName: DepartmentCode
  - name: divisionCode
    defaultName: DivisionCode
  - name: businessUnitCode
    defaultName: BusinessUnitCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Manage Residence Status
  parent:
    title: Manage Employee Infor
