id: PIT.FS.FR.049
status: draft
sort: 364
user_created: 43878dbb-aa6c-4ef4-8bd7-0ea811ffafa7
date_created: '2024-08-02T03:59:55.004Z'
user_updated: b403d5da-d684-45e4-ae6d-e25223baedc5
date_updated: '2025-07-07T02:25:19.390Z'
title: Finalize Attendance/Payroll/Personal Income Tax
requirement:
  time: 1722571192222
  blocks:
    - id: JH_2LlJWzk
      type: paragraph
      data:
        text: >-
          <PERSON><PERSON><PERSON> năng cho phép user thực hiện hoàn thành hoặc hủy việc t<PERSON>h l<PERSON>,
          tính thuế
  version: 2.29.1
screen_design: null
module: PIT
local_fields:
  - code: finalizeType
    title: Type
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: period
    title: Period
    description: Load period information
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: subPeriod
    title: Sub period
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: startDate
    title: Start date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
  - code: endDate
    title: End date
    description: Load end date information
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
  - code: actions
    title: Action
    description: Load action information
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: status
    title: Status
    description: Load status information
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
mock_data:
  - type: Payroll
    period: Kỳ lương tháng 01/2023
    subPeriod: Kỳ lương đợt 1 tháng 01/2023
    startDate: '2023-01-01'
    endDate: '2023-01-31'
    action: Finalize
    status: Completed
    createdBy: AnhTT
    createdOn: '2023-01-02 09:15:00'
  - type: Absense
    period: Kỳ công tháng 01/2023
    subPeriod: Kỳ công tháng 01/2023
    startDate: '2023-01-01'
    endDate: '2023-01-31'
    action: Finalize
    status: Completed
    createdBy: AnhTT
    createdOn: '2023-01-02 10:00:00'
  - type: Personal Income Tax
    period: Kỳ kê khai tháng 01/2023
    subPeriod: Kỳ kê khai tháng 01/2023
    startDate: '2023-01-01'
    endDate: '2023-01-31'
    action: Finalize
    status: Completed
    createdBy: AnhTT
    createdOn: '2023-01-02 10:00:00'
  - type: Personal Income Tax
    period: Kỳ kê khai tháng 01/2023
    subPeriod: Kỳ kê khai tháng 01/2023
    startDate: '2023-01-01'
    endDate: '2023-01-31'
    action: Cancel
    status: In Progress
    createdBy: AnhTT
    createdOn: '2023-01-02 10:10:00'
local_buttons: null
layout: layout-table
form_config:
  authAction: Calculation
  type: group
  n_cols: 1
  fields:
    - type: radio
      label: Action
      name: isCancel
      labelType: flex-row
      _value:
        transform: $.extend.formType = 'create' ? false
      _condition:
        transform: $not($.extend.formType = 'view')
      outputValue: value
      radio:
        - value: false
          label: Finalize
        - value: true
          label: Cancel
    - type: group
      n_cols: 3
      fields:
        - type: select
          name: finalizeTypeCode
          label: Type
          placeholder: Select Type
          labelType: flex-row
          _condition:
            transform: $not($.extend.formType = 'view')
          dependantField: $.fields.isCancel
          validators:
            - type: required
          outputValue: value
          _select:
            transform: >-
              $finalizationTypeList($.extend.limit, $.extend.page,
              $.extend.search)
          isLazyLoad: true
        - type: select
          name: periodCode
          label: Period
          placeholder: Select Period
          labelType: flex-row
          validators:
            - type: required
          dependantField: $.fields.finalizeTypeCode;$.fields.isCancel
          outputValue: value
          _condition:
            transform: $not($.extend.formType = 'view')
          isLazyLoad: true
          _select:
            transform: >-
              $.fields.finalizeTypeCode = 'PERSONAL_INCOME_TAX' ?
              $periodList($.fields.isCancel,$.extend.limit, $.extend.page,
              $.extend.search) : $.fields.finalizeTypeCode = 'PAYROLL' ?
              $payrollPeriodList($.extend.limit, $.extend.page, $.extend.search)
              : $.fields.finalizeTypeCode = 'ABSENSE' ?
              $managerTimekeepingsList($.fields.isCancel,$.extend.limit,
              $.extend.page, $.extend.search) : []
        - type: select
          name: subPeriodCode
          label: Sub-Period
          placeholder: Select Sub Period
          dependantField: $.fields.finalizeTypeCode;$.fields.periodCode;$.fields.isCancel
          _class:
            transform: '$.fields.finalizeTypeCode = ''PAYROLL''? ''required'': ''unrequired'''
          outputValue: value
          labelType: flex-row
          _disabled:
            transform: $not($.fields.finalizeTypeCode = 'PAYROLL')
          _condition:
            transform: $not($.extend.formType = 'view')
          isLazyLoad: true
          _select:
            transform: >-
              $payrollSubPeriodList($.fields.periodCode,$.fields.isCancel,$.extend.limit,
              $.extend.page, $.extend.search)
    - type: group
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - type: text
          name: finalizeType
          label: Type
          _value:
            transform: >-
              $.extend.defaultValue.finalizeType ?
              $.extend.defaultValue.finalizeType & ' (' &
              $.extend.defaultValue.finalizeTypeCode & ')'
        - name: periodName
          label: 'Period '
          type: text
          _value:
            transform: >-
              $.extend.defaultValue.period ? $.extend.defaultValue.period & ' ('
              & $.extend.defaultValue.periodCode & ')'
        - name: subPeriodName
          label: Sub-Period
          type: select
          outputValue: value
          _value:
            transform: >-
              $.extend.defaultValue.subPeriod ? $.extend.defaultValue.subPeriod
              & ' (' & $.extend.defaultValue.subPeriodCode & ')'
        - type: dateRange
          label: Start Date
          name: startDate
          mode: date-picker
        - type: dateRange
          label: End Date
          name: endDate
          mode: date-picker
        - type: text
          label: Action
          name: actions
        - type: text
          label: Status
          name: status
  sources:
    finalizationTypeList:
      uri: '"/api/finalization-type"'
      method: GET
      queryTransform: ' {''limit'': $.limit, ''page'': $.page, ''filter'':[{''field'':''search'',''operator'':''$eq'',''value'':$.search}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.longName.default}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    periodList:
      uri: '"/api/monthly-tax-declaration-period/by-finalize"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page,'search': $.search, 'filter':
        [{'field':'isCancel','operator': '$eq','value': $.isCancel}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - isCancel
        - limit
        - page
        - search
    payrollPeriodList:
      uri: '"/api/payroll-periods"'
      method: GET
      queryTransform: '{''limit'': $.limit, ''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    payrollSubPeriodList:
      uri: '"/api/payroll-period-settings"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page,'search': $.search, 'filter':
        [{'field':'payrollPeriodCode','operator': '$eq','value':
        $.periodCode},{'field':'payrollStatus','operator': '$eq','value':
        $.isCancel? 'Processing' : 'Completed'}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.id}})[]
      disabledCache: true
      params:
        - periodCode
        - isCancel
        - limit
        - page
        - search
    managerTimekeepingsList:
      uri: '"/api/ts-total-attendances/ts-manager-timekeeping"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page,'search': $.search, 'filter':
        [{'field': 'status', 'operator': '$eq', 'value': $.isCancel ? 2 : 3}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label':  $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - isCancel
        - limit
        - page
        - search
  footer:
    create: true
    createdOn: createdAt
    createdBy: createdBy
    updatedBy: updatedBy
filter_config:
  fields:
    - type: selectAll
      name: finalizeTypeCode
      label: Type
      placeholder: Select Type
      clearFieldsAfterChange:
        - periodCode
        - subPeriodCode
      _options:
        transform: $finalizationTypeList($.extend.limit, $.extend.page, $.extend.search)
      isLazyLoad: true
    - type: selectAll
      name: periodCode
      label: Period
      placeholder: Select Period
      mode: multiple
      _options:
        transform: $.variables._periodListTotal
    - type: selectAll
      name: subPeriodCode
      label: Sub-period
      placeholder: Select Sub period
      mode: multiple
      isLazyLoad: true
      _options:
        transform: >-
          $.fields.finalizeTypeCode.value = 'PAYROLL' ?
          $payrollSubPeriodList($.extend.limit,$.extend.page,$.extend.search,
          $.fields.finalizeTypeCode.value = 'PAYROLL'?
          $map($.fields.periodCode,function($item){$item.value})[]):[]
    - type: dateRange
      name: startDate
      label: Start date
      settings:
        format: dd/MM/yyyy
        mode: date
    - type: dateRange
      name: endDate
      label: End date
      settings:
        format: dd/MM/yyyy
        mode: date
    - type: selectAll
      mode: multiple
      name: action
      label: Action
      placeholder: Select Action
      _options:
        transform: $.variables._actionList
    - type: selectAll
      mode: multiple
      name: status
      label: Status
      placeholder: Select Status
      _options:
        transform: $.variables._statusList
  filterMapping:
    - valueField: finalizeTypeCode.(value)
      field: finalizeTypeCode
      operator: $in
    - valueField: periodCode.(value)
      field: periodCode
      operator: $in
    - valueField: subPeriodCode.(value)
      field: subPeriodCode
      operator: $in
    - valueField: startDate
      field: startDate
      operator: $between
    - valueField: endDate
      field: endDate
      operator: $between
    - valueField: action.(value)
      field: actionCode
      operator: $in
    - valueField: status.(value)
      field: statusCode
      operator: $in
  sources:
    finalizationTypeList:
      uri: '"/api/finalization-type"'
      method: GET
      queryTransform: ' {''limit'': $.limit, ''page'': $.page, ''filter'':[{''field'':''search'',''operator'':''$eq'',''value'':$.search}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.longName.default}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    payrollSubPeriodList:
      uri: '"/api/payroll-period-settings"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'payrollPeriodCode','operator': '$in','value':
        $.payrollPeriodCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - payrollPeriodCode
    managerTimekeepingsList:
      uri: '"/api/ts-total-attendances/ts-manager-timekeeping"'
      method: GET
      queryTransform: '{''limit'':5000}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label':  $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
    periodList:
      uri: '"/api/monthly-tax-declaration-period/by-finalize"'
      method: GET
      queryTransform: '{''limit'':5000}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
    payrollPeriodList:
      uri: '"/api/payroll-periods"'
      method: GET
      queryTransform: '{''limit'':5000}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
  variables:
    _actionList:
      transform: '[{''label'': ''Finalize'',''value'': 0},{''label'': ''Cancel'',''value'': 1}]'
    _statusList:
      transform: >-
        [{'label':'Started','value':'0'},{'label':'In
        Progress','value':'1'},{'label':'Completed','value':'2'},{'label':'Failed','value':'3'},{'label':'Recovered','value':'4'},{'label':'Interrupted','value':'5'},{'label':'Skipped','value':'6'}]
    _periodList:
      transform: $periodList()
    _payrollPeriodList:
      transform: $payrollPeriodList()
    _managerTimekeepingsList:
      transform: $managerTimekeepingsList()
    _periodListTotal:
      transform: >-
        $.fields.finalizeTypeCode.value = 'PERSONAL_INCOME_TAX' ?
        $.variables._periodList : $.fields.finalizeTypeCode.value = 'PAYROLL' ?
        $.variables._payrollPeriodList : $.fields.finalizeTypeCode.value =
        'ABSENSE' ? $.variables._managerTimekeepingsList: []
layout_options:
  show_detail_history: false
  expand_create_form:
    submit_button_title: Progress
  custom_create_api:
    url: /api/finalize-payroll
  show_dialog_footer:
    view: false
    filter: true
  hide_action_row: true
  tool_table:
    - id: export
  page_header_description: Attendance/ Payroll/ Personal Income Tax Run
  is_export_grid: true
layout_options__header_buttons: null
options: null
create_form: null
layout_options__footer_buttons: null
layout_options__row_actions: null
backend_url: api/finalization-attendance-payroll-tax
screen_name: finalize-attendance-payroll-personal-income-tax
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key: null
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Finalize absense, payroll and personal income tax
  parent:
    title: Function
