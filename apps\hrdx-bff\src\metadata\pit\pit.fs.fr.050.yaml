id: PIT.FS.FR.050
status: draft
sort: null
user_created: cdfee544-f2ba-4ed2-ace8-5adc1ae0ed61
date_created: '2024-12-30T08:47:42.277Z'
user_updated: cdfee544-f2ba-4ed2-ace8-5adc1ae0ed61
date_updated: '2025-06-13T06:53:12.111Z'
title: Manage Form/Seri Tax
requirement:
  time: 1748328347748
  blocks:
    - id: eUblIVu0EW
      type: paragraph
      data:
        text: >-
          - <PERSON>ứ<PERSON> năng đáp ứng các yêu cầu thêm mới, chỉnh sửa, xóa và tìm kiếm
          danh sách các thông tin về pháp nhân thuế tương ứng với nghiệp vụ của
          từng CTTV
  version: 2.30.7
screen_design: null
module: PIT
local_fields:
  - code: businessTaxCode
    title: Business Tax Code
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    pinned: false
    show_sort: true
  - code: legalEntityName
    title: Legal Entity
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: effectiveDate
    title: Effective Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
  - code: status
    title: Status
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: Boolean Tag
      collection: field_types
  - code: address
    title: Address
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: telephone
    title: Telephone
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: note
    title: Note
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
  - code: updatedBy
    title: Last Updated By
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: updatedAt
    title: Last Updated On
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
mock_data: []
local_buttons: null
layout: layout-table
form_config:
  formSize:
    create: large
    edit: large
    proceed: large
  formTitle:
    create: Add New Form/Seri Tax
    edit: Edit Form/Seri Tax
    view: Form/Seri Tax Detail
  fields:
    - type: group
      label: Business Tax Code Info
      collapse: false
      disableEventCollapse: true
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - name: businessTaxCodeObject
          label: Business Tax Code
          type: select
          placeholder: Select Business Tax Code
          outputValue: value
          isLazyLoad: true
          _select:
            transform: >-
              $businessTaxCodeList($.extend.limit, $.extend.page,
              $.extend.search)
          _disabled:
            transform: $.extend.formType = 'edit'
          validators:
            - type: required
        - name: legalEntityName
          label: Legal Entity
          type: text
          placeholder: System - Generated
          dependantField: $.fields.businessTaxCodeObject
          _value:
            transform: $.fields.businessTaxCodeObject.legalEntityName
          _disabled:
            transform: 'true'
        - type: group
          n_cols: 2
          fields:
            - type: dateRange
              label: Effective Date
              name: effectiveDate
              placeholder: dd/MM/yyyy
              mode: date-picker
              setting:
                format: dd/MM/yyyy
                type: date
              no_need_focus: true
              validators:
                - type: required
              _value:
                transform: $.extend.formType = 'create' ? $now()
            - name: status
              label: Status
              type: radio
              _value:
                transform: $.extend.formType = 'create' ? true
              radio:
                - label: Active
                  value: true
                - label: Inactive
                  value: false
            - name: address
              label: 'Address '
              type: text
              placeholder: Enter Address
              formatFn:
                - trim
              validators:
                - type: required
                - type: maxLength
                  args: 200
                  text: Maximum 200 characters
            - name: telephone
              label: Telephone
              type: tel
              placeholder: Enter Telephone
              validators:
                - type: maxLength
                  args: 15
                  text: Maximum 15 characters
        - type: translationTextArea
          label: Note
          name: note
          placeholder: Enter Note
          textarea:
            autoSize:
              minRows: 3
            maxCharCount: 1000
          validators:
            - type: maxLength
              args: 1000
              text: Maximum 1000 characters
    - type: group
      label: Form/Seri
      collapse: false
      disableEventCollapse: true
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - type: array
          mode: table
          name: details
          _condition:
            transform: $not($.extend.formType = 'view')
          minSize: 1
          arrayOptions:
            canChangeSize: true
          field:
            type: group
            skipCloneGroupControl: true
            fields:
              - name: form
                label: Form
                type: text
                validators:
                  - type: required
                  - type: maxLength
                    args: 15
                    text: Maximum 15 characters
                formatFn:
                  - trim
                placeholder: Enter Form
              - name: seri
                label: Seri
                type: text
                validators:
                  - type: required
                  - type: maxLength
                    args: 15
                    text: Maximum 15 characters
                formatFn:
                  - trim
                placeholder: Enter Seri
              - name: enabled
                label: Enable
                type: switch
                value: true
                width: 100px
              - name: id
                type: text
                unvisible: true
    - type: group
      label: Authentication
      collapse: false
      disableEventCollapse: true
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - type: array
          mode: table
          name: authentications
          _condition:
            transform: $not($.extend.formType = 'view')
          minSize: 1
          arrayOptions:
            canChangeSize: true
          field:
            type: group
            skipCloneGroupControl: true
            fields:
              - name: invoicePermissionTypeCodeObject
                label: Permission Type
                type: select
                _select:
                  transform: >-
                    $invoicePermissionType($.extend.limit, $.extend.page,
                    $.extend.search)
                isLazyLoad: true
                placeholder: Select Permission Type
              - name: authUsername
                label: Authentication User
                type: text
                validators:
                  - type: maxLength
                    args: 50
                    text: Maximum 50 characters
                formatFn:
                  - trim
                placeholder: Enter Authentication User
              - name: authPassword
                label: Authentication Password
                type: text
                validators:
                  - type: maxLength
                    args: 30
                    text: Maximum 30 characters
                formatFn:
                  - trim
                placeholder: Enter Authentication Password
    - type: group
      collapse: false
      label: Business Tax Code
      _condition:
        transform: $.extend.formType = 'view'
      isBorderTopNone: true
      fieldGroupTitleStyle:
        paddingTop: 0px
      fields:
        - type: group
          n_cols: 1
          fields:
            - name: businessTaxCodeName
              label: Business Tax Code
              type: text
            - name: legalEntityName
              label: Legal Entity
              type: text
            - type: dateRange
              label: Effective Date
              name: effectiveDate
              placeholder: dd/MM/yyyy
              mode: date-picker
              setting:
                format: dd/MM/YYYY
                type: date
            - name: status
              label: Status
              type: radio
              radio:
                - label: Active
                  value: true
                - label: Inactive
                  value: false
            - name: address
              label: Address
              type: text
              placeholder: Enter Address
            - name: telephone
              label: Telephone
              type: text
              placeholder: Enter Telephone
        - type: translationTextArea
          label: Note
          name: note
          placeholder: Enter Note
          textarea:
            autoSize:
              minRows: 3
            maxCharCount: 1000
          validators:
            - type: maxLength
              args: 1000
              text: Maximum 1000 characters
    - type: group
      collapse: false
      label: Form/Seri
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - type: table
          name: details
          rowIdName: id
          layout_option:
            hide_action_row: true
            show_pagination: false
          _condition:
            transform: $.extend.formType = 'view'
          columns:
            - code: form
              title: Form
              align: start
              data_type:
                key: String
                collection: data_types
              display_type:
                key: Label
                collection: field_types
              width: '6'
            - code: seri
              title: Seri
              align: start
              data_type:
                key: String
                collection: data_types
              display_type:
                key: Label
                collection: field_types
              width: '6'
            - code: enabled
              title: Enabled
              align: start
              data_type:
                key: Boolean
                collection: data_types
              display_type:
                key: Boolean Tag
                collection: field_types
              width: '6'
          _dataSource:
            transform: $.extend.defaultValue.details
    - type: group
      collapse: false
      label: Authentication
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - type: table
          name: authentications
          rowIdName: id
          layout_option:
            hide_action_row: true
            show_pagination: false
          _condition:
            transform: $.extend.formType = 'view'
          columns:
            - code: invoicePermissionTypeName
              title: Permission Type
              align: start
              data_type:
                key: String
                collection: data_types
              display_type:
                key: Label
                collection: field_types
              width: '6'
            - code: authUsername
              title: Authentication User
              align: start
              data_type:
                key: String
                collection: data_types
              display_type:
                key: Label
                collection: field_types
              width: '6'
            - code: authPassword
              title: Authentication Password
              align: start
              data_type:
                key: String
                collection: data_types
              display_type:
                key: Label
                collection: field_types
              width: '6'
          _dataSource:
            transform: $.extend.defaultValue.authentications
  sources:
    businessTaxCodeList:
      uri: '"/api/legal-entities/get-by-business-tax-code"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':'Y'},
        {'field':'effectiveDate','operator': '$eq','value':$.effectiveDate},
        {'field':'code','operator': '$eq','value':$.code}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name, 'value': {'code':
        $item.code, 'legalEntityName': $item.legalEntityName } }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - code
    invoicePermissionType:
      uri: '"/api/picklists/InvoicePermissionType/values/pagination"'
      method: GET
      queryTransform: ' {''limit'': $.limit,''search'':$.search , ''page'': $.page, ''filter'':[{''field'':''status'',''operator'':''$eq'',''value'':true}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data[], function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
  footer:
    update: true
    updatedOn: updatedAt
    updatedBy: updatedBy
  _mode:
    transform: $not($.extend.formType = 'view') ? 'mark-scroll'
filter_config:
  fields:
    - name: businessTaxCode
      labelType: type-grid
      label: Business Tax Code
      type: selectAll
      mode: multiple
      placeholder: Select Business Tax Code
      _options:
        transform: $businessTaxCodeList($.extend.limit, $.extend.page, $.extend.search)
      isLazyLoad: true
    - name: effectiveDate
      labelType: type-grid
      label: Effective Date
      type: dateRange
      setting:
        format: dd/MM/yyyy
        type: date
    - name: status
      labelType: type-grid
      label: Status
      type: radio
      value: ''
      radio:
        - label: All
          value: ''
        - label: Active
          value: true
        - label: Inactive
          value: false
    - name: address
      labelType: type-grid
      label: Address
      type: text
      placeholder: Enter Address
    - name: updatedBy
      label: Last Updated By
      type: selectAll
      mode: multiple
      labelType: type-grid
      placeholder: Select Last Updated By
      _options:
        transform: $userList()
    - name: updatedAt
      labelType: type-grid
      label: Last Updated On
      type: dateRange
      setting:
        format: dd/MM/yyyy
        type: date
  filterMapping:
    - field: businessTaxCode
      operator: $in
      valueField: businessTaxCode.(value)
    - field: address
      operator: $cont
      valueField: address
    - field: status
      operator: $eq
      valueField: status
    - field: updatedBy
      operator: $in
      valueField: updatedBy.(value)
    - field: updatedAt
      operator: $between
      valueField: updatedAt
    - field: effectiveDate
      operator: $between
      valueField: effectiveDate
  sources:
    businessTaxCodeList:
      uri: '"/api/legal-entities/get-by-business-tax-code"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':'Y'},
        {'field':'effectiveDate','operator': '$eq','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name, 'value':  $item.code
        }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
    userList:
      uri: '"/api/internal-users"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.userName, 'value':
        $item.userName}})
      disabledCache: true
layout_options:
  show_dialog_form_save_add_button: true
  toolTable:
    adjustDisplay: 'true'
  tool_table:
    - id: export
  show_detail_history: false
  delete_multi_items: true
  is_export_grid: true
  hide_action_row: true
layout_options__header_buttons:
  - id: create
    icon: plus
    type: primary
options: null
create_form: null
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    title: Edit
    icon: icon-pencil-simple
    type: ghost-gray
  - id: delete
    icon: icon-trash
    type: ghost-gray
backend_url: /api/form-seri-tax-management
screen_name: manage-form-seri-tax
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: legalEntityCode
    defaultName: LegalEntityCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Manage Form/Seri Tax
  parent:
    title: Tax Invoice
