id: PIT.FS.FR.051
status: draft
sort: 636
user_created: 7b005132-8c47-469d-87f8-c72f7305edb7
date_created: '2025-01-03T07:46:45.823Z'
user_updated: cdfee544-f2ba-4ed2-ace8-5adc1ae0ed61
date_updated: '2025-07-24T02:19:23.521Z'
title: Create Invoice/Income Cert
requirement:
  time: 1748338444057
  blocks:
    - id: bnDKoD9qvY
      type: paragraph
      data:
        text: >-
          - <PERSON><PERSON><PERSON> năng cho phép người dùng đẩy hóa đơn lên hệ thống e-Invioce để
          thực hiện phê duyệt và ký số
  version: 2.30.7
screen_design: null
module: PIT
local_fields:
  - code: businessTaxCode
    title: Business Tax Code
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 12
  - code: form
    title: Form
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: seri
    title: Seri
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: 'no'
    title: 'No'
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: employeeTypeCode
    title: Employee Type
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: employeeId
    title: EmployeeID
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: fullName
    title: Employee Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: taxCode
    title: PIT Code
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: status
    title: Status
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tag
      collection: field_types
    options__tabular__column_width: 12
    extra_config:
      tags:
        - value: Saved
          label: Saved
          style:
            background_color: '#E0FAE9'
        - value: Pending
          label: Pending Approved
          style:
            background_color: '#FEF9CC'
        - value: Approved
          label: Approved
          style:
            background_color: '#E6F2FF'
        - value: Cancelled
          label: Cancelled
          style:
            background_color: '#FFE8E5'
  - code: updatedBy
    title: Last Updated By
    data_type:
      key: User
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: updatedAt
    title: Last Updated On
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
mock_data: []
local_buttons: null
layout: layout-table
form_config:
  _mode:
    transform: $.extend.formType = 'create'  ? 'step-horizontal'
  formTitle:
    create: Add New Invoice/Income Cert
    view: Invoice/ Income Cert Detail
    edit: Edit Invoice/ Income Cert
  formSize:
    view: large
    create: small
    edit: small
  fields:
    - type: group
      label: Select Business Tax Code
      _condition:
        transform: $.extend.formType = 'create'
      fields:
        - name: businessTaxCodeObject
          label: Business Tax Code
          type: select
          placeholder: Select Business Tax Code
          outputValue: value
          clearFieldsAfterChange:
            - formObject
            - seri
            - employee
          isLazyLoad: true
          _select:
            transform: >-
              $businessTaxCodeList($.extend.limit, $.extend.page,
              $.extend.search)
          _disabled:
            transform: $.extend.formType = 'edit'
          validators:
            - type: required
        - type: group
          n_cols: 2
          fields:
            - type: select
              name: form
              label: Form
              placeholder: Select Form
              outputValue: value
              clearFieldsAfterChange:
                - seri
              validators:
                - type: required
              _select:
                transform: >-
                  $isNilorEmpty($.fields.businessTaxCodeObject)?[]:$.variables._formData
            - type: select
              name: seri
              label: Seri
              placeholder: Select Seri
              outputValue: value
              validators:
                - type: required
              _select:
                transform: >-
                  $isNilorEmpty($.fields.businessTaxCodeObject)?[]:$.variables._seriData
    - type: group
      n_cols: 2
      label: Add New Invoice/ Income Cert
      _condition:
        transform: $.extend.formType = 'create' or $.extend.formType = 'edit'
      fields:
        - type: text
          name: businessTaxCode
          unvisible: true
        - type: text
          name: id
          unvisible: true
        - type: text
          name: sid
          label: SID
          placeholder: System - Generated
          value: System - Generated
          disabled: true
        - type: text
          name: businessTaxCodeName
          label: Business Tax Code
          _value:
            transform: >-
              $.extend.formType =
              'edit'?$.extend.defaultValue.businessTaxCodeObject.value.name:$.fields.businessTaxCodeObject.name
          disabled: true
        - type: text
          name: formStepTwo
          label: Form
          placeholder: ''
          _value:
            transform: >-
              $isNilorEmpty($.extend.defaultValue.form)?$.fields.form:$.extend.defaultValue.form
          disabled: true
        - type: text
          name: seriStepTwo
          label: Seri
          placeholder: ''
          _value:
            transform: >-
              $isNilorEmpty($.extend.defaultValue.seri)?$.fields.seri:$.extend.defaultValue.seri
          disabled: true
        - type: text
          name: 'no'
          label: 'No'
          placeholder: ''
          disabled: true
          col: 2
          _condition:
            transform: $.extend.formType = 'create'
        - type: text
          name: 'no'
          label: 'No'
          placeholder: ''
          disabled: true
          _condition:
            transform: $.extend.formType = 'edit'
        - type: text
          name: status
          label: Status
          placeholder: ''
          disabled: true
          _condition:
            transform: $.extend.formType = 'edit'
        - type: checkbox
          name: noIncomeCertification
          label: ' '
          customLabelCheckbox: No Income Certification
          clearFieldsAfterChange:
            - onlyIncomeCertification
          _value:
            transform: >-
              $not($isNilorEmpty($.fields.onlyIncomeCertification)) and
              $.fields.onlyIncomeCertification? false
        - type: checkbox
          name: onlyIncomeCertification
          label: ' '
          customLabelCheckbox: Only Income Certification
          clearFieldsAfterChange:
            - noIncomeCertification
          _value:
            transform: >-
              $not($isNilorEmpty($.fields.noIncomeCertification)) and
              $.fields.noIncomeCertification? false
        - type: select
          name: employeeTypeCode
          label: Employee Type
          placeholder: Select Employee Type
          clearFieldsAfterChange:
            - employee
            - fullName
            - taxCode
            - address
            - countryObject
            - nationalId
            - issueDate
            - issuePlace
            - phone
            - email
            - employeeUnvisible
            - employeeId
          isLazyLoad: true
          outputValue: value
          _value:
            transform: >-
              $.extend.formType = 'create'?
              {'label':'Employee','value':'Employee'}
          _select:
            transform: $employeeTypeList($.extend.limit, $.extend.page, $.extend.search)
          validators:
            - type: required
        - type: select
          name: employee
          label: EmployeeID
          placeholder: Select EmployeeID
          clearFieldsAfterChange:
            - employee
            - fullName
            - taxCode
            - address
            - countryObject
            - nationalId
            - issueDate
            - issuePlace
            - phone
            - email
            - employeeUnvisible
            - employeeId
            - nationId
          _select:
            transform: >-
              $employeesList($.extend.limit, $.extend.page,
              $.extend.search,$.fields.businessTaxCodeObject.legalEntityCode)
          isLazyLoad: true
          _condition:
            transform: $.fields.employeeTypeCode = 'Employee'
          _value:
            transform: $.extend.defaultValue.employeeId
          outputValue: value
          validators:
            - type: required
        - type: text
          name: employeeUnvisible
          unvisible: true
          clearFieldsAfterChange:
            - employee
            - fullName
            - taxCode
            - address
            - countryObject
            - nationalId
            - issueDate
            - issuePlace
            - phone
            - email
            - employeeId
            - nationId
          col: 2
          _value:
            skip:
              count: 2
              condition: $.extend.formType = 'edit'
            transform: >-
              ($.flags.employeeFlag; $not($isNilorEmpty($.fields.employee)) and
              $.fields.employeeTypeCode = 'Employee'?
              $merge([$currentEmployee($.fields.employee),$employeeAddress($.fields.employee),$employeePhone($.fields.employee),$employeeEmail($.fields.employee)])
              :{})
            onSuccess:
              type: toast
              message: Successfully Refreshed
              whenChangeFields: $.flags.employeeFlag
        - type: text
          name: employeeId
          unvisible: true
          _value:
            transform: $.fields.employeeUnvisible.employeeId
        - type: text
          name: fullName
          label: Employee Name
          placeholder: Enter Employee Name
          col: 2
          _value:
            transform: ($.flags.employeeFlag; $.fields.employeeUnvisible.name)
          validators:
            - type: required
            - type: maxLength
              args: '400'
              text: Maximum 400 characters
          action:
            icon: icon-arrows-clockwise-bold
            type: primary
            _condition:
              transform: $.fields.employeeTypeCode = 'Employee'
            actionType: flag
            config:
              name: employeeFlag
            confirm:
              content: Do you want to Refesh Employee Data?
        - type: text
          name: taxCode
          label: PIT Code
          placeholder: Enter PIT Code
          col: 2
          _value:
            transform: ($.flags.employeeFlag; $.fields.employeeUnvisible.pitCode)
          validators:
            - type: required
            - type: maxLength
              args: '14'
              text: Maximum 14 characters
        - type: text
          name: address
          label: Address
          placeholder: Enter Address
          col: 2
          _value:
            transform: ($.flags.employeeFlag; $.fields.employeeUnvisible.employeeAddress)
          validators:
            - type: required
            - type: maxLength
              args: '400'
              text: Maximum 400 characters
        - name: countryObject
          label: Country
          type: select
          placeholder: Select Country
          _value:
            transform: >-
              ($.flags.employeeFlag;
              $exists($.fields.employeeUnvisible)?{'label':$.fields.employeeUnvisible.nationalityName,
              'value':$.fields.employeeUnvisible.nationalityCode })
          isLazyLoad: true
          _select:
            transform: $nationsList($.extend.limit, $.extend.page, $.extend.search)
          validators:
            - type: maxLength
              args: '400'
              text: Maximum 400 characters
        - type: checkbox
          name: resident
          label: ' '
          customLabelCheckbox: Resident
          _value:
            transform: $.extend.formType = 'create'? true
        - type: number
          name: residentCode
          unvisible: true
          _value:
            transform: '$.fields.resident? 1 : 0'
        - type: text
          name: nationId
          label: National ID
          _value:
            transform: ($.flags.employeeFlag; $.fields.employeeUnvisible.nationalId)
          placeholder: Enter National ID
          validators:
            - type: required
            - type: maxLength
              args: '20'
              text: Maximum 20 characters
        - type: dateRange
          name: issueDate
          mode: date-picker
          placeholder: Select Issue Date
          _value:
            transform: >-
              ($.flags.employeeFlag;
              $.fields.employeeUnvisible.nationalIssueDate)
          label: Issue Date
        - type: text
          name: issuePlace
          label: Issue Place
          placeholder: Enter Issue Place
          _value:
            transform: >-
              ($.flags.employeeFlag;
              $.fields.employeeUnvisible.nationalIssuePlaceName)
          placeholer: Enter Issue Place
          validators:
            - type: maxLength
              args: '400'
              text: Maximum 400 characters
        - type: text
          name: phone
          label: Phone
          placeholder: Enter Phone
          _value:
            transform: ($.flags.employeeFlag; $.fields.employeeUnvisible.phoneNumber)
          placeholer: Enter Phone
          validators:
            - type: maxLength
              args: '20'
              text: Maximum 20 characters
        - type: text
          name: email
          label: Email
          _value:
            transform: ($.flags.employeeFlag; $.fields.employeeUnvisible.email)
          placeholder: Enter Email
          validators:
            - type: maxLength
              args: '50'
              text: Maximum 50 characters
        - type: checkbox
          name: sendMail
          label: ' '
          customLabelCheckbox: Send Mail
          value: true
          _value:
            transform: $.extend.formType = 'edit'? $.fields.sendMail
        - name: typeOfIncome
          label: Type Of Income
          col: 2
          type: text
          placeholder: Enter Type Of Income
          validators:
            - type: required
            - type: maxLength
              args: '100'
              text: Maximum 100 characters
        - type: dateRange
          name: startDate
          mode: date-picker
          label: From Month
          placeholder: MM/YYYY
          setting:
            type: month
            format: MM/yyyy
          validators:
            - type: required
        - type: dateRange
          name: endDate
          mode: date-picker
          label: To Month
          placeholder: MM/YYYY
          setting:
            type: month
            format: MM/yyyy
          validators:
            - type: required
        - name: currencyObject
          label: Currency
          col: 2
          type: select
          placeholder: Select Currency
          _select:
            transform: $currencyList()
          validators:
            - type: required
        - name: totalIncomeTaxable
          label: Total Taxable Income
          type: number
          align: start
          validators:
            - type: required
          number:
            format: currency
            max: '999999999999999999999'
            precision: 0
            _suffix:
              transform: >-
                $isNilorEmpty($.fields.currencyObject)?'':$.fields.currencyObject.label
          _value:
            transform: $.extend.defaultValue.totalIncomeTaxableValue
          displayType: Currency
          placeholder: Enter Total Taxable Income
        - name: totalAssessableIncome
          label: Assessable income
          type: number
          align: start
          number:
            format: currency
            max: '999999999999999999999'
            precision: 0
            _suffix:
              transform: >-
                $isNilorEmpty($.fields.currencyObject)?'':$.fields.currencyObject.label
          _value:
            transform: $.extend.defaultValue.totalAssessableIncomeValue
          displayType: Currency
          placeholder: Enter Assessable income
        - name: totalInsurance
          label: Total Insurance Deducted
          type: number
          align: start
          number:
            format: currency
            max: '999999999999999999999'
            precision: 0
            _suffix:
              transform: >-
                $isNilorEmpty($.fields.currencyObject)?'':$.fields.currencyObject.label
          _value:
            transform: $.extend.defaultValue.totalInsuranceValue
          displayType: Currency
          placeholder: Enter Total Insurance Deducted
        - name: amountOfPersonalIncomeTax
          label: Total PIT Deducted
          type: number
          align: start
          number:
            format: currency
            max: '999999999999999999999'
            precision: 0
            _suffix:
              transform: >-
                $isNilorEmpty($.fields.currencyObject)?'':$.fields.currencyObject.label
          _value:
            transform: $.extend.defaultValue.amountOfPersonalIncomeTaxValue
          displayType: Currency
          placeholder: Enter Total PIT Deducted
        - name: totalDependent
          label: Total Number Of Dependents
          type: number
          align: start
          placeholder: Enter Total Number Of Dependents
          _value:
            transform: $.extend.defaultValue.totalDependentValue
          number:
            format: currency
            max: '99'
            precision: 0
        - name: totalMonthlyDeductions
          label: Total Number Of Dependent Deduction Months
          type: number
          align: start
          placeholder: Enter Total Number Of Dependent Deduction Months
          _value:
            transform: $.extend.defaultValue.totalMonthlyDeductionsValue
          number:
            format: currency
            max: '99'
            precision: 0
    - type: group
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - type: group
          n_cols: 2
          fields:
            - type: text
              name: sid
              label: SID
            - type: text
              name: businessTaxCode
              label: Business Tax Code
            - type: text
              name: form
              label: Form
            - type: text
              name: seri
              label: Seri
            - type: text
              name: 'no'
              label: 'No'
            - name: status
              type: select
              select:
                - value: Saved
                  label: Saved
                - value: Pending
                  label: Pending Approved
                - value: Approved
                  label: Approved
                - value: Cancelled
                  label: Cancelled
              label: Status
              readOnly: true
              displaySetting:
                type: Tag
                extraConfig:
                  tags:
                    - value: Saved
                      label: Saved
                      style:
                        background_color: '#E0FAE9'
                    - value: Cancelled
                      label: Cancelled
                      style:
                        background_color: '#FFE8E5'
                    - value: Approved
                      label: Approved
                      style:
                        background_color: '#E6F2FF'
                    - value: Pending
                      label: Pending Approved
                      style:
                        background_color: '#FEF9CC'
                  size: small
            - name: noIncomeCertification
              type: select
              select:
                - value: true
                  label: 'Yes'
                - value: false
                  label: 'No'
              label: No Income Certification
              readOnly: true
              displaySetting:
                type: Tag
                extraConfig:
                  tags:
                    - value: true
                      label: 'Yes'
                      style:
                        background_color: '#E0FAE9'
                    - value: false
                      label: 'No'
                      style:
                        background_color: '#F1F3F5'
                  size: small
            - name: onlyIncomeCertification
              type: select
              select:
                - value: true
                  label: 'Yes'
                - value: false
                  label: 'No'
              label: Only Income Certification
              readOnly: true
              displaySetting:
                type: Tag
                extraConfig:
                  tags:
                    - value: true
                      label: 'Yes'
                      style:
                        background_color: '#E0FAE9'
                    - value: false
                      label: 'No'
                      style:
                        background_color: '#F1F3F5'
                  size: small
        - type: group
          n_cols: 2
          hostStyle:
            borderTop: '1px solid #00000014'
            paddingTop: 20px
          fields:
            - type: text
              name: employeeTypeCode
              label: Employee Type
            - type: text
              name: employeeId
              label: Employee ID
            - type: text
              name: fullName
              col: 2
              label: Employee Name
            - type: text
              name: taxCode
              label: Tax Code
            - type: text
              name: address
              label: Address
            - type: text
              name: countryName
              label: Country
            - name: residentCode
              type: select
              select:
                - value: 1
                  label: 'Yes'
                - value: 0
                  label: 'No'
              label: Resident
              readOnly: true
              displaySetting:
                type: Tag
                extraConfig:
                  tags:
                    - value: 1
                      label: 'Yes'
                      style:
                        background_color: '#E0FAE9'
                    - value: 0
                      label: 'No'
                      style:
                        background_color: '#F1F3F5'
                  size: small
            - type: text
              name: nationId
              label: National ID
            - type: dateRange
              name: issueDate
              mode: date-picker
              label: Issue Date
            - type: text
              name: issuePlace
              label: Issue Place
            - type: text
              name: phone
              label: Phone
            - type: text
              name: email
              label: Email
            - name: sendMail
              type: select
              select:
                - value: true
                  label: 'Yes'
                - value: false
                  label: 'No'
              label: Send Mail
              readOnly: true
              displaySetting:
                type: Tag
                extraConfig:
                  tags:
                    - value: true
                      label: 'Yes'
                      style:
                        background_color: '#E0FAE9'
                    - value: false
                      label: 'No'
                      style:
                        background_color: '#F1F3F5'
                  size: small
        - type: group
          n_cols: 2
          hostStyle:
            borderTop: '1px solid #00000014'
            paddingTop: 20px
          fields:
            - type: text
              name: typeOfIncome
              label: Type Of Income
            - type: text
              name: currencyName
              label: Currency
            - type: dateRange
              name: startDate
              mode: date-picker
              label: From Month
              placeholder: MM/YYYY
              setting:
                type: month
                format: MM-yyyy
            - type: dateRange
              name: endDate
              mode: date-picker
              label: To Month
              placeholder: MM/YYYY
              setting:
                type: month
                format: MM-yyyy
            - name: totalIncomeTaxableValue
              label: Total Taxable Income
              type: number
              align: start
              number:
                format: currency
                max: '999999999999999999999'
                precision: 0
              displayType: Currency
            - name: totalAssessableIncomeValue
              label: Assessable income
              type: number
              align: start
              number:
                format: currency
                max: '999999999999999999999'
                precision: 0
              displayType: Currency
            - name: totalInsuranceValue
              label: Total Insurance Deducted
              type: number
              align: start
              number:
                format: currency
                max: '999999999999999999999'
                precision: 0
              displayType: Currency
            - name: amountOfPersonalIncomeTaxValue
              label: Total PIT Deducted
              type: number
              align: start
              number:
                format: currency
                max: '999999999999999999999'
                precision: 0
              displayType: Currency
            - type: text
              name: totalDependentValue
              label: Total Number Of Dependents
            - type: text
              name: totalMonthlyDeductionsValue
              label: Total Number Of Dependent Deduction Months
        - type: group
          n_cols: 2
          hostStyle:
            borderTop: '1px solid #00000014'
            paddingTop: 20px
          _condition:
            transform: $.fields.status = 'Cancelled'
          fields:
            - type: text
              name: cancelledReason
              label: Reason Type
            - type: text
              name: cancelledComment
              label: Comment
  sources:
    businessTaxCodeList:
      uri: '"/api/legal-entities/get-by-business-tax-code"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':'Y'},
        {'field':'taxCode','operator': '$eq','value':$.taxCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name, 'value': {'code':
        $item.code, 'name': $item.name, 'legalEntityCode': $item.legalEntityCode
        } }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - taxCode
    employeesList:
      uri: '"/api/personals/all-employees-job-datas"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page, 'search': $.search,'filter':
        [{'field':'legalEntity','operator': '$eq','value':
        $.legalEntityCode},{'field':'jobdataId','operator':'$gt','value':'0' }]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label':
        $join($filter([$item.employeeId,$item.fullName], $boolean), ' - '),
        'value':  $item.employeeId}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - legalEntityCode
    currentEmployee:
      uri: '"/api/personals"'
      method: GET
      queryTransform: '{''limit'': 1,''page'': 1, ''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $.data[0]
      disabledCache: true
      params:
        - search
    employeeAddress:
      uri: '"/api/personals/"  & $.employeeId & "/personal-addresses"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: '{''employeeAddress'':$not($isNilorEmpty($))?$[0].addressDetail}'
      disabledCache: true
      params:
        - employeeId
    employeePhone:
      uri: '"/api/personals/"  & $.employeeId & "/phone-contacts"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: '{''phoneNumber'': $boolean($[0].status) ? $[0].phoneNumber}'
      disabledCache: true
      params:
        - employeeId
    employeeEmail:
      uri: '"/api/personals/"  & $.employeeId & "/email-contacts/history"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        {'email': $['PRIV' in emailTypeCode][0]?$['PRIV' in
        emailTypeCode][0].emailAddress:'_setValueNull' }
      disabledCache: true
      params:
        - employeeId
    nationsList:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    formSeriDetailsList:
      uri: '"/api/form-seri-tax-management/" & $.businessTaxCode & "/details"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $
      disabledCache: true
      params:
        - businessTaxCode
    currencyList:
      uri: '"/api/picklists/CURRENCY/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code }})[]
      disabledCache: true
    employeeTypeList:
      uri: '"/api/employee-type"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
  variables:
    _formSeriDetail:
      transform: >-
        ($isNilorEmpty($.fields.businessTaxCodeObject)?[]:$formSeriDetailsList($.fields.businessTaxCodeObject.code)[])
    _formData:
      transform: >-
        ($.variables._formSeriDetail;
        $isNilorEmpty($.variables._formSeriDetail)? []:
        $map($distinct($.variables._formSeriDetail.form)[], function($item){{
        'value':$item, 'label':$item}})[])
    _seriData:
      transform: >-
        ($.fields.form; $isNilorEmpty($.variables._formSeriDetail)? []:
        $map($.variables._formSeriDetail, function($item){$item.form =
        $.fields.form?{'value':$item.seri, 'label':$item.seri}})[])
  footer:
    type: elements
    elements:
      - id: lastUpdatedBy
        type: lastUpdatedBy
        valueMapping:
          by: updatedBy
          'on': updatedAt
      - id: pushedBy
        type: pushedBy
        valueMapping:
          by: pushedBy
          'on': pushedAt
        displayExpression: $.status != 'Saved'
      - id: cancelledBy
        type: cancelledBy
        valueMapping:
          by: cancelledBy
          'on': cancelledAt
        displayExpression: $.status = 'Cancelled'
      - id: approvedBy
        type: approvedBy
        valueMapping:
          by: verifiedBy
          'on': verifiedAt
        displayExpression: $.status = 'Approved'
filter_config:
  fields:
    - type: selectAll
      labelType: type-grid
      name: businessTaxCode
      label: Business Tax Code
      outputValue: value
      placeholder: Select Legal Entity
      isLazyLoad: true
      _options:
        transform: $businessTaxCodeList($.extend.limit, $.extend.page, $.extend.search)
    - type: text
      labelType: type-grid
      name: form
      label: Form
      placeholder: Enter Form
    - type: text
      labelType: type-grid
      name: seri
      label: Seri
      placeholder: Enter Seri
    - type: text
      labelType: type-grid
      name: 'no'
      label: 'No'
      placeholder: Enter No
    - type: selectAll
      labelType: type-grid
      name: employeeTypeCode
      label: Employee Type
      mode: multiple
      placeholder: Select Employee Type
      outputValue: value
      isLazyLoad: true
      _options:
        transform: $employeeTypeList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      labelType: type-grid
      name: fullName
      label: Employee
      mode: multiple
      placeholder: Select Employee
      outputValue: value
      isLazyLoad: true
      _options:
        transform: $employeesList($.extend.limit, $.extend.page, $.extend.search)
    - type: text
      labelType: type-grid
      name: taxCode
      label: PIT Code
      placeholder: Enter PIT Code
    - type: select
      label: Status
      labelType: type-grid
      name: status
      mode: multiple
      select:
        - value: Saved
          label: Saved
        - value: Pending
          label: Pending Approved
        - value: Approved
          label: Approved
        - value: Cancelled
          label: Cancelled
    - labelType: type-grid
      name: updatedBy
      label: Last Updated By
      type: text
      mode: multiple
      placeholder: Enter last updated by
    - type: dateRange
      label: Last Updated On
      labelType: type-grid
      name: updatedAt
  filterMapping:
    - field: businessTaxCode
      operator: $in
      valueField: businessTaxCode
    - field: form
      operator: $cont
      valueField: form
    - field: seri
      operator: $cont
      valueField: seri
    - field: 'no'
      operator: $cont
      valueField: 'no'
    - field: taxCode
      operator: $cont
      valueField: taxCode
    - field: employeeTypeCode
      operator: $in
      valueField: employeeTypeCode
    - field: fullName
      operator: $in
      valueField: fullName
    - field: status
      operator: $in
      valueField: status.(value)
    - field: updatedBy
      operator: $cont
      valueField: updatedBy
    - field: updatedAt
      operator: $between
      valueField: updatedAt
  sources:
    legalEntitiesList:
      uri: '"/api/legal-entities"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    businessTaxCodeList:
      uri: '"/api/legal-entities/get-by-business-tax-code"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':'Y'}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name, 'value': $item.code
        }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    employeesList:
      uri: '"/api/personals/all-employees-job-datas-dropdownlist"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page, 'search': $.search,
        'filter':[{'field':'jobdataId','operator':'$gt','value':'0' }]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label':
        $join($filter([$item.employeeId,$item.fullName], $boolean), ' - '),
        'value':  $item.fullName}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    employeeTypeList:
      uri: '"/api/employee-type"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
  variables: {}
layout_options:
  toolTable:
    adjustDisplay: 'true'
  show_detail_history: false
  history_widget_header_options:
    duplicate: false
  tool_table:
    - id: export
    - id: import
  view_history_after_updated: false
  view_history_after_created: false
  delete_multi_items: true
  is_export_grid: true
  dialog_footer:
    - type: step
      showClearAll: true
      for:
        - create
      buttons:
        secondary:
          id: saveAsDraft
          title: Save as Draft
          action: saveAsDraft
        primary:
          id: pushToInvoice
          title: Push To Invoice/Income Cert
          action: pushToInvoice
    - type: buttons
      for:
        - view
      buttons:
        - id: delete
          action: toDelete
          title: Delete
          type: secondary
          _condition: $.status = 'Saved'
        - id: edit
          action: toEdit
          title: Edit
          type: primary
          _condition: $.status = 'Pending' or $.status = 'Saved'
    - type: buttons
      for:
        - edit
      buttons:
        - id: cancel
          action: cancel
          title: Cancel
          type: tertiary
        - id: edit
          action: editDraft
          title: Save as Draft
          type: secondary
          _condition: $.status = 'Saved'
        - id: pushToInvoice
          action: updatePushToInvoice
          title: Push To Invoice/Income Cert
          type: primary
          _condition: $.status = 'Saved'
        - id: updateInvoice
          action: updatePushToInvoice
          title: Update To Invoice/Income Cert
          type: primary
          _condition: $.status = 'Pending'
  dialog_actions:
    - id: saveAsDraft
      type: api
      config:
        url: /api/invoice-or-income-cert/save-as-draft
        method: POST
    - id: pushToInvoice
      type: api
      config:
        url: /api/invoice-or-income-cert/push-to-invoice
        method: POST
    - id: editDraft
      type: api
      config:
        _url:
          transform: '''/api/invoice-or-income-cert/save-as-draft/'' & $.id'
        method: PATCH
    - id: updatePushToInvoice
      type: api
      config:
        _url:
          transform: '''/api/invoice-or-income-cert/push-to-invoice/'' & $.id'
        method: PATCH
  hide_action_row: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    title: Edit
    icon: icon-pencil-simple-bold
    type: ghost-gray
    condition_func: $.status = 'Pending' or $.status = 'Saved'
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: ghost-gray
    condition_func: $.status = 'Saved'
backend_url: /api/invoice-or-income-cert
screen_name: create-invoice-income-cert
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: legalEntityCode
    defaultName: LegalEntityCode
  - name: employeeId
    defaultName: EmployeeId
  - name: countryCode
    defaultName: CountryCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item: null
