id: PIT.FS.FR.052
status: draft
sort: 622
user_created: cdfee544-f2ba-4ed2-ace8-5adc1ae0ed61
date_created: '2025-03-06T13:30:38.635Z'
user_updated: cdfee544-f2ba-4ed2-ace8-5adc1ae0ed61
date_updated: '2025-07-24T02:20:55.073Z'
title: Approve Invoice/Income Cert
requirement:
  time: 1749094935011
  blocks:
    - id: K2WGP7iZXu
      type: paragraph
      data:
        text: >-
          - Chứ<PERSON> năng cho phép người dùng thực hiện phê duyệt/ hủy hóa đơn,
          chứng từ thuế (<PERSON><PERSON> thống sẽ thực hiện gọi tích hợp để duyệt + ký số lên
          chứng từ hóa đơn) hoặc ký lên giấy xác nhận thu nhập (<PERSON><PERSON><PERSON><PERSON><PERSON> hợp chỉ
          tạo gi<PERSON>y xá<PERSON> nhận thu nhập)
  version: 2.30.7
screen_design: null
module: PIT
local_fields:
  - code: fullName
    title: Employee Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: taxCode
    title: PIT Code
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: employeeId
    title: Employee ID
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: businessTaxCode
    title: Business Tax Code
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: form
    title: Form
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: seri
    title: Seri
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: 'no'
    title: 'No'
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: status
    title: Status
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tag
      collection: field_types
    extra_config:
      tags:
        - value: Saved
          label: Saved
          style:
            background_color: '#E0FAE9'
        - value: Pending
          label: Pending Approved
          style:
            background_color: '#FEF9CC'
        - value: Approved
          label: Approved
          style:
            background_color: '#E6F2FF'
        - value: Cancelled
          label: Cancelled
          style:
            background_color: '#FFE8E5'
    options__tabular__column_width: 12
  - code: onlyIncomeCertification
    title: Only Income Certification
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: Tag
      collection: field_types
    extra_config:
      tags:
        - value: true
          label: 'Yes'
          style:
            background_color: '#E0FAE9'
        - value: false
          label: 'No'
          style:
            background_color: '#F1F3F5'
  - code: totalIncomeTaxableValue
    title: Total Income Taxable
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Currency
      collection: field_types
  - code: totalInsuranceValue
    title: Total Insurance
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Currency
      collection: field_types
  - code: amountOfPersonalIncomeTaxValue
    title: Amount Of Personal Income Tax
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Currency
      collection: field_types
  - code: updatedBy
    title: Last Updated By
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: updatedAt
    title: Last Updated On
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
mock_data: []
local_buttons: null
layout: layout-table
form_config:
  formTitle:
    create: Add New Invoice/Income Cert
    view: Invoice/ Income Cert Detail
    edit: Cancel Invoice/ Income Cert
  formSize:
    view: large
    create: small
    edit: small
  fields:
    - type: group
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - type: group
          n_cols: 2
          fields:
            - type: text
              name: sid
              label: SID
            - type: text
              name: id
              unvisible: true
            - type: text
              name: businessTaxCode
              label: Business Tax Code
            - type: text
              name: form
              label: Form
            - type: text
              name: seri
              label: Seri
            - type: text
              name: 'no'
              label: 'No'
            - name: status
              type: select
              placeholder: Select Status
              select:
                - value: Saved
                  label: Saved
                - value: Pending
                  label: Pending Approved
                - value: Approved
                  label: Approved
                - value: Cancelled
                  label: Cancelled
              label: Status
              readOnly: true
              displaySetting:
                type: Tag
                extraConfig:
                  tags:
                    - value: Saved
                      label: Saved
                      style:
                        background_color: '#E0FAE9'
                    - value: Cancelled
                      label: Cancelled
                      style:
                        background_color: '#FFE8E5'
                    - value: Approved
                      label: Approved
                      style:
                        background_color: '#E6F2FF'
                    - value: Pending
                      label: Pending Approved
                      style:
                        background_color: '#FEF9CC'
                  size: small
            - name: noIncomeCertification
              type: select
              select:
                - value: true
                  label: 'Yes'
                - value: false
                  label: 'No'
              label: No Income Certification
              readOnly: true
              displaySetting:
                type: Tag
                extraConfig:
                  tags:
                    - value: true
                      label: 'Yes'
                      style:
                        background_color: '#E0FAE9'
                    - value: false
                      label: 'No'
                      style:
                        background_color: '#F1F3F5'
                  size: small
            - name: onlyIncomeCertification
              type: select
              select:
                - value: true
                  label: 'Yes'
                - value: false
                  label: 'No'
              label: Only Income Certification
              readOnly: true
              displaySetting:
                type: Tag
                extraConfig:
                  tags:
                    - value: true
                      label: 'Yes'
                      style:
                        background_color: '#E0FAE9'
                    - value: false
                      label: 'No'
                      style:
                        background_color: '#F1F3F5'
                  size: small
        - type: group
          n_cols: 2
          hostStyle:
            borderTop: '1px solid #00000014'
            paddingTop: 20px
          fields:
            - type: text
              name: employeeTypeCode
              label: Employee Type
            - type: text
              name: employeeId
              label: Employee ID
            - type: text
              name: fullName
              col: 2
              label: Employee Name
            - type: text
              name: taxCode
              label: Tax Code
            - type: text
              name: address
              label: Address
            - type: text
              name: countryName
              label: Country
            - name: residentCode
              type: select
              select:
                - value: 1
                  label: 'Yes'
                - value: 0
                  label: 'No'
              label: Resident
              readOnly: true
              displaySetting:
                type: Tag
                extraConfig:
                  tags:
                    - value: 1
                      label: 'Yes'
                      style:
                        background_color: '#E0FAE9'
                    - value: 0
                      label: 'No'
                      style:
                        background_color: '#F1F3F5'
                  size: small
            - type: text
              name: nationId
              label: National ID
            - type: dateRange
              name: issueDate
              mode: date-picker
              label: Issue Date
            - type: text
              name: issuePlace
              label: Issue Place
            - type: text
              name: phone
              label: Phone
            - type: text
              name: email
              label: Email
            - name: sendMail
              type: select
              select:
                - value: true
                  label: 'Yes'
                - value: false
                  label: 'No'
              label: Send Mail
              readOnly: true
              displaySetting:
                type: Tag
                extraConfig:
                  tags:
                    - value: true
                      label: 'Yes'
                      style:
                        background_color: '#E0FAE9'
                    - value: false
                      label: 'No'
                      style:
                        background_color: '#F1F3F5'
                  size: small
        - type: group
          n_cols: 2
          hostStyle:
            borderTop: '1px solid #00000014'
            paddingTop: 20px
          fields:
            - type: text
              name: typeOfIncome
              label: Type Of Income
            - type: text
              name: currencyName
              label: Currency
            - type: dateRange
              name: startDate
              mode: date-picker
              label: From Month
              placeholder: MM/YYYY
              setting:
                type: month
                format: MM-yyyy
            - type: dateRange
              name: endDate
              mode: date-picker
              label: To Month
              placeholder: MM/YYYY
              setting:
                type: month
                format: MM-yyyy
            - name: totalIncomeTaxableValue
              label: Total Income Taxable
              type: number
              align: start
              number:
                format: currency
                max: '999999999999999999999'
                precision: 0
              displayType: Currency
            - name: totalAssessableIncomeValue
              label: Total Tax Calculation Income
              type: number
              align: start
              number:
                format: currency
                max: '999999999999999999999'
                precision: 0
              displayType: Currency
            - name: totalInsuranceValue
              label: Total Insurance
              type: number
              align: start
              number:
                format: currency
                max: '999999999999999999999'
                precision: 0
              displayType: Currency
            - name: amountOfPersonalIncomeTaxValue
              label: Amount Of Personal Income Tax
              type: number
              align: start
              number:
                format: currency
                max: '999999999999999999999'
                precision: 0
              displayType: Currency
            - type: text
              name: totalDependentValue
              label: Total Dependent
            - type: text
              name: totalMonthlyDeductionsValue
              label: Total Monthly Deductions
        - type: group
          n_cols: 2
          hostStyle:
            borderTop: '1px solid #00000014'
            paddingTop: 20px
          _condition:
            transform: $.fields.status = 'Cancelled'
          fields:
            - type: text
              name: cancelledReason
              label: Reason Type
            - type: text
              name: cancelledComment
              label: Comment
        - type: text
          name: verifiedLink
          _condition:
            transform: $.fields.status != 'Pending'
          displaySetting:
            style:
              fontWeight: 600
            type: Hyperlink
            name: verifiedLink
            _value:
              transform: >-
                {'label': $.extend.defaultValue.verifiedLink, 'linkUrl':
                $.extend.defaultValue.verifiedLink, 'showIcon': true}
    - type: group
      _condition:
        transform: $.extend.formType = 'edit'
      fields:
        - type: text
          name: id
          unvisible: true
        - name: reason
          label: Reason
          type: select
          placeholder: Select Reason
          _select:
            transform: $pitReasonList($.extend.limit, $.extend.page, $.extend.search)
          outputValue: value
          isLazyLoad: true
          validators:
            - type: required
        - type: textarea
          label: Comment
          name: comment
          placeholder: Enter Comment
          textarea:
            autoSize:
              minRows: 3
            maxCharCount: 1000
          validators:
            - type: required
  sources:
    pitReasonList:
      uri: '"/api/picklists/PIT_REASONS/values/pagination"'
      method: GET
      queryTransform: ' {''limit'': $.limit,''search'':$.search, ''page'': $.page}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data[], function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.name.default}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
  footer:
    type: elements
    elements:
      - id: lastUpdatedBy
        type: lastUpdatedBy
        valueMapping:
          by: updatedBy
          'on': updatedAt
      - id: pushedBy
        type: pushedBy
        valueMapping:
          by: pushedBy
          'on': pushedAt
        displayExpression: $.status != 'Saved'
      - id: cancelledBy
        type: cancelledBy
        valueMapping:
          by: cancelledBy
          'on': cancelledAt
        displayExpression: $.status = 'Cancelled'
      - id: approvedBy
        type: approvedBy
        valueMapping:
          by: verifiedBy
          'on': verifiedAt
        displayExpression: $.status = 'Approved'
filter_config:
  fields:
    - type: group
      label: Invoice Information
      fields:
        - type: group
          n_cols: 3
          fields:
            - type: selectAll
              name: businessTaxCode
              label: Business Tax Code
              outputValue: value
              placeholder: Select Legal Entity
              isLazyLoad: true
              _options:
                transform: >-
                  $businessTaxCodeList($.extend.limit, $.extend.page,
                  $.extend.search)
            - type: text
              name: form
              label: Form
              placeholder: Enter Form
            - type: text
              name: seri
              label: Seri
              placeholder: Enter Seri
        - type: group
          n_cols: 4
          fields:
            - type: text
              name: 'no'
              label: 'No'
              placeholder: Enter No
            - type: text
              name: d_status
              unvisible: true
              _value:
                transform: >-
                  $not($isNilorEmpty($.fields.status)) ? $.fields.status :
                  ['Pending', 'Approved', 'Cancelled']
            - type: selectAll
              label: Status
              name: status
              placeholder: Select Status
              outputValue: value
              _options:
                transform: >-
                  [{'value': 'Pending','label': 'Pending Approved'},{'value':
                  'Approved','label': 'Approved'},{'value': 'Cancelled','label':
                  'Cancelled'}]
            - type: dateRange
              label: Push Date From
              mode: date-picker
              name: startDate
              setting:
                format: dd/MM/yyyy
                type: date
            - type: dateRange
              label: Push Date To
              mode: date-picker
              name: endDate
              setting:
                autoFill: end-of
                format: dd/MM/yyyy
                type: date
    - type: group
      label: Employee Information
      fields:
        - type: group
          n_cols: 2
          fields:
            - type: selectAll
              name: employeeTypeCode
              label: Employee Type
              mode: multiple
              placeholder: Select Employee Type
              outputValue: value
              isLazyLoad: true
              _options:
                transform: >-
                  $employeeTypeList($.extend.limit, $.extend.page,
                  $.extend.search)
            - type: selectAll
              name: employeeId
              label: Employee
              mode: multiple
              placeholder: Select Employee
              outputValue: value
              isLazyLoad: true
              _options:
                transform: $employeesList($.extend.limit, $.extend.page, $.extend.search)
        - type: group
          n_cols: 4
          fields:
            - type: text
              name: taxCode
              label: PIT Code
              placeholder: Enter PIT Code
            - type: text
              name: nationalId
              label: National ID
              placeholder: Enter National ID
            - type: text
              name: fullName
              label: Employee Name
              placeholder: Enter Employee Name
            - type: text
              name: email
              label: Email
              placeholder: Enter Email
  filterMapping:
    - field: businessTaxCode
      operator: $in
      valueField: businessTaxCode
    - field: form
      operator: $cont
      valueField: form
    - field: seri
      operator: $cont
      valueField: seri
    - field: 'no'
      operator: $cont
      valueField: 'no'
    - field: status
      operator: $in
      valueField: d_status
    - field: pushedAt
      operator: $gte
      valueField: startDate
    - field: pushedAt
      operator: $lte
      valueField: endDate
    - field: employeeTypeCode
      operator: $in
      valueField: employeeTypeCode
    - field: employeeId
      operator: $in
      valueField: employeeId
    - field: taxCode
      operator: $cont
      valueField: taxCode
    - field: nationId
      operator: $cont
      valueField: nationalId
    - field: fullName
      operator: $cont
      valueField: fullName
    - field: email
      operator: $cont
      valueField: email
  sources:
    businessTaxCodeList:
      uri: '"/api/legal-entities/get-by-business-tax-code"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':'Y'}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name, 'value': $item.code
        }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    employeesList:
      uri: '"/api/personals/all-employees-job-datas-dropdownlist"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page, 'search': $.search, 'filter':
        [{'field':'jobdataId','operator':'$gt','value':'0' }]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label':
        $join($filter([$item.employeeId,$item.fullName], $boolean), ' - '),
        'value':  $item.employeeId}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    employeeTypeList:
      uri: '"/api/employee-type"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
layout_options:
  toolTable:
    adjustDisplay: 'true'
  show_detail_history: false
  history_widget_header_options:
    duplicate: false
  tool_table:
    - id: export
  show_create_data_table: false
  view_history_after_updated: false
  view_history_after_created: false
  is_export_grid: true
  expand_filter: true
  actions_many_handler:
    approve:
      action: edit
      confirm:
        title: Approve Invoices/ Income Certificates
        content: Do you want to approve Invoices/ Income Certificates
        type: popup-confirm
      api_config:
        url: /api/invoice-or-income-cert/approve
        method: PATCH
        messages:
          success: Approve invoice/ income cert successfully
    cancel:
      action: edit
      form_config:
        fields:
          - name: reason
            label: Reason
            type: select
            placeholder: Select Reason
            _select:
              transform: $pitReasonList($.extend.limit, $.extend.page, $.extend.search)
            outputValue: value
            isLazyLoad: true
            validators:
              - type: required
          - type: textarea
            label: Comment
            name: comment
            placeholder: Enter Comment
            textarea:
              autoSize:
                minRows: 3
              maxCharCount: 1000
            validators:
              - type: required
        sources:
          pitReasonList:
            uri: '"/api/picklists/PIT_REASONS/values/pagination"'
            method: GET
            queryTransform: ' {''limit'': $.limit,''search'':$.search , ''page'': $.page, ''filter'':[{''field'':''status'',''operator'':''$eq'',''value'':true}]}'
            bodyTransform: ''
            headerTransform: ''
            resultTransform: >-
              $map($.data[], function($item) {{'label': $item.name.default & '
              (' & $item.code & ')', 'value': $item.name.default}})[]
            disabledCache: true
            params:
              - limit
              - page
              - search
      title: Cancel Invoices/ Income Certificates
      api_config:
        url: /api/invoice-or-income-cert/cancel
        method: PATCH
        _body:
          transform: >-
            {'ids':$map($.selectedItems, function($v) { $v.id })[],
            'comment':$.formValue.comment, 'reason':$.formValue.reason}
        messages:
          success: Cancelled invoice/ income cert successfully
  dialog_footer:
    - type: buttons
      for:
        - view
      buttons:
        - id: cancel
          action: toEdit
          title: Cancel Invoice/ Income Cert
          type: primary
          _condition: $.status = 'Approved'
          authAction: Approve
        - id: approve
          action: approveInvoice
          title: Approve
          type: primary
          _condition: $.status = 'Pending'
    - type: buttons
      for:
        - edit
      buttons:
        - id: cancel
          action: cancel
          title: Cancel
          type: tertiary
        - id: confirmCancel
          action: cancelInvoice
          title: Confirm
          type: primary
  dialog_actions:
    - id: approveInvoice
      type: api
      confirm:
        title: Approve Invoices/ Income Certificates
        content: Do you want to approve Invoices/ Income Certificates
        type: popup-confirm
      config:
        _url:
          transform: '''/api/invoice-or-income-cert/approve/'' & $.id'
        method: PATCH
    - id: cancelInvoice
      type: api
      config:
        _url:
          transform: '''/api/invoice-or-income-cert/cancel/'' & $.id'
        method: PATCH
  hide_action_row: true
layout_options__header_buttons: null
options: null
create_form: null
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: view
    title: View
    icon: ' icon-eye-bold'
    type: ghost-gray
backend_url: /api/invoice-or-income-cert
screen_name: approve-invoice-income-cert
layout_options__actions_many:
  - id: approve
    title: Approve
    icon: icon-check-circle-bold
    type: secondary
  - id: cancel
    title: Cancel
    icon: icon-x-circle-bold
    type: secondary
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: legalEntityCode
    defaultName: LegalEntityCode
  - name: employeeId
    defaultName: EmployeeId
  - name: countryCode
    defaultName: CountryCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item: null
