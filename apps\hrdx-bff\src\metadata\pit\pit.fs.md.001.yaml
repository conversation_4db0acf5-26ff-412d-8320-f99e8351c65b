id: PIT.FS.MD.001
status: draft
sort: 75
user_created: 43878dbb-aa6c-4ef4-8bd7-0ea811ffafa7
date_created: '2024-07-26T08:31:49.434Z'
user_updated: cdfee544-f2ba-4ed2-ace8-5adc1ae0ed61
date_updated: '2025-06-13T06:54:35.481Z'
title: Tax Calculation Method
requirement:
  time: 1747368455366
  blocks:
    - id: rmNN5g6ePI
      type: paragraph
      data:
        text: >-
          <PERSON><PERSON><PERSON> năng cho phép bộ phận nhân sự tập đoàn/CTTV quản lý danh mục hình
          thức tính thuế
  version: 2.30.7
screen_design: null
module: PIT
local_fields:
  - code: code
    title: Code
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    description: Load data for the corresponding tax calculation method code
    pinned: true
    show_sort: true
  - code: countryName
    title: Country
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: >-
      Load country data according to the corresponding tax calculation method
      code
    pinned: false
  - code: shortName
    title: Short Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    description: >-
      Load short name data according to the corresponding tax calculation method
      code
  - code: longName
    title: Long Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    description: >-
      Load long name data according to the corresponding tax calculation method
      code
  - code: status
    title: Status
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: Boolean Tag
      collection: field_types
    description: >-
      Load status data according to the corresponding tax calculation method
      code
  - code: updatedBy
    title: Last Updated By
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: >-
      Load last editor information according to the corresponding tax
      calculation method code
  - code: updatedAt
    title: Last Updated On
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
    description: >-
      Load last edit time information according to the corresponding tax
      calculation method code
    show_sort: true
mock_data:
  - code: '00000001'
    country: Việt Nam
    shortName:
      default: Lũy tiến
      vietnamese: Lũy tiến
      english: Progressive
    longName:
      default: Lũy tiến
      vietnamese: Lũy tiến
      english: Progressive
    status: Active
    effectiveDate: '2024-06-22 10:16:25'
    createdBy: LinhHT
    createdOn: 01/05/2024 09:15:23
    lastUpdatedBy: TuanMH
    lastUpdatedOn: 01/10/2024 11:45:34
  - code: '00000002'
    country: Việt Nam
    shortName:
      default: Toàn phần
      vietnamese: Toàn phần
      english: Flat
    longName:
      default: Toàn phần
      vietnamese: Toàn phần
      english: Flat
    status: Active
    effectiveDate: '2024-06-22 10:16:25'
    createdBy: MaiNT
    createdOn: 02/12/2024 14:22:10
    lastUpdatedBy: KhoaPT
    lastUpdatedOn: 02/20/2024 16:30:15
local_buttons: null
layout: layout-table
form_config:
  formTitle:
    create: Add New Tax Calculation Method
  historyHeaderTitle: '''Tax Calculation Method Detail'''
  historyCloneInactive: true
  customTypeEmitInsertNew: proceed
  fields:
    - type: group
      _condition:
        transform: >-
          $not($.extend.formType = 'view') and $not($.extend.formType =
          'create')
      n_cols: 2
      fields:
        - name: code
          type: text
          label: Code
          disabled: true
        - name: country
          label: Country
          type: select
          placeholder: Select Country
          _select:
            transform: $nationsList($.extend.limit, $.extend.page, $.extend.search)
          isLazyLoad: true
    - type: group
      _condition:
        transform: ($.extend.formType = 'create')
      n_cols: 2
      fields:
        - name: codeAutoGenerate
          label: Code
          type: text
          disabled: true
          _value:
            transform: '''System – Generated'''
          placeholder: Automatically Generated Code
        - name: country
          label: Country
          type: select
          placeholder: Select Country
          _select:
            transform: $nationsList($.extend.limit, $.extend.page, $.extend.search)
          validators:
            - type: required
          isLazyLoad: true
    - name: shortName
      label: Short Name
      type: translation
      placeholder: Enter Short Name
      validators:
        - type: required
        - type: maxLength
          args: '300'
          text: Maximum 300 characters
      _condition:
        transform: $not($.extend.formType = 'view')
    - name: longName
      label: Long Name
      type: translation
      placeholder: Enter Long Name
      validators:
        - type: required
        - type: maxLength
          args: '500'
          text: Maximum 500 characters
      _condition:
        transform: $not($.extend.formType = 'view')
    - type: group
      _condition:
        transform: $not($.extend.formType = 'view')
      n_cols: 2
      fields:
        - name: effectiveDate
          label: Effective Date
          type: dateRange
          setting:
            format: dd/MM/yyyy
            type: date
          validators:
            - type: required
          mode: date-picker
          _value:
            transform: $isNilorEmpty($.extend.defaultValue.effectiveDate) ? $now()
          _class:
            transform: '$.extend.formType = ''view''?''unrequired'': ''required'''
        - name: status
          label: Status
          type: radio
          radio:
            - label: Active
              value: true
            - label: Inactive
              value: false
          _value:
            transform: 'true'
          validators:
            - type: required
          _condition:
            transform: $.extend.formType = 'create'
        - name: status
          label: Status
          type: radio
          radio:
            - label: Active
              value: true
            - label: Inactive
              value: false
          validators:
            - type: required
          _condition:
            transform: $.extend.formType = 'edit' or $.extend.formType = 'proceed'
    - type: group
      space: 12
      fields:
        - name: code
          type: text
          label: Code
          disabled: true
          _condition:
            transform: $.extend.formType = 'view'
        - name: country
          label: Country
          type: select
          placeholder: Select Country
          _select:
            transform: $nationsList($.extend.limit, $.extend.page, $.extend.search)
          isLazyLoad: true
          _condition:
            transform: $.extend.formType = 'view'
          validators:
            - type: required
        - name: shortName
          label: Short Name
          type: translation
          placeholder: Enter Short Name
          _condition:
            transform: $.extend.formType = 'view'
        - name: longName
          label: Long Name
          type: translation
          placeholder: Enter Long Name
          _condition:
            transform: $.extend.formType = 'view'
        - name: effectiveDate
          label: Effective Date
          type: dateRange
          setting:
            format: dd/MM/yyyy
            type: date
          validators:
            - type: required
          mode: date-picker
          _value:
            transform: $isNilorEmpty($.extend.defaultValue.effectiveDate) ? $now()
          _class:
            transform: '$.extend.formType = ''view''?''unrequired'': ''required'''
          _condition:
            transform: $.extend.formType = 'view'
        - name: status
          label: Status
          type: radio
          _condition:
            transform: $.extend.formType = 'view'
          radio:
            - label: Active
              value: true
            - label: Inactive
              value: false
        - type: translationTextArea
          label: Note
          name: note
          placeholder: Enter Note
          textarea:
            autoSize:
              minRows: 3
            maxCharCount: 1000
          validators:
            - type: maxLength
              args: '1000'
              text: Maximum 1000 characters
  sources:
    nationsList:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      queryTransform: ' {''limit'': $.limit,''search'':$.search , ''page'': $.page, ''filter'':[{''field'':''status'',''operator'':''$eq'',''value'':true}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data[], function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
filter_config:
  fields:
    - name: code
      labelType: type-grid
      label: Code
      type: text
      placeholder: Enter Code
    - name: country
      labelType: type-grid
      label: Country
      type: selectAll
      mode: multiple
      placeholder: Select Country
      _options:
        transform: $nationsList()
    - name: shortName
      labelType: type-grid
      label: Short Name
      type: text
      placeholder: Enter Short Name
    - name: longName
      labelType: type-grid
      label: Long Name
      type: text
      placeholder: Enter Long Name
    - name: status
      labelType: type-grid
      label: Status
      type: radio
      value: ''
      radio:
        - label: All
          value: ''
        - label: Active
          value: true
        - label: Inactive
          value: false
    - name: updatedBy
      label: Last Updated By
      type: selectAll
      mode: multiple
      labelType: type-grid
      placeholder: Select Last Updated By
      _options:
        transform: $userList()
    - name: lastUpdatedOn
      labelType: type-grid
      label: Last Updated On
      type: dateRange
  filterMapping:
    - field: code
      operator: $cont
      valueField: code
    - field: countryCode
      operator: $in
      valueField: country.(value)
    - field: shortName
      operator: $cont
      valueField: shortName
    - field: longName
      operator: $cont
      valueField: longName
    - field: status
      operator: $eq
      valueField: status
    - field: updatedBy
      operator: $in
      valueField: updatedBy.(value)
    - field: updatedAt
      operator: $between
      valueField: lastUpdatedOn
  sources:
    nationsList:
      uri: '"/api/picklists/COUNTRIES/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})
      disabledCache: true
    userList:
      uri: '"/api/internal-users"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.userName, 'value':
        $item.userName}})
      disabledCache: true
layout_options:
  view_after_created: false
  view_after_updated: false
  skip_insert_new_proceed_history: false
  show_dialog_form_save_add_button: true
  toolTable:
    adjustDisplay: 'true'
  show_filter_table: false
  tool_table:
    - id: export
      icon: icon-upload-simple-bold
  history_widget_header_options:
    duplicate: false
  custom_history_backend_url: /api/tax-form/custom-history/:code
  view_history_after_updated: true
  delete_multi_items: true
  is_export_grid: true
  is_open_history_after_crud: false
  hide_action_row: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: icon-plus-bold
    type: primary
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    title: Edit
    icon: icon-pencil-simple-bold
    type: ghost-gray
  - id: delete
    title: null
    icon: icon-trash
    type: ghost-gray
backend_url: /api/tax-form
screen_name: tax-calculation-method
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: countryCode
    defaultName: CountryCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Tax Calculation Method
  parent:
    title: General Setting
