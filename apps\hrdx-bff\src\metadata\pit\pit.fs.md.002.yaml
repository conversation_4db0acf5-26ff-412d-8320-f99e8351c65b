id: PIT.FS.MD.002
status: draft
sort: 93
user_created: 43878dbb-aa6c-4ef4-8bd7-0ea811ffafa7
date_created: '2024-07-26T08:54:12.535Z'
user_updated: cdfee544-f2ba-4ed2-ace8-5adc1ae0ed61
date_updated: '2025-07-22T02:04:44.836Z'
title: Tax Bracket
requirement:
  time: 1748422262979
  blocks:
    - id: GWG2J5n3lW
      type: paragraph
      data:
        text: >-
          Chứ<PERSON> năng đáp ứng các yêu cầu thêm mới, chỉnh sửa, xóa và tìm kiếm
          danh sách biểu mẫu thuế lũy tiến theo quy định nhà nước
  version: 2.30.7
screen_design: null
module: PIT
local_fields:
  - code: code
    title: Code
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    pinned: true
    show_sort: true
  - code: countryName
    title: Country
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: shortName
    title: Short name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
  - code: longName
    title: Long name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
  - code: taxCalculationMethod
    title: Tax Calculation Method
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
  - code: selfDeduction
    title: Self Deduction
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tag
      collection: field_types
    extra_config:
      tags:
        - value: true
          label: 'Yes'
          style:
            background_color: '#E0FAE9'
        - value: false
          label: 'No'
          style:
            background_color: '#F1F3F5'
  - code: status
    title: Status
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: Boolean Tag
      collection: field_types
  - code: updatedBy
    title: Last Updated By
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: updatedAt
    title: Last Updated On
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
mock_data:
  - code: '00000001'
    country: Việt Nam
    shortName:
      default: Lũy tiến VN 2013
      vietnamese: Lũy tiến VN 2013
      english: Progressive Vietnam 2013
    longName:
      default: Lũy tiến VN 2013
      vietnamese: Lũy tiến VN 2013
      english: Progressive Vietnam 2013
    currency: VND
    taxCalculationMethod: Lũy tiến
    status: Active
    effectiveDate: '2024-06-22'
    createdBy: HanhDT
    createdAt: 03/25/2024 07:55:45
    updatedBy: DucTB
    updatedAt: 03/30/2024 10:25:50
  - code: '00000002'
    country: Việt Nam
    shortName:
      default: Toàn phần 10%
      vietnamese: Toàn phần 10%
      english: Flat 10%
    longName:
      default: Toàn phần 10%
      vietnamese: Toàn phần 10%
      english: Flat Vietnam 10%
    currency: VND
    percent: 10%
    data:
      - taxGrade: '1'
        monthlyTaxableIncome: 5,000,000
        taxRate: '5'
      - taxGrade: '2'
        monthlyTaxableIncome: 10,000,000
        taxRate: '10'
      - taxGrade: '3'
        monthlyTaxableIncome: 18,000,000
        taxRate: '15'
      - taxGrade: '4'
        monthlyTaxableIncome: 32,000,000
        taxRate: '20'
      - taxGrade: '5'
        monthlyTaxableIncome: 52,000,000
        taxRate: '25'
    taxCalculationMethod: Toàn phần
    status: Active
    effectiveDate: '2024-06-22'
    createdBy: ThaoBT
    createdAt: 04/18/2024 16:10:12
    updatedBy: TrangLQ
    updatedAt: 04/22/2024 18:40:22
local_buttons: null
layout: layout-table
form_config:
  formTitle:
    create: Add New Tax Bracket
  historyHeaderTitle: '''Tax Bracket Details'''
  historyCloneInactive: true
  formSize:
    history: middle
  fields:
    - type: group
      n_cols: 2
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - name: code
          label: Code
          type: text
          placeholder: Enter Code
          _condition:
            transform: $.extend.formType = 'create'
          formatType: code
          validators:
            - type: required
            - type: pattern
              args: ^[a-zA-Z0-9_.-]*$
              text: >-
                Code must only contain letters, numbers, hyphens, underscores,
                or dots.
            - type: maxLength
              args: '8'
              text: Code should not exceed 8 characters
        - name: code
          type: text
          label: Code
          disabled: true
          placeholder: Enter Code
          _condition:
            transform: >-
              $.extend.formType = 'proceed' or $.extend.formType = 'view' or
              $.extend.formType = 'edit'
        - name: country
          label: Country
          type: select
          placeholder: Select Country
          _select:
            transform: $countriesList($.extend.limit, $.extend.page, $.extend.search)
          isLazyLoad: true
          validators:
            - type: required
    - name: code
      type: text
      label: Code
      disabled: true
      placeholder: Enter Code
      _condition:
        transform: $.extend.formType = 'view'
    - name: country
      label: Country
      type: select
      placeholder: Select Country
      _select:
        transform: $countriesList($.extend.limit, $.extend.page, $.extend.search)
      isLazyLoad: true
      _condition:
        transform: $.extend.formType = 'view'
    - name: shortName
      label: Short Name
      type: translation
      placeholder: Enter Short Name
      validators:
        - type: required
        - type: maxLength
          args: '300'
          text: Maximum 300 characters
      _condition:
        transform: $not($.extend.formType = 'view')
    - name: shortName
      label: Short Name
      type: translation
      placeholder: Enter Short Name
      _condition:
        transform: $.extend.formType = 'view'
    - name: longName
      label: Long Name
      type: translation
      placeholder: Enter Long Name
      validators:
        - type: required
        - type: maxLength
          args: '500'
          text: Maximum 500 characters
      _condition:
        transform: $not($.extend.formType = 'view')
    - name: longName
      label: Long Name
      type: translation
      placeholder: Enter Long Name
      _condition:
        transform: $.extend.formType = 'view'
    - type: group
      n_cols: 2
      _n_cols:
        transform: '$.extend.formType = ''view''? 1 : 2'
      fields:
        - name: taxCalculationMethodCode
          label: Tax Calculation Method
          type: select
          placeholder: Select Tax Calculation Method
          outputValue: value
          isLazyLoad: true
          _select:
            transform: >-
              $taxMethodList($.extend.page,$.extend.limit,$.extend.search,$.fields.effectiveDate,NULL,$.fields.country.value)
          validators:
            - type: required
          _value:
            transform: >-
              $.extend.formType = 'proceed' ? $boolean($.variables._insertNewRc)
              = true ? $.variables._insertNewRc : '_setSelectValueNull'
          dependantField: $.fields.effectiveDate,$.fields.country
          dependantFieldSkip: 2
        - type: radio
          label: Self Deduction
          name: selfDeduction
          _condition:
            transform: $not($.extend.formType = 'view')
          value: true
          radio:
            - label: 'Yes'
              value: true
            - label: 'No'
              value: false
        - label: Self Deduction
          name: selfDeduction
          _condition:
            transform: $.extend.formType = 'view'
          type: select
          readOnly: true
          select:
            - label: 'Yes'
              value: true
            - label: 'No'
              value: false
          displaySetting:
            type: Tag
            extraConfig:
              size: small
              tags:
                - value: true
                  label: 'Yes'
                  style:
                    background_color: '#E0FAE9'
                - value: false
                  label: 'No'
                  style:
                    background_color: '#F1F3F5'
    - type: group
      n_cols: 2
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - name: currencyCode
          label: Currency
          type: select
          placeholder: Select Currency
          outputValue: value
          _select:
            transform: $currencyList()
          validators:
            - type: required
        - name: effectiveDate
          label: Effective Date
          type: dateRange
          setting:
            format: dd/MM/yyyy
            type: date
          validators:
            - type: required
          mode: date-picker
          _value:
            transform: $isNilorEmpty($.extend.defaultValue.effectiveDate) ? $now()
          _class:
            transform: '$.extend.formType = ''view''?''unrequired'': ''required'''
        - name: status
          label: Status
          type: radio
          col: 2
          radio:
            - label: Active
              value: true
            - label: Inactive
              value: false
          _value:
            transform: 'true'
          validators:
            - type: required
          _condition:
            transform: $.extend.formType = 'create'
        - name: status
          label: Status
          type: radio
          col: 2
          radio:
            - label: Active
              value: true
            - label: Inactive
              value: false
          validators:
            - type: required
          _condition:
            transform: $.extend.formType = 'edit' or $.extend.formType = 'proceed'
    - name: currencyCode
      label: Currency
      type: select
      placeholder: Select Currency
      outputValue: value
      _select:
        transform: $currencyList()
      _condition:
        transform: $.extend.formType = 'view'
    - name: effectiveDate
      label: Effective Date
      type: dateRange
      setting:
        format: dd/MM/yyyy
        type: date
      mode: date-picker
      _condition:
        transform: $.extend.formType = 'view'
    - name: status
      label: Status
      type: radio
      radio:
        - label: Active
          value: true
        - label: Inactive
          value: false
      _condition:
        transform: $.extend.formType = 'view'
    - type: translationTextArea
      label: Note
      name: note
      placeholder: Enter Note
      textarea:
        autoSize:
          minRows: 3
        maxCharCount: 1000
      validators:
        - type: maxLength
          args: '1000'
          text: Maximum 1000 characters
    - type: array
      mode: table
      name: tariffLevels
      _condition:
        transform: $not($.extend.formType = 'view')
      minSize: 1
      arrayOptions:
        canChangeSize: true
      field:
        type: group
        skipCloneGroupControl: true
        fields:
          - name: taxGrade
            label: Tax Grade
            type: text
            validators:
              - type: required
            _disabled:
              transform: 'true'
            _value:
              transform: $.extend.path[-2] + 1
            align: start
          - name: taxedIncomePerMonth
            label: Monthly Taxable Income
            type: number
            validators:
              - type: required
            number:
              format: currency
              max: '99999999999999'
              precision: 4
            align: end
          - name: taxRate
            label: Tax Rate (%)
            type: number
            validators:
              - type: required
              - type: max
                args: 100
                text: Tax Rate can not over 100
            align: end
    - type: table
      name: tariffLevels
      rowIdName: taxGrade
      layout_option:
        hide_action_row: true
        show_pagination: false
      _condition:
        transform: $.extend.formType = 'view'
      columns:
        - code: taxGrade
          title: Tax Grade
          align: start
          data_type:
            key: String
            collection: data_types
          display_type:
            key: Label
            collection: field_types
          width: '6'
        - code: taxedIncomePerMonth
          title: Monthly Taxable Income
          align: end
          data_type:
            key: String
            collection: data_types
          display_type:
            key: Currency
            collection: field_types
          width: '12'
        - code: taxRate
          title: Tax Rate (%)
          align: end
          data_type:
            key: String
            collection: data_types
          display_type:
            key: Label
            collection: field_types
          width: '7'
      _dataSource:
        transform: $.extend.defaultValue.tariffLevels
  sources:
    countriesList:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      queryTransform: ' {''limit'': $.limit, ''page'': $.page, ''search'': $.search,  ''filter'':[{''field'':''status'',''operator'':''$eq'',''value'':true}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data[], function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    taxMethodList:
      uri: '"/api/tax-form"'
      method: GET
      queryTransform: >-
        {'limit': $.pageSize, 'page':$.page, 'search': $.search, 'filter':
        [{'field':'code','operator':'$eq','value':$.taxCalculationMethodCode},{'field':'countryCode','operator':'$eq','value':$.countryCode},{'field':'status','operator':'$eq','value':true},{'field':'effectiveDate','operator':'$lte','value':$.effectiveDate},{'field':'effectiveDateTo','operator':'$eq','value':'NULL'},{'field':'effectiveDateTo','operator':'$gt','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $string($item.code)}})[]
      disabledCache: true
      params:
        - page
        - pageSize
        - search
        - effectiveDate
        - taxCalculationMethodCode
        - countryCode
    currencyList:
      uri: '"/api/picklists/CURRENCY/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
  variables:
    _insertNewRc:
      transform: >-
        $filter($taxMethodList(1,1, '',$.fields.effectiveDate,
        $.extend.defaultValue.taxCalculationMethodCode.value), function($v)
        {$v.value = $.extend.defaultValue.taxCalculationMethodCode.value})
filter_config:
  fields:
    - name: code
      labelType: type-grid
      label: Code
      type: text
      placeholder: Enter Code
    - name: country
      labelType: type-grid
      label: Country
      type: selectAll
      mode: multiple
      placeholder: Select Country
      _options:
        transform: $countriesList($.extend.limit, $.extend.page, $.extend.search)
    - name: shortName
      labelType: type-grid
      label: Short Name
      type: text
      placeholder: Enter Short Name
    - name: longName
      labelType: type-grid
      label: Long Name
      type: text
      placeholder: Enter Long Name
    - name: taxCalculationMethod
      labelType: type-grid
      label: Tax Calculation Method
      type: select
      mode: multiple
      isLazyLoad: true
      placeholder: Select Tax Calculation Method
      _select:
        transform: $taxMethodList($.extend.page,$.extend.limit,$.extend.search)
    - name: status
      labelType: type-grid
      label: Status
      type: radio
      value: ''
      radio:
        - label: All
          value: ''
        - label: Active
          value: true
        - label: Inactive
          value: false
    - name: updatedBy
      label: Last Updated By
      type: selectAll
      mode: multiple
      labelType: type-grid
      placeholder: Select Last Updated By
      _options:
        transform: $userList()
    - name: updatedAt
      labelType: type-grid
      label: Last Updated On
      type: dateRange
      setting:
        format: dd/MM/yyyy
        type: date
  filterMapping:
    - field: code
      operator: $cont
      valueField: code
    - field: countryCode
      operator: $in
      valueField: country.(value)
    - field: shortName
      operator: $cont
      valueField: shortName
    - field: longName
      operator: $cont
      valueField: longName
    - field: taxCalculationMethodCode
      operator: $in
      valueField: taxCalculationMethod.(value)
    - field: status
      operator: $eq
      valueField: status
    - field: updatedBy
      operator: $in
      valueField: updatedBy.(value)
    - field: updatedAt
      operator: $between
      valueField: updatedAt
  sources:
    countriesList:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      queryTransform: ' {''limit'': $.limit, ''page'': $.page,''search'': $.search ,''filter'':[{''field'':''status'',''operator'':''$eq'',''value'':true}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data[], function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    taxMethodList:
      uri: '"/api/tax-form"'
      method: GET
      queryTransform: '{''limit'': $.pageSize, ''page'':$.page, ''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data[], function($item) {{'label': $item.longName.default & ' ('
        & $item.code & ')', 'value': $string($item.code)}})[]
      disabledCache: true
      params:
        - page
        - pageSize
        - search
    userList:
      uri: '"/api/internal-users"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.userName, 'value':
        $item.userName}})[]
      disabledCache: true
layout_options:
  show_dialog_form_save_add_button: true
  toolTable:
    adjustDisplay: 'true'
  show_detail_history: true
  history_widget_header_options:
    duplicate: false
  tool_table:
    - id: export
      icon: icon-upload-simple-bold
  view_history_after_updated: true
  custom_history_backend_url: /api/tariff/custom-history/:code
  view_history_after_created: true
  delete_multi_items: true
  is_export_grid: true
  hide_action_row: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    icon: pencil
    type: ghost-gray
  - id: delete
    icon: icon-trash
    type: ghost-gray
backend_url: /api/tariff
screen_name: tariff
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: countryCode
    defaultName: CountryCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Tax Bracket
  parent:
    title: General Setting
