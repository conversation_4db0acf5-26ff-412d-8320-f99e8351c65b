id: PIT.FS.MD.004
status: draft
sort: 95
user_created: 43878dbb-aa6c-4ef4-8bd7-0ea811ffafa7
date_created: '2024-07-26T10:16:54.005Z'
user_updated: cdfee544-f2ba-4ed2-ace8-5adc1ae0ed61
date_updated: '2025-07-28T08:39:53.277Z'
title: Tax Settlement Group
requirement:
  time: 1747369339207
  blocks:
    - id: eiXHI4GKJx
      type: paragraph
      data:
        text: >-
          Ch<PERSON><PERSON> năng cho phép bộ phận nhân sự tập đoàn/CT<PERSON> quản lý danh mục nhóm
          quyết toán thuế
  version: 2.30.7
screen_design: null
module: PIT
local_fields:
  - code: code
    title: Code
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    pinned: true
    show_sort: true
  - code: countryName
    title: Country
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: companyName
    title: Company
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: shortName
    title: Short Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
  - code: longName
    title: Long Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
  - code: status
    title: Status
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: Boolean Tag
      collection: field_types
  - code: updatedBy
    title: Last Updated By
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: updatedAt
    title: Last Updated On
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
mock_data:
  - code: '00000001'
    country: Viet Nam
    shortName:
      default: FSOFT HN
      vietnamese: FSOFT HN
      english: FSOFT Hanoi
    longName:
      default: Công ty TNHH Phần mềm FPT Hà Nội
      vietnamese: Công ty TNHH Phần mềm FPT Hà Nội
      english: FPT Software Company Hanoi
    status: Active
    createdBy: VyTT
    createdAt: 07/14/2024 15:05:10
    updatedBy: MinhHN
    updatedAt: 07/18/2024 16:30:40
    effectiveDate: '2024-06-22 10:16:25'
  - code: '00000002'
    country: Viet Nam
    shortName:
      default: FSOFT DN
      vietnamese: FSOFT ĐN
      english: FSOFT Danang
    longName:
      default: Công ty TNHH Phần mềm FPT Đà Nẵng
      vietnamese: Công ty TNHH Phần mềm FPT Đà Nẵng
      english: FPT Software Company Danang
    status: Active
    createdBy: NhatLV
    createdAt: 07/20/2024 08:50:50
    updatedBy: LinhDT
    updatedAt: 07/25/2024 09:55:30
    effectiveDate: '2024-06-22 10:16:25'
  - code: '00000003'
    country: Viet Nam
    shortName:
      default: FSOFT HCM
      vietnamese: FSOFT HCM
      english: FSOFT HCM
    longName:
      default: Công ty TNHH Phần mềm FPT Hồ Chí Minh
      vietnamese: Công ty TNHH Phần mềm FPT Hồ Chí Minh
      english: FPT Software Company Ho Chi Minh
    status: Active
    createdBy: NhatLV
    createdAt: 07/20/2024 08:50:50
    updatedBy: LinhDT
    updatedAt: 07/25/2024 09:55:30
    effectiveDate: '2024-06-22 10:16:25'
local_buttons: null
layout: layout-table
form_config:
  formTitle:
    create: Add New Tax Settlement Group
  historyHeaderTitle: '''Tax Settlement Group Detail'''
  historyCloneInactive: true
  fields:
    - type: group
      n_cols: 2
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - name: codeAutoGenerate
          col: 2
          label: Code
          type: text
          disabled: true
          _value:
            transform: '''System – Generated'''
          placeholder: Automatically Generated Code
          _condition:
            transform: $.extend.formType = 'create'
        - name: code
          type: text
          col: 2
          label: Code
          disabled: true
          _condition:
            transform: $not($.extend.formType = 'create')
        - type: group
          col: 2
          n_cols: 2
          _n_cols:
            transform: '$.extend.formType = ''view''? 1 : 2'
          fields:
            - name: effectiveDate
              label: Effective Date
              type: dateRange
              setting:
                format: dd/MM/yyyy
                type: date
              validators:
                - type: required
              clearFieldsAfterChange:
                - company
              mode: date-picker
              _value:
                transform: $isNilorEmpty($.extend.defaultValue.effectiveDate) ? $now()
              _class:
                transform: '$.extend.formType = ''view''?''unrequired'': ''required'''
            - name: effectiveDateTo
              type: number
              unvisible: true
              value: 0
            - name: status
              label: Status
              type: radio
              radio:
                - label: Active
                  value: true
                - label: Inactive
                  value: false
              _value:
                transform: $.extend.formType = 'create' ? true
              _condition:
                transform: $.extend.formType != 'view'
        - name: country
          label: Country
          type: select
          placeholder: Select Country
          _select:
            transform: $nationsList($.extend.limit, $.extend.page, $.extend.search)
          validators:
            - type: required
          isLazyLoad: true
          _condition:
            transform: $.extend.formType != 'view'
        - name: company
          label: Company
          type: select
          placeholder: Select Company
          _select:
            transform: >-
              $companiesList($.extend.limit, $.extend.page, $.extend.search,
              $.fields.effectiveDate)
          validators:
            - type: required
          isLazyLoad: true
          _condition:
            transform: $.extend.formType != 'view'
    - name: code
      label: Code
      type: text
      placeholder: System – Generated
      disabled: 'false'
      _condition:
        transform: $.extend.formType = 'view'
    - name: country
      label: Country
      type: select
      placeholder: Select Country
      _select:
        transform: $nationsList($.extend.limit, $.extend.page, $.extend.search)
      isLazyLoad: true
      _condition:
        transform: $.extend.formType = 'view'
    - name: company
      label: Company
      type: select
      placeholder: Select Company
      _select:
        transform: $companiesList($.extend.limit, $.extend.page, $.extend.search)
      isLazyLoad: true
      _condition:
        transform: $.extend.formType = 'view'
    - name: shortName
      label: Short Name
      type: translation
      placeholder: Enter Short Name
      validators:
        - type: required
        - type: maxLength
          args: 300
          text: Maximum 300 characters
      _condition:
        transform: $not($.extend.formType = 'view')
    - name: shortName
      label: Short Name
      type: translation
      placeholder: Enter Short Name
      _condition:
        transform: $.extend.formType = 'view'
    - name: longName
      label: Long Name
      type: translation
      placeholder: Enter Long Name
      validators:
        - type: required
        - type: maxLength
          args: 500
          text: Maximum 500 characters
      _condition:
        transform: $not($.extend.formType = 'view')
    - name: longName
      label: Long Name
      type: translation
      placeholder: Enter Long Name
      _condition:
        transform: $.extend.formType = 'view'
    - name: status
      label: Status
      type: radio
      radio:
        - label: Active
          value: true
        - label: Inactive
          value: false
      _value:
        transform: $.extend.formType = 'create' ? true
      _condition:
        transform: $.extend.formType = 'view'
    - name: note
      label: Note
      type: translationTextArea
      placeholder: Enter Note
      textarea:
        autoSize:
          minRows: 3
        maxCharCount: 1000
      validators:
        - type: maxLength
          args: '1000'
          text: Maximum 1000 characters
  sources:
    nationsList:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      queryTransform: ' {''limit'': $.limit, ''page'': $.page, ''search'': $.search ,''filter'':[{''field'':''status'',''operator'':''$eq'',''value'':true}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    companiesList:
      uri: '"/api/companies/get-by"'
      queryTransform: >-
        {'limit': $.limit,'page': $.page, 'search': $.search, 'filter':
        [{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
filter_config:
  fields:
    - name: code
      labelType: type-grid
      label: Code
      type: text
      placeholder: Enter Code
    - name: country
      labelType: type-grid
      label: Country
      type: selectAll
      placeholder: Select Country
      isLazyLoad: true
      _options:
        transform: $nationsList($.extend.limit, $.extend.page, $.extend.search)
    - name: company
      labelType: type-grid
      label: Company
      type: selectAll
      placeholder: Select Company
      isLazyLoad: true
      _options:
        transform: $companiesList($.extend.limit, $.extend.page, $.extend.search)
    - name: shortName
      labelType: type-grid
      label: Short Name
      type: text
      placeholder: Enter Short Name
    - name: longName
      labelType: type-grid
      label: Long Name
      type: text
      placeholder: Enter Long Name
    - name: status
      labelType: type-grid
      label: Status
      type: radio
      value: ''
      radio:
        - label: All
          value: ''
        - label: Active
          value: true
        - label: Inactive
          value: false
    - labelType: type-grid
      name: updatedBy
      label: Last Updated By
      type: selectAll
      mode: multiple
      placeholder: Select Last Updated By
      _options:
        transform: $userList()
    - name: updatedAt
      labelType: type-grid
      label: Last Updated On
      type: dateRange
      setting:
        format: dd/MM/yyyy
        type: date
  filterMapping:
    - field: code
      operator: $cont
      valueField: code
    - field: countryCode
      operator: $in
      valueField: country.(value)
    - field: companyCode
      operator: $in
      valueField: company.(value)
    - field: shortName
      operator: $cont
      valueField: shortName
    - field: longName
      operator: $cont
      valueField: longName
    - field: status
      operator: $eq
      valueField: status
    - field: updatedBy
      operator: $in
      valueField: updatedBy.(value)
    - field: updatedAt
      operator: $between
      valueField: updatedAt
  sources:
    nationsList:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      queryTransform: ' {''limit'': $.limit, ''page'': $.page, ''search'': $.search, ''filter'':[{''field'':''status'',''operator'':''$eq'',''value'':true}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    userList:
      uri: '"/api/internal-users"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.userName, 'value':
        $item.userName}})
      disabledCache: true
    companiesList:
      uri: '"/api/companies/get-by"'
      queryTransform: >-
        {'limit': $.limit,'page': $.page, 'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
  variables:
    _userList:
      transform: $userList()
layout_options:
  show_dialog_form_save_add_button: true
  toolTable:
    adjustDisplay: 'true'
  show_detail_history: true
  history_widget_header_options:
    duplicate: false
  tool_table:
    - id: export
      icon: icon-upload-simple-bold
  view_history_after_updated: false
  custom_history_backend_url: /api/group-tax-settlement/custom-history/:code
  historyCloneInactive: true
  view_history_after_created: false
  delete_multi_items: true
  is_export_grid: true
  hide_action_row: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    icon: pencil
    type: ghost-gray
  - id: delete
    icon: trash
    type: ghost-gray
backend_url: /api/group-tax-settlement
screen_name: tax-settlement-group
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: companyCode
    defaultName: CompanyCode
  - name: countryCode
    defaultName: CountryCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Tax Settlement Group
  parent:
    title: Entity Setting
