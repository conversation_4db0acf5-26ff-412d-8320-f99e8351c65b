id: PIT.FS.MD.005
status: draft
sort: 96
user_created: 43878dbb-aa6c-4ef4-8bd7-0ea811ffafa7
date_created: '2024-07-26T10:51:39.946Z'
user_updated: cdfee544-f2ba-4ed2-ace8-5adc1ae0ed61
date_updated: '2025-07-22T10:12:45.151Z'
title: Tax Period
requirement:
  time: 1748422236526
  blocks:
    - id: Yk9ShgvhjA
      type: paragraph
      data:
        text: >-
          Chức năng đáp ứng các yêu cầu thêm mới, chỉnh sửa, xóa và tìm kiếm
          danh sách kỳ tính thuế tương ứng với nghiệp vụ của từng CTTV
  version: 2.30.7
screen_design: null
module: PIT
local_fields:
  - code: code
    title: Tax Period Code
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    pinned: true
    show_sort: true
    options__tabular__column_width: 14
  - code: shortName
    title: Short Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    pinned: false
  - code: longName
    title: Long Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
  - code: assessmentPeriodType
    title: Tax Period Type
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: period
    title: Period
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: yearName
    title: Year
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: assessmentPeriodStartDate
    title: Tax Period Start Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
  - code: assessmentPeriodEndDate
    title: Tax Period End Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
  - code: note
    title: Note
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
  - code: updatedBy
    title: Last Updated By
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: updatedAt
    title: Last Updated On
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
mock_data:
  - code: '00000001'
    country: Viet Nam
    group: FPT
    company: FPT IS
    taxSettlementGroup: FPT IS HN
    shortName:
      default: FPT IS HN_2023
      vietnamese: FPT IS HN_2023
      english: FPT IS HN_2023
    longName:
      default: FPT IS HN_2023
      vietnamese: FPT IS HN_2023
      english: FPT IS HN_2023
    assessmentPeriodType: Quyết toán
    period: Năm
    assessmentPeriodStartDate: '2023-01-01'
    assessmentPeriodEndDate: '2023-12-31'
    note:
      default: Note here
      vietnamese: Ghi chú ở đây
      english: Note here
    createdBy: hoantr34
    createdOn: '2023-01-01 10:16:00'
    lastUpdatedBy: ChauPV
    lastUpdatedOn: '2024-01-01 10:16:25'
  - code: '00000002'
    country: Viet Nam
    group: FPT
    company: FPT IS
    taxSettlementGroup: FPT IS HN
    shortName:
      default: FPT IS HN_01/2023
      vietnamese: FPT IS HN_01/2023
      english: FPT IS HN_01/2023
    longName:
      default: FPT IS HN_01/2023
      vietnamese: FPT IS HN_01/2023
      english: FPT IS HN_01/2023
    assessmentPeriodType: Kê khai
    period: Tháng
    assessmentPeriodStartDate: '2023-01-01'
    assessmentPeriodEndDate: '2023-01-31'
    note:
      default: Note here
      vietnamese: Ghi chú ở đây
      english: Note here
    createdBy: hoantr34
    createdOn: '2023-01-01 10:16:00'
    lastUpdatedBy: ChauPV23
    lastUpdatedOn: '2023-01-01 10:16:25'
  - code: '00000003'
    country: Viet Nam
    group: FPT
    company: FPT IS
    taxSettlementGroup: FPT IS HN
    shortName:
      default: FPT IS HN_02/2023
      vietnamese: FPT IS HN_02/2023
      english: FPT IS HN_02/2023
    longName:
      default: FPT IS HN_02/2023
      vietnamese: FPT IS HN_02/2023
      english: FPT IS HN_02/2023
    assessmentPeriodType: Kê khai
    period: Tháng
    assessmentPeriodStartDate: '2023-02-01'
    assessmentPeriodEndDate: '2023-02-28'
    note:
      default: Note here
      vietnamese: Ghi chú ở đây
      english: Note here
    createdBy: hoantr34
    createdOn: '2023-01-01 10:16:00'
    lastUpdatedBy: ChauPV
    lastUpdatedOn: '2023-01-01 10:16:25'
  - code: '00000004'
    country: Viet Nam
    group: FPT
    company: FPT IS
    taxSettlementGroup: FPT IS HN
    shortName:
      default: FPT IS HN_03/2023
      vietnamese: FPT IS HN_03/2023
      english: FPT IS HN_03/2023
    longName:
      default: FPT IS HN_03/2023
      vietnamese: FPT IS HN_03/2023
      english: FPT IS HN_03/2023
    assessmentPeriodType: Kê khai
    period: Tháng
    assessmentPeriodStartDate: '2023-03-01'
    assessmentPeriodEndDate: '2023-03-31'
    note:
      default: Note here
      vietnamese: Ghi chú ở đây
      english: Note here
    createdBy: hoantr34
    createdOn: '2023-01-01 10:16:00'
    lastUpdatedBy: ChauPV
    lastUpdatedOn: '2023-01-01 10:16:25'
  - code: '00000005'
    country: Viet Nam
    group: FPT
    company: FPT IS
    taxSettlementGroup: FPT IS HN
    shortName:
      default: FPT IS HN_04/2023
      vietnamese: FPT IS HN_04/2023
      english: FPT IS HN_04/2023
    longName:
      default: FPT IS HN_04/2023
      vietnamese: FPT IS HN_04/2023
      english: FPT IS HN_04/2023
    assessmentPeriodType: Kê khai
    period: Tháng
    assessmentPeriodStartDate: '2023-04-01'
    assessmentPeriodEndDate: '2023-04-30'
    note:
      default: Note here
      vietnamese: Ghi chú ở đây
      english: Note here
    createdBy: hoantr34
    createdOn: '2023-01-01 10:16:00'
    lastUpdatedBy: ChauPV
    lastUpdatedOn: '2023-01-01 10:16:25'
  - code: '00000006'
    country: Viet Nam
    group: FPT
    company: FPT IS
    taxSettlementGroup: FPT IS HN
    shortName:
      default: FPT IS HN_05/2023
      vietnamese: FPT IS HN_05/2023
      english: FPT IS HN_05/2023
    longName:
      default: FPT IS HN_05/2023
      vietnamese: FPT IS HN_05/2023
      english: FPT IS HN_05/2023
    assessmentPeriodType: Kê khai
    period: Tháng
    assessmentPeriodStartDate: '2023-05-01'
    assessmentPeriodEndDate: '2023-05-31'
    note:
      default: Note here
      vietnamese: Ghi chú ở đây
      english: Note here
    createdBy: hoantr34
    createdOn: '2023-01-01 10:16:00'
    lastUpdatedBy: ChauPV
    lastUpdatedOn: '2023-01-01 10:16:25'
  - code: '00000007'
    country: Viet Nam
    group: FPT
    company: FPT IS
    taxSettlementGroup: FPT IS HN
    shortName:
      default: FPT IS HN_06/2023
      vietnamese: FPT IS HN_06/2023
      english: FPT IS HN_06/2023
    longName:
      default: FPT IS HN_06/2023
      vietnamese: FPT IS HN_06/2023
      english: FPT IS HN_06/2023
    assessmentPeriodType: Kê khai
    period: Tháng
    assessmentPeriodStartDate: '2023-06-01'
    assessmentPeriodEndDate: '2023-06-30'
    note:
      default: Note here
      vietnamese: Ghi chú ở đây
      english: Note here
    createdBy: hoantr34
    createdOn: '2023-01-01 10:16:00'
    lastUpdatedBy: ChauPV
    lastUpdatedOn: '2023-01-01 10:16:25'
  - code: '00000008'
    country: Viet Nam
    group: FPT
    company: FPT IS
    taxSettlementGroup: FPT IS HN
    shortName:
      default: FPT IS HN_07/2023
      vietnamese: FPT IS HN_07/2023
      english: FPT IS HN_07/2023
    longName:
      default: FPT IS HN_07/2023
      vietnamese: FPT IS HN_07/2023
      english: FPT IS HN_07/2023
    assessmentPeriodType: Kê khai
    period: Tháng
    assessmentPeriodStartDate: '2023-07-01'
    assessmentPeriodEndDate: '2023-07-31'
    note:
      default: Note here
      vietnamese: Ghi chú ở đây
      english: Note here
    createdBy: hoantr34
    createdOn: '2023-01-01 10:16:00'
    lastUpdatedBy: ChauPV
    lastUpdatedOn: '2023-01-01 10:16:25'
  - code: '00000009'
    country: Viet Nam
    group: FPT
    company: FPT IS
    taxSettlementGroup: FPT IS HN
    shortName:
      default: FPT IS HN_08/2023
      vietnamese: FPT IS HN_08/2023
      english: FPT IS HN_08/2023
    longName:
      default: FPT IS HN_08/2023
      vietnamese: FPT IS HN_08/2023
      english: FPT IS HN_08/2023
    assessmentPeriodType: Kê khai
    period: Tháng
    assessmentPeriodStartDate: '2023-08-01'
    assessmentPeriodEndDate: '2023-08-31'
    note:
      default: Note here
      vietnamese: Ghi chú ở đây
      english: Note here
    createdBy: hoantr34
    createdOn: '2023-01-01 10:16:00'
    lastUpdatedBy: ChauPV
    lastUpdatedOn: '2023-01-01 10:16:25'
  - code: '00000010'
    country: Viet Nam
    group: FPT
    company: FPT IS
    taxSettlementGroup: FPT IS HN
    shortName:
      default: FPT IS HN_09/2023
      vietnamese: FPT IS HN_09/2023
      english: FPT IS HN_09/2023
    longName:
      default: FPT IS HN_09/2023
      vietnamese: FPT IS HN_09/2023
      english: FPT IS HN_09/2023
    assessmentPeriodType: Kê khai
    period: Tháng
    assessmentPeriodStartDate: '2023-09-01'
    assessmentPeriodEndDate: '2023-09-30'
    note:
      default: Note here
      vietnamese: Ghi chú ở đây
      english: Note here
    createdBy: hoantr34
    createdOn: '2023-01-01 10:16:00'
    lastUpdatedBy: ChauPV
    lastUpdatedOn: '2023-01-01 10:16:25'
local_buttons: null
layout: layout-table
form_config:
  formTitle:
    create: Add New Tax Period
    view: Tax Period Detail
    edit: Edit Tax Period
  fields:
    - name: code
      label: Tax Period Code
      type: text
      disabled: true
      value: System – Generated
      _condition:
        transform: $.extend.formType != 'view'
    - name: code
      label: Tax Period Code
      type: text
      _condition:
        transform: $.extend.formType = 'view'
    - name: shortName
      label: Short Name
      type: translation
      placeholder: Enter Short Name
      _condition:
        transform: $.extend.formType = 'create' or $.extend.formType = 'edit'
      validators:
        - type: required
        - type: maxLength
          args: 300
          text: Maximum 300 characters
    - name: shortName
      label: Short Name
      type: translation
      _condition:
        transform: $.extend.formType = 'view'
    - name: longName
      label: Long Name
      type: translation
      placeholder: Enter Long Name
      _condition:
        transform: $.extend.formType = 'create' or $.extend.formType = 'edit'
      validators:
        - type: required
        - type: maxLength
          args: 500
          text: Maximum 500 characters
    - name: longName
      label: Long Name
      type: translation
      placeholder: Enter Long Name
      _condition:
        transform: $.extend.formType = 'view'
    - name: assessmentPeriodTypeCode
      label: Tax Period Type
      outputValue: value
      type: select
      placeholder: Select Tax Period Type
      _select:
        transform: $assessmentPeriodTypeCodesList()
      clearFieldsAfterChange:
        - periodCode
        - year
        - assessmentPeriodStartDate
        - assessmentPeriodEndDate
      _condition:
        transform: $not($.extend.formType = 'view')
      validators:
        - type: required
    - name: assessmentPeriodTypeCode
      label: Tax Period Type
      type: select
      placeholder: Select Tax Period Type
      _select:
        transform: $assessmentPeriodTypeCodesList()
      _condition:
        transform: $.extend.formType = 'view'
    - name: periodCode
      label: Period
      outputValue: value
      type: select
      _select:
        transform: $periodCodesList()
      _condition:
        transform: $.extend.formType = 'view'
    - type: dateRange
      label: Year
      name: year
      mode: date-picker
      setting:
        format: yyyy
        type: year
      _condition:
        transform: $.extend.formType = 'view'
    - type: group
      n_cols: 2
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - name: periodCode
          label: Period
          outputValue: value
          type: select
          placeholder: Select Period
          _select:
            transform: >-
              $not($isNilorEmpty($.fields.assessmentPeriodTypeCode))?
              $.fields.assessmentPeriodTypeCode != 'APT_TAX_SETTLEMENT' ?
              $periodCodesList()[value != 'PERIOD_Y1'] : $periodCodesList()
          _value:
            transform: >-
              $.fields.assessmentPeriodTypeCode = 'APT_TAX_SETTLEMENT' ?
              'PERIOD_Y1'
          validators:
            - type: required
          _disabled:
            transform: $.fields.assessmentPeriodTypeCode = 'APT_TAX_SETTLEMENT'
        - type: dateRange
          label: Year
          name: year
          placeholder: Select Year
          mode: date-picker
          setting:
            format: yyyy
            type: year
          validators:
            - type: required
        - name: assessmentPeriodStartDate
          label: Tax Period Start Date
          type: dateRange
          setting:
            format: dd/MM/yyyy
            type: date
          _value:
            transform: >-
              $exists($.fields.year) and $.fields.assessmentPeriodTypeCode =
              'APT_TAX_SETTLEMENT' ? $GetBoundaryDate($.fields.year, 'year',
              'start')
          mode: date-picker
          validators:
            - type: required
        - name: assessmentPeriodEndDate
          label: Tax Period End Date
          type: dateRange
          setting:
            format: dd/MM/yyyy
            type: date
          _value:
            transform: >-
              $exists($.fields.year) and $.fields.assessmentPeriodTypeCode =
              'APT_TAX_SETTLEMENT'? $GetBoundaryDate($.fields.year, 'year',
              'end')
          mode: date-picker
          validators:
            - type: required
    - name: assessmentPeriodStartDate
      label: Tax Period Start Date
      type: dateRange
      setting:
        format: dd/MM/yyyy
        type: date
      mode: date-picker
      _condition:
        transform: $.extend.formType = 'view'
    - name: assessmentPeriodEndDate
      label: Tax Period End Date
      type: dateRange
      setting:
        format: dd/MM/yyyy
        type: date
      mode: date-picker
      _condition:
        transform: $.extend.formType = 'view'
    - type: translationTextArea
      name: note
      label: Note
      placeholder: Enter Note
      validators:
        - type: maxLength
          args: '1000'
          text: Maximum 1000 characters
      textarea:
        autoSize:
          minRows: 4
          maxRows: 5
        maxCharCount: 1000
  sources:
    assessmentPeriodTypeCodesList:
      uri: '"/api/picklists/ASSESSMENTPERIODTYPE/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $string($item.code)}})[]
      disabledCache: true
    periodCodesList:
      uri: '"/api/picklists/PERIOD/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $string($item.code)}})[]
      disabledCache: true
  footer:
    create: true
    update: true
    createdOn: createdAt
    updatedOn: updatedAt
    createdBy: createdBy
    updatedBy: updatedBy
filter_config:
  fields:
    - name: code
      label: Tax Period Code
      type: text
      labelType: type-grid
      placeholder: Enter Tax Period Code
    - name: shortName
      label: Short Name
      type: text
      labelType: type-grid
      placeholder: Enter Short Name
    - name: longName
      label: Long Name
      type: text
      labelType: type-grid
      placeholder: Enter Long Name
    - name: assessmentPeriodType
      label: Tax Period Type
      type: selectAll
      mode: multiple
      labelType: type-grid
      placeholder: Select Tax Period Type
      _options:
        transform: $assessmentPeriodTypeList()
    - name: period
      label: Period
      type: selectAll
      mode: multiple
      labelType: type-grid
      placeholder: Select Period
      _options:
        transform: $periodCodesList()
    - type: dateRange
      label: Year
      placeholder: Select Year
      name: year
      mode: date-picker
      labelType: type-grid
      setting:
        format: yyyy
        type: year
    - name: startDate
      label: Tax Period Start Date
      type: dateRange
      labelType: type-grid
      setting:
        format: dd/MM/yyyy
        type: date
    - name: endDate
      label: Tax Period End Date
      type: dateRange
      labelType: type-grid
      setting:
        format: dd/MM/yyyy
        type: date
    - name: updatedBy
      label: Last Updated By
      type: selectAll
      mode: multiple
      labelType: type-grid
      placeholder: Select Last Updated By
      _options:
        transform: $userList()
    - name: updatedAt
      label: Last Updated On
      type: dateRange
      labelType: type-grid
      setting:
        format: dd/MM/yyyy
        type: date
  filterMapping:
    - field: code
      operator: $cont
      valueField: code
    - field: shortName
      operator: $cont
      valueField: shortName
    - field: longName
      operator: $cont
      valueField: longName
    - field: groupTaxSettlementCode
      operator: $in
      valueField: tax.(value)
    - field: periodCode
      operator: $in
      valueField: period.(value)
    - field: year
      operator: $eq
      valueField: year
    - field: assessmentPeriodTypeCode
      operator: $in
      valueField: assessmentPeriodType.(value)
    - field: assessmentPeriodStartDate
      operator: $between
      valueField: startDate
    - field: assessmentPeriodEndDate
      operator: $between
      valueField: endDate
    - field: updatedBy
      operator: $in
      valueField: updatedBy.(value)
    - field: updatedAt
      operator: $between
      valueField: updatedAt
  sources:
    assessmentPeriodTypeList:
      uri: '"/api/picklists/ASSESSMENTPERIODTYPE/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $string($item.code)}})[]
      disabledCache: true
    periodCodesList:
      uri: '"/api/picklists/PERIOD/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $string($item.code)}})[]
      disabledCache: true
    userList:
      uri: '"/api/internal-users"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.userName, 'value':
        $item.userName}})
      disabledCache: true
layout_options:
  show_dialog_form_save_add_button: true
  show_detail_history: false
  toolTable:
    adjustDisplay: 'true'
  view_history_after_updated: false
  view_history_after_created: false
  historyCloneInactive: true
  tool_table:
    - id: export
  delete_multi_items: true
  is_export_grid: true
  hide_action_row: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: null
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    icon: pencil
    type: ghost-gray
  - id: delete
    icon: trash
    type: ghost-gray
backend_url: api/assessment-period
screen_name: assessment-period
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key: null
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Tax Period
  parent:
    title: General Setting
