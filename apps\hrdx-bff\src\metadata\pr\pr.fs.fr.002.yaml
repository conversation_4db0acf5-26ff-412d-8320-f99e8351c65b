id: PR.FS.FR.002
status: draft
sort: 168
user_created: 56e71b91-dd39-4ebc-afd1-b4f00f38d4a5
date_created: '2024-07-23T12:08:08.442Z'
user_updated: b403d5da-d684-45e4-ae6d-e25223baedc5
date_updated: '2025-07-28T02:38:17.934Z'
title: Packages
requirement:
  time: 1747127248840
  blocks:
    - id: MmoH6mVddQ
      type: paragraph
      data:
        text: >-
          &nbsp;-Cho phép bộ phận nhân sự tập đoàn thêm mới gói thu nhập theo
          quy định ban hành.
    - id: 0oPKSXgSgu
      type: paragraph
      data:
        text: >-

          - Danh mục gói thu nhập sẽ được quản lý tập trung trên Tập đoàn, do bộ
          phận nhân sự của Tập đoàn khai báo và quản lý, c<PERSON><PERSON> đơn vị chỉ được
          quyền khai thác, <PERSON>h<PERSON><PERSON> đư<PERSON><PERSON> quyền điều chỉnh, thay đổi.
    - id: Z4JrpDUp8H
      type: paragraph
      data:
        text: >

          - Bộ phận nhân sự tập đoàn được phép điều chỉnh gói thu nhập theo thực
          tế sử dụng.
    - id: 8fn17KnEtW
      type: paragraph
      data:
        text: >-
          - Hệ thống sẽ hiển thị cảnh báo và không cho lưu đối với các trường
          hợp: thêm mới mã gói thu nhập đã tồn tại, xóa gói thu nhập đang được
          sử dụng.&nbsp;&nbsp;
  version: 2.30.7
screen_design: null
module: PR
local_fields:
  - code: code
    pinned: true
    title: Code
    description: '-Load thông tin mã gói thu nhập. Không cho chỉnh sửa'
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    show_sort: true
  - code: shortName
    pinned: false
    title: Short Name
    description: '-Load thông tin tên gói thu nhập. Không cho chỉnh sửa'
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: name
    pinned: false
    title: Long Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: companyName
    pinned: false
    title: Company
    description: null
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tags
      collection: field_types
    show_sort: true
  - code: effectiveDate
    pinned: false
    title: Effective Date
    description: '-  Load dữ liệu giá trị đã nhập'
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    dragable: false
    show_sort: true
  - code: status
    title: Status
    description: 'Hiển thị account của người tạo của bản ghi được chọn (Ví dụ: PhuongDT104)'
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: Boolean Tag
      collection: field_types
    show_sort: true
  - code: note
    title: Note
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: updatedAt
    title: Last Updated On
    description: >-
      Hiển thị đầy đủ thông tin Ngày/Tháng/Năm, Giờ/Phút/Giây thực hiện cập nhật
      dữ liệu cuối cùng (Ví dụ: 06/05/2024 10:20:53)
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
    show_sort: true
  - code: updatedBy
    title: Last Updated By
    description: >-
      - Hiển thị account của người sửa của bản ghi được chọn (Ví dụ:
      PhuongDT104)
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
mock_data:
  - incomePackageCode: IP001
    incomePackageName: Gói thu nhập cơ bản
    country: Viet Nam
    company: Công ty TNHH ABC
    minValue: 10,000,000 VND
    mediumValue: 15,000,000 VND
    maxValue: 20,000,000 VND
    attachFile: file1.pdf
    status: true
    note: Gói thu nhập cơ bản cho nhân viên
    createTime: 20/04/2024 09:31:00
    creator: Admin1
    lastEditTime: 21/04/2024 10:20:53
    lastEditor: Admin2
  - incomePackageCode: IP002
    incomePackageName: Gói thu nhập nâng cao
    country: Japan
    company: Công ty XYZ
    minValue: 12,000,000 JPY
    mediumValue: 18,000,000 JPY
    maxValue: 25,000,000 JPY
    attachFile: file2.pdf
    status: false
    note: Gói thu nhập nâng cao cho quản lý
    createTime: 21/04/2024 11:45:00
    creator: Admin2
    lastEditTime: 22/04/2024 14:22:10
    lastEditor: Admin3
  - incomePackageCode: IP003
    incomePackageName: Gói thu nhập chuyên gia
    country: China
    company: Công ty DEF
    minValue: 20,000,000 CNY
    mediumValue: 30,000,000 CNY
    maxValue: 40,000,000 CNY
    attachFile: file3.pdf
    status: true
    note: Gói thu nhập cho chuyên gia cao cấp
    createTime: 22/04/2024 12:00:00
    creator: Admin3
    lastEditTime: 23/04/2024 15:30:45
    lastEditor: Admin4
  - incomePackageCode: IP004
    incomePackageName: Gói thu nhập hợp tác
    country: Viet Nam
    company: Công ty GHI
    minValue: 8,000,000 VND
    mediumValue: 12,000,000 VND
    maxValue: 16,000,000 VND
    attachFile: file4.pdf
    status: true
    note: Gói thu nhập cho các hợp tác viên
    createTime: 23/04/2024 13:20:00
    creator: Admin4
    lastEditTime: 24/04/2024 16:00:00
    lastEditor: Admin5
  - incomePackageCode: IP005
    incomePackageName: Gói thu nhập quản lý cấp cao
    country: Japan
    company: Công ty JKL
    minValue: 25,000,000 JPY
    mediumValue: 35,000,000 JPY
    maxValue: 50,000,000 JPY
    attachFile: file5.pdf
    status: false
    note: Gói thu nhập cho quản lý cấp cao
    createTime: 24/04/2024 14:00:00
    creator: Admin5
    lastEditTime: 25/04/2024 17:15:30
    lastEditor: Admin6
local_buttons: null
layout: layout-table
form_config:
  formTitle:
    edit: Edit Package
    create: Add New Package
    view: View detail Package
  historyHeaderTitle: '''View detail Package'''
  defaultValidateError: Required fields have not been filled in completely (fields marked with *)
  footer:
    update: true
    updatedOn: updatedAt
    updatedBy: updatedBy
  effectiveDateValidations:
    - sameDate
    - greaterThanLastest
  fields:
    - type: group
      n_cols: 2
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - type: text
          label: Code
          name: code
          placeholder: Enter Code
          formatType: code
          validators:
            - type: required
            - type: pattern
              args: ^[a-zA-Z0-9_.-]*$
              text: >-
                Code must only contain letters, numbers, hyphens, underscores,
                or dots.
            - type: maxLength
              args: '50'
              text: Code should not exceed 50 characters
          _disabled:
            transform: $.extend.formType = 'edit' or $.extend.formType = 'proceed' ? true
          col: 1
        - type: translation
          label: Short Name
          placeholder: Enter Short Name
          name: shortName
          validators:
            - type: required
            - type: maxLength
              args: '300'
              text: Short Name should not exceed 300 characters
          col: 1
    - type: text
      label: Code
      name: code
      _condition:
        transform: $.extend.formType = 'view'
    - type: translation
      label: Short Name
      placeholder: Input Short Name
      name: shortName
      _condition:
        transform: $.extend.formType = 'view'
    - type: translation
      label: Long Name
      placeholder: Enter Long Name
      name: name
      validators:
        - type: required
        - type: maxLength
          args: '500'
          text: Long Name should not exceed 500 characters
    - type: selectAll
      label: Company
      placeholder: Select Company
      isLazyLoad: true
      name: companyObj
      mode: multiple
      outputValue: value
      _options:
        transform: >-
          $companiesList($.extend.limit, $.extend.page, $.extend.search,
          $.fields.effectiveDate)
    - type: group
      n_cols: 2
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - type: dateRange
          label: Effective Date
          name: effectiveDate
          mode: date-picker
          _disabled:
            transform: $.extend.formType= 'proceed' ? true
          _value:
            transform: $.extend.formType='create' ? $now()
          placeholder: Select effective date
          setting:
            format: dd/MM/yyyy
            type: date
          validators:
            - type: required
        - type: radio
          label: Status
          name: status
          _value:
            transform: $.extend.formType = 'create' ? true
          radio:
            - label: Active
              value: true
            - label: Inactive
              value: false
    - type: dateRange
      label: Effective Date
      name: effectiveDateFrom
      mode: date-picker
      setting:
        type: day
        format: dd/MM/yyyy
      _condition:
        transform: $.extend.formType = 'view'
    - type: radio
      label: Status
      name: status
      radio:
        - label: Active
          value: true
        - label: Inactive
          value: false
      _condition:
        transform: $.extend.formType = 'view'
    - type: translationTextArea
      label: Note
      name: note
      placeholder: Enter Note
      textarea:
        autoSize:
          minRows: 3
        maxCharCount: 1000
      validators:
        - type: maxLength
          args: 1000
          text: note should not exceed 1000 characters
  sources:
    companiesList:
      uri: '"/api/companies/get-by"'
      method: GET
      queryTransform: >-
        { 'limit': $.limit, 'page': $.page, 'search': $.search, 'filter':
        [{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator': '$eq','value':
        $.effectiveDate},{'field':'code','operator': '$in','value': $.code}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'code' :$item.code}}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
        - code
filter_config:
  fields:
    - type: text
      label: Code
      name: code
      placeholder: Enter Code
      labelType: type-grid
    - type: text
      label: Short Name
      name: shortName
      placeholder: Enter Short Name
      labelType: type-grid
    - type: text
      label: Long Name
      name: name
      placeholder: Enter Long Name
      labelType: type-grid
    - name: CompanyCodes
      label: Company
      type: selectAll
      labelType: type-grid
      mode: multiple
      placeholder: Select Company
      isLazyLoad: true
      _options:
        transform: $companiesList($.extend.limit, $.extend.page, $.extend.search)
    - type: dateRange
      label: Effective Date
      name: effectiveDateFrom
      setting:
        format: dd/MM/yyyy
      labelType: type-grid
    - name: status
      labelType: type-grid
      label: Status
      type: radio
      value: ''
      radio:
        - label: All
          value: ''
        - label: Active
          value: true
        - label: Inactive
          value: false
    - name: updatedBy
      label: Last Updated By
      type: text
      labelType: type-grid
      placeholder: Enter Last Updated By
    - name: updatedAt
      label: Last Updated On
      type: dateRange
      labelType: type-grid
      setting:
        mode: date
        format: dd/MM/yyyy
  filterMapping:
    - field: code
      operator: $cont
      valueField: code
    - field: shortName
      operator: $cont
      valueField: shortName
    - field: name
      operator: $cont
      valueField: name
    - field: CompanyCodes
      operator: $eq
      valueField: CompanyCodes.(value)
    - field: effectiveDateFrom
      operator: $between
      valueField: effectiveDateFrom
    - field: status
      operator: $eq
      valueField: status
    - field: updatedBy
      operator: $cont
      valueField: updatedBy
    - field: updatedAt
      operator: $between
      valueField: updatedAt
  sources:
    incomePackages:
      uri: '"/api/income-packages"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.code, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    userList:
      uri: '"/api/internal-users"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.userName, 'value':
        $item.userName}})
      disabledCache: true
    companiesList:
      uri: '"/api/companies"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
layout_options:
  delete_multi_items: true
  custom_history_backend_url: /api/income-packages/:id/clone
  apply_delete_multi_items_to_delete_one: true
  custom_delete_body: '{''ids'': $map($.data,function($item){$item.id})[]}'
  reset_page_index_after_do_action:
    edit: true
  show_detail_history: true
  toolTable:
    export: true
    adjustDisplay: true
  history_widget_header_options:
    duplicate: false
  tool_table:
    - id: export
      icon: icon-download-simple
  show_dialog_form_save_add_button: true
  show_filter_results_message: true
  hide_action_row: true
  store_selected_items: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    title: Edit
    icon: icon-pencil-simple
    type: ghost-gray
  - id: delete
    title: Delete
    icon: icon-trash
    type: ghost-gray
backend_url: api/income-packages
screen_name: null
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: companyCode
    defaultName: CompanyCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Income package List
  parent: null
