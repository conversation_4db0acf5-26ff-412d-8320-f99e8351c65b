id: PR.FS.FR.005
status: draft
sort: 275
user_created: 56e71b91-dd39-4ebc-afd1-b4f00f38d4a5
date_created: '2024-07-26T06:31:14.558Z'
user_updated: b403d5da-d684-45e4-ae6d-e25223baedc5
date_updated: '2025-07-16T09:37:40.048Z'
title: Parameter
requirement:
  time: 1748502191544
  blocks:
    - id: AiIfVasMzq
      type: paragraph
      data:
        text: update later
  version: 2.30.7
screen_design: null
module: PR
local_fields:
  - code: code
    pinned: true
    title: Parameter Code
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    show_sort: true
  - code: shortName
    pinned: false
    title: Short Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: longName
    pinned: false
    title: Long Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: country<PERSON><PERSON>
    pinned: false
    title: Country
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: companyNames
    pinned: false
    title: Company
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tags
      collection: field_types
    show_sort: true
  - code: typeName
    pinned: false
    title: Type
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: currency
    pinned: false
    title: Currency
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: effectiveDate
    title: Effective Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    show_sort: true
  - code: status
    pinned: false
    title: Status
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: Boolean Tag
      collection: field_types
    show_sort: true
  - code: note
    pinned: false
    title: Note
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: updatedBy
    pinned: false
    title: Last Updated By
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: updatedAt
    pinned: false
    title: Last Updated On
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
    show_sort: true
mock_data:
  - parameterCode: '00000001'
    effectiveDate: 05/07/2024
    shortName:
      default: Tiền cơm miễn thuế
      vietnamese: Tiền cơm miễn thuế vn
      english: Tiền cơm miễn thuế en
    longName:
      default: '% tăng ca miễn thuế'
      vietnamese: '% tăng ca miễn thuế vn'
      english: '% tăng ca miễn thuế en'
    countryName: Việt Nam
    companyName: FIS
    typeName: Amount
    typeValue: amount
    currency: VNĐ
    activeAt: 8/07/2024
    status: Active
    note:
      default: '% tăng ca miễn thuế df'
      vietnamese: '% tăng ca miễn thuế note vn'
      english: '% tăng ca miễn thuế note en'
    createdBy: ManNM7
    updatedBy: ManNM7
    createdAt: 2/07/2024
    updatedAt: 4/07/2024
  - parameterCode: '00000002'
    effectiveDate: 04/07/2024
    shortName:
      default: VN 0000002
      vietnamese: VN 0000002
      english: EN 0000002
    longName:
      default: '0000002'
      vietnamese: VN 0000002
      english: EN 0000002
    countryName: Việt Nam
    companyName: FIS
    typeName: Percent
    typeValue: percent
    currency: VNĐ
    activeAt: 4/07/2024
    status: Active
    note:
      default: '% tăng ca miễn thuế df'
      vietnamese: '% tăng ca miễn thuế note vn'
      english: '% tăng ca miễn thuế note en'
    createdBy: ManNM7
    updatedBy: ManNM7
    createdAt: 7/07/2024
    updatedAt: 2/27/2024
  - parameterCode: '00000003'
    effectiveDate: 01/07/2024
    shortName:
      default: Tiền cơm miễn thuế
      vietnamese: Tiền cơm miễn thuế
      english: Tiền cơm miễn thuế
    longName:
      default: '% tăng ca miễn thuế'
      vietnamese: '% tăng ca miễn thuế'
      english: '% tăng ca miễn thuế'
    countryName: Việt Nam
    companyName: FIS
    typeName: Percent
    typeValue: percent
    currency: VNĐ
    activeAt: 4/07/2024
    status: Active
    note:
      default: '% tăng ca miễn thuế df'
      vietnamese: '% tăng ca miễn thuế note vn'
      english: '% tăng ca miễn thuế note en'
    createdBy: ManNM7
    updatedBy: ManNM7
    createdAt: 5/07/2024
    updatedAt: 2/07/2024
local_buttons: null
layout: layout-table
form_config:
  effectiveDateValidations:
    - sameDate
    - greaterThanLastest
  defaultValidateError: Required fields have not been filled in completely (fields marked with *)
  formTitle:
    create: 'Add new: Parameter'
    edit: Edit Parameter
    view: View History Parameter
  fields:
    - type: group
      n_cols: 2
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - type: select
          label: Country
          placeholder: Select Country
          name: countryObj
          outputValue: value
          isLazyLoad: true
          _select:
            transform: $nationsList($.extend.limit, $.extend.page, $.extend.search)
        - type: selectAll
          label: Company
          placeholder: Select Company
          isLazyLoad: true
          name: companyObjs
          mode: multiple
          outputValue: value
          _validateFn:
            transform: >-
              ($listValue := $map($.value ,function($item) {$item.value ?
              $item.value : $item})[] ;
              $listAll:=$companyList($.fields.effectiveDate,$listValue);$count($listValue)
              > 0 ?  ( $count($map($listAll,function($item) {
              $filter($listValue, function($itemChild) {$itemChild = $item.value
              } )[0]} )[]) > 0 ? $map($listAll,function($item) {
              $filter($listValue, function($itemChild) {$itemChild = $item.value
              } )[0]} )[] : '_setSelectValueNull' )    )
          _options:
            transform: $companyList($.fields.effectiveDate)
    - type: text
      label: Country
      placeholder: Select Country
      _condition:
        transform: $.extend.formType = 'view'
      name: countryName
    - type: selectAll
      label: Company
      placeholder: Select Company
      name: companyObjs
      outputValue: value
      isLazyLoad: true
      _condition:
        transform: $.extend.formType = 'view'
      _options:
        transform: $companyList()
    - type: text
      label: Parameter Code
      name: code
      placeholder: Automatic Generation
      _disabled:
        transform: 'true'
      validators:
        - type: required
    - type: translation
      label: Short Name
      name: shortName
      placeholder: Enter Short Name
      validators:
        - type: required
        - type: maxLength
          args: '300'
          text: Short Name should not exceed 300 characters
    - type: translation
      label: Long Name
      name: longName
      placeholder: Enter Long Name
      validators:
        - type: required
        - type: maxLength
          args: '500'
          text: Long Name should not exceed 500 characters
    - type: group
      n_cols: 2
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - type: select
          label: Type
          placeholder: Select Type
          name: typeCode
          outputValue: value
          validators:
            - type: required
          _select:
            transform: $wageClassificationsList($.fields.effectiveDate)
        - type: select
          label: Currency
          placeholder: Select Currency
          name: currencyObj
          isLazyLoad: true
          outputValue: value
          _condition:
            transform: $.fields.typeCode = 'WGCSFT_00001'
          _select:
            transform: $currenciesList($.extend.limit, $.extend.page, $.extend.search)
    - type: text
      label: Type
      name: typeName
      _condition:
        transform: $.extend.formType = 'view'
    - type: text
      label: Currency
      name: currency
      _condition:
        transform: >-
          $.extend.formType = 'view' and $.extend.defaultValue.typeCode =
          'WGCSFT_00001'
    - type: group
      n_cols: 2
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - type: dateRange
          label: Effective Date
          name: effectiveDate
          mode: date-picker
          placeholder: dd/MM/yyyy
          _disabled:
            transform: $.extend.formType = 'proceed' or $.extend.formType = 'edit'
          _value:
            transform: $.extend.formType = 'create' ? $now()
          setting:
            type: day
            format: dd/MM/yyyy
          validators:
            - type: required
        - type: radio
          label: Status
          name: status
          value: true
          validators:
            - type: required
          radio:
            - value: true
              label: Active
            - value: false
              label: Inactive
    - type: dateRange
      label: Effective Date
      name: effectiveDate
      mode: date-picker
      _disabled:
        transform: $.extend.formType = 'edit' or $.extend.formType = 'proceed'
      _condition:
        transform: $.extend.formType = 'view'
      _value:
        transform: $.extend.formType = 'create' ? $now()
      setting:
        type: day
        format: dd/MM/yyyy
    - type: radio
      label: Status
      _condition:
        transform: $.extend.formType = 'view'
      name: status
      value: true
      radio:
        - value: true
          label: Active
        - value: false
          label: Inactive
    - type: translationTextArea
      label: Note
      placeholder: Enter Note
      name: note
      validators:
        - type: maxLength
          args: '1000'
          text: Note should not exceed 1000 characters
      textarea:
        autoSize:
          minRows: 3
          maxRows: 5
        maxCharCount: 1000
  sources:
    companyList:
      uri: '"/api/companies/get-by"'
      method: GET
      queryTransform: >-
        {'limit': 9999, 'filter': [{'field':'code','operator': '$in','value':
        $.code},{'field':'effectiveDate','operator': '$eq','value':
        $.effectiveDate},{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code }})[]
      disabledCache: true
      params:
        - effectiveDate
        - code
    nationsList:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': {'id': $item.id , 'code': $item.code} }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    wageClassificationsList:
      uri: '"/api/picklists/WAGECLASSIFICATION/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
    currenciesList:
      uri: '"/api/picklists/CURRENCY/values/pagination"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': {'code': $item.code} }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
filter_config:
  fields:
    - name: code
      labelType: type-grid
      label: Parameter Code
      type: text
      placeholder: Enter Parameter Code
    - type: text
      labelType: type-grid
      name: shortName
      placeholder: Enter Short Name
      label: Short Name
    - type: text
      labelType: type-grid
      name: longName
      placeholder: Enter Long Name
      label: Long Name
    - name: countryCode
      label: Country
      type: selectAll
      mode: multiple
      labelType: type-grid
      placeholder: Select Country
      isLazyLoad: true
      _options:
        transform: $nationsList($.extend.limit, $.extend.page, $.extend.search)
    - name: companyCode
      label: Company
      type: selectAll
      labelType: type-grid
      mode: multiple
      placeholder: Select Company
      isLazyLoad: true
      _options:
        transform: $companiesList($.extend.limit, $.extend.page, $.extend.search)
    - name: typeCode
      label: Type
      type: selectAll
      labelType: type-grid
      isLazyLoad: true
      mode: multiple
      placeholder: Select Type
      _options:
        transform: >-
          $wageClassificationsList($.extend.limit, $.extend.page,
          $.extend.search)
    - name: currencyCode
      label: Currency
      labelType: type-grid
      isLazyLoad: true
      type: selectAll
      mode: multiple
      placeholder: Select Type
      _options:
        transform: $currenciesList($.extend.limit, $.extend.page, $.extend.search)
    - type: dateRange
      label: Effective Date
      name: effectiveDate
      labelType: type-grid
      setting:
        type: day
        format: dd/MM/yyyy
    - name: status
      labelType: type-grid
      label: Status
      type: radio
      value: null
      radio:
        - label: All
          value: null
        - value: true
          label: Active
        - value: false
          label: Inactive
    - name: updatedBy
      label: Last Updated By
      type: text
      labelType: type-grid
      placeholder: Enter Last Updated By
    - name: updatedAt
      label: Last Updated On
      type: dateRange
      labelType: type-grid
      setting:
        mode: date
        format: dd/MM/yyyy
  filterMapping:
    - field: code
      operator: $cont
      valueField: code
    - field: shortName
      operator: $cont
      valueField: shortName
    - field: longName
      operator: $cont
      valueField: longName
    - field: countryCode
      operator: $in
      valueField: countryCode.(value)
    - field: CompanyCodesFilter
      operator: $eq
      valueField: companyCode.(value)
    - field: typeCode
      operator: $in
      valueField: typeCode.(value)
    - field: currencyCode
      operator: $in
      valueField: currencyCode.(value)
    - field: effectiveDate
      operator: $between
      valueField: effectiveDate
    - field: status
      operator: $eq
      valueField: status
    - field: note
      operator: $cont
      valueField: note
    - field: updatedBy
      operator: $cont
      valueField: updatedBy
    - field: updatedAt
      operator: $between
      valueField: updatedAt
  sources:
    userList:
      uri: '"/api/internal-users"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.userName, 'value':
        $item.userName}})[]
      disabledCache: true
    currenciesList:
      uri: '"/api/picklists/CURRENCY/values/pagination"'
      method: GET
      queryTransform: '{''limit'': $.limit, ''page'': $.page, ''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    companiesList:
      uri: '"/api/companies"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    nationsList:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    salaryStandardList:
      uri: '"/api/salary-standards"'
      method: GET
      queryTransform: '{''limit'': $.limit, ''page'': $.page, ''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    wageClassificationsList:
      uri: '"/api/picklists/WAGECLASSIFICATION/values/pagination"'
      method: GET
      queryTransform: '{''limit'': $.limit, ''page'': $.page, ''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
layout_options:
  show_dialog_form_save_add_button: true
  delete_multi_items: true
  apply_delete_multi_items_to_delete_one: true
  reset_page_index_after_do_action:
    edit: true
  history_widget_header_options:
    duplicate: false
  show_detail_history: true
  custom_delete_body: '{''ids'': $map($.data,function($item){$item.id})[]}'
  custom_history_backend_url: /api/salary-standards/:id/clone
  tool_table:
    - id: export
  show_filter_results_message: true
  store_selected_items: true
  hide_action_row: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    icon: edit
    type: tertiary
    title: Edit
  - id: delete
    icon: trash
    type: tertiary
    title: Delete
backend_url: /api/salary-standards
screen_name: parameter-list
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: companyCodes
    defaultName: CompanyCode
  - name: countryCode
    defaultName: CountryCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Parameter
  parent:
    title: PR Picklist
