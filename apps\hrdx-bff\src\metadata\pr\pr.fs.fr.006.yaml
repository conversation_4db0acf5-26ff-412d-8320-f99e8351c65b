id: PR.FS.FR.006
status: draft
sort: 133
user_created: 56e71b91-dd39-4ebc-afd1-b4f00f38d4a5
date_created: '2024-07-26T06:27:45.947Z'
user_updated: abd14398-1d53-4e6f-9834-26328f0ebde0
date_updated: '2025-06-19T09:25:19.600Z'
title: Deduction
requirement:
  time: 1745291014982
  blocks:
    - id: hMszgxizg9
      type: paragraph
      data:
        text: Quản lý danh mục các khoản khấu trừ
  version: 2.30.7
screen_design: null
module: PR
local_fields:
  - code: code
    pinned: true
    title: Deduction Code
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    options__tabular__column_width: null
    show_sort: true
  - code: shortName
    pinned: false
    title: Short Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    options__tabular__column_width: null
    show_sort: true
  - code: longName
    pinned: false
    title: Long Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: countryName
    pinned: false
    title: Country
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: null
    show_sort: true
  - code: currencyName
    pinned: false
    title: Currency
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: null
    show_sort: true
  - code: entryTypeName
    pinned: false
    title: Entry Type
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: null
    show_sort: true
  - code: deductTaxableIncome
    pinned: false
    title: Deducted From Taxable Income
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: YesNo
      collection: field_types
    options__tabular__column_width: null
    show_sort: true
  - code: effectiveDate
    title: Effective Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
  - code: status
    pinned: false
    title: Status
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: Boolean Tag
      collection: field_types
    options__tabular__column_width: null
    show_sort: true
  - code: note
    pinned: false
    title: Note
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: updatedBy
    pinned: false
    title: Last Updated By
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: null
    show_sort: true
  - code: updatedAt
    pinned: false
    title: Last Updated On
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
    show_sort: true
mock_data:
  - code: '00000001'
    shortName:
      default: DD_ABS_NCD0
      english: DD_ABS_NCD0
      vietnamese: DD_ABS_NCD0
    longName:
      default: Adjustment to monthly salary deduction
      english: Adjustment to monthly salary deduction
      vietnamese: Điều chỉnh trừ lương tháng
    country: Vietnam
    currency: VND
    entryType: Pre-tax
    deductedFromTaxableIncome: true
    effectiveDate: 01/08/2024
    status: true
    note: Ghi chú cho khoản khấu trừ A
    createTime: 01/07/2024 08:00:00
    creator: User1
    latestEditor: User2
    latestEditedTime: 02/07/2024 09:00:00
  - code: '00000002'
    shortName:
      default: DD_ABS_NCD0
      english: DD_ABS_NCD0
      vietnamese: DD_ABS_NCD0
    longName:
      default: Adjustment to monthly salary deduction
      english: Adjustment to monthly salary deduction
      vietnamese: Điều chỉnh trừ lương tháng
    country: Vietnam
    currency: VND
    entryType: Pre-tax
    deductedFromTaxableIncome: false
    effectiveDate: 02/08/2024
    status: true
    note: Ghi chú cho khoản khấu trừ B
    createTime: 02/07/2024 10:00:00
    creator: User2
    latestEditor: User3
    latestEditedTime: 03/07/2024 11:00:00
  - code: '00000003'
    shortName:
      default: DD_ABS_NCD0
      english: DD_ABS_NCD0
      vietnamese: DD_ABS_NCD0
    longName:
      default: Adjustment to monthly salary deduction
      english: Adjustment to monthly salary deduction
      vietnamese: Điều chỉnh trừ lương tháng
    country: Vietnam
    currency: VND
    entryType: Post-tax
    deductedFromTaxableIncome: true
    effectiveDate: 03/08/2024
    status: true
    note: Ghi chú cho khoản khấu trừ C
    createTime: 03/07/2024 12:00:00
    creator: User3
    latestEditor: User1
    latestEditedTime: 04/07/2024 13:00:00
local_buttons: null
layout: layout-table
form_config:
  effectiveDateValidations:
    - sameDate
    - greaterThanLastest
  defaultValidateError: Required fields have not been filled in completely (fields marked with *)
  fields:
    - type: group
      _condition:
        transform: $not($.extend.formType = 'view')
      n_cols: 2
      fields:
        - type: text
          label: Deduction Code
          name: code
          placeholder: Enter Deduction Code
          formatType: code
          validators:
            - type: required
            - type: pattern
              args: ^[a-zA-Z0-9_.-]*$
              text: >-
                Code must only contain letters, numbers, hyphens, underscores,
                or dots.
            - type: maxLength
              args: '50'
              text: Deduction Code should not exceed 50 characters
          _disabled:
            transform: $.extend.formType = 'edit'
        - type: translation
          label: Short Name
          placeholder: Enter Short Name
          name: shortName
          validators:
            - type: required
            - type: maxLength
              args: '300'
              text: Short Name should not exceed 300 characters
        - type: translation
          label: Long Name
          placeholder: Enter Long Name
          name: longName
          validators:
            - type: required
            - type: maxLength
              args: '500'
              text: Long Name should not exceed 500 characters
          col: 2
        - type: select
          label: Country
          name: countryObj
          clearFieldsAfterChange:
            - currency
          placeholder: Select Country
          outputValue: value
          isLazyLoad: true
          _select:
            transform: $countriesList($.extend.limit, $.extend.page, $.extend.search)
        - type: select
          label: Currency
          name: currency
          placeholder: Select Currency
          outputValue: value
          _value:
            skipWhenClear: true
            transform: >-
              $isNilorEmpty($.fields.currency) ?
              $filter($.variables._currencies,function($v){$v.linkCatalogDataCode=$.fields.countryObj.code
              } )[0].value
          _select:
            transform: $currencies()
        - type: radio
          label: Entry Type
          name: entryType
          placeholder: Select Entry Type
          outputValue: value
          _radio:
            transform: $.variables._entryTypeList
          _value:
            transform: $.extend.formType = 'create' ? $.variables._entryTypeList[0].value
        - type: radio
          label: Deducted From Taxable Income
          name: deductTaxableIncome
          _value:
            transform: $.extend.formType = 'create' ? true
          _condition:
            transform: $.fields.entryType = 'ENTRYTP_00001'
          radio:
            - label: 'Yes'
              value: true
            - label: 'No'
              value: false
        - type: dateRange
          label: Effective Date
          placeholder: dd/mm/yyyy
          name: effectiveDate
          mode: date-picker
          _value:
            transform: $.extend.formType = 'create' ? $now()
          _condition:
            transform: $not($.extend.formType = 'view')
          _disabled:
            transform: $.extend.formType = 'proceed'
          setting:
            type: day
            format: dd/MM/yyyy
          validators:
            - type: required
        - type: radio
          label: Status
          name: status
          _value:
            transform: $.extend.formType = 'create' ? true
          radio:
            - label: Active
              value: true
            - label: Inactive
              value: false
        - type: translationTextArea
          label: Note
          name: note
          placeholder: Enter Note
          textarea:
            autoSize:
              minRows: 3
            maxCharCount: '1000'
          col: 2
    - type: text
      label: Deduction Code
      name: code
      placeholder: Enter Deduction Code
      _condition:
        transform: $.extend.formType='view'
    - type: translation
      label: Short Name
      placeholder: Enter Short Name
      name: shortName
      _condition:
        transform: $.extend.formType = 'view'
    - type: translation
      label: Long Name
      placeholder: Enter Long Name
      name: longName
      _condition:
        transform: $.extend.formType = 'view'
    - type: text
      label: Country
      name: countryName
      _condition:
        transform: $.extend.formType = 'view'
    - type: text
      label: Currency
      name: currencyName
      _condition:
        transform: $.extend.formType = 'view'
    - type: radio
      label: Entry Type
      name: entryType
      placeholder: Select Entry Type
      outputValue: value
      _condition:
        transform: $.extend.formType = 'view'
      _radio:
        transform: $.variables._entryTypeList
      _value:
        transform: $.extend.formType = 'create' ? $.variables._entryTypeList[0].value
    - type: radio
      label: Deducted From Taxable Income
      name: deductTaxableIncome
      _value:
        transform: $.extend.formType = 'create' ? true
      _condition:
        transform: $.extend.formType = 'view' and $.fields.entryType = 'ENTRYTP_00001'
      radio:
        - label: 'Yes'
          value: true
        - label: 'No'
          value: false
    - type: dateRange
      label: Effective Date
      placeholder: dd/mm/yyyy
      name: effectiveDate
      mode: date-picker
      _condition:
        transform: $.extend.formType = 'view'
      setting:
        type: day
        format: dd/MM/yyyy
    - type: radio
      label: Status
      name: status
      _condition:
        transform: $.extend.formType = 'view'
      _value:
        transform: $.extend.formType = 'create' ? true
      radio:
        - label: Active
          value: true
        - label: Inactive
          value: false
    - type: translationTextArea
      label: Note
      name: note
      placeholder: Enter Note
      _condition:
        transform: $.extend.formType = 'view'
      textarea:
        autoSize:
          minRows: 3
        maxCharCount: '1000'
  sources:
    countriesList:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': {'id': $item.id , 'code': $item.code} }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    currencies:
      uri: '"/api/picklists/CURRENCY/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value':
        $item.code,'linkCatalogDataCode':$item.codE501}})[]
      disabledCache: true
    entryTypeList:
      uri: '"/api/picklists/ENTRYTYPE/values"'
      method: GET
      queryTransform: '{''sort'':[{''field'' : ''name''  , ''order'': ''DESC''  }] }'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
  variables:
    _currencies:
      transform: $currencies()
    _entryTypeList:
      transform: $entryTypeList()
  footer:
    create: true
    update: true
    createdOn: createdAt
    updatedOn: updatedAt
    createdBy: createdBy
    updatedBy: updatedBy
filter_config:
  fields:
    - name: code
      label: Deduction Code
      type: text
      labelType: type-grid
      placeholder: Enter Deduction Code
    - type: selectAll
      label: Country
      name: country
      placeholder: Select Country
      mode: multiple
      labelType: type-grid
      isLazyLoad: true
      _options:
        transform: $countriesList($.extend.limit, $.extend.page, $.extend.search)
    - type: text
      label: Short Name
      labelType: type-grid
      name: shortName
    - type: text
      labelType: type-grid
      label: Long Name
      name: longName
    - type: selectAll
      label: Currency
      name: currency
      labelType: type-grid
      mode: multiple
      isLazyLoad: true
      placeholder: Select Currency
      _options:
        transform: $currencies($.extend.limit, $.extend.page, $.extend.search)
    - type: dateRange
      label: ' Effective Date'
      name: effectiveDate
      labelType: type-grid
      setting:
        format: dd/MM/yyyy
        type: date
    - type: selectAll
      label: Entry Type
      name: entryType
      labelType: type-grid
      mode: multiple
      isLazyLoad: true
      placeholder: Select Entry Type
      _options:
        transform: $entryTypeList($.extend.limit, $.extend.page, $.extend.search)
    - type: radio
      label: Deducted From Taxable Income
      labelType: type-grid
      value: null
      name: deductTaxableIncome
      radio:
        - label: All
          value: null
        - label: 'Yes'
          value: true
        - label: 'No'
          value: false
    - name: status
      labelType: type-grid
      label: Status
      value: null
      type: radio
      radio:
        - label: All
          value: null
        - label: Active
          value: true
        - label: Inactive
          value: false
    - name: updatedBy
      label: Last Updated By
      type: text
      labelType: type-grid
      placeholder: Enter Last Updated By
    - name: updatedAt
      label: Last Updated On
      type: dateRange
      labelType: type-grid
      setting:
        mode: date
        format: dd/MM/yyyy
  filterMapping:
    - field: code
      operator: $cont
      valueField: code
    - field: effectiveDate
      operator: $between
      valueField: effectiveDate
    - field: countryCode
      operator: $in
      valueField: country.(value)
    - field: currency
      operator: $in
      valueField: currency.(value)
    - field: entryType
      operator: $in
      valueField: entryType.(value)
    - field: shortName
      operator: $cont
      valueField: shortName
    - field: longName
      operator: $cont
      valueField: longName
    - field: deductTaxableIncome
      operator: $eq
      valueField: deductTaxableIncome
    - field: status
      operator: $eq
      valueField: status
    - field: updatedBy
      operator: $cont
      valueField: updatedBy
    - field: updatedAt
      operator: $between
      valueField: updatedAt
  sources:
    countriesList:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    entryTypeList:
      uri: '"/api/picklists/ENTRYTYPE/values/pagination"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'sort':[{'field' :
        'name'  , 'order': 'DESC'  }] }
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    deductionCodeList:
      uri: '"/api/deductions"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.shortName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    currencies:
      uri: '"/api/picklists/CURRENCY/values/pagination"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
layout_options:
  custom_history_backend_url: /api/deductions/:id/clone
  delete_multi_items: true
  history_widget_header_options:
    duplicate: false
  show_dialog_form_save_add_button: true
  apply_delete_multi_items_to_delete_one: true
  custom_delete_body: '{''ids'': $map($.data,function($item){$item.id})[]}'
  reset_page_index_after_do_action:
    edit: true
  tool_table:
    - id: export
      icon: icon-download-simple
  show_filter_results_message: true
  hide_action_row: true
  store_selected_items: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    title: edit
    icon: edit
    type: tertiary
  - id: delete
    title: Delete
    icon: trash
    type: tertiary
backend_url: /api/deductions
screen_name: deduction-list
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: countryCode
    defaultName: CountryCode
screen_designs: []
function_specs: []
fields:
  - 78
  - 79
  - 80
  - 81
  - 82
  - 83
  - 84
children: []
menu_item:
  title: Deduction
  parent:
    title: PR Picklist
