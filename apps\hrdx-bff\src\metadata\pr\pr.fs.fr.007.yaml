id: PR.FS.FR.007
status: draft
sort: 270
user_created: 56e71b91-dd39-4ebc-afd1-b4f00f38d4a5
date_created: '2024-07-26T06:26:14.040Z'
user_updated: b403d5da-d684-45e4-ae6d-e25223baedc5
date_updated: '2025-07-06T05:23:33.198Z'
title: Allowance
requirement:
  time: 1747125689523
  blocks:
    - id: Q1xkbUvwV2
      type: paragraph
      data:
        text: >
          - Cho phép bộ phận nhân sự tập đoàn thêm mới đối với các khoản hỗ trợ
          cố định theo yêu cầu đề xuất từ các CTTV.
    - id: xeP3Z5VcO1
      type: paragraph
      data:
        text: >
          - Danh mục các khoản hỗ trợ cố định được quản lý tập trung trên Tậ<PERSON>
          đ<PERSON>, do bộ phận nhân sự của Tập đoàn khai báo và quản lý, c<PERSON>c đơn vị
          chỉ được quyền khai thác, khô<PERSON> được quyền điều chỉnh, thay đổi.
    - id: usqNUPk_NA
      type: paragraph
      data:
        text: >
          - Khi có thay đổi bộ phận nhân sự tập đoàn có thể chỉnh sửa/xóa danh
          mục khoản hỗ trợ cố định.
    - id: vt-vJq9-JE
      type: paragraph
      data:
        text: >+
          - Hệ thống cho phép phân loại đối với từng khoản hỗ trợ cố định: đơn
          vị tính/cách tính/tính thuế/cộng vào lương để tự động xử lý tính toán
          ra số mức hưởng tương ứng.

          - Hệ thống sẽ hiển thị cảnh báo và không cho lưu đối với các trường
          hợp: thêm mới mã khoản hỗ trợ cố định đã tồn tại, xóa khoản hỗ trợ cố
          định đang được sử dụng.

  version: 2.30.7
screen_design: null
module: PR
local_fields:
  - code: code
    pinned: true
    title: Allowance Code
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    show_sort: true
  - code: shortName
    title: Short Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: longName
    title: Long Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    options__tabular__column_width: null
    show_sort: true
  - code: countryName
    title: Country
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: frequencyName
    title: Frequency
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: calculationMethodName
    title: Calculation Method
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: null
    show_sort: true
  - code: currencyName
    title: Currency
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: entryTypeName
    title: Entry Type
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: taxCalculation
    title: Tax Calculation
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: YesNo
      collection: field_types
    show_sort: true
    extra_config:
      tags:
        - value: true
          label: 'Yes'
          class: success
        - value: false
          label: 'No'
          class: default
  - code: maxNonPIT
    title: Max Non-PIT
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Currency
      collection: field_types
    show_sort: false
  - code: insuranceAllowance
    title: Insurance Allowance
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: YesNo
      collection: field_types
    show_sort: true
    extra_config:
      tags:
        - value: true
          label: 'Yes'
          class: success
        - value: false
          label: 'No'
          class: default
  - code: automaticCalculation
    title: Automatic Calculation
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: YesNo
      collection: field_types
    show_sort: true
    extra_config:
      tags:
        - value: true
          label: 'Yes'
          class: success
        - value: false
          label: 'No'
          class: default
  - code: effectiveDate
    title: Effective Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
  - code: status
    title: Status
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Boolean Tag
      collection: field_types
    show_sort: true
  - code: note
    title: Note
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    show_sort: true
  - code: updatedBy
    title: Last Updated By
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: updatedAt
    title: Last Updated On
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
    options__tabular__column_width: null
    show_sort: true
mock_data:
  - allowanceCode: '00000001'
    shortName:
      default: PCDL
      vietnamese: PCDL vn
      english: PCDL en
    longName:
      default: Hỗ trợ đi lại
      vietnamese: Hỗ trợ đi lại vn
      english: Hỗ trợ đi lại en
    countryName: Việt Nam
    frequency: Month
    calculationMethod: Formula
    currency: VND
    formulaName: ABC
    entryType: true
    taxCalculation: true
    maxNonPIT: '500000'
    insuranceAllowance: true
    effectiveDate: 07/12/2024
    status: Active
    note:
      default: note1
      vietnamese: note1 vn
      english: note1 en
    created_by: ManNM7
    updated_by: ManNM7
    created_at: 7/07/2024
    updated_at: 2/27/2024
  - allowanceCode: '00000002'
    shortName:
      default: PCDL
      vietnamese: PCDL vn
      english: PCDL en
    longName:
      default: Hỗ trợ đi lại
      vietnamese: Hỗ trợ đi lại vn
      english: Hỗ trợ đi lại en
    countryName: Việt Nam
    frequency: Month
    calculationMethod: Working day
    currency: VND
    formulaName: null
    entryType: true
    taxCalculation: true
    maxNonPIT: '500000'
    insuranceAllowance: true
    effectiveDate: 07/12/2024
    status: Active
    note:
      default: note2
      vietnamese: note2 vn
      english: note2en
    created_by: ManNM7
    updated_by: ManNM7
    created_at: 7/07/2024
    updated_at: 2/27/2024
  - allowanceCode: '00000003'
    shortName:
      default: PCDL
      vietnamese: PCDL vn
      english: PCDL en
    longName:
      default: Hỗ trợ đi lại
      vietnamese: Hỗ trợ đi lại vn
      english: Hỗ trợ đi lại en
    countryName: Việt Nam
    frequency: Month
    calculationMethod: Paid salary day
    formulaName: null
    currency: VND
    entryType: true
    taxCalculation: true
    maxNonPIT: '500000'
    insuranceAllowance: true
    effectiveDate: 07/12/2024
    status: Active
    note:
      default: note3
      vietnamese: note3 vn
      english: note3 en
    created_by: ManNM7
    updated_by: ManNM7
    created_at: 7/07/2024
    updated_at: 2/27/2024
  - allowanceCode: '00000004'
    shortName:
      default: PCDL
      vietnamese: PCDL vn
      english: PCDL en
    longName:
      default: Hỗ trợ đi lại
      vietnamese: Hỗ trợ đi lại vn
      english: Hỗ trợ đi lại en
    countryName: Việt Nam
    frequency: Month
    calculationMethod: Monthly
    formulaName: null
    currency: VND
    entryType: true
    taxCalculation: true
    maxNonPIT: '500000'
    insuranceAllowance: true
    effectiveDate: 07/12/2024
    status: Active
    note:
      default: note4
      vietnamese: note4 vn
      english: note4 en
    created_by: ManNM7
    updated_by: ManNM7
    created_at: 7/07/2024
    updated_at: 2/27/2024
local_buttons: null
layout: layout-table
form_config:
  effectiveDateValidations:
    - sameDate
    - greaterThanLastest
  _formTitle:
    edit: '''Edit: '' & $.shortName.default & '' ('' & $.code & '')'''
  defaultValidateError: Required fields have not been filled in completely (fields marked with *)
  fields:
    - type: group
      _condition:
        transform: $not($.extend.formType = 'view')
      n_cols: 2
      fields:
        - type: text
          label: Allowance Code
          name: code
          placeholder: Enter Allowance Code
          formatType: code
          _condition:
            transform: $not($.extend.formType = 'view')
          _disabled:
            transform: $.extend.formType = 'edit' or $.extend.formType = 'proceed' ? true
          validators:
            - type: required
            - type: pattern
              args: ^[a-zA-Z0-9_.-]*$
              text: >-
                Code must only contain letters, numbers, hyphens, underscores,
                or dots.
            - type: maxLength
              args: '50'
              text: Allowance Code should not exceed 50 characters
        - type: select
          label: Country
          placeholder: Select Country
          clearFieldsAfterChange:
            - currency
          name: countryObj
          outputValue: value
          isLazyLoad: true
          _select:
            transform: $countriesList($.extend.limit, $.extend.page, $.extend.search)
        - type: translation
          label: Short Name
          placeholder: Enter Short Name
          name: shortName
          _condition:
            transform: $not($.extend.formType = 'view')
          validators:
            - type: required
            - type: maxLength
              args: '300'
              text: Short Name should not exceed 300 characters
          col: 2
        - type: translation
          label: Long Name
          placeholder: Enter Long Name
          name: longName
          _condition:
            transform: $not($.extend.formType = 'view')
          validators:
            - type: required
            - type: maxLength
              args: '500'
              text: Long Name should not exceed 500 characters
          col: 2
        - type: select
          label: Frequency
          placeholder: Select Frequency
          name: frequency
          outputValue: value
          _select:
            transform: $.variables._frequencies
        - type: select
          label: Calculation Method
          placeholder: Select Calculation Method
          name: calculationMethod
          outputValue: value
          _value:
            transform: $.extend.formType = 'create' ? 'CALMTD_00001'
          _select:
            transform: $.variables._calculationMethodList
        - type: select
          label: Currency
          placeholder: Select Currency
          name: currency
          outputValue: value
          _value:
            skipWhenClear: true
            transform: >-
              $isNilorEmpty($.fields.currency)?
              $filter($.variables._currencies,function($v){$v.linkCatalogDataCode=$.fields.countryObj.code
              } )[0].value 
          _select:
            transform: $currencies()
        - type: radio
          label: Entry Type
          name: entryType
          value: ENTRYTP_00001
          outputValue: value
          placeholder: Select Entry Type
          _radio:
            transform: $entryTypeList()
        - type: group
          fieldBackground: '#F8F9FA'
          borderRadius: 8px
          padding: 16px
          dependantFields: $.fields.entryType
          col: 2
          n_cols: 2
          _condition:
            transform: $.fields.entryType = 'ENTRYTP_00001'
          fields:
            - type: radio
              label: Tax calculation
              dependantFields: $.fields.entryType
              name: taxCalculation
              col: 2
              value: true
              radio:
                - label: 'Yes'
                  value: true
                - label: 'No'
                  value: false
            - type: number
              placeholder: Enter Max Non-PIT
              name: maxNonPIT
              dependantFields: $.fields.entryType
              col: 2
              _condition:
                transform: $.fields.taxCalculation = false
              _value:
                transform: >-
                  $.extend.formType != 'create' ?
                  $.extend.defaultValue.maxNonPITValue
              number:
                format: currency
                max: '999999999999999'
                precision: 4
              displayType: Currency
        - type: radio
          label: Insurance Allowance?
          name: insuranceAllowance
          value: false
          radio:
            - label: 'Yes'
              value: true
            - label: 'No'
              value: false
        - type: radio
          label: Automatic Calculation
          name: automaticCalculation
          value: true
          radio:
            - label: 'Yes'
              value: true
            - label: 'No'
              value: false
        - type: dateRange
          label: Effective Date
          placeholder: dd/mm/yyyy
          name: effectiveDate
          _value:
            transform: $.extend.formType = 'create' ? $now()
          mode: date-picker
          setting:
            type: day
            format: dd/MM/yyyy
          validators:
            - type: required
          _disabled:
            transform: $.extend.formType = 'proceed'
        - type: radio
          label: Status
          name: status
          _value:
            transform: $.extend.formType = 'create' ? true
          radio:
            - value: true
              label: Active
            - value: false
              label: Inactive
        - type: translationTextArea
          label: Note
          placeholder: Enter note
          name: note
          textarea:
            autoSize:
              minRows: 3
            maxCharCount: '1000'
          validators:
            - type: maxLength
              args: '1000'
              text: Note should not exceed 1000 characters
          col: 2
    - type: text
      label: Allowance Code
      name: code
      _condition:
        transform: $.extend.formType='view'
    - type: text
      label: Country
      name: countryName
      _condition:
        transform: $.extend.formType = 'view'
    - type: translation
      label: Short Name
      placeholder: Enter Short Name
      name: shortName
      _condition:
        transform: $.extend.formType = 'view'
    - type: translation
      label: Long Name
      placeholder: Enter Long Name
      name: longName
      _condition:
        transform: $.extend.formType = 'view'
    - type: select
      label: Frequency
      placeholder: Select Frequency
      name: frequency
      _condition:
        transform: $.extend.formType = 'view'
      outputValue: value
      _select:
        transform: $.variables._frequencies
    - type: select
      label: Calculation Method
      placeholder: Select Calculation Method
      name: calculationMethod
      outputValue: value
      _condition:
        transform: $.extend.formType = 'view'
      _value:
        transform: $.extend.formType = 'create' ? 'CALMTD_00001'
      _select:
        transform: $.variables._calculationMethodList
    - type: text
      label: Currency
      name: currencyName
      _condition:
        transform: $.extend.formType = 'view'
    - type: radio
      label: Entry Type
      name: entryType
      value: ENTRYTP_00001
      outputValue: value
      placeholder: Select Entry Type
      _condition:
        transform: $.extend.formType = 'view'
      _radio:
        transform: $entryTypeList()
    - type: radio
      label: Tax calculation
      name: taxCalculation
      value: true
      _condition:
        transform: $.extend.formType = 'view' and $.fields.entryType = 'ENTRYTP_00001'
      radio:
        - label: 'Yes'
          value: true
        - label: 'No'
          value: false
    - type: number
      label: Max Non-PIT
      placeholder: Enter Max Non-PIT
      name: maxNonPIT
      _condition:
        transform: >-
          $.extend.formType = 'view' and $.fields.taxCalculation = false and
          $.fields.entryType = 'ENTRYTP_00001'
      _value:
        transform: $.extend.formType != 'create' ? $.extend.defaultValue.maxNonPITValue
      number:
        format: currency
        max: '999999999999999'
        precision: 4
      displayType: Currency
    - type: radio
      label: Insurance Allowance?
      name: insuranceAllowance
      _condition:
        transform: $.extend.formType = 'view'
      value: false
      radio:
        - label: 'Yes'
          value: true
        - label: 'No'
          value: false
    - type: radio
      label: Automatic Calculation
      name: automaticCalculation
      _condition:
        transform: $.extend.formType = 'view'
      value: true
      radio:
        - label: 'Yes'
          value: true
        - label: 'No'
          value: false
    - type: dateRange
      label: Effective Date
      placeholder: dd/mm/yyyy
      name: effectiveDate
      mode: date-picker
      setting:
        type: day
        format: dd/MM/yyyy
      _condition:
        transform: $.extend.formType = 'view'
    - type: radio
      label: Status
      name: status
      _condition:
        transform: $.extend.formType = 'view'
      _value:
        transform: $.extend.formType = 'create' ? true
      radio:
        - value: true
          label: Active
        - value: false
          label: Inactive
    - type: translationTextArea
      label: Note
      placeholder: Enter note
      name: note
      textarea:
        autoSize:
          minRows: 3
        maxCharCount: '1000'
      _condition:
        transform: $.extend.formType = 'view'
      validators:
        - type: maxLength
          args: '1000'
          text: Note should not exceed 1000 characters
  sources:
    countriesList:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': {'id': $item.id , 'code': $item.code} }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    currencies:
      uri: '"/api/picklists/CURRENCY/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value':
        $item.code,'linkCatalogDataCode':$item.codE501}})[]
      disabledCache: true
    frequencies:
      uri: '"/api/picklists/SALARYFREQUENCY/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
    entryTypeList:
      uri: '"/api/picklists/ENTRYTYPE/values"'
      method: GET
      queryTransform: '{''sort'':[{''field'' : ''name''  , ''order'': ''DESC''  }] }'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
    calculationMethodList:
      uri: '"/api/picklists/CALCULATIONMETHOD/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
  variables:
    _currencies:
      transform: $currencies()
    _frequencies:
      transform: $frequencies()
    _calculationMethodList:
      transform: $calculationMethodList()
    _selectedCalculationMethod:
      transform: >-
        $filter($.variables._calculationMethodList, function ($v, $i, $a){
        $count($filter($.fields.calculationMethod, function($v1, $i1, $a1){$v1 =
        $v.value })) > 0 })[] 
  footer:
    create: true
    update: true
    createdOn: createdAt
    updatedOn: updatedAt
    createdBy: createdBy
    updatedBy: updatedBy
filter_config:
  fields:
    - type: selectAll
      label: Allowance Code
      name: code
      labelType: type-grid
      mode: multiple
      placeholder: Select Allowance Code
      isLazyLoad: true
      _options:
        transform: $allowanceCodeList($.extend.limit, $.extend.page, $.extend.search)
    - type: text
      label: Short Name
      labelType: type-grid
      placeholder: Enter Short Name
      name: shortName
    - type: text
      label: Long Name
      labelType: type-grid
      placeholder: Enter Long Name
      name: longName
    - type: selectAll
      labelType: type-grid
      label: Country
      name: country
      placeholder: Select Country
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $countriesList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      label: Frequency
      placeholder: Select Frequency
      labelType: type-grid
      name: frequency
      isLazyLoad: true
      mode: multiple
      _options:
        transform: $frequencies($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      label: Calculation Method
      labelType: type-grid
      isLazyLoad: true
      placeholder: Select Calculation Method
      name: calculationMethod
      mode: multiple
      _options:
        transform: $calculationMethodList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      label: Currency
      placeholder: Select Currency
      labelType: type-grid
      isLazyLoad: true
      name: currency
      mode: multiple
      _options:
        transform: $currencies($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      labelType: type-grid
      isLazyLoad: true
      label: Entry Type
      name: entryType
      mode: multiple
      placeholder: Select Entry Type
      _options:
        transform: $entryTypeList($.extend.limit, $.extend.page, $.extend.search)
    - type: radio
      labelType: type-grid
      label: Tax calculation?
      name: taxCalculation
      value: null
      radio:
        - label: All
          value: null
        - label: 'Yes'
          value: true
        - label: 'No'
          value: 'false'
    - type: radio
      label: Insurance Allowance?
      labelType: type-grid
      name: insuranceAllowance
      value: null
      radio:
        - label: All
          value: null
        - label: 'Yes'
          value: true
        - label: 'No'
          value: 'false'
    - type: radio
      label: Automatic Calculation
      labelType: type-grid
      name: automaticCalculation
      value: null
      radio:
        - label: All
          value: null
        - label: 'Yes'
          value: true
        - label: 'No'
          value: 'false'
    - name: effectiveDate
      label: Effective Date
      labelType: type-grid
      type: dateRange
      mode: date-picker
      setting:
        format: dd/MM/yyyy
        type: date
    - type: radio
      label: Status
      labelType: type-grid
      name: status
      value: null
      radio:
        - label: All
          value: null
        - label: Active
          value: true
        - label: Inactive
          value: false
    - name: updatedBy
      label: Last Updated By
      type: text
      labelType: type-grid
      placeholder: Enter Last Updated By
    - name: updatedAt
      label: Last Updated On
      type: dateRange
      labelType: type-grid
      setting:
        mode: date
        format: dd/MM/yyyy
  filterMapping:
    - field: countryCode
      operator: $in
      valueField: country.(value)
    - field: frequency
      operator: $in
      valueField: frequency.(value)
    - field: calculationMethod
      operator: $in
      valueField: calculationMethod.(value)
    - field: currency
      operator: $in
      valueField: currency.(value)
    - field: entryType
      operator: $in
      valueField: entryType.(value)
    - field: taxCalculation
      operator: $eq
      valueField: taxCalculation
    - field: status
      operator: $eq
      valueField: status
    - field: insuranceAllowance
      operator: $eq
      valueField: insuranceAllowance
    - field: automaticCalculation
      operator: $eq
      valueField: automaticCalculation
    - field: code
      operator: $in
      valueField: code.(value)
    - field: effectiveDate
      operator: $eq
      valueField: effectiveDate
    - field: shortName
      operator: $cont
      valueField: shortName
    - field: longName
      operator: $cont
      valueField: longName
    - field: updatedBy
      operator: $cont
      valueField: updatedBy
    - field: updatedAt
      operator: $between
      valueField: updatedAt
  sources:
    allowanceCodeList:
      uri: '"/api/fixed-allowances"'
      method: GET
      queryTransform: '{''limit'': $.limit, ''page'': $.page, ''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.shortName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    countriesList:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    currencies:
      uri: '"/api/picklists/CURRENCY/values/pagination"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})
      disabledCache: true
      params:
        - limit
        - page
        - search
    frequencies:
      uri: '"/api/picklists/SALARYFREQUENCY/values/pagination"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    entryTypeList:
      uri: '"/api/picklists/ENTRYTYPE/values/pagination"'
      method: GET
      queryTransform: '{''sort'':[{''field'' : ''name''  , ''order'': ''DESC''  }] }'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    calculationMethodList:
      uri: '"/api/picklists/CALCULATIONMETHOD/values/pagination"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    userList:
      uri: '"/api/internal-users"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.userName, 'value':
        $item.userName}})
      disabledCache: true
layout_options:
  show_dialog_form_save_add_button: true
  reset_page_index_after_do_action:
    edit: true
  delete_multi_items: true
  apply_delete_multi_items_to_delete_one: true
  custom_delete_body: '{''ids'': $map($.data,function($item){$item.id})[]}'
  history_widget_header_options:
    duplicate: false
  custom_history_backend_url: /api/fixed-allowances/:id/clone
  tool_table:
    - id: export
      icon: icon-download-simple
  show_filter_results_message: true
  is_new_dynamic_form: true
  hide_action_row: true
  store_selected_items: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: null
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    icon: pencil
    type: tertiary
  - id: delete
    icon: trash
    type: tertiary
backend_url: /api/fixed-allowances
screen_name: null
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: countryCode
    defaultName: CountryCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Allowance
  parent:
    title: PR Picklist
