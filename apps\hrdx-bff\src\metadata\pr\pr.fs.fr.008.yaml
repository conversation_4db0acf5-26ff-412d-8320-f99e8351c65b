id: PR.FS.FR.008
status: draft
sort: 119
user_created: 56e71b91-dd39-4ebc-afd1-b4f00f38d4a5
date_created: '2024-07-25T09:57:15.928Z'
user_updated: b403d5da-d684-45e4-ae6d-e25223baedc5
date_updated: '2025-07-17T09:41:28.790Z'
title: Earning
requirement:
  time: 1748420506978
  blocks:
    - id: _HBlB3Zxf8
      type: paragraph
      data:
        text: Danh mục khoản hỗ trợ không cố định - Lấy thông tin theo filter
    - id: OoxCkOS1Nc
      type: paragraph
      data:
        text: >-
          <PERSON> phép bộ phận nhân sự tập đoàn thêm mới đối với các khoản hỗ trợ
          không cố định theo yêu cầu đề xuất từ các CTTV.
    - id: f9wT7u9g6A
      type: paragraph
      data:
        text: >-
          <PERSON><PERSON> thống sẽ hiển thị cảnh báo và không cho phép lưu đối với các trường
          hợp: thêm mới mã khoản hỗ trợ đã tồn tại, xóa khoản hỗ trợ đang được
          sử dụng.
  version: 2.30.7
screen_design: null
module: PR
local_fields:
  - code: code
    title: Additional Earning Code
    description: >-
      - Hiển thị danh sách khoản hỗ trợ không cố định  đã tồn tại trong hệ
      thống. 

      - Danh sách các khoản hỗ trợ không cố định hiển thị theo tiêu chí tìm
      kiếm. 

      - Nếu không có tiêu chí tìm kiếm nào, thực hiện hiển thị toàn bộ danh sách
      khoản hỗ trợ không cố định đang có trên hệ thống theo thứ tự A-Z theo mã
      khoản hỗ trợ không cố định.
    data_type:
      key: Employee Code
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    show_sort: true
    pinned: true
  - code: shortName
    title: Short Name
    description: >-
      - Hiển thị thông tin tên viết tắt các khoản hỗ trợ không cố định. Không
      được chỉnh sửa. 
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: longName
    title: Long Name
    description: >-
      - Hiển thị thông tin tên đầy đủ các khoản hỗ trợ không cố định. Không được
      chỉnh sửa. 
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: countryName
    title: Country
    description: >-
      - Hiển thị quốc gia sử dụng khoản hỗ trợ không cố định. Không cho chỉnh
      sửa
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: calculationMethodName
    title: Calculation method
    description: >-
      - Hiển thị Cách tính khoản hỗ trợ trong 2 cách: theo tháng hoặc theo công
      thức. Không cho chỉnh sửa
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: currencyName
    title: Currency
    description: >-
      - Hiển thị thông tin loại tiền tệ của khoản hỗ trợ không cố định. Không
      được chỉnh sửa.
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: entryTypeName
    title: Entry Type
    description: >-
      - Hiển thị thông tin xác định khoản hỗ trợ không cố định là trước thuế hay
      sau thuế. Không cho chỉnh sửa

      - Có 2 Option: 

      + Trước thuế: hệ thống sẽ hiển thị tiếp trường xác định có trừ vào thu
      nhập chịu thuế hay không. Nếu có check thì khoản hỗ trợ này sẽ được tính
      vào thu nhập chịu thuế của nhân viên, nếu không thì không tính vào thu
      nhập chịu thuế mà chỉ tính vào tiền thực lãnh

      + Sau thuế: hệ thống không hiển thị trường xác định "có tính vào thu nhập
      chịu thuế hay không". Khoản hỗ trợ này sẽ chỉ tính vào phần thực lãnh của
      nhân viên
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: taxCalculation
    title: Tax calculation
    description: >-
      - Hiển thị thông tin có tính thuế không của khoản hỗ trợ không cố định.
      Không cho chỉnh sửa

      - Cho phép người dùng chọn 1 trong 2 Option: Có/Không.

      Đối với các khoản hỗ trợ không cố định CÓ xác định tính thuế thì đưa vào
      thu nhập chịu thuế. Ngược lại thì không đưa vào thu nhập tính thuế
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: YesNo
      collection: field_types
    show_sort: true
  - code: maxNonPIT
    title: Max Non PIT
    data_type:
      key: String
      collection: data_types
    display_type:
      key: VND
      collection: field_types
    show_sort: false
  - code: insuranceEarning
    title: Insurance Contribution
    data_type:
      key: String
      collection: data_types
    display_type:
      key: YesNo
      collection: field_types
    show_sort: true
  - code: automaticCalculation
    title: Automatic Calculation
    data_type:
      key: String
      collection: data_types
    display_type:
      key: YesNo
      collection: field_types
    show_sort: true
  - code: effectiveDate
    pinned: false
    title: Effective Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    show_sort: true
  - code: status
    title: Status
    description: '- Hiển thị Trạng thái theo thông tin theo bản ghi tương ứng.'
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Boolean Tag
      collection: field_types
    show_sort: true
  - code: note
    title: Note
    description: '- Hiển thị ghi chú. Không chỉnh sửa.'
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    show_sort: true
  - code: updatedBy
    title: Last Updated By
    description: 'Hiển thị account của người sửa của bản ghi được chọn (Ví dụ: PhuongDT104)'
    data_type:
      key: User
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: updatedAt
    title: Last Updated On
    description: >-
      Hiển thị đầy đủ thông tin Ngày/Tháng/Năm, Giờ/Phút/Giây thực hiện cập nhật
      dữ liệu cuối cùng (Ví dụ: 06/05/2024 10:20:53)
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
    show_sort: true
mock_data:
  - code: '********'
    shortName:
      default: 'ER_GOLD_CASH '
      vietnamese: 'ER_GOLD_CASH '
      english: 'ER_GOLD_CASH '
    longName:
      default: Hỗ trợ Cashout Gold MyFPT
      vietnamese: Hỗ trợ Cashout Gold MyFPT
      english: Hỗ trợ Cashout Gold MyFPT
    country: Việt Nam
    calculationMethod: Monthly
    formulaName: Công thức
    currency: VND
    entryType: Pre-tax
    taxCalculation: true
    maxNonPIT: '500.000'
    effectiveDate: 04/04/2024 09:31:00
    insuranceEarning: 'Yes'
    status: Active
    note:
      default: Another note.
      vietnamese: Another note. vn
      english: Another note. en
    createTime: 04/04/2024 09:31:00
    creator: PhuongDT104
    lastestEditedTime: 06/05/2024 10:20:53
    lastestEditor: PhuongDT104
  - code: '00000002'
    shortName:
      default: 00ER_PROJECT
      vietnamese: 00ER_PROJECT
      english: 00ER_PROJECT
    longName:
      default: Hỗ trợ dự án
      vietnamese: Hỗ trợ dự án
      english: Hỗ trợ dự án
    country: Nhật Bản
    calculationMethod: Formula
    formulaName: Formula2
    currency: JPY
    entryType: Post-tax
    taxCalculation: false
    maxNonPIT: 1.000.000
    effectiveDate: 11/05/2024 14:45:00
    insuranceEarning: 'No'
    status: Inactive
    note:
      default: This is another note.
      vietnamese: This is another note. vn
      english: This is another note. en
    createTime: 10/05/2024 14:45:00
    creator: PhuongDT105
    lastestEditedTime: 10/06/2024 11:30:00
    lastestEditor: PhuongDT105
local_buttons: null
layout: layout-table
form_config:
  _formTitle:
    create: '''Add New Additional Earning'''
    edit: '''Edit: '' & $.shortName.default & '' ('' & $.code & '')'''
  fields:
    - type: group
      n_cols: 2
      collapsed: false
      disableEventCollapse: true
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - type: select
          label: Country
          placeholder: Select Country
          name: countryObj
          clearFieldsAfterChange:
            - currencyObj
          outputValue: value
          isLazyLoad: true
          _select:
            transform: $countriesList($.extend.limit, $.extend.page, $.extend.search)
        - type: text
          label: Additional Earning Code
          placeholder: Enter Additional Earning Code
          name: code
          formatType: code
          _disabled:
            transform: $.extend.formType = 'edit'
          validators:
            - type: required
            - type: pattern
              args: ^[a-zA-Z0-9_.-]*$
              text: >-
                Code must only contain letters, numbers, hyphens, underscores,
                or dots.
            - type: maxLength
              args: '50'
              text: Additional Earning Code should not exceed 50 characters
        - type: translation
          label: Short Name
          placeholder: Enter Short Name
          name: shortName
          validators:
            - type: required
            - type: maxLength
              args: '300'
              text: Short Name should not exceed 300 characters
          col: 2
        - type: translation
          label: Long Name
          placeholder: Enter Long Name
          name: longName
          validators:
            - type: required
            - type: maxLength
              args: '500'
              text: Long Name should not exceed 500 characters
          col: 2
        - type: select
          outputValue: value
          label: Calculation Method
          name: calculationMethod
          placeholder: Select Calculation Method
          _value:
            transform: >-
              $.extend.formType = 'create' or $.extend.formType = 'proceed' ?
              'CALMTD_00001'
          _select:
            transform: $calculationMethodList()
        - type: select
          outputValue: value
          label: Currency
          name: currencyObj
          isLazyLoad: true
          placeholder: Select Currency
          _value:
            skipWhenClear: true
            transform: >-
              ($countryCode:=$.fields.countryObj.code;
              ($not($isNilorEmpty($countryCode)) and
              $isNilorEmpty($.fields.currencyObj.code)) ? ($currency :=
              $currencies($.extend.limit, $.extend.page, $.extend.search,
              $countryCode)[0]; $currency ? $currency : '_setSelectValueNull'))
          _select:
            transform: $currencies($.extend.limit, $.extend.page, $.extend.search)
        - type: radio
          label: Entry Type
          outputValue: value
          value: ENTRYTP_00001
          name: entryType
          _radio:
            transform: $entryTypeList()
          col: 2
        - type: group
          fieldBackground: '#F8F9FA'
          borderRadius: 8px
          padding: 16px
          dependantFields: $.fields.entryType
          col: 2
          n_cols: 2
          _condition:
            transform: $.fields.entryType = 'ENTRYTP_00001'
          fields:
            - type: radio
              label: Tax calculation
              dependantFields: $.fields.entryType
              name: taxCalculation
              col: 2
              value: true
              radio:
                - label: 'Yes'
                  value: true
                - label: 'No'
                  value: false
            - type: number
              placeholder: Enter Max Non-PIT
              name: maxNonPIT
              dependantFields: $.fields.entryType
              col: 2
              _condition:
                transform: $.fields.taxCalculation = false
              _value:
                transform: >-
                  $.extend.formType != 'create' ?
                  $.extend.defaultValue.maxNonPITValue
              number:
                format: currency
                max: '999999999999999'
                precision: 4
              displayType: Currency
        - type: radio
          label: Insurance Contribution
          name: insuranceEarning
          radio:
            - label: 'Yes'
              value: true
            - label: 'No'
              value: false
          value: false
          outputValue: value
        - type: radio
          label: Automatic calculation
          name: automaticCalculation
          radio:
            - label: 'Yes'
              value: true
            - label: 'No'
              value: false
          value: true
          outputValue: value
        - type: dateRange
          label: Effective Date
          placeholder: dd/mm/yyyy
          name: effectiveDate
          _value:
            transform: $.extend.formType = 'create' ? $now()
          validators:
            - type: required
          _disabled:
            transform: $.extend.formType = 'proceed'
          mode: date-picker
          setting:
            type: day
            format: dd/MM/yyyy
        - name: status
          label: Status
          type: radio
          _value:
            transform: $.extend.formType = 'create' ? true
          radio:
            - label: Active
              value: true
            - label: Inactive
              value: false
          outputValue: value
        - type: translationTextArea
          label: Note
          placeholder: Enter Note
          name: note
          textarea:
            autoSize:
              minRows: 3
            maxCharCount: 1000
          validators:
            - type: maxLength
              args: 1000
              text: Note should not exceed 1000 characters
          col: 2
    - type: group
      collapsed: false
      disableEventCollapse: true
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - type: text
          label: Country
          name: countryName
        - type: text
          label: Additional Earning Code
          name: code
          _disabled:
            transform: $.extend.formType = 'edit'
          validators:
            - type: required
            - type: maxLength
              args: '50'
              text: Additional Earning Code should not exceed 50 characters
          col: 2
        - type: translation
          label: Short Name
          placeholder: Enter short name
          name: shortName
          validators:
            - type: required
            - type: maxLength
              args: '300'
              text: Short Name should not exceed 300 characters
          col: 2
        - type: translation
          label: Long Name
          placeholder: Enter long name
          name: longName
          validators:
            - type: required
            - type: maxLength
              args: '500'
              text: Long Name should not exceed 500 characters
          col: 2
        - type: select
          outputValue: value
          label: Calculation Method
          name: calculationMethod
          placeholder: Select Calculation Method
          _select:
            transform: $calculationMethodList()
          col: 2
        - type: text
          outputValue: value
          label: Currency
          name: currencyName
          col: 2
        - type: radio
          label: Entry Type
          outputValue: value
          value: ENTRYTP_00001
          name: entryType
          _radio:
            transform: $entryTypeList()
          col: 2
        - type: radio
          label: Tax calculation
          name: taxCalculation
          value: true
          _condition:
            transform: $.fields.entryType = 'ENTRYTP_00001'
          radio:
            - label: 'Yes'
              value: true
            - label: 'No'
              value: false
          col: 2
        - type: number
          label: Max Non-PIT
          placeholder: Enter Max Non-PIT
          name: maxNonPIT
          _condition:
            transform: >-
              $.fields.taxCalculation = false and $.fields.entryType =
              'ENTRYTP_00001'
          _value:
            transform: >-
              $.extend.formType != 'create' ?
              $.extend.defaultValue.maxNonPITValue
          number:
            format: currency
            max: '999999999999999'
            precision: 4
          displayType: Currency
          col: 2
        - type: radio
          label: Insurance Contribution
          name: insuranceEarning
          radio:
            - label: 'Yes'
              value: true
            - label: 'No'
              value: false
          value: false
          outputValue: value
          col: 2
        - type: radio
          label: Automatic calculation
          name: automaticCalculation
          radio:
            - label: 'Yes'
              value: true
            - label: 'No'
              value: false
          value: true
          outputValue: value
          col: 2
        - type: dateRange
          label: Effective Date
          placeholder: dd/mm/yyyy
          name: effectiveDate
          mode: date-picker
          setting:
            type: day
            format: dd/MM/yyyy
          col: 2
        - name: status
          label: Status
          type: radio
          _value:
            transform: $.extend.formType = 'create' ? true
          radio:
            - label: Active
              value: true
            - label: Inactive
              value: false
          outputValue: value
          col: 2
        - type: translationTextArea
          label: Note
          placeholder: Enter note
          name: note
          textarea:
            autoSize:
              minRows: 3
            maxCharCount: 1000
          validators:
            - type: maxLength
              args: 1000
              text: Note should not exceed 1000 characters
          col: 2
  sources:
    currencies:
      uri: '"/api/picklists/CURRENCY/values/pagination"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'linkCatalogDataCode','operator':
        '$eq','value':$.linkCatalogDataCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': {'code': $item.code} }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - linkCatalogDataCode
    frequencies:
      uri: '"/api/picklists/SALARYFREQUENCY/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
    calculationMethodList:
      uri: '"/api/picklists/CalculationMethod/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
    countriesListNotPagination:
      uri: '"/api/picklists/COUNTRIES/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
    countriesList:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': {'id': $item.id , 'code': $item.code} }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    entryTypeList:
      uri: '"/api/picklists/ENTRYTYPE/values"'
      method: GET
      queryTransform: '{''sort'':[{''field'' : ''name''  , ''order'': ''DESC''  }] }'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default , 'value':
        $item.code}})[]
      disabledCache: true
    fomulaList:
      uri: '"/api/formula-structures"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.formulaLongName, 'value':
        $item.formulaCode}})[]
      disabledCache: true
  variables: {}
  historyTitle: $DateFormat($.effectiveDate, 'DD/MM/YYYY')
  historyDescription: '$.status = true ? ''Active'' : ''Inactive'''
  effectiveDateValidations:
    - sameDate
    - greaterThanLastest
  formTitle:
    create: 'Add new: Earning'
    edit: Edit Earning
    view: View Detail Earning
  footer:
    update: true
    updatedOn: updatedAt
    updatedBy: updatedBy
filter_config:
  fields:
    - type: selectAll
      label: Country
      labelType: type-grid
      placeholder: Select country
      name: country
      isLazyLoad: true
      _options:
        transform: $nationsList($.extend.limit, $.extend.page, $.extend.search)
    - name: code
      labelType: type-grid
      label: Additional Earning Code
      isLazyLoad: true
      type: selectAll
      placeholder: Select Additional Earning Code
      _options:
        transform: $earningList($.extend.limit, $.extend.page, $.extend.search)
    - type: text
      label: Short Name
      placeholder: Enter Short Name
      labelType: type-grid
      name: shortName
    - type: text
      label: Long Name
      labelType: type-grid
      placeholder: Enter Long Name
      name: longName
    - type: selectAll
      label: Calculation Method
      labelType: type-grid
      placeholder: Select Calculation Method
      name: calculationMethod
      isLazyLoad: true
      _options:
        transform: $calculationMethodList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      label: Currency
      labelType: type-grid
      placeholder: Select currency
      isLazyLoad: true
      name: currency
      _options:
        transform: $currencies($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      label: Entry Type
      name: entryType
      labelType: type-grid
      mode: multiple
      isLazyLoad: true
      placeholder: Select Entry Type
      _options:
        transform: $entryTypeList($.extend.limit, $.extend.page, $.extend.search)
    - type: radio
      label: Tax Calculation
      value: null
      labelType: type-grid
      name: taxCalculation
      radio:
        - label: All
          value: null
        - label: 'Yes'
          value: 'true'
        - label: 'No'
          value: 'false'
      _condition:
        transform: $.fields.entryType = 'PRE_TAX'
    - type: radio
      label: Insurance Contribution
      value: null
      labelType: type-grid
      name: insuranceEarning
      radio:
        - label: All
          value: null
        - label: 'Yes'
          value: true
        - label: 'No'
          value: false
    - type: radio
      label: Automatic Calculation
      value: null
      labelType: type-grid
      name: automaticCalculation
      radio:
        - label: All
          value: null
        - label: 'Yes'
          value: true
        - label: 'No'
          value: false
    - type: dateRange
      label: Effective Date
      name: effectiveDate
      labelType: type-grid
      setting:
        type: day
        format: dd/MM/yyyy
    - name: status
      labelType: type-grid
      label: Status
      value: null
      type: radio
      radio:
        - label: All
          value: null
        - label: Active
          value: true
        - label: Inactive
          value: false
    - name: updatedBy
      label: Last Updated By
      type: text
      labelType: type-grid
      placeholder: Enter Last Updated By
    - name: updatedAt
      label: Last Updated On
      type: dateRange
      labelType: type-grid
      setting:
        mode: date
        format: dd/MM/yyyy
  filterMapping:
    - field: countryCode
      operator: $in
      valueField: country.(value)
    - field: calculationMethod
      operator: $in
      valueField: calculationMethod.(value)
    - field: currency
      operator: $in
      valueField: currency.(value)
    - field: entryType
      operator: $in
      valueField: entryType.(value)
    - field: taxCalculation
      operator: $eq
      valueField: taxCalculation
    - field: automaticCalculation
      operator: $eq
      valueField: automaticCalculation
    - field: status
      operator: $eq
      valueField: status
    - field: code
      operator: $in
      valueField: code.(value)
    - field: effectiveDate
      operator: $between
      valueField: effectiveDate
    - field: shortName
      operator: $cont
      valueField: shortName
    - field: longName
      operator: $cont
      valueField: longName
    - field: updatedBy
      operator: $cont
      valueField: updatedBy
    - field: updatedAt
      operator: $between
      valueField: updatedAt
  sources:
    earningList:
      uri: '"/api/un-fixed-allowances"'
      method: GET
      queryTransform: '{''limit'': $.limit, ''page'': $.page, ''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    entryTypeList:
      uri: '"/api/picklists/ENTRYTYPE/values/pagination"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default , 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    currencies:
      uri: '"/api/picklists/CURRENCY/values/pagination"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default , 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    calculationMethodList:
      uri: '"/api/picklists/CalculationMethod/values/pagination"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    nationsList:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    userList:
      uri: '"/api/internal-users"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.userName, 'value':
        $item.userName}})[]
      disabledCache: true
layout_options:
  show_dialog_form_save_add_button: true
  show_create_data_table: true
  show_dialog_footer: true
  show_detail_history: true
  apply_delete_multi_items_to_delete_one: true
  custom_delete_body: '{''ids'': $map($.data,function($item){$item.id})[]}'
  history_widget_header_options:
    duplicate: false
  delete_multi_items: true
  custom_history_backend_url: /api/un-fixed-allowances/:id/clone
  tool_table:
    - id: export
      icon: icon-download-simple
  reset_page_index_after_do_action:
    edit: true
  show_filter_results_message: true
  is_new_dynamic_form: true
  hide_action_row: true
  store_selected_items: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    icon: pencil
    type: tertiary
  - id: delete
    icon: trash
    type: tertiary
backend_url: /api/un-fixed-allowances
screen_name: null
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: countryCode
    defaultName: CountryCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Earning
  parent:
    title: PR Picklist
