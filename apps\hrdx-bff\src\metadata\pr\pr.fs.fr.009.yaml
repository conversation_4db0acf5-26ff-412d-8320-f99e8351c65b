id: PR.FS.FR.009
status: draft
sort: null
user_created: 9cfe47ce-3920-4eea-91ec-1d8471b048c5
date_created: '2024-09-25T20:34:30.320Z'
user_updated: b403d5da-d684-45e4-ae6d-e25223baedc5
date_updated: '2025-07-15T05:46:48.811Z'
title: Exchange Rate
requirement:
  time: 1727296456498
  blocks:
    - id: kxdPNNPSkG
      type: paragraph
      data:
        text: "'-\t Cho phép bộ phận nhân sự tập đoàn thiết lập tỷ giá được quy đổi giữa các đơn vị tiền tệ."
    - id: iTUzR0ZXbl
      type: paragraph
      data:
        text: >-
          - Danh mục tỷ giá sẽ được quản lý tập trung trên Tập đoàn, do bộ phận
          nhân sự của Tập đoàn khai báo và quản lý, cá<PERSON> đơn vị chỉ được quyền
          khai thác, kh<PERSON><PERSON> được quyền điều chỉnh, thay đổi. (Trường hợp ngoại lệ
          sẽ vẫn được phân quyền)
    - id: tWEuDzxLWy
      type: paragraph
      data:
        text: >-
          - Bộ phận nhân sự tập đoàn có thể điều chỉnh thông tin khi có thay
          đổi.
  version: 2.29.1
screen_design: null
module: PR
local_fields:
  - code: sourceCurrencyCode
    title: Base Currency Code
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: targetCurrencyCode
    title: Target Currency Code
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: rate
    title: Exchange Rate
    data_type: null
    display_type:
      key: Currency
      collection: field_types
    show_sort: true
  - code: source
    title: 'Exchange Rate Source '
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: effectiveDate
    title: Effective Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    show_sort: true
  - code: status
    title: Status
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: Boolean Tag
      collection: field_types
    show_sort: true
  - code: note
    title: Note
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: updatedBy
    title: Last Updated By
    data_type:
      key: User
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: updatedAt
    title: Last Updated On
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
    show_sort: true
mock_data: null
local_buttons: null
layout: layout-table
form_config:
  defaultValidateError: Required fields have not been filled in completely (fields marked with *)
  fields:
    - type: select
      label: Base Currency Code
      name: sourceCurrencyCode
      outputValue: value
      placeholder: Select Currency Code
      validators:
        - type: required
      _select:
        transform: $.variables._currencyLst
    - type: select
      label: Target Currency Code
      name: targetCurrencyCode
      outputValue: value
      placeholder: Select Currency Code
      validators:
        - type: required
      _select:
        transform: $.variables._currencyLst
    - type: number
      label: Exchange Rate
      placeholder: Enter Exchange Rate
      name: rate
      number:
        format: currency
        precision: 6
      validators:
        - type: required
        - type: max
          args: 10000000000000000000
          text: Exchage Rate should not exceed 20 characters
        - type: maxLength
          args: '20'
          text: Exchage Rate should not exceed 20 characters
    - type: translation
      label: Exchange Rate Source
      name: source
      placeholder: Enter Exchange Rate Source
    - type: dateRange
      label: Effective Date
      name: effectiveDate
      validators:
        - type: required
      placeholder: dd/MM/yyyy
      _value:
        transform: >-
          $.extend.formType = 'create' ? $not($exists($.fields.effectiveDate)) ?
          $now()
      setting:
        format: dd/MM/yyyy
        type: date
      mode: date-picker
      code: effectiveDate
    - type: radio
      name: status
      label: Status
      value: true
      radio:
        - value: true
          label: Active
        - value: false
          label: Inactive
    - type: translationTextArea
      label: Note
      name: note
      textarea:
        autoSize:
          minRows: 3
        maxCharCount: '1000'
      validators:
        - type: maxLength
          args: '1000'
          text: Note should not exceed 1000 characters
  sources:
    currency:
      uri: '"/api/picklists/CURRENCY/values"'
      method: GET
      queryTransform: '{''filter'': [{''field'':''status'',''operator'': ''$eq'',''value'':true}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.code, 'value':
        $item.code}})[]
      disabledCache: true
  variables:
    _currencyLst:
      transform: $currency()
  footer:
    create: true
    update: true
    createdOn: createdAt
    updatedOn: updatedAt
    createdBy: createdBy
    updatedBy: updatedBy
filter_config:
  fields:
    - type: selectAll
      label: Base Currency Code
      name: sourceCurrencyCode
      outputValue: value
      placeholder: Select Base Currency Code
      labelType: type-grid
      isLazyLoad: true
      _options:
        transform: $currencies($.extend.limit, $.extend.page, $.extend.search)
      mode: multiple
    - type: selectAll
      label: Target Currency Code
      name: targetCurrencyCode
      labelType: type-grid
      outputValue: value
      placeholder: Select Target Currency Code
      isLazyLoad: true
      _options:
        transform: $currencies($.extend.limit, $.extend.page, $.extend.search)
      mode: multiple
    - type: number
      label: Exchange Rate
      labelType: type-grid
      name: rate
      placeholder: Enter Exchange Rate
      number:
        format: currency
        max: '999999999999999'
        precision: 6
      displayType: Currency
    - type: text
      label: Exchange Rate Source
      labelType: type-grid
      name: source
      placeholder: Enter Exchange Rate Source
    - type: dateRange
      label: Effective Date
      name: effectiveDate
      labelType: type-grid
      placeholder: dd/MM/yyyy
      _value:
        transform: >-
          $.extend.formType = 'create' ? $not($exists($.fields.effectiveDate)) ?
          $now()
      setting:
        format: dd/MM/yyyy
        type: date
      mode: date-picker
      code: effectiveDate
    - type: radio
      name: status
      labelType: type-grid
      label: Status
      value: ''
      radio:
        - value: ''
          label: All
        - value: true
          label: Active
        - value: false
          label: Inactive
    - name: updatedBy
      label: Last Updated By
      type: text
      labelType: type-grid
      placeholder: Enter Last Updated By
    - name: updatedAt
      label: Last Updated On
      type: dateRange
      labelType: type-grid
      setting:
        mode: date
        format: dd/MM/yyyy
  filterMapping:
    - field: sourceCurrencyCode
      operator: $in
      valueField: sourceCurrencyCode
    - field: targetCurrencyCode
      operator: $in
      valueField: targetCurrencyCode
    - field: status
      operator: $eq
      valueField: status
    - field: effectiveDate
      operator: $eq
      valueField: effectiveDate
    - field: rate
      operator: $eq
      valueField: rate
    - field: source
      operator: $cont
      valueField: source
    - type: dateRange
      name: createdAt
      label: Created Time
      settings:
        format: dd/MM/yyyy
        mode: date
    - type: text
      name: createdBy
      label: Creator
    - type: dateRange
      name: updatedAt
      label: Last Edited Time
      settings:
        format: dd/MM/yyyy
        mode: date
    - type: text
      name: updatedBy
      label: Last Editor
    - field: createdAt
      operator: $between
      valueField: createdAt
    - field: createdBy
      operator: $cont
      valueField: createdBy
    - field: updatedAt
      operator: $between
      valueField: updatedAt
    - field: updatedBy
      operator: $cont
      valueField: updatedBy
  sources:
    currencies:
      uri: '"/api/picklists/CURRENCY/values/pagination"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
  variables:
    _currencyLst:
      transform: $currency()
layout_options:
  show_detail_history: false
  show_detail_drawer: false
  show_table_filter: true
  delete_multi_items: true
  apply_delete_multi_items_to_delete_one: true
  custom_delete_body: '{''ids'': $map($.data,function($item){$item.id})[]}'
  show_dialog_form_save_add_button: true
  toolTable:
    export: true
    adjustDisplay: true
  tool_table:
    - id: export
      icon: icon-download-simple
  layout_detail_modal_footer_buttons:
    - id: detele
      title: Delete
    - id: edit
      title: Edit
      type: primary
  show_filter_results_message: true
  hide_action_row: true
  store_selected_items: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: null
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    icon: pencil
  - id: delete
    icon: trash
backend_url: api/exchange-rates
screen_name: exchange-rate-setting
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key: null
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Exchange Rate
  parent:
    title: PR Picklist
