id: PR.FS.FR.011
status: draft
sort: 136
user_created: b403d5da-d684-45e4-ae6d-e25223baedc5
date_created: '2024-12-09T03:59:26.288Z'
user_updated: b403d5da-d684-45e4-ae6d-e25223baedc5
date_updated: '2025-07-16T01:52:21.319Z'
title: Salary Plan
requirement:
  time: 1748420454235
  blocks:
    - id: mWbsubuMTP
      type: paragraph
      data:
        text: '- Cho phép bộ phận nhân sự tập đoàn thêm các Ngạch lương.'
    - id: SXqXA90I8P
      type: paragraph
      data:
        text: >-
          - Danh mục được quản lý tập trung trên Tập đoàn, do bộ phận nhân sự
          của Tập đoàn khai báo và quản lý, các đơn vị chỉ được quyền khai thác,
          khô<PERSON> đượ<PERSON> quyền điều chỉnh, thay đổi. (Tr<PERSON><PERSON><PERSON> hợp ngoại lệ sẽ vẫn
          được phân quyền)
    - id: _V2hKTlB7w
      type: paragraph
      data:
        text: >-
          - Bộ phận nhân sự tập đoàn có thể điều chỉnh thông tin khi có thay
          đổi.
  version: 2.30.7
screen_design: null
module: PR
local_fields:
  - code: code
    pinned: true
    title: Code
    description: >-
      - Load thông tin mã lý do giữ lương.

      - Danh sách các lý do giữ lương hiển thị theo tiêu chí tìm kiếm.

      - Nếu không có tiêu chí tìm kiếm nào, thực hiện hiển thị toàn bộ danh sách
      lý do giữ lương đang có trên hệ thống theo thứ tự tăng dấn A - Z mã lý do
      giữ lương.
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    show_sort: true
  - code: shortName
    pinned: false
    title: Short Name
    description: '- Load thông tin tên lý do giữ lương. Không cho chỉnh sửa.'
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: name
    title: Long Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: companyName
    title: Company
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tags
      collection: field_types
    dragable: false
    show_sort: true
  - code: effectiveDate
    pinned: false
    title: Effective Date
    description: '- Load ghi chú. Không chỉnh sửa.'
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    show_sort: true
  - code: status
    pinned: false
    title: Status
    description: '- Load thông tin người tạo theo account đăng nhập.'
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: Boolean Tag
      collection: field_types
    show_sort: true
  - code: note
    pinned: false
    title: Note
    description: '- Load trạng thái hiệu lực. Không chỉnh sửa.'
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: updatedAt
    pinned: false
    title: Last Updated On
    description: '- Load thông tin ngày sửa dữ liệu lần cuối.'
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
    show_sort: true
    options__tabular__column_width: 11
  - code: updatedBy
    title: Last Updated By
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
mock_data: []
local_buttons: null
layout: layout-table
form_config:
  formTitle:
    edit: Edit Salary Plan
    create: Add New Salary Plan
    view: View detail Salary Plan
  historyHeaderTitle: '''View detail Salary Plan'''
  defaultValidateError: Required fields have not been filled in completely (fields marked with *)
  footer:
    update: true
    updatedOn: updatedAt
    updatedBy: updatedBy
  effectiveDateValidations:
    - sameDate
    - greaterThanLastest
  fields:
    - type: group
      n_cols: 2
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - type: text
          label: Code
          name: code
          formatType: code
          validators:
            - type: required
            - type: pattern
              args: ^[a-zA-Z0-9_.-]*$
              text: >-
                Code must only contain letters, numbers, hyphens, underscores,
                or dots.
            - type: maxLength
              args: '50'
              text: Code should not exceed 50 characters
          _disabled:
            transform: $.extend.formType = 'edit' or $.extend.formType = 'proceed' ? true
          col: 1
        - type: translation
          label: Short Name
          placeholder: Enter Short Name
          name: shortName
          validators:
            - type: required
            - type: maxLength
              args: '300'
              text: Short Name should not exceed 300 characters
          col: 1
    - type: text
      label: Code
      name: code
      _condition:
        transform: $.extend.formType = 'view'
    - type: translation
      label: Short Name
      placeholder: Input Short Name
      name: shortName
      _condition:
        transform: $.extend.formType = 'view'
    - type: translation
      label: Long Name
      placeholder: Enter Long Name
      name: name
      validators:
        - type: required
        - type: maxLength
          args: '500'
          text: Long Name should not exceed 500 characters
    - type: selectAll
      label: Company
      placeholder: Select Company
      isLazyLoad: true
      name: companyObj
      mode: multiple
      outputValue: value
      _options:
        transform: >-
          $companiesList($.extend.limit, $.extend.page, $.extend.search,
          $.fields.effectiveDate)
      _validateFn:
        transform: >-
          $exists($.value.code) ? ($companiesList($.extend.limit, $.extend.page,
          $.extend.search, $.fields.startDate, $.value.code)[0] ?
          $companiesList($.extend.limit, $.extend.page, $.extend.search,
          $.fields.startDate, $.value.code) : '_setSelectValueNull' )
    - type: group
      n_cols: 2
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - type: dateRange
          label: Effective Date
          name: effectiveDate
          mode: date-picker
          _value:
            transform: $.extend.formType='create' ? $now()
          placeholder: Select effective date
          setting:
            format: dd/MM/yyyy
            type: day
          validators:
            - type: required
        - type: radio
          label: Status
          name: status
          _value:
            transform: $.extend.formType = 'create' ? true
          radio:
            - label: Active
              value: true
            - label: Inactive
              value: false
    - type: dateRange
      label: Effective Date
      name: effectiveDateFrom
      mode: date-picker
      setting:
        type: day
        format: dd/MM/yyyy
      _condition:
        transform: $.extend.formType = 'view'
    - type: radio
      label: Status
      name: status
      radio:
        - label: Active
          value: true
        - label: Inactive
          value: false
      _condition:
        transform: $.extend.formType = 'view'
    - type: translationTextArea
      label: Note
      name: note
      placeholder: Enter Note
      textarea:
        autoSize:
          minRows: 3
        maxCharCount: 1000
      validators:
        - type: maxLength
          args: 1000
          text: note should not exceed 1000 characters
  sources:
    companiesList:
      uri: '"/api/companies/get-by"'
      method: GET
      queryTransform: >-
        { 'limit': $.limit, 'page': $.page, 'search': $.search, 'filter':
        [{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator': '$eq','value':
        $.effectiveDate},{'field':'code','operator': '$in','value': $.code}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id': $item.id,'code' :$item.code}}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
        - code
filter_config:
  fields:
    - type: text
      label: Code
      name: code
      placeholder: Enter Code
      labelType: type-grid
    - type: text
      label: Short Name
      name: shortName
      placeholder: Enter Short Name
      labelType: type-grid
    - type: text
      label: Long Name
      name: name
      placeholder: Enter Long Name
      labelType: type-grid
    - name: CompanyCodes
      label: Company
      type: selectAll
      labelType: type-grid
      mode: multiple
      placeholder: Select Company
      isLazyLoad: true
      _options:
        transform: $companiesList($.extend.limit, $.extend.page, $.extend.search)
    - type: dateRange
      label: Effective Date
      name: effectiveDateFrom
      setting:
        format: dd/MM/yyyy
      labelType: type-grid
    - name: status
      labelType: type-grid
      label: Status
      type: radio
      value: ''
      radio:
        - label: All
          value: ''
        - label: Active
          value: true
        - label: Inactive
          value: false
    - name: updatedBy
      label: Last Updated By
      type: text
      labelType: type-grid
      placeholder: Enter Last Updated By
    - name: updatedAt
      label: Last Updated On
      type: dateRange
      labelType: type-grid
      setting:
        mode: date
        format: dd/MM/yyyy
  filterMapping:
    - field: code
      operator: $cont
      valueField: code
    - field: shortName
      operator: $cont
      valueField: shortName
    - field: name
      operator: $cont
      valueField: name
    - field: CompanyCodes
      operator: $eq
      valueField: CompanyCodes.(value)
    - field: effectiveDateFrom
      operator: $between
      valueField: effectiveDateFrom
    - field: status
      operator: $eq
      valueField: status
    - field: createdBy
      operator: $in
      valueField: createdBy.(value)
    - field: createdAt
      operator: $between
      valueField: createdAt
    - field: updatedBy
      operator: $cont
      valueField: updatedBy
    - field: updatedAt
      operator: $between
      valueField: updatedAt
  sources:
    salartAdminPlans:
      uri: '"/api/salary-admin-plans"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.code, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    userList:
      uri: '"/api/internal-users"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.userName, 'value':
        $item.userName}})
      disabledCache: true
    companiesList:
      uri: '"/api/companies"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
layout_options:
  delete_multi_items: true
  custom_history_backend_url: /api/salary-admin-plans/:id/clone
  apply_delete_multi_items_to_delete_one: true
  custom_delete_body: '{''ids'': $map($.data,function($item){$item.id})[]}'
  reset_page_index_after_do_action:
    edit: true
  history_widget_header_options:
    duplicate: false
  show_detail_history: true
  toolTable:
    export: true
    adjustDisplay: true
  tool_table:
    - id: export
      icon: icon-download-simple
  show_dialog_form_save_add_button: true
  show_filter_results_message: true
  hide_action_row: true
  store_selected_items: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    title: Edit
    icon: icon-pencil-simple
    type: ghost-gray
  - id: delete
    title: Delete
    icon: icon-trash
    type: ghost-gray
backend_url: /api/salary-admin-plans
screen_name: null
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: companyCode
    defaultName: CompanyCode
screen_designs: []
function_specs: []
fields:
  - 101
  - 102
  - 103
  - 104
children: []
menu_item: null
