id: PR.FS.FR.016
status: draft
sort: 136
user_created: 60e9ad50-48e2-446b-9124-eef839c521ad
date_created: '2024-12-11T02:04:17.007Z'
user_updated: b403d5da-d684-45e4-ae6d-e25223baedc5
date_updated: '2025-07-14T03:02:51.381Z'
title: Other Income Group
requirement:
  time: 1747128487573
  blocks:
    - id: mWbsubuMTP
      type: paragraph
      data:
        text: '- Cho phép bộ phận nhân sự tập đoàn thêm các Ngạch lương.'
    - id: SXqXA90I8P
      type: paragraph
      data:
        text: >-
          - Danh mục được quản lý tập trung trên Tập đoàn, do bộ phận nhân sự
          của Tập đoàn khai báo và quản lý, các đơn vị chỉ được quyền khai thác,
          khô<PERSON> đượ<PERSON> quyền điều chỉnh, thay đổi. (<PERSON>r<PERSON><PERSON><PERSON> hợp ngoại lệ sẽ vẫn
          được phân quyền)
    - id: _V2hKTlB7w
      type: paragraph
      data:
        text: >-
          - Bộ phận nhân sự tập đoàn có thể điều chỉnh thông tin khi có thay
          đổi.
  version: 2.30.7
screen_design: null
module: PR
local_fields:
  - code: code
    pinned: true
    title: Code
    description: >-
      - Load thông tin mã lý do giữ lương. 

      - Danh sách các lý do giữ lương hiển thị theo tiêu chí tìm kiếm. 

      - Nếu không có tiêu chí tìm kiếm nào, thực hiện hiển thị toàn bộ danh sách
      lý do giữ lương đang có trên hệ thống theo thứ tự tăng dấn A - Z mã lý do
      giữ lương.
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    show_sort: true
  - code: shortName
    pinned: false
    title: Short Name
    description: '- Load thông tin tên lý do giữ lương. Không cho chỉnh sửa.'
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: longName
    title: Long Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: effectiveDate
    pinned: false
    title: Effective Date
    description: '- Load ghi chú. Không chỉnh sửa.'
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    show_sort: true
  - code: status
    pinned: false
    title: Status
    description: '- Load thông tin người tạo theo account đăng nhập.'
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: Boolean Tag
      collection: field_types
    show_sort: true
  - code: note
    pinned: false
    title: Note
    description: '- Load trạng thái hiệu lực. Không chỉnh sửa.'
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: updatedAt
    pinned: false
    title: Last Updated On
    description: '- Load thông tin ngày sửa dữ liệu lần cuối.'
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
    show_sort: true
    options__tabular__column_width: 11
  - code: updatedBy
    title: Last Updated By
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
mock_data: []
local_buttons: null
layout: layout-table
form_config:
  formTitle:
    edit: Edit Other Income Group
    create: Add New Other Income Group
    history: View detail Other Income Group
  defaultValidateError: Required fields have not been filled in completely (fields marked with *)
  footer:
    update: true
    updatedOn: updatedAt
    updatedBy: updatedBy
  effectiveDateValidations:
    - sameDate
    - greaterThanLastest
  fields:
    - type: group
      _n_cols:
        transform: '$.extend.formType = ''view'' ? 1 : 2'
      fields:
        - type: text
          label: Code
          formatType: code
          name: code
          validators:
            - type: required
            - type: pattern
              args: ^[a-zA-Z0-9_.-]*$
              text: >-
                Code must only contain letters, numbers, hyphens, underscores,
                or dots.
            - type: maxLength
              args: '50'
              text: Code should not exceed 50 characters
          _disabled:
            transform: $.extend.formType = 'edit' or $.extend.formType = 'proceed'
          col: 1
        - type: translation
          label: Short Name
          placeholder: Enter Short Name
          name: shortName
          validators:
            - type: required
            - type: maxLength
              args: '300'
              text: Short Name should not exceed 300 characters
          col: 1
    - type: translation
      label: Long Name
      placeholder: Enter Long Name
      name: longName
      validators:
        - type: required
        - type: maxLength
          args: '500'
          text: Long Name should not exceed 500 characters
    - type: group
      _n_cols:
        transform: '$.extend.formType = ''view'' ? 1 : 2'
      fields:
        - type: dateRange
          label: Effective Date
          name: effectiveDate
          mode: date-picker
          _value:
            transform: $.extend.formType='create' ? $now()
          _disabled:
            transform: $.extend.formType = 'proceed'
          placeholder: Select effective date
          setting:
            format: dd/MM/yyyy
            type: date
          validators:
            - type: required
        - type: radio
          label: Status
          name: status
          _value:
            transform: $.extend.formType = 'create' ? true
          radio:
            - label: Active
              value: true
            - label: Inactive
              value: false
    - type: translationTextArea
      label: Note
      name: note
      placeholder: Enter Note
      textarea:
        autoSize:
          minRows: 3
        maxCharCount: 1000
      validators:
        - type: maxLength
          args: 1000
          text: note should not exceed 1000 characters
filter_config:
  fields:
    - type: text
      label: Code
      name: code
      placeholder: Enter Code
      labelType: type-grid
    - type: text
      label: Short Name
      name: shortName
      placeholder: Enter Short Name
      labelType: type-grid
    - type: text
      label: Long Name
      name: longName
      placeholder: Enter Long Name
      labelType: type-grid
    - type: dateRange
      label: Effective Date
      name: effectiveDate
      setting:
        format: dd/MM/yyyy
      labelType: type-grid
    - name: status
      labelType: type-grid
      label: Status
      type: radio
      value: ''
      radio:
        - label: All
          value: ''
        - label: Active
          value: true
        - label: Inactive
          value: false
    - name: updatedBy
      label: Last Updated By
      type: text
      labelType: type-grid
      placeholder: Enter Last Updated By
    - name: updatedAt
      label: Last Updated On
      type: dateRange
      labelType: type-grid
      setting:
        mode: date
        format: dd/MM/yyyy HH:mm:ss
  filterMapping:
    - field: code
      operator: $cont
      valueField: code
    - field: shortName
      operator: $cont
      valueField: shortName
    - field: longName
      operator: $cont
      valueField: longName
    - field: effectiveDate
      operator: $between
      valueField: effectiveDate
    - field: status
      operator: $eq
      valueField: status
    - field: updatedBy
      operator: $cont
      valueField: updatedBy
    - field: updatedAt
      operator: $between
      valueField: updatedAt
  sources:
    otherIncomeGroupList:
      uri: '"/api/other-income-group"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.code, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
layout_options:
  delete_multi_items: true
  custom_history_backend_url: /api/other-income-group/:id/clone
  apply_delete_multi_items_to_delete_one: true
  custom_delete_body: '{''ids'': $map($.data,function($item){$item.id})[]}'
  toolTable:
    export: true
    adjustDisplay: true
  history_widget_header_options:
    duplicate: false
  tool_table:
    - id: export
      icon: icon-download-simple
  show_dialog_form_save_add_button: true
  show_filter_results_message: true
  hide_action_row: true
  store_selected_items: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    title: Edit
    icon: icon-pencil-simple
    type: ghost-gray
  - id: delete
    title: Delete
    icon: icon-trash
    type: ghost-gray
backend_url: /api/other-income-group
screen_name: null
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key: null
screen_designs: []
function_specs: []
fields:
  - 117
  - 118
  - 119
  - 120
children: []
menu_item: null
