id: PR.FS.FR.017
status: draft
sort: 136
user_created: 60e9ad50-48e2-446b-9124-eef839c521ad
date_created: '2024-12-11T08:40:59.711Z'
user_updated: b403d5da-d684-45e4-ae6d-e25223baedc5
date_updated: '2025-07-17T08:59:51.737Z'
title: Other Income Type
requirement:
  time: 1745889736991
  blocks:
    - id: mWbsubuMTP
      type: paragraph
      data:
        text: '- Cho phép bộ phận nhân sự tập đoàn thêm các Ngạch lương.'
    - id: SXqXA90I8P
      type: paragraph
      data:
        text: >-
          - Danh mục được quản lý tập trung trên Tập đo<PERSON>n, do bộ phận nhân sự
          của Tập đoàn khai báo và quản lý, các đơn vị chỉ được quyền khai thác,
          không đư<PERSON><PERSON> quyền điều chỉnh, thay đổi. (<PERSON>r<PERSON><PERSON><PERSON> hợp ngoại lệ sẽ vẫn
          được phân quyền)
    - id: _V2hKTlB7w
      type: paragraph
      data:
        text: >-
          - Bộ phận nhân sự tập đoàn có thể điều chỉnh thông tin khi có thay
          đổi.
  version: 2.30.7
screen_design: null
module: PR
local_fields:
  - code: code
    pinned: true
    title: Code
    description: >-
      - Load thông tin mã lý do giữ lương. 

      - Danh sách các lý do giữ lương hiển thị theo tiêu chí tìm kiếm. 

      - Nếu không có tiêu chí tìm kiếm nào, thực hiện hiển thị toàn bộ danh sách
      lý do giữ lương đang có trên hệ thống theo thứ tự tăng dấn A - Z mã lý do
      giữ lương.
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    show_sort: true
  - code: shortName
    pinned: false
    title: Short Name
    description: '- Load thông tin tên lý do giữ lương. Không cho chỉnh sửa.'
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: name
    title: Long Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: countryName
    title: Country
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: effectiveDate
    pinned: false
    title: Effective Date
    description: '- Load ghi chú. Không chỉnh sửa.'
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    show_sort: true
  - code: otherIncomeGroupName
    pinned: false
    title: Other Income Group
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: currencyName
    title: Currency
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: typeName
    title: 'Other Type '
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: entryTypeModel
    title: Entry Type
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: isDeductTaxIncome
    title: Deducted from Taxable Income
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: YesNo
      collection: field_types
    show_sort: true
  - code: status
    pinned: false
    title: Status
    description: '- Load thông tin người tạo theo account đăng nhập.'
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: Boolean Tag
      collection: field_types
    show_sort: true
  - code: note
    pinned: false
    title: Note
    description: '- Load trạng thái hiệu lực. Không chỉnh sửa.'
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: updatedAt
    pinned: false
    title: Last Updated On
    description: '- Load thông tin ngày sửa dữ liệu lần cuối.'
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
    show_sort: true
    options__tabular__column_width: 11
  - code: updatedBy
    title: Last Updated By
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
mock_data: []
local_buttons: null
layout: layout-table
form_config:
  formTitle:
    edit: Edit Other Income Type
    create: Add New Other Income Type
    view: Other Income Type Details
  historyHeaderTitle: '''Other Income Type Details'''
  defaultValidateError: Required fields have not been filled in completely (fields marked with *)
  footer:
    update: true
    updatedOn: updatedAt
    updatedBy: updatedBy
  effectiveDateValidations:
    - sameDate
    - greaterThanLastest
  fields:
    - type: group
      _n_cols:
        transform: '$.extend.formType = ''view'' ? 1 : 2'
      fields:
        - type: select
          label: Country
          name: countryCode
          placeholder: Select Country
          outputValue: value
          isLazyLoad: true
          _validateFn:
            transform: >-
              $.extend.defaultValue ? {'label':
              $.extend.defaultValue.countryName, 'value':
              $.extend.defaultValue.countryCode}
            params:
              updateLabelExistOption: true
          _select:
            transform: $countryList($.extend.limit, $.extend.page, $.extend.search)
        - type: text
          label: Code
          formatType: code
          name: code
          validators:
            - type: required
            - type: pattern
              args: ^[a-zA-Z0-9_.-]*$
              text: >-
                Code must only contain letters, numbers, hyphens, underscores,
                or dots.
            - type: maxLength
              args: '50'
              text: Code should not exceed 50 characters
          _disabled:
            transform: $.extend.formType = 'proceed' or $.extend.formType = 'edit'
    - type: group
      _n_cols:
        transform: '$.extend.formType = ''view'' ? 1 : 2'
      fields:
        - type: dateRange
          label: Effective Date
          name: effectiveDate
          mode: date-picker
          _value:
            transform: $.extend.formType='create' ? $now()
          _disabled:
            transform: $.extend.formType = 'proceed'
          placeholder: Select effective date
          setting:
            format: dd/MM/yyyy
            type: date
          validators:
            - type: required
        - type: radio
          label: Status
          name: status
          _value:
            transform: $.extend.formType = 'create' ? true
          radio:
            - label: Active
              value: true
            - label: Inactive
              value: false
        - type: translation
          label: Short Name
          placeholder: Enter Short Name
          name: shortName
          validators:
            - type: required
            - type: maxLength
              args: '300'
              text: Short Name should not exceed 300 characters
        - type: translation
          label: Long Name
          placeholder: Enter Long Name
          name: name
          validators:
            - type: required
            - type: maxLength
              args: '500'
              text: Long Name should not exceed 500 characters
    - type: group
      _n_cols:
        transform: '$.extend.formType = ''view'' ? 1 : 2'
      fields:
        - type: selectCustom
          label: Other Income Group
          isLazyLoad: true
          name: otherIncomeGroupObjNotView
          _condition:
            transform: $not($.extend.formType = 'view')
          placeholder: Select Other Income Group
          outputValue: value
          _value:
            transform: >-
              $not($isNilorEmpty($.extend.defaultValue.otherIncomeGroupCode)) ?
              $otherIncomeGroupList(1,1,'',null,$.extend.defaultValue.otherIncomeGroupCode)[0]
          _select:
            transform: >-
              $otherIncomeGroupList($.extend.limit, $.extend.page,
              $.extend.search, $.fields.effectiveDate,null)
          validators:
            - type: required
          actions:
            - view
          actionsConfig:
            view:
              formConfig:
                fields:
                  - name: code
                    label: Other Income Group
                    type: text
                  - type: dateRange
                    label: Effective Date
                    name: effectiveDate
                    mode: date-picker
                    setting:
                      format: dd/MM/yyyy
                      type: date
                  - name: shortName
                    label: Short Name
                    type: translation
                  - name: longName
                    label: Long Name
                    type: translation
                  - name: status
                    label: Status
                    type: radio
                    radio:
                      - label: Active
                        value: true
                      - label: Inactive
                        value: false
                  - name: note
                    label: Note
                    type: translation
        - type: selectCustom
          label: Other Income Group
          name: otherIncomeGroupObj
          _condition:
            transform: $.extend.formType = 'view'
          placeholder: Select Other Income Group
          inputValue: code
          outputValue: value
          _select:
            transform: >-
              $otherIncomeGroupList($.extend.limit, $.extend.page,
              $.extend.search,
              $.fields.effectiveDate,$.extend.defaultValue.otherIncomeGroupObj.value.code)
          validators:
            - type: required
          actions:
            - view
          actionsConfig:
            view:
              formConfig:
                fields:
                  - name: code
                    label: Other Income Group
                    type: text
                  - type: dateRange
                    label: Effective Date
                    name: effectiveDate
                    mode: date-picker
                    setting:
                      format: dd/MM/yyyy
                      type: date
                  - name: shortName
                    label: Short Name
                    type: translation
                  - name: longName
                    label: Long Name
                    type: translation
                  - name: status
                    label: Status
                    type: radio
                    radio:
                      - label: Active
                        value: true
                      - label: Inactive
                        value: false
                  - name: note
                    label: Note
                    type: translation
        - type: select
          label: Currency
          name: currencyCode
          placeholder: Select Currency
          outputValue: value
          _condition:
            transform: $not($.extend.formType = 'view')
          dependantField: $.fields.countryCode
          _value:
            transform: $.variables._selectedCurrencyByCountryCode
          _select:
            transform: $.variables._currencies
        - type: text
          _condition:
            transform: $.extend.formType = 'view'
          label: Currency
          _value:
            transform: >-
              $not($isNilorEmpty($.extend.defaultValue.currencyCode)) ?
              $.extend.defaultValue.currencyCode & ' (' &
              $.extend.defaultValue.currencyName & ')' : '--'
        - type: select
          label: Other Type
          name: typeCode
          placeholder: Select Other Type
          outputValue: value
          _condition:
            transform: $not($.extend.formType = 'view')
          validators:
            - type: required
          _select:
            transform: $otherTypeList()
        - type: select
          label: Other Type
          name: typeCode
          placeholder: Select Other Type
          outputValue: value
          _condition:
            transform: $.extend.formType = 'view'
          _select:
            transform: $otherTypeList()
        - type: radio
          label: Entry Type
          name: entryType
          value: ENTRYTP_00001
          outputValue: value
          placeholder: Select Entry Type
          _radio:
            transform: $.variables._entryTypeList
        - type: radio
          label: Deducted From Taxable Income
          name: isDeductTaxIncome
          _condition:
            transform: $.fields.typeCode = 'DD'
          value: true
          radio:
            - label: 'Yes'
              value: true
            - label: 'No'
              value: false
    - type: translationTextArea
      label: Note
      name: note
      placeholder: Enter Note
      textarea:
        autoSize:
          minRows: 3
        maxCharCount: 1000
      validators:
        - type: maxLength
          args: 1000
          text: note should not exceed 1000 characters
  sources:
    countryList:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    otherTypeList:
      uri: '"/api/picklists/OTHERTYPEINCOME/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params: []
    otherIncomeGroupList:
      uri: '"/api/other-income-group"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page, 'search': $.search, 'filter':
        [{'field':'code','operator':
        '$eq','value':$.code},{'field':'IsCombobox','operator':
        '$eq','value':true},{'field':'effectiveDate','operator':
        '$lte','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id': $item.id, 'code': $item.code},
        'additionalData': $item }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
        - code
    currencies:
      uri: '"/api/picklists/CURRENCY/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code, 'linkCatalogDataCode':
        $item.linkCatalogDataCode, 'effectiveDate': $item.effectiveDate}})[]
      disabledCache: true
    entryTypeList:
      uri: '"/api/picklists/ENTRYTYPE/values"'
      method: GET
      queryTransform: '{''sort'':[{''field'' : ''name''  , ''order'': ''DESC''  }] }'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
  variables:
    _entryTypeList:
      transform: $entryTypeList()
    _currencies:
      transform: '$.extend.formType !=''view'' ? $currencies() : []'
    _currencyByCountryCode:
      transform: >-
        $filter($.variables._currencies, function($item)
        {$item.linkCatalogDataCode = $.fields.countryCode})[]
    _currencyListAfterFilter:
      transform: >-
        $count($.variables._currencyByCountryCode) > 0 ?
        $.variables._currencyByCountryCode : $.variables._currencies
    _selectedCurrencyByCountryCode:
      transform: >-
        ($.variables._currencyByCountryCode;$sort($.variables._currencyByCountryCode,
        function($l, $r) {$toMillis($l.effectiveDate) >
        $toMillis($r.effectiveDate) })[0].value)
filter_config:
  fields:
    - type: text
      label: Code
      name: code
      placeholder: Enter Code
      labelType: type-grid
    - type: text
      label: Short Name
      name: shortName
      placeholder: Enter Short Name
      labelType: type-grid
    - type: text
      label: Long Name
      name: name
      placeholder: Enter Long Name
      labelType: type-grid
    - type: selectAll
      label: Country
      name: countryCode
      placeholder: Select Country
      labelType: type-grid
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $countryList($.extend.limit, $.extend.page, $.extend.search)
    - type: dateRange
      label: Effective Date
      name: effectiveDate
      labelType: type-grid
      setting:
        format: dd/MM/yyyy
    - type: selectAll
      label: Other Income Group
      name: otherIncomeGroupCode
      placeholder: Select Other Income Group
      labelType: type-grid
      mode: multiple
      _options:
        transform: $otherIncomeGroupList($.extend.limit, $.extend.page, $.extend.search)
      isLazyLoad: 'true'
    - name: currencyCode
      label: Currency
      labelType: type-grid
      isLazyLoad: true
      type: selectAll
      mode: multiple
      placeholder: Select Type
      _options:
        transform: $currenciesList($.extend.limit, $.extend.page, $.extend.search)
    - name: typeCode
      label: Other Type
      labelType: type-grid
      isLazyLoad: true
      type: selectAll
      mode: multiple
      placeholder: Select Other Type
      _options:
        transform: $otherTypeList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      label: Entry Type
      name: entryType
      labelType: type-grid
      mode: multiple
      isLazyLoad: true
      placeholder: Select Entry Type
      _options:
        transform: $entryTypeList($.extend.limit, $.extend.page, $.extend.search)
    - type: radio
      label: Deducted from Taxable Income
      value: ''
      labelType: type-grid
      name: isDeductTaxIncome
      radio:
        - label: All
          value: ''
        - label: 'Yes'
          value: true
        - label: 'No'
          value: false
    - name: status
      labelType: type-grid
      label: Status
      type: radio
      value: ''
      radio:
        - label: All
          value: ''
        - label: Active
          value: true
        - label: Inactive
          value: false
    - name: updatedBy
      label: Last Updated By
      type: text
      labelType: type-grid
      placeholder: Enter Last Updated By
    - name: updatedAt
      label: Last Updated On
      type: dateRange
      labelType: type-grid
      setting:
        mode: date
        format: dd/MM/yyyy
  filterMapping:
    - field: code
      operator: $cont
      valueField: code
    - field: shortName
      operator: $cont
      valueField: shortName
    - field: name
      operator: $cont
      valueField: name
    - field: countryCode
      operator: $in
      valueField: countryCode.(value)
    - field: otherIncomeGroupCode
      operator: $in
      valueField: otherIncomeGroupCode.(value)
    - field: currencyCode
      operator: $in
      valueField: currencyCode.(value)
    - field: typeCode
      operator: $in
      valueField: typeCode.(value)
    - field: entryType
      operator: $in
      valueField: entryType.(value)
    - field: isDeductTaxIncome
      operator: $eq
      valueField: isDeductTaxIncome
    - field: effectiveDate
      operator: $between
      valueField: effectiveDate
    - field: status
      operator: $eq
      valueField: status
    - field: updatedBy
      operator: $cont
      valueField: updatedBy
    - field: updatedAt
      operator: $between
      valueField: updatedAt
  sources:
    otherTypeList:
      uri: '"/api/picklists/OTHERTYPEINCOME/values/pagination"'
      method: GET
      queryTransform: '{''limit'': $.limit, ''page'': $.page, ''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    entryTypeList:
      uri: '"/api/picklists/ENTRYTYPE/values/pagination"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default , 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    otherIncomeList:
      uri: '"/api/other-incomes"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.code, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    otherIncomeGroupList:
      uri: '"/api/other-income-group"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    currenciesList:
      uri: '"/api/picklists/CURRENCY/values/pagination"'
      method: GET
      queryTransform: '{''limit'': $.limit, ''page'': $.page, ''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    countryList:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})
      disabledCache: true
      params:
        - limit
        - page
        - search
layout_options:
  delete_multi_items: true
  show_detail_history: true
  apply_delete_multi_items_to_delete_one: true
  custom_delete_body: '{''ids'': $map($.data,function($item){$item.id})[]}'
  custom_history_backend_url: /api/other-incomes/:id/clone
  reset_page_index_after_do_action:
    edit: true
  toolTable:
    export: true
    adjustDisplay: true
  history_widget_header_options:
    duplicate: false
  tool_table:
    - id: export
      icon: icon-download-simple
  show_dialog_form_save_add_button: true
  show_filter_results_message: true
  hide_action_row: true
  store_selected_items: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    title: Edit
    icon: icon-pencil-simple
    type: ghost-gray
  - id: delete
    title: Delete
    icon: icon-trash
    type: ghost-gray
backend_url: /api/other-incomes
screen_name: null
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: countryCode
    defaultName: CountryCode
screen_designs: []
function_specs: []
fields:
  - 121
  - 122
  - 123
  - 124
children: []
menu_item: null
