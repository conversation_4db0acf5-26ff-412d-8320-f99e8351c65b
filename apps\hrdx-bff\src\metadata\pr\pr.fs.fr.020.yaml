id: PR.FS.FR.020
status: draft
sort: 175
user_created: 56e71b91-dd39-4ebc-afd1-b4f00f38d4a5
date_created: '2024-07-26T06:14:16.957Z'
user_updated: b403d5da-d684-45e4-ae6d-e25223baedc5
date_updated: '2025-06-09T08:55:18.949Z'
title: Payroll Period
requirement:
  time: 1721893154334
  blocks:
    - id: 3XvJyzBn8e
      type: paragraph
      data:
        text: ' Cho phép bộ phận nhân sự tập đoàn khai báo dữ liệu <PERSON> mục <PERSON> l<PERSON>ơ<PERSON> theo quy định, và đư<PERSON>c phân quyền màn hình.'
    - id: QtreL5Wmyq
      type: paragraph
      data:
        text: >-
          Từ dữ liệu <PERSON> l<PERSON> này, người dùng sẽ tạo các <PERSON>/chi lương
          tương ứng theo.
  version: 2.29.1
screen_design: eaab8f24-3061-4f2d-a87c-bf12a1b52182
module: PR
local_fields:
  - code: code
    title: Payroll Period Code
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    pinned: true
    show_sort: true
    options__tabular__column_width: 12
  - code: shortName
    title: Short Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: longName
    title: Long Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: month
    title: Month
    data_type:
      key: MM/yyyy
      collection: data_types
    display_type:
      key: MM/YYYY
      collection: field_types
    show_sort: true
  - code: frequencyName
    title: Frequency
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
    pinned: false
  - code: startDay
    title: Start Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    show_sort: true
  - code: endDay
    title: End Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    show_sort: true
  - code: status
    title: Status
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Boolean Tag
      collection: field_types
    show_sort: true
  - code: note
    title: Note
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: updatedBy
    title: Last Updated By
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: updatedAt
    title: Last Updated On
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
    show_sort: true
mock_data:
  - code: '00000001'
    shortName:
      default: Ky luong 1
      vietnamese: Kỳ lương 1
      english: Ky luong 1
    longName:
      default: Ky luong 1
      vietnamese: Kỳ lương 1
      english: Ky luong 1
    month: 2024-08
    frequency: Monthly
    startDay: '2024-06-01'
    endDay: '2024-06-30'
    status: Active
    note:
      default: ''
      vietnamese: ''
      english: ''
    creator: Ducnm54
    createTime: '2024-06-01 00:00:00'
    lastEditor: Ducnm54
    lastEditTime: '2024-06-01 00:00:00'
  - code: '00000002'
    shortName:
      default: Ky luong 2
      vietnamese: Kỳ lương 2
      english: Ky luong 2
    longName:
      default: Ky luong 2
      vietnamese: Kỳ lương 2
      english: Ky luong 2
    month: ''
    frequency: Weekly
    startDay: '2024-07-01'
    endDay: '2024-07-15'
    status: Active
    note:
      default: ''
      vietnamese: ''
      english: ''
    creator: Ducnm54
    createTime: '2024-07-01 00:00:00'
    lastEditor: Ducnm54
    lastEditTime: '2024-07-01 00:00:00'
local_buttons:
  - code: C1
    name: Test
    description: Test
    reference:
      key: PR.FR.001
      collection: function_specs
    note: Test Note
layout: layout-table
form_config:
  formTitle:
    create: Add new Payroll period
  fields:
    - type: text
      name: code
      disabled: true
      placeholder: Code
      validators:
        - type: required
      label: Payroll period code
      _condition:
        transform: $not($.extend.formType = 'view')
    - type: text
      name: code
      _value:
        transform: $.extend.formType = 'create' ? 'automatic'
      label: Payroll period code
      readOnly: true
      _condition:
        transform: $.extend.formType = 'view'
    - type: translation
      name: shortName
      label: Short Name
      placeholder: Enter short name
      _condition:
        transform: $not($.extend.formType = 'view')
      validators:
        - type: required
        - type: maxLength
          args: '300'
          text: Short Name should not exceed 300 characters
    - type: translation
      name: shortName
      label: Short Name
      placeholder: Enter short name
      readOnly: true
      _condition:
        transform: $.extend.formType = 'view'
    - type: translation
      name: longName
      label: Long Name
      placeholder: Enter Long Name
      _condition:
        transform: $not($.extend.formType = 'view')
      validators:
        - type: required
        - type: maxLength
          args: '500'
          text: Full Name should not exceed 500 characters
    - type: translation
      name: longName
      label: Long Name
      readOnly: true
      _condition:
        transform: $.extend.formType = 'view'
    - type: group
      required: true
      n_cols: 2
      _condition:
        transform: $not($.extend.formType = 'view')
      validators:
        - type: required
      fields:
        - type: select
          name: frequency
          label: Frequency
          outputValue: value
          _select:
            transform: $frequencyList()
          validators:
            - type: required
          placeholder: Select Frequency
        - type: dateRange
          name: month
          label: Month
          placeholder: MM/yyyy
          mode: date-picker
          setting:
            type: month
            format: MM-yyyy
            disabledDate:
              value: 01/01/1970
              operator: $lte
    - type: text
      name: frequencyName
      label: Frequency
      readOnly: true
      _condition:
        transform: $.extend.formType = 'view'
    - type: dateRange
      name: month
      label: Month
      mode: date-picker
      setting:
        type: month
        format: MM-yyyy
      _condition:
        transform: $.extend.formType = 'view'
    - type: group
      label: Start date - End date
      required: true
      n_cols: 2
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - type: dateRange
          name: startDay
          placeholder: dd/MM/yyyy
          mode: date-picker
          setting:
            type: date
            format: dd/MM/yyyy
            disabledDate:
              value: 01/01/1970
              operator: $lte
          _value:
            transform: $GetBoundaryDate($.fields.month, 'month', 'start')
          validators:
            - type: required
        - type: dateRange
          name: endDay
          mode: date-picker
          placeholder: dd/MM/yyyy
          setting:
            type: date
            format: dd/MM/yyyy
            disabledDate:
              value: 01/01/1970
              operator: $lte
          _value:
            transform: $GetBoundaryDate($.fields.month, 'month', 'end')
          validators:
            - type: required
            - type: ppx-custom
              args:
                transform: >-
                  $exists($.fields.endDay) and
                  $DateDiff($DateFormat($.fields.endDay, 'yyyy-MM-DD'),
                  $DateFormat($.fields.startDay, 'yyyy-MM-DD'), 'd') < 1
              text: End date must be greater than start date
      validators:
        - type: required
    - type: dateRange
      label: Start Date
      name: startDay
      mode: date-picker
      setting:
        type: date
        format: dd/MM/yyyy
      _condition:
        transform: $.extend.formType = 'view'
    - type: dateRange
      name: endDay
      label: End Date
      mode: date-picker
      setting:
        type: date
        format: dd/MM/yyyy
      _condition:
        transform: $.extend.formType = 'view'
    - type: radio
      name: status
      label: Status
      _value:
        transform: $.extend.formType = 'create' ? true
      radio:
        - label: Active
          value: true
        - label: Inactive
          value: false
    - type: translationTextArea
      name: note
      label: Note
      placeholder: Enter Note
      validators:
        - type: maxLength
          args: '1000'
          text: Note should not exceed 1000 characters
      textarea:
        autoSize:
          minRows: 3
          maxRows: 5
        maxCharCount: 1000
  sources:
    frequencyList:
      uri: '"/api/picklists/SALARYFREQUENCY/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
  footer:
    create: true
    update: true
    createdOn: createdAt
    updatedOn: updatedAt
    createdBy: createdBy
    updatedBy: updatedBy
filter_config:
  fields:
    - type: text
      name: code
      labelType: type-grid
      placeholder: Enter Payroll Period Code
      label: Payroll Period Code
    - type: text
      name: shortName
      label: Short Name
      labelType: type-grid
      placeholder: Enter Short Name
    - type: text
      name: longName
      label: Long Name
      labelType: type-grid
      placeholder: Enter Long Name
    - type: selectAll
      name: frequency
      label: Frequency
      _options:
        transform: $frequencyList()
      labelType: type-grid
      placeholder: Select Frequency
    - type: dateRange
      name: month
      label: Month
      mode: date-picker
      labelType: type-grid
      setting:
        type: month
        format: MM/yyyy
      placeholder: DD/MM/YYYY
    - name: startDay
      label: Start Day
      type: dateRange
      labelType: type-grid
      setting:
        mode: date
        format: dd/MM/yyyy
    - name: endDay
      label: End Day
      type: dateRange
      labelType: type-grid
      setting:
        mode: date
        format: dd/MM/yyyy
    - type: radio
      name: status
      label: Status
      value: ''
      labelType: type-grid
      radio:
        - label: All
          value: ''
        - label: Active
          value: true
        - label: Inactive
          value: false
    - type: text
      name: note
      label: Note
      labelType: type-grid
      placeholder: Enter Note
    - name: updatedBy
      label: Last Updated By
      type: text
      labelType: type-grid
      placeholder: Enter Last Updated By
    - name: updatedAt
      label: Last Updated On
      type: dateRange
      labelType: type-grid
      setting:
        mode: date
        format: dd/MM/yyyy
  filterMapping:
    - field: code
      operator: $cont
      valueField: code
    - field: shortName
      operator: $cont
      valueField: shortName
    - field: longName
      operator: $cont
      valueField: longName
    - field: frequency
      operator: $eq
      valueField: frequency.(value)
    - field: month
      operator: $eq
      valueField: month
    - field: startDay
      operator: $between
      valueField: startDay
    - field: endDay
      operator: $between
      valueField: endDay
    - field: status
      operator: $eq
      valueField: status
    - field: note
      operator: $cont
      valueField: note
    - field: updatedBy
      operator: $cont
      valueField: updatedBy
    - field: updatedAt
      operator: $between
      valueField: updatedAt
  sources:
    frequencyList:
      uri: '"/api/picklists/SALARYFREQUENCY/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
  variables:
    _longNameList:
      transform: $frequencyList()
layout_options:
  show_detail_history: false
  delete_multi_items: true
  show_dialog_form_save_add_button: true
  apply_delete_multi_items_to_delete_one: true
  custom_delete_body: '{''ids'': $map($.data,function($item){$item.id})[]}'
  reset_page_index_after_do_action:
    edit: true
  tool_table:
    - id: export
      icon: icon-upload-simple-bold
  show_filter_results_message: true
  store_selected_items: true
  show_dialog_duplicate_button: false
  hide_action_row: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    icon: icon-pencil-simple
    type: ghost-gray
  - id: delete
    icon: icon-trash
    type: ghost-gray
backend_url: /api/payroll-periods
screen_name: payroll-periods
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key: null
screen_designs: []
function_specs: []
fields:
  - 70
  - 71
  - 72
  - 73
  - 74
  - 75
  - 76
  - 77
children: []
menu_item:
  title: Payroll Period
  parent:
    title: PR Setting
