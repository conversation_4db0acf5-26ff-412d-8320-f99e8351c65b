id: PR.FS.FR.021
status: draft
sort: 266
user_created: 56e71b91-dd39-4ebc-afd1-b4f00f38d4a5
date_created: '2024-07-26T06:16:11.950Z'
user_updated: b403d5da-d684-45e4-ae6d-e25223baedc5
date_updated: '2025-07-24T04:01:26.175Z'
title: Payroll Sub-Period
requirement:
  time: 1749436127476
  blocks:
    - id: w767umcbt6
      type: paragraph
      data:
        text: >-
          - B<PERSON> phận nhân sự tập đoàn có thể điều chỉnh thông tin khi có thay
          đổi.
    - id: Kb7HiY7loT
      type: paragraph
      data:
        text: >-
          - <PERSON><PERSON> sách Thiết lập Kỳ chi tiết hiển thị theo tiêu chí tìm kiếm. Nếu
          không có tiêu chí tìm kiếm nào, thực hiện hiển thị toàn bộ danh sách
          Thiết lập <PERSON>ỳ chi tiết đang có trên hệ thống theo thứ tự từ mới đến cũ.
    - id: YBr1Bp5_lb
      type: paragraph
      data:
        text: >-
          - Hệ thống kiểm tra phân quyền để hiển thị các chức năng tương ứng
          (Sửa, Xóa,..).
  version: 2.30.7
screen_design: null
module: PR
local_fields:
  - code: code
    title: Payroll Sub-period Code
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    pinned: true
    show_sort: true
    options__tabular__column_width: 15
  - code: shortName
    title: Short Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: longName
    title: Long Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: countryName
    title: Country
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: companyName
    title: Company
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: legalEntityName
    title: Legal Entity
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: payGroupName
    title: Pay Group
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: payrollPeriodName
    title: Payroll Period
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: startDate
    title: Start Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    show_sort: true
  - code: endDate
    title: End Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    show_sort: true
  - code: paymentDay
    title: Payment Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    show_sort: true
  - code: elementGroupName
    title: Element Group
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: salaryTypeName
    title: Element Type
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: currencyName
    title: Currency
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: prExtendPeriodName
    title: PR Extend Period
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: tsPeriodName
    title: TS Period
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: note
    title: Note
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: updatedBy
    title: Last Updated By
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: updatedAt
    title: Last Updated On
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
    show_sort: true
mock_data:
  - id: '7222856086378471424'
    payrollSubPeriod: '202407'
    payrollSubPeriodCode: '00000003'
    effectiveDate: null
    status: false
    shortName:
      default: string
      vietnamese: string
      english: string
    longName:
      default: string
      vietnamese: string
      english: string
    startDate: '1970-01-20T22:18:01.566Z'
    endDate: '1970-01-20T22:20:54.366Z'
    paymentDay: '1970-01-01T00:00:00.000Z'
    company: string
    country: '00000003'
    legalEntity: string
    payGroup: string
    elementGroup: string
    salaryType: string
    note: string
    createdBy: null
    createdAt: '1970-01-20T22:21:03.086Z'
    updatedBy: null
    updatedAt: '1970-01-20T22:21:03.086Z'
  - id: '7222820160067227648'
    payrollSubPeriod: '202406'
    payrollSubPeriodCode: '00000002'
    effectiveDate: null
    status: false
    shortName:
      default: string
      vietnamese: string
      english: string
    longName:
      default: string
      vietnamese: string
      english: string
    startDate: '1970-01-20T22:18:01.566Z'
    endDate: '1970-01-20T22:20:54.366Z'
    paymentDay: '1970-01-01T00:00:00.000Z'
    company: string
    country: '00000002'
    legalEntity: string
    payGroup: string
    elementGroup: string
    salaryType: string
    note: string
    createdBy: null
    createdAt: '1970-01-20T22:20:54.521Z'
    updatedBy: null
    updatedAt: '1970-01-20T22:20:54.521Z'
  - id: '7222819716301271040'
    payrollSubPeriod: string
    payrollSubPeriodCode: '00000001'
    effectiveDate: null
    status: false
    shortName:
      default: string
      vietnamese: string
      english: string
    longName:
      default: string
      vietnamese: string
      english: string
    startDate: '1970-01-20T22:18:01.566Z'
    endDate: '1970-01-20T22:20:54.366Z'
    paymentDay: '1970-01-01T00:00:00.000Z'
    company: string
    country: '00000001'
    legalEntity: string
    payGroup: string
    elementGroup: string
    salaryType: string
    note: string
    createdBy: null
    createdAt: '1970-01-20T22:20:54.417Z'
    updatedBy: null
    updatedAt: '1970-01-20T22:20:54.417Z'
local_buttons: null
layout: layout-table
form_config:
  formSize:
    create: large
    edit: large
    proceed: large
  formTitle:
    create: Add New Payroll Sub-Period
    edit: Edit Payroll Sub-Period
    view: Payroll Sub-Period Detail
  defaultValidateError: Required fields have not been filled in completely (fields marked with *)
  fields:
    - type: group
      _n_cols:
        transform: '$.extend.formType = ''view'' ? 1 : 2'
      fields:
        - type: text
          name: countryName
          label: Country
          outputValue: value
          _condition:
            transform: $.extend.formType = 'view'
        - type: select
          name: country
          label: Country
          clearFieldsAfterChange:
            - currency
          outputValue: value
          _value:
            transform: $.extend.formType = 'create' ? $nationsList(1, 1, '', 'VNM')[0]
          isLazyLoad: true
          _select:
            transform: $nationsList($.extend.limit, $.extend.page, $.extend.search)
          _validateFn:
            transform: >-
              $.extend.defaultValue ? {'label':
              $.extend.defaultValue.countryName, 'value':
              $.extend.defaultValue.country}
            params:
              updateLabelExistOption: true
          placeholder: Select Country
          _condition:
            transform: $.extend.formType = 'create' or $.extend.formType = 'edit'
          col: 1
        - type: text
          name: payrollPeriodName
          label: Payroll Period
          placeholder: Select Payroll Period
          _condition:
            transform: $.extend.formType = 'view'
          _value:
            transform: >-
              $.extend.defaultValue.payrollPeriodName ?
              $.extend.defaultValue.payrollPeriodName & ' (' &
              $.extend.defaultValue.payrollPeriodCode & ') ': null
        - type: select
          name: payrollPeriodItem
          label: Payroll Period
          isLazyLoad: true
          _select:
            transform: $payrollPeriodList($.extend.limit, $.extend.page, $.extend.search)
          placeholder: Select Payroll Period
          validators:
            - type: required
          col: 1
          _condition:
            transform: $.extend.formType = 'create' or $.extend.formType = 'edit'
        - type: text
          name: payrollPeriodCode
          dependantFields: $.fields.payrollPeriodItem
          _value:
            transform: $.fields.payrollPeriodItem.value
          label: Payroll Period
          unvisible: true
        - type: text
          name: code
          disabled: true
          label: Payroll Sub-Period Code
          placeholder: Automatic
          _condition:
            transform: $.extend.formType = 'view'
        - type: text
          name: code
          disabled: true
          validators:
            - type: required
          label: Payroll Sub-Period Code
          placeholder: Automatic
          col: 2
          _condition:
            transform: $.extend.formType = 'create' or $.extend.formType = 'edit'
        - type: translation
          name: shortName
          label: Short Name
          placeholder: Enter Short Name
          _condition:
            transform: $.extend.formType = 'view'
        - type: translation
          name: shortName
          label: Short Name
          col: 2
          placeholder: Enter Short Name
          validators:
            - type: required
            - type: maxLength
              args: '300'
              text: Short Name should not exceed 300 characters
          _condition:
            transform: $.extend.formType = 'create' or $.extend.formType = 'edit'
        - type: translation
          name: longName
          label: Long Name
          placeholder: Enter Long Name
          _condition:
            transform: $.extend.formType = 'view'
        - type: translation
          name: longName
          col: 2
          label: Long Name
          placeholder: Enter Long Name
          validators:
            - type: required
            - type: maxLength
              args: '500'
              text: Long Name should not exceed 500 characters
          _condition:
            transform: $.extend.formType = 'create' or $.extend.formType = 'edit'
        - type: text
          name: companyName
          label: Company
          _condition:
            transform: $.extend.formType = 'view'
          _value:
            transform: >-
              $.extend.defaultValue.companyName ?
              $.extend.defaultValue.companyName & ' (' &
              $.extend.defaultValue.company & ') ': null
        - type: select
          name: companyObj
          label: Company
          outputValue: value
          isLazyLoad: true
          clearFieldsAfterChange:
            - legalEntityObj
            - payGroupCode
          _select:
            transform: >-
              $companyList($.extend.limit, $.extend.page,
              $.extend.search,$.fields.startDate,$.fields.endDate)
          col: 1
          placeholder: Select Company
          validators:
            - type: required
          _condition:
            transform: $.extend.formType = 'create' or $.extend.formType = 'edit'
        - type: text
          name: legalEntity
          outputValue: value
          label: Legal Entity
          _condition:
            transform: $.extend.formType = 'view'
          _value:
            transform: >-
              $.extend.defaultValue.legalEntityName ?
              $.extend.defaultValue.legalEntityName & ' (' &
              $.extend.defaultValue.legalEntityCode & ') ': null
        - type: select
          name: legalEntityObj
          outputValue: value
          col: 1
          label: Legal Entity
          isLazyLoad: true
          _select:
            transform: >-
              $legalEntityList($.extend.limit, $.extend.page, $.extend.search,
              $.fields.companyObj.code, $.fields.startDate,$.fields.endDate)
          _disabled:
            transform: >-
              $boolean($.fields.companyObj.code) = true ?
              $.variables._checkCompanyType != 'LegalEntity' : false
          placeholder: Select Legal Entity
          _condition:
            transform: $.extend.formType = 'create' or $.extend.formType = 'edit'
        - type: text
          name: payGroupCode
          outputValue: value
          label: Pay Group
          _condition:
            transform: $.extend.formType = 'view'
          _value:
            transform: >-
              $.extend.defaultValue.payGroupName ?
              $.extend.defaultValue.payGroupName & ' (' &
              $.extend.defaultValue.payGroupCodeView & ') ' : null
        - type: select
          name: payGroupCode
          col: 1
          outputValue: value
          isLazyLoad: true
          label: Pay Group
          validators:
            - type: required
          _select:
            transform: >-
              $payGroup($.extend.limit, $.extend.page,
              $.extend.search,$.fields.companyObj.code,$.fields.startDate,$.fields.endDate)
          _disabled:
            transform: >-
              $boolean($.fields.companyObj.code) = true ?
              $.variables._checkCompanyType != 'PayGroup' : false
          placeholder: Select Pay Group
          _condition:
            transform: $.extend.formType = 'create' or $.extend.formType = 'edit'
        - type: dateRange
          name: paymentDay
          label: Payment Date
          mode: date-picker
          setting:
            type: date
            format: dd/MM/yyyy
          _condition:
            transform: $.extend.formType = 'view'
        - type: dateRange
          name: paymentDay
          label: Payment Date
          col: 1
          mode: date-picker
          setting:
            type: date
            format: dd/MM/yyyy
          validators:
            - type: required
            - type: ppx-custom
              args:
                transform: >-
                  $exists($.fields.paymentDay) and
                  $DateDiff($DateFormat($.fields.paymentDay, 'yyyy-MM-DD'),
                  $DateFormat($.fields.startDate, 'yyyy-MM-DD'), 'd') < 1
              text: Payment date must be greater than start date
          placeholder: Select Payment Day
          _condition:
            transform: $.extend.formType = 'create' or $.extend.formType = 'edit'
        - type: dateRange
          name: startDate
          label: Start Date
          mode: date-picker
          setting:
            type: day
            format: dd/MM/yyyy
          _condition:
            transform: $.extend.formType = 'view'
        - type: dateRange
          name: startDate
          col: 1
          label: Start Date
          dependantFields: $.fields.payrollPeriodItem
          mode: date-picker
          _disabled:
            transform: 'true'
          _value:
            transform: $.fields.payrollPeriodItem.startDay
          setting:
            type: day
            format: dd/MM/yyyy
          validators:
            - type: required
          placeholder: Select Start Date
          _condition:
            transform: $.extend.formType = 'create' or $.extend.formType = 'edit'
        - type: dateRange
          name: endDate
          label: End Date
          mode: date-picker
          setting:
            type: date
            format: dd/MM/yyyy
          _condition:
            transform: $.extend.formType = 'view'
        - type: dateRange
          name: endDate
          label: End Date
          col: 1
          mode: date-picker
          _disabled:
            transform: 'true'
          dependantFields: $.fields.payrollPeriodItem
          _value:
            transform: $.fields.payrollPeriodItem.endDay
          setting:
            type: date
            format: dd/MM/yyyy
          validators:
            - type: required
            - type: ppx-custom
              args:
                transform: >-
                  $exists($.fields.endDate) and
                  $DateDiff($DateFormat($.fields.endDate, 'yyyy-MM-DD'),
                  $DateFormat($.fields.startDate, 'yyyy-MM-DD'), 'd') < 1
              text: End date must be greater than start date
          placeholder: Select End Date
          _condition:
            transform: $.extend.formType = 'create' or $.extend.formType = 'edit'
        - type: text
          name: elementGroupName
          label: Element Group
          _condition:
            transform: $.extend.formType = 'view'
        - type: select
          name: elementGroup
          col: 1
          outputValue: value
          label: Element Group
          _value:
            transform: >-
              $.variables._selectedElementType ?
              $.variables._selectedElementType.elementGroup
          _select:
            transform: >-
              $boolean($.fields.salaryType) = true ?
              $.variables._filterElementGroup : $.variables._elementGroup
          placeholder: Select Element Group
          validators:
            - type: required
          _condition:
            transform: $.extend.formType = 'create' or $.extend.formType = 'edit'
        - type: text
          name: salaryTypeName
          outputValue: value
          label: Element Type
          _condition:
            transform: $.extend.formType = 'view'
        - type: select
          col: 1
          name: salaryType
          outputValue: value
          _select:
            transform: >-
              $boolean($.fields.elementGroup) = true ? 
              $.variables._filterElementType : $.variables._elementType  
          label: Element Type
          placeholder: Select Salary Type
          validators:
            - type: required
          _condition:
            transform: $.extend.formType = 'create' or $.extend.formType = 'edit'
        - type: text
          name: currencyName
          label: Currency
          _condition:
            transform: $.extend.formType = 'view'
        - type: select
          name: currency
          outputValue: value
          col: 2
          _select:
            transform: $.variables._currencyList
          _value:
            transform: >-
              $isNilorEmpty($.fields.currency) ?
              $filter($.variables._currencyList,function($v,$i,$a)
              {$v.linkCatalogDataCode = $.fields.country}).value
          label: Currency
          placeholder: Select Currency
          validators:
            - type: required
          _condition:
            transform: $.extend.formType = 'create' or $.extend.formType = 'edit'
        - type: text
          name: prExtendPeriodName
          outputValue: value
          label: PR Extend Period
          _condition:
            transform: >-
              $.extend.defaultValue.salaryType = 'ET_001.2' and
              $.extend.formType = 'view'
          readOnly: true
          _value:
            transform: >-
              $.extend.defaultValue.prExtendPeriodName ?
              $.extend.defaultValue.prExtendPeriodName & ' (' &
              $.extend.defaultValue.prExtendPeriodCode & ') ': null
        - type: selectCustom
          isLazyLoad: true
          name: prExtendPeriodTable
          col: 2
          _select:
            transform: >-
              $PRExtendPeriodList($.extend.filter.company,$.extend.filter.legalEntity,
              $.extend.filter.payGroup
              ,$.extend.filter.payrollPeriodCode,$.extend.filter.startDateRange,$.extend.filter.endDateRange)
          _value:
            transform: >-
              $filter($.variables._PRExtendPeriodList ,
              function($v){$v.value.code = $.fields.prExtendPeriodCode})
          actions:
            - id: search
              title: Search for PR extend Period
          label: PR Extend Period
          placeholder: Select
          validators:
            - type: required
          _condition:
            transform: >-
              $.fields.salaryType = 'ET_001.2' and $not($.extend.formType =
              'view')
          actionsConfig:
            search:
              formConfig:
                fields:
                  - type: group
                    collapsed: false
                    key: prExtendPeriodTableKey
                    disableEventCollapse: true
                    n_cols: 3
                    fields:
                      - type: select
                        label: Company
                        name: company
                        key: companyCode
                        placeholder: Select Company
                        outputValue: value
                        _select:
                          transform: $companyList()
                      - type: select
                        label: Legal Entity
                        name: legalEntity
                        key: legalEntityCode
                        placeholder: Select Legal Entity
                        outputValue: value
                        _select:
                          transform: $legalEntityList()
                      - type: select
                        label: Pay Group
                        name: payGroup
                        key: payGroupCode
                        placeholder: Select Pay Group
                        outputValue: value
                        _select:
                          transform: $payGroup($.fields.companyCode)
                      - type: select
                        label: Period
                        name: payrollPeriodCode
                        key: payrollPeriodCode
                        placeholder: Select Pay Group
                        outputValue: value
                        _select:
                          transform: $payrollPeriodList()
                      - type: dateRange
                        name: startDateRange
                        label: Start Date
                        placeholder: Select Start Date
                      - type: dateRange
                        name: endDateRange
                        label: End Date
                        placeholder: Select End Date
                sources:
                  payrollPeriodList:
                    uri: '"/api/payroll-periods"'
                    method: GET
                    queryTransform: '{''limit'':1000}'
                    bodyTransform: ''
                    headerTransform: ''
                    resultTransform: >-
                      $map($.data, function($item) {{'label':
                      $item.longName.default & ' (' & $item.code & ')', 'value':
                      $item.code}})[]
                    disabledCache: true
                  companyList:
                    uri: '"/api/companies/by"'
                    method: GET
                    queryTransform: ''
                    bodyTransform: ''
                    headerTransform: ''
                    resultTransform: >-
                      $map($, function($item) {{'label': $item.longName.default
                      & ' (' & $item.code & ')', 'value': $item.code}})
                    disabledCache: true
                  legalEntityList:
                    uri: '"/api/legal-entities/by"'
                    method: GET
                    queryTransform: ''
                    bodyTransform: ''
                    headerTransform: ''
                    resultTransform: >-
                      $map($, function($item) {{'label': $item.longName.default
                      & ' (' & $item.code & ')', 'value': $item.code}})
                    disabledCache: true
                  payGroup:
                    uri: '"/api/pay-group-structures"'
                    method: GET
                    queryTransform: >-
                      {'limit':1000 , 'filter': [{'field':'status','operator':
                      '$eq','value':true}, {'field':'companyCode','operator':
                      '$eq','value': $.companyCode }]}
                    bodyTransform: ''
                    headerTransform: ''
                    resultTransform: >-
                      $map($.data, function($item) {{'label': $item.name.default
                      & ' (' & $item.code & ')', 'value': $item.code}})[]
                    disabledCache: true
                    params:
                      - companyCode
                variables: {}
              table:
                fields:
                  - name: code
                    key: tableCode
                    label: Payroll Sub-period code
                  - name: longName
                    key: tableSalaryAdminPlanCode
                    label: Long Name
                  - name: companyName
                    key: tableGradeCode
                    label: Company
                  - name: legalEntityName
                    key: tableStepCode
                    label: Legal Entity
                  - name: payGroupName
                    key: tableWageClassification
                    label: PayGroup
        - type: text
          name: prExtendPeriodCode
          unvisible: true
          _value:
            transform: $.fields.prExtendPeriodTable.value.code
        - type: text
          name: tsPeriodName
          label: TS period
          _condition:
            transform: $.extend.formType = 'view'
          _value:
            transform: >-
              $.extend.defaultValue.tsPeriodName ?
              $.extend.defaultValue.tsPeriodName & ' (' &
              $.extend.defaultValue.tsPeriodCode & ') ': null
        - type: text
          name: tsPeriodCode
          unvisible: true
          _value:
            transform: $.fields.tsPeriodTable.code
        - type: selectCustom
          name: tsPeriodTable
          label: TS period
          isLazyLoad: true
          col: 2
          outputValue: value
          actions:
            - id: search
              title: Select TS period
          _select:
            transform: >-
              $tsTimeKeepingList($.extend.limit, $.extend.page,
              $.extend.search,$boolean($.extend.filter.company) = true ?
              $.extend.filter.company :
              $.fields.companyObj.code,$boolean($.extend.filter.legalEntity) =
              true ? $.extend.filter.legalEntity : $.fields.legalEntityObj.code,
              $boolean($.extend.filter.legalEntity) = true ?
              $.extend.filter.payGroup : $.fields.payGroupCode
              ,'',$.extend.filter.startDateRange,$.extend.filter.endDateRange,$.extend.filter.code)
          placeholder: Select TS period
          validators:
            - type: required
          _value:
            transform: $.variables._selectedTSTimekeeping
          _validateFn:
            transform: >-
              ($exists($.fields.amountPickedValue.rateCode) ?
              ($selectedRateCodeList($.fields.salaryByTierMasterCode,
              $.extend.filter.salaryAdminPlanCode, $.extend.filter.gradeCode,
              $.extend.filter.stepCode, $.fields.jobDataId,
              $.fields.amountPickedValue.rateCode)[0] ?
              $selectedRateCodeList($.fields.salaryByTierMasterCode,
              $.extend.filter.salaryAdminPlanCode, $.extend.filter.gradeCode,
              $.extend.filter.stepCode, $.fields.jobDataId,
              $.fields.amountPickedValue.rateCode)[0]))
          _condition:
            transform: $.extend.formType = 'create' or $.extend.formType = 'edit'
          actionsConfig:
            search:
              formConfig:
                fields:
                  - type: group
                    collapsed: false
                    key: tsPeriodTableKey
                    disableEventCollapse: true
                    n_cols: 3
                    fields:
                      - type: select
                        label: Company
                        name: company
                        key: companyCode
                        placeholder: Select Company
                        outputValue: value
                        _select:
                          transform: >-
                            $companiesList($.extend.limit, $.extend.page,
                            $.extend.search)
                      - type: select
                        label: Legal Entity
                        name: legalEntity
                        key: legalEntityCode
                        placeholder: Select Legal Entity
                        outputValue: value
                        _select:
                          transform: >-
                            $legalEntitiesList($.extend.limit, $.extend.page,
                            $.extend.search, $.fields.companyCode)
                      - type: select
                        label: Pay Group
                        name: payGroup
                        key: payGroupCode
                        placeholder: Select Pay Group
                        outputValue: value
                        _select:
                          transform: >-
                            $payGroupsList($.extend.limit, $.extend.page,
                            $.extend.search, $.fields.companyCode)
                      - type: select
                        label: TS Period
                        name: code
                        key: code
                        placeholder: Select TS Period
                        outputValue: value
                        _select:
                          transform: >-
                            $tsTimeKeepingsList($.extend.limit, $.extend.page,
                            $.extend.search)
                      - type: dateRange
                        name: startDateRange
                        label: Start Date
                        placeholder: Select Start Date
                      - type: dateRange
                        name: endDateRange
                        label: End Date
                        placeholder: Select End Date
                sources:
                  companiesList:
                    uri: '"/api/companies/get-by"'
                    method: GET
                    queryTransform: >-
                      { 'limit': $.limit, 'page': $.page, 'search': $.search,
                      'filter': [{'field':'status','operator':
                      '$eq','value':true},{'field':'effectiveDate','operator':
                      '$eq','value': $.effectiveDate}]}
                    bodyTransform: ''
                    headerTransform: ''
                    resultTransform: >-
                      $map($.data, function($item) {{'label':
                      $item.longName.default & ' (' & $item.code & ')', 'value':
                      $item.code }})[]
                    disabledCache: true
                    params:
                      - limit
                      - page
                      - search
                  legalEntitiesList:
                    uri: '"/api/legal-entities/get-by"'
                    method: GET
                    queryTransform: >-
                      { 'limit': $.limit, 'page': $.page, 'search': $.search,
                      'filter': [{'field':'effectiveDate','operator':
                      '$eq','value':$.effectiveDate},{'field':'status','operator':
                      '$eq','value':true}, {'field':'companyCode', 'operator':
                      '$eq','value':$.companyCode}]}
                    bodyTransform: ''
                    headerTransform: ''
                    resultTransform: >-
                      $map($.data, function($item) {{'label':
                      $item.longName.default & ' (' & $item.code & ')', 'value':
                      $item.code }})[]
                    disabledCache: true
                    params:
                      - limit
                      - page
                      - search
                      - companyCode
                  payGroupsList:
                    uri: '"/api/pay-group-structures"'
                    method: GET
                    queryTransform: >-
                      {'limit': $.limit,'page': $.page,'search': $.search ,
                      'filter': [{'field':'effectiveDate','operator':
                      '$lte','value':$.effectiveDate},{'field':'status','operator':
                      '$eq','value':true}, {'field':'companyCode','operator':
                      '$eq','value': $.companyCode }]}
                    bodyTransform: ''
                    headerTransform: ''
                    resultTransform: >-
                      $map($.data, function($item) {{'label': $item.name.default
                      & ' (' & $item.code & ')', 'value': $item.code}})[]
                    disabledCache: true
                    params:
                      - limit
                      - page
                      - search
                      - companyCode
                  tsTimeKeepingsList:
                    uri: '"/api/ts-manager-timekeepings"'
                    method: GET
                    queryTransform: >-
                      {'limit': $.limit, 'page': $.page, 'search': $.search,
                      'filter': [{'field':'code','operator': '$eq','value':
                      $.code},{'field':'endDatePeriod','operator':
                      '$lte','value':
                      $.endDate[1]},{'field':'endDatePeriod','operator':
                      '$gte','value':
                      $.endDate[0]},{'field':'startDatePeriod','operator':
                      '$lte','value':
                      $.startDate[1]},{'field':'startDatePeriod','operator':
                      '$gte','value':
                      $.startDate[0]},{'field':'periodCode','operator':
                      '$eq','value':
                      $.payrollPeriodCode},{'field':'payGroupId','operator':
                      '$eq','value': $.payGroup},
                      {'field':'legalEntityCode','operator': '$eq','value':
                      $.legalEntityCode},{'field':'companyId','operator':
                      '$eq','value':
                      $.companyCode},{'field':'companyId','operator':
                      '$eq','value':$.companyCode?
                      'NULL':''},{'field':'status','operator':
                      '$eq','value':true}]}
                    bodyTransform: ''
                    headerTransform: ''
                    resultTransform: >-
                      $map($.data, function($item) {{'label':
                      $item.longName.default & ' (' & $item.code & ')', 'value':
                      $item.code }})[]
                    disabledCache: true
                    params:
                      - limit
                      - page
                      - search
                      - companyCode
                      - legalEntityCode
                      - payGroup
                      - payrollPeriodCode
                      - startDate
                      - endDate
                      - code
                variables: {}
              table:
                fields:
                  - name: code
                    key: tableRateCode
                    label: TS Period Name
                  - name: longName
                    key: tableSalaryAdminPlanCode
                    label: Long Name
                  - name: company
                    key: tableGradeCode
                    label: Company
                  - name: legalEntity
                    key: tableStepCode
                    label: Legal Entity
                  - name: payGroup
                    key: tableWageClassification
                    label: PayGroup
                  - name: startDatePeriod
                    key: tableStartDatePeriod
                    label: Start Date
                    displayType: DD/MM/YYYY
                    type: dateRange
                  - name: endDatePeriod
                    key: tableEndDatePeriod
                    label: End Date
                    displayType: DD/MM/YYYY
                    type: dateRange
        - type: translationTextArea
          name: note
          label: Note
          placeholder: Enter Note
          textarea:
            maxCharCount: 1000
            autoSize:
              minRows: 3
          validators:
            - type: maxLength
              args: '1000'
              text: Maximums 1000 characters
          _condition:
            transform: $.extend.formType = 'view'
        - type: translationTextArea
          name: note
          label: Note
          col: 2
          placeholder: Enter Note
          _condition:
            transform: $.extend.formType = 'create' or $.extend.formType = 'edit'
          textarea:
            maxCharCount: 1000
            autoSize:
              minRows: 3
          validators:
            - type: maxLength
              args: '1000'
              text: Maximums 1000 characters
  sources:
    nationsList:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search , 'filter':
        [{'field':'code','operator': '$eq','value':$.code}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - code
    payGroup:
      uri: '"/api/pay-group-structures"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search , 'filter':
        [{'field':'effectiveDate','operator':
        '$lte','value':$.endDate},{'field':'status','operator':
        '$eq','value':true}, {'field':'companyCode','operator': '$eq','value':
        $.companyCode }]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - companyCode
        - startDate
        - endDate
    currencyList:
      uri: '"/api/picklists/CURRENCY/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code, 'linkCatalogDataCode' : $item.linkCatalogDataCode}})[]
      disabledCache: true
    elementGroup:
      uri: '"/api/picklists/ELEMENTGROUP/values"'
      method: GET
      queryTransform: '{''limit'':5000}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
    elementType:
      uri: '"/api/picklists/ELEMENTTYPE/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code, 'elementGroup' : $item.codE501}})[]
      disabledCache: true
    payrollPeriodList:
      uri: '"/api/payroll-periods"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'startDay' :
        $item.startDay,'endDay': $item.endDay }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    payrollSubPeriodList:
      uri: '"/api/payroll-period-settings"'
      method: GET
      queryTransform: '{''limit'':1000}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
    companyList:
      uri: '"/api/companies/get-by"'
      method: GET
      queryTransform: >-
        { 'limit': $.limit, 'page': $.page, 'search': $.search, 'filter':
        [{'field':'effectiveDate','operator':
        '$eq','value':$.endDate},{'field':'status','operator':
        '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id': $item.id,'code' :$item.code}}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - startDate
        - endDate
    legalEntityList:
      uri: '"/api/legal-entities/get-by"'
      method: GET
      queryTransform: >-
        { 'limit': $.limit, 'page': $.page, 'search': $.search, 'filter':
        [{'field':'effectiveDate','operator':
        '$eq','value':$.endDate},{'field':'status','operator':
        '$eq','value':true}, {'field':'companyCode', 'operator':
        '$eq','value':$.companyCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id': $item.id,'code': $item.code} }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - companyCode
        - startDate
        - endDate
    tsTimeKeepingList:
      uri: '"/api/ts-manager-timekeepings"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'search': $.search, 'filter':
        [{'field':'code','operator': '$eq','value':
        $.code},{'field':'endDatePeriod','operator': '$lte','value':
        $.endDate[1]},{'field':'endDatePeriod','operator': '$gte','value':
        $.endDate[0]},{'field':'startDatePeriod','operator': '$lte','value':
        $.startDate[1]},{'field':'startDatePeriod','operator': '$gte','value':
        $.startDate[0]},{'field':'periodCode','operator': '$eq','value':
        $.payrollPeriodCode},{'field':'payGroupId','operator': '$eq','value':
        $.payGroup}, {'field':'legalEntityId','operator': '$eq','value':
        $.legalEntityCode},{'field':'companyId','operator': '$eq','value':
        $.companyCode},{'field':'companyId','operator':
        '$eq','value':$.companyCode? 'NULL':''},{'field':'status','operator':
        '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - companyCode
        - legalEntityCode
        - payGroup
        - payrollPeriodCode
        - startDate
        - endDate
        - code
    checkCompanyType:
      uri: '"/api/payroll-period-settings/check-company-type"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'companyCode','operator': '$eq','value':
        $.companyCode},{'field':'payrollPeriodCode','operator': '$eq','value':
        $.payrollPeriodCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $.companyType
      disabledCache: true
      params:
        - companyCode
        - payrollPeriodCode
    PRExtendPeriodList:
      uri: '"/api/payroll-period-settings"'
      method: GET
      queryTransform: >-
        {'limit':1000 ,'filter': [{'field':'endDate','operator': '$lte','value':
        $.endDate[1]},{'field':'endDate','operator': '$gte','value':
        $.endDate[0]},{'field':'startDate','operator': '$lte','value':
        $.startDate[1]},{'field':'startDate','operator': '$gte','value':
        $.startDate[0]},{'field':'payrollPeriodCode','operator': '$eq','value':
        $.payrollPeriodCode},{'field':'payGroup','operator': '$eq','value':
        $.payGroup}, {'field':'legalEntity','operator': '$eq','value':
        $.legalEntity},{'field':'company','operator': '$eq','value':
        $.companyCode},{'field':'salaryType','operator': '$eq','value':
        'ET_001.1'},{'field':'elementGroup','operator': '$eq','value':
        'EG_001'},{'field':'code','operator': '$eq','value': $.code}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item}})[]
      disabledCache: true
      params:
        - companyCode
        - legalEntity
        - payGroup
        - payrollPeriodCode
        - startDate
        - endDate
        - code
  variables:
    _PRExtendPeriodList:
      transform: >-
        $PRExtendPeriodList($.extend.filter.company,$.extend.filter.legalEntity,
        $.extend.filter.payGroup
        ,$.extend.filter.payrollPeriodCode,$.extend.filter.startDateRange,$.extend.filter.endDateRange)
    _checkCompanyType:
      transform: $checkCompanyType($.fields.companyObj.code, $.fields.payrollPeriodCode)
    _elementGroup:
      transform: $elementGroup()
    _elementType:
      transform: $elementType()
    _selectedElementType:
      transform: >-
        $filter($.variables._elementType , function($v) { $v.value =
        $.fields.salaryType})
    _filterElementType:
      transform: >-
        $filter($.variables._elementType , function($v) { $v.elementGroup =
        $.fields.elementGroup})[]
    _filterElementGroup:
      transform: >-
        $filter($.variables._elementGroup , function($v) { $v.value =
        $.variables._selectedElementType.elementGroup})[]
    _currencyList:
      transform: $currencyList()
    _selectedTSTimekeeping:
      transform: >-
        $.extend.formType = 'edit' ? $tsTimeKeepingList(1, 1,
        '','','','','','','',$.extend.defaultValue.tsPeriodCode)[0]
  footer:
    create: true
    update: true
    createdOn: createdAt
    updatedOn: updatedAt
    createdBy: createdBy
    updatedBy: updatedBy
filter_config:
  fields:
    - type: text
      name: code
      label: Payroll Sub Period Code
      labelType: type-grid
      placeholder: Enter Payroll Sub Period
    - type: selectAll
      name: country
      label: Country
      labelType: type-grid
      isLazyLoad: true
      _options:
        transform: $nationsList($.extend.limit, $.extend.page, $.extend.search)
      placeholder: Select Country
    - type: selectAll
      labelType: type-grid
      name: companyName
      label: Company
      isLazyLoad: true
      _options:
        transform: $companyList($.extend.limit, $.extend.page, $.extend.search)
      placeholder: Select Company
    - type: selectAll
      labelType: type-grid
      name: legalEntity
      label: Legal Entity
      isLazyLoad: true
      _options:
        transform: $legalEntityList($.extend.limit, $.extend.page, $.extend.search)
      placeholder: Select Legal Entity
    - type: selectAll
      labelType: type-grid
      name: payGroup
      isLazyLoad: true
      label: Pay Group
      _options:
        transform: $payGroup($.extend.limit, $.extend.page, $.extend.search)
      placeholder: Select Pay Group
    - type: selectAll
      name: payrollPeriodCode
      labelType: type-grid
      isLazyLoad: true
      label: Payroll Period
      _options:
        transform: $payrollPeriodList($.extend.limit, $.extend.page, $.extend.search)
      placeholder: Select Payroll Period
    - type: selectAll
      name: tsPeriodCode
      labelType: type-grid
      label: TS period
      _options:
        transform: >-
          $tsTimeKeepingList($.fields.companyName.id,$.fields.legalEntity.id,$.fields.payGroup.id)
      placeholder: Select TS period
    - type: text
      labelType: type-grid
      name: shortName
      label: Short Name
      placeholder: Enter Short Name
    - type: text
      name: longName
      labelType: type-grid
      label: Long Name
      placeholder: Enter Long Name
    - type: dateRange
      name: startDate
      labelType: type-grid
      label: Start Date
      placeholder: dd/MM/yyyy
    - type: dateRange
      labelType: type-grid
      name: endDate
      label: End Date
      placeholder: dd/MM/yyyy
    - type: dateRange
      name: paymentDay
      labelType: type-grid
      label: Payment Date
      placeholder: dd/MM/yyyy
    - type: selectAll
      name: elementGroup
      labelType: type-grid
      label: Element Group
      _options:
        transform: $.variables._elementGroup
      placeholder: Select Element Group
    - type: selectAll
      name: salaryType
      labelType: type-grid
      label: Element Type
      _options:
        transform: $.variables._elementType
      placeholder: Select Element Type
    - type: selectAll
      name: currency
      labelType: type-grid
      label: Currency
      _options:
        transform: $.variables._currencyList
      placeholder: Select Currency
    - type: selectAll
      name: prExtendPeriodCode
      labelType: type-grid
      label: PR Extend Period
      _options:
        transform: $PRExtendPeriodList($.extend.limit, $.extend.page, $.extend.search)
      placeholder: Select PR Extend Period
    - name: updatedBy
      label: Last Updated By
      type: text
      labelType: type-grid
      placeholder: Enter Last Updated By
    - name: updatedAt
      label: Last Updated On
      type: dateRange
      labelType: type-grid
      setting:
        mode: date
        format: dd/MM/yyyy
  filterMapping:
    - field: code
      operator: $cont
      valueField: code
    - field: country
      operator: $in
      valueField: country.(value)
    - field: payrollPeriodCode
      operator: $in
      valueField: payrollPeriodCode.(value)
    - field: tsPeriodCode
      operator: $in
      valueField: tsPeriodCode.(value)
    - field: shortName
      operator: $cont
      valueField: shortName
    - field: longName
      operator: $cont
      valueField: longName
    - field: company
      operator: $in
      valueField: companyName.(value)
    - field: legalEntityCode
      operator: $in
      valueField: legalEntity.(value)
    - field: payGroupCode
      operator: $in
      valueField: payGroup.(value)
    - field: startDate
      operator: $between
      valueField: startDate
    - field: endDate
      operator: $between
      valueField: endDate
    - field: paymentDay
      operator: $between
      valueField: paymentDay
    - field: elementGroup
      operator: $in
      valueField: elementGroup.(value)
    - field: salaryType
      operator: $in
      valueField: salaryType.(value)
    - field: currency
      operator: $in
      valueField: currency.(value)
    - field: note
      operator: $cont
      valueField: note
    - field: prExtendPeriodCode
      operator: $in
      valueField: prExtendPeriodCode.(value)
    - field: updatedBy
      operator: $cont
      valueField: updatedBy
    - field: updatedAt
      operator: $between
      valueField: updatedAt
  sources:
    nationsList:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    payGroup:
      uri: '"/api/pay-group-structures"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    currencyList:
      uri: '"/api/picklists/CURRENCY/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
    elementGroup:
      uri: '"/api/picklists/ELEMENTGROUP/values"'
      method: GET
      queryTransform: '{''limit'':5000}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
    elementType:
      uri: '"/api/picklists/ELEMENTTYPE/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})
      disabledCache: true
    payrollPeriodList:
      uri: '"/api/payroll-periods"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    payrollPeriodSettingList:
      uri: '"/api/payroll-period-settings"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    payrollSubPeriodList:
      uri: '"/api/payroll-period-settings"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    companyList:
      uri: '"/api/companies"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search, ''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    legalEntityList:
      uri: '"/api/legal-entities"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search, ''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default  & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    tsTimeKeepingList:
      uri: '"/api/ts-manager-timekeepings"'
      method: GET
      queryTransform: >-
        {'limit':999, 'filter': [{'field':'payGroupId','operator':
        '$eq','value': $.payGroup}, {'field':'legalEntityId','operator': '$eq',
        'value':$.legalEntityCode},{'field':'companyId','operator':
        '$eq','value': $.companyCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - companyCode
        - legalEntityCode
        - payGroup
    PRExtendPeriodList:
      uri: '"/api/payroll-period-settings"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search, ''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
  variables:
    _elementGroup:
      transform: $elementGroup()
    _elementType:
      transform: $elementType()
    _PRExtendPeriodList:
      transform: $PRExtendPeriodList()
    _currencyList:
      transform: $currencyList()
    _payrollSubPeriodList:
      transform: $payrollSubPeriodList()
layout_options:
  show_detail_history: false
  show_dialog_form_save_add_button: true
  delete_multi_items: true
  apply_delete_multi_items_to_delete_one: true
  custom_delete_body: '{''ids'': $map($.data,function($item){$item.id})[]}'
  reset_page_index_after_do_action:
    edit: true
  tool_table:
    - id: import
      icon: icon-upload-simple
      href: /GE/HR.FS.FR.092
      paramsRedirect:
        type: PR_OBJECT
        entityOrObj: PayrollPeriodSetting
    - id: export
      icon: icon-download-simple
  show_filter_results_message: true
  store_selected_items: true
  show_dialog_duplicate_button: false
  hide_action_row: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: null
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    title: Edit
    icon: icon-pencil-simple
    type: ghost-gray
  - id: delete
    title: Delete
    icon: icon-trash
    type: ghost-gray
backend_url: /api/payroll-period-settings
screen_name: payroll-period-settings
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: legalEntityCode
    defaultName: LegalEntityCode
  - name: payGroupCode
    defaultName: PayGroupCode
  - name: companyCode
    defaultName: CompanyCode
  - name: country
    defaultName: CountryCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Payroll Sub-Period
  parent:
    title: PR Setting
