id: PR.FS.FR.029
status: draft
sort: 244
user_created: 56e71b91-dd39-4ebc-afd1-b4f00f38d4a5
date_created: '2024-07-26T06:18:35.825Z'
user_updated: b403d5da-d684-45e4-ae6d-e25223baedc5
date_updated: '2025-07-17T03:05:47.023Z'
title: Organizational Parameter
requirement:
  time: 1749026447663
  blocks:
    - id: pFRKuzGF3Y
      type: paragraph
      data:
        text: >-
          &nbsp;Cho phép bộ phận nhân sự CTTV thiết lập định mức đã tạo ở
          PR.FS.FR.005 Danh mục Đ<PERSON>nh mức áp dụng cho các cơ cấu con mà mình quản
          lý.
    - id: TjO9VPvJy0
      type: paragraph
      data:
        text: >-
          <PERSON><PERSON> thống cho phép admin thực hiện c<PERSON><PERSON>, chỉnh sửa caption của các
          trường thông tin trên giao <PERSON>, tiêu đề của các cột dưới lưới và các
          nút chức năng tại MH chính và các popup
    - id: oWjaTKHh5E
      type: paragraph
      data:
        text: >-
          Hệ thống cho phép admin sắp xếp thứ tự của các trường thông tin hiển
          thị trên giao diện và thứ tự các cột dữ liệu dưới lưới.
    - id: X26hQYFSyi
      type: paragraph
      data:
        text: >-
          Với các trường thông tin cho phép nhập liệu, hệ thống thực hiện valid
          và cảnh báo ngay khi người dùng nhập sai định dạng cơ bản về kiểu dữ
          liệu (MessageID=10)
    - id: mRsQUUkDLu
      type: paragraph
      data:
        text: >-
          Với các valid dữ liệu về định dạng chi tiết (Không cho nhập ký tự đặc
          biệt, không cho nhập khoảng trắng), valid về logic thì khi người dùng
          bấm lưu thông tin thì hệ thống sẽ cảnh báo lỗi và hiển thị thông tin
          lỗi ngay dưới trường thông tin xảy ra lỗi
    - id: 6QmJrkmIKK
      type: paragraph
      data:
        text: >-
          Thông tin thiết lập tại chức năng này sẽ là nguyên tắc để hệ thống tự
          sinh dữ liệu tại chức năng FPT_PEOPLEX_FSD_PR.FS.FR.030_Thiết lập định
          mức theo nhân viênLuồng xử lý như sau: Khi có các trigger từ các phân
          hệ đầu vào (ONB, FO, HR) (*).
    - id: 4vcbXxasD5
      type: paragraph
      data:
        text: >-
          Chi tiết trigger sẽ liệt kê bổ sung sau ==&gt; Hệ thống sẽ có cảnh báo
          trên hệ thốngKhi người dùng xác nhận ""Đồng ý"" cập nhật thì hệ thống
          sử dụng service để tự động generate thông tin cho từng nhân viên có
          ảnh hưởng theo trigger (*) theo nguyên tắc được xác định tại chức năng
          này có hiệu lực tại ngày hiệu lực của action theo trigger  (*)Khi
          người dùng xác nhận ""Từ chối"", hệ thống không thực hiện gennarate
          thông tin.&nbsp;&nbsp;
  version: 2.30.7
screen_design: null
module: PR
local_fields:
  - code: code
    title: Parameter Code
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    pinned: true
    show_sort: true
  - code: parameterName
    title: Parameter Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: country
    title: Country
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: company
    title: Company
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: Display the company information set for the parameter
    show_sort: true
  - code: payGroupNames
    title: Pay Group
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tags
      collection: field_types
    show_sort: true
  - code: legalEntity
    title: Legal Entity
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: Display the legal entity information set for the parameter
    show_sort: true
  - code: businessUnit
    title: Business Unit
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: Display the business unit information set for the parameter
    show_sort: true
  - code: division
    title: Division
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: Display the division information set for the parameter
    show_sort: true
  - code: department
    title: Department
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: localExpatName
    title: Local/ Expat
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: employeeGroup
    title: Employee Group
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: employeeSubGroup
    title: Employee Sub Group
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: jobTitle
    title: Job Title
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: location
    title: Location
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: typeName
    title: Type
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: amount
    title: Amount
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Currency
      collection: field_types
    show_sort: false
  - code: currency
    title: Currency
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: startDate
    title: Effective Start Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/YYYY
      collection: field_types
    show_sort: true
  - code: endDate
    title: Effective End Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    show_sort: true
  - code: note
    title: Note
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    show_sort: true
  - code: updatedBy
    title: Last Updated By
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: updatedAt
    title: Last Updated On
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
    show_sort: true
mock_data:
  - parameterCode: '00000001'
    parameter: '00000001'
    shortName: Non-taxable Meal Allowance
    longName: Non-taxable Meal Allowance
    country: Vietnam
    company: FIS
    legalEntity: FIS_HCM
    businessUnit: FIS_HCM
    division: ES
    department: PB19
    localExpat: ''
    employeeGroup: ''
    employeeSubGroup: ''
    jobTitle: ''
    location: ''
    type: Amount
    currency: VND
    amount: 730,000
    effectiveStartDate: '2024-06-01'
    effectiveEndDate: '2024-06-30'
    status: Active
    note: ''
    creator: NhiVN
    createdTime: '2024-04-20 09:31:00'
    lastEditor: NhiVN
    lastEditedTime: '2024-05-06 10:20:53'
  - parameterCode: '00000002'
    parameter: '00000001'
    shortName: Non-taxable Meal Allowance
    longName: Non-taxable Meal Allowance
    country: Vietnam
    company: FIS
    legalEntity: FIS_HCM
    businessUnit: FIS_HCM
    division: ES
    department: PB19
    localExpat: ''
    employeeGroup: ''
    employeeSubGroup: ''
    jobTitle: ''
    location: ''
    type: Amount
    currency: VND
    amount: 730,000
    effectiveStartDate: '2024-06-01'
    effectiveEndDate: '2024-06-30'
    status: Active
    note: ''
    creator: NhiVN
    createdTime: '2024-04-20 09:31:00'
    lastEditor: NhiVN
    lastEditedTime: '2024-05-06 10:20:53'
local_buttons: null
layout: layout-table
form_config:
  formTitle:
    create: 'Add new: Setup Parameter for Payroll Calculation'
    edit: Edit Setup Parameter for Payroll Calculation
    view: Setup Parameter for Payroll Calculation Details
  defaultValidateError: Required fields have not been filled in completely (fields marked with *)
  fields:
    - type: group
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - type: text
          name: setupView
          unvisible: true
          _value:
            transform: >-
              $count($.extend.defaultValue.payGroups)>0 ? 'PAY_GROUP' :
              'LEGAL_ENTITY'
        - name: parameterName
          type: text
          label: Parameter
          _value:
            transform: >-
              $.extend.defaultValue.parameterName & ' (' &
              $.extend.defaultValue.code & ')'
        - name: typeName
          type: text
          label: Type
        - name: amount
          label: Percent/Coefficient
          type: number
          _condition:
            transform: $.extend.defaultValue.type = 'WGCSFT_00002'
          number:
            suffix: '%'
        - name: amount
          label: Percent/Coefficient
          type: number
          _condition:
            transform: $.extend.defaultValue.type = 'WGCSFT_00003'
        - name: amount
          label: Amount
          type: number
          _condition:
            transform: $.extend.defaultValue.type = 'WGCSFT_00001'
          number:
            _suffix:
              transform: $.extend.defaultValue.currencyCode
            format: currency
            precision: 3
            min: 0
        - name: country
          type: text
          label: Country
        - name: company
          type: text
          label: Company
        - type: select
          label: Paygroup
          placeholder: Enter Paygroup
          name: payGroups
          mode: multiple
          outputValue: value
          _condition:
            transform: $.fields.setupView = 'PAY_GROUP'
          _select:
            transform: $payGroupsList($.fields.startDate,$.fields.companyObj.code)
        - name: legalEntity
          type: text
          label: Legal entity
          _condition:
            transform: $.fields.setupView!='PAY_GROUP'
        - name: businessUnit
          type: text
          label: Business unit
          _condition:
            transform: $.fields.setupView!='PAY_GROUP'
        - name: division
          type: text
          label: Division
          _condition:
            transform: $.fields.setupView!='PAY_GROUP'
        - name: department
          type: text
          label: Department
          _condition:
            transform: $.fields.setupView!='PAY_GROUP'
        - name: localExpatName
          type: text
          label: Local/Expat
          _condition:
            transform: $.fields.setupView!='PAY_GROUP'
        - type: select
          label: Employee Group
          name: employeeGroupCode
          outputValue: value
          _condition:
            transform: $.fields.setupView!='PAY_GROUP'
          _select:
            transform: $employeeGroupsList()
        - type: select
          label: Employee Sub Group
          dependantField: $.fields.employeeGroupCode
          name: employeeSubGroupCode
          outputValue: value
          _condition:
            transform: $.fields.setupView!='PAY_GROUP'
          _select:
            transform: $employeeSubGroupsList($.fields.employeeGroupCode)
        - name: jobTitle
          type: text
          label: Job Title
          _condition:
            transform: $.fields.setupView!='PAY_GROUP'
        - name: location
          type: text
          label: Location
          _condition:
            transform: $.fields.setupView!='PAY_GROUP'
    - name: codeObj
      label: Parameter
      type: select
      isLazyLoad: true
      _condition:
        transform: $.extend.formType != 'view'
      _value:
        transform: $.extend.formType != 'create' ? $.extend.defaultValue.codeObj
      outputValue: value
      placeholder: Select Parameter
      _select:
        transform: >-
          $salaryStandardList($.extend.limit, $.extend.page, $.extend.search,
          $.fields.startDate)
      _validateFn:
        transform: >-
          $exists($.value.code) ? ($salaryStandardList(1, 1,'',
          $.fields.startDate,$.value.code)[0] ? $salaryStandardList(1, 1,'',
          $.fields.startDate,$.value.code)[0] : '_setSelectValueNull')
      validators:
        - type: required
    - type: text
      name: type
      dependantFields: $.fields.codeObj.code
      label: Type
      unvisible: true
      _value:
        transform: '$.fields.codeObj ? $.fields.codeObj.typeCode : ''_setValueNull'''
    - type: group
      n_cols: 2
      _condition:
        transform: $.extend.formType != 'view'
      fields:
        - name: typeSelect
          label: Type
          placeholder: ''
          dependantFields: $.fields.type
          type: text
          _disabled:
            transform: 'true'
          _value:
            transform: '$.fields.codeObj ? $.fields.codeObj.typeName : ''_setValueNull'''
        - name: amount
          label: Amount
          _condition:
            transform: $.fields.codeObj.typeCode = 'WGCSFT_00001'
          type: number
          placeholder: Enter Amount
          validators:
            - type: required
          addOnAfter:
            type: select
            name: currencyCode
            dependantFields: $.fields.codeObj.code
            _value:
              transform: $.fields.codeObj.currencyCode ? $.fields.codeObj.currencyCode
            _select:
              transform: $currenciesList()
            outputValue: value
          number:
            format: currency
            precision: 3
            min: 0
        - name: amount
          label: Percent/Coefficient
          placeholder: Enter Percent/Coefficient
          dependantFields: $.fields.codeObj.code
          _condition:
            transform: $.fields.codeObj.typeCode != 'WGCSFT_00001'
          validators:
            - type: required
          type: number
          number:
            _suffix:
              transform: $.fields.codeObj.typeCode = 'WGCSFT_00002'?'%':''
            format: currency
            precision: 3
            min: 0
    - type: group
      _condition:
        transform: $.extend.formType != 'view'
      n_cols: 2
      fields:
        - type: select
          label: Country
          placeholder: Select Country
          name: countryCode
          outputValue: value
          _select:
            transform: $nationsList()
        - type: select
          label: Company
          placeholder: Select Company
          isLazyLoad: true
          name: companyObj
          clearFieldsAfterChange:
            - payGroups
            - legalEntityObj
            - divisionObj
            - departmentObj
            - businessUnitObj
          outputValue: value
          _select:
            transform: >-
              $companiesList($.extend.limit, $.extend.page, $.extend.search,
              $.fields.startDate,'',$.fields.codeObj.companyCodes)
          _validateFn:
            transform: >-
              $exists($.value.code) ?
              ($companiesList(0,0,'',$.fields.startDate,$.value.id,$.fields.codeObj.companyCodes)[0]
              ?
              $companiesList(0,0,'',$.fields.startDate,$.value.id,$.fields.codeObj.companyCodes)[0]
              : {'label': null, 'value': null})
          _class:
            transform: '$isNilorEmpty($.fields.detailSetup) ? ''unrequired'' : ''required'''
        - type: select
          label: Detail Setup
          col: 2
          placeholder: Select Detail Setup
          name: detailSetup
          _value:
            transform: >-
              $.extend.formType != 'create' ?
              $count($.extend.defaultValue.payGroups) > 0 ? 'PAY_GROUP' :
              'LEGAL_ENTITY'
          outputValue: value
          select:
            - label: Organization
              value: LEGAL_ENTITY
            - label: Paygroup
              value: PAY_GROUP
    - type: group
      n_cols: 2
      fieldBackground: '#F8F9FA'
      borderRadius: 8px
      padding: 16px
      _condition:
        transform: $.fields.detailSetup='PAY_GROUP'
      fields:
        - type: select
          label: Paygroup
          placeholder: Enter Paygroup
          name: payGroups
          mode: multiple
          outputValue: value
          col: 2
          _select:
            transform: $payGroupsList($.fields.startDate,$.fields.companyObj.code)
    - type: group
      n_cols: 2
      fieldBackground: '#F8F9FA'
      borderRadius: 8px
      padding: 16px
      _condition:
        transform: $.fields.detailSetup='LEGAL_ENTITY'
      fields:
        - type: select
          label: Legal Entity
          placeholder: Select Legal Entity
          isLazyLoad: true
          _value:
            transform: '$isNilorEmpty($.fields.companyObj.code) ? ''_setSelectValueNull'' '
          _validateFn:
            transform: >-
              $not($isNilorEmpty($.value.code)) ? (
              $isNilorEmpty($legalEntitiesList(1,1,'',$.fields.startDate,
              $.fields.companyObj.code,$.value.code)[0]) ?
              '_setSelectValueNull'  )
          name: legalEntityObj
          clearFieldsAfterChange:
            - departmentObj
            - businessUnitObj
            - divisionObj
          outputValue: value
          _select:
            transform: >-
              $legalEntitiesList($.extend.limit, $.extend.page, $.extend.search
              ,$.fields.startDate, $.fields.companyObj.code)
        - type: select
          label: Business Unit
          placeholder: Select Business Unit
          _value:
            transform: >-
              $isNilorEmpty($.fields.companyObj.code) ? '_setSelectValueNull' :
              ($not($isNilorEmpty($.fields.departmentObj.businessUnitCode)) and
              $isNilorEmpty($.fields.businessUnitObj) ?
              $businessUnitsList(1,1,'',$.fields.startDate,
              $.fields.companyObj.code,$.fields.departmentObj.businessUnitCode)[0]
              )
          _validateFn:
            transform: >-
              $not($isNilorEmpty($.value.code)) ? (
              $isNilorEmpty($businessUnitsList(1,1,'',$.fields.startDate,
              $.fields.companyObj.code,$.value.code)[0]) ?
              '_setSelectValueNull'  )
          name: businessUnitObj
          isLazyLoad: true
          clearFieldsAfterChange:
            - divisionObj
            - departmentObj
          outputValue: value
          _select:
            transform: >-
              $isNilorEmpty($.fields.legalEntityObj.code) ?
              $businessUnitsList($.extend.limit, $.extend.page ,$.extend.search
              ,$.fields.startDate, $.fields.companyObj.code) :
              $get_businessUnitTree($.fields.companyObj.code,
              $.fields.legalEntityObj.code)
        - type: select
          label: Division
          placeholder: Select Division
          _value:
            transform: >-
              $isNilorEmpty($.fields.businessUnitObj.code) ?
              '_setSelectValueNull' :
              ($not($isNilorEmpty($.fields.departmentObj.divisionCode)) and
              $isNilorEmpty($.fields.divisionObj) ?
              $divisionsList(1,1,'',$.fields.startDate,$.fields.businessUnitObj.code,
              null,$.fields.departmentObj.divisionCode)[0])
          _validateFn:
            transform: >-
              $not($isNilorEmpty($.value.code)) ? (
              $divisionsList(1,1,'',$.fields.startDate,$.fields.businessUnitObj.code,
              null,$.value.code)[0] ?
              $divisionsList(1,1,'',$.fields.startDate,$.fields.businessUnitObj.code,
              null,$.value.code)[0] : '_setSelectValueNull'  )
          clearFieldsAfterChange:
            - departmentObj
          name: divisionObj
          isLazyLoad: true
          outputValue: value
          _disabled:
            transform: $isNilorEmpty($.fields.businessUnitObj)
          _select:
            transform: >-
              $divisionsList($.extend.limit, $.extend.page, $.extend.search,
              $.fields.startDate,$.fields.businessUnitObj.code)
        - type: select
          label: Department
          _value:
            transform: >-
              ( $isNilorEmpty($.fields.companyObj.code) or
              $isNilorEmpty($.fields.legalEntityObj.code) ) ?
              '_setSelectValueNull'
          _validateFn:
            transform: >-
              $not($isNilorEmpty($.value.code)) ? ( $departmentsList(1, 1,
              '',$.fields.startDate,$.fields.legalEntityObj.code,$.fields.companyObj.code,$.value.code)[0]
              ? $departmentsList(1, 1, '',
              $.fields.startDate,$.fields.legalEntityObj.code,$.fields.companyObj.code,$.value.code)[0]
              : '_setSelectValueNull'  )
          _disabled:
            transform: >-
              $isNilorEmpty($.fields.businessUnitObj) and
              $isNilorEmpty($.fields.legalEntityObj)
          placeholder: Select Department
          name: departmentObj
          isLazyLoad: true
          outputValue: value
          _select:
            transform: >-
              $departmentsList($.extend.limit, $.extend.page, $.extend.search,
              $.fields.startDate,$.fields.legalEntityObj.code,$.fields.companyObj.code,'',$.fields.divisionObj.code,$.fields.businessUnitObj.code)
        - type: group
          col: 2
          n_cols: 2
          fields:
            - type: select
              label: Local/ Expat
              placeholder: Select Local/ Expat
              name: residencyStatus
              outputValue: value
              col: 1
              _select:
                transform: $localExpatlist()
        - type: select
          label: Employee Group
          placeholder: Select Employee Group
          name: employeeGroupCode
          clearFieldsAfterChange:
            - employeeSubGroupCode
          outputValue: value
          _select:
            transform: $employeeGroupsList()
        - type: select
          label: Employee Sub Group
          placeholder: Select Employee Sub Group
          name: employeeSubGroupCode
          outputValue: value
          _select:
            transform: $employeeSubGroupsList($.fields.employeeGroupCode)
        - type: select
          label: Job Title
          placeholder: Select Job Title
          name: jobTitleObj
          isLazyLoad: true
          _validateFn:
            transform: >-
              $not($isNilorEmpty($.value.code)) ? ( $jobList(1, 1, '',
              $.fields.startDate,$.value.code,$.fields.companyObj.code)[0] ?
              $jobList(1, 1, '',
              $.fields.startDate,$.value.code,$.fields.companyObj.code)[0] :
              '_setSelectValueNull'  )
          outputValue: value
          _select:
            transform: >-
              $jobList($.extend.limit, $.extend.page, $.extend.search,
              $.fields.startDate,'',$.fields.companyObj.code)
        - type: select
          label: Location
          isLazyLoad: true
          placeholder: Select Location
          name: locationObj
          _validateFn:
            transform: >-
              $not($isNilorEmpty($.value.code)) ? ( $locationsList(1, 1, '',
              $.fields.startDate,$.value.code,$.fields.companyObj.code)[0] ?
              $locationsList(1, 1, '',
              $.fields.startDate,$.value.code,$.fields.companyObj.code)[0] :
              '_setSelectValueNull'  )
          outputValue: value
          _select:
            transform: >-
              $locationsList($.extend.limit, $.extend.page, $.extend.search,
              $.fields.startDate,'',$.fields.companyObj.code)
    - type: group
      collapse: false
      disableEventCollapse: true
      n_cols: 2
      _condition:
        transform: $.extend.formType != 'view'
      fields:
        - name: startDate
          type: dateRange
          label: Effective Start Date
          placeholder: dd/MM/yyyy
          setting:
            format: dd/MM/yyyy
            type: date
          mode: date-picker
          _value:
            transform: $.extend.formType = 'create' ? $now()
          validators:
            - type: required
        - name: endDate
          type: dateRange
          label: Effective End Date
          placeholder: dd/MM/yyyy
          setting:
            format: dd/MM/yyyy
            type: date
          mode: date-picker
          validators:
            - type: ppx-custom
              args:
                transform: >-
                  $exists($.fields.endDate) and
                  $DateDiff($DateFormat($.fields.endDate, 'yyyy-MM-DD'),
                  $DateFormat($.fields.startDate, 'yyyy-MM-DD'), 'd') < 1
              text: End date must be greater than start date
    - name: startDate
      type: dateRange
      label: Effective Start Date
      _condition:
        transform: $.extend.formType = 'view'
      setting:
        format: dd/MM/yyyy
        type: date
      mode: date-picker
    - name: endDate
      type: dateRange
      label: Effective End Date
      _condition:
        transform: $.extend.formType = 'view'
      setting:
        format: dd/MM/yyyy
        type: date
      mode: date-picker
    - name: note
      label: Note
      type: translationTextArea
      placeholder: Enter Note
      textarea:
        autoSize:
          minRows: 3
        maxCharCount: '1000'
      validators:
        - type: maxLength
          args: '1000'
          text: You are entering more than the allowed number of characters
  sources:
    salaryStandardList:
      uri: '"/api/salary-standards/combobox"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page,'search': $.search,'filter':
        [{'field':'code','operator':
        '$eq','value':$.code},{'field':'IsCombobox','operator':
        '$eq','value':true},{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator': '$lte','value':
        $.effectiveDate},{'field':'effectiveDateTo','operator': '$gt','value':
        $.effectiveDate},{'field':'effectiveDateTo','operator': '$eq','value':
        $.effectiveDate ? 'NULL'}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'code' : $item.code
        ,'typeCode':$item.typeCode,'typeName':$item.typeName,'currency':$item.currency,'currencyCode':$item.currencyCode,'companyCodes':
        $item.companyCodes }}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
        - code
    currenciesList:
      uri: '"/api/picklists/CURRENCY/values"'
      method: GET
      queryTransform: '{''filter'': [{''field'':''status'',''operator'': ''$eq'',''value'':true}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.code, 'value':
        $item.code}})[]
      disabledCache: true
    nationsList:
      uri: '"/api/picklists/COUNTRIES/values"'
      method: GET
      queryTransform: '{''filter'': [{''field'':''status'',''operator'': ''$eq'',''value'':true}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
    localExpatlist:
      uri: '"/api/picklists/LOCALFOREIGNERS/values"'
      method: GET
      queryTransform: '{''filter'': [{''field'':''status'',''operator'': ''$eq'',''value'':true}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
    companiesList:
      uri: '"/api/companies/get-by"'
      method: GET
      queryTransform: >-
        { 'limit': $.limit, 'page': $.page, 'search': $.search, 'filter':
        [{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator': '$eq','value':
        $.effectiveDate},{'field':'id','operator':
        '$eq','value':$.id},{'field':'code','operator':
        '$in','value':$.listCodes}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id': $item.id,'code' :$item.code}}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
        - id
        - listCodes
    legalEntitiesList:
      uri: '"/api/legal-entities/get-by"'
      method: GET
      queryTransform: >-
        { 'limit': $.limit, 'page': $.page, 'search': $.search, 'filter':
        [{'field':'code','operator':
        '$eq','value':$.code},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true}, {'field':'companyCode', 'operator':
        '$eq','value':$.companyCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id': $item.id,'code': $item.code} }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
        - companyCode
        - code
    businessUnitsList:
      uri: '"/api/business-units/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'code','operator':
        '$eq','value':$.code},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}, {'field':'status','operator':
        '$eq','value':true}, {'field':'companyCode', 'operator':
        '$eq','value':$.companyCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')',  'value': {'id': $item.id,'code': $item.code}}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
        - companyCode
        - code
    divisionsList:
      uri: '"/api/divisions/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'code','operator':
        '$eq','value':$.code},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}, {'field':'status','operator':
        '$eq','value':true}, {'field':'businessUnitCode', 'operator':
        '$eq','value':$.businessUnitCode},{'field':'companyCode', 'operator':
        '$eq','value':$.companyCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')',  'value': {'id': $item.id,'code': $item.code}}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
        - businessUnitCode
        - companyCode
        - code
    departmentsList:
      uri: '"/api/departments/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'code','operator':
        '$eq','value':$.code},{'field':'businessUnitCode','operator':
        '$eq','value':$.businessUnitCode},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}, {'field':'status','operator':
        '$eq','value':true}, {'field':'divisionCode', 'operator':
        '$eq','value':$.divisionCode}, {'field':'legalEntityCode', 'operator':
        '$eq','value':$.legalEntityCode}, {'field':'companyCode', 'operator':
        '$eq','value':$.companyCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')',  'value': {'id': $item.id,'code': $item.code,
        'businessUnitCode': $item.businessUnitCode,'divisionCode':
        $item.divisionCode}}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
        - legalEntityCode
        - companyCode
        - code
        - divisionCode
        - businessUnitCode
    locationsList:
      uri: '"/api/locations/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'pageSize': $.pageSize,'search':
        $.search, 'filter': [{'field':'companyCode','operator':
        '$eq','value':$.companyCode},{'field':'code','operator':
        '$eq','value':$.code},{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator': '$eq','value':
        $.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')',  'value': {'id': $item.id,'code': $item.code}}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
        - code
        - companyCode
    jobList:
      uri: '"/api/job-codes/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'pageSize': $.pageSize,'search':
        $.search, 'filter': [{'field':'companyCode','operator':
        '$eq','value':$.companyCode},{'field':'code','operator':
        '$eq','value':$.code},{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator': '$eq','value':
        $.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')',  'value': {'id': $item.id,'code': $item.code}}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
        - code
        - companyCode
    employeeGroupsList:
      uri: '"/api/picklists/EMPLOYEEGROUP/values"'
      method: GET
      queryTransform: '{''filter'': [{''field'':''status'',''operator'': ''$eq'',''value'':true}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
    employeeSubGroupsList:
      uri: '"/api/picklists/EMPLOYEESUBGROUP/values"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'status','operator':
        '$eq','value':true},{'field':'linkCatalogDataCode','operator':
        '$eq','value':$.employeeGroup}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - employeeGroup
    payGroupsList:
      uri: '"/api/pay-group-structures"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator': '$lte','value':
        $.effectiveDate},{'field':'companyCode','operator': '$eq','value':
        $.companyCode},{'field':'companyCode','operator':
        '$eq','value':$.companyCode? 'NULL':''}],'limit':1000}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - effectiveDate
        - companyCode
    get_businessUnitTree:
      uri: '"/api/business-units/get-by-children-with-tree/raw"'
      queryTransform: >-
        {'filter': [{'field':'status','operator': '$eq','value':true},
        {'field':'companyCode','operator': '$eq','value': $.companyCode },
        {'field':'legalEntityCode','operator': '$eq','value': $.legalEntityCode
        }]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.name & ' (' & $item.code &
        ')',  'value': {'id': $item.id,'code': $item.code}}})[]
      disabledCache: true
      params:
        - companyCode
        - legalEntityCode
  variables: {}
  footer:
    create: true
    update: true
    createdOn: createdAt
    updatedOn: updatedAt
    createdBy: createdBy
    updatedBy: updatedBy
filter_config:
  fields:
    - name: code
      label: Parameter Code
      type: text
      labelType: type-grid
      placeholder: Enter Parameter Code
    - name: countryCode
      label: Country
      type: selectAll
      mode: multiple
      labelType: type-grid
      placeholder: Select Country
      isLazyLoad: true
      _options:
        transform: $nationsList($.extend.limit, $.extend.page, $.extend.search)
    - name: companyCode
      label: Company
      type: selectAll
      labelType: type-grid
      mode: multiple
      placeholder: Select Company
      isLazyLoad: true
      _options:
        transform: $companiesList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      labelType: type-grid
      label: Pay Group
      dependantField: $.fields.companyCode
      name: payGroupCode
      isLazyLoad: true
      mode: multiple
      _options:
        transform: $payGroupsList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      label: Legal Entity
      labelType: type-grid
      name: legalEntityCode
      isLazyLoad: true
      dependantField: $.fields.companyCode
      mode: multiple
      _options:
        transform: $legalEntitiesList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      label: Type
      isLazyLoad: true
      name: typeCode
      labelType: type-grid
      mode: multiple
      _options:
        transform: $typeList($.extend.limit, $.extend.page, $.extend.search)
    - name: updatedBy
      label: Last Updated By
      type: text
      labelType: type-grid
      placeholder: Enter Last Updated By
    - name: updatedAt
      label: Last Updated On
      type: dateRange
      labelType: type-grid
      setting:
        mode: date
        format: dd/MM/yyyy
  filterMapping:
    - field: code
      operator: $cont
      valueField: code
    - field: countryCode
      operator: $in
      valueField: countryCode.(value)
    - field: companyCode
      operator: $in
      valueField: companyCode.(value)
    - field: payGroupCodesFilter
      operator: $eq
      valueField: payGroupCode.(value)
    - field: legalEntityCode
      operator: $in
      valueField: legalEntityCode.(value)
    - field: businessUnitCode
      operator: $in
      valueField: businessUnitCode.(value)
    - field: divisionCode
      operator: $in
      valueField: divisionCode.(value)
    - field: departmentCode
      operator: $in
      valueField: departmentCode.(value)
    - field: localExpat
      operator: $in
      valueField: localExpat.(value)
    - field: employeeGroupCode
      operator: $in
      valueField: employeeGroupCode.(value)
    - field: employeeSubGroupCode
      operator: $in
      valueField: employeeSubGroupCode.(value)
    - field: jobTitleCode
      operator: $in
      valueField: jobTitleCode.(value)
    - field: locationCode
      operator: $in
      valueField: locationCode.(value)
    - field: type
      operator: $in
      valueField: typeCode.(value)
    - field: currencyCode
      operator: $in
      valueField: currencyCode.(value)
    - field: startDate
      operator: $between
      valueField: startDate
    - field: endDate
      operator: $between
      valueField: endDate
    - field: status
      operator: $eq
      valueField: status
    - field: updatedBy
      operator: $cont
      valueField: updatedBy
    - field: updatedAt
      operator: $between
      valueField: updatedAt
  sources:
    salaryStandardList:
      uri: '"/api/salary-standards"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'effectiveDate','operator': '$lte','value':
        $.effectiveDate}],'limit': $.limit,'page': $.page,'search': $.search}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value':
        $item.code,'typeCode':$item.typeCode,'typeName':$item.typeName,'currency':$item.currency,'currencyCode':$item.currencyCode}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
    typeList:
      uri: '"/api/picklists/WAGECLASSIFICATION/values/pagination"'
      method: GET
      queryTransform: '{''limit'': $.limit, ''page'': $.page, ''search'': $.search, ''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    nationsList:
      uri: '"/api/picklists/COUNTRIES/values"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    payGroupsList:
      uri: '"/api/pay-group-structures"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'companyCode','operator': '$in','value':
        $.companyCode},{'field':'companyCode','operator':
        '$eq','value':$.companyCode? 'NULL':''}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - companyCode
    companiesList:
      uri: '"/api/companies"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    legalEntitiesList:
      uri: '"/api/legal-entities"'
      method: GET
      queryTransform: >-
        { 'limit': $.limit, 'page': $.page, 'search': $.search, 'filter': [
        {'field':'companyCode', 'operator': '$in','value':$.companyCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - companyCode
    businessUnitsList:
      uri: '"/api/business-units"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter': [
        {'field':'companyCode', 'operator': '$in','value':$.companyCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - companyCode
    divisionsList:
      uri: '"/api/divisions"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter': [
        {'field':'businessUnitCode', 'operator':
        '$in','value':$.businessUnitCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - businessUnitCode
    departmentsList:
      uri: '"/api/departments"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'pageSize': $.pageSize,'search':
        $.search, 'filter': [ {'field':'legalEntityCode', 'operator':
        '$in','value':$.legalEntityCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - legalEntityCode
    locationsList:
      uri: '"/api/locations"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'pageSize': $.pageSize,'search':
        $.search,'filter': []}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    jobList:
      uri: '"/api/job-codes"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'effectiveDate','operator': '$lte','value':
        $.fields.effectiveDate}],'limit':1000}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - effectiveDate
    employeeGroupsList:
      uri: '"/api/picklists/EMPLOYEEGROUP/values"'
      method: GET
      queryTransform: '{''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
    employeeSubGroupsList:
      uri: '"/api/picklists/EMPLOYEESUBGROUP/values"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'linkCatalogDataCode','operator':
        '$in','value':$.employeeGroup}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - employeeGroup
    currencysList:
      uri: '"/api/picklists/CURRENCY/values"'
      method: GET
      queryTransform: '{''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
layout_options:
  show_dialog_form_save_add_button: true
  show_detail_history: false
  delete_multi_items: true
  apply_delete_multi_items_to_delete_one: true
  custom_delete_body: '{''ids'': $map($.data,function($item){$item.id})[]}'
  tool_table:
    - id: import
      icon: icon-upload-simple
      paramsRedirect:
        type: PR_OBJECT
        entityOrObj: SalaryStandardStructure
    - id: export
      icon: icon-download-simple
  show_filter_results_message: true
  store_selected_items: true
  show_dialog_duplicate_button: false
  hide_action_row: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: null
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    icon: pencil
    type: tertiary
  - id: delete
    icon: trash
    type: tertiary
backend_url: /api/salary-standard-structures
screen_name: null
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: countryCode
    defaultName: CountryCode
  - name: companyCode
    defaultName: CompanyCode
  - name: legalEntityCode
    defaultName: LegalEntityCode
  - name: payGroupCodes
    defaultName: PayGroupCode
  - name: businessUnitCode
    defaultName: BusinessUnitCode
  - name: divisionCode
    defaultName: DivisionCode
  - name: departmentCode
    defaultName: DepartmentCode
  - name: employeeSubGroupCode
    defaultName: EmployeeSubGroupCode
  - name: locationCode
    defaultName: LocationCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Organizational Parameter
  parent:
    title: PR Setting
