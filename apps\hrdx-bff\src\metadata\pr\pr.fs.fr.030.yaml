id: PR.FS.FR.030
status: draft
sort: 271
user_created: 56e71b91-dd39-4ebc-afd1-b4f00f38d4a5
date_created: '2024-07-26T06:19:27.754Z'
user_updated: b403d5da-d684-45e4-ae6d-e25223baedc5
date_updated: '2025-07-28T04:30:42.736Z'
title: Set up Parameter For Employee In Payroll Calculation
requirement:
  time: 1747191315945
  blocks:
    - id: dxLeepZBXO
      type: paragraph
      data:
        text: >-
          <PERSON> phép bộ phận nhân sự CTTV thiết lập định mức đã tạo ở PR.FS.FR.005
          Danh mục <PERSON> mức áp dụng cho từng cá nhân riêng biệt.
  version: 2.30.7
screen_design: null
module: PR
local_fields:
  - code: employeeId
    title: Employee ID
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    pinned: true
    show_sort: true
  - code: fullName
    title: Employee Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: employeeGroup
    title: Employee Group
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: employeeRecordNumber
    title: Employee Record Number
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: code
    title: Parameter Code
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: longName
    title: Parameter Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: typeNameList
    title: Type
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: amount
    title: Amount
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Currency
      collection: field_types
    show_sort: false
  - code: currencyName
    title: Currency
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: startDate
    title: Effective Start Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    show_sort: true
  - code: endDate
    title: Effective End Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    show_sort: true
  - code: status
    title: Status
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: Boolean Tag
      collection: field_types
    show_sort: true
  - code: note
    title: Note
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: updatedBy
    title: Last Updated By
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: updatedAt
    title: Last Updated On
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
    show_sort: true
mock_data:
  - employeeID: '1234567'
    parameter: '00000001'
    fullName: Nguyen Van A
    employeeRecordNumber: '1'
    country: Vietnam
    company: ''
    legalEntity: ''
    businessUnit: ''
    division: ''
    department: ''
    jobTitle: ''
    levelCareer: ''
    contractType: ''
    location: ''
    parameterCode: '00000001'
    shortName: ''
    longName: Meal allowance non-taxable
    type: Amount
    currency: VND
    amount: 730,000
    effectiveStartDate: '2024-06-01'
    effectiveEndDate: '2024-06-30'
    status: Active
    note: ''
    creator: ''
    createdTime: ''
    lastEditor: ''
    lastEditedTime: ''
  - employeeID: '1234568'
    parameter: '00000002'
    fullName: Nguyen Van B
    employeeRecordNumber: '1'
    country: Vietnam
    company: ''
    legalEntity: ''
    businessUnit: ''
    division: ''
    department: ''
    jobTitle: ''
    levelCareer: ''
    contractType: ''
    location: ''
    parameterCode: '00000001'
    shortName: ''
    longName: Meal allowance non-taxable
    type: Rate
    currency: ''
    amount: '1'
    effectiveStartDate: '2024-06-01'
    effectiveEndDate: '2024-06-30'
    status: Inactive
    note: ''
    creator: ''
    createdTime: ''
    lastEditor: ''
    lastEditedTime: ''
local_buttons: null
layout: layout-table
form_config:
  defaultValidateError: Required fields have not been filled in completely (fields marked with *)
  fields:
    - type: group
      n_cols: 2
      fields:
        - type: select
          name: employee
          label: Employee
          placeholder: Select Employee
          isLazyLoad: true
          validators:
            - type: required
          _select:
            transform: >-
              $employeesList($.extend.limit, $.extend.page, $.extend.search,
              null, null, null,$.fields.startDate,$.fields.codeObj.companyCodes
              )
          _validateFn:
            transform: >-
              $not($isNilorEmpty($.value.employeeId)) ? (
              $isNilorEmpty($employeesList(1, 1,'',
              $.value.employeeId,null,$.value.employeeRecordNumber,
              $.fields.startDate,$.fields.codeObj.companyCodes)[0]) ? 
              '_setSelectValueNull' ) 
          _condition:
            transform: $not($not($.extend.formType = 'create'))
          outputValue: value
          col: 2
        - type: select
          name: employee
          label: Employee
          placeholder: Select Employee
          isLazyLoad: true
          validators:
            - type: required
          _select:
            transform: >-
              $employeesList($.extend.limit, $.extend.page, $.extend.search,
              null,null,null,$.fields.startDate )
          _disabled:
            transform: 'true'
          _condition:
            transform: $.extend.formType = 'edit'
          outputValue: value
          col: 2
        - type: text
          name: dateToShowEmployee
          unvisible: true
          _value:
            transform: >-
              $not($isNilorEmpty($.fields.startDate)) ?
              $DateToTimestampUTC($.fields.startDate) :
              $DateToTimestampUTC($now())
        - type: text
          name: dataEmployee
          unvisible: true
          dependantField: $.fields.employeeId; $.fields.startDate
          _value:
            transform: >-
              $not($isNilorEmpty($.fields.employeeId)) ? {'employeeId':
              $.fields.employeeId , 'employeeRecordNumber':
              $.fields.employeeRecordNumber, 'dateToShowEmployee':
              $.fields.dateToShowEmployee} : null
        - type: text
          name: employeeId
          dependantField: $.fields.employee
          label: Employee ID
          unvisible: true
          _value:
            transform: >-
              $not($isNilorEmpty($.fields.employee)) ?
              $.fields.employee.employeeId : null
          col: 2
        - type: number
          name: employeeRecordNumber
          label: Employee Record Number
          unvisible: true
          _value:
            transform: $.fields.employee.employeeRecordNumber
          col: 2
        - type: text
          name: companyCode
          unvisible: true
          _value:
            transform: $.fields.employee.company
          col: 2
        - type: group
          _condition:
            transform: $.extend.formType = 'view'
          fields:
            - name: employeeIdView
              type: text
              _value:
                transform: >-
                  $join($filter([$.extend.defaultValue.employeeId,$.extend.defaultValue.employeeGroupCode,$string($.extend.defaultValue.employeeRecordNumber),$.extend.defaultValue.fullName],
                  $boolean), ' - ')
              label: Employee ID
            - name: companyName
              type: text
              label: Company
            - name: legalEntityName
              type: text
              label: Legal entity
            - name: businessUnitName
              type: text
              label: Business unit
            - name: divisionName
              type: text
              label: Division
            - name: departmentName
              type: text
              label: Department
            - name: jobTitle
              type: text
              label: Job Title
            - name: contractTypeName
              type: text
              label: Contract Type
            - name: location
              type: text
              label: Location
          col: 2
        - name: codeObj
          label: Parameter
          type: select
          isLazyLoad: true
          clearFieldsAfterChange:
            - amountNonCurrency
            - amountCurrency
          _condition:
            transform: $.extend.formType != 'view'
          _value:
            transform: $.extend.formType != 'create' ? $.extend.defaultValue.codeObj
          outputValue: value
          placeholder: Select Parameter
          _select:
            transform: >-
              $salaryStandardList($.extend.limit, $.extend.page,
              $.extend.search,
              $.fields.startDate,'',$.fields.employee.companyCode)
          _validateFn:
            transform: >-
              $exists($.value.code) ? ($salaryStandardList(1, 1,'',
              $.fields.startDate,$.value.code,'')[0] ? $salaryStandardList(1,
              1,'', $.fields.startDate,$.value.code,'')[0] :
              '_setSelectValueNull') 
          validators:
            - type: required
          col: 2
        - name: parameterName
          type: text
          label: Parameter
          _condition:
            transform: $.extend.formType = 'view'
          _value:
            transform: >-
              $.extend.defaultValue.parameterName & ' (' &
              $.extend.defaultValue.code & ')'
        - type: text
          name: currencyCodeCreate
          unvisible: true
          _value:
            transform: >-
              $not($isNilorEmpty($.fields.codeObj.currencyCode)) ?
              $.fields.codeObj.currencyCode : 'VND'
        - type: text
          name: currencyCodeEdit
          dependantFieldSkip: 2
          dependantField: $.fields.codeObj.currencyCode
          unvisible: true
          _value:
            transform: >-
              $isNilorEmpty($.fields.currencyCodeEdit) ?
              ($not($isNilorEmpty($.fields.codeObj.currencyCode)) ?
              $.fields.codeObj.currencyCode : 'VND')
        - name: typeSelect
          label: Type
          placeholder: '--'
          type: text
          _condition:
            transform: $.extend.formType != 'view' and $boolean($.fields.codeObj.code)
          _disabled:
            transform: 'true'
          _value:
            transform: $.fields.codeObj.typeName
        - type: number
          name: amountCurrency
          _label:
            transform: '''Amount'''
          validators:
            - type: required
          _condition:
            transform: >-
              ($.extend.formType != 'view' and $boolean($.fields.codeObj.code))
              and $not($not($.fields.codeObj.typeCode = 'WGCSFT_00001'))
          addOnAfter:
            type: select
            name: currencyCodeAddOn
            clearFieldsAfterChange:
              - currencyCodeCreate
            width: 128px
            outputValue: value
            placeholder: Select Currency
            _select:
              transform: $.variables._currencyList
            _value:
              skipWhenClear: true
              transform: >-
                $.extend.formType = 'create' ? $.fields.currencyCodeCreate :
                $.fields.currencyCodeEdit
            validators:
              - type: required
          number:
            format: currency
            precision: 2
            min: 0
        - name: amountNonCurrency
          _label:
            transform: '''Percent/Coefficient'''
          validators:
            - type: required
          _condition:
            transform: >-
              ($.extend.formType != 'view' and $boolean($.fields.codeObj.code))
              and $not($not($.fields.codeObj.typeCode != 'WGCSFT_00001'))
          type: number
          number:
            _suffix:
              transform: $.fields.codeObj.typeCode = 'WGCSFT_00002'?'%'
            min: 0
        - name: typeName
          label: Type
          type: text
          disabled: true
          _condition:
            transform: $boolean($.fields.codeObj.code) and $.extend.formType = 'view'
          col: 2
        - name: amount
          label: Amount
          _condition:
            transform: >-
              $.extend.defaultValue.typeCode = 'WGCSFT_00001' and
              $.extend.formType = 'view'
          type: number
          placeholder: Enter Amount
          number:
            _suffix:
              transform: $.extend.defaultValue.currencyCodeAddOn
            format: currency
            precision: 2
            min: 0
          col: 2
        - name: amount
          label: Percent/Coefficient
          _condition:
            transform: >-
              $.extend.defaultValue.typeCode = 'WGCSFT_00002' and
              $.extend.formType = 'view'
          type: number
          number:
            suffix: '%'
            min: 0
          col: 2
        - name: amount
          label: Percent/Coefficient
          _condition:
            transform: >-
              $.extend.defaultValue.typeCode = 'WGCSFT_00003' and
              $.extend.formType = 'view'
          type: number
          col: 2
        - type: dateRange
          name: startDate
          label: Start Date
          mode: date-picker
          placeholder: dd/MM/yyyy
          _value:
            transform: $.extend.formType = 'create' ? $now()
          setting:
            type: date
            format: dd/MM/yyyy
          validators:
            - type: required
          _condition:
            transform: $.extend.formType = 'create' or $.extend.formType = 'edit'
          col: 1
        - type: dateRange
          name: endDate
          label: End Date
          mode: date-picker
          placeholder: dd/MM/yyyy
          setting:
            type: date
            format: dd/MM/yyyy
          validators:
            - type: ppx-custom
              args:
                transform: >-
                  $exists($.fields.endDate) and
                  $DateDiff($DateFormat($.fields.endDate, 'yyyy-MM-DD'),
                  $DateFormat($.fields.startDate, 'yyyy-MM-DD'), 'd') < 1
              text: End date must be greater than start date
          _condition:
            transform: '$boolean($.extend.formType != ''view'') '
          col: 1
        - name: startDate
          type: dateRange
          label: Effective Start Date
          setting:
            format: dd/MM/yyyy
            type: date
          mode: date-picker
          _value:
            transform: $.extend.formType = 'create' ? $now()
          _condition:
            transform: ' $.extend.formType = ''view'''
          col: 2
        - name: endDate
          type: dateRange
          label: Effective End Date
          setting:
            format: dd/MM/yyyy
            type: date
          mode: date-picker
          _condition:
            transform: ' $.extend.formType = ''view'''
          col: 2
        - name: status
          label: Status
          type: radio
          _value:
            transform: $.extend.formType = 'create' ? true
          radio:
            - label: Active
              value: true
            - label: Inactive
              value: false
          col: 2
        - type: translationTextArea
          label: Note
          placeholder: Enter note
          name: note
          textarea:
            autoSize:
              minRows: 3
            maxCharCount: '1000'
          validators:
            - type: maxLength
              args: '1000'
              text: Note should not exceed 1000 characters
          col: 2
  overview:
    border: true
    dependentField: dataEmployee
    noDataMessages: Choose Employee ID getting data
    header: Employee Detail
    uri: >-
      /api/pr-employees/:{employeeId}:/employee-record-number/:{employeeRecordNumber}:/effective-date/:{dateToShowEmployee}:
    display:
      - key: companyName
        label: Company
      - key: legalEntityName
        label: Legal entity
      - key: businessUnitName
        label: Business unit
      - key: divisionName
        label: Division
      - key: departmentName
        label: Department
      - key: jobTitleName
        label: Job Title
      - key: contractTypeName
        label: Contract Type
      - key: locationName
        label: Location
  footer:
    create: true
    update: true
    createdOn: createdAt
    updatedOn: updatedAt
    createdBy: createdBy
    updatedBy: updatedBy
  sources:
    salaryStandardList:
      uri: '"/api/salary-standards/combobox"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page,'search': $.search,'filter':
        [{'field':'CompanyCodesFilter','operator':
        '$eq','value':$.CompanyCodesFilter},{'field':'hasCompany','operator':
        '$eq','value':true },{'field':'code','operator':
        '$eq','value':$.code},{'field':'IsCombobox','operator':
        '$eq','value':true},{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator': '$lte','value':
        $.effectiveDate},{'field':'effectiveDateTo','operator': '$gt','value':
        $.effectiveDate},{'field':'effectiveDateTo','operator': '$eq','value':
        $.effectiveDate ? 'NULL'}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'code' : $item.code
        ,'typeCode':$item.typeCode,'typeName':$item.typeName,'currency':$item.currency,'currencyCode':$item.currencyCode
        ,'companyCodes': $item.companyCodes }}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
        - code
        - CompanyCodesFilter
    currencyList:
      uri: '"/api/picklists/CURRENCY/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.code, 'value':
        $item.code}})[]
      disabledCache: true
    jobDatasList:
      uri: '"/api/personals/"  & $.employeeId &  "/job-datas"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $.data
      disabledCache: true
      params:
        - employeeId
    employeesList:
      uri: '"/api/pr-employees"'
      method: GET
      queryTransform: >-
        {'limit': $.limit ,'page': $.page, 'search': $.search,'filter':
        [{'field':'companyCode','operator':
        '$in','value':$.companyCode},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'employeeId','operator':
        '$eq','value':$.empId},{'field':'employeeGroupCode','operator':
        '$cont','value':$.grpCode},{'field':'employeeRecordNumber','operator':
        '$eq','value':$.ern}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label':
        $join($filter([$item.employeeId,$item.employeeGroupCode,$string($item.employeeRecordNumber),$item.fullName],
        $boolean), ' - ')  , 'value': {'employeeId': $item.employeeId, 'code':
        $item.employeeId, 'employeeRecordNumber':
        $string($item.employeeRecordNumber), 'jobDataId':
        $item.jobDataId,'companyCode': $item.companyCode }}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - empId
        - grpCode
        - ern
        - effectiveDate
        - companyCode
  variables:
    _currencyList:
      transform: $currencyList()
filter_config:
  fields:
    - type: selectAll
      name: employeeId
      label: Employee
      placeholder: Select Employee
      labelType: type-grid
      _options:
        transform: $employeesList($.extend.limit, $.extend.page, $.extend.search)
      isLazyLoad: true
    - name: salartStandardCode
      label: Parameter Code
      type: text
      labelType: type-grid
      placeholder: Enter Parameter Code
    - name: salartStandardName
      label: Parameter Name
      type: text
      labelType: type-grid
      placeholder: Enter Parameter Name
    - name: amount
      label: Amount
      type: number
      labelType: type-grid
      placeholder: Enter Amount
    - name: currencyCode
      label: Currency
      labelType: type-grid
      isLazyLoad: true
      type: selectAll
      mode: multiple
      placeholder: Select Type
      _options:
        transform: $currenciesList($.extend.limit, $.extend.page, $.extend.search)
    - name: wageClassification
      label: Type
      type: selectAll
      placeholder: Select Type
      labelType: type-grid
      _options:
        transform: $.variables._salaryType
      outputValue: value
    - name: status
      label: Status
      labelType: type-grid
      type: radio
      value: ''
      radio:
        - value: ''
          label: All
        - value: true
          label: Active
        - value: false
          label: Inactive
    - name: startDate
      label: Start Date
      labelType: type-grid
      type: dateRange
    - name: endDate
      label: End Date
      labelType: type-grid
      type: dateRange
    - name: updatedBy
      label: Last Updated By
      type: text
      labelType: type-grid
      placeholder: Enter Last Updated By
    - name: updatedAt
      label: Last Updated On
      labelType: type-grid
      type: dateRange
  filterMapping:
    - operator: $or
      valueField:
        - field: employeeId
          operator: $elemMatch
          valueField: employeeId.(employeeId)
        - field: employeeRecordNumber
          operator: $elemMatch
          valueField: employeeId.(ern)
        - field: employeeGroupCode
          operator: $elemMatch
          valueField: employeeId.(empGroup)
    - field: wageClassification
      operator: $cont
      valueField: wageClassification
    - field: status
      operator: $eq
      valueField: status
    - field: updatedBy
      operator: $cont
      valueField: updatedBy
    - field: updatedAt
      operator: $between
      valueField: updatedAt
    - field: salaryStandardCodeFilter
      operator: $cont
      valueField: salartStandardCode
    - field: salaryStandardNameFilter
      operator: $cont
      valueField: salartStandardName
    - field: amountFilter
      operator: $eq
      valueField: amount
    - field: currencyCodeFilter
      operator: $in
      valueField: currencyCode.(value)
    - field: startDate
      operator: $between
      valueField: startDate
    - field: endDate
      operator: $between
      valueField: endDate
  sources:
    currenciesList:
      uri: '"/api/picklists/CURRENCY/values/pagination"'
      method: GET
      queryTransform: '{''limit'': $.limit, ''page'': $.page, ''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    salaryType:
      uri: '"/api/picklists/WAGECLASSIFICATION/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
    employeesList:
      uri: '"/api/personals"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label':
        $join($filter([$item.employeeId,$item.employeeGroup,$string($item.employeeRecordNumber),$item.fullName],
        $boolean), ' - ') , 'value':{'employeeId':$item.employeeId , 'ern':
        $item.employeeRecordNumber,'empGroup' :
        $.item.employeeGroup},'employeeId':$item.employeeId,
        'ern':$item.employeeRecordNumber, 'empGroup' : $item.employeeGroup }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    userList:
      uri: '"/api/internal-users"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.userName, 'value':
        $item.userName}})
      disabledCache: true
  variables:
    _salaryType:
      transform: $salaryType()
    _employeesList:
      transform: $employeesList()
layout_options:
  show_dialog_form_save_add_button: true
  delete_multi_items: true
  apply_delete_multi_items_to_delete_one: true
  custom_delete_body: '{''ids'': $map($.data,function($item){$item.id})[]}'
  reset_page_index_after_do_action:
    edit: true
  is_new_dynamic_form: true
  show_detail_history: false
  tool_table:
    - id: import
      icon: icon-upload-simple
      href: /GE/HR.FS.FR.092
      paramsRedirect:
        type: PR_OBJECT
        entityOrObj: SalaryStandardEmployee
    - id: export
      icon: icon-download-simple
      href: /GE/HR.FS.FR.092
  show_filter_results_message: true
  store_selected_items: true
  show_dialog_duplicate_button: false
  hide_action_row: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: null
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    icon: icon-pencil-simple
    type: ghost-gray
  - id: delete
    icon: icon-trash
    type: ghost-gray
backend_url: /api/salary-standard-employees
screen_name: salary-standard-employees
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: employeeId
    defaultName: EmployeeId
  - name: employeeRecordNumber
    defaultName: EmployeeRecordNumber
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Parameter Setup for Employee
  parent:
    title: PR Setting
