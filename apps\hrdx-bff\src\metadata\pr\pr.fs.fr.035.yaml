id: PR.FS.FR.035
status: draft
sort: 181
user_created: 56e71b91-dd39-4ebc-afd1-b4f00f38d4a5
date_created: '2024-07-25T09:58:01.971Z'
user_updated: b403d5da-d684-45e4-ae6d-e25223baedc5
date_updated: '2025-07-29T04:29:56.276Z'
title: Manage Additional Earnings/Deductions Information
requirement:
  time: 1748318842069
  blocks:
    - id: ejqS20ClTm
      type: paragraph
      data:
        text: >-
          <PERSON><PERSON> thống hỗ trợ quản lý thông tin lịch sử quá trình, thiết lập thông
          tin hưởng các khoản hỗ trợ không cố định cho nhân viên: loại hỗ trợ,
          thời gian hiệu lực, mức hưởng.
    - id: B8AUqppUpD
      type: paragraph
      data:
        text: >-
          <PERSON> phép người dùng thực hiện import hàng loạt thông tin hưởng của
          khoản hỗ trợ đã được phê duyệt bên ngoài lên hệ thống hoặc tích hợp dữ
          liệu đã tính toán từ hệ thống vệ tinh của các CTTV. Trường hợp dữ liệu
          lỗi (mã nhân viên chưa tồn tại, mã khoản hỗ trợ chưa đúng, …), hệ
          thống cảnh báo và nêu rõ lý do để cán bộ nhân sự kiểm tra lại.
    - id: PAx7yY1K7V
      type: paragraph
      data:
        text: >-
          Lưu ý: Trường hợp trong kỳ tính lương, nhân viên được hưởng cùng một
          khoản hỗ trợ nhiều lần, hệ thống cho phép import và ghi nhận toàn bộ.
    - id: PDD7EyPIPR
      type: paragraph
      data:
        text: >-
          Hệ thống tự động đồng bộ các khoản hỗ trợ đã được phê duyệt bởi các
          cấp có thẩm quyền từ quy trình: HR.BP.16 – Quy trình khen thưởng,
          HR.BP.09 – Quy trình kiêm nhiệm, HR.BP.12 – Quy trình quản lý hợp
          đồng.
  version: 2.30.7
screen_design: null
module: PR
local_fields:
  - code: employeeId
    title: Employee ID
    display_type:
      key: Hyperlink
      collection: field_types
    data_type:
      key: Increment ID
      collection: data_types
    description: >-
      - Hiển thị thông tin mã nhân viên có khoản hỗ trợ không cố định/ khấu trừ
      trong hệ thống theo phân quyền dữ liệu của tài khoản đăng nhập.
    pinned: true
    show_sort: true
  - code: fullName
    title: Full Name
    display_type:
      collection: field_types
      key: Label
    data_type:
      key: Person Name
      collection: data_types
    description: >-
      - Hiển thị thông tin tên nhân viên có khoản hỗ trợ không cố định/ khấu trừ
      trong hệ thống.
    show_sort: true
  - code: employeeRecordNumber
    title: Employee Record Number
    display_type:
      key: Label
      collection: field_types
    data_type:
      key: Nmberic
      collection: data_types
    description: '- Load thông tin danh sách khoản hỗ trợ của CBNV. '
    options__tabular__column_width: 15
    show_sort: true
  - code: employeeGroup
    title: Employee Group
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: typeName
    title: Type
    display_type:
      collection: field_types
      key: Label
    data_type:
      key: String
      collection: data_types
    description: >-
      -Hiển thị thông tin phân loại theo bản ghi tương ứng là Earning hay
      Deduction
    show_sort: true
  - code: elementCode
    title: Element Code
    data_type:
      key: Employee Code
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: elementName
    title: Element Name
    display_type:
      collection: field_types
      key: Label
    data_type:
      key: String
      collection: data_types
    description: '-Hiển thị thông tin tên khoản hỗ trợ theo bản ghi tương ứng'
    show_sort: true
  - code: amount
    title: Amount
    display_type:
      key: Currency
      collection: field_types
    data_type:
      key: String
      collection: data_types
    description: '-Hiển thị thông tin giá trị theo bản ghi tương ứng'
    show_sort: false
  - code: currencyName
    title: Currency
    display_type:
      collection: field_types
      key: Label
    data_type:
      key: String
      collection: data_types
    description: '-Hiển thị thông tin loại tiền tệ theo bản ghi tương ứng'
    show_sort: true
  - code: startDate
    title: Effective Start Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    show_sort: true
  - code: endDate
    title: Effective End Date
    display_type:
      key: DD/MM/YYYY
      collection: field_types
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    description: '-Hiển thị thông tin ngày kết thúc hiệu lực theo bản ghi tương ứng'
    show_sort: true
  - code: decisionNumber
    title: Decision Number
    display_type:
      key: Label
      collection: field_types
    data_type:
      key: String
      collection: data_types
    description: '-Hiển thị thông tin số quyết định theo bản ghi tương ứng'
    show_sort: true
  - code: signerName
    title: Signer
    display_type:
      collection: field_types
      key: Label
    data_type:
      key: User
      collection: data_types
    description: '-Hiển thị thông tin người ký theo bản ghi tương ứng'
    show_sort: true
  - code: signDate
    title: Sign Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    description: '-Hiển thị thông tin ngày ký theo bản ghi tương ứng'
    show_sort: true
  - code: note
    title: Note
    display_type:
      collection: field_types
      key: Label
    data_type:
      key: String
      collection: data_types
    description: '-Hiển thị thông tin ghi chú theo bản ghi tương ứng'
    show_sort: true
  - code: dataResource
    title: 'Data resource '
    display_type:
      collection: field_types
      key: Label
    data_type:
      key: String
      collection: data_types
    description: '-Hiển thị thông tin nguồn theo bản ghi tương ứng'
    show_sort: true
  - code: company
    title: Company
    display_type:
      key: Label
      collection: field_types
    data_type:
      key: Organization-1
      collection: data_types
    description: '-Hiển thị thông tin công ty của nhân viên theo bản ghi tương ứng'
    show_sort: true
  - code: legalEntity
    title: Legal Entity
    data_type:
      key: Organization-3
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: '-Hiển thị thông tin pháp nhân của nhân viên theo bản ghi tương ứng'
    show_sort: true
  - code: businessUnit
    title: Business Unit
    display_type:
      collection: field_types
      key: Label
    data_type:
      collection: data_types
      key: String
    description: '-Hiển thị thông tin Đơn vị quản trị của nhân viên theo bản ghi tương ứng'
    show_sort: true
  - code: division
    title: Division
    display_type:
      collection: field_types
      key: Label
    data_type:
      key: String
      collection: data_types
    description: '-Hiển thị thông tin khối của nhân viên theo bản ghi tương ứng'
    show_sort: true
  - code: department
    title: Department
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: '-Hiển thị thông tin phòng ban của nhân viên theo bản ghi tương ứng'
    show_sort: true
  - code: jobTitle
    title: Job Title
    display_type:
      collection: field_types
      key: Label
    data_type:
      key: String
      collection: data_types
    description: '-Hiển thị thông tin chức danh của nhân viên theo bản ghi tương ứng'
    show_sort: true
  - code: contractType
    title: Contract Type
    display_type:
      key: Label
      collection: field_types
    data_type:
      key: String
      collection: data_types
    description: null
    show_sort: true
  - code: location
    title: Location
    display_type:
      key: Label
      collection: field_types
    data_type:
      key: String
      collection: data_types
    description: '-Hiển thị thông tin nơi làm việc của nhân viên theo bản ghi tương ứng'
    show_sort: true
  - code: updatedBy
    title: Last Updated By
    description: '-Hiển thị người chỉnh sửa cuối cùng của bản ghi tương ứng'
    data_type:
      key: User
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: updatedAt
    title: Last Updated On
    description: '-Hiển thị thời gian chỉnh sửa cuối cùng của bản ghi tương ứng'
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
    show_sort: true
mock_data:
  - employeeId: '0389328'
    fullName: John Doe
    employeeRecordNumber: '1'
    type: Earning
    elementCode: '********'
    elementName: Hỗ trợ(********)
    amount: '500.000'
    currency: VND
    salaryCaculation: Monthly
    effectiveStartDate: 01/01/2024
    effectiveEndDate: 01/31/2024
    instance: '1'
    decisionNumber: ''
    signer: ''
    signDate: ''
    attachment: http://example.com/attachment1.pdf
    note: This is a note for employee 1.
    dataResource: HR System
    company: ABC Corp
    legalEntity: ABC Corp LLC
    businessUnit: Finance
    division: Operations
    department: Accounting
    jobTitle: Accountant
    careerLevel: Junior
    contractType: Permanent
    location: New York
    createdTime: 01/01/2024 09:00:00
    creator: admin
    lastEditedTime: 02/01/2024 10:00:00
    lastEditor: admin
  - employeeId: '0389322'
    fullName: Jane Roe
    employeeRecordNumber: '2'
    type: Deduction
    elementCode: '********'
    elementName: Khấu trừ(********)
    amount: '100.000'
    currency: VND
    salaryCaculation: Monthly
    effectiveStartDate: 01/01/2024
    effectiveEndDate: 01/31/2024
    instance: '1'
    decisionNumber: ''
    signer: ''
    signDate: ''
    attachment: http://example.com/attachment2.pdf
    note: This is a note for employee 2.
    dataResource: Payroll System
    company: XYZ Corp
    legalEntity: XYZ Corp Inc
    businessUnit: HR
    division: Support
    department: Recruitment
    jobTitle: HR Specialist
    careerLevel: Senior
    contractType: Temporary
    location: San Francisco
    createdTime: 01/01/2024 09:00:00
    creator: admin
    lastEditedTime: 02/01/2024 10:00:00
    lastEditor: admin
local_buttons: null
layout: layout-table
form_config:
  defaultValidateError: Required fields have not been filled in completely (fields marked with *)
  fields:
    - type: select
      name: employee
      label: Employee
      placeholder: Select Employee
      isLazyLoad: true
      validators:
        - type: required
      _select:
        transform: >-
          $employeesList($.extend.limit, $.extend.page, $.extend.search, null,
          null, null, $.fields.startDate)
      _validateFn:
        transform: >-
          $not($isNilorEmpty($.value.employeeId)) ? (
          $isNilorEmpty($employeesList(1, 1,'',
          $.value.employeeId,null,$.value.employeeRecordNumber,
          $.fields.startDate)[0]) ?  '_setSelectValueNull' ) 
      _condition:
        transform: $not($not($.extend.formType = 'create'))
      outputValue: value
    - type: select
      name: employee
      label: Employee
      isLazyLoad: true
      placeholder: Select Employee
      validators:
        - type: required
      _select:
        transform: >-
          $employeesList($.extend.limit, $.extend.page, $.extend.search,
          null,null,null, $.fields.startDate)
      _disabled:
        transform: 'true'
      _condition:
        transform: $boolean($.extend.formType = 'edit') = true
      outputValue: value
    - type: text
      name: dateToShowEmployee
      unvisible: true
      _value:
        transform: >-
          $not($isNilorEmpty($.fields.startDate)) ?
          $DateToTimestampUTC($.fields.startDate) : $DateToTimestampUTC($now())
    - type: text
      name: dataEmployee
      unvisible: true
      dependantField: $.fields.employeeId; $.fields.startDate
      _value:
        transform: >-
          $not($isNilorEmpty($.fields.employeeId)) ? {'employeeId':
          $.fields.employeeId , 'employeeRecordNumber':
          $.fields.employeeRecordNumber, 'dateToShowEmployee':
          $.fields.dateToShowEmployee} : null
    - type: text
      name: employeeId
      dependantField: $.fields.employee
      label: Employee ID
      unvisible: true
      _value:
        transform: >-
          $not($isNilorEmpty($.fields.employee)) ? $.fields.employee.employeeId
          : null
    - type: text
      name: employeeIdView
      label: Employee ID
      _value:
        transform: >-
          $join($filter([$.extend.defaultValue.employeeId,$.extend.defaultValue.employeeGroupCode,$string($.extend.defaultValue.employeeRecordNumber),$.extend.defaultValue.fullName],
          $boolean), ' - ')
      _condition:
        transform: $.extend.formType = 'view'
    - type: number
      name: employeeRecordNumber
      label: Employee Record Number
      unvisible: true
      _value:
        transform: $.fields.employee.employeeRecordNumber
    - type: group
      label: ' '
      readOnly: true
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - type: text
          label: Company
          name: company
          readOnly: true
        - type: text
          label: Legal Entity
          name: legalEntity
          readOnly: true
        - type: text
          label: Business Unit
          name: businessUnit
          readOnly: true
        - type: text
          label: Division
          name: division
          readOnly: true
        - type: text
          label: Department
          name: department
          readOnly: true
        - type: text
          label: Job Title
          name: jobTitle
          readOnly: true
        - type: text
          label: Contract Type
          name: contractType
          readOnly: true
        - type: text
          label: Location
          name: location
          readOnly: true
    - type: group
      n_cols: 2
      fields:
        - type: dateRange
          label: Effective Start Date
          name: startDate
          placeholder: dd/MM/yyyy
          mode: date-picker
          _condition:
            transform: $not($.extend.formType = 'view')
          setting:
            format: dd/MM/yyyy
            type: date
          validators:
            - type: required
          _value:
            transform: $.extend.formType = 'create' ? $now()
        - type: dateRange
          label: Effective End Date
          name: endDate
          placeholder: dd/MM/yyyy
          _condition:
            transform: $not($.extend.formType = 'view')
          validators:
            - type: ppx-custom
              args:
                transform: >-
                  $exists($.fields.endDate) and
                  $DateDiff($DateFormat($.fields.endDate, 'yyyy-MM-DD'),
                  $DateFormat($.fields.startDate, 'yyyy-MM-DD'), 'd') < 1
              text: End date must be greater than start date
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
    - type: group
      n_cols: 2
      fields:
        - type: select
          label: Type
          name: type
          clearFieldsAfterChange:
            - elementCode
          outputValue: value
          placeholder: Select Type
          _condition:
            transform: $not($.extend.formType = 'view')
          validators:
            - type: required
          _select:
            transform: $typesList()
          col: 1
        - type: select
          label: Element
          name: elementCode
          isLazyLoad: true
          outputValue: value
          _value:
            transform: $.extend.formType != 'create' ? $.extend.defaultValue.elementCode
          _condition:
            transform: $not($.extend.formType = 'view')
          _validateFn:
            transform: >-
              $exists($.value) ? ($.fields.type = 'UFALADTP_00002' ?
              ($deductionsList(1, 1, '',$exists($.fields.startDate) ?
              $.fields.startDate : $now(),$.value)[0] ? $deductionsList(1, 1,
              '',$exists($.fields.startDate) ? $.fields.startDate :
              $now(),$.value)[0] : '_setSelectValueNull' ) : $.fields.type =
              'UFALADTP_00001' ? ($unFixedAllowancesList($.extend.limit,
              $.extend.page, $.extend.search,$exists($.fields.startDate) ?
              $.fields.startDate : $now(),$.value)[0] ?
              $unFixedAllowancesList($.extend.limit, $.extend.page,
              $.extend.search,$exists($.fields.startDate) ? $.fields.startDate :
              $now(),$.value)[0] : '_setSelectValueNull') : [])
          validators:
            - type: required
          placeholder: Select Element
          _select:
            transform: >-
              $.fields.type = 'UFALADTP_00002' ? $deductionsList($.extend.limit,
              $.extend.page, $.extend.search,$.fields.startDate) : $.fields.type
              = 'UFALADTP_00001' ? $unFixedAllowancesList($.extend.limit,
              $.extend.page, $.extend.search,$.fields.startDate) : []
          col: 1
    - type: dateRange
      label: Effective Start Date
      name: startDate
      placeholder: dd/MM/yyyy
      mode: date-picker
      _condition:
        transform: $.extend.formType = 'view'
      setting:
        format: dd/MM/yyyy
        type: date
      _value:
        transform: $.extend.formType = 'create' ? $now()
    - type: dateRange
      label: Effective End Date
      name: endDate
      placeholder: dd/MM/yyyy
      validators:
        - type: ppx-custom
          args:
            transform: >-
              $exists($.fields.endDate) and
              $DateDiff($DateFormat($.fields.endDate, 'yyyy-MM-DD'),
              $DateFormat($.fields.startDate, 'yyyy-MM-DD'), 'd') < 1
          text: End date must be greater than start date
      _condition:
        transform: $.extend.formType = 'view'
      mode: date-picker
      setting:
        format: dd/MM/yyyy
        type: date
    - type: select
      label: Type
      name: type
      outputValue: value
      placeholder: Select Type
      _condition:
        transform: $.extend.formType = 'view'
      _select:
        transform: $typesList()
    - type: text
      name: currencyCodeCreate
      unvisible: true
      _value:
        transform: >-
          $not($isNilorEmpty($.variables._currencyCodeSelected)) ?
          $.variables._currencyCodeSelected : 'VND'
    - type: text
      name: currencyCodeEdit
      dependantFieldSkip: 2
      dependantField: $.fields.elementCode
      unvisible: true
      _value:
        transform: >-
          $not($isNilorEmpty($.variables._currencyCodeSelected)) ?
          $.variables._currencyCodeSelected : 'VND'
    - type: number
      label: Amount
      _condition:
        transform: $not($.extend.formType = 'view')
      name: amount
      number:
        format: currency
        precision: 0
      placeholder: Enter Amount
      addOnAfter:
        width: 100px
        name: currencyCode
        clearFieldsAfterChange:
          - currencyCodeCreate
        type: select
        outputValue: value
        _value:
          transform: >-
            $.extend.formType = 'create' ? $.fields.currencyCodeCreate :
            $.fields.currencyCodeEdit
        placeholder: Select Option
        _select:
          transform: $.variables._currenciesList
        validators:
          - type: required
      validators:
        - type: required
    - type: select
      label: Element
      name: elementCode
      outputValue: value
      _condition:
        transform: $.extend.formType = 'view'
      placeholder: Select Element
    - type: number
      label: Amount
      name: amount
      _condition:
        transform: $.extend.formType = 'view'
      number:
        format: currency
        precision: 4
        _suffix:
          transform: $.extend.defaultValue.currencyCode
    - type: radio
      label: Have Decision
      name: haveDecision
      clearFieldsAfterChange:
        - decisionNumber
        - signer
        - signDate
      value: false
      radio:
        - label: 'Yes'
          value: true
        - label: 'No'
          value: false
    - type: text
      label: Decision No
      name: decisionNumber
      placeholder: Enter Decision Number
      _condition:
        transform: >-
          $not($.extend.formType = 'view') and ($boolean($.fields.haveDecision)
          = true)
      validators:
        - type: required
        - type: maxLength
          args: '40'
          text: Decision No should not exceed 40 characters
    - type: text
      label: Decision No
      name: decisionNumber
      placeholder: Enter Decision Number
      _condition:
        transform: $.extend.formType = 'view'
    - type: select
      label: Signer
      name: signer
      placeholder: Select Signer
      isLazyLoad: true
      outputValue: value
      _condition:
        transform: >-
          $not($.extend.formType = 'view') and ($boolean($.fields.haveDecision)
          = true)
      _select:
        transform: $signerList($.extend.limit, $.extend.page, $.extend.search)
    - type: text
      label: Signer
      name: signerName
      placeholder: Select Signer
      outputValue: value
      _condition:
        transform: $.extend.formType = 'view'
    - type: dateRange
      label: Sign Date
      name: signDate
      placeholder: dd/MM/yyyy
      mode: date-picker
      _condition:
        transform: $boolean($.fields.haveDecision) = true
      setting:
        format: dd/MM/yyyy
        type: date
    - name: attachment
      label: File Attachment
      type: upload
      _condition:
        transform: $not($.extend.formType = 'view') and $.fields.haveDecision = true
      upload:
        size: 5
        accept:
          - application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
          - application/pdf
          - application/vnd.ms-excel
          - >-
            application/vnd.openxmlformats-officedocument.wordprocessingml.document
          - application/msword
    - name: attachmentResults
      label: File Attachment
      type: upload
      _condition:
        transform: $.extend.formType = 'view'
      upload:
        size: 100
        accept:
          - application/pdf
          - application/xls
          - application/xlsx
    - type: textarea
      label: Note
      name: note
      placeholder: Enter Note
      textarea:
        autoSize:
          minRows: 3
        maxCharCount: 1000
      validators:
        - type: maxLength
          args: 1000
  overview:
    dependentField: dataEmployee
    title: Employee Detail
    uri: >-
      /api/pr-employees/:{employeeId}:/employee-record-number/:{employeeRecordNumber}:/effective-date/:{dateToShowEmployee}:
    border: true
    display:
      - key: companyName
        label: Company
      - key: legalEntityName
        label: Legal entity
      - key: businessUnitName
        label: Business unit
      - key: divisionName
        label: Division
      - key: departmentName
        label: Department
      - key: jobTitleName
        label: Job Title
      - key: contractTypeName
        label: Contract Type
      - key: locationName
        label: Location
  sources:
    currenciesList:
      uri: '"/api/picklists/CURRENCY/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.code, 'value':
        $item.code}})[]
      disabledCache: true
    jobDatasDetail:
      uri: '"/api/personals/"  & $.employeeId & "/job-datas/" & $.id'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $
      disabledCache: true
      params:
        - employeeId
        - id
    jobDatasList:
      uri: '"/api/personals/"  & $.employeeId &  "/job-datas"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $.data
      disabledCache: true
      params:
        - employeeId
    signerList:
      uri: '"/api/personals/all-employees"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.employeeId & '-' &
        $item.fullName, 'value': $item.employeeId}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    deductionsList:
      uri: '"/api/deductions"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'sort':[{'field' :
        'longName'  , 'order': 'ascend'  }],'filter':
        [{'field':'code','operator':
        '$eq','value':$.code},{'field':'IsCombobox','operator':
        '$eq','value':true},{'field':'effectiveDate','operator':
        '$lte','value':$.startDate},{'field':'effectiveDate','operator':
        '$lte','value':$.endDate},{'field':'status','operator':
        '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code &')', 'value': $item.code, 'currencyCode' :
        $item.currency}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - startDate
        - code
    unFixedAllowancesList:
      uri: '"/api/un-fixed-allowances"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'sort':[{'field' :
        'longName'  , 'order': 'ascend'  }],'filter':
        [{'field':'code','operator':
        '$eq','value':$.code},{'field':'IsCombobox','operator':
        '$eq','value':true},{'field':'effectiveDate','operator':
        '$lte','value':$.startDate},{'field':'effectiveDate','operator':
        '$lte','value':$.endDate},{'field':'status','operator':
        '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code &')', 'value': $item.code, 'currencyCode' :
        $item.currency}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - startDate
        - code
        - endDate
    typesList:
      uri: '"/api/picklists/UNFIXEDALLOWANCEANDDEDUCTIONTYPE/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})
      disabledCache: true
    employeesList:
      uri: '"/api/pr-employees"'
      method: GET
      queryTransform: >-
        {'limit': $.limit ,'page': $.page, 'search': $.search,'filter':
        [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'employeeId','operator':
        '$eq','value':$.empId},{'field':'employeeGroupCode','operator':
        '$cont','value':$.grpCode},{'field':'employeeRecordNumber','operator':
        '$eq','value':$.ern}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label':
        $join($filter([$item.employeeId,$item.employeeGroupCode,$string($item.employeeRecordNumber),$item.fullName],
        $boolean), ' - ')  , 'value': {'employeeId': $item.employeeId, 'code':
        $item.employeeId, 'employeeRecordNumber':
        $string($item.employeeRecordNumber), 'jobDataId': $item.jobDataId}}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - empId
        - grpCode
        - ern
        - effectiveDate
  variables:
    _currenciesList:
      transform: $currenciesList()
    _currencyCodeSelected:
      transform: >-
        $not($isNilorEmpty($.fields.elementCode)) ? ($.fields.type =
        'UFALADTP_00002' ?
        $deductionsList(1,1,'',$.fields.startDate,$.fields.elementCode)[0].currencyCode 
        :
        $unFixedAllowancesList(1,1,'',$.fields.startDate,$.fields.elementCode)[0].currencyCode)
  footer:
    create: true
    update: true
    createdOn: createdAt
    updatedOn: updatedAt
    createdBy: createdBy
    updatedBy: updatedBy
filter_config:
  fields:
    - type: selectAll
      label: Employee
      name: employeeID
      labelType: type-grid
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $personalsList($.extend.limit, $.extend.page, $.extend.search)
      placeholder: Select Employee
    - type: text
      label: Full name
      labelType: type-grid
      name: fullName
      placeholder: Enter Full name
    - type: selectAll
      label: Employee Group
      name: employeeGroup
      labelType: type-grid
      mode: multiple
      placeholder: Select Employee Group
      _options:
        transform: $employeeGroupsList()
    - type: selectAll
      label: Type
      name: type
      labelType: type-grid
      placeholder: Select Type
      _options:
        transform: $typesList()
    - type: selectAll
      labelType: type-grid
      label: Element
      name: elementCode
      mode: multiple
      placeholder: Select Element
      _options:
        transform: $.variables._elementsList
    - name: currency
      labelType: type-grid
      type: selectAll
      label: Currency
      placeholder: Select Currency
      mode: multiple
      _options:
        transform: $currenciesList()
    - type: dateRange
      label: Effective Start Date
      labelType: type-grid
      name: startDate
      placeholder: dd/MM/yyyy
    - type: dateRange
      label: Effective End Date
      labelType: type-grid
      name: endDate
      placeholder: dd/MM/yyyy
    - type: number
      label: Instance
      labelType: type-grid
      placeholder: Enter Instance
      name: instanceId
    - type: text
      label: Decision No
      labelType: type-grid
      name: decisionNumber
      placeholder: Enter Decision Number
    - type: selectAll
      label: Signer
      labelType: type-grid
      name: signer
      placeholder: Select Signer
      isLazyLoad: true
      mode: multiple
      _options:
        transform: $signerList($.extend.limit, $.extend.page, $.extend.search)
    - type: dateRange
      labelType: type-grid
      label: Sign Date
      name: signDate
      placeholder: dd/MM/yyyy
    - type: text
      labelType: type-grid
      label: Note
      name: note
      placeholder: Enter Note
    - type: selectAll
      name: dataResource
      labelType: type-grid
      label: Data Resource
      placeholder: Select Data Resource
      mode: multiple
      options:
        - value: AddNew
          label: AddNew
        - value: Import
          label: Import
    - type: selectAll
      name: company
      labelType: type-grid
      label: Company
      isLazyLoad: true
      placeholder: Select Company
      mode: multiple
      _options:
        transform: $companiesList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      label: Legal Entity
      name: legalEntityCode
      isLazyLoad: true
      labelType: type-grid
      mode: multiple
      placeholder: Select Legal Entity
      _options:
        transform: $legalEntitiesList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      label: Bussiness Unit
      isLazyLoad: true
      name: businessUnit
      labelType: type-grid
      mode: multiple
      placeholder: Select Bussiness Unit
      _options:
        transform: $businessUnitList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      name: divisionCode
      label: Division
      placeholder: Select Department
      labelType: type-grid
      isLazyLoad: true
      mode: multiple
      _options:
        transform: >-
          $divisionsList($.extend.limit, $.extend.page,
          $.extend.search,$DateFormat($now(),'YYYY-MM-DDTHH:mm:ss'))
    - type: selectAll
      name: departmentCode
      labelType: type-grid
      label: Department
      placeholder: Select Department
      mode: multiple
      isLazyLoad: true
      _options:
        transform: >-
          $departmentsList($.extend.limit, $.extend.page,
          $.extend.search,$DateFormat($now(),'YYYY-MM-DDTHH:mm:ss'))
    - type: selectAll
      label: Job Title
      labelType: type-grid
      name: job
      mode: multiple
      isLazyLoad: true
      placeholder: Select Job Title
      _options:
        transform: $jobCodesList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      label: Contract Type
      labelType: type-grid
      name: contractTypeCode
      placeholder: Select Contract Type
      mode: multiple
      _options:
        transform: $contractTypeList()
    - type: selectAll
      labelType: type-grid
      label: Location
      isLazyLoad: true
      name: locationCode
      placeholder: Select Location
      mode: multiple
      _options:
        transform: $locationsList($.extend.limit, $.extend.page, $.extend.search)
    - type: dateRange
      labelType: type-grid
      name: updatedAt
      label: Last Edited Time
      settings:
        format: dd/MM/yyyy
        mode: date
    - name: updatedBy
      label: Last Updated By
      type: text
      labelType: type-grid
      placeholder: Enter Last Updated By
  filterMapping:
    - operator: $or
      valueField:
        - field: employeeId
          operator: $elemMatch
          valueField: employeeID.(employeeId)
        - field: employeeRecordNumber
          operator: $elemMatch
          valueField: employeeID.(ern)
        - field: employeeGroupCode
          operator: $elemMatch
          valueField: employeeID.(empGroup)
    - field: fullName
      operator: $cont
      valueField: fullName
    - field: employeeRecordNumber
      operator: $eq
      valueField: employeeRecordNumber
    - field: employeeGroupCode
      operator: $in
      valueField: employeeGroup.(value)
    - field: type
      operator: $in
      valueField: type.(value)
    - field: elementCode
      operator: $in
      valueField: elementCode.(value)
    - field: amount
      operator: $gte
      valueField: fromAmount
    - field: currencyCode
      operator: $in
      valueField: currency.(value)
    - field: startDate
      operator: $between
      valueField: startDate
    - field: endDate
      operator: $between
      valueField: endDate
    - field: instance
      operator: $eq
      valueField: instanceId
    - field: haveDecision
      operator: $eq
      valueField: haveDecision
    - field: decisionNumber
      operator: $cont
      valueField: decisionNumber
    - field: signer
      operator: $in
      valueField: signer.(value)
    - field: signDate
      operator: $between
      valueField: signDate
    - field: note
      operator: $cont
      valueField: note
    - field: dataResource
      operator: $in
      valueField: dataResource.(value)
    - field: companyCode
      operator: $in
      valueField: company.(value)
    - field: legalEntityCode
      operator: $in
      valueField: legalEntityCode.(value)
    - field: businessUnitCode
      operator: $in
      valueField: businessUnit.(value)
    - field: divisionCode
      operator: $in
      valueField: divisionCode.(value)
    - field: departmentCode
      operator: $in
      valueField: departmentCode.(value)
    - field: jobTitleCode
      operator: $in
      valueField: job.(value)
    - field: contractTypeCode
      operator: $in
      valueField: contractTypeCode.(value)
    - field: locationCode
      operator: $in
      valueField: locationCode.(value)
    - field: updatedAt
      operator: $between
      valueField: updatedAt
    - field: updatedBy
      operator: $cont
      valueField: updatedBy
  sources:
    divisionsList:
      uri: '"/api/divisions"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    contractTypeList:
      uri: '"/api/picklists/CONTRACTTYPE/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})
      disabledCache: true
    jobCodesList:
      uri: '"/api/job-codes"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    businessUnitList:
      uri: '"/api/business-units"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    locationsList:
      uri: '"/api/locations"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    companiesList:
      uri: '"/api/companies"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    legalEntitiesList:
      uri: '"/api/legal-entities"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    currenciesList:
      uri: '"/api/picklists/CURRENCY/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
    employeeGroupsList:
      uri: '"/api/employee-groups"'
      method: GET
      queryTransform: '{''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.employeeGroup.default,
        'value': $item.code}})
      disabledCache: true
    personalsList:
      uri: '"/api/personals"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label':
        $join($filter([$item.employeeId,$item.employeeGroup,$string($item.employeeRecordNumber),$item.fullName],
        $boolean), ' - ') , 'value':{'employeeId':$item.employeeId , 'ern':
        $item.employeeRecordNumber,'empGroup' :
        $.item.employeeGroup},'employeeId':$item.employeeId,
        'ern':$item.employeeRecordNumber, 'empGroup' : $item.employeeGroup }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    departmentsList:
      uri: '"/api/departments/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page,'search': $.search, 'filter':
        [{'field':'effectiveDate','operator': '$eq','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
    signerList:
      uri: '"/api/personals/all-employees"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.fullName & '-' &
        $item.employeeId, 'value': $item.employeeId}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    deductionsList:
      uri: '"/api/deductions"'
      method: GET
      queryTransform: '{''limit'': 10000,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code &')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - effectiveDate
    unFixedAllowancesList:
      uri: '"/api/un-fixed-allowances"'
      method: GET
      queryTransform: '{''limit'': 10000,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code &')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - effectiveDate
    typesList:
      uri: '"/api/picklists/UNFIXEDALLOWANCEANDDEDUCTIONTYPE/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})
      disabledCache: true
  variables:
    _elementsList:
      transform: >-
        $not($isNilorEmpty($.fields.type)) ? ($count($.fields.type) = 2 ?
        [$.variables._unfixedAllowance,$.variables._deduction] :
        $count($.fields.type) < 1 ?
        [$.variables._unfixedAllowance,$.variables._deduction] :
        $filter($.fields.type, function($item) {$item.value =
        'UFALADTP_00001'}).value = 'UFALADTP_00001' ?
        $.variables._unfixedAllowance : $.variables._deduction ) :
        [$.variables._unfixedAllowance,$.variables._deduction]
    _unfixedAllowance:
      transform: $unFixedAllowancesList()
    _deduction:
      transform: $deductionsList()
layout_options:
  show_detail_history: false
  show_dialog_form_save_add_button: true
  delete_multi_items: true
  is_popup: true
  apply_delete_multi_items_to_delete_one: true
  custom_delete_body: '{''ids'': $map($.data,function($item){$item.id})[]}'
  is_upload_file: true
  tool_table:
    - id: import
      paramsRedirect:
        type: PR_OBJECT
        entityOrObj: UnFixedAllowanceAndDeduction
    - id: export
      icon: icon-download-simple
  toolTable:
    export: true
    adjustDisplay: true
  show_filter_results_message: true
  store_selected_items: true
  hide_action_row: true
  show_dialog_duplicate_button: false
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    icon: pencil
    type: tertiary
  - id: delete
    icon: trash
    type: tertiary
backend_url: /api/un-fixed-allowance-and-deductions
screen_name: un-fixed-allowance-and-deductions
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: employeeId
    defaultName: EmployeeId
  - name: employeeRecordNumber
    defaultName: EmployeeRecordNumber
  - name: companyCode
    defaultName: CompanyCode
  - name: legalEntityCode
    defaultName: LegalEntityCode
  - name: businessUnitCode
    defaultName: BusinessUnitCode
  - name: divisionCode
    defaultName: DivisionCode
  - name: departmentCode
    defaultName: DepartmentCode
  - name: jobTitleCode
    defaultName: JobCode
  - name: locationCode
    defaultName: LocationCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Manage Additional Earnings/Deductions Information
  parent:
    title: PR Setting
