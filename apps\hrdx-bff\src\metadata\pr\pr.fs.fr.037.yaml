id: PR.FS.FR.037
status: draft
sort: 273
user_created: 2aa5db12-fa54-46d6-8e05-9e421858de0c
date_created: '2024-07-25T07:19:21.672Z'
user_updated: b403d5da-d684-45e4-ae6d-e25223baedc5
date_updated: '2025-07-26T08:24:03.662Z'
title: Manage Employee Salary Information
requirement:
  time: 1747822647497
  blocks:
    - id: HSrBrFadh7
      type: paragraph
      data:
        text: >-
          - <PERSON><PERSON> thống cho phép các CTTV thực hiện quản lý thông tin quá trình
          lương của nhân viên.
    - id: PAFmfH1HTn
      type: paragraph
      data:
        text: >-

          - B<PERSON> phân nhân sự CTTV có thể thêm mới/chỉnh sửa/xóa các thông tin
          lương nhân viên theo phân quyền cán bộ quản lý
    - id: GsCSmqyp4o
      type: paragraph
      data:
        text: >-

          - <PERSON> phép người dùng thực hiện import hàng loạt thông tin quá trình
          lương đã được phê duyệt bên ngoài lên hệ thống (Chức năng import phát
          triển sau).
    - id: ybiVAkHcrJ
      type: paragraph
      data:
        text: >

          - Trường hợp dòng dữ liệu chứa mã nhân viên, ngày hiệu lực đã tồn tại,
          hệ thống thực hiện cảnh báo để bộ phận nhân sự xác nhận có thực hiện
          cập nhật vào dữ liệu đã có trên hệ thống không? Trường hợp dữ liệu lỗi
          (mã nhân viên chưa tồn tại, mã khoản hỗ trợ chưa đúng, …), hệ thống
          cảnh báo và nêu rõ lý do.
    - id: IP_HRUBu40
      type: paragraph
      data:
        text: >-
          - Hệ thống tự động đồng bộ thông tin lương đã được phê duyệt bởi các
          cấp có thẩm quyền từ quy trình: HR.BP.12
    - id: VGXnGJTc_3
      type: paragraph
      data:
        text: |
          – Quy trình quản lý hợp đồng.
    - id: dLK4hrWavT
      type: paragraph
      data:
        text: |
          - Lưu ý hiển thị dữ liệu trên lưới:
    - id: c94mdYmU7j
      type: paragraph
      data:
        text: '+ Được group theo thông tin: Mã nhân viên '
    - id: HL93194dF5
      type: paragraph
      data:
        text: '+ Mã hồ sơ công việc '
    - id: BtaPiFMPkz
      type: paragraph
      data:
        text: '+ Tên nhân viên '
    - id: ywLSYACON2
      type: paragraph
      data:
        text: |

          + Dòng lương hiệu lực được nằm trên.
    - id: IcLVfKtoCm
      type: paragraph
      data:
        text: >-
          + Khi có dòng lương mới hiệu lực, dòng cũ sẽ tự động ghi nhận ngày kết
          thúc tương ứng.&nbsp;&nbsp;
  version: 2.30.7
screen_design: null
module: PR
local_fields:
  - code: effectiveDateFrom
    title: Effective Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DateHyperlink
      collection: field_types
    pinned: true
    show_sort: true
  - code: employeeSalaryTypeName
    title: Salary Type
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: salaryAdminPlanName
    title: Salary Plan
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: gradeName
    title: Salary Grade
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: stepName
    title: Salary Step
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: salaryTypeName
    title: Frequency
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: frequency
    title: Percent
    data_type:
      key: Nmberic
      collection: data_types
    display_type:
      key: Percent
      collection: field_types
    show_sort: true
  - code: totalSalaryAfterConvert
    title: Pro Rata Salary (Gross)
    data_type:
      key: Money Amount
      collection: data_types
    display_type:
      key: Currency
      collection: field_types
    options__tabular__align: right
    show_sort: false
  - code: totalSalary
    title: Official Salary (Gross)
    data_type:
      key: Money Amount
      collection: data_types
    display_type:
      key: Currency
      collection: field_types
    options__tabular__align: right
    show_sort: false
  - code: expectedIncreaseDate
    title: Expected Promotion Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    show_sort: true
  - code: decisionNo
    title: Decision Number
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: incomePackageName
    title: Package
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: decisionSignerName
    title: Signer
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: decisionSignDate
    title: Sign Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    show_sort: true
  - code: companyName
    title: Company
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: legalEntityName
    title: Legal Entity
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: businessUnitName
    title: Business Unit
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: divisionName
    title: Division
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: departmentName
    title: Department
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: jobCodeName
    title: Job Title
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: contractTypeName
    title: Contract Type
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: locationName
    title: Location
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: updatedBy
    title: Last Updated By
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: updatedAt
    title: Last Updated On
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
    show_sort: true
mock_data: []
local_buttons: null
layout: layout-table
form_config:
  formSize:
    create: largex
    edit: largex
    proceed: largex
    view: largex
  defaultValidateError: Required fields have not been filled in completely (fields marked with *)
  fields:
    - type: group
      label: Basic Information
      disableEventCollapse: false
      collapse: false
      n_cols: 2
      _titleStyle:
        transform: '$.extend.formType = ''view'' ? {''paddingTop'': ''0px'', ''border'': ''none''}'
      fields:
        - type: select
          name: employee
          clearFieldsAfterChange:
            - objectJobData
            - rateCodeGroup
            - rateCode
            - amountPickedValue
          label: Employee
          placeholder: Select Employee
          isLazyLoad: true
          col: 2
          validators:
            - type: required
          _select:
            transform: >-
              $employeesList($.extend.limit, $.extend.page, $.extend.search,
              null,null,null, $.fields.effectiveDateFrom)
          _validateFn:
            transform: >-
              $not($isNilorEmpty($.value.id)) ? (
              $isNilorEmpty($employeesList(1, 1,'',
              $.value.id,null,$.value.employeeRecordNumber,
              $.fields.effectiveDateFrom)[0]) ?  '_setSelectValueNull' )
          _condition:
            transform: $.extend.formType = 'create' and $not($.extend.isDuplicate)
          outputValue: value
        - type: select
          name: employee
          key: employee
          col: 2
          label: Employee
          placeholder: Select Employee
          isLazyLoad: true
          validators:
            - type: required
          _select:
            transform: >-
              $employeesList(1,1,'',$.extend.defaultValue.employeeId,$.extend.defaultValue.employeeGroupCode,$.extend.defaultValue.employeeRecordNumber,$.fields.effectiveDateFrom)
          _disabled:
            transform: 'true'
          _condition:
            transform: $.extend.formType = 'edit'
          outputValue: value
        - type: select
          name: employee
          key: employee
          clearFieldsAfterChange:
            - objectJobData
          col: 2
          label: Employee
          placeholder: Select Employee
          validators:
            - type: required
          _select:
            transform: $employeesList($.extend.limit, $.extend.page, $.extend.search)
          isLazyLoad: true
          _condition:
            transform: $.extend.isDuplicate
          outputValue: value
        - type: text
          name: employeeIdView
          key: employeeIdView
          label: Employee
          _condition:
            transform: $.extend.formType = 'view'
          _value:
            transform: >-
              $join($filter([$.extend.defaultValue.employeeId,$.extend.defaultValue.employeeGroupCode,$string($.extend.defaultValue.employeeRecordNumber),$.extend.defaultValue.fullName],
              $boolean), ' - ')
        - type: text
          name: employeeId
          label: Employee
          unvisible: true
          dependantField: $.fields.employee.id
          _value:
            transform: $.fields.employee.id
        - type: text
          name: employeeRecordIdJobData
          label: Employee
          unvisible: true
          dependantField: $.fields.employeeId; $.fields.effectiveDateFrom
          _value:
            transform: >-
              ($not($isNilorEmpty($.variables._jobData)) ?
              $.variables._jobData.employeeId &
              $.variables._jobData.employeeRecordNumber &
              $string($.fields.dateToShowEmployee))
        - type: text
          name: employeeRecordId
          label: Employee
          unvisible: true
          dependantField: $.fields.employee.id
          _value:
            transform: $.variables._jobData.employeeId
        - type: text
          name: dateToShowEmployee
          unvisible: true
          _value:
            transform: >-
              $not($isNilorEmpty($.fields.effectiveDateFrom)) ?
              $DateToTimestampUTC($.fields.effectiveDateFrom) :
              $DateToTimestampUTC($now())
        - type: text
          name: dataEmployee
          unvisible: true
          dependantField: $.fields.employeeId; $.fields.effectiveDateFrom
          _value:
            transform: >-
              $not($isNilorEmpty($.fields.employeeId)) ? {'employeeId':
              $.fields.employeeId , 'employeeRecordNumber':
              $.fields.employeeRecordNumber, 'dateToShowEmployee':
              $.fields.dateToShowEmployee} : null
        - type: text
          name: companyCode
          unvisible: true
          dependantField: $.fields.employee.id
          _value:
            transform: $.variables._jobData.companyCode
        - type: text
          name: employeeRecordNumber
          label: Employee Record Number
          unvisible: true
          _value:
            transform: $string($.fields.employee.employeeRecordNumber)
        - type: text
          name: objectJobData
          unvisible: true
          _value:
            transform: >-
              $not($isNilorEmpty($.fields.employeeId)) ?
              $jobDatasDetail($.fields.employeeId,
              $.fields.employeeRecordNumber,$.fields.dateToShowEmployee) : null
        - type: text
          name: jobDataId
          label: jobDataId
          unvisible: true
          dependantField: $.fields.employee
          clearFieldsAfterChange:
            - rateCodeGroup
          _value:
            transform: $string($.fields.objectJobData.id)
        - type: text
          name: companyName
          key: companyName
          label: Company
          _condition:
            transform: $.extend.formType = 'view'
          _value:
            transform: $.extend.addOnValue.dataDetail.companyName
        - type: text
          name: legalEntityName
          key: legalEntityName
          label: Legal Entity
          _condition:
            transform: $.extend.formType = 'view'
          _value:
            transform: $.extend.addOnValue.dataDetail.legalEntityName
        - type: text
          name: businessUnitName
          key: businessUnitName
          label: Business Unit
          _condition:
            transform: $.extend.formType = 'view'
          _value:
            transform: $.extend.addOnValue.dataDetail.businessUnitName
        - type: text
          name: divisionName
          key: divisionName
          label: Division
          _condition:
            transform: $.extend.formType = 'view'
          _value:
            transform: $.extend.addOnValue.dataDetail.divisionName
        - type: text
          name: departmentName
          key: departmentName
          label: Department
          _condition:
            transform: $.extend.formType = 'view'
          _value:
            transform: $.extend.addOnValue.dataDetail.departmentName
        - type: text
          name: jobCodeName
          key: jobCodeName
          label: Job Title
          _condition:
            transform: $.extend.formType = 'view'
          _value:
            transform: $.extend.addOnValue.dataDetail.jobCodeName
        - type: text
          name: contractTypeName
          key: contractTypeName
          label: Contract Type
          _condition:
            transform: $.extend.formType = 'view'
          _value:
            transform: $.extend.addOnValue.dataDetail.contractTypeName
        - type: text
          name: locationName
          key: locationName
          label: Location
          _condition:
            transform: $.extend.formType = 'view'
          _value:
            transform: $.extend.addOnValue.dataDetail.locationName
        - type: dateRange
          name: effectiveDateFrom
          key: effectiveDateFrom
          label: Effective Date
          placeholder: dd/MM/yyyy
          clearFieldsAfterChange:
            - objectJobData
          mode: date-picker
          _value:
            transform: >-
              $.extend.formType = 'create' ?
              $not($exists($.fields.effectiveDate)) ?  $now()
          setting:
            format: dd/MM/yyyy
            type: date
          validators:
            - type: required
          _condition:
            transform: $not($.extend.formType = 'view')
        - type: dateRange
          name: effectiveDateFromView
          key: effectiveDateFrom
          label: Effective Date
          placeholder: dd/MM/yyyy
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
          _condition:
            transform: $.extend.formType = 'view'
          _value:
            transform: $.extend.defaultValue.effectiveDateFrom
        - type: dateRange
          name: expectedIncreaseDate
          key: expectedIncreaseDate
          label: Expected Promotion Date
          validators:
            - type: ppx-custom
              args:
                transform: >-
                  $exists($.fields.expectedIncreaseDate) and
                  $DateDiff($DateFormat($.fields.expectedIncreaseDate,
                  'yyyy-MM-DD'), $DateFormat($.fields.effectiveDateFrom,
                  'yyyy-MM-DD'), 'd') < 1
              text: Expected Promotion Date must be greater than Effective Date
          placeholder: dd/MM/yyyy
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
        - type: text
          label: Decision Number
          key: decisionNoView
          name: decisionNo
          _condition:
            transform: >-
              $.extend.formType = 'view' and
              $boolean($.extend.defaultValue.isDecision)
        - type: radio
          label: Have Decision?
          key: isDecision
          value: false
          name: isDecision
          radio:
            - label: 'Yes'
              value: true
            - label: 'No'
              value: false
          _condition:
            transform: $.extend.formType = 'create' or $.extend.formType = 'edit'
        - type: text
          label: Decision Number
          key: decisionNo
          name: decisionNo
          placeholder: Enter Decision Number
          validators:
            - type: required
            - type: maxLength
              args: 40
              text: Decision Number should not exceed 40 characters
          _condition:
            transform: >-
              $boolean($.fields.isDecision) and ($.extend.formType = 'create' or
              $.extend.formType = 'edit')
        - type: text
          label: Signer
          key: decisionSignerName
          name: decisionSignerName
          _condition:
            transform: >-
              $.extend.formType = 'view' and
              $boolean($.extend.defaultValue.isDecision)
        - type: select
          label: Signer
          name: decisionSignerId
          placeholder: Select Signer
          isLazyLoad: true
          outputValue: value
          _condition:
            transform: $not($.extend.formType = 'view') and $boolean($.fields.isDecision)
          _select:
            transform: $signerList($.extend.limit, $.extend.page, $.extend.search)
        - type: dateRange
          name: decisionSignDate
          key: decisionSignDate
          label: Sign Date
          placeholder: dd/MM/yyyy
          mode: date-picker
          n_col: 2
          setting:
            format: dd/MM/yyyy
            type: date
          _condition:
            transform: >-
              $boolean($.fields.isDecision) and ($.extend.formType = 'create' or
              $.extend.formType = 'edit')
        - type: dateRange
          label: Sign Date
          mode: date-picker
          key: decisionSignDateView
          name: decisionSignDate
          setting:
            format: dd/MM/yyyy
            type: date
          _condition:
            transform: >-
              $.extend.formType = 'view' and
              $boolean($.extend.defaultValue.isDecision)
        - type: upload
          label: Attach file
          name: attachFile
          upload:
            accept:
              - >-
                application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
              - application/pdf
              - application/vnd.ms-excel
              - >-
                application/vnd.openxmlformats-officedocument.wordprocessingml.document
              - application/msword
            size: 5
          _condition:
            transform: $not($.extend.formType = 'view') and $boolean($.fields.isDecision)
        - name: isNoChangeFile
          label: ''
          dependantField: attachFile
          _value:
            transform: $not($type($.fields.attachFile) = 'array')
          unvisible: true
          type: checkbox
        - name: attachmentResults
          label: File Attachment
          type: upload
          _condition:
            transform: $.extend.formType = 'view'
          upload:
            size: 100
            accept:
              - application/pdf
              - application/xls
              - application/xlsx
    - type: group
      label: Salary Information
      collapse: false
      disableEventCollapse: false
      n_cols: 2
      fields:
        - label: Salary Type
          type: select
          key: employeeSalaryTypeCode
          name: employeeSalaryTypeCode
          placeholder: Select Salary Type
          clearFieldsAfterChange:
            - amount
            - rateCodeGroup
            - amountPickedValue
            - salaryAdminPlanName
            - gradeName
            - stepName
          validators:
            - type: required
          outputValue: value
          _select:
            transform: $.variables._salaryTypes
          _value:
            transform: $boolean($.fields.amountPickedValue.rateCode) ? 'SLRT_00002'
          _condition:
            transform: $.extend.formType = 'create' or $.extend.formType = 'edit'
        - type: text
          name: employeeSalaryTypeName
          label: Salary Type
          key: employeeSalaryTypeName
          _condition:
            transform: $.extend.formType = 'view'
        - type: text
          name: salaryByTierMasterName
          label: Payroll
          key: salaryByTierMasterName
          _condition:
            transform: >-
              $.extend.defaultValue.employeeSalaryTypeCode = 'SLRT_00002' and
              $.extend.formType = 'view'
        - type: text
          name: rateCodeView
          key: rateCoded
          label: Rate Code
          _condition:
            transform: >-
              $.extend.defaultValue.employeeSalaryTypeCode = 'SLRT_00002' and
              $.extend.formType = 'view'
        - label: Payroll
          type: select
          key: payroll
          placeholder: Select payroll
          dependantField: $.fields.jobDataId; $.fields.employeeSalaryTypeCode
          name: salaryByTierMasterCode
          outputValue: value
          _condition:
            transform: >-
              $.fields.employeeSalaryTypeCode = 'SLRT_00002' and
              $not($.extend.formType = 'view')
          clearFieldsAfterChange:
            - amount
            - rateCodeGroup
            - rateCode
            - amountPickedValue
            - salaryAdminPlanName
            - gradeName
            - stepName
          validators:
            - type: required
          _select:
            transform: $.variables._payrolls
          _value:
            transform: >-
              $isNilorEmpty($.fields.salaryByTierMasterCode) ?
              ($not($isNilorEmpty($.fields.amountPickedValue.salaryByTierMasterCode))
              ?  $.fields.amountPickedValue.salaryByTierMasterCode :
              $.variables._payrolls[0].value)
        - type: selectCustom
          name: rateCodeGroup
          label: Rate Code
          placeholder: Choose Rate Code
          clearFieldsAfterChange:
            - amount
            - rateCode
            - amountPickedValue
          validators:
            - type: required
          actions:
            - id: search
              title: Select Rate Code
          isLazyLoad: true
          outputValue: value
          _condition:
            transform: >-
              $.fields.employeeSalaryTypeCode = 'SLRT_00002' and
              $not($.extend.formType = 'view')
          _select:
            transform: >-
              $not($.extend.formType = 'view') ? $rateCodeList($.extend.limit,
              $.extend.page, $.extend.search,$.fields.salaryByTierMasterCode,
              $.extend.filter.salaryAdminPlanCode, $.extend.filter.gradeCode,
              $.extend.filter.stepCode, $.fields.jobDataId) : []
          _value:
            transform: $.fields.amountPickedValue.rateCode
          _validateFn:
            transform: >-
              ($exists($.fields.amountPickedValue.rateCode) ? ($selectedItem :=
              $selectedRateCodeList($.fields.salaryByTierMasterCode,
              $.extend.filter.salaryAdminPlanCode, $.extend.filter.gradeCode,
              $.extend.filter.stepCode, $.fields.jobDataId,
              $.fields.amountPickedValue.rateCode)[0];
              $not($isNilorEmpty($selectedItem)) ? $selectedItem))
          actionsConfig:
            search:
              _addOnValue:
                transform: '{''jobDataId'': $.fields.jobDataId}'
              formConfig:
                fields:
                  - type: group
                    collapsed: false
                    disableEventCollapse: false
                    n_cols: 3
                    fields:
                      - type: select
                        label: Salary Plan
                        name: salaryAdminPlanCode
                        key: rateSalaryAdminPlanCode
                        placeholder: Select Salary Plan
                        outputValue: value
                        isLazyLoad: true
                        _select:
                          transform: $plans($.extend.limit,$.extend.page,$.extend.search)
                      - type: select
                        label: Salary Grade
                        key: rateGradeCode
                        name: gradeCode
                        placeholder: Select Salary Grade
                        isLazyLoad: true
                        outputValue: value
                        _select:
                          transform: >-
                            $grades($.extend.limit,$.extend.page,$.extend.search)
                      - type: select
                        label: Salary Step
                        key: rateStepCode
                        name: stepCode
                        placeholder: Select Salary Step
                        outputValue: value
                        mode: multiple
                        _select:
                          transform: $steps($.extend.limit,$.extend.page,$.extend.search)
                sources:
                  plans:
                    uri: '"/api/salary-admin-plans"'
                    method: GET
                    queryTransform: >-
                      {'limit': $.limit,'page': $.page,'search':
                      $.search,'filter': []}
                    bodyTransform: ''
                    headerTransform: ''
                    resultTransform: >-
                      $map($.data, function($item) {{'label': $item.name.default
                      & ' (' & $item.code & ')', 'value': $item.code}})[]
                    disabledCache: true
                    params:
                      - limit
                      - page
                      - search
                  grades:
                    uri: '"/api/grades"'
                    method: GET
                    queryTransform: >-
                      {'limit': $.limit,'page': $.page,'search':
                      $.search,'filter': []}
                    bodyTransform: ''
                    headerTransform: ''
                    resultTransform: >-
                      $map($.data, function($item) {{'label': $item.name.default
                      & ' (' & $item.code & ')', 'value': $item.code}})[]
                    disabledCache: true
                    params:
                      - limit
                      - page
                      - search
                  steps:
                    uri: '"/api/steps"'
                    method: GET
                    queryTransform: >-
                      {'limit': $.limit,'page': $.page,'search':
                      $.search,'filter': []}
                    bodyTransform: ''
                    headerTransform: ''
                    resultTransform: >-
                      $map($.data, function($item) {{'label': $item.name.default
                      & ' (' & $item.code & ')', 'value': $item.code}})[]
                    disabledCache: true
                    params:
                      - limit
                      - page
                      - search
              table:
                fields:
                  - name: code
                    key: tableRateCode
                    label: Rate Code
                    displayType: Label
                  - name: salaryAdminPlanName
                    key: tableSalaryAdminPlanCode
                    label: Salary Plan
                    displayType: Label
                  - name: gradeName
                    key: tableGradeCode
                    label: Salary Grade
                    displayType: Label
                  - name: stepName
                    key: tableStepCode
                    label: Salary Step
                    displayType: Label
                  - name: wageClassificationName
                    key: tableWageClassification
                    label: Entry Type
                    displayType: Label
                  - name: amount
                    key: tableAmount
                    label: Amount
                    type: number
                    number:
                      format: currency
                      max: '999999999999999'
                      precision: 3
                    displayType: Currency
                  - name: currencyName
                    key: tableAmount
                    label: Currency
                    displayType: Label
        - type: text
          label: Salary Plan
          name: salaryAdminPlanName
          disabled: true
          placeholder: ' '
          _condition:
            transform: >-
              $.fields.employeeSalaryTypeCode = 'SLRT_00002' and
              $not($.extend.formType = 'view')
          _value:
            transform: >-
              $not($isNilorEmpty($.fields.rateCodeGroup.salaryAdminPlanName)) ?
              $.fields.rateCodeGroup.salaryAdminPlanName : ' '
        - type: text
          label: Salary Grade
          name: gradeName
          disabled: true
          placeholder: ' '
          _condition:
            transform: >-
              $.fields.employeeSalaryTypeCode = 'SLRT_00002' and
              $not($.extend.formType = 'view')
          _value:
            transform: >-
              $not($isNilorEmpty($.fields.rateCodeGroup.gradeName)) ?
              $.fields.rateCodeGroup.gradeName : ' '
        - type: text
          label: Salary Step
          name: stepName
          key: group1Step
          placeholder: ' '
          _condition:
            transform: >-
              $.fields.employeeSalaryTypeCode = 'SLRT_00002' and
              $not($.extend.formType = 'view')
          disabled: true
          _value:
            transform: >-
              $not($isNilorEmpty($.fields.rateCodeGroup.stepName)) ?
              $.fields.rateCodeGroup.stepName : ' '
        - type: text
          name: salaryAdminPlanName
          key: salaryAdminPlanName
          label: Salary Plan
          _condition:
            transform: >-
              $.extend.defaultValue.employeeSalaryTypeCode = 'SLRT_00002' and
              $.extend.formType = 'view'
        - type: text
          name: gradeName
          key: gradeName
          label: Salary Grade
          _condition:
            transform: >-
              $.extend.defaultValue.employeeSalaryTypeCode = 'SLRT_00002' and
              $.extend.formType = 'view'
        - type: text
          name: stepName
          label: Salary Step
          key: stepName
          _condition:
            transform: >-
              $.extend.defaultValue.employeeSalaryTypeCode = 'SLRT_00002' and
              $.extend.formType = 'view'
        - type: text
          name: wageClassificationName
          key: wageClassificationName
          label: Entry Type
          _condition:
            transform: >-
              $.extend.defaultValue.employeeSalaryTypeCode = 'SLRT_00002' and
              $.extend.formType = 'view'
        - type: text
          label: Entry Type
          key: group1EntryType
          name: wageClassificationName
          disabled: true
          _condition:
            transform: $.extend.formType = 'edit' or $.extend.formType = 'create'
          _value:
            transform: $not($isNilorEmpty($.fields.employeeSalaryTypeCode)) ? 'Số tiền'
        - type: text
          label: Entry Type
          key: wageClassification
          name: wageClassification
          unvisible: true
          _value:
            transform: >-
              $exists($.fields.rateCodeGroup.wageClassification) ?
              $.fields.rateCodeGroup.wageClassification :  'WGCSFT_00001'
        - name: amountPickedValue
          type: text
          unvisible: true
        - type: number
          label: Amount
          name: amount
          placeholder: Enter Amount
          _disabled:
            transform: $.fields.employeeSalaryTypeCode = 'SLRT_00002'
          _value:
            dependants:
              - $.fields.employeeSalaryTypeCode
              - $.fields.rateCodeGroup
            transform: >-
              ($.fields.employeeSalaryTypeCode = 'SLRT_00002' ?
              ($exists($.fields.rateCodeGroup.amount) ?
              $.fields.rateCodeGroup.amount))
          validators:
            - type: required
            - type: min
              args: 1
              text: The amount  must be greater than 0
          number:
            format: currency
            max: 9999999999999.99
            precision: 2
            min: '1'
          radioTable:
            autoSelectFirst: true
            clientPagination: true
            columns:
              - code: salaryByTierMasterName
                title: Payroll
                type: text
                align: start
                width: 150px
              - code: rateCode
                title: Rate Code
                type: text
                align: start
                width: 150px
              - code: salaryAdminPlanName
                title: Salary Plan
                type: text
                align: start
                width: 150px
              - code: gradeName
                title: Salary Grade
                type: textarea
                align: start
                width: 150px
              - code: stepName
                title: Salary Step
                type: text
                align: start
                width: 250px
              - code: amount
                title: Amount
                type: number
                number:
                  format: currency
                  max: '999999999999999'
                  precision: 3
                align: start
                width: 250px
              - code: currencyName
                title: Currency
                type: text
                align: start
                width: 250px
            source: /api/salary-by-tiers/rate-code
            type: array
            query: amount
            key: amount
            extraFilterConfig:
              _value:
                transform: '{''jobDataId'': $.fields.jobDataId}'
              filterMapping:
                - field: jobDataId
                  operator: $eq
                  valueField: jobDataId
            filterConfig:
              _addOnValue:
                transform: '{''jobDataId'': $.fields.jobDataId, ''amount'': $.fields.amount}'
              filterMapping:
                - field: code
                  operator: $eq
                  valueField: code
                - field: rateCode
                  operator: $eq
                  valueField: rateCode.(value)
                - field: salaryAdminPlanCode
                  operator: $eq
                  valueField: salaryAdminPlanCode.(value)
                - field: gradeCode
                  operator: $eq
                  valueField: gradeCode.(value)
                - field: stepCode
                  operator: $in
                  valueField: stepCode.(value)
              fields:
                - type: group
                  collapsed: false
                  key: rateCodeGroup1
                  disableEventCollapse: false
                  n_cols: 3
                  fields:
                    - type: select
                      label: Payroll
                      name: code
                      placeholder: Select Payroll
                      outputValue: value
                      clearFieldsAfterChange:
                        - rateCode
                      isLazyLoad: true
                      _select:
                        transform: >-
                          $payrolls($.extend.limit, $.extend.page,
                          $.extend.search, $.extend.addOnValue.jobDataId)
                    - type: select
                      label: Rate Code
                      name: rateCode
                      placeholder: Select Rate Code
                      isLazyLoad: true
                      _disabled:
                        transform: $isNilorEmpty($.fields.code)
                      _select:
                        transform: >-
                          $rateCodeAmountList($.extend.addOnValue.amount,$.fields.code,
                          $.extend.addOnValue.jobDataId)[]
                    - type: select
                      label: Salary Plan
                      name: salaryAdminPlanCode
                      placeholder: Select Salary Plan
                      _value:
                        transform: >-
                          $not($isNilorEmpty($.fields.rateCode)) ?
                          $.fields.rateCode.salaryAdminPlan : null
                      _disabled:
                        transform: $isNilorEmpty($.fields.code)
                      _select:
                        transform: $plans($.extend.limit,$.extend.page,$.extend.search)
                    - type: select
                      label: Salary Grade
                      name: gradeCode
                      placeholder: Select Salary Grade
                      _value:
                        transform: >-
                          $not($isNilorEmpty($.fields.rateCode)) ?
                          $.fields.rateCode.grade : null
                      _disabled:
                        transform: $isNilorEmpty($.fields.code)
                      _select:
                        transform: $grades($.extend.limit,$.extend.page,$.extend.search)
                    - type: select
                      label: Salary Step
                      name: stepCode
                      _value:
                        transform: >-
                          $not($isNilorEmpty($.fields.rateCode)) ?
                          [$.fields.rateCode.step] : null
                      placeholder: Select Salary Step
                      _disabled:
                        transform: $isNilorEmpty($.fields.code)
                      mode: multiple
                      _select:
                        transform: $steps($.extend.limit,$.extend.page,$.extend.search)
              sources:
                payrolls:
                  uri: '"/api/salary-by-tiers"'
                  method: GET
                  queryTransform: >-
                    {'limit': $.limit,'page': $.page,'search':
                    $.search,'filter': [{'field':'status','operator':
                    '$eq','value':true},{'field':'jobDataId','operator':
                    '$eq','value':$.jobDataId}]}
                  bodyTransform: ''
                  headerTransform: ''
                  resultTransform: >-
                    $map($.data, function($item) {{'label':
                    $item.payrollName.default, 'value': $item.code}})[]
                  disabledCache: true
                  params:
                    - limit
                    - page
                    - search
                    - jobDataId
                rateCodeList:
                  uri: '"/api/salary-by-tiers/detail"'
                  method: GET
                  queryTransform: >-
                    {'limit': $.limit,'page': $.page, 'search':
                    $.search,'sort':[{'field' : 'amount'  , 'order': 'ascend'
                    }] , 'filter': [{'field':'code','operator':
                    '$eq','value':$.salaryByTierMasterCode},{'field':'jobDataId','operator':
                    '$eq','value':$.jobDataId}]}
                  bodyTransform: ''
                  headerTransform: ''
                  resultTransform: >-
                    $map($.data, function($item) {{'label': $item.rateCode & '
                    (' & $NumberToCurrency($number($item.amount), 4) & ')' ,
                    'value': $item.rateCode, 'amount': $item.amount}})[]
                  disabledCache: true
                  params:
                    - limit
                    - page
                    - search
                    - salaryByTierMasterCode
                    - jobDataId
                rateCodeAmountList:
                  uri: '"/api/salary-by-tiers/rate-code"'
                  method: GET
                  queryTransform: >-
                    {'filter': [{'field':'amount','operator':
                    '$eq','value':$.amount},{'field':'code','operator':
                    '$eq','value':$.salaryByTierMasterCode},{'field':'jobDataId','operator':
                    '$eq','value':$.jobDataId}]}
                  bodyTransform: ''
                  headerTransform: ''
                  resultTransform: >-
                    $map($, function($item) {{'label': $item.rateCode & ' (' &
                    $NumberToCurrency($number($item.amount), 4) & ')' , 'value':
                    $item.rateCode, 'amount': $item.amount ,'salaryAdminPlan':
                    $item.salaryAdminPlanCode ? {'label':
                    $item.salaryAdminPlanName & ' (' & $item.salaryAdminPlanCode
                    & ')' , 'value':$item.salaryAdminPlanCode } : null,'grade':
                    $item.gradeCode ? {'label': $item.gradeName & ' (' &
                    $item.gradeCode & ')' , 'value':$item.gradeCode } :
                    null,'step': $item.stepCode ? {'label': $item.stepName & '
                    (' & $item.stepCode & ')' , 'value':$item.stepCode } : null
                    }})[]
                  disabledCache: true
                  params:
                    - amount
                    - salaryByTierMasterCode
                    - jobDataId
                plans:
                  uri: '"/api/salary-admin-plans"'
                  method: GET
                  queryTransform: >-
                    {'limit': $.limit,'page': $.page,'search':
                    $.search,'filter': []}
                  bodyTransform: ''
                  headerTransform: ''
                  resultTransform: >-
                    $map($.data, function($item) {{'label': $item.name.default &
                    ' (' & $item.code & ')', 'value': $item.code}})[]
                  disabledCache: true
                  params:
                    - limit
                    - page
                    - search
                grades:
                  uri: '"/api/grades"'
                  method: GET
                  queryTransform: >-
                    {'limit': $.limit,'page': $.page,'search':
                    $.search,'filter': []}
                  bodyTransform: ''
                  headerTransform: ''
                  resultTransform: >-
                    $map($.data, function($item) {{'label': $item.name.default &
                    ' (' & $item.code & ')', 'value': $item.code}})[]
                  disabledCache: true
                  params:
                    - limit
                    - page
                    - search
                steps:
                  uri: '"/api/steps"'
                  method: GET
                  queryTransform: >-
                    {'limit': $.limit,'page': $.page,'search':
                    $.search,'filter': []}
                  bodyTransform: ''
                  headerTransform: ''
                  resultTransform: >-
                    $map($.data, function($item) {{'label': $item.name.default &
                    ' (' & $item.code & ')', 'value': $item.code}})[]
                  disabledCache: true
                  params:
                    - limit
                    - page
                    - search
          _condition:
            transform: $not($.extend.formType = 'view')
        - type: number
          name: amount
          key: amountView
          label: Amount
          number:
            format: currency
            max: '999999999999999'
            precision: 3
          _condition:
            transform: $.extend.formType = 'view'
        - type: select
          label: Currency
          key: currencyCode
          name: currencyCode
          outputValue: value
          _disabled:
            transform: >-
              $.fields.employeeSalaryTypeCode = 'SLRT_00002' and
              $not($.extend.formType = 'view')
          _value:
            transform: >-
              $exists($.fields.rateCodeGroup.currencyCode) ?
              $.fields.rateCodeGroup.currencyCode : $.extend.formType = 'edit' ?
              $.extend.defaultValue.currencyCode :
              $filter($.variables._currencies, function($item){
              $contains($item.value, 'VND') })[0].value
          _condition:
            transform: $.extend.formType != 'view'
          _select:
            transform: $.variables._currencies
          validators:
            - type: required
        - type: text
          label: Currency
          name: currencyName
          _condition:
            transform: $.extend.formType = 'view'
        - type: text
          name: historicalSalaryCode
          placeholder: ' '
          label: Historical Salary
          disabled: true
          _condition:
            transform: $.extend.formType = 'edit' or $.extend.formType = 'view'
        - type: text
          name: incomePackageName
          dependantField: $.fields.employeeId; $.fields.effectiveDateFrom
          placeholder: Enter Package
          _condition:
            transform: $.extend.formType != 'view'
          _value:
            transform: >-
              $.fields.employeeId ?  ($.variables._packageItem ?
              $.variables._packageItem.longName : '_setValueNull' ) :
              '_setValueNull'
          label: Package
          disabled: true
        - type: text
          name: incomePackage
          label: Package
          labelTransform:
            transform: $.longName
          _condition:
            transform: $.extend.formType = 'view'
          _value:
            transform: >-
              $.fields.employeeId ? $packageItem($.fields.employeeId,
              $.fields.employeeRecordNumber,$.extend.defaultValue.effectiveDateFrom)
          formConfig:
            fields:
              - type: text
                name: code
                label: Code
              - type: text
                name: shortName
                label: Short Name
              - type: text
                name: longName
                label: Long Name
              - type: number
                label: Min
                name: min
                number:
                  format: currency
              - type: number
                label: Mid
                name: mid
                number:
                  format: currency
              - type: number
                label: Max
                name: max
                number:
                  format: currency
              - type: text
                label: Currency
                name: currencyCode
              - type: text
                name: note
                label: Note
        - type: text
          label: Reason
          key: reason
          name: reason
          validators:
            - type: maxLength
              args: '1000'
              text: Reason Code should not exceed 1000 characters
          placeholder: Enter Reason
        - type: select
          label: Frequency
          name: salaryTypeCode
          key: salaryTypeCode
          placeholder: Select Frequency
          outputValue: value
          _condition:
            transform: $.extend.formType = 'edit' or $.extend.formType = 'create'
          validators:
            - type: required
          value: SLRFQC_00001
          _select:
            transform: $.variables._frequencies
        - type: text
          name: salaryTypeName
          key: salaryTypeName
          label: Frequency
          _condition:
            transform: $.extend.formType = 'view'
        - type: number
          label: Percent
          key: frequency
          name: frequency
          placeholder: Enter Percent
          value: 100
          _condition:
            transform: $not($.extend.formType = 'view')
          validators:
            - type: required
            - type: ppx-custom
              args:
                transform: >-
                  $not($isNilorEmpty($.fields.frequency)) ? ($.fields.frequency
                  <= 0 or $.fields.frequency > 100)
              text: The entered percentage must be greater than 0 and not exceed 100
          number:
            precision: 10
            suffix: '%'
        - type: number
          label: Percent
          name: frequency
          _condition:
            transform: $.extend.formType = 'view'
          number:
            precision: 10
            suffix: '%'
        - type: text
          _condition:
            transform: $.extend.formType = 'create' or $.extend.formType = 'edit'
          label: Pro Rata Salary
          name: totalAfterSalaryView
          key: totalAfterSalaryView
          number:
            format: currency
            max: '999999999999999'
            precision: 3
          disabled: true
          _value:
            transform: $.variables._totalSalaryAfterConvert
        - type: number
          _condition:
            transform: $.extend.formType = 'view'
          number:
            format: currency
            max: '999999999999999'
            precision: 3
          label: Pro Rata Salary
          name: totalSalaryAfterConvert
          key: totalSalaryAfterConvert
        - type: text
          label: Official Gross Salary
          _condition:
            transform: $.extend.formType = 'create' or $.extend.formType = 'edit'
          name: totalSalaryView
          key: totalSalaryView
          disabled: true
          _value:
            transform: $.variables._totalSalary
        - type: number
          _condition:
            transform: $.extend.formType = 'view'
          number:
            format: currency
            max: '999999999999999'
            precision: 3
          label: Official Gross Salary
          name: totalSalary
          key: totalSalary
    - type: group
      label: Allowance Information
      collapse: false
      disableEventCollapse: false
      fields:
        - type: array
          mode: table
          name: details
          key: details
          arrayOptions:
            canChangeSize: true
          outputValue: value
          field:
            type: group
            label: Allowance Information
            fields:
              - name: 'no'
                label: 'No'
                type: number
                key: 'no'
                placeholder: ''
                align: start
                width: 70px
                readOnly: true
                _value:
                  transform: $.extend.path[-2] + 1
              - name: fixedAllowanceCode
                label: Allowance Name
                type: select
                key: fixedAllowanceCode
                align: start
                width: 200px
                outputValue: value
                validators:
                  - type: required
                _select:
                  transform: $.variables._allowancies
                _condition:
                  transform: $not($.extend.formType = 'view')
              - name: fixedAllowanceName
                label: Allowance Name
                type: text
                key: fixedAllowanceCode
                align: start
                width: 200px
                _condition:
                  transform: $.extend.formType = 'view'
              - name: amount
                label: Amount
                type: number
                align: start
                key: allowancyAmount
                width: 150px
                number:
                  format: currency
                  max: '999999999999999'
                  precision: 3
                  min: '1'
                _condition:
                  transform: $not($.extend.formType = 'view')
                validators:
                  - type: required
              - name: amount
                label: Amount
                type: number
                align: start
                width: 150px
                number:
                  format: currency
                  precision: 3
                _condition:
                  transform: $.extend.formType = 'view'
              - name: currencyCode
                label: Currency
                key: allowancyCurrencyCode
                type: text
                align: start
                width: 150px
                disabled: true
                _value:
                  transform: >-
                    ($valueCurrency := $filter($.variables._allowancies,
                    function($v) { $v.value = $getIdx($.fields.details,
                    $.extend.path[-2]).fixedAllowanceCode }
                    ).currency;$valueCurrency ? $valueCurrency :
                    $.fields.currencyCode)
                _condition:
                  transform: $.extend.formType != 'view'
              - name: currencyCode
                label: Currency
                type: text
                align: start
                width: 150px
                _condition:
                  transform: $.extend.formType = 'view'
              - name: frequency
                label: Percent
                type: number
                width: 100px
                number:
                  precision: 0
                  suffix: '%'
                _condition:
                  transform: $.extend.formType = 'view'
              - name: frequency
                label: Percent
                key: allowanceFrequency
                type: number
                align: start
                width: 100px
                value: 100
                validators:
                  - type: required
                  - type: ppx-custom
                    args:
                      transform: >-
                        $not($isNilorEmpty($.value)) ? ($.value <= 0 or $.value
                        > 100)
                    text: >-
                      The entered percentage must be greater than 0 and not
                      exceed 100
                number:
                  precision: 0
                  suffix: '%'
                _condition:
                  transform: $not($.extend.formType = 'view')
              - name: allownacePercent
                label: Pro Rata Amount
                key: allownacePercent
                type: number
                align: start
                width: 150px
                number:
                  format: currency
                disabled: true
                _value:
                  transform: >-
                    ($amount := $getIdx($.fields.details,
                    $.extend.path[-2]).amount; $percent :=
                    $getIdx($.fields.details, $.extend.path[-2]).frequency ;
                    $amount and $percent ? $number($formatNumber($amount *
                    ($percent / 100), '####.000')) : 0  )
  overview:
    dependentField: employeeRecordIdJobData
    title: Employee Detail
    collapsed: false
    display:
      - key: companyName
        label: Company
        _value:
          transform: $.variables._jobData.companyName
      - key: legalEntityName
        label: Legal entity
        _value:
          transform: $.variables._jobData.legalEntityName
      - key: businessUnitName
        label: Business unit
        _value:
          transform: $.variables._jobData.businessUnitName
      - key: divisionName
        label: Division
        _value:
          transform: $.variables._jobData.divisionName
      - key: departmentName
        label: Department
        _value:
          transform: $.variables._jobData.departmentName
      - key: jobTitleName
        label: Job Title
        _value:
          transform: $.variables._jobData.jobTitleName
      - key: contractTypeName
        label: Contract Type
        _value:
          transform: $.variables._jobData.contractTypeName
      - key: locationName
        label: Location
        _value:
          transform: $.variables._jobData.locationName
  _mode:
    transform: >-
      $.extend.formType = 'view' ? '' : {'name': 'mark-scroll',
      'showCollapseSection': true}
  sources:
    allowancies:
      uri: '"/api/fixed-allowances"'
      method: GET
      queryTransform: >-
        {'limit': 200,'filter': [{'field':'status','operator':
        '$eq','value':true},{'field':'companyCode','operator':
        '$eq','value':$.companyCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' - '
        &  $item.code, 'value': $item.code, 'currency': $item.currency}})[]
      disabledCache: true
      params: []
    currencies:
      uri: '"/api/picklists/CURRENCY/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.code, 'value':
        $item.code}})[]
      disabledCache: true
    entryTypes:
      uri: '"/api/picklists/ENTRYTYPE/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
    frequencies:
      uri: '"/api/picklists/SALARYFREQUENCY/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
    types:
      uri: '"/api/picklists/SALARYTYPE/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default , 'value':
        $item.code}})[]
      disabledCache: true
    employeesList:
      uri: '"/api/pr-employees"'
      method: GET
      queryTransform: >-
        {'limit': $.limit ,'page': $.page, 'search': $.search,'filter':
        [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'employeeId','operator':
        '$eq','value':$.empId},{'field':'employeeGroupCode','operator':
        '$cont','value':$.grpCode},{'field':'employeeRecordNumber','operator':
        '$eq','value':$.ern}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label':
        $join($filter([$item.employeeId,$item.employeeGroupCode,$string($item.employeeRecordNumber),$item.fullName],
        $boolean), ' - ')  , 'value': {'id': $item.employeeId, 'code':
        $item.employeeId, 'employeeRecordNumber':
        $string($item.employeeRecordNumber), 'jobDataId': $item.jobDataId}}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - empId
        - grpCode
        - ern
        - effectiveDate
    rateCodeList:
      uri: '"/api/salary-by-tiers/detail"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page,'sort':[{'field' : 'amount'  ,
        'order': 'ascend'  }] , 'filter': [{'field':'rateCode','operator':
        '$cont','value':$.search},{'field':'code','operator':
        '$eq','value':$.salaryByTierMasterCode},{'field':'salaryAdminPlanCode','operator':
        '$eq','value':$.salaryAdminPlanCode},{'field':'gradeCode','operator':
        '$eq','value': $.gradeCode},{'field':'stepCode','operator':
        '$in','value': $.stepCode},{'field':'jobDataId','operator':
        '$eq','value':$.jobDataId}, {'field': 'rateCode', 'operator': '$eq',
        'value': $.rateCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.rateCode & ' (' &
        $NumberToCurrency($number($item.amount), 4) & ')' , 'value': {'id':
        $item.rateCode, 'code': $item.rateCode,'salaryAdminPlanName':
        $item.salaryAdminPlanName, 'salaryAdminPlanCode':
        $item.salaryAdminPlanCode, 'gradeName': $item.gradeName, 'gradeCode':
        $item.gradeCode,'stepName': $item.stepName, 'stepCode': $item.stepCode,
        'amount': $item.amount, 'wageClassification': $item.wageClassification,
        'wageClassificationName': $item.wageClassificationName, 'currencyCode':
        $item.currencyCode, 'currencyName':$item.currencyName}}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - salaryByTierMasterCode
        - salaryAdminPlanCode
        - gradeCode
        - stepCode
        - jobDataId
        - rateCode
    selectedRateCodeList:
      uri: '"/api/salary-by-tiers/detail"'
      method: GET
      queryTransform: >-
        {'limit': 1,'page': 1,'sort':[{'field' : 'amount'  , 'order': 'ascend'
        }], 'filter': [{'field':'code','operator':
        '$eq','value':$.salaryByTierMasterCode},{'field':'salaryAdminPlanCode','operator':
        '$eq','value':$.salaryAdminPlanCode},{'field':'gradeCode','operator':
        '$eq','value': $.gradeCode},{'field':'stepCode','operator':
        '$in','value': $.stepCode},{'field':'jobDataId','operator':
        '$eq','value':$.jobDataId}, {'field': 'rateCode', 'operator': '$eq',
        'value': $.rateCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.rateCode & ' (' &
        $NumberToCurrency($number($item.amount), 4) & ')' , 'value': {'id':
        $item.rateCode, 'code': $item.rateCode,'salaryAdminPlanName':
        $item.salaryAdminPlanName, 'salaryAdminPlanCode':
        $item.salaryAdminPlanCode, 'gradeName': $item.gradeName, 'gradeCode':
        $item.gradeCode,'stepName': $item.stepName, 'stepCode': $item.stepCode,
        'amount': $item.amount, 'wageClassification': $item.wageClassification,
        'wageClassificationName': $item.wageClassificationName, 'currencyCode':
        $item.currencyCode, 'currencyName':$item.currencyName}}})[]
      disabledCache: true
      params:
        - salaryByTierMasterCode
        - salaryAdminPlanCode
        - gradeCode
        - stepCode
        - jobDataId
        - rateCode
    signerList:
      uri: '"/api/personals/all-employees"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.employeeId & ' - ' &
        $item.fullName, 'value': $item.employeeId}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    payrolls:
      uri: '"/api/salary-by-tiers"'
      method: GET
      queryTransform: >-
        {'limit': 500,'filter': [{'field':'status','operator':
        '$eq','value':true},{'field':'jobDataId','operator':
        '$eq','value':$.jobDataId},{'field':'effectiveDate','operator':
        '$lte','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.payrollName.default,
        'value': $item.code}})[]
      disabledCache: true
      params:
        - jobDataId
        - effectiveDate
    jobDatasDetail:
      uri: >-
        "/api/pr-employees/"  & $.employeeId & "/employee-record-number/" &
        $string($.ern) & "/effective-date/" & $string($.effectiveDate)
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $
      disabledCache: true
      params:
        - employeeId
        - ern
        - effectiveDate
    packageItem:
      uri: '"/api/pr-employee-salaries/income-package"'
      method: GET
      queryTransform: >-
        {'filter' : [{'field':'effectiveDate', 'operator':'$eq', 'value':
        $.effectiveDate},{'field':'employeeId', 'operator':'$eq', 'value':
        $.employeeId},{'field':'ern', 'operator':'$eq', 'value': $.ern}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: '$ ? $ : {}'
      disabledCache: true
      params:
        - employeeId
        - ern
        - effectiveDate
  variables:
    _allowancies:
      transform: '$.extend.formType != ''view'' ? $allowancies() : []'
    _currencies:
      transform: '$.extend.formType !=''view'' ? $currencies() : []'
    _calculateAllowancies:
      transform: >-
        $.fields.details ? $reduce($map($.fields.details,
        function($a){$number($a.amount) * $number($a.frequency) / 100}),
        function($i, $j){$i + $j}) : 0
    _totalSalary:
      transform: >-
        ($not($isNilorEmpty($.fields.amount)) ?
        $NumberToCurrency(($boolean($.variables._calculateAllowancies) ? (
        $.fields.salaryTypeCode = 'SLRFQC_00001' or $.fields.salaryTypeCode =
        'M' ? $number($.variables._calculateAllowancies) : 0) : 0) +
        ($number($.fields.amount))) : $string(0)) & ' ' & ($.fields.amount ?
        $.fields.currencyCode)
    _totalSalaryAfterConvert:
      transform: >-
        ($not($isNilorEmpty($.fields.amount)) ?
        $NumberToCurrency(($boolean($.variables._calculateAllowancies) ? (
        $.fields.salaryTypeCode = 'SLRFQC_00001' or $.fields.salaryTypeCode =
        'M' ? $number($.variables._calculateAllowancies) : 0) : 0) +
        ($number($.fields.amount)) * $number($.fields.frequency) / 100) :
        $string(0)) & ' ' & ($.fields.amount ? $.fields.currencyCode)
    _frequencies:
      transform: '$.extend.formType !=''view'' ? $frequencies() : []'
    _salaryTypes:
      transform: '$.extend.formType !=''view'' ? $types() : []'
    _jobData:
      transform: $.fields.objectJobData
    _payrolls:
      transform: >-
        $.fields.jobDataId ?
        $payrolls($.fields.jobDataId,$.fields.effectiveDateFrom) : []
    _packageItem:
      transform: >-
        $.fields.employeeId and $.fields.effectiveDateFrom ?
        $packageItem($.fields.employeeId,
        $.fields.employeeRecordNumber,$.fields.effectiveDateFrom) : null
  footer:
    create: true
    update: true
    createdOn: createdAt
    updatedOn: updatedAt
    createdBy: createdBy
    updatedBy: updatedBy
filter_config:
  fields:
    - type: selectAll
      name: companyCode
      label: Company
      labelType: type-grid
      placeholder: Select Company
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $companiesList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      name: legalEntityCode
      label: Legal Entity
      labelType: type-grid
      placeholder: Select Legal Entity
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $legalEntitiesList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      label: Business Unit
      name: businessUnitCode
      mode: multiple
      labelType: type-grid
      isLazyLoad: true
      placeholder: Select Business Unit
      _options:
        transform: $businessUnitList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      name: divisionCode
      labelType: type-grid
      label: Division
      placeholder: Select Division
      mode: multiple
      _options:
        transform: $divisionsList($.extend.limit,$.extend.page, $.extend.search)
    - type: selectAll
      name: departmentCode
      labelType: type-grid
      label: Department
      placeholder: Select Department
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $departmentsList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      name: payGroupCode
      labelType: type-grid
      label: Pay Group
      placeholder: Select Pay Group
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $payGroup($.extend.limit, $.extend.page,$.extend.search)
    - type: selectAll
      labelType: type-grid
      label: Employee
      name: employeeId
      placeholder: Select Employee ID
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $personalsList($.extend.limit, $.extend.page, $.extend.search)
    - type: text
      label: Full Name
      name: fullName
      labelType: type-grid
      placeholder: Input Full Name
    - type: radio
      label: Gender
      labelType: type-grid
      name: genderCode
      value: null
      _radio:
        transform: $gender()
    - type: selectAll
      label: Job Code
      name: jobCodeCode
      labelType: type-grid
      placeholder: Select Job Code
      mode: multiple
      _options:
        transform: $jobCodesList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      label: Position
      name: positionCode
      labelType: type-grid
      placeholder: Select Position
      mode: multiple
      _options:
        transform: $positionList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      label: Job Indicator
      name: jobIndicatorCode
      labelType: type-grid
      placeholder: Select Job Indicator
      mode: multiple
      _options:
        transform: $jobIndicatorList()
    - type: selectAll
      label: Contract Type
      name: contractTypeCode
      placeholder: Select Contract Type
      mode: multiple
      labelType: type-grid
      _options:
        transform: $contractTypeList()
    - name: locationCode
      label: Location
      type: selectAll
      mode: multiple
      placeholder: Select Location
      labelType: type-grid
      isLazyLoad: true
      _options:
        transform: $locationsList($.extend.limit, $.extend.page, $.extend.search)
    - name: effectiveDateFrom
      label: Effective Start Date
      type: dateRange
      labelType: type-grid
      setting:
        mode: day
        format: dd/MM/yyyy HH:mm:ss
    - type: selectAll
      label: Salary Type
      mode: multiple
      name: employeeSalaryTypeCode
      placeholder: Select Salary Type
      labelType: type-grid
      _options:
        transform: $salaryTypes()
    - type: selectAll
      label: Payroll
      labelType: type-grid
      mode: multiple
      name: salaryByTierMasterCode
      placeholder: Select Payroll
      isLazyLoad: true
      _options:
        transform: $payrolls($.extend.limit,$.extend.page,$.extend.search)
    - type: selectAll
      label: Salary Plan
      mode: multiple
      isLazyLoad: true
      name: salaryAdminPlanCode
      labelType: type-grid
      placeholder: Select Salary Plan
      _options:
        transform: $salaryPlans($.extend.limit,$.extend.page,$.extend.search)
    - type: selectAll
      labelType: type-grid
      label: Salary Grade
      isLazyLoad: true
      name: gradeCode
      mode: multiple
      placeholder: Select Salary Grade
      _options:
        transform: $grades($.extend.limit,$.extend.page,$.extend.search)
    - type: selectAll
      label: Salary Step
      isLazyLoad: true
      labelType: type-grid
      name: stepCode
      placeholder: Select Salary Step
      _options:
        transform: $steps($.extend.limit,$.extend.page,$.extend.search)
    - type: selectAll
      name: salaryTypeCode
      labelType: type-grid
      label: Frequency
      placeholder: Select Frequency
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $frequencies()
    - type: number
      label: Percent
      labelType: type-grid
      key: frequency
      name: frequency
      placeholder: Enter Percent
      number:
        precision: 0
        suffix: '%'
    - name: expectedIncreaseDate
      label: Expected Promotion Date
      labelType: type-grid
      type: dateRange
      setting:
        mode: day
        format: dd/MM/yyyy HH:mm:ss
    - type: text
      label: Decision Number
      name: decisionNo
      labelType: type-grid
      placeholder: Input Decision Number
    - type: selectAll
      label: Signer
      name: decisionSignerId
      placeholder: Select Signer
      labelType: type-grid
      isLazyLoad: true
      _options:
        transform: $personalNameList($.extend.limit,$.extend.page,$.extend.search)
    - name: decisionSignDate
      labelType: type-grid
      label: Sign Date
      type: dateRange
      setting:
        mode: date
        format: dd/MM/yyyy HH:mm:ss
    - type: text
      labelType: type-grid
      label: Last Updated By
      name: updatedBy
      placeholder: Input Last Updated By
    - name: updatedAt
      labelType: type-grid
      label: Last Updated On
      type: dateRange
      setting:
        mode: date
        format: dd/MM/yyyy HH:mm:ss
  filterMapping:
    - operator: $or
      valueField:
        - field: employeeIdFilter
          operator: $elemMatch
          valueField: employeeId.(employeeId)
        - field: employeeRecordNumber
          operator: $elemMatch
          valueField: employeeId.(ern)
    - field: groupCode
      operator: $in
      valueField: groupCode.(value)
    - field: companyCode
      operator: $in
      valueField: companyCode.(value)
    - field: legalEntityCode
      operator: $in
      valueField: legalEntityCode.(value)
    - field: businessUnitCode
      operator: $in
      valueField: businessUnitCode.(value)
    - field: divisionCode
      operator: $in
      valueField: divisionCode.(value)
    - field: departmentCode
      operator: $in
      valueField: departmentCode.(value)
    - field: payGroupCode
      operator: $in
      valueField: payGroupCode.(value)
    - field: employeeGroupCode
      operator: $in
      valueField: employeeGroupCode.(value)
    - field: employeeRecordNumber
      operator: $eq
      valueField: employeeRecordNumber
    - field: fullName
      operator: $cont
      valueField: fullName
    - field: genderCode
      operator: $eq
      valueField: genderCode
    - field: localExpart
      operator: $eq
      valueField: localExpart
    - field: jobCodeCode
      operator: $in
      valueField: jobCodeCode.(value)
    - field: positionCode
      operator: $in
      valueField: positionCode.(value)
    - field: jobIndicatorCode
      operator: $in
      valueField: jobIndicatorCode.(value)
    - field: contractTypeCode
      operator: $in
      valueField: contractTypeCode.(value)
    - field: locationCode
      operator: $in
      valueField: locationCode.(value)
    - field: hrStatusCode
      operator: $in
      valueField: hrStatusCode.(value)
    - field: payrollStatusCode
      operator: $in
      valueField: payrollStatusCode.(value)
    - field: effectiveDateFrom
      operator: $between
      valueField: effectiveDateFrom
    - field: employeeSalaryTypeCode
      operator: $in
      valueField: employeeSalaryTypeCode.(value)
    - field: salaryByTierMasterCode
      operator: $in
      valueField: salaryByTierMasterCode.(value)
    - field: salaryAdminPlanCode
      operator: $in
      valueField: salaryAdminPlanCode.(value)
    - field: stepCode
      operator: $in
      valueField: stepCode.(value)
    - field: gradeCode
      operator: $in
      valueField: gradeCode.(value)
    - field: salaryTypeCode
      operator: $in
      valueField: salaryTypeCode.(value)
    - field: frequency
      operator: $eq
      valueField: frequency
    - field: expectedIncreaseDate
      operator: $between
      valueField: expectedIncreaseDate
    - field: decisionNo
      operator: $eq
      valueField: decisionNo
    - field: packageCode
      operator: $in
      valueField: packageCode.(value)
    - field: decisionSignerId
      operator: $in
      valueField: decisionSignerId
    - field: decisionSignDate
      operator: $between
      valueField: decisionSignDate
    - field: updatedBy
      operator: $eq
      valueField: updatedBy
    - field: updatedAt
      operator: $between
      valueField: updatedAt
  sources:
    salaryPlans:
      uri: '"/api/salary-admin-plans"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.code, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    grades:
      uri: '"/api/grades"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.code, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    steps:
      uri: '"/api/steps"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.code, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    groups:
      uri: '"/api/groups"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'filter':
        [{'field':'search','operator': '$eq','value': $.search}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    companiesList:
      uri: '"/api/companies"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    legalEntitiesList:
      uri: '"/api/legal-entities"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    businessUnitList:
      uri: '"/api/business-units"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    divisionsList:
      uri: '"/api/divisions"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    departmentsList:
      uri: '"/api/departments/get-by"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    payGroup:
      uri: '"/api/pay-group-structures"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    incomePackages:
      uri: '"/api/income-packages"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    frequencies:
      uri: '"/api/picklists/SALARYFREQUENCY/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
    personalsList:
      uri: '"/api/personals"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label':
        $join($filter([$item.employeeId,$item.employeeGroup,$string($item.employeeRecordNumber),$item.fullName],
        $boolean), ' - ') , 'value':{'employeeId':$item.employeeId , 'ern':
        $item.employeeRecordNumber,'empGroup' :
        $.item.employeeGroup},'employeeId':$item.employeeId,
        'ern':$item.employeeRecordNumber, 'empGroup' : $item.employeeGroup }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    personalNameList:
      uri: '"/api/personals/all-employees"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.employeeId & ' - ' &
        $item.fullName , 'value': $item.employeeId}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    employeeGroup:
      uri: '"/api/picklists/EMPLOYEEGROUP/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
    gender:
      uri: '"/api/picklists/GENDER/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        ($optionAll := {'label': 'All', 'value': null}; $append($optionAll,
        $map($.data, function($item) {{'label': $item.name.default , 'value':
        $item.code}})[]))
      disabledCache: true
    localForeigners:
      uri: '"/api/picklists/LOCALFOREIGNERS/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        ($optionAll := {'label': 'All', 'value': null}; $append($optionAll,
        $map($.data, function($item) {{'label': $item.name.default , 'value':
        $item.code}})[]))
      disabledCache: true
    jobCodesList:
      uri: '"/api/job-codes"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    positionList:
      uri: '"/api/positions"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    jobIndicatorList:
      uri: '"/api/picklists/JOBINDICATOR/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})
      disabledCache: true
    contractTypeList:
      uri: '"/api/picklists/CONTRACTTYPE/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})
      disabledCache: true
    locationsList:
      uri: '"/api/locations"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'pageSize': $.pageSize,'search':
        $.search, 'filter': []}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')',  'value': $item.code }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    hrStatusList:
      uri: '"/api/picklists/HRSTATUS/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})
      disabledCache: true
    payrollStatusList:
      uri: '"/api/picklists/PAYROLLSTATUS/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})
      disabledCache: true
    salaryTypes:
      uri: '"/api/picklists/SALARYTYPE/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default , 'value':
        $item.code}})[]
      disabledCache: true
    payrolls:
      uri: '"/api/salary-by-tiers"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search, ''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.payrollName.default,
        'value': $item.id}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
layout_options:
  hide_detail_history_after_save: true
  nested_row_type: group
  transform_group_row_of_nested_row: $.employeeId & ' - ' & $.employeeRecordNumberView & ' - ' & $.fullName
  row_type: expand
  row_data_combine:
    - employeeId
    - employeeGroupCode
    - employeeRecordNumber
    - fullName
  show_detail_history: false
  tool_table:
    - id: import
      icon: icon-upload-simple
      paramsRedirect:
        type: PR_OBJECT
        entityOrObj: EmployeeSalary
    - id: export
      icon: icon-download-simple
  toolTable:
    export: true
    adjustDisplay: true
  delete_multi_items: true
  collapse: true
  table:
    defaultCollapse: true
  is_upload_file: true
  show_filter_results_message: true
  data_group:
    group_api:
      url: /api/pr-employee-salaries/group
    group_details_api:
      url: >-
        /api/pr-employee-salaries/group-detail/{{employeeId}}/{{employeeRecordNumber}}{{#if
        employeeGroupCode}}/{{employeeGroupCode}}{{else}}{{/if}}
  store_selected_items: true
  hide_action_row: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: null
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    icon: icon-pencil-simple
    type: ghost-gray
  - id: duplicate
    icon: icon-copy
    type: ghost-gray
  - id: delete
    icon: icon-trash
    type: ghost-gray
backend_url: /api/pr-employee-salaries
screen_name: manange-employee-salaries
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: companyCode
    defaultName: CompanyCode
  - name: legalEntityCode
    defaultName: LegalEntityCode
  - name: businessUnitCode
    defaultName: BusinessUnitCode
  - name: divisionCode
    defaultName: DivisionCode
  - name: departmentCode
    defaultName: DepartmentCode
  - name: jobCodeCode
    defaultName: JobCode
  - name: locationCode
    defaultName: LocationCode
  - name: employeeId
    defaultName: EmployeeId
  - name: employeeRecordNumber
    defaultName: EmployeeRecordNumber
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Manage Employee Salary Information
  parent:
    title: PR Setting
