id: PR.FS.FR.038
status: draft
sort: 158
user_created: 27c6db8f-5244-4827-bfd9-df7fa8d185f0
date_created: '2024-08-16T03:48:09.491Z'
user_updated: b403d5da-d684-45e4-ae6d-e25223baedc5
date_updated: '2025-07-20T05:36:05.602Z'
title: Set Up ABCD Salary
requirement:
  time: 1747279229439
  blocks:
    - id: ejqS20ClTm
      type: paragraph
      data:
        text: >-
          <PERSON><PERSON> thống hỗ trợ quản lý thông tin lịch sử quá trình, thiết lập thông
          tin hưởng các khoản hỗ trợ không cố định cho nhân viên: loại hỗ trợ,
          thời gian hiệu lực, mức hưởng.
    - id: B8AUqppUpD
      type: paragraph
      data:
        text: >-
          Cho phép người dùng thực hiện import hàng loạt thông tin hưởng của
          khoản hỗ trợ đã được phê duyệt bên ngoài lên hệ thống hoặc tích hợp dữ
          liệu đã tính toán từ hệ thống vệ tinh của các CTTV. Trường hợp dữ liệu
          lỗi (mã nhân viên chưa tồn tại, mã khoản hỗ trợ chưa đúng, …), hệ
          thống cảnh báo và nêu rõ lý do để cán bộ nhân sự kiểm tra lại.
    - id: PAx7yY1K7V
      type: paragraph
      data:
        text: >-
          Lưu ý: Trường hợp trong kỳ tính lương, nhân viên được hưởng cùng một
          khoản hỗ trợ nhiều lần, hệ thống cho phép import và ghi nhận toàn bộ.
    - id: PDD7EyPIPR
      type: paragraph
      data:
        text: >-
          Hệ thống tự động đồng bộ các khoản hỗ trợ đã được phê duyệt bởi các
          cấp có thẩm quyền từ quy trình: HR.BP.16 – Quy trình khen thưởng,
          HR.BP.09 – Quy trình kiêm nhiệm, HR.BP.12 – Quy trình quản lý hợp
          đồng.
  version: 2.30.7
screen_design: null
module: PR
local_fields:
  - code: code
    title: Payroll code
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    pinned: true
    options__tabular__column_width: 10
    show_sort: true
  - code: payrollName
    title: Payroll Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: country
    title: Country
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: companyNames
    title: Company
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tags
      collection: field_types
    show_sort: true
  - code: legalEntityNames
    title: Legal Entity
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tags
      collection: field_types
    show_sort: true
  - code: expectedSalaryIncreasePeriod
    title: Expected Salary Increase Period
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: effectiveDate
    title: Effective Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    show_sort: true
  - code: status
    title: Status
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: Boolean Tag
      collection: field_types
    options__tabular__column_width: null
    show_sort: true
  - code: updatedAt
    title: Last Updated On
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
    show_sort: true
  - code: updatedBy
    title: Last Updated By
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
mock_data:
  - 'no': '1'
    payrollCode: V2019
    payrollName:
      default: Bảng thiết lập lương ABCD V1-2019
      vietnamese: Bảng thiết lập lương ABCD V1-2019
      english: Payroll ABCD V1-2019
    status: Active
    group: FPT
    company: FSOFT
    legalEntity: FSOFT
    expectedSalaryIncreasePeriod: 1 month
    expectedSalaryIncreasePeriodNumber: 1
    attachedFile:
      name: file_name.pdf
      url: '123'
    createdOn: '2024-01-01 12:00:00'
    createdBy: Ducnm54
    lastUpdatedOn: '2022-05-02 12:00:00'
    lastUpdatedBy: Ducnm54
    country: Việt Nam
  - 'no': '2'
    payrollCode: Testing value
    payrollName:
      default: Demo testing value
      vietnamese: Demo testing value
      english: Demo testing value
    status: Inactive
    group: FPT
    company: FSOFT
    legalEntity: FSOFT
    expectedSalaryIncreasePeriod: 1 month
    expectedSalaryIncreasePeriodNumber: 1
    attachedFile:
      name: file_name.pdf
      url: '123'
    createdOn: '2024-01-01 12:00:00'
    createdBy: Ducnm54
    lastUpdatedOn: '2022-05-02 12:00:00'
    lastUpdatedBy: Ducnm54
    country: Việt Nam
local_buttons: null
layout: layout-table
form_config:
  formSize:
    create: largex
    edit: largex
    view: large
  defaultValidateError: Required fields have not been filled in completely (fields marked with *)
  fields:
    - type: group
      label: General information
      collapse: false
      disableEventCollapse: true
      isBorderTopNone: true
      fields:
        - type: group
          n_cols: 2
          collapsed: false
          _condition:
            transform: $not($.extend.formType = 'view')
          disableEventCollapse: true
          fields:
            - type: select
              label: Country
              isLazyLoad: true
              placeholder: Select Country
              name: countryObj
              _select:
                transform: $nationsList($.extend.limit, $.extend.page, $.extend.search)
            - type: text
              label: Payroll Code
              formatType: code
              placeholder: Enter Code
              name: code
              validators:
                - type: required
                - type: pattern
                  args: ^[a-zA-Z0-9_.-]*$
                  text: >-
                    Code must only contain letters, numbers, hyphens,
                    underscores, or dots.
                - type: maxLength
                  args: '50'
                  text: Payroll Code should not exceed 50 characters
        - type: group
          collapsed: false
          disableEventCollapse: true
          _condition:
            transform: $.extend.formType = 'view'
          fields:
            - type: select
              label: Country
              placeholder: Select Country
              name: countryObj
              outputValue: value
              _select:
                transform: $nationsList($.extend.limit, $.extend.page, $.extend.search)
            - type: text
              label: Payroll Code
              placeholder: Input Code
              name: code
        - name: payrollName
          label: Payroll Name
          placeholder: Enter Payroll Name
          type: translation
          _condition:
            transform: $not($.extend.formType = 'view')
          validators:
            - type: required
            - type: maxLength
              args: '50'
              text: Payroll Name should not exceed 300 characters
        - name: payrollName
          label: Payroll Name
          placeholder: Enter Payroll Name
          type: translation
          _condition:
            transform: $.extend.formType = 'view'
        - name: exportAll
          type: text
          unvisible: true
          value: true
        - type: group
          label: ''
          n_cols: 2
          _condition:
            transform: $not($.extend.formType = 'view')
          fields:
            - type: selectAll
              label: Company
              placeholder: Select Company
              name: companyObj
              mode: multiple
              isLazyLoad: true
              _options:
                transform: >-
                  $companiesList($.extend.limit, $.extend.page, $.extend.search,
                  $.fields.effectiveDate)
            - type: selectAll
              label: Legal Entity
              placeholder: Select Legal Entity
              name: legalEntityObj
              mode: multiple
              isLazyLoad: true
              _options:
                transform: >-
                  $legalEntitiesList($.extend.limit, $.extend.page,
                  $.extend.search, $map($.fields.companyObj,function($v)
                  {$v.value ? $v.value.id : $v.id}))
            - name: expectedSalaryIncreasePeriod
              label: Expected Salary Increase Period
              type: number
              placeholder: Enter Number
              number:
                precision: 0
                suffix: Month
            - type: dateRange
              label: Effective Date
              name: effectiveDate
              mode: date-picker
              placeholder: Enter Effective Date
              setting:
                format: dd/MM/yyyy
                type: date
              validators:
                - type: required
              _value:
                transform: >-
                  $.extend.formType = 'create' and
                  $isNilorEmpty($.fields.effectiveDate) ? $now()
            - type: radio
              label: Status
              name: status
              _value:
                transform: >-
                  $.extend.formType = 'create' and $.extend.isDuplicate = false
                  ? true
              radio:
                - value: true
                  label: Active
                - value: false
                  label: InActive
        - type: group
          label: ''
          _condition:
            transform: $.extend.formType = 'view'
          fields:
            - type: selectAll
              label: Company
              placeholder: Select Company
              name: companyObj
              mode: multiple
              isLazyLoad: true
              outputValue: value
              _options:
                transform: >-
                  $companiesList($.extend.limit, $.extend.page, $.extend.search,
                  $.fields.effectiveDate)
            - type: selectAll
              label: Legal Entity
              placeholder: Select Legal Entity
              name: legalEntityObj
              mode: multiple
              isLazyLoad: true
              outputValue: value
              _options:
                transform: >-
                  $legalEntitiesList($.extend.limit, $.extend.page,
                  $.extend.search)
            - name: expectedSalaryIncreasePeriod
              label: Expected Salary Increase Period
              placeholder: Enter Number
              type: number
              number:
                precision: 0
                suffix: Month
            - type: dateRange
              label: Effective Date
              name: effectiveDate
              mode: date-picker
              placeholder: Enter Effective Date
              setting:
                format: dd/MM/yyyy
                type: date
            - type: radio
              label: Status
              name: status
              radio:
                - value: true
                  label: Active
                - value: false
                  label: InActive
        - name: file
          label: File Attachment
          type: upload
          _value:
            transform: $.fields.attachFile
          _condition:
            transform: $.extend.formType = 'create'
          upload:
            size: 20
            accept:
              - >-
                application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
              - application/pdf
              - application/vnd.ms-excel
              - >-
                application/vnd.openxmlformats-officedocument.wordprocessingml.document
              - application/msword
        - name: attachFile
          label: File Attachment
          type: text
          unvisible: true
        - name: attachFileName
          label: File Attachment
          type: text
          _value:
            transform: '$.extend.formType = ''create'' ? $.fields.file : $.fields.attachFile'
          unvisible: true
        - name: attachFile
          label: File Attachment
          type: upload
          _condition:
            transform: $.extend.formType = 'edit'
          upload:
            size: 20
            accept:
              - >-
                application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
              - application/pdf
              - application/vnd.ms-excel
              - >-
                application/vnd.openxmlformats-officedocument.wordprocessingml.document
              - application/msword
        - name: isNoChangeFile
          label: ''
          dependantField: attachFile
          _value:
            transform: >-
              $.extend.formType = 'create' ? $not($type($.fields.file) =
              'array') : $not($type($.fields.attachFile) = 'array')
          unvisible: true
          type: checkbox
        - name: attachmentResults
          label: File Attachment
          type: upload
          _condition:
            transform: $.extend.formType = 'view'
          upload:
            size: 100
            accept:
              - application/pdf
              - application/xls
              - application/xlsx
    - type: group
      label: Set Up Detail
      collapse: false
      disableEventCollapse: true
      n_cols: 1
      fields:
        - type: text
          name: id
          unvisible: true
        - type: text
          name: detailsTable
          unvisible: true
          _value:
            transform: $.extend.defaultValue.details
        - type: treeTable
          treeForm:
            form:
              - type: group
                label: Set up detail
                mode: multiple
                collapse: false
                fields:
                  - type: text
                    label: Salary Plan
                    name: salaryAdminPlanName
                    readOnly: true
                  - type: text
                    label: ''
                    name: salaryAdminPlanCode
                    disabled: true
                    unvisible: true
                  - type: text
                    label: Salary Grade
                    name: gradeName
                    readOnly: true
                  - type: text
                    label: ''
                    name: gradeCode
                    disabled: true
                    unvisible: true
                  - type: text
                    label: Salary Step
                    name: stepName
                    readOnly: true
                  - type: text
                    label: ''
                    name: stepCode
                    disabled: true
                    unvisible: true
                  - type: text
                    label: Rate Code
                    name: rateCode
                    readOnly: true
                  - type: group
                    collapse: false
                    n_cols: 2
                    fields:
                      - type: text
                        label: Entry Type
                        name: wageClassificationName
                        readOnly: true
                      - type: text
                        label: ''
                        name: wageClassification
                        disabled: true
                        unvisible: true
                      - type: text
                        label: Currency
                        name: currencyName
                        _condition:
                          transform: $.fields.entryType.code = 'WGCSFT_00001'
                        readOnly: true
                      - type: text
                        label: ''
                        name: currencyCode
                        disabled: true
                        unvisible: true
                  - type: text
                    label: ''
                    name: initialValue
                    unvisible: true
                  - type: text
                    label: ''
                    name: leap
                    unvisible: true
                  - type: number
                    label: Amount
                    name: amount
                    number:
                      format: currency
                      max: '99999999999999'
                      precision: 3
          group:
            keyGroup: salaryAdminPlanCode
            rowExpandTitle:
              - salaryAdminPlanName
            keyTitle: salaryAdminPlanName
          _dataSource:
            transform: $.fields.detailsTable
          _condition:
            transform: $not($.extend.formType = 'view')
          rowActions:
            delete:
              precheck:
                source:
                  uri: '"/api/salary-by-tiers/check-details"'
                  method: POST
                  queryTransform: ''
                  bodyTransform: '$.id ? { ''detailIds'': [$.id] } : {}'
                  headerTransform: ''
                  resultTransform: $
                  disabledCache: true
          config:
            addSetup: true
            filter: true
          actions:
            search: {}
            addSetup:
              addValidate:
                type: noDataAdded
                errorMessage: Settings already exist. Please check the data
              title: Set up detail
              uniqueFields:
                - salaryAdminPlanCode
                - gradeCode
                - stepCode
              specificData: $.variables._data
              form:
                - type: text
                  name: dataTable
                  unvisible: true
                  _value:
                    transform: >-
                      {'salaryPlan': $.fields.salaryPlan,'salaryGrade':
                      $.fields.salaryGrade,'salaryStep':
                      $.fields.salaryStep,'initialValue':
                      $.fields.initialValue,'leap': $.fields.leap,'entryType':
                      $.fields.entryType,'currency': $.fields.currency}
                - type: select
                  label: Salary Plan
                  name: salaryPlan
                  placeholder: Select Salary Plan
                  outputValue: value
                  isLazyLoad: true
                  _select:
                    transform: $plans($.extend.limit,$.extend.page,$.extend.search)
                  validators:
                    - type: required
                - type: select
                  label: Salary Grade
                  name: salaryGrade
                  placeholder: Select Salary Grade
                  isLazyLoad: true
                  outputValue: value
                  _select:
                    transform: $grades($.extend.limit,$.extend.page,$.extend.search)
                - type: select
                  label: Salary Step
                  name: salaryStep
                  mode: multiple
                  isLazyLoad: true
                  placeholder: Select Salary Step
                  outputValue: value
                  _select:
                    transform: $steps($.extend.limit,$.extend.page,$.extend.search)
                  validators:
                    - type: required
                - type: group
                  collapse: false
                  n_cols: 2
                  fields:
                    - type: select
                      label: Entry Type
                      name: entryType
                      placeholder: Select Entry Type
                      outputValue: value
                      _select:
                        transform: >-
                          $filter($entryTypeList(), function($v,$i,$a)
                          {$v.value.code = 'WGCSFT_00001'})[]
                      validators:
                        - type: required
                    - type: select
                      label: Currency
                      name: currency
                      placeholder: Select Currency
                      outputValue: value
                      _select:
                        transform: $currencies()
                      _condition:
                        transform: $.fields.entryType.code = 'WGCSFT_00001'
                      validators:
                        - type: required
                - type: group
                  collapse: false
                  n_cols: 2
                  fields:
                    - type: number
                      label: Initial Value
                      placeholder: Enter Initial Value
                      name: initialValue
                      number:
                        format: currency
                        precision: 3
                    - type: number
                      label: Leap
                      placeholder: Enter Leap
                      name: leap
                      number:
                        format: currency
                        precision: 3
              sources:
                currencies:
                  uri: '"/api/picklists/CURRENCY/values"'
                  method: GET
                  queryTransform: ''
                  bodyTransform: ''
                  headerTransform: ''
                  resultTransform: >-
                    $map($.data, function($item) {{'label': $item.code, 'value':
                    {'code': $item.code, 'name': $item.code}}})[]
                  disabledCache: true
                entryTypeList:
                  uri: '"/api/picklists/WAGECLASSIFICATION/values"'
                  method: GET
                  queryTransform: ''
                  bodyTransform: ''
                  headerTransform: ''
                  resultTransform: >-
                    $map($.data, function($item) {{'label': $item.name.default,
                    'value': {'code': $item.code, 'name':
                    $item.name.default}}})[]
                  disabledCache: true
                grades:
                  uri: '"/api/grades"'
                  method: GET
                  queryTransform: >-
                    {'limit': $.limit,'page': $.page,'search':
                    $.search,'filter': [{'field':'status','operator':
                    '$eq','value':true}]}
                  bodyTransform: ''
                  headerTransform: ''
                  resultTransform: >-
                    $map($.data, function($item) {{'label': $item.name.default,
                    'value': {'code': $item.code, 'name':
                    $item.name.default}}})[]
                  disabledCache: true
                  params:
                    - limit
                    - page
                    - search
                steps:
                  uri: '"/api/steps"'
                  method: GET
                  queryTransform: >-
                    {'limit': $.limit,'page': $.page,'search':
                    $.search,'filter': [{'field':'status','operator':
                    '$eq','value':true}]}
                  bodyTransform: ''
                  headerTransform: ''
                  resultTransform: >-
                    $map($.data, function($item) {{'label': $item.name.default,
                    'value': {'code': $item.code, 'name':
                    $item.name.default}}})[]
                  disabledCache: true
                  params:
                    - limit
                    - page
                    - search
                plans:
                  uri: '"/api/salary-admin-plans"'
                  method: GET
                  queryTransform: >-
                    {'limit': $.limit,'page': $.page,'search':
                    $.search,'filter': [{'field':'status','operator':
                    '$eq','value':true}]}
                  bodyTransform: ''
                  headerTransform: ''
                  resultTransform: >-
                    $map($.data, function($item) {{'label': $item.name.default,
                    'value': {'code': $item.code, 'name':
                    $item.name.default}}})[]
                  disabledCache: true
                  params:
                    - limit
                    - page
                    - search
              variables:
                _data:
                  transform: >-
                    ($_getAllCombination:= function($i) { $map($i,
                    function($item) { $map($sort($item.salaryStep, function($l,
                    $r) {$l.code > $r.code}) , function($itemStep, $indexStep){
                    $merge({'salaryAdminPlanCode' :
                    $item.salaryPlan.code,'salaryAdminPlanName' :
                    $item.salaryPlan.name,'gradeCode' :
                    $item.salaryGrade.code,'gradeName' :
                    $item.salaryGrade.name,'stepCode':$itemStep.code,'stepName':$itemStep.name,'wageClassification'
                    : $item.entryType.code,'wageClassificationName' :
                    $item.entryType.name,'currencyCode' :
                    $item.currency.code,'currencyName' : $item.currency.name,
                    'amount': ($boolean($item.initialValue) ?
                    $number($item.initialValue) : 0)  + ($boolean($item.leap) ?
                    $number($item.leap) : 0) * $indexStep ,'rateCode':
                    $item.rateCode
                    })})})};$reduce($_getAllCombination($.fields.dataTable),$append,
                    []))
                _currencies:
                  transform: $currencies()
                _entryTypeMoney:
                  transform: >-
                    $filter($entryTypeList(), function($v,$i,$a) {$v.value.code
                    = 'WGCSFT_00001'})[0]
          name: details
          columns:
            - title: 'No'
              autoIndex: true
              align: left
              width: 3.5
              fixedLeft: true
            - title: Salary Plan
              align: left
              code: salaryAdminPlanName
              width: 10
              fixedLeft: true
            - title: Salary Grade
              align: left
              code: gradeName
              width: 10
              fixedLeft: true
            - title: Salary Step
              align: left
              code: stepName
              width: 8
              fixedLeft: true
            - display_type: text
              title: Rate Code
              align: left
              code: rateCode
              width: 12
              fixedLeft: true
              formatType: code
              validators:
                - type: required
            - display_type: currency
              title: Amount
              align: left
              code: amount
              width: 14
              fixedLeft: true
              validators:
                - type: required
                - type: min
                  args: 1
                  text: The amount must be greater than 0
              inputSettings:
                precision: 3
            - title: Currency
              align: left
              code: currencyName
              width: 6
              fixedLeft: true
          layout_option:
            tool_table:
              expand_filter: true
              show_table_checkbox: true
              collapse: true
            actions_many:
              - id: delete
                icon: icon-trash-bold
                title: Delete
                precheck:
                  source:
                    uri: '"/api/salary-by-tiers/check-details"'
                    method: POST
                    queryTransform: ''
                    bodyTransform: >-
                      $count($map($.items, function ($v) {$v.id})[]) > 0 ?
                      {'detailIds': [$map($.items, function ($v) {$v.id})]} : {}
                    headerTransform: ''
                    resultTransform: $
                    disabledCache: true
            fixed_action_column: true
        - type: treeTable
          treeForm:
            form:
              - type: group
                label: Set up detail
                mode: multiple
                collapse: false
                fields:
                  - type: text
                    label: ''
                    name: id
                    readOnly: true
                    unvisible: true
                  - type: text
                    label: ''
                    name: rateCode
                    readOnly: true
                    unvisible: true
                  - type: text
                    label: Salary Plan
                    name: salaryAdminPlanName
                    readOnly: true
                  - type: text
                    label: ''
                    name: salaryAdminPlanCode
                    disabled: true
                    unvisible: true
                  - type: text
                    label: Salary Grade
                    name: gradeName
                    readOnly: true
                  - type: text
                    label: ''
                    name: gradeCode
                    disabled: true
                    unvisible: true
                  - type: text
                    label: Salary Step
                    name: stepName
                    readOnly: true
                  - type: text
                    label: ''
                    name: stepCode
                    disabled: true
                    unvisible: true
                  - type: group
                    collapse: false
                    n_cols: 2
                    fields:
                      - type: text
                        label: Entry Type
                        name: entryType
                        readOnly: true
                      - type: text
                        label: ''
                        name: wageClassification
                        disabled: true
                        unvisible: true
                      - type: text
                        label: Currency
                        name: currencyName
                        _condition:
                          transform: $.fields.entryType.code = 'WGCSFT_00001'
                        readOnly: true
                      - type: text
                        label: ''
                        name: currencyCode
                        disabled: true
                        unvisible: true
                  - type: text
                    label: ''
                    name: initialValue
                    unvisible: true
                  - type: text
                    label: ''
                    name: leap
                    unvisible: true
                  - type: number
                    label: Amount
                    name: amount
                    number:
                      format: currency
                      max: '99999999999999'
                      precision: 3
          group:
            keyGroup: salaryAdminPlanCode
            rowExpandTitle:
              - salaryAdminPlanName
            keyTitle: salaryAdminPlanName
          _dataSource:
            transform: $.fields.detailsTable
          _condition:
            transform: $.extend.formType = 'view'
          config:
            filter: true
          actions:
            search: {}
            export:
              backendUrl: /api/salary-by-tiers/export-details
              filterQuery:
                - field: salaryByTierMasterId
                  operator: $eq
                  valueField: id
                - field: exportAll
                  operator: $eq
                  valueField: exportAll
          name: details
          columns:
            - title: 'No'
              autoIndex: true
              align: left
              width: '5'
              fixedLeft: true
            - title: Salary Plan
              align: left
              code: salaryAdminPlanName
              fixedLeft: true
            - title: Salary Grade
              align: left
              code: gradeName
              fixedLeft: true
            - title: Salary Step
              align: left
              code: stepName
              fixedLeft: true
            - title: Rate Code
              align: left
              code: rateCode
              fixedLeft: true
            - display_type: currency
              readOnly: true
              title: Amount
              align: left
              code: amount
              fixedLeft: true
            - title: Currency
              align: left
              code: currencyCode
              fixedLeft: true
          layout_option:
            tool_table:
              expand_filter: true
              collapse: true
          showPagination: true
  historyTitle: $.fullName
  historyDescription: >-
    $.relationship & ' - ' & ($.isDependent ? 'Là người phụ thuộc' : 'Không là
    người phụ thuộc')
  _mode:
    transform: >-
      $.extend.formType != 'view' ? {'name': 'mark-scroll',
      'showCollapseSection': true}
  sources:
    nationsList:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code &')', 'value': {'code': $item.code, 'id': $item.id}}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    companiesList:
      uri: '"/api/companies/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'search': $.search,  'filter':
        [{'field':'groupId','operator':
        '$eq','value':$.groupId},{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator': '$eq','value':
        $.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id': $item.id,'code' :$item.code}}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
        - groupId
    legalEntitiesList:
      uri: '"/api/legal-entities/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'search': $.search, 'filter':
        [{'field':'CompanyIds','operator':
        '$eq','value':$.companyId},{'field':'groupId','operator':
        '$eq','value':$.groupId},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')','value': {'id': $item.id,'code' :$item.code}}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - companyId
        - groupId
  variables:
    _isNoChangeFile:
      transform: >-
        ($exists($.fields.attachFile[0].data) or $count($.fields.attachFile) =
        0) ? false : true
    _currencies:
      transform: $currencies()
    _entryTypeList:
      transform: $entryTypeList()
  footer:
    create: true
    update: true
    createdOn: createdAt
    updatedOn: updatedAt
    createdBy: createdBy
    updatedBy: updatedBy
filter_config:
  fields:
    - type: text
      labelType: type-grid
      name: code
      label: Payroll Code
      placeholder: Enter Payroll Code
    - type: text
      name: payrollName
      placeholder: Enter Payroll Name
      labelType: type-grid
      label: Payroll Name
    - type: selectAll
      name: companyCode
      options:
        maxTag: 1000
      label: Company
      labelType: type-grid
      isLazyLoad: true
      placeholder: Select Company
      mode: multiple
      _options:
        transform: >-
          $companiesList($.extend.limit, $.extend.page,
          $.extend.search,$.fields.effectiveDate)
    - type: selectAll
      labelType: type-grid
      label: Legal Entity
      name: legalEntityCode
      placeholder: Select Legal Entity
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $legalEntitiesList($.extend.limit, $.extend.page, $.extend.search)
      _condition:
        transform: $.fields.object = 'Organization'
    - type: radio
      name: status
      label: Status
      value: null
      radio:
        - label: All
          value: null
        - label: Active
          value: true
        - label: Inactive
          value: false
    - name: updatedBy
      label: Last Updated By
      type: text
      labelType: type-grid
      placeholder: Enter Last Updated By
    - name: updatedAt
      label: Last Updated On
      type: dateRange
      labelType: type-grid
      setting:
        mode: date
        format: dd/MM/yyyy
  filterMapping:
    - field: code
      operator: $cont
      valueField: code
    - field: payrollName
      operator: $cont
      valueField: payrollName
    - field: groupCode
      operator: $in
      valueField: group.(value)
    - field: companyCode
      operator: $in
      valueField: companyCode.(value)
    - field: legalEntityCode
      operator: $in
      valueField: legalEntityCode.(value)
    - field: status
      operator: $eq
      valueField: status
    - field: updatedBy
      operator: $cont
      valueField: updatedBy
    - field: updatedAt
      operator: $between
      valueField: updatedAt
  sources:
    nationsList:
      uri: '"/api/nations"'
      method: GET
      queryTransform: '{''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
    salaryByTiersList:
      uri: '"/api/salary-by-tiers"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'effectiveDate','operator':
        '$lte','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - effectiveDate
    groupsList:
      uri: '"/api/groups/by"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'effectiveDate','operator':
        '$lte','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id}})[]
      disabledCache: true
      params:
        - effectiveDate
    companiesList:
      uri: '"/api/companies/get-by"'
      method: GET
      queryTransform: '{''limit'': $.limit, ''page'': $.page, ''search'': $.search,''filter'':[]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data,function($item){{'label':$item.longName.default&'('&$item.code&')','value':$item.code
        , 'id': $item.id}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
    legalEntitiesList:
      uri: '"/api/legal-entities/get-by"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
layout_options:
  show_detail_history: false
  is_upload_file: true
  apply_delete_multi_items_to_delete_one: true
  custom_delete_body: '{''ids'': $map($.data,function($item){$item.id})[]}'
  delete_multi_items: true
  is_popup: true
  tool_table:
    - id: export
      icon: icon-download-simple
  duplicate_value_transform:
    fields:
      - code
      - payrollName
    transform: $ & '_Copy'
  form_value_storage:
    - formType: create
      key: create_set-up-abcd-salary
  store_selected_items: true
  show_filter_results_message: true
  hide_action_row: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: duplicate
    icon: icon-copy-bold
    type: tertiary
  - id: edit
    icon: pencil
    type: tertiary
  - id: delete
    icon: trash
    type: tertiary
backend_url: /api/salary-by-tiers
screen_name: null
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: companyCodes
    defaultName: CompanyCode
  - name: legalEntityCodes
    defaultName: LegalEntityCode
  - name: countryCode
    defaultName: CountryCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Set Up ABCD Salary
  parent:
    title: PR Setting
