id: PR.FS.FR.039
status: draft
sort: 343
user_created: 56e71b91-dd39-4ebc-afd1-b4f00f38d4a5
date_created: '2024-08-05T06:52:10.679Z'
user_updated: b403d5da-d684-45e4-ae6d-e25223baedc5
date_updated: '2025-07-28T09:14:01.950Z'
title: Element Calculation
requirement:
  time: 1748934189831
  blocks:
    - id: Sx6dEiSVEm
      type: paragraph
      data:
        text: >-
          <PERSON><PERSON><PERSON> h<PERSON><PERSON>n lý các tiêu chí tính toán tính lương giúp người dùng
          Thêm mới/điều chỉnh/tra cứu các tiêu chí tính toán để thêm vào công
          thức tính lương TRƯỚC KHI tạo công thức.
    - id: 4CiSQFeR8W
      type: paragraph
      data:
        text: >-
          Ví dụ: <PERSON><PERSON><PERSON><PERSON> theo chứ<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ô<PERSON>
          thự<PERSON> tế,...
    - id: ACKL39osKz
      type: paragraph
      data:
        text: >-
          <PERSON><PERSON> thống kiểm tra thông tin và không cho thiết lập mới nếu thông tin
          thiết lập trùng (mã tiêu chí tính toán hoặc short name) với thiết lập
          đã có.
  version: 2.30.7
screen_design: null
module: PR
local_fields:
  - code: elementCode
    title: Element Calculation Code
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    sort: Sort Ascending
    pinned: true
    show_sort: true
  - code: shortName
    title: Short Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: longName
    title: Long Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: country
    title: Country
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: operationName
    title: Operation
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: elementGroupName
    title: Element Group
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: elementTypeName
    title: Element Type
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: dataTypeName
    title: Data Type
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: linkingElementTable
    title: Linking Element
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: generalElement
    title: General Element
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: Boolean
      collection: field_types
    show_sort: true
  - code: executeTypeNameTable
    pinned: false
    title: Segment Element
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: encryption
    title: Encryption
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: Boolean
      collection: field_types
    show_sort: true
  - code: status
    title: Status
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: Boolean Tag
      collection: field_types
    show_sort: true
  - code: note
    title: Note
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: updatedBy
    title: Last Updated By
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: updatedAt
    title: Last Updated On
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
    show_sort: true
mock_data:
  - code: E025
    shortName:
      default: INS_LCB
      vietnamese: Lương theo chức danh
      english: INS_LCB
    longName:
      default: Salary by title
      vietnamese: Lương theo chức danh
      english: Salary by title
    country: Vietnam
    effectiveDate: '2024-02-01'
    operation: ''
    elementGroup: Monthly Salary
    elementType: Monthly Salary
    applyToFormula: ''
    encryption: true
    status: Inactive
    note:
      default: ''
      vietnamese: ''
      english: ''
    creator: NhiVN
    createdTime: '2024-06-23 18:02:02'
    lastEditor: NhiVN
    lastEditedTime: '2024-06-23 18:02:02'
  - code: '00000001'
    shortName:
      default: HR_CONTRACT
      vietnamese: Loại hợp đồng
      english: HR_CONTRACT
    longName:
      default: Contract Type
      vietnamese: Loại hợp đồng
      english: Contract Type
    country: Vietnam
    effectiveDate: '2024-02-01'
    operation: ''
    elementGroup: Tax
    elementType: Monthly Salary
    applyToFormula: ''
    encryption: true
    status: Active
    note:
      default: ''
      vietnamese: ''
      english: ''
    creator: NhiVN
    createdTime: '2024-06-23 18:02:02'
    lastEditor: NhiVN
    lastEditedTime: '2024-06-23 18:02:02'
  - code: '00000002'
    shortName:
      default: PR_TN
      vietnamese: Lương thực lãnh
      english: PR_TN
    longName:
      default: Actual Salary
      vietnamese: Lương thực lãnh
      english: Actual Salary
    country: Vietnam
    effectiveDate: '2024-02-01'
    operation: Sum
    elementGroup: Monthly Salary
    elementType: Supplement Salary
    applyToFormula: ''
    encryption: true
    status: Inactive
    note:
      default: ''
      vietnamese: ''
      english: ''
    creator: NhiVN
    createdTime: '2024-06-23 18:02:02'
    lastEditor: NhiVN
    lastEditedTime: '2024-06-23 18:02:02'
  - code: '00000003'
    shortName:
      default: PR_RE_OT
      vietnamese: Lương tăng ca
      english: PR_RE_OT
    longName:
      default: Overtime Salary
      vietnamese: Lương tăng ca
      english: Overtime Salary
    country: Vietnam
    effectiveDate: '2024-02-01'
    operation: Sum
    elementGroup: Monthly Salary
    elementType: Monthly Salary
    applyToFormula: LT_CTLT_FIS_HCM - CT Monthly Salary FIS_HCM
    encryption: true
    status: Active
    note:
      default: ''
      vietnamese: ''
      english: ''
    creator: NhiVN
    createdTime: '2024-06-23 18:02:02'
    lastEditor: NhiVN
    lastEditedTime: '2024-06-23 18:02:02'
local_buttons: null
layout: layout-table
form_config:
  effectiveDateValidations:
    - sameDate
    - greaterThanLastest
  fields:
    - type: group
      n_cols: 2
      fields:
        - type: text
          name: elementCode
          label: Element Caculation Code
          placeholder: Automatic
          _disabled:
            transform: 'true'
          _condition:
            transform: $.extend.formType != 'view'
          _value:
            transform: $.extend.formType = 'create' ? 'Automatic'
          col: 1
        - type: text
          name: elementCode
          label: Element Caculation Code
          placeholder: Automatic
          _disabled:
            transform: 'true'
          _condition:
            transform: $.extend.formType = 'view'
          _value:
            transform: $.extend.formType = 'create' ? 'Automatic'
          col: 2
        - type: text
          name: shortName
          label: Short Name
          formatType: code
          defaultValue: TT_
          _condition:
            transform: $.extend.formType != 'view'
          validators:
            - type: maxLength
              args: '300'
              message: Maximum length is 300 characters
            - type: required
            - type: minLength
              args: 4
          placeholder: Enter Short Name
          col: 1
        - type: text
          name: shortName
          label: Short Name
          _condition:
            transform: $.extend.formType = 'view'
          col: 2
        - type: translation
          name: longName
          label: Long Name
          validators:
            - type: required
            - type: maxLength
              args: '500'
              message: Maximum length is 500 characters
          placeholder: Enter Long Name
          col: 2
        - name: countryCode
          label: Country
          type: select
          outputValue: value
          placeholder: Select Country
          _condition:
            transform: $.extend.formType != 'view'
          _select:
            transform: $nationsList()
          col: 1
        - name: countryCode
          label: Country
          type: select
          outputValue: value
          placeholder: Select Country
          _condition:
            transform: $.extend.formType = 'view'
          _select:
            transform: $nationsList()
          col: 2
        - type: select
          name: operation
          label: Operation
          _condition:
            transform: $.extend.formType != 'view'
          outputValue: value
          _select:
            transform: $.variables._operatorList
          placeholder: Select Operation
          col: 1
        - type: select
          name: operation
          label: Operation
          _condition:
            transform: $.extend.formType = 'view'
          outputValue: value
          _select:
            transform: $.variables._operatorList
          placeholder: Select Operation
          col: 2
        - type: select
          name: dataType
          label: Data Type
          outputValue: value
          _condition:
            transform: $.extend.formType != 'view'
          _select:
            transform: $dataTypesList()
          placeholder: Select Data Type
          validators:
            - type: required
          col: 1
        - type: select
          name: dataType
          label: Data Type
          outputValue: value
          _condition:
            transform: $.extend.formType = 'view'
          _select:
            transform: $dataTypesList()
          placeholder: Select Data Type
          validators:
            - type: required
          col: 2
        - type: select
          name: executeType
          label: Segment Calculation
          outputValue: value
          _condition:
            transform: $.extend.formType != 'view'
          _select:
            transform: $segmentCalculation()
          placeholder: Select Segment Calculation
          validators:
            - type: required
          col: 1
        - type: text
          name: executeTypeName
          label: Segment Calculation
          outputValue: value
          _condition:
            transform: $.extend.formType = 'view'
          col: 2
        - type: select
          name: elementGroup
          mode: multiple
          label: Element Group
          outputValue: value
          _value:
            transform: $boolean($.fields.elementType) ? $.variables._selectedType
          _condition:
            transform: $.extend.formType != 'view' and $.extend.formType != 'proceed'
          _select:
            transform: >-
              $boolean($.fields.elementType) = true ?
              $.variables._filterElementGroups  : $.variables._elementGroupsList
          placeholder: Select Element Group
          col: 1
          clearFieldsAfterChange:
            - elementType
        - type: select
          name: elementGroup
          mode: multiple
          label: Element Group
          outputValue: value
          clearFieldsAfterChange:
            - elementType
          _value:
            transform: >-
              $boolean($.fields.elementType) = true ? $.variables._selectedType
              : $map($.extend.defaultValue.elementItems, function($v)
              {$v.elementGroup})
          _condition:
            transform: $.extend.formType = 'proceed'
          _select:
            transform: >-
              $boolean($.fields.elementType) = true ?
              $.variables._filterElementGroups  : $.variables._elementGroupsList
          placeholder: Select Element Group
          col: 1
        - type: text
          name: elementGroupView
          label: Element Group
          _value:
            transform: >-
              $join($map($.extend.defaultValue.elementItems , function($item) {
              $item.elementGroupName }), ', ')
          _condition:
            transform: $.extend.formType = 'view'
          col: 2
        - type: select
          name: elementType
          label: Element Type
          outputValue: value
          _select:
            transform: >-
              $boolean($count($.fields.elementGroup) = 0) = true ?
              $.variables._elementTypesList :
              $boolean($count($.fields.elementGroup) = 1) = true ?
              $.variables._filterElementType : []
          _condition:
            transform: $.extend.formType != 'view' and $.extend.formType != 'proceed'
          placeholder: Select Element Type
          _disabled:
            transform: >-
              $not(($.fields.elementGroup; $count($.fields.elementGroup) = 0 or
              $count($.fields.elementGroup) = 1 ))
          col: 1
        - type: select
          name: elementType
          label: Element Type
          outputValue: value
          _value:
            transform: $.extend.defaultValue.elementItems[0].elementType
          _select:
            transform: >-
              $boolean($count($.fields.elementGroup) = 0) = true ?
              $.variables._elementTypesList :
              $boolean($count($.fields.elementGroup) = 1) = true ?
              $.variables._filterElementType : []
          _condition:
            transform: $.extend.formType = 'proceed'
          placeholder: Select Element Type
          _disabled:
            transform: >-
              $not(($.fields.elementGroup; $count($.fields.elementGroup) = 0 or
              $count($.fields.elementGroup) = 1 ))
          col: 1
        - type: text
          name: elementTypeView
          label: Element Type
          _value:
            transform: >-
              $split($map($.extend.defaultValue.elementItems ,  function($item)
              { ($filter($.variables._elementTypesList, function($v) { $v.value
              = $item.elementType})[0]).label }), ', ')
          _condition:
            transform: $.extend.formType = 'view'
          col: 2
        - type: select
          name: linkingElement
          label: Linking Element
          outputValue: value
          mode: multiple
          _condition:
            transform: $.extend.formType != 'view'
          _select:
            transform: $linkingElement()
          placeholder: Select Linking Element
          col: 2
        - type: text
          name: linkingElementsView
          label: Linking Element
          _value:
            transform: >-
              $join($map($.extend.defaultValue.linkingElements , function($item)
              { $item.linkingElementName }), ', ')
          _condition:
            transform: $.extend.formType = 'view'
          _select:
            transform: ''
          placeholder: Select Linking Element
          col: 2
        - type: radio
          name: generalElement
          label: General Element
          _condition:
            transform: $.extend.formType != 'view' and $.extend.formType = 'edit'
          _value:
            transform: $.extend.formType='create' ? false
          radio:
            - label: 'Yes'
              value: true
            - label: 'No'
              value: false
          col: 1
        - type: radio
          name: generalElement
          label: General Element
          _condition:
            transform: $.extend.formType != 'view' and $.extend.formType != 'edit'
          _value:
            transform: $.extend.formType='create' ? false
          radio:
            - label: 'Yes'
              value: true
            - label: 'No'
              value: false
          col: 2
        - type: radio
          name: generalElement
          label: General Element
          _condition:
            transform: $.extend.formType = 'view'
          _value:
            transform: $.extend.formType='create' ? true
          radio:
            - label: 'Yes'
              value: true
            - label: 'No'
              value: false
          col: 2
        - type: radio
          name: encryption
          label: Encryption
          _condition:
            transform: $.extend.formType != 'view' and $.extend.formType != 'create'
          _disabled:
            transform: 'true'
          _value:
            transform: $.extend.formType='create'  ? false
          radio:
            - label: 'Yes'
              value: true
            - label: 'No'
              value: false
          col: 1
        - type: radio
          name: encryption
          label: Encryption
          _condition:
            transform: $.extend.formType = 'view'
          _value:
            transform: $.extend.formType='create'  ? false
          radio:
            - label: 'Yes'
              value: true
            - label: 'No'
              value: false
          col: 1
        - type: radio
          name: status
          label: Status
          _condition:
            transform: $.extend.formType != 'view'
          _value:
            transform: $.extend.formType = 'create' ? true
          radio:
            - label: Active
              value: true
            - label: Inactive
              value: false
          validators:
            - type: required
          col: 1
        - type: radio
          name: status
          label: Status
          _condition:
            transform: $.extend.formType = 'view'
          _value:
            transform: $.extend.formType = 'create' ? true
          radio:
            - label: Active
              value: true
            - label: Inactive
              value: false
          validators:
            - type: required
          col: 2
        - type: translationTextArea
          name: note
          label: Note
          placeholder: Enter Note
          validators:
            - type: maxLength
              args: '1000'
              message: Maximum length is 1000 characters
          textarea:
            autoSize:
              minRows: 3
            maxCharCount: '1000'
          col: 2
  sources:
    nationsList:
      uri: '"/api/picklists/COUNTRIES/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' - ' &
        $item.code, 'value': $item.code}})
      disabledCache: true
    linkingElement:
      uri: '"/api/picklists/LINKINGELEMENT/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})
      disabledCache: true
    elementGroupsList:
      uri: '"/api/picklists/ELEMENTGROUP/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})
      disabledCache: true
    operatorList:
      uri: '"/api/report-column-cal/operator"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: '$map($, function($item) {{''label'': $item.value, ''value'': $item.key}})'
      disabledCache: true
    elementTypesList:
      uri: '"/api/picklists/ELEMENTTYPE/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code, 'elementGroup' : $item.codE501}})
      disabledCache: true
    dataTypesList:
      uri: '"/api/salary-formulas/data-types-v2"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.key, 'value': $item.value,
        'description': $item.example}})
      disabledCache: true
    segmentCalculation:
      uri: '"/api/report-column-cal/execute-type"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: '$map($, function($item) {{''label'': $item.key, ''value'': $item.value}})'
      disabledCache: true
  variables:
    _test:
      transform: >-
        $join($map($.extend.defaultValue.elementItems , function($item) {
        $item.elementGroupName }), ', ')
    _elementGroupsList:
      transform: $elementGroupsList()
    _operatorList:
      transform: $operatorList()
    _filterGroupValue:
      transform: ($.variables._filterElementGroups)[0].value
    _elementTypesList:
      transform: $elementTypesList()
    _fieldGroup:
      transform: $.fields.elementGroup
    _filterElementType:
      transform: >-
        $filter($.variables._elementTypesList , function($v) { $v.elementGroup =
        ($.fields.elementGroup)[0]})[]
    _selectedType:
      transform: >-
        [($filter($.variables._elementTypesList , function($v) { $v.value =
        $.fields.elementType})).elementGroup]
    _filterElementGroups:
      transform: >-
        [$filter($.variables._elementGroupsList , function($v) { $v.value =
        ($.variables._selectedType)[0]})]
  footer:
    create: true
    update: true
    createdOn: createdAt
    updatedOn: updatedAt
    createdBy: createdBy
    updatedBy: updatedBy
filter_config:
  fields:
    - type: selectAll
      name: CountryCodes
      label: Country
      isLazyLoad: true
      _options:
        transform: $nationsList($.extend.limit, $.extend.page, $.extend.search)
      labelType: type-grid
      placeholder: Select Country
    - type: text
      name: code
      labelType: type-grid
      label: Element Calculation Code
    - type: text
      name: shortName
      label: Short Name
      labelType: type-grid
      placeholder: Enter Short Name
    - type: text
      name: longName
      label: Long Name
      labelType: type-grid
      placeholder: Enter Long Name
    - type: selectAll
      labelType: type-grid
      name: OperationCodes
      label: Operation
      mode: multiple
      _options:
        transform: $operatorList()
      placeholder: Select Operation
    - type: selectAll
      labelType: type-grid
      name: ExecuteTypes
      label: Segment Calculation
      mode: multiple
      _options:
        transform: $segmentCalculation()
      placeholder: Select Segment Calculation
    - type: selectAll
      name: DataTypeCodes
      labelType: type-grid
      label: Data Type
      mode: multiple
      _options:
        transform: $dataTypesList()
      placeholder: Select Data Type
    - type: selectAll
      name: ElementGroupCodes
      labelType: type-grid
      label: Element Group
      _options:
        transform: $elementGroupsList()
      placeholder: Select Element Group
    - type: selectAll
      name: ElementTypeCodes
      labelType: type-grid
      label: Element Type
      _options:
        transform: $elementTypesList()
      placeholder: Select Element Type
    - type: selectAll
      labelType: type-grid
      name: LinkingElementCodes
      label: Linking Element
      _options:
        transform: $linkingElement()
      placeholder: Select Linking Element
    - type: radio
      name: generalElement
      labelType: type-grid
      label: General Element
      value: null
      radio:
        - label: All
          value: null
        - label: 'Yes'
          value: true
        - label: 'No'
          value: false
    - type: radio
      name: encryption
      labelType: type-grid
      label: Encryption
      value: null
      radio:
        - label: All
          value: null
        - label: 'Yes'
          value: true
        - label: 'No'
          value: false
    - type: radio
      name: status
      labelType: type-grid
      label: Status
      value: null
      radio:
        - label: All
          value: null
        - label: Active
          value: true
        - label: Inactive
          value: false
    - type: dateRange
      label: Effective Date
      labelType: type-grid
      name: effectiveDate
      mode: date-picker
      setting:
        format: dd/MM/yyyy
        type: date
    - type: textarea
      name: note
      label: Note
      labelType: type-grid
      placeholder: Enter Note
    - name: updatedBy
      label: Last Updated By
      type: text
      labelType: type-grid
      placeholder: Enter Last Updated By
    - name: updatedAt
      label: Last Updated On
      type: dateRange
      labelType: type-grid
      setting:
        mode: date
        format: dd/MM/yyyy
  filterMapping:
    - field: CountryCodes
      operator: $eq
      valueField: CountryCodes.(value)
    - field: code
      operator: $cont
      valueField: code
    - field: shortName
      operator: $cont
      valueField: shortName
    - field: longName
      operator: $cont
      valueField: longName
    - field: DataTypeCodes
      operator: $eq
      valueField: DataTypeCodes.(value)
    - field: OperationCodes
      operator: $eq
      valueField: OperationCodes.(value)
    - field: ExecuteTypes
      operator: $eq
      valueField: ExecuteTypes.(value)
    - field: ElementGroupCodes
      operator: $eq
      valueField: ElementGroupCodes.(value)
    - field: ElementTypeCodes
      operator: $eq
      valueField: ElementTypeCodes.(value)
    - field: LinkingElementCodes
      operator: $eq
      valueField: LinkingElementCodes.(value)
    - field: generalElement
      operator: $eq
      valueField: generalElement
    - field: encryption
      operator: $eq
      valueField: encryption
    - field: effectiveDate
      operator: $between
      valueField: effectiveDate
    - field: status
      operator: $eq
      valueField: status
    - field: note
      operator: $cont
      valueField: note
    - field: updatedBy
      operator: $cont
      valueField: updatedBy
    - field: updatedAt
      operator: $between
      valueField: updatedAt
  sources:
    nationsList:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    elementGroupsList:
      uri: '"/api/picklists/ELEMENTGROUP/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})
      disabledCache: true
    elementTypesList:
      uri: '"/api/picklists/ELEMENTTYPE/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})
      disabledCache: true
    operatorList:
      uri: '"/api/report-column-cal/operator"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: '$map($, function($item) {{''label'': $item.value, ''value'': $item.key}})'
      disabledCache: true
    dataTypesList:
      uri: '"/api/salary-formulas/data-types-v2"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: '$map($, function($item) {{''label'': $item.key, ''value'': $item.value}})'
      disabledCache: true
    segmentCalculation:
      uri: '"/api/report-column-cal/execute-type"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: '$map($, function($item) {{''label'': $item.key, ''value'': $item.value}})'
      disabledCache: true
    linkingElement:
      uri: '"/api/picklists/LINKINGELEMENT/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})
      disabledCache: true
layout_options:
  show_detail_history: false
  show_dialog_form_save_add_button: true
  delete_multi_items: true
  apply_delete_multi_items_to_delete_one: true
  custom_delete_body: '{''ids'': $map($.data,function($item){$item.id})[]}'
  tool_table:
    - id: export
      icon: icon-download-simple
  show_filter_results_message: true
  store_selected_items: true
  show_dialog_duplicate_button: false
  hide_action_row: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: null
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    icon: pencil
    type: tertiary
    title: null
  - id: delete
    icon: trash
    type: tertiary
backend_url: api/report-column-cal
screen_name: null
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: countryCode
    defaultName: CountryCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Element Calculation
  parent:
    title: PR Setting
