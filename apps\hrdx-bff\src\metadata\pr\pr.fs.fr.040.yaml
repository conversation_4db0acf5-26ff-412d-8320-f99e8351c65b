id: PR.FS.FR.040
status: draft
sort: 280
user_created: 7b005132-8c47-469d-87f8-c72f7305edb7
date_created: '2024-07-26T07:27:54.300Z'
user_updated: b403d5da-d684-45e4-ae6d-e25223baedc5
date_updated: '2025-06-23T10:43:18.126Z'
title: Formula
requirement:
  time: 1747733934341
  blocks:
    - id: jG22v_RMT0
      type: paragraph
      data:
        text: >-
          <PERSON><PERSON><PERSON> hình <PERSON>ản lý <PERSON>ông thức tính toán động giúp người dùng được phân
          quyền tạo các Công thức tính toán động tháng, l<PERSON><PERSON><PERSON> b<PERSON>, l<PERSON><PERSON><PERSON> hồi
          tố từ các công thức
  version: 2.30.7
screen_design: null
module: PR
local_fields:
  - code: code
    title: Formula Code
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
  - code: shortName
    title: Short Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
  - code: longName
    title: Long Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
  - code: country
    title: Country
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: elementGroup
    title: Element Group
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: elementType
    title: Element Type
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: effectiveDate
    title: Effective Date
    data_type:
      key: dd/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
  - code: status
    title: Status
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: Boolean Tag
      collection: field_types
  - code: expressionForReport
    title: Report Formula
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: note
    title: Note
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
  - code: updatedBy
    title: Last Updated By
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: updatedAt
    title: Last Updated On
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
mock_data: []
local_buttons: null
layout: layout-table
form_config:
  _mode:
    transform: >-
      $not($.extend.formType = 'view') ? {'name': 'mark-scroll',
      'showCollapseSection': true, 'focusElementPerTab': [{'index': 1,
      'elementId': 'report-columns-table'}]}
  form_value_to_base_64: true
  formSize:
    create: full
    edit: full
    view: largex
    history: largex
    proceed: largex
  effectiveDateValidations:
    - sameDate
    - greaterThanLastest
  defaultValidateError: Required fields have not been filled in completely (fields marked with *)
  fields:
    - type: group
      label: General information
      disableEventCollapse: false
      collapse: false
      _titleStyle:
        transform: '$.extend.formType = ''view'' ? {''borderTop'': ''none'', ''paddingTop'': 0}'
      fields:
        - type: group
          label: ''
          n_cols: 2
          _condition:
            transform: $not($.extend.formType = 'view')
          fields:
            - type: radio
              label: ''
              name: deleteYN
              unvisible: true
              _value:
                transform: 'true'
              radio:
                - label: Active
                  value: true
                - label: Inactive
                  value: false
            - type: select
              label: Country
              name: countryCode
              outputValue: value
              placeholder: Select Country
              isLazyLoad: true
              _validateFn:
                transform: >-
                  $.extend.defaultValue ? {'label':
                  $.extend.defaultValue.country & ' (' &
                  $.extend.defaultValue.countryCode & ')', 'value':
                  $.extend.defaultValue.countryCode}
                params:
                  updateLabelExistOption: true
              _select:
                transform: $nationsList($.extend.limit, $.extend.page, $.extend.search)
            - type: text
              label: Formula Code
              name: code
              disabled: true
              _condition:
                transform: $not($.extend.formType = 'create' and $.extend.isDuplicate)
              placeholder: Automatic
            - type: text
              label: Formula Code
              name: codeDuplicate
              disabled: true
              _condition:
                transform: $.extend.formType = 'create' and $.extend.isDuplicate
              placeholder: Automatic
            - type: translation
              label: Short Name
              placeholder: Enter Short Name
              name: shortName
              validators:
                - type: required
                - type: maxLength
                  args: '300'
                  text: Short Name should not exceed 300 characters
            - type: translation
              label: Long Name
              placeholder: Enter Long Name
              name: longName
              validators:
                - type: required
                - type: maxLength
                  args: '500'
                  text: Long Name should not exceed 500 characters
            - type: select
              label: Element group
              name: elementGroupCode
              outputValue: value
              _select:
                transform: $elementGroupsList()
              validators:
                - type: required
            - type: select
              label: Element Type
              name: elementTypeCode
              outputValue: value
              _select:
                transform: >-
                  $boolean($.fields.elementGroupCode) = true ? 
                  $.variables._filterElementType :
                  $.variables._elementTypesList  
            - type: dateRange
              mode: date-picker
              label: Effective Date
              name: effectiveDate
              validators:
                - type: required
              setting:
                type: day
                format: dd/MM/yyyy
              _value:
                transform: >-
                  $.extend.formType = 'create' ?
                  $not($exists($.fields.effectiveDate)) ?  $now()
            - type: radio
              label: Status
              name: status
              _value:
                transform: $.extend.formType = 'create' ? true
              radio:
                - label: Active
                  value: true
                - label: Inactive
                  value: false
        - type: group
          label: ''
          _condition:
            transform: $.extend.formType = 'view'
          fields:
            - type: radio
              label: ''
              name: deleteYN
              unvisible: true
              _value:
                transform: 'true'
              radio:
                - label: Active
                  value: true
                - label: Inactive
                  value: false
            - type: text
              label: Country
              name: country
            - type: text
              label: Formula Code
              name: code
              disabled: true
              placeholder: Automatic
            - type: translation
              label: Short Name
              placeholder: Enter Short Name
              name: shortName
              validators:
                - type: required
                - type: maxLength
                  args: '300'
                  text: Short Name should not exceed 300 characters
            - type: translation
              label: Long Name
              placeholder: Enter Long Name
              name: longName
              validators:
                - type: required
                - type: maxLength
                  args: '500'
                  text: Long Name should not exceed 500 characters
            - type: text
              label: Element group
              name: elementGroup
              outputValue: value
            - type: text
              label: Element Type
              name: elementType
              outputValue: value
            - type: dateRange
              mode: date-picker
              label: Effective Date
              name: effectiveDate
              validators:
                - type: required
              setting:
                type: day
                format: dd/MM/yyyy
              _value:
                transform: >-
                  $.extend.formType = 'create' ?
                  $not($exists($.fields.effectiveDate)) ?  $now()
            - type: radio
              label: Status
              name: status
              _value:
                transform: $.extend.formType = 'create' ? true
              radio:
                - label: Active
                  value: true
                - label: Inactive
                  value: false
        - type: textarea
          label: Report Formula
          name: expressionForReport
          textarea:
            autoSize:
              minRows: 3
            maxCharCount: 50000
          validators:
            - type: maxLength
              args: '50000'
              text: Report Formula should not exceed 50000 characters
        - type: translationTextArea
          label: Note
          name: note
          textarea:
            autoSize:
              minRows: 3
            maxCharCount: 1000
          validators:
            - type: maxLength
              args: '1000'
              text: Note should not exceed 1000 characters
    - type: group
      label: Element calculation list
      n_cols: 1
      collapse: false
      disableEventCollapse: true
      fields:
        - type: table
          tableId: report-columns-table
          name: reportColumns
          distinctByKey: codeMapping
          adjustHeight:
            elementSelector: .ant-modal-body
            difference: -200
          localPagination:
            pageSizeOptions:
              - 25
              - 50
              - 100
              - 250
          mapRowName:
            - codeMapping
          config:
            addSetup: true
            filter: true
            adjustHeaders: true
          _defaultFilterValue:
            transform: >-
              {'ElementGroupCode': $.fields.elementGroupCode, 'ElementTypeCode':
              $.fields.elementTypeCode}
          columns:
            - code: codeMapping
              title: Short Name
              width: 12
              type: text
              align: start
              readOnly: 'true'
            - code: name
              title: Long Name
              width: 12
              type: text
              align: start
              readOnly: 'true'
            - code: operatorCode
              title: Operation
              type: text
              width: 5
              align: start
              readOnly: 'true'
            - code: expressions
              title: Formula
              type: textarea
              name: expressions
              align: start
              width: 20
              display_type:
                key: ControlTextarea
              props:
                maxCharCount: 4000
                placeholder: Enter Formula
                action:
                  type: search
                  display:
                    type: ghost-gray
                    icon: icon-magnifying-glass-bold
                  updateType: replace
                suggestion:
                  - keyword: '@'
                    url: /api/salary-formulas/report-source-columns
                  - keyword: SystemData.
                    url: /api/report-column-sys
                  - keyword: CalcData.
                    url: /api/report-column-cal
            - code: priority
              title: Priority
              type: text
              width: 6
              align: start
              display_type:
                key: Control Number
              extra_config:
                type: integer
            - code: InputMasterShowYN
              title: Display Master
              type: Switch
              align: center
              width: 7
              display_type:
                key: Switch
              extra_config:
                tags:
                  - label: 'Yes'
                    value: true
                    class: success
                  - label: 'No'
                    value: false
                    class: default
              props:
                option:
                  displayLable: false
              readOnly: 'true'
            - code: InputDetailsShowYN
              title: Display Detail
              type: text
              extra_config:
                tags:
                  - label: 'Yes'
                    value: true
                    class: success
                  - label: 'No'
                    value: false
                    class: default
              align: center
              width: 7
              display_type:
                key: Switch
              props:
                option:
                  displayLable: false
              readOnly: 'true'
            - code: status
              title: Status
              width: 8
              type: text
              align: center
              display_type:
                key: Boolean Tag
              readOnly: 'true'
            - code: note
              label: textarea
              type: text
              title: Note
              align: start
              width: 20
              display_type:
                key: ControlTextarea
              props:
                maxCharCount: 1000
                placeholder: Enter Note
          layout_option:
            show_row_index: true
            tool_table:
              expand_filter: true
              show_table_checkbox: false
            show_pagination: false
            hide_action_row: false
            action_row:
              - id: delete
                type: ghost-gray
                icon: trash
                _disabled:
                  transform: $.disabled
            resize:
              height: true
          sortOption:
            key: priority
            orderOption: ASC
          _dataSource:
            transform: >-
              $.extend.formType != 'create' ?
              $sort($.variables._dataShow,function($l, $r) { $l.priority >
              $r.priority}) : $boolean($count($.variables._dataShow)) = true ?
              $sort($.variables._dataShow,function($l, $r) { $l.priority >
              $r.priority}) : []
          dependantField: $.fields.company
          addSetup:
            clientPagination: true
            columns:
              - code: codeMapping
                title: Short Name
                type: text
                align: start
                width: 150px
              - code: name
                title: Long Name
                type: text
                align: start
                width: 150px
              - code: operatorCode
                title: Operation
                type: textarea
                align: start
                width: 150px
              - code: priority
                title: Priority
                type: text
                align: start
                width: 250px
              - code: status
                title: Status
                type: text
                align: center
                width: 100px
                data_type:
                  key: Boolean
                  collection: data_types
                display_type:
                  key: Tag
                  collection: field_types
                readOnly: 'true'
              - code: note
                title: Note
                type: text
                align: start
                width: 250px
            filter:
              - type: group
                label: ''
                n_cols: 3
                fields:
                  - type: text
                    label: Element Caculation Code
                    placeholder: Enter Element Caculation Code
                    name: Code
                  - type: text
                    label: Short Name
                    placeholder: Enter Short Name
                    name: ShortName
                  - type: text
                    label: Long Name
                    placeholder: Enter Long Name
                    name: Name
            defaultData:
              id: 0
          _defaultData:
            transform: '{''id'': 0 , ''elementGroupCode'': $.fields.elementGroupCode}'
          filter:
            - type: group
              label: ''
              n_cols: 3
              fields:
                - type: text
                  label: Element Caculation Code
                  placeholder: Enter Element Caculation Code
                  name: elementCode
                - type: text
                  label: Short Name
                  placeholder: Enter Short Name
                  name: shortName
                - type: text
                  label: Long Name
                  placeholder: Enter Long Name
                  name: longName
          sources: >-
            /api/salary-formulas/report-type/report-columns/search-private-element
          cellAction:
            search:
              type: form-dialog
              settings:
                size: middle
                title: Search params
              config:
                submitValue:
                  transform: $.expressions
                fields:
                  - type: radio
                    label: Search Type
                    name: type
                    value: 1
                    radio:
                      - label: Param
                        value: 1
                      - label: Element
                        value: 2
                      - label: Element Calculation
                        value: 3
                  - type: select
                    name: elementGroupCode
                    clearFieldsAfterChange:
                      - elementCodeIncell
                    _label:
                      transform: >-
                        $.variables._typeSearch = 3 ? 'Element Group' : 'Data
                        Source'
                    _value:
                      transform: $.extend.value.elementGroupCode
                    outputValue: value
                    _condition:
                      transform: $.variables._typeSearch != 2
                    _select:
                      transform: $elementGroupsList()
                  - type: select
                    name: typeElementCalculation
                    label: Type
                    _condition:
                      transform: $.variables._typeSearch = 3
                    outputValue: value
                    value: 1
                    select:
                      - label: Element Caculation
                        value: 1
                      - label: Element Master
                        value: 2
                      - label: Element Month Salary
                        value: 3
                  - type: select
                    label: Element
                    isLazyLoad: true
                    _select:
                      transform: >-
                        $.variables._typeSearch = 1 ? $paramList($.extend.limit,
                        $.extend.page,
                        $.extend.search,$.fields.elementGroupCode) :
                        $.variables._typeSearch = 2 ?
                        $elementSystemList($.extend.limit, $.extend.page,
                        $.extend.search) : ($.fields.typeElementCalculation = 1
                        ? $elementCalculationList($.extend.limit, $.extend.page,
                        $.extend.search,$.fields.elementGroupCode) :
                        $.fields.typeElementCalculation = 2 ?
                        $elementCalculationMasterDataList($.extend.limit,
                        $.extend.page,
                        $.extend.search,$.fields.elementGroupCode) :
                        $.fields.typeElementCalculation = 3 ?
                        $elementCalculationMonthSalaryDataList($.extend.limit,
                        $.extend.page,
                        $.extend.search,$.fields.elementGroupCode) : [])
                    dependantField: $.fields.typeElementCalculation
                    name: elementCodeIncell
                    action:
                      field:
                        type: button
                        name: expressionQueue
                        dependantField: $.fields.elementCodeIncell
                        button:
                          - size: default
                            icon: icon-plus-circle-bold
                            type: ghost-gray
                            action: getDependentValue
                  - type: textarea
                    label: Description
                    dependantField: $.fields.elementCodeIncell
                    name: description
                    _value:
                      transform: $.variables._element.note
                    disabled: true
                  - type: textarea
                    label: Formula
                    updateType: replace
                    name: expressions
                    textarea:
                      autoSize:
                        minRows: 3
                      maxCharCount: 4000
                    suggestion:
                      - keyword: '@'
                        url: /api/salary-formulas/report-source-columns
                      - keyword: SystemData.
                        url: /api/report-column-sys
                      - keyword: CalcData.
                        url: /api/report-column-cal
                    _value:
                      transform: >-
                        $exists($.fields.expressionQueue.value) ? 'return ' &
                        $.fields.expressionQueue.value & ';' 
                sources:
                  elementGroupsList:
                    uri: '"/api/picklists/ELEMENTGROUP/values"'
                    method: GET
                    queryTransform: ''
                    bodyTransform: ''
                    headerTransform: ''
                    resultTransform: >-
                      $map($.data, function($item) {{'label':
                      $item.name.default, 'value': $item.code}})[]
                    disabledCache: true
                  elementCalculationList:
                    uri: '"/api/report-column-cal"'
                    method: GET
                    queryTransform: >-
                      {'limit': $.limit, 'page': $.page, 'search':
                      $.search,'filter':
                      [{'field':'elementGroupCodeFilter','operator':
                      '$eq','value': $.elementGroupCode}]}
                    bodyTransform: ''
                    headerTransform: ''
                    resultTransform: >-
                      $map($.data, function($item) {{'label': $item.label,
                      'value': $item.value, 'note': $item.note.default}})[]
                    disabledCache: true
                    params:
                      - limit
                      - page
                      - search
                      - elementGroupCode
                  elementCalculationMasterDataList:
                    uri: '"/api/report-column-cal/master-data"'
                    method: GET
                    queryTransform: >-
                      {'limit': $.limit, 'page': $.page, 'search':
                      $.search,'filter':
                      [{'field':'elementGroupCodeFilter','operator':
                      '$eq','value': $.elementGroupCode}]}
                    bodyTransform: ''
                    headerTransform: ''
                    resultTransform: >-
                      $map($.data, function($item) {{'label': $item.label,
                      'value': $item.value, 'note': $item.note.default}})[]
                    disabledCache: true
                    params:
                      - limit
                      - page
                      - search
                      - elementGroupCode
                  elementCalculationMonthSalaryDataList:
                    uri: '"/api/report-column-cal/month-salary-data"'
                    method: GET
                    queryTransform: >-
                      {'limit': $.limit, 'page': $.page, 'search':
                      $.search,'filter':
                      [{'field':'elementGroupCodeFilter','operator':
                      '$eq','value': $.elementGroupCode}]}
                    bodyTransform: ''
                    headerTransform: ''
                    resultTransform: >-
                      $map($.data, function($item) {{'label': $item.label,
                      'value': $item.value, 'note': $item.note.default}})[]
                    disabledCache: true
                    params:
                      - limit
                      - page
                      - search
                      - elementGroupCode
                  elementSystemList:
                    uri: '"/api/report-column-sys"'
                    method: GET
                    queryTransform: '{''limit'': $.limit, ''page'': $.page, ''search'': $.search}'
                    bodyTransform: ''
                    headerTransform: ''
                    resultTransform: >-
                      $map($.data, function($item) {{'label': $item.label,
                      'value': $item.value, 'note': $item.note.default}})[]
                    disabledCache: true
                    params:
                      - limit
                      - page
                      - search
                  paramList:
                    uri: '"/api/salary-formulas/report-source-columns"'
                    method: GET
                    queryTransform: >-
                      {'limit': $.limit, 'page': $.page, 'filter':
                      [{'field':'elementGroupCode','operator': '$eq','value':
                      $.elementGroupCode}, {'field':'label','operator':
                      '$cont','value': $.search}]}
                    bodyTransform: ''
                    headerTransform: ''
                    resultTransform: >-
                      $map($.items, function($item) {{'label': $item.label,
                      'value': $item.value, 'note': $item.name}})[]
                    disabledCache: true
                    params:
                      - limit
                      - page
                      - search
                      - elementGroupCode
                variables:
                  _element:
                    transform: $.fields.elementCodeIncell
                  _formula:
                    transform: $.fields.elementCodeStack
                  _elementCodeStack:
                    transform: $.fields.elementCodeStack
                  _typeSearch:
                    transform: '$exists($.fields.type) ? $.fields.type : 1 '
  sources:
    nationsList:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    salaryFormulasList:
      uri: '"/api/salary-formulas"'
      method: GET
      queryTransform: '{''filter'': [{''field'':''status'',''operator'': ''$eq'',''value'':true}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - effectiveDate
    elementGroupsList:
      uri: '"/api/picklists/ELEMENTGROUP/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})
      disabledCache: true
    elementTypesList:
      uri: '"/api/picklists/ELEMENTTYPE/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code,'elementGroup' : $item.codE501}})
      disabledCache: true
    elementCalculationList:
      uri: '"/api/report-column-cal"'
      method: GET
      queryTransform: '{''filter'': [{''field'':''status'',''operator'': ''$eq'',''value'':true}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $.data
      disabledCache: true
      params:
        - elementCode
        - shortName
        - longName
    elementGeneralList:
      uri: '"/api/salary-formulas/report-type/report-columns/search-general-element"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'ElementGroupCode','operator':
        '$eq','value':$.elementGroupCode},{'field':'ElementTypeCode','operator':
        '$eq','value':$.elementTypeCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $.data
      disabledCache: true
      params:
        - elementGroupCode
        - elementTypeCode
  variables:
    _elementGeneralList:
      transform: >-
        $.fields.elementGroupCode ?
        $elementGeneralList($.fields.elementGroupCode,$.fields.elementTypeCode)
    _elementTypesList:
      transform: $elementTypesList()
    _filterElementType:
      transform: >-
        $filter($.variables._elementTypesList , function($v) { $v.elementGroup =
        $.fields.elementGroupCode})[]
    _selectedType:
      transform: >-
        [($filter($.variables._elementTypesList , function($v) { $v.value =
        $.fields.elementTypeCode})).elementGroup]
    _elementGroupsList:
      transform: $elementGroupsList()
    _filterElementGroups:
      transform: >-
        [$filter($.variables._elementGroupsList , function($v) { $v.value =
        ($.variables._selectedType)[0]})]
    _dataNotIn:
      transform: >-
        $map($filter($.variables._elementGeneralList, function($item)
        {$not($item.code in $map($.extend.defaultValue.reportColumns,
        function($itemEdit) {$itemEdit.code}))}), function($v)
        {$merge([$v,{'id':0}])})
    _dataDefault:
      transform: >-
        ($.extend.formType = 'edit' or $.extend.isDuplicate = true or
        $.extend.formType = 'proceed') ?
        $map($.extend.defaultValue.reportColumns, function($child)
        {$merge([$child, {'elementGroupCode': $.fields.elementGroupCode}])}) :
        $map($.extend.defaultValue.reportColumns, function($child)
        {$merge([$child, {'elementGroupCode': $.fields.elementGroupCode}])})
    _dataShow:
      transform: >-
        $.extend.isDuplicate = true or $.extend.formType != 'create' ?
        $.variables._dataDefault : ($boolean($.fields.elementGroupCode) or
        $boolean($.fields.elementTypeCode)) ?
        $append($filter($.fields.reportColumns , function($item) {
        $item.isGeneralElement = false })[],
        $map($.variables._elementGeneralList, function($v)
        {$merge([$v,{'elementGroupCode': $.fields.elementGroupCode}])})) : []
    _longNameList:
      transform: $elementCalculationList()
filter_config:
  fields:
    - type: selectAll
      label: Formula Code
      name: code
      isLazyLoad: true
      _options:
        transform: $salaryFormulasList($.extend.limit, $.extend.page, $.extend.search)
    - name: shortName
      label: Short Name
      type: text
    - name: longName
      label: Long Name
      type: text
    - type: selectAll
      label: Country
      name: countryCode
      _options:
        transform: $nationsList()
    - type: selectAll
      label: Element group
      name: elementGroupCode
      _options:
        transform: $elementGroupsList()
    - type: selectAll
      label: Element Type
      name: elementTypeCode
      _options:
        transform: $elementTypesList()
    - type: dateRange
      label: Effective Date
      name: effectiveDate
    - type: radio
      label: Status
      name: status
      value: null
      radio:
        - label: All
          value: null
        - label: Active
          value: true
        - label: Inactive
          value: false
    - name: updatedBy
      label: Last Editor
      type: text
    - type: dateRange
      label: Lastest Edit Time
      name: updatedAt
  filterMapping:
    - field: shortName
      operator: $cont
      valueField: shortName
    - field: longName
      operator: $cont
      valueField: longName
    - field: code
      operator: $in
      valueField: code.(value)
    - field: countryCode
      operator: $in
      valueField: countryCode.(value)
    - field: elementGroupCode
      operator: $in
      valueField: elementGroupCode.(value)
    - field: elementTypeCode
      operator: $in
      valueField: elementTypeCode.(value)
    - field: effectiveDate
      operator: $between
      valueField: effectiveDate
    - field: status
      operator: $eq
      valueField: status
    - field: updatedAt
      operator: $between
      valueField: updatedAt
    - field: updatedBy
      operator: $cont
      valueField: updatedBy
  sources:
    nationsList:
      uri: '"/api/picklists/COUNTRIES/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})
      disabledCache: true
    salaryFormulasList:
      uri: '"/api/salary-formulas"'
      method: GET
      queryTransform: '{''limit'': $.limit, ''page'': $.page, ''search'': $.search, ''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    elementGroupsList:
      uri: '"/api/picklists/ELEMENTGROUP/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})
      disabledCache: true
    elementTypesList:
      uri: '"/api/picklists/ELEMENTTYPE/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})
      disabledCache: true
layout_options:
  show_dialog_form_save_add_button: true
  show_detail_history: true
  apply_delete_multi_items_to_delete_one: true
  custom_delete_body: '{''ids'': $map($.data,function($item){$item.id})[]}'
  reset_page_index_after_do_action:
    edit: true
  delete_multi_items: true
  custom_history_backend_url: /api/salary-formulas/:id/clone
  is_popup: true
  tool_table:
    - id: export
      icon: icon-download-simple
  show_filter_results_message: true
  store_selected_items: true
  hide_action_row: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: duplicate
    title: Duplicate
    icon: copy
  - id: edit
    title: Edit
    icon: pencil
    type: tertiary
  - id: delete
    title: Delete
    icon: trash
    type: tertiary
backend_url: /api/salary-formulas
screen_name: salary-formulas
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: countryCode
    defaultName: CountryCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Formula
  parent:
    title: PR Setting
