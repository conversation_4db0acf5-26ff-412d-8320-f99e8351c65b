id: PR.FS.FR.041
status: draft
sort: 319
user_created: 2aa5db12-fa54-46d6-8e05-9e421858de0c
date_created: '2024-07-25T03:22:02.319Z'
user_updated: b403d5da-d684-45e4-ae6d-e25223baedc5
date_updated: '2025-07-26T05:53:29.745Z'
title: Set up Salary Formula
requirement:
  time: 1749019429085
  blocks:
    - id: nzWvK3Ruqi
      type: paragraph
      data:
        text: >
          - mỗi <PERSON><PERSON> cấu, Pay group hoặc Nhóm thu nhập lại là những công thức khác
          nhau.
    - id: S7Jpv4gYjX
      type: paragraph
      data:
        text: >
          - B<PERSON> phận nhân sự tập đoàn có thể điều chỉnh thông tin khi có thay
          đổi.
    - id: ARUQrmf3C_
      type: paragraph
      data:
        text: >
          - <PERSON><PERSON> sách Thiết lập Kỳ tính lương hiển thị theo tiêu chí tìm kiếm.
          <PERSON><PERSON><PERSON> không có tiêu chí tìm kiếm nà<PERSON>, thực hiện hiển thị toàn bộ danh
          sách Thiết lập Kỳ tính lương đang có trên hệ thống theo thứ tự từ mới
          đến cũ.
    - id: i5V3Wp8zrS
      type: paragraph
      data:
        text: >-
          - Hệ thống kiểm tra phân quyền để hiển thị các chức năng tương ứng
          (Sửa, Xóa,..).&nbsp;&nbsp;
  version: 2.30.7
screen_design: null
module: PR
local_fields:
  - code: code
    title: Code
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    pinned: true
    show_sort: true
  - code: shortName
    title: Short Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: name
    title: Long Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: formulaCode
    pinned: false
    title: Formula Code
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: formulaShortName
    title: Formula Short Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: formulaLongName
    title: Formula Long Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: elementGroupName
    title: Element Group
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: elementTypeName
    title: Element Type
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: countryNames
    title: Country
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tags
      collection: field_types
    show_sort: true
  - code: companyNames
    title: Company
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tags
      collection: field_types
    show_sort: true
  - code: legalEntityNames
    title: Legal Entity
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tags
      collection: field_types
    show_sort: true
  - code: businessUnitNames
    title: Business Unit
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tags
      collection: field_types
    show_sort: true
  - code: divisionNames
    title: Division
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tags
      collection: field_types
    show_sort: true
  - code: departmentNames
    title: Department
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tags
      collection: field_types
    show_sort: true
  - code: payGroupNames
    title: Pay Group
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tags
      collection: field_types
    options__tabular__column_width: 20
    show_sort: true
  - code: localExpatName
    title: Local/Expat
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: effectiveDate
    title: Effective Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    show_sort: true
  - code: status
    title: Status
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: Boolean Tag
      collection: field_types
    show_sort: true
  - code: note
    title: Note
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: updatedBy
    title: Last Updated By
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: updatedAt
    title: Last Updated On
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
    show_sort: true
mock_data:
  - formulaCode: '00000001'
    formulaShortName: CT_LBS_FISHCM
    formulaLongName: CT Lương bổ sung FIS HCM
    elementGroup: Lương tháng
    elementType: Lương tháng
    country: Vietnam
    company: FPT IS
    legalEntity: FPT IS HCM
    businessUnit: Business Unit 1
    division: Division 1
    department: Department 1
    payGroupValue:
      - label: Pay Group 1
        value: Pay Group 1
      - label: Pay Group 1.1
        value: Pay Group 1.1
    localOrExpat: Local
    effectiveDate: 01/01/2023
    status: Active
    note: Note for record 1
    creator: User1
    createdTime: 01/01/2023
    lastEditor: Editor1
    lastEditTime: 01/01/2023
local_buttons: null
layout: layout-table
form_config:
  _formTitle:
    history: '''View Salary Formula Setup Details'''
    edit: '''Edit Salary Formula Setup'''
    create: '''Add New Salary Formula Setup'''
    duplicate: '''Duplicate Salary Formula Setup'''
    proceed: '''Insert New Record Salary Formula Setup'''
  historyHeaderTitle: '''View Salary Formula Setup Details'''
  defaultValidateError: Required fields have not been filled in completely (fields marked with *)
  fields:
    - type: group
      n_cols: 2
      collapsed: false
      _condition:
        transform: $.extend.formType != 'view'
      disableEventCollapse: true
      fields:
        - type: text
          label: Code
          name: code
          _value:
            transform: >-
              ($.extend.formType = 'create' and $.extend.isDuplicate = true) ?
              '_setValueNull'
          _disabled:
            transform: 'true'
          validators:
            - type: required
          placeholder: System - Generated
        - type: dateRange
          label: Effective Date
          name: effectiveDate
          mode: date-picker
          setting:
            type: day
            format: dd/MM/yyyy
          _value:
            transform: >-
              $.extend.formType = 'create' ?
              $not($exists($.fields.effectiveDate)) ?  $now()
          validators:
            - type: required
        - type: radio
          label: Status
          name: status
          _value:
            transform: $.extend.formType = 'create' ? true
          radio:
            - label: Active
              value: true
            - label: Inactive
              value: false
        - type: selectAll
          label: Country
          name: countryCode
          outputValue: value
          mode: multiple
          isLazyLoad: true
          _validateFn:
            transform: >-
              $.extend.defaultValue ? {'label': $.extend.defaultValue.country,
              'value': $.extend.defaultValue.countryCode}
            params:
              updateLabelExistOption: true
          _options:
            transform: $nationsList($.extend.limit, $.extend.page, $.extend.search)
        - type: translation
          label: Short Name
          placeholder: Enter Short Name
          name: shortName
          validators:
            - type: required
            - type: maxLength
              args: '300'
              text: Short Name should not exceed 300 characters
        - type: translation
          label: Long Name
          placeholder: Enter Long Name
          name: name
          validators:
            - type: required
            - type: maxLength
              args: '500'
              text: Long Name should not exceed 500 characters
        - type: select
          label: Element group
          clearFieldsAfterChange:
            - formulaCode
          name: elementGroupCode
          outputValue: value
          _value:
            transform: >-
              $.extend.formType = 'create' and $boolean($.fields.formulaCode) =
              true ? $.variables._formulaSelected.elementGroup
          _select:
            transform: $elementGroupsList($.fields.effectiveDate)
        - type: select
          label: Element Type
          clearFieldsAfterChange:
            - formulaCode
          name: elementTypeCode
          outputValue: value
          _value:
            transform: >-
              $.extend.formType = 'create' and $boolean($.fields.formulaCode) =
              true ? $.variables._formulaSelected.elementType
          _select:
            transform: >-
              $elementTypesList($.fields.effectiveDate,$.fields.elementGroupCode)
        - type: select
          label: Formula
          name: formulaCode
          validators:
            - type: required
          outputValue: value
          _select:
            transform: $.variables._formulaList
          _condition:
            transform: $.extend.formType != 'view'
          col: 2
    - type: text
      name: code
      label: Code
      placeholder: Automatic
      _condition:
        transform: $.extend.formType = 'view'
    - type: dateRange
      label: Effective Date
      name: effectiveDate
      mode: date-picker
      setting:
        type: day
        format: dd/MM/yyyy
      _condition:
        transform: $.extend.formType = 'view'
      _value:
        transform: >-
          $.extend.formType = 'create' ? $not($exists($.fields.effectiveDate))
          ?  $now()
      validators:
        - type: required
    - type: radio
      label: Status
      name: status
      _value:
        transform: $.extend.formType = 'create' ? true
      _condition:
        transform: $.extend.formType = 'view'
      radio:
        - label: Active
          value: true
        - label: Inactive
          value: false
    - type: selectAll
      label: Country
      name: countryCode
      outputValue: value
      mode: multiple
      isLazyLoad: true
      _condition:
        transform: $.extend.formType = 'view'
      _validateFn:
        transform: >-
          $.extend.defaultValue ? {'label': $.extend.defaultValue.country,
          'value': $.extend.defaultValue.countryCode}
        params:
          updateLabelExistOption: true
      _options:
        transform: $nationsList($.extend.limit, $.extend.page, $.extend.search)
    - type: translation
      label: Short Name
      placeholder: Enter Short Name
      name: shortName
      _condition:
        transform: $.extend.formType = 'view'
      validators:
        - type: required
        - type: maxLength
          args: '300'
          text: Short Name should not exceed 300 characters
    - type: translation
      label: Long Name
      placeholder: Enter Long Name
      name: name
      _condition:
        transform: $.extend.formType = 'view'
      validators:
        - type: required
        - type: maxLength
          args: '500'
          text: Long Name should not exceed 500 characters
    - type: text
      name: elementGroupName
      label: Element Group
      _condition:
        transform: $.extend.formType = 'view'
    - type: text
      name: elementTypeName
      label: Element Type
      _condition:
        transform: $.extend.formType = 'view'
    - type: text
      name: formulaCode
      label: Formula Code
      _condition:
        transform: $.extend.formType = 'view'
    - type: text
      label: Formula Long Name
      name: formulaLongName
      _condition:
        transform: $.extend.formType = 'view'
    - type: text
      label: Formula Short Name
      name: formulaShortName
      _condition:
        transform: $.extend.formType = 'view'
    - type: selectAll
      label: Company
      placeholder: Select option
      name: companyObj
      _condition:
        transform: $.extend.formType != 'view'
      clearFieldsAfterChange:
        - payGroupObj
        - legalEntityObj
        - businessUnitObj
        - divisionObj
        - departmentObj
      mode: multiple
      isLazyLoad: true
      outputValue: value
      _options:
        transform: >-
          $companiesList($.extend.limit, $.extend.page, $.extend.search,
          $.fields.effectiveDate)
    - type: text
      clearFieldsAfterChange:
        - payGroupObj
        - legalEntityObj
        - businessUnitObj
        - divisionObj
        - departmentObj
      name: companyCheckPayGroup
      dependantField: $.fields.companyObj
      unvisible: true
      _value:
        transform: >-
          ($count($.fields.companyObj) > 0 and $count($.fields.companyObj) < 2)
          ? $.fields.companyObj.value ? $.fields.companyObj[0].value :
          $.fields.companyObj[0]
    - type: radio
      label: Object
      name: object
      value: Organization
      validators:
        - type: required
      radio:
        - label: Organization
          value: Organization
        - label: Pay Group
          value: PayGroup
      _value:
        transform: >-
          $.extend.formType = 'create' and $.extend.isDuplicate = false ?
          $not($exists($.fields.object)) ?  'Organization'
      _condition:
        transform: >-
          $not($.extend.formType = 'view') and ($count($.fields.companyObj) > 0
          and $count($.fields.companyObj) < 2)
    - type: text
      label: Object
      name: object
      _condition:
        transform: >-
          ($.extend.formType = 'view') and ($count($.fields.companyObj) > 0 and
          $count($.fields.companyObj) < 2)
    - type: group
      fieldBackground: '#F8F9FA'
      borderRadius: 8px
      _condition:
        transform: >-
          $.extend.formType != 'view' and ($count($.fields.companyObj) > 0 and
          $count($.fields.companyObj) < 2)
      padding: 16px
      fields:
        - type: selectAll
          label: Legal Entity
          placeholder: Select option
          name: legalEntityObj
          dependantField: $.fields.companyObj
          mode: multiple
          isLazyLoad: true
          outputValue: value
          _condition:
            transform: $.fields.object='Organization'
          _disabled:
            transform: $count($.fields.companyObj) > 1
          _options:
            transform: >-
              $legalEntitiesList($.extend.limit, $.extend.page, $.extend.search,
              $map($.fields.companyObj,function($v) {$v.value ? $v.value.id :
              $v.id}))
        - type: selectAll
          label: Business Unit
          name: businessUnitObj
          dependantField: $.fields.companyObj
          clearFieldsAfterChange:
            - divisionObj
          isLazyLoad: true
          mode: multiple
          outputValue: value
          _disabled:
            transform: $count($.fields.companyObj) > 1
          _options:
            transform: >-
              $businessUnitsList($.extend.limit, $.extend.page ,$.extend.search
              ,$.fields.effectiveDate, $map($.fields.companyObj,function($v)
              {$v.value ? $v.value.id : $v.id}))
          _condition:
            transform: $.fields.object='Organization'
        - type: selectAll
          label: Division
          name: divisionObj
          clearFieldsAfterChange:
            - departmentObj
          mode: multiple
          isLazyLoad: true
          _disabled:
            transform: $count($.fields.companyObj) > 1
          outputValue: value
          _options:
            transform: >-
              $divisionsList($.extend.limit, $.extend.page, $.extend.search,
              $.fields.effectiveDate,$map($.fields.businessUnitObj,function($v)
              {$v.value ? $v.value.id : $v.id}))
          _condition:
            transform: $.fields.object='Organization'
        - type: selectAll
          label: Department
          name: departmentObj
          _disabled:
            transform: $count($.fields.companyObj) > 1
          isLazyLoad: true
          mode: multiple
          outputValue: value
          _options:
            transform: >-
              $departmentsList($.extend.limit, $.extend.page, $.extend.search,
              $.fields.effectiveDate,$map($.fields.legalEntityObj,function($v)
              {$v.value ? $v.value.id : $v.id}))
          _condition:
            transform: $.fields.object='Organization'
        - type: selectAll
          label: Pay Group
          name: payGroupObj
          dependantField: $.fields.companyCheckPayGroup
          _disabled:
            transform: $count($.fields.companyObj) > 1
          isLazyLoad: true
          mode: multiple
          _options:
            transform: >-
              $.fields.companyObj ? $payGroupList($.extend.limit, $.extend.page,
              $.extend.search, $map($.fields.companyObj,function($v) {$v.value ?
              $v.value.code : $v.code}))
          _condition:
            transform: $.fields.object='PayGroup'
    - type: selectAll
      label: Company
      placeholder: Select option
      name: companyObj
      _condition:
        transform: $.extend.formType = 'view'
      clearFieldsAfterChange:
        - payGroupObj
      mode: multiple
      isLazyLoad: true
      outputValue: value
      _options:
        transform: >-
          $companiesList($.extend.limit, $.extend.page, $.extend.search,
          $.fields.effectiveDate)
    - type: group
      _condition:
        transform: >-
          $.extend.formType = 'view' and ($count($.fields.companyObj) > 0 and
          $count($.fields.companyObj) < 2)
      fields:
        - type: selectAll
          label: Legal Entity
          placeholder: Select option
          name: legalEntityObj
          dependantField: $.fields.companyObj
          mode: multiple
          isLazyLoad: true
          outputValue: value
          _condition:
            transform: $.fields.object='Organization'
          _disabled:
            transform: $count($.fields.companyObj) > 1
          _options:
            transform: >-
              $legalEntitiesList($.extend.limit, $.extend.page, $.extend.search,
              $map($.fields.companyObj,function($v) {$v.value ? $v.value.id :
              $v.id}))
        - type: selectAll
          label: Business Unit
          name: businessUnitObj
          dependantField: $.fields.companyObj
          isLazyLoad: true
          mode: multiple
          outputValue: value
          _disabled:
            transform: $count($.fields.companyObj) > 1
          _options:
            transform: >-
              $businessUnitsList($.extend.limit, $.extend.page ,$.extend.search
              ,$.fields.effectiveDate, $map($.fields.companyObj,function($v)
              {$v.value ? $v.value.id : $v.id}))
          _condition:
            transform: $.fields.object='Organization'
        - type: selectAll
          label: Division
          name: divisionObj
          mode: multiple
          isLazyLoad: true
          _disabled:
            transform: $count($.fields.companyObj) > 1
          outputValue: value
          _options:
            transform: >-
              $divisionsList($.extend.limit, $.extend.page, $.extend.search,
              $.fields.effectiveDate,$map($.fields.businessUnitObj,function($v)
              {$v.value ? $v.value.id : $v.id}))
          _condition:
            transform: $.fields.object='Organization'
        - type: selectAll
          label: Department
          name: departmentObj
          _disabled:
            transform: $count($.fields.companyObj) > 1
          isLazyLoad: true
          mode: multiple
          outputValue: value
          _options:
            transform: >-
              $departmentsList($.extend.limit, $.extend.page, $.extend.search,
              $.fields.effectiveDate,$map($.fields.legalEntityObj,function($v)
              {$v.value ? $v.value.id : $v.id}))
          _condition:
            transform: $.fields.object='Organization'
        - type: selectAll
          label: Pay Group
          name: payGroupObj
          dependantField: $.fields.companyObj
          _disabled:
            transform: $count($.fields.companyObj) > 1
          isLazyLoad: true
          mode: multiple
          _options:
            transform: >-
              $payGroupList($.extend.limit, $.extend.page, $.extend.search,
              $map($.fields.companyObj,function($v) {$v.value ? $v.value.code :
              $v.code}))
          _condition:
            transform: $.fields.object='PayGroup'
    - type: select
      label: Local/Expat
      name: localExpat
      outputValue: value
      _condition:
        transform: $.fields.object='Organization'
      _select:
        transform: $locationsList()
    - type: translationTextArea
      label: Note
      name: note
      placeholder: Enter Note
      textarea:
        autoSize:
          minRows: 3
        maxCharCount: 1000
      validators:
        - type: maxLength
          args: '1000'
          text: Note should not exceed 1000 characters
  sources:
    nationsList:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    salaryFormulasList:
      uri: '"/api/salary-formulas"'
      method: GET
      queryTransform: >-
        {'limit': 1000,'filter': [{'field':'status','operator':
        '$eq','value':true}, {'field':'elementGroupCode','operator':
        '$eq','value':$.elementGroupCode} ,
        {'field':'elementTypeCode','operator': '$eq','value':$.salaryTypeCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code , 'elementGroup' :
        $item.elementGroupCode, 'elementType' : $item.elementTypeCode}})[]
      disabledCache: true
      params:
        - effectiveDate
        - elementGroupCode
        - salaryTypeCode
    elementGroupsList:
      uri: '"/api/picklists/ELEMENTGROUP/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - effectiveDate
    elementTypesList:
      uri: '"/api/picklists/ELEMENTTYPE/values"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'status','operator':
        '$eq','value':true},{'field':'linkCatalogDataCode','operator':'$in','value':$.elementGroupCode}
        ]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - effectiveDate
        - elementGroupCode
    companiesList:
      uri: '"/api/companies/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'search': $.search,  'filter':
        [{'field':'groupId','operator':
        '$eq','value':$.groupId},{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator': '$eq','value':
        $.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id': $item.id,'code' :$item.code}}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
        - groupId
    legalEntitiesList:
      uri: '"/api/legal-entities/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'search': $.search, 'filter':
        [{'field':'companyId','operator':
        '$eq','value':$.companyId},{'field':'groupId','operator':
        '$eq','value':$.groupId},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')','value': {'id': $item.id,'code' :$item.code}}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - companyId
        - groupId
    businessUnitsList:
      uri: '"/api/business-units/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'effectiveDate','operator': '$eq','value':$.effectiveDate},
        {'field':'status','operator': '$eq','value':true}, {'field':'companyId',
        'operator': '$eq','value':$.companyId}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value':{'id': $item.id,'code' : $item.code}}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
        - companyId
    divisionsList:
      uri: '"/api/divisions/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search , 'filter':
        [{'field':'effectiveDate','operator': '$eq','value':$.effectiveDate},
        {'field':'status','operator': '$eq','value':true},
        {'field':'businessUnitId', 'operator': '$in','value':$.businessUnitId}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value':{'id': $item.id,'code' : $item.code}}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
        - businessUnitId
    departmentsList:
      uri: '"/api/departments/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'pageSize': $.pageSize,'search':
        $.search, 'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}, {'field':'status','operator':
        '$eq','value':true}, {'field':'LegalEntityIds', 'operator':
        '$eq','value':$.LegalEntityIds}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value':{'id': $item.id,'code' : $item.code}}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
        - LegalEntityIds
    locationsList:
      uri: '"/api/picklists/LOCALFOREIGNERS/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id}})[]
      disabledCache: true
      params:
        - effectiveDate
    payGroupList:
      uri: '"/api/pay-group-structures"'
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'pageSize': $.pageSize,'search':
        $.search, 'filter': [{'field':'status','operator': '$eq','value':true},
        {'field':'companyCode','operator': '$eq','value': $.companyCode }]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': {'id': $item.id, 'code': $item.code} }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - companyCode
  variables:
    _formulaList:
      transform: >-
        $salaryFormulasList($.fields.effectiveDate , $.fields.elementGroupCode,
        $.fields.elementTypeCode)
    _formulaSelected:
      transform: >-
        $filter($.variables._formulaList, function($v) { $v.value =
        $.fields.formulaCode})
filter_config:
  fields:
    - type: text
      label: Code
      placeholder: Enter Code
      name: code
      labelType: type-grid
    - type: text
      label: Short Name
      placeholder: Enter Short Name
      name: shortName
      labelType: type-grid
    - type: text
      label: Long Name
      placeholder: Enter Long Name
      name: name
      labelType: type-grid
    - type: text
      label: Formula Code
      placeholder: Enter Formula Code
      name: formulaCode
      labelType: type-grid
    - type: selectAll
      label: Element Group
      placeholder: Select Element Group
      mode: multiple
      labelType: type-grid
      name: elementGroupCode
      _options:
        transform: $elementGroupsList()
    - type: selectAll
      label: Element Type
      labelType: type-grid
      placeholder: Select Element Type
      name: elementTypeCode
      mode: multiple
      _options:
        transform: $elementTypesList()
    - type: selectAll
      label: Country
      labelType: type-grid
      name: countryCodes
      placeholder: Select Country
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $nationsList($.extend.limit, $.extend.page, $.extend.search)
    - type: radio
      label: Object
      labelType: type-grid
      name: object
      radio:
        - label: Organization
          value: Organization
        - label: Pay Group
          value: PayGroup
    - type: selectAll
      label: Company
      labelType: type-grid
      placeholder: Select Company
      name: companyCodes
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $companiesList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      labelType: type-grid
      placeholder: Select Legal Entity
      label: Legal Entity
      name: legalEntityCodes
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $legalEntitiesList($.extend.limit, $.extend.page, $.extend.search)
      _condition:
        transform: $.fields.object = 'Organization'
    - type: selectAll
      label: Business Unit
      labelType: type-grid
      placeholder: Select Business Unit
      name: businessUnitCodes
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $businessUnitsList($.extend.limit, $.extend.page, $.extend.search)
      _condition:
        transform: $.fields.object = 'Organization'
    - type: selectAll
      label: Division
      labelType: type-grid
      placeholder: Select Division
      name: divisionCodes
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $divisionsList($.extend.limit, $.extend.page, $.extend.search)
      _condition:
        transform: $.fields.object = 'Organization'
    - type: selectAll
      label: Department
      placeholder: Select Department
      labelType: type-grid
      name: departmentCodes
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $departmentsList($.extend.limit, $.extend.page, $.extend.search)
      _condition:
        transform: $.fields.object = 'Organization'
    - type: selectAll
      label: Pay Group
      labelType: type-grid
      placeholder: Select Pay Group
      isLazyLoad: true
      name: payGroupCodes
      mode: multiple
      _options:
        transform: $payGroupsList($.extend.limit, $.extend.page, $.extend.search)
      _condition:
        transform: $.fields.object = 'Paygroup'
    - type: selectAll
      label: Local/ Expat
      name: localExpat
      placeholder: Select Local/ Expat
      labelType: type-grid
      mode: multiple
      _options:
        transform: $locationsList()
    - type: dateRange
      labelType: type-grid
      label: Effective Date
      name: effectiveDate
    - type: radio
      labelType: type-grid
      label: Status
      name: status
      value: ''
      radio:
        - label: All
          value: ''
        - label: Active
          value: true
        - label: Inactive
          value: false
    - type: text
      name: note
      labelType: type-grid
      label: Note
      placeholder: Enter Note
    - name: updatedBy
      label: Last Updated By
      type: text
      labelType: type-grid
      placeholder: Enter Last Updated By
    - name: updatedAt
      label: Last Updated On
      type: dateRange
      labelType: type-grid
      setting:
        mode: date
        format: dd/MM/yyyy
  filterMapping:
    - field: code
      operator: $cont
      valueField: code
    - field: shortName
      operator: $cont
      valueField: shortName
    - field: name
      operator: $cont
      valueField: name
    - field: formulaCode
      operator: $cont
      valueField: formulaCode
    - field: shortName
      operator: $cont
      valueField: shortName
    - field: longName
      operator: $cont
      valueField: longName
    - field: countryCodes
      operator: $eq
      valueField: countryCodes.(value)
    - field: elementGroupCodeFilter
      operator: $in
      valueField: elementGroupCode.(value)
    - field: elementTypeCodeFilter
      operator: $in
      valueField: elementTypeCode.(value)
    - field: object
      operator: $eq
      valueField: object
    - field: companyCodes
      operator: $eq
      valueField: companyCodes.(value)
    - field: legalEntityCodes
      operator: $eq
      valueField: legalEntityCodes.(value)
    - field: businessUnitCodes
      operator: $eq
      valueField: businessUnitCodes.(value)
    - field: divisionCodes
      operator: $eq
      valueField: divisionCodes.(value)
    - field: departmentCodes
      operator: $eq
      valueField: departmentCodes.(value)
    - field: payGroupCodes
      operator: $eq
      valueField: payGroupCodes.(value)
    - field: localExpat
      operator: $in
      valueField: localExpat.(value)
    - field: effectiveDate
      operator: $between
      valueField: effectiveDate
    - field: status
      operator: $eq
      valueField: status
    - field: note
      operator: $cont
      valueField: note
    - field: updatedBy
      operator: $cont
      valueField: updatedBy
    - field: updatedAt
      operator: $between
      valueField: updatedAt
  sources:
    nationsList:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    salaryFormulasList:
      uri: '"/api/salary-formulas"'
      method: GET
      queryTransform: '{''filter'': [],''limit'':1000}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - effectiveDate
    elementGroupsList:
      uri: '"/api/picklists/ELEMENTGROUP/values"'
      method: GET
      queryTransform: '{''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - effectiveDate
    elementTypesList:
      uri: '"/api/picklists/ELEMENTTYPE/values"'
      method: GET
      queryTransform: '{''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - effectiveDate
    companiesList:
      uri: '"/api/companies/get-by"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    legalEntitiesList:
      uri: '"/api/legal-entities/get-by"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    businessUnitsList:
      uri: '"/api/business-units/get-by"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    divisionsList:
      uri: '"/api/divisions/get-by"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    departmentsList:
      uri: '"/api/departments/get-by"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    locationsList:
      uri: '"/api/picklists/LOCALFOREIGNERS/values/pagination"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    payGroupsList:
      uri: '"/api/pay-group-structures"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'companyCode','operator': '$in','value':
        $.companyCode},{'field':'companyCode','operator':
        '$eq','value':$.companyCode? 'NULL':''}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - companyCode
    userList:
      uri: '"/api/internal-users"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.userName, 'value':
        $item.userName}})
      disabledCache: true
layout_options:
  custom_history_backend_url: /api/formula-structures/:id/clone
  show_dialog_form_save_add_button: true
  delete_multi_items: true
  apply_delete_multi_items_to_delete_one: true
  custom_delete_body: '{''ids'': $map($.data,function($item){$item.id})[]}'
  show_detail_history: true
  tool_table:
    - id: export
      icon: icon-download-simple
  show_filter_results_message: true
  store_selected_items: true
  is_new_dynamic_form: true
  hide_action_row: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: null
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: duplicate
    icon: icon-copy
    type: ghost-gray
  - id: edit
    icon: icon-pencil-simple
    type: ghost-gray
  - id: delete
    icon: icon-trash
    type: ghost-gray
backend_url: /api/formula-structures
screen_name: formula-structures
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: countryCodes
    defaultName: CountryCode
  - name: companyCodes
    defaultName: CompanyCode
  - name: payGroupCodes
    defaultName: PayGroupCode
  - name: legalEntityCodes
    defaultName: LegalEntityCode
  - name: businessUnitCodes
    defaultName: BusinessUnitCode
  - name: divisionCodes
    defaultName: DivisionCode
  - name: departmentCodes
    defaultName: DepartmentCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Set up Salary Formula
  parent:
    title: PR Setting
