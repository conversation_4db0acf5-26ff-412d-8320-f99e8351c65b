id: PR.FS.FR.046
status: draft
sort: 352
user_created: 56e71b91-dd39-4ebc-afd1-b4f00f38d4a5
date_created: '2024-07-25T07:34:11.226Z'
user_updated: b403d5da-d684-45e4-ae6d-e25223baedc5
date_updated: '2025-07-28T02:15:52.340Z'
title: List Of Paid Employees
requirement:
  time: 1747882006179
  blocks:
    - id: kBvYXQGZqs
      type: paragraph
      data:
        text: "-\t Mục đích: <PERSON><PERSON><PERSON> thị danh sách nhân viên được tính lương trong Kỳ&amp; <PERSON><PERSON><PERSON> t<PERSON>h lư<PERSON> tươ<PERSON>, để người dùng kiểm tra danh sách chuẩn bị tính lương."
    - id: IHkMjAcvr_
      type: paragraph
      data:
        text: >-
          1. <PERSON><PERSON><PERSON> buộc người dùng phải chọn dữ liệu: <PERSON><PERSON>, <PERSON><PERSON><PERSON> t<PERSON>
          l<PERSON>. <PERSON><PERSON> đ<PERSON> hệ thống load dữ liệu tương <PERSON>ng - <PERSON><PERSON> liệu đ<PERSON> lấy như
          sau:
    - id: pMW4W9o0td
      type: paragraph
      data:
        text: '   a. Lưới bên trái "Danh sách nhân viên tinh lương/List of salaried employees": Load dữ liệu các nhân sự có "PR Status" (Tham chiếu HR.FS.FR.009) là: '
    - id: 79YB4ILNzT
      type: paragraph
      data:
        text: '     - Active'
    - id: dmI8F151XL
      type: paragraph
      data:
        text: '     - Teminated (Là trạng thái các nhân viên Chấm dứt - tức vẫn cần kiểm tra và thanh toán trong kỳ lương =&gt; chưa Inactive)'
    - id: XxZA-mfnac
      type: paragraph
      data:
        text: '     - Leave of absence (Nghỉ phép)'
    - id: dzZnXb9xNv
      type: paragraph
      data:
        text: '   b. Lưới bên phải  "Danh sách nhân viên không tinh lương/List of salaried employees": Load dữ liệu các nhân sự có "PR Status" (Tham chiếu HR.FS.FR.009) là "Inactive"'
    - id: 4E-GhspzFE
      type: paragraph
      data:
        text: >-
          2. Bộ phận nhân sự tập đoàn có thể điều chỉnh thông tin khi có thay
          đổi bằng cách:
    - id: iQWSHmICgy
      type: paragraph
      data:
        text: '       + Thêm mới (dựa vào các điều kiện tìm kiếm)'
    - id: IwLpSmgylU
      type: paragraph
      data:
        text: '       + Hoặc đưa các nhân viên sang danh sách không được tính lương tháng (Lưới bên trai)'
    - id: FKUnic78t9
      type: paragraph
      data:
        text: >-
          3. Danh sách Danh sách nhân viên tính lương hiển thị theo tiêu chí tìm
          kiếm. Nếu không có tiêu chí tìm kiếm nào, thực hiện hiển thị toàn bộ
          danh sách Danh sách nhân viên tính lương theo tự động.
    - id: W1kCtQb-te
      type: paragraph
      data:
        text: >-
          4. Hệ thống kiểm tra phân quyền để hiển thị các chức năng tương ứng
          (Thêm, Xóa, Sao chép..).
  version: 2.30.7
screen_design: null
module: PR
local_fields:
  - code: employeeId
    title: Employee ID
    description: Hiển thị dữ liệu Mã nhân viên của nhân viên
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 10
    options__tabular__align: left
    show_sort: true
    pinned: true
  - code: fullName
    title: Full name
    description: Hiển thị thông tin Họ và tên của nhân viên
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 12
    show_sort: true
  - code: employeeRecordNumber
    title: ERN
    description: Hiển thị thông tin Mã hồ sơ công việc của nhân viên
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 2
    show_sort: true
  - code: employeeGroup
    title: Employee Group
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
    options__tabular__column_width: 12
  - code: jobIndicator
    title: Job indicator
    description: Hiển thị thông tin Việc chính/việc phụ của nhân viên
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 12
    show_sort: true
  - code: genderName
    title: Gender
    description: Hiển thị dữ liệu giới tính của nhân viên
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 8
    show_sort: true
  - code: legalEntity
    title: Legal Entity
    description: Hiển thị dữ liệu Pháp nhân (Legal Entity) của nhân viên
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 12
    show_sort: true
  - code: department
    title: Department
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 12
    show_sort: true
  - code: payGroup
    title: Pay Group
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: position
    title: Position
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 12
    show_sort: true
  - code: prStatus
    title: Payroll Status
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tag
      collection: field_types
    options__tabular__column_width: 12
    show_sort: true
    extra_config:
      tags:
        - value: Active
          label: Active
          class: success
        - value: Inactive
          label: Inactive
          class: default
        - value: Terminated
          label: Terminated
          class: error
          style:
            background_color: '#ffcfc9'
        - value: Suspended
          label: Suspended
          class: orange
          style:
            background_color: '#ffc8aa'
        - value: Leave of Absence
          label: Leave of Absence
          class: infor
          style:
            background_color: '#bfe1f6'
        - value: Leave With Pay
          label: Leave With Pay
          class: infor
          style:
            background_color: '#bfe1f6'
  - code: isLock
    title: Locked
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Lock
      collection: field_types
    options__tabular__column_width: 20
    dragable: false
    show_sort: true
  - code: note
    title: Note
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
mock_data:
  - employeeID: '0389328'
    employeeRecordNumber: '1'
    jobIndicator: Primary
    fullName: Trần Văn A
    gender: Nam
    legalEntity: FPT IS HCM
    department: PeopleX
    position: Trưởng nhóm
    payrollStatus: Active
    payrollCalculationStatus: Not locked
    period: Kỳ lương tháng 7/2024
    subPeriod: Kỳ lương tháng 7/2024 - Đợt 1
  - employeeID: '0837283'
    employeeRecordNumber: '2'
    jobIndicator: Primary
    fullName: Nguyễn Thị C
    gender: Nữ
    legalEntity: FPT IS HCM
    department: PeopleX
    position: Trưởng nhóm
    payrollStatus: Inactive
    payrollCalculationStatus: Not locked
    period: Kỳ lương tháng 7/2024
    subPeriod: Kỳ lương tháng 7/2024 - Đợt 1
  - employeeID: '0389328'
    employeeRecordNumber: '3'
    jobIndicator: Primary
    fullName: Nguyễn Thị B
    gender: Nữ
    legalEntity: FPT IS HCM
    department: PeopleX
    position: Trưởng nhóm
    payrollStatus: Inactive
    payrollCalculationStatus: Not locked
    period: Kỳ lương tháng 7/2024
    subPeriod: Kỳ lương tháng 7/2024 - Đợt 1
  - employeeID: '0389328'
    employeeRecordNumber: '4'
    jobIndicator: Secondary
    fullName: Nguyễn Thị D
    gender: Nữ
    legalEntity: FPT IS HCM
    department: PeopleX
    position: Trưởng nhóm
    payrollStatus: Active
    payrollCalculationStatus: Locked
    period: Kỳ lương tháng 7/2024
    subPeriod: Kỳ lương tháng 7/2024 - Đợt 1
  - employeeID: '0837283'
    employeeRecordNumber: '5'
    jobIndicator: Secondary
    fullName: Nguyễn Thị E
    gender: Nữ
    legalEntity: FPT IS HCM
    department: PeopleX
    position: Trưởng nhóm
    payrollStatus: Active
    payrollCalculationStatus: Locked
    period: Kỳ lương tháng 7/2024
    subPeriod: Kỳ lương tháng 7/2024 - Đợt 1
  - employeeID: '0389328'
    employeeRecordNumber: '1'
    jobIndicator: Primary
    fullName: Trần Văn A
    gender: Nam
    legalEntity: FPT IS HCM
    department: PeopleX
    position: Trưởng nhóm
    payrollStatus: Active
    payrollCalculationStatus: Not locked
    period: Kỳ lương tháng 7/2024
    subPeriod: Kỳ lương tháng 7/2024 - Đợt 1
  - employeeID: '0837283'
    employeeRecordNumber: '2'
    jobIndicator: Primary
    fullName: Nguyễn Thị C
    gender: Nữ
    legalEntity: FPT IS HCM
    department: PeopleX
    position: Trưởng nhóm
    payrollStatus: Inactive
    payrollCalculationStatus: Not locked
    period: Kỳ lương tháng 7/2024
    subPeriod: Kỳ lương tháng 7/2024 - Đợt 2
  - employeeID: '0389328'
    employeeRecordNumber: '3'
    jobIndicator: Primary
    fullName: Nguyễn Thị B
    gender: Nữ
    legalEntity: FPT IS HCM
    department: PeopleX
    position: Trưởng nhóm
    payrollStatus: Inactive
    payrollCalculationStatus: Not locked
    period: Kỳ lương tháng 7/2024
    subPeriod: Kỳ lương tháng 7/2024 - Đợt 2
  - employeeID: '0389328'
    employeeRecordNumber: '4'
    jobIndicator: Secondary
    fullName: Nguyễn Thị D
    gender: Nữ
    legalEntity: FPT IS HCM
    department: PeopleX
    position: Trưởng nhóm
    payrollStatus: Active
    payrollCalculationStatus: Locked
    period: Kỳ lương tháng 7/2024
    subPeriod: Kỳ lương tháng 7/2024 - Đợt 2
  - employeeID: '0837283'
    employeeRecordNumber: '5'
    jobIndicator: Secondary
    fullName: Nguyễn Thị E
    gender: Nữ
    legalEntity: FPT IS HCM
    department: PeopleX
    position: Trưởng nhóm
    payrollStatus: Active
    payrollCalculationStatus: Locked
    period: Kỳ lương tháng 7/2024
    subPeriod: Kỳ lương tháng 7/2024 - Đợt 2
  - employeeID: '0389328'
    employeeRecordNumber: '1'
    jobIndicator: Primary
    fullName: Trần Văn A
    gender: Nam
    legalEntity: FPT IS HCM
    department: PeopleX
    position: Trưởng nhóm
    payrollStatus: Active
    payrollCalculationStatus: Not locked
    period: Kỳ lương tháng 7/2024
    subPeriod: Kỳ lương tháng 7/2024 - Đợt 1
  - employeeID: '0837283'
    employeeRecordNumber: '2'
    jobIndicator: Primary
    fullName: Nguyễn Thị C
    gender: Nữ
    legalEntity: FPT IS HCM
    department: PeopleX
    position: Trưởng nhóm
    payrollStatus: Inactive
    payrollCalculationStatus: Not locked
    period: Kỳ lương tháng 7/2024
    subPeriod: Kỳ lương tháng 7/2024 - Đợt 2
  - employeeID: '0389328'
    employeeRecordNumber: '3'
    jobIndicator: Primary
    fullName: Nguyễn Thị B
    gender: Nữ
    legalEntity: FPT IS HCM
    department: PeopleX
    position: Trưởng nhóm
    payrollStatus: Inactive
    payrollCalculationStatus: Not locked
    period: Kỳ lương tháng 7/2024
    subPeriod: Kỳ lương tháng 7/2024 - Đợt 2
  - employeeID: '0389328'
    employeeRecordNumber: '4'
    jobIndicator: Secondary
    fullName: Nguyễn Thị D
    gender: Nữ
    legalEntity: FPT IS HCM
    department: PeopleX
    position: Trưởng nhóm
    payrollStatus: Active
    payrollCalculationStatus: Locked
    period: Kỳ lương tháng 7/2024
    subPeriod: Kỳ lương tháng 7/2024 - Đợt 2
  - employeeID: '0837283'
    employeeRecordNumber: '5'
    jobIndicator: Secondary
    fullName: Nguyễn Thị E
    gender: Nữ
    legalEntity: FPT IS HCM
    department: PeopleX
    position: Trưởng nhóm
    payrollStatus: Active
    payrollCalculationStatus: Locked
    period: Kỳ lương tháng 7/2024
    subPeriod: Kỳ lương tháng 7/2024 - Đợt 2
local_buttons: null
layout: layout-split-table
form_config:
  fields:
    - type: group
      n_cols: 2
      space: 12
      fields:
        - name: payrollPeriodSettingCode
          type: select
          labelType: flex-row
          outputValue: value
          placeholder: Select Payroll Sub Period
          isLazyLoad: true
          _select:
            transform: >-
              $payrollSubPeriodList($.extend.limit, $.extend.page,
              $.extend.search)
          dependantField: $.fields.payrollPeriodCode
          validators:
            - type: required
  filterMapping:
    - field: payrollPeriodCode
      operator: $eq
      valueField: payrollPeriodCode.(value)
    - field: payrollPeriodSettingCode
      operator: $eq
      valueField: payrollPeriodSettingCode.(code)
  sources:
    payrollPeriodList:
      uri: '"/api/payroll-periods"'
      method: GET
      queryTransform: '{''limit'' : 1000}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
    payrollSubPeriodList:
      uri: '"/api/payroll-period-settings"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'payrollPeriodCode','operator': '$eq','value':
        $.payrollPeriodCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'code' : $item.code,'payrollPeriodCode':
        $item.payrollPeriodCode}, 'payrollPeriodCode':
        $item.payrollPeriodCode}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - payrollPeriodCode
  variables:
    _payrollPeriodList:
      transform: $payrollPeriodList()
filter_config:
  fields:
    - type: selectAll
      name: legalEntityCode
      labelType: type-grid
      label: Legal Entity
      isLazyLoad: true
      _options:
        transform: $legalEntityList($.extend.limit, $.extend.page, $.extend.search)
      placeholder: Select Legal Entity
    - type: selectAll
      mode: multiple
      label: Department
      isLazyLoad: true
      labelType: type-grid
      name: department
      dependantField: $.fields.businessUnitCode
      _options:
        transform: >-
          $departmentsList($.extend.limit, $.extend.page, $.extend.search,
          $map($.fields.legalEntityCode , function($v) {$v.value}))
    - type: selectAll
      labelType: type-grid
      label: Paygroup
      dependantField: $.fields.companyCode
      name: payGroup
      isLazyLoad: true
      mode: multiple
      _options:
        transform: >-
          $payGroupsList($.extend.limit, $.extend.page, $.extend.search ,
          $.fields.companyCode)
    - type: select
      label: Employee ID
      name: employeeID
      mode: multiple
      labelType: type-grid
      isLazyLoad: true
      _select:
        transform: $personalsList($.extend.limit, $.extend.page, $.extend.search)
      placeholder: Select Employee ID
    - type: number
      label: Employee Record Number
      labelType: type-grid
      name: employeeRecordNumber
      placeholder: Enter Employee Record Number
    - type: text
      label: Full name
      labelType: type-grid
      name: fullName
      placeholder: Enter Full name
    - type: radio
      label: Gender
      labelType: type-grid
      value: null
      name: gender
      _radio:
        transform: '[{''label'': ''All'' , ''value'': null},$gender()]'
    - type: selectAll
      label: Position
      labelType: type-grid
      placeholder: Select Position
      isLazyLoad: true
      name: position
      _options:
        transform: $positionsList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      label: Job Indicator
      name: jobIndicator
      labelType: type-grid
      placeholder: Select Job Indicator
      isLazyLoad: true
      _options:
        transform: $jobIndicatorList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      label: Payroll Status
      placeholder: Select Payroll Status
      labelType: type-grid
      isLazyLoad: true
      name: payrollStatus
      _options:
        transform: $payrollStatusList($.extend.limit, $.extend.page, $.extend.search)
  filterMapping:
    - field: companyCode
      operator: $in
      valueField: company.(value)
    - field: legalEntityCode
      operator: $in
      valueField: legalEntityCode.(value)
    - field: departmentCode
      operator: $in
      valueField: department.(value)
    - field: payGroupCode
      operator: $in
      valueField: payGroup.(value)
    - field: employeeId
      operator: $in
      valueField: employeeID.(value)
    - field: employeeRecordNumber
      operator: $eq
      valueField: employeeRecordNumber
    - field: fullName
      operator: $cont
      valueField: fullName
    - field: genderCode
      operator: $eq
      valueField: gender
    - field: localExpat
      operator: $eq
      valueField: localExpat.(value)
    - field: job
      operator: $eq
      valueField: job.(value)
    - field: positionCode
      operator: $eq
      valueField: position.(value)
    - field: jobIndicatorCode
      operator: $eq
      valueField: jobIndicator.(value)
    - field: contractType
      operator: $eq
      valueField: contractType.(value)
    - field: hrStatus
      operator: $eq
      valueField: hrStatus.(value)
    - field: pRStatusCode
      operator: $in
      valueField: payrollStatus.(value)
  sources:
    companyList:
      uri: '"/api/companies"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search, ''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    legalEntityList:
      uri: '"/api/legal-entities"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search, ''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    departmentsList:
      uri: '"/api/departments"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'pageSize': $.pageSize,'search':
        $.search, 'filter': [{'field':'legalEntityCode', 'operator':
        '$in','value':$.legalEntityCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - legalEntityCode
    payGroupsList:
      uri: '"/api/pay-group-structures"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'companyCode','operator': '$in','value':
        $.companyCode},{'field':'companyCode','operator':
        '$eq','value':$.companyCode? 'NULL':''}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - companyCode
    elementGroup:
      uri: '"/api/picklists/ELEMENTGROUP/values"'
      method: GET
      queryTransform: '{''limit'':5000}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})
      disabledCache: true
    elementType:
      uri: '"/api/picklists/ELEMENTTYPE/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
    personalsList:
      uri: '"/api/personals"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.employeeId, 'value':
        $item.employeeId}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    gender:
      uri: '"/api/picklists/GENDER/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default , 'value':
        $item.code}})[]
      disabledCache: true
    localExpat:
      uri: '"/api/picklists/LOCALFOREIGNEMPLOYEE/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default , 'value':
        $item.code}})[]
      disabledCache: true
    jobCodesList:
      uri: '"/api/job-codes"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search, ''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    positionsList:
      uri: '"/api/positions"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search, ''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    jobIndicatorList:
      uri: '"/api/picklists/JOBINDICATOR/values/pagination"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    hrStatusList:
      uri: '"/api/picklists/HRSTATUS/values/pagination"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    payrollStatusList:
      uri: '"/api/picklists/PAYROLLSTATUS/values/pagination"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    contractTypeList:
      uri: '"/api/picklists/CONTRACTTYPE/values/pagination"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
layout_options:
  tool_table: []
  show_table_filter: false
  transfer_table:
    - name: List Of Non-Salaried Employees
      display_filter: true
      group_by: $.isSalaried = false
      show_filter: true
      transfer_fields:
        isSalaried: false
      default_filter_query:
        - field: isSalaried
          operator: $eq
          value: false
        - field: pRStatusCode
          operator: $ne
          value: T
          condition: >-
            $not($.searchValue) and $count($.filterQuery[field =
            'pRStatusCode'][]) = 0
      tool_table:
        - id: import
          title: Import
          icon: icon-download-simple-bold
          href: /GE/HR.FS.FR.092
          paramsRedirect:
            type: PR_OBJECT
            entityOrObj: PayrollEmployee
        - id: export
          title: Export
          icon: icon-upload-simple-bold
    - name: List Of Salaried Employee
      display_filter: true
      group_by: $.isSalaried = true
      show_filter: true
      transfer_fields:
        isSalaried: true
      default_filter_query:
        - field: isSalaried
          operator: $eq
          value: true
      tool_table:
        - id: import
          title: Import
          icon: icon-download-simple-bold
          href: /GE/HR.FS.FR.092
          paramsRedirect:
            type: PR_OBJECT
            entityOrObj: PayrollEmployee
        - id: export
          title: Export
          icon: icon-upload-simple-bold
  expand_filter: true
layout_options__header_buttons: null
options: null
create_form:
  fields:
    - type: group
      n_cols: 2
      space: 12
      fields:
        - type: select
          name: payrollPeriodCode
          labelType: flex-row
          placeholder: Payroll Period
          clearFieldsAfterChange:
            - payrollPeriodSettingCode
          isLazyLoad: true
          _select:
            transform: $payrollPeriodList($.extend.limit,$.extend.page,$.extend.search)
          _value:
            transform: >-
              $not($isNilorEmpty($.fields.payrollPeriodSettingCode)) ? {'label'
              : $.fields.payrollPeriodSettingCode.periodName & ' (' &
              $.fields.payrollPeriodSettingCode.periodName &')' , 'value' :
              $.fields.payrollPeriodSettingCode.periodCode} : null
          validators:
            - type: required
        - name: payrollPeriodSettingCode
          type: select
          labelType: flex-row
          placeholder: Select Payroll Sub Period
          isLazyLoad: true
          _select:
            transform: >-
              $payrollSubPeriodList($.extend.limit,$.extend.page,$.extend.search,$.fields.payrollPeriodCode.value)
          validators:
            - type: required
  filterMapping:
    - field: payrollPeriodSettingCode
      operator: $eq
      valueField: payrollPeriodSettingCode.(value)
  sources:
    payrollPeriodList:
      uri: '"/api/payroll-periods"'
      method: GET
      queryTransform: '{''limit'': $.limit, ''page'': $.page, ''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    payrollSubPeriodList:
      uri: '"/api/payroll-period-settings/authorization-get"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'search': $.search,'filter':
        [{'field':'payrollPeriodCode','operator': '$eq','value':
        $.payrollPeriodCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code , 'periodName':
        $item.payrollPeriodName , 'periodCode' : $item.payrollPeriodCode}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - payrollPeriodCode
  variables: {}
layout_options__footer_buttons: null
layout_options__row_actions: null
backend_url: /api/payroll-employees
screen_name: null
layout_options__actions_many:
  - id: export
    title: Export Selected
    icon: icon-upload-simple-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key: null
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: List of paid employees
  parent:
    title: PR Processes
