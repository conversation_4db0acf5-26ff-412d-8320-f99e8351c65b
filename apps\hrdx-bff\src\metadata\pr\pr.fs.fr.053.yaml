id: PR.FS.FR.053
status: draft
sort: 558
user_created: 60e9ad50-48e2-446b-9124-eef839c521ad
date_created: '2024-08-23T11:05:18.355Z'
user_updated: b403d5da-d684-45e4-ae6d-e25223baedc5
date_updated: '2025-07-17T08:21:35.025Z'
title: Manage External Payroll Import
requirement:
  time: 1747362528757
  blocks:
    - id: e1_DbqjuMj
      type: paragraph
      data:
        text: >-
          - Chứ<PERSON> năng cho phép bộ phận nhân sự tập đoàn/CTTV Thiết lập file
          import bảng lương.
    - id: Lf0ZV5zutP
      type: paragraph
      data:
        text: >-
          - Thiết lập đi theo Kỳ lương/Payroll period và Kỳ chi tiết/Payroll
          sub-period của Tập đoàn (hoặc công ty)
  version: 2.30.7
screen_design: null
module: PR
local_fields:
  - code: payrollPeriodName
    title: Payroll Period
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    href: null
    pinned: true
    show_sort: true
  - code: name
    title: Payroll Sub-period
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: countryName
    title: Country
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: companyName
    title: Company
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: legalEntityName
    title: Legal Entity
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: payGroupName
    title: Pay Group
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: elementGroupName
    title: Element Group
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: salaryTypeName
    title: Element Type
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: paymentDate
    title: Payment Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    show_sort: true
  - code: startDate
    title: Start Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    show_sort: true
  - code: endDate
    title: End Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    show_sort: true
  - code: totalCalculation
    title: Total Employee
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: totalFail
    title: Total Fail
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: totalNotCalculated
    title: Total Not Caculated
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: totalProcessing
    title: Total Processing
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: totalCompleted
    title: Total Completed
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: totalLocked
    title: Total Locked
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: currencyName
    title: Currency
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Currency
      collection: field_types
    show_sort: true
  - code: version
    title: Version
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: revision
    title: Revision
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: payrollStatus
    title: Period Status
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tag
      collection: field_types
    extra_config:
      tags:
        - value: NotCalculated
          label: Not Calculated
          class: infor
        - value: Processing
          label: Processing
          style:
            background_color: '#FEF9CC'
        - value: Finalized
          label: Finalized
          style:
            background_color: '#F3EAFB'
        - value: Locked
          label: Locked
          class: default
        - value: Failed
          label: Failed
          class: error
        - value: Completed
          label: Completed
          class: success
    show_sort: true
    options__tabular__column_width: null
    options__tabular__align: left
  - code: updatedBy
    title: Last Updated By
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
    resizable: true
    options__tabular__column_width: 12
    dragable: true
  - code: updatedAt
    title: Last Updated On
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
    show_sort: true
    options__tabular__column_width: 12
mock_data:
  - payrollPeriod: 2024-08
    payrollSubPeriod: First Half
    company: ABC Corp
    legalEntity: ABC Corp USA
    payGroup: Monthly Employees
    elementGroup: Salaries
    elementType: Base Pay
    periodStatus: Closed
    version: '1.0'
    revision: '0'
    grossSalary: $8,500.00
    netSalary: $6,800.00
    currency: USD
    updater: John Doe
    updatedTime: '2024-08-15 14:30:00'
  - payrollPeriod: 2024-07
    payrollSubPeriod: Second Half
    company: XYZ Ltd
    legalEntity: XYZ Ltd UK
    payGroup: Bi-weekly Employees
    elementGroup: Bonuses
    elementType: Performance Bonus
    periodStatus: Open
    version: '1.2'
    revision: '1'
    grossSalary: $3,200.00
    netSalary: $2,500.00
    currency: GBP
    updater: Jane Smith
    updatedTime: '2024-07-29 10:15:00'
  - payrollPeriod: 2024-06
    payrollSubPeriod: Full Month
    company: Global Tech
    legalEntity: Global Tech India
    payGroup: Contract Employees
    elementGroup: Allowances
    elementType: Housing Allowance
    periodStatus: Closed
    version: '2.0'
    revision: '0'
    grossSalary: ₹50,000.00
    netSalary: ₹40,000.00
    currency: INR
    updater: Amit Patel
    updatedTime: '2024-06-30 16:45:00'
local_buttons: null
layout: layout-table
form_config:
  formSize:
    create: largex
    edit: largex
    view: largex
    proceed: largex
    history: largex
  fields:
    - type: group
      label: ''
      collapse: false
      disableEventCollapse: false
      fields:
        - type: text
          name: code
          unvisible: true
          readOnly: true
        - type: text
          name: payrollPeriodCode
          unvisible: true
          readOnly: true
        - type: text
          name: reportColumnCode
          unvisible: true
          readOnly: true
        - type: text
          name: reportColumnCalCode
          unvisible: true
          readOnly: true
        - type: text
          name: reportTypeId
          unvisible: true
          readOnly: true
        - type: text
          name: id
          unvisible: true
          readOnly: true
        - type: text
          name: reportTypeReportSourceId
          unvisible: true
          readOnly: true
        - type: text
          name: reportSourceId
          unvisible: true
          _value:
            transform: $.fields.reportTypeReportSourceId
          readOnly: true
        - type: text
          name: reportTypeName
          unvisible: true
          readOnly: true
        - type: text
          name: name
          unvisible: true
          _value:
            transform: $.fields.reportTypeName
          readOnly: true
        - type: text
          name: reportTypeCode
          unvisible: true
          readOnly: true
        - type: text
          name: codePatch
          unvisible: true
          _value:
            transform: $.fields.reportTypeCode
          readOnly: true
        - type: text
          name: reportTypeElementGroupCode
          unvisible: true
          readOnly: true
        - type: text
          name: elementGroupCode
          unvisible: true
          _value:
            transform: $.fields.reportTypeElementGroupCode
          readOnly: true
        - type: text
          name: reportTypeCountryCode
          unvisible: true
          readOnly: true
        - type: text
          name: countryCode
          unvisible: true
          _value:
            transform: $.fields.reportTypeCountryCode
          readOnly: true
        - type: text
          name: reportTypeShortName
          unvisible: true
          readOnly: true
        - type: text
          name: shortName
          unvisible: true
          _value:
            transform: $.fields.reportTypeShortName
          readOnly: true
        - type: text
          name: reportTypeElementTypeCode
          unvisible: true
          readOnly: true
        - type: text
          name: elementTypeCode
          unvisible: true
          _value:
            transform: $.fields.reportTypeElementTypeCode
          readOnly: true
        - type: text
          name: reportTypeEffectiveDateFrom
          unvisible: true
          readOnly: true
        - type: text
          name: effectiveDateFrom
          unvisible: true
          _value:
            transform: $.fields.reportTypeEffectiveDateFrom
          readOnly: true
        - type: text
          name: reportTypeEffectiveDateTo
          unvisible: true
          readOnly: true
        - type: text
          name: effectiveDateTo
          unvisible: true
          _value:
            transform: $.fields.reportTypeEffectiveDateTo
          readOnly: true
        - type: text
          name: reportTypeNote
          unvisible: true
          readOnly: true
        - type: text
          name: note
          unvisible: true
          _value:
            transform: $.fields.reportTypeNote
          readOnly: true
        - type: text
          name: reportTypeEnable
          unvisible: true
          readOnly: true
        - type: text
          name: enabled
          unvisible: true
          _value:
            transform: $.fields.reportTypeEnable
          readOnly: true
        - type: text
          name: reportTypeCountryName
          label: Country
          unvisible: true
          readOnly: true
        - type: text
          name: reportTypeCode
          label: Formula Code
          unvisible: true
          readOnly: true
        - type: text
          name: reportTypeShortName
          label: Short Name
          unvisible: true
          readOnly: true
        - type: text
          name: reportTypeName
          label: Long Name
          unvisible: true
          readOnly: true
        - type: text
          name: reportTypeElementGroupName
          label: Element Group
          unvisible: true
          readOnly: true
        - type: text
          name: reportTypeElementTypeName
          label: Element Type
          unvisible: true
          readOnly: true
        - type: text
          name: reportTypeElementGroupCode
          label: Element Group
          unvisible: true
          readOnly: true
        - type: text
          name: reportTypeElementTypeCode
          label: Element Type
          unvisible: true
          readOnly: true
        - type: dateRange
          name: reportTypeEffectiveDateFrom
          label: Effective Date
          unvisible: true
          readOnly: true
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
        - name: reportTypeStatus
          label: Status
          unvisible: true
          type: radio
          radio:
            - label: Active
              value: true
            - label: Inactive
              value: false
        - type: text
          name: reportTypeNote
          label: Note
          unvisible: true
          readOnly: true
        - type: table
          mode: table
          distinctByKey: codeMapping
          mapRowName:
            - codeMapping
          name: reportColumns
          _defaultFilterValue:
            transform: '{''ReportTypeId'': $.fields.reportTypeId}'
          addSetup:
            columns:
              - code: reportColumnCalCode
                title: Element Code
                type: text
                align: start
                width: 150px
              - code: codeMapping
                title: Short Name
                type: text
                align: start
                width: 150px
              - code: name
                title: Long Name
                type: text
                align: start
                width: 150px
              - code: operatorName
                title: Operation
                type: text
                align: start
                width: 150px
            filter:
              - type: group
                label: ''
                n_cols: 3
                fields:
                  - type: text
                    label: Element Caculation Code
                    placeholder: Enter Element Caculation Code
                    name: code
                  - type: text
                    label: Short Name
                    placeholder: Enter Short Name
                    name: shortName
                  - type: text
                    label: Long Name
                    placeholder: Enter Long Name
                    name: name
            filterMapping:
              - field: Code
                operator: $cont
                valueField: code
              - field: ShortName
                operator: $cont
                valueField: shortName
              - field: Name
                operator: $cont
                valueField: name
              - field: ReportTypeId
                operator: $eq
                valueField: ReportTypeId
            defaultData:
              id: '0'
          layout_option:
            show_pagination: false
            show_row_index: true
            show_add_new_item: true
            add_new_button_title: Add Element
            tool_table:
              show_table_checkbox: false
            action_row:
              - id: delete
                type: ghost-gray
                icon: trash
          dependantField: ' $.fields.RequestTypeCode'
          columns:
            - code: reportColumnCalCode
              title: Element Calculation Code
              align: start
            - code: codeMapping
              title: Short Name
              align: start
            - code: name
              title: Long Name
              align: start
            - code: note
              title: Note
              align: start
            - code: requiredSetting
              title: Required
              type: Control Checkbox
              align: center
              width: 7
              display_type:
                _disabled:
                  dependants:
                    - $.fields.reportColumns[$index]
                  params:
                    $index: $.extend.index
                  transform: >-
                    ($idx := $.extend.index;
                    $.extend.defaultValue.reportColumns[$idx].requiredSetting =
                    'R') 
                key: Control Checkbox
              props:
                option:
                  displayLable: false
                mappingValue:
                  - input: true
                    output: 'Y'
                  - input: false
                    output: 'N'
                  - input: true
                    output: R
            - code: nameInTemplate
              title: Name In Template
              placeholder: Enter Note
              props:
                placeholder: Enter Name In Template
              align: start
              data_type:
                key: String
                collection: data_types
              display_type:
                key: Control Text
                collection: field_types
          form_config:
            fields:
              - type: text
                name: firstName
                label: First Name
              - type: text
                name: lastName
                label: Last Name
              - type: select
                name: companySlect
                isLazyLoad: true
                label: Company
                _select:
                  transform: >-
                    $companiesList($.extend.limit, $.extend.page,
                    $.extend.search)
              - type: text
                name: companyName
                label: Data Zone
                _value:
                  transform: $.fields.companySlect.label
                unvisible: true
              - type: text
                name: companyCode
                unvisible: true
                _value:
                  transform: $.fields.companySlect.value
              - type: text
                name: mailGroup
                label: Mail Group
            sources:
              companiesList:
                uri: '"/api/companies"'
                queryTransform: >-
                  {'limit': $.limit, 'page': $.page, 'filter':
                  [{'field':'search','operator':
                  '$eq','value':$.search},{'field':'status','operator':
                  '$eq','value':true}]}
                method: GET
                bodyTransform: ''
                headerTransform: ''
                resultTransform: >-
                  $map($.data, function($item) {{'label': $item.longName.default
                  & ' - ' & $item.code, 'value': $item.code, 'id': $item.id
                  }})[]
                disabledCache: true
                params:
                  - limit
                  - page
                  - search
            variables: {}
          sources: /api/manage-external-payroll-imports/search-element-calculations
filter_config:
  fields:
    - name: countryCode
      label: Country
      type: selectAll
      mode: multiple
      labelType: type-grid
      placeholder: Select Country
      isLazyLoad: true
      _options:
        transform: $nationsList($.extend.limit, $.extend.page, $.extend.search)
    - name: company
      label: Company
      isLazyLoad: true
      labelType: type-grid
      type: selectAll
      placeholder: Select Company
      _options:
        transform: $companiesList($.extend.limit, $.extend.page, $.extend.search)
    - name: payrollPeriod
      label: Payroll Period
      isLazyLoad: true
      labelType: type-grid
      type: selectAll
      placeholder: Select Payroll Period
      _options:
        transform: $payrollPeriodList($.extend.limit, $.extend.page, $.extend.search)
    - name: payrollSubPeriod
      label: Payroll Sub Period
      labelType: type-grid
      isLazyLoad: true
      type: selectAll
      placeholder: Select Payroll Sub Period
      _options:
        transform: >-
          $payrollSubPeriodList($.extend.limit, $.extend.page, $.extend.search,
          $.fields.payrollPeriod.value)
    - name: legalEntity
      label: Legal Entity
      labelType: type-grid
      isLazyLoad: true
      type: selectAll
      placeholder: Select Legal Entity
      _options:
        transform: $legalEntityList($.extend.limit, $.extend.page, $.extend.search)
    - name: payGroup
      label: Pay Group
      isLazyLoad: true
      labelType: type-grid
      type: selectAll
      placeholder: Select Pay Group
      _options:
        transform: $payGroupList($.extend.limit, $.extend.page, $.extend.search)
    - name: elementGroup
      label: Element Group
      labelType: type-grid
      type: selectAll
      placeholder: Select Element Group
      _options:
        transform: $elementGroupsList()
    - name: elementType
      labelType: type-grid
      label: Element Type
      type: selectAll
      placeholder: Select Element Type
      _options:
        transform: $elementTypesList()
    - name: prStatus
      label: Period Status
      labelType: type-grid
      mode: multiple
      type: select
      placeholder: Select Period Status
      select:
        - label: Not calculated
          value: NotCalculated
        - label: Completed
          value: Completed
        - label: Processing
          value: Processing
        - label: Finalized
          value: Finalized
    - name: paymentDate
      label: Payment Date
      type: dateRange
      labelType: type-grid
      setting:
        mode: date
        format: dd/MM/yyyy
    - name: startDate
      label: Start Date
      type: dateRange
      labelType: type-grid
      setting:
        mode: date
        format: dd/MM/yyyy
    - name: endDate
      label: End Date
      type: dateRange
      labelType: type-grid
      setting:
        mode: date
        format: dd/MM/yyyy
    - name: updatedBy
      label: Last Updated By
      type: text
      labelType: type-grid
      placeholder: Enter Last Updated By
    - name: updatedAt
      label: Last Updated On
      type: dateRange
      labelType: type-grid
      setting:
        mode: date
        format: dd/MM/yyyy
  filterMapping:
    - field: countryCode
      operator: $in
      valueField: countryCode.(value)
    - field: companyCode
      operator: $in
      valueField: company.(value)
    - field: payrollPeriodCode
      operator: $in
      valueField: payrollPeriod.(value)
    - field: code
      operator: $in
      valueField: payrollSubPeriod.(value)
    - field: legalEntityCode
      operator: $in
      valueField: legalEntity.(value)
    - field: payGroupCode
      operator: $in
      valueField: payGroup.(value)
    - field: elementGroupCode
      operator: $in
      valueField: elementGroup.(value)
    - field: salaryTypeCode
      operator: $in
      valueField: elementType.(value)
    - field: payrollStatus
      operator: $in
      valueField: prStatus.(value)
    - field: paymentDate
      operator: $between
      valueField: paymentDate
    - field: startDate
      operator: $between
      valueField: startDate
    - field: endDate
      operator: $between
      valueField: endDate
    - field: updatedBy
      operator: $cont
      valueField: updatedBy
    - field: updatedAt
      operator: $between
      valueField: updatedAt
  sources:
    payrollPeriodList:
      uri: '"/api/payroll-periods"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    payrollSubPeriodList:
      uri: '"/api/payroll-period-settings/authorization-get"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter' :
        [{'field':'payrollPeriodCode','operator': '$eq','value':
        $.payrollPeriodCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - payrollPeriodCode
    companiesList:
      uri: '"/api/companies"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search, ''filter'':[]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    nationsList:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    employeeGroupList:
      uri: '"/api/employee-groups"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.employeeGroup.default,
        'value': $item.code}})[]
      disabledCache: true
    personalsList:
      uri: '"/api/personals"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name, 'value':
        $item.id}})[]
      disabledCache: true
    legalEntityList:
      uri: '"/api/legal-entities"'
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search, ''filter'': []}'
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    elementGroupsList:
      uri: '"/api/picklists/ELEMENTGROUP/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})
      disabledCache: true
    elementTypesList:
      uri: '"/api/picklists/ELEMENTTYPE/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})
      disabledCache: true
    payGroupList:
      uri: '"/api/pay-group-structures"'
      queryTransform: '{''filter'': [], ''limit'': $.limit,''page'': $.page,''search'': $.search}'
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id,'companyCode' :
        $item.companyCode }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
layout_options:
  show_detail_history: false
  action_click_row:
    actionId: routing
    params: code
  custom_update_api:
    _url:
      transform: '''/api/manage-external-payroll-imports/report-import-column'''
    method: PATCH
  custom_export_api:
    _url:
      transform: '''/api/manage-external-payroll-imports'''
  show_create_data_table: false
  show_actions_delete: false
  modal_footer_buttons:
    - id: cancel
      title: Cancel
    - id: saveAndDownload
      title: Save & Download
      type: secondary
    - id: save
      title: Save
      type: primary
  toolTable:
    export: true
    adjustDisplay: true
  tool_table:
    - id: import
      paramsRedirect:
        type: PR_OBJECT
        entityOrObj: ManageExternalPayrollImports
    - id: export
      icon: icon-download-simple
  show_filter_results_message: true
  dialog_actions:
    - id: download
      type: api
      config:
        _url:
          transform: >-
            'api/manage-external-payroll-imports/template/' &
            $.payrollPeriodCode & '/' & $.code & '/export'
        method: GET
        isGetFile: true
  store_selected_items: true
  hide_action_row: true
layout_options__header_buttons: null
options: null
create_form: null
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    title: Edit
    icon: edit
    type: tertiary
backend_url: /api/manage-external-payroll-imports
screen_name: null
layout_options__actions_many:
  - id: export
    title: Export
    icon: file-arrow-down
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: code
default_filter: null
pre_condition_config: {}
permission_key:
  - name: countryCode
    defaultName: CountryCode
  - name: companyCode
    defaultName: CompanyCode
  - name: legalEntityCode
    defaultName: LegalEntityCode
  - name: payGroupCode
    defaultName: PayGroupCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Manage external payroll import
  parent: null
