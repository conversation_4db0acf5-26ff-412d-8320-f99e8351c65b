id: PR.FS.FR.053_detail
status: draft
sort: null
user_created: b403d5da-d684-45e4-ae6d-e25223baedc5
date_created: '2025-02-06T06:47:13.908Z'
user_updated: abd14398-1d53-4e6f-9834-26328f0ebde0
date_updated: '2025-07-02T07:36:27.715Z'
title: Manage external payroll import detail
requirement:
  time: 1747207414539
  blocks:
    - id: kpTB0vy_w3
      type: paragraph
      data:
        text: Manage external payroll import
  version: 2.30.7
screen_design: null
module: PR
local_fields: []
mock_data: null
local_buttons: null
layout: layout-tabs
form_config: {}
filter_config: {}
layout_options:
  show_detail_history: false
  parent_path: /PR/PR.FS.FR.053
  page_header_options:
    visible: true
    back_btn: true
    border: true
  tabs_title:
    - Summary
    - Set Up Import Information
  custom_title:
    transform: '''Manage External Payroll Import Detail'''
layout_options__header_buttons: null
options: null
create_form: null
layout_options__footer_buttons: null
layout_options__row_actions: null
backend_url: /api/manage-external-payroll-imports/:id1
screen_name: null
layout_options__actions_many: null
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: code
default_filter: null
pre_condition_config: {}
permission_key: null
screen_designs: []
function_specs: []
fields: []
children:
  - PR.FS.FR.053_summary
  - PR.FS.FR.053_set_up
menu_item: null
