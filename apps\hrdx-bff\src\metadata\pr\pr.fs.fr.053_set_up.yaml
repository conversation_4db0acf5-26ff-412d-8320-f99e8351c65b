id: PR.FS.FR.053_set_up
status: draft
sort: 5
user_created: b403d5da-d684-45e4-ae6d-e25223baedc5
date_created: '2025-02-12T04:49:00.760Z'
user_updated: b403d5da-d684-45e4-ae6d-e25223baedc5
date_updated: '2025-07-14T06:14:53.786Z'
title: set up payroll Import
requirement:
  time: 1747622277896
  blocks:
    - id: 5G1DfADUg-
      type: paragraph
      data:
        text: set up
  version: 2.30.7
screen_design: null
module: PR
local_fields: []
mock_data: null
local_buttons: null
layout: layout-form
form_config:
  footer:
    update: true
    updatedOn: updatedAt
    updatedBy: updatedBy
  fields:
    - type: group
      label: Formula Information
      disableEventCollapse: false
      collapse: false
      fieldGroupTitleStyle:
        border: none
        paddingTop: 0px
      fields:
        - type: text
          name: id
          unvisible: true
          readOnly: true
        - type: text
          name: code
          unvisible: true
          readOnly: true
        - type: text
          name: payrollPeriodCode
          unvisible: true
          readOnly: true
        - type: text
          name: reportTypeId
          unvisible: true
          readOnly: true
        - type: text
          name: reportTypeReportSourceId
          unvisible: true
          readOnly: true
        - type: text
          name: reportTypeName
          unvisible: true
          readOnly: true
        - type: text
          name: reportTypeCode
          unvisible: true
          readOnly: true
        - type: text
          name: reportTypeElementGroupCode
          unvisible: true
          readOnly: true
        - type: text
          name: reportTypeCountryCode
          unvisible: true
          readOnly: true
        - type: text
          name: reportTypeShortName
          unvisible: true
          readOnly: true
        - type: text
          name: reportTypeElementTypeCode
          unvisible: true
          readOnly: true
        - type: text
          name: reportTypeEffectiveDateFrom
          unvisible: true
          readOnly: true
        - type: text
          name: reportTypeEffectiveDateTo
          unvisible: true
          readOnly: true
        - type: text
          name: reportTypeNote
          unvisible: true
          readOnly: true
        - type: text
          name: reportTypeEnable
          unvisible: true
          readOnly: true
        - type: text
          name: reportTypeCountryName
          label: Country
          readOnly: true
        - type: text
          name: reportTypeCode
          label: Formula Code
          readOnly: true
        - type: text
          name: reportTypeShortName
          label: Short Name
          readOnly: true
        - type: text
          name: reportTypeLongName
          label: Long Name
          readOnly: true
        - type: text
          name: reportTypeElementGroupName
          label: Element Group
          readOnly: true
        - type: text
          name: reportTypeElementTypeName
          label: Element Type
          readOnly: true
        - type: text
          name: reportTypeElementGroupCode
          label: Element Group
          unvisible: true
          readOnly: true
        - type: text
          name: reportTypeElementTypeCode
          label: Element Type
          unvisible: true
          readOnly: true
        - type: dateRange
          name: reportTypeEffectiveDateFrom
          label: Effective Date
          readOnly: true
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
        - name: reportTypeStatus
          label: Status
          type: radio
          radio:
            - label: Active
              value: true
            - label: Inactive
              value: false
        - type: text
          name: reportTypeNote
          label: Note
          readOnly: true
    - type: group
      label: Set up Import Data
      collapse: false
      disableEventCollapse: false
      fields:
        - type: table
          mode: table
          name: reportColumns
          distinctByKey: codeMapping
          mapRowName:
            - codeMapping
          readOnly: true
          checkPermissionEnabled: true
          config:
            addSetup: false
            filter: true
            edit: true
            downloadTemplate: true
          _defaultFilterValue:
            transform: '{''ReportTypeId'': $.fields.reportTypeId}'
          downloadTemplateConfig:
            api:
              authConfig:
                actionCode: ExportTemplate
              url: >-
                api/manage-external-payroll-imports/template/{{values.fields.payrollPeriodCode}}/{{values.fields.code}}/export
          editTableConfig:
            modal:
              title: Edit Set up Import Data
              size: largex
            table:
              addNewBtnTitle: Add Element
            actions:
              - id: preEdit
                source:
                  uri: >-
                    "/api/manage-external-payroll-imports/" &
                    $.values.fields.code
                  queryTransform: ''
                  method: GET
                  headerTransform: ''
                  resultTransform: $.reportColumns
                  authAction: Update
                  disabledCache: true
              - id: save
                source:
                  uri: '"/api/manage-external-payroll-imports/report-import-column"'
                  queryTransform: ''
                  method: PATCH
                  bodyTransform: >-
                    {'code': $.values.fields.code, 'id': $.values.fields.id,
                    'reportColumns': $.data }
                  headerTransform: ''
                  resultTransform: $
                  disabledCache: true
                  authAction: Update
              - id: download
                api:
                  authConfig:
                    actionCode: ExportTemplate
                  url: >-
                    api/manage-external-payroll-imports/template/{{values.fields.payrollPeriodCode}}/{{values.fields.code}}/export
            reloadValueAfterSubmit: true
          addSetup:
            columns:
              - code: reportColumnCalCode
                title: Element Code
                type: text
                align: start
                width: 150px
              - code: codeMapping
                title: Short Name
                type: text
                align: start
                width: 150px
              - code: name
                title: Long Name
                type: text
                align: start
                width: 150px
              - code: operatorName
                title: Operation
                type: text
                align: start
                width: 150px
            filter:
              - type: group
                label: ''
                n_cols: 3
                fields:
                  - type: text
                    label: Element Caculation Code
                    placeholder: Enter Element Caculation Code
                    name: Code
                  - type: text
                    label: Short Name
                    placeholder: Enter Short Name
                    name: ShortName
                  - type: text
                    label: Long Name
                    placeholder: Enter Long Name
                    name: Name
                  - type: text
                    unvisible: true
                    name: omitCodes
                    _value:
                      transform: >-
                        $map($.extend.dataTable, function($v)
                        {$v.reportColumnCalCode})[]
            filterMapping:
              - field: Code
                operator: $cont
                valueField: Code
              - field: ShortName
                operator: $cont
                valueField: ShortName
              - field: Name
                operator: $cont
                valueField: Name
              - field: ReportTypeId
                operator: $eq
                valueField: ReportTypeId
              - field: Code
                operator: $notin
                valueField: omitCodes
            defaultData:
              id: '0'
          layout_option:
            show_pagination: false
            show_row_index: true
            tool_table:
              show_table_checkbox: false
            action_row:
              - id: delete
                type: ghost-gray
                icon: trash
                _disabled:
                  transform: $.requiredSetting = 'R'
          dependantField: ' $.fields.RequestTypeCode'
          columns:
            - code: reportColumnCalCode
              title: Element Calculation Code
              align: start
            - code: codeMapping
              title: Short Name
              align: start
            - code: name
              title: Long Name
              align: start
            - code: note
              title: Note
              align: start
            - code: requiredSetting
              title: Required
              type: Control Checkbox
              align: center
              width: 7
              display_type:
                key: Control Checkbox
              props:
                option:
                  displayLable: false
                mappingValue:
                  - input: true
                    output: 'Y'
                  - input: false
                    output: 'N'
                  - input: true
                    output: R
                disabledByInputValue:
                  value: R
                  operator: $eq
            - code: nameInTemplate
              title: Name In Template
              props:
                placeholder: Enter Name In Template
              align: start
              data_type:
                key: String
                collection: data_types
              display_type:
                key: Control Text
                collection: field_types
          form_config:
            fields:
              - type: text
                name: firstName
                label: First Name
              - type: text
                name: lastName
                label: Last Name
              - type: select
                name: companySlect
                isLazyLoad: true
                label: Company
                _select:
                  transform: >-
                    $companiesList($.extend.limit, $.extend.page,
                    $.extend.search)
              - type: text
                name: companyName
                label: Data Zone
                _value:
                  transform: $.fields.companySlect.label
                unvisible: true
              - type: text
                name: companyCode
                unvisible: true
                _value:
                  transform: $.fields.companySlect.value
              - type: text
                name: mailGroup
                label: Mail Group
            sources:
              companiesList:
                uri: '"/api/companies"'
                queryTransform: >-
                  {'limit': $.limit, 'page': $.page, 'filter':
                  [{'field':'search','operator':
                  '$eq','value':$.search},{'field':'status','operator':
                  '$eq','value':true}]}
                method: GET
                bodyTransform: ''
                headerTransform: ''
                resultTransform: >-
                  $map($.data, function($item) {{'label': $item.longName.default
                  & ' - ' & $item.code, 'value': $item.code, 'id': $item.id
                  }})[]
                disabledCache: true
                params:
                  - limit
                  - page
                  - search
            variables: {}
          sources: /api/manage-external-payroll-imports/search-element-calculations
filter_config:
  fields:
    - type: select
      label: Business Unit
      name: businessUnit
      mode: multiple
      placeholder: Select Bussiness Unit
      _select:
        transform: $businessUnitList()
    - name: division
      label: Division
      type: select
      placeholder: Select Division
      mode: multiple
      _select:
        transform: $divisionsList($DateFormat($now(),'YYYY-MM-DDTHH:mm:ss'))
    - name: department
      label: Department
      type: select
      placeholder: Select Department
      mode: multiple
      isLazyLoad: true
      _select:
        transform: >-
          $departmentsList($.extend.limit,
          $.extend.page,$DateFormat($now(),'YYYY-MM-DDTHH:mm:ss'),
          $.extend.search)
    - name: jobTitle
      label: Job Title
      type: select
      mode: multiple
      placeholder: Select Job Title
      _select:
        transform: $jobCodesList()
    - name: position
      label: Position
      type: select
      mode: multiple
      placeholder: Select Position
      _select:
        transform: $positionsList($DateFormat($now(),'YYYY-MM-DDTHH:mm:ss'))
    - name: location
      label: Location
      type: select
      placeholder: Select Location
      mode: multiple
      _select:
        transform: $locationsList($DateFormat($now(),'YYYY-MM-DDTHH:mm:ss'))
    - name: employeeGroup
      label: Employee Group
      type: select
      placeholder: Select Employee Group
      mode: multiple
      _select:
        transform: $employeeGroupsList()
    - name: contractType
      label: Contract Type
      type: select
      placeholder: Select Contract Type
      mode: multiple
      _select:
        transform: $contractTypeList()
    - name: employeeLevel
      label: Employee Level
      type: select
      placeholder: Select Employee Level
      mode: multiple
      _select:
        transform: $empLevelList()
  filterMapping:
    - field: businessUnitCode
      operator: $in
      valueField: businessUnit.(value)
    - field: divisionCode
      operator: $in
      valueField: division.(value)
    - field: departmentCode
      operator: $in
      valueField: department.(value)
    - field: payrollStatus
      operator: $in
      valueField: payrollStatus.(value)
    - field: jobTitleCode
      operator: $in
      valueField: jobTitle.(value)
    - field: positionCode
      operator: $in
      valueField: position.(value)
    - field: locationCode
      operator: $in
      valueField: location.(value)
    - field: employeeGroupCode
      operator: $in
      valueField: employeeGroup.(value)
    - field: contractTypeCode
      operator: $in
      valueField: contractType.(value)
    - field: employeeLevelCode
      operator: $in
      valueField: employeeLevel.(value)
  sources:
    empLevelList:
      uri: '"/api/picklists/EMPLEVEL/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})
      disabledCache: true
    contractTypeList:
      uri: '"/api/picklists/CONTRACTTYPE/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})
      disabledCache: true
    employeeGroupsList:
      uri: '"/api/employee-groups"'
      method: GET
      queryTransform: '{''filter'': [{''field'':''status'',''operator'': ''$eq'',''value'':true}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.employeeGroup.default,
        'value': $item.code}})
      disabledCache: true
    locationsList:
      uri: '"/api/locations/by"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.id,  'additionalData': $item}})[]
      disabledCache: true
      params:
        - effectiveDate
    positionsList:
      uri: '"/api/positions/by"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default & ' (' &
        $item.department.name & ')' , 'value': $item.id, 'jobCode':
        $item.id}})[]
      disabledCache: true
      params:
        - effectiveDate
    jobCodesList:
      uri: '"/api/job-codes/by"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default, 'value':
        $item.code}})[]
      disabledCache: true
    businessUnitList:
      uri: '"/api/business-units/by"'
      method: GET
      queryTransform: '{''filter'': [{''field'':''status'',''operator'': ''$eq'',''value'':true}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default, 'value':
        $item.code}})[]
      disabledCache: true
    divisionsList:
      uri: '"/api/divisions/by"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.id, 'additionalData': $item}})[]
      disabledCache: true
      params:
        - effectiveDate
    departmentsList:
      uri: '"/api/departments/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'filter':
        [{'field':'search','operator':
        '$cont','value':$.search},{'field':'effectiveDate','operator':
        '$lte','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.id, 'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - effectiveDate
        - search
layout_options:
  page_footer_options:
    visible: true
    show_credit: true
  page_header_options:
    visible: false
  customStyleContent:
    padding: 8px 0 0 0
    background: '#fff'
layout_options__header_buttons: null
options: null
create_form: null
layout_options__footer_buttons:
  - id: block
    title: Block
    type: primary
layout_options__row_actions:
  - id: note
    icon: icon-notebook-bold
    type: ghost-gray
    href: /api/payroll-employees/set-note-paymentdate
backend_url: /api/manage-external-payroll-imports/:id1
screen_name: null
layout_options__actions_many: null
parent: PR.FS.FR.053_detail
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: code
default_filter: null
pre_condition_config: {}
permission_key: null
screen_designs: []
function_specs: []
fields: []
children: []
menu_item: null
