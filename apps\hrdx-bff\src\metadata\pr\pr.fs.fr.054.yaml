id: PR.FS.FR.054
status: draft
sort: 517
user_created: 78a455fe-2ca9-4621-b542-98e98ebfefe8
date_created: '2024-08-10T03:05:37.889Z'
user_updated: abd14398-1d53-4e6f-9834-26328f0ebde0
date_updated: '2025-07-18T02:43:23.523Z'
title: Payroll
requirement:
  time: 1745290189488
  blocks:
    - id: vE45tnGya_
      type: paragraph
      data:
        text: Payroll Center
  version: 2.30.7
screen_design: null
module: PR
local_fields:
  - code: payrollPeriod
    title: Payroll Period
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    pinned: true
    href: null
    show_sort: true
  - code: payrollSubPeriod
    title: Payroll Sub-period
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    pinned: true
    options__tabular__column_width: 13
    href: null
    show_sort: true
  - code: country
    title: Country
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: company
    title: Company
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: legalEntity
    title: Legal Entity
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: payGroup
    title: Paygroup
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: elementGroup
    title: Element Group
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: elementType
    title: Element Type
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: paymentDate
    title: Payment Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    show_sort: true
  - code: startDate
    title: Start Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    show_sort: true
  - code: endDate
    title: End Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    show_sort: true
  - code: totalCalculation
    title: Total Calculation
    data_type:
      key: Nmberic
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: totalFail
    title: Total Fail
    data_type:
      key: Nmberic
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: totalNotCalculated
    title: Total Not Calculated
    data_type:
      key: Nmberic
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: totalProcessing
    title: Total Processing
    data_type:
      key: Nmberic
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: totalCompleted
    title: Total Completed
    data_type:
      key: Nmberic
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: totalLocked
    title: Total Locked
    data_type:
      key: Nmberic
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: currency
    title: Currency
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: version
    title: Version
    data_type:
      key: Nmberic
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: revision
    title: Revision
    data_type:
      key: Nmberic
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: periodStatusTable
    title: Period Status
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tag
      collection: field_types
    extra_config:
      tags:
        - value: NotCalculated
          label: Not Calculated
          class: infor
        - value: Processing
          label: Processing
          style:
            background_color: '#FEF9CC'
        - value: Finalized
          label: Finalized
          style:
            background_color: '#F3EAFB'
        - value: Locked
          label: Locked
          class: default
        - value: Failed
          label: Failed
          class: error
        - value: Completed
          label: Completed
          class: success
    options__tabular__column_width: null
    dragable: false
    show_sort: true
  - code: updatedBy
    title: Last Updated By
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: updatedAt
    title: Last Updated On
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
    options__tabular__column_width: null
    show_sort: true
mock_data: null
local_buttons: null
layout: layout-table-progressing
form_config:
  formSize:
    create: largex
    view: largex
    edit: largex
  formTitle:
    view: Payroll result
  btnModalDialogFooter:
    - id: identify
      title: Identify
      type: secondary
      custom_api:
        backendUrl: /api/identity-payroll-employee-polling/register
        method: POST
        body_list_keys:
          - payrollPeriodCode
          - payrollPeriodSettingCode
    - id: calculate
      title: Run
      type: primary
  fields:
    - type: group
      collapsed: false
      disableEventCollapse: true
      n_cols: 2
      fields:
        - name: company
          clearFieldsAfterChange:
            - payrollPeriodCode
            - payrollPeriodSetting
          label: Company
          type: select
          isLazyLoad: true
          col: 2
          validators:
            - type: required
          _value:
            transform: $.variables._companyFromList
          placeholder: Select Company
          outputValue: value
          _select:
            transform: $companiesList($.extend.limit, $.extend.page, $.extend.search)
        - name: companyCode
          dependantField: $.fields.company
          type: text
          unvisible: true
          _value:
            transform: $.fields.company.code
        - name: payrollPeriodForm
          label: Payroll Period
          isLazyLoad: true
          type: select
          outputValue: value
          validators:
            - type: required
          placeholder: Select Payroll Period
          clearFieldsAfterChange:
            - payrollPeriodSetting
          _select:
            transform: $payrollPeriodList($.extend.limit, $.extend.page, $.extend.search)
          _value:
            transform: >-
              ($ppsc:=$.fields.payrollPeriodSetting;
              ($not($isNilorEmpty($ppsc)) and $ppsc.payrollPeriodCode !=
              $.fields.payrollPeriodCode) ? $payrollPeriodList(1, 1, '',
              $ppsc.payrollPeriodCode)[0])
        - name: payrollPeriodCode
          dependantField: $.fields.payrollPeriodForm
          type: text
          unvisible: true
          _value:
            transform: $.fields.payrollPeriodForm
        - name: payrollPeriodSetting
          label: Payroll Sub-period
          isLazyLoad: true
          type: select
          outputValue: value
          _value:
            transform: $.variables._payrollPeriodSettingFromList
          validators:
            - type: required
          placeholder: Select Payroll Sub-period
          _select:
            transform: >-
              $not($isNilorEmpty($.fields.companyCode)) ?
              $payrollSubPeriodList($.extend.limit, $.extend.page,
              $.extend.search,$.fields.payrollPeriodForm, $.fields.companyCode)
              : []
        - name: payrollPeriodSettingCode
          dependantField: $.fields.payrollPeriodSetting
          type: text
          unvisible: true
          _value:
            transform: $.fields.payrollPeriodSetting.code
        - name: payrollPeriodSettingDetail
          dependantField: $.fields.payrollPeriodSetting
          type: text
          unvisible: true
          _value:
            transform: $.variables._detailPayrollSubPeriod
    - name: filter
      type: checkbox
      label: Filter
      _condition:
        transform: '$boolean($.fields.payrollPeriodSetting) ? true : false'
      dependantField: $.fields.payrollPeriodSetting
    - type: group
      collapsed: false
      disableEventCollapse: true
      fieldBackground: '#F8F9FA'
      padding: 16px
      borderRadius: 8px
      _condition:
        transform: $.fields.filter = true
      clearAll: true
      n_cols: 2
      fields:
        - name: legalEntityCode
          label: Legal Entity
          type: select
          outputValue: value
          placeholder: Select Legal Entity
          _select:
            transform: $legalEntityList($.fields.company.code)
          _condition:
            transform: $not($.variables._checkLegalPaygroup)
          dependantField: $.fields.payrollPeriodSetting
        - name: payGroupCode
          label: Pay Group
          type: select
          outputValue: value
          placeholder: Select Pay Group
          _select:
            transform: $payGroupList($.fields.companyCode)
          _condition:
            transform: $not($.variables._checkLegalPaygroup)
          dependantField: $.fields.payrollPeriodSetting
        - name: businessUnitCode
          label: Business Unit
          type: select
          outputValue: value
          placeholder: Select Business Unit
          _select:
            transform: $businessUnitsList($.fields.company.code)
          dependantField: $.fields.payrollPeriodSetting
        - name: divisionCode
          label: Division
          type: select
          outputValue: value
          placeholder: Select Division
          _select:
            transform: $divisionsList()
          dependantField: $.fields.payrollPeriodSetting
        - name: departmentCode
          label: Department
          type: select
          placeholder: Select Department
          isLazyLoad: true
          outputValue: value
          _select:
            transform: >-
              $departmentList($.extend.limit, $.extend.page, $.extend.search,
              $DateFormat($now(),'YYYY-MM-DD'),$.variables._selectedCompany.companyId)
          dependantField: $.fields.payrollPeriodSetting
        - name: jobTitleCode
          label: Job Title
          type: select
          placeholder: Select Job Title
          outputValue: value
          _select:
            transform: $jobsList()
          dependantField: $.fields.payrollPeriodSetting
        - name: positionCode
          label: Position
          type: select
          outputValue: value
          placeholder: Select Position
          _select:
            transform: $positionsList()
          dependantField: $.fields.payrollPeriodSetting
        - name: locationCode
          label: Location
          type: select
          isLazyLoad: true
          outputValue: value
          placeholder: Select Location
          _select:
            transform: >-
              $locationsList($.extend.limit, $.extend.page, $.extend.search,
              $DateFormat($now(),'YYYY-MM-DD'))
          dependantField: $.fields.payrollPeriodSetting
        - name: employeeGroupCode
          label: Employee Group
          type: select
          outputValue: value
          placeholder: Select Employee Group
          _select:
            transform: $employeeGroupList()
          dependantField: $.fields.payrollPeriodSetting
        - name: contractTypeCode
          label: Contract Type
          type: select
          outputValue: value
          placeholder: Select Contract Type
          _select:
            transform: $contractTypeList()
          dependantField: $.fields.payrollPeriodSetting
        - name: employeeLevelCode
          label: Employee Level
          type: select
          outputValue: value
          placeholder: Select Employee Level
          _select:
            transform: $levelsList()
          dependantField: $.fields.payrollPeriodSetting
        - name: prStatusCode
          label: Payroll Status
          type: select
          outputValue: value
          placeholder: Select Payroll Status
          _select:
            transform: $payrollStatusList()
          dependantField: $.fields.payrollPeriodSetting
        - name: calculateStatus
          label: Calculate Status
          outputValue: value
          type: select
          placeholder: Select Calculate Status
          select:
            - label: Not calculated
              value: NotCalculated
            - label: Failed
              value: Failed
            - label: Completed
              value: Completed
          dependantField: $.fields.payrollPeriodSetting
        - name: employeeIds
          label: Employee List
          type: select
          placeholder: Select Employee List
          outputValue: value
          mode: multiple
          _select:
            transform: $employeeList($.fields.payrollPeriodSetting)
          dependantField: $.fields.payrollPeriodSetting
  sources:
    companiesList:
      uri: '"/api/companies/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search':
        $.search,'filter':[{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      authAction: Calculation
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'code': $item.code},'companyId':
        $item.id}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    payrollPeriodList:
      uri: '"/api/payroll-periods"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter' :
        [{'field':'code','operator': '$eq','value': $.code}]}
      bodyTransform: ''
      authAction: Calculation
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - code
    payrollSubPeriodList:
      uri: '"/api/payroll-period-settings/authorization-get"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'code','operator': '$eq','value':
        $.subCode},{'field':'payrollPeriodCode','operator': '$eq','value':
        $.payrollPeriodCode}, {'field':'company','operator': '$eq','value':
        $.companyCode}]}
      bodyTransform: ''
      authAction: Calculation
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {$merge([$item, {'label':
        $item.longName.default & ' (' & $item.code & ')', 'value': {'code' :
        $item.code, 'id' : $item.id, 'payrollPeriodCode':
        $item.payrollPeriodCode}}])})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - payrollPeriodCode
        - companyCode
        - subCode
    payrollSubPeriodDetail:
      uri: '"/api/payroll-period-settings/" & $.id'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      authAction: Calculation
      headerTransform: ''
      resultTransform: $
      disabledCache: true
      params:
        - id
    selectedPayrollSubPeriod:
      uri: '"/api/payroll-period-settings"'
      method: GET
      queryTransform: >-
        {'limit' : 1,'filter': [{'field':'code','operator': '$eq','value':
        $.subCode}]}
      bodyTransform: ''
      authAction: Calculation
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {$merge([$item, {'label':
        $item.longName.default & ' (' & $item.code & ')', 'value':
        $item.code}])})[]
      disabledCache: true
      params:
        - subCode
    employeeGroupList:
      uri: '"/api/employee-groups"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      authAction: Calculation
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.employeeGroup.default,
        'value': $item.code}})[]
      disabledCache: true
    employeeList:
      uri: '"/api/payroll-employees"'
      method: GET
      queryTransform: >-
        {'limit': 1000,'filter': [{'field':'status','operator':
        '$eq','value':true},{'field':'payrollPeriodSettingCode','operator':
        '$eq','value':$.subPeriodCode}]}
      bodyTransform: ''
      authAction: Calculation
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.employeeId & '-' &
        $item.fullName, 'value': $item.employeeId}})[]
      disabledCache: true
      params:
        - subPeriodCode
    legalEntityList:
      uri: '"/api/legal-entities"'
      queryTransform: >-
        {'filter': [{'field':'status','operator': '$eq','value':true},
        {'field':'companyCode','operator': '$eq','value': $.companyCode }]}
      method: GET
      bodyTransform: ''
      authAction: Calculation
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id}})[]
      disabledCache: true
      params:
        - companyCode
    departmentList:
      uri: '"/api/departments/get-by"'
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'filter':
        [{'field':'search','operator':
        '$eq','value':$.search},{'field':'effectiveDate','operator':
        '$lte','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true},{'field':'companyId','operator':
        '$eq','value':$.companyId}]}
      method: GET
      bodyTransform: ''
      authAction: Calculation
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
        - companyId
    businessUnitsList:
      uri: '"/api/business-units"'
      queryTransform: >-
        {'filter': [{'field':'status','operator':
        '$eq','value':true},{'field':'companyCode','operator': '$eq','value':
        $.companyCode }], 'limit': 1000}
      method: GET
      bodyTransform: ''
      authAction: Calculation
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - companyCode
    divisionsList:
      uri: '"/api/divisions"'
      queryTransform: >-
        {'filter': [{'field':'status','operator': '$eq','value':true}],
        'limit':1000}
      method: GET
      bodyTransform: ''
      authAction: Calculation
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
    payGroupList:
      uri: '"/api/pay-group-structures"'
      queryTransform: >-
        {'filter': [{'field':'status','operator': '$eq','value':true},
        {'field':'companyCode','operator': '$eq','value': $.companyCode }],
        'limit':1000}
      method: GET
      bodyTransform: ''
      authAction: Calculation
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - companyCode
    levelsList:
      uri: '"/api/picklists/EMPLEVEL/values"'
      queryTransform: '{''filter'': [{''field'':''status'',''operator'': ''$eq'',''value'':true}]}'
      method: GET
      bodyTransform: ''
      authAction: Calculation
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
    locationsList:
      uri: '"/api/locations"'
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      method: GET
      bodyTransform: ''
      authAction: Calculation
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
    jobsList:
      uri: '"/api/job-codes/by"'
      queryTransform: '{''filter'': [{''field'':''status'',''operator'': ''$eq'',''value'':true}]}'
      method: GET
      bodyTransform: ''
      authAction: Calculation
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
    positionsList:
      uri: '"/api/positions/by"'
      queryTransform: '{''filter'': [{''field'':''status'',''operator'': ''$eq'',''value'':true}]}'
      method: GET
      bodyTransform: ''
      authAction: Calculation
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
    payrollStatusList:
      uri: '"/api/picklists/PAYROLLSTATUS/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      authAction: Calculation
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
    contractTypeList:
      uri: '"/api/picklists/CONTRACTTYPE/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      authAction: Calculation
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
  variables:
    _companyFromList:
      transform: >-
        ($defaultData := $.extend.defaultValue;$defaultData.id ? {'label' :
        $defaultData.company & ' (' & $defaultData.companyCode & ')'
        ,'value':{'code': $defaultData.companyCode} } : null)
    _payrollPeriodFromList:
      transform: >-
        ($defaultData := $.extend.defaultValue;$defaultData.id ? {'label' :
        $defaultData.payrollPeriod & ' (' & $defaultData.payrollPeriodCode & ')'
        ,'value':$defaultData.payrollPeriodCode } : null)
    _payrollPeriodSettingFromList:
      transform: >-
        ($defaultData := $.extend.defaultValue;$defaultData.id ? {'label' :
        $defaultData.payrollSubPeriod & ' (' &
        $defaultData.payrollPeriodSettingCode & ')' ,'value':{'code' :
        $defaultData.code, 'id' : $defaultData.id, 'payrollPeriodCode':
        $defaultData.payrollPeriodCode} } : null)
    _detailPayrollSubPeriod:
      transform: >-
        $.fields.payrollPeriodSetting.id ?
        $payrollSubPeriodDetail($.fields.payrollPeriodSetting.id) : {}
    _payrollPeriodList:
      transform: $payrollPeriodList()
    _legalEntityList:
      transform: $legalEntityList()
    _companiesList:
      transform: $companiesList()
    _selectedCompany:
      transform: >-
        $filter($.variables._companiesList , function($v) {$v.value =
        $.fields.companyCode})[0]
    _checkLegalPaygroup:
      transform: >-
        $boolean($boolean($.variables._detailPayrollSubPeriod.payGroup) = true
        or $boolean($.variables._detailPayrollSubPeriod.legalEntity)) ? true :
        false
  overview:
    dependentField: payrollPeriodSettingDetail
    title: Payroll Sub-period
    display:
      - key: paymentDay
        label: Payment Date
        _value:
          transform: >-
            $fromMillis($toMillis($.fields.payrollPeriodSettingDetail.paymentDay),
            '[D01]/[M01]/[Y]')
      - key: startDate
        label: Start Date
        _value:
          transform: >-
            $fromMillis($toMillis($.fields.payrollPeriodSettingDetail.startDate),
            '[D01]/[M01]/[Y]')
      - key: endDate
        label: End Date
        _value:
          transform: >-
            $fromMillis($toMillis($.fields.payrollPeriodSettingDetail.endDate),
            '[D01]/[M01]/[Y]')
      - key: elementGroup
        label: Element Group
        _value:
          transform: $.fields.payrollPeriodSettingDetail.elementGroupName
      - key: elementType
        label: Element Type
        _value:
          transform: $.fields.payrollPeriodSettingDetail.salaryTypeName
      - key: legalEntity
        label: Legal Entity
        _value:
          transform: $.fields.payrollPeriodSettingDetail.legalEntity
      - key: payGroup
        label: Paygroup
        _value:
          transform: $.fields.payrollPeriodSettingDetail.payGroupName
  calculateBackendUrl: /api/payroll-employees/process-payroll-employees
  checkNoteBackendUrl: /api/payroll-employees/check-note-process-payroll-employees
  codeCalculateKey: payrollPeriodSettingCode
filter_config:
  fields:
    - name: company
      label: Company
      isLazyLoad: true
      type: selectAll
      placeholder: Select Company
      _options:
        transform: $companiesList($.extend.limit, $.extend.page, $.extend.search)
    - name: payrollPeriod
      label: Payroll Period
      isLazyLoad: true
      type: selectAll
      placeholder: Select Payroll Period
      _options:
        transform: $payrollPeriodList($.extend.limit, $.extend.page, $.extend.search)
    - name: legalEntity
      label: Legal Entity
      isLazyLoad: true
      type: selectAll
      placeholder: Select Legal Entity
      _options:
        transform: $legalEntityList($.extend.limit, $.extend.page, $.extend.search)
    - name: payGroup
      label: Pay Group
      isLazyLoad: true
      type: selectAll
      placeholder: Select Pay Group
      _options:
        transform: $payGroupList($.extend.limit, $.extend.page, $.extend.search)
    - name: elementGroup
      label: Element Group
      type: selectAll
      placeholder: Select Element Group
      _options:
        transform: $elementGroupsList()
    - name: elementType
      label: Element Type
      type: selectAll
      placeholder: Select Element Type
      _options:
        transform: $elementTypesList()
    - name: prStatus
      label: Period Status
      mode: multiple
      type: select
      placeholder: Select Period Status
      select:
        - label: Not calculated
          value: NotCalculated
        - label: Completed
          value: Completed
        - label: Processing
          value: Processing
        - label: Finalized
          value: Finalized
  filterMapping:
    - field: companyCode
      operator: $in
      valueField: company.(value)
    - field: payrollPeriodCode
      operator: $in
      valueField: payrollPeriod.(value)
    - field: legalEntityCode
      operator: $in
      valueField: legalEntity.(value)
    - field: payGroupCode
      operator: $in
      valueField: payGroup.(value)
    - field: elementGroupCode
      operator: $in
      valueField: elementGroup.(value)
    - field: elementTypeCode
      operator: $in
      valueField: elementType.(value)
    - field: PayrollStatus
      operator: $in
      valueField: prStatus.(value)
  sources:
    payrollPeriodList:
      uri: '"/api/payroll-periods"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    companiesList:
      uri: '"/api/companies"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search, ''filter'':[]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    employeeGroupList:
      uri: '"/api/employee-groups"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.employeeGroup.default,
        'value': $item.code}})[]
      disabledCache: true
    personalsList:
      uri: '"/api/personals"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name, 'value':
        $item.id}})[]
      disabledCache: true
    legalEntityList:
      uri: '"/api/legal-entities"'
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search, ''filter'': []}'
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    elementGroupsList:
      uri: '"/api/picklists/ELEMENTGROUP/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})
      disabledCache: true
    elementTypesList:
      uri: '"/api/picklists/ELEMENTTYPE/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})
      disabledCache: true
    payGroupList:
      uri: '"/api/pay-group-structures"'
      queryTransform: '{''filter'': [], ''limit'': $.limit,''page'': $.page,''search'': $.search}'
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id,'companyCode' :
        $item.companyCode }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
layout_options:
  pre_check_calculate: /api/payroll-period-settings/{{values.id}}
  show_detail_history: false
  hide_action_row: true
  action_click_row:
    actionId: routing
    params: code
  show_create_data_table: false
  show_actions_delete: false
  toolTable:
    export: true
    adjustDisplay: true
  tool_table:
    - id: export
      icon: icon-download-simple
  fsdIdProgressingPath: /PR/PR.FS.FR.054
  progressing_info:
    title_progressing: Process List
    body_progressing:
      id: id
      title: payrollPeriodName
      subTitle: payrollPeriodSettingName
      code: payrollPeriodSettingCode
      progress: progress
      createdAt: createdAt
      type: type
      status: status
    get_progressing_list_api: /api/payroll-calculation-polling/get-by-user
    update_progressing_list_api:
      url: /api/payroll-calculation-polling/progresses
      method: POST
  show_filter_results_message: true
  initial_dialog_value:
    - fromAction: calculate
      valueTransform: >-
        $count($.selectedItems) = 1 and $.selectedItems[0].periodStatus !=
        'Finalized' ? $.selectedItems[0] : null
  store_selected_items: true
layout_options__header_buttons:
  - id: calculate
    title: Calculate
    icon: icon-plus-minus-bold
    type: primary
options: null
create_form: null
layout_options__footer_buttons: null
layout_options__row_actions: []
backend_url: /api/salary-formula-payroll-period-settings
screen_name: null
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: code
default_filter: null
pre_condition_config: {}
permission_key:
  - name: countryCode
    defaultName: CountryCode
  - name: companyCode
    defaultName: CompanyCode
  - name: legalEntityCode
    defaultName: LegalEntityCode
  - name: payGroupCode
    defaultName: PayGroupCode
  - name: employeeId
    defaultName: EmployeeId
  - name: employeeRecordNumber
    defaultName: EmployeeRecordNumber
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Payroll Center
  parent:
    title: PR Processes
