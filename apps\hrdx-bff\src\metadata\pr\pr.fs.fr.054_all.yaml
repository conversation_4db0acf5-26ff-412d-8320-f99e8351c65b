id: PR.FS.FR.054_all
status: draft
sort: 9
user_created: 78a455fe-2ca9-4621-b542-98e98ebfefe8
date_created: '2024-08-10T09:37:41.871Z'
user_updated: b403d5da-d684-45e4-ae6d-e25223baedc5
date_updated: '2025-07-18T08:39:29.492Z'
title: Payroll Result All
requirement:
  time: 1748520967057
  blocks:
    - id: 5G1DfADUg-
      type: paragraph
      data:
        text: Payroll result all
  version: 2.30.7
screen_design: null
module: PR
local_fields:
  - code: isLock
    title: Locked
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: Lock
      collection: field_types
  - code: employeeId
    title: Employee ID
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: employeeRecordNumber
    title: Employee record number
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: fullName
    title: Full Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: employeeGroup
    title: Employee Group
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: jobIndicator
    title: Job Indicator
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: businessUnit
    title: Business Unit
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: division
    title: Division
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: company
    title: Company
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: legalEntity
    title: Legal Entity
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: payGroup
    title: Paygroup
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: department
    title: Department
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: position
    title: Position
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: calculationStatus
    title: Status
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: note
    title: Note
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: paymentDate
    title: Payment Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/YYYY
      collection: field_types
  - code: action
    title: Action
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: reason
    title: Reason
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
mock_data: null
local_buttons: null
layout: layout-table-progressing
form_config:
  formSize:
    view: largex
  fields:
    - type: group
      label: Period information
      collapse: false
      disableEventCollapse: true
      n_cols: 2
      fieldGroupTitleStyle:
        borderTop: none
        paddingTop: 0px
      fields:
        - type: group
          fields:
            - name: payrollPeriod
              label: Payroll Period
              type: text
              readOnly: true
            - name: companyName
              label: Company
              type: text
              readOnly: true
            - name: elementGroup
              label: Element Group
              type: text
              readOnly: true
            - name: payGroup
              label: Paygroup
              type: text
              readOnly: true
            - name: startDate
              label: Start Date
              readOnly: true
              type: dateRange
              mode: date-picker
              setting:
                format: dd/MM/yyyy
                type: date
            - name: version
              label: Version
              type: text
              readOnly: true
              value: '1'
            - name: segment
              label: Segment
              type: text
              readOnly: true
            - name: reason
              label: Reason
              type: text
              readOnly: true
            - name: calculationStatus
              label: Status
              type: text
              readOnly: true
        - type: group
          fields:
            - name: payrollPeriodSetting
              label: Payroll Sub-period
              type: text
              readOnly: true
            - name: legalEntity
              label: Legal Entity
              type: text
              readOnly: true
            - name: elementTypeName
              label: Element Type
              type: text
              readOnly: true
            - name: paymentDate
              label: Payment Date
              readOnly: true
              type: dateRange
              mode: date-picker
              setting:
                format: dd/MM/yyyy
                type: date
            - name: endDate
              label: End Date
              readOnly: true
              type: dateRange
              mode: date-picker
              setting:
                format: dd/MM/yyyy
                type: date
            - name: revision
              label: Revision
              type: text
              readOnly: true
            - name: formula
              label: Formula
              type: text
              readOnly: true
            - name: action
              label: Action
              type: text
              readOnly: true
            - name: note
              label: Note
              type: textarea
              readOnly: true
              placeholder: Input content
              autoSize:
                minRows: 2
                maxRows: 4
  footer:
    create: true
    update: true
    createdBy: createdBy
    createdOn: createdAt
    updatedBy: updatedBy
    updatedOn: updatedAt
filter_config:
  fields:
    - type: selectAll
      label: Legal Entity
      labelType: type-grid
      isLazyLoad: true
      name: legalEntity
      mode: multiple
      placeholder: Select Legal Entity
      _options:
        transform: >-
          $legalEntityList($.extend.limit, $.extend.page, $.extend.search,
          $.extend.addOnValue.dataDetail.companyCode)
    - type: selectAll
      label: Business Unit
      labelType: type-grid
      isLazyLoad: true
      name: businessUnit
      mode: multiple
      placeholder: Select Bussiness Unit
      _options:
        transform: >-
          $businessUnitList($.extend.limit, $.extend.page, $.extend.search,
          $.extend.addOnValue.dataDetail.companyCode)
    - name: division
      label: Division
      labelType: type-grid
      isLazyLoad: true
      type: selectAll
      placeholder: Select Division
      mode: multiple
      _options:
        transform: >-
          $divisionsList($.extend.limit, $.extend.page,
          $.extend.search,$.extend.addOnValue.dataDetail.companyCode,$map($.fields.businessUnit
          , function($v) {$v.id}))
    - name: department
      label: Department
      labelType: type-grid
      type: selectAll
      placeholder: Select Department
      mode: multiple
      isLazyLoad: true
      _options:
        transform: >-
          $departmentList($.extend.limit, $.extend.page,
          $.extend.search,$.extend.addOnValue.dataDetail.companyCode
          ,$map($.fields.legalEntity , function($v) {$v.id}))
    - name: payrollStatus
      label: Payroll Status
      labelType: type-grid
      type: selectAll
      mode: multiple
      placeholder: Select Period Status
      options:
        - label: Not calculated
          value: NotCalculated
        - label: Failed
          value: Failed
        - label: Locked
          value: Locked
        - label: Completed
          value: Completed
        - label: Processing
          value: Processing
    - name: jobTitle
      label: Job Title
      isLazyLoad: true
      type: selectAll
      labelType: type-grid
      mode: multiple
      placeholder: Select Job Title
      _options:
        transform: >-
          $jobCodesList($.extend.limit, $.extend.page, $.extend.search,
          $.extend.addOnValue.dataDetail.companyCode)
    - name: position
      label: Position
      isLazyLoad: true
      labelType: type-grid
      type: selectAll
      mode: multiple
      placeholder: Select Position
      _options:
        transform: >-
          $positionsList($.extend.limit, $.extend.page, $.extend.search,
          $.extend.addOnValue.dataDetail.companyCode)
    - name: location
      label: Location
      labelType: type-grid
      type: selectAll
      placeholder: Select Location
      mode: multiple
      _options:
        transform: >-
          $locationsList($.extend.limit, $.extend.page, $.extend.search,
          $.extend.addOnValue.dataDetail.companyCode)
    - name: employeeGroup
      label: Employee Group
      type: selectAll
      placeholder: Select Employee Group
      labelType: type-grid
      mode: multiple
      _options:
        transform: $employeeGroupsList()
    - name: contractType
      label: Contract Type
      type: selectAll
      placeholder: Select Contract Type
      labelType: type-grid
      mode: multiple
      _options:
        transform: $contractTypeList()
    - name: employeeLevel
      label: Employee Level
      type: selectAll
      placeholder: Select Employee Level
      labelType: type-grid
      mode: multiple
      _options:
        transform: $empLevelList()
  filterMapping:
    - field: businessUnitCode
      operator: $in
      valueField: businessUnit.(value)
    - field: legalEntityCode
      operator: $in
      valueField: legalEntity.(value)
    - field: divisionCode
      operator: $in
      valueField: division.(value)
    - field: departmentCode
      operator: $in
      valueField: department.(value)
    - field: calculationStatus
      operator: $in
      valueField: payrollStatus.(value)
    - field: jobTitleCode
      operator: $in
      valueField: jobTitle.(value)
    - field: positionCode
      operator: $in
      valueField: position.(value)
    - field: locationCode
      operator: $in
      valueField: location.(value)
    - field: employeeGroupCode
      operator: $in
      valueField: employeeGroup.(value)
    - field: contractTypeCode
      operator: $in
      valueField: contractType.(value)
    - field: employeeLevelCode
      operator: $in
      valueField: employeeLevel.(value)
  sources:
    empLevelList:
      uri: '"/api/picklists/EMPLEVEL/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})
      disabledCache: true
    contractTypeList:
      uri: '"/api/picklists/CONTRACTTYPE/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})
      disabledCache: true
    employeeGroupsList:
      uri: '"/api/employee-groups"'
      method: GET
      queryTransform: '{''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.employeeGroup.default,
        'value': $item.code}})
      disabledCache: true
    locationsList:
      uri: '"/api/locations"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'CompanyCode','operator':
        '$eq','value':$.companyCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.id,  'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - companyCode
    positionsList:
      uri: '"/api/positions/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search ,
        'filter':[{'field':'companyCode','operator': '$eq','value':
        $.companyCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.id,  'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - companyCode
    jobCodesList:
      uri: '"/api/job-codes/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search ,
        'filter':[{'field':'companyCode','operator': '$eq','value':
        $.companyCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - companyCode
    legalEntityList:
      uri: '"/api/legal-entities"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search ,
        'filter':[{'field':'companyCode','operator':
        '$eq','value':$.companyCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default, 'value':
        $item.code , 'id' : $item.id}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - companyCode
    businessUnitList:
      uri: '"/api/business-units"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search ,
        'filter':[{'field':'companyCode','operator':
        '$eq','value':$.companyCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default, 'value':
        $item.code , 'id' : $item.id}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - companyCode
    divisionsList:
      uri: '"/api/divisions"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page,'search': $.search ,'filter':
        [{'field':'companyCode','operator':
        '$eq','value':$.companyCode},{'field':'businessUnitId','operator':
        '$in','value':$.businessUnitIds}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.id, 'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - companyCode
        - businessUnitIds
    departmentList:
      uri: '"/api/departments"'
      queryTransform: >-
        {'limit': $.limit, 'page': $.page,'search': $.search , 'filter':
        [{'field':'companyCodeFilter','operator':
        '$eq','value':$.companyCode},{'field':'legalEntityId','operator':
        '$in','value':$.legalEntityIds}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - companyCode
        - legalEntityIds
  variables: {}
layout_options:
  show_detail_history: false
  show_create_data_table: false
  customStyleContent:
    padding: 20px 0 0 0
  checkNoteBackendUrl: /api/payroll-employees/check-note-re-calculate-salary
  checkEmployeeBackendUrl: /api/payroll-employees/has-employee-tab
  custom_export_api:
    _url:
      transform: '''/api/payroll-employees/export-static-tab'''
  tool_table:
    - id: note
      title: Note
      icon: note
      backendUrl: /api/payroll-employees/set-note-paymentdate
      _disabled:
        transform: $.parentData.periodStatus = 'Finalized'
    - id: export
      icon: file-arrow-down
  modal_footer_buttons:
    - id: note
      title: Update Note & Payment Date
      type: tertiary
      backendUrl: /api/payroll-employees/set-note-paymentdate
    - id: lock
      title: Lock
      type: primary
      backendUrl: /api/payroll-employees/lock
      conditionByKey: calculationStatus
      conditionValue: Completed
    - id: unlock
      title: Unlock
      type: secondary
      backendUrl: /api/payroll-employees/un-lock
      conditionByKey: calculationStatus
      conditionValue: Locked
  show_actions_delete: false
  is_show_length_pagination: true
  page_header_options:
    visible: false
  row_actions_handler:
    lock:
      action: edit
      confirm:
        title: Lock
        content: >-
          This will not be accessible after activating Lock. Do you want to
          lock?
      _update_fields: >-
        {'payrollPeriodSettingCode': $.payrollPeriodSettingCode,
        'payrollPeriodCode': $.payrollPeriodCode, 'employeeId': $.employeeId,
        'employeeRecordNumber': $.employeeRecordNumber}
      backendUrl: /api/payroll-employees/lock
    unlock:
      action: edit
      confirm:
        title: Unlock
        content: Do you want to unlock?
      _update_fields: >-
        {'payrollPeriodSettingCode': $.payrollPeriodSettingCode,
        'payrollPeriodCode': $.payrollPeriodCode, 'employeeId': $.employeeId,
        'employeeRecordNumber': $.employeeRecordNumber}
      backendUrl: /api/payroll-employees/un-lock
    note:
      action: edit
      _update_fields: >-
        {'payrollPeriodSettingCode': $.payrollPeriodSettingCode,
        'payrollPeriodCode': $.payrollPeriodCode, 'employeeId': $.employeeId,
        'employeeRecordNumber': $.employeeRecordNumber}
      backendUrl: /api/payroll-employees/set-note-paymentdate
  view_detail_by_config:
    is_show: true
    show_view_detail_by_key: calculationStatus
    value_to_show:
      - Completed
      - Locked
  custom_view_detail_api:
    _url:
      transform: >-
        '/api/report-types/' & $.reportTypeId & '/calculate-employee-detail/' &
        $.employeeId
  _show_dialog_footer:
    transform: >-
      $not($.parentData.periodStatus = 'Finalized' and $.extend.formType =
      'view')
  store_selected_items: true
layout_options__header_buttons: null
options: null
create_form: null
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: note
    title: Note
    icon: icon-notebook-bold
    type: ghost-gray
    href: /api/payroll-employees/set-note-paymentdate
    _disabled: $.parentData.periodStatus = 'Finalized'
backend_url: /api/payroll-employees/get-for-payroll-calculation
screen_name: null
layout_options__actions_many:
  - id: export
    title: Export Selected
    icon: icon-upload-simple-bold
    type: secondary
parent: PR.FS.FR.054_detail
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter:
  - name: payrollPeriodSettingCode
    fieldValue: parent.code
    operator: $eq
  - name: isSalaried
    fieldValue: 'true'
    operator: $eq
pre_condition_config: {}
permission_key: null
screen_designs: []
function_specs: []
fields: []
children: []
menu_item: null
