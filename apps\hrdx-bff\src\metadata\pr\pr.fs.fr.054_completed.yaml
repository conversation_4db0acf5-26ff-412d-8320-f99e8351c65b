id: PR.FS.FR.054_completed
status: draft
sort: 5
user_created: 78a455fe-2ca9-4621-b542-98e98ebfefe8
date_created: '2024-08-10T07:07:11.187Z'
user_updated: b403d5da-d684-45e4-ae6d-e25223baedc5
date_updated: '2025-07-17T17:14:20.725Z'
title: Payroll Result Completed
requirement:
  time: 1744602563162
  blocks:
    - id: 5G1DfADUg-
      type: paragraph
      data:
        text: Payroll result completed
  version: 2.30.7
screen_design: null
module: PR
local_fields: []
mock_data: null
local_buttons: null
layout: layout-table-progressing
form_config:
  formSize:
    view: large
  fields:
    - type: group
      label: Period information
      collapse: false
      disableEventCollapse: false
      n_cols: 2
      fields:
        - type: group
          fields:
            - name: payrollPeriod
              label: Payroll Period
              type: text
              readOnly: true
            - name: companyName
              label: Company
              type: text
              readOnly: true
            - name: elementGroup
              label: Element Group
              type: text
              readOnly: true
            - name: payGroup
              label: Paygroup
              type: text
              readOnly: true
            - name: startDate
              label: Start Date
              readOnly: true
              type: dateRange
              mode: date-picker
              setting:
                format: dd/MM/yyyy
                type: date
            - name: version
              label: Version
              type: text
              readOnly: true
              value: '1'
            - name: segment
              label: Segment
              type: text
              readOnly: true
            - name: reason
              label: Reason
              type: text
              readOnly: true
            - name: calculationStatus
              label: Status
              type: text
              readOnly: true
            - name: action
              label: Action
              type: text
              readOnly: true
        - type: group
          fields:
            - name: payrollPeriodSetting
              label: Payroll Sub-period
              type: text
              readOnly: true
            - name: legalEntity
              label: Legal Entity
              type: text
              readOnly: true
            - name: elementTypeName
              label: Element Type
              type: text
              readOnly: true
            - name: paymentDate
              label: Payment Date
              readOnly: true
              type: dateRange
              mode: date-picker
              setting:
                format: dd/MM/yyyy
                type: date
            - name: endDate
              label: End Date
              readOnly: true
              type: dateRange
              mode: date-picker
              setting:
                format: dd/MM/yyyy
                type: date
            - name: revision
              label: Revision
              type: text
              readOnly: true
            - name: formula
              label: Formula
              type: text
              readOnly: true
            - name: formulaStructureName
              label: Set up Salary Formula
              type: text
              _value:
                transform: >-
                  $.extend.defaultValue.formulaStructureName & ' (' &
                  $.extend.defaultValue.formulaStructureCode & ')'
              readOnly: true
            - name: note
              label: Note
              type: textarea
              readOnly: true
              placeholder: Input content
              autoSize:
                minRows: 2
                maxRows: 4
  footer:
    create: true
    update: true
    createdBy: createdBy
    createdOn: createdAt
    updatedBy: updatedBy
    updatedOn: updatedAt
filter_config:
  fields:
    - type: select
      label: Business Unit
      name: businessUnit
      mode: multiple
      placeholder: Select Bussiness Unit
      _select:
        transform: $businessUnitList()
    - name: division
      label: Division
      type: select
      placeholder: Select Division
      mode: multiple
      _select:
        transform: $divisionsList($DateFormat($now(),'YYYY-MM-DDTHH:mm:ss'))
    - name: department
      label: Department
      type: select
      placeholder: Select Department
      mode: multiple
      isLazyLoad: true
      _select:
        transform: >-
          $departmentsList($.extend.limit,
          $.extend.page,$DateFormat($now(),'YYYY-MM-DDTHH:mm:ss'),
          $.extend.search)
    - name: jobTitle
      label: Job Title
      type: select
      mode: multiple
      placeholder: Select Job Title
      _select:
        transform: $jobCodesList()
    - name: position
      label: Position
      type: select
      mode: multiple
      placeholder: Select Position
      _select:
        transform: $positionsList($DateFormat($now(),'YYYY-MM-DDTHH:mm:ss'))
    - name: location
      label: Location
      type: select
      placeholder: Select Location
      mode: multiple
      _select:
        transform: $locationsList($DateFormat($now(),'YYYY-MM-DDTHH:mm:ss'))
    - name: employeeGroup
      label: Employee Group
      type: select
      placeholder: Select Employee Group
      mode: multiple
      _select:
        transform: $employeeGroupsList()
    - name: contractType
      label: Contract Type
      type: select
      placeholder: Select Contract Type
      mode: multiple
      _select:
        transform: $contractTypeList()
    - name: employeeLevel
      label: Employee Level
      type: select
      placeholder: Select Employee Level
      mode: multiple
      _select:
        transform: $empLevelList()
  filterMapping:
    - field: businessUnitCode
      operator: $in
      valueField: businessUnit.(value)
    - field: divisionCode
      operator: $in
      valueField: division.(value)
    - field: departmentCode
      operator: $in
      valueField: department.(value)
    - field: payrollStatus
      operator: $in
      valueField: payrollStatus.(value)
    - field: jobTitleCode
      operator: $in
      valueField: jobTitle.(value)
    - field: positionCode
      operator: $in
      valueField: position.(value)
    - field: locationCode
      operator: $in
      valueField: location.(value)
    - field: employeeGroupCode
      operator: $in
      valueField: employeeGroup.(value)
    - field: contractTypeCode
      operator: $in
      valueField: contractType.(value)
    - field: employeeLevelCode
      operator: $in
      valueField: employeeLevel.(value)
  sources:
    empLevelList:
      uri: '"/api/picklists/EMPLEVEL/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})
      disabledCache: true
    contractTypeList:
      uri: '"/api/picklists/CONTRACTTYPE/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})
      disabledCache: true
    employeeGroupsList:
      uri: '"/api/employee-groups"'
      method: GET
      queryTransform: '{''filter'': [{''field'':''status'',''operator'': ''$eq'',''value'':true}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.employeeGroup.default,
        'value': $item.code}})
      disabledCache: true
    locationsList:
      uri: '"/api/locations/by"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.id,  'additionalData': $item}})[]
      disabledCache: true
      params:
        - effectiveDate
    positionsList:
      uri: '"/api/positions/by"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default & ' (' &
        $item.department.name & ')' , 'value': $item.id, 'jobCode':
        $item.id}})[]
      disabledCache: true
      params:
        - effectiveDate
    jobCodesList:
      uri: '"/api/job-codes/by"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default, 'value':
        $item.code}})[]
      disabledCache: true
    businessUnitList:
      uri: '"/api/business-units/by"'
      method: GET
      queryTransform: '{''filter'': [{''field'':''status'',''operator'': ''$eq'',''value'':true}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default, 'value':
        $item.code}})[]
      disabledCache: true
    divisionsList:
      uri: '"/api/divisions/by"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.id, 'additionalData': $item}})[]
      disabledCache: true
      params:
        - effectiveDate
    departmentsList:
      uri: '"/api/departments/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'filter':
        [{'field':'search','operator':
        '$cont','value':$.search},{'field':'effectiveDate','operator':
        '$lte','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.id, 'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - effectiveDate
        - search
layout_options:
  tab_calculation_status: Completed
  show_detail_history: false
  show_create_data_table: false
  show_table_filter: false
  customStyleContent:
    padding: 20px 0 0 0
  checkNoteBackendUrl: /api/payroll-employees/check-note-re-calculate-salary
  checkEmployeeBackendUrl: /api/payroll-employees/has-employee-tab
  tool_table:
    - id: lock
      icon: lock
      title: Lock
      backendUrl: /api/payroll-employees/lock
      _disabled:
        transform: $.parentData.periodStatus = 'Finalized'
    - id: note
      icon: note
      title: Note
      backendUrl: /api/payroll-employees/set-note-paymentdate
      _disabled:
        transform: $.parentData.periodStatus = 'Finalized'
    - id: reCalculate
      icon: play
      title: Run
      backendUrl: /api/payroll-employees/re-calculate-salary
      _disabled:
        transform: $.parentData.periodStatus = 'Finalized'
    - id: export
      icon: icon-upload-simple-bold
  page_header_options:
    visible: false
  is_show_length_pagination: true
  is_dynamic_config_table: true
  modal_footer_buttons:
    - id: note
      title: Update Note & Payment Date
      type: tertiary
      backendUrl: /api/payroll-employees/set-note-paymentdate
    - id: lock
      title: Lock
      type: primary
      backendUrl: /api/payroll-employees/lock
  show_actions_delete: false
  row_actions_handler:
    lockOne:
      action: edit
      confirm:
        title: Lock
        content: >-
          This will not be accessible after activating Lock. Do you want to
          lock?
      _update_fields: >-
        {'payrollPeriodSettingCode': $.payrollPeriodSettingCode,
        'payrollPeriodCode': $.payrollPeriodCode, 'employeeId': $.employeeId,
        'employeeRecordNumber': $.employeeRecordNumber}
      backendUrl: /api/payroll-employees/lock
    lock:
      action: edit
      confirm:
        title: Lock
        content: >-
          This will not be accessible after activating Lock. Do you want to
          lock?
      _update_fields: >-
        {'payrollPeriodSettingCode': $.PayrollPeriodSettingCode,
        'payrollPeriodCode': $.PayrollPeriodCode, 'employeeId': $.EmployeeId,
        'employeeRecordNumber': $.EmployeeRecordNumber}
      backendUrl: /api/payroll-employees/lock
    note:
      action: edit
      _update_fields: >-
        {'payrollPeriodSettingCode': $.PayrollPeriodSettingCode,
        'payrollPeriodCode': $.PayrollPeriodCode, 'employeeId': $.EmployeeId,
        'employeeRecordNumber': $.EmployeeRecordNumber}
      backendUrl: /api/payroll-employees/set-note-paymentdate
      keyValue:
        calculationStatus: Completed
    reCalculate:
      action: edit
      _update_fields: >-
        {'payrollPeriodSettingCode': $.PayrollPeriodSettingCode,
        'payrollPeriodCode': $.PayrollPeriodCode, 'employeeId': $.EmployeeId,
        'employeeRecordNumber': $.EmployeeRecordNumber}
      backendUrl: /api/payroll-employees/re-calculate-salary
      keyValue:
        calculationStatus: Completed
  record_composite_key:
    name: id
    keys_mapping:
      - Id
  custom_export_api:
    selected_items_query_config:
      field: id
      operator: $eq
      valueField: id
layout_options__header_buttons: null
options: null
create_form: null
layout_options__footer_buttons:
  - id: block
    title: Block
    type: primary
layout_options__row_actions:
  - id: note
    icon: icon-notebook-bold
    type: ghost-gray
    href: /api/payroll-employees/set-note-paymentdate
backend_url: /api/report-types/{{parent.reportTypeId}}/calculate-employee/{{parent.month}}
screen_name: null
layout_options__actions_many:
  - id: export
    title: Export Selected
    icon: icon-upload-simple-bold
    type: secondary
parent: PR.FS.FR.054_detail
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter:
  - name: companyCode
    fieldValue: parent.companyCode
    operator: $eq
  - name: payrollPeriodCode
    fieldValue: parent.payrollPeriodCode
    operator: $eq
  - name: payrollPeriodSettingCode
    fieldValue: parent.code
    operator: $eq
  - name: calculateStatus
    fieldValue: '"Completed"'
    operator: $eq
  - name: isSalaried
    fieldValue: 'true'
    operator: $eq
pre_condition_config: {}
permission_key:
  - name: companyCode
    defaultName: CompanyCode
  - name: legalEntityCode
    defaultName: LegalEntityCode
  - name: payGroupCode
    defaultName: PayGroupCode
  - name: countryCode
    defaultName: CountryCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item: null
