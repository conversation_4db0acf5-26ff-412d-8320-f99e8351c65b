id: PR.FS.FR.054_detail
status: draft
sort: 525
user_created: 9cfe47ce-3920-4eea-91ec-1d8471b048c5
date_created: '2024-08-11T22:00:47.834Z'
user_updated: b403d5da-d684-45e4-ae6d-e25223baedc5
date_updated: '2025-07-04T11:03:02.229Z'
title: Detail Payroll Result
requirement:
  time: 1746635806628
  blocks:
    - id: 2CQZaIsiF1
      type: paragraph
      data:
        text: detail payroll result
  version: 2.30.7
screen_design: null
module: PR
local_fields: []
mock_data: null
local_buttons: null
layout: layout-tabs
form_config: {}
filter_config: {}
layout_options:
  is_layout_detail: true
  show_detail_history: false
  page_header_options:
    visible: true
    back_btn: true
    border: false
  parent_path: /PR/PR.FS.FR.054
  tabs_title:
    - Summary
    - All
    - Fail
    - Not Calculated
    - Processing
    - Completed
    - Locked
  disabled_tab_key:
    key: reportTypeId
    tabs:
      - Fail
      - Not Calculated
      - Processing
      - Completed
      - Locked
  key_set_tab_count:
    totalEmployee: All
    totalFail: Fail
    totalNotCalculated: Not Calculated
    totalProcessing: Processing
    totalCompleted: Completed
    totalLocked: Locked
  custom_title:
    transform: '''Payroll result - '' & $.parentData.payrollSubPeriod'
layout_options__header_buttons: null
options: null
create_form: null
layout_options__footer_buttons: null
layout_options__row_actions: null
backend_url: /api/salary-formula-payroll-period-settings/:id1
screen_name: null
layout_options__actions_many:
  - id: export
    title: Export Selected
    icon: icon-upload-simple-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: companyCode
    defaultName: CompanyCode
  - name: legalEntityCode
    defaultName: LegalEntityCode
  - name: payGroupCode
    defaultName: PayGroupCode
  - name: countryCode
    defaultName: CountryCode
  - name: employeeId
    defaultName: EmployeeId
  - name: employeeRecordNumber
    defaultName: EmployeeRecordNumber
screen_designs: []
function_specs: []
fields: []
children:
  - PR.FS.FR.054_summary
  - PR.FS.FR.054_all
  - PR.FS.FR.054_fail
  - PR.FS.FR.054_not_calculated
  - PR.FS.FR.054_processing
  - PR.FS.FR.054_completed
  - PR.FS.FR.054_locked
menu_item: null
