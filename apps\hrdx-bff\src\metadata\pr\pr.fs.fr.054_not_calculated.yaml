id: PR.FS.FR.054_not_calculated
status: draft
sort: 3
user_created: 78a455fe-2ca9-4621-b542-98e98ebfefe8
date_created: '2024-08-10T09:37:11.354Z'
user_updated: abd14398-1d53-4e6f-9834-26328f0ebde0
date_updated: '2025-07-18T03:33:06.240Z'
title: Payroll Result Not Calculated
requirement:
  time: 1744078763939
  blocks:
    - id: 5G1DfADUg-
      type: paragraph
      data:
        text: Payroll result not calculated
  version: 2.30.7
screen_design: null
module: PR
local_fields:
  - code: employeeId
    title: Employee ID
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: employeeRecordNumber
    title: Employee Record Number
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: fullName
    title: Full Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: company
    title: Company
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: legalEntity
    title: Legal Entity
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: paygroup
    title: Paygroup
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: employeeGroup
    title: Employee Group
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: jobIndicator
    title: Job Indicator
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: businessUnit
    title: Business Unit
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: division
    title: Division
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: position
    title: Position
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: department
    title: Department
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: calculationStatus
    title: Status
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: note
    title: Note
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: paymentDate
    title: Payment Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
  - code: action
    title: Action
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: reason
    title: Reason
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
mock_data:
  - employeeId: '094331'
    fullName: Nguyễn Văn A
    company: FIS
    legalEntity: FIS_HCM
    paygroup: ''
    businessUnit: ''
    division: ES
    department: PeopleX
    grossSalary: ''
    netSalary: ''
    status: Not Calculated
    note: Chưa chi
    paymentDate: 15/06/2024
    action: Rehire
    reason: Rehire in company
  - employeeId: '094332'
    fullName: Nguyễn Văn B
    company: FIS
    legalEntity: FIS_HCM
    paygroup: ''
    businessUnit: ''
    division: ES
    department: PeopleX
    grossSalary: ''
    netSalary: ''
    status: Not Calculated
    note: Chưa chi
    paymentDate: 15/06/2024
    action: Rehire
    reason: Rehire in company
local_buttons: null
layout: layout-table-progressing
form_config:
  fields:
    - type: group
      name: periodInformation
      label: Period information
      collapsed: false
      disableEventCollapse: true
      n_cols: 2
      fields:
        - name: payrollPeriod
          label: Payroll Period
          type: select
          readOnly: true
          placeholder: Select Payroll Period
          select:
            - label: Kỳ lương 6/2024
              value: Kỳ lương 6/2024
            - label: Kỳ lương 7/2024
              value: Kỳ lương 7/2024
            - label: Kỳ lương 8/2024
              value: Kỳ lương 8/2024
          value: Kỳ lương 6/2024
        - name: payrollSubPeriod
          label: Payroll Sub-period
          type: select
          placeholder: Select Payroll Sub-period
          readOnly: true
          select:
            - label: Lương tháng 6
              value: Lương tháng 6
            - label: Lương tháng 7
              value: Lương tháng 7
            - label: Lương tháng 8
              value: Lương tháng 8
          value: Lương tháng 6
        - name: company
          label: Company
          type: select
          readOnly: true
          placeholder: Select Company
          select:
            - label: FIS
              value: FIS
          value: FIS
        - name: legalEntity
          label: Legal Entity
          type: text
          readOnly: true
          value: FIS_HCM
        - name: elementType
          label: Element Type
          type: text
          readOnly: true
          value: Lương tháng
        - name: elementGroup
          label: Element Group
          type: text
          readOnly: true
          value: Lương tháng định kỳ
        - name: paygroup
          label: Paygroup
          type: text
          readOnly: true
          value: ''
        - name: paymentDate
          label: Payment Date
          type: text
          readOnly: true
          value: 15/06/2024
        - name: startDate
          label: Start Date
          type: text
          readOnly: true
          value: 01/06/2024
        - name: endDate
          label: End Date
          type: text
          readOnly: true
          value: 30/06/2024
        - name: version
          label: Version
          type: text
          readOnly: true
          value: '1'
        - name: reversion
          label: Reversion
          type: text
          readOnly: true
          value: '1'
        - name: segment
          label: Segment
          type: text
          readOnly: true
          value: '2'
        - name: formulaCode
          label: Formula Code
          type: text
          readOnly: true
          value: CT1
        - name: creator
          label: Creator
          type: text
          readOnly: true
          value: VyDT2
        - name: createdTime
          label: Created Time
          type: text
          readOnly: true
          value: 30/06/2024 15:09:33
        - name: updater
          label: Last Editor
          type: text
          readOnly: true
          value: VyDT2
        - name: updatedTime
          label: Last Edited Time
          type: text
          readOnly: true
          value: 30/07/2024 15:09:33
        - name: reason
          label: Reason
          type: text
          readOnly: true
          value: Rehire in company
        - name: action
          label: Action
          type: text
          readOnly: true
          value: Rehire
        - name: status
          label: Status
          type: text
          readOnly: true
          value: Not Calculated
        - name: note
          label: Note
          type: textarea
          placeholder: Input content
          autoSize:
            minRows: 2
            maxRows: 4
    - type: group
      collapsed: false
      disableEventCollapse: true
      _mode:
        transform: '''tabset'''
      fields:
        - type: group
          name: elementResult
          label: Element result
          fields:
            - type: array
              mode: table
              name: tableSecurity
              arrayOptions:
                searchBar: true
              field:
                type: group
                fields:
                  - name: elementName
                    label: Element Name
                    type: text
                    readOnly: true
                    align: start
                  - name: amount
                    label: Amount
                    type: text
                    readOnly: true
                    align: start
                  - name: description
                    label: Description
                    type: text
                    readOnly: true
                    align: start
                  - name: formula
                    label: Formula
                    type: text
                    readOnly: true
                    align: start
              value:
                - elementName: Lương theo chức danh
                  amount: 10,000,000
                  description: Lương theo chức danh
                  formula: HT_INS_Insurance_Salary
                - elementName: Khoản hỗ trợ sau thuế
                  amount: 1,200,000
                  description: Tính tổng khoản hỗ trợ cố định sau thuế nhân viên được hưởng
                  formula: HT_INS_Insurance_Salary
filter_config:
  fields:
    - type: selectAll
      label: Legal Entity
      labelType: type-grid
      isLazyLoad: true
      name: legalEntity
      mode: multiple
      placeholder: Select Legal Entity
      _options:
        transform: >-
          $legalEntityList($.extend.limit, $.extend.page, $.extend.search,
          $.extend.addOnValue.dataDetail.companyCode)
    - type: selectAll
      label: Business Unit
      labelType: type-grid
      isLazyLoad: true
      name: businessUnit
      mode: multiple
      placeholder: Select Bussiness Unit
      _options:
        transform: >-
          $businessUnitList($.extend.limit, $.extend.page, $.extend.search,
          $.extend.addOnValue.dataDetail.companyCode)
    - name: division
      label: Division
      labelType: type-grid
      isLazyLoad: true
      type: selectAll
      placeholder: Select Division
      mode: multiple
      _options:
        transform: >-
          $divisionsList($.extend.limit, $.extend.page,
          $.extend.search,$.extend.addOnValue.dataDetail.companyCode,$map($.fields.businessUnit
          , function($v) {$v.id}))
    - name: department
      label: Department
      labelType: type-grid
      type: selectAll
      placeholder: Select Department
      mode: multiple
      isLazyLoad: true
      _options:
        transform: >-
          $departmentList($.extend.limit, $.extend.page,
          $.extend.search,$.extend.addOnValue.dataDetail.companyCode
          ,$map($.fields.legalEntity , function($v) {$v.id}))
    - name: payrollStatus
      label: Payroll Status
      labelType: type-grid
      type: selectAll
      mode: multiple
      placeholder: Select Period Status
      options:
        - label: Not calculated
          value: NotCalculated
        - label: Failed
          value: Failed
        - label: Locked
          value: Locked
        - label: Completed
          value: Completed
        - label: Processing
          value: Processing
    - name: jobTitle
      label: Job Title
      isLazyLoad: true
      type: selectAll
      labelType: type-grid
      mode: multiple
      placeholder: Select Job Title
      _options:
        transform: >-
          $jobCodesList($.extend.limit, $.extend.page, $.extend.search,
          $.extend.addOnValue.dataDetail.companyCode)
    - name: position
      label: Position
      isLazyLoad: true
      labelType: type-grid
      type: selectAll
      mode: multiple
      placeholder: Select Position
      _options:
        transform: >-
          $positionsList($.extend.limit, $.extend.page, $.extend.search,
          $.extend.addOnValue.dataDetail.companyCode)
    - name: location
      label: Location
      labelType: type-grid
      type: selectAll
      placeholder: Select Location
      mode: multiple
      _options:
        transform: >-
          $locationsList($.extend.limit, $.extend.page, $.extend.search,
          $.extend.addOnValue.dataDetail.companyCode)
    - name: employeeGroup
      label: Employee Group
      type: selectAll
      placeholder: Select Employee Group
      labelType: type-grid
      mode: multiple
      _options:
        transform: $employeeGroupsList()
    - name: contractType
      label: Contract Type
      type: selectAll
      placeholder: Select Contract Type
      labelType: type-grid
      mode: multiple
      _options:
        transform: $contractTypeList()
    - name: employeeLevel
      label: Employee Level
      type: selectAll
      placeholder: Select Employee Level
      labelType: type-grid
      mode: multiple
      _options:
        transform: $empLevelList()
  filterMapping:
    - field: businessUnitCode
      operator: $in
      valueField: businessUnit.(value)
    - field: legalEntityCode
      operator: $in
      valueField: legalEntity.(value)
    - field: divisionCode
      operator: $in
      valueField: division.(value)
    - field: departmentCode
      operator: $in
      valueField: department.(value)
    - field: calculationStatus
      operator: $in
      valueField: payrollStatus.(value)
    - field: jobTitleCode
      operator: $in
      valueField: jobTitle.(value)
    - field: positionCode
      operator: $in
      valueField: position.(value)
    - field: locationCode
      operator: $in
      valueField: location.(value)
    - field: employeeGroupCode
      operator: $in
      valueField: employeeGroup.(value)
    - field: contractTypeCode
      operator: $in
      valueField: contractType.(value)
    - field: employeeLevelCode
      operator: $in
      valueField: employeeLevel.(value)
  sources:
    empLevelList:
      uri: '"/api/picklists/EMPLEVEL/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})
      disabledCache: true
    contractTypeList:
      uri: '"/api/picklists/CONTRACTTYPE/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})
      disabledCache: true
    employeeGroupsList:
      uri: '"/api/employee-groups"'
      method: GET
      queryTransform: '{''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.employeeGroup.default,
        'value': $item.code}})
      disabledCache: true
    locationsList:
      uri: '"/api/locations"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'CompanyCode','operator':
        '$eq','value':$.companyCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.id,  'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - companyCode
    positionsList:
      uri: '"/api/positions/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search ,
        'filter':[{'field':'companyCode','operator': '$eq','value':
        $.companyCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.id,  'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - companyCode
    jobCodesList:
      uri: '"/api/job-codes/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search ,
        'filter':[{'field':'companyCode','operator': '$eq','value':
        $.companyCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - companyCode
    legalEntityList:
      uri: '"/api/legal-entities"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search ,
        'filter':[{'field':'companyCode','operator':
        '$eq','value':$.companyCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default, 'value':
        $item.code , 'id' : $item.id}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - companyCode
    businessUnitList:
      uri: '"/api/business-units"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search ,
        'filter':[{'field':'companyCode','operator':
        '$eq','value':$.companyCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default, 'value':
        $item.code , 'id' : $item.id}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - companyCode
    divisionsList:
      uri: '"/api/divisions"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page,'search': $.search ,'filter':
        [{'field':'companyCode','operator':
        '$eq','value':$.companyCode},{'field':'businessUnitId','operator':
        '$in','value':$.businessUnitIds}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.id, 'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - companyCode
        - businessUnitIds
    departmentList:
      uri: '"/api/departments"'
      queryTransform: >-
        {'limit': $.limit, 'page': $.page,'search': $.search , 'filter':
        [{'field':'companyCodeFilter','operator':
        '$eq','value':$.companyCode},{'field':'legalEntityId','operator':
        '$in','value':$.legalEntityIds}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - companyCode
        - legalEntityIds
  variables: {}
layout_options:
  tab_calculation_status: NotCalculated
  show_detail_history: false
  show_actions_delete: false
  show_create_data_table: false
  customStyleContent:
    padding: 20px 0 0 0
  checkNoteBackendUrl: /api/payroll-employees/check-note-re-calculate-salary
  checkEmployeeBackendUrl: /api/payroll-employees/has-employee-tab
  custom_export_api:
    _url:
      transform: '''/api/payroll-employees/export-static-tab'''
  tool_table:
    - id: note
      icon: note
      title: Note
      backendUrl: /api/payroll-employees/set-note-paymentdate
      _disabled:
        transform: $.parentData.periodStatus = 'Finalized'
    - id: reCalculate
      icon: play
      title: Run
      backendUrl: /api/payroll-employees/re-calculate-salary
      _disabled:
        transform: $.parentData.periodStatus = 'Finalized'
    - id: export
      icon: file-arrow-down
  page_header_options:
    visible: false
  is_show_length_pagination: true
  disabled_click_row: true
  row_actions_handler:
    note:
      action: edit
      _update_fields: >-
        {'payrollPeriodSettingCode': $.payrollPeriodSettingCode,
        'payrollPeriodCode': $.payrollPeriodCode, 'employeeId': $.employeeId,
        'employeeRecordNumber': $.employeeRecordNumber}
      backendUrl: /api/payroll-employees/set-note-paymentdate
      keyValue:
        calculationStatus: NotCalculated
    reCalculate:
      action: edit
      _update_fields: >-
        {'payrollPeriodSettingCode': $.payrollPeriodSettingCode,
        'payrollPeriodCode': $.payrollPeriodCode, 'employeeId': $.employeeId,
        'employeeRecordNumber': $.employeeRecordNumber}
      backendUrl: /api/payroll-employees/re-calculate-salary
      keyValue:
        calculationStatus: NotCalculated
layout_options__header_buttons: null
options: null
create_form: null
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: note
    icon: icon-notebook-bold
    type: ghost-gray
    href: /api/payroll-employees/set-note-paymentdate
backend_url: /api/payroll-employees/get-for-payroll-calculation
screen_name: null
layout_options__actions_many:
  - id: export
    title: Export Selected
    icon: icon-upload-simple-bold
    type: secondary
parent: PR.FS.FR.054_detail
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter:
  - name: payrollPeriodSettingCode
    fieldValue: parent.code
    operator: $eq
  - name: calculationStatus
    fieldValue: '"NotCalculated"'
    operator: $eq
  - name: isSalaried
    fieldValue: 'true'
    operator: $eq
pre_condition_config: {}
permission_key: null
screen_designs: []
function_specs: []
fields: []
children: []
menu_item: null
