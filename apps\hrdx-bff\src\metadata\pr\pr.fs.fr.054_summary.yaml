id: PR.FS.FR.054_summary
status: draft
sort: 0
user_created: 78a455fe-2ca9-4621-b542-98e98ebfefe8
date_created: '2024-08-10T09:06:35.531Z'
user_updated: b403d5da-d684-45e4-ae6d-e25223baedc5
date_updated: '2025-06-24T04:14:49.105Z'
title: Payroll Result Summary
requirement:
  time: 1747041819839
  blocks:
    - id: 4haaE5nPhP
      type: paragraph
      data:
        text: Payroll result summary
  version: 2.30.7
screen_design: null
module: PR
local_fields:
  - code: paymentDate
    title: Payment Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
mock_data: null
local_buttons: null
layout: layout-form
form_config:
  footer:
    create: true
    update: true
    updatedOn: updatedAt
    updatedBy: updatedBy
  fields:
    - type: group
      label: Period Information
      collapse: false
      disableEventCollapse: true
      fieldGroupTitleStyle:
        border: none
      n_cols: 3
      fields:
        - type: text
          name: isImport
          unvisible: true
        - type: text
          name: country
          label: Country
          readOnly: true
        - type: text
          name: payrollPeriod
          label: Payroll Period
          readOnly: true
        - type: text
          name: payrollSubPeriod
          label: Payroll Sub-period
          readOnly: true
        - type: text
          name: company
          label: Company
          readOnly: true
        - type: text
          name: legalEntity
          label: Legal Entity
          readOnly: true
        - type: text
          name: payGroup
          label: Paygroup
          readOnly: true
        - type: text
          name: elementGroup
          label: Element Group
          readOnly: true
        - type: text
          name: elementType
          label: Element Type
          readOnly: true
        - type: dateRange
          name: paymentDate
          label: Payment Date
          readOnly: true
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
        - type: dateRange
          name: startDate
          label: Start Date
          readOnly: true
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
        - type: dateRange
          name: endDate
          label: End Date
          readOnly: true
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
    - type: group
      n_cols: 3
      label: Detail Information
      disableEventCollapse: true
      collapse: false
      fields:
        - type: text
          name: totalEmployee
          label: Total Calculation
          readOnly: true
        - type: text
          name: totalFail
          label: Total Fail
          readOnly: true
        - type: text
          name: totalNotCalculated
          label: Total Not Calculated
          readOnly: true
        - type: text
          name: totalProcessing
          label: Total Processing
          readOnly: true
        - type: text
          name: totalCompleted
          label: Total Completed
          readOnly: true
        - type: text
          name: totalLocked
          label: Total Locked
          readOnly: true
        - type: text
          name: currency
          label: Currency
          readOnly: true
        - type: text
          name: version
          label: Version
          readOnly: true
        - type: text
          name: revision
          label: Revision
          readOnly: true
        - type: text
          name: periodStatus
          label: Status
          readOnly: true
          displaySetting:
            type: Tag
            extraConfig:
              size: small
              tags:
                - value: NotCalculated
                  label: Not Calculated
                  class: infor
                - value: Processing
                  label: Processing
                  style:
                    background_color: '#FEF9CC'
                - value: Finalized
                  label: Finalized
                  style:
                    background_color: '#F3EAFB'
                - value: Locked
                  label: Locked
                  class: default
                - value: Failed
                  label: Failed
                  class: error
                - value: Completed
                  label: Completed
                  class: success
filter_config: {}
layout_options:
  page_footer_options:
    visible: true
    show_credit: true
  page_header_options:
    visible: false
  customStyleContent:
    padding: 8px 0 0 0
    background: '#fff'
  customStyleFormWrapper:
    padding: 0px 0px 16px
layout_options__header_buttons: null
options: null
create_form: null
layout_options__footer_buttons:
  - id: hidden
    type: link
layout_options__row_actions: null
backend_url: /api/salary-formula-payroll-period-settings/:id1
screen_name: null
layout_options__actions_many: null
parent: PR.FS.FR.054_detail
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: code
default_filter: null
pre_condition_config: {}
permission_key: null
screen_designs: []
function_specs: []
fields: []
children: []
menu_item: null
