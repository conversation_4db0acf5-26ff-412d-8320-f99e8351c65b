id: PR.FS.FR.066
status: draft
sort: 302
user_created: 310eb2e3-16b6-4a27-b1f8-54b45c3691be
date_created: '2024-07-22T06:31:30.780Z'
user_updated: b403d5da-d684-45e4-ae6d-e25223baedc5
date_updated: '2025-07-28T04:58:13.923Z'
title: Manage Other Income Information
requirement:
  time: 1747650324934
  blocks:
    - id: snFn5FWeFB
      type: paragraph
      data:
        text: >
          - Hi<PERSON><PERSON> thị danh sách các khoản thu nhập khác của hồ sơ nhân viên theo
          thứ tự có ngày hiệu lực mới nhất đến giảm dần.
    - id: '-pqPYtZzg-'
      type: paragraph
      data:
        text: >
          - <PERSON><PERSON> thống tự động đồng bộ dữ liệu qua lại giữa ESS và chức năng quản
          lý thông tin thu nhập khác FSD - PR.FS.PR.099 (<PERSON><PERSON><PERSON><PERSON> lý thông tin các
          khoản thu nhập kh<PERSON> (những khoản cộng vào thu nhập để quyết toán thuế
          - ESS))
    - id: 0D7g-FywNS
      type: paragraph
      data:
        text: >
          - Hệ thống ghi nhận nguồn tạo dữ liệu để bộ phận nhân sự thực hiện tra
          cứu khi tìm kiếm.
    - id: j_SJAn_tqQ
      type: paragraph
      data:
        text: >
          - Hệ thống cho phép phân loại đối với từng khoản thu nhập khách:phân
          loại (trước thuế hay sau thuế và có tính thuế không nếu là trước thuế)
          để tự động xử lý tính toán ra số mức hưởng tương ứng.
    - id: pEsEJkthZq
      type: paragraph
      data:
        text: >-
          - Bộ phận nhân sự CTTV có thể thực hiện điều chỉnh đối với các khoản
          thu nhập khác đã nhập trên hệ thống với điều kiện các khoản thu nhập
          khác chưa được dùng để tính lương hoặc tính lương bổ sung

          Lưu ý: Trường hợp trong kỳ tính lương, nhân viên được hưởng cùng một
          khoản thu nhập khác nhiều lần với các mã instance tự sinh khác nhau để
          phân biệt
  version: 2.30.7
screen_design: null
module: PR
local_fields:
  - code: employeeId
    pinned: true
    title: Employee ID
    description: '-Hiển thị thông tin Mã nhân viên theo bản ghi tương ứng'
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    show_sort: true
  - code: fullName
    title: Full Name
    description: '-Hiển thị thông tin Tên nhân viên theo bản ghi tương ứng'
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: employeeRecordNumber
    title: Employee Number Record
    description: '-Hiển thị thông tin Mã hồ sơ công việc theo bản ghi tương ứng'
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 15
    show_sort: true
  - code: employeeGroup
    title: Employee Group
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: company
    title: Company
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: legalEntity
    title: Legal Entity
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: businessUnit
    title: Business Unit
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: division
    title: Division
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: department
    title: Department
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: location
    title: Location
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: typeName
    title: Other Type
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: otherIncomeGroup
    title: Other Income Group
    description: >-
      -Hiển thị thông tin Nhóm khoản thu nhập khác của nhân viên theo bản ghi
      tương ứng
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 15
    show_sort: true
  - code: otherIncomeTypeName
    title: Other Income Type
    description: '-Hiển thị thông tin Tên khoản thu nhập khác theo bản ghi tương ứng'
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: otherIncomeWithCurrency
    title: Other Income Amount
    description: '-Hiển thị thông tin Khoản thu nhập khác theo bản ghi tương ứng'
    data_type:
      key: Money Amount
      collection: data_types
    display_type:
      key: Currency
      collection: field_types
    show_sort: false
  - code: personalIncomeTaxWithCurrency
    title: Personal Income Tax
    description: '-Hiển thị thông tin Thuế thu nhập các nhân theo bản ghi tương ứng'
    data_type:
      key: Money Amount
      collection: data_types
    display_type:
      key: Currency
      collection: field_types
    show_sort: false
  - code: exchangeRate
    title: Exchange Rate
    description: '-Hiển thị thông tinTỶ giá theo bản ghi tương ứng'
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: false
  - code: paymentDate
    title: Payment Date
    description: '-Hiển thị thông tin Ngày chi trả theo bản ghi tương ứng'
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/YYYY
      collection: field_types
    show_sort: true
  - code: note
    title: Note
    description: '-Hiển thị thông tin Ghi chú theo bản ghi tương ứng'
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: dataSource
    title: Data Source
    description: '-Hiển thị thông tin Nguồn dữ liệu theo bản ghi tương ứng'
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: updatedBy
    title: Last Updated By
    description: '-Hiển thị thông tin Người cập nhật mới nhất theo bản ghi tương ứng'
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: updatedAt
    title: Last Updated On
    description: '-Hiển thị thông tin Ngày cập nhật mới nhất theo bản ghi tương ứng'
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
    show_sort: true
mock_data:
  - employeeId: '0000000001'
    fullName: Nguyen Van A
    otherIncomeCode: OIC001
    otherIncomeName: Thưởng hiệu quả kinh doanh
    employeeNumberRecord: '1'
    company: Company A
    legalEntity: Entity A
    businessUnit: Unit A
    division: Division A
    department: Department A
    location: Hanoi
    instanceId: '1'
    otherIncomeGroupType: Perfomance Bonus
    otherIncome: Thưởng hiệu quả kinh doanh
    otherIncomeAmount: 1.000.000
    nonTaxIncome: 0
    personalIncomeTax: '100.000'
    netAmount: '900.000'
    exchangeRate: null
    currency: VND
    declarationPeriod: 2024-05
    paymentDate: 05/02/2024
    note:
      default: Content
      english: Content
      vietnamese: Nội dung
    source: HR System
    createdTime: 05/07/2024 10:00:00
    creator: Admin
    lastEditor: Admin
    lastEdit: 01/07/2024 10:00:00
  - employeeId: '0000000002'
    fullName: Tran Thi B
    otherIncomeCode: OIC002
    otherIncomeName: Thưởng hiệu quả kinh doanh
    employeeNumberRecord: '2'
    company: Company B
    legalEntity: Entity B
    businessUnit: Unit B
    division: Division B
    department: Department B
    location: Ho Chi Minh City
    instanceId: '1'
    otherIncomeGroupType: Perfomance Bonus
    otherIncome: Thưởng hiệu quả kinh doanh
    otherIncomeAmount: 1.000.000
    nonTaxIncome: 0
    personalIncomeTax: '100.000'
    netAmount: '900.000'
    exchangeRate: null
    currency: VND
    declarationPeriod: 2024-05
    paymentDate: 05/02/2024
    note:
      default: Content
      english: Content
      vietnamese: Nội dung
    source: HR System
    createdTime: 01/07/2024 10:00:00
    creator: Admin
    lastEditor: User1
    lastEdit: 02/07/2024 10:00:00
local_buttons: null
layout: layout-table
form_config:
  formTitle:
    create: Add New Other Income
    edit: 'Edit: Other Income'
    view: Other Income Details
  defaultValidateError: Required fields have not been filled in completely (fields marked with *)
  fields:
    - type: select
      name: employee
      label: Employee
      placeholder: Select Employee
      isLazyLoad: true
      validators:
        - type: required
      _select:
        transform: >-
          $employeesList($.extend.limit, $.extend.page, $.extend.search, null,
          null, null, $.fields.paymentDate)
      _validateFn:
        transform: >-
          $not($isNilorEmpty($.value.employeeId)) ? (
          $isNilorEmpty($employeesList(1, 1,'',
          $.value.employeeId,null,$.value.employeeRecordNumber,
          $.fields.paymentDate)[0]) ?  '_setSelectValueNull' ) 
      _condition:
        transform: $not($not($.extend.formType = 'create'))
      outputValue: value
    - type: select
      name: employee
      isLazyLoad: true
      label: Employee
      placeholder: Select Employee
      validators:
        - type: required
      _select:
        transform: >-
          $employeesList($.extend.limit, $.extend.page, $.extend.search,
          null,null,null, $.fields.paymentDate)
      _condition:
        transform: $.extend.formType = 'edit'
      outputValue: value
      _disabled:
        transform: 'true'
    - type: text
      name: employeeIdView
      label: Employee ID
      _condition:
        transform: $.extend.formType = 'view'
      _value:
        transform: >-
          $join($filter([$.extend.defaultValue.employeeId,$.extend.defaultValue.employeeGroupCode,$string($.extend.defaultValue.employeeRecordNumber),$.extend.defaultValue.fullName],
          $boolean), ' - ')
    - type: text
      name: dateToShowEmployee
      unvisible: true
      _value:
        transform: >-
          $not($isNilorEmpty($.fields.paymentDate)) ?
          $DateToTimestampUTC($.fields.paymentDate) :
          $DateToTimestampUTC($now())
    - type: text
      name: dataEmployee
      unvisible: true
      dependantField: $.fields.employeeId; $.fields.paymentDate
      _value:
        transform: >-
          $not($isNilorEmpty($.fields.employeeId)) ? {'employeeId':
          $.fields.employeeId , 'employeeRecordNumber':
          $.fields.employeeRecordNumber, 'dateToShowEmployee':
          $.fields.dateToShowEmployee} : null
    - type: text
      name: employeeId
      dependantField: $.fields.employee
      label: Employee ID
      unvisible: true
      _value:
        transform: >-
          $not($isNilorEmpty($.fields.employee)) ? $.fields.employee.employeeId
          : null
    - type: number
      name: employeeRecordNumber
      label: Employee Record Number
      unvisible: true
      _value:
        transform: $.fields.employee.employeeRecordNumber
    - type: group
      label: ' '
      readOnly: true
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - type: text
          label: Company
          name: company
          _value:
            transform: >-
              $not($isNilorEmpty($.extend.defaultValue.company)) ?
              ($.extend.defaultValue.company & ' (' &
              $.extend.defaultValue.companyCode & ')')
        - type: text
          label: Legal Entity
          name: legalEntity
          _value:
            transform: >-
              $not($isNilorEmpty($.extend.defaultValue.legalEntity)) ?
              ($.extend.defaultValue.legalEntity & ' (' &
              $.extend.defaultValue.legalEntityCode & ')')
        - type: text
          label: Business Unit
          name: businessUnit
          _value:
            transform: >-
              $not($isNilorEmpty($.extend.defaultValue.businessUnit)) ?
              ($.extend.defaultValue.businessUnit & ' (' &
              $.extend.defaultValue.businessUnitCode & ')')
        - type: text
          label: Division
          name: division
          _value:
            transform: >-
              $not($isNilorEmpty($.extend.defaultValue.division)) ?
              ($.extend.defaultValue.division & ' (' &
              $.extend.defaultValue.divisionCode & ')')
        - type: text
          label: Department
          name: department
          _value:
            transform: >-
              $not($isNilorEmpty($.extend.defaultValue.department)) ?
              ($.extend.defaultValue.department & ' (' &
              $.extend.defaultValue.departmentCode & ')')
        - type: text
          label: Location
          name: location
          _value:
            transform: >-
              $not($isNilorEmpty($.extend.defaultValue.location)) ?
              ($.extend.defaultValue.location & ' (' &
              $.extend.defaultValue.locationCode & ')')
    - type: dateRange
      label: Payment Date
      name: paymentDate
      mode: date-picker
      _value:
        transform: $.extend.formType = 'create' ? $now()
      placeholder: dd/MM/yyyy
      setting:
        type: day
        format: dd/MM/yyyy
      validators:
        - type: required
      _condition:
        transform: $not($.extend.formType = 'view')
    - type: dateRange
      label: Payment Date
      name: paymentDate
      mode: date-picker
      setting:
        type: day
        format: dd/MM/yyyy
      _condition:
        transform: ($.extend.formType = 'view')
    - type: group
      n_cols: 2
      fields:
        - type: select
          label: Other Type
          name: typeCode
          placeholder: Select Other Type
          outputValue: value
          _value:
            transform: >-
              $not($isNilorEmpty($.fields.otherIncomeType)) ?
              $.fields.otherIncomeType.typeCode
          _condition:
            transform: $not($.extend.formType = 'view')
          _select:
            transform: $otherTypeList()
        - type: select
          label: Other Income Type
          placeholder: Select Other Income Type
          isLazyLoad: true
          name: otherIncomeType
          clearFieldsAfterChange:
            - currencyCodeCreate
            - otherIncomeGroupCode
          outputValue: value
          _value:
            transform: >-
              $not($isNilorEmpty($.extend.defaultValue.otherIncomeCode)) ?
              $otherIncomeList(1,1,'','',$.extend.defaultValue.otherIncomeCode)[0]
          _select:
            transform: >-
              $otherIncomeList($.extend.limit, $.extend.page, $.extend.search,
              $.fields.typeCode)
          _condition:
            transform: $not($.extend.formType = 'view')
          _validateFn:
            transform: >-
              $exists($.value.code) ?
              ($otherIncomeList(1,1,'',$.fields.typeCode,$.value.code)[0] ?
              $otherIncomeList(1,1,'',$.fields.typeCode,$.value.code)[0] :
              '_setSelectValueNull')
          validators:
            - type: required
        - type: text
          name: currencyCodeCreate
          unvisible: true
          _value:
            transform: >-
              $not($isNilorEmpty($.fields.otherIncomeType.currencyCode)) ?
              $.fields.otherIncomeType.currencyCode : 'VND'
        - type: text
          name: currencyCodeEdit
          dependantFieldSkip: 2
          dependantField: $.fields.otherIncomeType.currencyCode
          unvisible: true
          _value:
            transform: >-
              $isNilorEmpty($.fields.currencyCodeEdit) ?
              ($not($isNilorEmpty($.fields.otherIncomeType.currencyCode)) ?
              $.fields.otherIncomeType.currencyCode : 'VND')
        - type: text
          name: personalIncomeTaxCurrencyCodeEdit
          dependantFieldSkip: 2
          dependantField: $.fields.otherIncomeType.currencyCode
          unvisible: true
          _value:
            transform: >-
              $isNilorEmpty($.fields.personalIncomeTaxCurrencyCodeEdit) ?
              ($not($isNilorEmpty($.fields.otherIncomeType.currencyCode)) ?
              $.fields.otherIncomeType.currencyCode : 'VND')
        - type: select
          label: Other Income Group
          name: otherIncomeGroupCode
          isLazyLoad: true
          outputValue: value
          _value:
            transform: >-
              $not($isNilorEmpty($.fields.otherIncomeType)) ?
              $otherIncomeGroupList($.fields.otherIncomeType.otherIncomeGroup)[0]
              : '_setSelectValueNull'
          _condition:
            transform: $not($.extend.formType = 'view')
          _disabled:
            transform: 'true'
        - type: currency
          name: otherIncomeAmount
          label: Other Income Amount
          placeholder: Enter Other Income Amount
          _value:
            transform: $.extend.formType = 'create' ? 0
          addOnAfter:
            type: select
            name: currencyCode
            width: 128px
            outputValue: value
            placeholder: Select Currency
            _select:
              transform: $.variables._currencyList
            _value:
              skipWhenClear: true
              transform: >-
                $.extend.formType = 'create' ? $.fields.currencyCodeCreate :
                $.fields.currencyCodeEdit
            validators:
              - type: required
          validators:
            - type: required
          _condition:
            transform: $not($.extend.formType = 'view')
          number:
            format: currency
            min: 0
            max: '99999999999999.9999'
            precision: 4
          displayType: Currency
        - type: currency
          name: personalIncomeTax
          label: Personal Income Tax
          placeholder: Enter Personal Income Tax
          addOnAfter:
            type: select
            width: 128px
            name: personalIncomeTaxCurrencyCode
            outputValue: value
            placeholder: Select Currency
            _select:
              transform: $.variables._currencyList
            _value:
              skipWhenClear: true
              transform: >-
                $.extend.formType = 'create' ? $.fields.currencyCodeCreate :
                $.fields.personalIncomeTaxCurrencyCodeEdit
          validators:
            - type: required
          _condition:
            transform: $not($.extend.formType = 'view')
          number:
            format: currency
            max: '99999999999999.9999'
            min: 0
            precision: 4
          displayType: Currency
          _value:
            transform: $.extend.formType = 'create' ? 0
        - type: number
          name: exchangeRate
          placeholder: Enter Exchange Rate
          _condition:
            transform: $not($.extend.formType = 'view')
          number:
            min: 0
          _value:
            transform: $.extend.formType = 'create' ? 1
          label: Exchange Rate
    - type: text
      label: Other Type
      name: typeName
      _condition:
        transform: $.extend.formType = 'view'
    - type: text
      label: Other Income Type
      name: otherIncomeTypeName
      _condition:
        transform: $.extend.formType = 'view'
    - type: text
      label: Other Income Group
      name: otherIncomeGroup
      _condition:
        transform: $.extend.formType = 'view'
    - type: number
      name: otherIncomeAmount
      label: Other Income Amount
      _condition:
        transform: $.extend.formType = 'view'
      number:
        _suffix:
          transform: $.extend.defaultValue.currencyCode
        format: currency
        precision: 4
    - type: number
      name: personalIncomeTax
      label: Personal Income Tax
      _condition:
        transform: $.extend.formType = 'view'
      number:
        _suffix:
          transform: $.extend.defaultValue.personalIncomeTaxCurrencyCode
        format: currency
        precision: 4
    - type: number
      name: exchangeRate
      number:
        min: 0
      _condition:
        transform: $.extend.formType = 'view'
      _value:
        transform: $.extend.formType = 'create' ? 1
      label: Exchange Rate
    - type: translationTextArea
      label: Note
      name: note
      placeholder: Enter Note
      textarea:
        autoSize:
          minRows: 3
        maxCharCount: 1000
      validators:
        - type: maxLength
          args: 1000
  overview:
    border: true
    dependentField: dataEmployee
    noDataMessages: Choose Employee ID getting data
    header: Employee Detail
    uri: >-
      /api/pr-employees/:{employeeId}:/employee-record-number/:{employeeRecordNumber}:/effective-date/:{dateToShowEmployee}:
    display:
      - key: companyName
        label: Company
      - key: legalEntityName
        label: Legal entity
      - key: businessUnitName
        label: Business unit
      - key: divisionName
        label: Division
      - key: departmentName
        label: Department
      - key: jobTitleName
        label: Job Title
      - key: contractTypeName
        label: Contract Type
      - key: locationName
        label: Location
  sources:
    employeesList:
      uri: '"/api/pr-employees"'
      method: GET
      queryTransform: >-
        {'limit': $.limit ,'page': $.page, 'search': $.search,'filter':
        [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'employeeId','operator':
        '$eq','value':$.empId},{'field':'employeeGroupCode','operator':
        '$cont','value':$.grpCode},{'field':'employeeRecordNumber','operator':
        '$eq','value':$.ern}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label':
        $join($filter([$item.employeeId,$item.employeeGroupCode,$string($item.employeeRecordNumber),$item.fullName],
        $boolean), ' - ')  , 'value': {'employeeId': $item.employeeId, 'code':
        $item.employeeId, 'employeeRecordNumber':
        $string($item.employeeRecordNumber), 'jobDataId': $item.jobDataId}}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - empId
        - grpCode
        - ern
        - effectiveDate
    taxPeriodList:
      uri: '"/api/assessment-period"'
      method: GET
      queryTransform: '{ ''limit'': 999 }'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default, 'value':
        $item.code}})[]
      disabledCache: true
    otherIncomeList:
      uri: '"/api/other-incomes"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator':
        '$eq','value':true},{'field':'code','operator':
        '$eq','value':$.code},{'field':'typeCode','operator':
        '$eq','value':$.typeCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default,
        'value':{'code' : $item.code , 'otherIncomeGroup' :
        $item.otherIncomeGroupCode , 'currencyCode':  $item.currencyCode ,
        'typeCode': $item.typeCode}}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - typeCode
        - code
    otherIncomeGroupList:
      uri: '"/api/other-income-group"'
      method: GET
      queryTransform: >-
        {'limit': 1,'filter': [{'field':'status','operator':
        '$eq','value':true},{'field':'code','operator': '$eq','value':$.code}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - code
    currencyList:
      uri: '"/api/picklists/CURRENCY/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.code, 'value':
        $item.code}})[]
      disabledCache: true
    otherTypeList:
      uri: '"/api/picklists/OTHERTYPEINCOME/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params: []
  variables:
    _taxPeriodList:
      transform: $taxPeriodList()
    _jobId:
      transform: $.fields.employee.jobDataId
    _elementsList:
      transform: >-
        $.fields.type = 'DED' ? $deductionsList($.fields.startDate) :
        $unFixedAllowancesList($.fields.startDate)
    _fullName:
      transform: >-
        $filter($.variables._personalsList, function($v) { $v.value =
        $.fields.employeeId })[0]
    _ern:
      transform: $.fields.employee.employeeRecordNumber
    _employee:
      transform: >-
        $employeesList(1,1,'',$.extend.defaultValue.employeeId,$.extend.defaultValue.employeeGroupCode,$.extend.defaultValue.employeeRecordNumber)
    _dataEdit:
      transform: $.variables._employee[0].value
    _currencyList:
      transform: $currencyList()
  footer:
    update: true
    updatedOn: updatedAt
    updatedBy: updatedBy
filter_config:
  fields:
    - type: selectAll
      name: employeeId
      isLazyLoad: true
      label: Employee ID
      labelType: type-grid
      placeholder: Select Employee
      outputValue: value
      _options:
        transform: $employeesList($.extend.limit, $.extend.page, $.extend.search)
    - type: text
      labelType: type-grid
      label: FullName
      name: fullName
    - type: selectAll
      name: employeeGroupCode
      isLazyLoad: true
      label: Employee Group
      labelType: type-grid
      placeholder: Select Employee Group
      outputValue: value
      _options:
        transform: $employeeGroupsList($.extend.limit, $.extend.page, $.extend.search)
    - type: text
      labelType: type-grid
      label: Employee Record Number
      name: employeeRecordNumber
    - name: company
      label: Company
      type: selectAll
      labelType: type-grid
      mode: multiple
      placeholder: Select Company
      isLazyLoad: true
      _options:
        transform: $companiesList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      label: Legal Entity
      isLazyLoad: true
      name: legalEntityCode
      labelType: type-grid
      mode: multiple
      placeholder: Select Legal Entity
      _options:
        transform: $legalEntitiesList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      isLazyLoad: true
      label: Bussiness Unit
      name: businessUnit
      labelType: type-grid
      mode: multiple
      placeholder: Select Bussiness Unit
      _options:
        transform: $businessUnitList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      name: divisionCode
      isLazyLoad: true
      label: Division
      labelType: type-grid
      placeholder: Select Division
      mode: multiple
      _options:
        transform: >-
          $divisionsList($DateFormat($now(),'YYYY-MM-DDTHH:mm:ss'),$.extend.limit,
          $.extend.page, $.extend.search)
    - type: selectAll
      name: departmentCode
      label: Department
      labelType: type-grid
      placeholder: Select Department
      mode: multiple
      isLazyLoad: true
      _options:
        transform: >-
          $departmentsList($.extend.limit,
          $.extend.page,$DateFormat($now(),'YYYY-MM-DDTHH:mm:ss'),
          $.extend.search)
    - type: selectAll
      label: Location
      name: locationCode
      labelType: type-grid
      placeholder: Select Location
      isLazyLoad: true
      mode: multiple
      _options:
        transform: $locationsList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      label: Other Type
      name: typeCode
      labelType: type-grid
      placeholder: Select Other Type
      isLazyLoad: true
      mode: multiple
      _options:
        transform: $otherTypeList($.extend.limit,$.extend.page,$.extend.search)
    - type: selectAll
      label: Other Income
      name: otherIncomeName
      labelType: type-grid
      placeholder: Select Other Income Name
      isLazyLoad: true
      mode: multiple
      _options:
        transform: $otherIncomeList($.extend.limit,$.extend.page,$.extend.search)
    - type: selectAll
      name: otherIncomeGroupType
      label: Other Income Group
      labelType: type-grid
      placeholder: Select Other Income Group
      mode: multiple
      _options:
        transform: $otherIncomeGroupList($.extend.limit, $.extend.page, $.extend.search)
      isLazyLoad: true
    - type: number
      name: exchangeRate
      label: Exchange Rate
      labelType: type-grid
      placeholder: Enter Exchange Rate
    - type: dateRange
      label: Payment Date
      labelType: type-grid
      name: paymentDate
      placeholder: dd/MM/yyyy
    - type: text
      label: Note
      labelType: type-grid
      name: note
      placeholder: Enter Note
    - type: selectAll
      name: dataSource
      labelType: type-grid
      label: Data Resource
      placeholder: Select option
      mode: multiple
      options:
        - value: Manual
          label: Manual
        - value: Import
          label: Import
    - name: updatedBy
      label: Last Updated By
      labelType: type-grid
      type: text
      placeholder: Enter Last Updated By
    - type: dateRange
      name: updatedAt
      labelType: type-grid
      label: Last Edited Time
      settings:
        format: dd/MM/yyyy
        mode: date
  filterMapping:
    - field: employeeId
      operator: $in
      valueField: employeeId
    - field: fullName
      operator: $cont
      valueField: fullName
    - field: employeeRecordNumber
      operator: $eq
      valueField: employeeRecordNumber
    - field: employeeGroupCode
      operator: $in
      valueField: employeeGroupCode
    - field: companyCode
      operator: $in
      valueField: company.(value)
    - field: legalEntityCode
      operator: $in
      valueField: legalEntityCode.(value)
    - field: businessUnitCode
      operator: $in
      valueField: businessUnit.(value)
    - field: divisionCode
      operator: $in
      valueField: divisionCode.(value)
    - field: departmentCode
      operator: $in
      valueField: departmentCode.(value)
    - field: locationCode
      operator: $in
      valueField: locationCode.(value)
    - field: typeCode
      operator: $in
      valueField: typeCode.(value)
    - field: otherIncomeCode
      operator: $in
      valueField: otherIncomeName.(value)
    - field: otherIncomeGroupCode
      operator: $in
      valueField: otherIncomeGroupType.(value)
    - field: exchangeRate
      operator: $eq
      valueField: exchangeRate
    - field: taxPeriodCode
      operator: $in
      valueField: taxPeriodCode.(value)
    - field: paymentDate
      operator: $between
      valueField: paymentDate
    - field: note
      operator: $cont
      valueField: note
    - field: dataSource
      operator: $in
      valueField: dataSource.(value)
    - field: updatedAt
      operator: $between
      valueField: updatedAt
    - field: updatedBy
      operator: $cont
      valueField: updatedBy
  sources:
    employeeGroupsList:
      uri: '"/api/employee-groups"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.employeeGroup.default,
        'value': $item.code}})
      disabledCache: true
      params:
        - limit
        - page
        - search
    taxPeriodList:
      uri: '"/api/assessment-period"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    employeesList:
      uri: '"/api/personals/all-employees"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label':
        $join($filter([$item.employeeId,$item.fullName], $boolean), ' - '),
        'value': $item.employeeId}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    companiesList:
      uri: '"/api/companies"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    legalEntitiesList:
      uri: '"/api/legal-entities"'
      method: GET
      queryTransform: '{''limit'': $.limit, ''page'': $.page,''search'': $.search, ''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    businessUnitList:
      uri: '"/api/business-units"'
      method: GET
      queryTransform: '{''limit'': $.limit, ''page'': $.page,''search'': $.search, ''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    divisionsList:
      uri: '"/api/divisions"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page,'search': $.search, 'filter':
        [{'field':'effectiveDate','operator': '$lte','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'additionalData': $item}})[]
      disabledCache: true
      params:
        - effectiveDate
        - limit
        - page
        - search
    departmentsList:
      uri: '"/api/departments/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page,'search': $.search, 'filter':
        [{'field':'effectiveDate','operator': '$eq','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - effectiveDate
        - search
    locationsList:
      uri: '"/api/locations"'
      method: GET
      queryTransform: '{''limit'': $.limit, ''page'': $.page,''search'': $.search, ''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code,  'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    otherIncomeList:
      uri: '"/api/other-incomes"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    otherIncomeGroupList:
      uri: '"/api/other-income-group"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    otherTypeList:
      uri: '"/api/picklists/OTHERTYPEINCOME/values/pagination"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
layout_options:
  show_detail_history: false
  delete_multi_items: true
  apply_delete_multi_items_to_delete_one: true
  custom_delete_body: '{''ids'': $map($.data,function($item){$item.id})[]}'
  show_dialog_form_save_add_button: true
  tool_table:
    - id: import
      icon: icon-upload-simple
      href: /GE/HR.FS.FR.092
      paramsRedirect:
        type: PR_OBJECT
        entityOrObj: OtherIncomeDetail
    - id: export
      icon: icon-download-simple
  show_filter_results_message: true
  store_selected_items: true
  show_dialog_duplicate_button: false
  hide_action_row: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: null
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    icon: icon-pencil-simple
    type: ghost-gray
  - id: delete
    icon: icon-trash
    type: ghost-gray
backend_url: /api/other-income-details
screen_name: null
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: employeeId
    defaultName: EmployeeId
  - name: employeeRecordNumber
    defaultName: EmployeeRecordNumber
  - name: companyCode
    defaultName: CompanyCode
  - name: legalEntityCode
    defaultName: LegalEntityCode
  - name: businessUnitCode
    defaultName: BusinessUnitCode
  - name: divisionCode
    defaultName: DivisionCode
  - name: departmentCode
    defaultName: DepartmentCode
  - name: locationCode
    defaultName: LocationCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Other Income
  parent:
    title: PR Setting
