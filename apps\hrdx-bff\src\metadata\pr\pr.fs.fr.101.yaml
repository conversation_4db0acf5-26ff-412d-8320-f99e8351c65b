id: PR.FS.FR.101
status: draft
sort: 359
user_created: 2aa5db12-fa54-46d6-8e05-9e421858de0c
date_created: '2024-07-26T09:25:30.265Z'
user_updated: b403d5da-d684-45e4-ae6d-e25223baedc5
date_updated: '2025-07-17T07:54:40.532Z'
title: Organizational Pay Group
requirement:
  time: 1744193177308
  blocks:
    - id: k03tRR5BhE
      type: paragraph
      data:
        text: "-\t Cho phép bộ phận nhân sự CTTV thiết lập thêm mới/ điều chỉnh/tra cứu nhóm trả lương theo cơ cấu. <PERSON><PERSON><PERSON><PERSON> hồ sơ nhân viên thỏa điều kiện thết lập theo cơ cấu hệ thống sẽ tự động xác định bộ công thức tính lương theo nhóm trả lương tương <PERSON>ng.\n"
    - id: hALJprlLxN
      type: paragraph
      data:
        text: >
          - <PERSON><PERSON> thống kiểm tra thông tin và không cho thiết lập mới nếu thông tin
          thiết lập trùng ( (Quốc gia, công ty, pháp nhân, Đơn vị quản trị,
          Khối, Phòng ban, Local/ Expart, Nơi làm việc, Ngày hiệu lực)
    - id: mzXQoW_hY3
      type: paragraph
      data:
        text: >-
          - Ngày hiệu lực của nhóm trả lương theo&nbsp; cơ cấu tổ chức có tính
          liên tục với nhau để đám bảo khi người dùng tính lương, hệ thống có
          thể tham chiếu áp dụng đúng bộ công thức theo cơ cấu cho nhân viên
          thỏa điều kiện.&nbsp; &nbsp;
  version: 2.30.7
screen_design: null
module: PR
local_fields:
  - code: code
    pinned: true
    title: Paygroup Code
    description: null
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    show_sort: true
  - code: shortName
    pinned: false
    title: Short Name
    description: null
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: name
    title: Long Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: countryName
    title: Country
    description: null
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: companyName
    title: Company
    description: '- Hiển thị thông tin Công ty được thiết lập cho nhóm trả lương'
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: effectiveDate
    title: Effective Date
    description: >-
      - Hệ thống hiển thị thông tin ngày bắt đầu hiệu lực của nhân viên đã chọn.
      Không được phép chỉnh sửa
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    show_sort: true
  - code: status
    title: Status
    description: |-
      "- Hiển thị thông tin trạng thái của dòng dữ liệu:
        + Sử dụng: nền chữ màu xanh.
        + Không sử dụng: nền chữ màu xám."
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: Boolean Tag
      collection: field_types
    show_sort: true
  - code: note
    title: Note
    description: '- Hiển thị thông tin ghi chú. Không được chỉnh sửa'
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: updatedBy
    title: Last Updated By
    description: '- Hiển thị tài khoản của người sửa (Ví dụ: NhiVN)'
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    pinned: false
    show_sort: true
  - code: updatedAt
    title: Last Updated On
    description: >-
      - Hiển thị ngày sửa mới nhất với đầy đủ thông tin Ngày/Tháng/Năm,
      Giờ/Phút/Giây (Ví dụ: 06/05/2024 10:20:53)
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
    show_sort: true
mock_data:
  - payGroupCode: '00000001'
    payGroupName: Nhóm chuyên gia Châu Âu
    country: Vietnam
    company: FSOFT
    legalEntity: FIS_HCM
    businessUnit: GHC
    division: ES
    department: PB19
    localExpat: Local
    location: Hanoi
    effectiveDate: 01/01/2024
    status: true
    note: This is the primary pay group for monthly salaries.
    creator: Admin
    createdTime: 20/04/2024
    lastEditor: Admin
    lastEditedTime: 06/05/2024
local_buttons: null
layout: layout-table
form_config:
  formTitle:
    edit: Edit Organizational Pay Group
    create: Add New Organizational Pay Group
  historyHeaderTitle: '''Organizational Pay Group Details'''
  defaultValidateError: Required fields have not been filled in completely (fields marked with *)
  fields:
    - type: text
      label: PayGroup Code
      name: code
      _condition:
        transform: $.extend.formType = 'view'
    - type: group
      n_cols: 2
      fields:
        - type: select
          label: Country
          name: countryObj
          outputValue: value
          placeholder: Select Country
          isLazyLoad: true
          _select:
            transform: $countryList($.extend.limit, $.extend.page, $.extend.search)
          _condition:
            transform: $.extend.formType != 'view'
        - type: select
          label: Company
          name: companyCode
          placeholder: Select Company
          outputValue: value
          isLazyLoad: true
          _disabled:
            transform: '$.extend.formType = ''proceed'' ? $.variables._checkIsUse : false'
          validators:
            - type: required
          _select:
            transform: >-
              $companiesList($.extend.limit, $.extend.page, $.extend.search,
              $.fields.effectiveDate)
          _validateFn:
            transform: >-
              $.extend.defaultValue ? {'label':
              $.extend.defaultValue.companyName, 'value':
              $.extend.defaultValue.companyCode}
            params:
              updateLabelExistOption: true
          _condition:
            transform: $.extend.formType != 'view'
        - type: text
          label: PayGroup Code
          name: code
          formatType: code
          placeholder: Enter Paygroup Code
          validators:
            - type: required
            - type: pattern
              args: ^[a-zA-Z0-9_.-]*$
              text: >-
                Code must only contain letters, numbers, hyphens, underscores,
                or dots.
            - type: maxLength
              args: '50'
              text: PayGroup Code should not exceed 50 characters
          _disabled:
            transform: $.extend.formType = 'edit' or $.extend.formType = 'proceed'
          _condition:
            transform: $.extend.formType != 'view' and $not($.extend.isDuplicate)
        - type: text
          label: PayGroup Code
          name: payGroupCode
          formatType: code
          placeholder: Enter Paygroup Code
          validators:
            - type: required
            - type: pattern
              args: ^[a-zA-Z0-9_.-]*$
              text: >-
                Code must only contain letters, numbers, hyphens, underscores,
                or dots.
            - type: maxLength
              args: '50'
              text: PayGroup Code should not exceed 50 characters
          _condition:
            transform: $.extend.formType != 'view' and $.extend.isDuplicate
        - type: translation
          label: Short Name
          placeholder: Enter Short Name
          name: shortName
          validators:
            - type: required
            - type: maxLength
              args: '300'
              text: Short Name should not exceed 300 characters
          _condition:
            transform: $.extend.formType != 'view'
        - type: translation
          label: Long Name
          placeholder: Enter Long Name
          name: name
          scale: 2.05
          validators:
            - type: required
            - type: maxLength
              args: '500'
              text: Long Name should not exceed 500 characters
          _condition:
            transform: $.extend.formType != 'view'
    - type: translation
      label: ShortName
      name: shortName
      _condition:
        transform: $.extend.formType = 'view'
    - type: translation
      label: Long Name
      name: name
      _condition:
        transform: $.extend.formType = 'view'
    - type: text
      label: Country
      name: countryName
      _condition:
        transform: $.extend.formType = 'view'
    - type: text
      label: Company
      name: companyName
      _condition:
        transform: $.extend.formType = 'view'
    - type: group
      n_cols: 2
      _condition:
        transform: $.extend.formType != 'view'
      fields:
        - type: dateRange
          label: Effective Date
          name: effectiveDate
          mode: date-picker
          setting:
            type: date
            format: dd/MM/yyyy
          _value:
            transform: >-
              $.extend.formType = 'create' ?
              $not($exists($.fields.effectiveDate)) ?  $now()
          validators:
            - type: required
        - type: radio
          label: Status
          name: status
          _value:
            transform: $.extend.formType = 'create' ? true
          radio:
            - label: Active
              value: true
            - label: Inactive
              value: false
    - type: group
      n_cols: 1
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - type: dateRange
          label: Effective Date
          name: effectiveDate
          mode: date-picker
          setting:
            type: day
            format: dd/MM/yyyy
        - type: radio
          label: Status
          name: status
          radio:
            - label: Active
              value: true
            - label: Inactive
              value: false
    - type: translationTextArea
      label: Note
      name: note
      placeholder: Enter Note
      textarea:
        autoSize:
          minRows: 3
        maxCharCount: 1000
      validators:
        - type: maxLength
          args: 1000
          text: note should not exceed 1000 characters
  sources:
    countryList:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    companiesList:
      uri: '"/api/companies/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
    payGroupsList:
      uri: '"/api/pay-group-structures"'
      method: GET
      queryTransform: >-
        {'limit': 200,'filter': [{'field':'status','operator':
        '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
    checkIsUse:
      uri: '"/api/pay-group-structures/check-is-used"'
      method: POST
      queryTransform: ''
      bodyTransform: >-
        {'payGroupCodeIsUsed' : $.payGroupCode,'companyCode' :
        $.companyCode,'effectiveDate' : $.effectiveDate}
      headerTransform: ''
      resultTransform: $
      disabledCache: true
      returnError: true
      params:
        - payGroupCode
        - companyCode
        - effectiveDate
  effectiveDateValidations:
    - sameDate
    - greaterThanLastest
  variables:
    _checkIsUse:
      transform: >-
        $.extend.formType = 'proceed' ? $checkIsUse($.fields.code,
        $.fields.companyCode, $.fields.effectiveDate).isError ? true : false
filter_config:
  fields:
    - type: text
      label: Paygroup
      name: code
      labelType: type-grid
      placeholder: Enter Paygroup Code
    - type: text
      label: Short Name
      labelType: type-grid
      placeholder: Enter Short Name
      name: shortName
    - type: text
      labelType: type-grid
      label: Long Name
      placeholder: Enter Long Name
      name: name
    - type: selectAll
      label: Country
      name: countryCode
      labelType: type-grid
      placeholder: Select Country
      mode: multiple
      _options:
        transform: $nationsList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      label: Company
      name: companyCode
      labelType: type-grid
      mode: multiple
      placeholder: Select Company
      _options:
        transform: $companyList($.extend.limit, $.extend.page, $.extend.search)
    - type: dateRange
      name: effectiveDate
      key: effectiveDateFrom
      labelType: type-grid
      label: Effective Date
      placeholder: dd/MM/yyyy
      mode: date-picker
      setting:
        format: dd/MM/yyyy
        type: date
    - type: radio
      label: Status
      labelType: type-grid
      name: status
      value: null
      radio:
        - label: All
          value: null
        - label: Active
          value: true
        - label: Inactive
          value: false
    - name: updatedBy
      label: Last Updated By
      type: text
      labelType: type-grid
      placeholder: Enter Last Updated By
    - name: updatedAt
      label: Last Updated On
      type: dateRange
      labelType: type-grid
      setting:
        mode: date
        format: dd/MM/yyyy
  filterMapping:
    - field: code
      operator: $cont
      valueField: code
    - field: countryCode
      operator: $in
      valueField: countryCode.(value)
    - field: companyCode
      operator: $in
      valueField: companyCode.(value)
    - field: status
      operator: $eq
      valueField: status
    - field: note
      operator: $cont
      valueField: note
    - field: shortName
      operator: $cont
      valueField: shortName
    - field: name
      operator: $cont
      valueField: name
    - field: effectiveDate
      operator: $between
      valueField: effectiveDate
    - field: updatedBy
      operator: $in
      valueField: updatedBy
    - field: updatedAt
      operator: $between
      valueField: updatedAt
    - field: lastEditTime
      operator: $between
      valueField: lastEditTime
    - field: note
      operator: $cont
      valueField: note
    - field: updatedBy
      operator: $cont
      valueField: updatedBy
    - field: updatedAt
      operator: $between
      valueField: updatedAt
  sources:
    nationsList:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    companyList:
      uri: '"/api/companies"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search, ''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    payGroupsList:
      uri: '"/api/pay-group-structures"'
      method: GET
      queryTransform: '{''limit'': 200,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
layout_options:
  show_dialog_form_save_add_button: true
  toolTable:
    export: true
    adjustDisplay: true
  tool_table:
    - id: export
      icon: icon-download-simple
  custom_history_backend_url: /api/pay-group-structures/:id/clone
  apply_delete_multi_items_to_delete_one: true
  custom_delete_body: '{''ids'': $map($.data,function($item){$item.id})[]}'
  delete_multi_items: true
  history_widget_header_options:
    duplicate: false
  show_filter_results_message: true
  store_selected_items: true
  show_dialog_duplicate_button: false
  hide_action_row: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: null
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    icon: icon-pencil-simple
    type: ghost-gray
  - id: delete
    icon: icon-trash
    type: ghost-gray
backend_url: /api/pay-group-structures
screen_name: pay-group-structures
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: code
    defaultName: PayGroupCode
  - name: countryCode
    defaultName: CountryCode
  - name: companyCode
    defaultName: CompanyCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Organizational Pay Group
  parent:
    title: PR Setting
