id: PR.FS.FR.102.assigned
status: draft
sort: 6
user_created: 60e9ad50-48e2-446b-9124-eef839c521ad
date_created: '2024-09-26T13:03:46.465Z'
user_updated: b403d5da-d684-45e4-ae6d-e25223baedc5
date_updated: '2025-07-22T06:47:36.850Z'
title: Assigned
requirement:
  time: 1747879521540
  blocks:
    - id: gTAUoXyUo-
      type: paragraph
      data:
        text: "-\t Cho phép bộ phận nhân sự CTTV thiết lập nhóm trả lương nhóm trả lương áp dụng cho từng hồ sơ nhân sự riêng biệt."
  version: 2.30.7
screen_design: null
module: PR
local_fields:
  - code: effectiveDate
    title: Effective Date
    description: >-
      - Hệ thống hiển thị thông tin ngày bắt đầu hiệu lực của nhân viên đã chọn.
      Không đượ<PERSON> phép chỉnh sửa
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DateHyperlink
      collection: field_types
    pinned: true
    show_sort: true
  - code: payGroupShowTable
    title: Pay Group Code
    description: '- Hiển thị thông mã nhóm trả lương. Không được chỉnh sửa. '
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: payGroupName
    title: Pay Group Name
    description: '- Hiển thị thông tin tên nhóm trả lương. Không được chỉnh sửa. '
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: status
    title: Status
    description: |-
      "- Hiển thị thông tin trạng thái của dòng dữ liệu:
        + Sử dụng: nền chữ màu xanh.
        + Không sử dụng: nền chữ màu xám."
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: Boolean Tag
      collection: field_types
    show_sort: true
  - code: companyName
    title: Company
    description: '- Hiển thị thông tin Công ty được thiết lập cho nhóm trả lương'
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: legalEntityName
    title: Legal Entity
    description: '- Hiển thị thông tin Pháp nhân được thiết lập cho nhóm trả lương'
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: businessUnitName
    title: Business Unit
    description: '- Hiển thị thông tin Đơn vị quản trị được thiết lập cho nhóm trả lương'
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: divisionName
    title: Division
    description: '- Hiển thị thông tin Khối được thiết lập cho nhóm trả lương'
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: departmentName
    title: Department
    description: >-
      - Hệ thống hiển thị thông tin Phòng ban của nhân viên đã chọn. Không được
      phép chỉnh sửa
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: note
    title: Note
    description: '- Hiển thị thông tin ghi chú. Không được chỉnh sửa'
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: updatedBy
    title: Last Updated By
    description: '- Hiển thị tài khoản của người sửa (Ví dụ: NhiVN)'
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    pinned: false
    show_sort: true
  - code: updatedAt
    title: Last Updated On
    description: >-
      - Hiển thị ngày sửa mới nhất với đầy đủ thông tin Ngày/Tháng/Năm,
      Giờ/Phút/Giây (Ví dụ: 06/05/2024 10:20:53)
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
    show_sort: true
mock_data: []
local_buttons: null
layout: layout-table
form_config:
  formSize:
    create: largex
    edit: largex
    proceed: largex
  formTitle:
    create: Create paygroup for employee
    edit: Edit paygroup for employee
    view: View paygroup for employee
    proceed: Edit paygroup for employee
  defaultValidateError: Required fields have not been filled in completely (fields marked with *)
  fields:
    - type: select
      name: employee
      label: Employee
      placeholder: Select Employee
      isLazyLoad: true
      validators:
        - type: required
      _select:
        transform: >-
          $employeesList($.extend.limit, $.extend.page, $.extend.search, null,
          null, null, ($not($isNilorEmpty($.fields.effectiveDate)) ?
          $.fields.effectiveDate : $now()))
      _condition:
        transform: $.extend.formType = 'create'
      outputValue: value
    - type: select
      name: employee
      label: Employee
      placeholder: Select Employee
      validators:
        - type: required
      isLazyLoad: true
      _condition:
        transform: $.extend.formType != 'view' and $.extend.formType != 'create'
      _select:
        transform: >-
          $employeesList(1,1,'',$.extend.defaultValue.employeeId,$.extend.defaultValue.employeeGroupCode,$.extend.defaultValue.employeeRecordNumber,$.fields.effectiveDateFrom)
      _disabled:
        transform: 'true'
      outputValue: value
    - type: text
      name: dateToShowEmployee
      unvisible: true
      _value:
        transform: >-
          $not($isNilorEmpty($.fields.effectiveDate)) ?
          $DateToTimestampUTC($.fields.effectiveDate) :
          $DateToTimestampUTC($now())
    - type: text
      name: dataEmployee
      unvisible: true
      dependantField: $.fields.employeeId; $.fields.effectiveDate
      _value:
        transform: >-
          $not($isNilorEmpty($.fields.employeeId)) ? {'employeeId':
          $.fields.employeeId , 'employeeRecordNumber':
          $.fields.employeeRecordNumber, 'dateToShowEmployee':
          $.fields.dateToShowEmployee} : null
    - type: text
      name: employeeIdView
      key: employeeIdView
      label: Employee
      _condition:
        transform: $.extend.formType = 'view'
      _value:
        transform: >-
          $join($filter([$.extend.defaultValue.employeeId,$.extend.defaultValue.employeeGroupCode,$string($.extend.defaultValue.employeeRecordNumber),$.extend.defaultValue.fullName],
          $boolean), ' - ')
    - type: text
      name: employeeId
      label: Employee
      unvisible: true
      _value:
        transform: $.fields.employee.employeeId
    - type: text
      name: employeeRecordNumber
      label: EmployeeRecordNumber
      unvisible: true
      _value:
        transform: $string($.fields.employee.employeeRecordNumber)
    - type: text
      name: jobDataId
      label: jobDataId
      unvisible: true
      _value:
        transform: $.fields.employee.jobDataId
    - type: text
      name: companyName
      key: companyName
      label: Company
      _condition:
        transform: $.extend.formType = 'view'
    - type: text
      name: legalEntityName
      key: legalEntityName
      label: Legal Entity
      _condition:
        transform: $.extend.formType = 'view'
    - type: text
      name: businessUnitName
      key: businessUnitName
      label: Business Unit
      _condition:
        transform: $.extend.formType = 'view'
    - type: text
      name: divisionName
      key: divisionName
      label: Division
      _condition:
        transform: $.extend.formType = 'view'
    - type: text
      name: departmentName
      key: departmentName
      label: Department
      _condition:
        transform: $.extend.formType = 'view'
    - type: text
      name: jobTitleName
      key: jobCodeName
      label: Job Title
      _condition:
        transform: $.extend.formType = 'view'
    - type: text
      name: staffLevelName
      label: Staff Level
      _condition:
        transform: $.extend.formType = 'view'
    - type: text
      name: contractTypeName
      key: contractTypeName
      label: Contract Type
      _condition:
        transform: $.extend.formType = 'view'
    - type: text
      name: locationName
      key: locationName
      label: Location
      _condition:
        transform: $.extend.formType = 'view'
    - type: group
      n_cols: 2
      _condition:
        transform: $.extend.formType = 'create' or $.extend.formType = 'edit'
      fields:
        - type: dateRange
          label: Effective Date
          name: effectiveDate
          mode: date-picker
          setting:
            type: day
            format: dd/MM/yyyy
          validators:
            - type: required
          clearFieldsAfterChange:
            - payGroupCode
          _value:
            transform: $.extend.formType='create' ? $now()
          _condition:
            transform: $.extend.formType != 'view'
          _disabled:
            transform: $.extend.formType = 'edit'
        - type: text
          name: companyCode
          unvisible: true
          _value:
            transform: >-
              $not($isNilorEmpty($.fields.employeeId)) and
              $not($isNilorEmpty($.fields.jobDataId)) ? $.variables._companyCode
        - type: select
          label: Pay Group
          name: payGroupCode
          placeholder: Select PayGroup
          _select:
            transform: >-
              $payGroupsList($.extend.limit, $.extend.page,
              $.extend.search,$.fields.effectiveDate,$.fields.companyCode)
          _condition:
            transform: $not($.extend.formType = 'view')
          outputValue: value
          isLazyLoad: true
          validators:
            - type: required
    - type: group
      n_cols: 1
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - type: dateRange
          label: EffectiveDate
          name: effectiveDate
          mode: date-picker
          setting:
            type: day
            format: dd/MM/yyyy
          _condition:
            transform: $.extend.formType = 'view'
        - type: text
          label: Pay Group
          name: payGroupCode
          _condition:
            transform: $.extend.formType = 'view'
          _value:
            transform: >-
              $join($filter([$.extend.defaultValue.payGroup.longName,$.extend.defaultValue.payGroupCodeShow],
              $boolean), ' - ')
    - type: radio
      label: Status
      name: status
      validators:
        - type: required
      _value:
        transform: $.extend.formType='create' ? true
      radio:
        - label: Active
          value: true
        - label: Inactive
          value: false
      _condition:
        transform: $.extend.formType != 'view'
    - type: radio
      label: Status
      name: status
      _condition:
        transform: $.extend.formType = 'view'
      radio:
        - label: Active
          value: true
        - label: Inactive
          value: false
    - type: translationTextArea
      label: Note
      name: note
      placeholder: Enter Note
      textarea:
        autoSize:
          minRows: 3
        maxCharCount: 1000
      validators:
        - type: maxLength
          args: 1000
  overview:
    dependentField: dataEmployee
    title: Employee Detail
    uri: >-
      /api/pr-employees/:{employeeId}:/employee-record-number/:{employeeRecordNumber}:/effective-date/:{dateToShowEmployee}:
    border: true
    display:
      - key: companyName
        label: Company
      - key: legalEntityName
        label: Legal entity
      - key: businessUnitName
        label: Business unit
      - key: divisionName
        label: Division
      - key: departmentName
        label: Department
      - key: jobTitleName
        label: Job Title
      - key: contractTypeName
        label: Contract Type
      - key: locationName
        label: Location
  sources:
    jobDatasList:
      uri: '"/api/personals/"  & $.employeeId &  "/job-datas"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $.data
      disabledCache: true
      params:
        - employeeId
    personalsList:
      uri: '"/api/personals?page=1&limit=1000"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.employeeId, 'value':
        $item.employeeId}})[]
      disabledCache: true
    payGroupsList:
      uri: '"/api/pay-group-structures"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page,'search': $.search,'filter':
        [{'field':'IsCombobox','operator':
        '$eq','value':true},{'field':'status','operator': '$eq','value':true},
        {'field': 'effectiveDate', 'operator': '$lte', 'value':
        $.effectiveDate},{'field': 'companyCode', 'operator': '$eq', 'value':
        $.companyCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value':  $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
        - companyCode
    employeesList:
      uri: '"/api/personals/job-datas/personal-ern-listemployeeid-info"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'filter':
        [{'field':'employeeId','operator':
        '$cont','value':$.search},{'field':'employeeId','operator':
        '$eq','value':$.empId},{'field':'employeeGroupCode','operator':
        '$cont','value':$.grpCode},{'field':'employeeRecordNumber','operator':
        '$eq','value':$.ern}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label':
        $join($filter([$item.employeeId,$item.employeeGroupCode,$string($item.employeeRecordNumber),$item.fullName],
        $boolean), ' - ')  , 'value': {'employeeId': $item.employeeId,
        'employeeRecordNumber': $string($item.employeeRecordNumber),
        'jobDataId': $item.jobDataId}, 'employeeId': $item.employeeId,
        'employeeRecordNumber': $string($item.employeeRecordNumber),'jobDataId':
        $item.jobDataId}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - empId
        - grpCode
        - ern
    jobDatasDetail:
      uri: '"/api/personals/"  & $.employeeId & "/job-datas/" & $.id'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $.companyCode
      disabledCache: true
      params:
        - employeeId
        - id
  variables:
    _employee:
      transform: >-
        $not($isNilorEmpty($.extend.defaultValue.employeeId)) ?
        $employeesList(1,1,'',$.extend.defaultValue.employeeId,$.extend.defaultValue.employeeGroupCode,$.extend.defaultValue.employeeRecordNumber)
    _fieldEmp:
      transform: $.fields.employee
    _dataEdit:
      transform: $.variables._employee[0].value
    _companyCode:
      transform: >-
        $not($isNilorEmpty($.fields.employeeId)) and
        $not($isNilorEmpty($.fields.jobDataId)) ?
        $jobDatasDetail($.fields.employeeId, $.fields.jobDataId)
  footer:
    create: true
    update: true
    createdOn: createdAt
    updatedOn: updatedAt
    createdBy: createdBy
    updatedBy: updatedBy
filter_config:
  fields:
    - type: selectAll
      name: companyCode
      labelType: type-grid
      label: Company
      placeholder: Select Company
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $companiesList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      name: legalEntityCode
      labelType: type-grid
      label: Legal Entity
      placeholder: Select Legal Entity
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $legalEntitiesList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      label: Business Unit
      labelType: type-grid
      name: businessUnitCode
      mode: multiple
      placeholder: Select Business Unit
      isLazyLoad: true
      _options:
        transform: $businessUnitList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      name: divisionCode
      label: Division
      isLazyLoad: true
      labelType: type-grid
      placeholder: Select Division
      mode: multiple
      _options:
        transform: $divisionsList($.extend.limit,$.extend.page, $.extend.search)
    - type: selectAll
      name: departmentCode
      label: Department
      labelType: type-grid
      placeholder: Select Department
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $departmentsList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      name: payGroupCode
      labelType: type-grid
      label: Pay Group
      placeholder: Select Pay Group
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $payGroup($.extend.limit, $.extend.page,$.extend.search)
    - type: selectAll
      label: Employee
      name: employeeId
      labelType: type-grid
      placeholder: Select Employee
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $employeesList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      label: Employee Group
      name: employeeGroupCode
      labelType: type-grid
      placeholder: Select Employee Group
      mode: multiple
      _options:
        transform: $employeeGroup()
    - type: selectAll
      label: Job Title
      labelType: type-grid
      name: jobTitleCode
      placeholder: Select Job Title
      mode: multiple
      _options:
        transform: $jobCodesList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      label: Location
      name: locationCode
      labelType: type-grid
      placeholder: Select Position
      mode: multiple
      _options:
        transform: $locationList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      label: Contract Type
      labelType: type-grid
      name: contractTypeCode
      placeholder: Select Contract Type
      mode: multiple
      _options:
        transform: $contractTypeList()
    - name: effectiveDateFrom
      label: Effective Start Date
      labelType: type-grid
      type: dateRange
      setting:
        type: day
        format: dd/MM/yyyy
    - type: radio
      label: Status
      labelType: type-grid
      name: status
      value: null
      radio:
        - label: All
          value: null
        - label: Active
          value: true
        - label: Inactive
          value: false
    - name: updatedBy
      label: Last Updated By
      type: text
      labelType: type-grid
      placeholder: Enter Last Updated By
    - name: updatedAt
      label: Last Updated On
      type: dateRange
      labelType: type-grid
      setting:
        mode: date
        format: dd/MM/yyyy
    - type: textarea
      label: Note
      name: note
      labelType: type-grid
      placeholder: Enter Note
      textarea:
        autoSize:
          minRows: 3
        maxCharCount: 1000
      validators:
        - type: maxLength
          args: 1000
  filterMapping:
    - operator: $or
      valueField:
        - field: employeeIdFilter
          operator: $elemMatch
          valueField: employeeId.(employeeId)
        - field: employeeRecordNumber
          operator: $elemMatch
          valueField: employeeId.(ern)
    - field: companyCode
      operator: $in
      valueField: companyCode.(value)
    - field: legalEntityCode
      operator: $in
      valueField: legalEntityCode.(value)
    - field: businessUnitCode
      operator: $in
      valueField: businessUnitCode.(value)
    - field: divisionCode
      operator: $in
      valueField: divisionCode.(value)
    - field: departmentCode
      operator: $in
      valueField: departmentCode.(value)
    - field: payGroupShowTable
      operator: $in
      valueField: payGroupCode.(value)
    - field: employeeGroupCode
      operator: $in
      valueField: employeeGroupCode.(value)
    - field: employeeRecordNumber
      operator: $eq
      valueField: employeeRecordNumber
    - field: fullName
      operator: $eq
      valueField: fullName
    - field: jobTitleCode
      operator: $in
      valueField: jobTitleCode.(value)
    - field: locationCode
      operator: $in
      valueField: locationCode.(value)
    - field: jobIndicatorCode
      operator: $in
      valueField: jobIndicatorCode.(value)
    - field: contractTypeCode
      operator: $in
      valueField: contractTypeCode.(value)
    - field: effectiveDate
      operator: $between
      valueField: effectiveDateFrom
    - field: note
      operator: $cont
      valueField: note
    - field: status
      operator: $eq
      valueField: status
    - field: updatedBy
      operator: $cont
      valueField: updatedBy
    - field: updatedAt
      operator: $between
      valueField: updatedAt
  sources:
    companiesList:
      uri: '"/api/companies"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    legalEntitiesList:
      uri: '"/api/legal-entities"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    businessUnitList:
      uri: '"/api/business-units"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    divisionsList:
      uri: '"/api/divisions"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    departmentsList:
      uri: '"/api/departments/get-by"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    payGroup:
      uri: '"/api/pay-group-structures"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    frequencies:
      uri: '"/api/picklists/SALARYFREQUENCY/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
    employeesList:
      uri: '"/api/personals"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page, ''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label':
        $join($filter([$item.employeeId,$item.employeeGroup,$string($item.employeeRecordNumber),$item.fullName],
        $boolean), ' - ') , 'value':{'employeeId':$item.employeeId , 'ern':
        $item.employeeRecordNumber,'empGroup' :
        $.item.employeeGroup},'employeeId':$item.employeeId,
        'ern':$item.employeeRecordNumber, 'empGroup' : $item.employeeGroup }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    personalNameList:
      uri: '"/api/personals/all-employees"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.employeeId & ' - ' &
        $item.fullName , 'value': $item.employeeId}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    employeeGroup:
      uri: '"/api/picklists/EMPLOYEEGROUP/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
    gender:
      uri: '"/api/picklists/GENDER/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default , 'value':
        $item.code}})[]
      disabledCache: true
    localForeigners:
      uri: '"/api/picklists/LOCALFOREIGNERS/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default , 'value':
        $item.code}})[]
      disabledCache: true
    jobCodesList:
      uri: '"/api/job-codes"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    locationList:
      uri: '"/api/locations"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    jobIndicatorList:
      uri: '"/api/picklists/JOBINDICATOR/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})
      disabledCache: true
    contractTypeList:
      uri: '"/api/picklists/CONTRACTTYPE/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})
      disabledCache: true
layout_options:
  page_header_options:
    visible: false
  show_detail_history: false
  nested_row_type: group
  reset_page_index_after_do_action:
    edit: true
    create: true
  row_type: expand
  row_data_combine:
    - employeeId
    - employeeGroupCode
    - employeeRecordNumber
    - employeeFullName
  tool_table:
    - id: import
      icon: icon-upload-simple
      paramsRedirect:
        type: PR_OBJECT
        entityOrObj: EmployeePayGroup
    - id: export
      icon: icon-download-simple
  toolTable:
    export: true
    adjustDisplay: true
  delete_multi_items: true
  apply_delete_multi_items_to_delete_one: true
  custom_delete_body: '{''ids'': $map($.data,function($item){$item.id})[]}'
  table:
    defaultCollapse: true
  data_group:
    group_api:
      url: /api/employee-pay-groups/group
    group_details_api:
      url: >-
        /api/employee-pay-groups/group-detail/{{employeeId}}/{{employeeRecordNumber}}{{#if
        employeeGroupCode}}/{{employeeGroupCode}}{{else}}{{/if}}
  show_filter_results_message: true
  store_selected_items: true
  show_dialog_duplicate_button: false
  hide_action_row: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: icon-plus
    type: primary
options: null
create_form: null
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    icon: icon-pencil-simple
    type: ghost-gray
  - id: delete
    icon: icon-trash
    type: ghost-gray
backend_url: /api/employee-pay-groups
screen_name: employee-pay-groups-assigned-1
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: secondary
parent: PR.FS.FR.102
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: employeeId
    defaultName: EmployeeId
  - name: employeeRecordNumber
    defaultName: EmployeeRecordNumber
  - name: payGroupCode
    defaultName: PayGroupCode
  - name: companyCode
    defaultName: CompanyCode
  - name: legalEntityCode
    defaultName: LegalEntityCode
  - name: businessUnitCode
    defaultName: BusinessUnitCode
  - name: divisionCode
    defaultName: DivisionCode
  - name: departmentCode
    defaultName: DepartmentCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item: null
