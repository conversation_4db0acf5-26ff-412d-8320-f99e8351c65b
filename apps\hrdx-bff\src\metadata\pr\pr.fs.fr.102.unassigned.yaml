id: PR.FS.FR.102.unassigned
status: draft
sort: 11
user_created: 60e9ad50-48e2-446b-9124-eef839c521ad
date_created: '2024-09-26T13:09:44.576Z'
user_updated: b403d5da-d684-45e4-ae6d-e25223baedc5
date_updated: '2025-07-23T03:32:35.642Z'
title: Unassigned
requirement:
  time: 1747282770775
  blocks:
    - id: 9LSP5XpZF8
      type: header
      data:
        text: Set Up Pay Group For Employee
        level: 1
  version: 2.30.7
screen_design: null
module: PR
local_fields:
  - code: employeeId
    title: Employee ID
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: fullName
    title: Employee Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: employeeGroupName
    title: Employee Group
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: employeeRecordNumber
    title: Employee Record Number
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: companyName
    title: Company
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: legalEntityName
    title: Legal Entity
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: businessUnitName
    title: Business Unit
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: divisionName
    title: Division
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: departmentName
    title: Department
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: jobTitleName
    title: Job Title
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: staffLevelName
    title: Staff Level
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: contractTypeName
    title: Contract Type
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: locationName
    title: Location
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
mock_data: []
local_buttons: null
layout: layout-table
form_config:
  formSize:
    create: largex
    edit: largex
    proceed: largex
  formTitle:
    create: Create paygroup for employee
    edit: Create paygroup for employee
    view: View paygroup for employee
    proceed: Edit paygroup for employee
  defaultValidateError: Required fields have not been filled in completely (fields marked with *)
  fields:
    - type: select
      name: employee
      label: Employee
      placeholder: Select Employee
      validators:
        - type: required
      _condition:
        transform: $.extend.formType != 'view'
      _select:
        transform: $.variables._employee
      _value:
        transform: $.variables._dataEdit
      _disabled:
        transform: 'true'
      outputValue: value
    - type: text
      name: employeeIdView
      unvisible: true
      key: employeeIdView
      label: Employee
      disabled: true
      _value:
        transform: >-
          $join($filter([$.extend.defaultValue.employeeId,$.extend.defaultValue.employeeGroupCode,$string($.extend.defaultValue.employeeRecordNumber),$.extend.defaultValue.fullName],
          $boolean), ' - ')
    - type: text
      name: employeeId
      label: Employee
      unvisible: true
    - type: text
      name: employeeRecordNumber
      label: EmployeeRecordNumber
      unvisible: true
    - type: text
      name: jobDataId
      label: jobDataId
      unvisible: true
    - type: text
      name: companyName
      key: companyName
      label: Company Name
      _condition:
        transform: $.extend.formType = 'view'
    - type: text
      name: legalEntityName
      key: legalEntityName
      label: Legal Entity
      _condition:
        transform: $.extend.formType = 'view'
    - type: text
      name: businessUnitName
      key: businessUnitName
      label: Business Unit
      _condition:
        transform: $.extend.formType = 'view'
    - type: text
      name: divisionName
      key: divisionName
      label: Division
      _condition:
        transform: $.extend.formType = 'view'
    - type: text
      name: departmentName
      key: departmentName
      label: Department
      _condition:
        transform: $.extend.formType = 'view'
    - type: text
      name: jobTitleName
      key: jobCodeName
      label: Job Title
      _condition:
        transform: $.extend.formType = 'view'
    - type: text
      name: contractTypeName
      key: contractTypeName
      label: Contract Type
      _condition:
        transform: $.extend.formType = 'view'
    - type: text
      name: locationName
      key: locationName
      label: Location
      _condition:
        transform: $.extend.formType = 'view'
    - type: group
      n_cols: 2
      _condition:
        transform: $.extend.formType != 'view'
      fields:
        - type: dateRange
          label: Effective Date
          name: effectiveDate
          mode: date-picker
          setting:
            type: day
            format: dd/MM/yyyy
          validators:
            - type: required
          _value:
            transform: $now()
          _condition:
            transform: $.extend.formType != 'view'
        - type: select
          label: Pay Group
          name: payGroupCode
          outputValue: value
          placeholder: Select PayGroup
          _select:
            transform: >-
              $payGroupsList($.extend.limit,$.extend.page,
              $.extend.search,$.fields.effectiveDate,
              $.extend.defaultValue.companyCode)
          isLazyLoad: true
          _condition:
            transform: $.extend.formType != 'view'
          validators:
            - type: required
    - type: dateRange
      label: EffectiveDate
      name: effectiveDate
      mode: date-picker
      setting:
        type: day
        format: dd/MM/yyyy
      _condition:
        transform: $.extend.formType = 'view'
    - type: text
      label: Pay Group
      name: payGroupCode
      _condition:
        transform: $.extend.formType = 'view'
      _value:
        transform: >-
          $join($filter([$.extend.defaultValue.payGroupName,$.extend.defaultValue.payGroupCode],
          $boolean), ' - ')
    - type: radio
      label: Status
      name: status
      validators:
        - type: required
      _value:
        transform: $.extend.formType='edit' ? true
      radio:
        - label: Active
          value: true
        - label: Inactive
          value: false
      _condition:
        transform: $.extend.formType != 'view'
    - type: radio
      label: Status
      name: status
      _condition:
        transform: $.extend.formType = 'view'
      radio:
        - label: Active
          value: true
        - label: Inactive
          value: false
    - type: translationTextArea
      label: Note
      name: note
      placeholder: Enter Note
      textarea:
        autoSize:
          minRows: 3
        maxCharCount: 1000
      validators:
        - type: maxLength
          args: 1000
  overview:
    dependentField: employee
    _condition:
      transform: $.extend.formType = 'create' or $.extend.formType = 'edit'
    border: true
    title: Employee Detail
    uri: '/api/personals/:{employeeId}:/job-datas/overview/:{jobDataId}:'
    display:
      - key: company
        label: Company
      - key: legalEntity
        label: Legal entity
      - key: businessUnit
        label: Business unit
      - key: division
        label: Division
      - key: department
        label: Department
      - key: jobTitle
        label: Job Title
      - key: contractTypeName
        label: Contract Type
      - key: location
        label: Location
  sources:
    jobDatasList:
      uri: '"/api/personals/"  & $.employeeId &  "/job-datas"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $.data
      disabledCache: true
      params:
        - employeeId
    personalsList:
      uri: '"/api/personals?page=1&limit=1000"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.employeeId, 'value':
        $item.employeeId}})[]
      disabledCache: true
    payGroupsList:
      uri: '"/api/pay-group-structures"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'IsCombobox','operator':
        '$eq','value':true},{'field':'status','operator': '$eq','value':true},
        {'field': 'effectiveDate', 'operator': '$lte', 'value':
        $.effectiveDate},{'field': 'companyCode', 'operator': '$eq', 'value':
        $.companyCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
        - companyCode
    employeesList:
      uri: '"/api/personals/job-datas/personal-ern-listemployeeid-info"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'filter':
        [{'field':'employeeId','operator':
        '$cont','value':$.search},{'field':'employeeId','operator':
        '$eq','value':$.empId},{'field':'employeeGroupCode','operator':
        '$cont','value':$.grpCode},{'field':'employeeRecordNumber','operator':
        '$eq','value':$.ern}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label':
        $join($filter([$item.employeeId,$item.employeeGroupCode,$string($item.employeeRecordNumber),$item.fullName],
        $boolean), ' - ')  , 'value': {'employeeId': $item.employeeId,
        'employeeRecordNumber': $string($item.employeeRecordNumber),
        'jobDataId': $item.jobDataId}, 'employeeId': $item.employeeId,
        'employeeRecordNumber': $string($item.employeeRecordNumber),'jobDataId':
        $item.jobDataId}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - empId
        - grpCode
        - ern
    jobDatasDetail:
      uri: '"/api/personals/"  & $.employeeId & "/job-datas/" & $.id'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $.companyCode
      disabledCache: true
      params:
        - employeeId
        - id
  variables:
    _employee:
      transform: >-
        $not($isNilorEmpty($.extend.defaultValue.employeeId)) ?
        $employeesList(1,1,'',$.extend.defaultValue.employeeId,$.extend.defaultValue.employeeGroupCode,$.extend.defaultValue.employeeRecordNumber)
    _dataEdit:
      transform: $.variables._employee[0].value
  footer:
    create: true
    update: true
    createdOn: createdAt
    updatedOn: updatedAt
    createdBy: createdBy
    updatedBy: updatedBy
filter_config:
  fields:
    - type: selectAll
      name: companyCode
      label: Company
      placeholder: Select Company
      labelType: type-grid
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $companiesList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      name: legalEntityCode
      label: Legal Entity
      placeholder: Select Legal Entity
      mode: multiple
      labelType: type-grid
      isLazyLoad: true
      _options:
        transform: $legalEntitiesList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      label: Business Unit
      name: businessUnitCode
      mode: multiple
      labelType: type-grid
      isLazyLoad: true
      placeholder: Select Business Unit
      _options:
        transform: $businessUnitList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      name: divisionCode
      label: Division
      placeholder: Select Division
      mode: multiple
      labelType: type-grid
      isLazyLoad: true
      _options:
        transform: $divisionsList($.extend.limit,$.extend.page, $.extend.search)
    - type: selectAll
      name: departmentCode
      label: Department
      placeholder: Select Department
      mode: multiple
      labelType: type-grid
      isLazyLoad: true
      _options:
        transform: $departmentsList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      name: payGroupCode
      label: Pay Group
      placeholder: Select Pay Group
      mode: multiple
      isLazyLoad: true
      labelType: type-grid
      _options:
        transform: $payGroup($.extend.limit, $.extend.page,$.extend.search)
    - type: selectAll
      label: Employee
      name: employeeId
      labelType: type-grid
      placeholder: Select Employee ID
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $employeesList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      labelType: type-grid
      label: Employee Group
      name: employeeGroupCode
      placeholder: Select Employee Group
      mode: multiple
      _options:
        transform: $employeeGroup()
    - type: text
      label: Full Name
      name: fullName
      labelType: type-grid
      placeholder: Input Full Name
    - type: selectAll
      label: Job Title
      name: jobTitleCode
      labelType: type-grid
      placeholder: Select Job Title
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $jobCodesList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      label: Location
      name: locationCode
      placeholder: Select Location
      labelType: type-grid
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $locationsList($.extend.limit, $.extend.page,$.extend.search)
    - type: selectAll
      label: Contract Type
      name: contractTypeCode
      labelType: type-grid
      placeholder: Select Contract Type
      mode: multiple
      _options:
        transform: $contractTypeList()
  filterMapping:
    - operator: $or
      valueField:
        - field: employeeId
          operator: $elemMatch
          valueField: employeeId.(employeeId)
        - field: employeeRecordNumber
          operator: $elemMatch
          valueField: employeeId.(ern)
    - field: companyCode
      operator: $in
      valueField: companyCode.(value)
    - field: legalEntityCode
      operator: $in
      valueField: legalEntityCode.(value)
    - field: businessUnitCode
      operator: $in
      valueField: businessUnitCode.(value)
    - field: divisionCode
      operator: $in
      valueField: divisionCode.(value)
    - field: departmentCode
      operator: $in
      valueField: departmentCode.(value)
    - field: payGroupCode
      operator: $in
      valueField: payGroupCode.(value)
    - field: employeeGroupCode
      operator: $in
      valueField: employeeGroupCode.(value)
    - field: employeeRecordNumber
      operator: $eq
      valueField: employeeRecordNumber
    - field: fullName
      operator: $eq
      valueField: fullName
    - field: jobTitleCode
      operator: $in
      valueField: jobTitleCode.(value)
    - field: locationCode
      operator: $in
      valueField: locationCode.(value)
    - field: jobIndicatorCode
      operator: $in
      valueField: jobIndicatorCode.(value)
    - field: contractTypeCode
      operator: $in
      valueField: contractTypeCode.(value)
    - field: effectiveDateFrom
      operator: $between
      valueField: effectiveDateFrom
    - field: createdBy
      operator: $in
      valueField: createdBy
    - field: createdAt
      operator: $between
      valueField: createdAt
    - field: updatedBy
      operator: $in
      valueField: updatedBy
    - field: updatedAt
      operator: $between
      valueField: updatedAt
  sources:
    companiesList:
      uri: '"/api/companies"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    legalEntitiesList:
      uri: '"/api/legal-entities"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    businessUnitList:
      uri: '"/api/business-units"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    divisionsList:
      uri: '"/api/divisions"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    departmentsList:
      uri: '"/api/departments/get-by"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    payGroup:
      uri: '"/api/pay-group-structures"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    frequencies:
      uri: '"/api/picklists/SALARYFREQUENCY/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
    employeesList:
      uri: '"/api/personals"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page, ''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label':
        $join($filter([$item.employeeId,$item.employeeGroup,$string($item.employeeRecordNumber),$item.fullName],
        $boolean), ' - ') , 'value':{'employeeId':$item.employeeId , 'ern':
        $item.employeeRecordNumber,'empGroup' :
        $.item.employeeGroup},'employeeId':$item.employeeId,
        'ern':$item.employeeRecordNumber, 'empGroup' : $item.employeeGroup }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    personalNameList:
      uri: '"/api/personals/all-employees"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.employeeId & ' - ' &
        $item.fullName , 'value': $item.employeeId}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    employeeGroup:
      uri: '"/api/picklists/EMPLOYEEGROUP/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
    gender:
      uri: '"/api/picklists/GENDER/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default , 'value':
        $item.code}})[]
      disabledCache: true
    localForeigners:
      uri: '"/api/picklists/LOCALFOREIGNERS/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default , 'value':
        $item.code}})[]
      disabledCache: true
    jobCodesList:
      uri: '"/api/job-codes"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    locationsList:
      uri: '"/api/locations"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    positionsList:
      uri: '"/api/locations"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    jobIndicatorList:
      uri: '"/api/picklists/JOBINDICATOR/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})
      disabledCache: true
    contractTypeList:
      uri: '"/api/picklists/CONTRACTTYPE/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})
      disabledCache: true
layout_options:
  page_header_options:
    visible: false
  show_detail_history: false
  precondition_filter:
    custom_path:
      transform: >-
        '/api/pr-employee-pay-groups-unassigned/' &
        $dateToTimestamp($.longDate)  & '/unassigned'
    auto_filter_take: 1
  disabled_click_row: true
  toolTable:
    export: true
    adjustDisplay: true
  tool_table:
    - id: import
      icon: icon-upload-simple
      paramsRedirect:
        type: PR_OBJECT
        entityOrObj: EmployeePayGroup
    - id: export
      icon: icon-download-simple
  show_table_checkbox: false
  show_create_data_table: false
  reset_page_index_after_do_action:
    edit: true
  custom_detail_backend_url: >-
    /api/pr-common-employee-pay-groups-unassigned/:employeeId/employee-record-number/:employeeRecordNumber
  custom_update_api:
    url: /api/pr-employee-pay-groups-unassigned
  custom_export_api:
    _url:
      transform: '''/api/pr-employee-pay-groups-unassigned/'' & $dateToTimestamp($.longDate)'
  show_filter_results_message: true
  store_selected_items: true
  hide_action_row: true
layout_options__header_buttons: null
options: null
create_form:
  fields:
    - type: dateRange
      label: Effective Date
      hiddenLabel: true
      name: longDate
      mode: date-picker
      _value:
        transform: $not($exists($.fields.longDate)) ? $now()
      setting:
        format: dd/MM/yyyy
        type: date
  filterMapping:
    - field: longDate
      operator: $eq
      valueField: longDate
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    icon: icon-plus-bold
    type: ghost-gray
    title: Create
backend_url: /api/pr-employee-pay-groups-unassigned
screen_name: employee-paygroup-unassigned
layout_options__actions_many: null
parent: PR.FS.FR.102
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: employeeId
    defaultName: EmployeeId
  - name: employeeRecordNumber
    defaultName: EmployeeRecordNumber
  - name: companyCode
    defaultName: CompanyCode
  - name: legalEntityCode
    defaultName: LegalEntityCode
  - name: businessUnitCode
    defaultName: BusinessUnitCode
  - name: divisionCode
    defaultName: DivisionCode
  - name: departmentCode
    defaultName: DepartmentCode
  - name: locationCode
    defaultName: LocationCode
  - name: staffLevelCode
    defaultName: EmployeeLevelCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item: null
