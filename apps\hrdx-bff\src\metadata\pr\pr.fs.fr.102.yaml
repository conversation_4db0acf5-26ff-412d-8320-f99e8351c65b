id: PR.FS.FR.102
status: draft
sort: 291
user_created: 60e9ad50-48e2-446b-9124-eef839c521ad
date_created: '2024-09-26T13:04:32.155Z'
user_updated: b403d5da-d684-45e4-ae6d-e25223baedc5
date_updated: '2025-07-28T06:16:08.664Z'
title: 'Set Up Paygroup For Employee '
requirement:
  time: 1745296521608
  blocks:
    - id: gTAUoXyUo-
      type: paragraph
      data:
        text: "-\t Cho phép bộ phận nhân sự CTTV thiết lập nhóm trả lương nhóm trả lương áp dụng cho từng hồ sơ nhân sự riêng biệt."
  version: 2.30.7
screen_design: null
module: PR
local_fields: null
mock_data: []
local_buttons: null
layout: layout-tabs
form_config:
  formSize:
    create: largex
    edit: largex
    proceed: largex
  formTitle:
    create: Create paygroup for employee
    edit: Edit paygroup for employee
    view: View paygroup for employee
    proceed: Edit paygroup for employee
  defaultValidateError: Required fields have not been filled in completely (fields marked with *)
  fields:
    - type: select
      name: employee
      label: Employee
      placeholder: Select Employee
      clearFieldsAfterChange:
        - objectJobData
      isLazyLoad: true
      validators:
        - type: required
      _select:
        transform: >-
          $employeesList($.extend.limit, $.extend.page, $.extend.search, null,
          null, null,$.fields.effectiveDate)
      _validateFn:
        transform: >-
          $not($isNilorEmpty($.value.employeeId)) ? (
          $isNilorEmpty($employeesList(1, 1,'',
          $.value.employeeId,null,$.value.employeeRecordNumber,
          $.fields.effectiveDate)[0]) ?  '_setSelectValueNull' ) 
      _condition:
        transform: $.extend.formType = 'create'
      outputValue: value
    - type: text
      name: jobDataId
      dependantField: $.fields.employeeId; $.fields.effectiveDate
      label: jobDataId
      unvisible: true
      _value:
        transform: $.variables._jobData.id
    - type: text
      name: employeeRecordIdJobData
      dependantField: $.fields.employeeId; $.fields.effectiveDate
      label: Employee
      unvisible: true
      _value:
        transform: >-
          ($not($isNilorEmpty($.variables._jobData)) ?
          $.variables._jobData.employeeId &
          $.variables._jobData.employeeRecordNumber &
          $string($.fields.dateToShowEmployee))
    - type: text
      name: dateToShowEmployee
      unvisible: true
      _value:
        transform: >-
          $not($isNilorEmpty($.fields.effectiveDate)) ?
          $DateToTimestampUTC($.fields.effectiveDate) :
          $DateToTimestampUTC($now())
    - type: text
      name: dataEmployee
      dependantField: $.fields.employeeId; $.fields.effectiveDate
      unvisible: true
      _value:
        transform: >-
          $not($isNilorEmpty($.fields.employeeId)) ? {'employeeId':
          $.fields.employeeId , 'employeeRecordNumber':
          $.fields.employeeRecordNumber, 'dateToShowEmployee':
          $.fields.dateToShowEmployee} : null
    - type: text
      name: employeeIdView
      key: employeeIdView
      label: Employee
      _condition:
        transform: $.extend.formType = 'view'
    - type: text
      name: employeeId
      label: Employee
      dependantField: $.fields.employee.employeeId
      unvisible: true
      _value:
        transform: $.fields.employee.employeeId
    - type: text
      name: employeeRecordNumber
      label: EmployeeRecordNumber
      dependantField: $.fields.employee.employeeRecordNumber
      unvisible: true
      _value:
        transform: $string($.fields.employee.employeeRecordNumber)
    - type: text
      name: objectJobData
      unvisible: true
      _value:
        transform: >-
          $not($isNilorEmpty($.fields.employeeId)) ?
          $jobDatasDetail($.fields.employeeId,
          $.fields.employeeRecordNumber,$.fields.dateToShowEmployee) : null
    - type: text
      name: jobDataId
      label: jobDataId
      unvisible: true
      dependantField: $.fields.employee.jobDataId
      _value:
        transform: $.fields.employee.jobDataId
    - type: text
      name: employeeGroupName
      key: employeeGroupName
      label: Employee Group
      _condition:
        transform: $.extend.formType = 'view'
    - type: text
      name: employeeRecordNumberView
      key: employeeRecordNumberView
      label: Employee Record Number
      _condition:
        transform: $.extend.formType = 'view'
      _value:
        transform: $string($.fields.employeeRecordNumberView)
    - type: text
      name: fullName
      key: fullName
      label: Employee Name
      _condition:
        transform: $.extend.formType = 'view'
    - type: text
      name: companyName
      key: companyName
      label: Company Name
      _condition:
        transform: $.extend.formType = 'view'
    - type: text
      name: legalEntityName
      key: legalEntityName
      label: Legal Entity
      _condition:
        transform: $.extend.formType = 'view'
    - type: text
      name: businessUnitName
      key: businessUnitName
      label: Business Unit
      _condition:
        transform: $.extend.formType = 'view'
    - type: text
      name: divisionName
      key: divisionName
      label: Division
      _condition:
        transform: $.extend.formType = 'view'
    - type: text
      name: departmentName
      key: departmentName
      label: Department
      _condition:
        transform: $.extend.formType = 'view'
    - type: text
      name: jobCodeName
      key: jobCodeName
      label: Job Title
      _condition:
        transform: $.extend.formType = 'view'
    - type: text
      name: contractTypeName
      key: contractTypeName
      label: Contract Type
      _condition:
        transform: $.extend.formType = 'view'
    - type: text
      name: locationName
      key: locationName
      label: Location
      _condition:
        transform: $.extend.formType = 'view'
    - type: group
      n_cols: 2
      fields:
        - type: dateRange
          label: Effective Date
          name: effectiveDate
          clearFieldsAfterChange:
            - objectJobData
          mode: date-picker
          setting:
            type: day
            format: dd/MM/yyyy
          validators:
            - type: required
          _value:
            transform: $.extend.formType='create' ? $now()
        - type: text
          name: companyCode
          unvisible: true
          dependantField: $.fields.employee.employeeId
          _value:
            transform: $.variables._jobData.companyCode
        - type: select
          label: Pay Group
          name: payGroupCode
          outputValue: value
          dependantField: $.fields.employee.employeeId
          placeholder: Select PayGroup
          isLazyLoad: true
          _select:
            transform: >-
              ($.fields.companyCode;$payGroupsList($.extend.limit,
              $.extend.page,$.extend.search,$.fields.effectiveDate,$.fields.companyCode))
          validators:
            - type: required
    - type: radio
      label: Status
      name: status
      validators:
        - type: required
      _value:
        transform: $.extend.formType='create' ? true
      radio:
        - label: Active
          value: true
        - label: Inactive
          value: false
    - type: translationTextArea
      label: Note
      name: note
      placeholder: Enter Note
      textarea:
        autoSize:
          minRows: 3
        maxCharCount: 1000
      validators:
        - type: maxLength
          args: 1000
  overview:
    border: true
    dependentField: employeeRecordIdJobData
    noDataMessages: Choose Employee ID getting data
    header: Employee Detail
    display:
      - key: companyName
        label: Company
        _value:
          transform: $.variables._jobData.companyName
      - key: legalEntityName
        label: Legal entity
        _value:
          transform: $.variables._jobData.legalEntityName
      - key: businessUnitName
        label: Business unit
        _value:
          transform: $.variables._jobData.businessUnitName
      - key: divisionName
        label: Division
        _value:
          transform: $.variables._jobData.divisionName
      - key: departmentName
        label: Department
        _value:
          transform: $.variables._jobData.departmentName
      - key: jobTitleName
        label: Job Title
        _value:
          transform: $.variables._jobData.jobTitleName
      - key: contractTypeName
        label: Contract Type
        _value:
          transform: $.variables._jobData.contractTypeName
      - key: locationName
        label: Location
        _value:
          transform: $.variables._jobData.locationName
  sources:
    jobDatasList:
      uri: '"/api/personals/"  & $.employeeId &  "/job-datas"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $.data
      disabledCache: true
      params:
        - employeeId
    personalsList:
      uri: '"/api/personals?page=1&limit=1000"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.employeeId, 'value':
        $item.employeeId}})[]
      disabledCache: true
    payGroupsList:
      uri: '"/api/pay-group-structures"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page,'search': $.search,'filter':
        [{'field':'IsCombobox','operator':
        '$eq','value':true},{'field':'status','operator': '$eq','value':true},
        {'field': 'effectiveDate', 'operator': '$lte', 'value':
        $.effectiveDate},{'field': 'companyCode', 'operator': '$eq', 'value':
        $.companyCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
        - companyCode
    employeesList:
      uri: '"/api/pr-employees"'
      method: GET
      queryTransform: >-
        {'limit': $.limit ,'page': $.page, 'search': $.search,'filter':
        [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'employeeId','operator':
        '$eq','value':$.empId},{'field':'employeeGroupCode','operator':
        '$cont','value':$.grpCode},{'field':'employeeRecordNumber','operator':
        '$eq','value':$.ern}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label':
        $join($filter([$item.employeeId,$item.employeeGroupCode,$string($item.employeeRecordNumber),$item.fullName],
        $boolean), ' - ')  , 'value': {'employeeId': $item.employeeId, 'code':
        $item.employeeId, 'employeeRecordNumber':
        $string($item.employeeRecordNumber), 'jobDataId': $item.jobDataId}}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - empId
        - grpCode
        - ern
        - effectiveDate
    jobDatasDetail:
      uri: >-
        "/api/pr-employees/"  & $.employeeId & "/employee-record-number/" &
        $string($.ern) & "/effective-date/" & $string($.effectiveDate)
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $
      disabledCache: true
      params:
        - employeeId
        - ern
        - effectiveDate
  variables:
    _jobData:
      transform: $.fields.objectJobData
    _fieldEmp:
      transform: $.fields.employee
filter_config: null
layout_options:
  show_dialog_form_save_add_button: true
  hide_action_row: true
  reset_page_index_after_do_action:
    edit: true
    create: true
  tabs_title:
    - Assigned
    - Unassigned
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: null
layout_options__footer_buttons: null
layout_options__row_actions: null
backend_url: /api/employee-pay-groups
screen_name: employee-pay-groupss
layout_options__actions_many: null
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: employeeId
    defaultName: EmployeeId
  - name: employeeRecordNumber
    defaultName: EmployeeRecordNumber
  - name: payGroupCode
    defaultName: PayGroupCode
  - name: companyCode
    defaultName: CompanyCode
  - name: legalEntityCode
    defaultName: LegalEntityCode
  - name: businessUnitCode
    defaultName: BusinessUnitCode
  - name: divisionCode
    defaultName: DivisionCode
  - name: departmentCode
    defaultName: DepartmentCode
screen_designs: []
function_specs: []
fields: []
children:
  - PR.FS.FR.102.assigned
  - PR.FS.FR.102.unassigned
menu_item:
  title: 'Set up paygroup for employee '
  parent:
    title: PR Setting
