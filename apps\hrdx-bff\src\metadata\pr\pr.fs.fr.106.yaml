id: PR.FS.FR.106
status: draft
sort: 353
user_created: 2aa5db12-fa54-46d6-8e05-9e421858de0c
date_created: '2024-07-31T03:19:28.799Z'
user_updated: b403d5da-d684-45e4-ae6d-e25223baedc5
date_updated: '2025-06-13T01:45:41.712Z'
title: Element
requirement:
  time: 1722393711612
  blocks:
    - id: mKov4Ig29O
      type: paragraph
      data:
        text: >-
          - <PERSON><PERSON><PERSON> hình <PERSON>n lý các tiêu chí hệ thống giúp người dùng thiết lập các
          tiêu chí hệ thống lấy trực tiếp từ các phân hệ chưa qua xử lý tính
          toán dùng để tính lương.

          Ví dụ: Lương theo chức danh, <PERSON><PERSON><PERSON><PERSON> ABCD, <PERSON><PERSON><PERSON>ô<PERSON>, <PERSON><PERSON><PERSON> công
          thực tế,...
  version: 2.29.1
screen_design: null
module: PR
local_fields:
  - code: code
    pinned: true
    title: Element Code
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    show_sort: true
  - code: shortName
    title: Short Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: longName
    title: Long Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: tableCountryName
    title: Country
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: priority
    title: Priority
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: dataTypeName
    title: Data Type
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: expressions
    title: Formula
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: status
    title: Status
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Boolean Tag
      collection: field_types
    show_sort: true
  - code: note
    title: Note
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: updatedBy
    title: Last Updated By
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: updatedAt
    title: Last Updated On
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
    show_sort: true
mock_data: []
local_buttons: null
layout: layout-table
form_config:
  _formTitle:
    edit: '''Edit '''
    view: '''View detail Element'''
  defaultValidateError: Required fields have not been filled in completely (fields marked with *)
  fields:
    - type: group
      _n_cols:
        transform: '$.extend.formType = ''view'' ? 1 : 2'
      fields:
        - type: select
          label: Country
          name: countryCode
          placeholder: Select Country
          outputValue: value
          _select:
            transform: $.variables._countryList
          _condition:
            transform: '$.extend.formType = ''create'' or $.extend.formType = ''edit'' '
        - type: text
          label: Element Code
          name: code
          placeholder: Auto-generated code
          disabled: true
          validators:
            - type: required
        - type: number
          label: Priority
          name: priority
          placeholder: Enter Priority
          validators:
            - type: pattern
              args: ^[0-9]*$
              text: Please enter only integers
        - type: text
          formatType: code
          label: Short Name
          placeholder: Enter Short Name
          name: shortName
          validators:
            - type: required
            - type: maxLength
              args: '300'
              text: Short Name should not exceed 300 characters
    - type: text
      label: Country
      name: countryName
      _condition:
        transform: $.extend.formType = 'view'
    - type: translation
      label: Long Name
      placeholder: Enter Long Name
      name: longName
      validators:
        - type: required
        - type: maxLength
          args: '500'
          text: Long Name should not exceed 500 characters
    - type: select
      label: Data Type
      name: dataType
      outputValue: value
      _select:
        transform: $dataTypesList()
      validators:
        - type: required
    - type: selectCustom
      name: param
      label: Param
      placeholder: Select Param
      _condition:
        transform: '$.extend.formType = ''create'' or $.extend.formType = ''edit'' '
      _select:
        transform: >-
          $paramList($.extend.limit, $.extend.page,
          $.extend.search,$.extend.filter.module, $.extend.filter.paramCode,
          $.extend.filter.paramName, $.extend.filter.paramDescription)
      actions:
        - id: search
          title: Select Param
      isLazyLoad: true
      actionsConfig:
        search:
          formConfig:
            fields:
              - type: group
                collapsed: false
                disableEventCollapse: true
                n_cols: 2
                fields:
                  - type: select
                    label: Module
                    name: module
                    outputValue: value
                    placeholder: Select Module
                    _select:
                      transform: $modules()
                  - type: text
                    label: Param Code
                    name: paramCode
                    placeholder: Enter Param Code
                  - type: text
                    label: Param Name
                    name: paramName
                    placeholder: Enter Param Name
                  - type: text
                    label: Description
                    name: paramDescription
                    placeholder: Enter Description
            sources:
              modules:
                uri: '"/api/report-column-sys/modules"'
                method: GET
                queryTransform: ''
                bodyTransform: ''
                headerTransform: ''
                resultTransform: >-
                  $map($, function($item) {{'label': $item.value, 'value':
                  $item.value}})
                disabledCache: true
          table:
            fields:
              - name: code
                label: Param code
                displayType: Hyperlink
              - name: name
                label: Param Name
              - name: dataType
                label: Data Type
              - name: serviceName
                label: Module
              - name: entityName
                label: Description
    - type: textarea
      label: Formula
      placeholder: Enter Formula
      name: expressions
      textarea:
        autoSize:
          minRows: 3
        maxCharCount: 1000
      dependantField: $.fields.param
      validators:
        - type: maxLength
          args: 1000
        - type: required
      updateType: push
      _value:
        transform: >-
          $boolean($.fields.param.value.code) ? $.fields.param.value.entityClass
          & '.' & $.fields.param.value.code : ''
    - type: radio
      label: Status
      name: status
      value: true
      radio:
        - value: true
          label: Active
        - value: false
          label: Inactive
    - type: translationTextArea
      label: Note
      placeholder: Enter Note
      name: note
      textarea:
        autoSize:
          minRows: 3
        maxCharCount: 1000
      validators:
        - type: maxLength
          args: 1000
  sources:
    countryList:
      uri: '"/api/picklists/COUNTRIES/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})
      disabledCache: true
    paramList:
      uri: '"/api/salary-formulas/report-source-columns-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'Module','operator': '$eq','value':
        $.module},{'field':'ParamCode','operator': '$eq','value':
        $.paramCode},{'field':'ParamName','operator': '$eq','value':
        $.paramName},{'field':'Description','operator': '$eq','value':
        $.paramDescription}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.items, function($item) {{'label': $item.serviceName & ' - ' &
        $item.code & ' - ' & $item.name, 'value': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - module
        - paramCode
        - paramName
        - paramDescription
    dataTypesList:
      uri: '"/api/salary-formulas/data-types-v2"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.key, 'value':
        $item.value,'description': $item.example}})
      disabledCache: true
  variables:
    _countryList:
      transform: $countryList()
  footer:
    create: true
    update: true
    createdOn: createdAt
    updatedOn: updatedAt
    createdBy: createdBy
    updatedBy: updatedBy
filter_config:
  fields:
    - type: selectAll
      label: Country
      name: countryCode
      labelType: type-grid
      placeholder: Select Country
      mode: multiple
      _options:
        transform: $countryList()
    - type: selectAll
      label: Element Code
      name: code
      labelType: type-grid
      mode: multiple
      placeholder: Select Code
      isLazyLoad: true
      _options:
        transform: $elementList($.extend.limit, $.extend.page, $.extend.search)
    - type: text
      label: Short Name
      placeholder: Enter Short Name
      labelType: type-grid
      name: shortName
    - type: text
      label: Long Name
      placeholder: Enter Long Name
      labelType: type-grid
      name: name
    - type: number
      label: Priority
      placeholder: Enter Priority
      labelType: type-grid
      name: priority
    - type: selectAll
      label: Data Type
      name: dataType
      labelType: type-grid
      mode: multiple
      placeholder: Select Data Type
      isLazyLoad: true
      _options:
        transform: $dataTypesList()
    - type: text
      label: Formula
      placeholder: Enter Formula
      labelType: type-grid
      name: expressions
    - type: radio
      label: Status
      labelType: type-grid
      name: status
      value: ''
      radio:
        - label: All
          value: ''
        - label: Active
          value: true
        - label: Inactive
          value: false
    - name: updatedBy
      label: Last Updated By
      type: text
      labelType: type-grid
      placeholder: Enter Last Updated By
    - name: updatedAt
      label: Last Updated On
      type: dateRange
      labelType: type-grid
      setting:
        mode: date
        format: dd/MM/yyyy
  filterMapping:
    - field: countryCode
      operator: $in
      valueField: countryCode.(value)
    - field: code
      operator: $in
      valueField: code.(value)
    - field: shortName
      operator: $cont
      valueField: shortName
    - field: longName
      operator: $cont
      valueField: name
    - field: priority
      operator: $eq
      valueField: priority
    - field: dataType
      operator: $in
      valueField: dataType.(value)
    - field: expressions
      operator: $cont
      valueField: expressions
    - field: encryption
      operator: $eq
      valueField: encryption
    - field: status
      operator: $eq
      valueField: status
    - field: updatedBy
      operator: $cont
      valueField: updatedBy
    - field: updatedAt
      operator: $between
      valueField: updatedAt
  sources:
    elementList:
      uri: '"/api/report-column-sys"'
      method: GET
      queryTransform: '{''limit'': $.limit, ''page'': $.page, ''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.code, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    countryList:
      uri: '"/api/picklists/COUNTRIES/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})
      disabledCache: true
    personalNameList:
      uri: '"/api/personals/all-employees"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.fullName & ' - ' & $item.email
        , 'value': $item.email}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    userList:
      uri: '"/api/internal-users"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.userName, 'value':
        $item.userName}})
      disabledCache: true
    dataTypesList:
      uri: '"/api/salary-formulas/data-types-v2"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: '$map($, function($item) {{''label'': $item.key, ''value'': $item.value}})'
      disabledCache: true
layout_options:
  show_detail_history: false
  show_dialog_form_save_add_button: true
  delete_multi_items: true
  apply_delete_multi_items_to_delete_one: true
  custom_delete_body: '{''ids'': $map($.data,function($item){$item.id})[]}'
  toolTable:
    export: true
    adjustDisplay: true
  tool_table:
    - id: export
      icon: icon-download-simple
  show_filter_results_message: true
  store_selected_items: true
  show_dialog_duplicate_button: false
  hide_action_row: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: null
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    icon: icon-pencil-simple
    type: ghost-gray
  - id: delete
    icon: icon-trash
    type: ghost-gray
backend_url: /api/report-column-sys
screen_name: null
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: countryCode
    defaultName: CountryCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Element
  parent:
    title: PR Setting
