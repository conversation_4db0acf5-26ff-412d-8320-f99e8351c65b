id: PR.FS.FR.109
status: draft
sort: null
user_created: b403d5da-d684-45e4-ae6d-e25223baedc5
date_created: '2025-01-22T08:06:27.650Z'
user_updated: b403d5da-d684-45e4-ae6d-e25223baedc5
date_updated: '2025-07-17T16:30:12.691Z'
title: Packages by Job
requirement:
  time: 1743131565022
  blocks:
    - id: oBlO5m43gM
      type: paragraph
      data:
        text: '&nbsp;'
  version: 2.30.7
screen_design: null
module: PR
local_fields:
  - code: code
    pinned: true
    title: Code
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    show_sort: true
  - code: companyName
    pinned: false
    title: Company
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: jobName
    title: Job
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: incomePackageNames
    title: Package
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tags
      collection: field_types
    show_sort: true
  - code: effectiveDate
    title: Effective Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    show_sort: true
  - code: note
    title: Note
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: updatedAt
    title: Last Updated On
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
    show_sort: true
  - code: updatedBy
    title: Last Updated By
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: status
    title: Status
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: Boolean Tag
      collection: field_types
    show_sort: true
mock_data: null
local_buttons: null
layout: layout-table
form_config:
  formSize:
    create: large
    edit: large
    proceed: large
  defaultValidateError: Required fields have not been filled in completely (fields marked with *)
  formTitle:
    create: 'Add new: Set up Packages by Job'
    edit: 'Edit: Set up Packages by Job'
    view: 'View Detail: Packages by Job'
  effectiveDateValidations:
    - sameDate
    - greaterThanLastest
  historyHeaderTitle: '''View Detail: Packages by Job'''
  fields:
    - type: group
      _n_cols:
        transform: '$.extend.formType = ''view'' ? 1 : 2'
      fields:
        - type: text
          label: Code
          name: code
          placeholder: System - Generated
          _disabled:
            transform: 'true'
          _condition:
            transform: $not($.extend.formType = 'view')
          validators:
            - type: required
          col: 2
        - type: text
          label: Code
          name: code
          placeholder: System - Generated
          _disabled:
            transform: 'true'
          _condition:
            transform: $.extend.formType = 'view'
          validators:
            - type: required
        - type: dateRange
          label: Effective Date
          name: effectiveDate
          mode: date-picker
          placeholder: dd/MM/yyyy
          setting:
            type: day
            format: dd/MM/yyyy
          validators:
            - type: required
        - type: radio
          label: Status
          name: status
          _value:
            transform: $.extend.formType = 'create' ? true
          radio:
            - label: Active
              value: true
            - label: Inactive
              value: false
        - type: select
          label: Company
          placeholder: Select Company
          isLazyLoad: true
          _condition:
            transform: $not($.extend.formType = 'view')
          clearFieldsAfterChange:
            - jobObj
          name: companyObj
          validators:
            - type: required
          outputValue: value
          _select:
            transform: >-
              $companiesList($.extend.limit, $.extend.page, $.extend.search,
              $.fields.effectiveDate)
        - type: text
          label: Company
          placeholder: Select Company
          isLazyLoad: true
          _condition:
            transform: $.extend.formType = 'view'
          _value:
            transform: >-
              $.extend.defaultValue.companyName ?
              $.extend.defaultValue.companyName & ' (' &
              $.extend.defaultValue.companyCode & ') ': null
          clearFieldsAfterChange:
            - jobObj
          name: companyObjView
          validators:
            - type: required
          outputValue: value
        - type: select
          label: Job
          placeholder: Select Job
          validators:
            - type: required
          isLazyLoad: true
          name: jobObj
          outputValue: value
          _select:
            transform: >-
              $jobList($.extend.limit, $.extend.page, $.extend.search,
              $.fields.effectiveDate, $.fields.companyObj.id)
        - unvisible: true
          name: dependantPackageField
          type: text
          _value:
            transform: $.fields.companyObj.code
        - unvisible: true
          name: dependantEffectiveDatePackageField
          type: text
          _value:
            transform: $.fields.effectiveDate
        - type: array
          mode: table
          name: incomePackageJobDetails
          minSize: 1
          arrayOptions:
            canChangeSize: true
            _customFieldValueAddItem:
              transform: >-
                {'priority' : $max($map($.fields.incomePackageJobDetails ,
                function($v) {$v.priority})) + 1}
          _condition:
            transform: $not($.extend.formType = 'view')
          field:
            type: group
            label: Package
            fields:
              - name: priority
                label: Priority
                type: number
                value: 1
                number:
                  min: 1
                width: 48px
                validators:
                  - type: required
                  - id: check_duplicate
                    type: ppx-custom
                    args:
                      transform: >-
                        $not($isNilorEmpty($.value)) ?
                        $count($map($.fields.incomePackageJobDetails,
                        function($item, $index) {($item.priority = $.value) ?
                        0})) > 1
                    text: Priority has been duplicated
                align: start
              - name: incomePackageObj
                label: Package
                key: incomePackageObj
                dependantFieldSkip: 1
                validators:
                  - type: required
                  - id: check_duplicate
                    type: ppx-custom
                    args:
                      transform: >-
                        $not($isNilorEmpty($.value)) ?
                        $count($map($.fields.incomePackageJobDetails,
                        function($item, $index) {($item.incomePackageObj =
                        $.value) ? {}})) > 1
                    text: Package has been duplicated
                type: select
                isLazyLoad: true
                outputValue: value
                align: start
                width: 100px
                _validateFn:
                  transform: >-
                    $not($isNilorEmpty($.value.code)) ? ($incomePackageList(1,
                    1, null, $.fields.companyObj.code ?
                    [$.fields.companyObj.code],$.fields.effectiveDate,
                    $.fields.companyObj.code ? true : null, $.value.code)[0] ?
                    $incomePackageList(1, 1, null, $.fields.companyObj.code ?
                    [$.fields.companyObj.code],$.fields.effectiveDate,
                    $.fields.companyObj.code ? true : null, $.value.code)[0] :
                    '_setSelectValueNull') 
                _select:
                  transform: >-
                    ($incomePackageList($.extend.limit, $.extend.page,
                    $.extend.search, $.fields.companyObj.code ?
                    [$.fields.companyObj.code],$.fields.effectiveDate,
                    $.fields.companyObj.code ? true : null))
              - name: note
                label: Note
                validators:
                  - type: maxLength
                    args: '1000'
                    text: Note should not exceed 1000 characters
                key: note
                type: text
                align: start
                width: 100px
          col: 2
        - type: array
          mode: table
          name: incomePackageJobDetails
          minSize: 1
          arrayOptions:
            canChangeSize: true
          _condition:
            transform: $.extend.formType = 'view'
          field:
            type: group
            label: Package
            fields:
              - name: priority
                label: Priority
                type: number
                number:
                  min: 1
                width: 48px
                value: 1
                validators:
                  - type: required
                align: start
              - name: incomePackageObj
                label: Package
                key: incomePackageObj
                dependantFields: $.fields.companyObj
                type: select
                isLazyLoad: true
                outputValue: value
                align: start
                width: 100px
                _select:
                  transform: >-
                    $incomePackageList($.extend.limit, $.extend.page,
                    $.extend.search)
              - name: note
                label: Note
                key: note
                type: text
                align: start
                width: 100px
        - type: translationTextArea
          label: Note
          name: note
          _condition:
            transform: $not($.extend.formType = 'view')
          placeholder: Enter Note
          textarea:
            autoSize:
              minRows: 3
            maxCharCount: 1000
          validators:
            - type: maxLength
              args: 1000
              text: note should not exceed 1000 characters
          col: 2
        - type: translationTextArea
          label: Note
          name: note
          _condition:
            transform: $.extend.formType = 'view'
          placeholder: Enter Note
          textarea:
            autoSize:
              minRows: 3
            maxCharCount: 1000
          validators:
            - type: maxLength
              args: 1000
              text: note should not exceed 1000 characters
  sources:
    companiesList:
      uri: '"/api/companies/get-by"'
      method: GET
      queryTransform: >-
        { 'limit': $.limit, 'page': $.page, 'search': $.search, 'filter':
        [{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator': '$eq','value':
        $.effectiveDate},{'field':'code','operator': '$in','value': $.code}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'code' : $item.code, 'id':$item.id} }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
        - code
    jobList:
      uri: '"/api/job-codes/get-by"'
      method: GET
      queryTransform: >-
        { 'limit': $.limit, 'page': $.page, 'search': $.search, 'filter':
        [{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator': '$eq','value':
        $.effectiveDate},{'field':'code','operator': '$in','value': $.code},
        {'field':'CompanyIds','operator': '$eq','value': $.companyIds}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'code' :$item.code} }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
        - companyIds
        - code
    incomePackageList:
      uri: '"/api/income-packages"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'search': $.search, 'filter':
        [{'field':'IsCombobox','operator':
        '$eq','value':true},{'field':'IsEmptyCompany','operator':
        '$eq','value':$.emptyPackage},{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator': '$lte','value':
        $.effectiveDate},{'field':'CompanyCodes','operator': '$eq','value':
        $.companyCode},{'field':'code','operator': '$eq','value': $.code}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': {'code' :$item.code} }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - companyCode
        - effectiveDate
        - emptyPackage
        - code
  variables: {}
filter_config:
  fields:
    - type: text
      label: Code
      name: code
      placeholder: Enter Code
      labelType: type-grid
    - type: dateRange
      label: Effective Date
      name: effectiveDateFrom
      setting:
        format: dd/MM/yyyy
      labelType: type-grid
    - name: status
      labelType: type-grid
      label: Status
      type: radio
      value: ''
      radio:
        - label: All
          value: ''
        - label: Active
          value: true
        - label: Inactive
          value: false
    - type: selectAll
      label: Company
      labelType: type-grid
      placeholder: Select Company
      name: companyCode
      isLazyLoad: true
      _options:
        transform: $companiesList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      label: Job
      labelType: type-grid
      placeholder: Select Job
      name: jobCode
      isLazyLoad: true
      _options:
        transform: $jobCodeList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      label: Package
      labelType: type-grid
      placeholder: Select Package
      name: incomePackageCodes
      isLazyLoad: true
      _options:
        transform: $incomePackageList($.extend.limit, $.extend.page, $.extend.search)
    - name: updatedBy
      label: Last Updated By
      type: text
      labelType: type-grid
      placeholder: Enter Last Updated By
    - name: updatedAt
      label: Last Updated On
      type: dateRange
      labelType: type-grid
      setting:
        mode: date
        format: dd/MM/yyyy
  filterMapping:
    - field: code
      operator: $cont
      valueField: code
    - field: effectiveDate
      operator: $between
      valueField: effectiveDateFrom
    - field: status
      operator: $eq
      valueField: status
    - field: companyCode
      operator: $in
      valueField: companyCode.(value)
    - field: jobCode
      operator: $in
      valueField: jobCode.(value)
    - field: incomePackageCodes
      operator: $eq
      valueField: incomePackageCodes.(value)
    - field: updatedBy
      operator: $cont
      valueField: updatedBy
    - field: updatedAt
      operator: $between
      valueField: updatedAt
  sources:
    incomePackageList:
      uri: '"/api/income-packages"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'IsCombobox','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    userList:
      uri: '"/api/internal-users"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.userName, 'value':
        $item.userName}})
      disabledCache: true
    companiesList:
      uri: '"/api/companies"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    jobCodeList:
      uri: '"/api/job-codes"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
layout_options:
  delete_multi_items: true
  apply_delete_multi_items_to_delete_one: true
  custom_delete_body: '{''ids'': $map($.data,function($item){$item.id})[]}'
  custom_history_backend_url: /api/income-package-jobs/:id/clone
  reset_page_index_after_do_action:
    edit: true
  toolTable:
    export: true
    adjustDisplay: true
  tool_table:
    - id: import
      paramsRedirect:
        type: PR_OBJECT
        entityOrObj: IncomePackageJobImportModel
    - id: export
      icon: icon-download-simple
  show_dialog_form_save_add_button: true
  show_filter_results_message: true
  store_selected_items: true
  hide_action_row: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: null
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: duplicate
    title: Duplicate
    icon: icon-copy-bold
    type: ghost-gray
  - id: edit
    title: Edit
    icon: icon-pencil-simple
    type: ghost-gray
  - id: delete
    title: Delete
    icon: icon-trash
    type: ghost-gray
backend_url: /api/income-package-jobs
screen_name: null
layout_options__actions_many: null
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: companyCode
    defaultName: CompanyCode
  - name: jobCode
    defaultName: JobCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item: null
