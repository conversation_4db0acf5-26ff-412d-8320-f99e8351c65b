id: PR.FS.FR.110
status: draft
sort: 642
user_created: b403d5da-d684-45e4-ae6d-e25223baedc5
date_created: '2025-02-10T07:49:29.921Z'
user_updated: b403d5da-d684-45e4-ae6d-e25223baedc5
date_updated: '2025-07-21T08:49:59.083Z'
title: Management Package System
requirement:
  time: 1742177014697
  blocks:
    - id: oBlO5m43gM
      type: paragraph
      data:
        text: '&nbsp;'
  version: 2.30.7
screen_design: null
module: PR
local_fields:
  - code: code
    pinned: true
    title: Code
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    show_sort: true
  - code: incomePackageName
    title: Package
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: companyName
    pinned: false
    title: Company
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: legalEntityNames
    title: Legal Entity
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tags
      collection: field_types
    show_sort: true
  - code: locationNames
    title: Location
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tags
      collection: field_types
    show_sort: true
  - code: min
    title: Min
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Currency
      collection: field_types
    show_sort: false
  - code: mid
    title: Mid
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Currency
      collection: field_types
    show_sort: false
  - code: max
    title: Max
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Currency
      collection: field_types
    show_sort: false
  - code: currencyName
    title: Currency
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: effectiveDate
    title: Effective Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    show_sort: true
  - code: status
    title: Status
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: Boolean Tag
      collection: field_types
    show_sort: true
  - code: note
    title: Note
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: updatedAt
    title: Last Updated On
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
    show_sort: true
  - code: updatedBy
    title: Last Updated By
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
mock_data: null
local_buttons: null
layout: layout-table
form_config:
  effectiveDateValidations:
    - sameDate
    - greaterThanLastest
  formSize:
    create: large
    edit: large
    proceed: large
  defaultValidateError: Required fields have not been filled in completely (fields marked with *)
  formTitle:
    create: Add new Package System
    edit: Edit Package System
    view: Package System Details
  historyHeaderTitle: '''Package System Details'''
  fields:
    - type: group
      _n_cols:
        transform: '$.extend.formType = ''view'' ? 1 : 2'
      fields:
        - type: text
          label: Code
          name: code
          placeholder: System - Generated
          _disabled:
            transform: 'true'
          _condition:
            transform: $not($.extend.formType = 'view')
          validators:
            - type: required
          col: 2
        - type: text
          label: Code
          name: code
          placeholder: System - Generated
          _disabled:
            transform: 'true'
          _condition:
            transform: $.extend.formType = 'view'
          validators:
            - type: required
        - type: select
          label: Package
          placeholder: Select Package
          validators:
            - type: required
          isLazyLoad: true
          name: incomePackageObj
          clearFieldsAfterChange:
            - companyObj
            - legalEntities
            - locations
          outputValue: value
          _select:
            transform: >-
              $incomePackageList($.extend.limit, $.extend.page, $.extend.search
              ,$.fields.effectiveDate)
        - type: select
          label: Company
          placeholder: Select Company
          isLazyLoad: true
          name: companyObj
          _condition:
            transform: $not($.extend.formType = 'view')
          validators:
            - type: required
          clearFieldsAfterChange:
            - legalEntities
            - locations
          outputValue: value
          _select:
            transform: >-
              $companiesList($.extend.limit, $.extend.page,
              $.extend.search,$.fields.incomePackageObj.companyCodes
              ,$.fields.effectiveDate)
        - type: text
          label: Company
          placeholder: Select Company
          isLazyLoad: true
          _condition:
            transform: $.extend.formType = 'view'
          _value:
            transform: >-
              $.extend.defaultValue.companyName ?
              $.extend.defaultValue.companyName & ' (' &
              $.extend.defaultValue.companyCode & ') ': null
          clearFieldsAfterChange:
            - jobObj
          name: companyObjView
          validators:
            - type: required
          outputValue: value
        - type: selectAll
          label: Legal Entity
          placeholder: Select Legal Entity
          isLazyLoad: true
          name: legalEntities
          validators:
            - type: required
          outputValue: value
          _options:
            transform: >-
              $legalEntitiesList($.extend.limit, $.extend.page, $.extend.search
              ,$.fields.effectiveDate, $.fields.companyObj.id)
        - type: selectAll
          label: Location
          placeholder: Select Location
          isLazyLoad: true
          name: locations
          outputValue: value
          _options:
            transform: >-
              $locationList($.extend.limit, $.extend.page, $.extend.search
              ,$.fields.effectiveDate,$.fields.companyObj.code)
        - type: group
          _n_cols:
            transform: '$.extend.formType = ''view'' ? 1 : 3'
          _condition:
            transform: $not($.extend.formType = 'view')
          col: 2
          fields:
            - type: number
              name: min
              label: Min
              placeholder: Enter Min
              number:
                format: currency
                max: '999999999999999'
                precision: 3
              validators:
                - type: ppx-custom
                  args:
                    transform: >-
                      $not($isNilorEmpty($.fields.mid)) and
                      $not($isNilorEmpty($.fields.min)) ? ($.fields.min ?
                      $number($.fields.min) : 0) >= ($.fields.mid ?
                      $number($.fields.mid) : 0)
                  text: The Min value must be less than the Mid value
              displayType: Currency
            - type: number
              name: mid
              label: Mid
              placeholder: Enter Mid
              validators:
                - type: required
                - type: ppx-custom
                  args:
                    transform: >-
                      $not($isNilorEmpty($.fields.mid)) ?
                      (($not($isNilorEmpty($.fields.min)) ? (($.fields.min ?
                      $number($.fields.min) : 0) >= ($.fields.mid ?
                      $number($.fields.mid) : 0))) or ((
                      $not($isNilorEmpty($.fields.max)) ? ($.fields.max ?
                      $number($.fields.max) : 0) <= ($.fields.mid ?
                      $number($.fields.mid) : 0))))
                  text: >-
                    The Mid value must be greater than the Min value and less
                    than the Max value
              number:
                format: currency
                max: '999999999999999'
                precision: 3
              displayType: Currency
            - type: number
              name: max
              label: Max
              validators:
                - type: ppx-custom
                  args:
                    transform: >-
                      $not($isNilorEmpty($.fields.mid)) and
                      $not($isNilorEmpty($.fields.max)) ? ($.fields.max ?
                      $number($.fields.max) : 0) <= ($.fields.mid ?
                      $number($.fields.mid) : 0)
                  text: The Max value must be greater than the Mid value
              placeholder: Enter Max
              number:
                format: currency
                max: '999999999999999'
                precision: 3
              displayType: Currency
        - type: group
          _n_cols:
            transform: '$.extend.formType = ''view'' ? 1 : 3'
          _condition:
            transform: $.extend.formType = 'view'
          fields:
            - type: number
              name: min
              label: Min
              placeholder: Enter Min
              number:
                format: currency
                max: '999999999999999'
                precision: 4
              displayType: Currency
            - type: number
              name: mid
              label: Mid
              placeholder: Enter Mid
              validators:
                - type: required
              number:
                format: currency
                max: '999999999999999'
                precision: 4
              displayType: Currency
            - type: number
              name: max
              label: Max
              placeholder: Enter Max
              number:
                format: currency
                max: '999999999999999'
                precision: 4
              displayType: Currency
        - type: select
          outputValue: value
          label: Currency
          name: currencyObj
          isLazyLoad: true
          placeholder: Select Currency
          _value:
            transform: >-
              $.extend.formType = 'create' ? $currencies($.extend.limit,
              $.extend.page, $.extend.search,'VND')[0]
          validators:
            - type: required
          _select:
            transform: $currencies($.extend.limit, $.extend.page, $.extend.search)
        - type: dateRange
          label: Effective Date
          name: effectiveDate
          mode: date-picker
          placeholder: dd/MM/yyyy
          _value:
            transform: $.extend.formType = 'create' ? $now()
          setting:
            type: day
            format: dd/MM/yyyy
          validators:
            - type: required
        - type: radio
          label: Status
          name: status
          _condition:
            transform: $not($.extend.formType = 'view')
          _value:
            transform: $.extend.formType = 'create' ? true
          radio:
            - label: Active
              value: true
            - label: Inactive
              value: false
          col: 2
        - type: radio
          label: Status
          name: status
          _condition:
            transform: $.extend.formType = 'view'
          radio:
            - label: Active
              value: true
            - label: Inactive
              value: false
        - type: translationTextArea
          name: note
          label: Note
          _condition:
            transform: $not($.extend.formType = 'view')
          placeholder: Enter Note
          textarea:
            autoSize:
              minRows: 3
            maxCharCount: 1000
          validators:
            - type: maxLength
              args: 1000
              text: note should not exceed 1000 characters
          col: 2
        - type: translationTextArea
          label: Note
          name: note
          _condition:
            transform: $.extend.formType = 'view'
          placeholder: Enter Note
          textarea:
            autoSize:
              minRows: 3
            maxCharCount: 1000
          validators:
            - type: maxLength
              args: 1000
              text: note should not exceed 1000 characters
  sources:
    currencies:
      uri: '"/api/picklists/CURRENCY/values/pagination"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'linkCatalogDataCode','operator':
        '$eq','value':$.linkCatalogDataCode},{'field':'code','operator':
        '$eq','value':$.code}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': {'code': $item.code} }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - code
        - linkCatalogDataCode
    companiesList:
      uri: '"/api/companies/get-by"'
      method: GET
      queryTransform: >-
        { 'limit': $.limit, 'page': $.page, 'search': $.search, 'filter':
        [{'field':'code','operator':
        '$in','value':$.code},{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator': '$eq','value':
        $.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id': $item.id,'code' :$item.code}}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - code
        - effectiveDate
    legalEntitiesList:
      uri: '"/api/legal-entities/get-by"'
      method: GET
      queryTransform: >-
        { 'limit': $.limit, 'page': $.page, 'search': $.search, 'filter':
        [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true}, {'field':'companyId', 'operator':
        '$eq','value':$.companyId}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id': $item.id,'code': $item.code} }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
        - companyId
    locationList:
      uri: '"/api/locations/get-by"'
      method: GET
      queryTransform: >-
        { 'limit': $.limit, 'page': $.page, 'search': $.search, 'filter':
        [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true}, {'field':'companyCode', 'operator':
        '$eq','value':$.companyCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': {'id': $item.id,'code': $item.code} }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
        - companyCode
    incomePackageList:
      uri: '"/api/income-packages"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'search': $.search, 'filter':
        [{'field':'IsCombobox','operator':
        '$eq','value':true},{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator': '$lte','value':
        $.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': {'code' :$item.code, 'companyCodes' :
        $map($item.companyScopes, function($itemCompany) {
        $itemCompany.companyCode })[]} }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
  variables: {}
filter_config:
  fields:
    - type: text
      label: Code
      name: code
      placeholder: Enter Code
      labelType: type-grid
    - type: selectAll
      label: Company
      labelType: type-grid
      placeholder: Select Company
      name: companyCode
      isLazyLoad: true
      _options:
        transform: $companiesList($.extend.limit, $.extend.page, $.extend.search)
    - type: select
      label: Packages
      name: incomePackageCode
      placeholder: Select Packages
      labelType: type-grid
      isLazyLoad: true
      _select:
        transform: >-
          $incomePackages($.extend.limit,$.extend.page,$.extend.search ,
          $map($.fields.companyCode,function($v) {$v.value}))
    - type: number
      labelType: type-grid
      label: Min
      name: min
      placeholder: Enter Min
      number:
        format: currency
        max: '999999999999999'
        precision: 3
      displayType: Currency
    - type: number
      labelType: type-grid
      label: Mid
      name: mid
      placeholder: Enter Mid
      number:
        format: currency
        max: '999999999999999'
        precision: 3
      displayType: Currency
    - type: number
      labelType: type-grid
      label: Max
      name: max
      placeholder: Enter Max
      number:
        format: currency
        max: '999999999999999'
        precision: 3
      displayType: Currency
    - type: selectAll
      label: Legal Entity
      labelType: type-grid
      placeholder: Select Legal Entity
      name: legalEntities
      isLazyLoad: true
      _options:
        transform: >-
          $legalEntitiesList($.extend.limit, $.extend.page,
          $.extend.search,$map($.fields.companyCode, function($item) {$item.id}
          )[])
    - type: selectAll
      label: Location
      labelType: type-grid
      placeholder: Select Location
      name: locationCodes
      isLazyLoad: true
      _options:
        transform: >-
          $locationsList($.extend.limit, $.extend.page,
          $.extend.search,$map($.fields.companyCode, function($item)
          {$item.value} )[])
    - name: currencyCode
      label: Currency
      labelType: type-grid
      isLazyLoad: true
      type: selectAll
      mode: multiple
      placeholder: Select Type
      _options:
        transform: $currenciesList($.extend.limit, $.extend.page, $.extend.search)
    - type: dateRange
      label: Effective Date
      name: effectiveDateFrom
      setting:
        format: dd/MM/yyyy
      labelType: type-grid
    - name: status
      labelType: type-grid
      label: Status
      type: radio
      value: ''
      radio:
        - label: All
          value: ''
        - label: Active
          value: true
        - label: Inactive
          value: false
    - type: text
      name: note
      placeholder: Enter Note
      labelType: type-grid
      label: Note
    - name: updatedBy
      label: Last Updated By
      type: text
      labelType: type-grid
      placeholder: Enter Last Updated By
    - name: updatedAt
      label: Last Updated On
      type: dateRange
      labelType: type-grid
      setting:
        mode: date
        format: dd/MM/yyyy
  filterMapping:
    - field: code
      operator: $cont
      valueField: code
    - field: companyCode
      operator: $in
      valueField: companyCode.(value)
    - field: incomePackageCode
      operator: $in
      valueField: incomePackageCode.(value)
    - field: min
      operator: $eq
      valueField: min
    - field: mid
      operator: $eq
      valueField: mid
    - field: max
      operator: $eq
      valueField: max
    - field: filterLegalEntityCodes
      operator: $eq
      valueField: legalEntities.(value)
    - field: filterLocationCodes
      operator: $eq
      valueField: locationCodes.(value)
    - field: currencyCode
      operator: $in
      valueField: currencyCode.(value)
    - field: effectiveDate
      operator: $between
      valueField: effectiveDateFrom
    - field: status
      operator: $eq
      valueField: status
    - field: note
      operator: $cont
      valueField: note
    - field: updatedBy
      operator: $cont
      valueField: updatedBy
    - field: updatedAt
      operator: $between
      valueField: updatedAt
  sources:
    incomePackageSystemList:
      uri: '"/api/income-package-systems"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.code, 'value':
        $item.code}})[]
      disabledCache: true
    userList:
      uri: '"/api/internal-users"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.userName, 'value':
        $item.userName}})
      disabledCache: true
    companiesList:
      uri: '"/api/companies"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    incomePackages:
      uri: '"/api/income-packages"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'CompanyCodes','operator': '$eq','value':$.CompanyCodes}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - CompanyCodes
    legalEntitiesList:
      uri: '"/api/legal-entities"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'companyId','operator': '$in','value':$.companyIds}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - companyIds
    locationsList:
      uri: '"/api/locations/get-by"'
      method: GET
      queryTransform: >-
        { 'limit': $.limit, 'page': $.page, 'search': $.search, 'filter': [
        {'field':'companyCode', 'operator': '$in','value':$.companyCodes}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - companyCodes
    currenciesList:
      uri: '"/api/picklists/CURRENCY/values/pagination"'
      method: GET
      queryTransform: '{''limit'': $.limit, ''page'': $.page, ''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
layout_options:
  delete_multi_items: true
  apply_delete_multi_items_to_delete_one: true
  custom_delete_body: '{''ids'': $map($.data,function($item){$item.id})[]}'
  custom_history_backend_url: /api/income-package-systems/:id/clone
  reset_page_index_after_do_action:
    edit: true
  toolTable:
    export: true
    adjustDisplay: true
  history_widget_header_options:
    duplicate: false
  tool_table:
    - id: import
      paramsRedirect:
        type: PR_OBJECT
        entityOrObj: IncomePackageSystem
    - id: export
      icon: icon-download-simple
  show_dialog_form_save_add_button: true
  show_filter_results_message: true
  store_selected_items: true
  show_dialog_duplicate_button: false
  hide_action_row: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: null
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    title: Edit
    icon: icon-pencil-simple
    type: ghost-gray
  - id: delete
    title: Delete
    icon: icon-trash
    type: ghost-gray
backend_url: /api/income-package-systems
screen_name: null
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: companyCode
    defaultName: CompanyCode
  - name: legalEntityListCodes
    defaultName: LegalEntityCode
  - name: locationListCodes
    defaultName: LocationCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item: null
