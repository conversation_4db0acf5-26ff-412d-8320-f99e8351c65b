id: PR.FS.FR.111
status: draft
sort: 100
user_created: b403d5da-d684-45e4-ae6d-e25223baedc5
date_created: '2025-02-14T02:58:22.524Z'
user_updated: b403d5da-d684-45e4-ae6d-e25223baedc5
date_updated: '2025-07-28T06:22:55.963Z'
title: Manage Employee Package
requirement:
  time: 1743655176788
  blocks:
    - id: tZ1QS0_YAt
      type: paragraph
      data:
        text: Manage Employee Package
  version: 2.30.7
screen_design: null
module: PR
local_fields:
  - code: employeeId
    title: Employee ID
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    pinned: true
    show_sort: true
  - code: fullName
    title: Full Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: employeeGroupName
    title: Employee Group
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: employeeRecordNumber
    title: ERN
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: jobCodeName
    title: Job Title
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: incomePackageName
    title: Package
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: min
    title: Min
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Currency
      collection: field_types
    show_sort: false
  - code: mid
    title: Mid
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Currency
      collection: field_types
  - code: max
    title: Max
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Currency
      collection: field_types
  - code: currencyName
    title: Currency
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: companyName
    title: Company
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: legalEntityName
    title: Legal Entity
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: divisionName
    title: Division
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: departmentName
    title: Department
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: locationName
    title: Location
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: effectiveDate
    title: Effective Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    show_sort: true
  - code: note
    title: Note
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    show_sort: true
  - code: status
    title: Status
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Boolean Tag
      collection: field_types
    show_sort: true
  - code: updatedBy
    title: Last Updated By
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    dragable: false
    show_sort: true
  - code: updatedAt
    title: Last Updated On
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
    show_sort: true
mock_data: []
local_buttons: null
layout: layout-table
form_config:
  formSize:
    create: large
    edit: large
    proceed: large
  effectiveDateValidations:
    - sameDate
    - greaterThanLastest
  defaultValidateError: Required fields have not been filled in completely (fields marked with *)
  formTitle:
    create: Add new Employee Package
    edit: Edit Employee Package
    view: Employee Package Details
  historyHeaderTitle: '''Employee Package Details'''
  fields:
    - type: group
      _n_cols:
        transform: '$.extend.formType = ''view'' ? 1 : 2'
      fields:
        - type: select
          name: employee
          label: Employee
          placeholder: Select Employee
          isLazyLoad: true
          validators:
            - type: required
          _select:
            transform: >-
              $employeesList($.extend.limit, $.extend.page, $.extend.search,
              null,null,null,$.fields.effectiveDate )
          _validateFn:
            transform: >-
              $not($isNilorEmpty($.value.employeeId)) ? (
              $isNilorEmpty($employeesList(1, 1,'',
              $.value.employeeId,null,$.value.employeeRecordNumber,
              $.fields.effectiveDate)[0]) ?  '_setSelectValueNull' ) 
          _condition:
            transform: $boolean($.extend.formType = 'create')
          outputValue: value
          col: 2
        - type: select
          name: employee
          label: Employee
          isLazyLoad: true
          placeholder: Select Employee
          validators:
            - type: required
          _select:
            transform: >-
              $employeesList($.extend.limit, $.extend.page, $.extend.search,
              null,null,null, $.fields.effectiveDate)
          _disabled:
            transform: 'true'
          _condition:
            transform: $.extend.formType = 'edit' or $.extend.formType = 'proceed'
          outputValue: value
          col: 2
        - type: text
          name: dateToShowEmployee
          unvisible: true
          _value:
            transform: >-
              $not($isNilorEmpty($.fields.effectiveDate)) ?
              $DateToTimestampUTC($.fields.effectiveDate) :
              $DateToTimestampUTC($now())
        - type: text
          name: dataEmployee
          unvisible: true
          dependantField: $.fields.employeeId; $.fields.effectiveDate
          _value:
            transform: >-
              $not($isNilorEmpty($.fields.employeeId)) ? {'employeeId':
              $.fields.employeeId , 'employeeRecordNumber':
              $.fields.employeeRecordNumber, 'dateToShowEmployee':
              $.fields.dateToShowEmployee} : null
        - type: text
          name: employeeIdView
          key: employeeIdView
          label: Employee
          _condition:
            transform: $.extend.formType = 'view'
          _value:
            transform: >-
              $join($filter([$.extend.defaultValue.employeeId,$.extend.defaultValue.employeeGroupCode,$string($.extend.defaultValue.employeeRecordNumber),$.extend.defaultValue.fullName],
              $boolean), ' - ')
        - type: text
          name: employeeId
          dependantField: $.fields.employee.employeeId
          unvisible: true
          _value:
            transform: $.fields.employee.employeeId
        - type: text
          name: employeeRecordNumber
          unvisible: true
          _value:
            transform: $string($.fields.employee.employeeRecordNumber)
        - type: select
          label: Package
          placeholder: Select Package
          isLazyLoad: true
          validators:
            - type: required
          name: incomePackageObj
          outputValue: value
          _select:
            transform: >-
              $incomePackageEmpList($.fields.employee.employeeId,$.fields.employee.employeeRecordNumber,$.fields.effectiveDate)
          _validateFn:
            transform: >-
              $exists($.value.code) and
              $not($isNilorEmpty($.fields.employee.employeeId)) ? ($itemPackage
              :=
              $filter($incomePackageEmpList($.fields.employee.employeeId,$.fields.employee.employeeRecordNumber,$.fields.effectiveDate)
              , function($v) {$v.value.code = $.value.code} )[]
              ;$count($itemPackage) > 0 ? $itemPackage[0] :
              '_setSelectValueNull'    )
          _condition:
            transform: $.extend.formType != 'view'
          col: 2
        - type: text
          label: Job Title
          name: jobTitleName
          _value:
            transform: >-
              $.extend.defaultValue.jobTitleName & ' (' &
              $.extend.defaultValue.jobTitleCode & ')'
          _condition:
            transform: $.extend.formType = 'view'
        - type: select
          label: Package
          placeholder: Select Package
          isLazyLoad: true
          validators:
            - type: required
          name: incomePackageObj
          outputValue: value
          _select:
            transform: >-
              $incomePackageEmpList($.fields.employee.employeeId,$.fields.employee.employeeRecordNumber,$.fields.effectiveDate)
          _condition:
            transform: $.extend.formType = 'view'
        - type: number
          name: detailMin
          label: Min
          placeholder: Enter Min
          _condition:
            transform: $.extend.formType = 'view'
          number:
            format: currency
            max: '999999999999999'
            precision: 4
          displayType: Currency
        - type: number
          name: detailMid
          label: Mid
          placeholder: Enter Mid
          _condition:
            transform: $.extend.formType = 'view'
          validators:
            - type: required
          number:
            format: currency
            max: '999999999999999'
            precision: 4
          displayType: Currency
        - type: number
          name: detailMax
          label: Max
          placeholder: Enter Max
          _condition:
            transform: $.extend.formType = 'view'
          number:
            format: currency
            max: '999999999999999'
            precision: 4
          displayType: Currency
        - type: text
          label: Currency
          name: detailCurrencyName
          _condition:
            transform: $.extend.formType = 'view'
        - type: text
          label: Company
          name: companyName
          _value:
            transform: >-
              $.extend.defaultValue.companyName & ' (' &
              $.extend.defaultValue.companyCode & ')'
          _condition:
            transform: $.extend.formType = 'view'
        - type: text
          label: Legal Entity
          name: legalEntityName
          _value:
            transform: >-
              $.extend.defaultValue.legalEntityName & ' (' &
              $.extend.defaultValue.legalEntityCode & ')'
          _condition:
            transform: $.extend.formType = 'view'
        - type: text
          label: Division
          name: divisionName
          _value:
            transform: >-
              $.extend.defaultValue.divisionName & ' (' &
              $.extend.defaultValue.divisionCode & ')'
          _condition:
            transform: $.extend.formType = 'view'
        - type: text
          label: Department
          name: departmentName
          _value:
            transform: >-
              $.extend.defaultValue.departmentName & ' (' &
              $.extend.defaultValue.departmentCode & ')'
          _condition:
            transform: $.extend.formType = 'view'
        - type: text
          label: Location
          name: locationName
          _value:
            transform: >-
              $.extend.defaultValue.locationName & ' (' &
              $.extend.defaultValue.locationCode & ')'
          _condition:
            transform: $.extend.formType = 'view'
        - type: dateRange
          label: Effective Date
          name: effectiveDate
          mode: date-picker
          _disabled:
            transform: $.extend.formType = 'proceed'
          placeholder: dd/MM/yyyy
          _value:
            transform: $.extend.formType = 'create' ? $now()
          setting:
            type: day
            format: dd/MM/yyyy
          validators:
            - type: required
        - type: radio
          label: Status
          name: status
          value: true
          radio:
            - label: Active
              value: true
            - label: Inactive
              value: false
        - type: translationTextArea
          name: note
          label: Note
          _condition:
            transform: $not($.extend.formType = 'view')
          placeholder: Enter Note
          textarea:
            autoSize:
              minRows: 3
            maxCharCount: 1000
          validators:
            - type: maxLength
              args: 1000
              text: note should not exceed 1000 characters
          col: 2
        - type: translationTextArea
          label: Note
          name: note
          _condition:
            transform: $.extend.formType = 'view'
          placeholder: Enter Note
          textarea:
            autoSize:
              minRows: 3
            maxCharCount: 1000
          validators:
            - type: maxLength
              args: 1000
              text: note should not exceed 1000 characters
  overviewGroup:
    - dependentField: dataEmployee
      _condition:
        transform: >-
          $.extend.formType = 'create' or $.extend.formType = 'edit' or
          $.extend.formType = 'proceed'
      title: Employee Detail
      noDataMessages: Select Employee to display data
      border: true
      collapsed: true
      uri: >-
        /api/pr-employees/:{employeeId}:/employee-record-number/:{employeeRecordNumber}:/effective-date/:{dateToShowEmployee}:
      display:
        - key: companyName
          label: Company
        - key: legalEntityName
          label: Legal entity
        - key: businessUnitName
          label: Business unit
        - key: divisionName
          label: Division
        - key: departmentName
          label: Department
        - key: jobTitleName
          label: Job Title
        - key: contractTypeName
          label: Contract Type
        - key: locationName
          label: Location
    - dependentField: incomePackageObj
      _condition:
        transform: >-
          ($.extend.formType = 'create' or $.extend.formType = 'edit' or
          $.extend.formType = 'proceed') and
          $exist($.fields.incomePackageObj.incomePackageSystemId)
      title: Package Detail
      uri: '/api/income-package-systems/:{incomePackageSystemId}:'
      display:
        - key: min
          label: Min
          displayType: Currency
        - key: mid
          label: Mid
          displayType: Currency
        - key: max
          label: Max
          displayType: Currency
        - key: currencyName
          label: Currency
        - key: noteDefault
          label: Note
  sources:
    employeesList:
      uri: '"/api/pr-employees"'
      method: GET
      queryTransform: >-
        {'limit': $.limit ,'page': $.page, 'search': $.search,'filter':
        [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'employeeId','operator':
        '$eq','value':$.empId},{'field':'employeeGroupCode','operator':
        '$cont','value':$.grpCode},{'field':'employeeRecordNumber','operator':
        '$eq','value':$.ern}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label':
        $join($filter([$item.employeeId,$item.employeeGroupCode,$string($item.employeeRecordNumber),$item.fullName],
        $boolean), ' - ')  , 'value': {'employeeId': $item.employeeId, 'code':
        $item.employeeId, 'employeeRecordNumber':
        $string($item.employeeRecordNumber), 'jobDataId': $item.jobDataId}}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - empId
        - grpCode
        - ern
        - effectiveDate
    incomePackageEmpList:
      uri: '"/api/income-package-employees/income-package"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'effectiveDateFrom','operator': '$eq','value':
        $.effectiveDate},{'field':'employeeId','operator': '$eq','value':
        $.employeeId},{'field':'ern','operator': '$eq','value': $.ern}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.incomePackageName & ' (' &
        $item.incomePackageCode & ')', 'value':  {'code':
        $item.incomePackageCode, 'incomePackageSystemId' :
        $not($isNilorEmpty($item.incomePackageSystemId)) ?
        $item.incomePackageSystemId : '0'} } })[]
      disabledCache: true
      params:
        - employeeId
        - ern
        - effectiveDate
  variables: {}
filter_config:
  fields:
    - type: selectAll
      name: employeeId
      label: Employee
      placeholder: Select Employee
      isLazyLoad: true
      labelType: type-grid
      _options:
        transform: $employeesList($.extend.limit, $.extend.page, $.extend.search)
    - name: fullName
      label: Full Name
      type: text
      labelType: type-grid
      placeholder: Enter Full Name
    - name: employeeGroupCode
      label: Employee Group
      type: selectAll
      mode: multiple
      placeholder: Select Employee Group
      labelType: type-grid
      isLazyLoad: true
      _options:
        transform: $employeeGroupsList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      label: Job Title
      labelType: type-grid
      name: jobTitleCode
      mode: multiple
      isLazyLoad: true
      placeholder: Select Job Title
      _options:
        transform: $jobCodesList($.extend.limit, $.extend.page, $.extend.search)
    - type: select
      label: Packages
      name: incomePackageCode
      placeholder: Select Packages
      labelType: type-grid
      isLazyLoad: true
      _select:
        transform: $incomePackages($.extend.limit,$.extend.page,$.extend.search)
    - name: currencyCode
      label: Currency
      labelType: type-grid
      isLazyLoad: true
      type: selectAll
      mode: multiple
      placeholder: Select Currency
      _options:
        transform: $currenciesList($.extend.limit, $.extend.page, $.extend.search)
    - name: companyCode
      label: Company
      type: selectAll
      mode: multiple
      placeholder: Select Company
      labelType: type-grid
      isLazyLoad: true
      _options:
        transform: $companiesList($.extend.limit, $.extend.page, $.extend.search)
    - name: legalEntityCode
      label: Legal Entity
      type: selectAll
      mode: multiple
      placeholder: Select Legal Entity
      labelType: type-grid
      isLazyLoad: true
      _options:
        transform: $legalEntitiesList($.extend.limit, $.extend.page, $.extend.search)
    - name: divisionCode
      label: Division
      type: selectAll
      mode: multiple
      placeholder: Select Division
      labelType: type-grid
      isLazyLoad: true
      _options:
        transform: $divisionList($.extend.limit, $.extend.page, $.extend.search)
    - name: departmentCode
      label: Department
      type: selectAll
      mode: multiple
      placeholder: Select Department
      labelType: type-grid
      isLazyLoad: true
      _options:
        transform: $departmentsList($.extend.limit, $.extend.page, $.extend.search)
    - name: locationCode
      label: Location
      type: selectAll
      mode: multiple
      placeholder: Select Location
      labelType: type-grid
      isLazyLoad: true
      _options:
        transform: $locationsList($.extend.limit, $.extend.page, $.extend.search)
    - name: effectiveDate
      label: Effective Date
      type: dateRange
      labelType: type-grid
      setting:
        format: dd/MM/yyyy
        type: date
    - name: note
      label: Note
      type: text
      labelType: type-grid
      placeholder: Enter Note
    - name: status
      labelType: type-grid
      label: Status
      value: null
      type: radio
      radio:
        - label: All
          value: null
        - label: Active
          value: true
        - label: Inactive
          value: false
    - name: updatedBy
      label: Last Updated By
      type: text
      labelType: type-grid
      placeholder: Enter Last Updated By
    - name: updatedAt
      label: Last Updated On
      labelType: type-grid
      type: dateRange
      setting:
        format: dd/MM/yyyy
        type: date
  filterMapping:
    - field: employeeIdErn
      operator: $in
      valueField: employeeId.(value)
    - field: fullName
      operator: $cont
      valueField: fullName
    - field: employeeGroupCode
      operator: $in
      valueField: employeeGroupCode.(value)
    - field: jobTitleCode
      operator: $in
      valueField: jobTitleCode.(value)
    - field: incomePackageCode
      operator: $in
      valueField: incomePackageCode.(value)
    - field: currencyCode
      operator: $in
      valueField: currencyCode.(value)
    - field: companyCode
      operator: $in
      valueField: companyCode.(value)
    - field: legalEntityCode
      operator: $in
      valueField: legalEntityCode.(value)
    - field: legalEntityCode
      operator: $in
      valueField: legalEntityCode.(value)
    - field: divisionCode
      operator: $in
      valueField: divisionCode.(value)
    - field: departmentCode
      operator: $in
      valueField: departmentCode.(value)
    - field: locationCode
      operator: $in
      valueField: locationCode.(value)
    - field: effectiveDate
      operator: $between
      valueField: effectiveDate
    - field: note
      operator: $cont
      valueField: note
    - field: status
      operator: $eq
      valueField: status
    - field: updatedBy
      operator: $cont
      valueField: updatedBy
    - field: updatedAt
      operator: $between
      valueField: updatedAt
  sources:
    locationsList:
      uri: '"/api/locations"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'pageSize': $.pageSize,'search':
        $.search, 'filter': []}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')',  'value': $item.code }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    businessUnitsList:
      uri: '"/api/business-units"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    currenciesList:
      uri: '"/api/picklists/CURRENCY/values/pagination"'
      method: GET
      queryTransform: '{''limit'': $.limit, ''page'': $.page, ''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    incomePackages:
      uri: '"/api/income-packages"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    employeeGroupsList:
      uri: '"/api/employee-groups"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'filter':
        [{'field':'search','operator': '$eq','value': $.search}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.employeeGroup.default & '
        (' & $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    companiesList:
      uri: '"/api/companies"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    legalEntitiesList:
      uri: '"/api/legal-entities"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    businessUnitList:
      uri: '"/api/business-units"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    divisionList:
      uri: '"/api/divisions"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    departmentsList:
      uri: '"/api/departments"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    assessmentPeriodList:
      uri: '"/api/assessment-period"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code,
        'assessmentPeriodEndDate':$item.assessmentPeriodEndDate,
        'assessmentPeriodStartDate':$item.assessmentPeriodStartDate }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    employeesList:
      uri: '"/api/personals/"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page, 'search': $.search, 'filter':
        [{'field':'employeeId','operator':
        '$eq','value':$.empId},{'field':'employeeGroupCode','operator':
        '$cont','value':$.grpCode},{'field':'employeeRecordNumber','operator':
        '$eq','value':$.ern}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label':
        $join($filter([$item.employeeId,$item.employeeGroup,$string($item.employeeRecordNumber),$item.fullName],
        $boolean), ' - ')  , 'value':$item.employeeId & '_' &
        $item.employeeRecordNumber }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    contractTypeList:
      uri: '"/api/picklists/CONTRACTTYPE/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
    jobCodesList:
      uri: '"/api/job-codes"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    userList:
      uri: '"/api/internal-users"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.userName, 'value':
        $item.userName}})
      disabledCache: true
layout_options:
  delete_multi_items: true
  apply_delete_multi_items_to_delete_one: true
  custom_delete_body: '{''ids'': $map($.data,function($item){$item.id})[]}'
  custom_history_backend_url: /api/income-package-employees/:id/clone
  reset_page_index_after_do_action:
    edit: true
  toolTable:
    export: true
    adjustDisplay: true
  history_widget_header_options:
    duplicate: false
  tool_table:
    - id: import
      paramsRedirect:
        type: PR_OBJECT
        entityOrObj: IncomePackageEmployee
    - id: export
      icon: icon-download-simple
  show_dialog_form_save_add_button: true
  show_filter_results_message: true
  store_selected_items: true
  show_dialog_duplicate_button: false
  hide_action_row: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    title: Edit
    icon: edit
    type: ghost-gray
  - id: delete
    title: Delete
    icon: trash
    type: ghost-gray
    _disabled: null
backend_url: api/income-package-employees
screen_name: null
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: employeeId
    defaultName: EmployeeId
  - name: employeeRecordNumber
    defaultName: EmployeeRecordNumber
  - name: jobTitleCode
    defaultName: JobCode
  - name: companyCode
    defaultName: CompanyCode
  - name: legalEntityCode
    defaultName: LegalEntityCode
  - name: divisionCode
    defaultName: DivisionCode
  - name: departmentCode
    defaultName: DepartmentCode
  - name: locationCode
    defaultName: LocationCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Manage Income Commitment
  parent:
    title: Manage Employee Infor
