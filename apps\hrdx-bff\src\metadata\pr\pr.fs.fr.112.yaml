id: PR.FS.FR.112
status: draft
sort: 99
user_created: b403d5da-d684-45e4-ae6d-e25223baedc5
date_created: '2025-06-19T03:13:56.377Z'
user_updated: b403d5da-d684-45e4-ae6d-e25223baedc5
date_updated: '2025-07-28T09:37:34.848Z'
title: Manage Termination Settlement Status for Employees
requirement:
  time: 1750303046081
  blocks:
    - id: tZ1QS0_YAt
      type: paragraph
      data:
        text: Manage Termination Settlement Status for Employees
  version: 2.29.1
screen_design: null
module: PR
local_fields:
  - code: employeeId
    title: Employee ID
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    pinned: true
    show_sort: true
  - code: fullName
    title: Full Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: employeeGroupName
    title: Employee Group
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: employeeRecordNumber
    title: ERN
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: jobTitleName
    title: Job Title
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: terminationDate
    title: Termination Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    show_sort: true
  - code: actionReasonName
    title: Reason
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: status
    title: Termination Settlement Status
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tag
      collection: field_types
    extra_config:
      tags:
        - value: true
          label: Done
          style:
            background_color: '#E0FAE9'
        - value: false
          label: Pending
          style:
            background_color: '#FEF9CC'
    show_sort: true
  - code: effectiveDateOfTermination
    title: Termination Settlement Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    show_sort: true
  - code: companyName
    title: Company
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: legalEntityName
    title: Legal Entity
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: businessUnitName
    title: Business Unit
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: divisionName
    title: Division
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: departmentName
    title: Department
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: locationName
    title: Location
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: note
    title: Note
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    show_sort: true
  - code: updatedAt
    title: Last Updated On
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
    show_sort: true
  - code: updatedBy
    title: Last Updated By
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    dragable: false
    show_sort: true
mock_data: []
local_buttons: null
layout: layout-table
form_config:
  formSize:
    create: large
    edit: large
    proceed: large
  defaultValidateError: Required fields have not been filled in completely (fields marked with *)
  formTitle:
    create: Add new Termination Settlement Status for Employees
    edit: Edit Termination Settlement Status for Employees
    view: Termination Settlement Status for Employees Details
  fields:
    - type: group
      _n_cols:
        transform: '$.extend.formType = ''view'' ? 1 : 2'
      fields:
        - type: select
          name: employee
          label: Employee
          clearFieldsAfterChange:
            - terminationDate
            - objectJobData
          placeholder: Select Employee
          isLazyLoad: true
          validators:
            - type: required
            - type: ppx-custom
              args:
                transform: >-
                  $not($isNilorEmpty($.fields.employee)) and
                  ($count($.variables._checkTerminate) = 0)
              text: 'Cannot select: the employee is still active!'
          _validateFn:
            transform: >-
              $not($isNilorEmpty($.value.employeeId)) ? (
              $isNilorEmpty($employeesList(1, 1,'',
              $.value.employeeId,null,$.value.employeeRecordNumber,
              $.fields.terminationDate)[0]) ?  '_setSelectValueNull' )
          _select:
            transform: >-
              $employeesList($.extend.limit, $.extend.page, $.extend.search,
              null,null,null, $.fields.terminationDate )
          _condition:
            transform: $boolean($.extend.formType = 'create')
          outputValue: value
          col: 2
        - type: select
          name: employee
          label: Employee
          isLazyLoad: true
          placeholder: Select Employee
          validators:
            - type: required
          _select:
            transform: >-
              $employeesList($.extend.limit, $.extend.page, $.extend.search,
              null,null,null, ($not($isNilorEmpty($.fields.effectiveDate)) ?
              $.fields.effectiveDate : $now()))
          _disabled:
            transform: 'true'
          _condition:
            transform: $.extend.formType = 'edit' or $.extend.formType = 'proceed'
          outputValue: value
          col: 2
        - type: text
          name: objectJobData
          unvisible: true
          _value:
            transform: >-
              $not($isNilorEmpty($.fields.employeeId)) ?
              $jobDatasDetail($.fields.employeeId,
              $.fields.employeeRecordNumber,$.fields.dateToShowEmployee) : null
        - type: text
          name: jobDataId
          dependantField: $.fields.employeeId; $.fields.effectiveDate
          label: jobDataId
          unvisible: true
          _value:
            transform: $.variables._jobData.id
        - type: text
          name: employeeRecordIdJobData
          dependantField: $.fields.employeeId; $.fields.terminationDate
          label: Employee
          unvisible: true
          _value:
            transform: >-
              ($not($isNilorEmpty($.variables._jobData)) ?
              $.variables._jobData.employeeId &
              $.variables._jobData.employeeRecordNumber &
              $string($.fields.dateToShowEmployee))
        - type: text
          name: dateToShowEmployee
          unvisible: true
          _value:
            transform: >-
              $not($isNilorEmpty($.fields.terminationDate)) ?
              $DateToTimestampUTC($.fields.terminationDate) :
              $DateToTimestampUTC($now())
        - type: text
          name: dataEmployee
          dependantField: $.fields.employeeId; $.fields.terminationDate
          unvisible: true
          _value:
            transform: >-
              $not($isNilorEmpty($.fields.employeeId)) ? {'employeeId':
              $.fields.employeeId , 'employeeRecordNumber':
              $.fields.employeeRecordNumber, 'dateToShowEmployee':
              $.fields.dateToShowEmployee} : null
        - type: text
          name: employeeIdView
          key: employeeIdView
          label: Employee
          _condition:
            transform: $.extend.formType = 'view'
          _value:
            transform: >-
              $join($filter([$.extend.defaultValue.employeeId,$.extend.defaultValue.employeeGroupCode,$string($.extend.defaultValue.employeeRecordNumber),$.extend.defaultValue.fullName],
              $boolean), ' - ')
        - type: text
          name: employeeId
          dependantField: $.fields.employee.employeeId
          unvisible: true
          _value:
            transform: $.fields.employee.employeeId
        - type: text
          name: employeeRecordNumber
          unvisible: true
          _value:
            transform: $string($.fields.employee.employeeRecordNumber)
        - type: select
          label: Termination Date
          placeholder: Select Termination Date
          isLazyLoad: true
          validators:
            - type: required
          name: terminationDate
          clearFieldsAfterChange:
            - objectJobData
          outputValue: value
          _select:
            transform: >-
              $terminationList($.extend.limit, $.extend.page, $.extend.search,
              $.fields.employee.employeeId,$.fields.employee.employeeRecordNumber)
          _value:
            transform: >-
              $.extend.formType = 'create' and
              $count($.variables._checkTerminate) = 1 ?
              $.variables._checkTerminate[0]
          _condition:
            transform: $.extend.formType != 'view'
          col: 2
        - type: text
          label: Job Title
          name: jobTitleName
          _condition:
            transform: $.extend.formType = 'view'
        - type: select
          label: Termination Date
          placeholder: Select Termination Date
          isLazyLoad: true
          validators:
            - type: required
          name: terminationDate
          outputValue: value
          _select:
            transform: >-
              $terminationList($.extend.limit, $.extend.page, $.extend.search,
              $.fields.employee.employeeId,$.fields.employee.employeeRecordNumber)
          _condition:
            transform: $.extend.formType = 'view'
        - type: text
          label: Reason
          name: actionReasonName
          _condition:
            transform: $.extend.formType = 'view'
        - type: select
          outputValue: value
          label: Termination Settlement Status
          clearFieldsAfterChange:
            - terminationSettlementDateGet
          name: status
          validators:
            - type: required
          select:
            - label: Done
              value: true
            - label: Pending
              value: false
          displaySetting:
            type: Tag
            extraConfig:
              tags:
                - value: true
                  label: Done
                  style:
                    background_color: '#E0FAE9'
                - value: false
                  label: Pending
                  style:
                    background_color: '#FEF9CC'
              size: small
        - type: dateRange
          label: Termination Settlement Date
          dependantField: $.fields.status
          name: terminationSettlementDateGet
          validators:
            - type: required
          mode: date-picker
          _condition:
            transform: $.fields.status = false
          _value:
            transform: '''_setValueNull'''
          _disabled:
            transform: 'true'
          placeholder: dd/MM/yyyy
          setting:
            type: day
            format: dd/MM/yyyy
        - type: dateRange
          label: Termination Settlement Date
          name: terminationSettlementDateGet
          validators:
            - type: required
            - type: ppx-custom
              args:
                transform: >-
                  $exists($.fields.terminationSettlementDateGet) and
                  $DateDiff($DateFormat($.fields.terminationSettlementDateGet,
                  'yyyy-MM-DD'), $DateFormat($.fields.terminationDate,
                  'yyyy-MM-DD'), 'd') < 0
              text: >-
                Termination Settlement Date must be more than or equal to
                Termination Date
          mode: date-picker
          _condition:
            transform: $.fields.status != false or $isNilorEmpty($.fields.status)
          placeholder: dd/MM/yyyy
          setting:
            type: day
            format: dd/MM/yyyy
        - type: text
          label: Company
          name: companyName
          _condition:
            transform: $.extend.formType = 'view'
        - type: text
          label: Legal Entity
          name: legalEntityName
          _condition:
            transform: $.extend.formType = 'view'
        - type: text
          label: Business Unit
          name: businessUnitName
          _condition:
            transform: $.extend.formType = 'view'
        - type: text
          label: Division
          name: divisionName
          _condition:
            transform: $.extend.formType = 'view'
        - type: text
          label: Department
          name: departmentName
          _condition:
            transform: $.extend.formType = 'view'
        - type: text
          label: Location
          name: locationName
          _condition:
            transform: $.extend.formType = 'view'
        - type: translationTextArea
          name: note
          label: Note
          _condition:
            transform: $not($.extend.formType = 'view')
          placeholder: Enter Note
          textarea:
            autoSize:
              minRows: 3
            maxCharCount: 1000
          validators:
            - type: maxLength
              args: 1000
              text: note should not exceed 1000 characters
          col: 2
        - type: translationTextArea
          label: Note
          name: note
          _condition:
            transform: $.extend.formType = 'view'
          placeholder: Enter Note
          textarea:
            autoSize:
              minRows: 3
            maxCharCount: 1000
          validators:
            - type: maxLength
              args: 1000
              text: note should not exceed 1000 characters
  overviewGroup:
    - border: true
      dependentField: employeeRecordIdJobData
      noDataMessages: Choose Employee ID getting data
      header: Employee Detail
      display:
        - key: companyName
          label: Company
          _value:
            transform: $.variables._jobData.companyName
        - key: legalEntityName
          label: Legal entity
          _value:
            transform: $.variables._jobData.legalEntityName
        - key: businessUnitName
          label: Business unit
          _value:
            transform: $.variables._jobData.businessUnitName
        - key: divisionName
          label: Division
          _value:
            transform: $.variables._jobData.divisionName
        - key: departmentName
          label: Department
          _value:
            transform: $.variables._jobData.departmentName
        - key: jobTitleName
          label: Job Title
          _value:
            transform: $.variables._jobData.jobTitleName
        - key: contractTypeName
          label: Contract Type
          _value:
            transform: $.variables._jobData.contractTypeName
        - key: locationName
          label: Location
          _value:
            transform: $.variables._jobData.locationName
  sources:
    employeesList:
      uri: '"/api/pr-employees"'
      method: GET
      queryTransform: >-
        {'limit': $.limit ,'page': $.page, 'search': $.search,'filter':
        [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'employeeId','operator':
        '$eq','value':$.empId},{'field':'employeeGroupCode','operator':
        '$cont','value':$.grpCode},{'field':'employeeRecordNumber','operator':
        '$eq','value':$.ern}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label':
        $join($filter([$item.employeeId,$item.employeeGroupCode,$string($item.employeeRecordNumber),$item.fullName],
        $boolean), ' - ')  , 'value': {'employeeId': $item.employeeId, 'code':
        $item.employeeId, 'employeeRecordNumber':
        $string($item.employeeRecordNumber), 'jobDataId': $item.jobDataId}}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - empId
        - grpCode
        - ern
        - effectiveDate
    jobDatasDetail:
      uri: >-
        "/api/pr-employees/"  & $.employeeId & "/employee-record-number/" &
        $string($.ern) & "/effective-date/" & $string($.effectiveDate)
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $
      disabledCache: true
      params:
        - employeeId
        - ern
        - effectiveDate
    terminationList:
      uri: '"/api/pr-employees/terminated"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'employeeId','operator': '$eq','value':
        $.employeeId},{'field':'employeeRecordNumber','operator': '$eq','value':
        $.ern}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label':
        $fromMillis($toMillis($item.terminateDate),'[D01]/[M01]/[Y0001]'),
        'value': $item.terminateDate } })[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - employeeId
        - ern
  variables:
    _jobData:
      transform: $.fields.objectJobData
    _checkTerminate:
      transform: >-
        $not($isNilorEmpty($.fields.employeeId)) ? $terminationList(2,
        1,'',$.fields.employeeId, $.fields.employeeRecordNumber)
  footer:
    create: true
    update: true
    createdOn: createdAt
    updatedOn: updatedAt
    createdBy: createdBy
    updatedBy: updatedBy
filter_config:
  fields:
    - type: selectAll
      name: employeeId
      label: Employee
      placeholder: Select Employee
      isLazyLoad: true
      labelType: type-grid
      _options:
        transform: $personalsList($.extend.limit, $.extend.page, $.extend.search)
    - name: employeeGroupCode
      label: Employee Group
      type: selectAll
      mode: multiple
      placeholder: Select Employee Group
      labelType: type-grid
      isLazyLoad: true
      _options:
        transform: $employeeGroupsList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      label: Job Title
      labelType: type-grid
      name: jobTitleCode
      mode: multiple
      isLazyLoad: true
      placeholder: Select Job Title
      _options:
        transform: $jobCodesList($.extend.limit, $.extend.page, $.extend.search)
    - name: terminationDate
      label: Termination Date
      labelType: type-grid
      type: dateRange
      setting:
        format: dd/MM/yyyy
        type: date
    - name: actionReasonCode
      label: Reason
      type: selectAll
      mode: multiple
      placeholder: Select Reason
      labelType: type-grid
      isLazyLoad: true
      _options:
        transform: $reasonsList($.extend.limit, $.extend.page, $.extend.search)
    - name: status
      label: Termination Settlement Status
      mode: multiple
      labelType: type-grid
      type: selectAll
      placeholder: Select Termination Settlement Status
      _options:
        transform: '[{''label'': ''Done'',''value'': ''Y''},{  ''label'': ''Pending'',  ''value'': ''N''}]'
    - name: effectiveDateOfTermination
      label: Termination Settlement Date
      labelType: type-grid
      type: dateRange
      setting:
        format: dd/MM/yyyy
        type: date
    - name: companyCode
      label: Company
      type: selectAll
      mode: multiple
      placeholder: Select Company
      labelType: type-grid
      isLazyLoad: true
      _options:
        transform: $companiesList($.extend.limit, $.extend.page, $.extend.search)
    - name: legalEntityCode
      label: Legal Entity
      type: selectAll
      mode: multiple
      placeholder: Select Legal Entity
      labelType: type-grid
      isLazyLoad: true
      _options:
        transform: $legalEntitiesList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      label: Business Unit
      name: businessUnitCode
      mode: multiple
      labelType: type-grid
      isLazyLoad: true
      placeholder: Select Business Unit
      _options:
        transform: $businessUnitList($.extend.limit, $.extend.page, $.extend.search)
    - name: divisionCode
      label: Division
      type: selectAll
      mode: multiple
      placeholder: Select Division
      labelType: type-grid
      isLazyLoad: true
      _options:
        transform: $divisionList($.extend.limit, $.extend.page, $.extend.search)
    - name: departmentCode
      label: Department
      type: selectAll
      mode: multiple
      placeholder: Select Department
      labelType: type-grid
      isLazyLoad: true
      _options:
        transform: $departmentsList($.extend.limit, $.extend.page, $.extend.search)
    - name: locationCode
      label: Location
      type: selectAll
      mode: multiple
      placeholder: Select Location
      labelType: type-grid
      isLazyLoad: true
      _options:
        transform: $locationsList($.extend.limit, $.extend.page, $.extend.search)
    - name: updatedAt
      label: Last Updated On
      labelType: type-grid
      type: dateRange
      setting:
        format: dd/MM/yyyy
        type: date
    - name: updatedBy
      label: Last Updated By
      type: text
      labelType: type-grid
      placeholder: Enter Last Updated By
  filterMapping:
    - operator: $or
      valueField:
        - field: employeeId
          operator: $elemMatch
          valueField: employeeId.(employeeId)
        - field: employeeRecordNumber
          operator: $elemMatch
          valueField: employeeId.(ern)
    - field: employeeGroupCode
      operator: $in
      valueField: employeeGroupCode.(value)
    - field: jobTitleCode
      operator: $in
      valueField: jobTitleCode.(value)
    - field: terminationDate
      operator: $between
      valueField: terminationDate
    - field: actionReasonCode
      operator: $in
      valueField: actionReasonCode.(value)
    - field: statusFilter
      operator: $in
      valueField: status.(value)
    - field: effectiveDateOfTermination
      operator: $between
      valueField: effectiveDateOfTermination
    - field: companyCode
      operator: $in
      valueField: companyCode.(value)
    - field: businessUnitCode
      operator: $in
      valueField: businessUnitCode.(value)
    - field: legalEntityCode
      operator: $in
      valueField: legalEntityCode.(value)
    - field: divisionCode
      operator: $in
      valueField: divisionCode.(value)
    - field: departmentCode
      operator: $in
      valueField: departmentCode.(value)
    - field: locationCode
      operator: $in
      valueField: locationCode.(value)
    - field: effectiveDate
      operator: $between
      valueField: effectiveDate
    - field: note
      operator: $cont
      valueField: note
    - field: updatedBy
      operator: $cont
      valueField: updatedBy
    - field: updatedAt
      operator: $between
      valueField: updatedAt
  sources:
    reasonsList:
      uri: '"/api/picklists/ACTIONREASON/values/pagination"'
      method: GET
      queryTransform: '{''limit'': $.limit, ''page'': $.page, ''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data,function($item){{'label':$item.name.default,'value':$item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    locationsList:
      uri: '"/api/locations"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'pageSize': $.pageSize,'search':
        $.search, 'filter': []}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')',  'value': $item.code }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    businessUnitsList:
      uri: '"/api/business-units"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    currenciesList:
      uri: '"/api/picklists/CURRENCY/values/pagination"'
      method: GET
      queryTransform: '{''limit'': $.limit, ''page'': $.page, ''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    incomePackages:
      uri: '"/api/income-packages"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    employeeGroupsList:
      uri: '"/api/employee-groups"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'filter':
        [{'field':'search','operator': '$eq','value': $.search}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.employeeGroup.default & '
        (' & $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    companiesList:
      uri: '"/api/companies"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    legalEntitiesList:
      uri: '"/api/legal-entities"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    businessUnitList:
      uri: '"/api/business-units"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    divisionList:
      uri: '"/api/divisions"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    departmentsList:
      uri: '"/api/departments"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    assessmentPeriodList:
      uri: '"/api/assessment-period"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code,
        'assessmentPeriodEndDate':$item.assessmentPeriodEndDate,
        'assessmentPeriodStartDate':$item.assessmentPeriodStartDate }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    personalsList:
      uri: '"/api/personals"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label':
        $join($filter([$item.employeeId,$item.employeeGroup,$string($item.employeeRecordNumber),$item.fullName],
        $boolean), ' - ') , 'value':{'employeeId':$item.employeeId , 'ern':
        $item.employeeRecordNumber,'empGroup' :
        $.item.employeeGroup},'employeeId':$item.employeeId,
        'ern':$item.employeeRecordNumber, 'empGroup' : $item.employeeGroup }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    contractTypeList:
      uri: '"/api/picklists/CONTRACTTYPE/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
    jobCodesList:
      uri: '"/api/job-codes"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    userList:
      uri: '"/api/internal-users"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.userName, 'value':
        $item.userName}})
      disabledCache: true
layout_options:
  show_detail_history: false
  delete_multi_items: true
  apply_delete_multi_items_to_delete_one: true
  custom_delete_body: '{''ids'': $map($.data,function($item){$item.id})[]}'
  reset_page_index_after_do_action:
    edit: true
  toolTable:
    export: true
    adjustDisplay: true
  history_widget_header_options:
    duplicate: false
  tool_table:
    - id: import
      paramsRedirect:
        type: PR_OBJECT
        entityOrObj: IncomePackageEmployee
    - id: export
      icon: icon-download-simple
  show_dialog_form_save_add_button: true
  show_filter_results_message: true
  store_selected_items: true
  show_dialog_duplicate_button: false
  hide_action_row: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    title: Edit
    icon: edit
    type: ghost-gray
  - id: delete
    title: Delete
    icon: trash
    type: ghost-gray
    _disabled: null
backend_url: api/termination-settlement-statuses
screen_name: null
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: employeeId
    defaultName: EmployeeId
  - name: employeeRecordNumber
    defaultName: EmployeeRecordNumber
  - name: jobTitleCode
    defaultName: JobCode
  - name: companyCode
    defaultName: CompanyCode
  - name: legalEntityCode
    defaultName: LegalEntityCode
  - name: divisionCode
    defaultName: DivisionCode
  - name: departmentCode
    defaultName: DepartmentCode
  - name: locationCode
    defaultName: LocationCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item: null
