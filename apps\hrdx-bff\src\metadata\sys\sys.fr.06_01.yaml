id: SYS.FR.06_01
status: draft
sort: 91
user_created: 43878dbb-aa6c-4ef4-8bd7-0ea811ffafa7
date_created: '2024-07-16T10:09:28.314Z'
user_updated: 43878dbb-aa6c-4ef4-8bd7-0ea811ffafa7
date_updated: '2025-07-10T09:58:27.950Z'
title: Manage User-Based Permission
requirement:
  time: 1749115719618
  blocks:
    - id: fYhHJi1Yr2
      type: paragraph
      data:
        text: '1'
  version: 2.30.7
screen_design: null
module: SYS
local_fields:
  - code: userName
    title: User
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    description: Load user information
    pinned: true
    show_sort: true
  - code: employeeCode
    title: Employee ID
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: Load employee ID information
    show_sort: true
  - code: employeeName
    title: Employee Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: Load employee name information
    options__tabular__column_width: 15
    show_sort: true
  - code: companyName
    title: Company
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    description: Load company information
    show_sort: true
    options__tabular__column_width: 15
  - code: effectiveDate
    title: Effective Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    description: Load effective date information
    show_sort: true
  - code: groupName
    title: User Group
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    description: Load user group information
    show_sort: true
  - code: roleDisplayTooltip
    title: Default Role
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    description: Load default role information
    show_sort: false
  - code: additionalRole
    title: Additional Role
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    show_sort: false
  - code: permissionEnabled
    title: Status
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Boolean Tag
      collection: field_types
    description: Load status information
    show_sort: true
  - code: permissionUpdatedAt
    title: Last Updated On
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
    description: Load edited time information
    options__tabular__column_width: 15
    show_sort: true
  - code: permissionUpdatedBy
    title: Last Updated By
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: Load editor information
    show_sort: true
mock_data:
  - user: Ducnm54
    employeeId: E1234
    employeeName: Nguyen Van A1
    company: FPT IS
    effectiveDate: '2024-05-01'
    effectiveStartDate: '2024-12-22'
    nation: Vietnam
    userGroup: HR_ES Group
    table:
      - default: true
        defaultRole: ADMIN.FPT
        componentsGroup: HR group
        locationBasedAccess: FPT_IS_PB18
        permissionWith: Structure
      - default: true
        defaultRole: HR_Block ES
        componentsGroup: Payroll group1
        locationBasedAccess: FPT_IS_PB22
        permissionWith: Structure
    defaultRole:
      - HR_Block ES
      - PAYROLL_BANK
    additionalRole:
      - label: CONTRACT_ESHN_BANK
        value: CONTRACT_ESHN_BANK
    securityGroup:
      - Security
      - Personnel
    status: Active
    createdOn: '2024-12-22 10:25:01'
    createdBy: Ducnm54
    lastUpdatedOn: '2024-12-22 10:25:01'
    lastUpdatedBy: Ducnm54
  - user: Ducnm55
    employeeId: E1235
    employeeName: Nguyen Van A2
    company: FPT IS
    effectiveDate: '2024-05-01'
    effectiveStartDate: '2024-12-22'
    nation: Vietnam
    userGroup: HR_ES Group
    table:
      - default: true
        defaultRole: ADMIN.FPT
        componentsGroup: HR group
        locationBasedAccess: FPT_IS_PB18
        permissionWith: Structure
      - default: true
        defaultRole: HR_Block ES
        componentsGroup: Payroll group1
        locationBasedAccess: FPT_IS_PB22
        permissionWith: Structure
    defaultRole:
      - label: HR_Block ES
        value: HR_Block ES
      - label: PAYROLL_BANK
        value: PAYROLL_BANK
    additionalRole:
      - label: CONTRACT_ESHN_BANK
        value: CONTRACT_ESHN_BANK
    securityGroup:
      - Security
      - Personnel
    status: Active
    createdOn: '2024-12-22 10:25:01'
    createdBy: Ducnm54
    lastUpdatedOn: '2024-12-22 10:25:01'
    lastUpdatedBy: Ducnm54
  - user: Ducnm56
    employeeId: E1236
    employeeName: Nguyen Van A3
    company: FPT IS
    effectiveDate: '2024-05-01'
    effectiveStartDate: '2024-12-22'
    nation: Vietnam
    userGroup: HR_ES Group
    table:
      - default: true
        defaultRole: ADMIN.FPT
        componentsGroup: HR group
        locationBasedAccess: FPT_IS_PB18
        permissionWith: Structure
      - default: true
        defaultRole: HR_Block ES
        componentsGroup: Payroll group1
        locationBasedAccess: FPT_IS_PB22
        permissionWith: Structure
    defaultRole:
      - label: HR_Block ES
        value: HR_Block ES
      - label: PAYROLL_BANK
        value: PAYROLL_BANK
    additionalRole:
      - label: CONTRACT_ESHN_BANK
        value: CONTRACT_ESHN_BANK
    securityGroup:
      - Security
      - Personnel
    status: Active
    createdOn: '2024-12-22 10:25:01'
    createdBy: Ducnm54
    lastUpdatedOn: '2024-12-22 10:25:01'
    lastUpdatedBy: Ducnm54
  - user: Ducnm57
    employeeId: E1237
    employeeName: Nguyen Van A4
    company: FPT IS
    effectiveDate: '2024-05-01'
    effectiveStartDate: '2024-12-22'
    nation: Vietnam
    userGroup: HR_ES Group
    table:
      - default: true
        defaultRole: ADMIN.FPT
        componentsGroup: HR group
        locationBasedAccess: FPT_IS_PB18
        permissionWith: Structure
      - default: true
        defaultRole: HR_Block ES
        componentsGroup: Payroll group1
        locationBasedAccess: FPT_IS_PB22
        permissionWith: Structure
    defaultRole:
      - label: HR_Block ES
        value: HR_Block ES
      - label: PAYROLL_BANK
        value: PAYROLL_BANK
    additionalRole:
      - label: CONTRACT_ESHN_BANK
        value: CONTRACT_ESHN_BANK
    securityGroup:
      - Security
      - Personnel
    status: Active
    createdOn: '2024-12-22 10:25:01'
    createdBy: Ducnm54
    lastUpdatedOn: '2024-12-22 10:25:01'
    lastUpdatedBy: Ducnm54
  - user: Ducnm58
    employeeId: E1238
    employeeName: Nguyen Van A5
    company: FPT IS
    effectiveDate: '2024-05-01'
    effectiveStartDate: '2024-12-22'
    nation: Vietnam
    userGroup: HR_ES Group
    table:
      - default: true
        defaultRole: ADMIN.FPT
        componentsGroup: HR group
        locationBasedAccess: FPT_IS_PB18
        permissionWith: Structure
      - default: true
        defaultRole: HR_Block ES
        componentsGroup: Payroll group1
        locationBasedAccess: FPT_IS_PB22
        permissionWith: Structure
    defaultRole:
      - label: HR_Block ES
        value: HR_Block ES
      - label: PAYROLL_BANK
        value: PAYROLL_BANK
    additionalRole:
      - label: CONTRACT_ESHN_BANK
        value: CONTRACT_ESHN_BANK
    securityGroup:
      - Security
      - Personnel
    status: Active
    createdOn: '2024-12-22 10:25:01'
    createdBy: Ducnm54
    lastUpdatedOn: '2024-12-22 10:25:01'
    lastUpdatedBy: Ducnm54
  - user: Ducnm59
    employeeId: E1239
    employeeName: Nguyen Van A6
    company: FPT IS
    effectiveDate: '2024-05-01'
    effectiveStartDate: '2024-12-22'
    nation: Vietnam
    userGroup: HR_ES Group
    table:
      - default: true
        defaultRole: ADMIN.FPT
        componentsGroup: HR group
        locationBasedAccess: FPT_IS_PB18
        permissionWith: Structure
      - default: true
        defaultRole: HR_Block ES
        componentsGroup: Payroll group1
        locationBasedAccess: FPT_IS_PB22
        permissionWith: Structure
    defaultRole:
      - label: HR_Block ES
        value: HR_Block ES
      - label: PAYROLL_BANK
        value: PAYROLL_BANK
    additionalRole:
      - label: CONTRACT_ESHN_BANK
        value: CONTRACT_ESHN_BANK
    securityGroup:
      - Security
      - Personnel
    status: Active
    createdOn: '2024-12-22 10:25:01'
    createdBy: Ducnm54
    lastUpdatedOn: '2024-12-22 10:25:01'
    lastUpdatedBy: Ducnm54
local_buttons: null
layout: layout-table
form_config:
  formSize:
    create: large
    edit: large
    history: large
    proceed: large
  formTitle:
    create: Add New User-Based Permission
    edit: Edit User-Based Permission
    view: User-Based Permission Details
    proceed: Edit User-Based Permission
  fields:
    - type: group
      _condition:
        transform: $.extend.formType = 'create'
      fields:
        - type: group
          n_cols: 2
          fields:
            - type: select
              name: companyCode
              label: Company
              outputValue: value
              placeholder: Select company
              isLazyLoad: true
              confirmPopup:
                title: Change Company
                content: >-
                  The user, user group and role permissions below will be reset.
                  Do you want to continue with the change?
                listFieldsDependantName:
                  - userId
                  - groupId
                  - permissionOnRoles.defaultRole
              _select:
                transform: $companiesList($.extend.limit, $.extend.page, $.extend.search)
            - type: select
              name: userId
              isLazyLoad: true
              label: User
              placeholder: Select User
              outputValue: value
              dependantField: $.fields.companyCode
              validators:
                - type: required
              confirmPopup:
                title: Change User
                content: >-
                  The user group and role permissions below will be reset. Do
                  you want to continue with the change?
                listFieldsDependantName:
                  - groupId
                  - permissionOnRoles.defaultRole
              _select:
                transform: >-
                  $usersList($.extend.limit, $.extend.page,
                  $.extend.search,$.fields.companyCode)
              _disabled:
                transform: $.extend.formType = 'edit'
            - type: dateRange
              name: effectiveDate
              label: Effective Date
              _value:
                transform: $.extend.formType = 'create' ? $now()
              _disabled:
                transform: $.extend.formType = 'edit'
              validators:
                - type: required
              settings:
                format: dd/MM/yyyy
                mode: date
              mode: date-picker
            - type: radio
              name: permissionEnabled
              label: Status
              validators:
                - type: required
              _value:
                transform: $.extend.formType = 'create' ? true
              radio:
                - value: true
                  label: Active
                - value: false
                  label: Inactive
    - type: group
      _condition:
        transform: >-
          $not($.extend.formType = 'create') and $not($.extend.formType =
          'view')
      fields:
        - type: group
          n_cols: 2
          fields:
            - type: select
              name: companySelect
              label: Company
              outputValue: value
              placeholder: Select company
              isLazyLoad: true
              _select:
                transform: $companiesList($.extend.limit, $.extend.page, $.extend.search)
              disabled: true
            - type: text
              name: companyCode
              label: Company
              unvisible: true
              _value:
                transform: $.fields.companySelect.value
            - type: text
              name: company
              label: Company
              unvisible: true
            - type: text
              name: userId
              label: User
              unvisible: true
            - type: text
              name: userName
              label: User
              disabled: true
            - type: dateRange
              name: effectiveDate
              label: Effective Date
              settings:
                format: dd/MM/yyyy
                mode: date
              mode: date-picker
              disabled: true
            - type: radio
              name: permissionEnabled
              label: Status
              radio:
                - value: true
                  label: Active
                - value: false
                  label: Inactive
    - type: group
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - type: select
          name: companySelect
          label: Company
          outputValue: value
          isLazyLoad: true
          _select:
            transform: $companiesList($.extend.limit, $.extend.page, $.extend.search)
          disabled: true
        - type: text
          name: companyCode
          label: Company
          unvisible: true
          _value:
            transform: $.fields.companySelect.value
        - type: text
          name: company
          label: Company
          unvisible: true
        - type: text
          name: userId
          label: User
          unvisible: true
        - type: text
          name: userName
          label: User
          disabled: true
        - type: dateRange
          name: effectiveDate
          label: Effective Date
          settings:
            format: dd/MM/yyyy
            mode: date
          mode: date-picker
          disabled: true
        - type: radio
          name: permissionEnabled
          label: Status
          radio:
            - value: true
              label: Active
            - value: false
              label: Inactive
    - type: group
      label: Assign User Group-Based Permission
      collapse: false
      dependantField: $.fields.companyCode, $.fields.userId
      border_top: '1px solid #e0e0e0'
      fields:
        - type: select
          name: groupId
          label: User Group
          placeholder: Select User Group
          _value:
            transform: $.extend.defaultValue.userGroup.groupId
          _condition:
            transform: $not($.extend.formType = 'view')
          outputValue: value
          _select:
            transform: '$isNilorEmpty($.fields.userId) ? [] : $.variables._groupssysList'
          validators:
            - type: ppx-custom
              args:
                transform: >-
                  $not($exists($.fields.groupId)) and
                  ($not($exists($.fields.permissionOnRoles.defaultRole)) or
                  $count($.fields.permissionOnRoles.defaultRole) < 1)
              text: 'Choose 1 of the fields: User Group, Role asign permission'
            - type: ppx-custom
              args:
                transform: >-
                  $not($boolean($.fields.groupId)) and
                  $not($boolean($.fields.permissionOnRoles.defaultRole))
              text: 'Choose 1 of the fields: User Group, Role asign permission'
        - type: select
          name: groupId
          label: User Group
          _value:
            transform: $.extend.userGroup.groupId
          _condition:
            transform: $.extend.formType = 'view'
          outputValue: value
          _select:
            transform: $.variables._groupssysList
    - type: select
      name: selectedDefaultRole
      unvisible: true
      mode: multiple
      dependantField: $.fields.companyCode
      _select:
        transform: $.variables._adminrolesListCheckDefault
      _value:
        transform: $.variables._selectedGroupSys.roleIds
    - type: select
      name: selectedAdditionalRole
      unvisible: true
      mode: multiple
      dependantField: $.fields.companyCode
      _select:
        transform: $.variables._adminrolesListCheckDefault
      _value:
        transform: $distinct($.fields.permissionOnRoles.defaultRole)
    - type: group
      name: permissionOnRoles
      label: Assign Role-Based Permission
      dependantField: $.fields.companyCode, $.fields.userId
      collapse: false
      border_top: '1px solid #e0e0e0'
      fields:
        - type: select
          label: Role
          name: defaultRole
          placeholder: Select Role
          mode: multiple
          outputValue: value
          _unvisible:
            transform: $.extend.formType = 'view'
          _select:
            transform: >-
              $isNilorEmpty($.fields.userId) ? [] :
              $.variables._adminrolesListCheckDefault
          _defaultValue:
            transform: >-
              $not($isNilorEmpty($.fields.groupId)) ? $.variables._roleDefault :
              []
          validators:
            - type: ppx-custom
              args:
                transform: >-
                  $not($exists($.fields.groupId)) and
                  $not($exists($.fields.permissionOnRoles.defaultRole))
              text: 'Choose 1 of the fields: User Group, Role asign permission'
            - type: ppx-custom
              args:
                transform: >-
                  $not($boolean($.fields.groupId)) and
                  $not($boolean($.fields.permissionOnRoles.defaultRole))
              text: 'Choose 1 of the fields: User Group, Role asign permission'
        - type: table
          name: table
          _dataSourceRequestStatus: _loadingPermissionWithRole
          rowIdName: idx
          mapRowName:
            - idx
            - roleId
            - componentsGroupValue
            - dataAreaId
            - dataAreaName
          layout_option:
            tool_table:
              show_table_checkbox: false
              show_table_filter: false
              show_table_group: false
              hidden_header: false
              collapse: false
            show_pagination: false
            hide_action_row: false
            action_row_disabled:
              - delete
              - edit
            action_row:
              - id: duplicate
                type: ghost-gray
                icon: icon-copy-bold
                appendObject:
                  isDefault:
                    label: 'No'
                    type: default
                  dataArea: null
                  permissionWith: null
                  criteria: null
                appendAction:
                  action:
                    - id: delete
                      type: ghost-gray
                      icon: icon-trash-bold
                      disabled: true
          columns:
            - code: isDefault
              title: Default
              align: start
              data_type:
                key: Boolean
                collection: data_types
              display_type:
                key: Tag
                collection: field_types
              options__tabular__column_width: 4
            - code: roleName
              title: Role
              align: start
              data_type:
                key: String
                collection: data_types
              display_type:
                key: Label
                collection: field_types
              options__tabular__column_width: 12.5
            - code: functionGroupPermissionName
              title: Function Group
              align: start
              data_type:
                key: String
                collection: data_types
              display_type:
                key: Hyperlink
                collection: field_types
              actionRow:
                action: ''
                baseUrl: >-
                  /SYS/SYS.FS.FR.12_01?dialogType=view&id={{componentsGroupValue}}
            - code: dataArea
              title: Data Zone
              align: start
              data_type:
                key: String
                collection: data_types
              display_type:
                key: Select
                collection: field_types
                _select:
                  transform: >-
                    $admindataareasList($.fields.companyCode).{'value': value,
                    'label': label}
                _readOnly:
                  transform: >-
                    $.extend.formType = 'view' or
                    ($not($getIdx($.fields.permissionOnRoles.table,
                    $.extend.index).isDuplicate = 'Y' or
                    $getIdx($.fields.permissionOnRoles.table,
                    $.extend.index).isDuplicate = true))
              options__tabular__column_width: 12.5
            - code: permissionWith
              title: Permission Based On
              align: start
              data_type:
                key: String
                collection: data_types
              display_type:
                key: Label
                collection: field_types
                _value:
                  transform: >-
                    $filter($.variables._admindataareasList, function($i){
                    $i.value = $getIdx($.fields.permissionOnRoles.table,
                    $.extend.index).dataArea.value})[].permissionWith
              options__tabular__column_width: 12.5
            - code: criteria
              title: Criteria
              align: start
              data_type:
                key: String
                collection: data_types
              display_type:
                collection: field_types
                _value:
                  transform: >-
                    ($_temp := $filter($.variables._admindataareasList,
                    function($i){ $i.value =
                    $getIdx($.fields.permissionOnRoles.table,
                    $.extend.index).dataArea.value})[].criteria; $_temp.Employee
                    ? $count($split($_temp.Employee, ';')) & ' employee(s)' :
                    $_temp[0])
              options__tabular__column_width: 12.5
              actionRow:
                action: ''
                baseUrl: /SYS/SYS.FS.FR.13_01?dialogType=view&id={{dataAreaId}}
              extra_config:
                _type:
                  transform: '$type($.value) = ''string'' ? ''Hyperlink'' : ''Tooltip'''
          _dataSource:
            transform: >-
              (    $v1 := $filter($.value,function($i) {      ($i.isDuplicate =
              'Y' or $i.isDuplicate = true) and $count(
              $filter($.variables._selectedRolelist ,     function ($a) { $a =
              $i.roleId})[])>0  })[];    $v2 :=
              $.variables._filteredPermissionWithRole;    $v3:= 
              $filter($.variables._dataAreaDetails,function($i) {     
              $i.isDuplicate = 'Y'  });    $allList := $map($append($append(
              $v2, $v1), $v3), function($i) {$merge([$i, {'idx': '',
              'companyCode': ''}])})[];    [        $map($distinct($allList),
              function($value, $idx){$merge([$value, {'idx': $string($idx),
              'companyCode': $.fields.companyCode}])})    ])
  footer:
    create: true
    update: true
    createdOn: permissionCreatedAt
    updatedOn: permissionUpdatedAt
    createdBy: permissionCreatedBy
    updatedBy: permissionUpdatedBy
  variables:
    _selectedRolelist:
      transform: '$isNilorEmpty($.fields.groupId) ? [] : $.variables._roleDefault'
    _admindataareasList:
      transform: $admindataareasList($.fields.companyCode)
    _permissionWithRole:
      transform: >-
        ($count($.fields.permissionOnRoles.defaultRole) > 0 ?
        $adminrolesInfoList($.fields.permissionOnRoles.defaultRole) : [])
    _loadingPermissionWithRole:
      transform: >-
        ($.extend.formType = 'create' ? [] :
        $count($.fields.permissionOnRoles.defaultRole) > 0 ?
        $adminrolesInfoList($.fields.permissionOnRoles.defaultRole) :
        $adminrolesInfoList())
    _filteredPermissionWithRole:
      transform: >-
        $sort(  $map($.variables._permissionWithRole, function($item) {   
        $merge([      $item,      {        'isDefault': (          $check :=
        $exists($.variables._selectedGroupSys) and                    
        $exists($.variables._selectedGroupSys.roleIds) ?                    
        ($item.roleId in $.variables._selectedGroupSys.roleIds) :
        false;          {            'label': $check ? 'Yes' : 'No',           
        'type': $check ? 'success' : 'default'          }        )      }    ]) 
        }),  function($a, $b) {    $b.isDefault.label = 'Yes' ? 1 : 0 -
        ($a.isDefault.label = 'Yes' ? 1 : 0)  })
    _groupssysList:
      transform: >-
        $groupssysList($.extend.formType = 'view' ?
        $.extend.defaultValue.companyCode : $.fields.companyCode ?
        $.fields.companyCode : $.variables._selectedUser.companyCode)
    _selectedGroupSys:
      transform: ' $filter($.variables._groupssysList, function($item) { $item.value = $.fields.groupId })[0]'
    _datasecuritygroupsList:
      transform: >-
        $datasecuritygroupsList(50000, $.extend.page,
        $.extend.search,$.fields.permissionOnSecurityGroups.countryCode,
        $DateToTimestamp($.fields.effectiveDate))[]
    _datasecuritygroupsList2:
      transform: >-
        $filter($.variables._datasecuritygroupsList, function($item){
        $item.value in $.variables._temp})[]
    _temp:
      transform: >-
        $map($.fields.permissionOnSecurityGroups.dataSecurityGroupIds,
        function($item) {$item.value})[]
    _adminrolesList:
      transform: >-
        $adminrolesList($.fields.companyCode ? $.fields.companyCode :
        $.variables._selectedUser.companyCode)
    _adminrolesListCheckDefault:
      transform: >-
        $map($.variables._adminrolesList,function ($item){$merge([$item,
        {'disabled': $exists($.variables._selectedGroupSys) and 
        $exists($.variables._selectedGroupSys.roleIds) ?   ($item.value in
        $.variables._selectedGroupSys.roleIds) :  false}]) })
    _roleDefault:
      transform: >-
        $filter($.variables._groupssysList, function($i) { $i.value =
        $.fields.groupId} ).roleIds[$ != null][]
    _dataAreaDetails:
      transform: >-
        [  $reduce(    $map($.extend.defaultValue.userRoles, function($item)
        {      $map($item.userRoleDataAreas, function($roleDetail) {       
        {          'action': $item.action,          'isDefault': {           
        'label': 'No',            'type': 'default'          },         
        'isDuplicate': 'Y',          'roleId': $item.role.id,         
        'dataArea': {            'value': $roleDetail.dataAreaId,           
        'label': $roleDetail.dataArea.name          },          'dataAreaId':
        $roleDetail.dataAreaId,          'roleName': $item.role.name,         
        'dataAreaName': $roleDetail.dataArea.name,         
        'functionGroupPermissionName':
        $roleDetail.functionGroupPermission.name,         
        'componentsGroupValue': $roleDetail.functionGroupPermission.id,         
        'permissionWith':            $roleDetail.dataArea.ruleCode = 'CN' ?
        'Individual' :            $roleDetail.dataArea.ruleCode = 'CTTT' ?
        'Direct superior' :            $roleDetail.dataArea.ruleCode = 'CTGT' ?
        'Indirect superior' :            $roleDetail.dataArea.ruleCode = 'ORG' ?
        'Structure' :            'Employee list',         
        'criteria':            $roleDetail.dataArea.isIncludesSubordinates =
        'Y'                ? [{ 'Include in direct staff': 'Yes'
        }]                : $reduce(                   
        $roleDetail.dataArea.dataAreaDetails,                    function($acc,
        $item) {                    $merge([                       
        $acc,                        {                       
        $item.dataAreaParamName:                            $lookup($acc,
        $item.dataAreaParamName)                            ? $lookup($acc,
        $item.dataAreaParamName) & '; ' & (                               
        ($item.paramValueDisplay ? $item.paramValueDisplay :
        '')                                 )                            :
        (                                ($item.paramValueDisplay ?
        $item.paramValueDisplay : '')                                
        )                        }                    ])                   
        },                    {}                )        }      })    }),   
        $append  )]
    _selectedUser:
      transform: >-
        $filter($usersList(10000, $.extend.page,
        $.extend.search,$.fields.companyCode), function($i) { $i.value =
        $.fields.userId} )
  sources:
    usersList:
      uri: '"/api/admin-users/infos"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page,'search': $.search, 'filter':
        [{'field':'companyCode','operator': '$eq','value':$.companyCode},
        {'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'permission','operator':
        '$eq','value':0}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label':
        $join($filter([$item.account,$item.companyName], $boolean), ' - '),
        'value': $item.id, 'companyCode': $item.companyCode }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - companyCode
        - effectiveDate
    companiesList:
      uri: '"/api/companies/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'search': $.search,'filter':
        [{'field':'status', 'operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    groupssysList:
      uri: '"/api/groups-sys/dropdown"'
      method: GET
      queryTransform: >-
        {'limit':99999,'filter': [{'field':'status', 'operator':
        '$eq','value':true}, {'field':'companyCode','operator':
        '$eq','value':$.companyCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label':
        $join($filter([$item.code,$item.name.default,$item.companyName],
        $boolean), ' - '), 'value': $item.id , 'roleIds': $item.roleIds}})[]
      disabledCache: true
      params:
        - companyCode
    adminrolesList:
      uri: '"/api/admin-roles/dropdown"'
      method: GET
      queryTransform: >-
        {'limit':999 , 'filter': [{'field':'status', 'operator':
        '$eq','value':true}, {'field':'companyCode','operator':
        '$eq','value':$.companyCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label':
        $join($filter([$item.code,$item.name.default,$item.companyName],
        $boolean), ' - '), 'value': $item.id}})[]
      disabledCache: true
      params:
        - companyCode
    adminrolesInfoList:
      uri: '"/api/admin-roles-infos"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'ids','operator': '$eq','value': $exists($.roleIds)
        ? $join($map($.roleIds, $string), ',') : ''}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        [  $reduce(    $map($, function($item) {      $map($item.roleDetails,
        function($roleDetail) {        {          'roleId': $item.id,         
        'dataAreaId': $roleDetail.dataArea.id,          'roleName':
        $item.name.default,          'dataAreaName':
        $roleDetail.dataArea.name,          'dataArea': {            'value':
        $roleDetail.dataArea.id,            'label':
        $roleDetail.dataArea.name          },         
        'functionGroupPermissionName':
        $roleDetail.functionGroupPermission.name.default,         
        'componentsGroupValue': $roleDetail.functionGroupPermission.id,         
        'permissionWith':            $roleDetail.dataArea.ruleCode = 'CN' ?
        'Individual' :            $roleDetail.dataArea.ruleCode = 'CTTT' ?
        'Direct superior' :            $roleDetail.dataArea.ruleCode = 'CTGT' ?
        'Indirect superior' :            $roleDetail.dataArea.ruleCode = 'ORG' ?
        'Structure' :            'Employee list',          'criteria':
        $roleDetail.criteria        }      })    }),    $append  )]
      disabledCache: true
      params:
        - roleIds
    nationsList:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'search': $.search,'filter':
        [{'field':'status', 'operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    datasecuritygroupsList:
      uri: '"/api/data-security-groups/infos"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'search': $.search, 'filter':
        [{'field':'countryCode','operator': '$eq','value': $.national
        },{'field':'effectiveDateQuery','operator': '$eq','value':
        $.effectiveDate  }]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($filter($.data, function ($v) {$v.status = true }), function($item)
        {{'label': $item.name.default & ' (' & $item.code & ')', 'value':
        $item.id, 'name': $item.name.default, 'dataSecurityFields':$append([],
        $map($item.dataSecurityFields, function($v){$v.fieldName}))}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - national
        - effectiveDate
    admindataareasList:
      uri: '"/api/admin-data-areas/infos"'
      method: GET
      queryTransform: >-
        {'limit':999,'filter': [{'field':'status','operator':
        '$eq','value':true} ,{'field':'companyCode','operator':
        '$eq','value':$.companyCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {  {    'criteria': $item.criteria,  'label':
        $item.name.default & ' (' & $item.code & ')',    'value': $item.id,   
        'name': $item.name.default,    'dataAreaDetails':
        $item.dataAreaDetails,    'includesSubordinates':
        $item.includesSubordinates,    'permissionWith':      $item.ruleCode =
        'CN'   ? 'Individual' :      $item.ruleCode = 'CTTT' ? 'Direct superior'
        :      $item.ruleCode = 'CTGT' ? 'Indirect superior' :     
        $item.ruleCode = 'ORG'  ? 'Structure' :      $item.ruleCode = 'NVS'  ?
        'Employee list' : ''  }})[]
      disabledCache: true
      params:
        - companyCode
  historyHeaderTitle: '''User-Based Permission Details'''
  historyStatusName: permissionEnabled
filter_config:
  fields:
    - type: selectAll
      name: user
      label: User
      labelType: type-grid-240px
      placeholder: Select User
      _options:
        transform: $usersList()
      mode: multiple
    - type: selectAll
      name: employeeId
      label: Employee
      labelType: type-grid-240px
      placeholder: Select Employee (ID; Name)
      _options:
        transform: $personalsList()
      mode: multiple
    - type: selectAll
      name: company
      label: Company
      labelType: type-grid-240px
      placeholder: Select Company
      _options:
        transform: $companiesList()
      mode: multiple
    - type: selectAll
      name: groupCode
      label: User Group
      labelType: type-grid-240px
      placeholder: Select User Group
      _options:
        transform: $groupssysList()
      mode: multiple
    - type: selectAll
      name: dfRoleCode
      label: Default Role
      labelType: type-grid-240px
      placeholder: Select Default Role
      _options:
        transform: $adminrolesList()
      mode: multiple
    - type: selectAll
      name: roleCode
      label: Addition Role
      labelType: type-grid-240px
      placeholder: Select Addition Role
      _options:
        transform: $adminrolesList()
      mode: multiple
    - type: dateRange
      name: effectiveDatePermission
      label: Effective Date
      labelType: type-grid-240px
      setting:
        format: dd/MM/yyyy
        type: date
    - type: radio
      name: permissionEnabled
      label: Status
      labelType: type-grid-240px
      radio:
        - label: All
          value: ''
        - label: Active
          value: true
        - label: Inactive
          value: false
    - type: dateRange
      name: permissionUpdatedAt
      label: Last Updated On
      labelType: type-grid-240px
      setting:
        format: dd/MM/yyyy
        type: date
    - type: text
      name: permissionUpdatedBy
      label: Last Updated By
      labelType: type-grid-240px
      placeholder: Enter Editor
  filterMapping:
    - valueField: user.(value)
      field: id
      operator: $in
    - valueField: employeeId.(value)
      field: employeeCode
      operator: $in
    - valueField: company.(value)
      field: companyCode
      operator: $in
    - valueField: groupCode.(value)
      field: groupCode
      operator: $in
    - valueField: dfRoleCode.(value)
      field: dfRoleCode
      operator: $in
    - valueField: roleCode.(value)
      field: roleCode
      operator: $in
    - valueField: effectiveDatePermission
      field: effectiveDatePermission
      operator: $between
    - valueField: permissionEnabled
      field: permissionEnabled
      operator: $eq
    - valueField: permissionUpdatedAt
      field: permissionUpdatedAt
      operator: $between
    - valueField: permissionUpdatedBy
      field: permissionUpdatedBy
      operator: $cont
  sources:
    usersList:
      uri: '"/api/admin-users/infos"'
      method: GET
      queryTransform: >-
        {'limit': 5000, 'filter': [{'field':'companyCode','operator':
        '$eq','value':$.companyCode}, {'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.account & ' (' &
        ($item.companyCode ? $item.companyCode : '-') & ')', 'value': $item.id
        }})[]
      disabledCache: true
      params:
        - companyCode
        - effectiveDate
    personalsList:
      uri: '"/api/personals"'
      method: GET
      queryTransform: >-
        {'limit':5000, 'filter': [{'field':'status','operator':
        '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $distinct($map($.data, function($item) {{'label': $item.name & ' (' &
        $item.employeeId & ')', 'value': $item.employeeId, 'employeeId':
        $item.employeeId  }})[])
      disabledCache: true
    companiesList:
      uri: '"/api/companies/by"'
      method: GET
      queryTransform: >-
        {'limit':5000, 'filter': [{'field':'status','operator':
        '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
    groupssysList:
      uri: '"/api/groups-sys"'
      method: GET
      queryTransform: >-
        {'limit':5000, 'filter': [{'field':'status','operator':
        '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.code & ' - ' &
        $item.name.default, 'value': $item.code}})[]
      disabledCache: true
    adminrolesList:
      uri: '"/api/admin-roles"'
      method: GET
      queryTransform: >-
        {'limit':5000, 'filter': [{'field':'status','operator':
        '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.code & ' - ' &
        $item.name.default , 'value': $item.code}})[]
      disabledCache: true
    datasecuritygroupsList:
      uri: '"/api/data-security-groups"'
      method: GET
      queryTransform: >-
        {'limit':5000, 'filter': [{'field':'status','operator':
        '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
layout_options:
  duplicate_value_transform:
    fields:
      - name
      - code
    transform: $ & ' - Copy'
  tool_table:
    - id: import
      icon: icon-upload-simple
      href: /GE/HR.FS.FR.092
    - id: export
      icon: icon-download-simple
  custom_detail_backend_url: >-
    /api/admin-users-effective-date/:id/effective-date/:effectiveDatePermissionTimestamp
  custom_delete_backend_url: >-
    /api/admin-users-effective-date/:id/effective-date/:effectiveDatePermissionTimestamp
  history_widget_header_options:
    duplicate: false
  is_new_dynamic_form: true
  hide_action_row: true
  delete_multi_items: true
  custom_delete_body: >-
    $map($.data, function($item){{'userId': $item.userId, 'effectiveDate':
    $item.effectiveDate, 'userName': $item.userName}})[]
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    icon: pencil
  - id: delete
    icon: trash
backend_url: api/admin-users-permisions
screen_name: admin-users
layout_options__actions_many: null
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: userId
    defaultName: EmployeeId
  - name: companyCode
    defaultName: CompanyCode
  - name: countryCode
    defaultName: CountryCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Manage User-Based Permission
  parent:
    title: Function list
