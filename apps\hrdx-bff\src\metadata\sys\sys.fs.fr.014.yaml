id: SYS.FS.FR.014
status: draft
sort: 511
user_created: 43878dbb-aa6c-4ef4-8bd7-0ea811ffafa7
date_created: '2024-08-05T12:41:27.862Z'
user_updated: 43878dbb-aa6c-4ef4-8bd7-0ea811ffafa7
date_updated: '2025-07-22T07:39:20.593Z'
title: Request Account Email FPT For Individual
requirement:
  time: *************
  blocks:
    - id: _X7_YF4zna
      type: paragraph
      data:
        text: >-
          <PERSON><PERSON><PERSON> năng cho phép tạo phiếu yêu cầu tạo Email FPT cho nhân viên bao
          gồm: Tạo account cho nhân viên, cho nhân viên người nước ngoài và tạo
          email với tên đặc biệt.
    - id: ZNDbWUNDfN
      type: paragraph
      data:
        text: '1'
  version: 2.30.7
screen_design: null
module: SYS
local_fields:
  - code: requestId
    title: Request ID
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    description: >-
      <PERSON><PERSON> phiếu yêu cầu sẽ tự sinh. Hiển thị danh sách phiếu yêu cầu đã tạo theo
      phân quyền.
    pinned: true
    show_sort: true
  - code: requestSubject
    title: Request Subject
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    description: Hiển thị tên phiếu yêu cầu tương ứng theo mã phiếu yêu cầu.
    show_sort: true
  - code: regionName
    title: Location Group
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: Hiển thị vùng/miền tương ứng với phiếu yêu cầu.
    show_sort: true
  - code: requesterName
    title: Requester
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: Hiển thị người yêu cầu tương ứng với phiếu yêu cầu.
    show_sort: true
  - code: requestTypeName
    title: Request Type
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: Hiển thị loại yêu cầu tương ứng với tên phiếu yêu cầu.
    show_sort: true
  - code: mailboxTypeName
    title: Mailbox Type
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: Hiển thị loại hộp thư tương ứng với phiếu yêu cầu.
    show_sort: true
  - code: approvalStatusCode
    title: Approval Status
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tag
      collection: field_types
    description: Hiển thị mô tả tương ứng với phiếu yêu cầu.
    extra_config:
      tags:
        - value: CREATED
          label: Created
          style:
            background_color: '#E6F2FF'
        - value: DELIVERED
          label: Transfer UService
          style:
            background_color: '#FEF9CC'
        - value: REJECTED
          label: Rejected
          style:
            background_color: '#FFE8E5'
        - value: APPROVED
          label: Approved
          style:
            background_color: '#E0FAE9'
    options__tabular__column_width: 13
    show_sort: true
  - code: updatedAt
    title: Last Updated On
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
    description: >-
      Hiển thị đầy đủ thông tin theo định dạng Ngày/Tháng/Năm Giờ/Phút/Giây của
      người thực hiện cập nhật dữ liệu cuối cùng (Ví dụ: 06/05/2024 10:20:53)
  - code: updatedBy
    title: Last Updated By
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: >-
      Hiển thị tên tài khoản của người cập nhật dữ liệu cuối cùng (Ví dụ:
      NguyenPN)
    show_sort: true
mock_data:
  - effectiveDate: '2024-05-06'
    requestId: '12345'
    requestSubject: Create account for employees
    yourLocation: HCM City
    requester: Loan Le Thi
    requestType: Create account for staff
    requestTypeId: '1'
    mailboxType: Local
    attachFile:
      - name: Filename.pdf
        url: '123'
    approvalStatus:
      type: infor
      label: Add new
    createdOn: '2024-05-06 10:25:01'
    createdBy: Benjamin Harrison
    lastUpdatedOn: '2024-05-06 18:59:11'
    lastUpdatedBy: Lucas Theodore
  - effectiveDate: '2024-05-06'
    requestId: '12346'
    requestSubject: Create account for foreign staff
    yourLocation: HCM City
    requester: Loan Le Thi
    requestType: Create account for foreign staff
    requestTypeId: '2'
    mailboxType: Local
    attachFile:
      - name: Filename.pdf
        url: '123'
    approvalStatus:
      type: warning
      label: Transfer UService
    createdOn: '2024-05-06 10:25:01'
    createdBy: Benjamin Harrison
    lastUpdatedOn: '2024-05-06 18:59:11'
    lastUpdatedBy: Lucas Theodore
  - effectiveDate: '2024-05-06'
    requestId: '12347'
    requestSubject: Create account special staff
    yourLocation: HCM City
    requester: Loan Le Thi
    requestType: Create account with special permissions
    requestTypeId: '3'
    mailboxType: Local
    attachFile:
      - name: Filename.pdf
        url: '123'
    approvalStatus:
      type: error
      label: Rejected
    createdOn: '2024-05-06 10:25:01'
    createdBy: Benjamin Harrison
    lastUpdatedOn: '2024-05-06 18:59:11'
    lastUpdatedBy: Lucas Theodore
  - effectiveDate: '2024-05-06'
    requestId: '12348'
    requestSubject: Disable account
    yourLocation: HCM City
    requester: Loan Le Thi
    requestType: Disable account email
    requestTypeId: '4'
    mailboxType: Local
    attachFile:
      - name: Filename.pdf
        url: '123'
    approvalStatus:
      type: success
      label: Approved
    createdOn: '2024-05-06 10:25:01'
    createdBy: Benjamin Harrison
    lastUpdatedOn: '2024-05-06 18:59:11'
    lastUpdatedBy: Lucas Theodore
  - effectiveDate: '2024-05-06'
    requestId: '12349'
    requestSubject: Reactive account email
    yourLocation: HCM City
    requester: Loan Le Thi
    requestType: Reactive account email
    requestTypeId: '5'
    mailboxType: Local
    attachFile:
      - name: Filename.pdf
        url: '123'
    approvalStatus:
      type: success
      label: Approved
    createdOn: '2024-05-06 10:25:01'
    createdBy: Benjamin Harrison
    lastUpdatedOn: '2024-05-06 18:59:11'
    lastUpdatedBy: Lucas Theodore
  - effectiveDate: '2024-05-06'
    requestId: '12350'
    requestSubject: Keep account
    yourLocation: HCM City
    requester: Loan Le Thi
    requestType: Keep account
    requestTypeId: '6'
    mailboxType: Local
    attachFile:
      - name: Filename.pdf
        url: '123'
    approvalStatus:
      type: success
      label: Approved
    createdOn: '2024-05-06 10:25:01'
    createdBy: Benjamin Harrison
    lastUpdatedOn: '2024-05-06 18:59:11'
    lastUpdatedBy: Lucas Theodore
local_buttons: null
layout: layout-table
form_config:
  formSize:
    create: large
    edit: large
    view: large
    proceed: large
    history: largex
  fields:
    - type: text
      name: id
      unvisible: true
    - type: group
      n_cols: '1'
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - name: requestId
          type: text
          label: Request ID
        - name: approvalStatus
          type: select
          select:
            - label: Created
              value: CREATED
            - label: Transferred UService
              value: DELIVERED
            - label: Rejected
              value: REJECTED
            - label: Approved
              value: APPROVED
          label: Approval Status
          _condition:
            transform: $not($.extend.formType = 'create')
          displaySetting:
            type: Tag
            extraConfig:
              tags:
                - value: CREATED
                  label: Created
                  style:
                    background_color: '#E6F2FF'
                - value: DELIVERED
                  label: Transfer UService
                  style:
                    background_color: '#FEF9CC'
                - value: REJECTED
                  label: Rejected
                  style:
                    background_color: '#FFE8E5'
                - value: APPROVED
                  label: Approved
                  style:
                    background_color: '#E0FAE9'
              size: small
        - name: requestSubject
          type: text
          label: Request Subject
        - name: requesterUserEmail
          type: text
          _select:
            transform: $requestersList()
          label: Requester
          _value:
            transform: >-
              $join($filter([$.extend.defaultValue.requesterUserName,$.extend.defaultValue.requesterUserEmail],
              $boolean), ' - ')
          outputValue: value
        - name: regionCode
          type: select
          _select:
            transform: $locationsList()
          label: Location Group
          outputValue: value
        - name: requestTypeName
          type: text
          label: Request Type
        - name: RequestTypeCode
          type: text
          label: Request Type
          unvisible: true
          _value:
            transform: $.extend.defaultValue.requestTypeCode
        - name: mailboxType
          type: select
          _select:
            transform: $mailBoxTypesList()
          label: Mail Box Type
          outputValue: value
    - type: group
      n_cols: '2'
      _condition:
        transform: $.extend.formType = 'edit'
      fields:
        - name: requestId
          type: text
          label: Request ID
          placeholder: Enter Request ID
          disabled: true
        - name: approvalStatus
          type: select
          select:
            - label: Created
              value: CREATED
            - label: Transferred UService
              value: DELIVERED
            - label: Rejected
              value: REJECTED
            - label: Approved
              value: APPROVED
          label: Approval Status
          placeholder: Select Approval Status
          _disabled:
            transform: 'true'
    - type: group
      n_cols: '4'
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - name: requestSubject
          type: text
          label: Request Subject
          placeholder: Enter Request Subject
          validators:
            - type: required
        - name: regionCode
          type: select
          _select:
            transform: $locationsList()
          outputValue: value
          label: Location Group
          placeholder: Select Location Group
          validators:
            - type: required
        - name: requester
          type: select
          _select:
            transform: $requestersList($.extend.limit, $.extend.page, $.extend.search)
          isLazyLoad: true
          label: Requester
          outputValue: value
          placeholder: Select Requester
          _value:
            transform: >-
              $isNilorEmpty($.extend.defaultValue.requesterCode) ? {'value':
              $substringBefore($.variables._personalInfo.email, '@'), 'label':
              $join($filter([$.variables._personalInfo.name,$.variables._personalInfo.email],
              $boolean), ' - ')} : $.variables._requesterForEdit
          validators:
            - type: required
        - name: mailboxType
          type: select
          _select:
            transform: $mailBoxTypesList()
          label: Mail Box Type
          placeholder: Select Mail Box Type
          _class:
            transform: >-
              $not($.fields.RequestTypeCode = 'create acc' or
              $.fields.RequestTypeCode = 'create acc foreign staff' or
              $.fields.RequestTypeCode = 'create acc special') ? 'unrequired':
              'required'
          outputValue: value
    - name: RequestTypeCode
      type: radio
      n_cols: '2'
      direction: column
      border: '1px solid #e0e0e0'
      collapse: false
      label: Request Type
      _condition:
        transform: $not($.extend.formType = 'view')
      _value:
        transform: >-
          $.extend.formType = 'edit' ? $.extend.defaultValue.requestTypeCode :
          $.extend.formType = 'create' ? 'create acc'
      toast:
        position: top
        type: warning
        content: 'Caution: If you change options, unsaved input data will be lost'
      validators:
        - type: required
      radio:
        - label: Create account for staff
          value: create acc
        - label: Disable account email
          value: disable acc
        - label: Reactive account email
          value: reactive acc
        - label: Create typical display name account
          value: create acc special
        - label: Keep account
          value: keep acc
      outputValue: value
    - name: approver
      type: select
      isLazyLoad: true
      _condition:
        transform: >-
          ($.fields.requestTypeCode = 'reactive acc' or $.fields.requestTypeCode
          = 'keep acc') and $.extend.formType = 'view'
      _select:
        transform: $approversList($.extend.limit, $.extend.page, $.extend.search)
      label: Approver
      outputValue: value
      placeholder: Select Approver
    - name: approver
      type: select
      isLazyLoad: true
      _condition:
        transform: >-
          ($.fields.RequestTypeCode = 'reactive acc' or $.fields.RequestTypeCode
          = 'keep acc') and $not($.extend.formType = 'view')
      _select:
        transform: $approversList($.extend.limit, $.extend.page, $.extend.search)
      label: Approver
      outputValue: value
      placeholder: Select Approver
      validators:
        - type: required
    - type: table
      mode: table
      name: details
      rowIdName: idx
      isDuplicateKeyName: true
      insertDataConfig:
        checkDuplicate:
          by:
            - employeeId
          validateMessage: There are some duplicate user, please check again.
        transformDataExpression: >-
          $map($.formValue.employeeInfo.employeeId, function($item){    {   
          'employeeId': $item.employeeId,    'firstName': $item.firstName,   
          'lastName': $item.lastName,    'companyName': $item.companyName,   
          'mailGroup': $.formValue.employeeInfo.mailGroup,    'companyCode':
          $item.company,    'CreateAccountType': 'FromHRList',    'from': 'HR
          List'  }})[]
        formConfig:
          sources:
            personalFullInfoList:
              uri: '"/api/personals"'
              method: GET
              queryTransform: >-
                {'limit': $.limit, 'page': $.page, 'sort':
                [{'field':'employeeId', 'order':'desc'},
                {'field':'employeeRecordNumber', 'order':'desc'}], 'filter':[
                {'field':'search','operator': '$eq','value':$.search}, {'field':
                'email', 'operator': '$eq', 'value': 'NULL'}]}
              bodyTransform: ''
              headerTransform: ''
              resultTransform: >-
                $map($.data, function($item) {{'label':  $item.employeeId & ' -
                ' & $item.employeeRecordNumber & ' - ' & $item.name & ' - '  &
                $item.company, 'value': $item}})[]
              disabledCache: true
              params:
                - limit
                - page
                - search
          fields:
            - type: group
              label: ''
              name: employeeInfo
              isResetOnTabChange: true
              dependantField: ' $.fields.RequestTypeCode'
              _condition:
                transform: ($.extend.formType = 'create' or $.extend.formType = 'edit')
              fields:
                - type: group
                  label: From HR List
                  tabName: hrList
                  n_cols: 2
                  padding: 8px 0px 0px 0px
                  actionsCustomStyle: 'width: 100%'
                  fields:
                    - name: employeeId
                      type: selectAll
                      label: Employee
                      placeholder: Select Employee
                      outputValue: value
                      mode: multiple
                      isLazyLoad: true
                      _options:
                        transform: >-
                          $personalFullInfoList($.extend.limit, $.extend.page,
                          $.extend.search )
                      validators:
                        - type: required
                    - name: mailGroup
                      type: text
                      label: Mail Group
                      placeholder: Enter Mail Group
              _mode:
                transform: '''tabset'''
      mapRowName:
        - idx
        - companyCode
        - isDuplicate
        - companySlect
      dependantField: ' $.fields.RequestTypeCode'
      _row_actions:
        transform: >-
          $.fields._tabActive = 'hrList' ?
          [{'id':'delete','type':'ghost-gray','icon':'icon-trash-bold'}] :
          [{'id':'delete','type':'ghost-gray','icon':'icon-trash-bold'}]
      _condition:
        transform: ($.fields.RequestTypeCode = 'create acc')
      arrayOptions:
        cloneAction: false
        enabledField: locationBasedAccess
        cloneValues:
          default: false
        isCheckboxTable: true
      layout_option:
        tool_table:
          show_table_checkbox: true
      _dataSource:
        transform: >-
          ( $lst :=[    ($isNilorEmpty($.fields.employeeInfo)     ?
          $test($.value[])     : $append(        $.value[],        
          $.fields._tabActive = 'hrList' and 
          $not($isNilorEmpty($.fields.employeeInfo.employeeId))  ?
          $map($.fields.employeeInfo.employeeId, function($emp){
          {'employeeId':$emp.employeeId,    'firstName': $emp.firstName,   
          'lastName': $emp.lastName ,    'companyName':$emp.companyView,   
          'mailGroup': $.fields.employeeInfo.mailGroup,    'companyCode': 
          $emp.company,    'CreateAccountType': 'FromHRList', 'from': 'HR
          List'   } })[]    : 
          $not($isNilorEmpty($.fields.employeeInfo.firstName))  ?  [{ 
          'firstName': $.fields.employeeInfo.firstName,'lastName':
          $.fields.employeeInfo.lastName ,'companyName':
          $.fields.employeeInfo.company.label,    'mailGroup':
          $.fields.employeeInfo.mailGroup,    'companyCode': 
          $.fields.employeeInfo.company.value,'CreateAccountType': 'Manual'   
          }  ]  ))];[$map($lst, function($value, $idx) {$merge([$value, {'idx':
          $string($idx), 'isDuplicate': true, 'companySlect': {'value':
          $value.companyCode, 'label':$value.companyName}, 'RequestTypeCode':
          $.fields.RequestTypeCode, 'from': 'HR List'  }])})] )
      columns:
        - code: employeeId
          title: Employee ID
          align: start
          width: '12.5'
          data_type:
            key: String
            collection: data_types
          display_type:
            key: Hyperlink
            collection: field_types
          hyperlink:
            hyperlinkTitle: Components permissions details
            fields: []
        - code: firstName
          title: First Name
          align: start
          data_type:
            key: String
            collection: data_types
          display_type:
            key: Label
            collection: field_types
        - code: lastName
          title: Last Name
          align: start
          data_type:
            key: String
            collection: data_types
          display_type:
            key: Label
            collection: field_types
        - code: companyName
          title: Company
          align: start
          data_type:
            key: String
            collection: data_types
          display_type:
            key: Tooltip
            collection: field_types
        - code: mailGroup
          title: Mail Group
          align: start
          _condition:
            transform: $.extend.formType = 'view'
          data_type:
            key: String
            collection: data_types
          display_type:
            key: Tooltip
            collection: field_types
        - code: mailGroup
          title: Mail Group
          align: start
          _condition:
            transform: $not($.extend.formType = 'view')
          data_type:
            key: String
            collection: data_types
          display_type:
            key: Control Text
            collection: field_types
        - code: from
          title: From
          align: start
          data_type:
            key: String
            collection: data_types
          display_type:
            key: Label
            collection: field_types
      form_config:
        fields:
          - type: text
            name: firstName
            label: First Name
          - type: text
            name: lastName
            label: Last Name
          - type: select
            name: companySlect
            isLazyLoad: true
            label: Company
            _select:
              transform: $companiesList($.extend.limit, $.extend.page, $.extend.search)
          - type: text
            name: companyName
            label: Data Zone
            _value:
              transform: $.fields.companySlect.label
            unvisible: true
          - type: text
            name: companyCode
            unvisible: true
            _value:
              transform: $.fields.companySlect.value
          - type: text
            name: mailGroup
            label: Mail Group
        sources:
          companiesList:
            uri: '"/api/companies"'
            queryTransform: >-
              {'limit': $.limit, 'page': $.page, 'filter':
              [{'field':'search','operator':
              '$eq','value':$.search},{'field':'status','operator':
              '$eq','value':true}]}
            method: GET
            bodyTransform: ''
            headerTransform: ''
            resultTransform: >-
              $map($.data, function($item) {{'label': $item.longName.default & '
              - ' & $item.code, 'value': $item.code, 'id': $item.id }})[]
            disabledCache: true
            params:
              - limit
              - page
              - search
        variables: {}
    - type: table
      mode: table
      name: details
      isDuplicateKeyName: true
      insertDataConfig:
        checkDuplicate:
          by:
            - employeeId
          validateMessage: There are some duplicate user, please check again.
        transformDataExpression: >-
          $map($.formValue.employeeInfo.employeeId, function($item){    {   
          'employeeId': $item.employeeId,    'displayName':
          $.formValue.employeeInfo.displayName,    'reason':
          $.formValue.employeeInfo.reason,    'firstName': $item.firstName,   
          'lastName': $item.lastName,    'companyName': $item.companyName,   
          'mailGroup':$.formValue.employeeInfo.mailGroup,    'companyCode':
          $item.company,    'CreateAccountType': 'FromHRList',    'from': 'HR
          List'  }})[]
        formConfig:
          sources:
            personalFullInfoList:
              uri: '"/api/personals"'
              method: GET
              queryTransform: >-
                {'limit': $.limit, 'page': $.page, 'sort':
                [{'field':'employeeId', 'order':'desc'},
                {'field':'employeeRecordNumber', 'order':'desc'}], 'filter':[
                {'field':'search','operator': '$eq','value':$.search}, {'field':
                'email', 'operator': '$eq', 'value': 'NULL'}]}
              bodyTransform: ''
              headerTransform: ''
              resultTransform: >-
                $map($.data, function($item) {{'label':  $item.employeeId & ' -
                ' & $item.employeeRecordNumber & ' - ' & $item.name & ' - '  &
                $item.company, 'value': $item}})[]
              disabledCache: true
              params:
                - limit
                - page
                - search
          fields:
            - type: group
              label: ''
              name: employeeInfo
              isResetOnTabChange: true
              dependantField: ' $.fields.RequestTypeCode'
              _condition:
                transform: ($.extend.formType = 'create' or $.extend.formType = 'edit')
              fields:
                - type: group
                  label: From HR List
                  tabName: hrList
                  n_cols: 4
                  padding: 8px 0px 0px 0px
                  actionsCustomStyle: 'width: 100%'
                  fields:
                    - name: employeeId
                      type: selectAll
                      label: Employee
                      placeholder: Select Employee
                      outputValue: value
                      mode: multiple
                      isLazyLoad: true
                      _options:
                        transform: >-
                          $personalFullInfoList($.extend.limit, $.extend.page,
                          $.extend.search )
                      validators:
                        - type: required
                    - name: displayName
                      type: text
                      label: Display Name
                      validators:
                        - type: required
                    - name: reason
                      type: text
                      label: Reason
                      validators:
                        - type: required
                    - name: mailGroup
                      type: text
                      label: Mail Group
                      placeholder: Enter Mail Group
              _mode:
                transform: '''tabset'''
      rowIdName: idx
      mapRowName:
        - idx
        - companyCode
        - isDuplicate
        - companySlect
      dependantField: ' $.fields.RequestTypeCode'
      _row_actions:
        transform: >-
          $.fields._tabActive = 'hrList' ?
          [{'id':'delete','type':'ghost-gray','icon':'icon-trash-bold'}] :
          [{'id':'delete','type':'ghost-gray','icon':'icon-trash-bold'}]
      _condition:
        transform: ' ($.fields.RequestTypeCode = ''create acc special'')'
      arrayOptions:
        cloneAction: false
        enabledField: locationBasedAccess
        cloneValues:
          default: false
        isCheckboxTable: true
      layout_option:
        tool_table:
          show_table_checkbox: true
      _dataSource:
        transform: >-
          ( $lst :=[ ($isNilorEmpty($.fields.employeeInfo) ? $.value[] :
          ($append($.value[], $.fields._tabActive = 'hrList' and
          $not($isNilorEmpty($.fields.employeeInfo.employeeId))
          ?$map($.fields.employeeInfo.employeeId, function($emp){ {'employeeId':
          $emp.employeeId,'firstName': $emp.firstName,'displayName':
          $.fields.employeeInfo.displayName,'reason':
          $.fields.employeeInfo.reason,'lastName': $emp.lastName ,'companyName':
          $emp.companyView & ' (' & $emp.company& ')','mailGroup':
          $.fields.employeeInfo.mailGroup,'companyCode':$emp.company,'CreateAccountType':
          'FromHRList','from':'HR List'}})[] :
          $not($isNilorEmpty($.fields.employeeInfo.displayName))?[{'displayName':
          $.fields.employeeInfo.displayName,'reason':
          $.fields.employeeInfo.reason,'firstName':
          $.fields.employeeInfo.firstName, 'lastName':
          $.fields.employeeInfo.lastName ,'companyName':
          $.fields.employeeInfo.company.label,'mailGroup':
          $.fields.employeeInfo.mailGroup,
          'companyCode':$.fields.employeeInfo.company.value ,
          'CreateAccountType': 'Manual'} ])))];[$map($distinct($lst),
          function($value, $idx){$merge([$value, {'idx': $string($idx),
          'isDuplicate': true, 'companySlect': {'value': $value.companyCode,
          'label':$value.companyName}, 'RequestTypeCode':
          $.fields.RequestTypeCode, 'from': 'HR List'}])})])
      columns:
        - code: employeeId
          title: Employee ID
          align: start
          width: '12.5'
          data_type:
            key: String
            collection: data_types
          display_type:
            key: Hyperlink
            collection: field_types
          hyperlink:
            hyperlinkTitle: Components permissions details
            fields: []
        - code: firstName
          title: First Name
          align: start
          data_type:
            key: String
            collection: data_types
          display_type:
            key: Label
            collection: field_types
        - code: lastName
          title: Last Name
          align: start
          data_type:
            key: String
            collection: data_types
          display_type:
            key: Label
            collection: field_types
        - code: displayName
          title: Display Name
          align: start
          _condition:
            transform: $.extend.formType = 'view'
          data_type:
            key: String
            collection: data_types
          display_type:
            key: Tooltip
            collection: field_types
        - code: displayName
          title: Display Name
          align: start
          _condition:
            transform: $not($.extend.formType = 'view')
          data_type:
            key: String
            collection: data_types
          display_type:
            key: Control Text
            collection: field_types
        - code: reason
          title: Reason
          align: start
          _condition:
            transform: $.extend.formType = 'view'
          data_type:
            key: String
            collection: data_types
          display_type:
            key: Tooltip
            collection: field_types
        - code: reason
          title: Reason
          align: start
          _condition:
            transform: $not($.extend.formType = 'view')
          data_type:
            key: String
            collection: data_types
          display_type:
            key: Control Text
            collection: field_types
        - code: companyName
          title: Company
          align: start
          data_type:
            key: String
            collection: data_types
          display_type:
            key: Tooltip
            collection: field_types
        - code: mailGroup
          title: Mail Group
          align: start
          _condition:
            transform: $.extend.formType = 'view'
          data_type:
            key: String
            collection: data_types
          display_type:
            key: Tooltip
            collection: field_types
        - code: mailGroup
          title: Mail Group
          align: start
          _condition:
            transform: $not($.extend.formType = 'view')
          data_type:
            key: String
            collection: data_types
          display_type:
            key: Control Text
            collection: field_types
        - code: from
          title: From
          align: start
          data_type:
            key: String
            collection: data_types
          display_type:
            key: Tooltip
            collection: field_types
      form_config:
        fields:
          - type: text
            name: firstName
            label: First Name
          - type: text
            name: lastName
            label: Last Name
          - name: displayName
            label: Display Name
            type: text
          - name: reason
            label: Reason
            type: text
          - type: select
            isLazyLoad: true
            name: companySlect
            label: Company
            _select:
              transform: $companiesList($.extend.limit, $.extend.page, $.extend.search)
          - type: text
            name: companyName
            label: Data Zone
            _value:
              transform: $.fields.companySlect.label
            unvisible: true
          - type: text
            name: companyCode
            unvisible: true
            _value:
              transform: $.fields.companySlect.value
          - type: text
            name: mailGroup
            label: Mail Group
        sources:
          companiesList:
            uri: '"/api/companies"'
            queryTransform: >-
              {'limit': $.limit, 'page': $.page, 'filter':
              [{'field':'search','operator':
              '$eq','value':$.search},{'field':'status','operator':
              '$eq','value':true}]}
            method: GET
            bodyTransform: ''
            headerTransform: ''
            resultTransform: >-
              $map($.data, function($item) {{'label': $item.longName.default & '
              - ' & $item.code, 'value': $item.code, 'id': $item.id }})[]
            disabledCache: true
            params:
              - limit
              - page
              - search
        variables: {}
    - type: table
      mode: table
      name: details
      isDuplicateKeyName: true
      rowIdName: idx
      mapRowName:
        - idx
        - company
        - isDuplicate
        - companySlect
      dependantField: ' $.fields.RequestTypeCode'
      _row_actions:
        transform: >-
          $.fields._tabActive = 'hrList' ?
          [{'id':'delete','type':'ghost-gray','icon':'icon-trash-bold'}] :
          [{'id':'edit','type':'ghost-gray','icon':'pencil'},{'id':'delete','type':'ghost-gray','icon':'icon-trash-bold'}]
      _condition:
        transform: ' ($.fields.RequestTypeCode = ''create acc foreign staff'')'
      arrayOptions:
        cloneAction: false
        enabledField: locationBasedAccess
        cloneValues:
          default: false
        isCheckboxTable: true
      layout_option:
        tool_table:
          show_table_checkbox: true
      _dataSource:
        transform: >-
          ( $lst :=[    ( $isNilorEmpty($.fields.employeeInfo) ? $.value[] :
          ($append(     $.value[],        $.fields._tabActive = 'hrList' and
          $not($isNilorEmpty($.fields.employeeInfo.employeeId))    
          ?             $map($.fields.employeeInfo.employeeId,
          function($emp){{             'employeeId':$emp.employeeId,        
          'firstName': $emp.firstName,                'middleName':
          $emp.middleName,          'lastName': $emp.lastName,        
          'companyName': $emp.companyView & ' (' & $emp.company  & ')',       
          'mailGroup': $.fields.employeeInfo.mailGroup,        'companyCode':
          $emp.company    ,        'CreateAccountType': 'FromHRList',  'from':
          'HR List'      }       })[]    :    
          $not($isNilorEmpty($.fields.employeeInfo.firstName))     ?     
          [{         'firstName': $.fields.employeeInfo.firstName,       
          'middleName': $.fields.employeeInfo.middleName   ,        'lastName':
          $.fields.employeeInfo.lastName ,        'companyName':
          $.fields.employeeInfo.company.label,        'mailGroup':
          $.fields.employeeInfo.mailGroup,        'companyCode': 
          $.fields.employeeInfo.company.value ,        'CreateAccountType':
          'Manual'    }]           )))];     [$map($distinct($lst),
          function($value, $idx){$merge([$value, {'idx': $string($idx),
          'isDuplicate': true, 'companySlect': {'value': $value.companyCode,
          'label':$value.companyName}, 'RequestTypeCode':
          $.fields.RequestTypeCode, 'from': 'HR List'  }])})]         )
      columns:
        - code: employeeId
          title: Employee ID
          align: start
          width: 100px
          data_type:
            key: String
            collection: data_types
          display_type:
            key: Hyperlink
            collection: field_types
          hyperlink:
            hyperlinkTitle: Components permissions details
            fields: []
        - code: firstName
          title: First Name
          align: start
          data_type:
            key: String
            collection: data_types
          display_type:
            key: Label
            collection: field_types
        - code: middleName
          title: Middle Name
          align: start
          data_type:
            key: String
            collection: data_types
          display_type:
            key: Label
            collection: field_types
        - code: lastName
          title: Last Name
          align: start
          data_type:
            key: String
            collection: data_types
          display_type:
            key: Label
            collection: field_types
        - code: companyName
          title: Company
          align: start
          data_type:
            key: String
            collection: data_types
          display_type:
            key: Tooltip
            collection: field_types
        - code: mailGroup
          title: Mail Group
          align: start
          _condition:
            transform: $.extend.formType = 'view'
          data_type:
            key: String
            collection: data_types
          display_type:
            key: Tooltip
            collection: field_types
        - code: mailGroup
          title: Mail Group
          align: start
          _condition:
            transform: $not($.extend.formType = 'view')
          data_type:
            key: String
            collection: data_types
          display_type:
            key: Control Text
            collection: field_types
        - code: from
          title: From
          align: start
          data_type:
            key: String
            collection: data_types
          display_type:
            key: Tooltip
            collection: field_types
      form_config:
        fields:
          - type: text
            name: firstName
            label: First Name
          - type: text
            name: middleName
            label: Middle Name
          - type: text
            name: lastName
            label: Last Name
          - type: select
            isLazyLoad: true
            name: companySlect
            label: Company
            _select:
              transform: $companiesList($.extend.limit, $.extend.page, $.extend.search)
          - type: text
            name: companyName
            label: Data Zone
            _value:
              transform: $.fields.companySlect.label
            unvisible: true
          - type: text
            name: companyCode
            unvisible: true
            _value:
              transform: $.fields.companySlect.value
          - type: text
            name: mailGroup
            label: Mail Group
        sources:
          companiesList:
            uri: '"/api/companies"'
            queryTransform: >-
              {'limit': $.limit, 'page': $.page, 'filter':
              [{'field':'search','operator':
              '$eq','value':$.search},{'field':'status','operator':
              '$eq','value':true}]}
            method: GET
            bodyTransform: ''
            headerTransform: ''
            resultTransform: >-
              $map($.data, function($item) {{'label': $item.longName.default & '
              - ' & $item.code, 'value': $item.code, 'id': $item.id }})[]
            disabledCache: true
            params:
              - limit
              - page
              - search
        variables: {}
    - type: table
      mode: table
      name: details
      isDuplicateKeyName: true
      rowIdName: idx
      mapRowName:
        - idx
        - company
        - isDuplicate
        - companySlect
        - inactiveDate
        - inactiveDatePicker
        - CreateAccountType
        - RequestTypeCode
      insertDataConfig:
        checkDuplicate:
          by:
            - employeeId
          validateMessage: There are some duplicate user, please check again.
        transformDataExpression: >-
          [{'employeeId': $.formValue.employeeInfo.employeeId.employeeId,
          'email': $.formValue.employeeInfo.employeeId.email, 'inactiveDate':
          $.formValue.employeeInfo.inactiveDate, 'companyName':
          $.formValue.employeeInfo.employeeId.companyView & ' (' &
          $.formValue.employeeInfo.employeeId.company  & ')', 'reason':
          $.formValue.employeeInfo.reason, 'companyCode': 
          $.formValue.employeeInfo.employeeId.company }]
        formConfig:
          sources:
            personalFullInfoHaveEmailList:
              uri: '"/api/personals"'
              method: GET
              queryTransform: >-
                {'limit': $.limit, 'page': $.page, 'filter':[
                {'field':'search','operator': '$eq','value':$.search}, {'field':
                'email', 'operator': '$ne', 'value': 'NULL'}]}
              bodyTransform: ''
              headerTransform: ''
              resultTransform: >-
                $map($.data, function($item) {{'label':  $item.employeeId & ' -
                ' & $item.employeeRecordNumber & ' - ' & $item.name & ' - '  &
                $item.company, 'value': $item}})[]
              disabledCache: true
              params:
                - limit
                - page
                - search
          fields:
            - type: group
              name: employeeInfo
              n_cols: 3
              _condition:
                transform: ($.extend.formType = 'create' or $.extend.formType = 'edit')
              fields:
                - name: employeeId
                  type: select
                  isLazyLoad: true
                  _select:
                    transform: >-
                      $personalFullInfoHaveEmailList($.extend.limit,
                      $.extend.page, $.extend.search )
                  outputValue: value
                  label: Employee
                  placeholder: Select Employee
                  validators:
                    - type: required
                    - args:
                        transform: >-
                          $test(($.value; ($not($isNilorEmpty(
                          $.value.employeeId))) and 
                          ($count($filter($.fields.details,
                          function($v){$v.employeeId = $.value.employeeId})[]))
                          > 0 ))
                      id: CheckDuplicated
                      text: Duplicate employee information. Please check again.
                      type: ppx-custom
                - name: inactiveDate
                  type: dateRange
                  label: Inactive Date
                  mode: date-picker
                  settings:
                    format: dd/MM/yyyy
                    mode: date
                  validators:
                    - type: required
                    - args:
                        transform: >-
                          $test($DateDiff($DateFormat($.value, 'yyyy-MM-DD'),
                          $DateFormat($now(), 'yyyy-MM-DD'), 'd') < 0)
                      text: Inactive Date must be on or after today's date.
                      type: ppx-custom
                  _value:
                    transform: >-
                      ($.fields.employeeInfo.employeeId.hrStatus = 'I' ?
                      $jobDataEffectiveDate($.fields.employeeInfo.employeeId.employeeId))
                - name: reason
                  type: text
                  label: Reason
                  validators:
                    - type: required
                  placeholder: Enter Reason
                  textarea:
                    autoSize:
                      minRows: 3
                      maxRows: 5
      dependantField: ' $.fields.RequestTypeCode'
      _row_actions:
        transform: >-
          $.fields._tabActive = 'hrList' ?
          [{'id':'delete','type':'ghost-gray','icon':'icon-trash-bold'}] :
          [{'id':'delete','type':'ghost-gray','icon':'icon-trash-bold'}]
      _condition:
        transform: ($.fields.RequestTypeCode = 'disable acc')
      arrayOptions:
        cloneAction: false
        enabledField: locationBasedAccess
        cloneValues:
          default: false
        isCheckboxTable: true
      layout_option:
        tool_table:
          show_table_checkbox: true
      _dataSource:
        transform: >-
          ( $lst :=[(  $isNilorEmpty($.fields.employeeInfo) ? $.value[] : 
          ($append(     $.value[],  [       
          $not($isNilorEmpty($.fields.employeeInfo.employeeId)) ?  {    
          'employeeId':$.fields.employeeInfo.employeeId.employeeId, 'email':
          $.fields.employeeInfo.employeeId.email,    'inactiveDate':
          $.fields.employeeInfo.inactiveDate    ,          'companyName':
          $.fields.employeeInfo.employeeId.companyView & ' (' &
          $.fields.employeeInfo.employeeId.company  & ')', 'reason':
          $.fields.employeeInfo.reason,     'companyCode': 
          $.fields.employeeInfo.employeeId.company     }         ]  )))];    
          [$map($distinct($lst), function($value, $idx){$merge([$value, {'idx':
          $string($idx), 'isDuplicate': true, 'companySlect': {'value':
          $value.company, 'label':$value.companyName} , 'RequestTypeCode':
          $.fields.RequestTypeCode , 'CreateAccountType': $.fields._tabActive =
          'manual'? 'Manual' : 'FromHRList', 'inactiveDate':
          $value.inActiveDate, 'inactiveDateView':
          $DateFormat($test($value.inactiveDate) ,'DD/MM/yyyy'), 'reason':
          $value.disableReason }])})]         )
      columns:
        - code: employeeId
          title: Employee ID
          align: start
          width: '12.5'
          data_type:
            key: String
            collection: data_types
          display_type:
            key: Hyperlink
            collection: field_types
          hyperlink:
            hyperlinkTitle: Components permissions details
            fields: []
        - code: email
          title: Email
          align: start
          data_type:
            key: String
            collection: data_types
          display_type:
            key: Label
            collection: field_types
        - code: companyName
          title: Company
          align: start
          data_type:
            key: String
            collection: data_types
          display_type:
            key: Label
            collection: field_types
        - code: inactiveDate
          title: Inactive Date
          _condition:
            transform: $not($.extend.formType = 'view')
          width: '17.5'
          align: start
          data_type:
            key: String
            collection: data_types
          display_type:
            key: Control DatePicker
            collection: field_types
            validators:
              - args:
                  transform: >-
                    $test($DateDiff($DateFormat($.value, 'yyyy-MM-DD'),
                    $DateFormat($now(), 'yyyy-MM-DD'), 'd') < 0)
                text: Inactive Date must be on or after today's date.
                type: ppx-custom
        - code: inactiveDateView
          title: Inactive Date
          align: start
          _condition:
            transform: $.extend.formType = 'view'
          data_type:
            key: String
            collection: data_types
          display_type:
            key: Label
            collection: field_types
        - code: reason
          title: Reason
          align: start
          _condition:
            transform: $.extend.formType = 'view'
          data_type:
            key: String
            collection: data_types
          display_type:
            key: Label
            collection: field_types
        - code: reason
          title: Reason
          align: start
          _condition:
            transform: $not($.extend.formType = 'view')
          data_type:
            key: String
            collection: data_types
          display_type:
            key: Control Text
            collection: field_types
        - code: error
          title: Error
          align: start
          data_type:
            key: String
            collection: data_types
          _condition:
            transform: $.extend.formType = 'view' or $.extend.formType = 'edit'
          display_type:
            key: Label
            collection: field_types
      form_config:
        fields:
          - type: text
            name: CreateAccountType
            label: Email
            unvisible: true
          - type: text
            name: RequestTypeCode
            label: Email
            unvisible: true
          - type: text
            name: email
            label: Email
            disabled: true
          - type: text
            isLazyLoad: true
            name: companyName
            label: Company
            _value:
              transform: $.fields.companySlect.label
            disabled: true
          - type: select
            label: Data Zone
            name: companySlect
            _select:
              transform: $companiesList($.extend.limit, $.extend.page, $.extend.search)
            unvisible: true
          - type: text
            name: company
            unvisible: true
            _value:
              transform: $.fields.companySlect.value
          - name: inactiveDatePicker
            type: dateRange
            label: Inactive Date
            mode: date-picker
            settings:
              format: dd/MM/yyyy
              mode: date
            validators:
              - type: required
          - name: inactiveDate
            type: text
            label: Inactive Date
            unvisible: true
            _value:
              transform: $DateToTimestamp($.fields.inactiveDatePicker)
          - name: inactiveDateView
            type: text
            label: Inactive Date
            unvisible: true
            _value:
              transform: $DateFormat($.fields.inactiveDatePicker, 'DD/MM/yyyy')
          - name: reason
            type: text
            label: Reason
            validators:
              - type: required
            textarea:
              autoSize:
                minRows: 3
                maxRows: 5
        sources:
          companiesList:
            uri: '"/api/companies"'
            queryTransform: >-
              {'limit': $.limit, 'page': $.page, 'filter':
              [{'field':'search','operator':
              '$eq','value':$.search},{'field':'status','operator':
              '$eq','value':true}]}
            method: GET
            bodyTransform: ''
            headerTransform: ''
            resultTransform: >-
              $map($.data, function($item) {{'label': $item.longName.default & '
              - ' & $item.code, 'value': $item.code, 'id': $item.id }})[]
            disabledCache: true
            params:
              - limit
              - page
              - search
        variables: {}
    - type: table
      mode: table
      name: details
      isDuplicateKeyName: true
      insertDataConfig:
        checkDuplicate:
          by:
            - email
          validateMessage: There are some duplicate email, please check again.
        transformDataExpression: >-
          [{        'addMailGroup':
          $.formValue.employeeInfo.row3.addMailGroup,        'email':
          $.formValue.employeeInfo.row1.email,        'reActiveNote':
          $.formValue.employeeInfo.row3.reActiveNote,        'fullName':
          $.formValue.employeeInfo.row1.fullName,        'newDisplayName':
          $.formValue.employeeInfo.row1.newDisplayName,       
          'mailboxTypeCode':
          $.formValue.employeeInfo.row2.mailboxType.value,       
          'mailboxTypeName':
          $.formValue.employeeInfo.row2.mailboxType.label,       
          'accountExpired': $.formValue.employeeInfo.row2.accountExpired,       
          'mailboxTypeSlect': {            'label':
          $.formValue.employeeInfo.row2.mailboxType.label,            'value':
          $.formValue.employeeInfo.row2.mailboxType.value        },       
          'newCompanyCode':
          $.formValue.employeeInfo.row2.newCompany.value,       
          'newCompanyName':
          $.formValue.employeeInfo.row2.newCompany.label,       
          'newCompanySlect': {            'label':
          $.formValue.employeeInfo.row2.newCompany.label,            'value':
          $.formValue.employeeInfo.row2.newCompany.value        },       
          'oldCompanyCode':
          $.formValue.employeeInfo.row2.oldCompany.value,       
          'oldCompanyName':
          $.formValue.employeeInfo.row2.oldCompany.label,       
          'oldCompanySlect': {            'label':
          $.formValue.employeeInfo.row2.oldCompany.label,            'value':
          $.formValue.employeeInfo.row2.oldCompany.value        },       
          'reactiveEndDate'    :
          $.formValue.employeeInfo.row3.expiredDate,        'accountExpriedCode'
          : $.formValue.employeeInfo.row2.accountExpired    }]
        formConfig:
          sources:
            companiesList:
              uri: '"/api/companies"'
              queryTransform: >-
                {'limit': $.limit, 'page': $.page, 'filter':
                [{'field':'search','operator':
                '$eq','value':$.search},{'field':'status','operator':
                '$eq','value':true}]}
              method: GET
              bodyTransform: ''
              headerTransform: ''
              resultTransform: >-
                $map($.data, function($item) {{'label': $item.longName.default &
                ' - ' & $item.code, 'value': $item.code, 'id': $item.id }})[]
              disabledCache: true
              params:
                - limit
                - page
                - search
            mailBoxTypesList:
              uri: '"/api/picklists/MAILBOXTYPE/values"'
              queryTransform: >-
                {'filter': [{'field':'status','operator':
                '$eq','value':true},{'field':'group','operator': '$eq','value':
                $.groupId }, {'field':'effectiveDate','operator':
                '$lte','value': $.effectiveDate}]}
              method: GET
              bodyTransform: ''
              headerTransform: ''
              resultTransform: >-
                $map($.data, function($item) {{'label': $item.name.default & '
                (' & $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
              disabledCache: true
              params:
                - effectiveDate
                - groupId
          fields:
            - type: group
              name: employeeInfo
              _condition:
                transform: ($.extend.formType = 'create' or $.extend.formType = 'edit')
              actionsCustomStyle: 'width: 100%'
              fields:
                - type: group
                  name: row1
                  n_cols: 3
                  fields:
                    - name: email
                      type: text
                      label: Email
                      placeholder: Enter Email
                      validators:
                        - type: required
                        - args:
                            transform: >-
                              $not($isNilorEmpty($.fields.employeeInfo.row1.email))
                              and
                              ($isNilorEmpty($filter($distinct($.extend.values.variables._validatorEmailList),
                              function($item) {$item.email =
                              $.fields.employeeInfo.row1.email}).fullName))
                          text: Email does not exist. Please check again!
                          type: ppx-custom
                          id: NotExistedEmail
                    - name: fullName
                      type: text
                      label: Full Name
                      placeholder: Enter Full Name
                      disabled: true
                      validators:
                        - type: required
                      dependantField: $.fields.employeeInfo.row1.email
                      _value:
                        transform: >-
                          $filter($distinct($.extend.values.variables._validatorEmailList),
                          function($item) {$item.email =
                          $.fields.employeeInfo.row1.email}).fullName
                    - name: newDisplayName
                      type: text
                      label: New Typical Display Name
                      placeholder: Enter Display Name
                      validators:
                        - type: required
                - type: group
                  name: row2
                  n_cols: 4
                  fields:
                    - name: newCompany
                      type: select
                      isLazyLoad: true
                      label: New Company
                      placeholder: Select New Company
                      _select:
                        transform: >-
                          $companiesList($.extend.limit, $.extend.page,
                          $.extend.search)
                      _value:
                        transform: >-
                          {'value': $.variables._personalInfo.company, 'label':
                          $join($filter([$.variables._personalInfo.companyName,$.variables._personalInfo.company],
                          $boolean), ' - ')}
                      validators:
                        - type: required
                    - name: oldCompany
                      type: select
                      label: Old Company
                      isLazyLoad: true
                      placeholder: Select Old Company
                      _select:
                        transform: >-
                          $companiesList($.extend.limit, $.extend.page,
                          $.extend.search)
                      validators:
                        - type: required
                    - name: mailboxType
                      type: select
                      label: Mailbox Type
                      placeholder: Select Mailbox Type
                      _select:
                        transform: $mailBoxTypesList()
                      validators:
                        - type: required
                    - name: accountExpired
                      type: select
                      label: Account Expired
                      placeholder: Select Account Expired
                      select:
                        - label: Never
                          value: Never
                        - label: Expired
                          value: Expired
                      value: Never
                      outputValue: value
                      validators:
                        - type: required
                - type: group
                  name: row3
                  _n_cols:
                    transform: >-
                      $.fields.employeeInfo.row2.accountExpired = 'Expired' ? 3
                      : 2
                  fields:
                    - name: expiredDate
                      type: dateRange
                      _condition:
                        transform: $.fields.employeeInfo.row2.accountExpired = 'Expired'
                      label: End Date
                      mode: date-picker
                      settings:
                        format: dd/MM/yyyy
                        mode: date
                      validators:
                        - type: required
                        - args:
                            transform: >-
                              ($DateDiff($DateFormat($.value, 'yyyy-MM-DD'),
                              $DateFormat($now(), 'yyyy-MM-DD'), 'd') < 0)
                          text: End Date must be after today's date.
                          type: ppx-custom
                    - name: addMailGroup
                      type: text
                      label: Add Mail Group
                      placeholder: Enter Add Mail Group
                      textarea:
                        autosize:
                          minRows: 3
                        maxCharCount: '1000'
                      validators:
                        - type: maxLength
                          args: '1000'
                          text: >-
                            You are entering more than the allowed number of
                            characters
                    - name: reActiveNote
                      type: text
                      label: Note
                      placeholder: Enter Note
                      textarea:
                        autosize:
                          minRows: 3
                        maxCharCount: 1000
                      validators:
                        - type: maxLength
                          args: 1000
                          text: Note should not exceed 1000 characters
      rowIdName: idx
      mapRowName:
        - idx
        - newCompany
        - newCompanySlect
        - oldCompany
        - oldCompanySlect
        - mailboxTypeCode
        - mailboxTypeSlect
        - isDuplicate
        - reactiveEndDatePicker
        - reactiveEndDate
        - accountExpiredCode
      dependantField: ' $.fields.RequestTypeCode'
      _row_actions:
        transform: >-
          $.fields._tabActive = 'hrList' ?
          [{'id':'delete','type':'ghost-gray','icon':'icon-trash-bold'}] :
          [{'id':'delete','type':'ghost-gray','icon':'icon-trash-bold'}]
      _condition:
        transform: ($.fields.RequestTypeCode = 'reactive acc')
      arrayOptions:
        cloneAction: false
        enabledField: locationBasedAccess
        cloneValues:
          default: false
        isCheckboxTable: true
      layout_option:
        tool_table:
          show_table_checkbox: true
      _dataSource:
        transform: >-
          ( $lst :=[ ( $isNilorEmpty($.fields.employeeInfo)   ? $.value[]     :
          $append(  $.value[], [        
          $not($isNilorEmpty($.fields.employeeInfo.row1.email))    ?       {   
          'addMailGroup': $.fields.employeeInfo.row3.addMailGroup,      'email':
          $.fields.employeeInfo.row1.email,     'reActiveNote':
          $.fields.employeeInfo.row3.reActiveNote,    
          'fullName':$.fields.employeeInfo.row1.fullName,   
          'newDisplayName':$.fields.employeeInfo.row1.newDisplayName,   
          'mailboxTypeCode': $.fields.employeeInfo.row2.mailboxType.value,   
          'mailboxTypeName': $.fields.employeeInfo.row2.mailboxType.label,
          'accountExpired': $.fields.employeeInfo.row2.accountExpired,   
          'mailboxTypeSlect':{'label':$.fields.employeeInfo.row2.mailboxType.label,
          'value':  $.fields.employeeInfo.row2.mailboxType.value},   
          'newCompanyCode': $.fields.employeeInfo.row2.newCompany.value,   
          'newCompanyName': $.fields.employeeInfo.row2.newCompany.label,   
          'newCompanySlect':{'label':$.fields.employeeInfo.row2.newCompany.label,
          'value':  $.fields.employeeInfo.row2.newCompany.value},   
          'oldCompanyCode': $.fields.employeeInfo.row2.oldCompany.value,   
          'oldCompanyName': $.fields.employeeInfo.row2.oldCompany.label,   
          'oldCompanySlect':{'label':$.fields.employeeInfo.row2.oldCompany.label,
          'value':  $.fields.employeeInfo.row2.oldCompany.value},   
          'reactiveEndDate': $.fields.employeeInfo.row3.expiredDate,   
          'accountExpriedCode': $.fields.employeeInfo.row2.accountExpired  }  ] 
          ))];                [$map($distinct($lst), function($value,
          $idx){$merge([$value, {'idx': $string($idx), 'isDuplicate': true,
          'RequestTypeCode': $.fields.RequestTypeCode , 'CreateAccountType':
          $.fields._tabActive = 'manual'? 'Manual' : 'FromHRList',
          'reactiveEndDateView': $DateFormat($value.reactiveEndDate,
          'DD/MM/YYYY'), 'accountExpired': $not($.extend.formType = 'create') ?
          $.extend.defaultValue.details.accountExpriedCode, reActiveNote:
          $value.reactiveNote }])})]         )
      columns:
        - code: email
          title: Email
          align: start
          data_type:
            key: String
            collection: data_types
          display_type:
            key: Label
            collection: field_types
        - code: fullName
          title: Full Name
          align: start
          data_type:
            key: String
            collection: data_types
          display_type:
            key: Label
            collection: field_types
        - code: newDisplayName
          title: New Display Name
          align: start
          data_type:
            key: String
            collection: data_types
          display_type:
            key: Label
            collection: field_types
        - code: mailboxTypeName
          title: Mailbox Type
          align: start
          data_type:
            key: String
            collection: data_types
          display_type:
            key: Label
            collection: field_types
        - code: newCompanyName
          title: New Company
          align: start
          data_type:
            key: String
            collection: data_types
          display_type:
            key: Label
            collection: field_types
        - code: oldCompanyName
          title: Old Company
          align: start
          data_type:
            key: String
            collection: data_types
          display_type:
            key: Label
            collection: field_types
        - code: accountExpired
          title: Account Expired
          align: start
          data_type:
            key: String
            collection: data_types
          display_type:
            key: Label
            collection: field_types
        - code: reactiveEndDate
          title: End Date
          _condition:
            transform: $not($.extend.formType = 'view')
          align: start
          data_type:
            key: String
            collection: data_types
          display_type:
            key: Control DatePicker
            collection: field_types
            _disabled:
              transform: >-
                $test($getIdx($.fields.details, $.extend.index).accountExpired =
                'Never')
            validators:
              - args:
                  transform: >-
                    ($DateDiff($DateFormat($.value, 'yyyy-MM-DD'),
                    $DateFormat($now(), 'yyyy-MM-DD'), 'd') < 0)
                text: End Date must be after today's date.
                type: ppx-custom
        - code: reactiveEndDateView
          title: End Date
          align: start
          _condition:
            transform: $.extend.formType = 'view'
          data_type:
            key: String
            collection: data_types
          display_type:
            key: Label
            collection: field_types
        - code: addMailGroup
          title: Mail Group
          _condition:
            transform: $.extend.formType = 'view'
          align: start
          data_type:
            key: String
            collection: data_types
          display_type:
            key: Label
            collection: field_types
        - code: addMailGroup
          title: Mail Group
          _condition:
            transform: $not($.extend.formType = 'view')
          align: start
          data_type:
            key: String
            collection: data_types
          display_type:
            key: Control Text
            collection: field_types
        - code: reActiveNote
          title: Note
          align: start
          _condition:
            transform: ($.extend.formType = 'view')
          data_type:
            key: String
            collection: data_types
          display_type:
            key: Label
            collection: field_types
        - code: reActiveNote
          title: Note
          align: start
          _condition:
            transform: $not($.extend.formType = 'view')
          data_type:
            key: String
            collection: data_types
          display_type:
            key: Control Text
            collection: field_types
      form_config:
        fields:
          - type: text
            name: email
            label: Email
          - type: text
            name: fullName
            label: Full Name
            disabled: true
          - type: text
            name: newDisplayName
            label: Display Name
          - type: select
            name: mailboxTypeCode
            label: Mailbox Type
            _select:
              transform: $mailBoxTypesList()
          - type: text
            name: mailboxTypeName
            label: Data Zone
            _value:
              transform: $.fields.mailboxTypeSlect.label
            unvisible: true
          - type: select
            name: newCompanySlect
            isLazyLoad: true
            label: New Company
            _select:
              transform: $companiesList($.extend.limit, $.extend.page, $.extend.search)
          - type: text
            name: newCompanyName
            label: Data Zone
            _value:
              transform: $.fields.newCompanySlect.label
            unvisible: true
          - type: select
            name: oldCompanySlect
            label: Old Company
            isLazyLoad: true
            _select:
              transform: $companiesList($.extend.limit, $.extend.page, $.extend.search)
          - type: text
            name: oldCompanyName
            label: Data Zone
            _value:
              transform: $.fields.oldCompanySlect.label
            unvisible: true
          - name: reactiveEndDatePicker
            type: dateRange
            label: End Date
            mode: date-picker
            settings:
              format: dd/MM/yyyy
              mode: date
            validators:
              - type: required
          - name: reactiveEndDateView
            type: text
            label: Inactive Date
            unvisible: true
            _value:
              transform: $DateFormat($.fields.reactiveEndDatePicker, 'DD/MM/yyyy')
          - name: reactiveEndDate
            type: text
            label: Inactive Date
            unvisible: true
            _value:
              transform: $DateToTimestamp($.fields.reactiveEndDatePicker)
          - name: mailGroup
            type: textarea
            label: Mail Group
            validators:
              - type: required
            textarea:
              autoSize:
                minRows: 3
                maxRows: 5
          - name: note
            type: text
            label: Note
            validators:
              - type: maxLength
                args: 1000
                text: Note should not exceed 1000 characters
            textarea:
              autoSize:
                minRows: 3
                maxRows: 5
              maxCharCount: 1000
        sources:
          companiesList:
            uri: '"/api/companies"'
            queryTransform: >-
              {'limit': $.limit, 'page': $.page, 'filter':
              [{'field':'search','operator':
              '$eq','value':$.search},{'field':'status','operator':
              '$eq','value':true}]}
            method: GET
            bodyTransform: ''
            headerTransform: ''
            resultTransform: >-
              $map($.data, function($item) {{'label': $item.longName.default & '
              - ' & $item.code, 'value': $item.code, 'id': $item.id }})[]
            disabledCache: true
            params:
              - limit
              - page
              - search
        variables: {}
    - type: table
      mode: table
      name: details
      isDuplicateKeyName: true
      rowIdName: idx
      mapRowName:
        - idx
        - company
        - isDuplicate
        - companySlect
        - keepEndDate
        - keepEndDatePicker
      dependantField: ' $.fields.RequestTypeCode'
      _row_actions:
        transform: '[{''id'':''delete'',''type'':''ghost-gray'',''icon'':''icon-trash-bold''}]'
      _condition:
        transform: ' ($.fields.RequestTypeCode = ''keep acc'')'
      insertDataConfig:
        checkDuplicate:
          by:
            - employeeId
          validateMessage: There are some duplicate user, please check again.
        transformDataExpression: >-
          [                    {                        'employeeId':
          $.formValue.employeeInfo.employeeId.employeeId,                       
          'email':
          $.formValue.employeeInfo.employeeId.email,                       
          'keepEndDate':
          $.formValue.employeeInfo.keepEndDate,                       
          'keepReason': $.formValue.employeeInfo.reason,                       
          'keepNote': $.formValue.employeeInfo.note,                       
          'companyName':
          $.formValue.employeeInfo.employeeId.companyName,                       
          'companyCode':
          $.formValue.employeeInfo.employeeId.company,                       
          'CreateAccountType': 'FromHRList',                        'from': 'HR
          List'                    }                  ]
        formConfig:
          sources:
            personalFullInfoHaveEmailList:
              uri: '"/api/personals"'
              method: GET
              queryTransform: >-
                {'limit': $.limit, 'page': $.page, 'filter':[
                {'field':'search','operator': '$eq','value':$.search}, {'field':
                'email', 'operator': '$ne', 'value': 'NULL'}]}
              bodyTransform: ''
              headerTransform: ''
              resultTransform: >-
                $map($.data, function($item) {{'label':  $item.employeeId & ' -
                ' & $item.employeeRecordNumber & ' - ' & $item.name & ' - '  &
                $item.company, 'value': $item}})[]
              disabledCache: true
              params:
                - limit
                - page
                - search
          fields:
            - type: group
              name: employeeInfo
              _condition:
                transform: ($.extend.formType = 'create' or $.extend.formType = 'edit')
              n_cols: 4
              actionsCustomStyle: 'width: 100%'
              fields:
                - name: employeeId
                  type: select
                  isLazyLoad: true
                  _select:
                    transform: >-
                      $personalFullInfoHaveEmailList($.extend.limit,
                      $.extend.page, $.extend.search )
                  label: Employee
                  placeholder: Select Employee
                  validators:
                    - type: required
                  outputValue: value
                - name: endDate
                  type: dateRange
                  label: End Date
                  mode: date-picker
                  settings:
                    format: dd/MM/yyyy
                    mode: date
                  validators:
                    - type: required
                - name: reason
                  type: text
                  label: Reason
                  placeholder: Enter Reason
                  validators:
                    - type: required
                - name: note
                  type: text
                  label: Note
                  placeholder: Enter Note
                  textarea:
                    autosize:
                      minRows: 3
                    maxCharCount: 1000
                  validators:
                    - type: maxLength
                      args: 1000
                      text: Note should not exceed 1000 characters
      arrayOptions:
        cloneAction: false
        enabledField: locationBasedAccess
        cloneValues:
          default: false
        isCheckboxTable: true
      layout_option:
        tool_table:
          show_table_checkbox: true
      _dataSource:
        transform: >-
          ( $lst :=[        ( $isNilorEmpty($.fields.employeeInfo) ? $.value[] :
          ($append(     $.value[],     [  
          $not($isNilorEmpty($.fields.employeeInfo.employeeId))    ?{    
          'employeeId':$.fields.employeeInfo.employeeId.employeeId,    'email':
          $.fields.employeeInfo.employeeId.email,       'keepEndDate':
          $.fields.employeeInfo.endDate ,   'companyName':
          $.fields.employeeInfo.employeeId.companyView, 'keepReason':
          $.fields.employeeInfo.reason,       'keepNote':
          $.fields.employeeInfo.note,       'companyCode': 
          $.fields.employeeInfo.employeeId.company }   ]  )))];    
          [$map($distinct($lst), function($value, $idx){$merge([$value, {'idx':
          $string($idx), 'isDuplicate': true, 'companySlect': {'value':
          $value.company, 'label':$value.companyName}, 'keepEndDateView':
          $DateFormat($value.keepEndDate,'DD/MM/yyyy'), 'RequestTypeCode':
          $.fields.RequestTypeCode , 'CreateAccountType': $.fields._tabActive =
          'manual'? 'Manual' : 'FromHRList'}])})]         )
      columns:
        - code: employeeId
          title: Employee ID
          align: start
          width: '12.5'
          data_type:
            key: String
            collection: data_types
          display_type:
            key: Hyperlink
            collection: field_types
          hyperlink:
            hyperlinkTitle: Components permissions details
            fields: []
        - code: email
          title: Email
          align: start
          data_type:
            key: String
            collection: data_types
          display_type:
            key: Label
            collection: field_types
        - code: companyName
          title: Company
          align: start
          data_type:
            key: String
            collection: data_types
          display_type:
            key: Label
            collection: field_types
        - code: keepEndDate
          title: End Date
          _condition:
            transform: $not($.extend.formType = 'view')
          align: start
          data_type:
            key: String
            collection: data_types
          display_type:
            key: Control DatePicker
            collection: field_types
            validators:
              - args:
                  transform: >-
                    ($DateDiff($DateFormat($.value, 'yyyy-MM-DD'),
                    $DateFormat($now(), 'yyyy-MM-DD'), 'd') < 0)
                text: End Date must be after today's date.
                type: ppx-custom
        - code: keepEndDateView
          title: End Date
          align: start
          _condition:
            transform: ($.extend.formType = 'view')
          data_type:
            key: String
            collection: data_types
          display_type:
            key: Label
            collection: field_types
        - code: keepReason
          title: Reason
          align: start
          data_type:
            key: String
            collection: data_types
          display_type:
            key: Label
            collection: field_types
        - code: keepNote
          title: Note
          align: start
          _condition:
            transform: ($.extend.formType = 'view')
          data_type:
            key: String
            collection: data_types
          display_type:
            key: Label
            collection: field_types
        - code: keepNote
          title: Note
          _condition:
            transform: $not($.extend.formType = 'view')
          align: start
          data_type:
            key: String
            collection: data_types
          display_type:
            key: Control Text
            collection: field_types
      form_config:
        fields:
          - type: text
            name: email
            label: Email
            disabled: true
          - type: select
            isLazyLoad: true
            name: companySlect
            label: Company
            _select:
              transform: $companiesList($.extend.limit, $.extend.page, $.extend.search)
            unvisible: true
          - type: text
            name: companyName
            label: Company
            _value:
              transform: $.fields.companySlect.label
            disabled: true
          - type: text
            name: company
            unvisible: true
            _value:
              transform: $.fields.companySlect.value
          - name: keepEndDatePicker
            type: dateRange
            label: End Date
            mode: date-picker
            settings:
              format: dd/MM/yyyy
              mode: date
            validators:
              - type: required
          - name: keepEndDateView
            type: text
            label: Inactive Date
            unvisible: true
            _value:
              transform: $DateFormat($.fields.keepEndDatePicker, 'DD/MM/yyyy')
          - name: keepEndDate
            type: text
            label: Inactive Date
            unvisible: true
            _value:
              transform: $DateToTimestamp($.fields.keepEndDatePicker)
          - name: keepReason
            type: textarea
            label: Reason
            validators:
              - type: required
            textarea:
              autoSize:
                minRows: 3
                maxRows: 5
          - name: keepNote
            type: text
            label: Note
            textarea:
              autoSize:
                minRows: 3
                maxRows: 5
              maxCharCount: 1000
            validators:
              - type: maxLength
                args: 1000
                text: Note should not exceed 1000 characters
        sources:
          companiesList:
            uri: '"/api/companies"'
            queryTransform: >-
              {'limit': $.limit, 'page': $.page, 'filter':
              [{'field':'search','operator':
              '$eq','value':$.search},{'field':'status','operator':
              '$eq','value':true}]}
            method: GET
            bodyTransform: ''
            headerTransform: ''
            resultTransform: >-
              $map($.data, function($item) {{'label': $item.longName.default & '
              - ' & $item.code, 'value': $item.code, 'id': $item.id }})[]
            disabledCache: true
            params:
              - limit
              - page
              - search
        variables: {}
  footer:
    create: true
    update: true
    createdOn: createdAt
    updatedOn: updatedAt
    createdBy: createdBy
    updatedBy: updatedBy
  historyTitle: $.approvalStatusDetail
  historyDescription: $.briefData
  sources:
    companiesList:
      uri: '"/api/companies"'
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'filter':
        [{'field':'search','operator':
        '$eq','value':$.search},{'field':'status','operator':
        '$eq','value':true}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' - ' &
        $item.code, 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    requestersList:
      uri: '"/api/admin-users/infos"'
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}, {'field':
        'employeeId', 'operator': '$ne', 'value': 'NULL'}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $distinct($map($.data, function($item) {{'label':
        $join($filter([$item.employeeName,$item.email], $boolean), ' - '),
        'value': {'empId': $item.account, 'name':  $item.employeeName, 'id':
        $item.id }, 'id': $item.id }})[])[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    requestersForLoginList:
      uri: '"/api/admin-users/infos"'
      queryTransform: >-
        {'limit': 1000, 'filter': [{'field':'status','operator':
        '$eq','value':true}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $distinct($map($.data, function($item) {{'value': {'label':
        $join($filter([$item.employeeName,$item.email], $boolean), ' - '),
        'value': {'empId': $item.account, 'name':  $item.employeeName, 'id':
        $item.id }, 'id': $item.id}, 'email': $item.email }})[])[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    approversList:
      uri: '"/api/admin-users/infos"'
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'filter':
        [{'field':'status','operator': '$eq','value':true},
        {'field':'search','operator': '$eq','value':$.search} ]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $distinct($map($.data, function($item) {{'label':
        $join($filter([$item.employeeName,$item.email], $boolean), ' - '),
        'value': $item.account, 'id': $item.id }})[])[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    personalFullInfoList:
      uri: '"/api/personals"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'sort': [{'field':'employeeId',
        'order':'desc'}, {'field':'employeeRecordNumber', 'order':'desc'}],
        'filter':[ {'field':'search','operator': '$eq','value':$.search},
        {'field': 'email', 'operator': '$eq', 'value': 'NULL'}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label':  $item.employeeId & ' - ' &
        $item.employeeRecordNumber & ' - ' & $item.name & ' - '  &
        $item.company, 'value': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    personalFullInfoForeignList:
      uri: '"/api/personals/all-employees-job-datas-dropdownlist"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'sort': [{'field':'employeeId',
        'order':'desc'}, {'field':'employeeRecordNumber', 'order':'desc'}],
        'filter':[ {'field':'search','operator': '$eq','value':$.search},
        {'field': 'email', 'operator': '$eq', 'value': 'NULL'}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label':  $item.employeeId & ' - ' &
        $item.employeeRecordNumber & ' - ' & $item.name & ' - '  &
        $item.company, 'value': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    personalFullInfoHaveEmailList:
      uri: '"/api/personals"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'filter':[
        {'field':'search','operator': '$eq','value':$.search}, {'field':
        'email', 'operator': '$ne', 'value': 'NULL'}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label':  $item.employeeId & ' - ' &
        $item.employeeRecordNumber & ' - ' & $item.name & ' - '  &
        $item.company, 'value': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    jobDataEffectiveDate:
      uri: '"/api/personals/"  & $.employeeId &  "/job-datas"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $.data[0].effectiveDate
      disabledCache: true
      params:
        - employeeId
    validatorEmailList:
      uri: '"/api/admin-user-management"'
      method: GET
      queryTransform: >-
        {'limit': 1000, 'page': $.page, 'filter':[ {'field':'search','operator':
        '$eq','value':$.search}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'email':  $item.email, 'fullName':
        $item.employeeName, 'status': $item.status}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    validatorManageUserList:
      uri: '"/api/admin-user-management"'
      method: GET
      queryTransform: >-
        {'limit': 1000, 'page': $.page, 'filter':[ {'field':'search','operator':
        '$eq','value':$.search}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'email':  $item.email, 'fullName':
        $item.employeeName, 'status': $item.status}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    personalInfo:
      uri: '"/api/personals/personal-info"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $
      disabledCache: true
    mailBoxTypesList:
      uri: '"/api/picklists/MAILBOXTYPE/values"'
      queryTransform: >-
        {'filter': [{'field':'status','operator':
        '$eq','value':true},{'field':'group','operator': '$eq','value':
        $.groupId }, {'field':'effectiveDate','operator': '$lte','value':
        $.effectiveDate}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - effectiveDate
        - groupId
    locationsList:
      uri: '"/api/picklists/LOCATIONGROUP/values"'
      queryTransform: >-
        {'filter': [{'field':'status','operator':
        '$eq','value':true},{'field':'group','operator': '$eq','value':
        $.groupId }, {'field':'effectiveDate','operator': '$lte','value':
        $.effectiveDate}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - effectiveDate
        - groupId
  variables:
    _requestersList:
      transform: $requestersList()
    _validatorEmailList:
      transform: $validatorEmailList()
    _personalInfo:
      transform: $personalInfo()
    _requesterForEdit:
      transform: >-
        $filter($.variables._requestersList, function($item) {$item.value.empId
        = $.extend.defaultValue.requesterCode})
filter_config:
  fields:
    - labelType: type-grid
      name: requestId
      label: Request Id
      type: selectAll
      placeholder: Select Request Id
      outputValue: value
      _options:
        transform: $requestIdList()
    - type: text
      name: requestSubject
      label: Request Subject
      labelType: type-grid
      placeholder: Enter Request Subject
    - type: selectAll
      name: location
      label: Location Group
      labelType: type-grid
      placeholder: Select Location Group
      _options:
        transform: $locationsList()
    - labelType: type-grid
      name: requester
      label: Requester
      type: selectAll
      placeholder: Select Requester
      outputValue: value
      _options:
        transform: $requestersList()
    - labelType: type-grid
      name: requestTypeCode
      type: selectAll
      direction: column
      collapse: false
      label: Request Type
      placeholder: Select Request Type
      _options:
        transform: >-
          [{'label':'Create account for staff','value':'create
          acc'},{'label':'Create account for foreign staff','value':'create acc
          foreign staff'},{'label':'Create account with special
          displayname','value':'create acc special'},{'label':'Disable account
          email','value':'disabl eacc'},{'label':'Reactive account
          email','value':'reactive acc'},{'label':'Keep account','value':'keep
          acc'}]
      outputValue: value
    - labelType: type-grid
      type: selectAll
      name: mailBoxType
      label: Mail Box Type
      placeholder: Select Mail Box Type
      _options:
        transform: $mailBoxTypesList()
      outputValue: value
    - labelType: type-grid
      name: approvalStatus
      type: selectAll
      _options:
        transform: >-
          [{'label':'Add new','value':'CREATED'},{'label':'Transfer
          UService','value':'DELIVERED'},{'label':'Rejected','value':'REJECTED'},{'label':'Approved','value':'APPROVED'}]
      label: Approval Status
      placeholder: Select Approval Status
      outputValue: value
    - type: dateRange
      label: Last Updated On
      labelType: type-grid
      name: updatedAt
    - labelType: type-grid
      name: updatedBy
      label: Last Updated By
      type: text
      placeholder: Enter Editor
  filterMapping:
    - field: requestId
      operator: $in
      valueField: requestId
    - field: requestSubject
      operator: $cont
      valueField: requestSubject
    - field: locationFilter
      operator: $in
      valueField: location.(value)
    - field: requester
      operator: $in
      valueField: requester
    - field: mailBoxType
      operator: $in
      valueField: mailBoxType
    - field: requestTypeCode
      operator: $in
      valueField: requestTypeCode
    - field: approvalStatus
      operator: $in
      valueField: approvalStatus
    - field: createdBy
      operator: $in
      valueField: createdBy.(value)
    - field: createdAt
      operator: $between
      valueField: createdAt
    - field: updatedBy
      operator: $cont
      valueField: updatedBy
    - field: updatedAt
      operator: $between
      valueField: updatedAt.(value)
  sources:
    locationsList:
      uri: '"/api/picklists/LOCATIONGROUP/values"'
      queryTransform: >-
        {'filter': [{'field':'status','operator':
        '$eq','value':true},{'field':'group','operator': '$eq','value':
        $.groupId }, {'field':'effectiveDate','operator': '$lte','value':
        $.effectiveDate}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - effectiveDate
        - groupId
    requestersList:
      uri: '"/api/admin-user-management"'
      queryTransform: '{''limit'': 1000}'
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label':
        $join($filter([$item.fullName,$item.email], $boolean), ' - '), 'value':
        $item.code }})[]
      disabledCache: true
    requestIdList:
      uri: '"/api/account-mail-requests"'
      queryTransform: '{''limit'': 1000}'
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.requestId, 'value':
        $item.requestId }})[]
      disabledCache: true
    createdByList:
      uri: '"/api/account-mail-requests"'
      method: GET
      queryTransform: '{''limit'': $.limit, ''page'': $.page, ''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.createdBy, 'value':
        $item.createdBy}})[]
      disabledCache: true
    updatedByList:
      uri: '"/api/account-mail-requests"'
      method: GET
      queryTransform: '{''limit'': 10000, ''page'': $.page, ''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $distinct($map($.data, function($item) {{'label': $item.updatedBy,
        'value': $item.updatedBy}})[])[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    mailBoxTypesList:
      uri: '"/api/picklists/MAILBOXTYPE/values"'
      queryTransform: >-
        {'filter': [{'field':'status','operator':
        '$eq','value':true},{'field':'group','operator': '$eq','value':
        $.groupId }, {'field':'effectiveDate','operator': '$lte','value':
        $.effectiveDate}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - effectiveDate
        - groupId
layout_options:
  show_history_insert_button: false
  custom_history_backend_url: /api/account-mail-requests/:requestId/histories
  custom_create_api:
    _url:
      transform: >-
        $.RequestTypeCode = 'create acc' or $.RequestTypeCode = 'create acc
        foreign staff' or $.RequestTypeCode = 'create acc special' ?
        '/api/account-mail-requests' : $.RequestTypeCode = 'disable acc' ?
        '/api/account-mail-requests/disable-accounts' : $.RequestTypeCode =
        'reactive acc' ? '/api/account-mail-requests/reactive-accounts' :
        $.RequestTypeCode = 'keep acc' ?
        '/api/account-mail-requests/keep-accounts'
  custom_update_api:
    _url:
      transform: >-
        $.RequestTypeCode = 'create acc' or $.RequestTypeCode = 'create acc
        foreign staff' or $.RequestTypeCode = 'create acc special' ?
        '/api/account-mail-requests' : $.RequestTypeCode = 'disable acc' ?
        '/api/account-mail-requests/disable-accounts' : $.RequestTypeCode =
        'reactive acc' ? '/api/account-mail-requests/reactive-accounts' :
        $.RequestTypeCode = 'keep acc' ?
        '/api/account-mail-requests/keep-accounts'
  custom_submit_api:
    url: /api/account-mail-requests/submit
  is_upload_file: true
  is_layout_widget: true
  history_widget_header_options:
    duplicate: false
    _edit: $.isEdit
    _delete: $.isDelete
  show_dialog_submit_button: true
  tool_table:
    - id: export
      icon: icon-download-simple
  hide_action_row: true
  delete_multi_items: true
  custom_delete_body: >-
    {'ids': $map($.data, function($item){$item.id})[],'requestIds': $map($.data,
    function($item){$item.requestId})[]}
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: null
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    icon: icon-pencil-simple
    type: ghost-gray
    condition_func: '$.approvalStatusCode = ''CREATED'' or $.approvalStatusCode = ''REJECTED'' '
  - id: delete
    icon: icon-trash
    type: ghost-gray
    condition_func: '$.approvalStatusCode = ''CREATED'' '
backend_url: /api/account-mail-requests
screen_name: null
layout_options__actions_many: null
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key: null
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Request Account Email FPT For Individual
  parent:
    title: Function list
