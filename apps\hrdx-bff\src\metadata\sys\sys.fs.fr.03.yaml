id: SYS.FS.FR.03
status: draft
sort: 86
user_created: 56e71b91-dd39-4ebc-afd1-b4f00f38d4a5
date_created: '2024-07-01T02:36:25.672Z'
user_updated: 43878dbb-aa6c-4ef4-8bd7-0ea811ffafa7
date_updated: '2025-06-23T03:25:39.944Z'
title: Manage Role
requirement:
  time: 1748420512065
  blocks:
    - id: TNqKVb5C0O
      type: paragraph
      data:
        text: '1'
  version: 2.30.7
screen_design: null
module: SYS
local_fields:
  - code: code
    pinned: true
    title: Code
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    show_sort: true
    options__tabular__column_width: null
  - code: name
    title: Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: companyName
    title: Company
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: isAdminReportView
    title: Admin
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: Tag
      collection: field_types
    show_sort: true
  - code: description
    title: Description
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: status
    title: Status
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: Boolean Tag
      collection: field_types
    show_sort: true
  - code: updatedAt
    title: Last Updated On
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
    show_sort: true
    options__tabular__column_width: 12
  - code: updatedBy
    title: Last Updated By
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
mock_data:
  - code: ADMIN.FPT
    name: ADMIN.FPT
    company: FPT IS
    description: Data will be displayed here with a max of 2 lines
    status: Active
    createdOn: '2024-05-06 10:25:01'
    createdBy: Ducnm54
    lastUpdatedOn: '2024-05-06 18:59:11'
    lastUpdatedBy: Ducnm54
    table:
      - componentsGroup: HR profile
        locationBasedAccess: FPT IS_DXG_HCM Zone
        permissionWith: Structure
        criteria: 3 criteria
      - componentsGroup: Payroll Group
        locationBasedAccess: FPT IS_DXG_HCM Zone
        permissionWith: Structure
        criteria: 3 criteria
      - componentsGroup: Insurance profile
        locationBasedAccess: FPT IS_DXG_HCM Zone
        permissionWith: Structure
        criteria: 3 criteria
    componentsGroup:
      - label: Payroll Group
        value: Payroll Group
      - label: Insurance profile
        value: Insurance profile
    locationBasedAccess:
      - label: FPT IS_DXG_HCM Zone
        value: FPT IS_DXG_HCM Zone
  - code: HR_ES
    name: HR_Block ES
    company: FPT IS
    description: Data will be displayed here with a max of 2 lines
    status: Active
    createdOn: '2024-05-06 10:25:01'
    createdBy: Ducnm54
    lastUpdatedOn: '2024-05-06 18:59:11'
    lastUpdatedBy: Ducnm54
    table:
      - componentsGroup: HR profile
        locationBasedAccess: FPT IS_DXG_HCM Zone
        permissionWith: Structure
        criteria: 3 criteria
      - componentsGroup: Payroll Group
        locationBasedAccess: FPT IS_DXG_HCM Zone
        permissionWith: Structure
        criteria: 3 criteria
      - componentsGroup: Insurance profile
        locationBasedAccess: FPT IS_DXG_HCM Zone
        permissionWith: Structure
        criteria: 3 criteria
    componentsGroup:
      - label: Payroll Group
        value: Payroll Group
      - label: Insurance profile
        value: Insurance profile
    locationBasedAccess:
      - label: FPT IS_DXG_HCM Zone
        value: FPT IS_DXG_HCM Zone
  - code: PAYROLL_BANK
    name: PAYROLL_BANK
    company: FPT IS
    description: Data will be displayed here with a max of 2 lines
    status: Active
    createdOn: '2024-05-06 10:25:01'
    createdBy: Ducnm54
    lastUpdatedOn: '2024-05-06 18:59:11'
    lastUpdatedBy: Ducnm54
    table:
      - componentsGroup: HR profile
        locationBasedAccess: FPT IS_DXG_HCM Zone
        permissionWith: Structure
        criteria: 3 criteria
      - componentsGroup: Payroll Group
        locationBasedAccess: FPT IS_DXG_HCM Zone
        permissionWith: Structure
        criteria: 3 criteria
      - componentsGroup: Insurance profile
        locationBasedAccess: FPT IS_DXG_HCM Zone
        permissionWith: Structure
        criteria: 3 criteria
    componentsGroup:
      - label: Payroll Group
        value: Payroll Group
      - label: Insurance profile
        value: Insurance profile
    locationBasedAccess:
      - label: FPT IS_DXG_HCM Zone
        value: FPT IS_DXG_HCM Zone
  - code: CONTRACT_ESHN_BANK
    name: CONTRACT_ESHN_BANK
    company: FPT IS
    description: Data will be displayed here with a max of 2 lines
    status: Active
    createdOn: '2024-05-06 10:25:01'
    createdBy: Ducnm54
    lastUpdatedOn: '2024-05-06 18:59:11'
    lastUpdatedBy: Ducnm54
    table:
      - componentsGroup: HR profile
        locationBasedAccess: FPT IS_DXG_HCM Zone
        permissionWith: Structure
        criteria: 3 criteria
      - componentsGroup: Payroll Group
        locationBasedAccess: FPT IS_DXG_HCM Zone
        permissionWith: Structure
        criteria: 3 criteria
      - componentsGroup: Insurance profile
        locationBasedAccess: FPT IS_DXG_HCM Zone
        permissionWith: Structure
        criteria: 3 criteria
    componentsGroup:
      - label: Payroll Group
        value: Payroll Group
      - label: Insurance profile
        value: Insurance profile
    locationBasedAccess:
      - label: FPT IS_DXG_HCM Zone
        value: FPT IS_DXG_HCM Zone
local_buttons: null
layout: layout-table
form_config:
  formSize:
    create: large
    edit: large
    view: large
    proceed: large
  confirmOnSubmit:
    edit:
      value: compareValue
      transform: >-
        ($.prevValue.status != $.currentValue.status) and ($.prevValue.status =
        true and $.currentValue.status = false) ? {'title': 'Change status',
        'content': 'The inactive security information group will be
        automatically removed from assigned users and user groups. Are you sure
        you want to change?'}
  formTitle:
    create: Add New Role
    view: Role Details
    edit: Edit Role
    proceed: Edit Role
    duplicate: Add New Role
  fields:
    - type: group
      n_cols: 2
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - type: text
          name: isUsed
          label: Used
          unvisible: true
        - type: text
          name: code
          label: Code
          placeholder: Input Code
          _disabled:
            transform: $not($.extend.formType = 'create') and $.fields.isUsed = true
          validators:
            - type: required
        - type: translation
          name: name
          label: Name
          placeholder: Input Name
          _disabled:
            transform: $not($.extend.formType = 'create') and $.fields.isUsed = true
          validators:
            - type: required
        - type: select
          label: Company
          isLazyLoad: true
          name: companyCode
          placeholder: Select Company
          _disabled:
            transform: $not($.extend.formType = 'create') and $.fields.isUsed = true
          _select:
            transform: $companiesList($.extend.limit, $.extend.page, $.extend.search)
          confirmPopup:
            title: Change Company
            content: >-
              The role permissions below will be reset. Do you want to continue
              with the change?
            listFieldsDependantName:
              - table
        - type: radio
          name: status
          label: Status
          value: true
          radio:
            - value: true
              label: Active
            - value: false
              label: Inactive
          validators:
            - type: required
    - type: group
      n_cols: 1
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - type: text
          name: code
          label: Code
        - type: text
          name: name
          label: Name
          _value:
            transform: $.fields.name.default
        - type: text
          label: Company
          name: companyName
          _value:
            transform: $.extend.defaultValue.companyCode.label
        - type: radio
          name: status
          label: Status
          radio:
            - value: true
              label: Active
            - value: false
              label: Inactive
    - type: checkbox
      label: Admin
      name: isAdminReport
      prefixLabel: false
      hiddenLabel: true
      customWidthDescription: true
      _condition:
        transform: $not($.extend.formType = 'view')
      description: |-
        • Full permission data on View Schedule function
        • Full permission data on Scheduled Job Manager function
    - type: checkbox
      label: Admin
      name: isAdminReport
      customWidthDescription: true
      _condition:
        transform: $.extend.formType = 'view'
      description: |-
        • Full permission data on View Schedule function
        • Full permission data on Scheduled Job Manager function
    - type: translationTextArea
      label: Description
      name: description
      placeholder: Input Description
      textarea:
        autoSize:
          minRows: 3
        maxCharCount: 1000
      validators:
        - type: maxLength
          args: 1000
    - type: group
      label: Assign Permissions To Role
      collapse: false
      _condition:
        transform: $not($.extend.formType = 'view')
      border_top: '1px solid #e0e0e0'
      fieldGroupContentStyle:
        padding: 0px
      fields:
        - type: group
          n_cols: 2
          border: '1px solid #d9d9d9'
          padding: 16px
          borderRadius: 8px
          actions:
            - name: renderBtn
              typeLeftIcon: plus
              isLeftIcon: true
              label: Insert data
              isReset: true
              type: secondary
          actionsCustomStyle: 'width: 100%'
          validators:
            - type: ppx-custom
              args:
                transform: $tempFunc($.fields.table[], $.value)
              text: >-
                The data is duplicated. Please select a different function group
                and data zone.
          fields:
            - type: selectAll
              label: Function Group
              name: _functionGroupPermissonIds
              placeholder: Select Function Group
              dependantField: $.fields.companyCode.value, $.fields.table
              dependantFieldSkip: 2
              mode: multiple
              isLazyLoad: true
              outputValue: value
              validators:
                - type: required
              _options:
                transform: >-
                  $adminfunctiongrouppermissionsShortList($.extend.limit,
                  $.extend.page, $.extend.search, $.fields.companyCode.value)
              settings:
                maxTag: 1
            - type: selectAll
              label: Data-Zone
              name: _dataAreaIds
              isLazyLoad: true
              placeholder: Select Data-Zone
              dependantField: $.fields.companyCode.value, $.fields.table
              dependantFieldSkip: 2
              mode: multiple
              outputValue: value
              validators:
                - type: required
              _options:
                transform: >-
                  $admindataareasShortList($.extend.limit, $.extend.page,
                  $.extend.search, $.fields.companyCode.value)
              settings:
                maxTag: 1
        - type: table
          name: table
          dependantField: $.fields.companyCode.value
          layout_option:
            tool_table:
              show_table_checkbox: true
              show_table_filter: false
              show_table_group: false
              hidden_header: false
              collapse: false
            show_pagination: false
            hide_action_row: true
          columns:
            - code: functionGroupPermissionName
              title: Function Group
              align: start
              data_type:
                key: String
                collection: data_types
              display_type:
                key: Hyperlink
                collection: field_types
              hyperlink:
                hyperlinkTitle: Components permissions details
                fields: []
              actionRow:
                action: ''
                baseUrl: >-
                  /SYS/SYS.FS.FR.12_01?dialogType=view&id={{functionGroupPermissionId}}
              options__tabular__column_width: 11.25
            - code: dataAreaName
              title: Data Zone
              align: start
              data_type:
                key: String
                collection: data_types
              display_type:
                key: Label
                collection: field_types
              options__tabular__column_width: 11.25
            - code: permissionWith
              title: Permission Based On
              align: start
              data_type:
                key: String
                collection: data_types
              display_type:
                key: Label
                collection: field_types
              options__tabular__column_width: 11.25
            - code: criteria
              title: Criteria
              align: start
              data_type:
                key: String
                collection: data_types
              display_type:
                key: Tooltip
                collection: field_types
              options__tabular__column_width: 11.25
              actionRow:
                action: ''
                baseUrl: /SYS/SYS.FS.FR.13_01?dialogType=view&id={{dataAreaId}}
              extra_config:
                _type:
                  transform: '$type($.value) = ''string'' ? ''Hyperlink'' : ''Tooltip'''
                _value:
                  transform: >-
                    $.value.Employee ? $count($split($.value.Employee, ';')) & '
                    Employee(s)' : $.value
          _defaultDataSource:
            transform: $.variables._handle_result_roleData
          _dataSource:
            transform: $.variables._handle_final_roleData
    - type: group
      label: Role permissions
      collapse: false
      _condition:
        transform: $.extend.formType = 'view'
      fieldGroupContentStyle:
        padding: 0px
      fields:
        - type: table
          name: table
          _dataSourceRequestStatus: _handle_finalView_roleData
          dependantField: $.fields.companyCode.value
          layout_option:
            tool_table:
              show_table_checkbox: true
              show_table_filter: false
              show_table_group: false
              hidden_header: false
              collapse: false
            show_pagination: false
            hide_action_row: true
          columns:
            - code: functionGroupPermissionName
              title: Function Group
              align: start
              data_type:
                key: String
                collection: data_types
              display_type:
                key: Hyperlink
                collection: field_types
              hyperlink:
                hyperlinkTitle: Components permissions details
                fields: []
              actionRow:
                action: ''
                baseUrl: >-
                  /SYS/SYS.FS.FR.12_01?dialogType=view&id={{functionGroupPermissionId}}
              options__tabular__column_width: 11.25
            - code: dataAreaName
              title: Data Zone
              align: start
              data_type:
                key: String
                collection: data_types
              display_type:
                key: Label
                collection: field_types
              options__tabular__column_width: 11.25
            - code: permissionWith
              title: Permission Based On
              align: start
              data_type:
                key: String
                collection: data_types
              display_type:
                key: Label
                collection: field_types
              options__tabular__column_width: 11.25
            - code: criteria
              title: Criteria
              align: start
              data_type:
                key: String
                collection: data_types
              display_type:
                key: Tooltip
                collection: field_types
              options__tabular__column_width: 11.25
              actionRow:
                action: ''
                baseUrl: /SYS/SYS.FS.FR.13_01?dialogType=view&id={{dataAreaId}}
              extra_config:
                _type:
                  transform: '$type($.value) = ''string'' ? ''Hyperlink'' : ''Tooltip'''
                _value:
                  transform: >-
                    $.value.Employee ? $count($split($.value.Employee, ';')) & '
                    Employee(s)' : $.value
          _dataSource:
            transform: $.variables._handle_finalView_roleData
  footer:
    create: true
    update: true
    createdOn: createdAt
    updatedOn: updatedAt
    createdBy: createdBy
    updatedBy: updatedBy
  sources:
    companiesList:
      uri: '"/api/companies/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'search': $.search,'filter':
        [{'field':'status', 'operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    adminfunctiongrouppermissionsList:
      uri: '"/api/admin-function-group-permissions/infos"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'status','operator': '$eq','value':true},
        {'field':'companyCode','operator': '$eq','value': $.companyCode}],
        'sort': [{'field':'name', 'order':'ascend'} ]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.id}})[]
      disabledCache: true
      params:
        - companyCode
    adminfunctiongrouppermissionsShortList:
      uri: '"/api/admin-function-group-permissions/short-list"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true},
        {'field':'companyCode','operator': '$eq','value': $.companyCode}],
        'sort': [{'field':'name', 'order':'ascend'} ]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.id}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - companyCode
    admindataareasList:
      uri: '"/api/admin-data-areas/infos"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'status','operator': '$eq','value':true}
        ,{'field':'companyCode','operator': '$eq','value': $.companyCode}],
        'sort': [{'field':'name', 'order':'ascend'} ]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $
      disabledCache: true
      params:
        - companyCode
    admindataareasShortList:
      uri: '"/api/admin-data-areas/short-list"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}
        ,{'field':'companyCode','operator': '$eq','value': $.companyCode}],
        'sort': [{'field':'name', 'order':'ascend'} ]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.id, 'permissionsWithLabel':
        $item.permissionWith, 'criteria': $item.includesSubordinates = true ?
        [{'Include in direct staff': 'Yes'}] : $reduce($item.dataAreaDetails,
        function($acc, $item) { $merge([ $acc, { $item.dataAreaParamName:
        $lookup($acc, $item.dataAreaParamName) ? $lookup($acc,
        $item.dataAreaParamName) & '; ' & ($item.paramValueDisplay ?
        $item.paramValueDisplay : $item.dataAreaParamValue) :
        ($item.paramValueDisplay ? $item.paramValueDisplay :
        $item.dataAreaParamValue) } ]) }, {})  }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - companyCode
  variables:
    _adminfunctiongrouppermissionsList:
      transform: $adminfunctiongrouppermissionsList($.fields.companyCode.value)
    _admindataareasList:
      transform: $admindataareasList($.fields.companyCode.value)
    _selectedFunctionGroups:
      transform: >-
        $distinct($filter($.variables._adminfunctiongrouppermissionsList,
        function ($v, $i, $a){
        $count($filter($.fields._functionGroupPermissonIds, function($v1, $i1,
        $a1){$v1 = $v.value })) > 0 })[])
    _selectedDataAreas:
      transform: >-
        $distinct($filter($.variables._admindataareasList, function ($v, $i,
        $a){ $count($filter($.fields._dataAreaIds, function($v1, $i1, $a1){$v1 =
        $v.id })) > 0 })[])
    _handle_result_roleData:
      transform: >-
        $map($.extend.defaultValue.roleDetails, function($item){    {   
        'functionGroupPermissionId': $item.functionGroupPermissionId,   
        'functionGroupPermissionName': $item.functionGroupPermission.name,   
        'dataAreaId': $item.dataAreaId,    'dataAreaName':
        $item.dataArea.name,    'permissionWith':
        $item.dataArea.permissionWith,    'criteria':
        $item.dataArea.isIncludesSubordinates = 'Y' ?     [{ 'Include in direct
        staff': 'Yes' }] :    $map(   
        $distinct($item.dataArea.dataAreaDetails.dataAreaParamName),   
        function($paramName) {    {    ($paramName): $join(    $map(   
        $filter($item.dataArea.dataAreaDetails, function($e) {   
        $e.dataAreaParamName = $paramName    }),    function($e) {   
        $e.paramValueDisplay ? $e.paramValueDisplay : $e.dataAreaParamValue   
        }    ),    '; '    )    }    }    ),    'countEmp':
        $count($i.dataArea.dataAreaDetails)    }    })[]
    _handle_rew_roleData:
      transform: >-
        $count($.variables._selectedFunctionGroups) > 0 and
        $count($.variables._selectedDataAreas)? $reduce(   
        $map($.variables._selectedFunctionGroups, function($f) {     
        $map($.variables._selectedDataAreas, function($d) {        {         
        'functionGroupPermissionId': $f.value,         
        'functionGroupPermissionName': $f.label,          'dataAreaId':
        $d.id,          'dataAreaName': $d.name.default,         
        'permissionWith': $d.permissionWith,          'criteria':
        $d.includesSubordinates = true            ? [{ 'Include in direct
        staff': 'Yes' }]            : $map(               
        $distinct($d.dataAreaDetails.dataAreaParamName),               
        function($paramName) {                  {                   
        ($paramName): $join(                     
        $filter(                        $map(                         
        $filter($d.dataAreaDetails, function($e) {                           
        $e.dataAreaParamName = $paramName                         
        }),                          function($e) {                           
        $string($e.paramValueDisplay ? $e.paramValueDisplay :
        $e.dataAreaParamValue)                          }                       
        ),                        function($v) {                         
        $exists($v) and $trim($v) != ''                       
        }                      ),                      '; '                   
        )                  }                }              ),         
        'countEmp': $count($d.dataAreaDetails)        }      })    })[],   
        $append  ): []
    _handle_final_roleData:
      transform: >-
        (    $composeKey := function($item) {   
        $string($item.functionGroupPermissionId) & '|' &
        $string($item.dataAreaId)    };    $all := $append($.fields.table,
        $.variables._handle_rew_roleData);    $reduce($all, function($acc,
        $item) {    $count(        $filter($acc, function($x) { $composeKey($x)
        = $composeKey($item) })    ) > 0 ? $acc : $append($acc, [$item])    },
        []))
    _handle_finalView_roleData:
      transform: $.variables._handle_result_roleData
filter_config:
  fields:
    - name: code
      label: Code
      type: text
      labelType: type-grid
      placeholder: Enter code
    - name: name
      label: Name
      labelType: type-grid
      type: text
      placeholder: Enter name
    - name: company
      label: Company
      type: selectAll
      labelType: type-grid
      mode: multiple
      outputValue: value
      isLazyLoad: true
      placeholder: Select company
      _options:
        transform: $companiesList($.extend.limit, $.extend.page, $.extend.search, $now())
    - type: radio
      name: isAdminReport
      labelType: type-grid
      label: Admin
      value: ''
      radio:
        - label: All
          value: ''
        - label: 'Yes'
          value: true
        - label: 'No'
          value: false
    - type: textarea
      label: Description
      labelType: type-grid
      name: description
      placeholder: Enter Description
      textarea:
        autoSize:
          minRows: 3
        maxCharCount: 1000
      validators:
        - type: maxLength
          args: 1000
    - type: radio
      name: status
      labelType: type-grid
      label: Status
      value: ''
      radio:
        - label: All
          value: ''
        - label: Active
          value: true
        - label: Inactive
          value: false
    - name: lastUpdatedOn
      label: Last Updated On
      type: dateRange
      labelType: type-grid
      placeholder: DD/MM/YYYY
      setting:
        mode: date
        format: dd/MM/yyyy
    - name: lastUpdatedBy
      label: Last Updated By
      labelType: type-grid
      type: text
      placeholder: Enter Editor
  filterMapping:
    - field: code
      operator: $cont
      valueField: code
    - field: name
      operator: $cont
      valueField: name
    - field: description
      operator: $cont
      valueField: description
    - field: companyCode
      operator: $in
      valueField: company
    - field: status
      operator: $eq
      valueField: status
    - field: isAdminReport
      operator: $eq
      valueField: isAdminReport
    - field: createdAt
      operator: $between
      valueField: createdOn
    - field: createdBy
      operator: $cont
      valueField: createdBy
    - field: updatedAt
      operator: $between
      valueField: lastUpdatedOn
    - field: updatedBy
      operator: $cont
      valueField: lastUpdatedBy
    - field: note
      operator: $cont
      valueField: description
  sources:
    adminrolesList:
      uri: '"/api/admin-roles"'
      queryTransform: ''
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: '$map($.data, function($item) {{''label'': $item.code, ''id'': $item.id}})[]'
      disabledCache: true
    companiesList:
      uri: '"/api/companies/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'search': $.search,'filter':
        [{'field':'status', 'operator': '$eq','value':true},
        {'field':'effectiveDate','operator': '$eq','value': $.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
layout_options:
  duplicate_value_transform:
    fields:
      - code
    transform: ''''''
  tool_table:
    - id: import
      icon: icon-upload-simple
      href: /GE/HR.FS.FR.092
    - id: export
      icon: icon-download-simple
  actions_many:
    - id: delete
      type: tertiary
      title: Delete
      icon: string
      backendUrl: string
  show_detail_history: false
  is_new_dynamic_form: true
  hide_action_row: true
  delete_multi_items: true
  custom_delete_body: $map($.data, function($item){$item.id})[]
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: {}
layout_options__footer_buttons:
  - id: cancel
    title: Cancel
    type: secondary
layout_options__row_actions:
  - id: edit
    title: Edit
    icon: icon-pencil-simple-bold
    type: ghost-gray
  - id: duplicate
    title: Duplicate
    icon: icon-copy-bold
    group: null
    type: ghost-gray
  - id: delete
    title: Delete
    icon: icon-trash-bold
    group: null
    type: ghost-gray
backend_url: api/admin-roles
screen_name: null
layout_options__actions_many: null
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: companyCode
    defaultName: CompanyCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Manage Role
  parent:
    title: Function list
