id: SYS.FS.FR.04_01
status: draft
sort: 90
user_created: 43878dbb-aa6c-4ef4-8bd7-0ea811ffafa7
date_created: '2024-07-15T08:49:55.553Z'
user_updated: 43878dbb-aa6c-4ef4-8bd7-0ea811ffafa7
date_updated: '2025-07-08T08:21:19.661Z'
title: Manage  User Group
requirement:
  time: 1749182924138
  blocks:
    - id: 5bur1SHRM7
      type: paragraph
      data:
        text: '1'
    - id: v-WeX7pt8o
      type: paragraph
      data:
        text: '1'
  version: 2.30.7
screen_design: null
module: SYS
local_fields:
  - code: code
    title: Code
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    description: |-
      - Display column name: User group code
      - Display the list of user groups according to permissions:
        + Login account is Admin, all user groups on the system are displayed.
        + Login account is Sub admin, both user groups created by Admin and Sub admin for that company and shared groups (not assigned company) are displayed.
    pinned: true
    show_sort: true
    options__tabular__column_width: null
  - code: name
    title: Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    description: >-
      - Display column name: User group name

      - Display the corresponding user group name according to the user group
      code.
    options__tabular__column_width: null
    extra_config: null
    show_sort: true
  - code: companyName
    title: Company
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: |-
      - Display column name: Company
      - Display the corresponding company according to the user group code.
    options__tabular__column_width: 15
    show_sort: true
  - code: status
    title: Status
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Boolean Tag
      collection: field_types
    description: |-
      - Display column name: Status
      - Display the corresponding status according to the user group code.
    options__tabular__column_width: 10
    show_sort: true
  - code: description
    title: Description
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    description: |-
      - Display column name: Description
      - Display the corresponding description according to the user group code.
    options__tabular__column_width: 15
    show_sort: true
  - code: roleDisplayTooltip
    title: Role
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    description: |-
      - Display column name: Role
      - Display the corresponding role according to the user group code.
    options__tabular__column_width: 10
    show_sort: false
  - code: lastUpdatedOn
    title: Last Updated On
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
    description: >-
      - Display column name: Edited time

      - Display full information in the format dd/MM/yyyy hh:mm:ss of the last
      data update (Example: 06/05/2024 10:20:53).
    options__tabular__column_width: 12.5
    show_sort: true
  - code: lastUpdatedBy
    title: Last Updated By
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: |-
      - Display column name: Editor
      - Display the account name of the last data updater (Example: NguyenPN).
    options__tabular__column_width: null
    show_sort: true
mock_data:
  - code: EmpGr
    name: Employee Group
    company: FPT IS
    systemAdmin: false
    description: ''
    status: Active
    nation: Vietnam
    secureData: Confidential payroll data group
    roleData:
      - label: Nhóm thông tin 1
        value: Nhóm thông tin 1
      - label: Nhóm bảo mật tài nguyên
        value: Nhóm bảo mật tài nguyên
    role:
      - label: IT_FIS
        value: IT_FIS
      - label: ADMIN_FIS
        value: ADMIN_FIS
    createdOn: '2024-05-20 12:00:00'
    createdBy: HoangPV
    lastUpdatedOn: '2024-05-21 12:00:00'
    lastUpdatedBy: Ducnm54
    securityGroup:
      - Security
      - Personnel
  - code: ADMIN.FPT IS
    name: System Administration - FPT IS
    company: FPT IS
    systemAdmin: true
    description: ''
    status: Active
    nation: Vietnam
    secureData: ''
    roleData:
      - label: Nhóm thông tin 2
        value: Nhóm thông tin 2
    role:
      - label: IT_FIS
        value: IT_FIS
    createdOn: '2024-05-20 12:00:00'
    createdBy: HoangPV
    lastUpdatedOn: '2024-05-21 12:00:00'
    lastUpdatedBy: Ducnm54
    securityGroup:
      - Security
      - Personnel
  - code: ADMIN.FSOFT
    name: System Administration - FSOFT
    company: FSOFT
    systemAdmin: true
    description: ''
    status: Active
    nation: Vietnam
    secureData: ''
    roleData:
      - label: Nhóm thông tin 3
        value: Nhóm thông tin 3
    role:
      - label: ADMIN_FIS
        value: ADMIN_FIS
    createdOn: '2024-05-20 12:00:00'
    createdBy: HoangPV
    lastUpdatedOn: '2024-05-21 12:00:00'
    lastUpdatedBy: Ducnm54
  - code: ADMIN.FTEL
    name: System Administration - FTEL
    company: FTEL
    systemAdmin: true
    description: ''
    status: Active
    nation: Vietnam
    secureData: ''
    roleData:
      - label: Nhóm thông tin 4
        value: Nhóm thông tin 4
    role:
      - label: IT_FIS
        value: IT_FIS
    createdOn: '2024-05-20 12:00:00'
    createdBy: HoangPV
    lastUpdatedOn: '2024-05-21 12:00:00'
    lastUpdatedBy: Ducnm54
local_buttons: null
layout: layout-table
form_config:
  formSize:
    create: large
    edit: large
    view: middle
    proceed: large
  confirmOnSubmit:
    edit:
      value: compareValue
      transform: >-
        ($.prevValue.status != $.currentValue.status) and ($.prevValue.status =
        true and $.currentValue.status = false) ? {'title': 'Save Changes',
        'content': 'If this status is changed to Active, it will affect on users
        who are being attached with this User Group'}
  formTitle:
    create: Add New User Group
    duplicate: Add New User Group
    edit: Edit User Group
    view: User Group Details
  fields:
    - type: group
      n_cols: 2
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - type: text
          name: code
          label: Code
          placeholder: Enter code
          _disabled:
            transform: >-
              $not($.extend.formType = 'create') and
              $.extend.defaultValue.isUsing = true
          validators:
            - type: required
        - type: translation
          name: name
          label: Name
          placeholder: Enter name
          _disabled:
            transform: >-
              $not($.extend.formType = 'create') and
              $.extend.defaultValue.isUsing = true
          validators:
            - type: required
        - type: select
          label: Company
          name: companySelect
          isLazyLoad: true
          _disabled:
            transform: >-
              $not($.extend.formType = 'create') and
              $.extend.defaultValue.isUsing = true
          placeholder: Select Company
          validators:
            - type: ppx-custom
              args:
                transform: $.variables._checkGroupList
          _select:
            transform: $companiesList($.extend.limit, $.extend.page, $.extend.search)
          confirmPopup:
            title: Change Company
            content: >-
              The role permissions below will be reset. Do you want to continue
              with the change?
            listFieldsDependantName:
              - roleIds
        - type: text
          name: company
          _value:
            transform: >-
              $.fields.companySelect.value ? $.fields.companySelect.value :
              'noData'
          unvisible: true
        - type: radio
          name: status
          label: Status
          value: true
          radio:
            - value: true
              label: Active
            - value: false
              label: Inactive
          validators:
            - type: required
    - type: group
      n_cols: 1
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - type: text
          name: code
          label: Code
        - type: translation
          name: name
          label: Name
        - type: text
          label: Company
          name: companyName
        - type: text
          label: Administration System of Member Companies
          _condition:
            transform: 'false'
          name: systemAdmin
          unvisible: true
        - type: radio
          name: status
          label: Status
          radio:
            - value: true
              label: Active
            - value: false
              label: Inactive
        - type: translationTextArea
          label: Description
          name: description
          textarea:
            autoSize:
              minRows: 3
    - type: checkbox
      label: Administration System of Member Companies
      name: systemAdmin
      _condition:
        transform: 'false'
      hiddenLabel: true
    - type: group
      label: ' '
      readOnly: true
      _condition:
        transform: $.variables._checkGroupList
      fields:
        - type: text
          name: checkGroupListDisplay
          _condition:
            transform: $.variables._checkGroupList
          readOnly: true
          color: red
          value: >-
            The selected company already has a user group created for system
            administration. Please choose a different company!
        - type: text
          name: checkGroupList
          unvisible: true
          validators:
            - type: ppx-custom
              args:
                transform: $.variables._checkGroupList
              text: >-
                The selected company already has a user group created for system
                administration. Please choose a different company!
    - type: translationTextArea
      label: Description
      name: description
      placeholder: Enter Description
      textarea:
        autoSize:
          minRows: 3
      _condition:
        transform: $not($.extend.formType = 'view')
      validators:
        - type: maxLength
          args: 1000
    - type: group
      label: Role Permission
      collapse: false
      disableEventCollapse: false
      dependantField: $.fields.company
      border_top: '1px solid #e0e0e0'
      fields:
        - type: selectAll
          label: Role
          name: roleIds
          placeholder: Select Role
          mode: multiple
          outputValue: value
          _options:
            transform: $.variables._roleList
          _unvisible:
            transform: $.extend.formType = 'view'
        - type: table
          name: table
          _size:
            transform: >-
              $count($count($.fields.roleIds) > 0 ?
              $.variables._listDataRoleDetails : [])
          _condition:
            transform: $boolean($.fields.roleIds)
          layout_option:
            tool_table:
              show_table_checkbox: false
              show_table_filter: false
              show_table_group: false
              hidden_header: false
              collapse: false
            show_pagination: false
            hide_action_row: true
          columns:
            - code: roleName
              title: Role
              align: start
              data_type:
                key: String
                collection: data_types
              display_type:
                key: Label
                collection: field_types
            - code: functionGroupPermissionName
              title: Function Group
              align: start
              data_type:
                key: String
                collection: data_types
              display_type:
                key: Hyperlink
                collection: field_types
              actionRow:
                action: navigate
                baseUrl: >-
                  /SYS/SYS.FS.FR.12_01?dialogType=view&id={{componentsGroupValue}}
            - code: dataAreaName
              title: Data Zone
              align: start
              data_type:
                key: String
                collection: data_types
              display_type:
                key: Label
                collection: field_types
            - code: permissionWith
              title: Permission Based On
              align: start
              data_type:
                key: String
                collection: data_types
              display_type:
                key: Label
                collection: field_types
            - code: criteria
              title: Criteria
              align: start
              data_type:
                key: String
                collection: data_types
              display_type:
                key: Tooltip
                collection: field_types
              actionRow:
                action: ''
                baseUrl: /SYS/SYS.FS.FR.13_01?dialogType=view&id={{dataAreaId}}
              extra_config:
                _type:
                  transform: '$type($.value) = ''string'' ? ''Hyperlink'' : ''Tooltip'''
                _value:
                  transform: >-
                    $.value.Employee ? $count($split($.value.Employee, ';')) & '
                    employee(s)' : $.value
          _dataSource:
            transform: >-
              $count($.fields.roleIds) > 0 ? $.variables._listDataRoleDetails :
              []
  footer:
    create: true
    update: true
    createdOn: createdOn
    updatedOn: lastUpdatedOn
    createdBy: createdBy
    updatedBy: lastUpdatedBy
  sources:
    companiesList:
      uri: '"/api/companies/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'search': $.search,'filter':
        [{'field':'status', 'operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    roleList:
      uri: '"/api/admin-roles/dropdown"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'companyCode','operator':
        '$eq','value':$.companycode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.id }})[]
      disabledCache: true
      params:
        - companycode
    adminrolesInfoList:
      uri: '"/api/admin-roles-infos"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'ids','operator': '$eq','value':
        $join($map($.roleIds, $string), ',') }]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        [  $reduce(    $map($, function($item) {      $map($item.roleDetails,
        function($roleDetail) {        {          'roleName':
        $item.name.default,          'dataAreaName':
        $roleDetail.dataArea.name,          'dataAreaId':
        $roleDetail.dataArea.id,          'functionGroupPermissionName':
        $roleDetail.functionGroupPermission.name.default,         
        'componentsGroupValue': $roleDetail.functionGroupPermission.id,         
        'permissionWith':            $roleDetail.dataArea.ruleCode = 'CN' ?
        'Individual' :            $roleDetail.dataArea.ruleCode = 'CTTT' ?
        'Direct superior' :            $roleDetail.dataArea.ruleCode = 'CTGT' ?
        'Indirect superior' :            $roleDetail.dataArea.ruleCode = 'ORG' ?
        'Structure' :            'Employee list', 'criteria':
        $roleDetail.criteria        }      })    }),    $append  )]
      disabledCache: true
      params:
        - roleIds
    groupList:
      uri: '"/api/groups-sys"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'company','operator':
        '$eq','value':$.companycode},{'field':'status','operator':
        '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'id': $item.id, 'systemAdmin':
        $item.systemAdmin }})[]
      disabledCache: true
      params:
        - companycode
  variables:
    _roleList:
      transform: >-
        $.fields.companySelect.value ? $roleList($.fields.companySelect.value) :
        $roleList()
    _groupList:
      transform: '$.fields.companySelect ? $groupList($.fields.companySelect.value) : []'
    _checkGroupList:
      transform: >-
        ($groupAdminObj := $filter($.variables._groupList, function($v)
        {$v.systemAdmin = true })[0]; $check := $exists($groupAdminObj) and
        $not($groupAdminObj.id = $.extend.defaultValue.id) and
        $boolean($.fields.systemAdmin) and $not($.extend.formType = 'view'); 
        $check )
    _listDataRoleDetails:
      transform: $adminrolesInfoList($.fields.roleIds)
  form_value_transform: >-
    $merge([$,{'company': $.companySelect.value ? $.companySelect.value : null
    }])
filter_config:
  fields:
    - name: code
      label: Code
      type: text
      labelType: type-grid
      placeholder: Enter Code
    - name: name
      label: Name
      type: text
      labelType: type-grid
      placeholder: Enter Name
    - name: company
      label: Company
      type: selectAll
      labelType: type-grid
      mode: multiple
      placeholder: Select Company
      _options:
        transform: $companiesList()
    - name: description
      label: Description
      type: text
      labelType: type-grid
      placeholder: Enter Description
    - name: status
      label: Status
      type: radio
      value: ''
      labelType: type-grid
      radio:
        - label: All
          value: ''
        - label: Active
          value: true
        - label: Inactive
          value: false
    - name: role
      label: Role
      type: selectAll
      labelType: type-grid
      placeholder: Select Role
      _options:
        transform: '$.fields.company ? $roleListDropdown($.fields.company) : $roleList()'
    - name: createdOn
      label: Created On
      type: dateRange
      labelType: type-grid
      placeholder: dd/MM/yyyy
      setting:
        mode: date
        format: dd/MM/yyyy HH:mm:ss
    - name: lastUpdatedBy
      label: Last Updated By
      type: text
      labelType: type-grid
      placeholder: Enter Editor
  filterMapping:
    - field: code
      operator: $cont
      valueField: code
    - field: name
      operator: $cont
      valueField: name
    - field: description
      operator: $cont
      valueField: description
    - field: company
      operator: $eq
      valueField: company.(value)
    - field: roleId
      operator: $eq
      valueField: role.(value)
    - field: systemAdmin
      operator: $eq
      valueField: memberCompanyAdministration
    - field: status
      operator: $eq
      valueField: status
    - field: createdOn
      operator: $between
      valueField: createdOn
    - field: createdBy
      operator: $cont
      valueField: createdBy
    - field: updatedOn
      operator: $between
      valueField: lastUpdatedOn
    - field: updatedBy
      operator: $cont
      valueField: lastUpdatedBy
  sources:
    nationsList:
      uri: '"/api/picklists/COUNTRIES/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
    companiesList:
      uri: '"/api/companies/by"'
      queryTransform: ''
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id}})[]
      disabledCache: true
    roleList:
      uri: '"/api/admin-roles"'
      method: GET
      queryTransform: >-
        {'limit':99999, 'filter': [{'field':'status','operator':
        '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.code, 'value': $item.id
        }})[]
      disabledCache: true
    roleListDropdown:
      uri: '"/api/admin-roles/dropdown"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'companyCode','operator': '$eq','value':
        $.companycode }]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.name.default, 'value': $item.id
        }})[]
      disabledCache: true
      params:
        - companycode
    secureList:
      uri: '"/api/data-security-groups/infos"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'national','operator': '$eq','value':
        $.nationalId}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.id }})[]
      disabledCache: true
      params:
        - nationalId
    groupCodeList:
      uri: '"/api/groups-sys"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code }})[]
      disabledCache: true
layout_options:
  duplicate_value_transform:
    fields:
      - code
    transform: ''''''
  tool_table:
    - id: import
      icon: icon-download-simple
      href: /GE/HR.FS.FR.092
    - id: export
      icon: icon-upload-simple
  show_detail_history: false
  is_new_dynamic_form: true
  hide_action_row: true
  delete_multi_items: true
  custom_delete_body: $map($.data, function($item){$item.id})[]
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    icon: pencil
  - id: duplicate
    icon: icon-copy-bold
  - id: delete
    icon: trash
backend_url: /api/groups-sys
screen_name: null
layout_options__actions_many: null
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: company
    defaultName: CompanyCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Manage  User Group
  parent:
    title: Function list
