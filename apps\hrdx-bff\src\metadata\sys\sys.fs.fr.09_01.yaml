id: SYS.FS.FR.09_01
status: draft
sort: 92
user_created: 43878dbb-aa6c-4ef4-8bd7-0ea811ffafa7
date_created: '2024-07-17T07:19:15.875Z'
user_updated: 43878dbb-aa6c-4ef4-8bd7-0ea811ffafa7
date_updated: '2025-06-26T03:11:15.453Z'
title: Manage User
requirement:
  time: *************
  blocks:
    - id: JZl6MQ5Bd0
      type: paragraph
      data:
        text: Quản lý người dùng
  version: 2.30.7
screen_design: null
module: SYS
local_fields:
  - code: account
    title: Account
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    description: >-
      - Display column name: Account

      - For Admin login account: display the entire list of accounts on the
      system.

      - For Sub admin login account: display the list of accounts according to
      the company of the Sub admin.
    pinned: true
    extra_config:
      rowspan: '2'
    options__tabular__column_width: 11.25
    show_sort: true
  - code: employeeCode
    title: Employee ID
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: |-
      - Display column name: Employee Code
      - Display the employee code corresponding to the account.
    extra_config:
      rowspan: '2'
    options__tabular__column_width: 11.25
    show_sort: true
  - code: recordId
    title: Record ID
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: |-
      - Display column name: Work Record ID
      - Display the work record ID of the employee.
    extra_config:
      rowspan: '2'
    options__tabular__column_width: 11.25
    show_sort: true
  - code: employeeName
    title: Employee Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: |-
      - Display column name: Employee Name
      - Display the employee name corresponding to the employee code.
    extra_config:
      rowspan: '2'
    options__tabular__column_width: 13.75
    show_sort: true
  - code: email
    title: Email
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: |-
      - Display column name: Email
      - Display the synchronized Email.
    pinned: false
    extra_config:
      rowspan: '2'
    options__tabular__column_width: 13.75
    show_sort: true
  - code: companyName
    title: Company
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: |-
      - Display column name: Company
      - Display the company corresponding to the employee code.
    extra_config:
      rowspan: '2'
    options__tabular__column_width: 12.5
    show_sort: true
  - code: legalEntityName
    title: Legal Entity
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: |-
      - Display column name: Legal Entity
      - Display the legal entity corresponding to the employee code.
    extra_config:
      rowspan: '2'
    options__tabular__column_width: 11.25
    show_sort: true
  - code: businessUnitName
    title: Business Unit
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: |-
      - Display column name: Business Unit
      - Display the business unit corresponding to the employee code.
    extra_config:
      rowspan: '2'
    options__tabular__column_width: 11.25
    show_sort: true
  - code: divisionName
    title: Division
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: |-
      - Display column name: Division
      - Display the division corresponding to the employee code.
    extra_config:
      rowspan: '2'
    options__tabular__column_width: 15
    show_sort: true
  - code: departmentName
    title: Department
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: |-
      - Display column name: Department
      - Display the department corresponding to the employee code.
    extra_config:
      rowspan: '2'
    options__tabular__column_width: 11.25
    show_sort: true
  - code: userGroupName
    title: User Group
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: |-
      - Display column name: User Group
      - Display the user group assigned to the user.
    extra_config:
      rowspan: '2'
    options__tabular__column_width: 11.25
    show_sort: true
  - code: role
    title: Default Role
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    description: >-
      - Display column name: Default Role

      - Display the role assigned to the user including both default and
      supplementary roles.
    extra_config:
      rowspan: '2'
    options__tabular__column_width: 11.25
  - code: additionalRole
    title: Additional Role
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    extra_config:
      rowspan: '2'
    options__tabular__column_width: 11.25
  - code: status
    title: Status
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Boolean Tag
      collection: field_types
    description: >-
      - Display column name: Status

      - Display the account status.

      - If the account is active and the effective date is less than or equal to
      the current date, the account can log into the system.

      - If the account is inactive or active but the effective date is greater
      than the current date, the account cannot log into the system.
    extra_config:
      rowspan: '2'
    options__tabular__column_width: 11.25
    show_sort: true
  - code: lockAccount
    title: Lock Account
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: Lock Label
      collection: field_types
    description: |-
      - Display column name: Lock Account
      - Display a lock icon if the account is locked, otherwise leave it blank.
      - If the account is locked, it cannot log into the system.
    extra_config:
      rowspan: '2'
    options__tabular__column_width: 11.25
    show_sort: true
  - code: effectiveDate
    title: Effective Start Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    description: |-
      - Display column name: Effective Start Date
      - Display the account's effective start date.
    extra_config:
      rowspan: '2'
    options__tabular__column_width: 12.5
    show_sort: true
  - code: effectiveEndDate
    title: Effective End Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    description: |-
      - Display column name: Effective End Date
      - Display the account's effective end date.
    pinned: false
    extra_config:
      rowspan: '2'
    options__tabular__column_width: 12.5
    show_sort: true
  - code: renewalStartDate
    title: Renewal Start Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    description: |-
      - Display column name: Renewal Start Date
      - Display the account's renewal start date.
    extra_config:
      rowspan: '2'
    options__tabular__column_width: 12.5
    show_sort: true
  - code: renewalEndDate
    title: Renewal End Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    description: |-
      - Display column name: Renewal End Date
      - Display the account's renewal end date.
    extra_config:
      rowspan: '2'
    options__tabular__column_width: 12.5
    show_sort: true
  - code: accessTime
    title: Set Access Time
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    extra_config:
      rowspan: 1
      colspan: 4
    options__tabular__align: center
    options__tabular__column_width: 50
  - code: accessStartDate
    title: Access Start Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    description: |-
      - Display column name: Access Start Date from
      - Display the access start date corresponding to the account.
    pinned: false
    extra_config:
      rowspan: '1'
      isGrouped: true
    options__tabular__column_width: 12.5
    options__tabular__align: left
  - code: accessEndDate
    title: Access End Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    description: |-
      - Display column name: Access End Date
      - Display the access end date corresponding to the account.
    extra_config:
      rowspan: '1'
      isGrouped: true
    options__tabular__column_width: 12.5
    options__tabular__align: left
  - code: accessStartHourfrom
    title: Login Time
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: HH:mm:ss
      collection: field_types
    description: |-
      - Display column name: Login Time
      - Display access start hour corresponding to the account.
    extra_config:
      rowspan: '1'
      isGrouped: true
    options__tabular__align: left
    options__tabular__column_width: 12.5
  - code: accessEndHourfrom
    title: Logout Time
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: HH:mm:ss
      collection: field_types
    description: |-
      - Display column name: Logout Time
      - Display access end hour corresponding to the account.
    extra_config:
      rowspan: '1'
      isGrouped: true
    options__tabular__align: left
    options__tabular__column_width: 12.5
  - code: updatedAt
    title: Last Updated On
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
    description: >-
      - Display column name: Edited time

      - Display full information in the format dd/MM/yyyy hh:mm:ss of the last
      data update (Example: 06/05/2024 10:20:53).
    pinned: false
    extra_config:
      rowspan: '2'
    options__tabular__column_width: null
    show_sort: true
    group: null
  - code: updatedBy
    title: Last Updated By
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: |-
      - Display column name: Editor
      - Display the account name of the last data updater (Example: NguyenPN).
    pinned: false
    extra_config:
      rowspan: '2'
    options__tabular__column_width: null
    show_sort: true
mock_data:
  - account: ThaoNTT34
    employeeCode: E000014626
    recordId: '1'
    employeeName: Nguyen Thi Thu Thao
    email: <EMAIL>
    company: FPT IS
    legalEntity: FPT IS HCM
    businessUnit: ES
    division: BP22
    department: BP22
    userGroup: Nhóm HR,ES
    defaultRole: HR, ES
    additionalRole: PR
    securityInfoGroup: ''
    status: Active
    lockAccount: false
    effectiveStartDate: '2024-04-01'
    effectiveEndDate: ''
    renewalStartDate: ''
    renewalEndDate: ''
    accessStartDate: ''
    accessEndDate: ''
    accessStartHourfromStr: '2024-01-01 08:35:00'
    accessStartHourfrom:
      - 8
      - 10
      - 3
    accessEndHourfrom: ''
    createdTime: '2024-02-20 09:31:00'
    creator: ThaoNT
    editedTime: '2024-05-06 10:20:53'
    editor: ThaoNT
  - account: AnhNV11
    employeeCode: E000014654
    recordId: '1'
    employeeName: Nguyen Van Anh
    email: <EMAIL>
    company: FPT IS
    legalEntity: FPT IS HCM
    businessUnit: ES
    division: BP22
    department: BP22
    userGroup: Nhóm HR,ES
    defaultRole: HSNS, FSOFT
    additionalRole: ''
    securityInfoGroup: ''
    status: Active
    lockAccount: ''
    effectiveStartDate: '2024-04-01'
    effectiveEndDate: ''
    renewalStartDate: ''
    renewalEndDate: ''
    accessStartDate: '2024-01-01'
    accessEndDate: '2024-01-31'
    accessStartHourfrom: '2024-01-01 08:30:00'
    accessEndHourfrom: '2024-01-01 17:30:00'
    createdTime: '2024-02-20 09:31:00'
    creator: ThaoNT
    editedTime: '2024-05-06 10:20:53'
    editor: ThaoNT
  - account: TuanTM342
    employeeCode: E000014612
    recordId: '1'
    employeeName: Tran Minh Tuan
    email: <EMAIL>
    company: FPT IS
    legalEntity: FPT IS HCM
    businessUnit: ES
    division: BP22
    department: BP22
    userGroup: Nhóm HR,ES
    defaultRole: HR,ES
    additionalRole: ''
    securityInfoGroup: info 2
    status: Active
    lockAccount: ''
    effectiveStartDate: '2024-04-01'
    effectiveEndDate: ''
    renewalStartDate: ''
    renewalEndDate: ''
    accessStartDate: '2024-01-01'
    accessEndDate: '2024-01-31'
    accessStartHourfrom: '2024-01-01 08:30:00'
    accessEndHourfrom: '2024-01-01 17:30:00'
    createdTime: '2024-02-20 09:31:00'
    creator: ThaoNT
    editedTime: '2024-05-06 10:20:53'
    editor: ThaoNT
  - account: HungDL156
    employeeCode: E000014513
    recordId: '1'
    employeeName: Le Duc Hung
    email: <EMAIL>
    company: FPT IS
    legalEntity: FPT IS HCM
    businessUnit: ES
    division: BP22
    department: BP22
    userGroup: Nhóm nhân viên
    defaultRole: HR
    additionalRole: ''
    securityInfoGroup: Nhóm thông tin 2
    status: Active
    lockAccount: ''
    effectiveStartDate: '2024-04-01'
    effectiveEndDate: ''
    renewalStartDate: ''
    renewalEndDate: ''
    accessStartDate: ''
    accessEndDate: ''
    accessStartHourfrom: ''
    accessEndHourfrom: ''
    createdTime: '2024-02-20 09:31:00'
    creator: ThaoNT
    editedTime: '2024-05-06 10:20:53'
    editor: ThaoNT
  - account: HuyPQ54
    employeeCode: E000014542
    recordId: '1'
    employeeName: Phung Quang Huy
    email: <EMAIL>
    company: FPT IS
    legalEntity: FPT IS HCM
    businessUnit: ES
    division: BPBX
    department: ''
    userGroup: Nhóm nhân viên
    defaultRole: HR
    additionalRole: ''
    securityInfoGroup: ''
    status: Inactive
    lockAccount: ''
    effectiveStartDate: '2024-04-01'
    effectiveEndDate: ''
    renewalStartDate: ''
    renewalEndDate: ''
    accessStartDate: '2024-01-01'
    accessEndDate: '2024-01-31'
    accessStartHourfrom: '2024-01-01 08:30:00'
    accessEndHourfrom: '2024-01-01 23:59:59'
    createdTime: '2024-02-20 09:31:00'
    creator: ThaoNT
    editedTime: '2024-05-06 10:20:53'
    editor: ThaoNT
  - account: AnhTQ03
    employeeCode: E000003357
    recordId: '1'
    employeeName: Hoang Thi Quynh Anh
    email: <EMAIL>
    company: FPT IS
    legalEntity: FPT IS HCM
    businessUnit: ES
    division: BP01
    department: BP01
    userGroup: HR
    defaultRole: PR
    additionalRole: ''
    securityInfoGroup: ''
    status: Active
    lockAccount: ''
    effectiveStartDate: '2024-04-01'
    effectiveEndDate: '2024-01-31'
    renewalStartDate: '2024-02-01'
    renewalEndDate: '2024-02-07'
    accessStartDate: '2024-01-01'
    accessEndDate: '2024-01-31'
    accessStartHourfrom: '2024-01-01 08:30:00'
    accessEndHourfrom: '2024-01-01 23:59:59'
    createdTime: '2024-02-20 09:31:00'
    creator: ThaoNT
    editedTime: '2024-05-06 10:20:53'
    editor: ThaoNT
  - account: QuangLM
    employeeCode: E000003578
    recordId: '1'
    employeeName: Le Minh Quang
    email: <EMAIL>
    company: FPT IS
    legalEntity: FPT IS HCM
    businessUnit: ES
    division: BP02
    department: BP01
    userGroup: Nhóm nhân viên
    defaultRole: HR
    additionalRole: PR
    securityInfoGroup: ''
    status: Inactive
    lockAccount: ''
    effectiveStartDate: '2024-04-01'
    effectiveEndDate: '2026-03-31'
    renewalStartDate: ''
    renewalEndDate: ''
    accessStartDate: '2024-01-01'
    accessEndDate: '2024-01-31'
    accessStartHourfrom: '2024-01-01 08:30:00'
    accessEndHourfrom: '2024-01-01 23:59:59'
    createdTime: '2024-02-20 09:31:00'
    creator: ThaoNT
    editedTime: '2024-05-06 10:20:53'
    editor: ThaoNT
  - account: LinhNT164
    employeeCode: E000017688
    recordId: '1'
    employeeName: Le Minh Linh
    email: <EMAIL>
    company: FPT IS
    legalEntity: FPT IS HCM
    businessUnit: GS
    division: SX
    department: ''
    userGroup: Nhóm quản lý
    defaultRole: HR, PR
    additionalRole: ''
    securityInfoGroup: ''
    status: Active
    lockAccount: ''
    effectiveStartDate: '2022-02-01'
    effectiveEndDate: ''
    renewalStartDate: ''
    renewalEndDate: ''
    accessStartDate: '2024-01-01'
    accessEndDate: '2024-01-31'
    accessStartHourfrom: '2024-01-01 08:30:00'
    accessEndHourfrom: '2024-01-01 17:30:00'
    createdTime: '2024-02-20 09:31:00'
    creator: ThaoNT
    editedTime: '2024-05-06 10:20:53'
    editor: ThaoNT
  - account: NgocPT1257
    employeeCode: E000035114
    recordId: '1'
    employeeName: Pham Thi Thao Ngoc
    email: <EMAIL>
    company: FPT IS
    legalEntity: FPT IS HCM
    businessUnit: ES
    division: BP19
    department: ''
    userGroup: Nhóm quản lý
    defaultRole: HR, PR
    additionalRole: ''
    securityInfoGroup: Sercurity 1
    status: Active
    lockAccount: ''
    effectiveStartDate: '2024-01-01'
    effectiveEndDate: ''
    renewalStartDate: ''
    renewalEndDate: ''
    accessStartDate: '2024-01-01'
    accessEndDate: '2024-01-31'
    accessStartHourfrom: '2024-01-01 08:30:00'
    accessEndHourfrom: '2024-01-01 23:59:59'
    createdTime: '2024-02-20 09:31:00'
    creator: ThaoNT
    editedTime: '2024-05-06 10:20:53'
    editor: ThaoNT
local_buttons: null
layout: layout-table
form_config:
  formSize:
    create: large
    view: large
    proceed: large
    edit: small
  historyTitle: >-
    $DateFormat($.effectiveDate, 'DD/MM/YYYY') & ($boolean($.renewalEndDate) ? 
    (' - ' & $DateFormat($.renewalEndDate, 'DD/MM/YYYY')) :
    $boolean($.effectiveEndDate) ? (' - ' & $DateFormat($.effectiveEndDate,
    'DD/MM/YYYY')))
  historyHeaderTitle: '''User Details'''
  formTitle:
    edit: Set access time
  title: Set access time
  fields:
    - type: text
      name: account
      label: Account
      _condition:
        transform: $.extend.formType = 'view'
    - type: group
      label: Job data
      collapse: false
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - type: text
          name: employeeId
          label: Employee ID
        - type: text
          name: recordId
          label: Record ID
        - type: text
          name: employeeName
          label: Employee Name
        - type: text
          name: email
          label: Email
        - type: text
          name: company
          label: Company
        - type: text
          name: legalEntity
          label: Legal Entity
        - type: text
          name: businessUnit
          label: Business Unit
        - type: text
          name: division
          label: Division
        - type: text
          name: department
          label: Department
    - type: group
      label: Permission Details
      _condition:
        transform: $.extend.formType = 'view'
      collapse: false
      fields:
        - type: text
          name: userGroup
          label: User Group
        - type: text
          name: roleDefault
          label: Default Role
        - type: text
          name: role
          label: Additional Role
    - type: group
      label: Account Details
      collapse: false
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - type: radio
          name: status
          label: Status
          radio:
            - value: true
              label: Active
            - value: false
              label: Inactive
        - type: text
          name: lockAccount
          label: Lock Account
        - type: dateRange
          name: effectiveDate
          label: Effective Start Date
          mode: date-picker
          setting:
            mode: date
            format: dd/MM/yyyy
        - type: dateRange
          name: effectiveEndDate
          label: Effective End Date
          mode: date-picker
          setting:
            mode: date
            format: dd/MM/yyyy
        - type: dateRange
          name: renewalStartDate
          label: Renewal Start Date
          mode: date-picker
          setting:
            mode: date
            format: dd/MM/yyyy
        - type: dateRange
          name: renewalEndDate
          label: Renewal End Date
          mode: date-picker
          setting:
            mode: date
            format: dd/MM/yyyy
    - type: group
      label: Access Times
      collapse: false
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - type: dateRange
          name: accessStartDate
          label: Access Start Date
          mode: date-picker
          setting:
            mode: date
            format: dd/MM/yyyy
        - type: dateRange
          name: accessEndDate
          label: Access End Date
          mode: date-picker
          setting:
            mode: date
            format: dd/MM/yyyy
        - type: dateRange
          name: accessStartHourfrom
          label: Login Time
          mode: date-picker
          setting:
            mode: date
            format: HH:mm:ss
        - type: dateRange
          name: accessEndHourfrom
          label: Logout Time
          mode: date-picker
          setting:
            mode: date
            format: HH:mm:ss
    - type: text
      name: account
      disabled: true
      label: Account
      _condition:
        transform: $.extend.formType = 'edit'
    - type: group
      n_cols: 2
      fields:
        - type: dateRange
          label: Access Start Date
          name: accessStartDate
          mode: date-picker
          placeholder: DD/MM/YYYY
          setting:
            format: dd/MM/yyyy
            type: date
          _condition:
            transform: $.extend.formType = 'edit'
        - type: dateRange
          label: Access End Date
          name: accessEndDate
          mode: date-picker
          placeholder: DD/MM/YYYY
          setting:
            format: dd/MM/yyyy
            type: date
          _condition:
            transform: $.extend.formType = 'edit'
        - type: timePicker
          name: accessStartHourfrom
          placeholder: hh:mm:ss
          label: Login Time
          setting:
            format: HH:mm:ss
          _condition:
            transform: $.extend.formType = 'edit'
        - type: timePicker
          name: accessEndHourfrom
          placeholder: hh:mm:ss
          label: Logout Time
          setting:
            format: HH:mm:ss
          _condition:
            transform: $.extend.formType = 'edit'
  footer:
    create: true
    update: true
    createdOn: createdAt
    updatedOn: updatedAt
    createdBy: createdBy
    updatedBy: updatedBy
filter_config:
  fields:
    - type: selectAll
      labelType: type-grid
      mode: multiple
      label: Account
      name: userName
      placeholder: Select Account
      _options:
        transform: >-
          ($distinct($map($adminUsersList($.fields.effectiveDate) ,
          function($item) {{'label': $item.account, 'value': $item.account
          }})[]))
    - type: selectAll
      mode: multiple
      labelType: type-grid
      label: Employee ID
      name: employeeCode
      placeholder: Select Employee ID
      _options:
        transform: >-
          ($distinct($map( $filter($adminUsersList($.fields.effectiveDate),
          function($v){ $v.employeeId })[] , function($item) {{'label':
          $item.employeeId, 'value': $item.employeeId }})[]))
    - type: selectAll
      mode: multiple
      label: Employee Name
      labelType: type-grid
      placeholder: Select Employee Name
      name: employeeName
      _options:
        transform: >-
          ($distinct($map($adminUsersList($.fields.effectiveDate) ,
          function($item) {{'label': $item.employeeName, 'value':
          $item.employeeName }})[]))
    - type: selectAll
      mode: multiple
      labelType: type-grid
      label: Email
      name: email
      placeholder: Select Email
      _options:
        transform: >-
          ($distinct($map( $filter($adminUsersList($.fields.effectiveDate),
          function($v){ $v.email })[] , function($item) {{'label': $item.email,
          'value': $item.email }})[]))
    - type: selectAll
      mode: multiple
      labelType: type-grid
      label: Company
      name: companyCode
      placeholder: Select Company
      _options:
        transform: $companiesList()
    - type: selectAll
      mode: multiple
      labelType: type-grid
      label: User Group
      name: groupCode
      placeholder: Select User Group
      _options:
        transform: >-
          $map($groupsList(), function($item) {{'label': $item.name.default,
          'value': $item.code}})[]
    - type: selectAll
      labelType: type-grid
      mode: multiple
      label: Default Role
      name: defaultRoleCode
      placeholder: Select Default Role
      _options:
        transform: >-
          $map($rolesList(), function($item) {{'label': $item.name.default,
          'value': $item.code}})[]
    - type: selectAll
      labelType: type-grid
      mode: multiple
      label: Additional Role
      name: roleCode
      placeholder: Select Additional Role
      _options:
        transform: >-
          $map($rolesList(), function($item) {{'label': $item.name.default,
          'value': $item.code}})[]
    - type: radio
      label: Status
      labelType: type-grid
      name: status
      value: ''
      radio:
        - label: All
          value: ''
        - value: true
          label: Active
        - value: false
          label: Inactive
    - type: radio
      labelType: type-grid
      label: Lock Account
      name: lockAccount
      value: ''
      radio:
        - label: All
          value: ''
        - value: true
          label: Lock
        - value: false
          label: 'No'
    - type: dateRange
      labelType: type-grid
      label: Effective Date
      name: effectiveDate
      setting:
        format: dd/MM/yyyy
        type: date
    - type: dateRange
      labelType: type-grid
      label: Renewal Date
      name: renewalDate
      setting:
        format: dd/MM/yyyy
        type: date
    - type: dateRange
      labelType: type-grid
      label: Access Date
      name: accessDate
      setting:
        format: dd/MM/yyyy
        type: date
    - type: dateRange
      labelType: type-grid
      label: Last Updated On
      name: updatedAt
      setting:
        format: dd/MM/yyyy
        type: date
    - type: text
      labelType: type-grid
      placeholder: Enter Editor
      name: updatedBy
      label: Last Updated By
  filterMapping:
    - field: account
      operator: $in
      valueField: userName.(value)
    - field: employeeCode
      operator: $in
      valueField: employeeCode.(value)
    - field: employeeName
      operator: $in
      valueField: employeeName.(value)
    - field: email
      operator: $in
      valueField: email.(value)
    - field: groupCode
      operator: $in
      valueField: groupCode.(value)
    - field: companyCode
      operator: $in
      valueField: companyCode.(value)
    - field: userId
      operator: $in
      valueField: userId.(value)
    - field: defaultRoleCode
      operator: $in
      valueField: defaultRoleCode.(value)
    - field: roleCode
      operator: $in
      valueField: roleCode.(value)
    - field: status
      operator: $eq
      valueField: status
    - field: lockAccount
      operator: $eq
      valueField: lockAccount
    - field: effectiveDate
      operator: $between
      valueField: effectiveDate
    - field: renewalStartDate
      operator: $between
      valueField: renewalDate
    - field: accessStartDate
      operator: $between
      valueField: accessDate
    - field: createdAt
      operator: $between
      valueField: createdAt
    - field: createdBy
      operator: $in
      valueField: createdBy
    - field: updatedAt
      operator: $between
      valueField: updatedAt
    - field: updatedBy
      operator: $cont
      valueField: updatedBy
  sources:
    personalsList:
      uri: '"/api/personals"'
      method: GET
      queryTransform: '{''filter'': [{''field'':''status'',''operator'': ''$eq'',''value'':true}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $.data
      disabledCache: true
    companiesList:
      uri: '"/api/companies/by"'
      method: GET
      queryTransform: '{''filter'': [{''field'':''status'',''operator'': ''$eq'',''value'':true}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default, 'value':
        $item.code}})[]
      disabledCache: true
    adminUsersList:
      uri: '"/api/admin-user-management"'
      method: GET
      queryTransform: >-
        {'limit': 1400, 'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $.data
      disabledCache: true
      params:
        - effectiveDate
    groupsList:
      uri: '"/api/groups-sys"'
      method: GET
      queryTransform: '{''filter'': [{''field'':''status'',''operator'': ''$eq'',''value'':true}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $.data
      disabledCache: true
    rolesList:
      uri: '"/api/admin-roles"'
      method: GET
      queryTransform: '{''filter'': [{''field'':''status'',''operator'': ''$eq'',''value'':true}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $.data
      disabledCache: true
layout_options:
  row_actions_handler:
    lock:
      action: edit
      update_fields:
        lockAccount: true
      confirm:
        title: Lock Account
        content: >-
          The account will not be accessible after activating Account Lock. Do
          you want to lock this account?
    unlock:
      action: edit
      update_fields:
        lockAccount: false
      confirm:
        title: Unlock Account
        content: Do you want to unlock this account?
  show_detail_history: true
  show_table_checkbox: true
  show_history_insert_button: false
  history_widget_header_options:
    duplicate: false
    delete: false
    _edit: '$.isAdmin = false ? true : false'
  tool_table:
    - id: export
      icon: icon-upload-simple-bold
  mapping_one_actions:
    history: true
  hide_action_row: true
layout_options__header_buttons: null
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    title: Edit
    icon: icon-pencil-simple
    type: ghost-gray
    condition_func: $.isAdmin = false
  - id: lock
    title: Lock
    icon: icon-lock-bold
    group: null
    type: ghost-gray
    condition_func: ($.lockAccount = false or $.lockAccount = 'No') and $.isAdmin = false
  - id: unlock
    title: Unlock
    icon: icon-lock-open
    type: ghost-gray
    group: null
    condition_func: ($.lockAccount = true or $.lockAccount = 'Yes') and $.isAdmin = false
backend_url: /api/admin-user-management
screen_name: users
layout_options__actions_many:
  - id: export
    title: Export
    icon: icon-upload-simple-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: companyCode
    defaultName: CompanyCode
  - name: legalEntityCode
    defaultName: LegalEntityCode
  - name: businessUnitCode
    defaultName: BusinessUnitCode
  - name: divisionCode
    defaultName: DivisionCode
  - name: departmentCode
    defaultName: DepartmentCode
  - name: employeeId
    defaultName: EmployeeId
  - name: recordId
    defaultName: EmployeeRecordNumber
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Manage User
  parent:
    title: Function list
