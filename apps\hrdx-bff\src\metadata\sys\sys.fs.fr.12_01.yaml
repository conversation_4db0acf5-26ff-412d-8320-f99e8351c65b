id: SYS.FS.FR.12_01
status: draft
sort: 95
user_created: 43878dbb-aa6c-4ef4-8bd7-0ea811ffafa7
date_created: '2024-07-15T06:38:26.490Z'
user_updated: 43878dbb-aa6c-4ef4-8bd7-0ea811ffafa7
date_updated: '2025-06-23T03:54:07.016Z'
title: Manage Function Group-Based Permission
requirement:
  time: 1748420670934
  blocks:
    - id: BBFXnhi2Re
      type: paragraph
      data:
        text: <PERSON>u<PERSON><PERSON> lý tập quyền theo chức năng
  version: 2.30.7
screen_design: null
module: SYS
local_fields:
  - code: code
    pinned: true
    title: Code
    data_type:
      key: Employee Code
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    show_sort: true
  - code: name
    title: Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: company
    title: Company
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
  - code: description
    title: Description
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: status
    title: Status
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Boolean Tag
      collection: field_types
    show_sort: true
  - code: lastUpdatedOn
    title: Last Updated On
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
    show_sort: true
  - code: lastUpdatedBy
    title: Last Updated By
    data_type:
      key: User
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    show_sort: true
mock_data:
  - code: NCN01
    name: Nhóm chức năng 01
    company: FPT IS
    description: Data will be displayed here with a max of 2 lines
    permissonsEmployee:
      - View
      - Add new
      - Edit
      - Delete
    permissonsContract:
      - View
      - Add new
      - Edit
      - Delete
    permissonsWorkHistory:
      - View
    status: Active
    createdOn: '2024-12-22 10:25:01'
    createdBy: Ducnm54
    lastUpdatedOn: '2024-12-22 10:25:01'
    lastUpdatedBy: Ducnm54
  - code: NCN02
    name: Nhóm chức năng 02
    company: FPT IS
    description: Data will be displayed here with a max of 2 lines
    permissonsEmployee:
      - View
      - Add new
      - Edit
      - Delete
    permissonsContract:
      - View
      - Add new
      - Edit
      - Delete
    permissonsWorkHistory:
      - View
    status: Active
    createdOn: '2024-12-22 10:25:01'
    createdBy: Ducnm54
    lastUpdatedOn: '2024-12-22 10:25:01'
    lastUpdatedBy: Ducnm54
  - code: NCN03
    name: Nhóm chức năng 03
    company: FPT IS
    description: Data will be displayed here with a max of 2 lines
    permissonsEmployee:
      - View
      - Add new
      - Edit
      - Delete
    permissonsContract:
      - View
      - Add new
      - Edit
      - Delete
    permissonsWorkHistory:
      - View
    status: Active
    createdOn: '2024-12-22 10:25:01'
    createdBy: Ducnm54
    lastUpdatedOn: '2024-12-22 10:25:01'
    lastUpdatedBy: Ducnm54
  - code: NCN04
    name: Nhóm chức năng 04
    company: FPT IS
    description: Data will be displayed here with a max of 2 lines
    permissonsEmployee:
      - View
      - Add new
      - Edit
      - Delete
    permissonsContract:
      - View
      - Add new
      - Edit
      - Delete
    permissonsWorkHistory:
      - View
    status: Inactive
    createdOn: '2024-12-22 10:25:01'
    createdBy: Ducnm54
    lastUpdatedOn: '2024-12-22 10:25:01'
    lastUpdatedBy: Ducnm54
  - code: NCN05
    name: Nhóm chức năng 05
    company: FPT IS
    description: Data will be displayed here with a max of 2 lines
    permissonsEmployee:
      - View
      - Add new
      - Edit
      - Delete
    permissonsContract:
      - View
      - Add new
      - Edit
      - Delete
    permissonsWorkHistory:
      - View
    status: Active
    createdOn: '2024-12-22 10:25:01'
    createdBy: Ducnm54
    lastUpdatedOn: '2024-12-22 10:25:01'
    lastUpdatedBy: Ducnm54
  - code: NCN06
    name: Nhóm chức năng 06
    company: FPT IS
    description: Data will be displayed here with a max of 2 lines
    permissonsEmployee:
      - View
      - Add new
      - Edit
      - Delete
    permissonsContract:
      - View
      - Add new
      - Edit
      - Delete
    permissonsWorkHistory:
      - View
    status: Active
    createdOn: '2024-12-22 10:25:01'
    createdBy: Ducnm54
    lastUpdatedOn: '2024-12-22 10:25:01'
    lastUpdatedBy: Ducnm54
  - code: NCN07
    name: Nhóm chức năng 07
    company: FPT IS
    description: Data will be displayed here with a max of 2 lines
    permissonsEmployee:
      - View
      - Add new
      - Edit
      - Delete
    permissonsContract:
      - View
      - Add new
      - Edit
      - Delete
    permissonsWorkHistory:
      - View
    status: Active
    createdOn: '2024-12-22 10:25:01'
    createdBy: Ducnm54
    lastUpdatedOn: '2024-12-22 10:25:01'
    lastUpdatedBy: Ducnm54
  - code: NCN08
    name: Nhóm chức năng 08
    company: FPT IS
    description: Data will be displayed here with a max of 2 lines
    permissonsEmployee:
      - View
      - Add new
      - Edit
      - Delete
    permissonsContract:
      - View
      - Add new
      - Edit
      - Delete
    permissonsWorkHistory:
      - View
    status: Active
    createdOn: '2024-12-22 10:25:01'
    createdBy: Ducnm54
    lastUpdatedOn: '2024-12-22 10:25:01'
    lastUpdatedBy: Ducnm54
  - code: NCN09
    name: Nhóm chức năng 09
    company: FPT IS
    description: Data will be displayed here with a max of 2 lines
    permissonsEmployee:
      - View
      - Add new
      - Edit
      - Delete
    permissonsContract:
      - View
      - Add new
      - Edit
      - Delete
    permissonsWorkHistory:
      - View
    status: Active
    createdOn: '2024-12-22 10:25:01'
    createdBy: Ducnm54
    lastUpdatedOn: '2024-12-22 10:25:01'
    lastUpdatedBy: Ducnm54
  - code: NCN10
    name: Nhóm chức năng 10
    company: FPT IS
    description: Data will be displayed here with a max of 2 lines
    permissonsEmployee:
      - View
      - Add new
      - Edit
      - Delete
    permissonsContract:
      - View
      - Add new
      - Edit
      - Delete
    permissonsWorkHistory:
      - View
    status: Active
    createdOn: '2024-12-22 10:25:01'
    createdBy: Ducnm54
    lastUpdatedOn: '2024-12-22 10:25:01'
    lastUpdatedBy: Ducnm54
local_buttons: null
layout: layout-table
form_config:
  formTitle:
    create: Add New Function Group-Based Permission
    duplicate: Add New Function Group-Based Permission
    edit: Edit Function Group-Based Permission
    view: Function Group-Based Permission Details
  confirmOnSubmit:
    edit:
      value: compareValue
      transform: >-
        ($.currentValue.isUsed = true) and ($.prevValue.status !=
        $.currentValue.status) and ($.prevValue.status = true and
        $.currentValue.status = false) ? {'title': 'Change Status', 'content':
        'The inactive function group will be automatically removed from assigned
        roles and user groups. Are you sure you want to change?'}
  formSize:
    create: large
    edit: large
    view: large
    proceed: large
  fields:
    - type: group
      n_cols: 2
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - type: text
          name: isUsed
          label: Used
          unvisible: true
        - type: text
          name: code
          label: Code
          placeholder: Enter code
          _disabled:
            transform: $not($.extend.formType = 'create') and $.fields.isUsed = true
          validators:
            - type: required
        - type: translation
          name: name
          label: Name
          placeholder: Enter name
          _disabled:
            transform: $not($.extend.formType = 'create') and $.fields.isUsed = true
          validators:
            - type: required
        - type: select
          label: Company
          name: companySelect
          isLazyLoad: true
          placeholder: Select Company
          _disabled:
            transform: $not($.extend.formType = 'create') and $.fields.isUsed = true
          _select:
            transform: $listCompany($.extend.limit, $.extend.page, $.extend.search)
        - type: radio
          name: status
          label: Status
          value: true
          radio:
            - value: true
              label: Active
            - value: false
              label: Inactive
    - type: group
      n_cols: 1
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - type: text
          name: code
          label: Code
        - type: translation
          name: name
          label: Name
        - type: text
          label: Company
          name: companyCode
          _value:
            transform: >-
              $.extend.defaultValue.company ? $.extend.defaultValue.company & '
              (' & $.extend.defaultValue.companyCode & ')' : ''
        - type: radio
          name: status
          label: Status
          radio:
            - value: true
              label: Active
            - value: false
              label: Inactive
    - type: translationTextArea
      label: Description
      name: description
      placeholder: Enter Description
      textarea:
        autoSize:
          minRows: 3
        maxCharCount: 1000
      validators:
        - type: maxLength
          args: 1000
    - type: text
      name: rawfunctionGroupPermissionDetails
      unvisible: true
    - type: group
      label: Assign Permission To Function Group
      collapse: false
      disableEventCollapse: true
      fieldGroupContentStyle:
        padding: 0px
      fields:
        - type: treeTable
          name: functionGroupPermissionDetails
          _dataSource:
            transform: $getDataTreeTable($.variables._rawData)
          _dataSourceRequestStatus: _rawData
          actions:
            search: {}
          columns:
            - code: functionName
              title: Function List
              align: left
              width: 35
            - code: permission
              title: Assign Permission To Function
              align: left
              width: 35
              data_type:
                key: String
                collection: data_types
              transform: >-
                $count($.value) = 1 and $not($.value[0] = '1') and
                $count($.preValue) = 0 ? $append($.value, ['1']) :
                $sort($.value)
              display_type: select
              validators: []
          layout_option:
            actions_many:
              - id: quickAdd
                title: Quick Assign
                icon: ''
                type: tertiary
              - id: deleteBySelection
                title: Quick Remove
                icon: ''
                type: tertiary
              - id: deleteAll
                title: Remove All
                icon: ''
                type: tertiary
            actions_many_handler:
              quickAdd:
                title: Quick add permission
                descriptions: >-
                  The system will only assign permissions to valid objects.
                  Invalid cases will not be recorded.
                fields:
                  - name: permission
                    placeHolder: Select options
                    type: selectAll
                    width: 200px
                    align: start
                    _options:
                      transform: $.variables._actionsList
                    outputValue: value
                sources:
                  actionsList:
                    uri: '"/api/actions-sys"'
                    method: GET
                    queryTransform: ''
                    bodyTransform: ''
                    headerTransform: ''
                    resultTransform: >-
                      $map($.data, function($item) {{'label': $item.code ,
                      'value': $item.id}})[]
                    disabledCache: true
                variables:
                  _actionsList:
                    transform: $actionsList()
              deleteBySelection:
                title: Delete permission by selection
                descriptions: >-
                  The system will only delete permissions for valid objects.
                  Invalid cases will not be recorded.
                extend: quickAdd
          groupMultiple:
            title:
              - key: modulId
                label: modulName
                onlyKey: true
                keyLabel: modulName
                bold: false
                spaceLeft: '80'
              - key: functionGroupId
                label: ''
                onlyKey: true
                keyLabel: functionGroupName
                bold: false
                spaceLeft: '10'
            expandDefault: false
            expandRowsWithValues: false
            expandRowsWithValuesDefault: true
          showPagination: false
          validators: []
  footer:
    create: true
    update: true
    createdOn: createdOn
    updatedOn: lastUpdatedOn
    createdBy: createdBy
    updatedBy: lastUpdatedBy
  sources:
    listCompany:
      uri: '"/api/companies/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'search': $.search,'filter':
        [{'field':'status', 'operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    actionsList:
      uri: '"/api/actions-sys"'
      method: GET
      queryTransform: '{''limit'': 9999}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.code , 'value':
        $item.id}})[]
      disabledCache: true
    adminModulesList:
      uri: '"/api/admin-modules"'
      method: GET
      queryTransform: '{''filter'': [{''field'':''status'',''operator'': ''$eq'',''value'':true}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $
      disabledCache: true
  variables:
    _rawData:
      transform: >-
        {'rawfunctionGroupPermissionDetails':
        [$.fields.rawfunctionGroupPermissionDetails], 'adminModulesList':
        $adminModulesList()}
    _actionsList:
      transform: $actionsList()
filter_config:
  fields:
    - type: text
      labelType: type-grid
      name: code
      label: Code
      placeholder: Enter Code
    - type: text
      labelType: type-grid
      name: name
      label: Name
      placeholder: Enter Name
    - type: selectAll
      labelType: type-grid
      label: Company
      name: companyCode
      placeholder: Select Company
      mode: multiple
      _options:
        transform: $companiesList()
    - type: text
      labelType: type-grid
      name: description
      label: Description
      placeholder: Enter Description
    - type: radio
      labelType: type-grid
      name: status
      label: Status
      value: ''
      radio:
        - label: All
          value: ''
        - label: Active
          value: true
        - label: Inactive
          value: false
    - type: dateRange
      labelType: type-grid
      name: lastUpdatedOn
      label: Last Updated On
      setting:
        format: dd/MM/yyyy
        type: date
    - type: text
      labelType: type-grid
      name: lastUpdatedBy
      label: Last Updated By
      placeholder: Enter Editor
  filterMapping:
    - valueField: code
      field: code
      operator: $cont
    - valueField: name
      field: name
      operator: $cont
    - valueField: companyCode.(value)
      field: companyCode
      operator: $eq
    - valueField: description
      field: description
      operator: $cont
    - valueField: status
      field: status
      operator: $eq
    - valueField: lastUpdatedOn
      field: lastUpdatedOn
      operator: $between
    - valueField: lastUpdatedBy
      field: lastUpdatedBy
      operator: $cont
  sources:
    companiesList:
      uri: '"/api/companies/by"'
      method: GET
      queryTransform: '{''filter'': [{''field'':''status'',''operator'': ''$eq'',''value'':true}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default & ' (' &
        $item.code &')', 'value': $item.code}})[]
      disabledCache: true
    dataPermission:
      uri: '"/api/admin-function-group-permissions"'
      method: GET
      queryTransform: '{''limit'': 9999}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $.data
      disabledCache: true
layout_options:
  link_redirect: /FO/FO.FR.036
  show_detail_history: false
  duplicate_value_transform:
    fields:
      - code
    transform: ''''''
  tool_table:
    - id: import
      icon: icon-upload-simple
      href: /GE/HR.FS.FR.092
    - id: export
      icon: icon-download-simple
  hide_action_row: true
  is_new_dynamic_form: false
  delete_multi_items: true
  custom_delete_body: $map($.data, function($item){$item.id})[]
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions: []
backend_url: /api/admin-function-group-permissions
screen_name: null
layout_options__actions_many: null
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: companyCode
    defaultName: CompanyCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Manage Function Group-Based Permission
  parent:
    title: Function list
