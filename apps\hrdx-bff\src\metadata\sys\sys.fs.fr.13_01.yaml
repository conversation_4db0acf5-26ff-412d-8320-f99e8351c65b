id: SYS.FS.FR.13_01
status: draft
sort: 119
user_created: 43878dbb-aa6c-4ef4-8bd7-0ea811ffafa7
date_created: '2024-07-15T02:26:23.995Z'
user_updated: 43878dbb-aa6c-4ef4-8bd7-0ea811ffafa7
date_updated: '2025-07-21T13:33:42.411Z'
title: Manage Data Zone-Based Permission
requirement:
  time: 1748402606483
  blocks:
    - id: QlwfZ2ClD0
      type: paragraph
      data:
        text: '111'
  version: 2.30.7
screen_design: null
module: SYS
local_fields:
  - code: code
    pinned: true
    title: Code
    data_type:
      key: Employee Code
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    options__tabular__column_width: 12.5
    show_sort: true
  - code: name
    title: Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    options__tabular__column_width: 20
    show_sort: true
  - code: companyName
    title: Company
    data_type:
      key: Organization-2
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    show_sort: true
    options__tabular__column_width: 15
  - code: description
    title: Description
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
    show_sort: true
  - code: notAuthorizedDisplay
    title: Not Permitted
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: Tag
      collection: field_types
    show_sort: true
  - code: notDecentralizeYourselfDisplay
    title: Not Assign Permission To Yourself
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: Tag
      collection: field_types
    options__tabular__column_width: 20
    show_sort: true
  - code: status
    title: Status
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Boolean Tag
      collection: field_types
    show_sort: true
  - code: permissionsWithLabel
    title: Permission Based On
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 12.5
    show_sort: true
  - code: criteria
    title: Criteria
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    options__tabular__column_width: 12.5
    extra_config:
      singular: Criteria
      plural: Criteria
      _type:
        transform: '$.value.Employee ? ''Label'' : ''Tooltip'''
      _value:
        transform: >-
          $.value.Employee ? $count($split($.value.Employee, ';')) & '
          Employee(s)' : $.value
    show_sort: false
  - code: editedTime
    title: Last Updated On
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
    options__tabular__column_width: 15
    show_sort: true
  - code: editor
    title: Last Updated By
    data_type:
      key: User
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 12.5
    show_sort: true
mock_data:
  - code: V1_FPTIS
    name: Region 1 FPTIS
    company: FPT IS
    legalEntity: FPT IS HN
    bussinessUnit: ES
    notAuthorized: false
    notDecentralizeYourself: true
    division: FPT IS
    department:
      - PB22
    country:
      - Vietnam
    location:
      - Ha Noi
    status: Active
    description: ''
    creator: Ducnm54
    editor: Ducnm54
    permissionsWithLabel: Permissions by structure
    permissionsWith:
      value: '4'
      label: By structure
    criteria:
      corporation: FPT
      company: FIS
      division: ES
    createdTime: 2023/12/02 10:00:13
    editedTime: 2024/05/03 10:00:00
  - code: V2_FPTIS
    name: Region 2 FPTIS
    company: FPT IS
    legalEntity: FPT IS HN
    bussinessUnit: ES
    division: FPT IS
    department:
      - PB22
    country:
      - Vietnam
    location:
      - Ha Noi
    status: Active
    description: ''
    creator: Ducnm54
    editor: Ducnm54
    permissionsWithLabel: Permissions by individual
    permissionsWith:
      value: '1'
      label: By individual
    criteria:
      corporation: FPT
      company: FIS
      division: ES
    createdTime: 2023/12/02 10:00:13
    editedTime: 2024/05/03 10:00:00
  - code: V3_FPTIS
    name: Region 3 FPTIS
    company: FPT IS
    legalEntity: FPT IS HN
    bussinessUnit: ES
    division: FPT IS
    department:
      - PB22
    country:
      - Vietnam
    location:
      - Ha Noi
    status: Inactive
    description: ''
    creator: Ducnm54
    editor: Ducnm54
    permissionsWithLabel: Permissions by direct supervisor
    permissionsWith:
      value: '2'
      label: By direct supervisor
    includesSubordinates: true
    criteria:
      corporation: FPT
      company: FIS
      division: ES
    createdTime: 2023/12/02 10:00:13
    editedTime: 2024/05/03 10:00:00
  - code: V4_FPTIS
    name: Region 4 FPTIS
    company: FPT IS
    legalEntity: FPT IS HN
    bussinessUnit: ES
    division: FPT IS
    department:
      - PB22
    country:
      - Vietnam
    location:
      - Ha Noi
    status: Active
    description: ''
    creator: Ducnm54
    editor: Ducnm54
    permissionsWithLabel: Permissions by employee list
    permissionsWith:
      value: '5'
      label: By employee list
    includesSubordinates: true
    criteria:
      corporation: FPT
      company: FIS
      division: ES
    createdTime: 2023/12/02 10:00:13
    editedTime: 2024/05/03 10:00:00
  - code: V5_FPTIS
    name: Region 5 FPTIS
    company: FPT IS
    legalEntity: FPT IS HN
    bussinessUnit: ES
    division: FPT IS
    department:
      - PB22
    country:
      - Vietnam
    location:
      - Ha Noi
    status: Active
    description: ''
    creator: Ducnm54
    editor: Ducnm54
    permissionsWithLabel: Permissions by individual
    permissionsWith:
      value: '1'
      label: By individual
    includesSubordinates: true
    criteria:
      corporation: FPT
      company: FIS
      division: ES
    createdTime: 2023/12/02 10:00:13
    editedTime: 2024/05/03 10:00:00
local_buttons: null
layout: layout-table
form_config:
  formSize:
    create: middle
    edit: middle
    view: large
    proceed: middle
  formTitle:
    create: Add New Data Zone-Based Permission
    view: Data Zone-Based Permission Details
    edit: Edit Data Zone-Based Permission
  confirmOnSubmit:
    edit:
      value: compareValue
      transform: >-
        ($.prevValue.status != $.currentValue.status) and ($.prevValue.status =
        true and $.currentValue.status = false) ? {'title': 'Change status',
        'content': 'The inactive security information group will be
        automatically removed from assigned users and user groups. Are you sure
        you want to change?'}
  fields:
    - type: group
      n_cols: 2
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - type: text
          name: isUsed
          label: Used
          unvisible: true
        - type: text
          name: code
          label: Code
          placeholder: Enter Code
          _disabled:
            transform: $not($.extend.formType = 'create') and $.fields.isUsed = true
          validators:
            - type: required
        - type: translation
          name: name
          label: Name
          _disabled:
            transform: $not($.extend.formType = 'create') and $.fields.isUsed = true
          placeholder: Enter Name
          validators:
            - type: required
        - type: select
          name: companyCode
          isLazyLoad: true
          label: Company
          _disabled:
            transform: $not($.extend.formType = 'create') and $.fields.isUsed = true
          placeholder: Select Company
          clearFieldsAfterChange:
            - dataAreaDetails.legal
            - dataAreaDetails.businessunit
            - dataAreaDetails.division
            - dataAreaDetails.department
          _select:
            transform: >-
              $companiesList($.extend.limit, $.extend.page, $.extend.search,
              $now())
        - type: radio
          name: status
          label: Status
          value: true
          radio:
            - value: true
              label: Active
            - value: false
              label: Inactive
    - type: group
      n_cols: 1
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - type: text
          name: code
          label: Code
        - type: translation
          name: name
          label: Name
        - type: text
          name: companyCode
          label: Company
          unvisible: true
        - type: text
          name: companyName
          label: Company
        - type: radio
          name: notAuthorized
          label: Not Permitted
          radio:
            - label: 'Yes'
              value: true
            - label: 'No'
              value: false
          displaySetting:
            type: Tag
            extraConfig:
              tags:
                - value: true
                  label: 'Yes'
                  class: success
                - value: false
                  label: 'No'
                  class: default
              size: small
        - type: radio
          name: notDecentralizeYourself
          label: Not Assign Permission To Yourself
          radio:
            - label: 'Yes'
              value: true
            - label: 'No'
              value: false
          displaySetting:
            type: Tag
            extraConfig:
              tags:
                - value: true
                  label: 'Yes'
                  class: success
                - value: false
                  label: 'No'
                  class: default
              size: small
        - type: radio
          name: status
          label: Status
          radio:
            - value: true
              label: Active
            - value: false
              label: Inactive
    - type: translationTextArea
      name: description
      label: Description
      placeholder: Enter Description
      textarea:
        autoSize:
          minRows: 3
          maxRows: 5
    - type: group
      n_cols: 1
      label: Permission
      _condition:
        transform: $not($.extend.formType = 'view')
      fieldGroupContentStyle:
        gap: 8px
      description: 'Caution: If you change options, unsaved input data may be lost'
      fields:
        - type: switch
          name: notAuthorized
          label: Not Permitted
          showSwitchLabel: true
          hiddenLabel: true
          value: false
          _value:
            transform: $.fields.notDecentralizeYourself = true ? false
        - type: switch
          name: notDecentralizeYourself
          label: Not Assign Permission To Yourself
          showSwitchLabel: true
          hiddenLabel: true
          value: false
          _value:
            transform: '$.fields.notAuthorized = true ? false '
    - type: group
      label: 'Assign Data Zone-Based Permission '
      collapse: false
      disableEventCollapse: false
      border_top: '1px solid #e0e0e0'
      fields:
        - type: radio
          name: ruleCode
          label: Permission Based On
          toast:
            position: top
            type: warning
            content: 'Caution: If you change options, unsaved input data will be lost'
          _radio:
            transform: $.variables._filteredPermissionsWith
          _unvisible:
            transform: $.extend.formType = 'view'
          selectOptions:
            alertWhenChanged: true
          validators:
            - type: required
          outputValue: value
        - type: text
          name: dataAreaParams
          label: Data Area Params
          unvisible: true
          _value:
            transform: $.variables._dataAreaParams
        - type: text
          name: permissionsWithLabel
          label: Permission Based On
          _condition:
            transform: $.extend.formType = 'view'
        - type: group
          label: ''
          _condition:
            transform: >-
              $not($.fields.notAuthorized = true) and
              $not($.fields.notDecentralizeYourself = true) and
              ($.fields.ruleCode = 'CTTT' or $.fields.ruleCode = 'CTGT') and
              $not($.extend.formType = 'view')
          padding: 16px
          borderRadius: 8px
          height: 52px
          border: '1px solid #e0e0e0'
          fields:
            - type: checkbox
              name: includesSubordinates
              label: Include in direct staff
              hiddenLabel: true
              height: 20px
        - type: text
          label: Include in direct staff
          name: includesSubordinates
          unvisible: true
        - type: text
          label: Include in direct staff
          name: ''
          _condition:
            transform: >-
              ($.fields.ruleCode = 'CTTT' or $.fields.ruleCode = 'CTGT') and
              $.extend.formType = 'view'
          _value:
            transform: '$.fields.includesSubordinates = true ? ''Yes'' : ''No'''
        - type: text
          name: structureLevel
          unvisible: true
          _value:
            transform: >-
              $not($.fields.ruleCode = 'ORG') ? '_setSelectValueNull' :
              $exists($.extend.defaultValue.structureLevel) ?
              $.extend.defaultValue.structureLevel : 'Company' 
        - type: group
          label: ''
          name: dataAreaDetails
          _condition:
            transform: $.fields.ruleCode = 'ORG' and $not($.extend.formType = 'view')
          padding: 16px
          borderRadius: 8px
          border: '1px solid #e0e0e0'
          fields:
            - type: radio
              name: _structureLevel
              label: Level of Permission
              _radio:
                transform: $.variables._structurePermissionsLevelList
              _value:
                transform: $.fields.structureLevel
              _unvisible:
                transform: $.extend.formType = 'view'
              validators:
                - type: required
              outputValue: value
            - type: group
              n_cols: 2
              fields:
                - type: select
                  name: company
                  label: Company
                  _value:
                    transform: $.variables._handle_companyValue
                  unvisible: true
                  clearFieldsAfterChange:
                    - legal
                    - businessunit
                    - division
                    - department
                - type: select
                  name: createCompany
                  label: Company
                  _placeholder:
                    transform: >-
                      $.fields.dataAreaDetails._structureLevel = 'Group' ? 'All
                      Company' : 'Select Company'
                  isLazyLoad: true
                  _select:
                    transform: >-
                      $companiesList($.extend.limit, $.extend.page,
                      $.extend.search, $now())
                  _condition:
                    transform: >-
                      $.extend.formType = 'create' and
                      $not($.extend.isDuplicate) and
                      $not($.fields.dataAreaDetails._structureLevel = 'Company')
                  _value:
                    transform: >-
                      $.fields.dataAreaDetails._structureLevel = 'Group' ?
                      '_setSelectValueNull' : $boolean($.fields.companyCode) ?
                      $.fields.companyCode
                  _disabled:
                    transform: $.fields.dataAreaDetails._structureLevel = 'Group'
                  dependantFieldSkip: 2
                  clearFieldsAfterChange:
                    - company
                    - legal
                    - businessunit
                    - division
                    - department
                  validators:
                    - type: required
                    - type: ppx-custom
                      args:
                        transform: >-
                          $not($isNilorEmpty($.fields.companyCode)) and
                          $not($.fields.companyCode.value =
                          $.fields.dataAreaDetails.company.value)
                      text: Must select the same company as selected above.
                - type: select
                  name: editCompany
                  label: Company
                  isLazyLoad: true
                  _placeholder:
                    transform: >-
                      $.fields.dataAreaDetails._structureLevel = 'Group' ? 'All
                      Company' : 'Select Company'
                  _select:
                    transform: >-
                      $companiesList($.extend.limit, $.extend.page,
                      $.extend.search, $now())
                  _condition:
                    transform: >-
                      ($.extend.formType = 'edit' or $.extend.isDuplicate) and
                      $not($.fields.dataAreaDetails._structureLevel = 'Company')
                  _value:
                    transform: >-
                      $.fields.dataAreaDetails._structureLevel = 'Group' ?
                      '_setSelectValueNull' : $.fields.isUsed = true ?
                      $.variables._dataAreaTransform.company :
                      $.extend.defaultValue.companyCode != $.fields.companyCode
                      ? $.fields.companyCode :
                      $.variables._dataAreaTransform.company
                  _disabled:
                    transform: $.fields.dataAreaDetails._structureLevel = 'Group'
                  clearFieldsAfterChange:
                    - company
                    - legal
                    - businessunit
                    - division
                    - department
                  dependantFieldSkip: 2
                  validators:
                    - type: required
                    - type: ppx-custom
                      args:
                        transform: >-
                          $not($isNilorEmpty($.fields.companyCode)) and
                          $not($.fields.companyCode.value =
                          $.fields.dataAreaDetails.company.value)
                      text: Must select the same company as selected above.
                - type: selectAll
                  name: multiCreateCompany
                  label: Company
                  placeholder: Select Company
                  isLazyLoad: true
                  _options:
                    transform: >-
                      $companiesList($.extend.limit, $.extend.page,
                      $.extend.search, $now())
                  _condition:
                    transform: >-
                      $.extend.formType = 'create' and
                      $not($.extend.isDuplicate) and
                      $.fields.dataAreaDetails._structureLevel = 'Company'
                  _value:
                    transform: $boolean($.fields.companyCode) ? [$.fields.companyCode]
                  clearFieldsAfterChange:
                    - company
                    - legal
                    - businessunit
                    - division
                    - department
                  validators:
                    - type: required
                    - type: ppx-custom
                      args:
                        transform: >-
                          $not($isNilorEmpty($.fields.companyCode)) and
                          $not($count($.fields.dataAreaDetails.company) = 1 and
                          ($.fields.dataAreaDetails.company[0].value =
                          $.fields.companyCode.value))
                      text: >-
                        To select multiple companies, first remove the company
                        in the general info.
                - type: selectAll
                  name: multiEditCompany
                  label: Company
                  isLazyLoad: true
                  placeholder: Select Company
                  _disabled:
                    transform: $boolean($.fields.companyCode) and $.fields.isUsed = true
                  _options:
                    transform: >-
                      $companiesList($.extend.limit, $.extend.page,
                      $.extend.search, $now())
                  _condition:
                    transform: >-
                      ($.extend.formType = 'edit' or $.extend.isDuplicate) and
                      $.fields.dataAreaDetails._structureLevel = 'Company'
                  _value:
                    dependants:
                      - $.variables._dataAreaTransform.company
                    transform: >-
                      $.fields.isUsed = true ?
                      $.variables._dataAreaTransform.company[] :
                      $.extend.defaultValue.companyCode != $.fields.companyCode
                      ? $.fields.companyCode[] :
                      $.variables._dataAreaTransform.company[]
                  clearFieldsAfterChange:
                    - company
                    - legal
                    - businessunit
                    - division
                    - department
                  validators:
                    - type: required
                    - type: ppx-custom
                      args:
                        transform: >-
                          $not($isNilorEmpty($.fields.companyCode)) and
                          $not($count($.fields.dataAreaDetails.company) = 1 and
                          ($.fields.dataAreaDetails.company[0].value =
                          $.fields.companyCode.value))
                      text: >-
                        To select multiple companies, first remove the company
                        in the general info.
                - type: treeSelect
                  name: legal
                  label: Legal Entity
                  placeholder: Select Legal Entity
                  mode: multiple
                  checkStrictly: true
                  _nodes:
                    transform: $.variables._legalTree
                  _value:
                    transform: ($.variables._dataAreaTransform.legal).value[]
                  _condition:
                    transform: >-
                      $count($filter(['Legal Entity','Department'],
                      function($item){ $item =
                      $.fields.dataAreaDetails._structureLevel})) > 0
                  dependantField: $.fields.dataAreaDetails.company
                  dependantFieldSkip: 3
                  validators:
                    - type: required
                    - type: ppx-custom
                      args:
                        transform: $.variables._validLegal
                      text: >-
                        {{values.variables._get_missingLegal}} is invalid as no
                        department under it is selected, remove it or add
                        departments
                - type: group
                  _condition:
                    transform: >-
                      $count($filter(['Business Unit','Division'],
                      function($item){ $item =
                      $.fields.dataAreaDetails._structureLevel})) > 0
                - type: treeSelect
                  name: businessunit
                  label: Business Unit
                  placeholder: Select Bussiness Unit
                  mode: multiple
                  checkStrictly: true
                  _nodes:
                    transform: $.variables._businessUnitTree
                  _value:
                    transform: ($.variables._dataAreaTransform.businessunit).value[]
                  _condition:
                    transform: >-
                      $count($filter(['Business Unit','Division' ,'Department'],
                      function($item){ $item =
                      $.fields.dataAreaDetails._structureLevel})) > 0
                  dependantField: >-
                    $.fields.dataAreaDetails.company,
                    $.fields.dataAreaDetails.legal
                  dependantFieldSkip: 3
                  _class:
                    transform: >-
                      $count($filter(['Business Unit','Division'],
                      function($item){ $item =
                      $.fields.dataAreaDetails._structureLevel})) > 0 ?
                      'required' : 'unrequired'
                  validators:
                    - type: ppx-custom
                      args:
                        transform: $.variables._validBU
                      text: >-
                        {{values.variables._get_missingBU}} is invalid as no
                        department under it is selected, remove it or add
                        departments
                - type: treeSelect
                  name: division
                  label: Division
                  placeholder: Select Division
                  mode: multiple
                  checkStrictly: true
                  _nodes:
                    transform: $.variables._divisionTree
                  _value:
                    transform: ($.variables._dataAreaTransform.division).value[]
                  _condition:
                    transform: >-
                      $count($filter(['Division' ,'Department'],
                      function($item){ $item =
                      $.fields.dataAreaDetails._structureLevel})) > 0
                  dependantField: >-
                    $.fields.dataAreaDetails.company,
                    $.fields.dataAreaDetails.legal,
                    $.fields.dataAreaDetails.businessunit
                  dependantFieldSkip: 3
                  _class:
                    transform: >-
                      $count($filter(['Business Unit','Division'],
                      function($item){ $item =
                      $.fields.dataAreaDetails._structureLevel})) > 0 ?
                      'required' : 'unrequired'
                  validators:
                    - type: ppx-custom
                      args:
                        transform: $.variables._validDivison
                      text: >-
                        {{values.variables._get_missingDiv}} is invalid as no
                        department under it is selected, remove it or add
                        departments
                - type: treeSelect
                  name: department
                  label: Department
                  mode: multiple
                  checkStrictly: true
                  placeholder: Select Department
                  _nodes:
                    transform: $.variables._departmentsTree
                  _condition:
                    transform: >-
                      $count($filter(['Department'], function($item){ $item =
                      $.fields.dataAreaDetails._structureLevel})) > 0
                  validators:
                    - type: required
                  dependantField: >-
                    $.fields.dataAreaDetails.company,
                    $.fields.dataAreaDetails.legal,
                    $.fields.dataAreaDetails.businessunit,
                    $.fields.dataAreaDetails.division
                  dependantFieldSkip: 3
                  _value:
                    transform: ($.variables._dataAreaTransform.department).value[]
            - type: group
              n_cols: 2
              label: Location
              border_top: '1px solid #e0e0e0'
              fields:
                - type: selectAll
                  settings:
                    maxTag: 3
                  name: country
                  label: Country
                  mode: multiple
                  placeholder: Select Country
                  isLazyLoad: true
                  clearFieldsAfterChange:
                    - area
                  _options:
                    transform: >-
                      $nationsList($.extend.limit, $.extend.page,
                      $.extend.search, $now())
                  _value:
                    transform: $.variables._dataAreaTransform.country
                  outputValue: value
                - type: selectAll
                  settings:
                    maxTag: 3
                  name: area
                  isLazyLoad: true
                  label: Region
                  mode: multiple
                  placeholder: Select Region
                  _options:
                    transform: >-
                      $.extend.formType = 'edit' ? $regionsList($.extend.limit,
                      $.extend.page, $.extend.search,
                      $.fields.dataAreaDetails.country.value) :
                      $regionsList($.extend.limit, $.extend.page,
                      $.extend.search, $.fields.dataAreaDetails.country.value[])
                  clearFieldsAfterChange:
                    - location
                  _value:
                    transform: $.variables._dataAreaTransform.area
                  outputValue: value
                - type: selectAll
                  settings:
                    maxTag: 3
                  name: location
                  label: Location
                  mode: multiple
                  isLazyLoad: true
                  placeholder: Select Location
                  dependantFieldSkip: 2
                  dependantField: $.fields.dataAreaDetails.company
                  _options:
                    transform: >-
                      $.extend.formType = 'edit' ?
                      $locationsList($.extend.limit, $.extend.page,
                      $.extend.search, $now(),
                      $.fields.dataAreaDetails.company.value,
                      $.fields.dataAreaDetails.area.value) :
                      $locationsList($.extend.limit, $.extend.page,
                      $.extend.search, $now(),
                      $.fields.dataAreaDetails.company.value,
                      $.fields.dataAreaDetails.area)
                  _value:
                    transform: $.variables._dataAreaTransform.location
                  outputValue: value
            - type: group
              n_cols: 2
              label: Employee Details
              border_top: '1px solid #e0e0e0'
              fields:
                - type: selectAll
                  settings:
                    maxTag: 3
                  name: employeetype
                  label: Employee Sub Group
                  mode: multiple
                  dependantFieldSkip: 2
                  placeholder: Select Employee Sub Group
                  isLazyLoad: true
                  _options:
                    transform: >-
                      $employeeTypesList($.extend.limit, $.extend.page,
                      $.extend.search, $now())
                  _value:
                    transform: $.variables._dataAreaTransform.employeetype
                  outputValue: value
                - type: selectAll
                  settings:
                    maxTag: 3
                  name: careerband
                  isLazyLoad: true
                  label: Career Band
                  dependantFieldSkip: 2
                  mode: multiple
                  placeholder: Select Career Band
                  _options:
                    transform: >-
                      $bandsList($.extend.limit, $.extend.page, $.extend.search,
                      $now())
                  _value:
                    transform: $.variables._dataAreaTransform.careerband
                  outputValue: value
                - type: selectAll
                  settings:
                    maxTag: 3
                  name: jobtitle
                  isLazyLoad: true
                  label: Job Title
                  mode: multiple
                  placeholder: Select Job Title
                  dependantFieldSkip: 2
                  dependantField: $.fields.dataAreaDetails.company
                  _options:
                    transform: >-
                      $jobTitlesList($.extend.limit, $.extend.page,
                      $.extend.search, $now(),
                      $.fields.dataAreaDetails.company.value)
                  _value:
                    transform: $.variables._dataAreaTransform.jobtitle
                  outputValue: value
                - type: selectAll
                  settings:
                    maxTag: 3
                  name: level
                  isLazyLoad: true
                  label: Employee Level
                  mode: multiple
                  placeholder: Select Employee Level
                  dependantFieldSkip: 2
                  _options:
                    transform: >-
                      $levelsList($.extend.limit, $.extend.page,
                      $.extend.search, $now())
                  _value:
                    transform: $.variables._dataAreaTransform.level
                  outputValue: value
            - type: group
              n_cols: 2
              label: Finance Information
              border_top: '1px solid #e0e0e0'
              fields:
                - type: selectAll
                  settings:
                    maxTag: 3
                  name: paygroup
                  label: Pay Group
                  mode: multiple
                  isLazyLoad: true
                  placeholder: Select Pay Group
                  _options:
                    transform: >-
                      $payGroupsList($.extend.limit, $.extend.page,
                      $.extend.search, $.variables._selected_company_dataForm,
                      $.fields.dataAreaDetails.country.value[] ,$now())
                  dependantFieldSkip: 3
                  _value:
                    transform: $.variables._dataAreaTransform.paygroup
                  dependantField: >-
                    $.fields.dataAreaDetails.company,
                    $.fields.dataAreaDetails.country
                  outputValue: value
                - type: selectAll
                  settings:
                    maxTag: 3
                  name: taxsettlementgroup
                  label: "Tax Settlement Group\_"
                  mode: multiple
                  dependantFieldSkip: 2
                  isLazyLoad: true
                  placeholder: "Select Tax Settlement Group\_"
                  _options:
                    transform: >-
                      $grouptaxsettlementsList($.extend.limit, $.extend.page,
                      $.extend.search, $.fields.dataAreaDetails.country.value[]
                      ,$now())
                  _value:
                    transform: $.variables._dataAreaTransform.taxsettlementgroup
                  dependantField: $.fields.dataAreaDetails.country
                  outputValue: value
        - type: group
          label: ''
          name: dataAreaDetails
          _condition:
            transform: $.fields.ruleCode = 'ORG' and $.extend.formType = 'view'
          padding: 16px
          borderRadius: 8px
          border: '1px solid #d1dae6'
          fields:
            - type: text
              name: structureLevel
              label: Level of Permission
              _value:
                transform: $.variables._dataAreaTransformView.structureLevel
            - type: text
              name: company
              label: Company
              _value:
                transform: >-
                  $.fields.dataAreaDetails.structureLevel = 'Group' ? 'All
                  Company' : $.variables._dataAreaTransformView.company
            - type: text
              name: legal
              label: Legal Entity
              _value:
                transform: $.variables._dataAreaTransformView.legal
              _condition:
                transform: >-
                  $count($filter(['Legal Entity','Department'], function($item){
                  $item = $.variables._dataAreaTransformView.structureLevel})) >
                  0
            - type: text
              name: businessunit
              label: Bussiness Unit
              _value:
                transform: $.variables._dataAreaTransformView.businessunit
              _condition:
                transform: >-
                  $count($filter(['Business Unit','Division' ,'Department'],
                  function($item){ $item =
                  $.variables._dataAreaTransformView.structureLevel})) > 0
            - type: text
              name: division
              label: Division
              _value:
                transform: $.variables._dataAreaTransformView.division
              _condition:
                transform: >-
                  $count($filter(['Division' ,'Department'], function($item){
                  $item = $.variables._dataAreaTransformView.structureLevel})) >
                  0
            - type: text
              name: department
              label: Department
              mode: multiple
              _value:
                transform: >-
                  ($.variables._dataAreaTransformView; $join(
                  $.variables._dataAreaTransformView.department.label[] , ', '))
              _condition:
                transform: >-
                  $count($filter(['Department'], function($item){ $item =
                  $.variables._dataAreaTransformView.structureLevel})) > 0
            - type: text
              name: country
              label: Country
              _value:
                transform: >-
                  ($.variables._dataAreaTransformView; 
                  $join($.variables._dataAreaTransformView.country.label[], ',')
                  )
            - type: text
              name: area
              label: Region
              _value:
                transform: >-
                  ($.variables._dataAreaTransformView;
                  $join($.variables._dataAreaTransformView.area.label[] , ', '))
            - type: text
              name: location
              label: Location
              _value:
                transform: >-
                  ($.variables._dataAreaTransformView; 
                  $join($.variables._dataAreaTransformView.location.label[],
                  ',') )
            - type: text
              name: level
              label: Employee Level
              _value:
                transform: >-
                  ($.variables._dataAreaTransformView;$join($.variables._dataAreaTransformView.level.label[]
                  , ', '))
            - type: text
              name: careerband
              label: Career Band
              _value:
                transform: >-
                  ($.variables._dataAreaTransformView;$join($.variables._dataAreaTransformView.careerband.label[]
                  , ', '))
            - type: text
              name: jobtitle
              label: Job Title
              _value:
                transform: >-
                  ($.variables._dataAreaTransformView;$join($.variables._dataAreaTransformView.jobtitle.label[]
                  , ', '))
            - type: text
              name: employeetype
              label: Employee Sub Group
              _value:
                transform: >-
                  ($.variables._dataAreaTransformView;$join($.variables._dataAreaTransformView.employeetype.label[]
                  , ', '))
            - type: text
              name: paygroup
              label: Pay Group
              _value:
                transform: >-
                  ($.variables._dataAreaTransformView;$join($.variables._dataAreaTransformView.paygroup.label[]
                  , ', '))
            - type: text
              name: taxsettlementgroup
              label: Tax Settlement Group
              _value:
                transform: >-
                  ($.variables._dataAreaTransformView;$join($.variables._dataAreaTransformView.taxsettlementgroup.label[]
                  , ', '))
        - type: group
          name: dataAreaDetails
          _condition:
            transform: $.fields.ruleCode = 'NVS' and $not($.extend.formType='view')
          borderRadius: 8px
          fields:
            - type: tableSelect
              _dataSourceRequestStatus: _listEmpViewFilter
              name: userSelect
              required: true
              mapRowName:
                - employeeId
                - employeeRecordNumber
              addNewBtnTitle: Add Employee(s)
              _optionList:
                transform: >-
                  $personalsFullInforList($.extend.limit, $.extend.page,
                  $.extend.search, $append($.extend.filter[0].field =
                  'employeeId' ? [{'operator': '$or','value':
                  $map($.extend.filter[0].value, function($item) {{'operator':
                  '$and', 'value': [    {'field': 'employeeId', 'operator':
                  '$eq', 'value': $item.empid},     {'field':
                  'employeeRecordNumber', 'operator': '$eq', 'value':
                  $item.ern}]}})[]}] : [], $.extend.filter[0].field =
                  'employeeId' ? $filter($.extend.filter, function ($v, $i) {$i
                  != 0}) : $.extend.filter ) )
              filterFields:
                - type: group
                  collapse: false
                  label: Employee Details
                  fieldGroupTitleStyle:
                    height: 48px
                  hostStyle:
                    marginTop: '-12px'
                  fields:
                    - type: selectAll
                      isLazyLoad: true
                      name: employeeId
                      label: Employee
                      placeholder: Select Employee (ID; Name)
                      _options:
                        transform: >-
                          $personalsList($.extend.limit, $.extend.page,
                          $.extend.search, $now())
                      mode: multiple
                    - type: selectAll
                      isLazyLoad: true
                      mode: multiple
                      label: Employee Level
                      name: employeeLevelId
                      placeholder: Select Employee Level
                      _options:
                        transform: >-
                          $employeeLevelsList($.extend.limit, $.extend.page,
                          $.extend.search)
                    - type: selectAll
                      isLazyLoad: true
                      mode: multiple
                      label: Job Title
                      name: title
                      placeholder: Select Job Title
                      _options:
                        transform: >-
                          $jobTitlesList($.extend.limit, $.extend.page,
                          $.extend.search, $now())
                    - type: selectAll
                      isLazyLoad: true
                      mode: multiple
                      label: Career Band
                      name: careerBandCode
                      placeholder: Select Career Band
                      _options:
                        transform: >-
                          $bandsList($.extend.limit, $.extend.page,
                          $.extend.search, $now())
                    - type: selectAll
                      isLazyLoad: true
                      mode: multiple
                      label: Employee Type
                      name: EmployeeTypeId
                      placeholder: Select Employee Type
                      _options:
                        transform: >-
                          $employeeTypesList($.extend.limit, $.extend.page,
                          $.extend.search, $now())
                - type: group
                  collapse: false
                  label: Company Information
                  hostStyle:
                    borderTop: '1px solid #e0e0e0'
                  fieldGroupTitleStyle:
                    height: 48px
                  fields:
                    - type: selectAll
                      isLazyLoad: true
                      mode: multiple
                      label: Company
                      name: company
                      placeholder: Select Company
                      _options:
                        transform: >-
                          $companiesList($.extend.limit, $.extend.page,
                          $.extend.search, $now())
                    - type: selectAll
                      isLazyLoad: true
                      mode: multiple
                      label: Legal Entity
                      name: legalEntity
                      placeholder: Select Legal Entity
                      _options:
                        transform: >-
                          $legalEntitiesFullList($.extend.limit, $.extend.page,
                          $.extend.search, $now())
                    - type: selectAll
                      isLazyLoad: true
                      mode: multiple
                      label: Business Unit
                      name: bussinessUnit
                      placeholder: Select Business Unit
                      _options:
                        transform: >-
                          $businessunitsFullList($.extend.limit, $.extend.page,
                          $.extend.search, $now())
                    - type: selectAll
                      isLazyLoad: true
                      mode: multiple
                      label: Division
                      name: division
                      placeholder: Select Division
                      _options:
                        transform: >-
                          $divisionsFullList($.extend.limit, $.extend.page,
                          $.extend.search, $now())
                    - type: selectAll
                      isLazyLoad: true
                      mode: multiple
                      label: Department
                      name: department
                      placeholder: Select Department
                      _options:
                        transform: >-
                          $departmentsList($.extend.limit, $.extend.page,
                          $.extend.search, $now())
                - type: group
                  collapse: false
                  label: Location Information
                  hostStyle:
                    borderTop: '1px solid #e0e0e0'
                  fieldGroupTitleStyle:
                    height: 48px
                  fields:
                    - type: selectAll
                      isLazyLoad: true
                      mode: multiple
                      label: Country
                      name: nationCode
                      placeholder: Select Country
                      _options:
                        transform: >-
                          $nationsList($.extend.limit, $.extend.page,
                          $.extend.search, $now())
                    - type: selectAll
                      isLazyLoad: true
                      mode: multiple
                      label: Region
                      name: religionCode
                      placeholder: Select Region
                      _options:
                        transform: >-
                          $regionsFullList($.extend.limit, $.extend.page,
                          $.extend.search, $now())
                    - type: selectAll
                      isLazyLoad: true
                      mode: multiple
                      label: Location
                      name: location
                      placeholder: Select Location
                      _options:
                        transform: >-
                          $locationsList($.extend.limit, $.extend.page,
                          $.extend.search, $now())
              filterTableConfig:
                fields:
                  - type: group
                    collapse: false
                    label: Employee Details
                    fieldGroupTitleStyle:
                      height: 48px
                    hostStyle:
                      marginTop: '-12px'
                    fields:
                      - type: selectAll
                        isLazyLoad: true
                        name: employeeId
                        label: Employee
                        placeholder: Select Employee (ID; Name)
                        _options:
                          transform: >-
                            $personalsList($.extend.limit, $.extend.page,
                            $.extend.search, $now())
                        mode: multiple
                  - type: group
                    collapse: false
                    label: Company Information
                    hostStyle:
                      borderTop: '1px solid #e0e0e0'
                    fieldGroupTitleStyle:
                      height: 48px
                    fields:
                      - type: selectAll
                        isLazyLoad: true
                        mode: multiple
                        label: Company
                        name: company
                        placeholder: Select Company
                        _options:
                          transform: >-
                            $companiesList($.extend.limit, $.extend.page,
                            $.extend.search, $now())
                      - type: selectAll
                        isLazyLoad: true
                        mode: multiple
                        label: Legal Entity
                        name: legalEntity
                        placeholder: Select Legal Entity
                        _options:
                          transform: >-
                            $legalEntitiesFullList($.extend.limit,
                            $.extend.page, $.extend.search, $now())
                      - type: selectAll
                        isLazyLoad: true
                        mode: multiple
                        label: Business Unit
                        name: bussinessUnit
                        placeholder: Select Business Unit
                        _options:
                          transform: >-
                            $businessunitsFullList($.extend.limit,
                            $.extend.page, $.extend.search, $now())
                      - type: selectAll
                        isLazyLoad: true
                        mode: multiple
                        label: Division
                        name: division
                        placeholder: Select Division
                        _options:
                          transform: >-
                            $divisionsFullList($.extend.limit, $.extend.page,
                            $.extend.search, $now())
                      - type: selectAll
                        isLazyLoad: true
                        mode: multiple
                        label: Department
                        name: department
                        placeholder: Select Department
                        _options:
                          transform: >-
                            $departmentsList($.extend.limit, $.extend.page,
                            $.extend.search, $now())
                filterMapping:
                  - field: EmpIdErn
                    operator: $in
                    valueField: employeeId.(EmpIdErn)
                  - field: companyCode
                    operator: $in
                    valueField: company.(value)
                  - field: legalEntityCode
                    operator: $in
                    valueField: legalEntity.(value)
                  - field: businessUnitCode
                    operator: $in
                    valueField: bussinessUnit.(value)
                  - field: divisionCode
                    operator: $in
                    valueField: division.(value)
                  - field: departmentCode
                    operator: $in
                    valueField: department.(value)
              filterMapping:
                - field: employeeId
                  valueField: employeeId
                - field: employeeLevelCode
                  operator: $in
                  valueField: employeeLevelId.(value)
                - field: title
                  operator: $in
                  valueField: title.(value)
                - field: careerBandCode
                  operator: $in
                  valueField: careerBandCode.(value)
                - field: EmployeeTypeId
                  operator: $in
                  valueField: EmployeeTypeId.(value)
                - field: company
                  operator: $in
                  valueField: company.(value)
                - field: legalEntity
                  operator: $in
                  valueField: legalEntity.(value)
                - field: bussinessUnit
                  operator: $in
                  valueField: bussinessUnit.(value)
                - field: division
                  operator: $in
                  valueField: division.(value)
                - field: department
                  operator: $in
                  valueField: department.(value)
                - field: nationCode
                  operator: $in
                  valueField: nationCode.(value)
                - field: location
                  operator: $in
                  valueField: location.(value)
                - field: religionCode
                  operator: $in
                  valueField: religionCode.(value)
              fields:
                - name: employeeId
                  label: Employee ID
                  type: string
                  width: 10
                - name: employeeRecordNumber
                  label: Record ID
                  type: string
                  width: 10
                - name: fullName
                  label: Name
                  type: string
                  width: 13
                - name: companyName
                  label: Company
                  type: string
                  width: 13
                - name: legalEntityName
                  label: Legal Entity
                  type: string
                  width: 13
                - name: businessUnitName
                  label: Business Unit
                  type: string
                  width: 13
                - name: divisionName
                  label: Division
                  type: string
                  width: 13
                - name: departmentName
                  label: Department
                  type: string
                  width: 13
              _value:
                transform: >-
                  $map($.variables._listEmpView, function($item) {$merge([$item,
                  {'EmpIdErn': $item.employeeId & '-' &
                  $string($item.employeeRecordNumber)}])})[]
        - type: tableSelect
          _dataSourceRequestStatus: _listEmpView
          name: emplyeeTable
          required: true
          mapRowName:
            - employeeId
            - employeeRecordNumber
          showToolTable: false
          readOnly: true
          _condition:
            transform: $.fields.ruleCode = 'NVS' and $.extend.formType = 'view'
          arrayOptions:
            canChangeSize: false
            isCheckboxTable: false
          fields:
            - type: text
              label: Employee ID
              name: employeeId
              width: 140px
              height: 48px
            - type: text
              label: Record ID
              name: employeeRecordNumber
              height: 48px
              width: 140px
            - type: text
              label: Name
              name: fullName
              width: 200px
            - type: text
              label: Company
              name: companyName
              width: 160px
            - type: text
              label: Legal Entity
              name: legalEntityName
              width: 160px
            - type: text
              label: Bussiness Unit
              name: businessUnitName
              width: 160px
            - type: text
              label: Division
              name: divisionName
              width: 160px
            - type: text
              label: Department
              name: departmentName
              width: 160px
          _value:
            transform: $.variables._listEmpView
  footer:
    create: true
    createdBy: creator
    createdOn: createdTime
    update: true
    updatedBy: editor
    updatedOn: editedTime
  sources:
    personalsList:
      uri: '"/api/personals"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'filter':
        [{'field':'search','operator': '$eq','value':$.search}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $distinct($map($.data, function($item) {{'label': $item.employeeId & ' -
        ' & $item.employeeRecordNumber & ' - ' & $item.name , 'value':
        $item.employeeId & ' - ' & $item.employeeRecordNumber, 'empid':
        $item.employeeId, 'ern': $item.employeeRecordNumber, 'EmpIdErn':
        $item.employeeId & '-' & $string($item.employeeRecordNumber)  }})[])
      disabledCache: true
      params:
        - limit
        - page
        - search
    personalsFullInforList:
      uri: '"/api/personals"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page,'search':
        $.search,'filter':$append($.filter,[{'field':'status','operator':'$eq','value':true},{'field':'effectiveDate','operator':'$eq','value':$.effectiveDate}])}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        {'data': $map($.data, function($item) {$merge([$item, {'EmpIdErn':
        $item.employeeId & '-' & $string($item.employeeRecordNumber),
        'companyCode': $item.company,  'legalEntityCode': $item.legalEntity, 
        'departmentCode': $item.department,  'divisionCode': $item.division,
        'businessUnitCode': $item.businessUnit}])})[], 'count': $.count,
        'total': $.total, 'page': $.page, 'pageCount': $.pageCount}
      disabledCache: true
      params:
        - limit
        - page
        - search
        - filter
    employeeLevelsList:
      uri: '"/api/picklists/EMPLEVEL/values/pagination"'
      queryTransform: '{''limit'': $.limit, ''page'': $.page, ''search'': $.search}'
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    jobTitlesList:
      uri: '"/api/job-codes/get-by"'
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true},
        {'field':'effectiveDate','operator': '$eq','value': $.effectiveDate},
        {'field':'companyCode','operator': '$eq','value': $.companyCode}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
        - companyCode
    employeeTypesList:
      uri: '"/api/employee-sub-groups"'
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true},
        {'field':'effectiveDate','operator': '$eq','value': $.effectiveDate}], 
        'sort': [{'field':'longName', 'order':'ascend'}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
    dataAreaParams:
      uri: '"/api/data-area-params"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default, 'value':
        $lowercase($item.code), 'id':$item.id, 'sort': $item.sort }})[]
      disabledCache: true
    companiesList:
      uri: '"/api/companies/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'search': $.search,'filter':
        [{'field':'status', 'operator': '$eq','value':true},
        {'field':'effectiveDate','operator': '$eq','value': $.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
    get_legalTree:
      uri: '"/api/legal-entities/get-by-children-with-tree"'
      queryTransform: >-
        {'filter': [{'field':'status','operator': '$eq','value':true},
        {'field':'companyCode','operator': '$eq','value': $.companyCode }]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $.data
      disabledCache: true
      params:
        - companyCode
    get_businessUnitTree:
      uri: '"/api/business-units/get-by-children-with-tree"'
      queryTransform: >-
        {'filter': [{'field':'status','operator': '$eq','value':true},
        {'field':'companyCode','operator': '$eq','value': $.companyCode },
        {'field':'legalEntityCode','operator': '$eq','value': $.legalEntityCode
        }]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $.data
      disabledCache: true
      params:
        - companyCode
        - legalEntityCode
    get_divisionTree:
      uri: '"/api/divisions/get-by-children-with-tree"'
      queryTransform: >-
        {'filter': [{'field':'status','operator': '$eq','value':true},
        {'field':'companyCode','operator': '$eq','value': $.companyCode },
        {'field':'legalEntityCode','operator': '$eq','value': $.legalEntityCode
        }, {'field':'businessUnitCode','operator': '$eq','value':
        $.businessUnitCode }]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $.data
      disabledCache: true
      params:
        - companyCode
        - legalEntityCode
        - businessUnitCode
    departmentsTree:
      uri: '"/api/departments/get-by-children-with-tree"'
      queryTransform: >-
        {'filter': [{'field':'status','operator': '$eq','value':true},
        {'field':'LegalEntityCodes','operator': '$eq','value': $.legalCode },
        {'field':'BusinessUnitCodes','operator': '$eq','value':
        $.businessUnitCode }, {'field':'DivisionCodes','operator':
        '$eq','value': $.divisionCode }, {'field':'effectiveDate','operator':
        '$eq','value': $.effectiveDate}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $.data
      disabledCache: true
      params:
        - effectiveDate
        - legalCode
        - businessUnitCode
        - divisionCode
    nationsList:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    levelsList:
      uri: '"/api/picklists/EMPLEVEL/values/pagination"'
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
    regionsList:
      uri: '"/api/picklists/REGIONS/values/pagination"'
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'search': $.search, 'filter': [
        {'field':'linkCatalogDataCode','operator':'$in','value': $.countries} ]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - countries
    regionsFullList:
      uri: '"/api/picklists/REGIONS/values/pagination"'
      queryTransform: '{''limit'': $.limit, ''page'': $.page, ''search'': $.search}'
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    locationsList:
      uri: '"/api/locations/get-by"'
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true},
        {'field':'effectiveDate','operator': '$eq','value': $.effectiveDate},
        {'field':'region','operator': '$in','value': $.region},
        {'field':'companyCode','operator': '$eq','value': $.company}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
        - company
        - region
    departmentsList:
      uri: '"/api/departments/get-by"'
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true},
        {'field':'effectiveDate','operator': '$eq','value': $.effectiveDate}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
    businessunitsFullList:
      uri: '"/api/business-units/get-by"'
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true},
        {'field':'effectiveDate','operator': '$eq','value': $.effectiveDate}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
    divisionsFullList:
      uri: '"/api/divisions/get-by"'
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true},
        {'field':'effectiveDate','operator': '$eq','value': $.effectiveDate}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
    legalEntitiesFullList:
      uri: '"/api/legal-entities/get-by"'
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true},
        {'field':'effectiveDate','operator': '$eq','value': $.effectiveDate}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
    jobsList:
      uri: '"/api/job-codes/get-by"'
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true},
        {'field':'effectiveDate','operator': '$eq','value': $.effectiveDate}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
    bandsList:
      uri: '"/api/bands/get-by"'
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true},
        {'field':'effectiveDate','operator': '$eq','value': $.effectiveDate}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
    personalsListById:
      uri: '"/api/personals"'
      queryTransform: >-
        {'limit': 9999, 'filter': [{'operator': '$or','value': $map($.emp,
        function($item) {{'operator': '$and', 'value': [    {'field':
        'employeeId', 'operator': '$eq', 'value': $item.value},     {'field':
        'employeeRecordNumber', 'operator': '$eq', 'value': $item.ern}]}})[]},
        {'field':'effectiveDate','operator': '$eq','value': $.effectiveDate}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $.data
      disabledCache: true
      params:
        - emp
        - effectiveDate
    employeeGroupsList:
      uri: '"/api/employee-sub-groups"'
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true},
        {'field':'effectiveDate','operator': '$eq','value': $.effectiveDate}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
    payGroupsList:
      uri: '"/api/pay-group-structures"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'search': $.search, 'filter':
        [{'field':'companyCode','operator': '$in','value': $.companyCode},
        {'field':'effectiveDate','operator': '$lte','value': $.effectiveDate},
        {'field':'countryCode','operator':'$in','value':$.countryCode}] }
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - companyCode
        - countryCode
        - effectiveDate
    grouptaxsettlementsList:
      uri: '"/api/group-tax-settlement"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'search': $.search, 'filter':
        [{'field':'effectiveDateTo','operator':'$eq','value':'NULL'},{'field':'countryCode','operator':'$in','value':$.countryCode},
        {'field':'effectiveDateTo','operator':'$eq','value':'NULL'},{'field':'effectiveDateTo','operator':'$gt','value':$.effectiveDate}]
        }
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - countryCode
        - effectiveDate
  variables:
    _permissionsWithFilter:
      transform: >-
        [{'value': 'CN', 'label': 'Individual'}, {'value': 'CTTT', 'label':
        'Direct superior'}, {'value': 'CTGT', 'label': 'Indirect superior'},
        {'value': 'ORG', 'label': 'Structure'}, {'value': 'NVS', 'label':
        'Employee list'}]
    _structurePermissionsLevelList:
      transform: >-
        [{'value': 'Group', 'label': 'Group'}, {'value': 'Company', 'label':
        'Company'}, {'value': 'Legal Entity', 'label': 'Legal Entity'},
        {'value': 'Business Unit', 'label': 'Business Unit'}, {'value':
        'Division', 'label': 'Division'}, {'value': 'Department', 'label':
        'Department'}]
    _filteredPermissionsWith:
      transform: >-
        ( $.fields.notDecentralizeYourself = true or $.fields.notAuthorized =
        true ? [$filter($.variables._permissionsWithFilter, function($v, $i, $a)
        {$v.value in ['ORG', 'NVS'] })] : $.variables._permissionsWithFilter )
    _selectedCompanyCode:
      transform: >-
        $not($exists($.fields.dataAreaDetails.company)) ? '_setSelectValueNull'
        : $exists($.fields.dataAreaDetails.company.value[]) ?
        $.fields.dataAreaDetails.company.value[] :
        $.fields.dataAreaDetails.company.value
    _selected_company_dataForm:
      transform: >-
        $not($exists($.fields.dataAreaDetails.company)) ? '' :
        $exists($.fields.dataAreaDetails.company.value[]) ?
        $.fields.dataAreaDetails.company.value[] :
        $.fields.dataAreaDetails.company.value
    _dataAreaParams:
      transform: $dataAreaParams()
    _dataAreaTransform:
      transform: >-
        (    $structureLevel := $.extend.defaultValue.structureLevel;   
        $list_darea_param := $.fields.dataAreaParams;    $list_data_darea :=
        $.extend.defaultValue.dataAreaDetails;    $get_listValue :=
        function($param){        $filter($list_data_darea, function($item){
        $item.dataAreaParamId = $string($param.id)})    };   
        $merge($map($list_darea_param, function($item, $i) {        (           
        $value := $item.value = 'company' ?                 $structureLevel =
        'Company' ?                     $map($get_listValue($item),
        function($i){ {'value': $i.dataAreaParamValue, 'label':
        $i.paramValueDisplay} })[] :                   
        $map($get_listValue($item), function($i){ {'value':
        $i.dataAreaParamValue, 'label': $i.paramValueDisplay} })
        :                $map($get_listValue($item), function($i){ {'value':
        $i.dataAreaParamValue, 'label': $i.paramValueDisplay} })[];           
        $count($get_listValue($item)) > 0 ? {$item.value : $value }        )   
        })))
    _dataAreaTransformView:
      transform: >-
        (    $exists($.extend.defaultValue.dataAreaDetails)    ?    
        $merge(        [            $map($.extend.defaultValue.dataAreaDetails,
        function($data){                (                    $param :=
        $single($.fields.dataAreaParams, function($item){$string($item.id) =
        $string($data.dataAreaParamId)});                    $value :=
        $filter($.extend.defaultValue.dataAreaDetails, function($item){
        $string($param.id) =
        $string($item.dataAreaParamId)});                    $count($param) > 0
        ?                    {                        $param.value: 
        $param.value in ['company','legal', 'businessunit',
        'division']                        ?                       
        $boolean($value.paramValueDisplay)  ? $value.paramValueDisplay :
        $value.dataAreaParamValue                       
        :                        $map($value[], function($v){  {'label':
        $boolean($v.paramValueDisplay)  ? $v.paramValueDisplay : '' , 'value':
        ($v.dataAreaParamValue)}  })[],                        'structureLevel':
        $.extend.defaultValue.structureLevel                    }               
        )            })        ]    ) :     {        'structureLevel':
        $.extend.defaultValue.structureLevel,        'company': 'All Company'   
        })
    _listEmpView:
      transform: >-
        ($.variables._dataAreaTransform;
        $count($.variables._dataAreaTransform.employee) > 0 ?
        $.extend.defaultValue.employees : [])
    _listEmpViewFilter:
      transform: >-
        $personalsFullInforList($.extend.limit, $.extend.page, $.extend.search,
        $append($.extend.filter[0].field = 'employeeId' ? [{'operator':
        '$or','value': $map($.extend.filter[0].value, function($item)
        {{'operator': '$and', 'value': [    {'field': 'employeeId', 'operator':
        '$eq', 'value': $item.empid},     {'field': 'employeeRecordNumber',
        'operator': '$eq', 'value': $item.ern}]}})[]}] : [],
        $.extend.filter[0].field = 'employeeId' ? $filter($.extend.filter,
        function ($v, $i) {$i != 0}) : $.extend.filter ) )
    _legalTree:
      transform: >-
        $.extend.formType = 'create' and
        $not($isNilorEmpty($.fields.dataAreaDetails.company))    ?    
        $get_legalTree($.variables._selectedCompanyCode)     :    
        $.extend.formType = 'edit' and
        $not($isNilorEmpty($.fields.dataAreaDetails.company.value))       
        ?         $get_legalTree($.variables._selectedCompanyCode)        
        :         []
    _businessUnitTree:
      transform: >-
        $.extend.formType = 'create' and
        $not($isNilorEmpty($.fields.dataAreaDetails.company)) ?       
        (            $.fields.dataAreaDetails._structureLevel = 'Department'
        ?                (                   
        $not($isNilorEmpty($.fields.dataAreaDetails.legal)) ?                   
        $get_businessUnitTree($.variables._selectedCompanyCode,
        $.fields.dataAreaDetails.legal)                    :                   
        []                )                :               
        $get_businessUnitTree($.variables._selectedCompanyCode,
        $.fields.dataAreaDetails.legal)        )         :        
        $.extend.formType = 'edit' ?            
        $get_businessUnitTree($.variables._selectedCompanyCode,
        $.fields.dataAreaDetails.legal)            :             []
    _divisionTree:
      transform: >-
        $.extend.formType = 'create' and
        $exists($.fields.dataAreaDetails.company) and
        $not($isNilorEmpty($.fields.dataAreaDetails.businessunit))     ?    
        $get_divisionTree($.variables._selectedCompanyCode,
        $.fields.dataAreaDetails.legal,
        $.fields.dataAreaDetails.businessunit)     :     $.extend.formType =
        'edit' and
        $not($isNilorEmpty($.fields.dataAreaDetails.businessunit))        
        ?         $get_divisionTree($.variables._selectedCompanyCode,
        $.fields.dataAreaDetails.legal,
        $.fields.dataAreaDetails.businessunit)         :         []
    _departmentsTree:
      transform: >-
        $isNilorEmpty($.fields.dataAreaDetails.legal) and
        $isNilorEmpty($.fields.dataAreaDetails.division) and
        $isNilorEmpty($.fields.dataAreaDetails.businessunit) ? [] :
        $departmentsTree($now(), $.fields.dataAreaDetails.legal,
        $.fields.dataAreaDetails.businessunit,
        $.fields.dataAreaDetails.division) 
    _departmentSelected:
      transform: >-
        $test($filter($.variables._departmentsTree, function($item) { $item.key
        in $.fields.dataAreaDetails.department })[])
    _selectedLegal_dept:
      transform: >-
        $map( $.variables._departmentSelected,
        function($item){$item.legalEntityCode})[$ != null][]
    _selectedBU_dept:
      transform: >-
        $map( $.variables._departmentSelected,
        function($item){$item.bussinessUnitCode})[$ != null][]
    _selectedDivision_dept:
      transform: >-
        $map( $.variables._departmentSelected,
        function($item){$item.divisionCode})[$ != null][]
    _validLegal:
      transform: >-
        $not($isNilorEmpty($.fields.dataAreaDetails.legal)) and
        $not($isNilorEmpty($.variables._departmentSelected)) and
        $count($count($.variables._departmentSelected.children) > 0 ?        
        $distinct(            $append(               
        $.variables._selectedLegal_dept,               
        $reduce(                   
        $.variables._departmentSelected.children,                   
        function($acc, $child) {                        $append($acc,
        $child.legalEntityCode)                    },                   
        []                )            )        )         :       
        $distinct($.variables._selectedLegal_dept)) <
        $count($.fields.dataAreaDetails.legal)
    _validBU:
      transform: >-
        $not($isNilorEmpty($.fields.dataAreaDetails.businessunit)) and
        $not($isNilorEmpty($.variables._departmentSelected)) and
        $count($count($.variables._departmentSelected.children) > 0 ?        
        $distinct(            $append(               
        $.variables._selectedBU_dept,                $reduce(                   
        $.variables._departmentSelected.children,                   
        function($acc, $child) {                        $append($acc,
        $child.bussinessUnitCode)                    },                   
        []                )            )        )         :       
        $distinct($.variables._selectedBU_dept)) <
        $count($.fields.dataAreaDetails.businessunit)
    _validDivison:
      transform: >-
        $not($isNilorEmpty($.fields.dataAreaDetails.division)) and
        $not($isNilorEmpty($.variables._departmentSelected)) and
        $count($count($.variables._departmentSelected.children) > 0 ?        
        $distinct(            $append(               
        $.variables._selectedDivision_dept,               
        $reduce(                   
        $.variables._departmentSelected.children,                   
        function($acc, $child) {                        $append($acc,
        $child.divisionCode)                    },                   
        []                )            )        )         :       
        $distinct($.variables._selectedDivision_dept)) <
        $count($.fields.dataAreaDetails.division)
    _handle_companyValue:
      transform: >-
        $.fields.dataAreaDetails._structureLevel = 'Group' ?
        '_setSelectValueNull' : $.extend.formType = 'create' and
        $not($.extend.isDuplicate) and
        $not($.fields.dataAreaDetails._structureLevel = 'Company') ?
        $.fields.dataAreaDetails.createCompany :    ($.extend.formType = 'edit'
        or $.extend.isDuplicate) and
        $not($.fields.dataAreaDetails._structureLevel = 'Company') ?
        $.fields.dataAreaDetails.editCompany :    $.extend.formType = 'create'
        and $not($.extend.isDuplicate) and
        $.fields.dataAreaDetails._structureLevel = 'Company' ?
        $.fields.dataAreaDetails.multiCreateCompany :    ($.extend.formType =
        'edit' or $.extend.isDuplicate) and
        $.fields.dataAreaDetails._structureLevel = 'Company' ?
        $.fields.dataAreaDetails.multiEditCompany : '_setSelectValueNull'
    _get_missingLegal:
      transform: >-
        $not($isNilorEmpty($.fields.dataAreaDetails.legal)) ? (    $difference
        := function($list1, $list2) {    $filter($list2, function($item)
        {        $not($item in $list1)    })[]    };   
        $join($difference($.variables._selectedLegal_dept,
        $.fields.dataAreaDetails.legal), ','))
    _get_missingBU:
      transform: >-
        $not($isNilorEmpty($.fields.dataAreaDetails.businessunit)) ? (   
        $difference := function($list1, $list2) {    $filter($list2,
        function($item) {        $not($item in $list1)    })[]    };   
        $join($difference($.variables._selectedBU_dept,
        $.fields.dataAreaDetails.businessunit), ','))
    _get_missingDiv:
      transform: >-
        $not($isNilorEmpty($.fields.dataAreaDetails.division)) ? (   
        $difference := function($list1, $list2) {    $filter($list2,
        function($item) {        $not($item in $list1)    }) []   };   
        $join($difference($.variables._selectedDivision_dept,
        $.fields.dataAreaDetails.division), ','))
filter_config:
  fields:
    - labelType: type-grid
      name: code
      label: Code
      type: text
      placeholder: Enter Code
    - type: text
      name: name
      label: Name
      labelType: type-grid
      placeholder: Enter Name
    - type: selectAll
      labelType: type-grid
      name: companyCode
      outputValue: value
      label: Company
      mode: multiple
      isLazyLoad: true
      placeholder: Select company
      _options:
        transform: $companiesList($.extend.limit, $.extend.page, $.extend.search, $now())
    - type: textarea
      name: description
      label: Description
      labelType: type-grid
      placeholder: Enter description
    - type: group
      labelType: type-row
      label: Permission
      fieldGroupContentStyle:
        display: flex
        gap: 4px
        alignItems: center
      fields:
        - type: checkbox
          name: notAuthorized
          label: Not Permitted
          hiddenLabel: true
        - type: checkbox
          name: notDecentralizeYourself
          label: Not Assign Permission To Yourself
          hiddenLabel: true
    - type: radio
      labelType: type-grid
      name: status
      label: Status
      value: ''
      radio:
        - label: All
          value: ''
        - label: Active
          value: true
        - label: Inactive
          value: false
      output: value
    - type: selectAll
      labelType: type-grid
      name: ruleCode
      label: Permission Based On
      mode: multiple
      outputValue: value
      placeholder: Select Permission Based On
      _options:
        transform: $.variables._filteredPermissionsWith
      selectOptions:
        alertWhenChanged: true
    - type: dateRange
      label: Last Updated On
      labelType: type-grid
      name: updatedOn
    - labelType: type-grid
      name: updatedBy
      label: Last Updated By
      type: text
      placeholder: Enter Editor
  filterMapping:
    - field: code
      operator: $cont
      valueField: code
    - field: name
      operator: $cont
      valueField: name
    - field: description
      operator: $cont
      valueField: description
    - field: companyCode
      operator: $in
      valueField: companyCode
    - field: ruleCode
      operator: $in
      valueField: ruleCode
    - field: status
      operator: $eq
      valueField: status
    - field: editor
      operator: $in
      valueField: updatedBy
    - field: editedOn
      operator: $between
      valueField: updatedOn
    - field: permissionWith
      operator: $in
      valueField: permissionWith
    - field: notAuthorized
      operator: $eq
      valueField: notAuthorized
    - field: notDecentralizeYourself
      operator: $eq
      valueField: notDecentralizeYourself
  sources:
    workinghoursList:
      uri: '"/api/ca-working-hours/by"'
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default, 'value':
        $item.code}})
      disabledCache: true
    groupsList:
      uri: '"/api/groups/by"'
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default, 'value':
        $item.id}})
      disabledCache: true
    companiesList:
      uri: '"/api/companies/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'search': $.search,'filter':
        [{'field':'status', 'operator': '$eq','value':true},
        {'field':'effectiveDate','operator': '$eq','value': $.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
  variables:
    _permissionsWithFilter:
      transform: >-
        [{'value': 'CN', 'label': 'Individual'}, {'value': 'CTTT', 'label':
        'Direct superior'}, {'value': 'CTGT', 'label': 'Indirect superior'},
        {'value': 'ORG', 'label': 'Structure'}, {'value': 'NVS', 'label':
        'Employee list'}]
    _filteredPermissionsWith:
      transform: >-
        ( $.fields.notDecentralizeYourself = true or $.fields.notAuthorized =
        true ? [$filter($.variables._permissionsWithFilter, function($v, $i, $a)
        {$v.value in ['ORG', 'NVS'] })] : $.variables._permissionsWithFilter )
    _companyList:
      transform: $companiesList($.fields.effectiveDate, $.variables._selectedGroup.id)
layout_options:
  duplicate_value_transform:
    fields:
      - code
    transform: ''''''
  tool_table:
    - id: import
      icon: icon-upload-simple
      href: /GE/HR.FS.FR.092
    - id: export
      icon: icon-download-simple
  link_redirect: /General/General-import-and-export-data
  show_detail_history: false
  is_new_dynamic_form: true
  hide_action_row: true
  delete_multi_items: true
  custom_delete_body: $map($.data, function($item){$item.id})[]
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    title: Edit
    icon: pencil
  - id: duplicate
    title: Duplicate
    icon: icon-copy-bold
    group: null
  - id: delete
    title: Delete
    icon: trash
    group: null
backend_url: /api/admin-data-areas
screen_name: null
layout_options__actions_many: null
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: companyCode
    defaultName: CompanyCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Manage Data Zone-Based Permission - Admin
  parent:
    title: Function list
