id: TS.FS.FR.069
status: draft
sort: 263
user_created: 27c6db8f-5244-4827-bfd9-df7fa8d185f0
date_created: '2024-07-12T04:06:43.934Z'
user_updated: abd14398-1d53-4e6f-9834-26328f0ebde0
date_updated: '2025-07-15T04:42:50.142Z'
title: Setup Holiday Calendar
requirement:
  time: 1720757156893
  blocks:
    - id: Xr0dZFDBDC
      type: paragraph
      data:
        text: Setup Holiday Calendar
  version: 2.29.1
screen_design: null
module: TS
local_fields:
  - code: code
    pinned: false
    title: Setup Code
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    options__tabular__column_width: 10
    options__tabular__align: left
  - code: nationName
    pinned: false
    title: Country
    description: Load dữ liệu Quốc gia theo dữ liệu đã tạo
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 10
    options__tabular__align: left
  - code: companyName
    pinned: false
    title: Company
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 10
    options__tabular__align: left
  - code: shortName
    title: Short Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
  - code: name
    title: Long Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
  - code: effectiveDate
    pinned: false
    title: Effective Date
    data_type:
      key: dd/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/YYYY
      collection: field_types
    options__tabular__column_width: 10
    options__tabular__align: center
  - code: status
    title: Status
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: Boolean Tag
      collection: field_types
  - code: note
    title: Note
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
  - code: updatedBy
    title: Last Updated By
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 10
    options__tabular__align: left
  - code: updatedAt
    title: Last Updated On
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
    options__tabular__column_width: 10
    options__tabular__align: center
mock_data:
  - code: '00000001'
    countryName: Việt Nam
    group: '00000001'
    groupName: FPT
    company: '00000001'
    companyName: FPT Corporation
    holidayCalendar: '00000001'
    holidayCalendarName: Lịch nghỉ lễ FPT Corporation(00000001)
    effectiveDate: 2024/12/31
    creator: Phương Bùi
    createTime: 01/01/2024
    lastEditer: Khánh Vy
    lastEditTime: 01/04/2024
    holidayInformation:
      - code: '00000001'
        countryName: Việt Nam
        group: FPT
        company: FPT Corporation
        shortName: QK
        longName: Quốc Khánh
        typeOfHoliday: Ngày lễ
        holidayStartDate: 01/09
        holidayEndDate: 02/09
        effectiveDate: 01/04/2024
        onCycle: Có
        status: Sử dụng
        creator: Phương Bùi
        createTime: 01/01/2024
        lastEditer: Khánh Vy
        lastEditTime: 01/04/2024
      - code: '00000002'
        countryName: Việt Nam
        group: FPT
        company: FPT Corporation
        shortName: QTLD
        longName: Quốc tế lao động
        typeOfHoliday: Ngày lễ
        holidayStartDate: 01/05
        holidayEndDate: 01/05
        effectiveDate: 01/04/2024
        onCycle: Có
        status: Sử dụng
        creator: Phương Bùi
        createTime: 01/01/2024
        lastEditer: Khánh Vy
        lastEditTime: 01/04/2024
      - code: '00000003'
        countryName: Việt Nam
        group: FPT
        company: FPT Corporation
        shortName: NBQTLD
        longName: Nghỉ bù Quốc tế lao động
        typeOfHoliday: Ngày nghỉ bù lễ
        holidayStartDate: 02/05
        holidayEndDate: 02/05
        effectiveDate: 01/04/2024
        onCycle: Có
        status: Sử dụng
        creator: Phương Bùi
        createTime: 01/01/2024
        lastEditer: Khánh Vy
        lastEditTime: 01/04/2024
      - code: '00000004'
        countryName: Việt Nam
        group: FPT
        company: FPT Corporation
        shortName: SNCT
        longName: Nghỉ sinh nhật công ty
        typeOfHoliday: Ngày nghỉ có hưởng lương
        holidayStartDate: 13/09
        holidayEndDate: 13/09
        effectiveDate: 01/04/2024
        onCycle: Có
        status: Không sử dụng
        creator: Phương Bùi
        createTime: 01/01/2024
        lastEditer: Khánh Vy
        lastEditTime: 01/04/2024
  - code: '00000002'
    countryName: Việt Nam
    group: '00000001'
    groupName: FPT
    company: '00000002'
    companyName: FPT SOFTWARE
    holidayCalendar: '00000002'
    holidayCalendarName: Lịch nghỉ lễ FPT SOFTWARE(00000002)
    effectiveDate: 2024/12/31
    creator: Phương Bùi
    createTime: 09/05/2024
    lastEditer: Khánh Vy
    lastEditTime: 09/05/2024
    holidayInformation: []
  - code: '00000003'
    countryName: Việt Nam
    group: '00000001'
    groupName: FPT
    company: '00000003'
    companyName: FPT IS
    holidayCalendar: '00000002'
    holidayCalendarName: Lịch nghỉ lễ FPT IS(00000003)
    effectiveDate: 2024/12/31
    creator: Phương Bùi
    createTime: 09/05/2024
    lastEditer: Khánh Vy
    lastEditTime: 09/05/2024
    holidayInformation: []
local_buttons: null
layout: layout-table
form_config:
  formTitle:
    create: Add New Holiday Calendar
    edit: Edit Holiday Calendar Settings
  formSize:
    create: large
    edit: large
    view: large
    proceed: large
  fields:
    - type: group
      n_cols: 3
      _condition:
        transform: $.extend.formType !='view'
      fields:
        - type: select
          name: nationId
          label: Country
          placeholder: Select Country
          outputValue: value
          _select:
            transform: $nationsList()
        - type: select
          name: companyId
          label: Company
          placeholder: Select Company
          outputValue: value
          _select:
            transform: $.variables._companyList
        - type: text
          name: code
          label: Setup Code
          disabled: true
          placeholder: System-generated
        - type: translation
          name: shortName
          label: Short Name
          placeholder: Enter Short Name
          validators:
            - type: required
        - type: translation
          name: name
          label: Long Name
          placeholder: Enter Long Name
          validators:
            - type: required
        - type: dateRange
          name: effectiveDate
          label: Effective Date
          placeholder: dd/MM/yyyy
          mode: date-picker
          validators:
            - type: required
          _value:
            transform: >-
              $.extend.formType = 'create' ?
              $not($exists($.fields.effectiveDate)) ?  $now()
          setting:
            format: dd/MM/yyyy
            type: date
        - name: status
          label: Status
          type: radio
          value: true
          outputValue: value
          radio:
            - label: Active
              value: true
            - label: Inactive
              value: false
    - type: text
      name: code
      label: Setup Code
      disabled: true
      _condition:
        transform: $.extend.formType = 'view'
      placeholder: Select Setup code
    - type: text
      name: nationName
      _condition:
        transform: $.extend.formType = 'view'
      label: Country
    - type: text
      name: companyName
      _condition:
        transform: $.extend.formType = 'view'
      label: Company
    - type: translation
      name: shortName
      _condition:
        transform: $.extend.formType = 'view'
      label: Short Name
    - type: translation
      name: name
      _condition:
        transform: $.extend.formType = 'view'
      label: Long Name
    - name: note
      label: Note
      type: translationTextArea
      validators:
        - type: maxLength
          args: '1000'
          text: Note should not exceed 1000 characters.
      textarea:
        autoSize:
          minRows: 3
        maxCharCount: 1000
      placeholder: Enter Note
    - type: tableSelect
      name: TSSettingHolidayCalendar_Detail
      label: Holiday Information
      titleTableForm: true
      _condition:
        transform: $.extend.formType != 'view'
      _optionList:
        transform: >-
          $caHolidaysList($.extend.limit, $.extend.page, $.extend.search,
          $test($.extend.filter), $.fields.effectiveDate, $.fields.nationId,
          $.fields.companyId)
      _value:
        transform: $.fields.caHolidayId != '' ? $.variables._caHolidaysDetails.data
      rowActions:
        delete: true
      toolTable:
        adjustDisplay: true
      modalSelect:
        title: Holiday List
      filterFields:
        - name: code
          label: Holiday Code
          type: text
        - name: shortName
          label: Short Name
          type: text
        - name: longName
          label: Long Name
          type: text
      filterMapping:
        - field: code
          operator: $cont
          valueField: code
        - field: shortName
          operator: $cont
          valueField: shortName
        - field: longName
          operator: $cont
          valueField: longName
      fields:
        - name: code
          label: Holiday Code
          type: string
        - name: nationName
          label: Country
          type: string
        - name: companyName
          label: Company
          type: string
        - name: shortName
          label: Short Name
          displayType: Translate
        - name: longName
          label: Long Name
          displayType: Translate
        - name: caHolidayTypeName
          label: Holiday Type
          type: string
        - name: holidayStartDate
          label: Holiday Start Date
          displayType: DD/MM
        - name: holidayEndDate
          label: Holiday End Date
          displayType: DD/MM
        - name: effectiveDate
          label: Effective Date
          displayType: DD/MM/YYYY
        - name: repeatEveryYears
          label: Repeats Annually
          displayType: YesNo
        - name: status
          label: Status
          displayType: Boolean Tag
    - type: text
      name: caHolidayId
      label: caHolidayId
      unvisible: true
    - type: group
      label: Holiday Information
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - type: tableSelect
          name: TSSettingHolidayCalendar_Detail
          _value:
            transform: $.extend.defaultValue.TSSettingHolidayCalendar_Detail
          showCalendar: true
          filterFields:
            - name: code
              label: Holiday Code
              type: text
            - name: shortName
              label: Short Name
              type: text
            - name: longName
              label: Long Name
              type: text
          toolTable:
            adjustDisplay: true
          filterMapping:
            - field: code
              operator: $cont
              valueField: code
            - field: shortName
              operator: $cont
              valueField: shortName
            - field: longName
              operator: $cont
              valueField: longName
          fields:
            - name: code
              label: Holiday Code
            - name: nation
              label: Country
            - name: company
              label: Company
            - name: shortName
              label: Short Name
              displayType: Translate
            - name: caHolidayName
              label: Long Name
            - name: caHolidayTypeName
              label: Holiday Type
            - name: holidayStartDate
              label: Holiday Start Date
              displayType: DD/MM
            - name: holidayEndDate
              label: Holiday End Date
              displayType: DD/MM
            - name: effectiveDate
              label: Effective Date
              displayType: DD/MM/YYYY
            - name: repeatEveryYears
              label: Repeats Annually
              displayType: YesNo
            - name: status
              label: Status
              displayType: Boolean Tag
          calendarOptions:
            isActive: true
            hasModal: true
            button:
              type: link
              title: Show Working schedule
              isRightIcon: true
              rightIcon: fa-eye
            _modalTitle:
              transform: >-
                'Working Schedule: ' & $.fields.shortName.default & '(' &
                $.fields.code & ')'
            _effectiveDate:
              transform: '''2000-01-01'''
            _method:
              transform: '$exists($.fields.method) ? $.fields.method : ''week'''
            _value:
              transform: >-
                $append([], $map($.fields.TSSettingHolidayCalendar_Detail,
                function($v, $i) { {'type': 'off-shift', 'code':
                $v.shortName.default, 'title': $v.caHolidayName, 'startDate':
                $v.holidayStartDate, 'endDate': $v.holidayEndDate} }))
            legendaryOptions:
              - off-day
              - today
  sources:
    nationsList:
      uri: '"/api/picklists/COUNTRIES/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
    companiesList:
      uri: '"/api/companies"'
      queryTransform: >-
        {'page':1,'limit':10000,'filter': [{'field':'effectiveDate','operator':
        '$lte','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id}})[]
      disabledCache: true
      params:
        - effectiveDate
    caHolidaysList:
      uri: '"/api/ca-holidays/list-data"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'search': $.search, 'filter':
        $append($.filter, [{'field':'status','operator':
        '$eq','value':true},{'field':'nationId','operator':
        '$eq','value':$.nationId},{'field':'companyId','operator':
        '$eq','value':$.companyId},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}, {'field': 'checkNationId', 'operator':
        '$eq', 'value': $boolean($.nationId)}, {'field': 'checkCompanyId',
        'operator': '$eq', 'value': $boolean($.companyId)}])}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: '{''data'': $.data , ''total'': $.total}'
      disabledCache: true
      params:
        - limit
        - page
        - search
        - filter
        - effectiveDate
        - nationId
        - companyId
    caHolidaysDetails:
      uri: '"/api/ca-holidays/list-data"'
      method: GET
      queryTransform: >-
        {'page': 1, 'limit': 10000, 'filter': [{'field': 'code', 'operator':
        '$in', 'value': $.code},{'field':'status','operator':
        '$eq','value':true},{'field':'nationId','operator':
        '$eq','value':$.nationId},{'field':'companyId','operator':
        '$eq','value':$.companyId},{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: '{''data'': $.data , ''total'': $.total}'
      disabledCache: true
      params:
        - code
        - effectiveDate
        - nationId
        - companyId
  variables:
    _companyList:
      transform: $companiesList($.fields.effectiveDate)
    _caHolidaysDetails:
      transform: >-
        $caHolidaysDetails($.fields.caHolidayId, $.fields.effectiveDate,
        $.fields.nationId, $.fields.companyId)
filter_config:
  fields:
    - name: country
      label: Country
      type: selectAll
      placeholder: Enter Country
      labelType: type-grid
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $nationsList($.extend.limit, $.extend.page, $.extend.search)
    - name: company
      label: Company
      type: selectAll
      placeholder: Enter Company
      labelType: type-grid
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $companiesList($.extend.limit, $.extend.page, $.extend.search)
    - type: dateRange
      label: Effective date
      labelType: type-grid
      name: effectiveDate
      setting:
        format: dd/MM/yyyy
        type: date
    - name: code
      label: Setup Code
      type: select
      placeholder: Select Setup Code
      labelType: type-grid
      mode: multiple
      _select:
        transform: $codeList()
    - name: status
      label: Status
      type: radio
      labelType: type-grid
      value: ''
      radio:
        - label: All
          value: ''
        - label: Acative
          value: true
        - label: Inactive
          value: false
    - name: shortName
      label: Short Name
      labelType: type-grid
      type: text
    - name: name
      label: Long Name
      labelType: type-grid
      type: text
    - type: dateRange
      label: Last Updated On
      labelType: type-grid
      name: updatedAt
      setting:
        format: dd/MM/yyyy
        type: date
    - name: updatedBy
      label: Last Updated By
      type: select
      mode: multitple
      labelType: type-grid
      placeholder: Select Editor
      _select:
        transform: $userList()
  filterMapping:
    - field: nationId
      operator: $in
      valueField: country.(value)
    - field: companyId
      operator: $in
      valueField: company.(value)
    - field: effectiveDate
      operator: $between
      valueField: effectiveDate
    - field: code
      operator: $in
      valueField: code.(value)
    - field: status
      operator: $eq
      valueField: status
    - field: shortName
      operator: $cont
      valueField: shortName
    - field: name
      operator: $cont
      valueField: name
    - field: updatedBy
      operator: $in
      valueField: updatedBy.(value)
    - field: updatedAt
      operator: $between
      valueField: updatedAt
  sources:
    codeList:
      uri: '"/api/ts-setting-holiday-calendars"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.caHolidayCalendarName & '
        (' & $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
    nationsList:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    companiesList:
      uri: '"/api/companies/get-by"'
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    userList:
      uri: '"/api/internal-users"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.userName, 'value':
        $item.userName}})[]
      disabledCache: true
layout_options:
  show_dialog_form_save_add_button: true
  toolTable:
    export: true
    adjustDisplay: true
  tool_table:
    - id: export
      icon: icon-download-simple
  export_all:
    type: base_total
  hide_action_row: true
  delete_multi_items: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    title: Edit
    icon: icon-pencil-simple
    type: ghost-gray
  - id: delete
    title: Delete
    icon: icon-trash
    type: ghost-gray
backend_url: api/ts-setting-holiday-calendars
screen_name: setup-holiday-calendar
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: nationId
    defaultName: CountryCode
  - name: companyId
    defaultName: CompanyCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Setup Holiday Calendar
  parent:
    title: Configuration
