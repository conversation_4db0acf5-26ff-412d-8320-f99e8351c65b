id: TS.FS.FR.010
status: draft
sort: 204
user_created: 2aa5db12-fa54-46d6-8e05-9e421858de0c
date_created: '2024-07-03T09:46:50.551Z'
user_updated: abd14398-1d53-4e6f-9834-26328f0ebde0
date_updated: '2025-06-18T09:53:47.382Z'
title: Set Holiday Schedule For Organization
requirement:
  time: 1746516160809
  blocks:
    - id: dtKx-2L5Ow
      type: paragraph
      data:
        text: >-
          - <PERSON><PERSON> thống kiểm tra dữ liệu, không cho phép tạo mới/chỉnh sửa và cảnh
          báo khi trường thông tin “Mã lịch” trùng với “Mã lịch” của các thiết
          lập đã tồn tại trên hệ thống.&nbsp; &nbsp;
  version: 2.30.7
screen_design: null
module: TS
local_fields:
  - code: country
    title: Country
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    pinned: true
  - code: group
    title: Group
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: company
    title: Company
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: holidayCalendar
    title: Holiday Calendar
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: effectiveDate
    title: Effective Start Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
  - code: effectiveDateTo
    title: Effective End Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
  - code: legalEntity
    title: Legal Entity
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: businessUnit
    title: Business Unit
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: division
    title: Division
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: department
    title: Department
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: nationality
    title: Nationality
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: updatedBy
    title: Last Updated By
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: updatedAt
    title: Last Updated On
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
  - code: note
    title: Note
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
mock_data:
  - setupCode: '00000001'
    group: Tập đoàn FPT
    company: FSOFT
    nation: Việt Nam
    businessUnit: FPT IS
    legalEntity: Nguyễn Văn Anh
    division: Division 1
    department: Department 1
    nationality: Người Việt Nam
    holidayCalendar: Lịch nghỉ lễ FSOFT(00000001)
    effectiveDate: 03/07/2024
  - setupCode: '00000002'
    group: Tập đoàn FPT
    company: FHO
    legalEntity: Nguyễn Văn Anh
    division: Division 2
    department: Department 2
    nation: Việt Nam
    nationality: Người Việt Nam
    holidayCalendar: Lịch nghỉ lễ FTEL(00000002)
    effectiveDate: 02/07/2024
  - setupCode: '00000003'
    group: Tập đoàn FPT
    legalEntity: Nguyễn Văn Anh
    division: Division 3
    department: Department 3
    company: FTEL
    businessUnit: FPT IS
    nation: Việt Nam
    nationality: Người Việt Nam
    holidayCalendar: Lịch nghỉ lễ FIS(00000003)
    effectiveDate: 01/07/2024
local_buttons: null
layout: layout-table
form_config:
  formSize:
    create: middle
    edit: middle
    proceed: middle
    view: middle
  fields:
    - type: select
      name: countryCode
      label: Country
      clearFieldsAfterChange:
        - holidayCalendarCode
      placeholder: Select Country
      outputValue: value
      _select:
        transform: $nationsList()
    - name: group
      label: Group
      type: text
      placeholder: Enter Group
      _condition:
        transform: $.extend.formType = 'view'
    - name: company
      label: Company
      type: text
      placeholder: Enter Company
      _condition:
        transform: $.extend.formType = 'view'
    - name: legalEntity
      label: Legal Entity
      placeholder: Enter Legal Entity
      type: text
      _condition:
        transform: $.extend.formType = 'view'
    - name: businessUnit
      label: Business Unit
      placeholder: Enter Business Unit
      type: text
      _condition:
        transform: $.extend.formType = 'view'
    - name: division
      label: Division
      placeholder: Enter Division
      type: text
      _condition:
        transform: $.extend.formType = 'view'
    - name: department
      label: Department
      placeholder: Enter Department
      type: text
      _condition:
        transform: $.extend.formType = 'view'
    - type: group
      label: Structural Information
      n_cols: 2
      fieldGroupTitleStyle:
        border-top: '1px solid #dfe3e8'
      fieldGroupContentStyle:
        border-bottom: '1px solid #dfe3e8'
        padding-bottom: 16px
      collapse: false
      _condition:
        transform: $.extend.formType = 'create' or $.extend.formType = 'edit'
      fields:
        - type: select
          label: Group
          name: groupCode
          clearFieldsAfterChange:
            - holidayCalendarCode
            - companyCode
            - legalEntityCode
            - businessUnitCode
            - divisionCode
            - departmentCode
          placeholder: Select Group
          outputValue: value
          _select:
            transform: $.variables._groupsList
        - type: select
          label: Company
          name: companyCode
          clearFieldsAfterChange:
            - holidayCalendarCode
            - legalEntityCode
            - businessUnitCode
            - divisionCode
            - departmentCode
          placeholder: Select Company
          outputValue: value
          _select:
            transform: $.variables._companyList
        - type: select
          label: Legal Entity
          name: legalEntityCode
          placeholder: Select Legal Entity
          outputValue: value
          _select:
            transform: $.variables._legalEntityList
        - type: select
          label: Business Unit
          name: businessUnitCode
          clearFieldsAfterChange:
            - divisionCode
            - departmentCode
          placeholder: Select Business Unit
          outputValue: value
          _select:
            transform: $.variables._businessUnitList
        - type: select
          label: Division
          name: divisionCode
          clearFieldsAfterChange:
            - departmentCode
          placeholder: Select Division
          outputValue: value
          _select:
            transform: $.variables._divisionList
        - type: select
          label: Department
          name: departmentCode
          placeholder: Select Department
          outputValue: value
          _select:
            transform: $.variables._departmentList
    - name: nationalityCode
      label: Nationality
      type: select
      placeholder: Select Nationality
      outputValue: value
      _select:
        transform: $nationalitysList()
    - type: group
      _n_cols:
        transform: '$.extend.formType = ''view'' ? 1 : 2'
      fields:
        - type: dateRange
          name: effectiveDate
          label: Effective Start Date
          mode: date-picker
          validators:
            - type: required
          _value:
            transform: $.extend.formType = 'create' ? $now()
          setting:
            format: dd/MM/yyyy
            type: date
        - type: dateRange
          name: effectiveDateTo
          label: Effective End Date
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
            autoFill: end-off
            _disabledDate:
              transform: '{''operator'': ''$lt'', ''value'': $.fields.effectiveDate}'
          validators:
            - type: ppx-custom
              args:
                transform: >-
                  $exists($.fields.effectiveDate) and
                  $exists($.fields.effectiveDateTo) and
                  $DateDiff($DateFormat($.fields.effectiveDate, 'yyyy-MM-DD'),
                  $DateFormat($.fields.effectiveDateTo, 'yyyy-MM-DD'), 'd') > 0
              text: Effective end date must be greater than effective start date.
    - type: select
      name: holidayCalendarCode
      label: Holiday Calendar
      placeholder: Select Holiday Calendar
      _condition:
        transform: $.extend.formType = 'create' or $.extend.formType = 'edit'
      validators:
        - type: required
      outputValue: value
      _select:
        transform: >-
          $tsSettingHolidayCalendarsList($.fields.effectiveDate,
          $.fields.countryCode, $.fields.groupCode, $.fields.companyCode)
    - type: text
      name: holidayCalendar
      label: Holiday Calendar
      _condition:
        transform: $.extend.formType = 'view'
    - name: note
      type: textarea
      label: Note
      placeholder: Enter Note
      textarea:
        maxCharCount: 1000
        autoSize:
          minRows: 3
      validators:
        - type: maxLength
          args: '1000'
          text: Note should not exceed 1000 characters
  sources:
    nationsList:
      uri: '"/api/picklists/COUNTRIES/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
    groupsList:
      uri: '"/api/groups/get-by"'
      queryTransform: >-
        {'page':1,'limit':10000,'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - effectiveDate
    companiesList:
      uri: '"/api/companies/get-by"'
      queryTransform: >-
        {'page':1,'limit':10000,'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true},{'field':'groupCode','operator': '$eq','value':
        $.groupCode }]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - effectiveDate
        - groupCode
    legalEntityList:
      uri: '"/api/legal-entities/get-list"'
      queryTransform: >-
        {'page':1,'limit':10000,'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true},{'field':'companyCode','operator': '$eq','value':
        $.companyCode }]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - effectiveDate
        - companyCode
    businessUnitList:
      uri: '"/api/business-units/get-list"'
      queryTransform: >-
        {'filter': [{'field':'status','operator':
        '$eq','value':true},{'field':'companyCode','operator': '$eq','value':
        $.companyCode }, {'field':'effectiveDate','operator': '$eq','value':
        $.effectiveDate}], 'limit': 10000}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - effectiveDate
        - companyCode
    divisionList:
      uri: '"/api/divisions/get-list"'
      queryTransform: >-
        {'filter': [{'field':'status','operator':
        '$eq','value':true},{'field':'businessUnitCode','operator':
        '$eq','value': $.businessUnitCode },{'field':'companyCode','operator':
        '$eq','value': $.companyCode } ,{'field':'effectiveDate','operator':
        '$eq','value': $.effectiveDate}], 'limit':10000}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - effectiveDate
        - businessUnitCode
        - companyCode
    departmentList:
      uri: '"/api/departments/get-list"'
      queryTransform: >-
        {'page':1,'limit':10000,'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true},{'field':'divisionCode','operator': '$eq','value':
        $.divisionCode }]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code }})[]
      disabledCache: true
      params:
        - effectiveDate
        - divisionCode
    tsSettingHolidayCalendarsList:
      uri: '"/api/ts-setting-holiday-calendars/list-data"'
      method: GET
      queryTransform: >-
        {'page':1,'limit':10000,'filter': [{'field':'status','operator':
        '$eq','value':true}, {'field':'effectiveDate','operator': '$eq','value':
        $.effectiveDate}, {'field':'nationId','operator':
        '$eq','value':$.nationId}, {'field':'groupId','operator':
        '$eq','value':$.groupId}, {'field':'companyId','operator':
        '$eq','value':$.companyId}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - effectiveDate
        - nationId
        - groupId
        - companyId
    nationalitysList:
      uri: '"/api/picklists/NATIONALITY/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
  variables:
    _groupsList:
      transform: $groupsList($.fields.effectiveDate)
    _companyList:
      transform: >-
        $.fields.groupCode ? $companiesList($.fields.effectiveDate,
        $.fields.groupCode)
    _legalEntityList:
      transform: >-
        $.fields.companyCode ? $legalEntityList($.fields.effectiveDate,
        $.fields.companyCode)
    _businessUnitList:
      transform: >-
        $.fields.companyCode ? $businessUnitList($.fields.effectiveDate,
        $.fields.companyCode)
    _divisionList:
      transform: >-
        $.fields.businessUnitCode and $.fields.companyCode ?
        $divisionList($.fields.effectiveDate, $.fields.businessUnitCode,
        $.fields.companyCode)
    _departmentList:
      transform: >-
        $.fields.divisionCode ? $departmentList($.fields.effectiveDate,
        $.fields.divisionCode)
  footer:
    create: true
    update: true
    createdOn: createdAt
    updatedOn: updatedAt
    createdBy: createdBy
    updatedBy: updatedBy
filter_config:
  fields:
    - type: selectAll
      name: countryCode
      label: Country
      placeholder: Select Country
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $nationsList($.extend.limit, $.extend.page, $.extend.search)
      labelType: type-row
    - type: selectAll
      name: groupCode
      label: Group
      isLazyLoad: true
      placeholder: Select Group
      mode: multiple
      _options:
        transform: $groupsList($.extend.limit, $.extend.page, $.extend.search)
      labelType: type-row
    - type: selectAll
      name: companyCode
      label: Company
      isLazyLoad: true
      placeholder: Select Company
      mode: multiple
      _options:
        transform: $companiesList($.extend.limit, $.extend.page, $.extend.search)
      labelType: type-row
    - type: selectAll
      name: legalEntityCode
      label: Legal Entity
      isLazyLoad: true
      placeholder: Select Legal Entity
      mode: multiple
      _options:
        transform: $legalEntitiesList($.extend.limit, $.extend.page, $.extend.search)
      labelType: type-row
    - type: selectAll
      name: businessUnitCode
      label: Business Unit
      isLazyLoad: true
      placeholder: Select Business Unit
      mode: multiple
      _options:
        transform: $businessUnitsList($.extend.limit, $.extend.page, $.extend.search)
      labelType: type-row
    - type: selectAll
      name: divisionCode
      label: Division
      isLazyLoad: true
      placeholder: Select Division
      mode: multiple
      _options:
        transform: $divisionsList($.extend.limit, $.extend.page, $.extend.search)
      labelType: type-row
    - type: selectAll
      name: departmentCode
      label: Department
      isLazyLoad: true
      placeholder: Select Department
      mode: multiple
      _options:
        transform: $departmentsList($.extend.limit, $.extend.page, $.extend.search)
      labelType: type-row
    - type: selectAll
      name: nationalityCode
      label: Nationality
      placeholder: Select Nationality
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $nationalitysList($.extend.limit, $.extend.page, $.extend.search)
      labelType: type-row
    - type: dateRange
      name: effectiveDate
      label: Effective Date
      placeholder: DD/MM/yyyy
      settings:
        format: dd/MM/yyyy
      labelType: type-row
    - type: select
      name: holidayCalendarCode
      label: Holiday Calendar
      isLazyLoad: true
      placeholder: Select Holiday Calendar
      mode: multiple
      _select:
        transform: >-
          $tsSettingHolidayCalendarsList($.extend.limit, $.extend.page,
          $.extend.search)
      labelType: type-row
    - type: select
      name: createdBy
      label: Created By
      placeholder: Select Created By
      mode: multiple
      _select:
        transform: $accountList()
      labelType: type-row
    - type: dateRange
      name: createdAt
      label: Created On
      placeholder: DD/MM/yyyy
      settings:
        format: dd/MM/yyyy
      labelType: type-row
    - type: select
      name: updatedBy
      label: Last Updated By
      placeholder: Select Last Updated By
      mode: multiple
      _select:
        transform: $accountList()
      labelType: type-row
    - type: dateRange
      name: updatedAt
      label: Last Updated On
      placeholder: DD/MM/yyyy
      settings:
        format: dd/MM/yyyy
      labelType: type-row
  filterMapping:
    - field: countryCode
      operator: $in
      valueField: countryCode.(value)
    - field: groupCode
      operator: $in
      valueField: groupCode.(value)
    - field: companyCode
      operator: $in
      valueField: companyCode.(value)
    - field: legalEntityCode
      operator: $in
      valueField: legalEntityCode.(value)
    - field: businessUnitCode
      operator: $in
      valueField: businessUnitCode.(value)
    - field: divisionCode
      operator: $in
      valueField: divisionCode.(value)
    - field: departmentCode
      operator: $in
      valueField: departmentCode.(value)
    - field: nationalityCode
      operator: $in
      valueField: nationalityCode.(value)
    - field: effectiveDate
      operator: $between
      valueField: effectiveDate
    - field: holidayCalendarCode
      operator: $in
      valueField: holidayCalendarCode.(value)
    - field: createdBy
      operator: $cont
      valueField: createdBy
    - field: createdAt
      operator: $between
      valueField: createdAt
    - field: updatedBy
      operator: $cont
      valueField: updatedBy
    - field: updatedAt
      operator: $between
      valueField: updatedAt
  sources:
    nationsList:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    groupsList:
      uri: '"/api/groups"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    companiesList:
      uri: '"/api/companies"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    legalEntitiesList:
      uri: '"/api/legal-entities"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    businessUnitsList:
      uri: '"/api/business-units"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    divisionsList:
      uri: '"/api/divisions"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    departmentsList:
      uri: '"/api/departments"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    tsSettingHolidayCalendarsList:
      uri: '"/api/ts-setting-holiday-calendars"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    nationalitysList:
      uri: '"/api/picklists/NATIONALITY/values/pagination"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    accountList:
      uri: '"/api/internal-users"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.userName, 'value':
        $item.userName}})
      disabledCache: true
layout_options:
  tool_table:
    - id: export
      icon: icon-upload-simple-bold
  show_dialog_form_save_add_button: true
  show_detail_history: false
  export_all:
    type: base_total
  hide_action_row: true
  show_dialog_duplicate_button: false
  delete_multi_items: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    title: Edit
    icon: icon-pencil-simple
    type: ghost-gray
  - id: delete
    title: Delete
    icon: icon-trash
    type: ghost-gray
backend_url: /api/ts-holidays
screen_name: ts-holidays
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: countryCode
    defaultName: CountryCode
  - name: companyCode
    defaultName: CompanyCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Set Holiday Schedule For Organization
  parent:
    title: Set Up Working Hours
