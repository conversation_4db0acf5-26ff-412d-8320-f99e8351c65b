id: TS.FS.FR.011
status: draft
sort: 209
user_created: 2aa5db12-fa54-46d6-8e05-9e421858de0c
date_created: '2024-07-03T09:47:23.044Z'
user_updated: abd14398-1d53-4e6f-9834-26328f0ebde0
date_updated: '2025-06-18T09:56:37.265Z'
title: Set Holiday Schedules For Employees
requirement:
  time: 1747024154362
  blocks:
    - id: b_viqL6oDR
      type: paragraph
      data:
        text: THIẾT LẬP LỊCH NGHỈ LỄ CHO NHÂN VIÊN&nbsp;
  version: 2.30.7
screen_design: null
module: TS
local_fields:
  - code: employeeCode
    title: Employee ID
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    description: Unique identifier for the employee
    pinned: true
    show_sort: true
    extra_config: null
  - code: employeeRecordNumber
    title: Employee Record Number (ERN)
    data_type:
      key: Number
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: Record number for the employee
    options__tabular__column_width: 18
    options__tabular__align: right
  - code: employeeName
    title: Employee Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: Name of the employee
  - code: holidaySchedule
    title: Holiday Schedule
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: Schedule of holidays assigned to the employee
    options__tabular__column_width: 12
  - code: effectiveDate
    title: Effective Start Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    description: Start date of the holiday
    options__tabular__column_width: 12
  - code: effectiveDateTo
    title: Effective End Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    description: End date of the holiday
    options__tabular__column_width: 12
  - code: companyName
    title: Company
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: Company the employee works for
  - code: legalEntityName
    title: Legal Entity
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: Legal entity of the company
  - code: businessUnitName
    title: Business Unit
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: Business unit the employee belongs to
  - code: divisionName
    title: Division
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: Division within the company
  - code: departmentName
    title: Department
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: Department where the employee works
  - code: note
    title: Note
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    description: Any additional notes
  - code: updatedBy
    title: Last Updated By
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: Person who last updated the record
  - code: updatedAt
    title: Last Updated On
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
    description: Date and time the record was last updated
    options__tabular__column_width: 12
mock_data:
  - code: '00000001'
    employeeID: '0001112'
    employeeRecordNumber: '00011'
    group: Tập đoàn FPT
    company: FSOFT
    nation: Việt Nam
    businessUnit: FPT IS
    legalEntity: Nguyễn Văn Anh
    division: Division 1
    department: Department 1
    employeeName: Cao Văn An
    holidaySchedule: Lịch nghỉ lễ FSOFT(00000001)
    effectiveDate: 07/07/2024
    effectiveStartDate: 07/07/2024
    effectiveEndDate: 07/15/2024
    note: Ghi chú
  - code: '00000002'
    employeeID: '0001113'
    employeeRecordNumber: '00011'
    group: Tập đoàn FPT
    company: FSOFT
    nation: Việt Nam
    businessUnit: FPT IS
    legalEntity: Nguyễn Văn Anh
    division: Division 1
    department: Department 1
    employeeName: Cao Văn An
    holidaySchedule: Lịch nghỉ lễ FSOFT(00000002)
    effectiveDate: 01/07/2024
    effectiveStartDate: 6/6/2024
    effectiveEndDate: 7/12/2024
    note: Ghi chú
local_buttons: null
layout: layout-table
form_config:
  formSize:
    create: largex
    edit: largex
    view: small
  fields:
    - type: group
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - type: text
          label: Employee
          name: employee
          _value:
            transform: >-
              $.extend.defaultValue.employeeCode & ' - ' &
              $.extend.defaultValue.employeeRecordNumber & ' - ' &
              $.extend.defaultValue.employeeName
        - name: companyName
          label: Company
          type: text
        - name: legalEntityName
          label: Legal Entity
          type: text
        - name: businessUnitName
          label: Business Unit
          type: text
        - name: divisionName
          label: Division
          type: text
        - name: departmentName
          label: Department
          type: text
        - name: effectiveDate
          label: Effective Start Date
          type: dateRange
          mode: date-picker
        - name: effectiveDateTo
          label: Effective End Date
          type: dateRange
          mode: date-picker
        - type: text
          name: holidaySchedule
          label: Holiday Schedule
        - name: note
          label: Note
          type: textarea
    - type: group
      _condition:
        transform: $.extend.formType != 'view'
      n_cols: 2
      fields:
        - type: select
          name: employee
          label: Employee
          col: 2
          clearFieldsAfterChange:
            - holidayScheduleId
          placeholder: Select Employee
          isLazyLoad: true
          validators:
            - type: required
          _select:
            transform: $employeesList($.extend.limit, $.extend.page, $.extend.search)
          _condition:
            transform: $.extend.formType = 'create'
          outputValue: value
        - type: text
          label: Employee
          disabled: true
          col: 2
          _value:
            transform: >-
              $.extend.defaultValue.employeeCode & ' - ' &
              $.extend.defaultValue.employeeRecordNumber & ' - ' &
              $.extend.defaultValue.employeeName
          _condition:
            transform: $.extend.formType = 'edit'
        - type: text
          name: employeeCode
          label: Employee
          unvisible: true
          _value:
            transform: $.fields.employee.employeeId
        - type: number
          name: employeeRecordNumber
          unvisible: true
          _value:
            transform: $string($.fields.employee.employeeRecordNumber)
        - type: text
          name: employeeIdObj
          unvisible: true
          _value:
            transform: $.variables._employeeId
        - type: text
          name: selectedJobData
          unvisible: true
          dependantField: $.fields.employee
          _value:
            transform: >-
              $jobDatasList($.fields.employeeCode,
              $.fields.employeeRecordNumber)
        - name: effectiveDate
          label: Effective Start Date
          type: dateRange
          placeholder: Select Date
          setting:
            format: dd/MM/yyyy
            type: date
          mode: date-picker
          validators:
            - type: required
        - name: effectiveDateTo
          label: Effective End date
          type: dateRange
          placeholder: Select Date
          setting:
            format: dd/MM/yyyy
            type: date
          mode: date-picker
          validators:
            - type: ppx-custom
              args:
                transform: >-
                  $exists($.fields.effectiveDateTo) and $DateDiff(
                  $.fields.effectiveDateTo, $.fields.effectiveDate, 'd') < 0
              text: End date must be greater than start date
        - type: select
          name: holidayScheduleId
          label: Holiday Schedule
          placeholder: Select Holiday Schedule
          outputValue: value
          dependantField: $.fields.effectiveDate
          dependantFieldSkip: 2
          col: 2
          _select:
            transform: >-
              $holidayScheduleList($.fields.effectiveDate,
              [$map($.fields.selectedJobData, function($v){$v.companyId})])
          validators:
            - type: required
        - name: note
          label: Note
          type: textarea
          col: 2
          placeholder: Enter note
          validators:
            - type: maxLength
              args: '1000'
              text: Note should not exceed 1000 characters.
          textarea:
            autoSize:
              minRows: 3
            maxCharCount: 1000
  overview:
    dependentField: employeeIdObj
    title: Employee Detail
    border: true
    uri: >-
      /api/personals/:{employeeId}:/job-datas?filter[0]=employeeRecordNumber||$eq||:{employeeRecordNumber}:
    display:
      - key: companyName
        label: Company
      - key: legalEntityName
        label: Legal Entity
      - key: businessUnitName
        label: Business Unit
      - key: divisionName
        label: Division
      - key: departmentName
        label: Department
  footer:
    create: true
    update: true
    createdOn: createdAt
    updatedOn: updatedAt
    createdBy: createdBy
    updatedBy: updatedBy
  sources:
    employeesList:
      uri: '"/api/personals"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'filter':
        [{'field':'search','operator': '$eq','value':$.search}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label':
        $join($filter([$item.employeeId,$string($item.employeeRecordNumber),$item.fullName],
        $boolean), ' - ')  , 'value': {'employeeId': $item.employeeId,
        'employeeRecordNumber': $string($item.employeeRecordNumber),
        'jobDataId': $item.jobDataId}, 'employeeId': $item.employeeId,
        'employeeRecordNumber': $string($item.employeeRecordNumber),'jobDataId':
        $item.jobDataId}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    jobDatasList:
      uri: '"/api/personals/"  & $.employeeId &  "/job-datas"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'employeeRecordNumber','operator': '$eq','value':
        $.employeeRecordNumber}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: '$map($.data, function($item) {{''companyId'': $item.company}})[]'
      disabledCache: true
      params:
        - employeeId
        - employeeRecordNumber
    holidayScheduleList:
      uri: '"/api/ts-setting-holiday-calendars/list-data"'
      method: GET
      queryTransform: >-
        {'page':1,'filter': [{'field':'status','operator': '$eq','value':true},
        {'field':'effectiveDate','operator': '$eq','value': $.effectiveDate},
        {'field':'companyId','operator': '$eq','value':$.companyId},
        {'field':'checkNationId','operator': '$eq','value': false}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code  }})[]
      disabledCache: true
      params:
        - effectiveDate
        - companyId
  variables:
    _employeeId:
      transform: >-
        $.fields.employeeCode != '' ? {'employeeId':
        $.fields.employeeCode,'employeeRecordNumber':
        $.fields.employeeRecordNumber}
filter_config:
  fields:
    - type: selectAll
      name: employeeCode
      label: Employee
      isLazyLoad: true
      mode: multiple
      labelType: type-grid
      _options:
        transform: $employeesList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      name: company
      label: Company
      isLazyLoad: true
      mode: multiple
      _options:
        transform: $companiesList($.extend.limit, $.extend.page, $.extend.search)
      labelType: type-grid
    - type: selectAll
      name: legal
      label: Legal Entity
      isLazyLoad: true
      mode: multiple
      _options:
        transform: $legalEntitiesList($.extend.limit, $.extend.page, $.extend.search)
      labelType: type-grid
    - type: selectAll
      name: businessUnit
      label: Business Unit
      isLazyLoad: true
      mode: multiple
      _options:
        transform: $businessUnitsList($.extend.limit, $.extend.page, $.extend.search)
      labelType: type-grid
    - type: selectAll
      name: division
      label: Division
      isLazyLoad: true
      mode: multiple
      _options:
        transform: $divisionsList($.extend.limit, $.extend.page, $.extend.search)
      labelType: type-grid
    - type: selectAll
      name: departmentId
      label: Department
      isLazyLoad: true
      mode: multiple
      _options:
        transform: $departmentsList($.extend.limit, $.extend.page, $.extend.search)
      labelType: type-grid
    - name: holidayCalendarId
      label: Holiday Calendar
      isLazyLoad: true
      type: select
      mode: multiple
      labelType: type-grid
      _select:
        transform: $holidayScheduleList($.extend.limit, $.extend.page, $.extend.search)
    - type: dateRange
      label: Effective Start Date
      labelType: type-grid
      name: effectiveDate
      mode: date-picker
    - type: dateRange
      label: Effective End Date
      labelType: type-grid
      name: effectiveDateTo
      mode: date-picker
    - name: createdBy
      label: Created By
      type: select
      mode: multitple
      labelType: type-grid
      _select:
        transform: $userList()
    - type: dateRange
      label: Created On
      labelType: type-grid
      name: createdAt
    - name: updatedBy
      label: Last Updated By
      type: select
      mode: multitple
      labelType: type-grid
      _select:
        transform: $userList()
    - type: dateRange
      label: Last updated On
      labelType: type-grid
      name: updatedAt
  filterMapping:
    - field: $
      operator: $in
      valueField: employeeCode.(value)
    - field: holidayScheduleId
      operator: $in
      valueField: holidayCalendarId.(value)
    - field: companyId
      operator: $in
      valueField: company.(value)
    - field: legalEntityId
      operator: $in
      valueField: legal.(value)
    - field: businessUnitId
      operator: $in
      valueField: businessUnit.(value)
    - field: divisionId
      operator: $in
      valueField: division.(value)
    - field: departmentId
      operator: $in
      valueField: departmentId.(value)
    - field: createdBy
      operator: $in
      valueField: createdBy.(value)
    - field: createdAt
      operator: $between
      valueField: createdAt
    - field: effectiveDateTo
      operator: $eq
      valueField: effectiveDateTo
    - field: effectiveDate
      operator: $eq
      valueField: effectiveDate
    - field: updatedBy
      operator: $in
      valueField: updatedBy.(value)
    - field: updatedAt
      operator: $between
      valueField: updatedAt
  sources:
    employeesList:
      uri: '"/api/personals"'
      method: GET
      queryTransform: '{''limit'': $.limit, ''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.employeeId &' - '&
        $item.employeeRecordNumber &' - '& $item.name,'code': $item.employeeId,
        'value': {'employeeCode': $item.employeeId,'employeeRecordNumber':
        $string($item.employeeRecordNumber)}}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    holidayScheduleList:
      uri: '"/api/ts-setting-holiday-calendars"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code  }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    groupsList:
      uri: '"/api/groups"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    companiesList:
      uri: '"/api/companies"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    legalEntitiesList:
      uri: '"/api/legal-entities"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    businessUnitsList:
      uri: '"/api/business-units"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    divisionsList:
      uri: '"/api/divisions"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    departmentsList:
      uri: '"/api/departments"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    userList:
      uri: '"/api/internal-users"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.userName, 'value':
        $item.userName}})
      disabledCache: true
layout_options:
  tool_table:
    - id: export
      icon: icon-upload-simple-bold
  show_dialog_form_save_add_button: true
  show_detail_history: false
  export_all:
    type: base_total
  hide_action_row: true
  show_dialog_duplicate_button: false
  delete_multi_items: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    title: Edit
    icon: icon-pencil-simple
  - id: delete
    title: Delete
    icon: icon-trash
backend_url: api/ts-set-day-off-in-years
screen_name: null
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: companyId
    defaultName: CompanyCode
  - name: departmentId
    defaultName: DepartmentCode
  - name: employeeCode
    defaultName: EmployeeId
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Set Holiday Schedules For Employees
  parent:
    title: Set Up Working Hours
