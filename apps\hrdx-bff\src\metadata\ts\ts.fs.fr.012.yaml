id: TS.FS.FR.012
status: draft
sort: 233
user_created: 2aa5db12-fa54-46d6-8e05-9e421858de0c
date_created: '2024-07-03T10:49:50.938Z'
user_updated: abd14398-1d53-4e6f-9834-26328f0ebde0
date_updated: '2025-07-27T02:19:56.951Z'
title: Set Work Schedule For Organization
requirement:
  time: 1748413932163
  blocks:
    - id: FLvm6gkQwS
      type: paragraph
      data:
        text: <PERSON><PERSON><PERSON><PERSON> lậ<PERSON> lịch làm việc theo cơ cấu
  version: 2.30.7
screen_design: null
module: TS
local_fields:
  - code: nationName
    title: Country
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: groupName
    title: Group
    data_type:
      key: Organization-1
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: companyName
    title: Company
    data_type:
      key: Organization-2
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: payGroup
    title: Pay Group
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: legalEntityName
    title: Legal Entity
    data_type:
      key: User
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: caworkSchedulesName
    title: 'Working Schedule '
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 12
  - code: effectiveDate
    title: Effective Start Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/YYYY
      collection: field_types
  - code: effectiveDateTo
    title: Effective End Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
  - code: businessUnitName
    title: Business Unit
    data_type:
      key: Organization-3
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: divisionName
    title: Division
    data_type:
      key: Organization-4
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: departmentName
    title: Department
    data_type:
      key: Organization-5
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: locationName
    title: Location
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: updatedBy
    title: Last Updated By
    data_type:
      key: User
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: updatedAt
    title: Last Updated On
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
  - code: note
    title: Note
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
mock_data:
  - country: Việt Nam
    code: '00000001'
    group: Tập đoàn FPT
    company: FSOFT
    businessUnit: FPT IS
    legalEntity: Nguyễn Văn Anh
    location: Phạm Văn Bạch
    workingScheduleCode: '001'
    effectiveDate: 03/07/2024
    division: Division 1
    department: Department 1
    workingScheduleName: Lịch nghỉ lễ FSOFT
  - country: Việt Nam
    code: '00000002'
    group: Tập đoàn FPT
    company: FHO
    businessUnit: FPT IS
    legalEntity: Nguyễn Văn Anh
    location: Phạm Văn Bạch
    workingScheduleCode: '002'
    effectiveDate: 02/07/2024
    division: Division 1
    department: Department 1
    workingScheduleName: Lịch nghỉ lễ FHO
  - country: Việt Nam
    code: '00000003'
    group: Tập đoàn FPT
    company: FTEL
    businessUnit: FPT IS
    legalEntity: Nguyễn Văn Anh
    location: Phạm Văn Bạch
    workingScheduleCode: '003'
    effectiveDate: 01/07/2024
    division: Division 1
    department: Department 1
    workingScheduleName: Lịch nghỉ lễ FTEL
local_buttons: null
layout: layout-table
form_config:
  formSize:
    create: small
    edit: small
  fields:
    - type: group
      n_cols: 2
      fields:
        - name: nationId
          label: Country
          type: select
          clearFieldsAfterChange:
            - caworkSchedulesId
          outputValue: value
          placeholder: Select Country
          _select:
            transform: $countryList($.extend.limit, $.extend.page, $.extend.search)
          isLazyLoad: true
          initOptionFromDefaultValue:
            value: nationId
            label: '{{nationName}} ({{nationId}})'
        - name: groupId
          label: Group
          type: select
          outputValue: value
          clearFieldsAfterChange:
            - caworkSchedulesId
            - companyId
            - legalEntityId
            - businessUnitId
            - divisionId
            - departmentId
          placeholder: Select Group
          _select:
            transform: >-
              $groupsList($.extend.limit, $.extend.page, $.extend.search,
              $.fields.effectiveDate)
          isLazyLoad: true
          initOptionFromDefaultValue:
            value: groupId
            label: '{{groupName}} ({{groupId}})'
        - name: companyId
          label: Company
          type: select
          clearFieldsAfterChange:
            - caworkSchedulesId
            - legalEntityId
            - businessUnitId
            - divisionId
            - departmentId
            - payGroupId
          outputValue: value
          placeholder: Select Company
          _select:
            transform: >-
              $companiesList($.extend.limit, $.extend.page, $.extend.search,
              $.fields.effectiveDate, $.fields.groupId)
          isLazyLoad: true
          initOptionFromDefaultValue:
            value: companyId
            label: '{{companyName}} ({{companyId}})'
        - name: payGroupId
          type: select
          label: Pay Group
          placeholder: Select Pay Group
          outputValue: value
          _select:
            transform: >-
              $.fields.companyId ?
              $payGroupsList($.fields.nationId,$.fields.companyId)
        - name: legalEntityId
          label: Legal Entity
          type: select
          outputValue: value
          placeholder: Select Legal Entity
          _select:
            transform: >-
              $legalEntityList($.extend.limit, $.extend.page, $.extend.search,
              $.fields.effectiveDate, $.fields.companyId)
          isLazyLoad: true
          initOptionFromDefaultValue:
            value: legalEntityId
            label: '{{legalEntityName}} ({{legalEntityId}})'
        - name: businessUnitId
          label: Business Unit
          type: select
          outputValue: value
          clearFieldsAfterChange:
            - divisionId
            - departmentId
          placeholder: Select Business Unit
          _select:
            transform: >-
              $businessUnitList($.extend.limit, $.extend.page, $.extend.search,
              $.fields.effectiveDate, $.fields.companyId)
          isLazyLoad: true
          initOptionFromDefaultValue:
            value: businessUnitId
            label: '{{businessUnitName}} ({{businessUnitId}})'
        - name: divisionId
          label: Division
          type: select
          outputValue: value
          clearFieldsAfterChange:
            - departmentId
          placeholder: Select Division
          _select:
            transform: >-
              $divisionList($.extend.limit, $.extend.page, $.extend.search,
              $.fields.effectiveDate, $.fields.companyId,
              $.fields.businessUnitId)
          isLazyLoad: true
          initOptionFromDefaultValue:
            value: divisionId
            label: '{{divisionName}} ({{divisionId}})'
        - name: departmentId
          label: Department
          type: select
          outputValue: value
          placeholder: Select Department
          _select:
            transform: >-
              $departmentList($.extend.limit, $.extend.page, $.extend.search,
              $.fields.effectiveDate, $.fields.divisionId)
          isLazyLoad: true
          initOptionFromDefaultValue:
            value: departmentId
            label: '{{departmentName}} ({{departmentId}})'
        - name: locationId
          label: Location
          type: select
          outputValue: value
          isLazyLoad: true
          placeholder: Select Location
          _select:
            transform: >-
              $locationsList($.fields.effectiveDate,
              $.extend.limit,$.extend.page,$.extend.search)
        - name: caworkSchedulesId
          label: Working Schedule
          type: select
          outputValue: value
          placeholder: Select Working Schedule
          _condition:
            transform: $not($.extend.formType = 'view')
          _select:
            transform: $.variables._workschedulesList
          validators:
            - type: required
        - name: effectiveDate
          label: Effective Start Date
          type: dateRange
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
          validators:
            - type: required
          _value:
            transform: $.extend.formType = 'create' ?  $now()
        - type: dateRange
          name: effectiveDateTo
          label: Effective End Date
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
            autoFill: end-off
            _disabledDate:
              transform: '{''operator'': ''$lt'', ''value'': $.fields.effectiveDate}'
          validators:
            - type: ppx-custom
              args:
                transform: >-
                  $exists($.fields.effectiveDate) and
                  $exists($.fields.effectiveDateTo) and
                  $DateDiff($DateFormat($.fields.effectiveDate, 'yyyy-MM-DD'),
                  $DateFormat($.fields.effectiveDateTo, 'yyyy-MM-DD'), 'd') > 0
              text: Effective end date must be greater than effective start date.
        - name: note
          label: Note
          type: textarea
          placeholder: Enter Note
          col: 2
          textarea:
            maxCharCount: 1000
            autoSize:
              minRows: 3
      _condition:
        transform: $not($.extend.formType = 'view')
    - type: group
      n_cols: 1
      fields:
        - name: nationName
          label: Country
          type: text
        - name: groupName
          label: Group
          type: text
        - name: companyName
          label: Company
          type: text
        - name: payGroup
          type: text
          label: Pay Group
        - name: legalEntityName
          label: Legal Entity
          type: text
        - name: businessUnitName
          label: Business Unit
          type: text
        - name: divisionName
          label: Division
          type: text
        - name: departmentName
          label: Department
          type: text
        - name: locationName
          label: Location
          type: text
        - name: effectiveDate
          label: Effective Start Date
          type: dateRange
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
        - name: effectiveDateTo
          label: Effective End Date
          type: dateRange
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
            autoFill: end-off
        - name: caworkSchedulesName
          label: Working Schedule
          type: text
        - name: note
          label: Note
          type: textarea
      _condition:
        transform: $.extend.formType = 'view'
  footer:
    create: true
    update: true
    createdOn: createdAt
    updatedOn: updatedAt
    createdBy: createdBy
    updatedBy: updatedBy
  sources:
    countryList:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      queryTransform: >-
        {'page':$.page,'limit': $.limit, 'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    groupsList:
      uri: '"/api/groups/get-by"'
      queryTransform: >-
        {'page':$.page,'limit': $.limit, 'search': $.search,'filter':
        [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
    companiesList:
      uri: '"/api/companies/get-by"'
      queryTransform: >-
        {'page':$.page,'limit': $.limit, 'search': $.search,'filter':
        [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true},{'field':'groupCode','operator': '$eq','value':
        $.groupId }]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
        - groupId
    legalEntityList:
      uri: '"/api/legal-entities/get-by"'
      queryTransform: >-
        {'page':$.page,'limit': $.limit, 'search': $.search,'filter':
        [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true},{'field':'companyCode','operator': '$eq','value':
        $.companyId }]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
        - companyId
    businessUnitList:
      uri: '"/api/business-units/get-by"'
      queryTransform: >-
        {'page':$.page,'limit': $.limit, 'search': $.search,'filter':
        [{'field':'status','operator':
        '$eq','value':true},{'field':'companyCode','operator': '$eq','value':
        $.companyId }, {'field':'effectiveDate','operator': '$eq','value':
        $.effectiveDate}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
        - companyId
    divisionList:
      uri: '"/api/divisions/get-by"'
      queryTransform: >-
        {'page':$.page,'limit': $.limit, 'search': $.search,'filter':
        [{'field':'status','operator':
        '$eq','value':true},{'field':'businessUnitCode','operator':
        '$eq','value': $.businessUnitId },{'field':'companyCode','operator':
        '$eq','value': $.companyId } ,{'field':'effectiveDate','operator':
        '$eq','value': $.effectiveDate}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
        - companyId
        - businessUnitId
    departmentList:
      uri: '"/api/departments/get-by"'
      queryTransform: >-
        {'page':$.page,'limit': $.limit, 'search': $.search,'filter':
        [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true},{'field':'divisionCode','operator': '$eq','value':
        $.divisionId }]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
        - divisionId
    locationsList:
      uri: '"/api/locations/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - effectiveDate
        - limit
        - page
        - search
    workschedulesList:
      uri: '"/api/ca-work-schedules/list-data"'
      queryTransform: >-
        {'filter': [{'field':'companyId','operator': '$eq','value':
        $.companyId},{'field':'groupId','operator': '$eq','value':
        $.groupId},{'field':'nationId','operator': '$eq','value':
        $.nationId},{'field':'effectiveDate','operator': '$eq','value':
        $.effectiveDate},{'field':'checkNationId','operator':'$eq','value':
        true}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.code & ' - ' &
        $item.name.default & ' - ' & $item.shortName.default, 'value':
        $item.code,'id': $item.id, 'additionalData': $item ,'companyId':
        $item.companyId,'groupId': $item.groupId,'nationId': $item.nationId}})[]
      disabledCache: true
      params:
        - companyId
        - groupId
        - nationId
        - effectiveDate
    payGroupsList:
      uri: '"/api/pay-group-structures"'
      method: GET
      queryTransform: >-
        {'page':1,'limit':10000,'filter':[{'field':'status','operator':
        '$eq','value':true},{'field':'nationId','operator': '$eq','value':
        $.nationId},{'field':'companyCode','operator': '$eq','value':
        $.companyCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - nationId
        - companyCode
  variables:
    _workschedulesList:
      transform: >-
        $workschedulesList($.fields.companyId,$.fields.groupId,
        $.fields.nationId, $.fields.effectiveDate)
filter_config:
  fields:
    - name: nationId
      label: Country
      type: selectAll
      mode: multiple
      placeholder: Select Country
      labelType: type-grid
      isLazyLoad: true
      _options:
        transform: $countryList($.extend.limit, $.extend.page, $.extend.search)
    - name: groupId
      label: Group
      type: selectAll
      mode: multiple
      placeholder: Select Group
      labelType: type-grid
      isLazyLoad: true
      _options:
        transform: $groupList($.extend.limit, $.extend.page, $.extend.search)
    - name: company
      label: Company
      type: selectAll
      mode: multiple
      placeholder: Select Company
      labelType: type-grid
      isLazyLoad: true
      _options:
        transform: $companiesList($.extend.limit, $.extend.page, $.extend.search)
    - name: payGroupId
      type: selectAll
      label: Pay Group
      labelType: type-grid
      placeholder: Enter pay group
      isLazyLoad: true
      mode: multiple
      _options:
        transform: $payGroupsList($.extend.limit, $.extend.page, $.extend.search)
    - name: legalEntity
      label: Legal Entity
      type: selectAll
      mode: multiple
      placeholder: Select Legal Entity
      labelType: type-grid
      isLazyLoad: true
      _options:
        transform: $legalEntitiesList($.extend.limit, $.extend.page, $.extend.search)
    - name: businessUnit
      label: Business Unit
      type: selectAll
      mode: multiple
      placeholder: Select Business Unit
      labelType: type-grid
      isLazyLoad: true
      _options:
        transform: $businessUnitsList($.extend.limit, $.extend.page, $.extend.search)
    - name: division
      label: Division
      type: selectAll
      mode: multiple
      placeholder: Select Division
      labelType: type-grid
      isLazyLoad: true
      _options:
        transform: $divisionsList($.extend.limit, $.extend.page, $.extend.search)
    - name: department
      label: Department
      type: selectAll
      placeholder: Select Department
      labelType: type-grid
      mode: multiple
      isLazyLoad: true
      _options:
        transform: >-
          $departmentsList($.extend.limit,
          $.extend.page,$.fields.effectiveDate,$.extend.defaultValue.id,$.extend.search)
    - name: location
      label: Location
      type: selectAll
      mode: multiple
      isLazyLoad: true
      placeholder: Select Location
      labelType: type-grid
      _options:
        transform: $locationsList($.extend.limit,$.extend.page,$.extend.search)
    - type: dateRange
      name: effectiveDate
      label: Effective Date
      labelType: type-grid
      setting:
        format: dd/MM/yyyy
        type: date
    - name: workingSchedule
      label: Working Schedule
      type: select
      mode: multiple
      placeholder: Select Working Schedule
      labelType: type-grid
      _select:
        transform: $.variables._workschedulesList
    - name: createdBy
      label: Created By
      type: select
      mode: multiple
      placeholder: Select Creator
      labelType: type-grid
      _select:
        transform: $.variables._userList
    - name: createdAt
      label: Created On
      type: dateRange
      labelType: type-grid
      setting:
        format: dd/MM/yyyy
        type: date
    - name: updatedBy
      label: Last Updated By
      type: select
      mode: multiple
      placeholder: Select Last Editor
      labelType: type-grid
      _select:
        transform: $.variables._userList
    - name: updatedAt
      label: Last Updated On
      type: dateRange
      labelType: type-grid
      setting:
        format: dd/MM/yyyy
        type: date
  filterMapping:
    - field: nationId
      operator: $in
      valueField: nationId.(value)
    - field: groupId
      operator: $in
      valueField: groupId.(value)
    - field: companyId
      operator: $in
      valueField: company.(value)
    - field: legalEntityId
      operator: $in
      valueField: legalEntity.(value)
    - field: businessUnitId
      operator: $in
      valueField: businessUnit.(value)
    - field: divisionId
      operator: $in
      valueField: division.(value)
    - field: departmentId
      operator: $in
      valueField: department.(value)
    - field: locationId
      operator: $in
      valueField: location.(value)
    - field: effectiveDate
      operator: $between
      valueField: effectiveDate
    - field: caworkSchedulesId
      operator: $in
      valueField: workingSchedule.(value)
    - field: createdBy
      operator: $in
      valueField: createdBy.(value)
    - field: createdAt
      operator: $between
      valueField: createdAt
    - field: updatedBy
      operator: $in
      valueField: updatedBy.(value)
    - field: updatedAt
      operator: $between
      valueField: updatedAt
    - field: payGroupId
      operator: $in
      valueField: payGroupId.(value)
  sources:
    countryList:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    groupList:
      uri: '"/api/groups/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    companiesList:
      uri: '"/api/companies/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    legalEntitiesList:
      uri: '"/api/legal-entities/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    businessUnitsList:
      uri: '"/api/business-units/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    divisionsList:
      uri: '"/api/divisions/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    departmentsList:
      uri: '"/api/departments/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'filter':
        [{'field':'search','operator':
        '$eq','value':$.search},{'field':'effectiveDate','operator':
        '$lte','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true}, {'field':'IdFilter','operator': '$ne','value':
        $.codeFilter}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - effectiveDate
        - codeFilter
        - search
    locationsList:
      uri: '"/api/locations/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    workschedulesList:
      uri: '"/api/ca-work-schedules/by"'
      method: GET
      queryTransform: '{''filter'': [{''field'':''status'',''operator'': ''$eq'',''value'':true}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - effectiveDate
    userList:
      uri: '"/api/internal-users"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.userName, 'value':
        $item.userName}})
      disabledCache: true
    payGroupsList:
      uri: '"/api/pay-group-structures"'
      method: GET
      queryTransform: '{''limit'': $.limit, ''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
  variables:
    _workschedulesList:
      transform: $workschedulesList()
    _userList:
      transform: $userList()
layout_options:
  tool_table:
    - id: export
      icon: icon-upload-simple-bold
  show_detail_history: false
  show_dialog_form_save_add_button: true
  export_all:
    type: base_total
  hide_action_row: true
  show_dialog_duplicate_button: false
  delete_multi_items: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    icon: icon-pencil-simple
    type: ghost-gray
  - id: delete
    icon: icon-trash
    type: ghost-gray
backend_url: api/ts-config-work-schedules
screen_name: ts-config-work-schedules
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: companyId
    defaultName: CompanyCode
  - name: nationId
    defaultName: CountryCode
  - name: payGroupId
    defaultName: PayGroupCode
  - name: legalEntityId
    defaultName: LegalEntityCode
  - name: locationId
    defaultName: LocationCode
  - name: businessUnitId
    defaultName: BusinessUnitCode
  - name: divisionId
    defaultName: DivisionCode
  - name: departmentId
    defaultName: DepartmentCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Set Work Schedule For Organization
  parent:
    title: Set Up Working Hours
