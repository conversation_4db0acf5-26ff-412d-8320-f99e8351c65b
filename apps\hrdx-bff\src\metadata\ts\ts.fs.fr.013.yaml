id: TS.FS.FR.013
status: draft
sort: 201
user_created: 2aa5db12-fa54-46d6-8e05-9e421858de0c
date_created: '2024-07-04T02:15:19.413Z'
user_updated: abd14398-1d53-4e6f-9834-26328f0ebde0
date_updated: '2025-06-19T02:06:50.849Z'
title: Set work schedule for employees
requirement:
  time: 1746517837434
  blocks:
    - id: Uq6K7BM1LY
      type: paragraph
      data:
        text: T<PERSON><PERSON><PERSON> lập lịch làm việc cho nhân viên
  version: 2.30.7
screen_design: null
module: TS
local_fields:
  - code: employeeCode
    title: Employee ID
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    pinned: true
    show_sort: true
  - code: employeeRecordNumber
    title: Employee Record Number (ERN)
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    pinned: false
    options__tabular__align: right
    options__tabular__column_width: 16
  - code: employeeName
    title: Employee Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: caWorkScheduleName
    title: Working Schedules
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 15
  - code: effectiveDate
    title: 'Effective Start Date '
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    options__tabular__column_width: 12
  - code: effectiveEndDate
    title: Effective End Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    options__tabular__column_width: 12
  - code: companyName
    title: Company
    data_type:
      key: Organization-2
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: legalEntityName
    title: Legal Entity
    data_type:
      key: User
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: businessUnitName
    title: Business Unit
    data_type:
      key: Organization-3
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: divisionName
    title: Division
    data_type:
      key: Organization-4
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: departmentName
    title: Department
    data_type:
      key: Organization-5
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: note
    title: Note
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    options__tabular__column_width: 12
  - code: updatedBy
    title: Last Updated By
    data_type:
      key: User
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: updatedAt
    title: Last Updated On
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
mock_data:
  - employeeID: '00123456'
    employeeRecordNumber: '1'
    employeeName: Bùi Phương
    country: Việt Nam
    group: Tâp đoàn FPT
    company: FSOFT
    legal: FSOFT HN
    businessUnit: FGV
    division: FGV BU1
    department: FGV BU1 SK
    workingSchedules: ******** - Lịch làm việc 01 - L01
    effectiveStartDate: 01/01/2024
    effectiveEndDate: 01/01/2024
    note: Làm việc theo lịch đặc biệt của khách hàng Nhật
    createTime: 01/01/2024 10:00:00
    creator: Phuong Bui
    editor: Khanh Vy
    lastUpdate: 01/01/2024 10:10:00
  - employeeID: '********'
    employeeRecordNumber: '1'
    employeeName: Nguyễn Hải Anh
    country: Việt Nam
    group: Tâp đoàn FPT
    company: FPT IS
    legal: FPT IS HN
    businessUnit: FPT IS BANK
    division: BANK PB11
    department: BANK PB11 TEST
    workingSchedules: ********- Lịch làm việc O2 - LO2
    effectiveStartDate: 01/01/2024
    effectiveEndDate: 01/01/2024
    note: Làm việc theo lịch đặc biệt của khách hàng Nhật
    createTime: 01/01/2024 10:02:00
    creator: Phuong Bui
    editor: Khanh Vy
    lastUpdate: 01/01/2024 10:05:00
local_buttons: null
layout: layout-table
form_config:
  formSize:
    create: largex
    edit: largex
  formTitle:
    create: Add New Working Schedule for Employees
  fields:
    - type: group
      fields:
        - type: select
          name: employee
          label: Employee
          placeholder: Select Employee
          isLazyLoad: true
          validators:
            - type: required
          _select:
            transform: $employeesList($.extend.limit, $.extend.page, $.extend.search)
          _value:
            transform: $.variables._employee[0].value
          _condition:
            transform: $.extend.formType = 'create'
          outputValue: value
        - type: text
          label: Employee
          disabled: true
          _value:
            transform: >-
              $.extend.defaultValue.employeeCode & ' - ' &
              $.extend.defaultValue.employeeRecordNumber & ' - ' &
              $.extend.defaultValue.employeeName
          _condition:
            transform: $.extend.formType = 'edit'
        - type: select
          name: selectedJobData
          unvisible: true
          dependantField: $.fields.employee.employeeId
          _value:
            transform: $jobDatasList($.fields.employee.employeeId)[0]
        - type: text
          name: employeeCode
          label: Employee
          unvisible: true
          _value:
            transform: $.fields.employee.employeeId
        - type: number
          name: employeeRecordNumber
          unvisible: true
          _value:
            transform: $string($.fields.employee.employeeRecordNumber)
        - type: text
          name: employeeIdObj
          unvisible: true
          _value:
            transform: $.variables._employeeIdObj
    - type: group
      readOnly: true
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - type: text
          label: Employee
          name: employee
          _value:
            transform: >-
              $.extend.defaultValue.employeeCode & ' - ' &
              $.extend.defaultValue.employeeRecordNumber & ' - ' &
              $.extend.defaultValue.employeeName
        - type: text
          label: Company
          name: companyName
        - type: text
          label: Legal Entity
          name: legalEntityName
        - type: text
          label: Business Unit
          name: businessUnitName
        - type: text
          label: Division
          name: divisionName
        - type: text
          label: Department
          name: departmentName
        - type: dateRange
          label: Effective Start Date
          name: effectiveDate
          mode: date-picker
        - type: dateRange
          label: Effective End Date
          name: effectiveEndDate
          mode: date-picker
        - type: text
          label: Working Schedules
          name: caWorkScheduleName
    - type: group
      n_cols: 2
      _condition:
        transform: $.extend.formType != 'view'
      fields:
        - type: dateRange
          name: effectiveDate
          label: Effective Start Date
          placeHolder: dd/MM/yyyy
          mode: date-picker
          validators:
            - type: required
        - type: dateRange
          name: effectiveEndDate
          label: Effective End Date
          placeHolder: dd/MM/yyyy
          mode: date-picker
          validators:
            - type: ppx-custom
              args:
                transform: >-
                  $exists($.fields.effectiveEndDate) and
                  $DateDiff($.fields.effectiveEndDate, $.fields.effectiveDate,
                  'd') < 0
              text: Effective End date must be greater than effective start date
    - type: group
      fields:
        - type: select
          label: Working Schedules
          name: caWorkScheduleId
          outputValue: value
          dependantField: $.fields.employee.employeeId, $.fields.effectiveDate
          placeholder: Select Working Schedules
          _select:
            transform: >-
              $workschedulesList($.fields.selectedJobData.companyId,$.fields.selectedJobData.groupId,$.fields.effectiveDate,$.fields.employeeCode,$.fields.employeeRecordNumber)
          _condition:
            transform: $.extend.formType != 'view'
          validators:
            - type: required
        - name: note
          label: Note
          type: textarea
          placeholder: Enter note
          validators:
            - type: maxLength
              args: '1000'
              text: Note should not exceed 1000 characters.
          textarea:
            autoSize:
              minRows: 3
            maxCharCount: 1000
  overview:
    dependentField: employeeIdObj
    title: Employee Detail
    border: true
    noDataMessages: Choose Employee ID getting data
    uri: >-
      /api/personals/:{employeeId}:/job-datas?filter[0]=employeeRecordNumber||$eq||:{employeeRecordNumber}:
    display:
      - label: Employee record number
        _value:
          transform: $string($.fields.employeeIdObj.employeeRecordNumber)
      - key: companyName
        label: Company
      - key: legalEntityName
        label: Legal Entity
      - key: businessUnitName
        label: Bussiness Unit
      - key: divisionName
        label: Division
      - key: departmentName
        label: Department
  footer:
    create: true
    update: true
    createdOn: createdAt
    updatedOn: updatedAt
    createdBy: createdBy
    updatedBy: updatedBy
  sources:
    employeesList:
      uri: '"/api/personals"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'filter':
        [{'field':'search','operator': '$eq','value':$.search}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label':
        $join($filter([$item.employeeId,$string($item.employeeRecordNumber),$item.fullName],
        $boolean), ' - ')  , 'value': {'employeeId': $item.employeeId,
        'employeeRecordNumber': $string($item.employeeRecordNumber),
        'jobDataId': $item.jobDataId}, 'employeeId': $item.employeeId,
        'employeeRecordNumber': $string($item.employeeRecordNumber),'jobDataId':
        $item.jobDataId}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    jobDatasList:
      uri: '"/api/personals/"  & $.employeeId &  "/job-datas"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'companyId': $item.company, 'groupId':
        $item.group}})[]
      disabledCache: true
      params:
        - employeeId
    workschedulesList:
      uri: '"/api/ca-work-schedules/list-data"'
      queryTransform: >-
        {'filter': [{'field':'companyId','operator': '$eq','value':
        $.companyId},{'field':'groupId','operator': '$eq','value':
        $.groupId},{'field':'effectiveDate','operator': '$eq','value':
        $.effectiveDate},{'field':'employeeCode','operator': '$eq','value':
        $.employeeCode},{'field':'employeeRecordNumber','operator':
        '$eq','value': $.employeeRecordNumber}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.code & ' - ' &
        $item.name.default & ' - ' & $item.shortName.default, 'value':
        $item.code,'id': $item.id, 'additionalData': $item ,'companyId':
        $item.companyId,'groupId': $item.groupId,'nationId': $item.nationId}})[]
      disabledCache: true
      params:
        - companyId
        - groupId
        - effectiveDate
        - employeeCode
        - employeeRecordNumber
  variables:
    _employeeIdObj:
      transform: >-
        $.fields.employeeCode != '' ? {'employeeId': $.fields.employeeCode,
        'employeeRecordNumber': $.fields.employeeRecordNumber}
filter_config:
  fields:
    - name: company
      label: Company
      type: selectAll
      mode: multiple
      labelType: type-grid
      placeholder: Select Company
      isLazyLoad: true
      _options:
        transform: $companiesList($.extend.limit, $.extend.page, $.extend.search)
    - name: legalEntity
      label: Legal Entity
      type: selectAll
      mode: multiple
      labelType: type-grid
      placeholder: Select Legal Entity
      isLazyLoad: true
      _options:
        transform: $legalEntitiesList($.extend.limit, $.extend.page, $.extend.search)
    - name: businessUnit
      label: Business Unit
      type: selectAll
      mode: multiple
      labelType: type-grid
      placeholder: Select Business Unit
      isLazyLoad: true
      _options:
        transform: $businessUnitsList($.extend.limit, $.extend.page, $.extend.search)
    - name: division
      label: Division
      type: selectAll
      mode: multiple
      labelType: type-grid
      placeholder: Select Division
      isLazyLoad: true
      _options:
        transform: $divisionsList($.extend.limit, $.extend.page, $.extend.search)
    - name: department
      label: Department
      type: selectAll
      mode: multiple
      labelType: type-grid
      placeholder: Select Department
      isLazyLoad: true
      _options:
        transform: $departmentsList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      name: employeeCode
      label: Employee
      placeholder: Select Employee
      isLazyLoad: true
      mode: multiple
      labelType: type-grid
      _options:
        transform: $employeesList($.extend.limit, $.extend.page, $.extend.search)
    - type: dateRange
      name: effectiveStartDate
      label: Effective Start Date
      labelType: type-grid
      placeholder: dd/MM/yyyy
      mode: date-picker
      setting:
        format: dd/MM/yyyy
        type: date
    - type: dateRange
      name: effectiveEndDate
      label: Effective End Date
      labelType: type-grid
      placeholder: dd/MM/yyyy
      mode: date-picker
      setting:
        format: dd/MM/yyyy
        type: date
    - name: workingSchedule
      label: Working Schedule Name
      type: select
      mode: multitple
      labelType: type-grid
      placeholder: Select Holiday Calendar Name
      _select:
        transform: $workschedulesList()
    - name: createdBy
      label: Created By
      type: select
      mode: multitple
      labelType: type-grid
      placeholder: Select Creator
      _select:
        transform: $userList()
    - type: dateRange
      name: CreatedAt
      label: Created On
      labelType: type-grid
      placeholder: dd/MM/yyyy
      mode: date-picker
      setting:
        format: dd/MM/yyyy
        type: date
    - name: updatedBy
      label: Last Updated By
      type: select
      mode: multitple
      labelType: type-grid
      placeholder: Select Editor
      _select:
        transform: $userList()
    - type: dateRange
      name: updatedAt
      label: Last Updated On
      labelType: type-grid
      placeholder: dd/MM/yyyy
      setting:
        format: dd/MM/yyyy
        type: date
  filterMapping:
    - field: companyId
      operator: $in
      valueField: company.(value)
    - field: legalEntityId
      operator: $in
      valueField: legalEntity.(value)
    - field: businessUnitId
      operator: $in
      valueField: businessUnit.(value)
    - field: divisionId
      operator: $in
      valueField: division.(value)
    - field: departmentId
      operator: $in
      valueField: department.(value)
    - field: $
      operator: $in
      valueField: employeeCode.(value)
    - field: effectiveDate
      operator: $gte
      valueField: effectiveStartDate
    - field: effectiveEndDate
      operator: $lte
      valueField: effectiveEndDate
    - field: workingScheduleId
      operator: $in
      valueField: workingSchedule.(value)
    - field: createdBy
      operator: $in
      valueField: createdBy.(value)
    - field: createdAt
      operator: $between
      valueField: createdAt
    - field: updatedBy
      operator: $in
      valueField: updatedBy.(value)
    - field: updatedAt
      operator: $between
      valueField: updatedAt
  sources:
    companiesList:
      uri: '"/api/companies/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    legalEntitiesList:
      uri: '"/api/legal-entities/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})
      disabledCache: true
      params:
        - limit
        - page
        - search
    businessUnitsList:
      uri: '"/api/business-units/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})
      disabledCache: true
      params:
        - limit
        - page
        - search
    divisionsList:
      uri: '"/api/divisions/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})
      disabledCache: true
      params:
        - limit
        - page
        - search
    departmentsList:
      uri: '"/api/departments/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})
      disabledCache: true
      params:
        - limit
        - page
        - search
    employeesList:
      uri: '"/api/personals"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'filter':
        [{'field':'search','operator': '$eq','value':$.search}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.employeeId &' - '&
        $item.employeeRecordNumber &' - '& $item.name,'code': $item.employeeId,
        'value': {'employeeCode': $item.employeeId,'employeeRecordNumber':
        $string($item.employeeRecordNumber)}}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    workschedulesList:
      uri: '"/api/ca-work-schedules/by"'
      method: GET
      queryTransform: '{''filter'': [{''field'':''status'',''operator'': ''$eq'',''value'':true}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.name.default, 'value':
        $item.code}})
      disabledCache: true
      params:
        - effectiveDate
    userList:
      uri: '"/api/internal-users"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.userName, 'value':
        $item.userName}})[]
      disabledCache: true
layout_options:
  tool_table:
    - id: import
      icon: icon-upload-simple
      paramsRedirect:
        type: TS_OBJECT
        entityOrObj: TSDetermineWorkSchedule
    - id: export
      icon: icon-download-simple
  toolTable:
    export: true
    import: true
    adjustDisplay: true
  show_dialog_form_save_add_button: true
  show_detail_history: false
  export_all:
    type: base_total
  hide_action_row: true
  show_dialog_duplicate_button: false
  delete_multi_items: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: null
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    title: Edit
    icon: icon-pencil-simple
    type: ghost-gray
  - id: delete
    title: Delete
    icon: icon-trash
    type: ghost-gray
backend_url: api/ts-determine-work-schedules
screen_name: null
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: companyId
    defaultName: CompanyCode
  - name: employeeCode
    defaultName: EmployeeId
  - name: legalEntityId
    defaultName: LegalEntityCode
  - name: departmentId
    defaultName: DepartmentCode
  - name: divisionId
    defaultName: DivisionCode
  - name: businessUnitId
    defaultName: BusinessUnitCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Set Up Work Schedules For Employees
  parent:
    title: Set Up Working Hours
