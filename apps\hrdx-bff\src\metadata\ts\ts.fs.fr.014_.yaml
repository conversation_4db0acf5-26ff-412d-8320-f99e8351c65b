id: TS.FS.FR.014_
status: draft
sort: 540
user_created: 43878dbb-aa6c-4ef4-8bd7-0ea811ffafa7
date_created: '2024-08-16T03:44:43.767Z'
user_updated: abd14398-1d53-4e6f-9834-26328f0ebde0
date_updated: '2025-07-03T02:41:02.618Z'
title: Set Standard Work Schedule (Organization)
requirement:
  time: 1747104692652
  blocks:
    - id: Kaur_Bafmw
      type: paragraph
      data:
        text: >-
          -<PERSON><PERSON> thống cho phép thiết lập công chuẩn cho từng đơn vị/pháp nhân, các
          thông tin thiết lập:
    - id: pG3IGUKrrn
      type: paragraph
      data:
        text: >-
          - Hệ thống kiểm tra thông tin và không cho phép tạo mới/ chỉnh sửa nếu
          thông tin thiết lập trùng thông tin về “Đơn vị áp dụng” và “<PERSON><PERSON>y hiệu
          lực” với các thiết lập trước đó. 
    - id: sbU_wtQtmW
      type: paragraph
      data:
        text: >-
          - H<PERSON> thống chỉ cho phép sửa thiết lập công chuẩn của tháng, xóa thiết
          lập công chuẩn của tháng khi chưa thực hiện tổng hợp công tháng đó. 
    - id: PCKWJWO2gx
      type: paragraph
      data:
        text: >-
          - Trường hợp muốn thay đổi thiết lập công chuẩn thì người dùng thực
          hiện thêm 01 dòng thiết lập với ngày hiệu lực mới.  
    - id: l9s2mOKNK1
      type: paragraph
      data:
        text: >-
          - Hệ thống cho phép đếm số ngày công chuẩn theo nguyên tắc: Công chuẩn
          trong tháng = Số ngày làm việc (Số ngày có thời giờ làm việc) trong
          tháng + Số ngày nghỉ lễ trong tháng. 
  version: 2.30.7
screen_design: null
module: TS
local_fields:
  - code: groupName
    title: Group
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    description: The group of the entity
    options__tabular__column_width: 6.25
  - code: companyName
    title: Company
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    description: The company of the entity
    options__tabular__column_width: 6.25
  - code: legalEntityName
    title: Legal Entity
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    description: The legal entity
    options__tabular__column_width: 7
  - code: businessUnitName
    title: Business Unit
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    description: The business unit
    options__tabular__column_width: 7.8125
  - code: divisionName
    title: Division
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    description: The division
    options__tabular__column_width: 7.5
  - code: departmentName
    title: Department
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    description: The department
    options__tabular__column_width: 7.5
  - code: jobTitleName
    title: Job Title
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    options__tabular__column_width: 8.75
  - code: effectiveDate
    title: Effective Start Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    description: The effective date
    show_sort: true
    options__tabular__column_width: 9.5
  - code: effectiveDateTo
    title: Effective End Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
  - code: planForSettingSWHDisplay
    title: Set Standard Work Method
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    description: The method to set standard work
    options__tabular__column_width: 13.3475
  - code: note
    title: Note
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    description: Additional note
    options__tabular__column_width: 7.5
  - code: updatedAt
    title: Last Updated On
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
    description: The date and time when the record was last updated
    show_sort: true
    options__tabular__column_width: 11.25
  - code: updatedBy
    title: Last Updated By
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    description: Who last updated the record
    options__tabular__column_width: 9
mock_data: null
local_buttons: null
layout: layout-table
form_config:
  formSize:
    create: small
    edit: small
  formTitle:
    create: Add New Standard Work
    edit: Edit Standard Work Schedule (Organization)
    view: Standard Work Schedule (Organization) Details
  fields:
    - type: group
      n_cols: 2
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - name: groupId
          label: Group
          type: select
          placeholder: Select Group
          clearFieldsAfterChange:
            - companyId
            - legalEntityId
            - businessUnitId
            - divisionId
            - departmentId
            - caWorkSchedulesId
          outputValue: value
          _select:
            transform: >-
              $groupsList($.extend.limit, $.extend.page, $.extend.search,
              $.fields.effectiveDate)
          isLazyLoad: true
          initOptionFromDefaultValue:
            value: groupId
            label: '{{groupName}} ({{groupId}})'
        - name: companyId
          label: Company
          type: select
          clearFieldsAfterChange:
            - legalEntityId
            - businessUnitId
            - divisionId
            - departmentId
            - jobTitleId
            - caWorkSchedulesId
          placeholder: Select Company
          outputValue: value
          _select:
            transform: >-
              $companiesList($.extend.limit, $.extend.page, $.extend.search,
              $.fields.effectiveDate, $.fields.groupId)
          isLazyLoad: true
          initOptionFromDefaultValue:
            value: companyId
            label: '{{companyName}} ({{companyId}})'
        - name: legalEntityId
          label: Legal Entity
          type: select
          placeholder: Select Legal Entity
          outputValue: value
          _select:
            transform: >-
              $legalEntityList($.extend.limit, $.extend.page, $.extend.search,
              $.fields.effectiveDate, $.fields.companyId)
          isLazyLoad: true
          initOptionFromDefaultValue:
            value: legalEntityId
            label: '{{legalEntityName}} ({{legalEntityId}})'
        - name: businessUnitId
          label: Business Unit
          type: select
          clearFieldsAfterChange:
            - divisionId
            - departmentId
          placeholder: Select Business Unit
          outputValue: value
          _select:
            transform: >-
              $businessUnitList($.extend.limit, $.extend.page, $.extend.search,
              $.fields.effectiveDate, $.fields.companyId)
          isLazyLoad: true
          initOptionFromDefaultValue:
            value: businessUnitId
            label: '{{businessUnitName}} ({{businessUnitId}})'
        - name: divisionId
          label: Division
          type: select
          clearFieldsAfterChange:
            - departmentId
          placeholder: Select Division
          outputValue: value
          _select:
            transform: >-
              $divisionList($.extend.limit, $.extend.page, $.extend.search,
              $.fields.effectiveDate, $.fields.businessUnitId)
          isLazyLoad: true
          initOptionFromDefaultValue:
            value: divisionId
            label: '{{divisionName}} ({{divisionId}})'
        - name: departmentId
          label: Department
          type: select
          placeholder: Select Department
          outputValue: value
          _select:
            transform: >-
              $departmentList($.extend.limit, $.extend.page, $.extend.search,
              $.fields.effectiveDate, $.fields.divisionId)
          isLazyLoad: true
          initOptionFromDefaultValue:
            value: departmentId
            label: '{{departmentName}} ({{departmentId}})'
        - name: jobTitleId
          label: Job Title
          type: select
          isLazyLoad: true
          placeholder: Select Job Title
          outputValue: value
          col: 2
          _select:
            transform: >-
              $jobCodesList($.fields.companyId, $.extend.limit, $.extend.page,
              $.extend.search,$.fields.effectiveDate)
          initOptionFromDefaultValue:
            value: jobTitleId
            label: '{{jobTitleName}} ({{jobTitleId}})'
        - type: dateRange
          label: Effective Start Date
          name: effectiveDate
          placeholder: dd/MM/yyyy
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
          validators:
            - type: required
          _value:
            transform: $.extend.formType = 'create' ?  $now()
        - type: dateRange
          name: effectiveDateTo
          label: Effective End Date
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
            autoFill: end-off
            _disabledDate:
              transform: '{''operator'': ''$lt'', ''value'': $.fields.effectiveDate}'
          validators:
            - type: ppx-custom
              args:
                transform: >-
                  $exists($.fields.effectiveDate) and
                  $exists($.fields.effectiveDateTo) and
                  $DateDiff($DateFormat($.fields.effectiveDate, 'yyyy-MM-DD'),
                  $DateFormat($.fields.effectiveDateTo, 'yyyy-MM-DD'), 'd') > 0
              text: Effective end date must be greater than effective start date.
        - name: planForSettingSWH
          label: Set Standard Work Method
          type: select
          placeholder: Select Standard Work Method
          outputValue: value
          validators:
            - type: required
          select:
            - value: '1'
              label: Theo lịch làm việc
            - value: '2'
              label: Theo lịch năm
        - name: numberOfWorkHourPerDay
          label: Daily Work Hours
          type: number
          placeholder: Enter Daily Work Hours
          _condition:
            transform: $.fields.planForSettingSWH = '2'
          validators:
            - type: required
              text: Cannot be empty
    - type: group
      n_cols: 2
      _condition:
        transform: $not($.extend.formType = 'view') and $.fields.planForSettingSWH = '2'
      fields:
        - type: group
          fields: []
        - type: group
          fields:
            - name: excludingSaturday
              type: checkbox
              label: Excluding Saturday
              hiddenLabel: true
              value: 'true'
              _value:
                transform: $.fields.excludingHalfSaturday = true ? false
            - name: excludingHalfSaturday
              type: checkbox
              label: Excluding 1/2 Saturday
              hiddenLabel: true
              _value:
                transform: $.fields.excludingSaturday = true ? false
            - name: excludingSundays
              type: checkbox
              label: Excluding Sundays
              hiddenLabel: true
              value: 'true'
    - type: group
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - name: groupName
          label: Group
          type: text
          _value:
            transform: >-
              ($value := $.extend.defaultValue; $value.groupId ?
              $value.groupName & ' (' & $value.groupId & ')')
        - name: companyName
          label: Company
          type: text
          _value:
            transform: >-
              ($value := $.extend.defaultValue; $value.companyId ?
              $value.companyName & ' (' & $value.companyId & ')')
        - name: legalEntityName
          label: Legal Entity
          type: text
          _value:
            transform: >-
              ($value := $.extend.defaultValue; $value.legalEntityId ?
              $value.legalEntityName & ' (' & $value.legalEntityId & ')')
        - name: businessUnitName
          label: Business Unit
          type: text
          _value:
            transform: >-
              ($value := $.extend.defaultValue; $value.businessUnitId ?
              $value.businessUnitName & ' (' & $value.businessUnitId & ')')
        - name: divisionName
          label: Division
          type: text
          _value:
            transform: >-
              ($value := $.extend.defaultValue; $value.divisionId ?
              $value.divisionName & ' (' & $value.divisionId & ')')
        - name: departmentName
          label: Department
          type: text
          _value:
            transform: >-
              ($value := $.extend.defaultValue; $value.departmentId ?
              $value.departmentName & ' (' & $value.departmentId & ')')
        - name: jobTitleName
          label: Job Title
          type: text
          _value:
            transform: >-
              ($value := $.extend.defaultValue; $value.jobTitleId ?
              $value.jobTitleName & ' (' & $value.jobTitleId & ')')
        - type: dateRange
          name: effectiveDate
          label: Effective Start Date
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
        - type: dateRange
          name: effectiveDateTo
          label: Effective End Date
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
            autoFill: end-off
        - name: planForSettingSWH
          label: Set Standard Work Method
          type: select
          select:
            - value: '1'
              label: Theo lịch làm việc
            - value: '2'
              label: Theo lịch năm
        - name: numberOfWorkHourPerDay
          label: Daily Work Hours
          type: number
          displaySetting:
            style:
              display: flex
              gap: 4px
              flexDirection: column
              width: 100%
            type: Label
            elements:
              - type: Tags
                name: tags
                _value:
                  transform: >-
                    $map($.extend.defaultValue.excluding, function($value) {
                    $value = '1' ? 'Excluding Saturday' : $value = '2' ?
                    'Excluding 1/2 Saturday' : 'Excluding Sundays' })[]
                extraConfig:
                  resize: false
    - type: textarea
      label: Note
      name: note
      placeholder: Enter Note
      textarea:
        maxCharCount: 1000
        autoSize:
          minRows: 3
      validators:
        - type: maxLength
          args: '1000'
          text: Maximum length is 1000 characters.
  sources:
    groupsList:
      uri: '"/api/groups/get-by"'
      queryTransform: >-
        {'page':$.page,'limit': $.limit, 'search': $.search, 'filter':
        [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
    workScheduleList:
      uri: '"/api/ca-work-schedules/list-data"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'groupId','operator':
        '$eq','value':$.groupId},{'field':'companyId','operator':
        '$eq','value':$.companyId},{'field':'effectiveDate','operator':
        '$eq','value':
        $.effectiveDate},{'field':'checkNationId','operator':'$eq','value':
        false}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - groupId
        - companyId
        - effectiveDate
    companiesList:
      uri: '"/api/companies/get-by"'
      queryTransform: >-
        {'page':$.page,'limit':$.limit, 'search': $.search, 'filter':
        [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true},{'field':'groupCode','operator': '$eq','value':
        $.groupId }]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
        - groupId
    legalEntityList:
      uri: '"/api/legal-entities/get-by"'
      queryTransform: >-
        {'page':$.page,'limit':$.limit, 'search': $.search, 'filter':
        [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true},{'field':'companyCode','operator': '$eq','value':
        $.companyId }]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
        - companyId
    businessUnitList:
      uri: '"/api/business-units/get-by"'
      queryTransform: >-
        {'page':$.page,'limit':$.limit, 'search': $.search,'filter':
        [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true},{'field':'companyCode','operator': '$eq','value':
        $.companyId }]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
        - companyId
    divisionList:
      uri: '"/api/divisions/get-by"'
      queryTransform: >-
        {'page':$.page,'limit':$.limit, 'search': $.search,'filter':
        [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true},{'field':'businessUnitCode','operator':
        '$eq','value': $.businessUnitId }]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
        - businessUnitId
    departmentList:
      uri: '"/api/departments/get-by"'
      queryTransform: >-
        {'page':$.page,'limit':$.limit, 'search': $.search,'filter':
        [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true},{'field':'divisionCode','operator': '$eq','value':
        $.divisionId }]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
        - divisionId
    jobCodesList:
      uri: '"/api/job-codes/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator':
        '$eq','value':true},{'field':'companyCode','operator':
        '$eq','value':$.companyId},{'field':'effectiveDate','operator':
        '$eq','value': $.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id}})[]
      disabledCache: true
      params:
        - companyId
        - limit
        - page
        - search
        - effectiveDate
  variables: {}
  footer:
    create: true
    update: true
    createdOn: createdAt
    updatedOn: updatedAt
    createdBy: createdBy
    updatedBy: updatedBy
filter_config:
  fields:
    - name: groupId
      label: Group
      type: selectAll
      placeholder: Select Group
      mode: multiple
      labelType: type-grid
      isLazyLoad: true
      _options:
        transform: $groupsList($.extend.limit, $.extend.page, $.extend.search)
    - name: companyId
      label: Company
      type: selectAll
      placeholder: Select Company
      labelType: type-grid
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $companiesList($.extend.limit, $.extend.page, $.extend.search)
    - name: legalEntityId
      label: Legal Entity
      type: selectAll
      placeholder: Select Legal Entity
      labelType: type-grid
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $legalEntityList($.extend.limit, $.extend.page, $.extend.search)
    - name: businessUnitId
      label: Business Unit
      type: selectAll
      placeholder: Select Business Unit
      labelType: type-grid
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $businessUnitList($.extend.limit, $.extend.page, $.extend.search)
    - name: divisionId
      label: Division
      type: selectAll
      placeholder: Select Division
      labelType: type-grid
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $divisionList($.extend.limit, $.extend.page, $.extend.search)
    - name: departmentId
      label: Department
      type: selectAll
      placeholder: Select Department
      labelType: type-grid
      mode: multiple
      isLazyLoad: true
      _options:
        transform: >-
          $departmentsList($.extend.limit,
          $.extend.page,$.fields.effectiveDate,$.extend.defaultValue.id,$.extend.search)
    - name: jobTitleId
      label: Job Title
      type: selectAll
      isLazyLoad: true
      placeholder: Select Job Title
      labelType: type-grid
      mode: multiple
      _options:
        transform: >-
          $jobTitleList($.extend.limit,
          $.extend.page,$.fields.effectiveDate,$.extend.defaultValue.id,$.extend.search)
    - name: planForSettingSWH
      label: Set Standard Work Method
      type: select
      labelType: type-grid
      placeholder: Select Set Standard Work Method
      mode: multiple
      select:
        - value: '1'
          label: Theo lịch làm việc
        - value: '2'
          label: Theo lịch năm
    - name: createdBy
      label: Created By
      type: selectAll
      mode: multiple
      labelType: type-grid
      placeholder: Select Created By
      isLazyLoad: true
      _options:
        transform: $userList()
    - type: dateRange
      labelType: type-grid
      label: Created On
      name: createdAt
      setting:
        format: dd/MM/yyyy
        type: date
    - name: updatedBy
      label: Last Updated By
      type: selectAll
      mode: multiple
      labelType: type-grid
      placeholder: Select Last Update By
      isLazyLoad: true
      _options:
        transform: $userList()
    - type: dateRange
      labelType: type-grid
      label: Last Updated On
      name: updatedAt
      setting:
        format: dd/MM/yyyy
        type: date
  filterMapping:
    - field: groupId
      operator: $in
      valueField: groupId.(value)
    - field: companyId
      operator: $in
      valueField: companyId.(value)
    - field: legalEntityId
      operator: $in
      valueField: legalEntityId.(value)
    - field: businessUnitId
      operator: $in
      valueField: businessUnitId.(value)
    - field: divisionId
      operator: $in
      valueField: divisionId.(value)
    - field: departmentId
      operator: $in
      valueField: departmentId.(value)
    - field: jobTitleId
      operator: $in
      valueField: jobTitleId.(value)
    - field: planForSettingSWH
      operator: $in
      valueField: planForSettingSWH.(value)
    - field: createdBy
      operator: $in
      valueField: createdBy.(value)
    - field: createdAt
      operator: $between
      valueField: createdAt
    - field: updatedBy
      operator: $in
      valueField: updatedBy.(value)
    - field: updatedAt
      operator: $between
      valueField: updatedAt
  sources:
    groupsList:
      uri: '"/api/groups/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code , 'id': $item.id}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    companiesList:
      uri: '"/api/companies/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    legalEntityList:
      uri: '"/api/legal-entities/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    businessUnitList:
      uri: '"/api/business-units/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    divisionList:
      uri: '"/api/divisions/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    departmentsList:
      uri: '"/api/departments/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'filter':
        [{'field':'search','operator':
        '$eq','value':$.search},{'field':'effectiveDate','operator':
        '$lte','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true}, {'field':'IdFilter','operator': '$ne','value':
        $.codeFilter}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - effectiveDate
        - codeFilter
        - search
    departmentsView:
      uri: '"/api/departments/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'filter':
        [{'field':'effectiveDate','operator':
        '$lte','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true}, {'field':'IdFilter','operator': '$eq','value':
        $.codeFilter}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.id, 'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - effectiveDate
        - codeFilter
    jobTitleList:
      uri: '"/api/job-codes/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'filter':
        [{'field':'search','operator':
        '$eq','value':$.search},{'field':'effectiveDate','operator':
        '$lte','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true}, {'field':'IdFilter','operator': '$ne','value':
        $.codeFilter}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - effectiveDate
        - codeFilter
        - search
    jobTitleView:
      uri: '"/api/job-codes/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'filter':
        [{'field':'effectiveDate','operator':
        '$lte','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true}, {'field':'IdFilter','operator': '$eq','value':
        $.codeFilter}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.id, 'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - effectiveDate
        - codeFilter
    userList:
      uri: '"/api/internal-users"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.userName, 'value':
        $item.userName}})
      disabledCache: true
      params:
        - limit
        - page
        - search
layout_options:
  tool_table:
    - id: export
      icon: icon-download-simple
  show_detail_history: false
  show_dialog_form_save_add_button: true
  export_all:
    type: base_total
  hide_action_row: true
  show_dialog_duplicate_button: false
  delete_multi_items: true
layout_options__header_buttons:
  - id: create
    title: Create
    type: primary
    icon: plus
options: null
create_form: null
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    title: Edit
    icon: icon-pencil-simple
    type: ghost-gray
  - id: delete
    title: Delete
    icon: icon-trash
    type: ghost-gray
backend_url: api/ts-config-working-hours
screen_name: null
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: companyId
    defaultName: CompanyCode
  - name: legalEntityId
    defaultName: LegalEntityCode
  - name: businessUnitId
    defaultName: BusinessUnitCode
  - name: divisionId
    defaultName: DivisionCode
  - name: departmentId
    defaultName: DepartmentCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Set Standard Work Schedule (Organization)
  parent:
    title: Configuration
