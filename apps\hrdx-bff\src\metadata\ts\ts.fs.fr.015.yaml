id: TS.FS.FR.015
status: draft
sort: 581
user_created: 2aa5db12-fa54-46d6-8e05-9e421858de0c
date_created: '2024-08-27T09:07:26.220Z'
user_updated: abd14398-1d53-4e6f-9834-26328f0ebde0
date_updated: '2025-07-03T06:51:04.158Z'
title: Edit Work Schedule
requirement:
  time: 1724748412677
  blocks:
    - id: qwYGVG-Qua
      type: paragraph
      data:
        text: >-
          <PERSON><PERSON><PERSON> năng cho phép bộ phận nhân sự tập đoàn/CTTV điều chỉnh lịch làm
          việc của một nhân viên
  version: 2.29.1
screen_design: null
module: TS
local_fields: null
mock_data: null
local_buttons: null
layout: layout-dynamic-table
form_config: {}
filter_config:
  fields:
    - type: group
      n_cols: 3
      fields:
        - type: number
          label: pageIndex
          name: pageIndex
          unvisible: true
          value: 1
        - type: number
          label: pageSize
          name: pageSize
          unvisible: true
          value: 25
        - type: select
          name: employees
          label: Employee
          placeholder: Select Employee
          mode: multiple
          isLazyLoad: true
          outputValue: value
          validators:
            - type: required
          _select:
            transform: >-
              $employeesList($.extend.limit, $.extend.page, $.extend.search,
              $.fields.company.code, $.fields.legalEntity.code,
              $.fields.businessUnit.code, $.fields.division.code,
              $.fields.department.code)
        - type: dateRange
          name: startDate
          label: Start Date
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
          validators:
            - type: required
        - type: dateRange
          name: endDate
          label: End Date
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
          validators:
            - type: required
            - type: ppx-custom
              args:
                transform: >-
                  $not( $isNilorEmpty($.fields.endDate) or 
                  $isNilorEmpty($.fields.startDate) ) ?
                  $test(($DateDiff($.fields.endDate, $.fields.startDate, 'd') >
                  31))
              text: Please select a time period within 31 days
            - type: ppx-custom
              args:
                transform: >-
                  $not( $isNilorEmpty($.fields.endDate) or 
                  $isNilorEmpty($.fields.startDate) ) ?
                  ($DateDiff($.fields.endDate, $.fields.startDate, 'd') < 0)
              text: End date must be greater than start date
        - type: select
          label: Company
          name: company
          placeholder: Select Company
          isLazyLoad: true
          clearFieldsAfterChange:
            - employees
          outputValue: value
          _select:
            transform: $companiesList($.extend.limit, $.extend.page, $.extend.search)
        - type: select
          label: Company
          name: companyIds
          unvisible: true
          dependantField: $.fields.company
          _value:
            transform: '[$.fields.company.code]'
        - type: select
          label: Legal Entity
          name: legalEntity
          placeholder: Select Legal Entity
          outputValue: value
          clearFieldsAfterChange:
            - employees
          dependantField: $.fields.company
          isLazyLoad: true
          _select:
            transform: >-
              $legalEntityList($.extend.limit, $.extend.page, $.extend.search,
              $.fields.company.id)
        - type: select
          label: Legal Entity
          name: legalEntityIds
          dependantField: $.fields.legalEntity
          unvisible: true
          _value:
            transform: '[$.fields.legalEntity.code]'
        - type: select
          label: Business Unit
          name: businessUnit
          placeholder: Select Business Unit
          outputValue: value
          clearFieldsAfterChange:
            - employees
          dependantField: $.fields.company
          isLazyLoad: true
          _select:
            transform: >-
              $businessUnitList($.extend.limit, $.extend.page, $.extend.search,
              $.fields.company.id)
        - type: select
          label: Business Unit
          name: businessUnitIds
          unvisible: true
          dependantField: $.fields.businessUnit
          _value:
            transform: '[$.fields.businessUnit.code]'
        - type: select
          label: Division
          name: division
          placeholder: Select Division
          outputValue: value
          clearFieldsAfterChange:
            - employees
          dependantField: $.fields.company
          isLazyLoad: true
          _select:
            transform: >-
              $divisionList($.extend.limit, $.extend.page, $.extend.search,
              $.fields.businessUnit.id)
        - type: select
          label: Division
          name: divisionIds
          unvisible: true
          dependantField: $.fields.division
          _value:
            transform: '[$.fields.division.code]'
        - type: select
          label: Department
          name: department
          placeholder: Select Department
          outputValue: value
          clearFieldsAfterChange:
            - employees
          dependantField: $.fields.division
          isLazyLoad: true
          _select:
            transform: >-
              $departmentList($.extend.limit, $.extend.page, $.extend.search,
              $.fields.division.id)
        - type: select
          label: Department
          name: departmentIds
          unvisible: true
          dependantField: $.fields.department
          _value:
            transform: '[$.fields.department.code]'
  sources:
    employeesList:
      uri: '"/api/personals"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'filter':
        [{'field':'search','operator':
        '$eq','value':$.search},{'field':'company','operator':
        '$eq','value':$.companyIds},{'field':'legalEntity','operator':
        '$eq','value':$.legalEntityIds},{'field':'bussinessUnit','operator':
        '$eq','value':$.businessUnitIds},{'field':'division','operator':
        '$eq','value':$.divisionIds},{'field':'department','operator':
        '$eq','value':$.departmentIds}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label':
        $join($filter([$item.employeeId,$string($item.employeeRecordNumber),$item.fullName],
        $boolean), ' - ')  , 'value': {'employeeCode': $item.employeeId,
        'employeeRecordNumber': $string($item.employeeRecordNumber)}}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - companyIds
        - legalEntityIds
        - businessUnitIds
        - divisionIds
        - departmentIds
    companiesList:
      uri: '"/api/companies"'
      queryTransform: >-
        {'page': $.page,'limit': $.limit, 'filter':
        [{'field':'search','operator':
        '$eq','value':$.search},{'field':'status','operator':
        '$eq','value':true}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value':{'code': $item.code, 'id': $item.id}}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    legalEntityList:
      uri: '"/api/legal-entities"'
      queryTransform: >-
        {'page': $.page,'limit': $.limit, 'filter':
        [{'field':'search','operator':
        '$eq','value':$.search},{'field':'companyId','operator': '$eq','value':
        $.companyIds},{'field':'status','operator': '$eq','value':true}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value':{'code': $item.code, 'id': $item.id}}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - companyIds
    businessUnitList:
      uri: '"/api/business-units"'
      queryTransform: >-
        {'page': $.page,'limit': $.limit, 'filter':
        [{'field':'search','operator':
        '$eq','value':$.search},{'field':'companyId','operator': '$eq','value':
        $.companyIds},{'field':'status','operator': '$eq','value':true}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value':{'code': $item.code, 'id': $item.id}}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - companyIds
    divisionList:
      uri: '"/api/divisions"'
      queryTransform: >-
        {'page': $.page,'limit': $.limit, 'filter':
        [{'field':'search','operator':
        '$eq','value':$.search},{'field':'businessUnitId','operator':
        '$eq','value': $.businessUnitIds},{'field':'status','operator':
        '$eq','value':true}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value':{'code': $item.code, 'id': $item.id}}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - businessUnitIds
    departmentList:
      uri: '"/api/departments"'
      queryTransform: >-
        {'page': $.page,'limit': $.limit, 'filter':
        [{'field':'search','operator':
        '$eq','value':$.search},{'field':'divisionId','operator': '$eq','value':
        $.divisionIds},{'field':'status','operator': '$eq','value':true}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value':{'code': $item.code, 'id': $item.id}}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - divisionIds
layout_options:
  expand_filter: true
  toolTable:
    export: true
    import: true
    adjustDisplay: true
  tool_table:
    - id: import
      icon: icon-upload-simple-bold
      href: /GE/HR.FS.FR.092
      paramsRedirect:
        type: TS_OBJECT
        entityOrObj: TSAjustWorkingHours
    - id: export
      icon: icon-download-simple-bold
  table_toasts:
    - text: Choose the records to save.
      type: info
      icon: icon-info-bold
  custom_get_list_api:
    url: /api/ts-ajust-working-hours/filter
    method: POST
  custom_update_api:
    url: /api/ts-ajust-working-hours/batch
layout_options__header_buttons: null
options: null
create_form: null
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: hidden
backend_url: api/ts-ajust-working-hours
screen_name: null
layout_options__actions_many:
  - id: edit
    title: Save
    icon: icon-floppy-disk
    type: secondary
  - id: reset
    title: Reset
    icon: icon-clock-clockwise
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key: null
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Edit Work Schedule
  parent:
    title: Parent Demo
