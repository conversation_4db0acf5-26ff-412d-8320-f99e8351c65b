id: TS.FS.FR.016
status: draft
sort: 115
user_created: 56e71b91-dd39-4ebc-afd1-b4f00f38d4a5
date_created: '2024-07-22T11:04:53.912Z'
user_updated: abd14398-1d53-4e6f-9834-26328f0ebde0
date_updated: '2025-06-19T03:28:24.981Z'
title: Set Holiday Regulations
requirement:
  time: 1747020735309
  blocks:
    - id: Xoo-oiSs0V
      type: paragraph
      data:
        text: >-
          - Chức năng cho phép bộ phận nhân sự tập đoàn thiết lập các quy định
          cho từng loại nghỉ tương ứng theo quy định của tập đoàn/ CTTV
    - id: hlb6az2zka
      type: paragraph
      data:
        text: >-
          - Hệ thống kiểm tra thông tin và không cho phép tạo mới/ chỉnh sửa nếu
          thông tin thiết lập trùng thông tin về “Loại ngày nghỉ” và “<PERSON><PERSON><PERSON> hiệ<PERSON>
          lự<PERSON>” với các thiết lập trước đ<PERSON>. 
    - id: HxaDbKi1YE
      type: paragraph
      data:
        text: >-
          - Hệ thống chỉ cho phép sửa/ xóa dữ liệu loại nghỉ khi dữ liệu đó chưa
          được sử dụng trên hệ thống. 
    - id: 1JHDJW6-ZQ
      type: paragraph
      data:
        text: >-
          - Hệ thống cho phép nhập dữ liệu hàng loạt (import) thông tin thiết
          lập quy định các loại nghỉ. 
    - id: JH6MyITX28
      type: paragraph
      data:
        text: >-
          - Khi CBNV đăng ký nghỉ, hệ thống kiểm tra các điều kiện ràng buộc về
          các giới hạn số ngày tối đa/tối thiểu được đăng ký theo lần/tháng/năm
          được thiết lập để cảnh báo và xử lý theo ràng buộc tương ứng chi tiết
          mô tả tại chức năng TS.FR.021 – CBNV tạo đơn nghỉ cá nhân 
  version: 2.30.7
screen_design: null
module: TS
local_fields:
  - code: nationName
    title: Country
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    default_display: true
    pinned: true
    show_sort: false
  - code: groupName
    title: Group
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    default_display: true
  - code: companyName
    title: Company
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    default_display: true
    options__tabular__column_width: null
  - code: legalEntityName
    title: Legal Entity
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    default_display: true
    options__tabular__column_width: null
  - code: effectiveDate
    title: Effective Start Date
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    default_display: true
  - code: effectiveDateTo
    title: Effective End Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
  - code: dayOffTypeName
    title: Absence Type
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    default_display: true
  - code: dayOffTypeGroupName
    title: Absence Type Group
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    default_display: true
  - code: forOnlineRegistration
    title: Enable for Online Registration
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: YesNo
      collection: field_types
  - code: getPaid
    title: Paid Leave
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: YesNo
      collection: field_types
  - code: note
    title: Note
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
  - code: updatedAt
    title: Last Updated On
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
  - code: updatedBy
    title: Last Updated By
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
mock_data:
  - country: Viet Nam
    group: FPT Corp
    company: Công ty TNHH phần mềm FPT (FSOFT)
    legalEntity: Công ty TNHH phần mềm FPT (FSOFT)
    absenceTypes: Nghỉ phép
    absenceGroup: Tháng
    unitType: Theo ngày
    salaryAvailable: Có
    benefitLevel: '5000000'
    howToCalculateRestTime:
      includeHolidays: true
      includeRestDays: true
    usedForOnlineRegistrationProcess: Có
    leaveFundCalculationCycle: Tháng
    generalSettings:
      - parameters: Đơn vị đăng ký nghỉ nhỏ nhất
        value: '2'
        unit: Days
      - parameters: Khoảng cách tối thiểu giữa 02 event nghỉ
        value: '2'
        unit: Months
    setConditions:
      conditionsApply:
        conditionsApplyArray:
          - parameters: And
            criteria: Effective as of
            logicOperators: '='
            condition: Nam
      priorityOrder:
        priorityOrderArray:
          - stt: 1
            alternativeHolidayType: And
    note: This is a mock note.
    effectiveDate: 01/10/2024
    periodStartDate: 01/11/2024
  - country: Japan
    group: FPT Corp
    company: Công ty TNHH phần mềm FPT (FSOFT)
    legalEntity: Công ty TNHH FPT IS (FIS)
    absenceTypes: Nghỉ lễ
    absenceGroup: Quý
    unitType: Theo giờ
    salaryAvailable: Không
    benefitLevel: '2000000'
    howToCalculateRestTime:
      includeHolidays: false
      includeRestDays: true
    usedForOnlineRegistrationProcess: Không
    leaveFundCalculationCycle: Năm
    generalSettings:
      - parameters: Tổng quỹ nghỉ trong chu kỳ
        value: '10'
        unit: Days
      - parameters: Cho phép đăng ký nhiều đơn trong ngày
        value: 'Yes'
        unit: ''
    setConditions:
      conditionsApply:
        conditionsApplyArray:
          - parameters: Or
            criteria: First name
            logicOperators: like
            condition: John
      priorityOrder:
        priorityOrderArray:
          - stt: 1
            alternativeHolidayType: Or
    note: This is another mock note.
    effectiveDate: 01/10/2024
    periodStartDate: 01/11/2024
  - country: China
    group: FPT Corp
    company: Công ty TNHH FPT IS (FIS)
    legalEntity: Công ty TNHH phần mềm FPT (FSOFT)
    absenceTypes: Nghỉ phép
    absenceGroup: Năm
    unitType: Theo ngày
    salaryAvailable: Có
    benefitLevel: '3000000'
    howToCalculateRestTime:
      includeHolidays: true
      includeRestDays: false
    usedForOnlineRegistrationProcess: Có
    leaveFundCalculationCycle: 6 tháng
    generalSettings:
      - parameters: Đơn vị đăng ký nghỉ nhỏ nhất
        value: '1'
        unit: Days
      - parameters: Cho phép đăng ký nhiều đơn trong ngày
        value: 'No'
        unit: ''
    setConditions:
      conditionsApply:
        conditionsApplyArray:
          - parameters: And
            criteria: Middle name
            logicOperators: '>'
            condition: A
      priorityOrder:
        priorityOrderArray:
          - stt: 1
            alternativeHolidayType: And
    note: Mock note 3.
    effectiveDate: 01/10/2024
    periodStartDate: 01/11/2024
  - country: Viet Nam
    group: FPT Corp
    company: Công ty TNHH phần mềm FPT (FSOFT)
    legalEntity: Công ty TNHH FPT IS (FIS)
    absenceTypes: Nghỉ lễ
    absenceGroup: 6 tháng
    unitType: Theo giờ
    salaryAvailable: Không
    benefitLevel: '1000000'
    howToCalculateRestTime:
      includeHolidays: false
      includeRestDays: false
    usedForOnlineRegistrationProcess: Không
    leaveFundCalculationCycle: Tháng
    generalSettings:
      - parameters: Khoảng cách tối thiểu giữa 02 event nghỉ
        value: '3'
        unit: Months
      - parameters: Cho phép đăng ký nhiều đơn trong ngày
        value: 'Yes'
        unit: ''
    setConditions:
      conditionsApply:
        conditionsApplyArray:
          - parameters: Or
            criteria: Last name
            logicOperators: <
            condition: Z
      priorityOrder:
        priorityOrderArray:
          - stt: 1
            alternativeHolidayType: Or
    note: Mock note 4.
    effectiveDate: 01/10/2024
    periodStartDate: 01/11/2024
  - country: Japan
    group: FPT Corp
    company: Công ty TNHH phần mềm FPT (FSOFT)
    legalEntity: Công ty TNHH phần mềm FPT (FSOFT)
    absenceTypes: Nghỉ phép
    absenceGroup: Tháng
    unitType: Theo ngày
    salaryAvailable: Có
    benefitLevel: '4000000'
    howToCalculateRestTime:
      includeHolidays: true
      includeRestDays: true
    usedForOnlineRegistrationProcess: Có
    leaveFundCalculationCycle: 6 tháng
    generalSettings:
      - parameters: Đơn vị đăng ký nghỉ nhỏ nhất
        value: '3'
        unit: Days
      - parameters: Cho phép đăng ký nhiều đơn trong ngày
        value: 'No'
        unit: ''
    setConditions:
      conditionsApply:
        conditionsApplyArray:
          - parameters: And
            criteria: Effective as of
            logicOperators: '='
            condition: April
      priorityOrder:
        priorityOrderArray:
          - stt: 1
            alternativeHolidayType: And
    note: Mock note 5.
    effectiveDate: 01/10/2024
    periodStartDate: 01/11/2024
local_buttons: null
layout: layout-table
form_config:
  _formTitle:
    edit: '''Edit Absence Type Rule'''
  formSize:
    create: large
    edit: large
    view: middle
  fields:
    - type: group
      label: Basic Information
      collapse: false
      disableEventCollapse: true
      n_cols: 2
      fields:
        - name: nationId
          label: Country
          type: select
          outputValue: value
          placeholder: Select Country
          _select:
            transform: $.variables._countryList
        - name: groupId
          label: Group
          type: select
          clearFieldsAfterChange:
            - companyId
            - legalEntityId
          outputValue: value
          placeholder: Select Group
          _select:
            transform: $.variables._groupsList
        - name: companyId
          label: Company
          type: selectCustom
          outputValue: value
          clearFieldsAfterChange:
            - legalEntityId
          placeholder: Select Company
          _select:
            transform: $.fields.groupId ? $.variables._companyList
        - name: legalEntityId
          label: Legal Entity
          type: selectCustom
          outputValue: value
          placeholder: Select Legal Entity
          _select:
            transform: $.fields.companyId ? $.variables._legalEntityList
        - type: select
          label: Absence Type
          name: dayOffTypeId
          outputValue: value
          placeholder: Select Absence Types
          _select:
            transform: $.variables._absenceTypeList
          validators:
            - type: required
        - label: Absence Type group
          name: absenceGroupId
          type: selectCustom
          outputValue: value
          placeholder: Absence Type group
          dependantField: $.fields.dayOffTypeId
          disabled: true
          _select:
            transform: $.variables._absenceTypeGroupList
        - name: absenceGroupId
          type: text
          unvisible: true
          _value:
            transform: $.variables._selectedabsenceType.caTypeOfDayOffGroupId
        - type: dateRange
          label: Effective Start Date
          name: effectiveDate
          mode: date-picker
          setting:
            format: dd/MM/yyyy
          validators:
            - type: required
          _value:
            transform: '$.extend.formType = ''create'' ? $now() '
        - type: dateRange
          name: effectiveDateTo
          label: Effective End Date
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
            autoFill: end-off
            _disabledDate:
              transform: '{''operator'': ''$lt'', ''value'': $.fields.effectiveDate}'
          validators:
            - type: ppx-custom
              args:
                transform: >-
                  $exists($.fields.effectiveDate) and
                  $exists($.fields.effectiveDateTo) and
                  $DateDiff($DateFormat($.fields.effectiveDate, 'yyyy-MM-DD'),
                  $DateFormat($.fields.effectiveDateTo, 'yyyy-MM-DD'), 'd') > 0
              text: Effective end date must be greater than effective start date.
        - type: radio
          label: Paid Leave
          name: getPaid
          col: 2
          _value:
            transform: ($.extend.formType = 'create' ? true)
          validators:
            - type: required
          radio:
            - value: true
              label: 'Yes'
            - value: false
              label: 'No'
        - type: group
          label: Leave Time Calculation Method
          n_cols: 2
          space: 4
          padding: 0
          fieldGroupContentStyle:
            gap: 8px !important
          fields:
            - type: checkbox
              name: includeHoliday
              label: Includes Holidays
              hiddenLabel: true
              col: 1
            - type: checkbox
              label: Includes Weekends
              name: includeDayOff
              hiddenLabel: true
              col: 1
        - type: switch
          label: Enable for Online Registration
          name: forOnlineRegistration
          _value:
            transform: $not($.extend.formType = 'create' ? true)
          validators:
            - type: required
        - type: select
          label: Leave Fund Calculation Cycle
          name: calculateCycles
          outputValue: value
          placeholder: Select Leave Fund Calculation Cycle
          select:
            - value: '1'
              label: Tháng
            - value: '2'
              label: Quý
            - value: '3'
              label: Năm
            - value: '4'
              label: 6 tháng
        - type: dateRange
          label: Cycle Start Date
          name: cyclesStartDate
          mode: date-picker
          placeholder: DD/MM/YYYY
          setting:
            format: dd/MM/yyyy
          _class:
            transform: >-
              ($exists($.fields.calculateCycles) and
              $string($.fields.calculateCycles) != 'null' and
              $string($.fields.calculateCycles) != '') ? 'required' :
              'unrequired' 
          validators:
            - type: ppx-custom
              args:
                transform: >-
                  ($exists($.fields.calculateCycles) and
                  $string($.fields.calculateCycles) != 'null' and
                  $string($.fields.calculateCycles) != '') and
                  ($length($.fields.cyclesStartDate) = 0 or
                  $not($exists($.fields.cyclesStartDate)))
              text: Cycle Start Date is required.
        - type: textarea
          label: Note
          name: note
          placeholder: Enter Note
          col: 2
          textarea:
            maxCharCount: 1000
            autoSize:
              minRows: 2
          validators:
            - type: maxLength
              args: '1000'
              text: Maximum length is 1000 characters.
      _condition:
        transform: $.extend.formType = 'create' or $.extend.formType = 'edit'
    - type: group
      label: Basic Information
      collapse: false
      disableEventCollapse: true
      fields:
        - name: nationName
          label: Country
          type: text
        - name: groupName
          label: Group
          type: text
        - name: companyName
          label: Company
          type: text
        - name: legalEntityName
          label: Legal Entity
          type: text
        - name: effectiveDate
          label: Effective Start Date
          type: dateRange
          mode: date-picker
          setting:
            format: dd/MM/yyyy
        - name: effectiveDateTo
          label: Effective End Date
          type: dateRange
          mode: date-picker
          setting:
            format: dd/MM/yyyy
        - name: dayOffTypeName
          label: Absence Type
          type: text
        - name: dayOffTypeGroupName
          label: Absence Type Group
          type: text
        - name: getPaid
          label: Paid Leave
          type: select
          outputValue: value
          select:
            - value: true
              label: 'Yes'
            - value: false
              label: 'No'
        - label: Leave Time Calculation Method
          type: select
          _value:
            transform: $.variables._leaveTimeCalculationMethod
          select:
            - value: truetrue
              label: Có bao gồm ngày lễ, Có bao gồm ngày nghỉ
            - value: falsetrue
              label: Không bao gồm ngày lễ, Có bao gồm ngày nghỉ
            - value: falsefalse
              label: Không bao gồm ngày lễ, Không bao gồm ngày nghỉ
            - value: truefalse
              label: Có bao gồm ngày lễ, Không bao gồm ngày nghỉ
        - name: includeHoliday
          type: select
          outputValue: value
          unvisible: true
          select:
            - value: true
              label: Có bao gồm ngày lễ
            - value: false
              label: Không bao gồm ngày lễ
        - name: includeDayOff
          type: select
          outputValue: value
          unvisible: true
          select:
            - value: true
              label: Có bao gồm ngày nghỉ
            - value: false
              label: Không bao gồm ngày nghỉ
        - name: forOnlineRegistration
          label: Enable for Online Registration
          type: select
          outputValue: value
          select:
            - value: true
              label: 'Yes'
            - value: false
              label: 'No'
        - name: calculateCycles
          label: Leave Fund Calculation Cycle
          type: select
          outputValue: value
          placeholder: Select Leave Fund Calculation Cycle
          select:
            - value: '1'
              label: Tháng
            - value: '2'
              label: Quý
            - value: '3'
              label: Năm
            - value: '4'
              label: 6 tháng
        - name: cyclesStartDate
          label: Cycle Start Date
          type: dateRange
          mode: date-picker
          setting:
            format: dd/MM/yyyy
        - name: note
          label: Note
          type: textarea
      _condition:
        transform: $.extend.formType = 'view'
    - type: group
      label: Setting Infomation
      collapse: false
      disableEventCollapse: true
      fields:
        - type: array
          name: parameters
          mode: table
          _defaultRowIndex:
            transform: >-
              $.variables._selectedabsenceType.caTypeOfDayOffGroupId = 'LOA' ?
              -1 : 0
          arrayOptions:
            canChangeSize: true
          field:
            type: group
            fields:
              - type: select
                label: Parameters
                name: name
                placeholder: Select Parameters
                outputValue: value
                validators:
                  - type: ppx-custom
                    args:
                      transform: >-
                        $count($queryInArray($.fields.parameters, {'name':
                        $.value}))>1
                    text: This parameter already exists
                  - type: required
                _disabled:
                  transform: >-
                    ($idx := $.extend.path[-2]; $idx = 0 ?
                    $.variables._selectedabsenceType.caTypeOfDayOffGroupId =
                    'LOA' ? false : true : false)
                _select:
                  transform: $.variables._tsParametersList
                _value:
                  transform: >-
                    ($idx := $.extend.path[-2]; $idx = 0 and $.extend.formType =
                    'create' ?
                    ($not($.variables._selectedabsenceType.caTypeOfDayOffGroupId
                    = 'LOA') ? '00000001'))
                width: 250px
              - name: value
                label: Usable value of the parameter
                type: number
                positionContentTable: right
                placeholder: Enter Value
                number:
                  format: currency
                validators:
                  - type: required
                _value:
                  transform: >-
                    ($idx:= $.extend.path[-2];$value :=
                    $.fields.parameters[$idx].name;
                    $isNilorEmpty($.fields.parameters[$idx].value) ?
                    ($.variables._tsParametersList[value=$value])[0].amount)
                  dependants:
                    - $.fields.parameters[$idx].name
                  params:
                    $idx: $.extend.path[-1]
                width: 250px
              - name: unit
                label: Unit
                type: select
                outputValue: value
                placeholder: Select Unit
                width: 170px
                _select:
                  transform: $.variables._unitList
                _disabled:
                  transform: >-
                    ($idx := $.extend.path[-2]; $idx = 0 ?
                    $.variables._selectedabsenceType.caTypeOfDayOffGroupId =
                    'LOA' ? false : true : false)
                _value:
                  transform: >-
                    ($idx:= $.extend.path[-2];
                    $value:=$.fields.parameters[$idx].name;
                    ($not($.variables._selectedabsenceType.caTypeOfDayOffGroupId=
                    'LOA') and $idx = 0) ?
                    ($.variables._tsParametersList[value=$value])[0].unit :
                    ($isNilorEmpty($.fields.parameters[$idx].unit) ?
                    ($.variables._tsParametersList[value=$value])[0].unit))
                  dependants:
                    - $.fields.parameters[$idx].name
                    - $.variables._tsParametersList
                  params:
                    $idx: $.extend.path[-1]
    - type: text
      name: firstLoad
      value: '1'
      unvisible: true
      dependantField: $.fields.dayOffTypeId
      dependantFieldSkip: 2
    - type: group
      label: Set Conditions
      collapse: false
      disableEventCollapse: true
      fields:
        - type: group
          fields:
            - type: group
              collapsed: true
              disableEventCollapse: true
              label: Conditions Apply
              hostStyle:
                marginTop: 16px
              renderChildFields: true
              fields:
                - type: group
                  label: Setup Instructions
                  n_cols: 1
                  collapse: true
                  readOnly: true
                  border: '1px solid #DFE3E8'
                  borderRadius: 8px
                  borderBottomLabel: true
                  lastGroupStyleOff: true
                  fieldGroupTitleStyle:
                    padding: 8px 12px
                  fieldGroupContentStyle:
                    padding: 12px
                  fields:
                    - type: group
                      label: Điều kiện AND
                      name: conditionAnd
                      readOnly: true
                      fieldBackground: '#F8F9FA'
                      padding: 10px 12px
                      borderRadius: 8px
                      lastGroupStyleOff: true
                      isLeftCollapse: true
                      n_cols: 1
                      collapse: true
                      fields:
                        - type: text
                          label: ''
                          readOnly: true
                          padding: 0 0 0 32px
                          value: >-
                            Toán tử AND (và) yêu cầu tất cả các điều kiện phải
                            đúng thì kết quả cuối cùng mới là đúng. Nếu bất kỳ
                            điều kiện nào trong số các điều kiện được kết hợp
                            bởi AND là sai, thì kết quả cuối cùng sẽ là sai.
                    - type: group
                      label: Điều kiện OR
                      name: conditionOr
                      readOnly: true
                      fieldBackground: '#F8F9FA'
                      padding: 10px 12px
                      lastGroupStyleOff: true
                      isLeftCollapse: true
                      borderRadius: 8px
                      n_cols: 1
                      collapse: true
                      fields:
                        - type: text
                          label: ''
                          readOnly: true
                          padding: 0 0 0 32px
                          value: >-
                            Toán tử OR (hoặc) yêu cầu ít nhất một trong các điều
                            kiện phải đúng thì kết quả cuối cùng sẽ là đúng
                    - type: group
                      label: Điều kiện AND và OR
                      name: conditionAndOr
                      readOnly: true
                      lastGroupStyleOff: true
                      isLeftCollapse: true
                      fieldBackground: '#F8F9FA'
                      padding: 10px 12px
                      borderRadius: 8px
                      n_cols: 1
                      collapse: true
                      fields:
                        - type: text
                          label: ''
                          readOnly: true
                          padding: 0 0 0 32px
                          value: 'Thứ tự thực hiện: AND được thực hiện trước OR'
                  _condition:
                    transform: $.extend.formType != 'view'
                - type: array
                  name: conditions
                  mode: table
                  hostStyle:
                    marginTop: 8px
                  arrayOptions:
                    canChangeSize: true
                  field:
                    type: group
                    fields:
                      - name: sequence
                        label: No.
                        type: text
                        width: 60px
                        _value:
                          transform: $ParseInt($.extend.path[-2]) + 1
                        readOnly: true
                      - name: conditionOperator
                        label: Combined Conditions
                        type: select
                        outputValue: value
                        placeholder: Enter Combined Conditions
                        _class:
                          transform: >-
                            ($idx := $.extend.path[-2]; $idx = 0 ? 'unrequired'
                            : 'required')
                        select:
                          - value: AND
                            label: AND
                          - value: OR
                            label: OR
                        _disabled:
                          transform: '($idx := $.extend.path[-2]; $idx = 0 ? true : false)'
                        width: 250px
                      - name: criteria
                        label: Criteria
                        type: select
                        outputValue: value
                        placeholder: Select Criteria
                        validators:
                          - type: required
                        select:
                          - value: GENDER
                            label: Gender
                          - value: WORKINGSTATUS
                            label: Working Status
                          - value: CONTRACTTYPE
                            label: Contract Type
                          - value: MARITALSTATUS
                            label: Marital Status
                          - value: EMPLEVEL
                            label: Employee Level
                        width: 250px
                      - name: logicalOperator
                        label: Logic/Operators
                        type: select
                        outputValue: value
                        placeholder: Select Logic/Operators
                        width: 150px
                        validators:
                          - type: required
                        select:
                          - value: '='
                            label: '='
                          - value: '>'
                            label: '>'
                          - value: <
                            label: <
                          - value: '><'
                            label: '><'
                          - value: like
                            label: like
                      - name: conditionValue
                        label: Condition
                        type: select
                        outputValue: value
                        placeholder: Choose Condition
                        width: 250px
                        validators:
                          - type: required
                        _select:
                          transform: >-
                            $getIdx($.fields.conditions,
                            $.extend.path[-2]).criteria ?
                            $pickList($getIdx($.fields.conditions,
                            $.extend.path[-2]).criteria)
            - type: group
              collapsed: false
              label: Priority order of types of holidays
              _condition:
                transform: $.extend.formType != 'view'
              hostStyle:
                marginTop: 16px
              renderChildFields: true
              fields:
                - type: array
                  name: dateTypePriorities
                  mode: table
                  arrayOptions:
                    canChangeSize: true
                  field:
                    type: group
                    fields:
                      - name: sequence
                        label: No.
                        type: text
                        width: 150px
                        _value:
                          transform: $ParseInt($.extend.path[-2]) + 1
                        readOnly: true
                      - type: select
                        name: dayOffTypeId
                        label: Alternative holiday type
                        placeholder: Select Alternative holiday type
                        outputValue: value
                        validators:
                          - type: required
                          - type: ppx-custom
                            args:
                              transform: >-
                                $count($queryInArray($.fields.dateTypePriorities,
                                {'dayOffTypeId': $.value}))>1
                            text: This Alternative holiday already exists
                        _select:
                          transform: $.variables._absenceTypeList
                        width: 430px
            - type: group
              collapsed: false
              label: Priority order of types of holidays
              _condition:
                transform: $.extend.formType = 'view'
              hostStyle:
                marginTop: 16px
              renderChildFields: true
              fields:
                - type: array
                  name: dateTypePriorities
                  mode: table
                  arrayOptions:
                    canChangeSize: true
                  field:
                    type: group
                    fields:
                      - name: sequence
                        label: No.
                        type: text
                        width: 60px
                        _value:
                          transform: $ParseInt($.extend.path[-2]) + 1
                        readOnly: true
                      - type: text
                        name: dayOffTypeName
                        label: Alternative holiday type
          mode: tabset
  _mode:
    transform: $.extend.formType != 'view' ? 'mark-scroll'
  footer:
    create: true
    update: true
    createdOn: createdAt
    updatedOn: updatedAt
    createdBy: createdBy
    updatedBy: updatedBy
  sources:
    countryList:
      uri: '"/api/picklists/COUNTRIES/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
    groupsList:
      uri: '"/api/groups/get-by"'
      queryTransform: >-
        {'page':1,'limit':10000,'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - effectiveDate
    companiesList:
      uri: '"/api/companies/get-by"'
      queryTransform: >-
        {'page':1,'limit':10000,'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true},{'field':'groupCode','operator': '$eq','value':
        $.groupCode }]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - effectiveDate
        - groupCode
    legalEntityList:
      uri: '"/api/legal-entities/get-list"'
      queryTransform: >-
        {'page':1,'limit':10000,'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true},{'field':'companyCode','operator': '$eq','value':
        $.companyCode }]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - effectiveDate
        - companyCode
    absenceTypeList:
      uri: '"/api/ca-type-of-days-offs/list-data"'
      queryTransform: >-
        {'filter': [{'field':'effectiveDate','operator': '$eq','value':
        $.effectiveDate},{'field':'nationId','operator': '$eq','value':
        $.nationId},{'field':'groupId','operator': '$eq','value':
        $.groupId},{'field':'companyId','operator': '$eq','value':
        $.companyId}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code , 'caTypeOfDayOffGroupId':
        $item.caTypeOfDayOffGroupId }})[]
      disabledCache: true
      params:
        - effectiveDate
        - nationId
        - groupId
        - companyId
    absenceTypeGroupList:
      uri: '"/api/ca-type-of-day-off-groups"'
      queryTransform: >-
        {'filter': [{'field':'code','operator': '$eq','value':
        $.caTypeOfDayOffGroupId}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - caTypeOfDayOffGroupId
    tsParametersList:
      uri: '"/api/ts-parameters"'
      queryTransform: >-
        {'page':1,'limit':10000,'filter': [{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator': '$lte','value':
        $.effectiveDate}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.caparameterName & ' (' &
        $item.caparameterId & ')', 'value': $item.caparameterId, 'amount':
        $item.value , 'unit': $item.unit }})[]
      disabledCache: true
      params:
        - effectiveDate
    unitList:
      uri: '"/api/picklists/TSUNIT/values"'
      method: GET
      queryTransform: '{''filter'': [{''field'':''status'',''operator'': ''$eq'',''value'':true}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
    pickList:
      uri: '"/api/picklists/" & $.code  & "/values"'
      method: GET
      queryTransform: '{''filter'': [{''field'':''status'',''operator'': ''$eq'',''value'':true}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - code
  variables:
    _countryList:
      transform: $countryList()
    _groupsList:
      transform: $groupsList($.fields.effectiveDate)
    _companyList:
      transform: >-
        $.fields.groupId ? $companiesList($.fields.effectiveDate,
        $.fields.groupId)
    _legalEntityList:
      transform: >-
        $.fields.companyId ? $legalEntityList($.fields.effectiveDate,
        $.fields.companyId)
    _absenceTypeList:
      transform: >-
        $absenceTypeList($.fields.effectiveDate,$.fields.nationId,$.fields.groupId,$.fields.companyId)
    _selectedabsenceType:
      transform: >-
        ( $filter($.variables._absenceTypeList , function($v, $i, $a) { $v.value
        = $.fields.dayOffTypeId }))
    _absenceTypeGroupList:
      transform: >-
        $absenceTypeGroupList($.variables._selectedabsenceType.caTypeOfDayOffGroupId)
    _tsParametersList:
      transform: $tsParametersList($.fields.effectiveDate)
    _includeHoliday:
      transform: '$.fields.includeHoliday ? $.fields.includeHoliday : false'
    _includeDayOff:
      transform: '$.fields.includeDayOff ? $.fields.includeDayOff : false'
    _leaveTimeCalculationMethod:
      transform: $.variables._includeHoliday & $.variables._includeDayOff
    _unitList:
      transform: $unitList()
filter_config:
  fields:
    - type: selectAll
      label: Country
      name: country
      mode: multiple
      placeholder: Select Country
      isLazyLoad: true
      _options:
        transform: $countryList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      label: Group
      name: group
      placeholder: Select Group
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $groupsList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      label: Company
      name: company
      placeholder: Select Company
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $companiesList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      label: Legal Entity
      name: legalEntity
      placeholder: Select Legal Entity
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $legalEntityList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      label: Absence Type
      name: absenceTypes
      placeholder: Select Absence Types
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $absenceTypeList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      label: Absence Type Group
      name: absenceGroup
      placeholder: Select Absence group
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $absenceTypeGroupList($.extend.limit, $.extend.page, $.extend.search)
    - type: dateRange
      label: Effective date
      name: effectiveDate
      setting:
        format: dd/MM/yyyy
    - type: radio
      label: Leave unit
      name: unitType
      radio:
        - value: ''
          label: All
        - value: D
          label: Days
        - value: H
          label: Hours
    - type: radio
      label: Enable for Online Registration
      name: forOnlineRegistration
      radio:
        - value: ''
          label: All
        - value: true
          label: 'Yes'
        - value: false
          label: 'No'
    - type: radio
      label: Paid Leave
      name: paidLeave
      radio:
        - value: ''
          label: All
        - value: true
          label: 'Yes'
        - value: false
          label: 'No'
    - type: group
      label: Leave Time Calculation Method
      n_cols: 2
      fields:
        - type: checkbox
          name: includeHoliday
          label: Includes Holidays
          hiddenLabel: true
          col: 1
        - type: checkbox
          label: Includes Weekends
          name: includeDayOff
          hiddenLabel: true
          col: 1
    - type: dateRange
      label: Created On
      name: createdAt
      setting:
        format: dd/MM/yyyy
    - type: select
      label: Created By
      name: createdBy
      placeholder: Select Created By
      mode: multiple
      _select:
        transform: $userList()
    - type: dateRange
      label: Last Updated On
      name: updatedAt
      setting:
        format: dd/MM/yyyy
    - type: select
      label: Last Updated By
      name: updatedBy
      placeholder: Select Last Updated By
      mode: multiple
      _select:
        transform: $userList()
  filterMapping:
    - field: nationId
      operator: $in
      valueField: country.(value)
    - field: groupId
      operator: $in
      valueField: group.(value)
    - field: companyId
      operator: $in
      valueField: company.(value)
    - field: legalEntityId
      operator: $in
      valueField: legalEntity.(value)
    - field: dayOffTypeId
      operator: $in
      valueField: absenceTypes.(value)
    - field: dayOffTypeGroupId
      operator: $in
      valueField: absenceGroup.(value)
    - field: unitType
      operator: $eq
      valueField: unitType
    - field: forOnlineRegistration
      operator: $eq
      valueField: forOnlineRegistration
    - field: getPaid
      operator: $eq
      valueField: paidLeave
    - field: includeHoliday
      operator: $eq
      valueField: includeHoliday
    - field: includeDayOff
      operator: $eq
      valueField: includeDayOff
    - field: effectiveDate
      operator: $between
      valueField: effectiveDate
    - field: createdBy
      operator: $in
      valueField: createdBy.(value)
    - field: createdAt
      operator: $between
      valueField: createdAt
    - field: updatedBy
      operator: $in
      valueField: updatedBy.(value)
    - field: updatedAt
      operator: $between
      valueField: updatedAt
  sources:
    countryList:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    groupsList:
      uri: '"/api/groups"'
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    companiesList:
      uri: '"/api/companies"'
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    legalEntityList:
      uri: '"/api/legal-entities"'
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    absenceTypeList:
      uri: '"/api/ca-type-of-days-offs"'
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code , 'caTypeOfDayOffGroupId':
        $item.caTypeOfDayOffGroupId }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    absenceTypeGroupList:
      uri: '"/api/ca-type-of-day-off-groups"'
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    userList:
      uri: '"/api/internal-users?page=1&limit=10000"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.userName, 'value':
        $item.userName}})[]
      disabledCache: true
layout_options:
  tool_table:
    - id: export
      icon: icon-download-simple
  show_detail_history: false
  show_dialog_form_save_add_button: true
  export_all:
    type: base_total
  hide_action_row: true
  show_dialog_duplicate_button: false
  delete_multi_items: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    icon: icon-pencil-simple
    type: ghost-gray
  - id: delete
    icon: icon-trash
    type: ghost-gray
backend_url: api/ts-set-dayoff-type-rules
screen_name: null
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: companyId
    defaultName: CompanyCode
  - name: nationId
    defaultName: CountryCode
  - name: legalEntityId
    defaultName: LegalEntityCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Set Up Holiday Regulations
  parent:
    title: Configuration
