id: TS.FS.FR.018
status: draft
sort: 117
user_created: 59722cb8-f858-481b-97f1-c97dd2a8e4be
date_created: '2024-07-04T03:18:04.602Z'
user_updated: abd14398-1d53-4e6f-9834-26328f0ebde0
date_updated: '2025-06-19T03:40:19.158Z'
title: Set Standard Leave Days
requirement:
  time: 1741919131753
  blocks:
    - id: Mb6bNe3-FL
      type: paragraph
      data:
        text: >-
          <PERSON><PERSON> thống cho phép bộ phận nhân sự tập đoàn thiết lập số ngày phép
          chuẩn theo quy định của Tập đoàn/ CTTV.
    - id: WX9ZR5WKrb
      type: paragraph
      data:
        text: >-
          <PERSON><PERSON> thống kiểm tra thông tin và không cho phép tạo mới/ chỉnh sửa nếu
          thông tin thiết lập trùng thông tin về “Thông tin đơn vị/phòng ban”,
          “<PERSON><PERSON><PERSON> bậc nh<PERSON> viên”, “Người Việt Nam/ Người nướ<PERSON> ngoài”, “<PERSON>ày hiệu
          lực” với các thiết lập trước đó.&nbsp;
    - id: 4bZ4HTucv9
      type: paragraph
      data:
        text: >-
          Hệ thống cho phép nhập dữ liệu hàng loạt (import) thông tin thiết lập
          số ngày phép chuẩn của từng cơ cấu tổ chức. 
    - id: Kgm8Hh2Tqi
      type: paragraph
      data:
        text: >-
          Hệ thống sẽ căn cứ vào thông tin nhân viên và thiết lập số phép chuẩn
          để tính toán ra số ngày phép năm của từng CBNV.
  version: 2.30.7
screen_design: null
module: TS
local_fields:
  - code: country
    title: Country
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: Load dữ liệu Quốc gia theo dữ liệu đã tạo
  - code: group
    title: Group
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: Load dữ liệu Tập đoàn theo dữ liệu đã tạo
  - code: company
    title: Company
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: Load dữ liệu Công ty thành viên theo dữ liệu đã tạo
  - code: legalEntityName
    title: Legal Entity
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: Load dữ liệu Pháp nhân theo dữ liệu đã tạo
  - code: localForeignersName
    title: Local/Foreign Employees
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: Load dữ liệu Người bản địa/ Người nước ngoài nhân viên
    options__tabular__column_width: 20
  - code: employeeGroupName
    title: ' Employee Group'
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: contractTypeName
    title: Contract Type
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: careerStreamName
    title: Career Stream
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: Load dữ liệu Ngạch theo dữ liệu nhập
  - code: employeeLevelName
    title: Employee Level
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: Load dữ liệu Cấp bậc nhân viên theo dữ liệu nhập
  - code: jobTitleName
    title: Job Title
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: effectiveDate
    title: Effective Start Date
    data_type:
      key: dd/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    default_sort: Sort Ascending
    description: Load dữ liệu Ngày hiệu lực từ theo dữ liệu nhập
  - code: effectiveDateTo
    title: Effective End Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
  - code: standardLeaveDate
    title: Standard Leave Date
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: Load dữ liệu Ngày phép chuẩn định theo dữ liệu nhập
    options__tabular__column_width: 12
  - code: note
    title: Note
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    description: Load dữ liệu Ghi chú đã tạo
  - code: updatedBy
    title: Last Updated By
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: Load thông tin người sửa mới nhất theo account đăng nhập
  - code: updatedAt
    title: Last Updated On
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
    description: Load thông tin ngày sửa dữ liệu lần cuối
mock_data:
  - country: Vietnam
    group: FPT
    company: FPT IS
    legalEntity: FPT IS HCM
    careerStream: Dev
    staffLevel: Level 2
    employeeGroup: Nhân viên chính thức
    contractType: Hợp đồng XĐTH 12 tháng
    jobTitle: Nhân viên kiểm thử phần mềm
    localForeigners: Local
    effectiveDate: 2024/01/01
    standardLeaveDate: '15'
    note: Test 123
    creator: Phuong Bui
    createTime: 2023/04/05
    lastEditer: Khanh Vy
    lastEditTime: 2024/05/09
  - country: Vietnam
    group: FPT
    company: FPT IS
    legalEntity: FPT IS HCM
    careerStream: Dev
    staffLevel: Level 2
    employeeGroup: Nhân viên chính thức
    contractType: Hợp đồng XĐTH 12 tháng
    jobTitle: Nhân viên kiểm thử phần mềm
    localForeigners: Local
    effectiveDate: 2024/01/01
    standardLeaveDate: '15'
    note: Test 123
    creator: Phuong Bui
    createTime: 2023/04/05
    lastEditer: Khanh Vy
    lastEditTime: 2024/05/09
  - country: Vietnam
    group: FPT
    company: FPT IS
    legalEntity: FPT IS HCM
    careerStream: Dev
    staffLevel: Level 2
    employeeGroup: Nhân viên chính thức
    contractType: Hợp đồng XĐTH 12 tháng
    jobTitle: Nhân viên kiểm thử phần mềm
    localForeigners: Local
    effectiveDate: 2024/01/01
    standardLeaveDate: '15'
    note: Test 123
    creator: Phuong Bui
    createTime: 2023/04/05
    lastEditer: Khanh Vy
    lastEditTime: 2024/05/09
  - country: Vietnam
    group: FPT
    company: FPT IS
    legalEntity: FPT IS HCM
    careerStream: Dev
    staffLevel: Level 2
    employeeGroup: Nhân viên chính thức
    contractType: Hợp đồng XĐTH 12 tháng
    jobTitle: Nhân viên kiểm thử phần mềm
    localForeigners: Local
    effectiveDate: 2024/01/01
    standardLeaveDate: '15'
    note: Test 123
    creator: Phuong Bui
    createTime: 2023/04/05
    lastEditer: Khanh Vy
    lastEditTime: 2024/05/09
  - country: Vietnam
    group: FPT
    company: FPT IS
    legalEntity: FPT IS HCM
    careerStream: Dev
    staffLevel: Level 2
    employeeGroup: Nhân viên chính thức
    contractType: Hợp đồng XĐTH 12 tháng
    jobTitle: Nhân viên kiểm thử phần mềm
    localForeigners: Local
    effectiveDate: 2024/01/01
    standardLeaveDate: '15'
    note: Test 123
    creator: Phuong Bui
    createTime: 2023/04/05
    lastEditer: Khanh Vy
    lastEditTime: 2024/05/09
  - country: Vietnam
    group: FPT
    company: FPT IS
    legalEntity: FPT IS HCM
    careerStream: Dev
    staffLevel: Level 2
    employeeGroup: Nhân viên chính thức
    contractType: Hợp đồng XĐTH 12 tháng
    jobTitle: Nhân viên kiểm thử phần mềm
    localForeigners: Local
    effectiveDate: 2024/01/01
    standardLeaveDate: '15'
    note: Test 123
    creator: Phuong Bui
    createTime: 2023/04/05
    lastEditer: Khanh Vy
    lastEditTime: 2024/05/09
  - country: Vietnam
    group: FPT
    company: FPT IS
    legalEntity: FPT IS HCM
    careerStream: Dev
    staffLevel: Level 2
    employeeGroup: Nhân viên chính thức
    contractType: Hợp đồng XĐTH 12 tháng
    jobTitle: Nhân viên kiểm thử phần mềm
    localForeigners: Local
    effectiveDate: 2024/01/01
    standardLeaveDate: '15'
    note: Test 123
    creator: Phuong Bui
    createTime: 2023/04/05
    lastEditer: Khanh Vy
    lastEditTime: 2024/05/09
local_buttons: null
layout: layout-table
form_config:
  formTitle:
    create: Add New Standard Leave Days
    view: View Standard Leave Day Details
  fields:
    - type: group
      n_cols: 2
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - name: countryCode
          label: Country
          type: select
          outputValue: value
          placeholder: Select Country
          isLazyLoad: true
          _select:
            transform: $nationsList($.extend.limit, $.extend.page, $.extend.search)
          _validateFn:
            transform: >-
              $.extend.defaultValue.country ? {'label':
              $.extend.defaultValue.country, 'value':
              $.extend.defaultValue.countryCode}
            params:
              updateLabelExistOption: true
        - type: select
          label: Group
          name: groupCode
          clearFieldsAfterChange:
            - companyId
            - legalEntityId
          placeholder: Select Group
          outputValue: value
          isLazyLoad: true
          _select:
            transform: >-
              $groupsList($.fields.effectiveDate, $.extend.limit, $.extend.page,
              $.extend.search)
          _validateFn:
            transform: >-
              $.extend.defaultValue.group ? {'label':
              $.extend.defaultValue.group & ' (' &
              $.extend.defaultValue.groupCode & ')', 'value':
              $.extend.defaultValue.groupCode}
            params:
              updateLabelExistOption: true
        - type: select
          label: Company
          name: companyId
          clearFieldsAfterChange:
            - legalEntityId
          isLazyLoad: true
          placeholder: Select Company
          outputValue: value
          _select:
            transform: >-
              $companiesList($.fields.effectiveDate, $.fields.groupCode,
              $.extend.limit, $.extend.page, $.extend.search)
          _validateFn:
            transform: >-
              $.extend.defaultValue.company ? {'label':
              $.extend.defaultValue.company & ' (' &
              $.extend.defaultValue.companyId & ')', 'value':
              $.extend.defaultValue.companyId}
            params:
              updateLabelExistOption: true
        - name: legalEntityId
          label: Legal Entity
          type: select
          outputValue: value
          placeholder: Select Legal Entity
          isLazyLoad: true
          _select:
            transform: >-
              $legalEntityList($.fields.effectiveDate, $.fields.companyId,
              $.extend.limit, $.extend.page, $.extend.search)
          _validateFn:
            transform: >-
              $.extend.defaultValue.legalEntityName ? {'label':
              $.extend.defaultValue.legalEntityName & ' (' &
              $.extend.defaultValue.legalEntityId & ')', 'value':
              $.extend.defaultValue.legalEntityId}
            params:
              updateLabelExistOption: true
        - name: localForeigners
          label: Local/Foreign Employees
          type: select
          placeholder: Select Local/Foreign Employees
          outputValue: value
          _select:
            transform: $localForeignersList()
        - name: employeeGroupCode
          label: Employee group
          type: select
          placeholder: Select Employee Group
          outputValue: value
          _select:
            transform: $employeeGroupsList($.fields.effectiveDate)
        - name: contractTypeCode
          label: Contract Type
          type: select
          placeholder: Select Contract Type
          outputValue: value
          _select:
            transform: $contractTypesList()
        - name: careerStreamCode
          label: Career Stream
          placeholder: Select Career Stream
          outputValue: value
          type: select
          isLazyLoad: true
          _select:
            transform: >-
              $careerStreamsList($.fields.effectiveDate, $.extend.limit,
              $.extend.page, $.extend.search)
          _validateFn:
            transform: >-
              $.extend.defaultValue.careerStreamName ? {'label':
              $.extend.defaultValue.careerStreamName & ' (' &
              $.extend.defaultValue.careerStreamCode & ')', 'value':
              $.extend.defaultValue.careerStreamCode}
            params:
              updateLabelExistOption: true
        - name: employeeLevelCode
          label: Employee Level
          placeholder: Select Employee Level
          type: select
          outputValue: value
          _select:
            transform: $levelsList()
        - name: jobTitleCode
          label: Job Title
          type: select
          placeholder: Select Job Title
          outputValue: value
          isLazyLoad: true
          _select:
            transform: >-
              $jobCodesList($.fields.companyId, $.extend.limit, $.extend.page,
              $.extend.search,$.fields.effectiveDate)
          _validateFn:
            transform: >-
              $.extend.defaultValue.jobTitleName ? {'label':
              $.extend.defaultValue.jobTitleName & ' (' &
              $.extend.defaultValue.jobTitleCode & ')', 'value':
              $.extend.defaultValue.jobTitleCode}
            params:
              updateLabelExistOption: true
        - type: dateRange
          label: Effective Start Date
          name: effectiveDate
          mode: date-picker
          setting:
            type: day
            format: dd/MM/yyyy
          _value:
            transform: $.extend.formType = 'create' ?  $now()
          validators:
            - type: required
        - type: dateRange
          name: effectiveDateTo
          label: Effective End Date
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
            autoFill: end-off
            _disabledDate:
              transform: '{''operator'': ''$lt'', ''value'': $.fields.effectiveDate}'
          validators:
            - type: ppx-custom
              args:
                transform: >-
                  $exists($.fields.effectiveDate) and
                  $exists($.fields.effectiveDateTo) and
                  $DateDiff($DateFormat($.fields.effectiveDate, 'yyyy-MM-DD'),
                  $DateFormat($.fields.effectiveDateTo, 'yyyy-MM-DD'), 'd') > 0
              text: Effective end date must be greater than effective start date.
        - name: standardLeaveDate
          label: Standard Leave Days
          type: number
          placeholder: Enter Standard Leave Days
          number:
            min: 0
            max: 50
          validators:
            - type: required
    - type: group
      n_cols: 1
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - name: country
          label: Country
          type: text
        - type: text
          label: Group
          name: group
        - type: text
          label: Company
          name: company
        - type: text
          label: Legal Entity
          name: legalEntityName
        - name: localForeigners
          label: Local/Foreign Employees
          type: select
          outputValue: value
          _select:
            transform: $localForeignersList()
        - name: employeeGroupName
          label: Employee group
          type: text
        - name: contractTypeName
          label: Contract Type
          type: text
        - name: careerStreamName
          label: Career Stream
          type: text
        - name: employeeLevelCode
          label: Employee Level
          type: select
          outputValue: value
          _select:
            transform: $levelsList()
        - name: jobTitleName
          label: Job Title
          type: text
        - type: dateRange
          label: Effective Start Date
          name: effectiveDate
          mode: date-picker
          setting:
            type: day
            format: dd/MM/yyyy
        - type: dateRange
          label: Effective End Date
          name: effectiveDateTo
          mode: date-picker
          setting:
            type: day
            format: dd/MM/yyyy
        - name: standardLeaveDate
          label: Standard Leave Days
          type: number
          placeholder: Enter Standard Leave Days
    - name: note
      label: Note
      type: textarea
      placeholder: Enter Note
      textarea:
        maxCharCount: 1000
        autoSize:
          minRows: 3
  sources:
    nationsList:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    groupsList:
      uri: '"/api/groups/get-by"'
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - effectiveDate
        - limit
        - page
        - search
    companiesList:
      uri: '"/api/companies/get-by"'
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true},{'field':'groupCode','operator': '$eq','value':
        $.groupCode }]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - effectiveDate
        - groupCode
        - limit
        - page
        - search
    legalEntityList:
      uri: '"/api/legal-entities/get-list"'
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true},{'field':'companyCode','operator': '$eq','value':
        $.companyId }]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - effectiveDate
        - companyId
        - limit
        - page
        - search
    careerStreamsList:
      uri: '"/api/career-streams/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - effectiveDate
        - limit
        - page
        - search
    levelsList:
      uri: '"/api/picklists/EMPLEVEL/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
    employeeGroupsList:
      uri: '"/api/employee-groups"'
      method: GET
      queryTransform: >-
        {'page':1,'limit':10000,'filter': [{'field':'status','operator':
        '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.employeeGroup.default & '
        (' & $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - effectiveDate
    contractTypesList:
      uri: '"/api/picklists/CONTRACTTYPE/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
    jobCodesList:
      uri: '"/api/job-codes/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator':
        '$eq','value':true},{'field':'companyCode','operator':
        '$eq','value':$.companyId},{'field':'effectiveDate','operator':
        '$eq','value': $.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id}})[]
      disabledCache: true
      params:
        - companyId
        - limit
        - page
        - search
        - effectiveDate
    jobProfilesList:
      uri: '"/api/job-profiles"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'effectiveDate','operator':
        '$lte','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.jobProfileName & ' (' &
        $item.jobProfileCode & ')', 'value': $item.jobProfileCode}})[]
      disabledCache: true
      params:
        - effectiveDate
        - limit
        - page
        - search
    nationalitysList:
      uri: '"/api/picklists/NATIONALITY/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
    localForeignersList:
      uri: '"/api/picklists/LOCALFOREIGNERS/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
  footer:
    create: true
    update: true
    createdOn: createdAt
    updatedOn: updatedAt
    createdBy: createdBy
    updatedBy: updatedBy
filter_config:
  fields:
    - name: countryCode
      label: Country
      type: selectAll
      mode: multiple
      labelType: type-grid
      placeholder: Select Country
      isLazyLoad: true
      _options:
        transform: $nationsList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      label: Group
      name: groupCode
      labelType: type-grid
      placeholder: Select Group
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $groupsList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      label: Company
      name: companyId
      labelType: type-grid
      placeholder: Select Company
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $companiesList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      label: Legal Entity
      name: legalEntityId
      labelType: type-grid
      placeholder: Select Legal Entity
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $legalEntitiesList($.extend.limit, $.extend.page, $.extend.search)
    - name: localForeigners
      label: Local/Foreign Employees
      type: select
      labelType: type-grid
      placeholder: Select Local/Foreign Employees
      mode: multiple
      _select:
        transform: $.variables._localForeignersList
    - name: employeeGroupCode
      label: Employee group
      type: selectAll
      labelType: type-grid
      placeholder: Select Employee Group
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $employeeGroupsList($.extend.limit, $.extend.page, $.extend.search)
    - name: contractTypeCode
      label: Contract Type
      type: selectAll
      labelType: type-grid
      placeholder: Select Contract Type
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $contractTypesList($.extend.limit, $.extend.page, $.extend.search)
    - name: careerStreamCode
      label: Career Stream
      mode: multiple
      labelType: type-grid
      placeholder: Select Career Stream
      type: selectAll
      isLazyLoad: true
      _options:
        transform: $careerStreamsList($.extend.limit, $.extend.page, $.extend.search)
    - name: employeeLevelCode
      label: Employee Level
      type: selectAll
      mode: multiple
      labelType: type-grid
      placeholder: Select Employee Level
      isLazyLoad: true
      _options:
        transform: $levelsList($.extend.limit, $.extend.page, $.extend.search)
    - name: jobTitleCode
      label: Job Title
      type: selectAll
      labelType: type-grid
      placeholder: Select Job Title
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $jobCodesList($.extend.limit, $.extend.page, $.extend.search)
    - type: dateRange
      label: Effective Date
      name: effectiveDate
      labelType: type-grid
    - type: group
      label: Standard Leave Date
      labelType: type-row
      n_cols: 2
      name: standardLeaveDate
      fields:
        - name: from
          type: number
          placeholder: From
        - name: to
          type: number
          placeholder: To
    - name: createdBy
      label: Created By
      type: select
      labelType: type-grid
      placeholder: Select Created By
      _select:
        transform: $.variables._userList
    - type: dateRange
      label: Created On
      name: createdAt
      labelType: type-grid
    - name: updatedBy
      label: Last Updated By
      type: select
      placeholder: Select Last Updated By
      labelType: type-grid
      _select:
        transform: $.variables._userList
    - type: dateRange
      label: Last Updated On
      name: updatedAt
      labelType: type-grid
  filterMapping:
    - field: countryCode
      operator: $in
      valueField: countryCode.(value)
    - field: groupCode
      operator: $in
      valueField: groupCode.(value)
    - field: companyId
      operator: $in
      valueField: companyId.(value)
    - field: legalEntityId
      operator: $in
      valueField: legalEntityId.(value)
    - field: careerStreamCode
      operator: $in
      valueField: careerStreamCode.(value)
    - field: employeeLevelCode
      operator: $in
      valueField: employeeLevelCode.(value)
    - field: employeeGroupCode
      operator: $in
      valueField: employeeGroupCode.(value)
    - field: contractTypeCode
      operator: $in
      valueField: contractTypeCode.(value)
    - field: jobTitleCode
      operator: $in
      valueField: jobTitleCode.(value)
    - field: localForeigners
      operator: $in
      valueField: localForeigners.(value)
    - field: effectiveDate
      operator: $between
      valueField: effectiveDate
    - field: standardLeaveDate
      operator: $gte
      valueField: standardLeaveDate.from
    - field: standardLeaveDate
      operator: $lte
      valueField: standardLeaveDate.to
    - field: createdBy
      operator: $in
      valueField: createdBy.(value)
    - field: createdAt
      operator: $between
      valueField: createdAt
    - field: updatedBy
      operator: $in
      valueField: updatedBy.(value)
    - field: updatedAt
      operator: $between
      valueField: updatedAt
  sources:
    nationsList:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    groupsList:
      uri: '"/api/groups"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    companiesList:
      uri: '"/api/companies"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code,'id':$item.id}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    legalEntitiesList:
      uri: '"/api/legal-entities"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    careerStreamsList:
      uri: '"/api/career-streams"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    levelsList:
      uri: '"/api/picklists/EMPLEVEL/values/pagination"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    employeeGroupsList:
      uri: '"/api/employee-groups"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.employeeGroup.default & '
        (' & $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    contractTypesList:
      uri: '"/api/picklists/CONTRACTTYPE/values/pagination"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    localForeigners:
      uri: '"/api/picklists/LOCALFOREIGNERS/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params: []
    jobCodesList:
      uri: '"/api/job-codes"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    jobProfilesList:
      uri: '"/api/job-profiles"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.jobProfileName & ' (' &
        $item.jobProfileCode & ')', 'value': $item.jobProfileCode}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    userList:
      uri: '"/api/internal-users"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.userName, 'value':
        $item.userName}})
      disabledCache: true
  variables:
    _userList:
      transform: $userList()
    _localForeignersList:
      transform: $localForeigners()
layout_options:
  show_detail_history: false
  show_dialog_form_save_add_button: true
  tool_table:
    - id: export
      icon: icon-download-simple-bold
  export_all:
    type: base_total
  hide_action_row: true
  show_dialog_duplicate_button: false
  delete_multi_items: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: icon-plus-bold
    type: primary
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    title: Edit
    icon: icon-pencil-simple-bold
    type: ghost-gray
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: ghost-gray
backend_url: /api/ts-set-no-day-offs
screen_name: ts-set-no-day-offs
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: companyId
    defaultName: CompanyCode
  - name: countryCode
    defaultName: CountryCode
  - name: legalEntityId
    defaultName: LegalEntityCode
  - name: jobTitleCode
    defaultName: JobCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Set Standard Leave Days
  parent:
    title: Leave Fund Regulations
