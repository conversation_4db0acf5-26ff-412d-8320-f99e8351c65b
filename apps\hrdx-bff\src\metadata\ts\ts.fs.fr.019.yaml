id: TS.FS.FR.019
status: draft
sort: 392
user_created: 56e71b91-dd39-4ebc-afd1-b4f00f38d4a5
date_created: '2024-08-14T08:21:19.776Z'
user_updated: abd14398-1d53-4e6f-9834-26328f0ebde0
date_updated: '2025-06-19T03:44:58.206Z'
title: Standard Leave Day For Employee
requirement:
  time: 1722497717489
  blocks:
    - id: jheEPB4afU
      type: paragraph
      data:
        text: >
          - <PERSON><PERSON> thống cho phép bộ phận nhân sự tập đoàn thiết lập số ngày phép
          chuẩn theo quy định của Tập đoàn/ CTTV.
    - id: s12_1D_tF1
      type: paragraph
      data:
        text: >-
          - <PERSON><PERSON> thống kiểm tra thông tin và không cho phép tạo mới/ chỉnh sửa nếu
          thông tin thiết lập trùng thông tin về điều kiện hưởng:&nbsp; "<PERSON><PERSON> nhân
          viên", "Tên nhân viên", "<PERSON><PERSON> hồ sơ công việc" và “Ngày hiệu lực” với
          các thiết lập trước đó. 
    - id: O52-H3ihaR
      type: paragraph
      data:
        text: >-

          - Hệ thống sẽ căn cứ vào thông tin nhân viên và thiết lập số phép
          chuẩn để tính toán ra số ngày phép năm của từng CBNV.&nbsp;&nbsp;
  version: 2.29.1
screen_design: null
module: TS
local_fields:
  - code: employeeCode
    pinned: true
    title: Employee ID
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    show_sort: true
  - code: employeeRecordNumber
    title: Employee Record Number (ERN)
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 16
    options__tabular__align: right
  - code: employeeName
    title: Employee Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: employeeGroupName
    title: 'Employee Group '
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: contractTypeName
    title: Contract Type
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: jobTitleName
    title: Job
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: typeOfNationality
    title: Local/Foreign Employees
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 12
  - code: effectiveDate
    title: Effective Start Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    options__tabular__column_width: 12
  - code: effectiveDateTo
    title: Effective End Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    options__tabular__column_width: 12
  - code: standardNodayOff
    title: Number of days of leave
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 14
  - code: note
    title: Note
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: updatedBy
    title: Last Updated By
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: updatedAt
    title: Last Updated On
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
    options__tabular__column_width: 12
mock_data:
  - employeeCode: '00123456'
    employeeRecord: '1'
    employeeName: Bùi Phương
    employeeGroup1: Cán bộ kiểm thử phần mềm
    contractType: Nhân viên chính thức
    job: Hợp đồng chính thức
    localForeigners: Người bản địa
    effectiveStartDate: 01/01/2024
    effectiveEndDate: 12/31/2024
    numberOfDaysOfLeave: '12'
    note: Test 123
    creator: Phuong Bui
    createTime: 01/04/2024 10:55:00
    lastEditer: Khanh Vy
    lastEditTime: 01/04/2024 10:57:00
  - employeeCode: '00123455'
    employeeRecord: '2'
    employeeName: Nguyễn Văn A
    employeeGroup1: Cán bộ triển khai phần mềm
    contractType: Nhân viên chính thức
    job: Hợp đồng chính thức
    localForeigners: Người bản địa
    effectiveStartDate: 01/01/2024
    effectiveEndDate: 12/31/2024
    numberOfDaysOfLeave: '12'
    note: Test 123
    creator: Phuong Bui
    createTime: 01/04/2024 10:55:00
    lastEditer: Khanh Vy
    lastEditTime: 01/04/2024 10:57:00
  - employeeCode: '00123454'
    employeeRecord: '1'
    employeeName: Nguyễn Văn B
    employeeGroup1: Cán bộ triển khai phần mềm
    contractType: Nhân viên thử việc
    job: Hợp đồng thử việc
    localForeigners: Người bản địa
    effectiveStartDate: 01/01/2024
    effectiveEndDate: 12/31/2024
    numberOfDaysOfLeave: '12'
    note: Test 123
    creator: Phuong Bui
    createTime: 01/04/2024 10:55:00
    lastEditer: Khanh Vy
    lastEditTime: 01/04/2024 10:57:00
  - employeeCode: '00123453'
    employeeRecord: '1'
    employeeName: Nguyễn Văn C
    employeeGroup1: Cán bộ triển khai phần mềm
    contractType: Nhân viên thử việc
    job: Hợp đồng thử việc
    localForeigners: Người nước ngoài
    effectiveStartDate: 01/01/2024
    effectiveEndDate: 12/31/2024
    numberOfDaysOfLeave: '15'
    note: Test 123
    creator: Phuong Bui
    createTime: 01/04/2024 10:55:00
    lastEditer: Khanh Vy
    lastEditTime: 01/04/2024 10:57:00
  - employeeCode: '00123452'
    employeeRecord: '1'
    employeeName: Nguyễn Văn D
    employeeGroup1: Cán bộ triển khai phần mềm
    contractType: Nhân viên thử việc
    job: Hợp đồng thử việc
    localForeigners: Người nước ngoài
    effectiveStartDate: 01/01/2024
    effectiveEndDate: 12/31/2024
    numberOfDaysOfLeave: '15'
    note: Test 123
    creator: Phuong Bui
    createTime: 01/04/2024 10:55:00
    lastEditer: Khanh Vy
    lastEditTime: 01/04/2024 10:57:00
local_buttons: null
layout: layout-table
form_config:
  formSize:
    create: largex
    edit: largex
    view: small
  fields:
    - type: group
      fields:
        - type: select
          name: employee
          label: Employee
          placeholder: Select Employee
          isLazyLoad: true
          validators:
            - type: required
          _select:
            transform: $employeesList($.extend.limit, $.extend.page, $.extend.search)
          _condition:
            transform: $.extend.formType = 'create'
          outputValue: value
        - type: text
          label: Employee
          disabled: true
          _value:
            transform: >-
              $.fields.employeeCode &' - '& $.fields.employeeRecordNumber &' -
              '& $.fields.employeeName
          _condition:
            transform: $.extend.formType = 'edit'
        - type: text
          name: employeeCode
          unvisible: true
          _value:
            transform: $.fields.employee.employeeId
        - type: number
          name: employeeRecordNumber
          unvisible: true
          _value:
            transform: $string($.fields.employee.employeeRecordNumber)
        - type: text
          name: employeeName
          unvisible: true
          _value:
            transform: $.fields.employeeName
        - type: text
          name: employeeLocalForeigner
          unvisible: true
          _value:
            transform: $.variables._selectedLocalForeigner.localForeigner
        - type: text
          name: employeeIdObj
          unvisible: true
          _value:
            transform: $.variables._employeeIdObj
    - type: group
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - type: text
          label: Employee
          name: employee
          _value:
            transform: >-
              $.extend.defaultValue.employeeCode & ' - ' &
              $.extend.defaultValue.employeeRecordNumber & ' - ' &
              $.extend.defaultValue.employeeName
        - name: typeOfNationality
          label: Local/Foreign Employees
          type: text
        - name: employeeGroupName
          label: Employee Group
          type: text
        - name: contractTypeName
          label: Contract Type
          type: text
        - name: jobTitleName
          label: Job
          type: text
        - name: effectiveDate
          label: Effective Start Date
          type: dateRange
          mode: date-picker
        - name: effectiveDateTo
          label: Effective End Date
          type: dateRange
          mode: date-picker
        - name: standardNodayOff
          label: Number of days of leave
          type: text
    - type: group
      n_cols: 2
      fields:
        - name: effectiveDate
          label: Effective Start Date
          type: dateRange
          placeholder: DD/MM/YYYY
          setting:
            format: dd/MM/yyyy
            type: date
          mode: date-picker
          validators:
            - type: required
          _condition:
            transform: $.extend.formType != 'view'
        - name: effectiveDateTo
          label: Effective End Date
          type: dateRange
          placeholder: DD/MM/YYYY
          setting:
            format: dd/MM/yyyy
            type: date
          mode: date-picker
          validators:
            - type: ppx-custom
              args:
                transform: >-
                  $exists($.fields.effectiveDateTo) and
                  $DateDiff($.fields.effectiveDateTo, $.fields.effectiveDate,
                  'd') < 1
              text: End date must be greater than start date
          _condition:
            transform: $.extend.formType != 'view'
    - name: standardNodayOff
      label: Number of days of leave
      type: number
      scale: 0.5
      placeholder: Enter number of days of leave
      validators:
        - type: required
        - type: maxLength
          args: '50'
      _condition:
        transform: $.extend.formType != 'view'
    - name: note
      label: Note
      type: textarea
      placeholder: Enter note
      validators:
        - type: maxLength
          args: '1000'
          text: Note should not exceed 1000 characters.
      textarea:
        autoSize:
          minRows: 3
        maxCharCount: 1000
  overview:
    dependentField: employeeIdObj
    title: Employee Detail
    border: true
    uri: >-
      /api/personals/:{employeeId}:/job-datas?filter%5B0%5D=employeeRecordNumber%7C%7C%24eq%7C%7C:{employeeRecordNumber}:
    display:
      - label: Local/Foreign Employees
        _value:
          transform: $.fields.employeeLocalForeigner
      - key: employeeGroupName
        label: Employee Group
      - label: Contract Type
        _value:
          transform: $.variables._selectedContract.contractTypeName
      - key: jobName
        label: Job
  footer:
    create: true
    update: true
    createdOn: createdAt
    updatedOn: updatedAt
    createdBy: createdBy
    updatedBy: updatedBy
  sources:
    employeesList:
      uri: '"/api/personals"'
      method: GET
      queryTransform: '{''limit'': $.limit, ''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.employeeId &' - '&
        $item.employeeRecordNumber &' - '& $item.name, 'value': {'employeeId':
        $item.employeeId, 'employeeRecordNumber': $item.employeeRecordNumber},
        'employeeId': $item.employeeId, 'employeeRecordNumber':
        $item.employeeRecordNumber}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    detailLocalForeigner:
      uri: '"/api/employee-related-info/local-foreigner"'
      method: GET
      queryTransform: >-
        {'filter':[{'field': 'employeeId', 'operator': '$eq', 'value':
        $.employeeId}, {'field': 'employeeRecordNumber', 'operator': '$eq',
        'value': $.ern}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $
      disabledCache: true
      params:
        - employeeId
        - ern
    detailContract:
      uri: '"/api/personals/" & $.employeeId & "/contracts"'
      method: GET
      queryTransform: >-
        {'filter':[{'field': 'employeeRecordNumber', 'operator': '$eq', 'value':
        $.ern}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $[$count($.data)-1]
      disabledCache: true
      params:
        - employeeId
        - ern
  variables:
    _employeeIdObj:
      transform: >-
        $.fields.employeeCode != '' ? {'employeeId': $.fields.employeeCode,
        'employeeRecordNumber': $.fields.employeeRecordNumber}
    _selectedLocalForeigner:
      transform: >-
        ($exists($.fields.employeeCode)?
        $detailLocalForeigner($.fields.employeeCode,
        $.fields.employeeRecordNumber):{})
    _selectedContract:
      transform: >-
        ($exists($.fields.employeeCode)? $detailContract($.fields.employeeCode,
        $.fields.employeeRecordNumber):{})
filter_config:
  fields:
    - type: selectAll
      name: employee
      label: Employee
      placeholder: Select Employee
      labelType: type-grid
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $employeesList($.extend.limit, $.extend.page, $.extend.search)
    - name: typeOfNationality
      label: Local/Foreign Employees
      placeholder: Select Local/Foreign Employees
      type: select
      labelType: type-grid
      mode: multiple
      select:
        - value: VN
          label: Người bản địa
        - value: NN
          label: Người nước ngoài
    - name: employeeGroup
      label: Employee Group
      placeholder: Select employee group
      mode: multiple
      type: selectAll
      labelType: type-grid
      isLazyLoad: true
      _options:
        transform: $employeegroupsList($.extend.limit, $.extend.page, $.extend.search)
    - name: contractType
      label: Contract Type
      type: selectAll
      labelType: type-grid
      mode: multiple
      placeholder: Select contract type
      isLazyLoad: true
      _options:
        transform: $contracttypesList($.extend.limit, $.extend.page, $.extend.search)
    - name: jobTitleId
      label: Job
      type: selectAll
      labelType: type-grid
      mode: multiple
      placeholder: Select job title
      isLazyLoad: true
      _options:
        transform: $jobcodesList($.extend.limit, $.extend.page, $.extend.search)
    - name: effectiveDate
      type: dateRange
      labelType: type-grid
      label: Effective Start Date
      setting:
        format: dd/MM/yyyy
        type: date
    - name: effectiveDateTo
      type: dateRange
      labelType: type-grid
      label: Effective End Date
      setting:
        format: dd/MM/yyyy
        type: date
    - name: standardNodayOff
      label: Number of days of leave
      type: number
      labelType: type-grid
      placeholder: Enter number of days of leave
      validators:
        - type: maxLength
          args: '50'
    - name: createdBy
      label: Created By
      type: select
      mode: multitple
      labelType: type-grid
      placeholder: Select Users
      _select:
        transform: $userList()
    - name: createdAt
      type: dateRange
      label: Created On
      labelType: type-grid
      setting:
        format: dd/MM/yyyy
        type: date
    - name: updatedBy
      label: Last Updated By
      type: select
      mode: multitple
      labelType: type-grid
      placeholder: Select Users
      _select:
        transform: $userList()
    - name: updatedAt
      type: dateRange
      labelType: type-grid
      label: Last Updated On
      setting:
        format: dd/MM/yyyy
        type: date
  filterMapping:
    - field: $
      operator: $in
      valueField: employee.(value)
    - field: typeOfNationality
      operator: $eq
      valueField: typeOfNationality.(value)
    - field: employeeGroupId
      operator: $in
      valueField: employeeGroup.(value)
    - field: contractTypeId
      operator: $in
      valueField: contractType.(value)
    - field: jobTitleId
      operator: $in
      valueField: jobTitleId.(value)
    - field: effectiveDate
      operator: $between
      valueField: effectiveDate
    - field: effectiveDateTo
      operator: $between
      valueField: effectiveDateTo
    - field: standardNodayOff
      operator: $eq
      valueField: standardNodayOff
    - field: createdBy
      operator: $in
      valueField: createdBy.(value)
    - field: updatedBy
      operator: $in
      valueField: updatedBy.(value)
    - field: createdAt
      operator: $between
      valueField: createdAt
    - field: updatedAt
      operator: $between
      valueField: updatedAt
  sources:
    contracttypesList:
      uri: '"/api/picklists/CONTRACTTYPE/values/pagination"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default& ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    jobcodesList:
      uri: '"/api/job-codes"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default, 'value':
        $item.code }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    employeegroupsList:
      uri: '"/api/employee-groups"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.employeeGroup.default,
        'value': $item.code }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    employeesList:
      uri: '"/api/personals"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.employeeId &' - '&
        $item.employeeRecordNumber &' - '& $item.name,'code': $item.employeeId,
        'value': {'employeeCode': $item.employeeId,'employeeRecordNumber':
        $string($item.employeeRecordNumber)}}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    userList:
      uri: '"/api/admin-users"'
      method: GET
      queryTransform: '{''filter'': [{''field'':''status'',''operator'': ''$eq'',''value'':true}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.account, 'value':
        $item.account}})[]
      disabledCache: true
layout_options:
  show_dialog_form_save_add_button: true
  show_detail_history: false
  toolTable:
    export: true
    import: true
    adjustDisplay: true
  tool_table:
    - id: import
      icon: icon-upload-simple-bold
      paramsRedirect:
        type: TS_OBJECT
        entityOrObj: TSSetNODayOffForEmployee
    - id: export
      icon: icon-download-simple-bold
  export_all:
    type: base_total
  hide_action_row: true
  show_dialog_duplicate_button: false
  delete_multi_items: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: icon-plus-bold
    type: primary
options: null
create_form: null
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    title: Edit
    icon: icon-pencil-simple-bold
    type: ghost-gray
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: ghost-gray
backend_url: api/ts-set-no-day-off-for-employees
screen_name: ts-set-no-day-off-for-employees
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: employeeCode
    defaultName: EmployeeId
  - name: employeeRecordNumber
    defaultName: EmployeeRecordNumber
  - name: jobTitleId
    defaultName: JobCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Standard Leave Day For Employee
  parent:
    title: Leave Fund Regulations
