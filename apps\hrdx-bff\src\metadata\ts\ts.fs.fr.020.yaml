id: TS.FS.FR.020
status: draft
sort: 220
user_created: 59722cb8-f858-481b-97f1-c97dd2a8e4be
date_created: '2024-07-05T03:29:14.723Z'
user_updated: abd14398-1d53-4e6f-9834-26328f0ebde0
date_updated: '2025-06-23T02:20:40.039Z'
title: Set Annual Leave Policy
requirement:
  time: 1741861029048
  blocks:
    - id: EkMRODDvnj
      type: code
      data:
        code: "- Hệ thống cho phép bộ phận nhân sự tập đoàn thiết lập các nguyên tắc tính phép năm theo quy định của Tập đoàn/ CTTV.\n- Hệ thống kiểm tra thông tin và không cho phép tạo mới/ chỉnh sửa nếu thông tin thiết lập trùng thông tin về “Thông tin đơn vị/phòng ban” và “Ng<PERSON>y hiệu lự<PERSON>” với các thiết lập trước đó. \"\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t"
  version: 2.30.7
screen_design: null
module: TS
local_fields:
  - code: nationName
    title: Country
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: groupName
    title: Group
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: companyName
    title: Company
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: legalEntityName
    title: Legal Entity
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: effectiveDate
    title: Effective Start Date
    data_type:
      key: YYYY/MM/DD
      collection: data_types
    display_type:
      key: DD/MM/YYYY
      collection: field_types
    show_sort: true
  - code: effectiveDateTo
    title: Effective End Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
  - code: periodName
    title: Period
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: vacationCycleFrom
    title: Period start date
    data_type:
      key: String
      collection: data_types
    display_type:
      key: DD/MM
      collection: field_types
  - code: unitTypeName
    title: Unit Type
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: startDateForCalLeaveName
    title: Leave Accrual Start Date
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: startDateOfUseName
    title: Leave Usage Start Date
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: maxNoDayOffMove
    title: Maximum Transferable Leave Days
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: numberOfExpiredDate
    title: Expiration Date for Transferred Leave
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: setupLeaveAdvanceName
    title: Advanced Leave Setup
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: advancedLeavePeriodName
    title: Advanced Leave Period
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: note
    title: Note
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
  - code: updatedAt
    title: Last Updated On
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
  - code: updatedBy
    title: ' Last Updated By'
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
mock_data:
  - country: Việt nam
    group: FPT
    company: FPT TEL
    legalEntity: FIS HCM
    effectiveDate: 01/01/2024
    period: Năm
    periodStartDate: 01/01
    personalStartDate: Ngày thử việc
    personalUsedDate: Ngày vào chính thức
    maximum: 5
    transferred: 3 tháng
    setUpTemporaryLeave: Theo ngày vào chính thức
    periodAdvance: 12 Tháng
    note: Áp dụng chung
    creator: Phương Bùi
    createTime: 01/04/2024 10:55:00
    lastEditor: Khánh Vy
    lastEditTime: 01/04/2024 10:57:00
  - country: Việt nam
    group: FPT
    company: FPT TEL
    legalEntity: FIS HCM
    effectiveDate: 01/01/2024
    period: Năm
    periodStartDate: 01/01
    personalStartDate: Ngày thử việc
    personalUsedDate: Ngày vào chính thức
    maximum: 5
    transferred: 3 tháng
    setUpTemporaryLeave: Theo ngày vào chính thức
    periodAdvance: 12 Tháng
    note: Áp dụng chung
    creator: Phương Bùi
    createTime: 01/04/2024 10:55:00
    lastEditor: Khánh Vy
    lastEditTime: 01/04/2024 10:57:00
  - country: Việt nam
    group: FPT
    company: FPT TEL
    legalEntity: FIS HCM
    effectiveDate: 01/01/2024
    period: Năm
    periodStartDate: 01/01
    personalStartDate: Ngày thử việc
    personalUsedDate: Ngày vào chính thức
    maximum: 5
    transferred: 3 tháng
    setUpTemporaryLeave: Theo ngày vào chính thức
    periodAdvance: 12 Tháng
    note: Áp dụng chung
    creator: Phương Bùi
    createTime: 01/04/2024 10:55:00
    lastEditor: Khánh Vy
    lastEditTime: 01/04/2024 10:57:00
  - country: Việt nam
    group: FPT
    company: FPT TEL
    legalEntity: FIS HCM
    effectiveDate: 01/01/2024
    period: Năm
    periodStartDate: 01/01
    personalStartDate: Ngày thử việc
    personalUsedDate: Ngày vào chính thức
    maximum: 5
    transferred: 3 tháng
    setUpTemporaryLeave: Theo ngày vào chính thức
    periodAdvance: 12 Tháng
    note: Áp dụng chung
    creator: Phương Bùi
    createTime: 01/04/2024 10:55:00
    lastEditor: Khánh Vy
    lastEditTime: 01/04/2024 10:57:00
  - country: Việt nam
    group: FPT
    company: FPT TEL
    legalEntity: FIS HCM
    effectiveDate: 01/01/2024
    period: Năm
    periodStartDate: 01/01
    personalStartDate: Ngày thử việc
    personalUsedDate: Ngày vào chính thức
    maximum: 5
    transferred: 3 tháng
    setUpTemporaryLeave: Theo ngày vào chính thức
    periodAdvance: 12 Tháng
    note: Áp dụng chung
    creator: Phương Bùi
    createTime: 01/04/2024 10:55:00
    lastEditor: Khánh Vy
    lastEditTime: 01/04/2024 10:57:00
  - country: Việt nam
    group: FPT
    company: FPT TEL
    legalEntity: FIS HCM
    effectiveDate: 01/01/2024
    period: Năm
    periodStartDate: 01/01
    personalStartDate: Ngày thử việc
    personalUsedDate: Ngày vào chính thức
    maximum: 5
    transferred: 3 tháng
    setUpTemporaryLeave: Theo ngày vào chính thức
    periodAdvance: 12 Tháng
    note: Áp dụng chung
    creator: Phương Bùi
    createTime: 01/04/2024 10:55:00
    lastEditor: Khánh Vy
    lastEditTime: 01/04/2024 10:57:00
  - country: Việt nam
    group: FPT
    company: FPT TEL
    legalEntity: FIS HCM
    effectiveDate: 01/01/2024
    period: Năm
    periodStartDate: 01/01
    personalStartDate: Ngày thử việc
    personalUsedDate: Ngày vào chính thức
    maximum: 5
    transferred: 3 tháng
    setUpTemporaryLeave: Theo ngày vào chính thức
    periodAdvance: 12 Tháng
    note: Áp dụng chung
    creator: Phương Bùi
    createTime: 01/04/2024 10:55:00
    lastEditor: Khánh Vy
    lastEditTime: 01/04/2024 10:57:00
local_buttons: null
layout: layout-table
form_config:
  formSize:
    create: large
    edit: large
    proceed: large
  fields:
    - type: group
      label: Basic Information
      collapse: false
      _condition:
        transform: $.extend.formType != 'view'
      n_cols: 2
      fields:
        - name: nationId
          label: Country
          type: select
          outputValue: value
          placeholder: Select Country
          _select:
            transform: $nationsList()
          col: 1
        - name: groupId
          label: Group
          type: select
          outputValue: value
          clearFieldsAfterChange:
            - companyId
            - legalEntityId
          placeholder: Select Group
          _select:
            transform: $.variables._groupsList
          col: 1
        - name: companyId
          label: Company
          type: select
          clearFieldsAfterChange:
            - legalEntityId
          outputValue: value
          placeholder: Select Company
          _select:
            transform: $.variables._companiesList
          col: 1
        - name: legalEntityId
          label: Legal Entity
          type: select
          outputValue: value
          placeholder: Select Legal Entity
          _select:
            transform: $.variables._legalEntityList
          col: 1
        - type: dateRange
          name: effectiveDate
          mode: date-picker
          label: Effective Start Date
          setting:
            format: dd/MM/yyyy
            type: date
          col: 1
          validators:
            - type: required
          _value:
            transform: $.extend.formType = 'create' ? $now()
        - type: dateRange
          name: effectiveDateTo
          label: Effective End Date
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
            autoFill: end-off
            _disabledDate:
              transform: '{''operator'': ''$lt'', ''value'': $.fields.effectiveDate}'
          validators:
            - type: ppx-custom
              args:
                transform: >-
                  $exists($.fields.effectiveDate) and
                  $exists($.fields.effectiveDateTo) and
                  $DateDiff($DateFormat($.fields.effectiveDate, 'yyyy-MM-DD'),
                  $DateFormat($.fields.effectiveDateTo, 'yyyy-MM-DD'), 'd') > 0
              text: Effective end date must be greater than effective start date.
        - type: radio
          name: unitType
          label: Unit Type
          value: D
          radio:
            - label: Days
              value: D
            - label: Hours
              value: H
          col: 1
          validators:
            - type: required
        - name: period
          label: Period
          type: select
          outputValue: value
          placeholder: Select Period
          col: 1
          validators:
            - type: required
          select:
            - label: Months
              value: M
            - label: Quarter
              value: Q
            - label: 06 months
              value: H
            - label: Year
              value: 'Y'
        - type: dateRange
          label: Period start date
          name: vacationCycleFrom
          mode: date-picker
          setting:
            format: dd/MM
            type: date
          col: 1
          validators:
            - type: required
        - name: startDateForCalLeave
          label: Leave Accrual Start Date
          type: select
          outputValue: value
          placeholder: Select Leave Accrual Start Date
          col: 1
          validators:
            - type: required
          _select:
            transform: $seniorityDateList()
        - name: startDateOfUse
          label: Leave Usage Start Date
          type: select
          outputValue: value
          placeholder: Select Leave Usage Start Date
          col: 1
          validators:
            - type: required
          _select:
            transform: $seniorityDateList()
        - name: note
          label: Note
          type: textarea
          col: 2
          placeholder: Enter note
          validators:
            - type: maxLength
              args: '1000'
              text: Note should not exceed 1000 characters.
          textarea:
            autoSize:
              minRows: 3
            maxCharCount: 1000
    - type: group
      label: Basic Information
      collapse: false
      fieldGroupTitleStyle:
        borderTop: none
        paddingTop: 0px
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - name: nationName
          label: Country
          type: text
          _value:
            transform: >-
              $.extend.defaultValue.nationId ? $.extend.defaultValue.nationName
              & ' (' & $.extend.defaultValue.nationId & ')'
        - name: groupName
          label: Group
          type: text
          _value:
            transform: >-
              $.extend.defaultValue.groupId ? $.extend.defaultValue.groupName &
              ' (' & $.extend.defaultValue.groupId & ')'
        - name: companyName
          label: Company
          type: text
          _value:
            transform: >-
              $.extend.defaultValue.companyId ?
              $.extend.defaultValue.companyName & ' (' &
              $.extend.defaultValue.companyId & ')'
        - name: legalEntityName
          label: Legal Entity
          type: text
          _value:
            transform: >-
              $.extend.defaultValue.legalEntityId ?
              $.extend.defaultValue.legalEntityName & ' (' &
              $.extend.defaultValue.legalEntityId & ')'
        - type: dateRange
          name: effectiveDate
          mode: date-picker
          label: Effective Start Date
          setting:
            format: dd/MM/yyyy
            type: date
        - type: dateRange
          name: effectiveDateTo
          mode: date-picker
          label: Effective End Date
          setting:
            format: dd/MM/yyyy
            type: date
        - type: radio
          name: unitType
          label: Unit Type
          value: D
          radio:
            - label: Days
              value: D
            - label: Hours
              value: H
        - name: period
          label: Period
          type: select
          outputValue: value
          placeholder: Select Period
          select:
            - label: Months
              value: M
            - label: Quarter
              value: Q
            - label: 06 months
              value: H
            - label: Year
              value: 'Y'
        - type: dateRange
          label: Period start date
          name: vacationCycleFrom
          mode: date-picker
          setting:
            format: dd/MM
            type: date
        - name: startDateForCalLeave
          label: Leave Accrual Start Date
          type: select
          _select:
            transform: $seniorityDateList()
        - name: startDateOfUse
          label: Leave Usage Start Date
          type: select
          outputValue: value
          _select:
            transform: $seniorityDateList()
        - type: textarea
          name: note
          label: Note
          textarea:
            autoSize:
              minRows: 3
    - type: group
      label: Leave Accrual
      collapse: false
      _condition:
        transform: $.extend.formType != 'view'
      n_cols: 2
      fields:
        - name: maxNoDayOffMove
          label: Maximum Transferable Leave Days
          type: number
          placeholder: Enter Maximum Transferable Leave Days
          number:
            suffix: Days
            max: 1000
        - type: number
          name: numberOfExpiredDate
          label: Expiration Date for Transferred Leave
          placeholder: Enter Expiration Date for Transferred Leave
          number:
            max: 1000
          addOnAfter:
            type: select
            name: expiredDateTranferredLeaveType
            outputValue: value
            value: M
            select:
              - label: Days
                value: D
              - label: Months
                value: M
              - label: Quarter
                value: Q
              - label: Weeks
                value: W
              - label: Years
                value: 'Y'
    - type: group
      label: Leave Accrual
      collapse: false
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - name: maxNoDayOffMove
          label: Maximum Transferable Leave Days
          type: number
          placeholder: Enter Maximum Transferable Leave Days
          number:
            suffix: Days
        - type: number
          name: numberOfExpiredDate
          label: Expiration Date for Transferred Leave
          placeholder: Enter Expiration Date for Transferred Leave
          addOnAfter:
            type: select
            name: expiredDateTranferredLeaveType
            select:
              - label: Days
                value: D
              - label: Months
                value: M
              - label: Quarter
                value: Q
              - label: Weeks
                value: W
              - label: Years
                value: 'Y'
    - type: group
      label: Advanced Leave
      collapse: false
      fields:
        - name: setupLeaveAdvance
          label: Advanced Leave Setup
          type: radio
          value: '3'
          _class:
            transform: $.extend.formType = 'view' ? 'unrequired'
          validators:
            - type: required
          radio:
            - label: Theo thâm niên
              value: '3'
            - label: Không cho ứng phép
              value: '1'
            - label: Theo ngày vào chính thức
              value: '2'
        - type: group
          n_cols: 2
          _condition:
            transform: $.extend.formType != 'view'
          fields:
            - type: number
              label: Advance Start Time
              name: numberOfLeaveAdvance
              placeholder: Enter Advance Start Time
              _condition:
                transform: $.fields.setupLeaveAdvance = '3'
              number:
                max: 1000
              addOnAfter:
                type: select
                name: startDateLeaveAdvanceType
                outputValue: value
                value: M
                select:
                  - label: Months
                    value: M
                  - label: Years
                    value: 'Y'
              validators:
                - type: required
            - name: advancedLeavePeriod
              label: Advance Leave Period
              type: select
              outputValue: value
              placeholder: Choose Advance Leave Period
              _condition:
                transform: $.fields.setupLeaveAdvance != '1'
              validators:
                - type: required
              select:
                - label: Months
                  value: M
                - label: Quarter
                  value: Q
                - label: 06 months
                  value: H
                - label: Years
                  value: 'Y'
        - type: group
          _condition:
            transform: $.extend.formType = 'view'
          fields:
            - type: number
              label: Advance Start Time
              name: numberOfLeaveAdvance
              _condition:
                transform: $.fields.setupLeaveAdvance = '3'
              addOnAfter:
                type: select
                name: startDateLeaveAdvanceType
                select:
                  - label: Months
                    value: M
                  - label: Years
                    value: 'Y'
            - name: advancedLeavePeriod
              label: Advance Leave Period
              type: select
              _condition:
                transform: $.fields.setupLeaveAdvance != '1'
              select:
                - label: Months
                  value: M
                - label: Quarter
                  value: Q
                - label: 06 months
                  value: H
                - label: Years
                  value: 'Y'
        - type: group
          _mode:
            transform: '$.extend.formType = ''create'' ? ''tabset'' : ''tabset'''
          fields:
            - type: group
              collapsed: false
              tabName: leaveAccrual
              disableEventCollapse: true
              label: Leave Accrual Policy Settings
              fields:
                - name: principleOfLeaveCalculation
                  label: Setup Leave Calculation Rules
                  type: radio
                  _class:
                    transform: $.extend.formType = 'view' ? 'unrequired'
                  validators:
                    - type: required
                  value: '1'
                  outputValue: value
                  radio:
                    - label: By Day
                      value: '1'
                    - label: According To Standard
                      value: '2'
                    - label: By Leave According To The Formula
                      value: '3'
                - type: array
                  name: tsSetPrincipleOfLeaveCalculationByDay
                  mode: table
                  borderRadius: 6
                  _size:
                    transform: $.extend.formType = 'create' ? 1
                  _condition:
                    transform: $.fields.principleOfLeaveCalculation = '1'
                  arrayOptions:
                    canChangeSize: true
                  field:
                    type: group
                    _condition:
                      transform: $.fields.principleOfLeaveCalculation = '1'
                    fields:
                      - type: select
                        name: eligibleEmployee
                        label: Eligible Employees
                        placeholder: Select EligibleEmployee
                        _class:
                          transform: $.extend.formType = 'view' ? 'unrequired'
                        select:
                          - label: Mới vào làm việc trong tháng
                            value: '1'
                          - label: Nghỉ việc trong tháng
                            value: '2'
                        validators:
                          - type: required
                        outputValue: value
                        width: 280px
                      - type: number
                        name: startDate
                        label: Start Date
                        placeholder: 0 - 31
                        _class:
                          transform: $.extend.formType = 'view' ? 'unrequired'
                        number:
                          min: 0
                          max: 31
                        validators:
                          - type: required
                          - type: ppx-custom
                            args:
                              transform: >-
                                ($idx := $.extend.path[-2]; $value := $.value;
                                $count($map($.fields.tsSetPrincipleOfLeaveCalculationByDay,
                                function($v, $i) { ($i != $idx and
                                $.fields.tsSetPrincipleOfLeaveCalculationByDay[$idx].eligibleEmployee
                                = $v.eligibleEmployee and $value >= $v.startDate
                                and  $value <= $v.endDate) ? $v })) > 0)
                            text: Date has overlapped with the previous record
                        width: 120px
                      - type: number
                        name: endDate
                        label: End Date
                        placeholder: 0 - 32
                        _class:
                          transform: $.extend.formType = 'view' ? 'unrequired'
                        number:
                          min: 0
                          max: 32
                        validators:
                          - type: required
                          - type: ppx-custom
                            args:
                              transform: >-
                                ($idx := $.extend.path[-2]; $value := $.value;
                                $count($map($.fields.tsSetPrincipleOfLeaveCalculationByDay,
                                function($v, $i) { ($i = $idx and $value <=
                                $v.startDate) ? $v })) > 0)
                            text: EndDate must greater than StartDate
                            id: validateEndDate
                          - type: ppx-custom
                            args:
                              transform: >-
                                ($idx := $.extend.path[-2]; $value := $.value;
                                $count($map($.fields.tsSetPrincipleOfLeaveCalculationByDay,
                                function($v, $i) { ($i != $idx and
                                $.fields.tsSetPrincipleOfLeaveCalculationByDay[$idx].eligibleEmployee
                                = $v.eligibleEmployee and $value >= $v.startDate
                                and  $value <= $v.endDate) ? $v })) > 0)
                            text: Date has overlapped with the previous record
                            id: duplicate
                        width: 120px
                      - type: number
                        name: leaveAllowance
                        label: Leave Allowance
                        placeholder: '00'
                        _class:
                          transform: $.extend.formType = 'view' ? 'unrequired'
                        number:
                          suffix: Days
                          max: 1000
                        validators:
                          - type: required
                        width: 140px
                - type: array
                  name: tsSetPrincipleOfLeaveCalculationByStandardWork
                  mode: table
                  _size:
                    transform: $.extend.formType = 'create' ? 1
                  _condition:
                    transform: $.fields.principleOfLeaveCalculation = '2'
                  arrayOptions:
                    canChangeSize: true
                  field:
                    type: group
                    _condition:
                      transform: $.fields.principleOfLeaveCalculation = '2'
                    fields:
                      - type: select
                        name: leaveAccrualUnit
                        label: Leave Accrual Unit
                        placeholder: Select LeaveAccrualUnit
                        outputValue: value
                        _class:
                          transform: $.extend.formType = 'view' ? 'unrequired'
                        select:
                          - label: By % of standard working days
                            value: '1'
                          - label: By number of working days
                            value: '2'
                        validators:
                          - type: required
                        width: 280px
                      - type: multitype
                        label: Value
                        name: value
                        key: '1'
                        _class:
                          transform: $.extend.formType = 'view' ? 'unrequired'
                        validators:
                          - type: required
                        _key:
                          transform: >-
                            $getFieldGroup($.extend.path, $.fields,
                            1).leaveAccrualUnit
                        fields:
                          - type: number
                            placeholder: Enter number 0 - 100
                            key: '1'
                            number:
                              suffix: '%'
                              min: 0
                              max: 100
                          - type: number
                            placeholder: Enter number 0 - 1000
                            key: '2'
                            number:
                              suffix: Days
                              min: 0
                              max: 1000
                        width: 320px
                - type: array
                  name: tsSetPrincipleOfLeaveCalculationByFunction
                  mode: table
                  _size:
                    transform: $.extend.formType = 'create' ? 1
                  _condition:
                    transform: $.fields.principleOfLeaveCalculation = '3'
                  arrayOptions:
                    canChangeSize: true
                  field:
                    type: group
                    _condition:
                      transform: $.fields.principleOfLeaveCalculation = '3'
                    fields:
                      - type: select
                        name: functionId
                        label: Leave Accrual Formula
                        placeholder: Select Leave Accrual Formula
                        outputValue: value
                        _select:
                          transform: $.variables._cafunctiontksList
                        _class:
                          transform: $.extend.formType = 'view' ? 'unrequired'
                        validators:
                          - type: required
                      - type: text
                        name: functionName
                        _value:
                          transform: >-
                            ($selectedFunc :=
                            $filter($.variables._cafunctiontksList, function($v,
                            $i, $a) { $v.value =
                            $getIdx($.fields.tsSetPrincipleOfLeaveCalculationByFunction,
                            $.extend.path[-2]).functionId }) ;
                            $selectedFunc.label )
                        unvisible: true
            - type: group
              collapsed: false
              tabName: leaveDeduction
              label: Leave Deduction Settings
              fields:
                - name: principleOfSubstractDayOff
                  label: Leave Calculation Rules
                  type: radio
                  radio:
                    - label: Theo nhóm loại ngày nghỉ
                      value: '1'
                    - label: Theo công thức
                      value: '2'
                - type: array
                  name: tsSetPrincipleOfSubstractDayOffByAbsenceGroup
                  mode: table
                  _condition:
                    transform: $.fields.principleOfSubstractDayOff = '1'
                  _size:
                    transform: $.extend.formType = 'create' ? 1
                  arrayOptions:
                    canChangeSize: true
                  field:
                    type: group
                    _condition:
                      transform: $.fields.principleOfSubstractDayOff = '1'
                    fields:
                      - type: select
                        label: Absence Group
                        name: catypeOfDayOffGroupId
                        outputValue: value
                        placeholder: Select Absence Group
                        _class:
                          transform: $.extend.formType = 'view' ? 'unrequired'
                        validators:
                          - type: ppx-custom
                            args:
                              transform: >-
                                $count($queryInArray($.fields.tsSetPrincipleOfSubstractDayOffByAbsenceGroup,
                                {'catypeOfDayOffGroupId': $.value}))>1
                            text: This absence type already exists
                          - type: required
                        _select:
                          transform: $catypeOfDayOffGroupList()
                        width: 220px
                      - type: number
                        label: Maximum Unpaid Leave Days
                        name: maxUnpaidLeaveDays
                        placeholder: 0 - 1000
                        number:
                          suffix: Days
                          min: 0
                          max: 1000
                        width: 180px
                      - type: number
                        label: Exclude from Leave Accrual
                        name: excludeFromLeaveCalculation
                        placeholder: 0 - 1000
                        number:
                          suffix: Days
                          min: 0
                          max: 1000
                        width: 180px
                      - type: number
                        label: Leave Deduction Unit
                        name: leaveDeductionUnit
                        placeholder: 0 - 1000
                        number:
                          suffix: Days
                          min: 0
                          max: 1000
                        width: 180px
                - type: array
                  name: tsSetPrincipleOfSubtractDayOffByFunction
                  mode: table
                  _size:
                    transform: $.extend.formType = 'create' ? 1
                  _condition:
                    transform: $.fields.principleOfSubstractDayOff = '2'
                  arrayOptions:
                    canChangeSize: true
                  field:
                    type: group
                    _condition:
                      transform: $.fields.principleOfSubstractDayOff = '2'
                    fields:
                      - type: select
                        name: functionId
                        label: Leave Accrual Formula
                        placeholder: Select Leave Accrual Formula
                        outputValue: value
                        _select:
                          transform: $.variables._cafunctiontksList
                      - type: text
                        name: functionName
                        _value:
                          transform: >-
                            ($selectedFunc :=
                            $filter($.variables._cafunctiontksList, function($v,
                            $i, $a) { $v.value =
                            $getIdx($.fields.tsSetPrincipleOfLeaveCalculationByFunction,
                            $.extend.path[-2]).functionId }) ;
                            $selectedFunc.label )
                        unvisible: true
  _mode:
    transform: $.extend.formType != 'view' ? 'mark-scroll'
  footer:
    create: true
    update: true
    createdOn: createdAt
    updatedOn: updatedAt
    createdBy: createdBy
    updatedBy: updatedBy
  sources:
    nationsList:
      uri: '"/api/picklists/COUNTRIES/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
    groupsList:
      uri: '"/api/groups/get-by"'
      queryTransform: >-
        {'page':1,'limit':10000,'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - effectiveDate
    companiesList:
      uri: '"/api/companies/get-by"'
      queryTransform: >-
        {'page':1,'limit':10000,'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true},{'field':'groupCode','operator': '$eq','value':
        $.groupId }]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - effectiveDate
        - groupId
    legalEntityList:
      uri: '"/api/legal-entities/get-list"'
      queryTransform: >-
        {'page':1,'limit':10000,'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true},{'field':'companyCode','operator': '$eq','value':
        $.companyId }]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - effectiveDate
        - companyId
    seniorityDateList:
      uri: '"/api/picklists/SENIORITYDATE/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
    catypeOfDayOffGroupList:
      uri: '"/api/ca-type-of-day-off-groups/by"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'status','operator': '$eq','value':true},
        {'field':'effectiveDate','operator': '$lte','value': $.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default, 'value':
        $item.code}})
      disabledCache: true
      params:
        - companyId
        - effectiveDate
    cafunctiontksList:
      uri: '"/api/ca-function-tks/by"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'status','operator': '$eq','value':true},
        {'field':'effectiveDate','operator': '$lte','value': $.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default, 'value':
        $item.code}})
      disabledCache: true
      params:
        - effectiveDate
  variables:
    _groupsList:
      transform: $groupsList($.fields.effectiveDate)
    _companiesList:
      transform: >-
        $.fields.groupId ? $companiesList($.fields.effectiveDate,
        $.fields.groupId)
    _legalEntityList:
      transform: >-
        $.fields.companyId ? $legalEntityList($.fields.effectiveDate,
        $.fields.companyId)
    _cafunctiontksList:
      transform: $cafunctiontksList($.fields.effectiveDate)
filter_config:
  fields:
    - name: nationId
      label: Country
      type: selectAll
      mode: multiple
      placeholder: Select Country
      labelType: type-grid
      isLazyLoad: true
      _options:
        transform: $nationsList($.extend.limit, $.extend.page, $.extend.search)
    - name: groupId
      label: Group
      type: selectAll
      mode: multiple
      placeholder: Select Group
      labelType: type-grid
      isLazyLoad: true
      _options:
        transform: $groupsList($.extend.limit, $.extend.page, $.extend.search)
    - name: companyId
      label: Company
      type: selectAll
      mode: multiple
      placeholder: Select Company
      labelType: type-grid
      isLazyLoad: true
      _options:
        transform: $companiesList($.extend.limit, $.extend.page, $.extend.search)
    - name: legalEntityId
      label: Legal Entity
      type: selectAll
      mode: multiple
      placeholder: Select Legal Entity
      labelType: type-grid
      isLazyLoad: true
      _options:
        transform: $legalEntitiesList($.extend.limit, $.extend.page, $.extend.search)
    - type: dateRange
      name: effectiveDate
      label: Effective Date
      labelType: type-grid
      setting:
        format: dd/MM/yyyy
        type: date
    - name: period
      label: Period
      type: select
      placeholder: Select Period
      labelType: type-grid
      mode: multiple
      select:
        - label: Year
          value: 'Y'
        - label: 06 months
          value: H
        - label: Quarter
          value: Q
        - label: Months
          value: M
    - type: dateRange
      label: Period start date
      name: vacationCycleDate
      labelType: type-grid
      setting:
        format: dd/MM/yyyy
        type: date
    - type: radio
      name: unitType
      label: Unit Type
      labelType: type-grid
      value: ''
      radio:
        - label: All
          value: ''
        - label: Days
          value: D
        - label: Hours
          value: H
    - name: startDateForCalLeave
      label: Leave Accrual Start Date
      type: select
      placeholder: Select Leave Accrual Start Date
      labelType: type-grid
      mode: multiple
      _select:
        transform: $seniorityDateList()
    - name: startDateOfUse
      label: Leave Usage Start Date
      type: select
      placeholder: Select Leave Usage Start Date
      labelType: type-grid
      mode: multiple
      _select:
        transform: $seniorityDateList()
    - type: group
      n_cols: 5
      fields:
        - name: maxNoDayOffMoveFrom
          label: Maximum Transferable Leave Days
          type: number
          labelType: type-grid
          placeholder: Enter Number
          number:
            prefix: From
          col: 3
        - name: maxNoDayOffMoveTo
          type: number
          label: '   '
          labelType: flex-row
          placeholder: Enter Number
          number:
            prefix: To
          col: 2
    - name: expiredDateTranferredLeaveType
      label: Expiration Date for Transferred Leave
      type: select
      labelType: type-grid
      mode: multiple
      placeholder: Select Expiration Date for Transferred Leave
      select:
        - label: Days
          value: D
        - label: Weeks
          value: W
        - label: Months
          value: M
        - label: Quarter
          value: Q
        - label: Year
          value: 'Y'
    - name: setupLeaveAdvance
      label: Advanced Leave Setup
      type: select
      labelType: type-grid
      mode: multiple
      placeholder: Select Advanced Leave Setup
      select:
        - label: Do Not Allow Advanced Leave
          value: '1'
        - label: By Hire Date
          value: '2'
        - label: By Seniority
          value: '3'
    - name: advancedLeavePeriod
      label: Advance Leave Period
      type: select
      labelType: type-grid
      mode: multiple
      placeholder: Select Advance Leave Period
      select:
        - label: Year
          value: 'Y'
        - label: 06 months
          value: H
        - label: Quarter
          value: Q
        - label: Months
          value: M
    - type: select
      name: createdBy
      label: Created By
      placeholder: Select Creator
      labelType: type-grid
      mode: multiple
      _select:
        transform: $userList()
    - name: createdAt
      type: dateRange
      label: Created On
      labelType: type-grid
      setting:
        format: dd/MM/yyyy
        type: date
    - type: select
      name: updatedBy
      label: Last Updated By
      placeholder: Select Updated By
      labelType: type-grid
      mode: multiple
      _select:
        transform: $userList()
    - name: updatedAt
      type: dateRange
      label: Last Updated On
      labelType: type-grid
      setting:
        format: dd/MM/yyyy
        type: date
  filterMapping:
    - field: nationId
      operator: $in
      valueField: nationId.(value)
    - field: groupId
      operator: $in
      valueField: groupId.(value)
    - field: companyId
      operator: $in
      valueField: companyId.(value)
    - field: legalEntityId
      operator: $in
      valueField: legalEntityId.(value)
    - field: effectiveDate
      operator: $between
      valueField: effectiveDate
    - field: period
      operator: $in
      valueField: period.(value)
    - field: vacationCycleDate
      operator: $between
      valueField: vacationCycleDate
    - field: unitType
      operator: $eq
      valueField: unitType
    - field: startDateForCalLeave
      operator: $in
      valueField: startDateForCalLeave.(value)
    - field: startDateOfUse
      operator: $in
      valueField: startDateOfUse.(value)
    - field: maxNoDayOffMove
      operator: $gte
      valueField: maxNoDayOffMoveFrom
    - field: maxNoDayOffMove
      operator: $lte
      valueField: maxNoDayOffMoveTo
    - field: expiredDateTranferredLeaveType
      operator: $in
      valueField: expiredDateTranferredLeaveType.(value)
    - field: setupLeaveAdvance
      operator: $eq
      valueField: setupLeaveAdvance.(value)
    - field: advancedLeavePeriod
      operator: $eq
      valueField: advancedLeavePeriod.(value)
    - field: createdBy
      operator: $in
      valueField: createdBy.(value)
    - field: updatedBy
      operator: $in
      valueField: updatedBy.(value)
    - field: createdAt
      operator: $between
      valueField: createdAt
    - field: updatedAt
      operator: $between
      valueField: updatedAt
  sources:
    nationsList:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      bodyTransform: ''
      headerTransform: ''
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    groupsList:
      uri: '"/api/groups/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    companiesList:
      uri: '"/api/companies/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    legalEntitiesList:
      uri: '"/api/legal-entities/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})
      disabledCache: true
      params:
        - limit
        - page
        - search
    catypeOfDayOffGroupList:
      uri: '"/api/ca-type-of-day-off-groups/by"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'status','operator': '$eq','value':true},
        {'field':'effectiveDate','operator': '$lte','value': $.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default, 'value':
        $item.code}})
      disabledCache: true
      params:
        - companyId
        - effectiveDate
    seniorityDateList:
      uri: '"/api/picklists/SENIORITYDATE/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
    userList:
      uri: '"/api/internal-users"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.userName, 'value':
        $item.userName}})[]
      disabledCache: true
layout_options:
  show_dialog_form_save_add_button: true
  show_detail_history: false
  toolTable:
    export: true
    adjustDisplay: true
  tool_table:
    - id: export
      icon: icon-download-simple
  export_all:
    type: base_total
  hide_action_row: true
  show_dialog_duplicate_button: false
  delete_multi_items: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    title: Edit
    icon: icon-pencil-simple
    type: ghost-gray
  - id: delete
    title: Delete
    icon: icon-trash
    type: ghost-gray
backend_url: /api/ts-set-no-do-years
screen_name: ts-set-no-do-years
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: companyId
    defaultName: CompanyCode
  - name: legalEntityId
    defaultName: LegalEntityCode
  - name: nationId
    defaultName: CountryCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Set Annual Leave Policy
  parent:
    title: Configuration
