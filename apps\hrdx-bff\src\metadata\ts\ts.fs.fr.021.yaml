id: TS.FS.FR.021
status: draft
sort: 210
user_created: 59722cb8-f858-481b-97f1-c97dd2a8e4be
date_created: '2024-07-04T08:43:14.521Z'
user_updated: abd14398-1d53-4e6f-9834-26328f0ebde0
date_updated: '2025-06-19T03:49:31.558Z'
title: Set Up Seniority Leave Calculation Rules
requirement:
  time: 1747635745336
  blocks:
    - id: LjCv9CesAQ
      type: paragraph
      data:
        text: >-
          - <PERSON><PERSON> thống cho phép bộ phận nhân sự thiết lập nguyên tắc tính phép
          thâm niên theo quy định của Tập đoàn/ CTTV
  version: 2.30.7
screen_design: null
module: TS
local_fields:
  - code: code
    title: Setup Code
    data_type:
      key: Increment ID
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
  - code: country
    title: Country
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: shortName
    title: Short Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
  - code: longName
    title: Long Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Translate
      collection: field_types
  - code: conversionPrincipleLabel
    title: Conversion Principle
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 12
  - code: statusTag
    title: Status
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Boolean Tag
      collection: field_types
  - code: lastEditTime
    title: Last Updated On
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
    options__tabular__column_width: 12
  - code: lastEditor
    title: Last Updated By
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 12
mock_data:
  - code: '00000001'
    country: Việt Nam
    principle: Nguyên tắc phép thâm niên 3 năm
    effectiveDate: 01/01/2024
    conversionPrinciple: Theo khoảng thời gian
    status: Sử dụng
    note: Áp dụng chung
    createTime: 01/01/2024
    creator: Phương Bùi
    lastEditTime: 01/04/2024
    lastEditor: Khánh Vy
  - code: '00000002'
    country: Việt Nam
    principle: Nguyên tắc phép thâm niên 3 năm
    effectiveDate: 01/01/2024
    conversionPrinciple: Theo khoảng thời gian
    status: Sử dụng
    note: Áp dụng chung
    createTime: 01/01/2024
    creator: Phương Bùi
    lastEditTime: 01/04/2024
    lastEditor: Khánh Vy
  - code: '00000003'
    country: Việt Nam
    principle: Nguyên tắc phép thâm niên 3 năm
    effectiveDate: 01/01/2024
    conversionPrinciple: Theo khoảng thời gian
    status: Sử dụng
    note: Áp dụng chung
    createTime: 01/01/2024
    creator: Phương Bùi
    lastEditTime: 01/04/2024
    lastEditor: Khánh Vy
  - code: '00000004'
    country: Việt Nam
    principle: Nguyên tắc phép thâm niên 3 năm
    effectiveDate: 01/01/2024
    conversionPrinciple: Theo khoảng thời gian
    status: Sử dụng
    note: Áp dụng chung
    createTime: 01/01/2024
    creator: Phương Bùi
    lastEditTime: 01/04/2024
    lastEditor: Khánh Vy
  - code: '00000005'
    country: Việt Nam
    principle: Nguyên tắc phép thâm niên 3 năm
    effectiveDate: 01/01/2024
    conversionPrinciple: Theo khoảng thời gian
    status: Sử dụng
    note: Áp dụng chung
    createTime: 01/01/2024
    creator: Phương Bùi
    lastEditTime: 01/04/2024
    lastEditor: Khánh Vy
  - code: '00000006'
    country: Việt Nam
    principle: Nguyên tắc phép thâm niên 3 năm
    effectiveDate: 01/01/2024
    conversionPrinciple: Theo khoảng thời gian
    status: Sử dụng
    note: Áp dụng chung
    createTime: 01/01/2024
    creator: Phương Bùi
    lastEditTime: 01/04/2024
    lastEditor: Khánh Vy
  - code: '00000007'
    country: Việt Nam
    principle: Nguyên tắc phép thâm niên 3 năm
    effectiveDate: 01/01/2024
    conversionPrinciple: Theo khoảng thời gian
    status: Sử dụng
    note: Áp dụng chung
    createTime: 01/01/2024
    creator: Phương Bùi
    lastEditTime: 01/04/2024
    lastEditor: Khánh Vy
  - code: '00000008'
    country: Việt Nam
    principle: Nguyên tắc phép thâm niên 3 năm
    effectiveDate: 01/01/2024
    conversionPrinciple: Theo khoảng thời gian
    status: Sử dụng
    note: Áp dụng chung
    createTime: 01/01/2024
    creator: Phương Bùi
    lastEditTime: 01/04/2024
    lastEditor: Khánh Vy
local_buttons: null
layout: layout-table
form_config:
  formSize:
    create: large
    edit: large
    view: large
    proceed: large
  fields:
    - type: group
      label: Basic Infomation
      collapse: false
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - type: group
          n_cols: 2
          fields:
            - name: countryId
              label: Country
              type: select
              placeholder: Select Country
              outputValue: value
              isLazyLoad: true
              _select:
                transform: $nationsList($.extend.limit, $.extend.page, $.extend.search)
              _validateFn:
                transform: >-
                  $.extend.defaultValue.country ? {'label':
                  $.extend.defaultValue.country, 'value':
                  $.extend.defaultValue.countryId}
                params:
                  updateLabelExistOption: true
            - name: code
              label: Setup Code
              type: text
              disabled: true
              placeholder: System-generated
            - name: shortName
              label: Short Name
              type: translation
              validators:
                - type: required
                - type: maxLength
                  args: 300
            - name: longName
              label: Long Name
              type: translation
              validators:
                - type: required
                - type: maxLength
                  args: 500
            - type: dateRange
              label: Effective Date
              name: effectiveDate
              mode: date-picker
              placeholder: dd/MM/yyyy
              setting:
                format: dd/MM/yyyy
                type: date
              _value:
                transform: $.extend.formType = 'create' ? $now()
              validators:
                - type: required
            - name: status
              label: Status
              type: radio
              _value:
                transform: >-
                  ($.extend.formType = 'proceed' or $.extend.formType =
                  'create') ? true
              radio:
                - label: Active
                  value: true
                - label: Inactive
                  value: false
        - type: translationTextArea
          label: Note
          name: note
          placeholder: Enter Note
          textarea:
            maxCharCount: 1000
            autoSize:
              minRows: 3
              maxRows: 5
          validators:
            - type: maxLength
              args: '1000'
              text: Maximum length is 1000 characters.
    - type: group
      label: Principles For Converting Seniority Spells
      collapse: false
      borderRadius: 16px
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - type: group
          fields:
            - name: conversionPrinciple
              label: Conversion Principle
              type: radio
              _value:
                transform: $.extend.formType = 'create' ? 'T'
              radio:
                - label: By Time Period
                  value: T
                - label: By Block
                  value: B
                - label: By Specific Formula
                  value: F
              validators:
                - type: required
            - type: select
              label: Calculation Formula
              name: calculationFormula
              placeholder: Select Calculation Formula
              outputValue: value
              dependantField: $.fields.countryId, $.fields.effectiveDate
              _select:
                transform: >-
                  $calculationFormulaList($.fields.countryId,
                  $.fields.effectiveDate)
              validators:
                - type: required
        - name: tsSetPrincipalOfSeniorityByTimes
          type: array
          mode: table
          _size:
            transform: $.extend.formType = 'create' ? 1
          arrayOptions:
            canChangeSize: true
            canDragDrop: false
          _condition:
            transform: $.fields.conversionPrinciple = 'T'
          field:
            type: group
            _condition:
              transform: $.fields.conversionPrinciple = 'T'
            fields:
              - name: conversionUnit
                type: select
                label: Leave Accrual Unit
                outputValue: value
                placeholder: Select Leave Accrual Unit
                width: 200px
                validators:
                  - type: required
                select:
                  - value: 'N'
                    label: Năm
                  - value: T
                    label: Tháng
                _disabled:
                  transform: >-
                    ($.fields.tsSetPrincipalOfSeniorityByTimes; $index:=
                    $.extend.path[-2]; $index=0 ? false : true)
                _value:
                  transform: >-
                    ($index:=$.extend.path[-2]; $index !=0 ?
                    $.fields.tsSetPrincipalOfSeniorityByTimes[0].conversionUnit)
              - label: Seniority From
                type: number
                name: seniorityFrom
                placeholder: Enter Number
                width: 142px
                validators:
                  - type: ppx-custom
                    args:
                      transform: >-
                        ($idx := $.extend.path[-2]; $value := $.value;
                        $count($map($.fields.tsSetPrincipalOfSeniorityByTimes,
                        function($v, $i) { ($i != $idx and $value >=
                        $v.seniorityFrom and  $value <= $v.seniorityTo) ? $v }))
                        > 0)
                    text: Seniority period has overlapped with the previous record
              - label: Seniority To
                type: number
                name: seniorityTo
                placeholder: Enter Number
                width: 142px
                validators:
                  - type: ppx-custom
                    args:
                      transform: >-
                        ($idx := $.extend.path[-2]; $value := $.value;
                        $count($map($.fields.tsSetPrincipalOfSeniorityByTimes,
                        function($v, $i) { ($i != $idx and $value >=
                        $v.seniorityFrom and  $value <= $v.seniorityTo) ? $v }))
                        > 0)
                    text: Seniority period has overlapped with the previous record
                    id: validateSeniorityTo
                  - type: ppx-custom
                    args:
                      transform: >-
                        ($idx := $.extend.path[-2]; $value := $.value;
                        $count($map($.fields.tsSetPrincipalOfSeniorityByTimes,
                        function($v, $i) { ($i = $idx and $value <=
                        $v.seniorityFrom) ? $v })) > 0)
                    text: Seniority period has overlapped with the previous record
                    id: duplicateSeniority
              - name: seniorityPermission
                type: number
                label: Seniority Leave
                placeholder: Enter Number
                width: 200px
                number:
                  suffix: Days
        - name: tsSetPrincipalOfSeniorityByBlocks
          type: array
          mode: table
          arrayOptions:
            canChangeSize: true
            canDragDrop: false
          _size:
            transform: $.extend.formType = 'create' ? 1
          _condition:
            transform: $.fields.conversionPrinciple = 'B'
          field:
            type: group
            _condition:
              transform: $.fields.conversionPrinciple = 'B'
            fields:
              - name: conversionUnit
                type: select
                label: Leave Accrual Unit
                outputValue: value
                placeholder: Select Leave Accrual Unit
                width: 200px
                select:
                  - value: 'N'
                    label: Năm
                  - value: T
                    label: Tháng
                _disabled:
                  transform: >-
                    ($.fields.tsSetPrincipalOfSeniorityByBlocks; $index:=
                    $test($.extend.path[-2]); $index=0 ? false : true)
                _value:
                  transform: >-
                    ($index:=$.extend.path[-2]; $index !=0 ?
                    $.fields.tsSetPrincipalOfSeniorityByBlocks[0].conversionUnit)
                validators:
                  - type: required
              - label: Seniority Block
                type: number
                name: seniorityBlock
                placeholder: Enter Seniority Block
                width: 142px
              - name: seniorityPermission
                type: number
                placeholder: Enter Seniority Permission
                label: Seniority Leave
                width: 200px
                number:
                  suffix: Leaves
    - type: group
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - type: text
          name: country
          label: Country
        - type: text
          name: code
          label: Setup Code
        - type: translation
          name: shortName
          label: Short Name
        - name: longName
          label: Long Name
          type: translation
        - type: dateRange
          name: effectiveDate
          label: Effective Date
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
        - type: radio
          name: status
          label: Status
          radio:
            - label: Active
              value: true
            - label: Inactive
              value: false
        - type: translationTextArea
          label: Note
          name: note
          placeholder: Enter Note
          textarea:
            autoSize:
              minRows: 3
              maxRows: 5
        - type: group
          label: Principles For Converting Seniority Spells
          borderRadius: 16px
          fields:
            - name: conversionPrinciple
              label: Conversion Principle
              type: radio
              radio:
                - label: By Time Period
                  value: T
                - label: By Block
                  value: B
                - label: By Specific Formula
                  value: F
            - type: text
              label: Calculation Formula
              name: caFunctionName
            - name: tsSetPrincipalOfSeniorityByTimes
              type: array
              mode: table
              _condition:
                transform: $.fields.conversionPrinciple = 'T'
              field:
                type: group
                fields:
                  - name: conversionUnit
                    type: select
                    label: Leave Accrual Unit
                    outputValue: value
                    placeholder: Select Leave Accrual Unit
                    width: 150px
                    select:
                      - value: 'N'
                        label: Năm
                      - value: T
                        label: Tháng
                  - label: Seniority From
                    type: number
                    name: seniorityFrom
                    width: 115px
                  - label: Seniority To
                    type: number
                    name: seniorityTo
                    width: 100px
                  - name: seniorityPermission
                    type: text
                    unvisible: true
                  - name: seniorityPermissionDisplay
                    type: text
                    label: Seniority Leave
                    width: 115px
                    _value:
                      transform: >-
                        $getFieldGroup($.extend.path, $.fields,
                        1).seniorityPermission & ' Leaves'
            - name: tsSetPrincipalOfSeniorityByBlocks
              type: array
              mode: table
              _condition:
                transform: $.fields.conversionPrinciple = 'B'
              field:
                type: group
                fields:
                  - name: conversionUnit
                    type: select
                    label: Leave Accrual Unit
                    outputValue: value
                    width: 140px
                    select:
                      - value: 'N'
                        label: Năm
                      - value: T
                        label: Tháng
                  - label: Seniority Block
                    type: number
                    name: seniorityBlock
                    placeholder: Enter Seniority Block
                    width: 110px
                  - name: seniorityPermission
                    type: text
                    unvisible: true
                  - name: seniorityPermissionDisplay
                    type: text
                    placeholder: Enter Seniority Leave
                    label: Seniority Leave
                    width: 115px
                    _value:
                      transform: >-
                        $getFieldGroup($.extend.path, $.fields,
                        1).seniorityPermission & ' Leaves'
  footer:
    create: false
    createdBy: creator
    createdOn: createdTime
    update: true
    updatedBy: lastEditor
    updatedOn: lastEditTime
  _mode:
    transform: $not($.extend.formType = 'view') ? 'mark-scroll'
  sources:
    nationsList:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    calculationFormulaList:
      uri: '"/api/ca-function-tks"'
      method: GET
      queryTransform: >-
        {'page':1,'limit':10000,'filter': [{'field':'status','operator':
        '$eq','value': true},{'field':'nationId','operator': '$eq','value':
        $.nationId},{'field':'effectiveDate','operator': '$lte','value':
        $.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - nationId
        - effectiveDate
  historyHeaderTitle: >-
    'Set Up Seniority Leave Calculation Rules: ' & $.longName.default & ' (' &
    $.code & ' )'
filter_config:
  fields:
    - name: code
      label: Setup Code
      type: text
      labelType: type-grid
      placeholder: Select Setup Code
    - name: countryId
      label: Country
      type: selectAll
      mode: multiple
      labelType: type-grid
      placeholder: Select Country
      isLazyLoad: true
      _options:
        transform: $nationsList($.extend.limit, $.extend.page, $.extend.search)
    - name: shortName
      label: Short Name
      type: text
      labelType: type-grid
      placeholder: Select Short Name
    - name: longName
      label: Long Name
      type: text
      labelType: type-grid
      placeholder: Select Long Name
    - name: conversionPrinciple
      label: Conversion Principle
      mode: multiple
      labelType: type-grid
      type: select
      placeholder: Select Conversion Principle
      select:
        - label: By Time Period
          value: T
        - label: By Block
          value: B
        - label: By Specific Formula
          value: F
    - name: status
      label: Status
      labelType: type-grid
      type: radio
      value: ''
      radio:
        - label: All
          value: ''
        - label: Active
          value: true
        - label: Inactive
          value: false
    - name: lastEditor
      label: Last Updated By
      labelType: type-grid
      type: selectAll
      placeholder: Select Creator
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $userList()
    - type: dateRange
      label: Last Updated On
      labelType: type-grid
      name: lastEditTime
      setting:
        format: dd/MM/yyyy
        type: date
  filterMapping:
    - field: countryId
      operator: $in
      valueField: countryId.(value)
    - field: code
      operator: $in
      valueField: code
    - field: shortName
      operator: $cont
      valueField: shortName
    - field: longName
      operator: $cont
      valueField: longName
    - field: conversionPrinciple
      operator: $in
      valueField: conversionPrinciple.(value)
    - field: status
      operator: $eq
      valueField: status
    - field: lastEditor
      operator: $in
      valueField: lastEditor.(value)
    - field: lastEditTime
      operator: $between
      valueField: lastEditTime
  sources:
    nationsList:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    userList:
      uri: '"/api/internal-users"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.userName, 'value':
        $item.userName}})
      disabledCache: true
layout_options:
  show_dialog_form_save_add_button: true
  history_widget_header_options:
    duplicate: false
  toolTable:
    export: true
    adjustDisplay: true
  tool_table:
    - id: export
      icon: icon-download-simple-bold
  export_all:
    type: base_total
  hide_action_row: true
  delete_multi_items: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: icon-plus-bold
    type: primary
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    title: Edit
    icon: icon-pencil-simple-bold
    type: ghost-gray
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: ghost-gray
backend_url: api/ts-set-principal-of-seniorities
screen_name: null
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: countryId
    defaultName: CountryCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Set Up Seniority Leave Calculation Rules
  parent:
    title: Leave Fund Regulations
