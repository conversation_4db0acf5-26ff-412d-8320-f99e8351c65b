id: TS.FS.FR.022
status: draft
sort: 212
user_created: 59722cb8-f858-481b-97f1-c97dd2a8e4be
date_created: '2024-07-04T02:48:22.576Z'
user_updated: abd14398-1d53-4e6f-9834-26328f0ebde0
date_updated: '2025-06-19T03:58:32.785Z'
title: Setup Additional Leave for Employee
requirement:
  time: 1741855196978
  blocks:
    - id: ZapCiBJgiP
      type: paragraph
      data:
        text: >
          - <PERSON><PERSON> thống cho phép bộ phận nhân sự tập đoàn/CTTV thiết lập số ngày
          phép cộng thêm cho CBNV áp dụng cho trường hợp CBNV có thành tích xuất
          sắc hoặc các trường hợp đặc thù khác theo quy định của Tập đoàn/ CTTV.
    - id: G0Pwp_SWcp
      type: paragraph
      data:
        text: >-
          - <PERSON><PERSON> thống kiểm tra thông tin và không cho phép tạo mới/ chỉnh sửa nếu
          thông tin thiết lập trùng thông tin về “Thông tin nhân viên”, “Thông
          tin đơn vị/phòng ban” và” Ngày hiệu lực” với các thiết lập trước đó. 
    - id: uPEfu9KytC
      type: paragraph
      data:
        text: >-

          - Hệ thống cho phép import thông tin thiết lập phép cộng thêm cho nhân
          viên.&nbsp;&nbsp;
  version: 2.30.7
screen_design: null
module: TS
local_fields:
  - code: employeeCode
    title: ' Employee ID'
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    pinned: true
    show_sort: true
  - code: employeeRecordNumber
    title: Employee Record Number (ERN)
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 16
    options__tabular__align: left
  - code: employeeName
    title: ' Employee Name'
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: localForeigners
    title: Local/Foreign Employees
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 12
  - code: employeeGroup
    title: Employee Group
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: contractType
    title: Contract Type
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: jobTitle
    title: Job Title
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: effectiveStartDate
    title: Effective Start Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    options__tabular__column_width: 12
  - code: effectiveEndDate
    title: Effective End Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    options__tabular__column_width: 12
  - code: daysOf
    title: Number of Leave Days
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 12
  - code: note
    title: Note
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
  - code: updatedBy
    title: Last Updated By
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: updatedAt
    title: Last Updated On
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
mock_data:
  - employeeCode: '00123456'
    employeeName: Bùi Phương
    employeeRecord: '1'
    contractType: Hợp đồng XĐTH 12 tháng
    group: Nhân viên chính thức
    jobTitle: Cán bộ kiểm thử phần mềm
    localForeigners: Người bản địa
    effectiveStartDate: 01/01/2024
    effectiveEndDate: 01/05/2024
    daysOf: '2'
    note: Áp dụng chung
    createTime: 01/01/2024
    creator: Phuong Bui
    lastEditTime: 01/04/2024
    lastEditor: Khanh Vy
  - employeeCode: '00123457'
    employeeName: Nguyễn Văn A
    employeeRecord: '2'
    contractType: Hợp đồng XĐTH 24 tháng
    group: Nhân viên chính thức
    jobTitle: Cán bộ kiểm thử phần mềm
    localForeigners: Người bản địa
    effectiveStartDate: 01/02/2024
    effectiveEndDate: 01/05/2024
    daysOf: '2'
    note: Áp dụng chung
    createTime: 01/02/2024
    creator: Nguyen Bui
    lastEditTime: 01/05/2024
    lastEditor: Thanh Hằng
  - employeeCode: '00123458'
    employeeName: Trần Thị B
    employeeRecord: '3'
    contractType: Hợp đồng dài hạn
    group: Nhân viên chính thức
    jobTitle: Cán bộ kiểm thử phần mềm
    localForeigners: Người bản địa
    effectiveStartDate: 01/03/2024
    effectiveEndDate: 01/05/2024
    daysOf: '2'
    note: Áp dụng chung
    createTime: 01/03/2024
    creator: Trần Bui
    lastEditTime: 01/06/2024
    lastEditor: Thanh Huyền
  - employeeCode: '00123459'
    employeeName: Lê Văn C
    employeeRecord: '4'
    contractType: Hợp đồng ngắn hạn
    group: Nhân viên chính thức
    jobTitle: Cán bộ kiểm thử phần mềm
    localForeigners: Người bản địa
    effectiveStartDate: 01/04/2024
    effectiveEndDate: 01/05/2024
    daysOf: '2'
    note: Áp dụng chung
    createTime: 01/04/2024
    creator: Lê Bui
    lastEditTime: 01/07/2024
    lastEditor: Minh Khang
  - employeeCode: '00123460'
    employeeName: Phạm Thị D
    employeeRecord: '5'
    contractType: Hợp đồng thực tập
    group: Nhân viên chính thức
    jobTitle: Cán bộ kiểm thử phần mềm
    localForeigners: Người bản địa
    effectiveStartDate: 01/05/2024
    effectiveEndDate: 01/05/2024
    daysOf: '2'
    note: Áp dụng chung
    createTime: 01/05/2024
    creator: Phạm Bui
    lastEditTime: 01/08/2024
    lastEditor: Minh Hằng
  - employeeCode: '00123461'
    employeeName: Đỗ Văn E
    employeeRecord: '6'
    contractType: Hợp đồng thử việc
    group: Nhân viên chính thức
    jobTitle: Cán bộ kiểm thử phần mềm
    localForeigners: Người bản địa
    effectiveStartDate: 01/06/2024
    effectiveEndDate: 01/05/2024
    daysOf: '2'
    note: Áp dụng chung
    createTime: 01/06/2024
    creator: Đỗ Bui
    lastEditTime: 01/09/2024
    lastEditor: Ngọc Linh
  - employeeCode: '00123462'
    employeeName: Vũ Thị F
    employeeRecord: '7'
    contractType: Hợp đồng cộng tác viên
    group: Nhân viên chính thức
    jobTitle: Cán bộ kiểm thử phần mềm
    localForeigners: Người bản địa
    effectiveStartDate: 01/07/2024
    effectiveEndDate: 01/05/2024
    daysOf: '2'
    note: Áp dụng chung
    createTime: 01/07/2024
    creator: Vũ Bui
    lastEditTime: 01/10/2024
    lastEditor: Khánh Linh
  - employeeCode: '00123463'
    employeeName: Nguyễn Văn G
    employeeRecord: '8'
    contractType: Hợp đồng dự án
    group: Nhân viên chính thức
    jobTitle: Cán bộ kiểm thử phần mềm
    localForeigners: Người bản địa
    effectiveStartDate: 01/08/2024
    effectiveEndDate: 01/05/2024
    daysOf: '2'
    note: Áp dụng chung
    createTime: 01/08/2024
    creator: Nguyễn Bui
    lastEditTime: 01/11/2024
    lastEditor: Khánh Huyền
  - employeeCode: '00123464'
    employeeName: Lê Thị H
    employeeRecord: '9'
    contractType: Hợp đồng tạm thời
    group: Nhân viên chính thức
    jobTitle: Cán bộ kiểm thử phần mềm
    localForeigners: Người bản địa
    effectiveStartDate: 01/09/2024
    effectiveEndDate: 01/05/2024
    daysOf: '2'
    note: Áp dụng chung
    createTime: 01/09/2024
    creator: Lê Bui
    lastEditTime: 01/12/2024
    lastEditor: Thanh Tú
local_buttons: null
layout: layout-table
form_config:
  formSize:
    create: largex
    edit: largex
    view: small
  fields:
    - type: group
      fields:
        - type: select
          name: employee
          key: employee
          label: Employee
          placeholder: Select Employee
          isLazyLoad: true
          outputValue: value
          validators:
            - type: required
          _select:
            transform: $employeesList($.extend.limit, $.extend.page, $.extend.search)
          _condition:
            transform: $.extend.formType = 'create'
        - type: text
          label: Employee
          disabled: true
          _value:
            transform: >-
              $.fields.employeeCode & ' - ' & $.fields.employeeRecordNumber & '
              - ' & $.extend.defaultValue.employeeName
          _condition:
            transform: $.extend.formType = 'edit'
        - type: text
          name: employeeCode
          label: Employee
          unvisible: true
          _value:
            transform: $.fields.employee.employeeId
        - type: text
          name: employeeIdObj
          unvisible: true
          _value:
            transform: $.variables._employeeId
        - type: text
          name: employeeRecordNumber
          label: employeeRecordNumber
          unvisible: true
          _value:
            transform: $string($.fields.employee.employeeRecordNumber)
    - type: group
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - type: text
          label: Employee ID
          name: employeeCode
        - type: text
          label: Employee Record Number
          name: employeeRecordNumber
        - type: text
          label: Employee Name
          name: employeeName
        - type: text
          label: Contract Type
          name: contractType
        - type: text
          label: Employee Group
          name: employeeGroup
        - type: text
          label: Job Title
          name: jobTitle
        - type: text
          label: Local/Foreign Employees
          name: localForeigners
        - type: dateRange
          name: effectiveStartDate
          label: Effective Start Date
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
        - type: dateRange
          name: effectiveEndDate
          label: Effective End Date
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
        - name: daysOf
          label: Number of Leave Days
          type: text
    - type: group
      n_cols: 2
      _condition:
        transform: $.extend.formType != 'view'
      fields:
        - type: dateRange
          name: effectiveStartDate
          label: Effective Start Date
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
          validators:
            - type: required
        - type: dateRange
          name: effectiveEndDate
          label: Effective End Date
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
          validators:
            - type: required
            - type: ppx-custom
              args:
                transform: >-
                  $DateDiff($.fields.effectiveEndDate,
                  $.fields.effectiveStartDate, 'd') < 0
              text: End Date must be on or after start date.
        - name: daysOf
          label: Number of Leave Days
          type: number
          outputValue: value
          placeholder: Enter Number of Leave Days
          _condition:
            transform: $.extend.formType != 'view'
          validators:
            - type: required
            - type: ppx-custom
              args:
                transform: $.fields.daysOf > 9999
              text: Number of Leave Days must be less than or equal to 9999.
    - type: group
      fields:
        - name: note
          label: Note
          type: textarea
          validators:
            - type: maxLength
              args: '1000'
              text: Note should not exceed 1000 characters.
          textarea:
            autoSize:
              minRows: 3
            maxCharCount: 1000
          placeholder: Enter Note
  overview:
    dependentField: employeeIdObj
    _condition:
      transform: $.extend.formType = 'create' or $.extend.formType = 'edit'
    noDataMessages: Select employee information to display data
    title: Employee Detail
    border: true
    uri: >-
      /api/personals/:{employeeId}:/job-datas?filter[0]=employeeRecordNumber||$eq||:{employeeRecordNumber}:
    display:
      - label: Local/Foreign
        _value:
          transform: $.variables._selectedLocalExpat.localForeigner
      - key: employeeGroupName
        label: Employee Group
      - label: Contract Type
        _value:
          transform: $.variables._selectedContract.contractTypeName
      - key: jobName
        label: Job Title
  footer:
    create: true
    update: true
    createdOn: createdAt
    updatedOn: updatedAt
    createdBy: createdBy
    updatedBy: updatedBy
  sources:
    employeesList:
      uri: '"/api/personals"'
      method: GET
      queryTransform: '{''limit'': $.limit, ''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.employeeId &' - '&
        $item.employeeRecordNumber &' - '& $item.name, 'value': {'employeeId':
        $item.employeeId, 'employeeRecordNumber': $item.employeeRecordNumber,
        'jobDataId': $item.jobDataId}, 'employeeId': $item.employeeId,
        'employeeRecordNumber': $item.employeeRecordNumber}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    detailLocalExpat:
      uri: '"/api/employee-related-info/local-foreigner"'
      method: GET
      queryTransform: >-
        {'filter':[{'field': 'employeeId', 'operator': '$eq', 'value':
        $.employeeId}, {'field': 'employeeRecordNumber', 'operator': '$eq',
        'value': $.ern}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $
      disabledCache: true
      params:
        - employeeId
        - ern
    detailContract:
      uri: '"/api/personals/" & $.employeeId & "/contracts"'
      method: GET
      queryTransform: >-
        {'filter':[{'field': 'employeeRecordNumber', 'operator': '$eq', 'value':
        $.ern}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $[0]
      disabledCache: true
      params:
        - employeeId
        - ern
  variables:
    _employeeId:
      transform: >-
        $.fields.employeeCode != '' ? {'employeeId':
        $.fields.employeeCode,'employeeRecordNumber' :
        $.fields.employeeRecordNumber}
    _selectedLocalExpat:
      transform: >-
        ($exists($.fields.employeeCode)?
        $detailLocalExpat($.fields.employeeCode,
        $.fields.employeeRecordNumber):{})
    _selectedContract:
      transform: >-
        ($exists($.fields.employeeCode)? $detailContract($.fields.employeeCode,
        $.fields.employeeRecordNumber):{})
filter_config:
  fields:
    - type: selectAll
      name: employeeCode
      label: Employee
      placeholder: Select Employee
      labelType: type-grid
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $employeesList($.extend.limit, $.extend.page, $.extend.search)
    - type: select
      name: localForeigners
      label: Local/Foreign Employees
      labelType: type-grid
      placeholder: Select Local/Foreign Employees
      mode: multiple
      _select:
        transform: $.variables._localExpatList
    - type: select
      name: contractTypeId
      labelType: type-grid
      label: Contract Type
      placeholder: Select Contract Type
      mode: multiple
      _select:
        transform: $.variables._contractTypeList
    - type: select
      name: employeeGroupId
      label: Employee Group
      labelType: type-grid
      placeholder: Select Employee Group
      mode: multiple
      _select:
        transform: $.variables._employeeGroupList
    - type: select
      name: jobTitleId
      label: Job Title
      labelType: type-grid
      placeholder: Select Job
      mode: multiple
      _select:
        transform: $.variables._jobList
    - type: dateRange
      label: Effective Start Date
      labelType: type-grid
      name: effectiveStartDate
    - type: dateRange
      label: Effective End Date
      labelType: type-grid
      name: effectiveEndDate
    - name: daysOf
      label: Number of Leave Days
      labelType: type-grid
      type: text
      placeholder: Enter Number of Leave Days
    - type: select
      name: createdBy
      label: Create By
      labelType: type-grid
      placeholder: Select Create By
      mode: multiple
      _select:
        transform: $userList()
    - type: dateRange
      label: Created On
      labelType: type-grid
      name: createdAt
    - type: select
      name: updatedBy
      label: Last Updated By
      labelType: type-grid
      placeholder: Select Last Updated By
      mode: multiple
      _select:
        transform: $userList()
    - type: dateRange
      label: Last Updated On
      labelType: type-grid
      name: updatedAt
  filterMapping:
    - field: $
      operator: $in
      valueField: employeeCode.(value)
    - field: contractTypeId
      operator: $in
      valueField: contractTypeId.(value)
    - field: jobTitleId
      operator: $in
      valueField: jobTitleId.(value)
    - field: employeeGroupId
      operator: $in
      valueField: employeeGroupId.(value)
    - field: localForeigners
      operator: $in
      valueField: localForeigners.(value)
    - field: effectiveStartDate
      operator: $between
      valueField: effectiveStartDate
    - field: effectiveEndDate
      operator: $between
      valueField: effectiveEndDate
    - field: daysOf
      operator: $eq
      valueField: daysOf
    - field: createdBy
      operator: $in
      valueField: createdBy.(value)
    - field: createdAt
      operator: $between
      valueField: createdAt
    - field: updatedBy
      operator: $in
      valueField: updatedBy.(value)
    - field: updatedAt
      operator: $between
      valueField: updatedAt
  sources:
    employeesList:
      uri: '"/api/personals"'
      method: GET
      queryTransform: '{''limit'': $.limit, ''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.employeeId &' - '&
        $item.employeeRecordNumber &' - '& $item.name,'code': $item.employeeId,
        'value': {'employeeCode': $item.employeeId,'employeeRecordNumber':
        $item.employeeRecordNumber}}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    contractTypeList:
      uri: '"/api/picklists/CONTRACTTYPE/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})
      disabledCache: true
    localExpatList:
      uri: '"/api/picklists/LOCALFOREIGNERS/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})
      disabledCache: true
    employeeGroupList:
      uri: '"/api/employee-groups"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.employeeGroup.default,
        'value': $item.code}})[]
      disabledCache: true
    jobList:
      uri: '"/api/picklists/JOBWORKPERMIT/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})
      disabledCache: true
    userList:
      uri: '"/api/internal-users"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.userName, 'value':
        $item.userName}})
      disabledCache: true
  variables:
    _contractTypeList:
      transform: $contractTypeList()
    _localExpatList:
      transform: $localExpatList()
    _employeeGroupList:
      transform: $employeeGroupList()
    _jobList:
      transform: $jobList()
layout_options:
  toolTable:
    export: true
    import: true
    adjustDisplay: true
  tool_table:
    - id: import
      icon: icon-upload-simple
      paramsRedirect:
        type: TS_OBJECT
        entityOrObj: TSSetAddDayOffRule
    - id: export
      icon: icon-download-simple
  show_dialog_form_save_add_button: true
  show_detail_history: false
  export_all:
    type: base_total
  hide_action_row: true
  show_dialog_duplicate_button: false
  delete_multi_items: true
layout_options__header_buttons:
  - id: create
    title: Create
    type: primary
    icon: plus
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    title: Edit
    icon: icon-pencil-simple
  - id: delete
    title: Delete
    icon: icon-trash
backend_url: /api/ts-set-add-day-off-rules
screen_name: ts-set-add-day-off-rules
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: employeeCode
    defaultName: EmployeeId
  - name: employeeRecordNumber
    defaultName: EmployeeRecordNumber
  - name: jobTitleId
    defaultName: JobCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Setup Additional Leave for Employee
  parent:
    title: Leave Fund Regulations
