id: TS.FS.FR.023
status: draft
sort: 227
user_created: 59722cb8-f858-481b-97f1-c97dd2a8e4be
date_created: '2024-07-08T03:59:43.553Z'
user_updated: abd14398-1d53-4e6f-9834-26328f0ebde0
date_updated: '2025-06-19T04:04:39.826Z'
title: List of Carryover Leave
requirement:
  time: 1720410665646
  blocks:
    - id: '-36wghDB1j'
      type: paragraph
      data:
        text: >-
          - <PERSON><PERSON> thống cho phép bộ phận nhân sự tập đoàn/CTTV theo dõi và quản lý
          thông tin phép năm cũ của nhân viên theo quy định của tập đoàn/ CTTV.
    - id: KwYwh4RaZ_
      type: paragraph
      data:
        text: >

          - <PERSON>ệ thống kiểm tra thông tin và không cho thiết lập mới nếu thông tin
          thiết lập trùng thông tin về “Thông tin nhân viên”, “Thông tin Đơn
          vị/phòng ban” và “Năm hiệu lực” với các thiết lập trước đó. 
    - id: ymJo8uiGMZ
      type: paragraph
      data:
        text: |+
          - Hệ thống cho phép import thông tin thông tin phép cũ của nhân viên.

  version: 2.29.1
screen_design: null
module: TS
local_fields:
  - code: employeeCode
    title: Employee ID
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    pinned: true
    show_sort: true
    options__tabular__column_width: 8.75
  - code: employeeName
    title: Employee Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 8.75
  - code: employeeRecordNumber
    title: Employee Record Number (ERN)
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__align: left
    options__tabular__column_width: 15.625
  - code: localForeignersName
    title: Local/Foreign Employees
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 12.125
  - code: group
    title: Employee Group
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 11.25
  - code: contractType
    title: Contract Type
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: jobTitle
    title: Job Title
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 12
  - code: tssetNodoyearDetailName
    title: Annual Carrayover Leave
    data_type:
      key: Nmberic
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 14
  - code: calculationOfLeave
    title: Leave Accrual End Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    options__tabular__column_width: 13
  - code: licensePeriod
    title: Leave Validity Period
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    options__tabular__column_width: 13
  - code: allowedToTransfer
    title: Transferable Leave Days
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__align: left
    options__tabular__column_width: 13
  - code: note
    title: Note
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    options__tabular__column_width: 13
  - code: lastEditer
    title: Last Updated By
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 12
  - code: lastEditTime
    title: Last Updated On
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
    options__tabular__column_width: 12
mock_data:
  - code: '00123459'
    name: Bùi Phương
    record: '1'
    contractType: Hợp đồng chính thức
    group: Người bản địa
    jobTitle: Nhân viên chính thức
    localForeigners: Cán bộ kiểm thử phần mềm
    holidayOld: '2024'
    calculationOfLeave: 01/01/2024
    allowedToTransfer: 31/03/2024
    licensePeriod: '3'
    note: Áp dụng chung
    creator: Phương Bùi
    createTime: 01/01/2024
    lastEditer: Khánh Vy
    lastEditTime: 01/04/2024
  - code: '00123458'
    name: Bùi Phương
    record: '1'
    contractType: Hợp đồng chính thức
    group: Người bản địa
    jobTitle: Nhân viên chính thức
    localForeigners: Cán bộ kiểm thử phần mềm
    holidayOld: '2024'
    calculationOfLeave: 01/01/2024
    allowedToTransfer: 31/03/2024
    licensePeriod: '3'
    note: Áp dụng chung
    creator: Phương Bùi
    createTime: 01/01/2024
    lastEditer: Khánh Vy
    lastEditTime: 01/04/2024
  - code: '00123457'
    name: Bùi Phương
    record: '1'
    contractType: Hợp đồng chính thức
    group: Người bản địa
    jobTitle: Nhân viên chính thức
    localForeigners: Cán bộ kiểm thử phần mềm
    holidayOld: '2024'
    calculationOfLeave: 01/01/2024
    allowedToTransfer: 31/03/2024
    licensePeriod: '3'
    note: Áp dụng chung
    creator: Phương Bùi
    createTime: 01/01/2024
    lastEditer: Khánh Vy
    lastEditTime: 01/04/2024
  - code: '00123456'
    name: Bùi Phương
    record: '1'
    contractType: Hợp đồng chính thức
    group: Người bản địa
    jobTitle: Nhân viên chính thức
    localForeigners: Cán bộ kiểm thử phần mềm
    holidayOld: '2024'
    calculationOfLeave: 01/01/2024
    allowedToTransfer: 31/03/2024
    licensePeriod: '3'
    note: Áp dụng chung
    creator: Phương Bùi
    createTime: 01/01/2024
    lastEditer: Khánh Vy
    lastEditTime: 01/04/2024
  - code: '00123455'
    name: Bùi Phương
    record: '1'
    contractType: Hợp đồng chính thức
    group: Người bản địa
    jobTitle: Nhân viên chính thức
    localForeigners: Cán bộ kiểm thử phần mềm
    holidayOld: '2024'
    calculationOfLeave: 01/01/2024
    allowedToTransfer: 31/03/2024
    licensePeriod: '3'
    note: Áp dụng chung
    creator: Phương Bùi
    createTime: 01/01/2024
    lastEditer: Khánh Vy
    lastEditTime: 01/04/2024
  - code: '00123454'
    name: Bùi Phương
    record: '1'
    contractType: Hợp đồng chính thức
    group: Người bản địa
    jobTitle: Nhân viên chính thức
    localForeigners: Cán bộ kiểm thử phần mềm
    holidayOld: '2024'
    calculationOfLeave: 01/01/2024
    allowedToTransfer: 31/03/2024
    licensePeriod: '3'
    note: Áp dụng chung
    creator: Phương Bùi
    createTime: 01/01/2024
    lastEditer: Khánh Vy
    lastEditTime: 01/04/2024
  - code: '00123453'
    name: Bùi Phương
    record: '1'
    contractType: Hợp đồng chính thức
    group: Người bản địa
    jobTitle: Nhân viên chính thức
    localForeigners: Cán bộ kiểm thử phần mềm
    holidayOld: '2024'
    calculationOfLeave: 01/01/2024
    allowedToTransfer: 31/03/2024
    licensePeriod: '3'
    note: Áp dụng chung
    creator: Phương Bùi
    createTime: 01/01/2024
    lastEditer: Khánh Vy
    lastEditTime: 01/04/2024
  - code: '00123452'
    name: Bùi Phương
    record: '1'
    contractType: Hợp đồng chính thức
    group: Người bản địa
    jobTitle: Nhân viên chính thức
    localForeigners: Cán bộ kiểm thử phần mềm
    holidayOld: '2024'
    calculationOfLeave: 01/01/2024
    allowedToTransfer: 31/03/2024
    licensePeriod: '3'
    note: Áp dụng chung
    creator: Phương Bùi
    createTime: 01/01/2024
    lastEditer: Khánh Vy
    lastEditTime: 01/04/2024
  - code: '00123451'
    name: Bùi Phương
    record: '1'
    contractType: Hợp đồng chính thức
    group: Người bản địa
    jobTitle: Nhân viên chính thức
    localForeigners: Cán bộ kiểm thử phần mềm
    holidayOld: '2024'
    calculationOfLeave: 01/01/2024
    allowedToTransfer: 31/03/2024
    licensePeriod: '3'
    note: Áp dụng chung
    creator: Phương Bùi
    createTime: 01/01/2024
    lastEditer: Khánh Vy
    lastEditTime: 01/04/2024
  - code: '00123450'
    name: Bùi Phương
    record: '1'
    contractType: Hợp đồng chính thức
    group: Người bản địa
    jobTitle: Nhân viên chính thức
    localForeigners: Cán bộ kiểm thử phần mềm
    holidayOld: '2024'
    calculationOfLeave: 01/01/2024
    allowedToTransfer: 31/03/2024
    licensePeriod: '3'
    note: Áp dụng chung
    creator: Phương Bùi
    createTime: 01/01/2024
    lastEditer: Khánh Vy
    lastEditTime: 01/04/2024
local_buttons: null
layout: layout-table
form_config:
  formSize:
    create: large
    edit: large
  formTitle:
    create: Add New Carryover Leave
    edit: Edit Carryover Leave
    view: View Carryover Leave Details
  fields:
    - type: group
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - type: select
          name: employee
          label: Employee
          placeholder: Select Employee
          isLazyLoad: true
          validators:
            - type: required
          _select:
            transform: $employeesList($.extend.limit, $.extend.page, $.extend.search)
          _value:
            transform: $.variables._employee[0].value
          _condition:
            transform: $.extend.formType = 'create'
          outputValue: value
        - type: text
          label: Employee
          disabled: true
          _value:
            transform: >-
              $.extend.defaultValue.employeeCode & ' - ' &
              $.extend.defaultValue.employeeRecordNumber & ' - ' &
              $.extend.defaultValue.employeeName
          _condition:
            transform: $.extend.formType = 'edit'
        - type: select
          name: selectedJobData
          unvisible: true
          dependantField: $.fields.employee.employeeId
          _value:
            transform: >-
              $jobDatasList($.extend.formType = 'edit'? $.fields.employeeCode :
              $.fields.employee.employeeId)[0]
        - type: text
          name: employeeCode
          label: Employee
          unvisible: true
          _value:
            transform: $.fields.employee.employeeId
        - type: number
          name: employeeRecordNumber
          unvisible: true
          _value:
            transform: $string($.fields.employee.employeeRecordNumber)
        - type: text
          name: employeeIdObj
          unvisible: true
          _value:
            transform: $.variables._employeeIdObj
    - type: group
      readOnly: true
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - type: text
          label: Employee ID
          name: employeeCode
        - type: text
          label: Emloyee Record Number
          name: employeeRecordNumber
        - type: text
          label: Employee Name
          name: employeeName
        - type: text
          label: Local/Foreign Employees
          name: localForeignersName
        - type: text
          label: Employee Group
          name: group
        - type: text
          label: Contract Type
          name: contractType
        - type: text
          label: Job Title
          name: jobTitle
        - type: text
          label: Annual Carrayover Leave
          name: tssetNodoyearDetailName
        - name: calculationOfLeave
          label: Leave Accrual End Date
          type: dateRange
          mode: date-picker
          setting:
            format: dd/MM/YYYY
            type: date
        - type: text
          label: Transferable Leave Days
          name: allowedToTransfer
        - name: licensePeriod
          label: Leave Validity Period
          type: dateRange
          mode: date-picker
          setting:
            format: dd/MM/YYYY
            type: date
    - type: group
      n_cols: 2
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - type: select
          label: Annual Carryover Leave
          name: tssetNodoyearDetailId
          outputValue: value
          dependantField: $.fields.employee.employeeId
          placeholder: Select Annual Carrayover Leave
          validators:
            - type: ppx-custom
              args:
                transform: >-
                  $.variables._selectedAnualLeave.year >
                  $string($GetDatePart($.fields.licensePeriod, 'y'))
              text: Annual Carryover Leave must be less than Leave Validity Period
            - type: required
          _select:
            transform: $.variables._anualLeaveList
        - name: calculationOfLeave
          label: Leave Accrual End Date
          type: dateRange
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
          validators:
            - type: required
        - name: allowedToTransfer
          label: Transferable Leave Days
          type: number
          number:
            suffix: Days
          validators:
            - type: required
            - type: max
              args: '999'
              text: >-
                Number Of Days Allowed To Transfer should not exceed 3
                characters.
        - name: licensePeriod
          label: Leave Validity Period
          type: dateRange
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
          validators:
            - type: ppx-custom
              args:
                transform: >-
                  $.variables._selectedAnualLeave.year >
                  $string($GetDatePart($.fields.licensePeriod, 'y'))
              text: Annual Carryover Leave must be less than Leave Validity Period
            - type: required
    - type: group
      fields:
        - name: note
          label: Note
          type: textarea
          placeholder: Enter note
          validators:
            - type: maxLength
              args: '1000'
              text: Note should not exceed 1000 characters.
          textarea:
            autoSize:
              minRows: 3
            maxCharCount: 1000
  overview:
    dependentField: employeeIdObj
    _condition:
      transform: $not($.extend.formType = 'view')
    border: true
    title: Employee Detail
    uri: >-
      /api/personals/:{employeeId}:/job-datas?filter[0]=employeeRecordNumber||$eq||:{employeeRecordNumber}:
    display:
      - label: Local/Foreign
        _value:
          transform: $.variables._selectedLocalExpat.localForeigner
      - key: employeeGroupName
        label: Employee Group
      - label: Contract Type
        _value:
          transform: $.variables._selectedContract.contractTypeName
      - key: jobName
        label: Job Title
  footer:
    create: true
    update: true
    createdOn: createTime
    updatedOn: lastEditTime
    createdBy: creator
    updatedBy: lastEditer
  sources:
    employeesList:
      uri: '"/api/personals"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'filter':
        [{'field':'search','operator': '$eq','value':$.search}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label':
        $join($filter([$item.employeeId,$string($item.employeeRecordNumber),$item.fullName],
        $boolean), ' - ')  , 'value': {'employeeId': $item.employeeId,
        'employeeRecordNumber': $string($item.employeeRecordNumber),
        'jobDataId': $item.jobDataId}, 'employeeId': $item.employeeId,
        'employeeRecordNumber': $string($item.employeeRecordNumber),'jobDataId':
        $item.jobDataId}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    jobDatasList:
      uri: '"/api/personals/"  & $.employeeId &  "/job-datas"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'companyId': $item.company, 'groupId':
        $item.group}})[]
      disabledCache: true
      params:
        - employeeId
    anualLeave:
      uri: '"/api/ts-set-no-do-years/list-data"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'companyId','operator': '$eq','value':
        $.companyId},{'field':'groupId','operator': '$eq','value':
        $.groupId},{'field':'checkNationId','operator': '$eq','value': false}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.name, 'value': $item.id,
        'year': $string($item.year)}})[]
      disabledCache: true
      params:
        - companyId
        - groupId
    detailLocalExpat:
      uri: '"/api/employee-related-info/local-foreigner"'
      method: GET
      queryTransform: >-
        {'filter':[{'field': 'employeeId', 'operator': '$eq', 'value':
        $.employeeId}, {'field': 'employeeRecordNumber', 'operator': '$eq',
        'value': $.ern}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $
      disabledCache: true
      params:
        - employeeId
        - ern
    detailContract:
      uri: '"/api/personals/" & $.employeeId & "/contracts"'
      method: GET
      queryTransform: >-
        {'filter':[{'field': 'employeeRecordNumber', 'operator': '$eq', 'value':
        $.ern}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $[0]
      disabledCache: true
      params:
        - employeeId
        - ern
  variables:
    _employeeIdObj:
      transform: >-
        $.fields.employeeCode != '' ? {'employeeId': $.fields.employeeCode,
        'employeeRecordNumber': $.fields.employeeRecordNumber}
    _selectedLocalExpat:
      transform: >-
        ($exists($.fields.employeeCode)?
        $detailLocalExpat($.fields.employeeCode,
        $.fields.employeeRecordNumber):{})
    _selectedContract:
      transform: >-
        ($exists($.fields.employeeCode)? $detailContract($.fields.employeeCode,
        $.fields.employeeRecordNumber):{})
    _anualLeaveList:
      transform: >-
        $anualLeave($.fields.selectedJobData.companyId,$.fields.selectedJobData.groupId)
    _selectedAnualLeave:
      transform: >-
        ($.fields.tssetNodoyearDetailId; $filter($.variables._anualLeaveList ,
        function($v, $i, $a) { $v.value = $.fields.tssetNodoyearDetailId }))
filter_config:
  fields:
    - type: selectAll
      label: Group
      name: groupCode
      mode: multiple
      labelType: type-grid
      isLazyLoad: true
      _options:
        transform: $groupsList($.extend.limit, $.extend.page, $.extend.search)
    - name: companyCode
      label: Company
      type: selectAll
      mode: multiple
      labelType: type-grid
      isLazyLoad: true
      _options:
        transform: $companiesList($.extend.limit, $.extend.page, $.extend.search)
    - name: legalEntityCode
      label: Legal Entity
      type: selectAll
      mode: multiple
      labelType: type-grid
      isLazyLoad: true
      _options:
        transform: $legalEntitiesList($.extend.limit, $.extend.page, $.extend.search)
    - name: businessUnitCode
      label: Business Unit
      type: selectAll
      mode: multiple
      labelType: type-grid
      isLazyLoad: true
      _options:
        transform: $businessUnitsList($.extend.limit, $.extend.page, $.extend.search)
    - name: divisionCode
      label: Division
      type: selectAll
      mode: multiple
      labelType: type-grid
      isLazyLoad: true
      _options:
        transform: $divisionsList($.extend.limit, $.extend.page, $.extend.search)
    - name: departmentCode
      label: Department
      type: selectAll
      mode: multiple
      labelType: type-grid
      isLazyLoad: true
      _options:
        transform: $departmentsList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      name: employee
      label: Employee
      labelType: type-grid
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $employeesList($.extend.limit, $.extend.page, $.extend.search)
    - name: jobTitleCode
      label: Job Title
      type: selectAll
      labelType: type-grid
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $jobCodesList($.extend.limit, $.extend.page, $.extend.search)
    - type: dateRange
      name: startDate
      label: Start Date
      mode: date-picker
      labelType: type-grid
      setting:
        format: dd/MM/yyyy
        type: date
    - type: dateRange
      name: endDate
      label: End Date
      labelType: type-grid
      mode: date-picker
      setting:
        format: dd/MM/yyyy
        type: date
    - type: dateRange
      name: paymentDate
      label: Payment Date
      labelType: type-grid
      setting:
        format: dd/MM/yyyy
        type: date
    - name: createdBy
      label: Created By
      type: select
      mode: multitple
      labelType: type-grid
      _select:
        transform: $userList()
    - type: dateRange
      name: createdAt
      label: Created On
      labelType: type-grid
    - name: updatedBy
      label: Last Updated By
      type: select
      mode: multitple
      labelType: type-grid
      _select:
        transform: $userList()
    - type: dateRange
      name: updatedAt
      labelType: type-grid
      label: Last Updated On
  filterMapping:
    - field: groupCode
      operator: $in
      valueField: groupCode.(value)
    - field: companyCode
      operator: $in
      valueField: companyCode.(value)
    - field: legalEntityCode
      operator: $in
      valueField: legalEntityCode.(value)
    - field: businessUnitCode
      operator: $in
      valueField: businessUnitCode.(value)
    - field: divisionCode
      operator: $in
      valueField: divisionCode.(value)
    - field: departmentCode
      operator: $in
      valueField: departmentCode.(value)
    - field: $
      operator: $in
      valueField: employee.(value)
    - field: jobTitleCode
      operator: $in
      valueField: jobTitleCode.(value)
    - field: startDate
      operator: $eq
      valueField: startDate
    - field: endDate
      operator: $eq
      valueField: endDate
    - field: paymentDate
      operator: $between
      valueField: paymentDate
    - field: creator
      operator: $in
      valueField: createdBy.(value)
    - field: createTime
      operator: $between
      valueField: createdAt
    - field: lastEditer
      operator: $in
      valueField: updatedBy.(value)
    - field: lastEditTime
      operator: $between
      valueField: updatedAt
  sources:
    groupsList:
      uri: '"/api/groups/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    companiesList:
      uri: '"/api/companies/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    legalEntitiesList:
      uri: '"/api/legal-entities/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})
      disabledCache: true
      params:
        - limit
        - page
        - search
    businessUnitsList:
      uri: '"/api/business-units/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})
      disabledCache: true
      params:
        - limit
        - page
        - search
    divisionsList:
      uri: '"/api/divisions/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})
      disabledCache: true
      params:
        - limit
        - page
        - search
    departmentsList:
      uri: '"/api/departments/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})
      disabledCache: true
      params:
        - limit
        - page
        - search
    employeesList:
      uri: '"/api/personals"'
      method: GET
      queryTransform: '{''limit'': $.limit, ''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.employeeId &' - '&
        $item.employeeRecordNumber &' - '& $item.name,'code': $item.employeeId,
        'value': {'employeeCode': $item.employeeId,'employeeRecordNumber':
        $string($item.employeeRecordNumber)}}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    jobCodesList:
      uri: '"/api/job-codes"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    userList:
      uri: '"/api/internal-users"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.userName, 'value':
        $item.userName}})[]
      disabledCache: true
layout_options:
  tool_table:
    - id: import
      icon: icon-download-simple-bold
      paramsRedirect:
        type: TS_OBJECT
        entityOrObj: TSManagerOyli
    - id: export
      icon: icon-upload-simple-bold
  show_dialog_form_save_add_button: true
  show_detail_history: false
  export_all:
    type: base_total
  hide_action_row: true
  show_dialog_duplicate_button: false
  delete_multi_items: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: icon-plus-bold
    type: primary
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    icon: icon-pencil-simple-bold
    type: ghost-gray
  - id: delete
    icon: icon-trash-bold
    type: ghost-gray
backend_url: api/ts-manager-oylis
screen_name: null
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: employeeCode
    defaultName: EmployeeId
  - name: employeeRecordNumber
    defaultName: EmployeeRecordNumber
  - name: jobTitleId
    defaultName: JobCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: List of Carryover Leave
  parent:
    title: Leave Fund Regulations
