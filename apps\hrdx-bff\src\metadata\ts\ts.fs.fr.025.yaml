id: TS.FS.FR.025
status: draft
sort: 287
user_created: 59722cb8-f858-481b-97f1-c97dd2a8e4be
date_created: '2024-07-08T06:45:49.703Z'
user_updated: abd14398-1d53-4e6f-9834-26328f0ebde0
date_updated: '2025-06-19T04:12:08.609Z'
title: Maximum Overtime Hours Settings
requirement:
  time: 1746609971209
  blocks:
    - id: WrhYWEjSZn
      type: paragraph
      data:
        text: >
          - <PERSON><PERSON> thống cho phép bộ phận nhân sự tập đoàn/CTTV thiết lập thông tin
          làm thêm giờ tối đa để ràng buộc và cảnh báo khi số giờ làm thêm của
          CBNV vượt quá quy định của tập đoàn/ CTTV.
    - id: G77tiu-6EX
      type: paragraph
      data:
        text: >+
          - <PERSON><PERSON> thống kiểm tra thông tin và không cho thiết lập mới nếu thông tin
          thiết lập trùng thông tin về “Thông tin đơn vị/phòng ban”, “Thông tin
          cấp bậc nhân viên” và “Ngày hiệu lực” với các thiết lập trước đó.

  version: 2.30.7
screen_design: null
module: TS
local_fields:
  - code: country
    title: Country
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: group
    title: Group
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: company
    title: Company
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: legalEntity
    title: Legal Entity
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: payGroup
    title: Pay Group
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: employeeGroup
    title: Employee Group
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: careerStream
    title: Career Stream
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: employeeLevel
    title: Employee Level
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: effectiveDate
    title: Effective Start Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
  - code: effectiveDateTo
    title: Effective End Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
  - code: note
    title: Note
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
  - code: updatedBy
    title: Last Updated By
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: updatedAt
    title: Last Updated On
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
mock_data:
  - country: Việt Nam
    group: FPT
    company: FPT IS
    legalEntity: FIS HCM
    payGroup: Chăm sóc khách hàng (CS)
    careerStream: Chăm sóc khách hàng
    employeeLevel: Cấp 2
    employeeGroup: Chăm sóc khách hàng cá nhân
    effectiveDate: 01/01/2024
    note: Test 123
    creator: Phương Bùi
    createTime: 01/01/2024 10:00:02
    lastEditer: Khánh Vy
    lastEditTime: 01/01/2024 10:00:02
  - country: Việt Nam
    group: FPT
    company: FPT IS
    legalEntity: FIS HCM
    payGroup: Chăm sóc khách hàng (CS)
    careerStream: Chăm sóc khách hàng
    employeeLevel: Cấp 2
    employeeGroup: Chăm sóc khách hàng cá nhân
    effectiveDate: 01/01/2024
    note: Test 123
    creator: Phương Bùi
    createTime: 01/01/2024 10:00:02
    lastEditer: Khánh Vy
    lastEditTime: 01/01/2024 10:00:02
  - country: Việt Nam
    group: FPT
    company: FPT IS
    legalEntity: FIS HCM
    payGroup: Chăm sóc khách hàng (CS)
    careerStream: Chăm sóc khách hàng
    employeeLevel: Cấp 2
    employeeGroup: Chăm sóc khách hàng cá nhân
    effectiveDate: 01/01/2024
    note: Test 123
    creator: Phương Bùi
    createTime: 01/01/2024 10:00:02
    lastEditer: Khánh Vy
    lastEditTime: 01/01/2024 10:00:02
  - country: Việt Nam
    group: FPT
    company: FPT IS
    legalEntity: FIS HCM
    payGroup: Chăm sóc khách hàng (CS)
    careerStream: Chăm sóc khách hàng
    employeeLevel: Cấp 2
    employeeGroup: Chăm sóc khách hàng cá nhân
    effectiveDate: 01/01/2024
    note: Test 123
    creator: Phương Bùi
    createTime: 01/01/2024 10:00:02
    lastEditer: Khánh Vy
    lastEditTime: 01/01/2024 10:00:02
  - country: Việt Nam
    group: FPT
    company: FPT IS
    legalEntity: FIS HCM
    payGroup: Chăm sóc khách hàng (CS)
    careerStream: Chăm sóc khách hàng
    employeeLevel: Cấp 2
    employeeGroup: Chăm sóc khách hàng cá nhân
    effectiveDate: 01/01/2024
    note: Test 123
    creator: Phương Bùi
    createTime: 01/01/2024 10:00:02
    lastEditer: Khánh Vy
    lastEditTime: 01/01/2024 10:00:02
  - country: Việt Nam
    group: FPT
    company: FPT IS
    legalEntity: FIS HCM
    payGroup: Chăm sóc khách hàng (CS)
    careerStream: Chăm sóc khách hàng
    employeeLevel: Cấp 2
    employeeGroup: Chăm sóc khách hàng cá nhân
    effectiveDate: 01/01/2024
    note: Test 123
    creator: Phương Bùi
    createTime: 01/01/2024 10:00:02
    lastEditer: Khánh Vy
    lastEditTime: 01/01/2024 10:00:02
  - country: Việt Nam
    group: FPT
    company: FPT IS
    legalEntity: FIS HCM
    payGroup: Chăm sóc khách hàng (CS)
    careerStream: Chăm sóc khách hàng
    employeeLevel: Cấp 2
    employeeGroup: Chăm sóc khách hàng cá nhân
    effectiveDate: 01/01/2024
    note: Test 123
    creator: Phương Bùi
    createTime: 01/01/2024 10:00:02
    lastEditer: Khánh Vy
    lastEditTime: 01/01/2024 10:00:02
  - country: Việt Nam
    group: FPT
    company: FPT IS
    legalEntity: FIS HCM
    payGroup: Chăm sóc khách hàng (CS)
    careerStream: Chăm sóc khách hàng
    employeeLevel: Cấp 2
    employeeGroup: Chăm sóc khách hàng cá nhân
    effectiveDate: 01/01/2024
    note: Test 123
    creator: Phương Bùi
    createTime: 01/01/2024 10:00:02
    lastEditer: Khánh Vy
    lastEditTime: 01/01/2024 10:00:02
  - country: Việt Nam
    group: FPT
    company: FPT IS
    legalEntity: FIS HCM
    payGroup: Chăm sóc khách hàng (CS)
    careerStream: Chăm sóc khách hàng
    employeeLevel: Cấp 2
    employeeGroup: Chăm sóc khách hàng cá nhân
    effectiveDate: 01/01/2024
    note: Test 123
    creator: Phương Bùi
    createTime: 01/01/2024 10:00:02
    lastEditer: Khánh Vy
    lastEditTime: 01/01/2024 10:00:02
local_buttons: null
layout: layout-table
form_config:
  formTitle:
    view: Maximum Overtime Hours Details
    edit: Edit Maximum Overtime Hours
  fields:
    - type: group
      n_cols: 3
      _condition:
        transform: $.extend.formType = 'create' or $.extend.formType = 'edit'
      fields:
        - name: countryId
          label: Country
          type: select
          outputValue: value
          clearFieldsAfterChange:
            - payGroupId
          placeholder: Select Country
          _select:
            transform: $nationsList()
        - name: groupId
          label: Group
          type: select
          outputValue: value
          clearFieldsAfterChange:
            - companyId
            - legalEntityId
          placeholder: Select Group
          _select:
            transform: $.variables._groupsList
        - name: companyId
          label: Company
          type: select
          outputValue: value
          placeholder: Select Company
          clearFieldsAfterChange:
            - legalEntityId
            - payGroupId
          _select:
            transform: $.variables._companiesList
        - name: legalEntityId
          label: Legal Entity
          type: select
          outputValue: value
          placeholder: Select Legal Entity
          _select:
            transform: $.variables._legalEntityList
        - name: payGroupId
          label: Pay Group
          type: select
          outputValue: value
          placeholder: Select Pay Group
          _select:
            transform: $.variables._payGroupsList
        - name: employeeGroupId
          label: Employee Group
          type: select
          outputValue: value
          placeholder: Select Employee Group
          _select:
            transform: $employeeGroupsList()
        - name: careerStreamId
          label: Career Stream
          type: select
          outputValue: value
          placeholder: Select Carrer Stream
          _select:
            transform: $careerStreamsList()
        - name: employeeLevelId
          label: Employee Level
          type: select
          outputValue: value
          placeholder: Select Employee Level
          _select:
            transform: $employeeLevelsList()
        - name: effectiveDate
          type: dateRange
          label: Effective Start Date
          setting:
            format: dd/MM/yyyy
            type: date
          _value:
            transform: $.extend.formType = 'create' ? $now()
          mode: date-picker
          validators:
            - type: required
        - type: dateRange
          name: effectiveDateTo
          label: Effective End Date
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
            autoFill: end-off
            _disabledDate:
              transform: '{''operator'': ''$lt'', ''value'': $.fields.effectiveDate}'
          validators:
            - type: ppx-custom
              args:
                transform: >-
                  $exists($.fields.effectiveDate) and
                  $exists($.fields.effectiveDateTo) and
                  $DateDiff($DateFormat($.fields.effectiveDate, 'yyyy-MM-DD'),
                  $DateFormat($.fields.effectiveDateTo, 'yyyy-MM-DD'), 'd') > 0
              text: Effective end date must be greater than effective start date.
    - type: group
      _condition:
        transform: $.extend.formType = 'create' or $.extend.formType = 'edit'
      fields:
        - name: note
          label: Note
          type: textarea
          validators:
            - type: maxLength
              args: '1000'
              text: Note should not exceed 1000 characters.
          textarea:
            autoSize:
              minRows: 3
            maxCharCount: 1000
          placeholder: Enter Note
    - type: group
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - name: country
          label: Country
          type: text
        - name: group
          label: Group
          type: text
        - name: company
          label: Company
          type: text
        - name: legalEntity
          label: Legal Entity
          type: text
        - name: payGroup
          label: Pay Group
          type: text
        - name: employeeGroup
          label: Employee Group
          type: text
        - name: careerStream
          label: Career Stream
          type: text
        - name: employeeLevelId
          label: Employee Level
          type: select
          outputValue: value
          placeholder: Select Employee Level
          _select:
            transform: $employeeLevelsList()
        - name: effectiveDate
          type: dateRange
          label: Effective Start Date
          setting:
            format: dd/MM/yyyy
            type: date
          mode: date-picker
        - name: effectiveDateTo
          type: dateRange
          label: Effective End Date
          setting:
            format: dd/MM/yyyy
            type: date
          mode: date-picker
        - name: note
          label: Note
          type: text
          placeholder: Enter Note
        - label: Formula
          type: text
          name: functionTkname
    - type: array
      mode: table
      name: tssetPrincipleOfOt_Detail
      arrayOptions:
        canChangeSize: true
        canDragDrop: true
      field:
        type: group
        fields:
          - type: select
            label: Cycle
            name: cycleTest
            outputValue: value
            _class:
              transform: $.extend.formType = 'view' ? 'unrequired'
            width: 250px
            validators:
              - type: ppx-custom
                args:
                  transform: >-
                    $count($queryInArray($.fields.tssetPrincipleOfOt_Detail,
                    {'cycleTest': $.value})) > 1
                text: This Cycle already exists
              - type: required
            select:
              - label: Ngày
                value: '1'
              - label: Tháng
                value: '3'
              - label: Năm
                value: '4'
              - label: Tuần
                value: '2'
          - type: number
            label: Number of Hour
            name: numberOfHours
            _class:
              transform: $.extend.formType = 'view' ? 'unrequired'
            number:
              min: 0
              max: 1000
              suffix: Hours
            validators:
              - type: required
            width: 150px
    - type: group
      fields:
        - label: Formula
          type: select
          name: functionTkid
          outputValue: value
          _select:
            transform: $caFunctionTksList($.fields.effectiveDate)
          validators:
            - type: required
          _condition:
            transform: '$.extend.formType = ''create'' or $.extend.formType = ''edit'' '
  sources:
    nationsList:
      uri: '"/api/picklists/COUNTRIES/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
    groupsList:
      uri: '"/api/groups/get-by"'
      queryTransform: >-
        {'page':1,'limit':10000,'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - effectiveDate
    companiesList:
      uri: '"/api/companies/get-by"'
      queryTransform: >-
        {'page':1,'limit':10000,'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true},{'field':'groupCode','operator': '$eq','value':
        $.groupCode }]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - groupCode
        - effectiveDate
    legalEntityList:
      uri: '"/api/legal-entities/get-list"'
      queryTransform: >-
        {'page':1,'limit':10000,'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true},{'field':'companyCode','operator': '$eq','value':
        $.companyCode }]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - companyCode
        - effectiveDate
    payGroupsList:
      uri: '"/api/pay-group-structures"'
      method: GET
      queryTransform: >-
        {'page':1,'limit':10000,'filter': [{'field':'status','operator':
        '$eq','value':true},{'field':'companyCode','operator': '$eq','value':
        $.companyCode }, {'operator': '$or','value': [{'field':
        'countryCode','operator': '$eq','value': 'NULL'},{'field':
        'countryCode','operator': '$eq','value': $.countryCode}]},
        {'field':'effectiveDate','operator': '$lte','value': $.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - effectiveDate
        - companyCode
        - countryCode
    careerStreamsList:
      uri: '"/api/career-streams/by"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.fields.effectiveDate},{'field':'status','operator':
        '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - effectiveDate
    employeeLevelsList:
      uri: '"/api/picklists/EMPLEVEL/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $string($item.code)}})[]
      disabledCache: true
      params:
        - effectiveDate
    caFunctionTksList:
      uri: '"/api/ca-function-tks"'
      method: GET
      queryTransform: '{''filter'': [{''field'':''status'',''operator'': ''$eq'',''value'':true}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default, 'value':
        $string($item.code)}})[]
      disabledCache: true
      params:
        - effectiveDate
    employeeGroupsList:
      uri: '"/api/employee-groups"'
      method: GET
      queryTransform: '{''filter'': [{''field'':''status'',''operator'': ''$eq'',''value'':true}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.employeeGroup.default & '
        (' & $item.code & ')', 'value': $string($item.code)}})[]
      disabledCache: true
  variables:
    _countriesList:
      transform: $nationsList()
    _groupsList:
      transform: $groupsList($.fields.effectiveDate)
    _companiesList:
      transform: >-
        $.fields.groupId ?
        $companiesList($.fields.groupId,$.fields.effectiveDate)
    _legalEntityList:
      transform: >-
        $.fields.companyId ?
        $legalEntityList($.fields.companyId,$.fields.effectiveDate)
    _payGroupsList:
      transform: >-
        $payGroupsList($.fields.effectiveDate, $.fields.companyId,
        $.fields.countryId)
  formSize:
    create: middle
    edit: middle
    view: middle
    proceed: middle
  footer:
    create: true
    update: true
    createdOn: createdAt
    updatedOn: updatedAt
    createdBy: createdBy
    updatedBy: updatedBy
filter_config:
  fields:
    - name: countryId
      label: Country
      mode: multiple
      type: selectAll
      placeholder: Select Country
      labelType: type-grid
      isLazyLoad: true
      _options:
        transform: $nationsList($.extend.limit, $.extend.page, $.extend.search)
    - name: groupId
      label: Group
      type: selectAll
      mode: multiple
      isLazyLoad: true
      placeholder: Enter Group
      labelType: type-grid
      _options:
        transform: $groupsList($.extend.limit, $.extend.page, $.extend.search)
    - name: companyId
      label: Company
      type: selectAll
      mode: multiple
      isLazyLoad: true
      placeholder: Enter company
      labelType: type-grid
      _options:
        transform: $companiesList($.extend.limit, $.extend.page, $.extend.search)
    - name: legalEntityId
      label: Legal Entity
      type: selectAll
      mode: multiple
      isLazyLoad: true
      placeholder: Enter Legal Entity
      labelType: type-grid
      _options:
        transform: $legalEntitiesList($.extend.limit, $.extend.page, $.extend.search)
    - name: payGroupId
      label: Pay Group
      type: selectAll
      mode: multiple
      isLazyLoad: true
      placeholder: Enter Pay Group
      labelType: type-grid
      _options:
        transform: $payGroupsList($.extend.limit, $.extend.page, $.extend.search)
    - name: careerStreamId
      label: Career Stream
      type: selectAll
      mode: multiple
      isLazyLoad: true
      placeholder: Enter Career Stream
      labelType: type-grid
      _options:
        transform: $careerStreamsList($.extend.limit, $.extend.page, $.extend.search)
    - name: employeeLevelId
      label: Employee Level
      type: selectAll
      mode: multiple
      placeholder: Enter Employee Level
      labelType: type-grid
      isLazyLoad: true
      _options:
        transform: $employeeLevelsList($.extend.limit, $.extend.page, $.extend.search)
    - name: employeeGroupId
      label: Employee Group
      type: selectAll
      mode: multiple
      isLazyLoad: true
      placeholder: Enter Employee Group
      labelType: type-grid
      _options:
        transform: $employeeGroupsList($.extend.limit, $.extend.page, $.extend.search)
    - type: dateRange
      label: Effective Date
      labelType: type-grid
      name: effectiveDate
    - name: createdBy
      label: Created By
      type: selectAll
      placeholder: Select Created By
      labelType: type-grid
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $userList()
    - type: dateRange
      label: Created On
      labelType: type-grid
      name: createdAt
    - name: updatedBy
      label: Last Updated By
      type: selectAll
      placeholder: Select Last Updated By
      mode: multiple
      labelType: type-grid
      isLazyLoad: true
      _options:
        transform: $userList()
    - type: dateRange
      label: Last Updated On
      labelType: type-grid
      name: updatedAt
  filterMapping:
    - field: countryId
      operator: $in
      valueField: countryId.(value)
    - field: groupId
      operator: $in
      valueField: groupId.(value)
    - field: companyId
      operator: $in
      valueField: companyId.(value)
    - field: legalEntityId
      operator: $in
      valueField: legalEntityId.(value)
    - field: payGroupId
      operator: $in
      valueField: payGroupId.(value)
    - field: careerStreamId
      operator: $in
      valueField: careerStreamId.(value)
    - field: employeeLevelId
      operator: $in
      valueField: employeeLevelId.(value)
    - field: employeeGroupId
      operator: $in
      valueField: employeeGroupId.(value)
    - field: effectiveDate
      operator: $between
      valueField: effectiveDate
    - field: createdBy
      operator: $in
      valueField: createdBy.(value)
    - field: createdAt
      operator: $between
      valueField: createdAt
    - field: updatedBy
      operator: $in
      valueField: updatedBy.(value)
    - field: updatedAt
      operator: $between
      valueField: updatedAt
  sources:
    nationsList:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default  & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    groupsList:
      uri: '"/api/groups"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default  & ' (' &
        $item.code & ')', 'value': $item.code , 'id': $item.id}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    companiesList:
      uri: '"/api/companies"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default  & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    legalEntitiesList:
      uri: '"/api/legal-entities"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})
      disabledCache: true
      params:
        - limit
        - page
        - search
    payGroupsList:
      uri: '"/api/pay-group-structures"'
      method: GET
      queryTransform: '{''limit'': $.limit, ''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    careerStreamsList:
      uri: '"/api/career-streams"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})
      disabledCache: true
      params:
        - limit
        - page
        - search
    employeeLevelsList:
      uri: '"/api/picklists/EMPLEVEL/values/pagination"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $string($item.code)}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    employeeGroupsList:
      uri: '"/api/employee-groups"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.employeeGroup.default & '
        (' & $item.code & ')', 'value': $item.code}})
      disabledCache: true
      params:
        - limit
        - page
        - search
    userList:
      uri: '"/api/internal-users"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.userName, 'value':
        $item.userName}})
      disabledCache: true
layout_options:
  toolTable:
    export: true
    adjustDisplay: true
  tool_table:
    - id: export
      icon: icon-download-simple
  show_dialog_form_save_add_button: true
  show_detail_history: false
  export_all:
    type: base_total
  hide_action_row: true
  show_dialog_duplicate_button: false
  delete_multi_items: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    title: edit
    icon: icon-pencil-simple
    type: ghost-gray
  - id: delete
    title: Delete
    icon: icon-trash
    type: ghost-gray
backend_url: /api/ts-set-principle-of-ots
screen_name: ts-set-principle-of-ots
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: countryId
    defaultName: CountryCode
  - name: companyId
    defaultName: CompanyCode
  - name: legalEntityId
    defaultName: LegalEntityCode
  - name: payGroupId
    defaultName: PayGroupCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Maximum Overtime Hours Settings
  parent:
    title: Leave Fund Regulations
