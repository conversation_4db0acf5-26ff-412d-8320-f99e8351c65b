id: TS.FS.FR.026
status: draft
sort: 317
user_created: 59722cb8-f858-481b-97f1-c97dd2a8e4be
date_created: '2024-07-09T07:08:47.311Z'
user_updated: abd14398-1d53-4e6f-9834-26328f0ebde0
date_updated: '2025-07-28T03:15:15.472Z'
title: Overtime Rate Settings
requirement:
  time: 1746766393840
  blocks:
    - id: hrkdM-bXJG
      type: paragraph
      data:
        text: >
          - <PERSON><PERSON> thống cho phép bộ phận nhân sự tập đoàn/CTTV thiết lập hệ số làm
          thêm giờ để làm căn cứ tính lương làm thêm giờ cho CBNV theo quy định
          của Tập đoàn/ CTTV.
    - id: z208Nq6PxF
      type: paragraph
      data:
        text: >+
          - <PERSON><PERSON> thống kiểm tra thông tin và không cho phép tạo mới/ chỉnh sửa nếu
          thông tin thiết lập trùng thông tin về “Thông tin đơn vị/phòng ban” và
          “Ngày hiệu lực” với các thiết lập trước đó.

  version: 2.30.7
screen_design: null
module: TS
local_fields:
  - code: country
    title: Country
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: group
    title: Group
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: company
    title: Company
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: legalEntity
    title: Legal Entity
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: payGroup
    title: Pay Group
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: effectiveDate
    title: Effective Start Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
  - code: effectiveDateTo
    title: Effective End Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
  - code: note
    title: Note
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
  - code: lastEditTime
    title: Last Updated On
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
  - code: lastEditer
    title: Last Updated By
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
mock_data:
  - country: Việt Nam
    group: FPT
    company: FPT IS
    legalEntity: FIS HCM
    payGroup: Chăm sóc khách hàng (CS)
    effectiveDate: 01/01/2024
    note: Áp dụng chung
    creator: Phương Bùi
    createTime: 01/04/2024 10:00:02
    lastEditer: Khánh Vy
    lastEditTime: 01/04/2024 10:00:02
  - country: Việt Nam
    group: FPT
    company: FPT IS
    legalEntity: FIS HCM
    payGroup: Chăm sóc khách hàng (CS)
    effectiveDate: 01/01/2024
    note: Áp dụng chung
    creator: Phương Bùi
    createTime: 01/04/2024 10:00:02
    lastEditer: Khánh Vy
    lastEditTime: 01/04/2024 10:00:02
  - country: Việt Nam
    group: FPT
    company: FPT IS
    legalEntity: FIS HCM
    payGroup: Chăm sóc khách hàng (CS)
    effectiveDate: 01/01/2024
    note: Áp dụng chung
    creator: Phương Bùi
    createTime: 01/04/2024 10:00:02
    lastEditer: Khánh Vy
    lastEditTime: 01/04/2024 10:00:02
  - country: Việt Nam
    group: FPT
    company: FPT IS
    legalEntity: FIS HCM
    payGroup: Chăm sóc khách hàng (CS)
    effectiveDate: 01/01/2024
    note: Áp dụng chung
    creator: Phương Bùi
    createTime: 01/04/2024 10:00:02
    lastEditer: Khánh Vy
    lastEditTime: 01/04/2024 10:00:02
  - country: Việt Nam
    group: FPT
    company: FPT IS
    legalEntity: FIS HCM
    payGroup: Chăm sóc khách hàng (CS)
    effectiveDate: 01/01/2024
    note: Áp dụng chung
    creator: Phương Bùi
    createTime: 01/04/2024 10:00:02
    lastEditer: Khánh Vy
    lastEditTime: 01/04/2024 10:00:02
  - country: Việt Nam
    group: FPT
    company: FPT IS
    legalEntity: FIS HCM
    payGroup: Chăm sóc khách hàng (CS)
    effectiveDate: 01/01/2024
    note: Áp dụng chung
    creator: Phương Bùi
    createTime: 01/04/2024 10:00:02
    lastEditer: Khánh Vy
    lastEditTime: 01/04/2024 10:00:02
  - country: Việt Nam
    group: FPT
    company: FPT IS
    legalEntity: FIS HCM
    payGroup: Chăm sóc khách hàng (CS)
    effectiveDate: 01/01/2024
    note: Áp dụng chung
    creator: Phương Bùi
    createTime: 01/04/2024 10:00:02
    lastEditer: Khánh Vy
    lastEditTime: 01/04/2024 10:00:02
  - country: Việt Nam
    group: FPT
    company: FPT IS
    legalEntity: FIS HCM
    payGroup: Chăm sóc khách hàng (CS)
    effectiveDate: 01/01/2024
    note: Áp dụng chung
    creator: Phương Bùi
    createTime: 01/04/2024 10:00:02
    lastEditer: Khánh Vy
    lastEditTime: 01/04/2024 10:00:02
local_buttons: null
layout: layout-table
form_config:
  formSize:
    create: large
    edit: large
    view: middle
    proceed: large
  fields:
    - type: group
      n_cols: 1
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - name: country
          label: Country
          type: text
        - name: group
          label: Group
          type: text
        - name: company
          label: Company
          type: text
        - name: legalEntity
          label: Legal Entity
          type: text
        - name: payGroup
          label: Pay Group
          type: text
        - name: effectiveDate
          type: dateRange
          label: Effective Start Date
          placeholder: dd/MM/yyyy
          setting:
            format: dd/MM/yyyy
            type: date
          mode: date-picker
        - name: effectiveDateTo
          type: dateRange
          label: Effective End Date
          placeholder: dd/MM/yyyy
          setting:
            format: dd/MM/yyyy
            type: date
          mode: date-picker
    - type: group
      n_cols: 2
      _condition:
        transform: $.extend.formType != 'view'
      fields:
        - name: countryId
          label: Country
          type: select
          outputValue: value
          placeholder: Select Country
          _select:
            transform: $countriesList()
        - name: groupId
          label: Group
          type: select
          outputValue: value
          clearFieldsAfterChange:
            - companyId
            - legalEntityId
          placeholder: Select Group
          _select:
            transform: $.variables._groupsList
        - name: companyId
          label: Company
          type: select
          clearFieldsAfterChange:
            - legalEntityId
            - payGroupId
          placeholder: Select Company
          outputValue: value
          _select:
            transform: $.variables._companiesList
        - name: legalEntityId
          label: Legal Entity
          type: select
          placeholder: Select Legal Entity
          outputValue: value
          _select:
            transform: $.variables._legalEntityList
        - name: payGroupId
          label: Pay Group
          type: select
          outputValue: value
          placeholder: Select Pay Group
          actions:
            - view
          _select:
            transform: $payGroupsList($.fields.effectiveDate,$.fields.companyId)
        - name: effectiveDate
          type: dateRange
          label: Effective Start Date
          placeholder: dd/MM/yyyy
          setting:
            format: dd/MM/yyyy
            type: date
          _value:
            transform: $.extend.formType = 'create' ?  $now()
          mode: date-picker
          validators:
            - type: required
        - type: dateRange
          name: effectiveDateTo
          label: Effective End Date
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
            autoFill: end-off
            _disabledDate:
              transform: '{''operator'': ''$lt'', ''value'': $.fields.effectiveDate}'
          validators:
            - type: ppx-custom
              args:
                transform: >-
                  $exists($.fields.effectiveDate) and
                  $exists($.fields.effectiveDateTo) and
                  $DateDiff($DateFormat($.fields.effectiveDate, 'yyyy-MM-DD'),
                  $DateFormat($.fields.effectiveDateTo, 'yyyy-MM-DD'), 'd') > 0
              text: Effective end date must be greater than effective start date.
    - type: group
      fields:
        - type: textarea
          name: note
          label: Note
          placeholder: Enter note
          textarea:
            maxCharCount: 1000
            autoSize:
              minRows: 3
          validators:
            - type: maxLength
              args: '1000'
              text: Maximum length is 1000 characters.
    - type: group
      label: Set the coefficient
      collapse: false
      fields:
        - name: ratingLst
          type: array
          mode: table
          _size:
            transform: $.extend.formType = 'create' ? 1
          arrayOptions:
            canChangeSize: true
            canDragDrop: true
          field:
            type: group
            fields:
              - name: cycle
                type: select
                label: Overtime Type
                outputValue: value
                width: 180px
                align: end
                _class:
                  transform: $.extend.formType = 'view' ? 'unrequired'
                validators:
                  - type: ppx-custom
                    args:
                      transform: >-
                        $count($queryInArray($.fields.ratingLst, {'cycle':
                        $.value}))>1
                    text: This Type overtime already exists
                  - type: required
                _select:
                  transform: $.variables._otTypesList
              - label: Non-taxable coefficient
                type: number
                name: NonTaxableCoefficient
                width: 220px
                align: end
                placeholder: Enter Number
                prefix: e343
                _class:
                  transform: $.extend.formType = 'view' ? 'unrequired'
                validators:
                  - type: required
                  - type: max
                    args: 9999.99
                _condition:
                  transform: $.extend.formType != 'view'
                number:
                  max: 9999.99
                  precision: 2
              - label: Non-taxable coefficient
                type: number
                name: NonTaxableCoefficient
                width: 220px
                align: end
                positionContentTable: right
                prefix: e343
                _condition:
                  transform: $.extend.formType = 'view'
              - label: Taxable coefficient
                type: number
                name: taxableCoefficient
                width: 185px
                align: end
                placeholder: Enter Number
                prefix: e343
                _class:
                  transform: $.extend.formType = 'view' ? 'unrequired'
                validators:
                  - type: required
                  - type: max
                    args: 9999.99
                _condition:
                  transform: $.extend.formType != 'view'
                number:
                  max: 9999.99
                  precision: 2
              - label: Taxable coefficient
                type: number
                name: taxableCoefficient
                width: 185px
                align: end
                positionContentTable: right
                prefix: e343
                _class:
                  transform: $.extend.formType = 'view' ? 'unrequired'
                validators:
                  - type: required
                _condition:
                  transform: $.extend.formType = 'view'
              - label: Additional coefficient
                type: number
                name: additionalCoefficient
                width: 185px
                align: end
                placeholder: Enter Number
                prefix: e343
                _condition:
                  transform: $.extend.formType != 'view'
                validators:
                  - type: max
                    args: 9999.99
                number:
                  max: 9999.99
                  precision: 2
              - label: Additional coefficient
                type: number
                name: additionalCoefficient
                width: 185px
                align: end
                positionContentTable: right
                prefix: e343
                _condition:
                  transform: $.extend.formType = 'view'
  sources:
    countriesList:
      uri: '"/api/picklists/COUNTRIES/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
    groupsList:
      uri: '"/api/groups/get-by"'
      queryTransform: >-
        {'page':1,'limit':10000,'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - effectiveDate
    companiesList:
      uri: '"/api/companies/get-by"'
      queryTransform: >-
        {'page':1,'limit':10000,'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true},{'field':'groupCode','operator': '$eq','value':
        $.groupCode }]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - groupCode
        - effectiveDate
    legalEntityList:
      uri: '"/api/legal-entities/get-list"'
      queryTransform: >-
        {'page':1,'limit':10000,'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true},{'field':'companyCode','operator': '$eq','value':
        $.companyCode }]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - companyCode
        - effectiveDate
    payGroupsList:
      uri: '"/api/pay-group-structures"'
      method: GET
      queryTransform: >-
        {'page':1,'limit':10000,'filter': [{'field':'effectiveDate','operator':
        '$lte','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true},{'field':'companyCode','operator':
        '$eq','value':$.companyCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - effectiveDate
        - companyCode
    otTypesList:
      uri: '"/api/picklists/TYPEOVERTIME/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
  variables:
    _groupsList:
      transform: $groupsList($.fields.effectiveDate)
    _companiesList:
      transform: >-
        $.fields.groupId ?
        $companiesList($.fields.groupId,$.fields.effectiveDate)
    _legalEntityList:
      transform: >-
        $.fields.companyId ?
        $legalEntityList($.fields.companyId,$.fields.effectiveDate)
    _otTypesList:
      transform: $otTypesList()
  footer:
    create: true
    update: true
    createdOn: createTime
    updatedOn: lastEditTime
    createdBy: creator
    updatedBy: lastEditer
filter_config:
  fields:
    - name: country
      label: Country
      type: selectAll
      mode: multiple
      placeholder: Select Country
      isLazyLoad: true
      _options:
        transform: $countriesList($.extend.limit, $.extend.page, $.extend.search)
      labelType: type-row
    - name: group
      label: Group
      type: selectAll
      mode: multiple
      placeholder: Select Group
      isLazyLoad: true
      _options:
        transform: $groupsList($.extend.limit, $.extend.page, $.extend.search)
      labelType: type-row
    - name: company
      label: Company
      type: selectAll
      mode: multiple
      placeholder: Select Company
      isLazyLoad: true
      _options:
        transform: $companiesList($.extend.limit, $.extend.page, $.extend.search)
      labelType: type-row
    - name: legalEntity
      label: Legal Entity
      type: selectAll
      mode: multiple
      placeholder: Select Legal Entity
      isLazyLoad: true
      _options:
        transform: $legalEntitiesList($.extend.limit, $.extend.page, $.extend.search)
      labelType: type-row
    - name: payGroup
      label: Pay Group
      type: selectAll
      mode: multiple
      placeholder: Select Pay Group
      isLazyLoad: true
      _options:
        transform: $payGroupsList($.extend.limit, $.extend.page, $.extend.search)
      labelType: type-row
    - type: dateRange
      label: Effective Date
      name: effectiveDate
      labelType: type-row
    - name: creator
      label: Created By
      type: selectAll
      placeholder: Select Created By
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $userList()
      labelType: type-row
    - type: dateRange
      label: Created On
      name: createTime
      labelType: type-row
    - name: lastEditer
      label: Last Updated By
      type: selectAll
      placeholder: Select Editor
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $userList()
      labelType: type-row
    - type: dateRange
      label: Last Updated On
      name: lastEditTime
      labelType: type-row
  filterMapping:
    - field: countryId
      operator: $in
      valueField: country.(value)
    - field: groupId
      operator: $in
      valueField: group.(value)
    - field: companyId
      operator: $in
      valueField: company.(value)
    - field: legalEntityId
      operator: $in
      valueField: legalEntity.(value)
    - field: payGroupId
      operator: $in
      valueField: payGroup.(value)
    - field: effectiveDate
      operator: $between
      valueField: effectiveDate
    - field: creator
      operator: $in
      valueField: creator.(value)
    - field: createTime
      operator: $between
      valueField: createTime
    - field: lastEditer
      operator: $in
      valueField: lastEditer.(value)
    - field: lastEditTime
      operator: $between
      valueField: lastEditTime
  sources:
    countriesList:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    groupsList:
      uri: '"/api/groups/get-by"'
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    companiesList:
      uri: '"/api/companies/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    legalEntitiesList:
      uri: '"/api/legal-entities/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})
      disabledCache: true
      params:
        - limit
        - page
        - search
    payGroupsList:
      uri: '"/api/pay-group-structures"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    userList:
      uri: '"/api/internal-users"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.userName, 'value':
        $item.userName}})
      disabledCache: true
layout_options:
  tool_table:
    - id: export
      icon: icon-download-simple
  show_dialog_form_save_add_button: true
  show_detail_history: false
  export_all:
    type: base_total
  hide_action_row: true
  show_dialog_duplicate_button: false
  delete_multi_items: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    title: Edit
    icon: icon-pencil-simple
  - id: delete
    title: Delete
    icon: icon-trash
backend_url: /api/ts-set-ot-factors
screen_name: ts-set-ot-factors
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: countryId
    defaultName: CountryCode
  - name: companyId
    defaultName: CompanyCode
  - name: legalEntityId
    defaultName: LegalEntityCode
  - name: payGroupId
    defaultName: PayGroupCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Overtime Rate Settings
  parent:
    title: Manage work schedule
