id: TS.FS.FR.027
status: draft
sort: 311
user_created: 59722cb8-f858-481b-97f1-c97dd2a8e4be
date_created: '2024-07-12T02:25:23.295Z'
user_updated: abd14398-1d53-4e6f-9834-26328f0ebde0
date_updated: '2025-06-19T06:34:59.512Z'
title: Monthly Overtime Management
requirement:
  time: 1720751120575
  blocks:
    - id: kSMiszUtlU
      type: paragraph
      data:
        text: >-
          - H<PERSON> thống cho phép bộ phận nhân sự tập đoàn/CTTV thiết lập thông tin
          làm thêm giờ của CBNV của tập đoàn/ CTTV.
    - id: DXtBg9gBAq
      type: paragraph
      data:
        text: >+

          - <PERSON><PERSON> thống kiểm tra thông tin và không cho thiết lập mới nếu thông tin
          thiết lập trùng thông tin về“Thông tin nhân viên” và “<PERSON>ỳ lương” với
          các thiết lập trước đó.

  version: 2.29.1
screen_design: null
module: TS
local_fields:
  - code: employeeCode
    title: Employee ID
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    show_sort: true
    pinned: true
  - code: employeeRecordNumber
    title: Employee Record Number (ERN)
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 16
    options__tabular__align: right
  - code: employeeName
    title: Employee name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: company
    title: Company
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: legalEntity
    title: Legal Entity
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: businessUnit
    title: Business Unit
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: division
    title: Division
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: department
    title: Department
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: jobTitleName
    title: Job title
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    group: null
    options__tabular__column_width: 12
  - code: startDate
    title: Start date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
  - code: endDate
    title: End date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
  - code: totalOtHours
    title: Total OT Hours
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__align: center
    show_sort: false
  - code: paymentDate
    title: Payment Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
  - code: note
    title: Note
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    options__tabular__column_width: 12
  - code: updatedBy
    title: Last Updated By
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: updatedAt
    title: Last Updated On
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
mock_data:
  - code: '00123456'
    name: Bùi Phương
    record: 1
    contractType: Hợp đồng XĐTH 12 tháng
    group: Nhân viên chính thức
    jobTitle: Cán bộ kiểm thử phần mềm
    jobIndicator: Việc chính
    periodSalary: Tháng 1
    note: Áp dụng chung
    creator: Phuong Bui
    CreateTime: 01/04/2024 10:00:02
    lastEditer: Khanh Vy
    LastEditTime: 01/04/2024 10:00:02
  - code: '00123457'
    name: Bùi Phương
    record: 1
    contractType: Hợp đồng XĐTH 12 tháng
    group: Nhân viên chính thức
    jobTitle: Cán bộ kiểm thử phần mềm
    jobIndicator: Việc chính
    periodSalary: Tháng 1
    note: Áp dụng chung
    creator: Phuong Bui
    CreateTime: 01/04/2024 10:00:02
    lastEditer: Khanh Vy
    LastEditTime: 01/04/2024 10:00:02
  - code: '00123458'
    name: Bùi Phương
    record: 1
    contractType: Hợp đồng XĐTH 12 tháng
    group: Nhân viên chính thức
    jobTitle: Cán bộ kiểm thử phần mềm
    jobIndicator: Việc chính
    periodSalary: Tháng 1
    note: Áp dụng chung
    creator: Phuong Bui
    CreateTime: 01/04/2024 10:00:02
    lastEditer: Khanh Vy
    LastEditTime: 01/04/2024 10:00:02
  - code: '00123459'
    name: Bùi Phương
    record: 1
    contractType: Hợp đồng XĐTH 12 tháng
    group: Nhân viên chính thức
    jobTitle: Cán bộ kiểm thử phần mềm
    jobIndicator: Việc chính
    periodSalary: Tháng 1
    note: Áp dụng chung
    creator: Phuong Bui
    CreateTime: 01/04/2024 10:00:02
    lastEditer: Khanh Vy
    LastEditTime: 01/04/2024 10:00:02
  - code: '00123450'
    name: Bùi Phương
    record: 1
    contractType: Hợp đồng XĐTH 12 tháng
    group: Nhân viên chính thức
    jobTitle: Cán bộ kiểm thử phần mềm
    jobIndicator: Việc chính
    periodSalary: Tháng 1
    note: Áp dụng chung
    creator: Phuong Bui
    CreateTime: 01/04/2024 10:00:02
    lastEditer: Khanh Vy
    LastEditTime: 01/04/2024 10:00:02
  - code: '00123451'
    name: Bùi Phương
    record: 1
    contractType: Hợp đồng XĐTH 12 tháng
    group: Nhân viên chính thức
    jobTitle: Cán bộ kiểm thử phần mềm
    jobIndicator: Việc chính
    periodSalary: Tháng 1
    note: Áp dụng chung
    creator: Phuong Bui
    CreateTime: 01/04/2024 10:00:02
    lastEditer: Khanh Vy
    LastEditTime: 01/04/2024 10:00:02
  - code: '00123453'
    name: Bùi Phương
    record: 1
    contractType: Hợp đồng XĐTH 12 tháng
    group: Nhân viên chính thức
    jobTitle: Cán bộ kiểm thử phần mềm
    jobIndicator: Việc chính
    periodSalary: Tháng 1
    note: Áp dụng chung
    creator: Phuong Bui
    CreateTime: 01/04/2024 10:00:02
    lastEditer: Khanh Vy
    LastEditTime: 01/04/2024 10:00:02
local_buttons: null
layout: layout-table
form_config:
  fields:
    - type: group
      fields:
        - type: select
          name: employee
          label: Employee
          placeholder: Select Employee
          isLazyLoad: true
          validators:
            - type: required
          _select:
            transform: $employeesList($.extend.limit, $.extend.page, $.extend.search)
          _condition:
            transform: $.extend.formType = 'create'
          outputValue: value
        - type: text
          label: Employee
          disabled: true
          _value:
            transform: >-
              $.extend.defaultValue.employeeCode & ' - ' &
              $.extend.defaultValue.employeeRecordNumber & ' - ' &
              $.extend.defaultValue.employeeName
          _condition:
            transform: $.extend.formType = 'edit'
        - type: text
          name: employeeCode
          label: Employee
          unvisible: true
          _value:
            transform: $.fields.employee.employeeId
        - type: number
          name: employeeRecordNumber
          unvisible: true
          _value:
            transform: $string($.fields.employee.employeeRecordNumber)
        - type: number
          name: employeeName
          unvisible: true
          _value:
            transform: $.fields.employee.employeeName
        - type: text
          name: employeeIdObj
          unvisible: true
          _value:
            transform: $.variables._employeeIdObj
    - type: group
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - type: text
          label: Employee ID
          name: employeeCode
        - type: text
          label: Employee Name
          name: employeeName
        - type: text
          label: Employee Record Number (ERN)
          name: employeeRecordNumber
        - type: text
          label: Company
          name: company
        - type: text
          label: Legal Entity
          name: legalEntity
        - type: text
          label: Business Unit
          name: businessUnit
        - type: text
          label: Division
          name: division
        - type: text
          label: Department
          name: department
        - type: text
          label: Job title
          name: jobTitleName
        - name: startDate
          label: Start Date
          type: dateRange
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
        - name: endDate
          label: End Date
          type: dateRange
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
        - name: paymentDate
          label: Payment Date
          type: dateRange
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
    - type: group
      n_cols: 3
      _condition:
        transform: '$.extend.formType = ''create'' or $.extend.formType = ''edit'' '
      fields:
        - name: startDate
          label: Start Date
          type: dateRange
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
          validators:
            - type: required
        - name: endDate
          label: End Date
          type: dateRange
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
          _value:
            transform: >-
              ($not($isNilorEmpty($.fields.startDate)) ? ($output :=
              $CalDate($.fields.startDate, 1, 'M');
              $DateFormat($.fields.startDate, 'DD') = '01' ?
              $CalDate($output,-1,'d') : $output))
            skip:
              condition: $not($isNilorEmpty($.extend.defaultValue.endDate))
              count: 2
          validators:
            - type: required
            - type: ppx-custom
              args:
                transform: $DateDiff($.fields.endDate, $.fields.startDate, 'd') < 0
              text: The End Date must be great than or equal to the Start Date.
            - type: ppx-custom
              args:
                transform: >-
                  $DateDiff($.fields.endDate, $.fields.startDate, 'M', false) >
                  1
              text: The period must not exceed 1 month.
        - name: paymentDate
          label: Payment Date
          type: dateRange
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
    - type: group
      fields:
        - type: group
          fields:
            - name: note
              label: Note
              type: textarea
              placeholder: Enter note
              validators:
                - type: maxLength
                  args: '1000'
                  text: Note should not exceed 1000 characters.
              textarea:
                autoSize:
                  minRows: 3
                maxCharCount: 1000
        - type: group
          fields:
            - type: array
              name: tsotInput_Detail
              label: Manage Total Overtime Hours
              _description:
                transform: >-
                  'Total OT Hours: ' &
                  ($round($sum($getFieldGroup($.extend.path, $.fields,
                  1).tsotInput_Detail.otHours),2) > 0
                  ?$round($sum($getFieldGroup($.extend.path, $.fields,
                  1).tsotInput_Detail.otHours),2) : 0 ) & ' hours'
              mode: table
              _size:
                transform: $.extend.formType = 'create' ? 1
              validators:
                - type: required
              arrayOptions:
                canChangeSize: true
                canDragDrop: true
              field:
                type: group
                fields:
                  - type: text
                    name: id
                    unvisible: true
                  - type: select
                    name: otType
                    label: Overtime Type
                    placeholder: Select Overtime Type
                    _select:
                      transform: $typeOvertimeList()
                    _class:
                      transform: $.extend.formType = 'view' ? 'unrequired'
                    validators:
                      - type: ppx-custom
                        args:
                          transform: >-
                            $count($queryInArray($.fields.tsotInput_Detail,
                            {'otType': $.value}))>1
                        text: This Overtime type already exists
                      - type: required
                    outputValue: value
                  - type: number
                    name: otHours
                    label: OT hours
                    placeholder: Enter Number
                    number:
                      suffix: Hours
                    _class:
                      transform: $.extend.formType = 'view' ? 'unrequired'
                    validators:
                      - type: required
  overview:
    dependentField: employeeIdObj
    title: Employee Detail
    border: true
    uri: >-
      /api/personals/:{employeeId}:/job-datas?filter[0]=employeeRecordNumber||$eq||:{employeeRecordNumber}:
    display:
      - label: Company
        key: companyName
      - label: Legal Entity
        key: legalEntityName
      - label: Business Unit
        key: businessUnitName
      - label: Division
        key: divisionName
      - label: Department
        key: departmentName
      - label: Job title
        key: jobName
  footer:
    create: true
    update: true
    createdOn: createdAt
    updatedOn: updatedAt
    createdBy: createdBy
    updatedBy: updatedBy
  sources:
    typeOvertimeList:
      uri: '"/api/picklists/TYPEOVERTIME/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
    employeesList:
      uri: '"/api/personals"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'filter':
        [{'field':'search','operator': '$eq','value':$.search}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label':
        $join($filter([$item.employeeId,$string($item.employeeRecordNumber),$item.fullName],
        $boolean), ' - ')  , 'value': {'employeeId': $item.employeeId,
        'employeeRecordNumber': $string($item.employeeRecordNumber),
        'employeeName': $item.fullName}, 'employeeId': $item.employeeId,
        'employeeRecordNumber': $string($item.employeeRecordNumber),
        'employeeName': $item.fullName}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
  variables:
    _employeeIdObj:
      transform: >-
        $.fields.employeeCode != '' ? {'employeeId': $.fields.employeeCode,
        'employeeRecordNumber': $.fields.employeeRecordNumber, 'employeeName':
        $.fields.employeeName}
filter_config:
  fields:
    - name: companyCode
      label: Company
      type: selectAll
      mode: multiple
      labelType: type-grid
      placeholder: Select Company
      isLazyLoad: true
      _options:
        transform: $companiesList($.extend.limit, $.extend.page, $.extend.search)
    - name: legalEntityCode
      label: Legal Entity
      type: selectAll
      mode: multiple
      labelType: type-grid
      placeholder: Select Legal Entity
      isLazyLoad: true
      _options:
        transform: $legalEntitiesList($.extend.limit, $.extend.page, $.extend.search)
    - name: businessUnitCode
      label: Business Unit
      type: selectAll
      mode: multiple
      labelType: type-grid
      placeholder: Select Business Unit
      isLazyLoad: true
      _options:
        transform: $businessUnitsList($.extend.limit, $.extend.page, $.extend.search)
    - name: divisionCode
      label: Division
      type: selectAll
      mode: multiple
      labelType: type-grid
      placeholder: Select Division
      isLazyLoad: true
      _options:
        transform: $divisionsList($.extend.limit, $.extend.page, $.extend.search)
    - name: departmentCode
      label: Department
      type: selectAll
      mode: multiple
      labelType: type-grid
      placeholder: Select Department
      isLazyLoad: true
      _options:
        transform: $departmentsList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      name: employee
      label: Employee
      placeholder: Select Employee
      isLazyLoad: true
      labelType: type-grid
      mode: multiple
      _options:
        transform: $employeesList($.extend.limit, $.extend.page, $.extend.search)
    - name: jobTitleCode
      label: Job Title
      type: selectAll
      labelType: type-grid
      placeholder: Select Job Title
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $jobCodesList($.extend.limit, $.extend.page, $.extend.search)
    - type: dateRange
      name: startDate
      label: Start Date
      mode: date-picker
      labelType: type-grid
      placeholder: dd/MM/yyyy
      setting:
        format: dd/MM/yyyy
        type: date
    - type: dateRange
      name: endDate
      label: End Date
      labelType: type-grid
      placeholder: dd/MM/yyyy
      mode: date-picker
      setting:
        format: dd/MM/yyyy
        type: date
    - type: dateRange
      name: paymentDate
      label: Payment Date
      labelType: type-grid
      placeholder: dd/MM/yyyy
      setting:
        format: dd/MM/yyyy
        type: date
    - name: createdBy
      label: Created By
      type: selectAll
      mode: multitple
      labelType: type-grid
      placeholder: Select Created By
      isLazyLoad: true
      _options:
        transform: $userList()
    - type: dateRange
      name: createdAt
      label: Created On
      labelType: type-grid
    - name: updatedBy
      label: Last Updated By
      type: select
      mode: multitple
      labelType: type-grid
      placeholder: Select Last Updated By
      isLazyLoad: true
      _options:
        transform: $userList()
    - type: dateRange
      name: updatedAt
      labelType: type-grid
      label: Last Updated On
  filterMapping:
    - field: groupCode
      operator: $in
      valueField: groupCode.(value)
    - field: companyCode
      operator: $in
      valueField: companyCode.(value)
    - field: legalEntityCode
      operator: $in
      valueField: legalEntityCode.(value)
    - field: businessUnitCode
      operator: $in
      valueField: businessUnitCode.(value)
    - field: divisionCode
      operator: $in
      valueField: divisionCode.(value)
    - field: departmentCode
      operator: $in
      valueField: departmentCode.(value)
    - field: $
      operator: $in
      valueField: employee.(value)
    - field: jobTitleCode
      operator: $in
      valueField: jobTitleCode.(value)
    - field: startDate
      operator: $eq
      valueField: startDate
    - field: endDate
      operator: $eq
      valueField: endDate
    - field: paymentDate
      operator: $between
      valueField: paymentDate
    - field: createdBy
      operator: $in
      valueField: createdBy.(value)
    - field: createdAt
      operator: $between
      valueField: createdAt
    - field: updatedBy
      operator: $in
      valueField: updatedBy.(value)
    - field: updatedAt
      operator: $between
      valueField: updatedAt
  sources:
    companiesList:
      uri: '"/api/companies/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    legalEntitiesList:
      uri: '"/api/legal-entities/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})
      disabledCache: true
      params:
        - limit
        - page
        - search
    businessUnitsList:
      uri: '"/api/business-units/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})
      disabledCache: true
      params:
        - limit
        - page
        - search
    divisionsList:
      uri: '"/api/divisions/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})
      disabledCache: true
      params:
        - limit
        - page
        - search
    departmentsList:
      uri: '"/api/departments/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})
      disabledCache: true
      params:
        - limit
        - page
        - search
    employeesList:
      uri: '"/api/personals"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.employeeId &' - '&
        $item.employeeRecordNumber &' - '& $item.name,'code': $item.employeeId,
        'value': {'employeeCode': $item.employeeId,'employeeRecordNumber':
        $item.employeeRecordNumber}}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    jobCodesList:
      uri: '"/api/job-codes"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    userList:
      uri: '"/api/internal-users"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.userName, 'value':
        $item.userName}})[]
      disabledCache: true
layout_options:
  show_dialog_form_save_add_button: true
  show_detail_history: false
  toolTable:
    export: true
    import: true
    adjustDisplay: true
  tool_table:
    - id: import
      icon: icon-upload-simple
      paramsRedirect:
        type: TS_OBJECT
        entityOrObj: TSOTInput
    - id: export
      icon: icon-download-simple
  export_all:
    type: base_total
  hide_action_row: true
  show_dialog_duplicate_button: false
  delete_multi_items: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    icon: icon-pencil-simple
    type: ghost-gray
  - id: delete
    icon: icon-trash
    type: ghost-gray
backend_url: /api/ts-ot-inputs
screen_name: ts-ot-inputs
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: companyCode
    defaultName: CompanyCode
  - name: departmentCode
    defaultName: DepartmentCode
  - name: employeeCode
    defaultName: EmployeeId
  - name: employeeRecordNumber
    defaultName: EmployeeRecordNumber
  - name: jobTitleCode
    defaultName: JobCode
  - name: legalEntityCode
    defaultName: LegalEntityCode
  - name: businessUnitCode
    defaultName: BusinessUnitCode
  - name: divisionCode
    defaultName: DivisionCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Manage Total Overtime Hours In The Period
  parent:
    title: Leave Fund Regulations
