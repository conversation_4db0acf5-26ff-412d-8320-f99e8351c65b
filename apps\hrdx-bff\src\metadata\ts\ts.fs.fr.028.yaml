id: TS.FS.FR.028
status: draft
sort: 134
user_created: 8ad48761-02ef-4f69-bef6-ac3a6f107610
date_created: '2024-08-09T03:38:09.680Z'
user_updated: abd14398-1d53-4e6f-9834-26328f0ebde0
date_updated: '2025-07-25T10:24:34.679Z'
title: Leave Request Management
requirement:
  time: 1746670979700
  blocks:
    - id: Xoo-oiSs0V
      type: paragraph
      data:
        text: >-
          - Chức năng cho phép bộ phận nhân sự tập đoàn thiết lập các quy định
          cho từng loại nghỉ tương ứng theo quy định của tập đoàn/ CTTV
    - id: hlb6az2zka
      type: paragraph
      data:
        text: >-
          - <PERSON><PERSON> thống kiểm tra thông tin và không cho phép tạo mới/ chỉnh sửa nếu
          thông tin thiết lập trùng thông tin về “Loại ngày nghỉ” và “<PERSON><PERSON><PERSON> hiệ<PERSON>
          lự<PERSON>” với các thiết lập trước đ<PERSON>. 
    - id: HxaDbKi1YE
      type: paragraph
      data:
        text: >-
          - Hệ thống chỉ cho phép sửa/ xóa dữ liệu loại nghỉ khi dữ liệu đó chưa
          được sử dụng trên hệ thống. 
    - id: 1JHDJW6-ZQ
      type: paragraph
      data:
        text: >-
          - Hệ thống cho phép nhập dữ liệu hàng loạt (import) thông tin thiết
          lập quy định các loại nghỉ. 
    - id: JH6MyITX28
      type: paragraph
      data:
        text: >-
          - Khi CBNV đăng ký nghỉ, hệ thống kiểm tra các điều kiện ràng buộc về
          các giới hạn số ngày tối đa/tối thiểu được đăng ký theo lần/tháng/năm
          được thiết lập để cảnh báo và xử lý theo ràng buộc tương ứng chi tiết
          mô tả tại chức năng TS.FR.021 – CBNV tạo đơn nghỉ cá nhân 
  version: 2.30.7
screen_design: null
module: TS
local_fields:
  - code: employeeId
    title: Employee ID
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    default_display: true
    pinned: true
    show_sort: true
    options__tabular__column_width: 8.75
  - code: employeeRecord
    title: Employee Record Number (ERN)
    data_type:
      key: Number
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    default_display: true
    group: null
    options__tabular__column_width: 15.625
    options__tabular__align: right
  - code: employeeName
    title: Employee Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    default_display: true
    options__tabular__column_width: 9
  - code: catypeOfDayOffName
    pinned: false
    title: Absence Type
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    options__tabular__column_width: 8.75
  - code: leaveUnitName
    title: Leave Unit
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    default_display: true
    options__tabular__column_width: 8.75
  - code: startDate
    title: Start Date
    data_type:
      key: dd/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    default_display: true
    options__tabular__column_width: 8.75
  - code: endDate
    title: End Date
    data_type:
      key: dd/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    default_display: true
    options__tabular__column_width: 8.75
  - code: groupName
    title: Group
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    default_display: true
    options__tabular__column_width: 6.25
  - code: companyName
    title: Company
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    default_display: true
    options__tabular__column_width: 6.25
  - code: legalEntityName
    title: Legal Entity
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    default_display: true
    options__tabular__column_width: 7
  - code: businessUnitName
    title: Business Unit
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    default_display: true
    options__tabular__column_width: 7.8125
  - code: divisionName
    title: Division
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    default_display: true
    options__tabular__column_width: 8.75
  - code: departmentName
    title: Department
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    default_display: true
    options__tabular__column_width: 8.75
  - code: leaveReason
    title: Leave Reason
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    default_display: true
    options__tabular__column_width: 8.25
  - code: leaveRequestStatusName
    title: Leave Request Status
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    default_display: true
    options__tabular__column_width: 11.1875
  - code: dataSourceName
    title: Data Source
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    default_display: true
    options__tabular__column_width: 8.25
  - code: lastUpdatedBy
    title: Last Updated By
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    options__tabular__column_width: 9
  - code: lastUpdatedOn
    title: Last Updated On
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
    default_display: true
    options__tabular__column_width: 10.75
mock_data:
  formSize:
    create: largex
    edit: largex
    view: large
  fields:
    - type: group
      label: Employee Infomation
      collapse: false
      fieldBackground: rgba(192, 203, 216, 0.12)
      padding: 8px 12px
      borderRadius: 8px
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - type: text
          label: Employee Id
          name: employeeCode
        - type: text
          label: Employee Record
          name: employeeRecord
        - type: text
          label: Employee Name
          name: employeeName
        - type: text
          label: Company
          name: companyName
        - type: text
          label: Legal Entity
          name: legalEntityName
        - type: text
          label: Business Unit
          name: businessUnitName
        - type: text
          label: Division
          name: divisionName
        - type: text
          label: Department
          name: departmentName
    - type: group
      label: Absence Detail
      collapse: false
      fieldBackground: rgba(192, 203, 216, 0.12)
      padding: 8px 12px
      borderRadius: 8px
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - type: radio
          name: leaveUnit
          label: Leave Unit
          radio:
            - label: Days
              value: '1'
            - label: Hours
              value: '2'
        - type: text
          label: Leave Reason
          name: leaveReason
        - type: translationTextArea
          label: Note
          name: note
        - type: text
          label: Leave Request Status
          name: leaveRequestStatus
        - type: number
          label: Hours Off
          name: hoursOff
          _condition:
            transform: $.fields.leaveUnit = '2'
          number:
            precision: 0
            suffix: Hours
        - type: number
          label: Days Off
          name: daysOff
          _condition:
            transform: $.fields.leaveUnit = '1'
          number:
            precision: 0
            suffix: Days
    - type: group
      label: Absence Type Details
      collapse: false
      fieldBackground: rgba(192, 203, 216, 0.12)
      padding: 8px 12px
      borderRadius: 8px
      name: tsapplicationForLeaveTypes
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - type: text
          label: Absence Types
          name: cAtypeOfDayOffName
        - name: startDate
          label: Start Date
          type: dateRange
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
        - name: endDate
          label: End Date
          type: dateRange
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
        - type: array
          mode: table
          label: Absence Types Detail
          name: tsapplicationForLeaveDetails
          fieldBackground: rgba(192, 203, 216, 0.12)
          padding: 8px 12px
          borderRadius: 8px
          arrayOptions:
            canChangeSize: true
          field:
            type: group
            name: ''
            fields:
              - type: dateRange
                name: leaveDate
                label: Leave Date
                mode: date-picker
                setting:
                  type: day
                  format: dd/MM/yyyy
              - type: text
                name: dateDetails
                label: Date Details
                readOnly: true
              - type: timePicker
                name: fromTime
                label: From Time
                _condition:
                  transform: $.fields.leaveUnit = '2'
                _disabled:
                  transform: >-
                    $getFieldGroup($.extend.path, $.fields, 1).dateDetails =
                    'OFF'
              - type: timePicker
                name: endTime
                label: End Time
                _condition:
                  transform: $.fields.leaveUnit = '2'
                _disabled:
                  transform: >-
                    $getFieldGroup($.extend.path, $.fields, 1).dateDetails =
                    'OFF'
              - type: select
                name: leavePeriodDetail
                label: Leave Period
                _disabled:
                  transform: >-
                    $getFieldGroup($.extend.path, $.fields, 1).dateDetails =
                    'OFF'
                _condition:
                  transform: $.fields.leaveUnit = '1'
                select:
                  - label: Nghỉ cả ngày
                    value: '1'
                  - label: Nghỉ đầu ca
                    value: '2'
                  - label: Nghỉ cuối ca
                    value: '3'
                  - label: Khác
                    value: '4'
              - type: text
                name: daysOff003
                label: Days Off
                _condition:
                  transform: $.fields.leaveUnit = 1
                _value:
                  transform: >-
                    ($leavePeriod := $getFieldGroup($.extend.path, $.fields,
                    1).leavePeriodDetail.value;  $leavePeriod = '1' ? 1 : 

                    $leavePeriod = '2' ? 0.5 : 

                    $leavePeriod = '3' ? 0.5: 0;)
                disabled: true
              - type: text
                name: hoursOff003
                label: Hours Off
                _condition:
                  transform: $.fields.leaveUnit = '2'
                disabled: true
    - type: select
      label: Employee
      name: employee
      outputValue: value
      validators:
        - type: required
      _select:
        transform: $.variables._employeesList
      _condition:
        transform: $.extend.formType = 'create'
    - type: text
      name: employeeCode
      label: 'Employee '
      _unvisible:
        transform: $.extend.formType = 'create' or $.extend.formType = 'view'
      _value:
        transform: $.fields.employee.employeeId
    - type: text
      name: employeeRecord
      unvisible: true
      label: Employee Record Number
      value: '0'
    - type: text
      label: Data Source
      name: dataSource
      outputValue: value
      unvisible: true
      value: '1'
    - type: translationTextArea
      name: note
      label: Note
      _condition:
        transform: $.extend.formType = 'create' or $.extend.formType = 'edit'
      placeholder: Enter note
      textarea:
        autoSize:
          minRows: 3
    - type: group
      label: Absence Types Information
      collapse: false
      n_cols: 2
      _condition:
        transform: $.extend.formType = 'create' or $.extend.formType = 'edit'
      fields:
        - type: radio
          name: leaveUnit
          label: Leave Unit
          value: '1'
          radio:
            - label: Days
              value: '1'
            - label: Hours
              value: '2'
          validators:
            - type: required
        - name: hoursOff
          type: number
          label: Hours Off
          disabled: true
          _condition:
            transform: >-
              ($.extend.formType = 'create' or $.extend.formType = 'edit') and
              $.fields.leaveUnit = '2'
          _value:
            transform: >-
              $reduce($.fields.tsapplicationForLeaveTypes, function($i, $j) { $i
              + $j.hoursOff002}, 0)
          number:
            precision: 0
            suffix: Hours
        - name: daysOff
          type: number
          label: Days Off
          readOnly: true
          _value:
            transform: >-
              $reduce($.fields.tsapplicationForLeaveTypes, function($i, $j) {
              $j.leavePeriod = '4' ? $i +
              $reduce($j.tsapplicationForLeaveDetails, function($ic,$jc) {$ic +
              ($jc.daysOff003 ? $jc.daysOff003 : 0)},0) : $i + $j.daysOff002},
              0)
          _condition:
            transform: >-
              ($.extend.formType = 'create' or $.extend.formType = 'edit') and
              $.fields.leaveUnit = '1'
          number:
            precision: 0
            suffix: Days
    - type: group
      collapse: false
      n_cols: 2
      _condition:
        transform: $.extend.formType = 'create' or $.extend.formType = 'edit'
      fields:
        - name: leaveRequestStatus
          type: select
          label: Leave Request Status
          outputValue: value
          _select:
            transform: $.variables._LeaveRequestStatusList
        - name: leaveReason
          type: text
          label: Leave Reason
          validators:
            - type: required
          placeholder: Enter Leave Reason
    - type: array
      name: tsapplicationForLeaveTypes
      arrayOptions:
        canChangeSize: true
      _condition:
        transform: $.extend.formType = 'create' or $.extend.formType = 'edit'
      field:
        type: group
        label: Absence Types
        collapse: false
        fieldBackground: rgba(192, 203, 216, 0.12)
        padding: 8px 12px
        borderRadius: 8px
        fields:
          - type: group
            n_cols: 2
            fields:
              - name: absenceTypes
                label: Absence Types
                type: select
                placeholder: Select Absence Types
                outputValue: value
                _condition:
                  transform: $.fields.leaveUnit = '1'
                select:
                  - label: Nghỉ phép
                    value: '12'
                  - label: Nghỉ không lương
                    value: '0.5'
                  - label: Nghỉ ốm
                    value: '3'
                  - label: Nghỉ cá nhân
                    value: '1.5'
                _value:
                  transform: '$.extend.formType = ''edit'' ? ''12'' '
              - name: absenceTypes
                label: Absence Types
                type: select
                placeholder: Select Absence Types
                outputValue: value
                _condition:
                  transform: $.fields.leaveUnit = '2'
                select:
                  - label: Nghỉ công tác
                    value: '12'
                  - label: Nghỉ phụ nữ
                    value: '4'
                _value:
                  transform: '$.extend.formType = ''edit'' ? ''12'' '
              - name: leaveBalance
                label: Leave Balance
                type: text
                _value:
                  transform: >-
                    ($absenceTypes := $getFieldGroup($.extend.path, $.fields,
                    1).absenceTypes;  $absenceTypes = '4' ? 4 : 

                    $absenceTypes = '1.5' ? 1.5 : 

                    $absenceTypes = '3' ? 3 : $absenceTypes = '12' ? 12 :
                    $absenceTypes = '0.5' ? 0.5 : 0;)
                disabled: true
                hyperlink:
                  _condition:
                    transform: >-
                      ($absenceTypes := $getFieldGroup($.extend.path, $.fields,
                      1).absenceTypes; $absenceTypes != '12' ? true : false)
                  hyperlinkText: Leave balance details
                  hyperlinkTitle: View Leave Balance Details
                  fields:
                    - type: array
                      mode: table
                      name: table1
                      arrayOptions:
                        canChangeSize: false
                        canAddItem: false
                      size: 1
                      value:
                        - vacationType: Nghỉ không lương
                          expiryDate: 31/12/2024
                          leaveBalance: 30
                          leavesTake: 10
                          leavesPendingApproval: 10
                          remaningDays: 10
                      field:
                        type: group
                        name: vacationFund
                        fields:
                          - type: text
                            readOnly: true
                            label: Vacation type
                            name: vacationType
                          - type: text
                            readOnly: true
                            label: Expiry date
                            name: expiryDate
                          - type: text
                            readOnly: true
                            label: Leave balance
                            name: leaveBalance
                          - type: text
                            readOnly: true
                            label: Leaves taken
                            name: leavesTake
                          - type: text
                            readOnly: true
                            label: Leaves pending approval
                            name: leavesPendingApproval
                            width: 250px
                          - type: text
                            readOnly: true
                            label: Remaining days
                            name: remaningDays
          - type: group
            fieldBackground: rgba(230, 242, 255, 1)
            padding: 12px
            borderRadius: 8px
            name: ' '
            _condition:
              transform: >-
                ($absenceTypes := $getFieldGroup($.extend.path, $.fields,
                1).absenceTypes; $absenceTypes = '12' ? true : false)
            fields:
              - type: text
                readOnly: true
                value: 'Nghỉ phép:'
              - type: text
                readOnly: true
                value: '- Nghỉ 1 ngày: Yêu cầu tạo đơn nghỉ trước 1 ngày'
              - type: text
                readOnly: true
                value: '- Nghỉ 3 - 7 ngày: Yêu cầu tạo đơn nghỉ trước 3 ngày'
          - type: group
            fieldBackground: rgba(230, 242, 255, 1)
            padding: 12px
            name: ' '
            borderRadius: 8px
            _condition:
              transform: >-
                ($absenceTypes := $getFieldGroup($.extend.path, $.fields,
                1).absenceTypes; $absenceTypes = '0.5' ? true : false)
            fields:
              - type: text
                readOnly: true
                value: 'Nghỉ không lương:'
              - type: text
                readOnly: true
                value: '- Nghỉ 1 ngày: Yêu cầu tạo đơn nghỉ trước 1 ngày'
              - type: text
                readOnly: true
                value: '- Nghỉ 3 - 7 ngày: Yêu cầu tạo đơn nghỉ trước 3 ngày'
          - type: group
            collapse: false
            n_cols: 3
            fields:
              - type: group
                n_cols: 2
                fields:
                  - name: startDate
                    label: Start Date
                    type: dateRange
                    mode: date-picker
                    setting:
                      format: dd/MM/yyyy
                      type: date
                    validators:
                      - type: required
                  - name: endDate
                    label: End Date
                    type: dateRange
                    mode: date-picker
                    setting:
                      format: dd/MM/yyyy
                      type: date
                    validators:
                      - type: required
              - name: leavePeriod
                label: Leave Period
                type: select
                outputValue: value
                placeholder: Select Leave Period
                _condition:
                  transform: $.fields.leaveUnit = '1'
                select:
                  - label: Nghỉ cả ngày
                    value: '1'
                  - label: Nghỉ đầu ca
                    value: '2'
                  - label: Nghỉ cuối ca
                    value: '3'
                  - label: Khác
                    value: '4'
                validators:
                  - type: required
              - name: daysOff002
                type: number
                label: Days Off
                _condition:
                  transform: >-
                    ($.extend.formType = 'create' or $.extend.formType = 'edit')
                    and $.fields.leaveUnit = '1'
                _value:
                  transform: >-
                    ($leavePeriod := $getFieldGroup($.extend.path, $.fields,
                    1).leavePeriod;$startDate := $getFieldGroup($.extend.path,
                    $.fields, 1).startDate; $endDate :=
                    $getFieldGroup($.extend.path, $.fields, 1).endDate; $days :=
                    $DateDiff($endDate, $startDate, 'd'); $leavePeriod = '1' ?
                    1+$days : (

                    $leavePeriod = '2' or 

                    $leavePeriod = '3') ? 0.5*($days+1) : 0;)
                number:
                  precision: 1
                  suffix: Days
                _disabled:
                  transform: >-
                    ($.extend.formType = 'create' or $.extend.formType = 'edit')
                    and $.fields.leaveUnit = '1'
              - type: group
                n_cols: 2
                fields:
                  - type: timePicker
                    name: fromTime
                    label: From Time
                    outputValue: value
                    _condition:
                      transform: $.fields.leaveUnit = '2'
                  - type: timePicker
                    name: endTime
                    label: End Time
                    outputValue: value
                    _condition:
                      transform: $.fields.leaveUnit = '2'
              - name: hoursOff002
                type: number
                label: Hours Off
                disabled: true
                _condition:
                  transform: $.fields.leaveUnit = '2'
                _value:
                  transform: >-
                    ($fromTime := $getFieldGroup($.extend.path, $.fields,
                    1).fromTime; $endTime := $getFieldGroup($.extend.path,
                    $.fields, 1).endTime; $hours := $DateDiff($endTime,
                    $fromTime, 'h'); $hours)
                number:
                  precision: 0
                  suffix: Hours
          - type: array
            mode: table
            label: Absence Types Detail
            name: tsapplicationForLeaveDetails
            fieldBackground: rgba(192, 203, 216, 0.12)
            padding: 8px 12px
            _value:
              transform: >-
                ($startDate := $getFieldGroup($.extend.path, $.fields,
                1).startDate; $endDate := $getFieldGroup($.extend.path,
                $.fields, 1).endDate; $days := $DateDiff($endDate, $startDate,
                'd');$map([0..$days], function($item){{'leaveDate':
                $CalDate($.$startDate,$item,'d')}}))
            borderRadius: 8px
            arrayOptions:
              canChangeSize: true
            field:
              type: group
              name: ''
              fields:
                - type: dateRange
                  name: leaveDate
                  label: Leave Date
                  mode: date-picker
                  setting:
                    type: day
                    format: dd/MM/yyyy
                - type: text
                  name: dateDetails
                  label: Date Details
                  readOnly: true
                - type: timePicker
                  name: fromTime
                  label: From Time
                  _condition:
                    transform: $.fields.leaveUnit = '2'
                  _disabled:
                    transform: >-
                      $getFieldGroup($.extend.path, $.fields, 1).dateDetails =
                      'OFF'
                - type: timePicker
                  name: endTime
                  label: End Time
                  _condition:
                    transform: $.fields.leaveUnit = '2'
                  _disabled:
                    transform: >-
                      $getFieldGroup($.extend.path, $.fields, 1).dateDetails =
                      'OFF'
                - type: select
                  name: leavePeriodDetail
                  label: Leave Period
                  _disabled:
                    transform: >-
                      $getFieldGroup($.extend.path, $.fields, 1).dateDetails =
                      'OFF'
                  _condition:
                    transform: $.fields.leaveUnit = '1'
                  select:
                    - label: Nghỉ cả ngày
                      value: '1'
                    - label: Nghỉ đầu ca
                      value: '2'
                    - label: Nghỉ cuối ca
                      value: '3'
                    - label: Khác
                      value: '4'
                  validators:
                    - type: required
                - type: text
                  name: daysOff003
                  label: Days Off
                  _condition:
                    transform: $.fields.leaveUnit = '1'
                  _value:
                    transform: >-
                      ($leavePeriod := $getFieldGroup($.extend.path, $.fields,
                      1).leavePeriodDetail.value;  $leavePeriod = '1' ? 1 : 

                      $leavePeriod = '2' ? 0.5 : 

                      $leavePeriod = '3' ? 0.5: 0;)
                  disabled: true
                - type: text
                  name: hoursOff003
                  label: Hours Off
                  _condition:
                    transform: $.fields.leaveUnit = '2'
                  disabled: true
  overview:
    dependentField: employee
    title: Employee Detail
    _condition:
      transform: $.extend.formType = 'create'or $.extend.formType = 'edit'
    uri: /api/personals/:{employeeId}:/job-datas
    display:
      - key: groupName
        label: Group
      - key: companyName
        label: Company
      - key: legalEntityName
        label: Legal Entity
      - key: businessUnitName
        label: Bussiness Unit
      - key: divisionName
        label: Division
      - key: departmentName
        label: Department
  sources:
    leaveStatusList:
      uri: '"/api/picklists/LEAVEREQUESTSTATUS/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
    employeesList:
      uri: '"/api/personals/job-datas/personal-ern-listemployeeid-info"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.employeeId &' - '&
        $item.employeeRecordNumber &' - '& $item.fullName, 'value':
        {'employeeId': $item.employeeId, 'employeeRecordNumber':
        $item.employeeRecordNumber, 'jobDataId': $item.jobDataId },
        'employeeId': $item.employeeId, 'employeeRecordNumber':
        $item.employeeRecordNumber}})[]
      disabledCache: true
    employeeNameList:
      uri: '"/api/ts-list-application-for-leaves"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.employeeName, 'value':
        $item.employeeName}})[]
      disabledCache: true
    absenceTypeList:
      uri: '"/api/ca-type-of-days-offs"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
  variables:
    _employeesList:
      transform: $employeesList()
    _absenceTypeList:
      transform: $absenceTypeList()
    _LeaveRequestStatusList:
      transform: $leaveStatusList()
local_buttons: null
layout: layout-table
form_config:
  formSize:
    create: largex
    edit: largex
    view: middle
  fields:
    - type: group
      label: Employee Infomation
      collapse: false
      borderBottom: true
      fieldGroupContentStyle:
        padding: 8px 8px 0 8px
      fieldGroupTitleStyle:
        padding: 8px
        border: none
        paddingTop: 0px
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - type: text
          label: Employee Id
          name: employeeCode
          unvisible: true
        - type: text
          label: Employee Record
          name: employeeRecord
          unvisible: true
        - type: text
          label: Employee Name
          name: employeeName
          unvisible: true
        - type: text
          label: Employee
          name: employee
          _value:
            transform: >-
              $.fields.employeeCode & ' - ' & $.fields.employeeRecord & ' - ' &
              $.fields.employeeName
        - type: text
          label: Group
          name: groupName
        - type: text
          label: Company
          name: companyName
        - type: text
          label: Legal Entity
          name: legalEntityName
        - type: text
          label: Business Unit
          name: businessUnitName
        - type: text
          label: Division
          name: divisionName
        - type: text
          label: Department
          name: departmentName
        - type: text
          label: Job Title
          name: jobName
    - type: group
      label: Absence Detail
      collapse: false
      borderBottom: true
      fieldGroupContentStyle:
        padding: 8px 8px 0 8px
      fieldGroupTitleStyle:
        padding: 8px
        border: none
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - name: leaveUnit
          type: text
          unvisible: true
        - name: leaveRequestStatus
          type: text
          label: Leave Request Status
          unvisible: true
        - name: leaveRequestStatus1
          type: text
          displaySetting:
            type: Tag
            _value:
              transform: >-
                $test($filter($.variables._LeaveRequestStatusList, function($v,
                $i) {$v.value = $.fields.leaveRequestStatus}).label)
            extraConfig:
              tags:
                - value: Approval Proces
                  style:
                    background_color: '#E6F2FF'
                - value: Awaiting Final Approval
                  style:
                    background_color: '#E6F2FF'
                - value: Saved
                  style:
                    background_color: '#FFE8E5'
                - value: Cancelled
                  style:
                    background_color: '#FFE8E5'
                - value: Submitted
                  style:
                    background_color: '#F1F3F5'
                - value: Approved
                  class: success
          label: Leave Request Status
        - type: text
          label: System Note
          name: systemNote
          _value:
            transform: >-
              ($absenceTypes :=
              $.fields.tsapplicationForLeaveTypes[0].absenceTypes; $absenceTypes
              ? $getAbsenceTypeByCode($absenceTypes).note.default : '')
            dependants:
              - $.fields.tsapplicationForLeaveTypes[0].absenceTypes
            params:
              $index: '0'
    - type: array
      name: tsapplicationForLeaveTypes
      arrayOptions:
        canChangeSize: true
      _condition:
        transform: $.extend.formType = 'view'
      field:
        type: group
        collapse: false
        fieldGroupContentStyle:
          padding: 8px
        fieldGroupTitleStyle:
          padding: 8px
          border: none
        label: Absence Type Details
        fields:
          - type: radio
            name: leaveUnit
            label: Leave Unit
            radio:
              - label: Days
                value: '1'
              - label: Hours
                value: '2'
          - type: text
            label: Leave Reason
            name: leaveReason
          - name: absenceTypes
            label: Absence Types
            type: select
            placeholder: Select Absence Types
            outputValue: value
            _select:
              transform: $absenceTypeList()
          - name: startDate
            label: Start Date
            type: dateRange
            mode: date-picker
            setting:
              format: dd/MM/yyyy
              type: date
          - name: endDate
            label: End Date
            type: dateRange
            mode: date-picker
            setting:
              format: dd/MM/yyyy
              type: date
          - type: timePicker
            name: fromTime
            label: From Time
            setting:
              format: HH:mm
            _condition:
              transform: $.fields.leaveUnit = '2'
          - type: timePicker
            name: endTime
            label: End Time
            setting:
              format: HH:mm
            _condition:
              transform: $.fields.leaveUnit = '2'
          - type: select
            label: Leave Period
            name: leavePeriod
            _condition:
              transform: $.fields.leaveUnit = '1'
            _select:
              transform: $.variables._leavePeriodList
    - type: group
      borderBottom: true
      _condition:
        transform: $.extend.formType = 'create' or $.extend.formType = 'edit'
      fields:
        - type: group
          fields:
            - type: select
              label: Employee
              name: employee
              outputValue: value
              isLazyLoad: true
              validators:
                - type: required
              _select:
                transform: $employeesList($.extend.limit, $.extend.page, $.extend.search)
              _condition:
                transform: $.extend.formType = 'create'
            - type: text
              name: employeeCode
              label: Employee
              unvisible: true
              _value:
                transform: $.fields.employee.employeeId
            - type: number
              name: employeeRecord
              unvisible: true
              _value:
                transform: $string($.fields.employee.employeeRecordNumber)
            - type: number
              name: employeeName
              unvisible: true
            - type: text
              label: Employee
              name: employeeFullName
              disabled: true
              _value:
                transform: >-
                  $.fields.employeeCode & ' - ' & $.fields.employeeRecord & ' -
                  ' & $.extend.defaultValue.employeeName
              _condition:
                transform: $.extend.formType = 'edit'
            - type: text
              name: employeeCode
              label: Employee
              unvisible: true
              _value:
                transform: $.fields.employee.employeeId
            - type: number
              name: employeeRecord
              unvisible: true
              _value:
                transform: $string($.fields.employee.employeeRecordNumber)
            - type: text
              label: Data Source
              name: dataSource
              outputValue: value
              unvisible: true
              value: '1'
            - type: text
              name: employeeInfor
              unvisible: true
              _value:
                transform: >-
                  $.variables._employeeInfor ? $.variables._employeeInfor :
                  '_setValueNull'
            - name: leaveRequestStatus
              type: select
              label: Leave Request Status
              outputValue: value
              _select:
                transform: $.variables._LeaveRequestStatusList
              value: LRS_001
              validators:
                - type: required
    - type: group
      label: Absence Types Information
      _condition:
        transform: $.extend.formType = 'create'
      fields:
        - type: array
          name: tsapplicationForLeaveTypes
          arrayOptions:
            canChangeSize: true
            add_btn_style: full-width
            add_btn_size: large
            add_label: Add more Absence Type
          minSize: 1
          field:
            type: group
            _label:
              transform: '''Absence Types '' & ($number($.extend.path[-1]) + 1)'
            collapse: false
            _disableEventCollapse:
              transform: 'false'
            fieldGroupContentStyle:
              padding: 20px
              paddingBottom: 0px
            fieldGroupTitleStyle:
              padding: 12px 20px
            gap: 16px
            borderRadius: 8px
            border: '1px solid #DFE3E8'
            borderBottomLabel: '1px solid #DFE3E8'
            n_cols: 2
            fields:
              - type: radio
                name: leaveUnit
                label: Leave Unit
                value: '1'
                col: 2
                radio:
                  - label: Days
                    value: '1'
                  - label: Hours
                    value: '2'
                validators:
                  - type: required
              - name: leaveReason
                type: text
                label: Leave Reason
                col: 2
                validators:
                  - type: required
                placeholder: Enter Leave Reason
              - name: absenceTypes
                label: Absence Types
                type: select
                placeholder: Select Absence Types
                dependantFieldSkip: 2
                outputValue: value
                isLazyLoad: true
                _select:
                  transform: >-
                    $absenceTypeList($.extend.limit, $.extend.page,
                    $.extend.search)
                validators:
                  - type: required
              - type: text
                label: dayOffType
                unvisible: true
                name: dayOffType
                _value:
                  transform: >-
                    ($absenceTypes := $getFieldGroup($.extend.path, $.fields,
                    1).absenceTypes; $absenceTypes ?
                    $getAbsenceTypeByCode($absenceTypes) : {})
                  dependants:
                    - $.fields.tsapplicationForLeaveTypes[$index].absenceTypes
                  params:
                    $index: $.extend.path[-1]
              - type: text
                unvisible: true
                label: Vacation type
                name: leaveBalanceDetails
                _value:
                  transform: >-
                    ($employeeCode := $.fields.employeeCode; $employeeRecord :=
                    $.fields.employeeRecord; $startDate :=
                    $getFieldGroup($.extend.path, $.fields, 1).startDate;
                    $endDate := $getFieldGroup($.extend.path, $.fields,
                    1).endDate; $absenceTypes := $getFieldGroup($.extend.path,
                    $.fields, 1).absenceTypes; $leaveUnit :=
                    $getFieldGroup($.extend.path, $.fields, 1).leaveUnit;
                    ($not($isNilorEmpty($employeeCode)) and
                    $not($isNilorEmpty($employeeRecord)) and
                    $not($isNilorEmpty($absenceTypes)) and
                    $not($isNilorEmpty($leaveUnit)))  ? ($ouput :=
                    $dayDetailDayOff($employeeCode,$employeeRecord,$absenceTypes,$startDate,$endDate,
                    $leaveUnit); $ouput))
                  dependants:
                    - $.fields.tsapplicationForLeaveTypes[$index].absenceTypes
                    - $.fields.tsapplicationForLeaveTypes[$index].leaveUnit
                    - $.fields.employeeCode
                    - $.fields.employeeRecord
                    - $.fields.tsapplicationForLeaveTypes[$index].startDate
                    - $.fields.tsapplicationForLeaveTypes[$index].endDate
                  params:
                    $index: $.extend.path[-1]
              - type: number
                label: index
                name: indexLevel1
                _value:
                  transform: ($index := $.extend.path[-2]; $index)
                unvisible: true
              - name: leaveBalance
                label: Leave Balance
                type: number
                _value:
                  transform: >-
                    ($leaveBalanceDetails := $getFieldGroup($.extend.path,
                    $.fields, 1).leaveBalanceDetails;
                    $sum($leaveBalanceDetails.remainDayOffs.remainingVacationFund))
                number:
                  precision: 0
                  _suffix:
                    transform: >-
                      $getFieldGroup($.extend.path, $.fields, 1).leaveUnit = '1'
                      ? 'Days' : 'Hours'
                    dependants:
                      - $.fields.tsapplicationForLeaveTypes[$index].leaveUnit
                    params:
                      $index: $.extend.path[-1]
                disabled: true
                hyperlink:
                  _condition:
                    transform: >-
                      ($absenceTypes := $getFieldGroup($.extend.path, $.fields,
                      1).absenceTypes; $absenceTypes ? true : false)
                    dependants:
                      - $.fields.tsapplicationForLeaveTypes[$index].absenceTypes
                    params:
                      $index: $.extend.path[-1]
                  _value:
                    transform: >-
                      ($index := 0;$leaveUnit := $getFieldGroup($.extend.path,
                      $.fields, 1).leaveUnit;$absenceTypes :=
                      $.fields.tsapplicationForLeaveTypes[$index].absenceTypes;$leaveBalanceDetails
                      :=
                      $.fields.tsapplicationForLeaveTypes[$index].leaveBalanceDetails;
                      $map($leaveBalanceDetails, function($item)
                      {{'leaveUnit':$leaveUnit,'absenceTypes':$absenceTypes,'numberStandard':
                      $item.numberStandard,'expiryDate': 
                      $DateFormat($item.fromDate, 'DD/MM/YYYY') & ' to ' &
                      $DateFormat($item.toDate, 'DD/MM/YYYY'), 'remainDayOffs':
                      $item.remainDayOffs}}))
                  hyperlinkText: Leave balance details
                  hyperlinkTitle: See details of vacation fund
                  fields:
                    - type: text
                      readOnly: true
                      label: leaveUnit
                      name: leaveUnit
                      unvisible: true
                    - type: text
                      readOnly: true
                      label: Absence Types
                      name: absenceTypes
                      unvisible: true
                    - type: text
                      readOnly: true
                      label: Standard Leave
                      name: numberStandard
                      _unvisible:
                        transform: $not($.fields.absenceTypes = '253242')
                    - type: text
                      readOnly: true
                      label: Expiry date
                      name: expiryDate
                      _unvisible:
                        transform: $not($.fields.absenceTypes = '253242')
                    - type: array
                      mode: table
                      name: remainDayOffs
                      arrayOptions:
                        canChangeSize: false
                        rowTotal:
                          - noDaysOffRemain
                          - numberOfDaysOff
                          - leavesPendingApproval
                          - remainingVacationFund
                      field:
                        type: group
                        name: vacationFund
                        fields:
                          - type: text
                            readOnly: true
                            label: Vacation type
                            name: typeDayOff
                            width: 180px
                          - type: dateRange
                            readOnly: true
                            label: Expiry date
                            name: expiryDate
                            mode: date-picker
                            setting:
                              type: day
                              format: dd/MM/yyyy
                            width: 129px
                          - type: number
                            readOnly: true
                            label: Leave Balance
                            name: noDaysOffRemain
                            align: end
                            width: 130px
                          - type: number
                            readOnly: true
                            label: Leaves Taken
                            name: numberOfDaysOff
                            align: end
                            width: 129px
                          - type: number
                            readOnly: true
                            label: Leaves Pending Approval
                            name: leavesPendingApproval
                            width: 220px
                            align: end
                          - type: number
                            readOnly: true
                            _label:
                              transform: >-
                                $.fields.leaveUnit = '1' ? 'Remaining Days' :
                                'Remaining Hours'
                            name: remainingVacationFund
                            align: end
                            width: 174px
              - type: text
                hiddenInput: true
                col: 2
                _condition:
                  transform: >-
                    $getFieldGroup($.extend.path, $.fields,
                    1).dayOffType.note.default ? true : false
                _toast:
                  transform: >-
                    {'type':'info','title': $getFieldGroup($.extend.path,
                    $.fields, 1).dayOffType.longName.default ,'content':
                    $getFieldGroup($.extend.path, $.fields,
                    1).dayOffType.note.default ,'position':'top','contentType':
                    'textarea'}
              - name: date
                label: Start Date - End Date
                type: dateRange
                setting:
                  format: dd/MM/yyyy
                validators:
                  - type: required
                  - type: ppx-custom
                    args:
                      transform: >-
                        ($exists($.value[0]) and $not($exists($.value[1])) ?
                        true : false)
                    text: Cannot be empty
              - name: leavePeriod
                label: Leave Period
                type: select
                outputValue: value
                placeholder: Select Leave Period
                _condition:
                  transform: $.groupValue.leaveUnit = '1'
                _select:
                  transform: $.variables._leavePeriodList
                validators:
                  - type: required
              - name: time
                label: From Time - End Time
                type: timePicker
                mode: range
                _condition:
                  transform: $.groupValue.leaveUnit = '2'
                validators:
                  - type: required
                  - type: ppx-custom
                    args:
                      transform: >-
                        ($exists($.value[0]) and $not($exists($.value[1])) ?
                        true : false)
                    text: Cannot be empty
              - type: text
                name: fromTime
                label: From Time
                _value:
                  transform: >-
                    ($time := $getFieldGroup($.extend.path, $.fields,
                    1).time;$exists($time[0]) ? $time[0] : '_setValueNull')
                  dependants:
                    - $.fields.tsapplicationForLeaveTypes[$index].time
                  params:
                    $index: $.extend.path[-1]
                unvisible: true
              - type: text
                name: endTime
                label: End Time
                _value:
                  transform: >-
                    ($time := $getFieldGroup($.extend.path, $.fields,
                    1).time;$exists($time[1]) ? $time[1] : '_setValueNull')
                  dependants:
                    - $.fields.tsapplicationForLeaveTypes[$index].time
                  params:
                    $index: $.extend.path[-1]
                unvisible: true
              - name: startDate
                label: Start Date
                type: text
                _value:
                  transform: >-
                    ($date := $getFieldGroup($.extend.path, $.fields,
                    1).date;$exists($date[0]) ? $date[0] : '_setValueNull')
                  dependants:
                    - $.fields.tsapplicationForLeaveTypes[$index].date
                  params:
                    $index: $.extend.path[-1]
                unvisible: true
              - name: endDate
                label: End Date
                type: text
                _value:
                  transform: >-
                    ($date := $getFieldGroup($.extend.path, $.fields,
                    1).date;$exists($date[1]) ? $date[1] : '_setValueNull')
                  dependants:
                    - $.fields.tsapplicationForLeaveTypes[$index].date
                  params:
                    $index: $.extend.path[-1]
                unvisible: true
    - type: group
      label: Absence Types Information
      n_cols: 1
      space: 12
      _condition:
        transform: $.extend.formType = 'edit'
      fields:
        - name: absenceTypes
          label: Absence Types
          type: select
          readOnly: true
          placeholder: Select Absence Types
          outputValue: value
          _select:
            transform: $absenceTypeList()
          _value:
            transform: $.fields.tsapplicationForLeaveTypes[0].absenceTypes
        - name: leaveBalance
          label: Leave Balance
          readOnly: true
          type: number
          _value:
            transform: >-
              ($leaveBalanceDetails :=
              $.fields.tsapplicationForLeaveTypes[0].leaveBalanceDetails;
              $sum($leaveBalanceDetails.remainDayOffs.remainingVacationFund))
          number:
            precision: 0
            _suffix:
              transform: '$.fields.leaveUnit = ''1'' ? ''Days'' : ''Hours'''
          hyperlink:
            _value:
              transform: >-
                ($index := 0;$leaveUnit := $.fields.leaveUnit;$absenceTypes :=
                $.fields.tsapplicationForLeaveTypes[$index].absenceTypes;$leaveBalanceDetails
                :=
                $.fields.tsapplicationForLeaveTypes[$index].leaveBalanceDetails;
                $map($leaveBalanceDetails, function($item)
                {{'leaveUnit':$leaveUnit,'absenceTypes':$absenceTypes,'numberStandard':
                $item.numberStandard,'expiryDate':  $DateFormat($item.fromDate,
                'DD/MM/YYYY') & ' to ' & $DateFormat($item.toDate,
                'DD/MM/YYYY'), 'remainDayOffs': $item.remainDayOffs}}))
            hyperlinkText: Leave balance details
            hyperlinkTitle: See details of vacation fund
            fields:
              - type: text
                readOnly: true
                label: leaveUnit
                name: leaveUnit
                unvisible: true
              - type: text
                readOnly: true
                label: Absence Types
                name: absenceTypes
                unvisible: true
              - type: text
                readOnly: true
                label: Standard Leave
                name: numberStandard
                _unvisible:
                  transform: $not($.fields.absenceTypes = '253242')
              - type: text
                readOnly: true
                label: Expiry date
                name: expiryDate
                _unvisible:
                  transform: $not($.fields.absenceTypes = '253242')
              - type: array
                mode: table
                name: remainDayOffs
                arrayOptions:
                  canChangeSize: false
                  rowTotal:
                    - noDaysOffRemain
                    - numberOfDaysOff
                    - leavesPendingApproval
                    - remainingVacationFund
                field:
                  type: group
                  name: vacationFund
                  fields:
                    - type: text
                      readOnly: true
                      label: Vacation type
                      name: typeDayOff
                      width: 180px
                    - type: dateRange
                      readOnly: true
                      label: Expiry date
                      name: expiryDate
                      mode: date-picker
                      setting:
                        type: day
                        format: dd/MM/yyyy
                      width: 129px
                    - type: number
                      readOnly: true
                      label: Leave Balance
                      name: noDaysOffRemain
                      align: end
                      width: 130px
                    - type: number
                      readOnly: true
                      label: Leaves Taken
                      name: numberOfDaysOff
                      align: end
                      width: 129px
                    - type: number
                      readOnly: true
                      label: Leaves Pending Approval
                      name: leavesPendingApproval
                      width: 220px
                      align: end
                    - type: number
                      readOnly: true
                      _label:
                        transform: >-
                          $.fields.leaveUnit = '1' ? 'Remaining Days' :
                          'Remaining Hours'
                      name: remainingVacationFund
                      align: end
                      width: 174px
        - type: group
          n_cols: 2
          fields:
            - type: radio
              name: leaveUnit
              label: Leave Unit
              radio:
                - label: Days
                  value: '1'
                - label: Hours
                  value: '2'
              disabled: true
    - type: array
      name: tsapplicationForLeaveTypes
      arrayOptions:
        canChangeSize: false
      _condition:
        transform: $.extend.formType = 'edit'
      size: 1
      field:
        type: group
        collapse: false
        _disableEventCollapse:
          transform: '$.extend.formType = ''edit'' ? true : false'
        fields:
          - name: id
            type: text
            unvisible: true
          - name: leaveUnit
            type: text
            unvisible: true
          - name: leaveReason
            type: text
            label: Leave Reason
            validators:
              - type: required
            placeholder: Enter Leave Reason
          - name: absenceTypes
            label: Absence Types
            type: text
            unvisible: true
          - type: text
            label: dayOffType
            name: dayOffType
            _value:
              transform: >-
                ($absenceTypes := $getFieldGroup($.extend.path, $.fields,
                1).absenceTypes; $absenceTypes ?
                $getAbsenceTypeByCode($absenceTypes) : {})
              dependants:
                - $.fields.tsapplicationForLeaveTypes[$index].absenceTypes
              params:
                $index: $.extend.path[-1]
            unvisible: true
          - type: text
            label: Vacation type
            name: leaveBalanceDetails
            _value:
              transform: >-
                ($leaveUnit := $getFieldGroup($.extend.path, $.fields,
                1).leaveUnit; $employeeCode :=
                $.fields.employeeCode;$employeeRecord :=
                $.fields.employeeRecord; $startDate :=
                $getFieldGroup($.extend.path, $.fields, 1).startDate; $endDate
                := $getFieldGroup($.extend.path, $.fields, 1).endDate;
                $absenceTypes := $getFieldGroup($.extend.path, $.fields,
                1).absenceTypes;($not($isNilorEmpty($employeeCode)) and
                $not($isNilorEmpty($employeeRecord)) and
                $not($isNilorEmpty($absenceTypes)) and
                $not($isNilorEmpty($leaveUnit))) ? ($ouput :=
                $dayDetailDayOff($employeeCode,$employeeRecord,$absenceTypes,$startDate,$endDate,
                $leaveUnit); $ouput))
              dependants:
                - $.fields.tsapplicationForLeaveTypes[$index].absenceTypes
                - $.fields.tsapplicationForLeaveTypes[$index].leaveUnit
                - $.fields.employeeCode
                - $.fields.employeeRecord
                - $.fields.tsapplicationForLeaveTypes[$index].startDate
                - $.fields.tsapplicationForLeaveTypes[$index].endDate
              params:
                $index: $.extend.path[-1]
            unvisible: true
          - type: number
            label: index
            name: indexLevel1
            _value:
              transform: ($index := $.extend.path[-2]; $index)
            unvisible: true
          - type: text
            hiddenInput: true
            col: 2
            _condition:
              transform: >-
                $getFieldGroup($.extend.path, $.fields,
                1).dayOffType.note.default ? true : false
            _toast:
              transform: >-
                {'type':'info','title': $getFieldGroup($.extend.path, $.fields,
                1).dayOffType.longName.default ,'content':
                $getFieldGroup($.extend.path, $.fields,
                1).dayOffType.note.default ,'position':'top','contentType':
                'textarea'}
          - type: group
            padding: 0px
            n_cols: 2
            fields:
              - name: date
                label: Start Date - End Date
                type: dateRange
                setting:
                  format: dd/MM/yyyy
                validators:
                  - type: required
                  - type: ppx-custom
                    args:
                      transform: >-
                        ($exists($.value[0]) and $not($exists($.value[1])) ?
                        true : false)
                    text: Cannot be empty
              - name: leavePeriod
                label: Leave Period
                type: select
                outputValue: value
                placeholder: Select Leave Period
                _condition:
                  transform: $.fields.leaveUnit = '1'
                _select:
                  transform: $.variables._leavePeriodList
                validators:
                  - type: required
              - name: startDate
                label: Start Date
                type: text
                _value:
                  transform: >-
                    ($date := $getFieldGroup($.extend.path, $.fields,
                    1).date;$exists($date[0]) ? $date[0] : '_setValueNull')
                  dependants:
                    - $.fields.tsapplicationForLeaveTypes[$index].date
                  params:
                    $index: $.extend.path[-1]
                unvisible: true
              - name: endDate
                label: End Date
                type: text
                _value:
                  transform: >-
                    ($date := $getFieldGroup($.extend.path, $.fields,
                    1).date;$exists($date[1]) ? $date[1] : '_setValueNull')
                  dependants:
                    - $.fields.tsapplicationForLeaveTypes[$index].date
                  params:
                    $index: $.extend.path[-1]
                unvisible: true
              - name: time
                label: From Time - End Time
                type: timePicker
                mode: range
                validators:
                  - type: required
                  - type: ppx-custom
                    args:
                      transform: >-
                        ($exists($.value[0]) and $not($exists($.value[1])) ?
                        true : false)
                    text: Cannot be empty
                _condition:
                  transform: $.fields.leaveUnit = '2'
              - type: text
                name: fromTime
                label: From Time
                _value:
                  transform: >-
                    ($time := $getFieldGroup($.extend.path, $.fields,
                    1).time;$exists($time[0]) ? $time[0] : '_setValueNull')
                  dependants:
                    - $.fields.tsapplicationForLeaveTypes[$index].time
                  params:
                    $index: $.extend.path[-1]
                unvisible: true
              - type: text
                name: endTime
                label: End Time
                _value:
                  transform: >-
                    ($time := $getFieldGroup($.extend.path, $.fields,
                    1).time;$exists($time[1]) ? $time[1] : '_setValueNull')
                  dependants:
                    - $.fields.tsapplicationForLeaveTypes[$index].time
                  params:
                    $index: $.extend.path[-1]
                unvisible: true
  overview:
    dependentField: employeeInfor
    title: Employee Detail
    noDataMessages: Select employee information to display data
    border: true
    uri: >-
      /api/personals/:{employeeId}:/job-datas?filter%5B0%5D=employeeRecordNumber%7C%7C%24eq%7C%7C:{employeeRecordNumber}:
    display:
      - key: companyName
        label: Company
      - key: legalEntityName
        label: Legal Entity
      - key: businessUnitName
        label: Business Unit
      - key: divisionName
        label: Division
      - key: departmentName
        label: Department
      - key: jobName
        label: Job Title
  sources:
    leaveStatusList:
      uri: '"/api/picklists/LEAVEREQUESTSTATUS/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
    leavePeriodList:
      uri: '"/api/picklists/LEAVEPERIOD/values"'
      method: GET
      queryTransform: '{''sort'': [{''field'' : ''code'' , ''order'' : ''ascend''}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
    employeesList:
      uri: '"/api/personals"'
      method: GET
      queryTransform: '{''limit'': $.limit, ''page'': $.page,''search'': $.search,''filter'': []}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.employeeId &' - '&
        $item.employeeRecordNumber &' - '& $item.name, 'value': {'employeeId':
        $item.employeeId, 'employeeRecordNumber': $item.employeeRecordNumber,
        'jobDataId': $item.jobDataId}, 'employeeId': $item.employeeId,
        'employeeRecordNumber': $item.employeeRecordNumber}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    employeeNameList:
      uri: '"/api/ts-list-application-for-leaves"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.employeeName, 'value':
        $item.employeeName}})[]
      disabledCache: true
    absenceTypeList:
      uri: '"/api/ts-set-dayoff-type-rules/list-data"'
      method: GET
      queryTransform: >-
        {'limit': $.limit ? $.limit : 10000, 'page': $.page ? $.page : 1,
        'search': $.search}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.dayOffTypeName & ' (' &
        $item.dayOffTypeId & ')', 'value': $item.dayOffTypeId}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - unitType
    dayDetailList:
      uri: '"/api/ts-list-application-for-leaves/day-detail"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'CAtypeOfDayOffId','operator': '$eq','value':
        $.CAtypeOfDayOffId},{'field':'employeeCode','operator': '$eq','value':
        $.employeeCode},{'field':'employeeRecordNumber','operator':
        '$eq','value':$.employeeRecordNumber},{'field':'startDate','operator':
        '$eq','value': $.startDate},{'field':'endDate','operator':
        '$eq','value': $.endDate},{'field':'leavePeriod','operator':
        '$eq','value': $.leavePeriod}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $
      disabledCache: true
      params:
        - employeeCode
        - employeeRecordNumber
        - startDate
        - endDate
        - leavePeriod
        - CAtypeOfDayOffId
    dayDetailDayOff:
      uri: '"/api/ts-list-application-for-leaves/get-detail-day-off"'
      method: POST
      queryTransform: ''
      bodyTransform: >-
        {'employeeCode': $.employeeCode,'employeeRecordNumber':
        $.employeeRecordNumber,'absenceTypes': $.absenceTypes,'startDate':
        $.startDate,'endDate': $.endDate, 'leaveUnit': $.leaveUnit}
      headerTransform: ''
      resultTransform: $
      disabledCache: true
      params:
        - employeeCode
        - employeeRecordNumber
        - absenceTypes
        - startDate
        - endDate
        - leaveUnit
    getAbsenceTypeByCode:
      uri: '"/api/ca-type-of-days-offs"'
      method: GET
      queryTransform: >-
        {'limit': 10, 'page': 1,'filter': [{'field':'code','operator':
        '$eq','value': $.code}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $.data[0]
      disabledCache: true
      params:
        - code
  variables:
    _LeaveRequestStatusList:
      transform: $leaveStatusList()
    _employeeInfor:
      transform: >-
        $.fields.employeeCode != '' ? {'employeeId': $.fields.employeeCode,
        'employeeRecordNumber': $.fields.employeeRecord}
    _leavePeriodList:
      transform: $leavePeriodList()
  footer:
    create: false
    update: true
    createdOn: createdAt
    updatedOn: updatedAt
    createdBy: createdBy
    updatedBy: updatedBy
filter_config:
  fields:
    - type: selectAll
      name: employeeCode
      label: Employee
      labelType: type-grid
      placeholder: Select Employee
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $employeesList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      name: group
      label: Group
      labelType: type-grid
      placeholder: Select Group
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $groupList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      name: company
      label: Company
      labelType: type-grid
      placeholder: Select Company
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $companyList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      name: legalEntity
      label: Legal Entity
      labelType: type-grid
      placeholder: Select Legal Entity
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $legalEntityList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      name: businessUnit
      label: Business Unit
      labelType: type-grid
      placeholder: Select Business Unit
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $businessUnitList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      name: division
      label: Division
      labelType: type-grid
      placeholder: Select Division
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $divisionList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      name: department
      label: Department
      labelType: type-grid
      placeholder: Select Department
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $departmentList($.extend.limit, $.extend.page, $.extend.search)
    - type: radio
      name: leaveUnit
      labelType: type-grid
      label: Leave Unit
      radio:
        - label: All
          value: ''
        - label: Days
          value: '1'
        - label: Hours
          value: '2'
    - type: selectAll
      name: absenceTypes
      label: Absence Types
      labelType: type-grid
      mode: multiple
      isLazyLoad: true
      placeholder: Select Absence Types
      _options:
        transform: $absenceTypeList($.extend.limit, $.extend.page, $.extend.search)
    - type: dateRange
      label: Start Date
      labelType: type-grid
      name: startDate
    - type: dateRange
      label: End Date
      labelType: type-grid
      name: endDate
    - type: selectAll
      name: createdBy
      label: Create By
      labelType: type-grid
      placeholder: Select Create By
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $userList()
    - type: dateRange
      label: Created On
      labelType: type-grid
      name: createdAt
    - type: selectAll
      name: updatedBy
      label: Last Updated By
      labelType: type-grid
      placeholder: Select Last Updated By
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $userList()
    - type: dateRange
      label: Last Updated On
      labelType: type-grid
      name: updatedAt
  filterMapping:
    - field: $
      operator: $in
      valueField: employeeCode.(value)
    - field: groupId
      operator: $in
      valueField: group.(value)
    - field: company
      operator: $in
      valueField: company.(value)
    - field: legalEntity
      operator: $in
      valueField: legalEntity.(value)
    - field: division
      operator: $in
      valueField: division.(value)
    - field: businessUnit
      operator: $in
      valueField: businessUnit.(value)
    - field: department
      operator: $in
      valueField: department.(value)
    - field: leaveUnit
      operator: $eq
      valueField: leaveUnit
    - field: catypeOfDayOffId
      operator: $in
      valueField: absenceTypes.(value)
    - field: startDate
      operator: $between
      valueField: startDate
    - field: endDate
      operator: $between
      valueField: endDate
    - field: createdBy
      operator: $in
      valueField: createdBy.(value)
    - field: createdAt
      operator: $between
      valueField: createdAt
    - field: lastUpdatedBy
      operator: $in
      valueField: updatedBy.(value)
    - field: lastUpdatedOn
      operator: $between
      valueField: updatedAt
  sources:
    groupList:
      uri: '"/api/groups"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    divisionList:
      uri: '"/api/divisions"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    businessUnitList:
      uri: '"/api/business-units"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    companyList:
      uri: '"/api/companies"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    departmentList:
      uri: '"/api/departments"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    legalEntityList:
      uri: '"/api/legal-entities"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    employeesList:
      uri: '"/api/personals"'
      method: GET
      queryTransform: '{''limit'': $.limit, ''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.employeeId &' - '&
        $item.employeeRecordNumber &' - '& $item.name,'code': $item.employeeId,
        'value': {'employeeCode': $item.employeeId,'employeeRecord':
        $string($item.employeeRecordNumber)}}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    userList:
      uri: '"/api/internal-users"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.userName, 'value':
        $item.userName}})
      disabledCache: true
    absenceTypeList:
      uri: '"/api/ca-type-of-days-offs"'
      method: GET
      queryTransform: '{''limit'': $.limit, ''page'': $.page,''search'':$.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
layout_options:
  tool_table:
    - id: import
      icon: icon-upload-simple
      paramsRedirect:
        type: TS_OBJECT
        entityOrObj: TsApplicationforLeave
    - id: export
      icon: icon-download-simple
  show_dialog_form_save_add_button: true
  show_detail_history: false
  export_all:
    type: base_total
  hide_action_row: true
  show_dialog_duplicate_button: false
  delete_multi_items: true
  custom_export_api:
    url: api/ts-list-application-for-leaves
    selected_items_query_config:
      field: applicationForLeaveId
      operator: $in
      valueField: id
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    title: Edit
    icon: icon-pencil-simple
  - id: delete
    title: Delete
    icon: icon-trash
backend_url: api/ts-list-application-for-leaves
screen_name: null
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: company
    defaultName: CompanyCode
  - name: department
    defaultName: DepartmentCode
  - name: employeeId
    defaultName: EmployeeId
  - name: employeeRecord
    defaultName: EmployeeRecordNumber
  - name: jobCode
    defaultName: JobCode
  - name: legalEntity
    defaultName: LegalEntityCode
  - name: division
    defaultName: DivisionCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Leave Application Information Management
  parent:
    title: Parent Demo
