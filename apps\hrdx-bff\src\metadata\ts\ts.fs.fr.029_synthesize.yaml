id: TS.FS.FR.029_synthesize
status: draft
sort: 308
user_created: 7ff5c796-6aaa-4ed5-a1e2-96f1c88e1fe9
date_created: '2024-08-11T19:07:58.005Z'
user_updated: abd14398-1d53-4e6f-9834-26328f0ebde0
date_updated: '2025-07-25T03:18:29.605Z'
title: Attendance Period Management Synthesize
requirement:
  time: 1745564573629
  blocks:
    - id: Q_x62Tq-6U
      type: paragraph
      data:
        text: >-
          - <PERSON><PERSON><PERSON> năng cho phép bộ phận nhân sự tập đo<PERSON>n/CTTV khai báo và quản lý
          thông tin tổng hợp công của công ty thành viên.&nbsp; &nbsp; &nbsp;
          &nbsp; &nbsp;
  version: 2.30.7
screen_design: null
module: TS
local_fields:
  - code: code
    title: Period Code
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 9
    pinned: true
  - code: longName
    title: Period Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 13
  - code: revision
    title: Revision
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 6.25
  - code: group
    title: Group
    description: Group
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    options__tabular__column_width: 13
  - code: company
    title: Company
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    options__tabular__column_width: 13
  - code: legalEntity
    title: Legal Entity
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    options__tabular__column_width: 13
  - code: payGroupCode
    title: Pay Group Code
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: payGroup
    title: Pay Group Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    options__tabular__column_width: 12
  - code: startDatePeriod
    title: Period Start Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    options__tabular__column_width: 10
  - code: endDatePeriod
    title: Period End Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    options__tabular__column_width: 10
  - code: statusName
    title: Status
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: note
    title: Note
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    options__tabular__column_width: 11.25
  - code: updatedBy
    title: Last Updated By
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 9
  - code: updatedAt
    title: Last Updated On
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
    options__tabular__column_width: 10.75
mock_data:
  - id: 1
    period: 06/2024
    revision: '1'
    group: FPT
    company: FPT IS
    legalEntity: IS HN
    payGroup: Nhóm 1
    periodStartDate: 2024/06/01
    periodEndDate: 2024/06/30
    status: Đang tổng hợp
    note: Tổng hợp công tháng
    createdBy: Phương Bùi
    createdOn: 2024/06/30 10:00:02
    lastUpdatedBy: Khánh Vy
    lastUpdatedOn: 2024/06/30 10:00:02
  - id: 2
    period: 06/2024
    revision: '1'
    group: FPT
    company: FPT IS
    legalEntity: IS HN
    payGroup: Nhóm 1
    periodStartDate: 2024/06/01
    periodEndDate: 2024/06/30
    status: Hoàn thành
    note: Tổng hợp công tháng
    createdBy: Phương Bùi
    createdOn: 2024/06/30 10:00:02
    lastUpdatedBy: Khánh Vy
    lastUpdatedOn: 2024/06/30 10:00:02
local_buttons: null
layout: layout-table
form_config:
  formSize:
    create: large
    edit: large
    view: clamp(800px, 80%, 1600px)
  integrateBackendUrl: '''/api/ts-total-attendances'''
  btnModalDialogFooter:
    - id: synthesize
      title: Run
      type: primary
  fields:
    - type: group
      collapse: false
      n_cols: 2
      label: Time and Attendance
      fields:
        - name: tsmanagerTimekeeping
          label: Period
          type: select
          isLazyLoad: true
          validators:
            - type: required
          placeholder: Select Period
          _select:
            transform: >-
              $tsManagerTimekeepingsList($.fields.effectiveDate,$.extend.limit,
              $.extend.page, $.extend.search)
          clearFieldsAfterChange:
            - legalEntityCode
            - payGroupCode
        - name: tsmanagerTimekeepingCode
          label: tsmanagerTimekeepingCode
          type: text
          unvisible: true
          _value:
            transform: $.variables._tsManagerTimekeepingsSelected.id
        - name: country
          label: Country
          type: text
          disabled: true
          _value:
            transform: $.variables._tsManagerTimekeepingsSelected.country
        - name: countryCode
          label: Country
          type: text
          unvisible: true
          _value:
            transform: $.variables._tsManagerTimekeepingsSelected.countryCode
        - name: group
          label: Group
          type: text
          disabled: true
          _value:
            transform: $.variables._tsManagerTimekeepingsSelected.group
        - name: groupCode
          label: Group
          type: text
          unvisible: true
          _value:
            transform: $.variables._tsManagerTimekeepingsSelected.groupId
        - name: company
          label: Company
          type: text
          disabled: true
          _value:
            transform: $.variables._tsManagerTimekeepingsSelected.company
        - name: companyCode
          label: Company
          type: text
          unvisible: true
          _value:
            transform: $.variables._tsManagerTimekeepingsSelected.companyId
        - name: legalEntityCode
          label: Legal Entity
          type: select
          outputValue: value
          placeholder: Select Legal Entity
          _select:
            transform: $legalEntitiesList($.fields.effectiveDate,$.fields.companyCode)
          _value:
            transform: $.variables._tsManagerTimekeepingsSelected.legalEntityId
          _unvisible:
            transform: >-
              $.variables._tsManagerTimekeepingsSelected.legalEntityId  ? true :
              false
        - name: legalEntity
          label: Legal Entity
          type: text
          disabled: true
          _value:
            transform: $.variables._tsManagerTimekeepingsSelected.legalEntity
          _condition:
            transform: >-
              $.variables._tsManagerTimekeepingsSelected.legalEntityId ? true :
              false
        - name: payGroupCode
          label: Pay Group
          type: select
          placeholder: Select Pay Group
          isLazyLoad: true
          outputValue: value
          _select:
            transform: >-
              $payGroupsList($.fields.effectiveDate,$.extend.limit,
              $.extend.page,
              $.extend.search,$.fields.countryCode,$.fields.companyCode)
          _value:
            transform: $.variables._tsManagerTimekeepingsSelected.payGroupId
          _unvisible:
            transform: >-
              $.variables._tsManagerTimekeepingsSelected.payGroupId ? true :
              false
        - name: payGroup
          label: Pay Group
          type: text
          disabled: true
          _value:
            transform: $.variables._tsManagerTimekeepingsSelected.payGroup
          _condition:
            transform: >-
              $.variables._tsManagerTimekeepingsSelected.payGroupId ? true :
              false
        - name: startDatePeriod1
          label: Period start date
          type: dateRange
          mode: date-picker
          disabled: true
          _value:
            transform: $.variables._tsManagerTimekeepingsSelected.startDatePeriod
        - name: startDatePeriod
          label: Period start date
          type: dateRange
          mode: date-picker
          unvisible: true
          _value:
            transform: $.variables._tsManagerTimekeepingsSelected.startDatePeriod
        - name: endDatePeriod1
          label: Period end date
          type: dateRange
          mode: date-picker
          disabled: true
          _value:
            transform: $.variables._tsManagerTimekeepingsSelected.endDatePeriod
        - name: endDatePeriod
          label: Period end date
          type: dateRange
          mode: date-picker
          unvisible: true
          _value:
            transform: $.variables._tsManagerTimekeepingsSelected.endDatePeriod
        - name: effectiveDate
          label: effectiveDate
          type: dateRange
          mode: date-picker
          unvisible: true
          _value:
            transform: $now()
    - type: group
      collapse: false
      n_cols: 2
      label: Time and Attendance according to conditions
      fields:
        - name: divisionCode
          label: Division
          type: select
          isLazyLoad: true
          placeholder: Select Division
          outputValue: value
          _select:
            transform: >-
              $divisionsList($.fields.effectiveDate,$.extend.limit,
              $.extend.page, $.extend.search)
        - name: businessUnitCode
          label: Business Unit
          isLazyLoad: true
          type: select
          placeholder: Select Business Unit
          outputValue: value
          _select:
            transform: >-
              $businessUnitsList($.fields.effectiveDate,$.extend.limit,
              $.extend.page, $.extend.search)
        - name: listEmployeeCode
          label: Employee
          type: select
          placeholder: Select Employee
          isLazyLoad: true
          mode: multiple
          dependantField: $.fields.companyCode
          outputValue: value
          _select:
            transform: >-
              $.fields.payGroupCode ?
              $employeePayGroupList($.fields.effectiveDate,$.extend.limit,
              $.extend.page, $.extend.search, $.fields.payGroupCode) :
              $personalsList($.extend.limit, $.extend.page,
              $.extend.search,$.fields.companyCode)
        - name: departmentCode
          label: Department
          type: select
          isLazyLoad: true
          placeholder: Select Department
          outputValue: value
          _select:
            transform: >-
              $departmentsList($.fields.effectiveDate,$.extend.limit,
              $.extend.page, $.extend.search)
        - name: contractTypeCode
          label: Contract Type
          type: select
          placeholder: Select Contract Type
          outputValue: value
          _select:
            transform: $contractTypesList($.fields.effectiveDate)
        - name: employeeGroupCode
          label: Employee Group
          type: select
          isLazyLoad: true
          placeholder: Select Employee Group
          outputValue: value
          _select:
            transform: >-
              $employeeGroupsList($.fields.effectiveDate,$.extend.limit,
              $.extend.page, $.extend.search)
        - name: jobTitleCode
          label: Job Title
          type: select
          isLazyLoad: true
          placeholder: Select Job Title
          outputValue: value
          _select:
            transform: >-
              $jobsList($.fields.effectiveDate,$.extend.limit, $.extend.page,
              $.extend.search)
        - name: status
          label: Status
          type: select
          outputValue: value
          placeholder: Select Status
          select:
            - value: '1'
              label: Đang thực hiện
            - value: '2'
              label: Đã thực hiện
            - value: '3'
              label: Đã khóa
            - value: '4'
              label: Hoàn thành
  sources:
    personalsList:
      uri: '"/api/personals"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page,'search': $.search,'filter':
        [{'field':'company','operator': '$eq','value':$.companyCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.employeeId &' - '&
        $item.employeeRecordNumber &' - '& $item.name, 'value':
        {'employeeCode':$item.employeeId,'employeeRecordNumber':
        $item.employeeRecordNumber}, 'employeeId': $item.employeeId,
        'employeeRecordNumber': $item.employeeRecordNumber}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - companyCode
    legalEntitiesList:
      uri: '"/api/legal-entities/get-list"'
      queryTransform: >-
        {'page':1,'limit':10000,'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true},{'field':'companyCode','operator': '$eq','value':
        $.companyCode }]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - effectiveDate
        - companyCode
    businessUnitsList:
      uri: '"/api/business-units"'
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true},
        {'field':'effectiveDate','operator': '$lte','value': $.effectiveDate}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - effectiveDate
        - limit
        - page
        - search
    divisionsList:
      uri: '"/api/divisions"'
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true},
        {'field':'effectiveDate','operator': '$lte','value': $.effectiveDate}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - effectiveDate
        - limit
        - page
        - search
    departmentsList:
      uri: '"/api/departments"'
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true},
        {'field':'effectiveDate','operator': '$lte','value': $.effectiveDate}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code }})[]
      disabledCache: true
      params:
        - effectiveDate
        - limit
        - page
        - search
    employeeGroupsList:
      uri: '"/api/employee-groups"'
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true},
        {'field':'effectiveDate','operator': '$lte','value': $.effectiveDate}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.employeeGroup.default & '
        (' & $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - effectiveDate
        - limit
        - page
        - search
    jobsList:
      uri: '"/api/job-codes"'
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true},
        {'field':'effectiveDate','operator': '$lte','value': $.effectiveDate}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - effectiveDate
        - limit
        - page
        - search
    contractTypesList:
      uri: '"/api/picklists/CONTRACTTYPE/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - effectiveDate
    tsManagerTimekeepingsList:
      uri: '"/api/ts-manager-timekeepings"'
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true},
        {'field':'effectiveDate','operator': '$lte','value': $.effectiveDate}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.id, 'id': $item.id, 'data' : $item
        }})[]
      disabledCache: true
      params:
        - effectiveDate
        - limit
        - page
        - search
    payGroupsList:
      uri: '"/api/pay-group-structures"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':
        true},{'field':'effectiveDate','operator': '$lte','value':
        $.effectiveDate},{'field':'countryCode','operator': '$eq','value':
        $.countryCode},{'field':'companyCode','operator': '$eq','value':
        $.companyCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - effectiveDate
        - limit
        - page
        - search
        - countryCode
        - companyCode
    employeePayGroupList:
      uri: '"/api/employee-pay-groups"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'effectiveDate','operator': '$lte','value':
        $.effectiveDate},{'field':'payGroupCode','operator': '$eq','value':
        $.payGroupCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.employeeId &' - '&
        $item.employeeRecordNumber &' - '& $item.fullName, 'value':
        {'employeeCode':$item.employeeId,'employeeRecordNumber':
        $item.employeeRecordNumber}, 'employeeId': $item.employeeId,
        'employeeRecordNumber': $item.employeeRecordNumber}})[]
      disabledCache: true
      params:
        - effectiveDate
        - limit
        - page
        - search
        - payGroupCode
  variables:
    _tsManagerTimekeepingsSelected:
      transform: $.fields.tsmanagerTimekeeping.data
filter_config:
  fields:
    - name: period
      label: Period
      type: selectAll
      isLazyLoad: true
      mode: multiple
      _options:
        transform: >-
          $tsManagerTimekeepingsList($.extend.limit, $.extend.page,
          $.extend.search)
      placeholder: Select Period
    - name: revision
      label: Revision
      type: number
      number:
        min: '0'
        max: '1000'
      placeholder: Enter Revision
    - name: group
      label: Group
      type: selectAll
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $groupList($.extend.limit, $.extend.page, $.extend.search)
      placeholder: Select a company
    - name: company
      label: Company
      mode: multiple
      isLazyLoad: true
      type: selectAll
      _options:
        transform: $companyList($.extend.limit, $.extend.page, $.extend.search)
      placeholder: Select a company
    - name: legalEntity
      label: Legal Entity
      isLazyLoad: true
      mode: multiple
      type: selectAll
      _options:
        transform: $legalEntityList($.extend.limit, $.extend.page, $.extend.search)
      placeholder: Select a legal entity
    - name: payGroup
      label: Pay group
      mode: multiple
      type: selectAll
      isLazyLoad: true
      _options:
        transform: $payGroupsList($.extend.limit, $.extend.page, $.extend.search)
      placeholder: Select a pay group
    - name: periodStartDate
      label: Period Start Date
      type: dateRange
      placeholder: Enter the period start date
    - name: periodEndDate
      label: Period End Date
      type: dateRange
      placeholder: Enter the period end date
    - name: status
      label: Status
      mode: multiple
      type: select
      select:
        - value: '1'
          label: Đang thực hiện
        - value: '2'
          label: Đã thực hiện
        - value: '3'
          label: Đã khóa
        - value: '4'
          label: Hoàn thành
      placeholder: Select Status
    - name: createdBy
      label: Created By
      type: text
      placeholder: Enter Created By
    - name: createdOn
      label: Created On
      type: dateRange
      placeholder: Enter Created On
    - name: lastUpdatedBy
      label: Last Updated By
      type: text
      placeholder: Enter Last Updated By
    - name: lastUpdatedOn
      label: Last Updated On
      type: dateRange
      placeholder: Enter Last Updated On
  sources:
    groupList:
      uri: '"/api/groups"'
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    companyList:
      uri: '"/api/companies"'
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    legalEntityList:
      uri: '"/api/legal-entities"'
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    tsManagerTimekeepingsList:
      uri: '"/api/ts-manager-timekeepings"'
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    payGroupsList:
      uri: '"/api/pay-group-structures"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
  filterMapping:
    - field: code
      operator: $in
      valueField: period.(value)
    - field: revision
      operator: $eq
      valueField: revision
    - field: groupCode
      operator: $in
      valueField: group.(value)
    - field: companyCode
      operator: $in
      valueField: company.(value)
    - field: creator
      operator: $cont
      valueField: creator
    - field: legalEntityCode
      operator: $in
      valueField: legalEntity.(value)
    - field: payGroupCode
      operator: $in
      valueField: payGroup.(value)
    - field: startDatePeriod
      operator: $between
      valueField: periodStartDate
    - field: endDatePeriod
      operator: $between
      valueField: periodEndDate
    - field: status
      operator: $in
      valueField: status.(value)
    - field: createdBy
      operator: $cont
      valueField: createdBy
    - field: createdAt
      operator: $between
      valueField: createdOn
    - field: updatedBy
      operator: $cont
      valueField: lastUpdatedBy
    - field: updatedAt
      operator: $between
      valueField: lastUpdatedOn
layout_options:
  toolTable:
    export: true
    import: true
    adjustDisplay: true
  tool_table:
    - id: import
      icon: icon-upload-simple
      paramsRedirect:
        type: TS_OBJECT
        entityOrObj: TSSOTSByMonth
    - id: export
      icon: icon-download-simple
  show_detail_history: false
  hide_action_row: true
  modal_footer_buttons:
    - id: cancel
      title: Cancel
      type: secondary
    - id: save
      title: Synthesize
      type: primary
  export_all:
    type: base_total
  precheck_action:
    - action: synthesize
      api:
        _url:
          transform: >-
            '/api/ts-so-ot-ts-by-months/check-lock-status/' &
            $.tsmanagerTimekeepingCode
      expression: >-
        $.status = 'LockSome' ? {'message': $.message, 'type': 'confirm'} :
        $.status = 'LockAll' ? {'message': $.message, 'type': 'info'}
layout_options__header_buttons:
  - id: synthesize
    title: Synthesize
    icon: icon-monitor-play
    type: primary
options: null
create_form: null
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: view
    title: View
    icon: eye
    type: tertiary
backend_url: api/ts-total-attendances
screen_name: null
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
parent: null
detail_function_spec: TS.FS.FR.029_synthesize_detail
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key: null
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Time and Attendance Synthesize
  parent:
    title: Configuration
