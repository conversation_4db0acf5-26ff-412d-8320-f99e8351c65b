id: TS.FS.FR.029_synthesize_all
status: draft
sort: 10
user_created: 2aa5db12-fa54-46d6-8e05-9e421858de0c
date_created: '2024-09-04T06:40:41.750Z'
user_updated: abd14398-1d53-4e6f-9834-26328f0ebde0
date_updated: '2025-07-25T03:26:15.324Z'
title: All
requirement:
  time: 1725432021929
  blocks:
    - id: Awvbdan-_n
      type: paragraph
      data:
        text: All
  version: 2.29.1
screen_design: null
module: TS
local_fields:
  - code: lockName
    pinned: true
    title: Lock Status
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tag
      collection: field_types
    options__tabular__column_width: 7
    extra_config:
      tags:
        - value: Unlocked
          style:
            background_color: '#E6F2FF'
        - value: Locked
          style:
            background_color: '#F1F3F5'
  - code: employeeCode
    title: Employee ID
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 7.75
    pinned: true
  - code: employeeR<PERSON>ordNumber
    title: Employee Record Number (ERN)
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 8
    options__tabular__align: right
    pinned: true
  - code: employeeName
    title: Employee name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    options__tabular__column_width: 12.0625
    pinned: true
  - code: payGroupName
    title: Pay Group
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    options__tabular__column_width: 8.25
  - code: legalEntityDisplay
    title: Legal Entity Display
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    options__tabular__column_width: 11.25
  - code: departmentDisplay
    title: Department Display
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    options__tabular__column_width: 11.25
  - code: contractName
    title: Contract type
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    options__tabular__column_width: 8.9375
  - code: ernPeriod
    title: ERN Period
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    options__tabular__column_width: 11.375
  - code: periodStartDate
    title: Period start date
    data_type:
      key: dd/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/YYYY
      collection: field_types
    options__tabular__column_width: 11.25
  - code: periodEndDate
    title: Period end date
    data_type:
      key: dd/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/YYYY
      collection: field_types
    options__tabular__column_width: 11.25
  - code: tsUnitName
    title: TS Unit
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 6
  - code: standardWorkingDaysPeriod
    title: Standard Working Days
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 12
    options__tabular__align: right
  - code: workingHoursPeriod
    title: Working hours Period
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 11.25
    options__tabular__align: right
  - code: paidLeaveDayPeriod
    title: Paid Leave Day Period
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 12
    options__tabular__align: right
  - code: unpaidDayPeriod
    title: Unpaid Day Period
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 10
    options__tabular__align: right
  - code: workingDayByCalendarPeriod
    title: Working Day By Calendar Period
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 16
    options__tabular__align: right
  - code: workingDayPeriod
    title: Working Day Period
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 10.5
    options__tabular__align: right
  - code: holidayByCalendarPeriod
    title: Holiday By Calendar Period
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 14
    options__tabular__align: right
  - code: holidayPaid
    title: Holiday Paid
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 8.5
    options__tabular__align: right
  - code: paidDayPeriod
    title: Paid Day Period
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 8.75
    options__tabular__align: right
  - code: totalOTOnWeekdays
    title: Total OT on weekdays
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 11.5
    options__tabular__align: right
  - code: totalOTOnNightWeekdaysContinuous
    title: Total OT On Night Weekdays Continuous
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 19.5
    options__tabular__align: right
  - code: totalOTOnNightWeekdaysNonContinuous
    title: Total OT On Night Weekdays NonContinuous
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 21
    options__tabular__align: right
  - code: totalOTDayOff
    title: Total OT Day Off
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 9
    options__tabular__align: right
  - code: totalOTNightDayOff
    title: Total OT Night Day Off
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 11.5
    options__tabular__align: right
  - code: totalHolidayOT
    title: Total Holiday OT
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 9
    options__tabular__align: right
  - code: totalNightHolidayOT
    title: Total Night Holiday OT
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 11.5
    options__tabular__align: right
  - code: statusName
    title: Status
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tag
      collection: field_types
    pinned: false
    options__tabular__column_width: 10
    extra_config:
      tags:
        - value: Đang tổng hợp
          style:
            background_color: '#E6F2FF'
        - value: Chưa tổng hợp công
          style:
            background_color: '#FFE8E5'
        - value: Lỗi tổng hợp công
          style:
            background_color: '#FFE8E5'
        - value: Đã khóa
          style:
            background_color: '#F1F3F5'
        - value: Hoàn thành
          class: success
  - code: detailedError
    title: Detailed error
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    options__tabular__column_width: 11.25
mock_data:
  - employeeId: '00123454'
    employeeRecordNumber: 1
    employeeName: Nguyễn Văn C
    legalEntityDisplay: IS HN
    departmentDisplay: People X
    contractType: HĐ XĐTH 12 tháng
    periodStartDate: 01/09/2024
    periodEndDate: 30/09/2024
    tsUnit: USD
    standardWorkingDaysPeriod: 22
    workingHoursPeriod: 176
    actualWorkingDayProMaster: 20
    paidLeaveProMaster: 2
    holidayProMaster: 0
    paidDayProMaster: 22
    unpaidDayProMaster: 0
    actualWorkingDayEmpMaster: 20
    paidDayEmpMaster: 22
    holidayEmpMaster: 0
    unpaidDayEmpMaster: 0
    totalOTOnWeekdays: 10
    totalOTOnNightWeekdaysContinuous: 5
    totalOTOnNightWeekdaysNonContinuous: 2
    totalOTDayOff: 3
    totalOTNightOff: 3
    totalHolidayOT: 0
    totalNightHolidayOT: 0
    status: Hoàn thành
    detailedError: ''
  - employeeId: '00123454'
    employeeRecordNumber: 2
    employeeName: Nguyễn Văn D
    legalEntityDisplay: IS HN
    departmentDisplay: People X
    contractType: HĐ XĐTH 12 tháng
    periodStartDate: 01/09/2024
    periodEndDate: 30/09/2024
    tsUnit: USD
    standardWorkingDaysPeriod: 22
    workingHoursPeriod: 88
    actualWorkingDayProMaster: 18
    paidLeaveProMaster: 2
    holidayProMaster: 0
    paidDayProMaster: 20
    unpaidDayProMaster: 0
    actualWorkingDayEmpMaster: 18
    paidDayEmpMaster: 20
    holidayEmpMaster: 0
    unpaidDayEmpMaster: 0
    totalOTOnWeekdays: 5
    totalOTOnNightWeekdaysContinuous: 0
    totalOTOnNightWeekdaysNonContinuous: 1
    totalOTDayOff: 2
    totalOTNightOff: 2
    totalHolidayOT: 0
    totalNightHolidayOT: 0
    status: Lỗi tổng hợp công
    detailedError: ''
local_buttons: null
layout: layout-table
form_config:
  formSize:
    history: largex
  fields:
    - type: group
      collapse: false
      n_cols: 2
      label: Time and Attendance
      fieldGroupTitleStyle:
        border: none
        margin-top: '-4px'
        padding: 8px 0 8px 0
        font-size: 16px
      fieldGroupContentStyle:
        padding: 0 0 12px 0
      readOnly: true
      fields:
        - name: country
          label: Country
          type: text
        - name: group
          label: Group
          type: text
        - name: company
          label: Company
          type: text
        - name: legalEntity
          label: Legal Entity
          type: text
        - name: payGroup
          label: Pay Group
          type: text
        - name: period
          label: Period
          type: text
        - name: periodStartDate
          label: Period start date
          type: dateRange
          mode: date-picker
        - name: periodEndDate
          label: Period end date
          type: dateRange
          mode: date-picker
        - name: revision
          label: Revision
          type: text
        - name: version
          label: Version
          type: text
    - type: group
      collapse: false
      n_cols: 2
      label: Segment Detail
      fieldGroupTitleStyle:
        border: none
        padding: 8px 0 8px 0
        font-size: 16px
      fieldGroupContentStyle:
        padding: 0 0 24px 0
      readOnly: true
      fields:
        - name: fromDate
          label: From date
          type: dateRange
          mode: date-picker
          readOnly: true
        - name: toDate
          label: To date
          type: dateRange
          mode: date-picker
          readOnly: true
        - name: segment
          label: Segment
          type: text
          readOnly: true
        - name: employeeGroup
          label: Employee Group
          type: text
          readOnly: true
        - name: contractTypeName
          label: Contract Type
          type: text
          readOnly: true
        - name: jobTitle
          label: Job Title
          type: text
          readOnly: true
  historyTitle: '$.segment = ''0'' ? ''Summary'' : ''Segment '' & $.segment'
  historyDescription: >-
    $DateFormat($.fromDate,'DD/MM/YYYY') & ' - ' &
    $DateFormat($.toDate,'DD/MM/YYYY')
  historyHeaderTitle: >-
    'View Detail: ' & $.employeeName &'_'& $.employeeRecordNumber&'_'&
    $.employeeCode
  footer:
    create: false
    update: false
filter_config:
  fields:
    - name: employeeId
      label: Employee ID
      type: selectAll
      mode: multiple
      isLazyLoad: true
      placeholder: Select Employee ID
      _options:
        transform: $personalsList($.extend.limit, $.extend.page, $.extend.search)
    - name: legalEntityDisplay
      label: Legal Entity Display
      type: selectAll
      mode: multiple
      isLazyLoad: true
      placeholder: Select Legal Entity Display
      _options:
        transform: $legalEntityList($.extend.limit, $.extend.page, $.extend.search)
    - name: departmentDisplay
      label: Department Display
      type: selectAll
      mode: multiple
      isLazyLoad: true
      placeholder: Select Department Display
      _options:
        transform: $departmentList($.extend.limit, $.extend.page, $.extend.search)
    - name: contractType
      label: Contract Type
      type: selectAll
      mode: multiple
      isLazyLoad: true
      placeholder: Select Contract Type
      _options:
        transform: $contractTypeList()
    - name: employeeGroupCode
      label: ERN Period
      type: selectAll
      mode: multiple
      isLazyLoad: true
      placeholder: Select ERN Period
      _options:
        transform: $employeeGroupsList($.extend.limit, $.extend.page, $.extend.search)
    - name: periodStartDate
      label: Period Start Date
      type: dateRange
      placeholder: Enter the Period Start Date
    - name: periodEndDate
      label: Period End Date
      type: dateRange
      placeholder: Enter the Period End Date
    - name: tsUnit
      label: TS Unit
      type: select
      mode: multiple
      placeholder: Select TS Unit
      select:
        - label: Ngày
          value: '1'
        - label: Giờ
          value: '2'
    - name: status
      label: Status
      type: select
      mode: multiple
      select:
        - label: Đang tổng hợp
          value: '1'
        - label: Hoàn thành
          value: '2'
        - label: Đã khóa
          value: '3'
        - label: Tổng hợp lỗi
          value: '4'
    - name: detailedError
      label: Detailed Error
      type: text
      placeholder: Select Detailed Error
  sources:
    personalsList:
      uri: '"/api/personals"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item, $index) {{'label': $item.employeeId & ' - '
        & $item.employeeRecordNumber & ' - ' & $item.fullName, 'value':
        {'employeeCode':$item.employeeId,'employeeRecordNumber':$item.employeeRecordNumber
        }}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    legalEntityList:
      uri: '"/api/legal-entities"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    contractTypeList:
      uri: '"/api/picklists/CONTRACTTYPE/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
    departmentList:
      uri: '"/api/departments"'
      queryTransform: >-
        {'limit': $.limit, 'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    employeeGroupsList:
      uri: '"/api/employee-groups"'
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.employeeGroup.default & '
        (' & $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
  filterMapping:
    - field: $
      operator: $in
      valueField: employeeId.(value)
    - field: employeeGroupCode
      operator: $in
      valueField: employeeGroupCode.(value)
    - field: legalEntityId
      operator: $in
      valueField: legalEntityDisplay.(value)
    - field: pxDeptId
      operator: $in
      valueField: departmentDisplay.(value)
    - field: contractTypeId
      operator: $in
      valueField: contractType.(value)
    - field: periodStartDate
      operator: $between
      valueField: periodStartDate
    - field: periodEndDate
      operator: $between
      valueField: periodEndDate
    - field: status
      operator: $eq
      valueField: status.(value)
    - field: detailedError
      operator: $cont
      valueField: detailedError
layout_options:
  show_detail_history: true
  show_history_insert_button: false
  show_history_detail_function: true
  show_create_data_table: false
  history_dialog_options:
    sidebar:
      header:
        visible: false
    content:
      widget:
        header:
          visible: false
  page_header_options:
    visible: false
  customStyleContent:
    padding: 20px 0 0 0
  tool_table:
    - id: synthesize
      icon: icon-monitor-play-bold
      _condition: $.parentData.status != '4'
    - id: lock
      icon: icon-lock-bold
      _condition: $.parentData.status != '4'
      confirm:
        content: >-
          Confirm the public key. Data after locking will be transferred for
          salary calculation. Are you sure it's locked?
    - id: unlock
      icon: icon-lock-open-bold
      _condition: $.parentData.status != '4'
      confirm:
        content: Confirm public unlocking. Are you sure to unlock?
    - id: export
      icon: file-arrow-down-bold
  row_actions_handler:
    lock:
      action: lock
      confirm:
        title: Lock
        icon: lock
        content: >-
          Confirm the public key. Data after locking will be transferred for
          salary calculation. Are you sure it's locked?
        type: popup-info
      _backendUrl: /api/ts-so-ot-ts-by-months/:id/lock
      _update_fields: '[{''id'': $.id}]'
      method: POST
    unlock:
      action: unlock
      confirm:
        title: Unlock
        icon: lock-open
        content: Confirm public unlocking. Are you sure to unlock?
        type: popup-info
      _backendUrl: /api/ts-so-ot-ts-by-months/:id/unlock
      _update_fields: '[{''id'': $.id}]'
      method: POST
  export_all:
    type: base_total
layout_options__header_buttons: null
options: null
create_form: null
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: lock
    title: Lock
    icon: icon-lock-bold
    type: ghost-gray
    condition_func: $.status='2'
  - id: unlock
    title: Unlock
    icon: icon-lock-open-bold
    type: ghost-gray
    condition_func: $.status='3'
backend_url: /api/ts-so-ot-ts-by-months/{{parent.id}}
screen_name: null
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
parent: TS.FS.FR.029_synthesize_detail
detail_function_spec: TS.FS.FR.029_synthesize_timesheet_detail
payment_config: {}
multi_typing_config: {}
inherited_default_detail: true
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key: null
screen_designs: []
function_specs: []
fields: []
children: []
menu_item: null
