id: TS.FS.FR.029_synthesize_summary
status: draft
sort: 0
user_created: 2aa5db12-fa54-46d6-8e05-9e421858de0c
date_created: '2024-09-04T03:52:10.367Z'
user_updated: abd14398-1d53-4e6f-9834-26328f0ebde0
date_updated: '2025-06-12T02:25:41.283Z'
title: Summary
requirement:
  time: 1725421842886
  blocks:
    - id: adtKBmNLoi
      type: paragraph
      data:
        text: Attendance Period Management Synthesize Summary
  version: 2.29.1
screen_design: null
module: TS
local_fields: []
mock_data: null
local_buttons: null
layout: layout-form
form_config:
  fields:
    - type: group
      n_cols: 2
      padding: 0px
      fields:
        - type: group
          label: Time and Attendance Summary
          gap: 16px
          borderRadius: 8px
          border: '1px solid #DFE3E8'
          fieldGroupTitleStyle:
            padding: 12px
            font-size: 14px
            border-bottom: '1px solid #DFE3E8'
          fieldGroupContentStyle:
            padding: 12px
          fields:
            - name: countryName
              label: Country
              type: text
              readOnly: true
              _value:
                transform: >-
                  $.extend.defaultValue.countryCode ?
                  $.extend.defaultValue.countryName & ' (' &
                  $.extend.defaultValue.countryCode & ')'
            - name: group
              label: Group
              type: text
              readOnly: true
              _value:
                transform: >-
                  $.extend.defaultValue.groupCode ? $.extend.defaultValue.group
                  & ' (' & $.extend.defaultValue.groupCode & ')'
            - name: company
              label: Company
              type: text
              readOnly: true
              _value:
                transform: >-
                  $.extend.defaultValue.companyCode ?
                  $.extend.defaultValue.company & ' (' &
                  $.extend.defaultValue.companyCode & ')'
            - name: legalEntity
              label: Legal Entity
              type: text
              readOnly: true
              _value:
                transform: >-
                  $.extend.defaultValue.legalEntityCode ?
                  $.extend.defaultValue.legalEntity & ' (' &
                  $.extend.defaultValue.legalEntityCode & ')'
            - name: payPeriod
              label: Pay Period
              type: text
              readOnly: true
            - name: payGroup
              label: Pay group
              type: text
              readOnly: true
            - name: periodStartDate
              label: Period start date
              type: dateRange
              readOnly: true
              mode: date-picker
            - name: periodEndDate
              label: Period end date
              type: dateRange
              readOnly: true
              mode: date-picker
        - type: group
          label: Segment Information
          gap: 16px
          borderRadius: 8px
          border: '1px solid #DFE3E8'
          fieldGroupTitleStyle:
            padding: 12px
            font-size: 14px
            border-bottom: '1px solid #DFE3E8'
          fieldGroupContentStyle:
            padding: 12px
          fields:
            - name: totalEmployees
              label: Total Employees
              type: text
              readOnly: true
            - name: revision
              label: Revision
              type: text
              readOnly: true
            - name: version
              label: Version
              type: text
              readOnly: true
            - name: fails
              label: Fails
              type: text
              readOnly: true
            - name: processing
              label: Processing
              type: text
              readOnly: true
            - name: completed
              label: Completed
              type: text
              readOnly: true
            - name: locked
              label: Locked
              type: text
              readOnly: true
            - name: status
              label: Status
              type: select
              readOnly: true
              select:
                - value: '1'
                  label: Đang thực hiện
                - value: '2'
                  label: Đã thực hiện
                - value: '3'
                  label: Đã khóa
                - value: '4'
                  label: Hoàn thành
  footer:
    create: false
    update: true
    createdOn: createdAt
    updatedOn: updatedAt
    createdBy: createdBy
    updatedBy: updatedBy
filter_config: {}
layout_options:
  page_header_options:
    visible: false
  page_footer_options:
    visible: false
  customStyleContent:
    padding: 0px
  customStyleFormWrapper:
    padding: 20px 0 0 0
layout_options__header_buttons: null
options: null
create_form: null
layout_options__footer_buttons:
  - id: hidden
    type: link
layout_options__row_actions: null
backend_url: /api/ts-total-attendances/summary/{{parent.id}}
screen_name: null
layout_options__actions_many: null
parent: TS.FS.FR.029_synthesize_detail
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key: null
screen_designs: []
function_specs: []
fields: []
children: []
menu_item: null
