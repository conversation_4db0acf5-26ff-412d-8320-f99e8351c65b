id: TS.FS.FR.031_01
status: draft
sort: 420
user_created: 43878dbb-aa6c-4ef4-8bd7-0ea811ffafa7
date_created: '2024-08-01T07:53:41.340Z'
user_updated: abd14398-1d53-4e6f-9834-26328f0ebde0
date_updated: '2025-07-22T06:07:09.192Z'
title: 'Attendance Period Management '
requirement:
  time: 1722498109138
  blocks:
    - id: Hs4e3u7EhE
      type: paragraph
      data:
        text: >-
          Chứ<PERSON> năng đáp ứng các yêu cầu thêm mới, chỉnh sửa, xóa và tìm kiếm
          danh sách kỳ chấm công tương ứng với nghiệp vụ của từng CTTV
  version: 2.29.1
screen_design: null
module: TS
local_fields:
  - code: code
    title: TS Period Code
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: Load timesheet period code information
    sort: ascending
  - code: country
    title: Country
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: Load country information
  - code: group
    title: Group
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: Load group information
  - code: company
    title: Company
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: Load company information
  - code: legalEntity
    title: Legal Entity
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: Load legal entity information
  - code: payGroup
    title: Pay Group
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: Load pay group information
  - code: shortName
    title: Short name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    description: Load short name information
  - code: longName
    title: Long name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    description: Load long name information
  - code: timesheetFormula
    title: Timesheet Formula
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: Load timesheet formula information
  - code: period
    title: Period
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: Load period information
  - code: startDatePeriod
    title: Period Start Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    description: Load period start date information
  - code: endDatePeriod
    title: Period End Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    description: Load period end date information
  - code: note
    title: Note
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    description: Load note information
  - code: updatedBy
    title: Last Updated By
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: Load last updated by information
  - code: updatedAt
    title: Last Updated On
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
    description: Load last updated on information
mock_data:
  - code: '00000001'
    country: Việt Nam
    group: FPT
    company: FPT IS
    legalEntity: FIS HN
    shortName:
      default: FPT IS HN_2023
      vietnamese: FPT IS HN_2023
      english: FPT IS HN_2023
    longName:
      default: FPT IS HN_2023
      vietnamese: FPT IS HN_2023
      english: FPT IS HN_2023
    payGroup: Chăm sóc khách hàng (CS)
    timesheetFormula: Công thức chấm công bổ sung
    period: Năm
    periodStartDate: '2023-01-01'
    periodEndDate: '2023-12-31'
    status: Đang sử dụng
    note: Dữ liệu test
    createdBy: Phuong Bui
    createdOn: '2023-01-01 10:00:02'
    lastUpdatedBy: Khanh Vy
    lastUpdatedOn: '2023-01-01 12:00:02'
  - code: '00000002'
    country: Việt Nam
    group: FPT
    company: FPT IS
    legalEntity: FIS HN
    shortName:
      default: FPT IS HN_01/2023
      vietnamese: FPT IS HN_01/2023
      english: FPT IS HN_01/2023
    longName:
      default: FPT IS HN_01/2023
      vietnamese: FPT IS HN_01/2023
      english: FPT IS HN_01/2023
    payGroup: Triển khai bảo trì
    timesheetFormula: Công thức chấm công bổ sung
    period: Tháng
    periodStartDate: '2023-01-01'
    periodEndDate: '2023-01-31'
    status: Đang sử dụng
    note: Dữ liệu test
    createdBy: Phuong Bui
    createdOn: '2023-01-01 10:00:02'
    lastUpdatedBy: Khanh Vy
    lastUpdatedOn: '2023-01-01 12:00:02'
  - code: '00000003'
    country: Việt Nam
    group: FPT
    company: FPT IS
    legalEntity: FIS HN
    shortName:
      default: FPT IS HN_02/2023
      vietnamese: FPT IS HN_02/2023
      english: FPT IS HN_02/2023
    longName:
      default: FPT IS HN_02/2023
      vietnamese: FPT IS HN_02/2023
      english: FPT IS HN_02/2023
    payGroup: Triển khai bảo trì
    timesheetFormula: Công thức chấm công bổ sung
    period: Tháng
    periodStartDate: '2023-02-01'
    periodEndDate: '2023-02-28'
    status: Đang sử dụng
    note: Dữ liệu test
    createdBy: Phuong Bui
    createdOn: '2023-01-01 10:00:02'
    lastUpdatedBy: Khanh Vy
    lastUpdatedOn: '2023-01-01 12:00:02'
  - code: '00000004'
    country: Việt Nam
    group: FPT
    company: FPT IS
    legalEntity: FIS HN
    shortName:
      default: FPT IS HN_03/2023
      vietnamese: FPT IS HN_03/2023
      english: FPT IS HN_03/2023
    longName:
      default: FPT IS HN_03/2023
      vietnamese: FPT IS HN_03/2023
      english: FPT IS HN_03/2023
    payGroup: Khối cửa hàng
    timesheetFormula: Công thức chấm công bổ sung
    period: Tháng
    periodStartDate: '2023-03-01'
    periodEndDate: '2023-03-31'
    status: Đang sử dụng
    note: Dữ liệu test
    createdBy: Phuong Bui
    createdOn: '2023-01-01 10:00:02'
    lastUpdatedBy: Khanh Vy
    lastUpdatedOn: '2023-01-01 12:00:02'
  - code: '00000005'
    country: Việt Nam
    group: FPT
    company: FPT IS
    legalEntity: FIS HN
    shortName:
      default: FPT IS HN_04/2023
      vietnamese: FPT IS HN_04/2023
      english: FPT IS HN_04/2023
    longName:
      default: FPT IS HN_04/2023
      vietnamese: FPT IS HN_04/2023
      english: FPT IS HN_04/2023
    payGroup: Khối văn phòng
    timesheetFormula: Công thức chấm công bổ sung
    period: Tháng
    periodStartDate: '2023-04-01'
    periodEndDate: '2023-04-30'
    status: Đang sử dụng
    note: Dữ liệu test
    createdBy: Phuong Bui
    createdOn: '2023-01-01 10:00:02'
    lastUpdatedBy: Khanh Vy
    lastUpdatedOn: '2023-01-01 12:00:02'
  - code: '00000006'
    country: Việt Nam
    group: FPT
    company: FPT IS
    legalEntity: FIS HN
    shortName:
      default: FPT IS HN_05/2023
      vietnamese: FPT IS HN_05/2023
      english: FPT IS HN_05/2023
    longName:
      default: FPT IS HN_05/2023
      vietnamese: FPT IS HN_05/2023
      english: FPT IS HN_05/2023
    payGroup: Chăm sóc khách hàng (CS)
    timesheetFormula: Công thức chấm công bổ sung
    period: Tháng
    periodStartDate: '2023-05-01'
    periodEndDate: '2023-05-31'
    status: Đang sử dụng
    note: Dữ liệu test
    createdBy: Phuong Bui
    createdOn: '2023-01-01 10:00:02'
    lastUpdatedBy: Khanh Vy
    lastUpdatedOn: '2023-01-01 12:00:02'
  - code: '00000007'
    country: Việt Nam
    group: FPT
    company: FPT IS
    legalEntity: FIS HN
    shortName:
      default: FPT IS HN_06/2023
      vietnamese: FPT IS HN_06/2023
      english: FPT IS HN_06/2023
    longName:
      default: FPT IS HN_06/2023
      vietnamese: FPT IS HN_06/2023
      english: FPT IS HN_06/2023
    payGroup: Chăm sóc khách hàng (CS)
    timesheetFormula: Công thức chấm công bổ sung
    period: Tháng
    periodStartDate: '2023-06-01'
    periodEndDate: '2023-06-30'
    status: Đang sử dụng
    note: Dữ liệu test
    createdBy: Phuong Bui
    createdOn: '2023-01-01 10:00:02'
    lastUpdatedBy: Khanh Vy
    lastUpdatedOn: '2023-01-01 12:00:02'
  - code: '00000008'
    country: Việt Nam
    group: FPT
    company: FPT IS
    legalEntity: FIS HN
    shortName:
      default: FPT IS HN_07/2023
      vietnamese: FPT IS HN_07/2023
      english: FPT IS HN_07/2023
    longName:
      default: FPT IS HN_07/2023
      vietnamese: FPT IS HN_07/2023
      english: FPT IS HN_07/2023
    payGroup: Chăm sóc khách hàng (CS)
    timesheetFormula: Công thức chấm công bổ sung
    period: Tháng
    periodStartDate: '2023-07-01'
    periodEndDate: '2023-07-31'
    status: Đang sử dụng
    note: Dữ liệu test
    createdBy: Phuong Bui
    createdOn: '2023-01-01 10:00:02'
    lastUpdatedBy: Khanh Vy
    lastUpdatedOn: '2023-01-01 12:00:02'
  - code: '00000009'
    country: Việt Nam
    group: FPT
    company: FPT IS
    legalEntity: FIS HN
    shortName:
      default: FPT IS HN_08/2023
      vietnamese: FPT IS HN_08/2023
      english: FPT IS HN_08/2023
    longName:
      default: FPT IS HN_08/2023
      vietnamese: FPT IS HN_08/2023
      english: FPT IS HN_08/2023
    payGroup: Chăm sóc khách hàng (CS)
    timesheetFormula: Công thức chấm công bổ sung
    period: Tháng
    periodStartDate: '2023-08-01'
    periodEndDate: '2023-08-31'
    status: Đang sử dụng
    note: Dữ liệu test
    createdBy: Phuong Bui
    createdOn: '2023-01-01 10:00:02'
    lastUpdatedBy: Khanh Vy
    lastUpdatedOn: '2023-01-01 12:00:02'
  - code: '00000010'
    country: Việt Nam
    group: FPT
    company: FPT IS
    legalEntity: FIS HN
    shortName:
      default: FPT IS HN_09/2023
      vietnamese: FPT IS HN_09/2023
      english: FPT IS HN_09/2023
    longName:
      default: FPT IS HN_09/2023
      vietnamese: FPT IS HN_09/2023
      english: FPT IS HN_09/2023
    payGroup: Chăm sóc khách hàng (CS)
    timesheetFormula: Công thức chấm công bổ sung
    period: Tháng
    periodStartDate: '2023-09-01'
    periodEndDate: '2023-09-30'
    status: Đang sử dụng
    note: Dữ liệu test
    createdBy: Phuong Bui
    createdOn: '2023-01-01 10:00:02'
    lastUpdatedBy: Khanh Vy
    lastUpdatedOn: '2023-01-01 12:00:02'
local_buttons: null
layout: layout-table
form_config:
  fields:
    - type: group
      _condition:
        transform: $.extend.formType != 'view'
      n_cols: 2
      fields:
        - name: countryCode
          type: select
          label: Country
          placeholder: Select country
          clearFieldsAfterChange:
            - timesheetFormulaCode
          outputValue: value
          _select:
            transform: $nationsList($.extend.limit, $.extend.page, $.extend.search)
          isLazyLoad: true
          initOptionFromDefaultValue:
            value: countryCode
            label: '{{country}}'
        - name: groupId
          type: select
          label: Group
          clearFieldsAfterChange:
            - timesheetFormulaCode
            - companyId
            - payGroupId
          placeholder: Select Group
          outputValue: value
          _select:
            transform: $groupsList($.extend.limit, $.extend.page, $.extend.search)
          isLazyLoad: true
          initOptionFromDefaultValue:
            value: groupId
            label: '{{group}} ({{groupId}})'
        - name: companyId
          type: select
          label: Company
          clearFieldsAfterChange:
            - timesheetFormulaCode
            - legalEntityId
            - payGroupId
          placeholder: Select Company
          outputValue: value
          _select:
            transform: >-
              $companiesList($.extend.limit, $.extend.page, $.extend.search,
              $.fields.groupId)
          isLazyLoad: true
          initOptionFromDefaultValue:
            value: companyId
            label: '{{company}} ({{companyId}})'
        - name: legalEntityId
          label: Legal Entity
          type: select
          clearFieldsAfterChange:
            - timesheetFormulaCode
          outputValue: value
          placeholder: Select Legal Entity
          _select:
            transform: >-
              $legalEntityList($.extend.limit, $.extend.page, $.extend.search,
              $.fields.companyId)
          isLazyLoad: true
          initOptionFromDefaultValue:
            value: legalEntityId
            label: '{{legalEntity}} ({{legalEntityId}})'
        - name: code
          type: text
          label: TS Period Code
          placeholder: Automatic
          disabled: true
          col: 2
        - name: payGroupId
          type: select
          label: Pay Group
          placeholder: Select pay group
          outputValue: value
          _select:
            transform: >-
              $payGroupsList($.extend.limit, $.extend.page, $.extend.search,
              $.fields.companyId)
          isLazyLoad: true
          initOptionFromDefaultValue:
            value: payGroupId
            label: '{{payGroup}} ({{payGroupId}})'
        - name: shortName
          type: translation
          label: Short Name
          placeholder: Enter short name
          validators:
            - type: required
            - type: maxLength
              args: '300'
              text: Value should not exceed 300 characters
        - name: longName
          type: translation
          label: Long Name
          placeholder: Enter long name
          col: 2
          validators:
            - type: required
            - type: maxLength
              args: '500'
              text: Value should not exceed 500 characters
        - name: timesheetFormulaCode
          type: select
          label: Timesheet Formula
          placeholder: Select timesheet formula
          outputValue: value
          _value:
            transform: >-
              $.fields.companyId ? ($timesheetFormulaCode :=
              $formulaStructuresList($.fields.endDatePeriod,$.fields.countryCode,$.fields.companyId,$.fields.legalEntityId);
              $count($timesheetFormulaCode) > 0 ? $timesheetFormulaCode[0].value
              : '_setSelectValueNull')
          _select:
            transform: >-
              $formulaStructuresList($.fields.endDatePeriod,$.fields.countryCode,$.fields.companyId,$.fields.legalEntityId)
          validators:
            - type: required
        - name: periodCode
          type: select
          label: Period
          placeholder: Select period
          outputValue: value
          _select:
            transform: $periodsList($.fields.endDatePeriod )
          validators:
            - type: required
        - name: startDatePeriod
          type: dateRange
          label: Period Start Date
          setting:
            format: dd/MM/yyyy
            type: date
          mode: date-picker
          validators:
            - type: required
        - name: endDatePeriod
          type: dateRange
          label: Period End Date
          setting:
            format: dd/MM/yyyy
            type: date
          mode: date-picker
          _value:
            transform: >-
              $exists($.fields.periodCode) and $exists($.fields.startDatePeriod)
              and $.extend.formType = 'create' ? ($output :=
              ($.fields.periodCode = 'PERIOD_M' ?
              $CalDate($.fields.startDatePeriod,1,'M') : $.fields.periodCode =
              'PERIOD_Q' ? $CalDate($.fields.startDatePeriod,3,'M') :
              $.fields.periodCode = 'PERIOD_H' ?
              $CalDate($.fields.startDatePeriod,6,'M') : $.fields.periodCode =
              'PERIOD_Y1' ? $CalDate($.fields.startDatePeriod,12,'M'));
              $DateFormat($.fields.startDatePeriod, 'DD') = '01' ?
              $CalDate($output,-1,'d') : $output)
          validators:
            - type: required
            - type: ppx-custom
              args:
                transform: >-
                  $DateDiff($.fields.endDatePeriod, $.fields.startDatePeriod,
                  'd') < 0
              text: >-
                Period end date  must be greater than or equal to period start
                date.
    - type: group
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - name: country
          type: text
          label: Country
        - name: group
          type: text
          label: Group
        - name: company
          type: text
          label: Company
        - name: legalEntity
          type: text
          label: Legal Entity
        - name: payGroup
          type: text
          label: Pay Group
        - name: code
          type: text
          label: TS Period Code
          placeholder: Automatic
          disabled: true
        - name: shortName
          type: translation
          label: Short Name
        - name: longName
          type: translation
          label: Long Name
        - name: timesheetFormula
          type: text
          label: Timesheet Formula
        - name: period
          type: text
          label: Period
        - name: startDatePeriod
          type: dateRange
          label: Period Start Date
          setting:
            format: dd/MM/yyyy
            type: date
          mode: date-picker
        - name: endDatePeriod
          type: dateRange
          label: Period End Date
          setting:
            format: dd/MM/yyyy
            type: date
          mode: date-picker
    - name: note
      label: Note
      type: translationTextArea
      placeholder: Enter Note
      textarea:
        maxCharCount: 1000
        autoSize:
          minRows: 3
      validators:
        - type: maxLength
          args: '1000'
          text: Value should not exceed 1000 characters
  sources:
    groupsList:
      uri: '"/api/groups/get-by"'
      queryTransform: >-
        {'page':$.page,'limit': $.limit, 'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    companiesList:
      uri: '"/api/companies/get-by"'
      queryTransform: >-
        {'page':$.page,'limit': $.limit, 'search': $.search,'filter':
        [{'field':'status','operator':
        '$eq','value':true},{'field':'groupCode','operator': '$eq','value':
        $.groupCode }]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - groupCode
    legalEntityList:
      uri: '"/api/legal-entities/get-by"'
      queryTransform: >-
        {'page':$.page,'limit': $.limit, 'search': $.search,'filter':
        [{'field':'status','operator':
        '$eq','value':true},{'field':'companyCode','operator': '$eq','value':
        $.companyCode }]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - companyCode
    nationsList:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      queryTransform: >-
        {'page':$.page,'limit': $.limit, 'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    payGroupsList:
      uri: '"/api/pay-group-structures"'
      method: GET
      queryTransform: >-
        {'page':$.page,'limit': $.limit, 'search':
        $.search,'filter':[{'field':'status','operator':
        '$eq','value':true},{'field':'companyCode','operator': '$eq','value':
        $.companyCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - companyCode
    formulaStructuresList:
      uri: '"/api/formula-structures"'
      queryTransform: >-
        {'page':1,'limit':10000,'filter':
        [{'field':'elementGroupCode','operator':
        '$eq','value':'EG_003'},{'field':'countryCode','operator':
        '$eq','value':$.countryCode},{'field':'companyCode','operator':
        '$eq','value':$.companyCode},{'field':'legalEntityCode','operator':
        '$eq','value':$.legalEntityCode},{'field':'containsFullLegalEntityScope','operator':
        '$eq','value': true}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.formulaLongName & ' (' &
        $item.formulaCode & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - effectiveDate
        - countryCode
        - companyCode
        - legalEntityCode
    periodsList:
      uri: '"/api/picklists/PERIOD/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
  variables: {}
  footer:
    create: true
    update: true
    createdOn: createdAt
    updatedOn: updatedAt
    createdBy: createdBy
    updatedBy: updatedBy
filter_config:
  fields:
    - name: countryCode
      type: selectAll
      label: Country
      labelType: type-row
      placeholder: Enter country
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $nationsList($.extend.limit, $.extend.page, $.extend.search)
    - name: groupCode
      type: selectAll
      label: Group
      placeholder: Enter group
      isLazyLoad: true
      mode: multiple
      labelType: type-row
      _options:
        transform: $groupsList($.extend.limit, $.extend.page, $.extend.search)
    - name: companyCode
      type: selectAll
      label: Company
      labelType: type-row
      placeholder: Enter company
      isLazyLoad: true
      mode: multiple
      _options:
        transform: $companiesList($.extend.limit, $.extend.page, $.extend.search)
    - name: legalEntityCode
      type: selectAll
      label: Legal Entity
      labelType: type-row
      placeholder: Enter legal entity
      isLazyLoad: true
      mode: multiple
      _options:
        transform: $legalEntityList($.extend.limit, $.extend.page, $.extend.search)
    - name: payGroupCode
      type: selectAll
      label: Pay Group
      labelType: type-row
      placeholder: Enter pay group
      isLazyLoad: true
      mode: multiple
      _options:
        transform: $payGroupsList($.extend.limit, $.extend.page, $.extend.search)
    - name: code
      type: selectAll
      label: TS Period Code
      labelType: type-row
      isLazyLoad: true
      mode: multiple
      placeholder: Select TS Period Code
      _options:
        transform: $tsPeriodList($.extend.limit, $.extend.page, $.extend.search)
    - name: shortName
      type: text
      labelType: type-row
      label: Short Name
      placeholder: Enter short name
    - name: longName
      type: text
      labelType: type-row
      label: Long Name
      placeholder: Enter long name
    - name: timesheetFormulaCode
      type: selectAll
      label: Timesheet Formula
      labelType: type-row
      placeholder: Enter timesheet formula
      isLazyLoad: true
      mode: multiple
      _options:
        transform: $formulaStructuresList($.extend.limit, $.extend.page, $.extend.search)
    - name: periodCode
      type: select
      label: Period
      placeholder: Enter period
      mode: multiple
      labelType: type-row
      _select:
        transform: $periodsList()
    - name: startDatePeriod
      type: dateRange
      labelType: type-row
      label: Period Start Date
      setting:
        format: dd/MM/yyyy
        type: date
    - name: endDatePeriod
      type: dateRange
      label: Period End Date
      labelType: type-row
      setting:
        format: dd/MM/yyyy
        type: date
    - name: createdBy
      label: Created By
      type: selectAll
      labelType: type-row
      placeholder: Select Created By
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $userList()
    - name: createdAt
      label: Created On
      labelType: type-row
      type: dateRange
    - name: updatedBy
      label: Last Updated By
      labelType: type-row
      type: selectAll
      placeholder: Select Last Updated By
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $userList()
    - name: updatedAt
      labelType: type-row
      label: Last Updated On
      type: dateRange
  sources:
    groupsList:
      uri: '"/api/groups"'
      queryTransform: >-
        {'limit': $.limit, 'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    companiesList:
      uri: '"/api/companies"'
      queryTransform: >-
        {'limit': $.limit, 'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    legalEntityList:
      uri: '"/api/legal-entities"'
      queryTransform: >-
        {'limit': $.limit, 'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    nationsList:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    payGroupsList:
      uri: '"/api/pay-group-structures"'
      method: GET
      queryTransform: '{''limit'': $.limit, ''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    formulaStructuresList:
      uri: '"/api/formula-structures"'
      queryTransform: >-
        {'limit': $.limit, 'page': $.page,'search': $.search,'filter':
        [{'field':'elementGroupCode','operator':
        '$eq','value':'EG_003'},{'field':'status','operator':
        '$eq','value':true}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.formulaLongName & ' (' &
        $item.formulaCode & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    periodsList:
      uri: '"/api/picklists/PERIOD/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
    userList:
      uri: '"/api/internal-users?page=1&limit=10000"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.userName, 'value':
        $item.userName}})[]
      disabledCache: true
    tsPeriodList:
      uri: '"/api/ts-manager-timekeepings"'
      method: GET
      queryTransform: '{''limit'': $.limit, ''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
  filterMapping:
    - field: code
      operator: $in
      valueField: code.(value)
    - field: countryCode
      operator: $in
      valueField: countryCode.(value)
    - field: groupId
      operator: $in
      valueField: groupCode.(value)
    - field: companyId
      operator: $in
      valueField: companyCode.(value)
    - field: legalEntityId
      operator: $in
      valueField: legalEntityCode.(value)
    - field: payGroupId
      operator: $in
      valueField: payGroupCode.(value)
    - field: shortName
      operator: $cont
      valueField: shortName
    - field: longName
      operator: $cont
      valueField: longName
    - field: timesheetFormulaCode
      operator: $in
      valueField: timesheetFormulaCode.(value)
    - field: periodCode
      operator: $in
      valueField: periodCode.(value)
    - field: startDatePeriod
      operator: $between
      valueField: startDatePeriod
    - field: endDatePeriod
      operator: $between
      valueField: endDatePeriod
    - field: createdBy
      operator: $in
      valueField: createdBy.(value)
    - field: createdAt
      operator: $between
      valueField: createdAt
    - field: updatedBy
      operator: $in
      valueField: updatedBy.(value)
    - field: updatedAt
      operator: $between
      valueField: updatedAt
layout_options:
  tool_table:
    - id: export
      icon: icon-download-simple
  show_detail_history: false
  show_dialog_form_save_add_button: true
  export_all:
    type: base_total
  hide_action_row: true
  show_dialog_duplicate_button: false
  delete_multi_items: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: null
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    title: Edit
    icon: icon-pencil-simple
  - id: delete
    title: Delete
    icon: icon-trash
backend_url: api/ts-manager-timekeepings
screen_name: null
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: companyId
    defaultName: CompanyCode
  - name: countryCode
    defaultName: CountryCode
  - name: legalEntityId
    defaultName: LegalEntityCode
  - name: payGroupId
    defaultName: PayGroupCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: 'Attendance Period Management '
  parent:
    title: Configuration
