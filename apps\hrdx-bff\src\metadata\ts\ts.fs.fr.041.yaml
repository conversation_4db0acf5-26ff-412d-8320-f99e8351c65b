id: TS.FS.FR.041
status: draft
sort: 265
user_created: 56e71b91-dd39-4ebc-afd1-b4f00f38d4a5
date_created: '2024-08-09T04:05:38.102Z'
user_updated: abd14398-1d53-4e6f-9834-26328f0ebde0
date_updated: '2025-06-19T04:40:43.114Z'
title: Setup Attendance Objects For Organization
requirement:
  time: 1746670244752
  blocks:
    - id: jOR9FPl7Yv
      type: paragraph
      data:
        text: >-
          Chứ<PERSON> năng cho phép bộ phận nhân sự tập đoàn/<PERSON><PERSON> thiết lập, quản lý
          danh sách nguyên tắc xác định đối tượng chấm công cho đơn vị tương ứng
  version: 2.30.7
screen_design: null
module: TS
local_fields:
  - code: country
    title: Country
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: Load dữ liệu Quốc gia theo dữ liệu nhập
    pinned: false
  - code: group
    title: Group
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: Load dữ liệu Tập đoàn theo dữ liệu nhập
  - code: company
    title: Company
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: Load dữ liệu Công ty theo dữ liệu nhập
  - code: legalEntity
    title: Legal Entity
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: Load dữ liệu Pháp nhân theo dữ liệu nhập
  - code: businessUnit
    title: Business Unit
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: Hiển thị thông tin đơn vị của nhân viên theo thiết lập đã tạo
  - code: division
    title: Division
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: Hiển thị thông tin trung tâm của nhân viên theo thiết lập đã tạo
  - code: department
    title: Department
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: Hiển thị thông tin phòng ban của nhân viên theo thiết lập đã tạo
  - code: jobTitle
    title: Job
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: Load dữ liệu Chức danh theo dữ liệu nhập
  - code: effectiveDate
    title: Effective Start Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    description: Load dữ liệu Ngày hiệu lực theo dữ liệu nhập
    sort: ascending
  - code: effectiveDateTo
    title: Effective End Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
  - code: timeKeepingFormDisplay
    title: Attendance Tracking
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: Load dữ liệu Đối tượng chấm công theo dữ liệu nhập
    options__tabular__column_width: 12
  - code: note
    title: Note
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: Load dữ liệu Ghi chú theo dữ liệu nhập
  - code: lastUpdatedBy
    title: ' Last Updated By'
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    description: Load dữ liệu Người cập nhật mới nhất theo dữ liệu nhập
  - code: lastUpdatedOn
    title: ' Last Updated On'
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
    description: Load dữ liệu Ngày cập nhật mới nhất theo dữ liệu nhập
mock_data:
  - country: Vietnam
    group: FPT
    company: FPT Telecom
    legalEntity: FPT Telecom
    businessUnit: FPT Telecom
    division: Northern Deployment
    department: Northern Deployment
    jobTitle: HR Officer
    effectiveDate: '2024-01-01'
    timekeepingObject: Timekeeping exemption
    note: ''
    createdBy: AnhTT11
    createdOn: '2024-01-01 10:00:02'
    lastModifiedBy: AnhTT11
    lastModifiedOn: '2024-01-01 10:00:02'
  - country: Vietnam
    group: FPT
    company: FPT Retail
    legalEntity: FPT Retail
    businessUnit: FPT Shop
    division: FPT Shop
    department: ''
    jobTitle: IT Officer
    effectiveDate: '2020-01-01'
    timekeepingObject: Timekeeping exemption
    note: ''
    createdBy: AnhTT11
    createdOn: '2024-01-01 10:00:02'
    lastModifiedBy: AnhTT11
    lastModifiedOn: '2024-01-01 10:00:02'
  - country: Vietnam
    group: FPT
    company: FPT IS
    legalEntity: FPT IS
    businessUnit: FPT IS
    division: ''
    department: ''
    jobTitle: Business Officer
    effectiveDate: '2024-01-01'
    timekeepingObject: Timekeeping exemption
    note: ''
    createdBy: AnhTT11
    createdOn: '2024-01-01 10:00:02'
    lastModifiedBy: AnhTT11
    lastModifiedOn: '2024-01-01 10:00:02'
  - country: Vietnam
    group: FPT
    company: FPT IS
    legalEntity: FPT IS
    businessUnit: FPT IS
    division: ''
    department: ''
    jobTitle: Business Analysis Officer
    effectiveDate: '2021-01-01'
    timekeepingObject: Using check-in/check-out data
    note: ''
    createdBy: AnhTT11
    createdOn: '2023-01-01 10:00:02'
    lastModifiedBy: AnhTT11
    lastModifiedOn: '2024-01-01 10:00:02'
local_buttons: null
layout: layout-table
form_config:
  formSize:
    view: middle
  fields:
    - type: group
      label: ' '
      n_cols: 2
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - name: countryId
          label: Country
          type: select
          outputValue: value
          isLazyLoad: true
          placeholder: Select Country
          _select:
            transform: $nationsList($.extend.limit, $.extend.page, $.extend.search)
        - name: groupId
          label: Group
          type: select
          outputValue: value
          clearFieldsAfterChange:
            - companyId
            - legalEntityId
            - businessUnitId
            - divisionId
            - departmentId
          placeholder: Select Group
          _select:
            transform: $.variables._groupsList
        - name: companyId
          label: Company
          type: select
          outputValue: value
          clearFieldsAfterChange:
            - legalEntityId
            - businessUnitId
            - divisionId
            - departmentId
          placeholder: Select Company
          _select:
            transform: $.variables._companyList
        - name: legalEntityId
          label: Legal Entity
          type: select
          outputValue: value
          placeholder: Select Legal Entity
          _select:
            transform: $.variables._legalEntityList
        - name: businessUnitId
          label: Business Unit
          clearFieldsAfterChange:
            - divisionId
            - departmentId
          type: select
          outputValue: value
          placeholder: Select Business Unit
          _select:
            transform: $.variables._businessUnitList
        - name: divisionId
          label: Division
          type: select
          clearFieldsAfterChange:
            - departmentId
          outputValue: value
          placeholder: Select Division
          _select:
            transform: $.variables._divisionList
        - name: departmentId
          label: Department
          type: select
          outputValue: value
          placeholder: Select Department
          _select:
            transform: $.variables._departmentList
        - name: jobTitleId
          label: Job title
          type: select
          outputValue: value
          placeholder: Select Job title
          _select:
            transform: $jobCodeList($.fields.effectiveDate)
    - type: group
      label: ' '
      n_cols: 1
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - name: country
          label: Country
          type: text
        - name: group
          label: Group
          type: text
        - name: company
          label: Company
          type: text
        - name: legalEntity
          label: Legal Entity
          type: text
        - name: businessUnit
          label: Business Unit
          type: text
        - name: division
          label: Division
          type: text
        - name: department
          label: Department
          type: text
        - name: jobTitle
          label: Job title
          type: text
    - name: timeKeepingForm
      label: Attendance Tracking
      type: radio
      value: '1'
      validators:
        - type: required
      radio:
        - label: Miễn chấm công
          value: '1'
        - label: Sử dụng dữ liệu check in-check out
          value: '2'
    - type: group
      _n_cols:
        transform: '$.extend.formType = ''view'' ? 1 : 2'
      fields:
        - name: effectiveDate
          label: Effective Start Date
          type: dateRange
          mode: date-picker
          _value:
            transform: $.extend.formType = 'create' ? $now()
          validators:
            - type: required
          placeholder: dd/MM/yyyy
          setting:
            format: dd/MM/yyyy
            type: date
        - type: dateRange
          name: effectiveDateTo
          label: Effective End Date
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
            autoFill: end-off
            _disabledDate:
              transform: '{''operator'': ''$lt'', ''value'': $.fields.effectiveDate}'
          validators:
            - type: ppx-custom
              args:
                transform: >-
                  $exists($.fields.effectiveDate) and
                  $exists($.fields.effectiveDateTo) and
                  $DateDiff($DateFormat($.fields.effectiveDate, 'yyyy-MM-DD'),
                  $DateFormat($.fields.effectiveDateTo, 'yyyy-MM-DD'), 'd') > 0
              text: Effective end date must be greater than effective start date.
    - name: note
      label: Note
      type: textarea
      placeholder: Enter Note
      textarea:
        maxCharCount: 1000
        autoSize:
          minRows: 3
  footer:
    create: true
    update: true
    createdOn: createdOn
    updatedOn: lastUpdatedOn
    createdBy: createdBy
    updatedBy: lastUpdatedBy
  sources:
    groupsList:
      uri: '"/api/groups/get-by"'
      queryTransform: >-
        {'page':1,'limit':10000,'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - effectiveDate
    companiesList:
      uri: '"/api/companies/get-by"'
      queryTransform: >-
        {'page':1,'limit':10000,'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true},{'field':'groupCode','operator': '$eq','value':
        $.groupCode }]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - effectiveDate
        - groupCode
    legalEntityList:
      uri: '"/api/legal-entities/get-by"'
      queryTransform: >-
        {'page':1,'limit':10000,'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true},{'field':'companyCode','operator': '$eq','value':
        $.companyCode }]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - effectiveDate
        - companyCode
    businessUnitList:
      uri: '"/api/business-units/get-by"'
      queryTransform: >-
        {'page':1,'limit':10000,'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true},{'field':'companyCodeFilter','operator':
        '$eq','value': $.companyCode }]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - effectiveDate
        - companyCode
    divisionList:
      uri: '"/api/divisions/get-by"'
      queryTransform: >-
        {'page':1,'limit':10000,'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true},{'field':'businessUnitCode','operator':
        '$eq','value': $.businessUnitCode }]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - effectiveDate
        - businessUnitCode
    departmentList:
      uri: '"/api/departments/get-by"'
      queryTransform: >-
        {'page':1,'limit':10000,'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true},{'field':'divisionCode','operator': '$eq','value':
        $.divisionCode }]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code }})[]
      disabledCache: true
      params:
        - effectiveDate
        - divisionCode
    jobCodeList:
      uri: '"/api/job-codes"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'effectiveDate','operator':
        '$lte','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - effectiveDate
    nationsList:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
  variables:
    _groupsList:
      transform: $groupsList($.fields.effectiveDate)
    _companyList:
      transform: >-
        $.fields.groupId ? $companiesList($.fields.effectiveDate,
        $.fields.groupId)
    _legalEntityList:
      transform: >-
        $.fields.companyId ? $legalEntityList($.fields.effectiveDate,
        $.fields.companyId)
    _businessUnitList:
      transform: >-
        $.fields.companyId ? $businessUnitList($.fields.effectiveDate,
        $.fields.companyId)
    _divisionList:
      transform: >-
        $.fields.businessUnitId ? $divisionList($.fields.effectiveDate,
        $.fields.businessUnitId)
    _departmentList:
      transform: >-
        $.fields.divisionId ? $departmentList($.fields.effectiveDate,
        $.fields.divisionId)
filter_config:
  fields:
    - name: country
      label: Country
      type: selectAll
      mode: multiple
      labelType: type-grid
      placeholder: Select Country
      isLazyLoad: true
      _options:
        transform: $nationsList($.extend.limit, $.extend.page, $.extend.search)
    - name: group
      label: Group
      type: selectAll
      mode: multiple
      labelType: type-grid
      placeholder: Select Group
      isLazyLoad: true
      _options:
        transform: $groupsList($.extend.limit, $.extend.page, $.extend.search)
    - name: company
      label: Company
      type: selectAll
      mode: multiple
      labelType: type-grid
      placeholder: Select Company
      isLazyLoad: true
      _options:
        transform: $companiesList($.extend.limit, $.extend.page, $.extend.search)
    - name: legalEntity
      label: Legal Entity
      type: selectAll
      mode: multiple
      labelType: type-grid
      placeholder: Select Legal Entity
      isLazyLoad: true
      _options:
        transform: $legalEntitiesList($.extend.limit, $.extend.page, $.extend.search)
    - name: businessUnit
      label: Business Unit
      type: selectAll
      mode: multiple
      labelType: type-grid
      placeholder: Select Business Unit
      isLazyLoad: true
      _options:
        transform: $businessUnitsList($.extend.limit, $.extend.page, $.extend.search)
    - name: division
      label: Division
      type: selectAll
      mode: multiple
      labelType: type-grid
      placeholder: Select Division
      isLazyLoad: true
      _options:
        transform: $divisionsList($.extend.limit, $.extend.page, $.extend.search)
    - name: departmentId
      label: Department
      type: selectAll
      placeholder: Select Department
      labelType: type-grid
      mode: multiple
      isLazyLoad: true
      _options:
        transform: >-
          $departmentsList($.extend.limit,
          $.extend.page,$.fields.effectiveDate,$.extend.defaultValue.id,$.extend.search)
    - name: job
      label: Job
      type: selectAll
      mode: multiple
      labelType: type-grid
      placeholder: Select Job
      isLazyLoad: true
      _options:
        transform: $jobCodeList($.extend.limit, $.extend.page, $.extend.search)
    - name: effectiveDateFrom
      label: Effective Date
      type: dateRange
      labelType: type-grid
      placeholder: Enter Effective Date
      setting:
        format: dd/MM/yyyy
        type: date
    - name: timeKeepingForm
      label: Attendance Tracking
      type: select
      labelType: type-grid
      placeholder: Select Attendance Tracking
      select:
        - label: All
          value: ''
        - label: 'Miễn chấm công '
          value: '1'
        - label: Sử dụng dữ liệu check in-check out
          value: '2'
    - name: createdBy
      label: Created By
      type: selectAll
      labelType: type-grid
      placeholder: Select Creator
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $userList()
    - name: createdOn
      label: Created On
      type: dateRange
      labelType: type-grid
      placeholder: Enter Created On
      setting:
        format: dd/MM/yyyy
        type: date
    - name: LastUpdatedBy
      label: Last Updated By
      type: selectAll
      labelType: type-grid
      mode: multiple
      placeholder: Select Last Updated By
      isLazyLoad: true
      _options:
        transform: $userList()
    - name: LastUpdatedOn
      label: Last Updated On
      type: dateRange
      labelType: type-grid
      placeholder: Enter Last Updated On
      setting:
        format: dd/MM/yyyy
        type: date
  filterMapping:
    - field: countryId
      operator: $in
      valueField: country.(value)
    - field: groupId
      operator: $in
      valueField: group.(value)
    - field: companyId
      operator: $in
      valueField: company.(value)
    - field: legalEntityId
      operator: $in
      valueField: legalEntity.(value)
    - field: businessUnitId
      operator: $in
      valueField: businessUnit.(value)
    - field: divisionId
      operator: $in
      valueField: division.(value)
    - field: departmentId
      operator: $in
      valueField: departmentId.(value)
    - field: jobTitleId
      operator: $in
      valueField: job.(value)
    - field: effectiveDate
      operator: $between
      valueField: effectiveDateFrom
    - field: timeKeepingForm
      operator: $eq
      valueField: timeKeepingForm.(value)
    - field: createdBy
      operator: $in
      valueField: createdBy.(value)
    - field: createdOn
      operator: $between
      valueField: createdOn
    - field: lastUpdatedBy
      operator: $in
      valueField: LastUpdatedBy.(value)
    - field: lastUpdatedOn
      operator: $between
      valueField: LastUpdatedOn
  sources:
    groupsList:
      uri: '"/api/groups/get-by"'
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    companiesList:
      uri: '"/api/companies/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    legalEntitiesList:
      uri: '"/api/legal-entities/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})
      disabledCache: true
      params:
        - limit
        - page
        - search
    businessUnitsList:
      uri: '"/api/business-units/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})
      disabledCache: true
      params:
        - limit
        - page
        - search
    divisionsList:
      uri: '"/api/divisions/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})
      disabledCache: true
      params:
        - limit
        - page
        - search
    departmentsList:
      uri: '"/api/departments/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'filter':
        [{'field':'search','operator':
        '$eq','value':$.search},{'field':'effectiveDate','operator':
        '$lte','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true}, {'field':'IdFilter','operator': '$ne','value':
        $.codeFilter}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - effectiveDate
        - codeFilter
        - search
    jobCodeList:
      uri: '"/api/job-codes"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    nationsList:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    userList:
      uri: '"/api/internal-users"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.userName, 'value':
        $item.userName}})
      disabledCache: true
layout_options:
  toolTable:
    export: true
    adjustDisplay: true
  tool_table:
    - id: export
      icon: icon-download-simple
  show_dialog_form_save_add_button: true
  show_detail_history: false
  export_all:
    type: base_total
  hide_action_row: true
  show_dialog_duplicate_button: false
  delete_multi_items: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: null
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    icon: icon-pencil-simple
    type: ghost-gray
  - id: delete
    icon: icon-trash
    type: ghost-gray
backend_url: /api/ts-setup-timekeeping-objects
screen_name: ts-setup-timekeeping-objects
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: businessUnitId
    defaultName: BusinessUnitCode
  - name: companyId
    defaultName: CompanyCode
  - name: countryId
    defaultName: CountryCode
  - name: legalEntityId
    defaultName: LegalEntityCode
  - name: departmentId
    defaultName: DepartmentCode
  - name: divisionId
    defaultName: DivisionCode
  - name: jobTitleId
    defaultName: JobCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Setup Attendance Objects For Organization
  parent:
    title: Configuration
