id: TS.FS.FR.042
status: draft
sort: 303
user_created: 7ff5c796-6aaa-4ed5-a1e2-96f1c88e1fe9
date_created: '2024-07-16T10:28:17.619Z'
user_updated: abd14398-1d53-4e6f-9834-26328f0ebde0
date_updated: '2025-06-19T04:48:33.973Z'
title: Setup Attendance Objects For Employee
requirement:
  time: 1747108783218
  blocks:
    - id: JEusg0LQnH
      type: paragraph
      data:
        text: >-
          - <PERSON><PERSON> thống có hỗ trợ chức năng tự động xác định đối tượng chấm công
          dựa trên thông tin hợp đồng lao động của phân hệ HR.&nbsp; &nbsp;
          &nbsp; &nbsp; &nbsp;
  version: 2.30.7
screen_design: null
module: TS
local_fields:
  - code: employeeCode
    title: Employee ID
    description: EmployeeCode
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    pinned: true
    show_sort: true
  - code: employeeRecordNumber
    title: Employee Record Number (ERN)
    description: Employee record number
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__align: left
    options__tabular__column_width: 16
  - code: employeeName
    pinned: false
    title: Employee name
    description: Employee name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: company
    title: Company
    description: company
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: legalEntityName
    title: Legal entity
    description: legalEntity
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: null
  - code: businessUnit
    title: Business unit
    description: Business unit
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: division
    title: Division
    description: division
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: department
    title: Department
    description: department
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: jobTitleName
    title: Job
    description: job
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: effectiveDateFrom
    title: Effective Date From
    description: Effective date from
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    options__tabular__column_width: 12
  - code: effectiveDateTo
    title: Effective Date To
    description: Effective date to
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
    options__tabular__column_width: 12
  - code: attendanceTrackingName
    title: Attendance Tracking
    description: Timekeeping object
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 12
  - code: note
    title: Note
    description: note
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    options__tabular__column_width: 12
  - code: updatedBy
    title: Last Updated By
    description: Last editer
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: updatedAt
    title: Last Updated On
    description: Last edit time
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
mock_data:
  - employeeCode: '123545'
    employeeName: Nguyễn Văn A
    employeeRecordNumber: '123'
    group: FPT
    company: FPT IS
    legalEntity: FPT IS HCM
    businessUnit: ES HCM
    division: PB22
    department: Phòng triển khai miền Nam
    job: CB phân tích nghiệp vụ
    effectiveDateFrom: 01/05/2024
    effectiveDateTo: 01/01/2022
    timekeepingObject: Miễn chấm công
    note: ''
    creator: AnhTT1
    createTime: 01/01/2023 10:00:02
    lastEditer: AnhTT1
    lastEditTime: 01/01/2021 00:00:00
  - employeeCode: '456789'
    employeeName: Trần Thị B
    employeeRecordNumber: '456'
    group: ABC
    company: XYZ
    legalEntity: XYZ HCM
    businessUnit: ES HCM
    division: PB22
    department: Phòng triển khai miền Nam
    job: CB phân tích nghiệp vụ
    effectiveDateFrom: 01/05/2024
    effectiveDateTo: 01/01/2022
    timekeepingObject: Miễn chấm công
    note: ''
    creator: AnhTT1
    createTime: 01/01/2023 10:00:02
    lastEditer: AnhTT1
    lastEditTime: 01/01/2021 00:00:00
  - employeeCode: '987654'
    employeeName: Lê Văn C
    employeeRecordNumber: '789'
    group: DEF
    company: GHI
    legalEntity: GHI HCM
    businessUnit: ES HCM
    division: PB22
    department: Phòng triển khai miền Nam
    job: CB phân tích nghiệp vụ
    effectiveDateFrom: 01/05/2024
    effectiveDateTo: 01/01/2022
    timekeepingObject: Miễn chấm công
    note: ''
    creator: AnhTT1
    createTime: 01/01/2023 10:00:02
    lastEditer: AnhTT1
    lastEditTime: 01/01/2021 00:00:00
  - employeeCode: '135792'
    employeeName: Phạm Thị D
    employeeRecordNumber: '246'
    group: JKL
    company: MNO
    legalEntity: MNO HCM
    businessUnit: ES HCM
    division: PB22
    department: Phòng triển khai miền Nam
    job: CB phân tích nghiệp vụ
    effectiveDateFrom: 01/05/2024
    effectiveDateTo: 01/01/2022
    timekeepingObject: Miễn chấm công
    note: ''
    creator: AnhTT1
    createTime: 01/01/2023 10:00:02
    lastEditer: AnhTT1
    lastEditTime: 01/01/2021 00:00:00
  - employeeCode: '246813'
    employeeName: Hoàng Văn E
    employeeRecordNumber: '135'
    group: PQR
    company: STU
    legalEntity: STU HCM
    businessUnit: ES HCM
    division: PB22
    department: Phòng triển khai miền Nam
    job: CB phân tích nghiệp vụ
    effectiveDateFrom: 01/05/2024
    effectiveDateTo: 01/01/2022
    timekeepingObject: Miễn chấm công
    note: ''
    creator: AnhTT1
    createTime: 01/01/2023 10:00:02
    lastEditer: AnhTT1
    lastEditTime: 01/01/2021 00:00:00
local_buttons: null
layout: layout-table
form_config:
  formSize:
    view: middle
    create: large
    edit: large
  fields:
    - type: group
      fields:
        - type: select
          name: employee
          label: Employee
          placeholder: Select Employee
          isLazyLoad: true
          validators:
            - type: required
          _select:
            transform: $employeesList($.extend.limit, $.extend.page, $.extend.search)
          _condition:
            transform: $.extend.formType = 'create'
          outputValue: value
        - type: text
          label: Employee
          disabled: true
          _value:
            transform: >-
              $.extend.defaultValue.employeeCode & ' - ' &
              $.extend.defaultValue.employeeRecordNumber & ' - ' &
              $.extend.defaultValue.employeeName
          _condition:
            transform: $.extend.formType = 'edit'
        - type: text
          name: employeeCode
          label: Employee
          unvisible: true
          _value:
            transform: $.fields.employee.employeeId
        - type: number
          name: employeeRecordNumber
          unvisible: true
          _value:
            transform: $string($.fields.employee.employeeRecordNumber)
        - type: text
          name: employeeIdObj
          unvisible: true
          _value:
            transform: $.variables._employeeIdObj
    - type: group
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - type: text
          label: Employee Name
          name: employeeName
        - type: text
          label: Employee Record
          name: employeeRecordNumber
        - type: text
          label: Company
          name: company
        - type: text
          label: Legal Entity
          name: legalEntityName
        - type: text
          label: Business Unit
          name: businessUnit
        - type: text
          label: Division
          name: division
        - type: text
          label: Department
          name: department
        - type: text
          label: Job Title
          name: jobTitleName
        - name: effectiveDateFrom
          label: Effective Date From
          type: dateRange
          setting:
            format: dd/MM/yyyy
            type: date
          mode: date-picker
        - name: effectiveDateTo
          label: Effective Date To
          type: dateRange
          setting:
            format: dd/MM/yyyy
            type: date
          mode: date-picker
        - type: radio
          label: Attendance Tracking
          name: attendanceTracking
          radio:
            - label: Miễn chấm công
              value: '1'
            - label: Sử dụng dữ liệu check in-check out
              value: '2'
    - type: group
      _condition:
        transform: $.extend.formType != 'view'
      n_cols: 2
      fields:
        - name: effectiveDateFrom
          label: Effective Date From
          type: dateRange
          validators:
            - type: required
          setting:
            format: dd/MM/yyyy
            type: date
          mode: date-picker
        - name: effectiveDateTo
          label: Effective Date To
          type: dateRange
          setting:
            format: dd/MM/yyyy
            type: date
          mode: date-picker
          validators:
            - type: ppx-custom
              args:
                transform: >-
                  $exists($.fields.effectiveDateTo) and
                  $DateDiff($.fields.effectiveDateTo,
                  $.fields.effectiveDateFrom, 'd') < 0
              text: Effective date to must be greater than effective date from
    - type: group
      fields:
        - type: radio
          label: Attendance Tracking
          name: attendanceTracking
          placeholder: Select Attendance Tracking
          _condition:
            transform: $.extend.formType = 'edit' or $.extend.formType = 'create'
          validators:
            - type: required
          radio:
            - label: Miễn chấm công
              value: '1'
            - label: Sử dụng dữ liệu check in-check out
              value: '2'
        - name: note
          label: Note
          type: textarea
          placeholder: Enter note
          validators:
            - type: maxLength
              args: '1000'
              text: Note should not exceed 1000 characters.
          textarea:
            autoSize:
              minRows: 3
            maxCharCount: 1000
  overview:
    dependentField: employeeIdObj
    border: true
    title: Employee Detail
    uri: >-
      /api/personals/:{employeeId}:/job-datas?filter%5B0%5D=employeeRecordNumber%7C%7C%24eq%7C%7C:{employeeRecordNumber}:
    display:
      - key: employeeRecordNumber
        label: Employee record number
      - key: companyName
        label: Company
      - key: legalEntityName
        label: Legal Entity
      - key: businessUnitName
        label: Business Unit
      - key: divisionName
        label: Division
      - key: departmentName
        label: Department
      - key: jobName
        label: Job
  footer:
    create: true
    update: true
    createdOn: createdAt
    updatedOn: updatedAt
    createdBy: createdBy
    updatedBy: updatedBy
  sources:
    employeesList:
      uri: '"/api/personals"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'filter':
        [{'field':'search','operator': '$eq','value':$.search}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label':
        $join($filter([$item.employeeId,$string($item.employeeRecordNumber),$item.fullName],
        $boolean), ' - ')  , 'value': {'employeeId': $item.employeeId,
        'employeeRecordNumber': $string($item.employeeRecordNumber),
        'jobDataId': $item.jobDataId}, 'employeeId': $item.employeeId,
        'employeeRecordNumber': $string($item.employeeRecordNumber),'jobDataId':
        $item.jobDataId}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
  variables:
    _employeeIdObj:
      transform: >-
        $.fields.employeeCode != '' ? {'employeeId': $.fields.employeeCode,
        'employeeRecordNumber': $.fields.employeeRecordNumber}
filter_config:
  fields:
    - type: selectAll
      name: employeeCode
      label: Employee
      placeholder: Select Employee
      mode: multiple
      labelType: type-grid
      isLazyLoad: true
      _options:
        transform: $employeesList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      name: companyCode
      label: Company
      placeholder: Select Company
      labelType: type-grid
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $companyList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      name: legalEntityCode
      label: Legal Entity
      placeholder: Select Legal Entity
      labelType: type-grid
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $legalEntityList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      name: businessUnitCode
      label: Business Unit
      placeholder: Select Business Unit
      labelType: type-grid
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $businessUnitList($.extend.limit, $.extend.page, $.extend.search)
    - type: selectAll
      name: divisionCode
      label: Division
      placeholder: Select Division
      labelType: type-grid
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $divisionList($.extend.limit, $.extend.page, $.extend.search)
    - name: departmentId
      label: Department
      type: selectAll
      placeholder: Select Department
      labelType: type-grid
      mode: multiple
      isLazyLoad: true
      _options:
        transform: >-
          $departmentsList($.extend.limit,
          $.extend.page,$.fields.effectiveDate,$.extend.defaultValue.id,$.extend.search)
    - name: jobTitleCode
      label: Job
      type: selectAll
      labelType: type-grid
      placeholder: Select Job Title
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $jobCodesList($.extend.limit, $.extend.page, $.extend.search)
    - name: effectiveDateFrom
      label: Effective Date From
      type: dateRange
      labelType: type-grid
      mode: date-picker
      placeholder: dd/MM/yyyy
      setting:
        format: dd/MM/yyyy
        type: date
    - name: effectiveDateTo
      label: Effective Date To
      type: dateRange
      labelType: type-grid
      mode: date-picker
      placeholder: dd/MM/yyyy
      setting:
        format: dd/MM/yyyy
        type: date
    - name: attendanceTracking
      label: Attendance Tracking
      placeholder: Select Attendance Tracking
      labelType: type-grid
      type: select
      mode: multiple
      select:
        - label: Miễn chấm công
          value: '1'
        - label: Sử dụng dữ liệu check in-check out
          value: '2'
    - type: selectAll
      name: createdBy
      label: Create By
      placeholder: Select Employee
      labelType: type-grid
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $userList()
    - type: dateRange
      label: Created On
      name: createdAt
      labelType: type-grid
    - type: select
      name: updatedBy
      label: Last Updated By
      placeholder: Select Employee
      labelType: type-grid
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $userList()
    - type: dateRange
      label: Last Updated On
      labelType: type-grid
      name: updatedAt
  filterMapping:
    - field: $
      operator: $in
      valueField: employeeCode.(value)
    - field: jobTitleCode
      operator: $in
      valueField: jobTitleCode.(value)
    - field: attendanceTracking
      operator: $in
      valueField: attendanceTracking.(value)
    - field: companyCode
      operator: $in
      valueField: companyCode.(value)
    - field: legalEntityCode
      operator: $in
      valueField: legalEntityCode.(value)
    - field: divisionCode
      operator: $in
      valueField: divisionCode.(value)
    - field: businessUnitCode
      operator: $in
      valueField: businessUnitCode.(value)
    - field: departmentId
      operator: $in
      valueField: departmentId.(value)
    - field: effectiveDateFrom
      operator: $gte
      valueField: effectiveDateFrom
    - field: effectiveDateTo
      operator: $lte
      valueField: effectiveDateTo
    - field: createdBy
      operator: $in
      valueField: createdBy.(value)
    - field: createdAt
      operator: $between
      valueField: createdAt
    - field: updatedBy
      operator: $in
      valueField: updatedBy.(value)
    - field: updatedAt
      operator: $between
      valueField: updatedAt
  sources:
    employeesList:
      uri: '"/api/personals"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.employeeId &' - '&
        $item.employeeRecordNumber &' - '& $item.name,'code': $item.employeeId,
        'value': {'employeeCode': $item.employeeId,'employeeRecordNumber':
        $string($item.employeeRecordNumber)}}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    divisionList:
      uri: '"/api/divisions"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    businessUnitList:
      uri: '"/api/business-units"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    companyList:
      uri: '"/api/companies"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    departmentsList:
      uri: '"/api/departments/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page, 'filter':
        [{'field':'search','operator':
        '$eq','value':$.search},{'field':'effectiveDate','operator':
        '$lte','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true}, {'field':'IdFilter','operator': '$ne','value':
        $.codeFilter}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'additionalData': $item}})[]
      disabledCache: true
      params:
        - limit
        - page
        - effectiveDate
        - codeFilter
        - search
    legalEntityList:
      uri: '"/api/legal-entities"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    jobCodesList:
      uri: '"/api/job-codes"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    userList:
      uri: '"/api/internal-users"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.userName, 'value':
        $item.userName}})
      disabledCache: true
layout_options:
  toolTable:
    export: true
    adjustDisplay: true
  tool_table:
    - id: export
      icon: icon-download-simple
  show_dialog_form_save_add_button: true
  show_detail_history: false
  export_all:
    type: base_total
  hide_action_row: true
  show_dialog_duplicate_button: false
  delete_multi_items: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: null
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    title: Edit
    icon: icon-pencil-simple
    type: ghost-gray
  - id: delete
    title: Delete
    icon: icon-trash
    type: ghost-gray
backend_url: /api/ts-setup-timekeeping-object-for-employees
screen_name: ts-setup-timekeeping-object-for-employees
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: companyCode
    defaultName: CompanyCode
  - name: departmentId
    defaultName: DepartmentCode
  - name: employeeCode
    defaultName: EmployeeId
  - name: employeeRecordNumber
    defaultName: EmployeeRecordNumber
  - name: jobTitleCode
    defaultName: JobCode
  - name: legalEntityCode
    defaultName: LegalEntityCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Setup Attendance Objects For Employee
  parent:
    title: Configuration
