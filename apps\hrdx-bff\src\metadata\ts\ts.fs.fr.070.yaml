id: TS.FS.FR.070
status: draft
sort: 99
user_created: 56e71b91-dd39-4ebc-afd1-b4f00f38d4a5
date_created: '2024-07-31T11:36:33.264Z'
user_updated: abd14398-1d53-4e6f-9834-26328f0ebde0
date_updated: '2025-06-18T09:37:13.388Z'
title: General TS Parameter Settings
requirement:
  time: 1746783680403
  blocks:
    - id: GTK8fk7CaG
      type: paragraph
      data:
        text: >-
          &nbsp;Chức năng đáp ứng các yêu cầu thêm mới, chỉnh sửa, xóa và tìm
          kiếm danh sách thiếp lập các tham số chung của phân hệ thuế
  version: 2.30.7
screen_design: null
module: TS
local_fields:
  - code: nationName
    title: Country
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    pinned: false
  - code: caparameterName
    title: Parameter
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
  - code: issuedDate
    title: Issue Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
  - code: applicationDate
    title: Valid From
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
  - code: value
    title: Value
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: unitName
    title: Unit
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: note
    title: Note
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
  - code: updatedBy
    title: Last Updated By
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: updatedAt
    title: Last Updated On
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
    show_sort: true
mock_data:
  - country: Việt Nam
    parameter: Giảm trừ cho bản thân
    dateIssued: '2020-07-01'
    dateOfApplication: '2020-01-01'
    value: 11,000,000
    unit: Tiền tệ
    note: Theo nghị quyết 954/2020/UBTVQH14
    currency: VND
    createdBy: ChauPV
    createdOn: '2024-01-01 14:05:00'
    lastUpdatedBy: ChauPV
    lastUpdatedOn: '2024-01-01 14:05:00'
  - country: Việt Nam
    parameter: Giảm trừ cho người phụ thuộc
    dateIssued: '2020-07-01'
    dateOfApplication: '2020-01-01'
    value: 4,400,000
    unit: Tiền tệ
    note: Theo nghị quyết 954/2020/UBTVQH14
    currency: VND
    createdBy: ChauPV
    createdOn: '2024-01-01 14:05:00'
    lastUpdatedBy: ChauPV
    lastUpdatedOn: '2024-01-01 14:05:00'
local_buttons: null
layout: layout-table
form_config:
  _formTitle:
    create: '''Add new General TS Parameter Settings'''
  fields:
    - name: nationName
      label: Country
      type: text
      _condition:
        transform: $.extend.formType = 'view'
    - type: group
      n_cols: 2
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - name: nationId
          label: Country
          type: select
          outputValue: value
          placeholder: Select country
          _condition:
            transform: '$.extend.formType = ''create'' or $.extend.formType = ''edit'' '
          _select:
            transform: $nationsList()
        - name: caparameterId
          label: Parameter
          type: select
          outputValue: value
          placeholder: Select Parameter
          _condition:
            transform: $not($.extend.formType = 'view')
          validators:
            - type: required
          _select:
            transform: >-
              ( $DateDiff($.fields.issuedDate, $.fields.applicationDate, 'd') <
              1 ? $caParametersList($.fields.issuedDate) :
              $caParametersList($.fields.applicationDate))
        - name: issuedDate
          label: Issue Date
          type: dateRange
          placeholder: Select date issued
          validators:
            - type: required
          setting:
            format: dd/MM/yyyy
            type: date
          mode: date-picker
        - name: applicationDate
          label: Valid From
          type: dateRange
          mode: date-picker
          placeholder: Select date of application
          validators:
            - type: required
          setting:
            format: dd/MM/yyyy
            type: date
    - name: issuedDate
      label: Issue Date
      type: dateRange
      placeholder: Select date issued
      setting:
        format: dd/MM/yyyy
        type: date
      mode: date-picker
      _condition:
        transform: $.extend.formType = 'view'
    - name: applicationDate
      label: Valid From
      type: dateRange
      mode: date-picker
      placeholder: Select date of application
      setting:
        format: dd/MM/yyyy
        type: date
      _condition:
        transform: $.extend.formType = 'view'
    - name: caparameterName
      label: Parameter
      type: text
      _condition:
        transform: $.extend.formType = 'view'
    - type: group
      n_cols: 2
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - name: value
          label: Value
          type: number
          placeholder: Enter value
        - name: unit
          label: Unit
          outputValue: value
          type: select
          placeholder: Select unit
          _select:
            transform: $unitList()
    - name: value
      label: Value
      type: number
      _condition:
        transform: $.extend.formType = 'view'
    - name: unit
      label: Unit
      outputValue: value
      type: select
      _select:
        transform: $unitList()
      _condition:
        transform: $.extend.formType = 'view'
    - name: note
      label: Note
      type: textarea
      placeholder: Enter note
      textarea:
        autoSize:
          minRows: 3
        maxCharCount: 1000
      validators:
        - type: maxLength
          args: 1000
    - type: group
      lastGroupStyleOff: true
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - name: createdAt
          type: dateRange
          label: Created On
          setting:
            format: dd/MM/yyyy HH:mm:ss
            type: date
          mode: date-picker
        - name: updatedAt
          type: dateRange
          label: Last Updated On
          setting:
            format: dd/MM/yyyy HH:mm:ss
            type: date
          mode: date-picker
  sources:
    caParametersList:
      uri: '"/api/ca-parameters/list-data"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - effectiveDate
    nationsList:
      uri: '"/api/picklists/COUNTRIES/values"'
      method: GET
      queryTransform: '{''filter'': [{''field'':''status'',''operator'': ''$eq'',''value'':true}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
    unitList:
      uri: '"/api/picklists/TSUNIT/values"'
      method: GET
      queryTransform: '{''filter'': [{''field'':''status'',''operator'': ''$eq'',''value'':true}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
  footer:
    create: true
    update: true
    createdOn: createdAt
    updatedOn: updatedAt
    createdBy: createdBy
    updatedBy: updatedBy
filter_config:
  fields:
    - name: nationId
      label: Country
      type: selectAll
      mode: multiple
      placeholder: Select Country
      labelType: type-grid
      isLazyLoad: true
      _options:
        transform: $nationsList($.extend.limit, $.extend.page, $.extend.search)
    - name: caparameterId
      label: Parameter
      type: select
      labelType: type-grid
      mode: multiple
      placeholder: Select Parameter
      _select:
        transform: $caParametersList()
    - name: issuedDate
      label: Issue Date
      type: dateRange
      labelType: type-grid
      placeholder: dd/MM/yyyy
      setting:
        format: dd/MM/yyyy
        type: date
    - name: applicationDate
      label: Valid From
      type: dateRange
      labelType: type-grid
      placeholder: dd/MM/yyyy
      setting:
        format: dd/MM/yyyy
        type: date
    - name: unit
      label: Unit
      type: select
      mode: multiple
      labelType: type-grid
      placeholder: Select Unit
      _select:
        transform: $unitList()
    - name: createdBy
      label: Created By
      type: select
      labelType: type-grid
      placeholder: Select Creator
      _select:
        transform: $.variables._userList
    - name: createdAt
      label: Created On
      type: dateRange
      labelType: type-grid
      placeholder: dd/MM/yyyy
      setting:
        format: dd/MM/yyyy
        type: date
    - name: updatedBy
      label: Last Updated By
      labelType: type-grid
      type: select
      placeholder: Select Editor
      _select:
        transform: $.variables._userList
    - name: updatedAt
      label: Last Updated On
      type: dateRange
      labelType: type-grid
      placeholder: dd/MM/yyyy
      setting:
        format: dd/MM/yyyy
        type: date
  filterMapping:
    - field: nationId
      operator: $in
      valueField: nationId.(value)
    - field: caparameterId
      operator: $in
      valueField: caparameterId.(value)
    - field: issuedDate
      operator: $between
      valueField: issuedDate
    - field: applicationDate
      operator: $between
      valueField: applicationDate
    - field: unit
      operator: $in
      valueField: unit.(value)
    - field: createdBy
      operator: $cont
      valueField: createdBy.(value)
    - field: createdAt
      operator: $between
      valueField: createdAt
    - field: updatedBy
      operator: $in
      valueField: updatedBy.(value)
    - field: updatedAt
      operator: $between
      valueField: updatedAt
  sources:
    caParametersList:
      uri: '"/api/ca-parameters/by"'
      method: GET
      queryTransform: '{''filter'': [{''field'':''status'',''operator'': ''$eq'',''value'':true}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default  & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
    nationsList:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    unitList:
      uri: '"/api/picklists/UNIT/values"'
      method: GET
      queryTransform: '{''filter'': [{''field'':''status'',''operator'': ''$eq'',''value'':true}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
    userList:
      uri: '"/api/internal-users"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.userName, 'value':
        $item.userName}})
      disabledCache: true
  variables:
    _userList:
      transform: $userList()
layout_options:
  show_dialog_form_save_add_button: true
  show_history_insert_button: false
  show_detail_history: false
  tool_table:
    - id: export
      icon: icon-upload-simple-bold
  export_all:
    type: base_total
  hide_action_row: true
  show_dialog_duplicate_button: false
  delete_multi_items: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: icon-plus-bold
    type: primary
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    title: Edit
    icon: icon-pencil-simple-bold
    type: ghost-gray
  - id: delete
    title: Delete
    icon: icon-trash-bold
    group: null
    type: ghost-gray
backend_url: /api/ts-parameters
screen_name: ts-parameters
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: nationId
    defaultName: CountryCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: General TS Parameter Settings
  parent:
    title: Configuration
