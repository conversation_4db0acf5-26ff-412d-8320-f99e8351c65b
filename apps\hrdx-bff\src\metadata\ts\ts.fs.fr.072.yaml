id: TS.FS.FR.072
status: draft
sort: 299
user_created: 56e71b91-dd39-4ebc-afd1-b4f00f38d4a5
date_created: '2024-07-31T11:36:00.827Z'
user_updated: abd14398-1d53-4e6f-9834-26328f0ebde0
date_updated: '2025-06-19T03:55:53.587Z'
title: List of Seniority Leave Conversion Rules
requirement:
  time: 1741920049162
  blocks:
    - id: YlfYfyY0jm
      type: paragraph
      data:
        text: >-
          Thi<PERSON><PERSON> lập nguyên tắc tính phép thâm niên cho cơ cấu theo quy định của
          Tập đoàn/ CTTV
    - id: FiQ06irNab
      type: paragraph
      data:
        text: Chuyển từ mã&nbsp;TS.FS.FR.070 sang&nbsp;TS.FS.FR.072
  version: 2.30.7
screen_design: null
module: TS
local_fields:
  - code: country
    pinned: false
    title: Country
    description: Load dữ liệu Quốc gia theo dữ liệu đã tạo
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 10
    options__tabular__align: left
    show_sort: false
    resizable: false
    dragable: false
  - code: group
    pinned: false
    title: Group
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 10
    options__tabular__align: left
    show_sort: false
  - code: company
    pinned: false
    title: Company
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 10
    options__tabular__align: left
    show_sort: false
  - code: legalEntity
    pinned: false
    title: Legal Entity
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 10
    options__tabular__align: left
    dragable: false
    show_sort: false
    resizable: false
  - code: effectiveDate
    pinned: false
    title: Effective Start Date
    data_type:
      key: dd/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/YYYY
      collection: field_types
    options__tabular__column_width: 10
    options__tabular__align: left
    show_sort: false
  - code: effectiveDateTo
    title: Effective End Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
  - code: tssetPrincipalOfSeniority
    title: The seniority conversion rule
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 20
    options__tabular__align: left
    show_sort: false
  - code: note
    title: Note
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 10
    options__tabular__align: left
    show_sort: false
  - code: updatedBy
    title: Last Updated By
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 10
    options__tabular__align: left
    show_sort: false
  - code: updatedAt
    title: Last Updated On
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
    options__tabular__column_width: 10
    options__tabular__align: left
    show_sort: false
mock_data:
  - country: '00000001'
    countryName: Việt Nam
    group: '00000001'
    groupName: FPT
    company: '00000001'
    companyName: FPT Corporation
    legalEntity: '00000002'
    legalEntityName: FIS HCM
    effectiveDate: 2024/01/01
    seniorityConversionRule: '00000001'
    seniorityConversionRuleName: Nguyên tắc phép thâm niên theo 3 năm
    note: Nội dung
    creator: Phương Bùi
    createTime: 01/04/2024
    lastEditer: Phương Bùi
    lastEditTime: 01/04/2024
  - country: '00000001'
    countryName: Việt Nam
    group: '00000001'
    groupName: FPT
    company: '00000002'
    companyName: FPT SOFTWARE
    legalEntity: ''
    legalEntityName: ''
    effectiveDate: 2024/04/01
    seniorityConversionRule: '00000001'
    seniorityConversionRuleName: Nguyên tắc phép thâm niên theo 3 năm
    note: Nội dung
    creator: Phương Bùi
    createTime: 01/04/2024
    lastEditer: Phương Bùi
    lastEditTime: 01/04/2024
  - country: '00000001'
    countryName: Việt Nam
    group: '00000001'
    groupName: FPT
    company: '00000003'
    companyName: FPT IS
    legalEntity: ''
    legalEntityName: ''
    effectiveDate: 2024/04/01
    seniorityConversionRule: '00000002'
    seniorityConversionRuleName: Nguyên tắc phép thâm niên theo 5 năm
    note: Nội dung
    creator: Phương Bùi
    createTime: 01/04/2024
    lastEditer: Phương Bùi
    lastEditTime: 01/04/2024
local_buttons: null
layout: layout-table
form_config:
  formSize:
    create: small
    edit: small
    view: small
  fields:
    - type: group
      n_cols: 2
      _condition:
        transform: $.extend.formType != 'view'
      fields:
        - name: countryId
          label: Country
          type: select
          clearFieldsAfterChange:
            - tssetPrincipalOfSeniorityId
          outputValue: value
          placeholder: Enter Country
          _select:
            transform: $nationsList()
        - name: groupId
          label: Group
          type: select
          outputValue: value
          clearFieldsAfterChange:
            - companyId
            - legalEntityId
          placeholder: Enter Group
          _select:
            transform: $.variables._groupsList
        - name: companyId
          label: Company
          type: select
          outputValue: value
          clearFieldsAfterChange:
            - legalEntityId
          placeholder: Enter Company
          _select:
            transform: $.variables._companiesList
        - name: legalEntityId
          label: Legal Entity
          type: select
          outputValue: value
          placeholder: Enter Legal Entity
          _select:
            transform: $.variables._legalEntityList
        - type: dateRange
          label: Effective Start Date
          mode: date-picker
          name: effectiveDate
          _value:
            transform: $.extend.formType = 'create' ? $now()
          setting:
            format: dd/MM/yyyy
            type: date
          validators:
            - type: required
        - type: dateRange
          name: effectiveDateTo
          label: Effective End Date
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
            autoFill: end-off
            _disabledDate:
              transform: '{''operator'': ''$lt'', ''value'': $.fields.effectiveDate}'
          validators:
            - type: ppx-custom
              args:
                transform: >-
                  $exists($.fields.effectiveDate) and
                  $exists($.fields.effectiveDateTo) and
                  $DateDiff($DateFormat($.fields.effectiveDate, 'yyyy-MM-DD'),
                  $DateFormat($.fields.effectiveDateTo, 'yyyy-MM-DD'), 'd') > 0
              text: Effective end date must be greater than effective start date.
        - name: tssetPrincipalOfSeniorityId
          label: Seniority Leave Conversion Rules
          type: select
          outputValue: value
          col: 2
          placeholder: Enter Seniority Leave Conversion Rules
          validators:
            - type: required
          _select:
            transform: >-
              $seniorityConversionRuleList($.fields.effectiveDate,
              $.fields.countryId)
    - type: group
      n_cols: 1
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - name: country
          label: Country
          type: text
        - name: group
          label: Group
          type: text
        - name: company
          label: Company
          type: text
        - name: legalEntity
          label: Legal Entity
          type: text
        - type: dateRange
          label: Effective Start Date
          name: effectiveDate
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
        - type: dateRange
          label: Effective End Date
          name: effectiveDateTo
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
        - name: tssetPrincipalOfSeniority
          label: Seniority Leave Conversion Rules
          type: text
    - name: note
      label: Note
      type: textarea
      placeholder: Enter note
      validators:
        - type: maxLength
          args: '1000'
          text: Note should not exceed 1000 characters.
      textarea:
        autoSize:
          minRows: 3
        maxCharCount: 1000
  footer:
    create: true
    update: true
    createdOn: createdAt
    updatedOn: updatedAt
    createdBy: createdBy
    updatedBy: updatedBy
  sources:
    nationsList:
      uri: '"/api/picklists/COUNTRIES/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default& ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
    groupsList:
      uri: '"/api/groups/get-by"'
      queryTransform: >-
        {'page':1,'limit':10000,'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - effectiveDate
    companiesList:
      uri: '"/api/companies/get-by"'
      queryTransform: >-
        {'page':1,'limit':10000,'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true},{'field':'groupCode','operator': '$eq','value':
        $.groupId }]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - effectiveDate
        - groupId
    legalEntityList:
      uri: '"/api/legal-entities/get-list"'
      queryTransform: >-
        {'page':1,'limit':10000,'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true},{'field':'companyCode','operator': '$eq','value':
        $.companyId }]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - effectiveDate
        - companyId
    seniorityConversionRuleList:
      uri: '"/api/ts-set-principal-of-seniorities/list-data"'
      method: GET
      queryTransform: >-
        {'page': 1,'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'nationId','operator':
        '$eq','value': $.countryId}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - effectiveDate
        - countryId
  variables:
    _groupsList:
      transform: $groupsList($.fields.effectiveDate)
    _companiesList:
      transform: >-
        $.fields.groupId ? $companiesList($.fields.effectiveDate,
        $.fields.groupId)
    _legalEntityList:
      transform: >-
        $.fields.companyId ? $legalEntityList($.fields.effectiveDate,
        $.fields.companyId)
filter_config:
  fields:
    - name: countryId
      label: Country
      type: selectAll
      placeholder: Enter Country
      labelType: type-grid
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $nationsList($.extend.limit, $.extend.page, $.extend.search)
    - name: groupId
      label: Group
      type: selectAll
      placeholder: Enter Group
      labelType: type-grid
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $groupsList($.extend.limit, $.extend.page, $.extend.search)
    - name: companyId
      label: Company
      type: selectAll
      placeholder: Enter Company
      labelType: type-grid
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $companiesList($.extend.limit, $.extend.page, $.extend.search)
    - name: legalEntityId
      label: Legal Entity
      type: selectAll
      placeholder: Enter Legal Entity
      labelType: type-grid
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $legalEntitiesList($.extend.limit, $.extend.page, $.extend.search)
    - type: dateRange
      label: Effective date
      name: effectiveDate
      labelType: type-grid
      setting:
        format: dd/MM/yyyy
        type: date
    - name: tssetPrincipalOfSeniorityId
      label: Seniority conversion rule
      type: selectAll
      placeholder: Enter seniority conversion rule
      labelType: type-grid
      mode: multiple
      isLazyLoad: true
      _options:
        transform: >-
          $seniorityConversionRuleList($.extend.limit, $.extend.page,
          $.extend.search)
    - type: select
      name: createdBy
      label: Create By
      placeholder: Select Employee
      labelType: type-grid
      mode: multiple
      _select:
        transform: $userList()
    - type: dateRange
      label: Created On
      labelType: type-grid
      name: createdAt
    - type: select
      name: updatedBy
      label: Last Updated By
      placeholder: Select Employee
      labelType: type-grid
      mode: multiple
      _select:
        transform: $userList()
    - type: dateRange
      label: Last Updated On
      labelType: type-grid
      name: updatedAt
  filterMapping:
    - field: countryId
      operator: $in
      valueField: countryId.(value)
    - field: groupId
      operator: $in
      valueField: groupId.(value)
    - field: companyId
      operator: $in
      valueField: companyId.(value)
    - field: legalEntityId
      operator: $in
      valueField: legalEntityId.(value)
    - field: effectiveDate
      operator: $between
      valueField: effectiveDate
    - field: tssetPrincipalOfSeniorityId
      operator: $in
      valueField: tssetPrincipalOfSeniorityId.(value)
    - field: createdBy
      operator: $in
      valueField: createdBy.(value)
    - field: createdAt
      operator: $between
      valueField: createdAt
    - field: updatedBy
      operator: $in
      valueField: updatedBy.(value)
    - field: updatedAt
      operator: $between
      valueField: updatedAt
  sources:
    nationsList:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default& ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    groupsList:
      uri: '"/api/groups/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & '( ' &
        $item.code & ' )', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    companiesList:
      uri: '"/api/companies/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & '( ' &
        $item.code & ' )', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    legalEntitiesList:
      uri: '"/api/legal-entities/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & '( ' &
        $item.code & ' )', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    seniorityConversionRuleList:
      uri: '"/api/ts-set-principal-of-seniorities"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    userList:
      uri: '"/api/internal-users"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.userName, 'value':
        $item.userName}})
      disabledCache: true
layout_options:
  tool_table:
    - id: export
      icon: icon-download-simple
  show_dialog_form_save_add_button: true
  show_detail_history: false
  export_all:
    type: base_total
  hide_action_row: true
  show_dialog_duplicate_button: false
  delete_multi_items: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    title: Edit
    icon: icon-pencil-simple
  - id: delete
    title: Delete
    icon: icon-trash
backend_url: /api/ts-setting-principle-for-calculating-seniorities
screen_name: ts-setting-princiaple-for-calculating-seniorities
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: companyId
    defaultName: CompanyCode
  - name: countryId
    defaultName: CountryCode
  - name: legalEntityId
    defaultName: LegalEntityCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: List of Seniority Leave Conversion Rules
  parent:
    title: Configuration
