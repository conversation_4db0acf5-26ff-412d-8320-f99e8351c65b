id: TS.FS.FR.112
status: draft
sort: null
user_created: abd14398-1d53-4e6f-9834-26328f0ebde0
date_created: '2025-04-26T02:21:08.283Z'
user_updated: abd14398-1d53-4e6f-9834-26328f0ebde0
date_updated: '2025-06-20T07:56:16.512Z'
title: Check The Holiday Schedule And Work Schedule
requirement:
  time: 1747816212478
  blocks:
    - id: PaXEx9JTgy
      type: paragraph
      data:
        text: Check The Holiday Schedule And Work Schedule
  version: 2.30.7
screen_design: null
module: TS
local_fields:
  - code: employeeInfo
    pinned: true
    title: Employee
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 14
  - code: typeName
    title: Schedule
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: scheduleCode
    title: Schedule Code
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: scheduleName
    title: Schedule Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: effectiveDateFrom
    title: Effective Start Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
  - code: effectiveDateTo
    title: Effective End Date
    data_type:
      key: DD/MM/yyyy
      collection: data_types
    display_type:
      key: DD/MM/yyyy
      collection: field_types
  - code: countryName
    title: Country
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: groupName
    title: Group
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: companyName
    title: Company
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: payGroupName
    title: Pay Group
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: legalEntityName
    title: Legal Entity
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: businessUnitName
    title: Business Unit
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: divisionName
    title: Division
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: departmentName
    title: Department
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: nationName
    title: Nationality
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: locationName
    title: Location
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
mock_data: null
local_buttons: null
layout: layout-table
form_config: {}
filter_config:
  fields:
    - type: group
      n_cols: 4
      fields:
        - name: countryCode
          label: Country
          type: select
          placeholder: Select Country
          isLazyLoad: true
          outputValue: value
          _select:
            transform: $nationsList($.extend.limit, $.extend.page, $.extend.search)
        - type: select
          name: groupCode
          isLazyLoad: true
          label: Group
          outputValue: value
          placeholder: Select Group
          _select:
            transform: $groupsList($.extend.limit,$.extend.page,$.extend.search)
        - name: companyCode
          label: Company
          type: select
          placeholder: Select Company
          outputValue: value
          isLazyLoad: true
          _select:
            transform: $companiesList($.extend.limit, $.extend.page, $.extend.search)
        - type: select
          label: Paygroup
          dependantField: $.fields.companyCode
          name: paygroupCode
          outputValue: value
          isLazyLoad: true
          _select:
            transform: >-
              $payGroupsList($.extend.limit, $.extend.page, $.extend.search,
              $.fields.companyCode)
        - type: select
          label: Legal Entity
          name: legalEntityCode
          isLazyLoad: true
          dependantField: $.fields.companyCode
          outputValue: value
          _select:
            transform: >-
              $legalEntitiesList($.extend.limit, $.extend.page, $.extend.search,
              $.fields.companyCode)
        - type: select
          label: Bussiness Unit
          isLazyLoad: true
          name: businessUnit
          placeholder: Select Bussiness Unit
          outputValue: value
          _select:
            transform: $businessUnitList($.extend.limit, $.extend.page, $.extend.search)
        - type: select
          name: divisionCode
          label: Division
          placeholder: Select Department
          isLazyLoad: true
          outputValue: value
          _select:
            transform: >-
              $divisionsList($.extend.limit, $.extend.page,
              $.extend.search,$DateFormat($now(),'YYYY-MM-DDTHH:mm:ss'))
        - type: select
          name: departmentCode
          label: Department
          placeholder: Select Department
          isLazyLoad: true
          outputValue: value
          _select:
            transform: $departmentsList($.extend.limit, $.extend.page, $.extend.search)
        - type: select
          label: Nationality
          name: nationalityCode
          outputValue: value
          placeholder: Select Nationality
          isLazyLoad: true
          _select:
            transform: $nationalityList($.extend.limit, $.extend.page, $.extend.search)
        - type: select
          label: Location
          isLazyLoad: true
          name: locationCode
          placeholder: Select Location
          outputValue: value
          _select:
            transform: $locationsList($.extend.limit, $.extend.page, $.extend.search)
        - type: group
          col: 2
          n_cols: 2
          fields:
            - type: select
              label: Employee
              name: employee
              isLazyLoad: true
              outputValue: value
              _select:
                transform: $personalsList($.extend.limit, $.extend.page, $.extend.search)
              placeholder: Select Employee
        - type: group
          col: 4
          n_cols: 4
          fields:
            - type: dateRange
              label: Start Date
              name: startDate
              placeholder: dd/MM/yyyy
              mode: date-picker
              setting:
                format: dd/MM/yyyy
                type: date
              validators:
                - type: required
            - type: dateRange
              label: End Date
              name: endDate
              placeholder: dd/MM/yyyy
              validators:
                - type: required
                - type: ppx-custom
                  args:
                    transform: >-
                      $exists($.fields.endDate) and $exists($.fields.startDate)
                      and $DateDiff($DateFormat($.fields.endDate, 'yyyy-MM-DD'),
                      $DateFormat($.fields.startDate, 'yyyy-MM-DD'), 'd') < 1
                  text: End date must be greater than start date
              mode: date-picker
              setting:
                format: dd/MM/yyyy
                type: date
                autoFill: end-of
        - type: radio
          label: Schedule
          name: scheduleType
          col: 4
          radio:
            - value: null
              label: Lịch chung
            - value: Holiday
              label: Lịch nghỉ lễ
            - value: WorkSchedule
              label: Lịch làm việc
            - value: Leave
              label: Lịch nghỉ
  filterMapping:
    - field: countryCode
      operator: $eq
      valueField: countryCode
    - field: groupCode
      operator: $eq
      valueField: groupCode
    - field: companyCode
      operator: $eq
      valueField: companyCode
    - field: paygroupCode
      operator: $eq
      valueField: paygroupCode
    - field: legalEntityCode
      operator: $eq
      valueField: legalEntityCode
    - field: businessUnit
      operator: $eq
      valueField: businessUnit
    - field: divisionCode
      operator: $eq
      valueField: divisionCode
    - field: departmentCode
      operator: $eq
      valueField: departmentCode
    - field: locationCode
      operator: $eq
      valueField: locationCode
    - field: employeeID
      operator: $in
      valueField: employeeID
    - field: startDate
      operator: $gte
      valueField: startDate
    - field: endDate
      operator: $lte
      valueField: endDate
    - field: scheduleType
      operator: $eq
      valueField: scheduleType
  sources:
    nationsList:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    groupsList:
      uri: '"/api/groups/get-by"'
      method: GET
      queryTransform: >-
        {'limit':$.limit,'page':$.page,'filter': [{'field':'status','operator':
        '$eq','value':true},{'field':'search','operator':
        '$eq','value':$.search}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    payGroupsList:
      uri: '"/api/pay-group-structures"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator':
        '$eq','value':true},{'field':'companyCode','operator': '$eq','value':
        $.companyCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - companyCode
    companiesList:
      uri: '"/api/companies"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    legalEntitiesList:
      uri: '"/api/legal-entities"'
      method: GET
      queryTransform: >-
        { 'limit': $.limit, 'page': $.page, 'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true},
        {'field':'companyCode', 'operator': '$eq','value':$.companyCode}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - companyCode
    businessUnitList:
      uri: '"/api/business-units"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    divisionsList:
      uri: '"/api/divisions"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    departmentsList:
      uri: '"/api/departments/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit, 'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.id}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    locationsList:
      uri: '"/api/locations"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    personalsList:
      uri: '"/api/personals"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label':
        $join($filter([$item.employeeId,$string($item.employeeRecordNumber),$item.fullName],
        $boolean), ' - ') , 'value':{'EmployeeCode':$item.employeeId ,
        'EmployeeRecordNumber': $item.employeeRecordNumber,'empGroup' :
        $.item.employeeGroup} }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    nationalityList:
      uri: '"/api/picklists/NATIONALITY/values"'
      method: GET
      queryTransform: '{''limit'': $.limit,''page'': $.page,''search'': $.search}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
layout_options:
  show_detail_history: false
  expand_filter: true
  show_create_data_table: false
  custom_get_list_api:
    method: POST
    url: /api/ts-calendar/filter
  show_table_checkbox: true
  tool_table:
    - id: calendar
      icon: icon-calendar-bold
      _disabled:
        transform: $not($boolean($count($.selectedItems)))
  schedule_config:
    api:
      url: /api/ts-calendar
      method: POST
      bodyTransform: >-
        {'employeeRecordNumber':
        $.extend.filterValue.employee.EmployeeRecordNumber, 'employeeCode':
        $.extend.filterValue.employee.EmployeeCode, 'startDate':
        $.selectedDateRange[0], 'endDate': $.selectedDateRange[1],
        'calendarTypeQuery': $map($.extend.selectedItems, function($v) {{'code':
        $v.scheduleCode, 'type': $v.type}})[]}
    calendarSettings:
      viewType: period
  disabled_click_row: true
  tool_table_context_data_options:
    parent_data: true
    selected_items: true
layout_options__header_buttons: null
options: null
create_form: null
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: calendar
    title: View Schedule
    icon: icon-calendar-bold
    type: secondary
backend_url: /api/ts-calendar
screen_name: null
layout_options__actions_many:
  - id: calendar
    title: View Schedule
    icon: icon-calendar-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key: null
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Check The Holiday Schedule And Work Schedule
  parent:
    title: Holiday
