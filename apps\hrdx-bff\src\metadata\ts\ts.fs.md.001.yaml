id: TS.FS.MD.001
status: draft
sort: 260
user_created: 2aa5db12-fa54-46d6-8e05-9e421858de0c
date_created: '2024-07-10T06:51:58.114Z'
user_updated: abd14398-1d53-4e6f-9834-26328f0ebde0
date_updated: '2025-06-18T09:40:31.401Z'
title: Holiday Type
requirement:
  time: 1747015509778
  blocks:
    - id: u52SbLb8CG
      type: paragraph
      data:
        text: Qu<PERSON>n lý danh mục lo<PERSON> ng<PERSON> lễ
  version: 2.30.7
screen_design: null
module: TS
local_fields:
  - code: code
    title: Holiday Type Code
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    description: Code for the type of holiday
    pinned: true
    show_sort: true
    options__tabular__column_width: 11.5
  - code: nationName
    title: Country
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    options__tabular__column_width: 8.8125
  - code: shortName
    title: Short Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    options__tabular__column_width: 8
  - code: longName
    title: Long Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    options__tabular__column_width: 12.5
  - code: otMode
    title: Overtime Mode
    data_type:
      key: String
      collection: data_types
    display_type:
      key: OtMoteLabel
      collection: field_types
    options__tabular__column_width: 11.25
  - code: status
    title: Status
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Boolean Tag
      collection: field_types
    options__tabular__column_width: 6.25
  - code: updatedAt
    title: Last Updated On
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
    options__tabular__column_width: 10.25
  - code: updatedBy
    title: Last Updated By
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    options__tabular__column_width: 10.25
mock_data:
  - typeHolidayCode: '00000001'
    country: Việt Nam
    shortName:
      default: NL
      english: NL
      vietnamese: NL
    longName:
      default: Holiday
      english: National Holiday
      vietnamese: Ngày lễ
    overtimeMode: OT ngày lễ
    status: Active
    lastUpdatedOn: '2024-06-22 10:00:00'
    lastUpdatedBy: Khanh Vy
  - typeHolidayCode: '00000002'
    country: Việt Nam
    shortName:
      default: NNHT
      english: NNHT
      vietnamese: NNHT
    longName:
      default: Weekly Rest
      english: Weekly Rest Day
      vietnamese: Ngày nghỉ hàng tuần
    overtimeMode: OT ngày nghỉ
    status: Active
    lastUpdatedOn: '2024-05-09 10:00:00'
    lastUpdatedBy: Khanh Vy
  - typeHolidayCode: '00000003'
    country: Việt Nam
    shortName:
      default: NBL
      english: NBL
      vietnamese: NBL
    longName:
      default: Compensation
      english: Compensatory Day Off
      vietnamese: Nghỉ bù lễ
    overtimeMode: OT ngày nghỉ
    status: Active
    lastUpdatedOn: '2024-05-09 10:00:00'
    lastUpdatedBy: Khanh Vy
  - typeHolidayCode: '00000004'
    country: Việt Nam
    shortName:
      default: NCHL
      english: NCHL
      vietnamese: NCHL
    longName:
      default: Paid Leave
      english: Paid Day Off
      vietnamese: Nghỉ có hưởng lương
    overtimeMode: OT ngày thường
    status: Inactive
    lastUpdatedOn: '2024-05-09 10:00:00'
    lastUpdatedBy: Khanh Vy
local_buttons: null
layout: layout-table
form_config:
  formSize:
    create: large
    edit: large
  formTitle:
    create: Add New Holiday Type
    edit: Edit Holiday Type
  fields:
    - type: group
      n_cols: 2
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - name: code
          label: Holiday Type Code
          placeholder: System-generated
          type: text
          disabled: true
        - name: nationId
          label: Country
          type: select
          outputValue: value
          placeholder: Select Country
          _select:
            transform: $nationsList()
    - name: shortName
      label: Short Name
      type: translation
      _condition:
        transform: $not($.extend.formType = 'view')
      placeholder: Enter Short Name
      validators:
        - type: required
        - type: maxLength
          args: '300'
          text: Maximum length is 300 characters.
    - name: longName
      label: Long Name
      type: translation
      _condition:
        transform: $not($.extend.formType = 'view')
      placeholder: Enter Long Name
      validators:
        - type: required
        - type: maxLength
          args: '500'
          text: Maximum length is 500 characters.
    - name: otMode
      label: Overtime Mode
      type: select
      placeholder: Select Overtime Mode
      _condition:
        transform: $not($.extend.formType = 'view')
      outputValue: value
      validators:
        - type: required
      select:
        - label: Overtime on holidays
          value: '1'
        - label: Overtime on days off
          value: '2'
        - label: Overtime on weekdays
          value: '3'
    - type: group
      n_cols: 2
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - type: dateRange
          name: effectiveDate
          label: Effective Date
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
          validators:
            - type: required
        - type: radio
          label: Status
          name: status
          _value:
            transform: $.extend.formType = 'create' ? true
          radio:
            - value: true
              label: Active
            - value: false
              label: Inactive
    - name: code
      label: Holiday Type Code
      placeholder: System-generated
      type: text
      _condition:
        transform: $.extend.formType = 'view'
    - name: nationId
      label: Country
      type: select
      _condition:
        transform: $.extend.formType = 'view'
      _select:
        transform: $nationsList()
    - name: shortName
      label: Short Name
      type: translation
      _condition:
        transform: $.extend.formType = 'view'
    - name: longName
      label: Long Name
      type: translation
      _condition:
        transform: $.extend.formType = 'view'
    - name: otMode
      label: Overtime Mode
      type: select
      _condition:
        transform: $.extend.formType = 'view'
      select:
        - label: Overtime on holidays
          value: '1'
        - label: Overtime on days off
          value: '2'
        - label: Overtime on weekdays
          value: '3'
    - type: radio
      label: Status
      name: status
      _condition:
        transform: $.extend.formType = 'view'
      radio:
        - value: true
          label: Active
        - value: false
          label: Inactive
    - type: translationTextArea
      label: Note
      name: note
      placeholder: Enter Note
      textarea:
        maxCharCount: 1000
        autoSize:
          minRows: 3
      validators:
        - type: maxLength
          args: '1000'
          text: Maximum length is 1000 characters.
  sources:
    nationsList:
      uri: '"/api/picklists/COUNTRIES/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
  historyHeaderTitle: '''Holiday Type: '' & $.longName.default & '' ('' & $.code & '' )'''
filter_config:
  fields:
    - name: nationId
      label: Country
      type: selectAll
      labelType: type-grid
      mode: multiple
      placeholder: Select Country
      isLazyLoad: true
      _options:
        transform: $nationsList($.extend.limit, $.extend.page, $.extend.search)
    - type: radio
      label: Status
      name: status
      labelType: type-grid
      value: ''
      radio:
        - label: All
          value: ''
        - label: Active
          value: true
        - label: Inactive
          value: false
    - name: shortName
      label: Short Name
      labelType: type-grid
      type: text
      placeholder: Enter Short Name
    - name: longName
      label: Long Name
      labelType: type-grid
      type: text
      placeholder: Enter Long Name
    - name: otMode
      label: Overtime Mode
      type: select
      mode: multiple
      labelType: type-grid
      placeholder: Select Overtime Mode
      select:
        - label: Overtime on holidays
          value: '1'
        - label: Overtime on days off
          value: '2'
        - label: Overtime on weekdays
          value: '3'
    - name: updatedBy
      label: Last Updated By
      type: select
      mode: multiple
      labelType: type-grid
      placeholder: Select Editor
      _select:
        transform: $userList()
    - type: dateRange
      labelType: type-grid
      label: Last Updated On
      name: updatedAt
      setting:
        format: dd/MM/yyyy
        type: date
  filterMapping:
    - field: nationId
      operator: $in
      valueField: nationId.(value)
    - field: otMode
      operator: $in
      valueField: otMode.(value)
    - field: status
      operator: $eq
      valueField: status
    - field: shortNameFilter
      operator: $cont
      valueField: shortName
    - field: longNameFilter
      operator: $cont
      valueField: longName
    - field: updatedBy
      operator: $in
      valueField: updatedBy.(value)
    - field: updatedAt
      operator: $between
      valueField: updatedAt
  sources:
    nationsList:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    userList:
      uri: '"/api/internal-users"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.userName, 'value':
        $item.userName}})
      disabledCache: true
    holidaysList:
      uri: '"/api/ca-holiday-types/by"'
      method: GET
      queryTransform: '{''filter'': [{''field'':''status'',''operator'': ''$eq'',''value'':true}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $
      disabledCache: true
  variables:
    _holidaysList:
      transform: $holidaysList()
    _shortNameList:
      transform: >-
        $map($.variables._holidaysList, function($item) {{'label':
        $item.shortName.default, 'value': $item.shortName.default}})[]
    _longNameList:
      transform: >-
        $map($.variables._holidaysList, function($item) {{'label':
        $item.longName.default, 'value': $item.longName.default}})[]
layout_options:
  show_dialog_form_save_add_button: true
  show_detail_drawer: false
  toolTable:
    adjustDisplay: 'true'
  tool_table:
    - id: export
      icon: icon-download-simple
  history_widget_header_options:
    duplicate: false
  export_all:
    type: base_total
  hide_action_row: true
  delete_multi_items: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    title: Edit
    icon: icon-pencil-simple
    type: ghost-gray
  - id: delete
    title: Delete
    icon: icon-trash
    group: null
    type: ghost-gray
backend_url: /api/ca-holiday-types
screen_name: null
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: nationId
    defaultName: CountryCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Holiday Type
  parent:
    title: Manage work schedule
