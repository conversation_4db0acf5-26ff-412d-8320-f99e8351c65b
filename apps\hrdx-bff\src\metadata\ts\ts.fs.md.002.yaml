id: TS.FS.MD.002
status: draft
sort: 236
user_created: 2aa5db12-fa54-46d6-8e05-9e421858de0c
date_created: '2024-07-10T02:53:09.701Z'
user_updated: abd14398-1d53-4e6f-9834-26328f0ebde0
date_updated: '2025-07-14T09:21:30.645Z'
title: Holiday
requirement:
  time: 1746587800626
  blocks:
    - id: cpTLsotSFA
      type: paragraph
      data:
        text: Quản lý danh mục ngày lễ
  version: 2.30.7
screen_design: null
module: TS
local_fields:
  - code: code
    title: Holiday Code
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    pinned: true
    show_sort: true
  - code: nationName
    title: Country
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: companyName
    title: Company
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: shortName
    title: Short Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
  - code: longName
    title: Long Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    options__tabular__column_width: 12
  - code: caHolidayTypeName
    title: Holiday Type
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: holidayStartDate
    title: Holiday Start Date
    data_type:
      key: String
      collection: data_types
    display_type:
      key: DD/MM
      collection: field_types
    group: null
    options__tabular__column_width: 12
  - code: holidayEndDate
    title: Holiday End Date
    data_type:
      key: String
      collection: data_types
    display_type:
      key: DD/MM
      collection: field_types
    group: null
    options__tabular__column_width: 12
  - code: repeatEveryYears
    title: Repeats Annually
    data_type:
      key: String
      collection: data_types
    display_type:
      key: YesNo
      collection: field_types
  - code: status
    title: Status
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Boolean Tag
      collection: field_types
  - code: updatedBy
    title: Last Updated By
    data_type:
      key: User
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 12
  - code: updatedAt
    title: Last Updated On
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
    options__tabular__column_width: 12
mock_data:
  - code: '00000001'
    nation: Việt Nam
    shortName:
      default: QK
      english: QK
      vietnamese: QK
    effectiveDate: 10/07/2024
    holidayStartDate: 01/09
    holidayEndDate: 02/09
    fullName:
      default: Quốc khánh
      english: Quốc khánh
      vietnamese: Quốc khánh
    type: Ngày lễ
    onCycle: Có
    status: true
  - code: '00000002'
    nation: Việt Nam
    shortName:
      default: QTLĐ
      english: QTLĐ
      vietnamese: QTLĐ
    effectiveDate: 06/07/2024
    holidayStartDate: 01/01
    holidayEndDate: 01/05
    fullName:
      default: Quốc tế lao động
      english: Quốc tế lao động
      vietnamese: Quốc tế lao động
    type: Ngày lễ
    onCycle: Có
    status: true
local_buttons: null
layout: layout-table
form_config:
  formTitle:
    create: Add New Holiday
    edit: Edit Holiday
  fields:
    - type: group
      n_cols: 2
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - name: code
          label: Holiday Code
          type: text
          placeholder: System - generated
          disabled: true
          class: custom-disabled
        - name: nationId
          label: Country
          type: select
          outputValue: value
          clearFieldsAfterChange:
            - caHolidayTypeId
          placeholder: Select Country
          _select:
            transform: $countriesList()
        - name: companyId
          label: Company
          type: select
          outputValue: value
          placeholder: Select Company
          _select:
            transform: $.variables._companyList
        - name: shortName
          label: Short Name
          type: translation
          placeholder: Enter Short Name
          validators:
            - type: required
            - type: maxLength
              args: '300'
              text: Maximum length is 300 characters.
        - name: longName
          label: Long Name
          type: translation
          placeholder: Enter Long Name
          validators:
            - type: required
            - type: maxLength
              args: '500'
              text: Maximum length is 500 characters.
        - type: select
          label: Holiday Type
          outputValue: value
          name: caHolidayTypeId
          placeholder: Select Holiday Type
          _select:
            transform: $holidayList($.fields.nationId, $.fields.effectiveDate)
          validators:
            - type: required
        - type: radio
          label: Repeats Annually
          name: repeatEveryYears
          _value:
            transform: >-
              $.extend.formType = 'create' ?
              $not($exists($.fields.repeatEveryYears)) ?  true
          radio:
            - value: true
              label: 'Yes'
            - value: false
              label: 'No'
        - type: dateRange
          label: Holiday Start Date
          name: holidayStartDate
          placeholder: dd/MM
          mode: date-picker
          setting:
            format: dd/MM
            type: date
          validators:
            - type: required
        - type: dateRange
          label: Holiday End Date
          name: holidayEndDate
          placeholder: dd/MM
          mode: date-picker
          setting:
            format: dd/MM
            type: date
          validators:
            - type: required
        - type: dateRange
          label: Effective Date
          name: effectiveDate
          placeholder: dd/MM/yyyy
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
          validators:
            - type: required
          _value:
            transform: >-
              $.extend.formType = 'create' ?
              $not($exists($.fields.effectiveDate)) ?  $now()
        - type: radio
          label: Status
          name: status
          _value:
            transform: >-
              $.extend.formType = 'create' ? $not($exists($.fields.status)) ? 
              true
          radio:
            - value: true
              label: Active
            - value: false
              label: Inactive
    - type: group
      n_cols: 1
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - name: nationName
          label: Country
          type: text
        - name: companyName
          label: Company
          type: text
        - type: dateRange
          label: Effective Date
          name: effectiveDate
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
        - type: radio
          label: Status
          name: status
          radio:
            - value: true
              label: Active
            - value: false
              label: Inactive
        - name: code
          label: Holiday Code
          type: text
          disabled: true
          class: custom-disabled
        - name: shortName
          label: Short Name
          type: translation
        - name: longName
          label: Long Name
          type: translation
        - type: text
          label: Holiday Type
          name: caHolidayTypeName
        - type: dateRange
          label: Holiday Start Date
          name: holidayStartDate
          mode: date-picker
          setting:
            format: dd/MM
            type: date
        - type: dateRange
          label: Holiday End Date
          name: holidayEndDate
          mode: date-picker
          setting:
            format: dd/MM
            type: date
        - type: radio
          label: Repeats Annually
          name: repeatEveryYears
          radio:
            - value: true
              label: 'Yes'
            - value: false
              label: 'No'
    - type: translationTextArea
      label: Note
      name: note
      placeholder: Enter Note
      textarea:
        maxCharCount: 1000
        autoSize:
          minRows: 3
      validators:
        - type: maxLength
          args: '1000'
          text: Maximum length is 1000 characters.
  sources:
    holidayList:
      uri: '"/api/ca-holiday-types/list-data"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'nationId','operator':
        '$eq','value':nationId}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - nationId
        - effectiveDate
    countriesList:
      uri: '"/api/picklists/COUNTRIES/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
    companiesList:
      uri: '"/api/companies/get-by"'
      queryTransform: >-
        {'page':1,'limit':10000,'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - effectiveDate
  variables:
    _companyList:
      transform: $companiesList($.fields.effectiveDate)
  historyHeaderTitle: '''Holiday: '' & $.longName.default & '' ('' & $.code & '' )'''
filter_config:
  fields:
    - name: code
      label: Holiday Code
      placeholder: Select Holiday Code
      mode: multiple
      labelType: type-grid
      type: select
      _select:
        transform: $holidayCodeList()
    - name: nationId
      label: Country
      type: selectAll
      placeholder: Select Country
      mode: multiple
      labelType: type-grid
      isLazyLoad: true
      _options:
        transform: $countriesList($.extend.limit, $.extend.page, $.extend.search)
    - name: companyId
      label: Company
      type: selectAll
      placeholder: Select Company
      labelType: type-grid
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $companyList($.extend.limit, $.extend.page, $.extend.search)
    - type: radio
      value: ''
      labelType: type-grid
      label: Status
      name: status
      radio:
        - value: ''
          label: All
        - value: true
          label: Active
        - value: false
          label: Inactive
    - name: shortName
      label: Short Name
      labelType: type-grid
      type: text
      placeholder: Enter Short Name
    - name: longName
      label: Long Name
      labelType: type-grid
      type: text
      placeholder: Enter Long Name
    - name: caHolidayTypeId
      label: Holiday Type
      type: select
      labelType: type-grid
      placeholder: Select Holiday Type
      mode: multiple
      _select:
        transform: $holidayList()
    - type: dateRange
      label: Holiday Start Date
      name: holidayStartDate
      labelType: type-grid
      mode: date-picker
      setting:
        format: dd/MM
        type: date
    - type: dateRange
      label: Holiday End Date
      name: holidayEndDate
      labelType: type-grid
      mode: date-picker
      setting:
        format: dd/MM
        type: date
    - name: repeatEveryYears
      label: Repeats Annually
      type: radio
      value: ''
      labelType: type-grid
      radio:
        - label: All
          value: ''
        - label: 'Yes'
          value: true
        - label: 'No'
          value: false
    - name: updatedBy
      label: Last Updated By
      type: select
      mode: multiple
      labelType: type-grid
      placeholder: Select Editor
      _select:
        transform: $userList()
    - type: dateRange
      labelType: type-grid
      label: Last Updated On
      name: updatedAt
      setting:
        format: dd/MM/yyyy
        type: date
  filterMapping:
    - field: code
      operator: $in
      valueField: code.(value)
    - field: nationId
      operator: $in
      valueField: nationId.(value)
    - field: companyId
      operator: $in
      valueField: companyId.(value)
    - field: status
      operator: $eq
      valueField: status
    - field: shortNameFilter
      operator: $cont
      valueField: shortName
    - field: longNameFilter
      operator: $cont
      valueField: name
    - field: caHolidayTypeId
      operator: $in
      valueField: caHolidayTypeId.(value)
    - field: holidayStartDate
      operator: $eq
      valueField: holidayStartDate
    - field: holidayEndDate
      operator: $eq
      valueField: holidayEndDate
    - field: updatedBy
      operator: $in
      valueField: updatedBy.(value)
    - field: updatedAt
      operator: $between
      valueField: updatedAt
    - field: repeatEveryYears
      operator: $eq
      valueField: repeatEveryYears
  sources:
    holidayCodeList:
      uri: '"/api/ca-holidays/by"'
      method: GET
      queryTransform: '{''filter'': [{''field'':''status'',''operator'': ''$eq'',''value'':true}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: '$map($, function($item) {{''label'': $item.code, ''value'': $item.code}})[]'
      disabledCache: true
    holidayList:
      uri: '"/api/ca-holiday-types/by"'
      method: GET
      queryTransform: '{''filter'': [{''field'':''status'',''operator'': ''$eq'',''value'':true}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default, 'value':
        $item.code}})[]
      disabledCache: true
    countriesList:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})
      disabledCache: true
      params:
        - limit
        - page
        - search
    userList:
      uri: '"/api/internal-users"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.userName, 'value':
        $item.userName}})
      disabledCache: true
    companyList:
      uri: '"/api/companies/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
layout_options:
  tool_table:
    - id: export
      icon: icon-download-simple
  show_dialog_form_save_add_button: true
  custom_history_backend_url: /api/ca-holidays
  history_widget_header_options:
    duplicate: false
  export_all:
    type: base_all
  hide_action_row: true
  delete_multi_items: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    title: Edit
    icon: icon-pencil-simple
    type: ghost-gray
  - id: delete
    title: Delete
    icon: icon-trash
    type: ghost-gray
backend_url: api/ca-holidays
screen_name: null
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: nationId
    defaultName: CountryCode
  - name: companyId
    defaultName: CompanyCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Holiday
  parent:
    title: Set Up Working Hours
