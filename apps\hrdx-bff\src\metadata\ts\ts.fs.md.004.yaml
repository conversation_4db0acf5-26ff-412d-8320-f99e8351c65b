id: TS.FS.MD.004
status: draft
sort: 123
user_created: 2aa5db12-fa54-46d6-8e05-9e421858de0c
date_created: '2024-07-10T09:32:41.840Z'
user_updated: abd14398-1d53-4e6f-9834-26328f0ebde0
date_updated: '2025-06-30T02:46:09.513Z'
title: Shift Management
requirement:
  time: 1747033488825
  blocks:
    - id: C9_ehXEuOo
      type: paragraph
      data:
        text: >-
          - Chức năng cho phép bộ phận nhân sự tập đoàn/CTTV quản lý/thiết lập
          thông tin thời giờ làm việc
    - id: C4okDRTwWy
      type: paragraph
      data:
        text: >-
          - Hệ thống kiểm tra dữ liệu, không cho phép tạo mới/ chỉnh sửa và cảnh
          báo khi trường thông tin “Mã thời giờ làm việc” trùng với “Mã thời giờ
          làm việc” của các thiết lập đã tồn tại trên hệ thống.
    - id: OvXw77vRUb
      type: paragraph
      data:
        text: >2-
           
          - Hệ thống chỉ cho phép sửa, xóa thông tin thời giờ khi thông tin thời
          giờ làm việc, lịch làm việc chưa được gán cho CBNV.
    - id: 1-WJNLvgpp
      type: paragraph
      data:
        text: >2-
           
          - Hệ thống cho phép nhập dữ liệu hàng loạt (import) thông tin thiết
          lập thời giờ làm việc. 
    - id: pKfBe9C0q1
      type: paragraph
      data:
        text: >-

          - Hệ thống cho phép sao chép thông thông tin từ 01 thời giờ làm việc
          đã được thiết lập trước đó sang 01 thời giờ làm việc mới và cho phép
          chỉnh sửa.&nbsp; &nbsp;
  version: 2.30.7
screen_design: null
module: TS
local_fields:
  - code: code
    title: Shift Code
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    pinned: true
    show_sort: true
  - code: shortName
    title: Short Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
  - code: longName
    title: Long name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    options__tabular__column_width: 12
  - code: nationName
    title: Country
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: groupName
    title: Group
    data_type:
      key: Organization-1
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: companyName
    title: Company
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: typeOfShift
    title: Shift Type
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: classify
    title: Classify
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: workingHourStart
    title: Start time
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: workingHourEnd
    title: End time
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: noWorkingHours
    title: Working hours
    data_type:
      key: Nmberic
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: noWorkingDays
    title: Working Days
    data_type:
      key: Nmberic
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: status
    title: Status
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: Boolean Tag
      collection: field_types
  - code: updatedBy
    title: Lastest Updated By
    data_type:
      key: User
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 12
  - code: updatedAt
    title: Lastest Updated On
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
    options__tabular__column_width: 12
mock_data:
  - code: HC1
    effectiveDate: 06/07/2024
    fullName:
      default: Part office time
      english: Part office time
      vietnamese: Ca hành chính
    shortName:
      default: PAT
      english: PAT
      vietnamese: PAT
    division: Division 1
    department: Department 1
    nation: Việt Nam
    legal: Nhân sự
    unit: Nhân sự
    startTime: '08:30'
    endTime: '17:30'
    numberOfManHours: 8
    workingDays: 6
    status: true
    note: Ghi chú
  - code: HC2
    effectiveDate: 03/07/2024
    fullName:
      default: Part office time
      english: Part office time
      vietnamese: Ca hành chính
    shortName:
      default: PAT
      english: PAT
      vietnamese: PAT
    division: Division 1
    department: Department 1
    nation: Việt Nam
    legal: Nhân sự
    unit: Nhân sự
    startTime: '08:30'
    endTime: '17:30'
    numberOfManHours: 8
    workingDays: 6
    status: true
    note: Ghi chú
local_buttons: null
layout: layout-table
form_config:
  formSize:
    create: large
    edit: large
    view: large
    proceed: large
  fields:
    - type: group
      label: Basic Information
      collapse: false
      _condition:
        transform: $.extend.formType != 'view'
      fields:
        - type: group
          n_cols: 2
          fields:
            - name: code
              label: Shift Code
              type: text
              placeholder: Enter Code
              validators:
                - type: required
                - type: maxLength
                  args: '8'
                  text: Code should not exceed 8 characters
                - type: pattern
                  args: ^[a-zA-Z0-9]*$
                  text: >-
                    The code must not contain special characters, except +, -,
                    *, /, and spaces.
              _disabled:
                transform: $.extend.formType = 'edit'
            - name: nationId
              label: Country
              type: select
              outputValue: value
              placeholder: Select Country
              _select:
                transform: $nationsList()
            - name: shortName
              label: Short Name
              type: translation
              placeholder: Enter Short Name
              validators:
                - type: required
                - type: maxLength
                  args: '300'
                  text: Code should not exceed 300 characters
            - name: longName
              label: Long Name
              type: translation
              placeholder: Enter Long Name
              validators:
                - type: required
                - type: maxLength
                  args: '500'
                  text: Code should not exceed 500 characters
            - type: dateRange
              label: Effective Date
              name: effectiveDate
              placeholder: dd/MM/yyyy
              mode: date-picker
              setting:
                format: dd/MM/yyyy
                type: date
              validators:
                - type: required
              _value:
                transform: >-
                  $.extend.formType = 'create' ?
                  $not($exists($.fields.effectiveDate)) ? $now()
            - type: radio
              name: status
              label: Status
              value: true
              radio:
                - label: Active
                  value: true
                - label: Inactive
                  value: false
        - type: translationTextArea
          name: note
          label: Note
          placeholder: Enter note
          textarea:
            maxCharCount: 1000
            autoSize:
              minRows: 3
          validators:
            - type: maxLength
              args: '1000'
              text: Maximum length is 1000 characters.
    - type: group
      collapse: false
      label: Basic Information
      fieldGroupTitleStyle:
        borderTop: none
        paddingTop: 0px
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - name: code
          label: Code
          type: text
          placeholder: Enter Code
        - name: longName
          label: Long Name
          type: translation
          placeholder: Enter Long Name
        - name: shortName
          label: Short Name
          type: translation
          placeholder: Enter Short Name
        - name: nationName
          label: Country
          type: text
        - type: dateRange
          label: Effective Date
          name: effectiveDate
          placeholder: dd/MM/yyyy
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
        - type: radio
          name: status
          label: Status
          radio:
            - label: Active
              value: true
            - label: Inactive
              value: false
        - type: translationTextArea
          name: note
          label: Note
          placeholder: Enter note
          textarea:
            maxCharCount: 1000
            autoSize:
              minRows: 3
    - type: group
      n_cols: 2
      collapse: false
      label: Organization Information
      _condition:
        transform: $.extend.formType != 'view'
      fields:
        - type: select
          label: Group
          name: groupId
          clearFieldsAfterChange:
            - companyId
            - legalEntityId
            - businessUnitId
            - divisionId
            - departmentId
          placeholder: Select Group
          outputValue: value
          _select:
            transform: $.variables._groupsList
        - type: select
          label: Company
          name: companyId
          clearFieldsAfterChange:
            - legalEntityId
            - businessUnitId
            - divisionId
            - departmentId
          placeholder: Select Company
          outputValue: value
          _select:
            transform: $.variables._companyList
    - type: group
      collapse: false
      label: Organization Information
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - name: groupName
          label: Group
          type: text
        - name: companyName
          label: Company
          type: text
    - type: group
      n_cols: 1
      label: Setting Time
      collapse: false
      _condition:
        transform: $.extend.formType != 'view'
      fields:
        - type: group
          n_cols: 2
          fields:
            - name: typeOfShift
              label: Shift Type
              type: select
              outputValue: value
              placeholder: Select Shift Type
              select:
                - label: Ca cố định
                  value: S
                - label: Ca linh hoạt
                  value: F
              validators:
                - type: required
              _disabled:
                transform: $.extend.formType = 'edit'
            - name: classify
              label: Classify
              type: select
              outputValue: value
              placeholder: Select Classify
              validators:
                - type: required
              _select:
                transform: $classifyList()
            - type: checkbox
              name: offShift
              label: Off Shift
              labelType: type2
              value: false
              hiddenLabel: true
              col: 1
        - type: group
          n_cols: 2
          fields:
            - type: text
              label: Start Time
              name: workingHourStartView
              placeholder: Start Time
              dependentField: $.fields.typeOfShift
              disabled: true
              _value:
                transform: >-
                  ($value := $filter($.fields.caWorkingHours_Detail,
                  function($v, $i, $a) {$v.hoursOfType = 'I'})[0]; $value ?
                  $DateFormat($value.hours, 'HH:mm') : '  ')
            - type: text
              label: Start Time
              name: workingHourStart
              placeholder: Start Time
              dependentField: $.fields.typeOfShift
              unvisible: true
              _value:
                transform: >-
                  ($value := $filter($.fields.caWorkingHours_Detail,
                  function($v, $i, $a) {$v.hoursOfType = 'I'})[0];
                  $DateFormat($value.hours, 'HH:mm'))
            - type: text
              name: workingHourEndView
              label: End Time
              placeholder: End Time
              dependentField: $.fields.typeOfShift
              disabled: true
              _value:
                transform: >-
                  ($value := $filter($.fields.caWorkingHours_Detail,
                  function($v, $i, $a) {$v.hoursOfType = 'U'})[0]; $value ?
                  $DateFormat($value.hours, 'HH:mm') : '  ')
            - type: text
              name: workingHourEnd
              placeholder: End Time
              dependentField: $.fields.typeOfShift
              unvisible: true
              _value:
                transform: >-
                  ($value := $filter($.fields.caWorkingHours_Detail,
                  function($v, $i, $a) {$v.hoursOfType = 'U'})[0];
                  $DateFormat($value.hours, 'HH:mm'))
        - type: text
          name: label
          _value:
            transform: >-
              'Thời gian sẽ được ghi nhận từ '& $.fields.workingHourStart & '
              hôm trước đến ' & $.fields.workingHourEnd & ' hôm sau'
          _condition:
            transform: >-
              $number($replace($.fields.workingHourStart,':','')) >
              $number($replace($.fields.workingHourEnd,':',''))
          readOnly: true
          isInfo: true
        - type: group
          n_cols: 2
          _condition:
            transform: $.extend.formType != 'view'
          fields:
            - name: noWorkingHours
              label: Working Hours Of Day
              type: number
              placeholder: Enter hour
              validators:
                - type: required
              _disabled:
                transform: $.fields.typeOfShift = 'S'
              _value:
                transform: >-
                  $count($.fields.caWorkingHours_Detail) > 0 ? ($listIn :=
                  $filter($.fields.caWorkingHours_Detail, function($v, $i, $a) {
                  $v.hoursOfType = 'I' })[]; $listBreak :=
                  $filter($.fields.caWorkingHours_Detail, function($v, $i, $a) {
                  $v.hoursOfType = 'B' })[]; $totalIn := $count($listIn) > 0 ?
                  $reduce($listIn, function($i, $j) { $i + $j.duration }, 0) :
                  0; $totalBreak := $count($listBreak) > 0 ? $reduce($listBreak,
                  function($i, $j) { $i + $j.duration }, 0) : 0; $totalIn +
                  $totalBreak)
            - name: noWorkingDays
              label: Converted Workdays
              type: number
              placeholder: Enter Converted Workdays
              validators:
                - type: required
              number:
                precision: 2
            - type: text
              name: addBreakOrMealTime
              label: addBreakOrMealTime
              value: '1'
              unvisible: true
        - type: array
          mode: table
          name: caWorkingHours_Detail
          _size:
            transform: $.fields.typeOfShift and $.extend.formType = 'create' ? 0
          arrayOptions:
            canChangeSize: true
          field:
            type: group
            fields:
              - type: select
                label: Hours Type
                name: hoursOfType
                align: start
                outputValue: value
                width: 160px
                placeholder: Select Type
                select:
                  - label: In
                    value: I
                  - label: Meal
                    value: E
                  - label: Break
                    value: B
                  - label: Out
                    value: U
                _class:
                  transform: '$.fields.typeOfShift = ''S'' ? ''required'' : ''unrequired'''
              - type: timePicker
                label: Start
                name: hours
                align: start
                width: 120px
                setting:
                  format: HH:mm
              - type: number
                label: Duration
                name: duration
                align: start
                disabled: true
                width: 150px
                number:
                  suffix: Hour(s)
                  precision: 2
                _value:
                  transform: >-
                    ( $.fields.caWorkingHours_Detail; $idx:= $.extend.path[-2];
                    $idx_next := $idx +1; $value :=
                    $count($.fields.caWorkingHours_Detail) = $idx_next ? 0 :
                    $round(($toMillis($DateFormat($.fields.caWorkingHours_Detail[$idx_next].hours,'YYYY-MM-DDTHH:mm:00.000Z'))
                    - 
                    $toMillis($DateFormat($.fields.caWorkingHours_Detail[$idx].hours,'YYYY-MM-DDTHH:mm:00.000Z')))/(1000*60*60),2);
                    $not($.fields.caWorkingHours_Detail[$idx].hoursOfType = 'U')
                    ? ($value < 0 ? ($value + 24) : $value)  )
              - type: timePicker
                label: Late Hours
                name: comeLateHour
                align: start
                width: 120px
                _disabled:
                  transform: >-
                    $not($getIdx($.fields.caWorkingHours_Detail,
                    $.extend.path[-2]).hoursOfType = 'I' and $.extend.path[-2] =
                    0)
                setting:
                  format: HH:mm
              - type: timePicker
                label: Early Hours
                name: leaveEarlyHour
                align: start
                width: 120px
                _disabled:
                  transform: >-
                    $not($getIdx($.fields.caWorkingHours_Detail,
                    $.extend.path[-2]).hoursOfType = 'U'  and $.extend.path[-2]
                    = $count($.fields.caWorkingHours_Detail) - 1)
                setting:
                  format: HH:mm
              - type: checkbox
                showLabelCheckbox: false
                label: Cfg1
                name: attribute1
                align: start
                width: 80px
              - type: checkbox
                showLabelCheckbox: false
                label: Cfg2
                name: attribute2
                align: start
                width: 80px
              - type: checkbox
                showLabelCheckbox: false
                label: Cfg3
                name: attribute3
                align: start
                width: 80px
              - type: checkbox
                showLabelCheckbox: false
                label: Cfg4
                name: attribute4
                align: start
                width: 80px
          dependantField: $.fields.typeOfShift
    - type: group
      n_cols: 1
      label: Time
      collapse: false
      lastGroupStyleOff: true
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - name: typeOfShift
          label: Shift Type
          type: select
          placeholder: Select Shift Type
          select:
            - label: Ca cố định
              value: S
            - label: Ca linh hoạt
              value: F
        - name: classify
          label: Shift Group
          type: select
          outputValue: value
          placeholder: Select Classify
          _select:
            transform: $classifyList()
        - name: workingHourStart
          label: Working Hours Start
          type: text
          unvisible: true
        - name: workingHourEnd
          label: Working Hours End
          type: text
          unvisible: true
        - name: workingTime
          label: Working Time
          type: text
          _value:
            transform: $.fields.workingHourStart & ' - ' & $.fields.workingHourEnd
        - name: noWorkingHours
          label: Working Hours
          type: number
          placeholder: Enter hour
        - name: noWorkingDays
          label: Converted Workdays
          type: number
          number:
            precision: 2
        - type: select
          name: offShift
          label: Off Shift
          select:
            - label: Có
              value: true
            - label: Không
              value: false
        - type: array
          mode: table
          name: caWorkingHours_Detail
          arrayOptions:
            canChangeSize: false
          field:
            type: group
            fields:
              - type: select
                label: Hours Type
                name: hoursOfType
                align: start
                outputValue: value
                width: 160px
                placeholder: Select Type
                select:
                  - label: In
                    value: I
                  - label: Meal
                    value: E
                  - label: Break
                    value: B
                  - label: Out
                    value: U
              - type: timePicker
                label: Start
                name: hours
                align: start
                width: 120px
                setting:
                  format: HH:mm
              - type: number
                label: Duration
                name: duration
                align: start
                disabled: true
                width: 150px
                number:
                  suffix: Hour(s)
                  precision: 2
              - type: timePicker
                label: Late Hours
                name: comeLateHour
                align: start
                width: 120px
                setting:
                  format: HH:mm
              - type: timePicker
                label: Early Hours
                name: leaveEarlyHour
                align: start
                width: 120px
                setting:
                  format: HH:mm
              - type: checkbox
                showLabelCheckbox: false
                label: Cfg1
                name: attribute1
                align: start
                width: 80px
              - type: checkbox
                showLabelCheckbox: false
                label: Cfg2
                name: attribute2
                align: start
                width: 80px
              - type: checkbox
                showLabelCheckbox: false
                label: Cfg3
                name: attribute3
                align: start
                width: 80px
              - type: checkbox
                showLabelCheckbox: false
                label: Cfg4
                name: attribute4
                align: start
                width: 80px
  _mode:
    transform: $.extend.formType != 'view' ? 'mark-scroll'
  sources:
    groupsList:
      uri: '"/api/groups/get-by"'
      queryTransform: >-
        {'page':1,'limit':10000,'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - effectiveDate
    companiesList:
      uri: '"/api/companies/get-by"'
      queryTransform: >-
        {'page':1,'limit':10000,'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true},{'field':'groupCode','operator': '$eq','value':
        $.groupCode }]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - effectiveDate
        - groupCode
    legalEntityList:
      uri: '"/api/legal-entities/get-list"'
      queryTransform: >-
        {'page':1,'limit':10000,'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true},{'field':'companyCode','operator': '$eq','value':
        $.companyCode }]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - effectiveDate
        - companyCode
    businessUnitList:
      uri: '"/api/business-units/get-list"'
      queryTransform: >-
        {'filter': [{'field':'status','operator':
        '$eq','value':true},{'field':'companyCode','operator': '$eq','value':
        $.companyCode }, {'field':'effectiveDate','operator': '$eq','value':
        $.effectiveDate}], 'limit': 10000}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - effectiveDate
        - companyCode
    divisionList:
      uri: '"/api/divisions/get-list"'
      queryTransform: >-
        {'filter': [{'field':'status','operator':
        '$eq','value':true},{'field':'businessUnitCode','operator':
        '$eq','value': $.businessUnitCode },{'field':'companyCode','operator':
        '$eq','value': $.companyCode } ,{'field':'effectiveDate','operator':
        '$eq','value': $.effectiveDate}], 'limit':10000}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - effectiveDate
        - businessUnitCode
        - companyCode
    departmentList:
      uri: '"/api/departments/get-list"'
      queryTransform: >-
        {'page':1,'limit':10000,'filter': [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true},{'field':'divisionCode','operator': '$eq','value':
        $.divisionCode }]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code }})[]
      disabledCache: true
      params:
        - effectiveDate
        - divisionCode
    nationsList:
      uri: '"/api/picklists/COUNTRIES/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
    classifyList:
      uri: '"/api/picklists/CLASSIFY/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
  variables:
    _groupsList:
      transform: $groupsList($.fields.effectiveDate)
    _companyList:
      transform: >-
        $.fields.groupId ? $companiesList($.fields.effectiveDate,
        $.fields.groupId)
filter_config:
  fields:
    - name: code
      label: Shift Code
      type: select
      mode: multiple
      isLazyLoad: true
      labelType: type-grid
      _select:
        transform: $workinghoursList()
    - name: shortName
      label: Short Name
      type: text
      labelType: type-grid
    - name: longName
      label: Long Name
      type: text
      labelType: type-grid
    - name: country
      label: Country
      type: selectAll
      mode: multiple
      labelType: type-grid
      isLazyLoad: true
      _options:
        transform: $nationsList($.extend.limit, $.extend.page, $.extend.search)
    - name: group
      label: Group
      type: selectAll
      mode: multiple
      isLazyLoad: true
      labelType: type-grid
      _options:
        transform: $groupsList($.extend.limit, $.extend.page, $.extend.search)
    - name: company
      label: Company
      type: selectAll
      labelType: type-grid
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $companiesList($.extend.limit, $.extend.page, $.extend.search)
    - name: typeOfShift
      label: Shift Type
      type: select
      labelType: type-grid
      mode: multiple
      select:
        - label: Ca cố định
          value: S
        - label: Ca linh hoạt
          value: F
    - name: classify
      label: Shift Group
      type: select
      mode: multiple
      labelType: type-grid
      _select:
        transform: $classifyList()
    - type: timePicker
      label: Start Time
      name: workingHourStart
      labelType: type-grid
      placeholder: hh:mm
    - type: timePicker
      label: End Time
      name: workingHourEnd
      labelType: type-grid
      placeholder: hh:mm
    - type: group
      label: Working Hours
      labelType: type-row
      lastGroupStyleOff: true
      n_cols: 2
      fields:
        - type: number
          name: workingHoursFrom
          number:
            prefix: From
        - type: number
          name: workingHoursTo
          number:
            prefix: To
    - type: group
      label: Converted Workdays
      labelType: type-row
      lastGroupStyleOff: true
      n_cols: 2
      fields:
        - type: number
          name: workingDaysFrom
          number:
            prefix: From
        - type: number
          name: workingDaysTo
          number:
            prefix: To
    - type: radio
      label: Status
      name: status
      value: ''
      labelType: type-grid
      radio:
        - label: All
          value: ''
        - label: Active
          value: true
        - label: Inactive
          value: false
    - name: updatedBy
      label: Last Updated By
      type: select
      labelType: type-grid
      placeholder: Select Last Updated By
      mode: multiple
      _select:
        transform: $userList()
    - type: dateRange
      label: Last Updated On
      labelType: type-grid
      name: updatedAt
  filterMapping:
    - field: code
      operator: $in
      valueField: code.(value)
    - field: shortName
      operator: $cont
      valueField: shortName
    - field: longName
      operator: $cont
      valueField: longName
    - field: nationId
      operator: $in
      valueField: country.(value)
    - field: groupId
      operator: $in
      valueField: group.(value)
    - field: companyId
      operator: $in
      valueField: company.(value)
    - field: legalEntityId
      operator: $in
      valueField: legalEntity.(value)
    - field: businessUnitId
      operator: $in
      valueField: businessUnit.(value)
    - field: divisionId
      operator: $in
      valueField: division.(value)
    - field: departmentId
      operator: $in
      valueField: department.(value)
    - field: startTime
      operator: $eq
      valueField: startTime
    - field: endTime
      operator: $eq
      valueField: endTime
    - field: noWorkingDays
      operator: $gte
      valueField: workingDaysFrom
    - field: noWorkingDays
      operator: $lte
      valueField: workingDaysTo
    - field: noWorkingHours
      operator: $gte
      valueField: workingHoursFrom
    - field: noWorkingHours
      operator: $lte
      valueField: workingHoursTo
    - field: status
      operator: $eq
      valueField: status
    - field: updatedBy
      operator: $in
      valueField: updatedBy.(value)
    - field: updatedAt
      operator: $between
      valueField: updatedAt
    - field: typeOfShift
      operator: $in
      valueField: typeOfShift.(value)
  sources:
    workinghoursList:
      uri: '"/api/ca-working-hours"'
      method: GET
      bodyTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    groupsList:
      uri: '"/api/groups"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    companiesList:
      uri: '"/api/companies"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    legalEntitiesList:
      uri: '"/api/legal-entities"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    businessUnitsList:
      uri: '"/api/business-units"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    divisionsList:
      uri: '"/api/divisions"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    departmentsList:
      uri: '"/api/departments"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    nationsList:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search,'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    classifyList:
      uri: '"/api/picklists/CLASSIFY/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
    userList:
      uri: '"/api/internal-users"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.userName, 'value':
        $item.userName}})[]
      disabledCache: true
layout_options:
  tool_table:
    - id: export
      icon: icon-download-simple
  show_dialog_form_save_add_button: true
  history_widget_header_options:
    duplicate: false
  view_history_after_created: true
  custom_history_backend_url: /api/ca-working-hours/insert-new-record
  export_all:
    type: base_total
  hide_action_row: true
  show_dialog_duplicate_button: false
  is_new_dynamic_form: true
  delete_multi_items: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    title: Edit
    icon: icon-pencil-simple
  - id: delete
    title: Delete
    icon: icon-trash
backend_url: /api/ca-working-hours
screen_name: null
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: companyId
    defaultName: CompanyCode
  - name: nationId
    defaultName: CountryCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Shift Management
  parent:
    title: Manage work schedule
