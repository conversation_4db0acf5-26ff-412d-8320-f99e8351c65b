id: TS.FS.MD.005
status: draft
sort: 250
user_created: 2aa5db12-fa54-46d6-8e05-9e421858de0c
date_created: '2024-07-10T09:34:01.914Z'
user_updated: abd14398-1d53-4e6f-9834-26328f0ebde0
date_updated: '2025-07-21T09:01:12.073Z'
title: Work Schedule Management
requirement:
  time: 1747022440978
  blocks:
    - id: k5A7_GUnQ_
      type: paragraph
      data:
        text: Quản lý lịch làm việc
  version: 2.30.7
screen_design: null
module: TS
local_fields:
  - code: code
    title: Working schedule Code
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    pinned: true
    show_sort: true
    options__tabular__column_width: 15
  - code: name
    title: Long Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
  - code: shortName
    title: Short Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
  - code: nationName
    title: Country
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: groupName
    title: Group
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: companyName
    title: Company
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    options__tabular__column_width: 16
  - code: workScheduleTypeDisplay
    title: Work Schedule Type
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 12
  - code: scheduleSettingPlanDisplay
    title: Method
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: workdays
    title: Days in Schedule
    data_type:
      key: Nmberic
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: status
    title: Status
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Boolean Tag
      collection: field_types
  - code: updatedBy
    title: Last Updated By
    data_type:
      key: User
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: updatedAt
    title: Last Updated On
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
    pinned: false
mock_data:
  - code: '00000001'
    effectiveDate: 07/07/2024
    fullName:
      default: Lịch làm việc 1
      english: Lịch làm việc 1
      vietnamese: Lịch làm việc 1
    shortName:
      default: Lịch làm việc 1
      english: Lịch làm việc 1
      vietnamese: Lịch làm việc 1
    nation: Việt Nam
    calendarCode: '001'
    days: 7
  - code: '00000002'
    effectiveDate: 07/07/2024
    fullName:
      default: Lịch làm việc 1
      english: Lịch làm việc 1
      vietnamese: Lịch làm việc 1
    shortName:
      default: Lịch làm việc 1
      english: Lịch làm việc 1
      vietnamese: Lịch làm việc 1
    nation: Việt Nam
    calendarCode: '001'
    days: 7
local_buttons: null
layout: layout-table
form_config:
  form_value_transform: >-
    $ ~> | $ | {'caWorkSchedules_Detail':
    $map($.caWorkSchedules_Detail,function($item){{'workingHoursId':
    $item.workingHoursId.value, 'id': $item.id, 'dayInWeek': $item.dayInWeek,
    'dayInPeriod': $item.dayInPeriod }})[]} |
  formSize:
    create: large
    edit: large
    proceed: large
    history: largex
  formTitle:
    create: Add New Working Shedule
    edit: Edit Working Shedule
    view: View Detail Working Schedule
    proceed: Insert New Record
  fields:
    - type: group
      n_cols: 3
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - name: code
          label: Working schedule Code
          type: text
          placeholder: System - generated
          disabled: true
          class: custom-disabled
        - name: nationId
          label: Country
          type: select
          placeholder: Select Country
          outputValue: value
          _select:
            transform: $nationsList($.extend.limit, $.extend.page, $.extend.search)
          isLazyLoad: true
          initOptionFromDefaultValue:
            value: nationId
            label: '{{nationName}}'
        - name: groupId
          label: Group
          type: select
          clearFieldsAfterChange:
            - companyId
          placeholder: Select Group
          outputValue: value
          _select:
            transform: >-
              $groupsList($.extend.limit, $.extend.page, $.extend.search,
              $.fields.effectiveDate)
          isLazyLoad: true
          initOptionFromDefaultValue:
            value: groupId
            label: '{{groupName}} ({{groupId}})'
        - name: companyId
          label: Company
          type: select
          placeholder: Select Company
          outputValue: value
          _select:
            transform: >-
              $companiesList($.extend.limit, $.extend.page, $.extend.search,
              $.fields.effectiveDate, $.fields.groupId)
          isLazyLoad: true
          initOptionFromDefaultValue:
            value: companyId
            label: '{{companyName}} ({{companyId}})'
        - name: name
          label: Long Name
          type: translation
          placeholder: Enter Long Name
          validators:
            - type: required
            - type: maxLength
              args: '500'
              text: Maximum length is 500 characters.
        - name: shortName
          label: Short Name
          type: translation
          placeholder: Enter Short Name
          validators:
            - type: required
            - type: maxLength
              args: '300'
              text: Maximum length is 300 characters.
        - name: workScheduleType
          label: Work Schedule Type
          type: select
          placeholder: Select Work Schedule Type
          outputValue: value
          validators:
            - type: required
          select:
            - label: Lịch cố định
              value: S
            - label: Lịch linh hoạt
              value: F
        - name: scheduleSettingPlan
          label: Method
          type: select
          placeholder: Select Method
          outputValue: value
          validators:
            - type: required
          select:
            - value: '1'
              label: Tuần
            - value: '2'
              label: Khoảng thời gian
        - type: group
          n_cols: 1
          actions:
            - name: renderBtn
              icon: icons:arrow-elbow-down-left
              isReset: false
          actionsStyle: config
          fields:
            - name: _workdays
              label: Days in Schedule
              type: number
              _disabled:
                transform: $.fields.scheduleSettingPlan = '1'
              _value:
                transform: $.fields.workdays
              placeholder: Enter Days in Schedule
              validators:
                - type: required
        - name: workdays
          type: number
          _value:
            transform: >-
              $.fields.scheduleSettingPlan = '1' ? 7 :
              $count($.fields.caWorkSchedules_Detail)
          unvisible: true
          placeholder: Enter Days in Schedule
          validators:
            - type: required
        - type: dateRange
          label: Effective Date
          name: effectiveDate
          placeholder: dd/MM/yyyy
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
          validators:
            - type: required
        - type: radio
          label: Status
          name: status
          radio:
            - value: true
              label: Active
            - value: false
              label: Inactive
          _value:
            transform: '$.extend.formType = ''create'' ? true : $.fields.status'
    - type: group
      _condition:
        transform: $.extend.formType = 'view'
      fields:
        - name: nationId
          type: text
          unvisible: true
        - name: nationName
          label: Country
          type: text
          _value:
            transform: >-
              $.extend.defaultValue.nationId ? $.extend.defaultValue.nationName
              & ' (' & $.extend.defaultValue.nationId & ')'
        - name: groupId
          type: text
          unvisible: true
        - name: groupName
          label: Group
          type: text
          _value:
            transform: >-
              $.extend.defaultValue.groupId ? $.extend.defaultValue.groupName &
              ' (' & $.extend.defaultValue.groupId & ')'
        - name: companyId
          type: text
          unvisible: true
        - name: companyName
          label: Company
          type: text
          _value:
            transform: >-
              $.extend.defaultValue.companyId ?
              $.extend.defaultValue.companyName & ' (' &
              $.extend.defaultValue.companyId & ')'
        - type: dateRange
          name: effectiveDate
          label: Effective Date
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
        - name: code
          label: Working Schedule Code
          type: text
        - name: shortName
          label: Short Name
          type: translation
        - name: name
          label: Long Name
          type: translation
          placeholder: Enter Long Name
        - name: workScheduleType
          label: Work Schedule Type
          type: select
          select:
            - label: Lịch cố định
              value: S
            - label: Lịch linh hoạt
              value: F
        - name: scheduleSettingPlan
          label: Method
          type: select
          select:
            - value: '1'
              label: Tuần
            - value: '2'
              label: Khoảng thời gian
        - name: workdays
          label: Days in Schedule
          type: text
        - name: status
          label: Status
          type: radio
          radio:
            - value: true
              label: Active
            - value: false
              label: Inactive
    - type: translationTextArea
      label: Note
      name: note
      placeholder: Enter Note
      textarea:
        maxCharCount: 1000
        autoSize:
          minRows: 3
      validators:
        - type: maxLength
          args: '1000'
          text: Maximum length is 1000 characters.
    - type: group
      label: Schedule setting
      fields:
        - type: table
          mode: table
          name: caWorkSchedules_Detail
          rowIdName: idx
          _row_actions:
            transform: >-
              $.fields.scheduleSettingPlan = '1' ?[]:
              [{'id':'delete','type':'ghost-gray','icon':'icon-trash-bold'}]
          layout_option:
            tool_table:
              show_table_checkbox: false
              show_table_filter: false
              show_table_group: false
              hidden_header: false
              collapse: false
            show_pagination: false
            hide_action_row: false
            calendarOptions:
              isActive: true
              hasModal: true
              button:
                type: link
                title: Show Working schedule
                isRightIcon: true
                rightIcon: fa-eye
              _modalTitle:
                transform: >-
                  'Working Schedule: ' & $.fields.shortName.default & '(' &
                  $.fields.code & ')'
              _effectiveDate:
                transform: >-
                  $exists($.fields.effectiveDate) ?
                  $DateFormat($.fields.effectiveDate, 'YYYY-MM-DD') :
                  $DateFormat($now(), 'YYYY-MM-DD')
              _method:
                transform: '$.fields.scheduleSettingPlan = ''1'' ? ''week'' : ''period'''
              _value:
                transform: >-
                  $append([], $map($.fields.caWorkSchedules_Detail, function($v,
                  $i) { { 'dayNumber': $number($v.dayInWeek), 'type':
                  $v.offShift ? 'off-shift' : 'work-shift', 'code': $v.shift,
                  'title': $v.fullName, 'time': $v.in & ' - ' & $v.out } }))
          columns:
            - code: stt
              title: Day Number
              type: number
              width: 10
              align: left
              readOnly: true
              data_type:
                key: String
                collection: data_types
              display_type:
                key: Label
                collection: field_types
                _value:
                  transform: $number($.extend.index) + 1
            - title: Day
              code: dayInWeekDisplay
              type: text
              readOnly: true
              width: 10
              align: left
              pinned: true
              display_type:
                key: Label
                collection: field_types
                _value:
                  transform: >-
                    $DateFormat( $CalDate($.fields.effectiveDate,
                    $.extend.index, 'd' ), 'dddd')
            - title: Day
              code: dayInWeek
              type: text
              width: 0
              unvisible: true
              display_type:
                _value:
                  transform: >-
                    $DateFormat( $CalDate($.fields.effectiveDate,
                    $.extend.index, 'd' ), 'd')
            - code: dayInPeriod
              title: Day
              width: 0
              type: number
              unvisible: true
              display_type:
                _value:
                  transform: $number($.extend.index)
            - type: select
              code: workingHoursId
              title: Shift
              placeholder: Select Shift
              width: 20
              align: start
              dependantField: >-
                $.fields.companyId,$.fields.groupId,$.fields.nationId,$.fields.workScheduleType
              props:
                syncValueWithOptions: true
              data_type:
                key: String
                collection: data_types
              display_type:
                key: Select
                collection: field_types
                _select:
                  transform: $.variables._workingHoursList
            - type: checkbox
              code: offShift
              showLabelCheckbox: false
              title: Off Shift
              disabled: true
              width: 7
              align: start
              display_type:
                key: Control Checkbox
                collection: field_types
                _value:
                  transform: >-
                    ( $idx:= $.extend.index; $workingHoursId :=
                    $.fields.caWorkSchedules_Detail[$idx].workingHoursId.value ;
                    $selectedWorkingHour := $getFieldGroup([$workingHoursId],
                    $.variables._workingHoursMapped); $selectedWorkingHour ?
                    $selectedWorkingHour.offShift)
            - title: In
              code: in
              type: text
              readOnly: true
              width: 5
              align: start
              data_type:
                key: String
                collection: data_types
              display_type:
                key: Label
                collection: field_types
                _value:
                  transform: >-
                    ( $idx:= $.extend.index;$workingHoursId :=
                    $.fields.caWorkSchedules_Detail[$idx].workingHoursId.value;
                    $selectedWorkingHour := $getFieldGroup([$workingHoursId],
                    $.variables._workingHoursMapped); $indexIn :=
                    $selectedWorkingHour.caWorkingHours_Detail[0].hoursOfType =
                    'I' ? 0 :
                    ($selectedWorkingHour.caWorkingHours_Detail[hoursOfType =
                    'I'][0] = $selectedWorkingHour.caWorkingHours_Detail[0] ? 0
                    :
                    null);$selectedWorkingHour.caWorkingHours_Detail[$indexIn].hours
                    ?$DateFormat(
                    $selectedWorkingHour.caWorkingHours_Detail[$indexIn].hours,
                    'HH:mm'): ' ')
            - title: Break
              code: break
              type: text
              readOnly: true
              width: 5
              data_type:
                key: String
                collection: data_types
              display_type:
                key: Label
                collection: field_types
                _value:
                  transform: >-
                    ( $idx:= $.extend.index; $workingHoursId :=
                    $.fields.caWorkSchedules_Detail[$idx].workingHoursId.value ;
                    $selectedWorkingHour := $getFieldGroup([$workingHoursId],
                    $.variables._workingHoursMapped); $value :=
                    $filter($selectedWorkingHour.caWorkingHours_Detail,
                    function($v, $i, $a) {$v.hoursOfType = 'B'})[0]; $value ?
                    $DateFormat($value.hours, 'HH:mm') : ' ')
              align: start
            - title: In
              code: in2
              type: text
              readOnly: true
              width: 5
              align: start
              display_type:
                key: Label
                collection: field_types
                _value:
                  transform: >-
                    ( $idx:= $.extend.index;$workingHoursId :=
                    $.fields.caWorkSchedules_Detail[$idx].workingHoursId.value;$selectedWorkingHour
                    := $getFieldGroup([$workingHoursId],
                    $.variables._workingHoursMapped);$hours :=
                    $reduce($selectedWorkingHour.caWorkingHours_Detail,
                    function($acc, $v, $i, $a) {    ($acc = null and
                    $v.hoursOfType = 'B' and $i < $count($a) - 1 and $a[$i +
                    1].hoursOfType = 'I') ? $a[$i + 1].hours : $acc},
                    null);$hours ? $DateFormat($hours, 'HH:mm'): ' ')
            - title: Meal
              code: meal
              type: text
              readOnly: true
              width: 5
              align: start
              display_type:
                key: Label
                collection: field_types
                _value:
                  transform: >-
                    ( $idx:= $.extend.index; $workingHoursId :=
                    $.fields.caWorkSchedules_Detail[$idx].workingHoursId.value ;
                    $selectedWorkingHour := $getFieldGroup([$workingHoursId],
                    $.variables._workingHoursMapped); $value :=
                    $filter($selectedWorkingHour.caWorkingHours_Detail,
                    function($v, $i, $a) {$v.hoursOfType = 'E'})[0]; $value ?
                    $DateFormat($value.hours, 'HH:mm') : ' ')
            - title: In
              code: in3
              type: text
              readOnly: true
              width: 5
              align: start
              display_type:
                key: Label
                collection: field_types
                _value:
                  transform: >-
                    ( $idx:= $.extend.index;$workingHoursId :=
                    $.fields.caWorkSchedules_Detail[$idx].workingHoursId.value;$selectedWorkingHour
                    := $getFieldGroup([$workingHoursId],
                    $.variables._workingHoursMapped);$hours :=
                    $reduce($selectedWorkingHour.caWorkingHours_Detail,
                    function($acc, $v, $i, $a) {$v.hoursOfType = 'E' and $i <
                    $count($a) - 1 and $a[$i + 1].hoursOfType = 'I' ? $a[$i +
                    1].hours : $acc}, null);$hours ? $DateFormat($hours,
                    'HH:mm'): ' ')
            - title: Out
              code: out
              type: text
              readOnly: true
              width: 5
              align: start
              display_type:
                key: Label
                collection: field_types
                _value:
                  transform: >-
                    ( $idx:= $.extend.index; $workingHoursId :=
                    $.fields.caWorkSchedules_Detail[$idx].workingHoursId.value ;
                    $selectedWorkingHour := $getFieldGroup([$workingHoursId],
                    $.variables._workingHoursMapped); $value :=
                    $filter($selectedWorkingHour.caWorkingHours_Detail,
                    function($v, $i, $a) {$v.hoursOfType = 'U'})[0]; $value ?
                    $DateFormat($value.hours, 'HH:mm') : ' ')
            - title: Working Hours
              code: workingHours
              type: text
              readOnly: true
              width: 10
              align: start
              display_type:
                key: Label
                collection: field_types
                _value:
                  transform: >-
                    ( $idx:= $.extend.index; $workingHoursId :=
                    $.fields.caWorkSchedules_Detail[$idx].workingHoursId.value ;
                    $selectedWorkingHour := $selectedWorkingHour :=
                    $getFieldGroup([$workingHoursId],
                    $.variables._workingHoursMapped); $selectedWorkingHour ?
                    $selectedWorkingHour.noWorkingHours : ' ')
          _dataSource:
            transform: >-
              (  $buildList := function($count, $list) {    $count > 0 ?
              $buildList($count - 1, ([{},$list])) : $list  };
              $not($isNilorEmpty($.fields._workdays)) ?
              $buildList($ParseInt($.fields._workdays), []))
  sources:
    groupsList:
      uri: '"/api/groups/get-by"'
      queryTransform: >-
        {'page': $.page,'limit': $.limit, 'search': $.search, 'filter':
        [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true}]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
    companiesList:
      uri: '"/api/companies/get-by"'
      queryTransform: >-
        {'page': $.page,'limit': $.limit, 'search': $.search,'filter':
        [{'field':'effectiveDate','operator':
        '$eq','value':$.effectiveDate},{'field':'status','operator':
        '$eq','value':true},{'field':'groupCode','operator': '$eq','value':
        $.groupId }]}
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code, 'id': $item.id }})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
        - effectiveDate
        - groupId
    nationsList:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      queryTransform: >-
        {'page':$.page,'limit': $.limit, 'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    workingHoursList:
      uri: '"/api/ca-working-hours/list-data"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'companyId','operator': '$eq','value':
        $.companyId},{'field':'groupId','operator': '$eq','value':
        $.groupId},{'field':'nationId','operator': '$eq','value':
        $.nationId},{'field':'typeOfShift','operator': '$eq','value':
        $.workScheduleType},{'field':'effectiveDate','operator': '$eq','value':
        $.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label':$item.code & ' - ' &
        $item.longName.default & ' - ' & $item.shortName.default, 'value':
        $item.id,'additionalData': $item ,'companyId':$item.companyId,'groupId':
        $item.groupId,'nationId':$item.nationId,'typeOfShift':
        $item.typeOfShift, 'noWorkingHours': $item.noWorkingHours, 'offShift':
        $item.offShift,'caWorkingHours_Detail':
        $map($item.caWorkingHours_Detail, function($i){{'hoursOfType':
        $i.hoursOfType, 'duration': $i.duration , 'hours':$i.hours}})[]  }})[]
      disabledCache: true
      params:
        - companyId
        - groupId
        - nationId
        - workScheduleType
        - effectiveDate
  variables:
    _workingHoursList:
      transform: >-
        $workingHoursList($.fields.companyId,$.fields.groupId,$.fields.nationId,$.fields.workScheduleType,
        $.fields.effectiveDate)
    _workingHoursMapped:
      transform: >-
        $reduce($.variables._workingHoursList, function($i, $j){$merge([$i,
        {($string($j.additionalData.id)):$j.additionalData}])},{})
filter_config:
  fields:
    - name: nationId
      label: Country
      type: selectAll
      placeholder: Select Country
      mode: multiple
      labelType: type-grid
      isLazyLoad: true
      _options:
        transform: $nationsList($.extend.limit, $.extend.page, $.extend.search)
    - name: groupId
      label: Group
      type: selectAll
      placeholder: Select Group
      mode: multiple
      labelType: type-grid
      isLazyLoad: true
      _options:
        transform: $groupsList($.extend.limit, $.extend.page, $.extend.search)
    - name: companyId
      label: Company
      type: selectAll
      placeholder: Select Company
      labelType: type-grid
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $companiesList($.extend.limit, $.extend.page, $.extend.search)
    - name: code
      label: Code
      type: select
      placeholder: Select Code
      labelType: type-grid
      mode: multiple
      _select:
        transform: $.variables._codeList
    - name: shortName
      label: Short Name
      labelType: type-grid
      type: text
      placeholder: Enter Short Name
    - name: longName
      label: Long Name
      labelType: type-grid
      type: text
      placeholder: Enter Long Name
    - name: workScheduleType
      label: Definition Type
      type: select
      labelType: type-grid
      placeholder: Select Definition Type
      mode: multiple
      select:
        - label: Lịch cố định
          value: S
        - label: Lịch linh hoạt
          value: F
    - name: scheduleSettingPlan
      label: Method
      type: select
      labelType: type-grid
      placeholder: Select Method
      mode: multiple
      select:
        - value: '1'
          label: Tuần
        - value: '2'
          label: Khoảng thời gian
    - name: work vdays
      label: Days in Schedule
      labelType: type-grid
      type: select
      mode: multiple
      placeholder: Select Days in Schedule
      _select:
        transform: $.variables._workdaysList
    - name: status
      label: Status
      type: radio
      value: ''
      labelType: type-grid
      radio:
        - label: All
          value: ''
        - label: Active
          value: true
        - label: Inactive
          value: false
    - name: updatedBy
      label: Last Updated By
      type: select
      mode: multiple
      labelType: type-grid
      placeholder: Select Editor
      _select:
        transform: $userList()
    - type: dateRange
      labelType: type-grid
      label: Last Updated On
      name: updatedAt
      setting:
        format: dd/MM/yyyy
        type: date
  filterMapping:
    - field: nationId
      operator: $in
      valueField: nationId.(value)
    - field: code
      operator: $in
      valueField: code.(value)
    - field: groupId
      operator: $in
      valueField: groupId.(value)
    - field: shortNameFilter
      operator: $cont
      valueField: shortName
    - field: longNameFilter
      operator: $cont
      valueField: name
    - field: companyId
      operator: $in
      valueField: companyId.(value)
    - field: workScheduleType
      operator: $in
      valueField: workScheduleType.(value)
    - field: scheduleSettingPlan
      operator: $in
      valueField: scheduleSettingPlan.(value)
    - field: updatedBy
      operator: $in
      valueField: updatedBy.(value)
    - field: updatedAt
      operator: $between
      valueField: updatedAt
    - field: status
      operator: $eq
      valueField: status
  sources:
    nationsList:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})
      disabledCache: true
      params:
        - limit
        - page
        - search
    groupsList:
      uri: '"/api/groups/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code , 'id': $item.id}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    companiesList:
      uri: '"/api/companies/get-by"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    workScheduleList:
      uri: '"/api/ca-work-schedules/by"'
      method: GET
      queryTransform: '{''filter'': [{''field'':''status'',''operator'': ''$eq'',''value'':true}]}'
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $
      disabledCache: true
    userList:
      uri: '"/api/internal-users"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.userName, 'value':
        $item.userName}})
      disabledCache: true
  variables:
    _workScheduleList:
      transform: $workScheduleList()
    _codeList:
      transform: >-
        $map($.variables._workScheduleList, function($item) {{'label':
        $item.code, 'value': $item.code}})[]
    _shortNameList:
      transform: >-
        $map($.variables._workScheduleList, function($item) {{'label':
        $item.shortName.default, 'value': $item.shortName.default}})[]
    _longNameList:
      transform: >-
        $map($.variables._workScheduleList, function($item) {{'label':
        $item.name.default, 'value': $item.name.default}})[]
    _workdaysList:
      transform: >-
        $map($.variables._workScheduleList, function($item) {{'label':
        $item.workdays, 'value': $item.workdays}})[]
layout_options:
  tool_table:
    - id: export
      icon: icon-upload-simple-bold
  view_history_after_created: true
  show_dialog_form_save_add_button: true
  history_widget_header_options:
    duplicate: false
  export_all:
    type: base_total
  is_new_dynamic_form: true
  hide_action_row: true
  delete_multi_items: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    title: Edit
    icon: icon-pencil-simple
    type: ghost-gray
  - id: delete
    title: Delete
    icon: icon-trash
    type: ghost-gray
backend_url: api/ca-work-schedules
screen_name: null
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: companyId
    defaultName: CompanyCode
  - name: nationId
    defaultName: CountryCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Work Schedule Management
  parent:
    title: Set Up Working Hours
