id: TS.FS.MD.006
status: draft
sort: 229
user_created: 2aa5db12-fa54-46d6-8e05-9e421858de0c
date_created: '2024-07-08T03:05:19.512Z'
user_updated: abd14398-1d53-4e6f-9834-26328f0ebde0
date_updated: '2025-07-08T04:44:47.110Z'
title: Absence Group
requirement:
  time: 1746608082050
  blocks:
    - id: Uj7-AIQgTA
      type: paragraph
      data:
        text: Quản lý danh mục nhóm loại ngày nghỉ
  version: 2.30.7
screen_design: null
module: TS
local_fields:
  - code: code
    title: Absence Group Code
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    options__tabular__column_width: 12.5626
    show_sort: true
    pinned: true
  - code: country
    title: Country
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    options__tabular__column_width: 6.25
  - code: shortName
    title: Short Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    options__tabular__column_width: 10
  - code: longName
    title: Long Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    options__tabular__column_width: 16.25
  - code: status
    title: Status
    data_type:
      key: Boolean
      collection: data_types
    display_type:
      key: Boolean Tag
      collection: field_types
    options__tabular__column_width: 6.3125
  - code: lastEditer
    title: Last Updated By
    data_type:
      key: User
      collection: data_types
    display_type:
      key: Label
      collection: field_types
    options__tabular__column_width: 9
  - code: lastEditTime
    title: Last Updated On
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
    options__tabular__column_width: 11.25
  - code: note
    title: Note
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
    options__tabular__column_width: 12
mock_data:
  - code: '00000001'
    country: Việt Nam
    shortName:
      default: On leave
      english: On leave
      vietnamese: Nghỉ phép
    effectiveDate: 10/07/2024
    fullName:
      default: On leave
      english: On leave
      vietnamese: Nghỉ phép
    note:
      default: Content
      english: Content
      vietnamese: Nội dung
    status: true
  - code: '00000002'
    country: Việt Nam
    shortName:
      default: Take a sick leave
      english: Take a sick leave
      vietnamese: Nghỉ phép
    effectiveDate: 06/07/2024
    fullName:
      default: Take a sick leave
      english: Take a sick leave
      vietnamese: Nghỉ phép
    note:
      default: Content
      english: Content
      vietnamese: Nội dung
    status: true
local_buttons: null
layout: layout-table
form_config:
  formSize:
    create: middle
    edit: middle
    view: large
  fields:
    - type: group
      n_cols: 2
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - name: code
          label: Absence Group Code
          type: text
          placeholder: Enter Absence Group Code
          validators:
            - type: required
            - type: maxLength
              args: '8'
              text: Code should not exceed 8 characters
            - type: pattern
              args: ^[a-zA-Z0-9]*$
              text: >-
                The code must not contain special characters, except +, -, *, /,
                and spaces.
          _disabled:
            transform: $.extend.formType != 'create'
          suffix:
            type: char-count
          maxCharCount: 8
          _unvisible:
            transform: $.extend.formType = 'proceed'
        - name: countryCode
          label: Country
          type: select
          placeholder: Select Country
          outputValue: value
          _select:
            transform: $countriesList()
    - type: group
      n_cols: 2
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - name: shortName
          label: Short Name
          type: translation
          placeholder: Enter Short Name
          validators:
            - type: required
        - name: longName
          label: Long Name
          type: translation
          placeholder: Enter Long Name
          validators:
            - type: required
    - type: group
      n_cols: 2
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - type: dateRange
          label: Effective Date
          name: effectiveDate
          placeholder: dd/MM/yyyy
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
          validators:
            - type: required
          _value:
            transform: >-
              $.extend.formType = 'create' ?
              $not($exists($.fields.effectiveDate)) ?  $now()
        - type: radio
          label: Status
          name: status
          _value:
            transform: >-
              $.extend.formType = 'create' ? $not($exists($.fields.status)) ? 
              true
          radio:
            - value: true
              label: Active
            - value: false
              label: Inactive
    - name: code
      label: Absence Group Code
      type: text
      _condition:
        transform: $.extend.formType = 'view'
    - name: countryCode
      label: Country
      type: select
      _condition:
        transform: $.extend.formType = 'view'
      _select:
        transform: $countriesList()
    - name: shortName
      label: Short Name
      type: translation
      _condition:
        transform: $.extend.formType = 'view'
    - name: longName
      label: Long Name
      type: translation
      placeholder: Enter Long Name
      _condition:
        transform: $.extend.formType = 'view'
    - type: dateRange
      label: Effective Date
      name: effectiveDate
      mode: date-picker
      setting:
        format: dd/MM/yyyy
        type: date
      _condition:
        transform: $.extend.formType = 'view'
    - type: radio
      label: Status
      name: status
      _condition:
        transform: $.extend.formType = 'view'
      radio:
        - value: true
          label: Active
        - value: false
          label: Inactive
    - type: translationTextArea
      label: Note
      name: note
      placeholder: Enter Note
      textarea:
        maxCharCount: 1000
        autoSize:
          minRows: 3
      validators:
        - type: maxLength
          args: '1000'
          text: Maximum length is 1000 characters.
  sources:
    countriesList:
      uri: '"/api/picklists/COUNTRIES/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default, 'value':
        $item.code}})[]
      disabledCache: true
  historyHeaderTitle: '''Absence Group Details: '' & $.longName.default & '' ('' & $.code & '' )'''
filter_config:
  fields:
    - name: code
      labelType: type-grid
      label: Absence Group Code
      type: select
      mode: multiple
      _select:
        transform: $.variables._codeList
    - name: countryCode
      label: Country
      type: selectAll
      mode: multiple
      labelType: type-grid
      isLazyLoad: true
      _options:
        transform: $countriesList($.extend.limit, $.extend.page, $.extend.search)
    - name: shortName
      label: Short Name
      placeholder: Enter Short Name
      labelType: type-grid
      type: text
    - name: longName
      label: Long Name
      placeholder: Enter Long Name
      labelType: type-grid
      type: text
    - type: radio
      label: Status
      name: status
      labelType: type-grid
      value: ''
      radio:
        - label: All
          value: ''
        - value: true
          label: Active
        - value: false
          label: Inactive
    - name: updatedBy
      label: Last Updated By
      placeholder: Select Editor
      type: selectAll
      mode: multiple
      labelType: type-grid
      isLazyLoad: true
      _options:
        transform: $userList()
    - type: dateRange
      labelType: type-grid
      label: Last Updated On
      name: updatedAt
      setting:
        format: dd/MM/yyyy
        type: date
  filterMapping:
    - field: code
      operator: $in
      valueField: code.(value)
    - field: countryCode
      operator: $in
      valueField: countryCode.(value)
    - field: status
      operator: $eq
      valueField: status
    - field: effectiveDate
      operator: $between
      valueField: effectiveDate
    - field: shortNameFilter
      operator: $cont
      valueField: shortName
    - field: longNameFilter
      operator: $cont
      valueField: longName
    - field: updatedBy
      operator: $in
      valueField: updatedBy.(value)
    - field: updatedAt
      operator: $between
      valueField: updatedAt
  sources:
    countriesList:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    absenceTypeGroupList:
      uri: '"/api/ca-type-of-day-off-groups/by"'
      queryTransform: ''
      method: GET
      bodyTransform: ''
      headerTransform: ''
      resultTransform: $
      disabledCache: true
    userList:
      uri: '"/api/internal-users"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.userName, 'value':
        $item.userName}})
      disabledCache: true
      params:
        - limit
        - page
        - search
  variables:
    _absenceTypeGroupList:
      transform: $absenceTypeGroupList()
    _codeList:
      transform: >-
        $map($.variables._absenceTypeGroupList, function($item) {{'label':
        $item.code, 'value': $item.code}})[]
    _shortNameList:
      transform: >-
        $map($.variables._absenceTypeGroupList, function($item) {{'label':
        $item.shortName.default, 'value': $item.shortName.default}})[]
    _longNameList:
      transform: >-
        $map($.variables._absenceTypeGroupList, function($item) {{'label':
        $item.longName.default, 'value': $item.longName.default}})[]
layout_options:
  tool_table:
    - id: export
      icon: icon-download-simple
  show_dialog_form_save_add_button: true
  history_widget_header_options:
    duplicate: false
  view_history_after_created: true
  custom_history_backend_url: /api/ca-type-of-day-off-groups/insert-new-record
  export_all:
    type: base_total
  hide_action_row: true
  show_dialog_duplicate_button: false
  delete_multi_items: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    title: Edit
    icon: icon-pencil-simple
  - id: delete
    title: Delete
    icon: icon-trash
    type: ghost-gray
backend_url: api/ca-type-of-day-off-groups
screen_name: null
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: countryCode
    defaultName: CountryCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Absence Group
  parent:
    title: Set Up Working Hours
