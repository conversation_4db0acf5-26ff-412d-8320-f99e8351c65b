id: TS.FS.MD.007
status: draft
sort: 234
user_created: 2aa5db12-fa54-46d6-8e05-9e421858de0c
date_created: '2024-07-08T09:04:01.727Z'
user_updated: abd14398-1d53-4e6f-9834-26328f0ebde0
date_updated: '2025-07-11T10:08:11.347Z'
title: Absence Types
requirement:
  time: 1722480028885
  blocks:
    - id: Si6PKX8m2-
      type: paragraph
      data:
        text: loại ngày nghỉ
  version: 2.29.1
screen_design: null
module: TS
local_fields:
  - code: code
    title: Absence Type Code
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Hyperlink
      collection: field_types
    options__tabular__column_width: 12
    show_sort: true
    pinned: true
  - code: nationName
    title: Country
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: groupName
    title: Group
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: companyName
    title: Company
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: caTypeOfDayOffGroupName
    title: Absence Group
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: shortName
    title: Short Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
  - code: longName
    title: Long Name
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
  - code: status
    title: Status
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Boolean Tag
      collection: field_types
  - code: updatedBy
    title: Last Updated By
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Label
      collection: field_types
  - code: updatedAt
    title: Last Updated On
    data_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: data_types
    display_type:
      key: DD/MM/yyyy HH:mm:ss
      collection: field_types
  - code: note
    title: Note
    data_type:
      key: String
      collection: data_types
    display_type:
      key: Tooltip
      collection: field_types
mock_data:
  - code: '00000001'
    nation: Việt Nam
    shortName:
      default: On leave
      english: On leave
      vietnamese: Nghỉ phép
    absenceGroup: Nghỉ phép
    effectiveDate: 10/07/2024
    fullName:
      default: On leave
      english: On leave
      vietnamese: Nghỉ phép
    note:
      default: Content
      english: Content
      vietnamese: Nội dung
    status: true
  - code: '00000002'
    nation: Việt Nam
    shortName:
      default: Take a sick leave
      english: Take a sick leave
      vietnamese: Nghỉ phép
    absenceGroup: Nghỉ ốm
    effectiveDate: 06/07/2024
    fullName:
      default: Take a sick leave
      english: Take a sick leave
      vietnamese: Nghỉ ốm
    note:
      default: Content
      english: Content
      vietnamese: Nội dung
    status: true
local_buttons: null
layout: layout-table
form_config:
  defaultValidateError: Required fields have not been filled in completely (fields marked with *)
  fields:
    - type: group
      n_cols: 2
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - name: code
          label: Absence Type Code
          type: text
          placeholder: Enter Absence Type Code
          validators:
            - type: required
            - type: maxLength
              args: '20'
              text: Absence Type Code should not exceed 20 characters
            - type: pattern
              args: ^[a-zA-Z0-9]*$
              text: >-
                The code must not contain special characters, except +, -, *, /,
                and spaces.
          _disabled:
            transform: $.extend.formType != 'create'
        - name: nationId
          label: Country
          type: select
          clearFieldsAfterChange:
            - caTypeOfDayOffGroupId
          outputValue: value
          placeholder: Select Country
          _select:
            transform: $nationsList()
    - type: group
      n_cols: 2
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - name: groupId
          label: Group
          type: select
          outputValue: value
          clearFieldsAfterChange:
            - companyId
          placeholder: Select Group
          _select:
            transform: $.variables._groupsList
        - name: companyId
          label: Company
          type: select
          outputValue: value
          placeholder: Select Company
          _select:
            transform: >-
              $.variables._selectedGroup.id ?
              $companiesList($.variables._selectedGroup.id,
              $.fields.effectiveDate)
    - type: group
      n_cols: 2
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - name: caTypeOfDayOffGroupId
          label: Absence Group
          type: select
          outputValue: value
          placeholder: Select Absence Group
          _select:
            transform: $absenceGroupList($.fields.nationId,$.fields.effectiveDate)
          _validateFn:
            dependants:
              - $.fields.nationId
              - $.fields.effectiveDate
            transform: >-
              $not($isNilorEmpty($.fields.caTypeOfDayOffGroupId)) ?
              $isNilorEmpty($absenceGroupList($.fields.nationId,
              $.fields.effectiveDate, $.fields.caTypeOfDayOffGroupId)[0]) ?
              '_setSelectValueNull'
          validators:
            - type: required
        - name: shortName
          label: Short Name
          type: translation
          placeholder: Enter Short Name
          validators:
            - type: required
    - name: longName
      label: Long Name
      type: translation
      placeholder: Enter Long Name
      _condition:
        transform: $not($.extend.formType = 'view')
      validators:
        - type: required
    - type: group
      n_cols: 2
      _condition:
        transform: $not($.extend.formType = 'view')
      fields:
        - type: dateRange
          label: Effective Date
          name: effectiveDate
          placeholder: dd/MM/yyyy
          mode: date-picker
          setting:
            format: dd/MM/yyyy
            type: date
          validators:
            - type: required
          _value:
            transform: >-
              $.extend.formType = 'create' ?
              $not($exists($.fields.effectiveDate)) ?  $now()
        - type: radio
          label: Status
          name: status
          _value:
            transform: >-
              $.extend.formType = 'create' ? $not($exists($.fields.status)) ? 
              true
          radio:
            - value: true
              label: Active
            - value: false
              label: Inactive
          validators:
            - type: required
    - name: code
      label: Absence Type Code
      type: text
      _condition:
        transform: $.extend.formType = 'view'
    - name: nationName
      label: Country
      type: text
      _condition:
        transform: $.extend.formType = 'view'
    - name: groupName
      label: Group
      type: text
      _condition:
        transform: $.extend.formType = 'view'
    - name: companyName
      label: Company
      type: text
      _condition:
        transform: $.extend.formType = 'view'
    - name: caTypeOfDayOffGroupName
      label: Absence Group
      type: text
      _condition:
        transform: $.extend.formType = 'view'
    - name: shortName
      label: Short Name
      type: translation
      _condition:
        transform: $.extend.formType = 'view'
    - name: longName
      label: Long Name
      type: translation
      _condition:
        transform: $.extend.formType = 'view'
    - type: dateRange
      label: Effective Date
      name: effectiveDate
      mode: date-picker
      setting:
        format: dd/MM/yyyy
        type: date
      _condition:
        transform: $.extend.formType = 'view'
    - type: radio
      label: Status
      name: status
      _condition:
        transform: $.extend.formType = 'view'
      radio:
        - value: true
          label: Active
        - value: false
          label: Inactive
    - type: translationTextArea
      label: Note
      name: note
      placeholder: Enter Note
      textarea:
        maxCharCount: 1000
        autoSize:
          minRows: 3
      validators:
        - type: maxLength
          args: '1000'
          text: Maximum length is 1000 characters.
  sources:
    groupsList:
      uri: '"/api/groups"'
      method: GET
      queryTransform: >-
        {'page':1,'limit':10000,'filter': [{'field':'status','operator':
        '$eq','value':true},{'field':'effectiveDate','operator': '$lte','value':
        $.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code , 'id': $item.id}})[]
      disabledCache: true
      params:
        - effectiveDate
    companiesList:
      uri: '"/api/companies"'
      method: GET
      queryTransform: >-
        {'page':1,'limit':10000,'filter': [{'field':'status','operator':
        '$eq','value':true},{'field':'groupId','operator': '$eq','value':
        $.groupId }, {'field':'effectiveDate','operator': '$lte','value':
        $.effectiveDate}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - groupId
        - effectiveDate
    absenceGroupList:
      uri: '"/api/ca-type-of-day-off-groups/list-data"'
      method: GET
      queryTransform: >-
        {'filter': [{'field':'countryCode','operator':
        '$eq','value':$.countryCode},{'field':'effectiveDate','operator':
        '$lte','value': $.effectiveDate}, {'field': 'code', 'operator': '$eq',
        'value': $.code}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - countryCode
        - effectiveDate
        - code
    nationsList:
      uri: '"/api/picklists/COUNTRIES/values"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
  variables:
    _groupsList:
      transform: $groupsList($.fields.effectiveDate)
    _selectedGroup:
      transform: >-
        ($.fields.groupId; $filter($.variables._groupsList , function($v, $i,
        $a) { $v.value = $.fields.groupId }))
  historyHeaderTitle: '''Absence Type Details: '' & $.longName.default & '' ('' & $.code & '' )'''
filter_config:
  fields:
    - name: code
      label: Absence Code
      type: selectAll
      labelType: type-grid
      placeholder: Select Absence Code
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $absenceTypeCodeList($.extend.limit, $.extend.page, $.extend.search)
    - name: country
      label: Country
      type: selectAll
      labelType: type-grid
      mode: multiple
      placeholder: Select Country
      isLazyLoad: true
      _options:
        transform: $nationsList($.extend.limit, $.extend.page, $.extend.search)
    - name: group
      label: Group
      type: selectAll
      mode: multiple
      labelType: type-grid
      placeholder: Select Group
      isLazyLoad: true
      _options:
        transform: $groupsList($.extend.limit, $.extend.page, $.extend.search)
    - name: company
      label: Company
      type: selectAll
      mode: multiple
      labelType: type-grid
      placeholder: Select Company
      isLazyLoad: true
      _options:
        transform: $companiesList($.extend.limit, $.extend.page, $.extend.search)
    - name: shortName
      label: Short Name
      type: text
      labelType: type-grid
      placeholder: Enter Short Name
    - name: fullName
      label: Long Name
      labelType: type-grid
      type: text
      placeholder: Enter Long Name
    - name: absenceGroup
      label: Absence Group
      type: selectAll
      labelType: type-grid
      placeholder: Select Absence Group
      mode: multiple
      isLazyLoad: true
      _options:
        transform: $absenceGroupList($.extend.limit, $.extend.page, $.extend.search)
    - type: radio
      label: Status
      name: status
      labelType: type-grid
      value: ''
      radio:
        - value: ''
          label: All
        - value: true
          label: Active
        - value: false
          label: Inactive
    - name: lastUpdatedBy
      label: Last Updated By
      type: select
      labelType: type-grid
      placeholder: Select Last Updated By
      mode: multiple
      _select:
        transform: $userList()
    - type: dateRange
      label: Last Updated On
      name: lastUpdatedOn
      labelType: type-grid
      placeholder: dd/MM/yyyy
      setting:
        format: dd/MM/yyyy
        type: date
  filterMapping:
    - field: code
      operator: $in
      valueField: code.(value)
    - field: caTypeOfDayOffGroupId
      operator: $in
      valueField: absenceGroup.(value)
    - field: nationId
      operator: $in
      valueField: country.(value)
    - field: groupId
      operator: $in
      valueField: group.(value)
    - field: companyId
      operator: $in
      valueField: company.(value)
    - field: status
      operator: $eq
      valueField: status
    - field: effectiveDateFrom
      operator: $between
      valueField: effectiveDate
    - field: shortName
      operator: $cont
      valueField: shortName
    - field: longName
      operator: $cont
      valueField: fullName
    - field: updatedBy
      operator: $in
      valueField: lastUpdatedBy.(value)
    - field: updatedAt
      operator: $between
      valueField: lastUpdatedOn
  sources:
    groupsList:
      uri: '"/api/groups"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    companiesList:
      uri: '"/api/companies"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    absenceGroupList:
      uri: '"/api/ca-type-of-day-off-groups"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.longName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    absenceTypeLongNameList:
      uri: '"/api/ca-type-of-days-offs/by"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.longName.default, 'value':
        $item.longName.default}})[]
      disabledCache: true
    absenceTypeShortNameList:
      uri: '"/api/ca-type-of-days-offs/by"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.shortName.default, 'value':
        $item.shortName.default}})[]
      disabledCache: true
    absenceTypeCodeList:
      uri: '"/api/ca-type-of-days-offs"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.shortName.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    nationsList:
      uri: '"/api/picklists/COUNTRIES/values/pagination"'
      method: GET
      queryTransform: >-
        {'limit': $.limit,'page': $.page,'search': $.search, 'filter':
        [{'field':'status','operator': '$eq','value':true}]}
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($.data, function($item) {{'label': $item.name.default & ' (' &
        $item.code & ')', 'value': $item.code}})[]
      disabledCache: true
      params:
        - limit
        - page
        - search
    userList:
      uri: '"/api/internal-users"'
      method: GET
      queryTransform: ''
      bodyTransform: ''
      headerTransform: ''
      resultTransform: >-
        $map($, function($item) {{'label': $item.userName, 'value':
        $item.userName}})
      disabledCache: true
layout_options:
  tool_table:
    - id: export
      icon: icon-download-simple
  show_dialog_form_save_add_button: true
  history_widget_header_options:
    duplicate: false
  view_history_after_created: true
  custom_history_backend_url: /api/ca-type-of-days-offs/insert-new-record
  export_all:
    type: base_total
  hide_action_row: true
  show_dialog_duplicate_button: false
  delete_multi_items: true
layout_options__header_buttons:
  - id: create
    title: Create
    icon: plus
    type: primary
options: null
create_form: {}
layout_options__footer_buttons: null
layout_options__row_actions:
  - id: edit
    title: Edit
    icon: icon-pencil-simple
    type: ghost-gray
  - id: delete
    title: Delete
    icon: icon-trash
    type: ghost-gray
backend_url: api/ca-type-of-days-offs
screen_name: null
layout_options__actions_many:
  - id: export
    title: Export selected
    icon: icon-upload-simple-bold
    type: secondary
  - id: delete
    title: Delete
    icon: icon-trash-bold
    type: secondary
parent: null
detail_function_spec: null
payment_config: {}
multi_typing_config: {}
inherited_default_detail: false
key_detail: id
default_filter: null
pre_condition_config: {}
permission_key:
  - name: companyId
    defaultName: CompanyCode
  - name: nationId
    defaultName: CountryCode
screen_designs: []
function_specs: []
fields: []
children: []
menu_item:
  title: Absence Types
  parent:
    title: Set Up Working Hours
