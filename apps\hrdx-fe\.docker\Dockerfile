FROM bitnami/nginx:1.25 AS runner
LABEL author="QuanNDM2 <<EMAIL>>"
LABEL maintainer="QuanNDM2 <<EMAIL>>"

## Copy our default nginx config
# COPY ./.docker/nginx.conf /opt/bitnami/nginx/conf/bitnami/default.conf
## Remove default nginx website
#RUN rm -rf /app/*
USER 0
RUN install_packages gettext-base

RUN echo 'location / {  root   /app; index  index.html index.htm;  try_files $uri $uri/ /index.html; }' > /opt/bitnami/nginx/conf/bitnami/default.conf

COPY dist/apps/hrdx-fe/ /app

# COPY apps/hrdx-fe/.docker/nginx.conf  /etc/nginx/conf.d/default.conf

RUN chown -R 1001:1001 /app/configs
USER 1001

ENV PPX_MENU_LOAD_FROM_BACKEND=
ENV PPX_AUTHORITY=
ENV PPX_CLIENT_ID=
ENV PPX_SECURE_ROUTES=

# to enable dangerous input sanitize
ENV ENABLE_DANGEROUS_INPUT_SANITIZE=

EXPOSE 8080

CMD ["/bin/sh",  "-c",  "envsubst < /app/configs/config.template.json > /app/configs/config.json && /opt/bitnami/scripts/nginx/run.sh"]

# CMD ["/bin/sh",  "-c",  "envsubst < /app/assets/configs-template/settings.template.json > /app/assets/configs/settings.json && /opt/bitnami/scripts/nginx/run.sh"]
# CMD ["/bin/sh",  "-c",  "/opt/bitnami/scripts/nginx/run.sh"]
