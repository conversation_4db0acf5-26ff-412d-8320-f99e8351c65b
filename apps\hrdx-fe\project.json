{"name": "hrdx-fe", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "prefix": "app", "sourceRoot": "apps/hrdx-fe/src", "tags": [], "targets": {"build": {"executor": "@angular-devkit/build-angular:browser", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/apps/hrdx-fe", "index": "apps/hrdx-fe/src/index.html", "main": "apps/hrdx-fe/src/main.ts", "polyfills": ["zone.js"], "tsConfig": "apps/hrdx-fe/tsconfig.app.json", "inlineStyleLanguage": "less", "assets": [{"glob": "**/*", "input": "apps/hrdx-fe/public"}, {"glob": "**/*", "input": "libs/hrdx-design/src/assets/icons", "output": "assets/icons"}, {"glob": "**/*", "input": "libs/hrdx-design/src/assets/modules", "output": "assets/modules"}, {"glob": "**/*", "input": "libs/hrdx-design/src/assets/illustrations", "output": "assets/illustrations"}, {"glob": "**/*", "input": "libs/hrdx-design/src/assets/font_awesome", "output": "assets/font_awesome"}, {"glob": "**/*", "input": "node_modules/ng2-pdfjs-viewer/pdfjs", "output": "assets/pdfjs/"}], "styles": ["apps/hrdx-fe/src/styles.less"], "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "10mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "1mb"}], "outputHashing": "all"}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"options": {"port": 4200, "publicHost": "http://localhost:4200", "proxyConfig": "apps/hrdx-fe/proxy.conf.json"}, "executor": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "hrdx-fe:build:production"}, "development": {"buildTarget": "hrdx-fe:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"executor": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "hrdx-fe:build"}}, "lint": {"executor": "@nx/eslint:lint"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/hrdx-fe/jest.config.ts"}}, "container": {"executor": "@nx-tools/nx-container:build", "dependsOn": ["build"], "options": {"engine": "docker", "push": true, "pull": true, "file": "apps/hrdx-fe/.docker/Dockerfile", "metadata": {"images": ["rc.paas.ttgt.vn/hrdx/hrdx-fe", "$CI_REGISTRY/$REGISTRY_PATH/$CI_PROJECT_PATH_SLUG-web", "$FPT_CI_REGISTRY/$FPT_REGISTRY_PATH/$CI_PROJECT_PATH_SLUG-web"], "load": true, "tags": ["$CI_PIPELINE_ID", "latest", "type=schedule", "type=ref,event=branch", "type=ref,event=tag", "type=ref,event=pr"]}}}}}