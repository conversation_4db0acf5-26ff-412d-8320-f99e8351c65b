import { registerLocaleData } from '@angular/common';
import {
  HTTP_INTERCEPTORS,
  HttpClient,
  provideHttpClient,
  withFetch,
  withInterceptors,
  withInterceptorsFromDi,
} from '@angular/common/http';
import vi from '@angular/common/locales/vi';
import {
  APP_INITIALIZER,
  ApplicationConfig,
  EnvironmentProviders,
  importProvidersFrom,
  inject,
  Injectable,
} from '@angular/core';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import {
  ActivatedRouteSnapshot,
  provideRouter,
  Resolve,
  Route,
  Router,
} from '@angular/router';
import {
  MSAL_GUARD_CONFIG,
  MSAL_INSTANCE,
  MSAL_INTERCEPTOR_CONFIG,
  MsalBroadcastService,
  MsalGuard,
  MsalInterceptor,
  MsalService,
} from '@azure/msal-angular';
import { LayoutDynamicComponent } from '@hrdx-fe/layout-dynamic';
import { LayoutWelcomeComponent } from '@hrdx-fe/layout-welcome';
import {
  FCInterceptor,
  FunctionSpec,
  MasterdataService,
} from '@hrdx-fe/shared';
import { Menu } from '@hrdx/hrdx-design';
import axios from 'axios';
import { cloneDeep, isArray, isEmpty } from 'lodash';
import { en_US, NZ_DATE_LOCALE, provideNzI18n } from 'ng-zorro-antd/i18n';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { firstValueFrom, Observable, tap } from 'rxjs';
import { MasterLayoutComponent } from '../master-layout/master-layout.component';
import { appRoutes } from './app.routes';
import {
  authInterceptor,
  authSessionInterceptor,
  timezoneInterceptor,
} from './auth/auth-interceptor';
import {
  MSALGuardConfigFactory,
  MSALInstanceFactory,
  MSALInterceptorConfigFactory,
} from './auth/auth.config';
import { enUS } from 'date-fns/locale';
import {
  provideTranslateService,
  TranslateLoader,
  TranslateModule,
} from '@ngx-translate/core';
import { AppTranslateLoader } from './app.trans';
import { keywordEncodeInterceptor } from './auth/keyword-encode-interceptor';
import { dangerousInputSanitizeInterceptor } from './auth/dangerous-input-sanitize-interceptor';
registerLocaleData(vi);
export function HttpLoaderFactory(http: HttpClient) {
  return new AppTranslateLoader(http);
}
export function provideNzIcons(): EnvironmentProviders {
  return importProvidersFrom(NzIconModule);
}
@Injectable({
  providedIn: 'root',
})
export class FunctionSpecResolver implements Resolve<FunctionSpec> {
  masterDataService = inject(MasterdataService);
  resolve(
    route: ActivatedRouteSnapshot,
  ): Observable<FunctionSpec> | Promise<FunctionSpec> | FunctionSpec {
    const fsId = route.paramMap.get('id');

    if (!fsId) return {};
    return this.masterDataService.getFunctionSpecById(fsId);
  }
}
export function initializeApp(
  router: Router,
  service: MasterdataService,
  resolver: FunctionSpecResolver,
  authService: MsalService,
  guard: MsalGuard,
): () => Promise<void> {
  return async () => {
    await axios
      .get('configs/config.json')
      .then((res) => {
        service.setConfig(res.data);
      })
      .catch((err) => {
        console.log(err);
      });
    await initializeAuth(authService);
    await firstValueFrom(authService.initialize());
    await firstValueFrom(
      guard.canActivate(
        router.routerState.snapshot.root,
        router.routerState.snapshot,
      ),
    );
    const me = await firstValueFrom(await service.getUserByMe());
    if (!me) return;
    return await initializeRoutes(router, service, resolver);
  };
}

export async function initializeAuth(authService: MsalService) {
  if (!authService) return;
  await firstValueFrom(authService.initialize());
  try {
    if (authService.instance?.getAllAccounts()?.length > 0) {
      const authRes = await firstValueFrom(
        authService.acquireTokenSilent({ scopes: ['user.read'] }),
      );
      // console.log('authRes', authRes);
      if (authRes?.accessToken) {
        localStorage.setItem('accessToken', authRes.accessToken);
      }
    } else {
      localStorage.removeItem('accessToken');
    }
  } catch (error) {
    console.log(error);
  }
}

export async function initializeRoutes(
  router: Router,
  masterDataService: MasterdataService,
  resolver: FunctionSpecResolver,
) {
  await firstValueFrom(
    masterDataService.initModule().pipe(
      tap((menusByModules) => {
        const routesByModule: Route[] = menusByModules.map((m) => ({
          path: m.key,
          component: MasterLayoutComponent,
          children: [{ path: '', component: LayoutWelcomeComponent }],
          data: { moduleId: m.key },
          canActivate: [MsalGuard],
        }));
        const routes = [...routesByModule, ...cloneDeep(router.config)];

        menusByModules.forEach(({ value, key }) => {
          const menuItems = structuredClone(value);
          // const moduleId = key;
          const dynamicRoutes = menuItems
            .map((menu) => getRoutes(menu, masterDataService, resolver))
            .flat();
          dynamicRoutes.forEach((route) => {
            const idx = routes.findIndex((r) => r.path === key);
            if (idx !== -1) {
              if (!routes[idx].children) {
                routes[idx].children = [];
              }
              routes[idx].children?.push({
                ...route,
                path: route.path?.split('/').slice(1).join('/'),
              });
            }
          });
        });
        // console.log(routes);
        router.resetConfig(routes);

        return menusByModules;
      }),
    ),
  );

  // //TODO - check menu không có quyền thì redirect sang màn hình 403
  // const pathname = window.location.pathname;
  // const paths = pathname?.split('/');
  // const moduleId = paths[1];
  // const menuId = paths[2];

  // if (moduleId && menuId) {
  //   const getMenu = router.config.find((moduleItem) => {
  //     return (
  //       moduleItem.data?.['moduleId'] === moduleId &&
  //       !!moduleItem.children?.find(
  //         (menuItem) => menuItem.data?.['fsId'] === menuId,
  //       )
  //     );
  //   });

  //   const checkMenuId = menuId.split('.')?.length > 1;

  //   // console.log('router', router);
  //   // console.log('getMenu', getMenu);
  //   if (!getMenu && checkMenuId) {
  //     const fs = await firstValueFrom(
  //       masterDataService.getFunctionSpecById(menuId),
  //     );
  //     // console.log('fs', fs);
  //     if (!isEmpty(fs)) {
  //       router.navigate(['/no-permission']);
  //     }
  //   }
  // }

  // console.log('Initializing routes done');
}

function getRoutes(
  menu: Menu | undefined,
  service: MasterdataService,
  resolver: FunctionSpecResolver,
): Route[] {
  if (!menu) return [];
  if (menu.children && menu.children.length > 0) {
    const routes = menu.route
      ? ['/', ...menu.route.split('/').slice(1)]
      : undefined;
    if (menu.fsdFE)
      return [
        {
          path: isArray(routes)
            ? routes.filter((p) => p && p !== '/').join('/')
            : (routes ?? ''),
          component: LayoutDynamicComponent,
          data: {
            fsId: menu.fsdFE,
            permissionActionId: menu.faceCode,
          },
          // resolve: {
          //   functionSpec: resolver,
          // },
        } as Route,
        ...menu.children
          .map((child) => getRoutes(child, service, resolver))
          .flat(),
      ];
    return menu.children
      .map((child) => getRoutes(child, service, resolver))
      .flat();
  }
  const routes = menu.route
    ? ['/', ...menu.route.split('/').slice(1)]
    : undefined;
  // console.log(menu)
  return [
    {
      path: isArray(routes)
        ? routes.filter((p) => p && p !== '/').join('/')
        : (routes ?? ''),
      component: LayoutDynamicComponent,
      data: {
        fsId: menu.fsdFE,
        permissionActionId: menu.faceCode,
      },
      // resolve: {
      //   functionSpec: resolver,
      // },
    } as Route,
  ];
}

export function getAppConfig(config: any): ApplicationConfig {
  const enableSanitizer = config?.ENABLE_DANGEROUS_INPUT_SANITIZE === true;
  return {
    providers: [
      ...(enableSanitizer
        ? [provideHttpClient(withInterceptors([dangerousInputSanitizeInterceptor]))]
        : [])
    ]
  };
}

export const appConfig: ApplicationConfig = {
  providers: [
    provideTranslateService({
      defaultLanguage: 'en',
    }),
    importProvidersFrom(
      TranslateModule.forRoot({
        loader: {
          provide: TranslateLoader,
          useFactory: HttpLoaderFactory,
          deps: [HttpClient],
        },
      }),
    ),
    // provideZoneChangeDetection({ eventCoalescing: true }),
    provideRouter(appRoutes),
    MasterdataService,
    FunctionSpecResolver,
    {
      provide: APP_INITIALIZER,
      useFactory: initializeApp,
      deps: [
        Router,
        MasterdataService,
        FunctionSpecResolver,
        MsalService,
        MsalGuard,
      ],
      multi: true,
    },
    provideNzIcons(),
    provideNzI18n(en_US),
    provideAnimationsAsync(),
    // { provide: NZ_I18N, useValue: en_US },
    // for login with azure
    // importProvidersFrom(BrowserModule),
    // provideNoopAnimations(),
    provideHttpClient(
      withInterceptorsFromDi(),
      withFetch(),
      withInterceptors([
        // encode the query params
        keywordEncodeInterceptor,
        authInterceptor,
        timezoneInterceptor,
        authSessionInterceptor,
      ]),
    ),
    {
      provide: HTTP_INTERCEPTORS,
      useClass: MsalInterceptor,
      multi: true,
    },
    {
      provide: MSAL_INSTANCE,
      useFactory: MSALInstanceFactory,
    },
    {
      provide: MSAL_GUARD_CONFIG,
      useFactory: MSALGuardConfigFactory,
    },
    {
      provide: MSAL_INTERCEPTOR_CONFIG,
      useFactory: MSALInterceptorConfigFactory,
    },
    { provide: HTTP_INTERCEPTORS, useClass: FCInterceptor, multi: true },
    MsalService,
    MsalGuard,
    MsalBroadcastService,
    { provide: NZ_DATE_LOCALE, useValue: enUS },
  ],
};
