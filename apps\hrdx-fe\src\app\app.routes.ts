import { Route } from '@angular/router';
import { MsalGuard } from '@azure/msal-angular';
import { HomeLayoutComponent } from '../home-layout/home-layout.component';
import { LayoutLoginComponent } from './auth/layouts';
import { LayoutErrorStateComponent } from './auth/layouts/layout-error-state/layout-error-state.component';
import { LayoutNoPermissionComponent } from './auth/layouts/layout-no-permission/layout-no-permission.component';
import { InitAuthSessionComponent } from './auth/init-auth-session.component';
import { PostLogoutComponent } from './auth/post-logout.component';

export const appRoutes: Route[] = [
  // {
  //   path: 'PR',
  //   component: MasterLayoutComponent,
  //   children: [{ path: '', component: LayoutWelcomeComponent }],
  //   data: { moduleId: 'PR' },
  //   canActivate: [MsalGuard],
  // },
  // {
  //   path: 'HR',
  //   component: MasterLayoutComponent,
  //   children: [{ path: '', component: LayoutWelcomeComponent }],
  //   data: { moduleId: 'HR' },
  //   canActivate: [MsalGuard],
  // },
  // {
  //   path: 'FO',
  //   component: MasterLayoutComponent,
  //   children: [{ path: '', component: LayoutWelcomeComponent }],
  //   data: { moduleId: 'FO' },
  //   canActivate: [MsalGuard],
  // },
  // {
  //   path: 'SYS',
  //   component: MasterLayoutComponent,
  //   children: [{ path: '', component: LayoutWelcomeComponent }],
  //   data: { moduleId: 'SYS' },
  //   canActivate: [MsalGuard],
  // },
  // {
  //   path: 'PIT',
  //   component: MasterLayoutComponent,
  //   children: [{ path: '', component: LayoutWelcomeComponent }],
  //   data: { moduleId: 'PIT' },
  //   canActivate: [MsalGuard],
  // },
  // {
  //   path: 'TS',
  //   component: MasterLayoutComponent,
  //   children: [{ path: '', component: LayoutWelcomeComponent }],
  //   data: { moduleId: 'TS' },
  //   canActivate: [MsalGuard],
  // },
  // {
  //   path: 'COM',
  //   component: MasterLayoutComponent,
  //   children: [{ path: '', component: LayoutWelcomeComponent }],
  //   data: { moduleId: 'COM' },
  //   canActivate: [MsalGuard],
  // },
  // {
  //   path: 'General',
  //   component: MasterLayoutComponent,
  //   children: [{ path: '', component: LayoutWelcomeComponent }],
  //   data: { moduleId: 'General' },
  //   canActivate: [MsalGuard],
  // },
  // {
  //   path: 'ELN',
  //   component: MasterLayoutComponent,
  //   children: [{ path: '', component: LayoutWelcomeComponent }],
  //   data: { moduleId: 'ELN' },
  //   canActivate: [MsalGuard],
  // },
  // {
  //   path: 'INS',
  //   component: MasterLayoutComponent,
  //   children: [{ path: '', component: LayoutWelcomeComponent }],
  //   data: { moduleId: 'INS' },
  //   canActivate: [MsalGuard],
  // },
  {
    path: '',
    component: HomeLayoutComponent,
    canActivate: [MsalGuard],
  },
  {
    path: 'auth',
    children: [
      { path: 'login', component: LayoutLoginComponent },
      { path: 'unauthorized', component: LayoutErrorStateComponent },
        // for init auth session
      { path: 'init-session', component: InitAuthSessionComponent },
      { path: 'post-logout', component: PostLogoutComponent },
    ],
  },
  {
    path: 'not-found',
    component: LayoutErrorStateComponent,
    data: { error: 'not-found' },
  },
  {
    path: 'no-permission',
    component: LayoutNoPermissionComponent,
    data: { error: 'no-permission' },
  },
  {
    path: '**',
    component: LayoutErrorStateComponent,
    data: { error: 'not-found' },
  },

];
