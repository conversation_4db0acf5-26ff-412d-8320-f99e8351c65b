import {
  MsalGuardConfiguration,
  MsalInterceptorConfiguration,
} from '@azure/msal-angular';
import {
  BrowserCacheLocation,
  InteractionType,
  IPublicClientApplication,
  LogLevel,
  PublicClientApplication,
} from '@azure/msal-browser';

//TODO: should define env variables in .env file
const clientId = 'cba3ed1b-3e2c-4982-9d01-32a241967270';
const protectedResourceMapKey = [
  '/api',
  //TODO - should move reporting to bff
  // TODO: should define all protected resources in env file, not hardcoded here
  'https://betest-ppx.fis.vn',
  'https://beuat-ppx.fpt.com',
  'https://betesthr-ppx.fis.vn',
  'https://bedev-ppx.fis.vn',
];

export function loggerCallback(logLevel: LogLevel, message: string) {
  // console.log(message);
}

export function MSALInstanceFactory(): IPublicClientApplication {
  // const tenantId = '0ca6562c-9ccc-4a41-a66f-ea9edeb021fc';
  return new PublicClientApplication({
    auth: {
      clientId,
      // authority: `https://login.microsoftonline.com/${tenantId}`,
      authority: `https://login.microsoftonline.com/common`,
      redirectUri: '/',
      postLogoutRedirectUri: '/auth/login',
    },
    cache: {
      cacheLocation: BrowserCacheLocation.LocalStorage,
    },
    system: {
      allowNativeBroker: false, // Disables WAM Broker
      loggerOptions: {
        loggerCallback,
        logLevel: LogLevel.Info,
        piiLoggingEnabled: false,
      },
    },
  });
}

export function MSALInterceptorConfigFactory(): MsalInterceptorConfiguration {
  const protectedResourceMap = new Map<string, Array<string>>();
  protectedResourceMap.set('https://graph.microsoft.com/v1.0/me', [
    'user.read',
  ]);
  protectedResourceMapKey.forEach((key) => {
    protectedResourceMap.set(key, [`${clientId}/.default`]);
  });

  return {
    interactionType: InteractionType.Redirect,
    protectedResourceMap,
  };
}

export function MSALGuardConfigFactory(): MsalGuardConfiguration {
  return {
    interactionType: InteractionType.Redirect,
    authRequest: {
      // scopes: ['user.read'],
      scopes: [`${clientId}/.default`],
      prompt: 'select_account',
    },
    // loginFailedRoute: '/login-failed',
  };
}
