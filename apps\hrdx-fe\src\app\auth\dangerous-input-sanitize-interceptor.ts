import { HttpRequest, HttpHandlerFn, HttpParams } from '@angular/common/http';
import { sanitizeDangerousInput } from '@hrdx-fe/shared';

// List of patterns: '*' at the end means prefix match, otherwise exact match
const MATCH_PATTERNS = [
  '/api/personals/create-employee',   // exact match
  '/api/employee-groups/*'
];

function matchesUrl(url: string, patterns: string[]): boolean {
  return patterns.some(pattern => {
    if (pattern.endsWith('*')) {
      const prefix = pattern.slice(0, -1);
      return url.startsWith(prefix);
    }
    return url === pattern;
  });
}

export function dangerousInputSanitizeInterceptor(
  req: HttpRequest<unknown>,
  next: HttpHandlerFn,
) {
  // Only sanitize if the URL matches any pattern (relative or absolute)
  const url = req.url.startsWith('http') ? new URL(req.url).pathname : req.url;
  if (!matchesUrl(url, MATCH_PATTERNS)) {
    return next(req);
  }

  // Sanitize body (if present)
  let sanitizedBody = req.body;
  if (sanitizedBody && typeof sanitizedBody === 'object') {
    sanitizedBody = sanitizeDangerousInput(sanitizedBody);
  }

  // Sanitize query params (if present)
  let sanitizedParams = req.params;
  if (sanitizedParams && typeof sanitizedParams === 'object' && 'keys' in sanitizedParams) {
    const newParams: Record<string, any> = {};
    sanitizedParams.keys().forEach((key: string) => {
      newParams[key] = sanitizeDangerousInput(sanitizedParams.getAll(key));
    });
    sanitizedParams = new HttpParams({ fromObject: newParams });
  }

  const reqWithSanitized = req.clone({
    body: sanitizedBody,
    params: sanitizedParams,
  });
  return next(reqWithSanitized);
} 