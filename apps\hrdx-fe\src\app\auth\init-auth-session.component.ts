import { NoResult } from './../../../../../libs/hrdx-design/src/lib/components/empty-state/empty-state.stories';
import { CommonModule } from '@angular/common';
import { Component, computed, inject, OnInit, signal } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { AuthService } from '@hrdx-fe/shared';
import {
  ButtonComponent,
  IllustrationsComponent,
  IllustrationsSize,
  IllustrationsType,
  LoadingComponent,
} from '@hrdx/hrdx-design';

@Component({
  standalone: true,
  imports: [
    LoadingComponent,
    CommonModule,
    ButtonComponent,
    IllustrationsComponent,
  ],
  selector: 'app-init-auth-session',
  template: `<div class="init-auth-container">
    @switch (initSessionState()) {
      @case ('initializing') {
        <hrdx-loading></hrdx-loading>
        <span>Initializing auth session...</span>
      }
      @case ('failed') {
        <hrdx-illustrations
          [type]="illustrationsConfig().type"
          [size]="illustrationsConfig().size"
          [isSubDescription]="false"
        ></hrdx-illustrations>
        <span>Failed to initialize auth session</span>
        <hrdx-button
          (click)="initAuthSession()"
          [type]="'primary'"
          [title]="'Try again'"
        ></hrdx-button>
      }
    }
  </div>`,
  styles: [
    `
      .init-auth-container {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        gap: 1rem;
        height: 100vh;
        width: 100vw;
        position: fixed;
        top: 0;
        left: 0;
        z-index: 2;
      }
    `,
  ],
})
export class InitAuthSessionComponent implements OnInit {
  private authService = inject(AuthService);
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  initSessionState = signal<'initializing' | 'initialized' | 'failed' | 'none'>(
    'none',
  );
  illustrationsConfig = computed(() => {
    const state = this.initSessionState();
    switch (state) {
      case 'failed':
        return {
          type: IllustrationsType.NoResult,
          size: IllustrationsSize.Medium,
        };
      default:
        return {
          type: IllustrationsType.CompleteTask,
          size: IllustrationsSize.Medium,
        };
    }
  });

  async ngOnInit() {
    this.initAuthSession();
  }

  protected initAuthSession = async () => {
    this.initSessionState.set('initializing');
    const res = await this.authService.createAuthSession();
    if (!res.success) {
      this.initSessionState.set('failed');
      return;
    }
    this.initSessionState.set('initialized');
    this.router.navigate([this.getRedirectUrl() ?? '/']);
  };

  private getRedirectUrl() {
    return this.route.snapshot.queryParamMap.get('redirectUrl');
  }
}
