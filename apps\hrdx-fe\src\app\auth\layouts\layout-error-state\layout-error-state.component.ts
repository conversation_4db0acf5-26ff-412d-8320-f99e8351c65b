import { CommonModule } from '@angular/common';
import { Component, inject, OnInit, signal } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { MsalService } from '@azure/msal-angular';
import { AccountInfo } from '@azure/msal-browser';
import { ResponseStatusCode } from '@hrdx-fe/shared';
import {
  EmptyStateComponent,
  StateSizeEnum,
  StateTypeEnum,
} from '@hrdx/hrdx-design';
import { NzSafeAny } from 'ng-zorro-antd/core/types';
import { injectRouteData } from 'ngxtension/inject-route-data';

@Component({
  selector: 'app-layout-error-state',
  standalone: true,
  imports: [CommonModule, EmptyStateComponent],
  templateUrl: './layout-error-state.component.html',
  styleUrl: './layout-error-state.component.less',
})
export class LayoutErrorStateComponent implements OnInit {
  readonly stateType = StateTypeEnum;
  readonly stateSize = StateSizeEnum;
  readonly timeout = 5000;
  router = inject(Router);
  type = signal(this.stateType.NotFound);
  dataRoute = injectRouteData();
  subTitle = signal('Sorry, the page you visited does not exist.');
  showButton = signal(true);
  timeoutId: any;
  account: AccountInfo | null = null;

  constructor(
    private route: ActivatedRoute,
    private authService: MsalService,
  ) {
    this.account = this.authService.instance.getActiveAccount();
  }

  ngOnInit() {
    const queryParams = this.route.snapshot.queryParams as {
      code?: number;
      message?: string;
    };
    const code = +(queryParams?.code ?? 0);
    if (code === ResponseStatusCode.UNAUTHORIZED) {
      this.type.set(this.stateType.Unauthorized);
      let subTitle =
        'Your account has been blocked. Please contact support for assistance.';
      const message = queryParams.message;
      if (message) {
        subTitle = message;
      }
      this.subTitle.set(subTitle);
      this.showButton.set(false);
    }
  }

  logout() {
    this.authService.logoutRedirect({
      account: this.account,
    });
  }

  navigateToRedirectUrl() {
    const queryParams: Record<string, NzSafeAny> =
      this.route.snapshot.queryParams;
    const redirectUrl = queryParams?.['redirectUrl'];
    if (redirectUrl) {
      this.router.navigate([redirectUrl]);
    }
  }

  onButtonClicked() {
    switch (this.type()) {
      case this.stateType.NotFound: {
        this.router.navigate(['/']);
        break;
      }

      case this.stateType.Unauthorized: {
        this.router.navigate(['/']);
        break;
      }
    }
  }
}
