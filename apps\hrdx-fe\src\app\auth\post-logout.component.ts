import { CommonModule } from '@angular/common';
import { Component, inject, OnInit, signal } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { AuthService } from '@hrdx-fe/shared';
import { LoadingComponent } from '@hrdx/hrdx-design';

@Component({
  standalone: true,
  imports: [LoadingComponent, CommonModule],
  selector: 'app-post-logout',
  template: `<div class="loading-container" *ngIf="isProcessing()">
    <hrdx-loading></hrdx-loading>
  </div>`,
  styles: [
    `
      .loading-container {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100vh;
        width: 100vw;
        position: fixed;
        top: 0;
        left: 0;
        z-index: 2;
      }
    `,
  ]
})
export class PostLogoutComponent implements OnInit {
  private authService = inject(AuthService);
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  isProcessing = signal(false);


  async ngOnInit() {
    this.isProcessing.set(true);
    await this.authService.clearAuthSession();
    this.isProcessing.set(false);
    this.router.navigate([this.getRedirectUrl() ?? '/']);
  }

  private getRedirectUrl() {
    return this.route.snapshot.queryParamMap.get('redirectUrl');
  }
}
