<nz-layout class="home-layout">
  <nz-header>
    <hrdx-header-search
      (userActionClicked)="handleUserActionClick($event)"
      [account]="account"
      [userInfo]="userInfo"
      [avatarLink]="avatarLink()"
    ></hrdx-header-search>
  </nz-header>
  <nz-content>
    <main>
      <div class="modules">
        @for (module of modules(); track $index) {
          <ng-container
            [ngTemplateOutlet]="moduleCardTemplate"
            [ngTemplateOutletContext]="{ module: module }"
          ></ng-container>
        }
      </div>
    </main>
  </nz-content>
</nz-layout>

<ng-template #moduleCardTemplate let-module="module">
  <div
    [ngClass]="['module', module.disabled ? 'disabled' : '']"
    (click)="!module.disabled && onModuleClick(module.id)"
  >
    <!-- <hrdx-icon class="module-icon" [icon]="module.icon"></hrdx-icon> -->
    <img
      class="module-icon"
      src="/assets/modules/{{ module.id }}{{
        module.disabled ? '-disabled' : ''
      }}.png"
      alt=""
    />
    <h4 class="module-name">{{ module.name }}</h4>
  </div>
</ng-template>
