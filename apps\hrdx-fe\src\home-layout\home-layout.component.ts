import { CommonModule } from '@angular/common';
import { Component, effect, inject, signal } from '@angular/core';
import { Router } from '@angular/router';
import {
  AuthService,
  BffService,
  ModuleStore,
  TAccountInfo,
  UserStore,
  UtilService,
} from '@hrdx-fe/shared';
import { HeaderSearchComponent } from '@hrdx/hrdx-design';
import { NzSafeAny } from 'ng-zorro-antd/core/types';
import { NzLayoutModule } from 'ng-zorro-antd/layout';
import { catchError, of } from 'rxjs';

@Component({
  selector: 'app-home-layout',
  standalone: true,
  imports: [CommonModule, NzLayoutModule, HeaderSearchComponent],
  templateUrl: './home-layout.component.html',
  styleUrl: './home-layout.component.less',
})
export class HomeLayoutComponent {
  #moduleStore = inject(ModuleStore);
  #userStore = inject(UserStore);
  utilService = inject(UtilService);

  router = inject(Router);
  account: TAccountInfo | null = null;

  modules = this.#moduleStore.modules;
  private authService = inject(AuthService);

  constructor() {
    this.account = this.authService.activeAccount;
  }

  onModuleClick(moduleId: string) {
    this.router.navigate([moduleId]);
  }

  handleUserActionClick(id: string) {
    switch (id) {
      case 'logout': {
        this.logout();
      }
    }
  }

  async logout(popup?: boolean) {
    await this.utilService.setEncryptedCookie('ppx', null, 0);
    if (popup) {
      this.authService.logoutPopup({
        mainWindowRedirectUri: '/',
      });
    } else {
      this.authService.logoutRedirect();
    }
  }

  get userInfo() {
    return this.#userStore.user();
  }

  _service = inject(BffService);
  // create avatar link
  avatarLink = signal<string>('');
  avatarLinkEffect = effect(async () => {
    const data = this.userInfo as NzSafeAny;
    if (data && data?.avatarFile) {
      const url = await this._service.generateAvatarLink(
        data?.avatarFile,
        'HR_000',
      );
      this.avatarLink.set(url);
    }
  });
}
