import { bootstrapApplication } from '@angular/platform-browser';
import { appConfig, getAppConfig } from './app/app.config';
import { AppComponent } from './app/app.component';

// Load runtime config before bootstrapping Angular
fetch('/configs/config.json')
  .then(response => response.json())
  .then(config => {
    (window as any).__env__ = config;
    // Merge the default appConfig and the conditional one
    const mergedConfig = {
      ...appConfig,
      providers: [
        ...(appConfig.providers ?? []),
        ...((getAppConfig(config).providers) ?? [])
      ]
    };
    return bootstrapApplication(AppComponent, mergedConfig);
  })
  .then(() => hideLoading())
  .catch((err) => console.error(err));

function hideLoading() {
  const loading = document.querySelector('#app-loading');
  if (loading) {
    loading.classList.add('hidden');
  }
}
