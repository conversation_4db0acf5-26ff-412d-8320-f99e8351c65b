<nz-layout class="app-layout">
  <div class="loading-container" *ngIf="isLoading()">
    <hrdx-loading></hrdx-loading>
  </div>
  <nz-header>
    <!-- <hrdx-header-menu [modules]="modules()"> </hrdx-header-menu> -->
    <hrdx-header-search
      (userActionClicked)="handleUserActionClick($event)"
      [account]="account"
      [userInfo]="userInfo"
      [avatarLink]="avatarLink()"
    ></hrdx-header-search>
  </nz-header>
  <nz-layout>
    <nz-sider
      nzCollapsible
      [nzWidth]="'242px'"
      [nzCollapsedWidth]="64"
      nzBreakpoint="md"
      [(nzCollapsed)]="isCollapsed"
      [nzTrigger]="null"
      class="sider"
    >
      <nz-layout
        [ngClass]="['sidebar-layout', isCollapsed() ? 'collapsed' : '']"
      >
        <hrdx-top-left-menu
          [modules]="modules()"
          [selectedModuleId]="moduleId()"
          [isCollapsed]="isCollapsed()"
          (departmentClicked)="routing($event)"
          (changeModuleId)="onChangeModuleId($event)"
        ></hrdx-top-left-menu>
        <div class="sidebar-menu-wrapper">
          <hrdx-sidebar-menu [menus]="_menus()" [isCollapsed]="isCollapsed()">
          </hrdx-sidebar-menu>
        </div>
        <nz-footer>
          <img src="/favicon.png" alt="logo" />
          <div class="copyright" *ngIf="!isCollapsed()">
            &copy; {{ getCurrentYear() }}, Copyright by FPT
          </div>
          <!-- button handle collapse sider -->
          <hrdx-button
            [onlyIcon]="true"
            [icon]="
              isCollapsed() ? 'icon-arrow-right-bold' : 'icon-arrow-left-bold'
            "
            [type]="'tertiary'"
            [size]="'xsmall'"
            (clicked)="handleCollapse()"
            class="collapse-btn"
          ></hrdx-button>
        </nz-footer>
      </nz-layout>
    </nz-sider>

    <nz-content>
      <div class="inner-content">
        <router-outlet></router-outlet>
      </div>
    </nz-content>
  </nz-layout>
</nz-layout>
<hrdx-progressing-popover></hrdx-progressing-popover>
