import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  computed,
  effect,
  inject,
  OnD<PERSON>roy,
  OnInit,
  signal,
} from '@angular/core';
import { NavigationStart, Router, RouterModule } from '@angular/router';
import { NzBreadCrumbModule } from 'ng-zorro-antd/breadcrumb';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzLayoutModule } from 'ng-zorro-antd/layout';
import { NzMenuModule } from 'ng-zorro-antd/menu';
// import { HeaderMenuComponent } from './../../../../libs/hrdx-design/src/lib/components/header-menu/header-menu.component';
import {
  AuthActions,
  AuthService,
  BffService,
  LayoutStore,
  ModuleStore,
  TAccountInfo,
  UserStore,
  UtilService,
} from '@hrdx-fe/shared';
import {
  ButtonComponent,
  HeaderSearchComponent,
  LoadingComponent,
  ProgressingPopoverComponent,
  SidebarMenuComponent,
  TopLeftMenuComponent,
} from '@hrdx/hrdx-design';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { injectParams } from 'ngxtension/inject-params';
import { injectRouteData } from 'ngxtension/inject-route-data';
import { TranslateService } from '@ngx-translate/core';
import { catchError, of, Subject, takeUntil } from 'rxjs';
import { NzSafeAny } from 'ng-zorro-antd/core/types';

@Component({
  selector: 'app-master-layout',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    NzIconModule,
    NzLayoutModule,
    NzMenuModule,
    NzBreadCrumbModule,
    SidebarMenuComponent,
    NzInputModule,
    TopLeftMenuComponent,
    HeaderSearchComponent,
    ButtonComponent,
    ProgressingPopoverComponent,
    NzModalModule,
    LoadingComponent,
  ],
  templateUrl: './master-layout.component.html',
  styleUrl: './master-layout.component.less',
  // changeDetection: ChangeDetectionStrategy.Default,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class MasterLayoutComponent implements OnInit, OnDestroy {
  #layoutStore = inject(LayoutStore);
  #moduleStore = inject(ModuleStore);
  #userStore = inject(UserStore);
  utilService = inject(UtilService);

  modules = this.#moduleStore.modules;
  menus = this.#layoutStore.menus;

  isLoading = this.#layoutStore.loading;

  _menus = computed(() => {
    const menus = this.menus();
    const user = this.#userStore.user();
    if (!user) return menus;

    for (const menu of menus) {
      if (menu.children) {
        const empProfile = menu.children.find(
          (m) => m.title === 'Employee Profile',
        );
        if (empProfile) {
          empProfile.route = `/HR/employees/${user.employeeCode}`;
          break;
        }
      }
    }
    return menus;
  });
  moduleId = this.#layoutStore.currentModuleId;
  isCollapsed = signal(false);
  account: TAccountInfo | null = null;

  params = injectParams();

  routeData = injectRouteData();

  private readonly destroy$ = new Subject<void>();

  constructor(
    private authService: AuthService,
    private translate: TranslateService,
  ) {
    this.account = this.authService.activeAccount;
  }

  ngOnInit() {
    // listen route change to reset auth action to READ
    this.router.events.pipe(takeUntil(this.destroy$)).subscribe((event) => {
      if (event instanceof NavigationStart) {
        this.#layoutStore.setCurrentAuthAction(AuthActions.Read);
      }
    });
  }

  _service = inject(BffService);
  // create avatar link
  avatarLink = signal<string>('');
  avatarLinkEffect = effect(async () => {
    const data = this.userInfo as NzSafeAny;
    if (data && data?.avatarFile) {
      const url = await this._service.generateAvatarLink(
        data?.avatarFile,
        'HR_000',
      );
      this.avatarLink.set(url);
    }
  });

  ngOnDestroy() {
    this.destroy$.next(); // Emit to signal teardown
    this.destroy$.complete(); // Complete the destroy observable
  }

  getCurrentYear() {
    return new Date().getFullYear();
  }

  routeDataEffect = effect(
    () => {
      this.#layoutStore.selectModule(this.routeData()['moduleId']);
    },
    { allowSignalWrites: true },
  );

  handleCollapse() {
    this.isCollapsed.set(!this.isCollapsed());
  }

  handleUserActionClick(id: string) {
    switch (id) {
      case 'logout': {
        this.logout();
        break;
      }
      case 'language': {
        const currentLang = this.translate.currentLang;
        const newLang = currentLang === 'vi' ? 'en' : 'vi';
        this.translate.use(newLang);
      }
    }
  }

  async logout(popup?: boolean) {
    await this.utilService.setEncryptedCookie('ppx', null, 0);
    if (popup) {
      this.authService.logoutPopup({
        mainWindowRedirectUri: '/',
      });
    } else {
      this.authService.logoutRedirect();
    }
  }
  router = inject(Router);

  routing(route: string) {
    this.router.navigate([route]);
  }

  get userInfo() {
    return this.#userStore.user() ?? {};
  }

  onChangeModuleId(moduleId: string) {
    this.#layoutStore.selectModule(moduleId);
  }
}
