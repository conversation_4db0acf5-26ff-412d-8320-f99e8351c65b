import { ExtendBoot } from '@hrdx-bff/backend-common';
import { Span, TraceService } from '@metinseylan/nestjs-opentelemetry';
import { Cache } from '@nestjs/cache-manager';
import { HttpException, Logger } from '@nestjs/common';
import { CrudRequest } from '@nestjsx/crud';
import { SFields } from '@nestjsx/crud-request';
import { urlJoin } from '@peoplex/url-join';
import * as axios from 'axios';
import * as Handlebars from 'handlebars';
import {
  cloneDeep,
  compact,
  isArray,
  isNil,
  isEmpty,
  isObject,
  isPlainObject,
  isString,
  keys,
  values,
} from 'lodash';
import { MemoryStoredFile } from 'nestjs-form-data';
import { CrudServiceUpstream } from '../models/crud-service.upstream.model';
import {
  CrudModel,
  CrudModelConfig,
  CrudModelDetail,
  CrudModelPagination,
  CrudResponseTypes,
  JsonValue,
  ParamConfigType,
  safeAny,
} from '../models/crud.config';
import jsonata = require('jsonata');
import moment = require('moment');
import _ = require('lodash');

function getValue(obj: safeAny, keys: string[]) {
  const results = keys.map((keyPath) => {
    const subKeys = keyPath.split('.');

    return subKeys.reduce((innerObj, k) => {
      if (Array.isArray(innerObj)) {
        if (innerObj.length === 0) {
          return null;
        }

        return innerObj.map((item) => item[k]);
      }

      return (innerObj || {})[k];
    }, obj);
  });
  return keys.length === 1 ? results[0] : results;
}

type MultiLanguageBE = { languageCode: string; [key: string]: safeAny };

function setValue(
  obj: safeAny,
  keys: string[],
  value: safeAny,
  isSubObj = false,
) {
  if (keys.length === 1) {
    if (isArray(value) && isSubObj)
      value.map((item) => obj.push({ [keys[0]]: item }));
    else obj[keys[0]] = value;
    return;
  }

  if (!obj[keys[0]]) {
    obj[keys[0]] = isArray(value) ? [] : {};
  }

  setValue(obj[keys[0]], keys.slice(1), value, true);
}

export class DynamicCrudService {
  private models: CrudModel[];
  private upstreamController?: string;
  private config: safeAny;

  private upstreamUrl: string | undefined;
  private upstreamPath: string | undefined;

  constructor(
    pathFile: string,
    public cacheManager: Cache,
    public readonly traceService: TraceService,
  ) {
    const boot = new ExtendBoot({ filePath: pathFile });
    this.models = boot.get<CrudModel[]>('models', []);

    this.upstreamController = boot.get<string | undefined>(
      'upstream',
      undefined,
    );

    this.config = boot.get('', {});

    this.upstreamUrl = this.config?.upstream;
    this.upstreamPath = this.config?.upstreamPath;
  }

  private processSCondition(
    s: safeAny,
    modelConfig?: CrudModelConfig,
    query?: safeAny,
    elemMatch = false,
  ) {
    const key = keys(s)[0];
    switch (key) {
      case '$and':
      case '$or': {
        if (s[key].length === 0) return '';

        if (elemMatch) {
          const res = this.elemMatch(
            s[key].map((item: safeAny) => {
              const _key = keys(item)[0];
              if (!_key || !item) return '';
              if (this.isKeyInUpstreamConfig(_key, query)) return '';
              return this.processSCondition(item, modelConfig, query);
            }),
          ) as string;

          return res ? `(${res})` : '';
        } else {
          // Original logic
          const res = s[key]
            .map((item: safeAny) => {
              const _key = keys(item)[0];
              if (!_key || !item) return '';
              if (this.isKeyInUpstreamConfig(_key, query)) return '';
              return this.processSCondition(item, modelConfig, query);
            })
            .filter((v: safeAny) => v)
            .join(key === '$and' ? ',' : '|');
          return res ? `(${res})` : '';
        }
      }
      default:
        return this.processItem(key, s[key], modelConfig);
    }
  }
  elemMatch(res: safeAny) {
    // Check if res is valid
    if (!res || !res.length || !res[0]) {
      return '';
    }

    let str = res[0];

    // Make sure str is a string
    if (typeof str !== 'string') {
      return '';
    }

    // Remove outermost redundant parentheses
    str = str.replace(/^\(\(\(|\)\)\)$/g, '');
    const partSplit = str
      .split(')|')
      .map((group: safeAny) => group.replace(/[()]/g, '').split(','));
    // Split conditions by '|'
    const lastPart = partSplit[partSplit.length - 1];
    const getElemLastPart = lastPart.slice(0, partSplit[0].length);
    const restOfLastPart = lastPart.slice(partSplit[0].length);

    const groupedElemConditions: safeAny = {};

    const parts = restOfLastPart;
    const elemtPart = [
      ...partSplit.slice(0, partSplit.length - 1),
      getElemLastPart,
    ];
    // Organize conditions by key
    elemtPart.forEach((group) => {
      group.forEach((condition: safeAny) => {
        if (!condition) return;

        const parts = condition.split('=');
        if (parts.length < 2) return;

        const key = parts[0].trim();
        const value = parts[1].trim();

        if (!groupedElemConditions[key]) {
          groupedElemConditions[key] = [];
        }
        groupedElemConditions[key].push(value);
      });
    });
    const keys = Object.keys(groupedElemConditions);
    if (elemtPart.length < 2) {
      return `(${str})`;
    }
    if (!keys || keys.length < 2) {
      return `(${str})`;
    }

    // Safely get the records from the first key
    const firstKey = keys[0];
    if (!firstKey || !groupedElemConditions[firstKey]) return '';

    const records = Array.from(groupedElemConditions[firstKey]);
    if (!records.length) return '';
    const combined = records.map((record, item) => {
      return `(${keys
        .map((key) => {
          // Check if key exists and is iterable before using Array.from
          if (!groupedElemConditions[key]) return '';

          const values = Array.from(groupedElemConditions[key]);
          return `${key} = ${values.includes(record) ? record : values[item]}`;
        })
        .filter(Boolean)
        .join(',')})`;
    });

    const restQuery = parts;
    return `(${combined.join('|')} ${restQuery.length > 0 ? `,${restQuery.map((e: safeAny) => `(${e})`).join(',')}` : ''})`;
  }

  private isKeyInUpstreamConfig(key: string, query?: safeAny) {
    if (!query) return false;
    const regex = /[:{]+(.*?)[:}]+/g;
    const queryString = JSON.stringify(query);
    const queryKeys = [
      ...new Set([...queryString.matchAll(regex)].map((match) => match[1])),
    ];
    return queryKeys.includes(key);
  }

  private convertFilterValue(value: string) {
    // [',', '|', '(', ')', '/i', '^'].forEach((item) => {
    ['|', '(', ')', '/i', '^'].forEach((item) => {
      if (typeof value === 'string') value = value.replace(item, `\\\\${item}`);
    });
    return value;
  }

  private processItem(
    field: string,
    value: safeAny,
    modelConfig?: CrudModelConfig,
  ) {
    const _field = modelConfig?.[field]?.from;
    const valueField = this.convertType(
      modelConfig?.[field]?.typeOptions,
      values(value)?.[0],
    );

    if (!_field || (!valueField && valueField !== 0)) return '';
    if (isPlainObject(value)) {
      const operator = keys(value)[0];
      switch (operator) {
        case '$elemMatch':
          if (Array.isArray(valueField)) {
            const filterStr = valueField
              .map((v: safeAny) => `${_field} = ${this.convertFilterValue(v)}`)
              .join(',');
            return `(${filterStr})`;
          }
          return valueField !== ''
            ? `${_field} = ${this.convertFilterValue(valueField)}`
            : '';
        case '$in':
          if (Array.isArray(valueField)) {
            const filterStr = valueField
              .map((v: safeAny) => `${_field} = ${this.convertFilterValue(v)}`)
              .join('|');
            return `(${filterStr})`;
          }
          return `${_field} = ${this.convertFilterValue(valueField)}`;
        case '$notin':
          if (Array.isArray(valueField)) {
            const filterStr = valueField
              .map((v: safeAny) => `${_field} != ${this.convertFilterValue(v)}`)
              .join(',');
            return `(${filterStr})`;
          }
          return `${_field} != ${this.convertFilterValue(valueField)}`;
        case '$eq':
          return `${_field} = ${this.convertFilterValue(valueField)}`;
        case '$ne':
          return `${_field} != ${this.convertFilterValue(valueField)}`;
        case '$gt':
          return `${_field} > ${this.convertFilterValue(valueField)}`;
        case '$lt':
          return `${_field} < ${this.convertFilterValue(valueField)}`;
        case '$gte':
          return `${_field} >= ${this.convertFilterValue(valueField)}`;
        case '$lte':
          return `${_field} <= ${this.convertFilterValue(valueField)}`;
        case '$cont':
          return `${_field} =* ${this.convertFilterValue(valueField)}`;
        case '$between':
          if (!valueField.toDate) return `${_field} >= ${valueField}`;
          return `${_field} >= ${valueField.formDate} , ${_field} <= ${valueField.toDate}`;
        case '$startsWith':
          return `${_field} ^ ${this.convertFilterValue(valueField)}`;
      }
    }

    return `${_field} = ${this.convertFilterValue(valueField)}`;
  }

  @Span()
  private buildQuery(req: CrudRequest) {
    const {
      limit = 10,
      page = 1,
      offset: skip = 0,
      filter,
      // fields = [],
      // join = [],
      paramsFilter,
      search,
      authPersist,
    } = req.parsed;

    const span = this.traceService.getSpan();

    let { sort = [] } = req.parsed;
    if (search?.$and) {
      search.$and = compact(search.$and);
    }

    if (search.$or) {
      search.$or = compact(search.$or);
    }

    const routeConfig = structuredClone(authPersist?.['routeConfig']);
    // const defaultQuery = cloneDeep(authPersist?.['defaultQuery']);

    if (!routeConfig) throw new HttpException('Route config not found', 400);
    const modelName = routeConfig.model ?? '_';

    filter.forEach((f) => {
      const v: safeAny = {};
      v[f.operator] = f.value;
      if (!search.$and?.some((item) => f.field in item))
        search.$and?.push({ [f.field]: v });
    });
    search.$and = search.$and?.reverse();
    const paramObject = this.config?.crudConfig?.params;

    search.$and = compact(
      search.$and?.filter((x) => {
        if (!Object.keys(x).length) {
          return null;
        }

        const key = Object.keys(x);
        const paramConfig = paramObject[key[0]];
        if (!paramConfig) {
          return x;
        }

        if (
          !paramConfig.configType ||
          paramConfig.configType.includes(ParamConfigType.FILTER)
        ) {
          return x;
        }
        return null;
      }),
    );
    const modelConfig = this.models.find((m) => m.name === modelName)?.config;

    if (!sort || sort.length === 0) {
      sort = req.options?.query?.sort ?? [];
    }
    const options = {
      page,
      skip,
      limit,
      sort: sort
        .map((s) => {
          if (!modelConfig?.[s.field]?.from) return '';
          return `${modelConfig?.[s.field].from} ${s.order.toLowerCase()}`;
        })
        .filter((v) => v)
        .join(','),
    };

    filter.forEach((f) => {
      const v: safeAny = {};

      v[f.operator] = f.value;
      (search as SFields)[f.field] = v;
    });

    let searchCondition = '';
    if (
      (search as safeAny)?.['$and']?.[0]?.['$and']?.find(
        (it: safeAny) => it['search'],
      )
    ) {
      searchCondition = (
        (search as safeAny)['$and'][0]['$and'] as safeAny[]
      ).find((it: safeAny) => it['search'])['search']['$eq'];
    }
    if ((search as safeAny)['search']) {
      searchCondition = (search as safeAny)['search']['$eq'];
    }

    const where = this.processSCondition(
      search,
      modelConfig,
      routeConfig.upstreamConfig?.query,
      routeConfig.elemMatch,
    );
    const queryFilter: Record<string, safeAny> = {};
    const ignoreKeys: string[] = [];

    const processFilters = (items: safeAny[]) => {
      const key = Object.entries(items)[0][0];
      const operator = keys(Object.entries(items)[0][1])[0];
      const value = (Object.entries(items)[0][1] as safeAny)['$eq'];

      const valueField = this.convertType(
        modelConfig?.[key]?.typeOptions,
        value,
        [],
        [],
        modelConfig,
      );

      if (['$between'].includes(operator)) ignoreKeys.push(key);
      queryFilter[key] =
        typeof valueField === 'string'
          ? this.convertFilterValue(valueField)
          : valueField;
    };

    (search.$and as safeAny[])?.forEach((item) => {
      if (item.$and as safeAny[])
        (item.$and as safeAny[])?.forEach((itemFilter) => {
          processFilters(itemFilter as safeAny[]);
        });
      else processFilters(item as safeAny[]);
    });

    const url = (routeConfig.upstreamConfig?.path ?? '')
      .replace(/::\{/g, '{{{')
      .replace(/\}::/g, '}}}')
      .replace(/:\{/g, '{{')
      .replace(/\}:/g, '}}');
    const param = paramsFilter.reduce(
      (acc, param) => ({ ...acc, [param.field]: param.value }),
      {},
    );

    routeConfig.upstreamConfig.path = Handlebars.compile(url)({
      ...queryFilter,
      ...param,
    });

    const buildQueryData = {
      routeConfig: routeConfig,
      headers: authPersist?.['headers'] ?? {},
      query: {
        queryFilter: queryFilter,
        filter: routeConfig.isExtendedFilter
          ? where
          : this.transformQuery(where, ignoreKeys),
        search: searchCondition,
        options: options,
      },
      modelName: modelName,
    };

    const buildQueryDataLog = structuredClone(buildQueryData);

    if (process.env['NODE_ENV'] === 'production') {
      delete buildQueryDataLog.headers;
    }

    span.setAttributes({
      'bff.buildQuery': JSON.stringify(buildQueryDataLog, null, 2),
    });
    return buildQueryData;
  }

  private transformQuery(input: string, ignoreKeys?: string[]) {
    const items = input
      .split(/[,|]/)
      .map((item) => {
        const operatorMatch = item.match(/(=|!=|>=|<=|>|<|=\*)/);
        if (!operatorMatch) {
          return null;
        }
        const operator = operatorMatch[0].trim();
        const key = item.split(operator)[0].trim().replace(/\(/g, '');
        const value = item.split(operator)[1].trim().replace(/\)/g, '');

        return { key, operator, value };
      })
      .filter(
        (item): item is { key: string; operator: string; value: string } =>
          item !== null,
      );

    const result: {
      [key: string]: {
        values: { operator: string; value: string }[];
      };
    } = {};

    // Group conditions by key
    for (const { key, operator, value } of items) {
      if (!result[key]) {
        result[key] = { values: [] };
      }
      result[key].values.push({ operator, value });
    }

    // Build the final query string
    return Object.entries(result)
      .map(([key, { values }]) => {
        // If there are multiple conditions for the same key
        if (values.length > 1) {
          if (ignoreKeys?.includes(key)) {
            return values.map((v) => `${key}${v.operator}${v.value}`).join(',');
          }

          // Filter conditions by operators
          const greaterConditions = values.filter(
            (v) => v.operator === '>' || v.operator === '>=',
          );
          const lessConditions = values.filter(
            (v) => v.operator === '<' || v.operator === '<=',
          );
          const otherConditions = values.filter(
            (v) => !['>', '>=', '<', '<='].includes(v.operator),
          );

          // Handle range conditions
          let rangeCondition = '';
          if (greaterConditions.length > 0 && lessConditions.length > 0) {
            const maxGreater = Math.max(
              ...greaterConditions.map((v) => parseInt(v.value)),
            );
            const minLess = Math.min(
              ...lessConditions.map((v) => parseInt(v.value)),
            );

            // Check if the range is valid
            if (maxGreater <= minLess) {
              rangeCondition = `${key}>=${maxGreater},${key}<=${minLess}`;
            }
          }

          // Build condition strings
          const conditionStrings = values
            .map((v) => `${key}${v.operator}${v.value}`)
            .join('|');

          // If there are valid range conditions
          if (rangeCondition) {
            return `(${rangeCondition})`;
          }

          return `(${conditionStrings})`;
        }
        return `${key}${values[0].operator}${values[0].value}`; // Single condition
      })
      .join(','); // Join different keys with |
  }

  private defaultTransformResponseData(
    _data: safeAny,
    model: CrudModel | undefined,
  ) {
    // const object: safeAny = {};

    if (!model) {
      return _data;
    }
    if (isString(model?.config) && model?.config === '*') {
      return _data;
    }

    //TODO: handle typeOptions
    if (!model.config) return _data;
    return this.transformResponseObject(model.config, _data);
  }
  private transformArrayObject(
    config: Record<string, CrudModelDetail>,
    source: Record<string, safeAny>,
  ) {
    const sourceKey = config[Object.keys(config)[0]].from;
    if (!sourceKey) {
      return null;
    }
    const sourceArray = source[sourceKey];
    if (!Array.isArray(sourceArray)) {
      return null;
    }
    const arrayObject: Array<safeAny> = [];
    sourceArray.forEach((item, index) => {
      const mappedObject: { [key: string]: safeAny } = {};
      Object.keys(config).forEach((key) => {
        const sourceKey = config[key].from;
        mappedObject[key] = sourceKey
          ? (this.transformResponseValue(
              source[sourceKey][index],
              config[key].typeOptions,
            ) ?? null)
          : null;
      });
      arrayObject.push(mappedObject);
    });
    return arrayObject;
  }

  private transformResponseObject(
    modelDetails: Record<string, CrudModelDetail>,
    _source: Record<string, safeAny> & { languages?: MultiLanguageBE[] },
  ) {
    const target: Record<string, safeAny> = {};
    const source = structuredClone(_source);
    source.languages?.forEach((lang: MultiLanguageBE) => {
      const { languageCode, ...rest } = lang;
      if (languageCode) {
        Object.keys(rest).forEach((key) => {
          if (
            key === 'id' || // TODO: only transform if have func stringToMultiLang
            key === 'createdBy' ||
            key === 'createdAt' ||
            key === 'updatedBy' ||
            key === 'updatedAt'
          )
            return;

          //only transform if have func stringToMultiLang
          const filteredDetails = Object.keys(modelDetails).filter(
            (k) =>
              modelDetails[k]?.from === key &&
              modelDetails[k]?.typeOptions?.func === 'stringToMultiLang',
          );

          if (filteredDetails.length == 0) return;
          const defaultValue = _source[key];

          // if (!_data[key]) {
          // }
          if (!isObject(source[key])) {
            source[key] = {};
            source[key]['default'] = defaultValue;
          }
          source[key][languageCode] = rest[key];
        });
      }
    });

    Object.keys(modelDetails).map((key) => {
      const item = modelDetails[key] as CrudModelDetail;
      const { from, typeOptions, objectChildren, arrayChildren, arrayObjects } =
        item;
      if (!from) {
        return undefined;
      }
      let value: safeAny;
      const dataFrom = Array.isArray(from)
        ? from.map((f) => getValue(source, f?.split(',')))
        : getValue(source, from.split(','));
      if (
        Array.isArray(dataFrom) &&
        dataFrom.every(
          (item) => isNil(item) || (typeof item === 'string' && isEmpty(item)),
        )
      )
        return null;

      if (
        (isNil(dataFrom) ||
          (typeof dataFrom === 'string' && isEmpty(dataFrom?.trim()))) &&
        from !== '$' &&
        isNil(arrayObjects)
      ) {
        return null;
      }

      if (objectChildren) {
        const obj = this.transformResponseObject(
          objectChildren,
          from === '$' ? source : dataFrom,
        );
        value = Object.keys(obj).length > 0 ? obj : null;
      } else if (arrayChildren) {
        value = dataFrom.map((item: safeAny) => {
          const _item = this.transformResponseObject(arrayChildren, item);
          return _item;
        });
      } else if (arrayObjects) {
        value = this.transformArrayObject(arrayObjects, source);
      } else {
        value = this.transformResponseValue(dataFrom, typeOptions);
      }
      //TODO: handle typeOptions
      setValue(target, key.split('.'), value);
      return undefined;
    });
    return target;
  }

  private transformResponseValue(
    dataFrom: safeAny,
    typeOptions?: { func: string; args: any } & JsonValue,
  ) {
    let value: safeAny;
    if (typeOptions) {
      switch (typeOptions.func) {
        case 'stringToMultiLang':
          value = {
            default: isObject(dataFrom)
              ? (dataFrom as Record<string, string>)['default']
              : dataFrom,
            'vi-VN': dataFrom?.vn,
            'en-US': dataFrom?.en,
          };
          break;

        case 'YNToBoolean':
          value = dataFrom === 'Y';
          break;
        case 'AIToBoolean':
          value = dataFrom === 'A';
          break;
        case 'YNToBooleanTag':
          value = dataFrom === 'Y' ? 'Active' : 'Inactive';
          break;
        case 'timestampToDateTime':
          value = new Date(+dataFrom * 1000);
          break;
        case 'intYearToDateTime':
          value = new Date(parseInt(dataFrom), 0, 1);
          break;
        case 'monthStringToDateTime': {
          const formatStr = typeOptions.args ?? 'YYYY-MM';
          value = moment(dataFrom, formatStr).utc().endOf('day').toISOString();

          // value = moment(dataFrom)
          //   .utcOffset(moment().utcOffset())
          //   .format(formatStr);
          break;
        }
        case 'dayStringToDateTime': {
          const formatStr = typeOptions.args ?? 'DD/MM';
          value = moment(dataFrom, formatStr).toISOString();
          break;
        }
        case 'timeStringToDateTime': {
          const formatStr = typeOptions.args ?? 'HH:mm';
          value = moment(dataFrom, formatStr).toISOString();
          break;
        }
        case 'toString':
          value = dataFrom.toString();
          break;
        case 'arrayValueToString':
          value = dataFrom.join(',');
          break;
        case 'arrayValueToBreaklineString':
          value = dataFrom.join('\n');
          break;
        case 'numberToCurrency':
          value = dataFrom;
          if (typeof dataFrom !== 'number') break;
          if (
            !isNil(typeOptions.args) &&
            typeof typeOptions.args === 'number'
          ) {
            value = _.round(dataFrom, typeOptions.args);
          }
          value = value.toString()?.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
          break;
        case 'fieldsToNameCode':
          if (Array.isArray(dataFrom) && dataFrom.length > 0) {
            if (dataFrom.every((item) => !Array.isArray(item))) {
              value = dataFrom[1]
                ? `${dataFrom[0]} (${dataFrom[1]})`
                : `${dataFrom[0]}`;
            } else {
              value = dataFrom[0].map((item: string, index: number) => {
                const secondValue =
                  dataFrom[1] && dataFrom[1][index] ? dataFrom[1][index] : '';
                return `${item}${secondValue ? ` (${secondValue})` : ''}`;
              });
            }
          }
          break;
        case 'join': {
          if (!Array.isArray(dataFrom)) {
            value = dataFrom;
            break;
          }
          const separator = typeOptions.args ?? ', ';
          value = dataFrom.filter((item) => !isNil(item)).join(separator);
          break;
        }
        case 'mapValueToLabel': {
          const options = typeOptions.args as {
            value: string;
            label: string;
          }[];
          value =
            options.find((item) => item.value === dataFrom)?.label ?? dataFrom;
          break;
        }
        default:
          value = dataFrom;
      }
    } else {
      value = dataFrom;
    }

    return value;
  }

  private setLanguage(
    languages: MultiLanguageBE[],
    languageCode: 'vn' | 'en',
    value: safeAny,
    key: string[],
  ) {
    const languageIdx = languages.findIndex(
      (lang) => lang.languageCode === languageCode,
    );
    if (languageIdx !== -1) {
      setValue(languages[languageIdx], key, value);
    } else {
      const idx =
        languages.push({
          languageCode: languageCode,
        }) - 1;
      setValue(languages[idx], key, value);
    }
  }

  private convertType(
    typeOptions: safeAny,
    value: safeAny,
    key?: string[],
    languages?: MultiLanguageBE[],
    modelDetails?: Record<string, CrudModelDetail>,
  ) {
    if (value === 'NULL') return 'null';
    if (!typeOptions?.func) return value;
    switch (typeOptions.func) {
      case 'stringToMultiLang':
        if (languages && key) {
          if (value?.['vi-VN'])
            this.setLanguage(languages, 'vn', value?.['vi-VN'], key);
          if (value?.['en-US'])
            this.setLanguage(languages, 'en', value?.['en-US'], key);
        }
        if (!isObject(value)) {
          return value;
        }
        return (value as safeAny)?.default;
      case 'upperCase':
        return typeof value === 'string' && value !== null
          ? value.toUpperCase()
          : value;
      case 'YNToBoolean':
        return value && JSON.parse(value.toString()) ? 'Y' : 'N';
      case 'AIToBoolean':
        return value && JSON.parse(value) ? 'A' : 'I';
      case 'YNToBooleanTag':
        return value === 'Active' ? 'Y' : 'N';
      case 'timestampToDateTime':
        if (isArray(value) && value?.length > 1) {
          return {
            formDate: this.timestampToDateTime(value?.[0], 'dateHour'),
            toDate: this.timestampToDateTime(value?.[1], 'dateHour'),
          };
        } else {
          //xử lý date chỉ lấy ngày ko lấy giờ theo UTC.
          return this.timestampToDateTime(
            value,
            typeOptions.args,
            typeOptions.isNewVersion,
          );
        }
      case 'intYearToDateTime':
        if (isArray(value) && value?.length > 1) {
          return {
            formDate: new Date(value?.[0]).getFullYear(),
            toDate: new Date(value?.[1]).getFullYear(),
          };
        }
        return new Date(value).getFullYear();
      case 'monthStringToDateTime': {
        const formatStr = typeOptions.args ?? 'YYYY-MM';
        return moment(value).format(formatStr);
        // return moment(value).utcOffset(moment().utcOffset()).format(formatStr);
      }
      case 'dayStringToDateTime': {
        const formatStr = typeOptions.agrs ?? 'DD/MM';
        return moment(value)?.format(formatStr);
      }
      case 'timeStringToDateTime': {
        const formatStr = typeOptions.args ?? 'HH:mm';
        return moment(value)?.format(formatStr);
      }
      case 'YMDToDateTime': {
        const formatStr = typeOptions.args ?? 'YYYY-MM-DD';
        return moment(value)?.format(formatStr);
      }
      case 'attachFileToNameFile':
        return value[0]?.name || undefined;
      case 'arrayValueToString':
        return value.split(',');
      case 'arrayValueToBreaklineString':
        return value.split('\n');
      case 'toString':
        return value?.toString();
      case 'toJsonString':
        return JSON.stringify(value).replace(/"/g, '\\"');
      case 'toValueByKey':
        return value?.[typeOptions.agrs] ?? value;
      case 'listKeyFields':
        if (!modelDetails) return value;
        if (isArray(value)) {
          return value.map((v) => modelDetails[v]?.from);
        }
        return value;
      case 'transformQueryFilter':
        return this.transformQueryFilterCustom(value);
      default:
        return value;
    }
  }
  private transformQueryFilterCustom(filters: any[]) {
    if (!Array.isArray(filters)) {
      return "";
    }

    const transformedFilters: string[] = [];

    filters.forEach(filterItem => {
      if (filterItem.operator === '$in' && filterItem.field && filterItem.value) {
        const field = filterItem.field;

        // Chuyển giá trị thành array nếu là string
        const values = Array.isArray(filterItem.value) ? filterItem.value : [filterItem.value];

        // Tạo filter string theo format: Field=Value1|Field=Value2
        if (values.length > 0) {
          const filterParts = values.map((value: safeAny) => `${field}=${value}`);
          transformedFilters.push(filterParts.join('|'));
        }
      }
    });

    return transformedFilters.join('|');
  }
  private timestampToDateTime(
    value: safeAny,
    args: string,
    isNewVersion?: boolean,
  ) {
    const currentDate = new Date(value);
    const isDateHour = args === 'dateHour';

    let startOfDay = currentDate;

    if (isNewVersion) {
      startOfDay = currentDate
        ? new Date(
            currentDate?.getFullYear(),
            currentDate?.getMonth(),
            currentDate?.getDate(),
            isDateHour ? currentDate.getHours() : 0,
            isDateHour ? currentDate.getMinutes() : 0,
            isDateHour ? currentDate.getSeconds() : 0,
          )
        : new Date();
      startOfDay = new Date(startOfDay.toUTCString());
    } else {
      startOfDay = currentDate
        ? new Date(
            Date.UTC(
              currentDate?.getFullYear(),
              currentDate?.getMonth(),
              currentDate?.getDate(),
              isDateHour ? currentDate.getHours() : 0,
              isDateHour ? currentDate.getMinutes() : 0,
              isDateHour ? currentDate.getSeconds() : 0,
            ),
          )
        : new Date();
    }

    const seconds = Math.floor(startOfDay.getTime() / 1000);
    return seconds;
  }

  private defaultTransformRequestBody(
    data: safeAny,
    model: CrudModel | undefined,
    options?: { omitLanguages: boolean },
  ) {
    if (!model || !model.config) {
      return data;
    }

    return this.transformRequestObject(model.config, data, options);
  }

  private transformRequestObject(
    modelDetails: Record<string, CrudModelDetail>,
    source: Record<string, safeAny> & { languages?: MultiLanguageBE[] },
    options?: { omitLanguages: boolean },
  ) {
    const target: Record<string, safeAny> = {};
    const languages: MultiLanguageBE[] = [];
    Object.keys(modelDetails as safeAny).map((key) => {
      const item = (modelDetails as safeAny)[key] as CrudModelDetail;
      const {
        type,
        from,
        typeOptions,
        objectChildren,
        arrayChildren,
        arrayObjects,
      } = item;
      let value: safeAny;

      const dataFrom = getValue(source, key.split(','));
      if (isNil(dataFrom)) {
        return null;
      }
      //TODO: handle typeOptions
      if (objectChildren) {
        value = this.transformRequestObject(objectChildren, dataFrom);
      } else if (arrayChildren) {
        value = dataFrom.map((item: safeAny) => {
          const _item = this.transformRequestObject(arrayChildren, item);
          return _item;
        });
      } else if (arrayObjects) {
        if (!Array.isArray(dataFrom)) return dataFrom;
        value = {};

        dataFrom.forEach((item) => {
          Object.keys(arrayObjects).forEach((key) => {
            const targetKey = arrayObjects[key].from ?? '';
            if (!value[targetKey]) value[targetKey] = [];
            value[targetKey].push(
              this.convertType(arrayObjects[key].typeOptions, item[key]),
            );
          });
        });
      } else {
        if (typeOptions) {
          value = this.convertType(
            typeOptions,
            dataFrom,
            from?.split('.'),
            languages,
            modelDetails,
          );
        } else {
          value = dataFrom;
        }

        if (this.isNilorEmpty(value)) return null;
        value = this.removeNullEmpty(value);
      }
      if (from) {
        if (from === '$') {
          Object.entries(value).forEach(([k, val]) => {
            setValue(target, k.split('.'), val);
          });
        } else setValue(target, from.split('.'), value);
      }
      return undefined;
    });

    return {
      ...target,
      languages: options?.omitLanguages ? undefined : languages,
    };
  }

  private removeNullEmpty(
    data: safeAny,
  ): safeAny[] | Record<string, safeAny> | safeAny {
    if (typeof data === 'object') {
      if (data instanceof Date) {
        return data;
      }
      if (Array.isArray(data)) {
        return data
          .map((item) => this.removeNullEmpty(item))
          .filter((item) => !this.isNilorEmpty(item));
      }
      return Object.keys(data).reduce(
        (acc, key) => {
          if (data[key] === null || data[key] === undefined) {
            return acc;
          }
          const cleanedValue = this.removeNullEmpty(data[key]);
          if (!this.isNilorEmpty(cleanedValue)) {
            acc[key] = cleanedValue;
          }
          return acc;
        },
        {} as Record<string, safeAny>,
      );
    }
    return data;
  }

  private isNilorEmpty(value: safeAny) {
    if (isNil(value)) return true;
    if (typeof value === 'string' && value.trim() === '') {
      return true;
    }

    if (typeof value === 'object') {
      if (Object.keys(value).length === 0) {
        if (!(value instanceof Date)) return true;
      }
      if (Object.values(value).every((v) => this.isNilorEmpty(v))) return true;
    }

    if (Array.isArray(value) && value.length === 0) {
      return true;
    }
    return false;
  }

  private parseJsonSafely(input: string) {
    let cleaned = input;

    try {
      return JSON.parse(cleaned);
    } catch (e) {
      console.warn('❌ Cannot parse json: ', input);
      console.warn('next step -->');
    }

    // 1. Loại bỏ escape không cần thiết như \"
    cleaned = cleaned.replace(/\\"/g, '"');

    // 2. Loại bỏ chuỗi """value""" thành "value"
    cleaned = cleaned.replace(/"{2,}([^"]*?)"{2,}/g, '"$1"');

    // 3. Fix các trường hợp key bị thiếu giá trị: "key":, => "key": ""
    cleaned = cleaned.replace(/"([^"]+)"\s*:\s*,/g, '"$1": "",');
    cleaned = cleaned.replace(/"([^"]+)"\s*:\s*}/g, '"$1": ""}');

    // 4. Fix các giá trị dạng: "key": 123 => "key": "123"
    cleaned = cleaned.replace(
      /"([^"]+)"\s*:\s*([0-9]+)([,}])/g,
      '"$1": "$2"$3',
    );

    // 5. Với các trường hợp có dấu phẩy ngoài giá trị string, gom thành 1 chuỗi
    cleaned = cleaned.replace(
      /:\s*"([^"]*?)",\s*([0-9][^"}]*)"/g,
      (_match, val1, val2) => {
        const full = [val1, ...val2.split(',')].map((v) => v.trim()).join(',');
        return `: "${full}"`;
      },
    );
    try {
      return JSON.parse(cleaned);
    } catch (e) {
      console.error('❌ JSON parse error:', e);
      console.warn('➡️ Chuỗi sau khi xử lý:', cleaned);
      return '';
    }
  }

  @Span('call-backend')
  private async callUpstream(
    upstream: CrudServiceUpstream,
    httpMethod?: string,
    body?: unknown,
  ) {
    if (!upstream) {
      return null;
    }
    const span = this.traceService.getSpan();

    span.updateName(`${httpMethod} ${this.config.controller}`);

    const { routeConfig, query, headers } = upstream;

    const isBinaryResponse =
      routeConfig.upstreamConfig?.response?.dataType === 'binary';

    const isFormData = routeConfig.dataType === 'formData';
    const isBase64String = routeConfig.dataType === 'base64Data';

    let url = urlJoin(
      routeConfig?.upstreamConfig?.url ??
        this.upstreamController ??
        this.upstreamUrl ??
        '',
      routeConfig?.upstreamConfig?.path ?? this.upstreamPath ?? '',
    );

    const sourceQueryTemplate = Handlebars.compile(
      JSON.stringify(routeConfig?.upstreamConfig?.query ?? {})
        .replace(/::\{/g, '{{{')
        .replace(/\}::/g, '}}}')
        .replace(/:\{/g, '{{')
        .replace(/\}:/g, '}}'),
    );

    const sourceQuery = this.parseJsonSafely(
      sourceQueryTemplate({
        ...upstream.query,
        ...query.queryFilter,
      }),
    );

    const modelName = routeConfig.model ?? '_';
    const modelConfig = this.models.find((m) => m.name === modelName)?.config;
    const queryString = compact(
      Object.keys(sourceQuery).map((key) => {
        const value = sourceQuery[key];
        const _field = modelConfig?.[key];
        if (value) {
          if (_field?.typeOptions?.func === 'toJsonString') {
            const jsonObject = JSON.parse(value);
            return key + '=' + encodeURIComponent(JSON.stringify(jsonObject));
          } else if (
            value.includes(',') &&
            key != 'Filter' &&
            key != 'OrderBy'
          ) {
            return value
              .split(',')
              .map((item: string) => {
                return key + '=' + encodeURIComponent(item.normalize('NFC'));
              })
              .join('&');
          } else {
            return key + '=' + encodeURIComponent(value.normalize('NFC'));
          }
        }
        return null;
      }),
    ).join('&');

    if (queryString) {
      url += '?' + queryString;
    }

    let transformBody: safeAny = body;

    if (body) {
      if (!isBinaryResponse && !isFormData) {
        Logger.debug(JSON.stringify(body), 'body');
      }
      if (isBase64String) body = JSON.parse(this.decodeBase64String(body));
      if (isFormData) body = this.convertFormDataToJson(body);
      transformBody = await this.formatRequest(body, upstream);
    }

    Logger.debug(url, routeConfig.upstreamConfig?.method);
    const formData = new FormData();
    if (isFormData) {
      headers['content-type'] = 'multipart/form-data';
      const tmp = transformBody as Record<string, safeAny>;

      if (tmp) {
        Object.keys(tmp).forEach((key_BE) => {
          this.appendToFormData(formData, key_BE, tmp[key_BE]);
        });
      }
    }

    span.setAttributes({
      'bff.upstream.url': url,
      'bff.upstream.method': routeConfig.upstreamConfig?.method ?? 'GET',
    });

    const cacheKey = this.getCacheKey(cloneDeep(url), cloneDeep(headers));
    const value = await this.cacheManager.get(cacheKey);
    if (value && httpMethod === 'GET') {
      return value;
    }

    const headersUpstream: Record<string, string | undefined> = {
      Authorization: headers['authorization'],
      'content-type': headers['content-type'] ?? 'application/json',
      'x-correlation-id': headers['x-correlation-id'],
      timezone: headers['timezone'],
      'Ppx-Action-Code': headers['ppx-action-code'],
      'User-Agent': headers['user-agent'],
      'X-Forwarded-For': headers['x-forwarded-for'],
    };

    if (!routeConfig?.request?.ignoreFunctionCode) {
      headersUpstream['Ppx-Function-Code'] = headers['ppx-function-code'];
    }

    const headerLog = cloneDeep(headersUpstream);

    if (process.env['NODE_ENV'] === 'production') {
      delete headerLog['Authorization'];
    }

    span.setAttributes({
      'bff.upstream.headers': JSON.stringify(headerLog, null, 2),
    });

    const axiosConfig: any = {
      url: url,
      method: routeConfig.upstreamConfig?.method ?? 'GET',
      headers: headersUpstream,
      responseType:
        upstream.routeConfig.upstreamConfig?.response?.dataType === 'binary'
          ? 'arraybuffer'
          : 'json',
    };

    // Convert request data if method is POST, PUT, PATCH, or DELETE
    if (
      ['POST', 'PUT', 'PATCH', 'DELETE'].includes(
        (routeConfig.upstreamConfig?.method ?? 'GET').toUpperCase(),
      )
    ) {
      axiosConfig.data = this.convertRequestData(
        formData,
        transformBody,
        routeConfig.dataType,
      );
    }

    const rs = await axios.default.request(axiosConfig).catch((e) => {
      Logger.error(`status: ${e.status}, message: ${e.message}`, 'Error');
      span.setAttributes({
        statusCode: e.response?.status ?? 400,
      });
      if (e?.key) {
        throw new HttpException(
          {
            key: e?.key,
            message: e.response?.data ?? e.message,
          },
          e.response?.status === 500 ? 400 : (e.response?.status ?? 400),
        );
      }

      let dataError = e.response?.data;
      if (e.config.responseType === 'arraybuffer') {
        const jsonData = JSON.parse(new TextDecoder().decode(e.response?.data));
        dataError = jsonData;
      }

      throw new HttpException(
        dataError ?? e.message,
        e.response?.status === 500 ? 400 : (e.response?.status ?? 400),
      );
    });

    if (rs && rs.status >= 200 && rs.status < 400) {
      span.setAttributes({
        statusCode: rs.status,
      });
      if (
        upstream.routeConfig.upstreamConfig?.response?.dataType === 'binary'
      ) {
        return rs;
      }
      if (rs.data) {
        const data = await this.formatResponse(rs.data, upstream);
        if (httpMethod === 'GET' && data) {
          await this.cacheManager.set(url, data).catch((e) => {
            Logger.error(e);
          });
        }
        // Logger.debug(JSON.stringify(data), 'Reponse Data');
        return data;
      }
    }

    Logger.debug(JSON.stringify(rs.data), 'Response Data');
    return rs.data;
  }
  private convertFormDataToJson(body: safeAny) {
    const nestedObject: Record<string, safeAny> = {};
    Object.entries(body).forEach(([key, value]) => {
      const keys = key.split('.');
      let temp = nestedObject;

      while (keys.length > 1) {
        const k = keys.shift();
        if (k) {
          const arrayIndexMatch = k.match(/(.+)\[(\d+)\]/);
          if (arrayIndexMatch) {
            const [_, arrayKey, index] = arrayIndexMatch;
            if (!temp[arrayKey]) {
              temp[arrayKey] = [];
            }
            if (!temp[arrayKey][index]) {
              temp[arrayKey][index] = {};
            }
            temp = temp[arrayKey][index];
          } else {
            if (!temp[k]) {
              temp[k] = {};
            }
            temp = temp[k];
          }
        }
      }
      const lastKey = keys[0];
      const arrayIndexMatch = lastKey.match(/(.+)\[(\d+)\]/);
      if (arrayIndexMatch) {
        const [_, arrayKey, index] = arrayIndexMatch;
        if (!temp[arrayKey]) {
          temp[arrayKey] = [];
        }
        temp[arrayKey][index] = value;
      } else {
        temp[lastKey] = value;
      }
    });
    return nestedObject;
  }

  private convertRequestData(
    formData: FormData,
    transformBody: safeAny,
    dataType?: string,
  ) {
    if (dataType === 'formData') {
      //Log formData
      // console.log('=======================FormData================');
      // formData.forEach((value, key) => {
      //   if (!(value instanceof Blob)) {
      //     console.log(key, ':', value);
      //   } else console.log(key);
      // });
      // console.log('=======================END FormData================');

      return formData;
    }
    if (dataType === 'base64Data') {
      const base64Obj = {
        base64Data: this.encodeBase64String(transformBody),
      };
      return base64Obj;
    }

    !isNil(transformBody) &&
      Logger.debug(JSON.stringify(transformBody), 'transformBody');
    return transformBody;
  }

  private encodeBase64String(value: safeAny) {
    // const utf8Bytes = new TextEncoder().encode(JSON.stringify(value));
    // return btoa(String.fromCharCode(...utf8Bytes));

    const utf8Bytes = new TextEncoder().encode(JSON.stringify(value));
    let binary = '';
    const len = utf8Bytes.length;

    for (let i = 0; i < len; i++) {
      binary += String.fromCharCode(utf8Bytes[i]);
    }

    return btoa(binary);
  }

  private decodeBase64String(value: safeAny) {
    const binaryString = atob(value.base64_string); // Decode Base64 to binary string
    const utf8Bytes = Uint8Array.from(binaryString, (char) =>
      char.charCodeAt(0),
    ); // Convert binary string to byte array
    return new TextDecoder().decode(utf8Bytes);
  }

  private getCacheKey(url: string, headers: Record<string, string>) {
    if (!headers) {
      return url;
    }
    const headerString = Object.keys({
      Authorization: headers?.['Authorization'],
      Accept: headers?.['Accept'],
      Language: headers?.['Language'],
      Host: headers?.['Host'],
      Referer: headers?.['Referer'],
      timezone: headers?.['timezone'],
    })
      .filter((x) => {
        return headers?.[x];
      })
      .map((key) => `${key}:${headers?.[key]}`)
      .join(';');

    return (url + headerString).replace(/\s/g, '');
  }

  private appendToFormData(
    formData: FormData,
    key: string,
    value: safeAny,
    parentKey?: string,
  ) {
    if (value instanceof MemoryStoredFile) {
      formData.append(
        parentKey ? `${parentKey}.${key}` : key,
        new Blob([value.buffer], { type: value.mimetype }),
        decodeURIComponent(value.originalName),
      );
    } else if (value?.buffer?.data || value?.buffer) {
      const bufferData = Buffer.from(
        value?.buffer?.data ?? Object.values(value?.buffer),
      );
      formData.append(
        parentKey ? `${parentKey}.${key}` : key,
        new Blob([bufferData], {
          type: value?.fileType?.mime ?? value.mimetype,
        }),
        decodeURIComponent(value.originalName),
      );
    } else if (isArray(value)) {
      value.forEach((v: safeAny, index: number) => {
        if (v instanceof MemoryStoredFile || v?.buffer?.data || v?.buffer) {
          this.appendToFormData(formData, `${key}`, v, parentKey);
        } else {
          this.appendToFormData(formData, `${key}[${index}]`, v, parentKey);
        }
      });
    } else if (isObject(value)) {
      Object.keys(value).forEach((subKey) => {
        this.appendToFormData(
          formData,
          `${key}.${subKey}`,
          (value as Record<string, safeAny>)[subKey],
          parentKey,
        );
      });
    } else {
      formData.append(parentKey ? `${parentKey}.${key}` : key, value);
    }
  }

  @Span('format-request')
  private async formatRequest(data: safeAny, upstream: CrudServiceUpstream) {
    const { routeConfig } = upstream;
    const model = this.models.find((m) => m.name === upstream.modelName);
    if (isString(model?.config) && model?.config === '*') {
      const expressionBody = jsonata(routeConfig.bodyTransform ?? '$');
      return await expressionBody.evaluate(data);
    }
    switch (routeConfig.request?.dataType) {
      case CrudResponseTypes.ARRAY:
        data = data.map((d: safeAny) => {
          return this.defaultTransformRequestBody(d, model);
        });
        break;

      default:
        data = this.defaultTransformRequestBody(
          data,
          model,
          (routeConfig as any)?.['options'],
        );
        break;
    }
    // if (routeConfig.dataType !== 'formData') Logger.debug(JSON.stringify(data));
    const expressionBody = jsonata(routeConfig.bodyTransform ?? '$');
    return await expressionBody.evaluate(data);
  }

  @Span('transform-response')
  private async formatResponse(data: safeAny, upstream: CrudServiceUpstream) {
    const { routeConfig } = upstream;

    const model = this.models.find((m) => m.name === upstream.modelName);
    switch (routeConfig.upstreamConfig?.response?.dataType) {
      case CrudResponseTypes.ARRAY:
        data = data.map((d: safeAny) => {
          return this.defaultTransformResponseData(d, model);
        });
        break;

      case CrudResponseTypes.OBJECT:
        data = this.defaultTransformResponseData(data, model);
        break;

      case CrudResponseTypes.PAGINATED:
        {
          data = this.processPagination(data, upstream);
        }
        break;
    }

    const expressionBody = jsonata(
      routeConfig.upstreamConfig?.transform ?? '$',
    );

    return await expressionBody.evaluate(data);
  }

  private processPagination(data: safeAny, upstream: CrudServiceUpstream) {
    const result: safeAny = {};
    const model = this.models.find((m) => m.name === upstream.modelName);

    if (!model?.pagination) {
      return data;
    }

    for (const key in model?.pagination) {
      const form = model?.pagination?.[key as keyof CrudModelPagination]?.from;
      if (!form) {
        continue;
      }

      const itemKey = data?.[form];
      if (!itemKey) {
        continue;
      }

      if (key === 'data') {
        for (let i = 0; i < itemKey?.length; i++) {
          const rs = this.defaultTransformResponseData(itemKey[i], model);

          itemKey[i] = rs;
        }
      }

      result[key] = itemKey;
    }
    return result;
  }

  async getMany(req: CrudRequest) {
    const config = this.buildQuery(req);
    return this.callUpstream(config, 'GET');
  }

  async getOne(req: CrudRequest) {
    const config = this.buildQuery(req);
    return this.callUpstream(config, 'GET');
  }

  async createOne(req: CrudRequest, dto: unknown) {
    const config = this.buildQuery(req);
    return this.callUpstream(config, 'POST', dto);
  }

  async createMany(req: CrudRequest, dto: unknown[]) {
    const config = this.buildQuery(req);
    return this.callUpstream(config, 'POST', dto);
  }

  async updateOne(req: CrudRequest, dto: unknown) {
    const config = this.buildQuery(req);
    return this.callUpstream(config, 'PATCH', dto);
  }

  async replaceOne(req: CrudRequest, dto: unknown) {
    const config = this.buildQuery(req);
    return this.callUpstream(config, 'PUT', dto);
  }

  async deleteOne(req: CrudRequest) {
    const config = this.buildQuery(req);
    return this.callUpstream(config, 'DELETE');
  }

  async recoverOne(req: CrudRequest) {
    const config = this.buildQuery(req);
    return this.callUpstream(config, 'PATCH');
  }

  async customGet(req: CrudRequest) {
    const config = this.buildQuery(req);
    return this.callUpstream(config, 'GET');
  }

  async customPatch(req: CrudRequest, body: safeAny) {
    const config = this.buildQuery(req);
    return this.callUpstream(config, 'PATCH', body);
  }

  async customDelete(req: CrudRequest, body: safeAny) {
    const config = this.buildQuery(req);
    return this.callUpstream(config, 'DELETE', body);
  }

  async customDownloadFile(req: CrudRequest, body: safeAny) {
    const config = this.buildQuery(req);
    return this.callUpstream(config, undefined, body);
  }

  async customPost(req: CrudRequest, body: safeAny) {
    const config = this.buildQuery(req);
    return this.callUpstream(config, 'POST', body);
  }

  async uploadFile(req: CrudRequest, files: safeAny) {
    // log files
    const data: Record<string, safeAny> = {};
    Object.keys(files).forEach((key) => {
      if (files[key] instanceof MemoryStoredFile || isArray(files[key])) {
        data[key] = files[key];
        return;
      }
      // try to parse file to json
      try {
        data[key] = JSON.parse(files[key]);
      } catch (ex) {
        data[key] = files[key];
      }
    });

    return this.callUpstream(this.buildQuery(req), undefined, data);
  }

  async getConfigs() {
    return this.config;
  }
}
function join(arg0: string) {
  throw new Error('Function not implemented.');
}
