import { Component, computed, input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconComponent } from '@hrdx/hrdx-design';

@Component({
  selector: 'dynamic-toast',
  standalone: true,
  imports: [CommonModule, IconComponent],
  templateUrl: './toast.component.html',
  styleUrl: './toast.component.less',
})
export class ToastComponent {
  icon = input<string>();
  title = input<string>();
  content = input.required<string>();
  contentType = input<'default' | 'textarea'>('default');
  type = input<'warning' | 'error' | 'success' | 'info'>('info');

  iconComputed = computed(() => {
    if (this.icon()) return this.icon();
    switch (this.type()) {
      case 'warning':
        return 'icon-warning-bold';
      case 'error':
        return 'icon-x-circle-bold';
      case 'success':
        return 'icon-check-circle';
      case 'info':
        return 'icon-info-bold';
      default:
        return 'icon-info-bold';
    }
  });
}
