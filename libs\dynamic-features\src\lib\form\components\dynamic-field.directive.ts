import {
  ComponentRef,
  Directive,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
  ViewContainerRef,
  inject,
} from '@angular/core';
import { FormArray, FormGroup } from '@angular/forms';
import * as _ from 'lodash';
import { FieldConfig } from '../models/field-config.interface';
import { Field, Values } from '../models/field.interface';
import { FormConfigService } from '../services/form-config.service';
import { supportedFields } from './fields';

@Directive({
  selector: '[dynamicDynamicField]',
  standalone: true,
})
export class DynamicFieldDirective implements Field, OnChanges, OnInit {
  private readonly fieldsSupportDisabledInput = ['number', 'text'];
  @Input()
  config!: FieldConfig;

  @Input()
  group!: FormGroup | FormArray;

  @Input() disabled? = false;
  @Input() isAddOn? = false;

  @Input() values: Values = {};
  @Output() radioEmit = new EventEmitter();
  @Output() clickedCommonSetting = new EventEmitter();
  @Output() clickedSetting = new EventEmitter();
  @Output() invalidEmitter = new EventEmitter();
  @Output() subInvalidEmitter = new EventEmitter();

  component!: ComponentRef<Field>;
  private readonly formService = inject(FormConfigService);

  private readonly container = inject(ViewContainerRef);

  ngOnChanges(changes: SimpleChanges) {
    if (this.component && changes['config'] && this.config) {
      this.component.instance.config = this.config;
    }
    if (this.component && changes['group']) {
      this.component.instance.group = this.group;
    }
    if (changes['values']) {
      if (
        _.isEqual(
          changes['values'].currentValue,
          changes['values'].previousValue,
        )
      )
        return;
      this.component?.setInput('values', this.values);
    }

    if (changes['disabled']) {
      this.setDisabled(this.disabled);
    }
  }

  ngOnInit() {
    if (this.config.type !== 'paragraph') {
      if (
        this.group instanceof FormGroup &&
        !this.group.contains(this.config.name)
      ) {
        this.group.addControl(
          this.config.name,
          this.formService.createControl(this.config, () => this.values),
        );
      }
      if (
        this.group instanceof FormArray &&
        !this.group.at(+this.config.name)
      ) {
        this.group.insert(
          +this.config.name,
          this.formService.createControl(this.config, () => this.values),
        );
      }
      if (!this.config?.type || !supportedFields[this.config.type]) {
        const supportedTypes = Object.keys(supportedFields).join(', ');
        throw new Error(
          `Trying to use an unsupported type (${this.config?.type}).
        Supported types: ${supportedTypes}`,
        );
      }
    }

    this.component = this.container.createComponent(
      supportedFields[this.config.type],
    );
    this.component.instance.config = this.config;
    this.component.instance.isAddOn = this.isAddOn;
    this.component.instance.group = this.group;
    (this.component.location.nativeElement as HTMLDivElement).style.width =
      '100%';
    this.component.setInput('values', this.values);
    this.setDisabled(this.disabled);
    this.component.instance.radioEmit?.subscribe((v) => this.radioEmit.emit(v));
    this.component.instance.clickedCommonSetting?.subscribe((v) =>
      this.clickedCommonSetting.emit(v),
    );
    this.component.instance.clickedSetting?.subscribe((v) =>
      this.clickedSetting.emit(v),
    );
    this.component.instance.invalidEmitter?.subscribe((v) => {
      this.invalidEmitter.emit(v);
    });
    this.component.instance.subInvalidEmitter?.subscribe((v) => {
      this.subInvalidEmitter.emit(v);
    });
  }

  private setDisabled(disabled?: boolean) {
    if (!this.fieldsSupportDisabledInput.includes(this.config.type)) return;
    this.component?.setInput('disabled', disabled ?? false);
  }
}
