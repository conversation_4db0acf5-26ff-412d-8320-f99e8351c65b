import { CommonModule } from '@angular/common';
import {
  ChangeDetectorRef,
  Component,
  inject,
  Input,
  OnChanges,
  OnInit,
  SimpleChanges,
  SkipSelf,
  viewChild,
  ViewEncapsulation,
} from '@angular/core';
import { FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import {
  DatePickerComponent,
  IconComponent,
  DateRangeInputDirective,
} from '@hrdx/hrdx-design';
import { isArray, isDate, isNil } from 'lodash';
import * as moment from 'moment';
import {
  DisabledTimeFn,
  NzDatePickerComponent,
  NzDatePickerModule,
} from 'ng-zorro-antd/date-picker';
import {
  BehaviorSubject,
  combineLatest,
  distinct,
  distinctUntilChanged,
  filter,
  map,
  of,
  Subject,
  Subscription,
  switchMap,
  takeUntil,
  tap,
} from 'rxjs';
import {
  DatePickerDisabledDate,
  DatePickerSetting,
  Field,
  FieldConfig,
  SourceField,
  Values,
} from '../../../models';
import { DynamicFormService } from '../../../services/dynamic-form.service';
import { NzSafeAny } from 'ng-zorro-antd/core/types';
import { differenceInCalendarDays, differenceInMilliseconds } from 'date-fns';
import { FormControlService } from '../../../services/form-control.service';

@Component({
  selector: 'dynamic-field-datepicker',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    NzDatePickerModule,
    FormsModule,
    DatePickerComponent,
    IconComponent,
    DateRangeInputDirective,
  ],
  templateUrl: './field-datepicker.component.html',
  styleUrls: ['./field-datepicker.component.less'],
  encapsulation: ViewEncapsulation.None,
})
export class FieldDatepickerComponent implements Field, OnInit, OnChanges {
  config!: FieldConfig & {
    clearFieldsAfterChange?: string[];
    _defaultValue?: SourceField;
  };
  group!: FormGroup;
  @Input() values: Values = {};
  service = inject(DynamicFormService);
  values$ = new BehaviorSubject<Values>({});
  value?: string | Date | string[] | (Date | undefined)[] = [];
  setting?: any;
  inputReadonly = false;
  dateRangePicker = viewChild<NzDatePickerComponent>('dateRangePicker');
  disabledDate?: (d: Date) => boolean;
  disabledTime?: DisabledTimeFn;

  static SET_VALUE_NULL_KEY = '_setValueNull';

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['values']) {
      this.values$.next(this.values);
    }
  }
  // vnLocale: NzDatePickerI18nInterface = {
  //   lang: {
  //     placeholder: 'Chọn thời điểm',
  //     yearPlaceholder: 'Chọn năm',
  //     quarterPlaceholder: 'Chọn quý',
  //     monthPlaceholder: 'Chọn tháng',
  //     weekPlaceholder: 'Chọn tuần',
  //     rangePlaceholder: ['Ngày bắt đầu', 'Ngày kết thúc'],
  //     rangeYearPlaceholder: ['Năm bắt đầu', 'Năm kết thúc'],
  //     rangeMonthPlaceholder: ['Tháng bắt đầu', 'Tháng kết thúc'],
  //     rangeWeekPlaceholder: ['Tuần bắt đầu', 'Tuần kết thúc'],
  //     yearFormat: 'YYYY',
  //     dateFormat: 'DD/MM/YYYY',
  //   },
  //   timePickerLocale: {},
  // };

  get disabled() {
    return this.group.get(this.config.name)?.disabled;
  }

  get dateRangePlaceholder() {
    const placeholder = this.config.placeholder;
    if (!placeholder) return ['DD/MM/YYYY', 'DD/MM/YYYY'];
    if (typeof placeholder === 'string') return [placeholder, placeholder];
    return placeholder as string[];
  }

  get datePlaceholder() {
    let placeholder = this.config.placeholder;
    if (this.getDateFormat(this.setting) === 'dd/MM/yyyy') {
      placeholder = 'DD/MM/YYYY';
    }

    return placeholder;
  }

  getDateFormat(configSetting: DatePickerSetting | undefined) {
    if (!configSetting) {
      return 'dd/MM/yyyy';
      // return 'dd-MM-yyyy';
    }

    if (configSetting.format) {
      return configSetting.format;
    }

    let formatStr = '';
    switch (configSetting.type) {
      case 'week':
        formatStr = 'ww-yyyy';
        break;

      case 'month':
        formatStr = 'MM-yyyy';
        break;

      case 'date':
        formatStr = 'dd-MM-yyyy';
        break;

      case 'year':
        formatStr = 'yyyy';
        break;

      case 'date-time':
        formatStr = 'dd-MM-yyyy HH:mm';
        break;

      case 'date-time-full':
        formatStr = 'dd-MM-yyyy HH:mm:ss';
        break;

      default:
        formatStr = 'dd-MM-yyyy';
        break;
    }
    return formatStr + ' ' + this.getTimePickerFormat(configSetting);
  }

  getPickerMode(
    configSetting: DatePickerSetting | undefined,
  ): 'decade' | 'year' | 'month' | 'week' | 'date' | 'time' {
    switch (configSetting?.type) {
      case 'decade':
        return 'decade';
      case 'year':
        return 'year';
      case 'month':
        return 'month';
      case 'week':
        return 'week';
      case 'time':
        return 'time';
      default:
        return 'date';
    }
  }

  getTimePickerFormat(configSetting: DatePickerSetting | undefined) {
    if (!configSetting) {
      return '';
    }

    if (!configSetting.hasTimePicker) return '';

    if (configSetting.timePickerFormat) {
      return configSetting.timePickerFormat;
    }

    return 'HH:mm';
  }

  getShowTime(configSetting: DatePickerSetting | undefined) {
    if (!configSetting) {
      return false;
    }
    if (!configSetting.hasTimePicker) {
      return false;
    }

    return { nzFormat: this.getTimePickerFormat(configSetting) };
  }

  setDisabledDate(setting?: DatePickerSetting['disabledDate']) {
    if (!setting) return;
    let settings: DatePickerDisabledDate[] = [];
    if (Array.isArray(setting)) {
      settings = setting;
    } else {
      settings = [setting];
    }
    const mappingFuncs = settings.map((s) => this.getDisabledDateFunction(s));
    this.disabledDate = (d: Date) => mappingFuncs.every((func) => func(d));
  }

  getDisabledDateFunction(setting: DatePickerDisabledDate) {
    const { value, operator } = setting;
    const defaultFunc = (d: Date) => false;
    if (!value || !operator) return defaultFunc;

    let dateObj: Date | Date[];
    if (value === 'today') {
      dateObj = new Date();
    } else if (typeof value === 'string') {
      dateObj = moment(value, 'DD/MM/yyyy').toDate();
    } else if (Array.isArray(value)) {
      dateObj = value.map((d) => {
        if (d === 'today') return new Date();
        return moment(d, 'DD/MM/yyyy').toDate();
      });
    } else {
      dateObj = value;
    }

    const arrayOperators = ['$between', '$in', '$nin'];
    if (
      Array.isArray(dateObj) &&
      (dateObj.length <= 0 || !arrayOperators.includes(operator))
    ) {
      return defaultFunc;
    }

    switch (operator) {
      case '$between': {
        const dates = dateObj as Date[];
        return (d: Date) => {
          const startDate = dates[0];
          const endDate = dates[dates.length - 1];

          return (
            differenceInCalendarDays(d, startDate) >= 0 &&
            differenceInCalendarDays(d, endDate) <= 0
          );
        };
      }
      case '$in': {
        const dates = dateObj as Date[];

        return (d: Date) => {
          return dates.some((date) => differenceInCalendarDays(d, date) === 0);
        };
      }
      case '$nin': {
        const dates = dateObj as Date[];

        return (d: Date) => {
          return dates.every((date) => differenceInCalendarDays(d, date) !== 0);
        };
      }
      case '$eq': {
        return (d: Date) => {
          return differenceInCalendarDays(d, dateObj as Date) === 0;
        };
      }
      case '$ne': {
        return (d: Date) => {
          return differenceInCalendarDays(d, dateObj as Date) !== 0;
        };
      }
      case '$lte': {
        return (d: Date) => {
          return differenceInCalendarDays(d, dateObj as Date) <= 0;
        };
      }
      case '$gte': {
        return (d: Date) => {
          return differenceInCalendarDays(d, dateObj as Date) >= 0;
        };
      }
      case '$lt': {
        return (d: Date) => {
          return differenceInCalendarDays(d, dateObj as Date) < 0;
        };
      }
      case '$gt': {
        return (d: Date) => {
          return differenceInCalendarDays(d, dateObj as Date) > 0;
        };
      }
      default:
        return defaultFunc;
    }
  }

  setDisabledTime(setting: DatePickerSetting['disabledTime'] | undefined) {
    if (!setting) return;
    const { value, operator } = setting;
    if (!value || !operator) return;

    if (value === FieldDatepickerComponent.SET_VALUE_NULL_KEY) {
      this.disabledTime = undefined;
      return;
    }

    let hours: number[] = [],
      minutes: number[] = [],
      seconds: number[] = [];

    if (value === 'now') {
      const now = new Date();
      [hours, minutes, seconds] = [
        [now.getHours()],
        [now.getMinutes()],
        [now.getSeconds()],
      ];
    } else if (typeof value === 'object' && value !== null) {
      ({ hours = [], minutes = [], seconds = [] } = value);
    } else return;

    if (
      !['$between', '$in', '$eq', '$lte', '$gte', '$lt', '$gt'].includes(
        operator,
      )
    )
      return;

    const generateRange = (start: number, end: number) =>
      Array.from({ length: 60 }, (_, i) => i).filter(
        (n) => n >= start && n <= end,
      );

    this.disabledTime = () => {
      switch (operator) {
        case '$between':
          return {
            nzDisabledHours: () =>
              generateRange(Math.min(...hours), Math.max(...hours)),
            nzDisabledMinutes: () =>
              generateRange(Math.min(...minutes), Math.max(...minutes)),
            nzDisabledSeconds: () =>
              generateRange(Math.min(...seconds), Math.max(...seconds)),
          };
        case '$in':
        case '$eq':
          return {
            nzDisabledHours: () => hours,
            nzDisabledMinutes: () => minutes,
            nzDisabledSeconds: () => seconds,
          };
        case '$lte':
          return {
            nzDisabledHours: () => generateRange(0, Math.max(...hours)),
            nzDisabledMinutes: () => generateRange(0, Math.max(...minutes)),
            nzDisabledSeconds: () => generateRange(0, Math.max(...seconds)),
          };
        case '$gte':
          return {
            nzDisabledHours: () => generateRange(Math.min(...hours), 23),
            nzDisabledMinutes: () => generateRange(Math.min(...minutes), 59),
            nzDisabledSeconds: () => generateRange(Math.min(...seconds), 59),
          };
        case '$lt':
          return {
            nzDisabledHours: () => generateRange(0, Math.min(...hours) - 1),
            nzDisabledMinutes: () => generateRange(0, Math.min(...minutes) - 1),
            nzDisabledSeconds: () => generateRange(0, Math.min(...seconds) - 1),
          };
        case '$gt':
          return {
            nzDisabledHours: () => generateRange(Math.max(...hours) + 1, 23),
            nzDisabledMinutes: () =>
              generateRange(Math.max(...minutes) + 1, 59),
            nzDisabledSeconds: () =>
              generateRange(Math.max(...seconds) + 1, 59),
          };
      }
    };
  }

  tempValue: (Date | null)[] = [null, null];
  onCalendarChange(dates: (Date | null)[]) {
    this.tempValue = dates;
  }

  onOpenChange(open: boolean) {
    this.touch();
    if (open) return;

    // Initialize tempValue if it's undefined
    if (!this.tempValue) {
      this.tempValue = [null, null];
    }

    const validDates = this.tempValue.filter((date) => !isNil(date));
    if (validDates.length >= 2) return;
    this.formatDateRangeChange(validDates);
  }

  onDateRangeInputChange(dates: [Date | null, Date | null] | null) {
    if (!dates) {
      this.value = undefined;
      this.group.get(this.config.name)?.setValue(undefined);
      return;
    }
  }

  onDatePickerSelection(event: { index: number; date: Date }) {
    // Initialize tempValue if it's undefined
    if (!this.tempValue) {
      this.tempValue = [null, null];
    }

    // Update the tempValue array with the selected date
    this.tempValue[event.index] = event.date;

    // If we have both start and end dates, update the form value
    if (this.tempValue[0] && this.tempValue[1]) {
      this.formatDateRangeChange([this.tempValue[0], this.tempValue[1]]);
    }
  }

  subscriptionSetValueForm!: Subscription;
  constructor(
    @SkipSelf() private formControlService: FormControlService,
    private cdr: ChangeDetectorRef,
  ) {}

  setValueDate(value: NzSafeAny) {
    if (value === FieldDatepickerComponent.SET_VALUE_NULL_KEY) {
      this.dateChanged(undefined, true);
    } else if (typeof value === 'string') {
      const newValueString = new Date(value);
      this.dateChanged(newValueString, true);
    } else if (value instanceof Date) {
      this.dateChanged(value, true);
    } else if (Array.isArray(value)) {
      const check = value.every((it: NzSafeAny) => typeof it === 'string');
      if (check) {
        const newValueArray = value.map((it: NzSafeAny) => {
          return new Date(it);
        });
        this.dateChanged(newValueArray, true);
      }
    }
  }

  private readonly destroySetDefaultValue$ = new Subject<void>();

  ngOnInit() {
    const config = this.config;
    this.value = config.value;
    this.group.get(this.config.name)?.setValue(this.value);
    if (config.mode !== 'date-picker') {
      this.tempValue = (this.value ?? []) as (Date | null)[];
    }
    this.setting = config.setting;

    this.group.get(config.name)?.valueChanges.subscribe((value) => {
      if (isNil(value)) {
        this.value = undefined;
        this.tempValue = [];
      }
    });

    //lắng nghe thay đổi value từ service handleAfterChange
    this.subscriptionSetValueForm =
      this.formControlService.setValueSubject$.subscribe((value: NzSafeAny) => {
        let currentPath = structuredClone(this.values.extend?.['path']);
        currentPath?.pop();
        currentPath = currentPath?.join('.');

        const checkPath = value.path ? value.path === currentPath : true;

        if (value.key === config.name && !isNil(value) && checkPath) {
          const newValue = value?.value;
          this.setValueDate(newValue);
        }
      });

    this.values$
      .pipe(
        distinctUntilChanged((prev, curr) =>
          this.service.distinct(prev, curr, config._setting),
        ),
        switchMap((values) =>
          this.service.getObservable(
            this.values.function,
            values,
            this.config._setting,
          ),
        ),
      )
      .subscribe((data) => {
        if (data) this.setting = data;
      });

    if (this.config._defaultValue) {
      combineLatest({
        _value: this.values$.pipe(
          distinctUntilChanged((prev, curr) => {
            return this.service.distinct(prev, curr, this.config._defaultValue);
          }),
          takeUntil(this.destroySetDefaultValue$),
          switchMap(() =>
            this.service.getObservable(
              this.values.function,
              this.values,
              this.config._defaultValue,
            ),
          ),
          tap(() => this.cdr.detectChanges()),
        ),
      })
        .pipe(
          map(({ _value }) => {
            return _value;
          }),
          tap(() => this.cdr.detectChanges()),
        )
        .subscribe((value) => {
          if (value) {
            this.setValueDate(value);
            this.destroySetDefaultValue$.next();
            this.destroySetDefaultValue$.complete();
          }
        });
    }
    if (config._value) {
      let skipCount = 0;
      const skip = config._value?.skip;
      this.values$
        .pipe(
          distinctUntilChanged((prev, curr) =>
            this.service.distinct(prev, curr, config._value),
          ),
          switchMap((values) => {
            const count = skip?.count ?? 1;
            if (!skip?.condition || skipCount >= count)
              return of({ skip: false, values });
            return this.service
              .getObservable(this.values.function, values, {
                transform: skip.condition,
              })
              .pipe(map((res) => ({ skip: res, values })));
          }),
          tap((values) => {
            if (values.skip) skipCount++;
          }),
          filter((values) => !values.skip),
          switchMap(({ values }) =>
            this.service.getObservable(
              this.values.function,
              values,
              this.config._value,
            ),
          ),
        )
        .subscribe((data) => {
          if (config.isRecommend && !isNil(this.value)) return;
          if (!data) {
            // this.value = undefined;
          } else {
            this.setValueDate(data);
          }
          // this.group.get(config.name)?.setValue(this.value);
        });
    }

    // config for input readonly
    if (config.inputReadonly) {
      this.inputReadonly = config.inputReadonly;
    }
    if (config._inputReadonly) {
      this.subscribeValuesChanges(config._inputReadonly).subscribe((value) => {
        if (value && typeof value === 'boolean') {
          this.inputReadonly = value;
        } else {
          this.inputReadonly = false;
        }
      });
    }

    this.setDisabledDate(this.setting?.disabledDate);
    const _disabledDate = this.setting?._disabledDate;
    if (_disabledDate) {
      this.subscribeValuesChanges(_disabledDate).subscribe((value) => {
        if (!value) return;
        this.setDisabledDate(value);
      });
    }

    this.setDisabledTime(this.setting?.disabledTime);

    const _disabledTime = this.setting?._disabledTime;
    if (_disabledTime) {
      this.subscribeValuesChanges(_disabledTime).subscribe((value) => {
        if (!value || typeof value !== 'object') return;
        this.setDisabledTime(value);
      });
    }
  }

  private subscribeValuesChanges(transform: SourceField) {
    return this.values$.pipe(
      distinctUntilChanged((prev, curr) =>
        this.service.distinct(prev, curr, transform),
      ),
      switchMap((values) =>
        this.service.getObservable(this.values.function, values, transform),
      ),
    );
  }

  clearFields(fields: string[]) {
    fields.forEach((field) => {
      const control = this.group.get(field);
      if (control) {
        control.setValue(null);
      } else {
        this.formControlService.updateFormControlValue(field, null);
      }
    });
  }

  dateChanged(
    event: Date | (Date | undefined)[] | undefined | string | string[],
    isNotUserChange = false,
  ) {
    this.formatDateChange(event);
    const clearFieldsAfterChange = this.config.clearFieldsAfterChange;
    if (clearFieldsAfterChange && !isNotUserChange) {
      this.clearFields(clearFieldsAfterChange);
    }
  }

  dateRangeChanged(event: (Date | null)[]) {
    this.formatDateRangeChange(event);
  }

  formatDateRangeChange(e: (Date | null)[]) {
    const type = this.setting?.type ?? 'date';
    if (!e[0] && !e[1]) {
      // this.value = [undefined, undefined];
      this.value = undefined;
    } else {
      const valueFormatted = [];
      if (e[0]) {
        valueFormatted.push(moment(e[0]).startOf(type).toDate());
      }
      if (e[1]) {
        valueFormatted.push(moment(e[1]).endOf(type).toDate());
      }

      this.value = valueFormatted;
    }

    this.group.get(this.config.name)?.setValue(this.value);
    this.tempValue = this.value as (Date | null)[];
  }

  formatDateChange(e: NzSafeAny) {
    const control = this.group.get(this.config.name);
    if (!e) {
      control?.setValue(undefined);
      // in case clear value first time, mark as touched to show validate message
      if (!control?.touched) {
        control?.markAsUntouched();
      }
      return;
    }
    const type = this.setting?.type ?? 'date';
    const autoFill = this.setting?.autoFill ?? 'start-of';
    const valueFormatted =
      autoFill === 'start-of'
        ? moment(e).startOf(type).toString()
        : moment(e).endOf(type).toString();
    this.value = valueFormatted;
    const emitValueFormat = this.setting?.emitValueFormat;
    if (emitValueFormat) {
      const emitValue = moment(e).format(emitValueFormat);
      this.value = emitValue;
      control?.setValue(emitValue);
    } else {
      control?.setValue(valueFormatted);
    }
  }

  initialDateRangeChange(dateArray: (Date | null)[]) {
    const type = this.setting?.type ?? 'date';
    if (dateArray.length === 1 && dateArray[0]) {
      this.value = [moment(dateArray[0]).startOf(type).toDate()];
    }
  }

  touch() {
    // this.group.get(this.config.name)?.markAsTouched();
    this.group.get(this.config.name)?.markAsUntouched();
  }

  getReadOnlyValue() {
    let format = this.getDateFormat(this.setting);

    format = format.replace('dd', '11');
    format = format.replace('DD', 'dd');
    format = format.replace('11', 'DD');
    if (this.config.mode === 'date-picker') {
      if (!this.value) return '--';
      if (!isArray(this.value)) return moment(this.value).format(format);
      return '--';
    }
    if (isArray(this.value)) {
      if (!this.value) return '--';
      if (isDate(this.value[0]) && isDate(this.value[1]))
        return `${moment(this.value[0]).format(format)} - ${moment(
          this.value[1],
        ).format(format)}`;
    }
    return '--';
  }
}
