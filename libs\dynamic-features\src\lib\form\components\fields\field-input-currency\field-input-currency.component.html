@if (config.readOnly) {
  <span>{{ getReadOnlyValue() }}</span>
} @else {
  <div
    class="dynamic-field form-input dynamic-field--field-input"
    [formGroup]="group"
  >
    <hrdx-input-currency
      [ngModel]="value"
      [formControlName]="config.name"
      [precision]="precision"
      [suffix]="suffix"
      [prefix]="prefix"
      [outputType]="config.outputType ?? 'string'"
      [customStyle]="{padding: '5px 0px'}"
      [max]="max"
      [min]="min"
      [borderless]="true"
    >
    </hrdx-input-currency>
  </div>
}
