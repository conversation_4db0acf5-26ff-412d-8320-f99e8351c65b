import { CommonModule } from '@angular/common';
import {
  AfterContentInit,
  Component,
  Input,
  OnChanges,
  SimpleChanges,
  ViewEncapsulation,
  inject,
} from '@angular/core';
import { FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { isNil } from 'lodash';
import {
  BehaviorSubject,
  combineLatest,
  distinctUntilChanged,
  map,
  switchMap,
} from 'rxjs';
import {
  FieldConfig,
  SourceField,
} from '../../../models/field-config.interface';
import { Field, Values } from '../../../models/field.interface';
import { DynamicFormService } from '../../../services/dynamic-form.service';
import { formatCurrency, InputCurrencyComponent } from '@hrdx/hrdx-design';

@Component({
  selector: 'dynamic-field-input-number',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, InputCurrencyComponent, FormsModule],
  templateUrl: './field-input-currency.component.html',
  styleUrls: ['./field-input-currency.component.less'],
  encapsulation: ViewEncapsulation.None,
})
export class FieldInputCurrencyComponent
  implements Field, OnChanges, AfterContentInit
{
  config!: FieldConfig & {outputType?: 'string' | 'number'};
  group!: FormGroup;
  @Input() values: Values = {};
  service = inject(DynamicFormService);
  values$ = new BehaviorSubject<Values>({});
  precision?: number;
  prefix?: string;
  suffix?: string;
  value?: string;
  min?: number;
  max?: number;

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['values']) {
      this.values$.next(this.values);
    }
  }

  ngAfterContentInit() {
    const config = this.config;
    this.value = config.value;

    if (config._value) {
      this.observeValuesChanges(config._value).subscribe((value) => {
        if (isNil(value)) return;
        this.value = value;
      });
    }

    const _suffix = config?.number?._suffix;
    if (_suffix) {
      this.observeValuesChanges(_suffix).subscribe((value) => {
        this.suffix = value;
      });
    }

    const _prefix = config?.number?._prefix;
    if (_prefix) {
      this.observeValuesChanges(_prefix).subscribe((value) => {
        this.suffix = value;
      });
    }

    const _precision = config?.number?._precision;
    if (_precision) {
      this.observeValuesChanges(_precision).subscribe((value) => {
        this.precision = value;
      });
    }

    const _number = config?._number;
    if (_number) {
      this.observeValuesChanges(_number).subscribe((value) => {
        this.updateSetting(value);
      });
    }
    this.updateSetting(config?.number);
  }

  observeValuesChanges(transform: SourceField) {
    return combineLatest({
      _value: this.values$.pipe(
        distinctUntilChanged((prev, curr) => {
          return this.service.distinct(prev, curr, transform);
        }),
        switchMap(() =>
          this.service.getObservable(
            this.values.function,
            this.values,
            transform,
          ),
        ),
      ),
    }).pipe(
      map(({ _value }) => {
        return _value;
      }),
    );
  }

  updateSetting(config: FieldConfig['number']) {
    this.min = config?.min ?? -Infinity;
    this.max = config?.max ?? Infinity;
    this.precision = config?.precision;
    this.prefix = config?.prefix;
    this.suffix = config?.suffix;
  }

  getReadOnlyValue() {
    if (isNil(this.value)) return '--';
    const numberConfig = this.config.number;

    let val = this.value.toString();
    val = formatCurrency(val, { precision: numberConfig?.precision });

    return [this.prefix, val, this.suffix].filter((val) => !!val).join(' ');
  }
}
