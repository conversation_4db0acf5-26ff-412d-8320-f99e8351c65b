import { CommonModule } from '@angular/common';
import {
  AfterContentInit,
  Component,
  Input,
  OnChanges,
  SimpleChanges,
  ViewEncapsulation,
  inject,
  signal,
  viewChild,
} from '@angular/core';
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
} from '@angular/forms';
import { isNil } from 'lodash';
import { NzInputModule } from 'ng-zorro-antd/input';
import {
  NzInputNumberComponent,
  NzInputNumberModule,
} from 'ng-zorro-antd/input-number';
import { NzSelectModule } from 'ng-zorro-antd/select';
import {
  BehaviorSubject,
  combineLatest,
  distinctUntilChanged,
  map,
  switchMap,
  tap,
} from 'rxjs';
import {
  FieldConfig,
  SourceField,
} from '../../../models/field-config.interface';
import { Field, Values } from '../../../models/field.interface';
import { DynamicFormService } from '../../../services/dynamic-form.service';
import {
  TableValuePickerComponent,
  TableValuePickerConfig,
} from './table-value-picker/table-value-picker.component';
import { debounceTime } from 'rxjs/operators';
import { Subject } from 'rxjs';
import { NzSafeAny } from 'ng-zorro-antd/core/types';
import { formatCurrency, ModalComponent } from '@hrdx/hrdx-design';
import * as _ from 'lodash';

@Component({
  selector: 'dynamic-field-input-number',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    NzInputModule,
    NzInputNumberModule,
    FormsModule,
    NzSelectModule,
    TableValuePickerComponent,
  ],
  templateUrl: './field-input-number.component.html',
  styleUrls: ['./field-input-number.component.less'],
  encapsulation: ViewEncapsulation.None,
})
export class FieldInputNumberComponent
  implements Field, OnChanges, AfterContentInit
{
  config!: FieldConfig & {
    radioTable?: TableValuePickerConfig;
  };
  group!: FormGroup;
  @Input() values: Values = {};
  service = inject(DynamicFormService);
  values$ = new BehaviorSubject<Values>({});
  value?: number | null;
  formatter: (value: number) => string | number = (value: number) => value;
  parser: (value: string) => string = (value: string) => value;
  min = -Infinity;
  max = Infinity;
  precision: number | undefined;
  prefix: string | undefined;
  suffix: string | undefined;
  Infinity = Infinity;
  tempValue?: number;
  valuePickerFilterValue = signal<Record<string, NzSafeAny>>({});
  inputRef = viewChild<NzInputNumberComponent>('inputNumber');
  valuePickerAddOnValue = signal<Record<string, unknown>>({});
  @Input() disabled?: boolean;

  modalComponent = inject(ModalComponent);

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['values']) {
      this.values$.next(this.values);
    }
    if (changes['disabled'] && this.disabled) {
      // TODO: blur input field when disabled input is true
      // this.inputRef()?.setDisabledState(true)
      setTimeout(() => {
        this.inputRef()?.blur();
      }, 1);
    }
  }
  value$ = new BehaviorSubject<number | undefined | null>(this.value);
  ngAfterContentInit() {
    const config = this.config;
    this.value = config.value;
    this.value$.next(this.value);
    if (config._value) {
      combineLatest({
        _value: this.values$.pipe(
          distinctUntilChanged((prev, curr) => {
            return this.service.distinct(prev, curr, config._value);
          }),
          switchMap(() =>
            this.service.getObservable(
              this.values.function,
              this.values,
              this.config._value,
            ),
          ),
        ),
      })
        .pipe(
          map(({ _value }) => {
            return _value;
          }),
        )
        .subscribe((value) => {
          if (
            config.isRecommend &&
            !(isNil(this.value) || (this.value as any) === '')
          )
            return;
          if (!isNil(value)) {
            this.value$.next(value);
            this.tempValue = value;
          }
        });
    }

    const _suffix = config?.number?._suffix;
    if (_suffix) {
      this.subscribeValueChange(_suffix).subscribe((value) => {
        this.suffix = value;
      });
    }

    const _prefix = config?.number?._prefix;
    if (_prefix) {
      this.subscribeValueChange(_prefix).subscribe((value) => {
        this.suffix = value;
      });
    }

    const _format = config?.number?._format;
    if (_format) {
      this.subscribeValueChange(_format).subscribe((value) => {
        this.updateFormatter(value);
      });
    }

    const _precision = config?.number?._precision;
    if (_precision) {
      this.subscribeValueChange(_precision).subscribe((value) => {
        this.precision = value;
      });
    }

    if (config.radioTable?.extraFilterConfig?.value) {
      this.valuePickerFilterValue.set(
        config.radioTable?.extraFilterConfig?.value,
      );
    }
    const _extraFilterValue = config.radioTable?.extraFilterConfig?._value;
    if (_extraFilterValue) {
      this.subscribeValueChange(_extraFilterValue).subscribe((value) =>
        this.valuePickerFilterValue.set(value),
      );
    }

    const _radioTableAddOnValue = config.radioTable?.filterConfig?._addOnValue;
    if (_radioTableAddOnValue) {
      this.subscribeValueChange(_radioTableAddOnValue).subscribe((value) =>
        this.valuePickerAddOnValue.set(value),
      );
    }

    this.group.controls[this.config.name].valueChanges.subscribe((value) => {
      if (value === '') {
        this.group.controls[this.config.name]?.setValue(null, {
          emitEvent: false,
        });
        this.value = null;
      } else {
        this.value = value;
      }
      // fix error on blur next temp value will be update old value
      if (value === null) {
        this.tempValue = undefined;
        if (this.config?.radioTable) {
          this.radioTableSubject.next(value);
        }
      }
    });
    this.getNumberFormat(config.number);
    combineLatest({
      _value: this.values$.pipe(
        distinctUntilChanged((prev, curr) => {
          return this.service.distinct(prev, curr, config._number);
        }),
        switchMap(() =>
          this.service.getObservable(
            this.values.function,
            this.values,
            this.config._number,
          ),
        ),
      ),
    })
      .pipe(
        map(({ _value }) => {
          return _value;
        }),
      )
      .subscribe((value) => {
        if (value) {
          this.getNumberFormat(value);
        }
      });
  }

  // subscribeValueChange<T extends keyof this>(key: T, transform: SourceField) {
  subscribeValueChange(transform: SourceField) {
    return combineLatest({
      _value: this.values$.pipe(
        distinctUntilChanged((prev, curr) => {
          return this.service.distinct(prev, curr, transform);
        }),
        switchMap(() =>
          this.service.getObservable(
            this.values.function,
            this.values,
            transform,
          ),
        ),
      ),
    }).pipe(
      map(({ _value }) => {
        return _value;
      }),
    );
  }

  getNumberFormat(config: FieldConfig['number']) {
    this.min = config?.min ?? -this.Infinity;
    this.max = config?.max ?? this.Infinity;
    this.precision = config?.precision;
    this.prefix = config?.prefix;
    this.suffix = config?.suffix;
    this.updateFormatter(config?.format ?? '');
    return;
  }

  prevFormattedNumber = '';
  prevCursorPos = 0;
  isFormatCurrency = false;
  updateFormatter(format: string) {
    if (format === 'currency') {
      this.isFormatCurrency = true;
      this.formatter = (value: number): string | number => {
        // if (!value) return '';
        if (isNil(value)) return '';
        return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
      };
      this.parser = (value: string): string => {
        if (this.isFormatCurrency) {
          this.prevFormattedNumber = value;

          const inputRef = this.inputRef();
          const nativeElement = inputRef?.inputElement.nativeElement;
          if (nativeElement) {
            this.prevCursorPos = nativeElement.selectionStart ?? 0;
          }
        }
        const parsedValue = value.replace(/\$\s?|(,*)/g, '');
        return parsedValue;
      };
      return;
    } else {
      this.isFormatCurrency = false;
      this.resetFormatter();
    }
  }

  resetFormatter() {
    this.formatter = (value: number) => value;
    this.parser = (value: string) => value;
  }

  setInputSelectionRange(start = 0, end = 0) {
    const inputRef = this.inputRef();
    const nativeElement = inputRef?.inputElement.nativeElement;
    if (!nativeElement) return;
    nativeElement.setSelectionRange(start, end);
  }

  run$ = this.value$
    .pipe(
      tap((value) => {
        const originalValue = value;
        if (value === undefined || value === null) return;
        if (this.max !== undefined && value > this.max) {
          value = this.max;
        }

        if (this.min !== undefined) {
          if (value < this.min) {
            if (this.config.number?.alertOnMin) {
              this.modalComponent.showDialog(
                {
                  nzContent: this.config.number?.alertOnMin,
                  nzWrapClassName: 'popup popup-confirm',
                  nzIconType: 'icons:warning',
                  nzOkText: 'OK',
                  nzCancelText: null,
                },
                'warning',
              );
            }

            value = this.min;
          }
        }
        if (value !== originalValue && this.config?.radioTable) {
          this.radioTableSubject.next(value);
        }
        this.value = value;
        this.tempValue = value;
        this.group.get(this.config.name)?.setValue(value);
      }),
    )
    .subscribe();

  prevValue?: number;
  onChange(value: number) {
    this.tempValue = value;
    this.value = value;

    if (this.isFormatCurrency) {
      if (value) {
        this.inputRef()?.updateDisplayValue(value);
        const formattedValue = this.formatCurrency(value);
        let newSelectionStart =
          this.prevCursorPos +
          (formattedValue.length - this.prevFormattedNumber.length);

        if (newSelectionStart >= 2 && this.prevValue === 0) {
          newSelectionStart -= 1;
        }

        this.setInputSelectionRange(newSelectionStart, newSelectionStart);
      } else {
        if (this.prevCursorPos > 0) return;
        this.setInputSelectionRange(0);
      }
    }
    this.prevValue = value;
  }

  formatCurrency(value: number) {
    return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  }

  onBlur() {
    this.value$.next(this.tempValue);

    if (this.config?.radioTable) {
      const value = this.group.get(this.config.name)?.value;
      this.radioTableValue = value;
    }
  }
  focus() {
    this.group.get(this.config.name)?.markAsTouched();
  }

  getReadOnlyValue() {
    if (this.value || this.value === 0) {
      const numberConfig = this.config.number;
      if (!numberConfig) return this.value;

      let val = this.value.toString();
      if (numberConfig.format === 'currency') {
        val = formatCurrency(val, { precision: numberConfig?.precision });
      }

      const addOnAfterVal =
        this.config.addOnAfter?.type === 'select'
          ? this.config.addOnAfter?.select?.[0]?.label
          : null;

      return [this.prefix, val, this.suffix, addOnAfterVal]
        .filter((val) => !!val)
        .join(' ');
    }
    return '--';
  }

  radioTableValue: number | null = null;
  // Add this at the beginning of your class
  private radioTableSubject = new Subject<number>();

  constructor() {
    this.radioTableSubject.pipe(debounceTime(1000)).subscribe((value) => {
      this.radioTableValue = value;
    });
  }

  onUpdateValue(value: NzSafeAny) {
    const selectedValue = value[this.config?.radioTable?.key ?? ''];
    this.tempValue = selectedValue;
    this.value = selectedValue;
    this.group.get(this.config.name)?.setValue(selectedValue);

    const pickedValue = this.config.name + 'PickedValue';
    if (this.group.contains(pickedValue)) {
      this.group.get(pickedValue)?.setValue(value);
    } else {
      this.group.addControl(pickedValue, new FormControl(value));
    }
  }

  readonly allowKeys: string[] = [
    'Backspace',
    'Delete',
    'Tab',
    'Enter',
    'ArrowLeft',
    'ArrowRight',
  ];
  keydown($event: KeyboardEvent) {
    const key = $event.key;
    const regex = /^[\d.,-]+$/;
    if (this.config?.radioTable) {
      if (key === 'Enter') {
        const inputValue = this.value;
        if (!_.isNaN(Number(inputValue))) {
          this.radioTableValue = Number(inputValue);
        }
      }
    }

    const ctrlOrMeta = $event.ctrlKey || $event.metaKey;

    if (ctrlOrMeta) return;

    if (this.allowKeys.includes(key)) return;

    if (!regex.test($event.key) || (this.precision === 0 && key === '.')) {
      $event.preventDefault();
      return;
    }

    if (this.precision && this.precision > 0) {
      const currentValue = ($event.target as HTMLInputElement)?.value;
      const [integer, fractional = ''] = currentValue.toString().split('.');
      if (fractional.length < this.precision) return;
      $event.preventDefault();
    }
  }
}
