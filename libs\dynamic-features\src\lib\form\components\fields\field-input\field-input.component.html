<div
  class="dynamic-field form-input dynamic-field--field-input"
  [ngClass]="{ hidden: config.readOnly, disabled: disabled }"
  [formGroup]="group"
>
  <!-- <input
      [type]="config.type"
      [placeholder]="
        config.placeholder ?? 'Enter ' + (config.label ?? '').toLowerCase()
      "
      [formControlName]="config.name"
      [ngModel]="value"
      nz-input
    /> -->

  <hrdx-input
    [placeHolder]="placeholder()"
    [(value)]="value"
    [type]="config.type"
    [borderLess]="true"
    [formControlName]="config.name"
    [ngClass]="config.class"
    [suffix]="config.suffix"
    [prefix]="config.prefix"
    [maxCharCount]="config.maxCharCount"
    [formatFn]="formatFn"
    [forbiddenKeys]="forbiddenKeys"
    [formatByKeydown]="formatByKeydown"
    [disabledInput]="disabled"
  >
  </hrdx-input>
</div>

<div class="form-input-read-only">
  @if (valueType === 'object' && !isValueArray) {
    <div [ngClass]="{ hidden: !config.readOnly }" class="input-config">
      <span
        [ngClass]="{
          'read-only': config.readOnly,
          hyperlink: config.hyperlink,
        }"
      >
        {{ customLabel || '--' }}
      </span>
      <hrdx-button
        [size]="'xsmall'"
        [type]="'ghost-color'"
        (clicked)="openModal()"
        [onlyIcon]="true"
        [icon]="'icon-article-bold'"
        *ngIf="!!customLabel"
      >
      </hrdx-button>
    </div>
  } @else {
    <div
      class="read-only-text"
      [ngClass]="{ hidden: !config.readOnly, isInfo: config.isInfo }"
    >
      <hrdx-icon
        [name]="'eye'"
        [fontStyle]="'light'"
        *ngIf="config.hyperlink"
      />
      <hrdx-icon
        [name]="'icon-info-bold'"
        *ngIf="config.isInfo"
        [color]="'color-icon-disabled'"
        [size]="20"
      />
      <span
        [ngClass]="{
          'read-only': config.readOnly,
          hyperlink: config.hyperlink,
        }"
        [style.color]="config.color"
      >
        {{ getReadOnlyValue() }}
      </span>
    </div>

    <label
      nz-tooltip
      [nzTooltipTitle]="titleTooltip"
      nzTooltipPlacement="topRight"
      [nzTooltipOverlayClassName]="'custom-tooltip'"
      *ngIf="config?.additionalData?.fields"
    >
      <hrdx-icon [name]="'circle-exclamation'" [fontStyle]="'light'" />
    </label>
    <ng-template #titleTooltip>
      <ng-container>
        <div class="header-tooltip">
          <div class="header">{{ value }}</div>
        </div>
        <nz-divider class="custom-divider" />
        <div class="content-tooltip">
          <dynamic-form
            [config]="config?.additionalData?.fields ?? []"
            [sources]="config?.additionalData?.sources ?? {}"
            [variables]="config?.additionalData?.variables ?? {}"
            [formValue]="selectedActionValue()"
            [readOnly]="true"
            [ppxClass]="'ppxm-style'"
            [extend]="{
              formType: 'view',
            }"
            #formObj
          ></dynamic-form>
        </div>
      </ng-container>
    </ng-template>
  }
</div>

<hrdx-modal
  [isVisible]="modalVisible()"
  [title]="customLabel"
  [footer]="footer"
  [size]="'small'"
  (canceled)="closeModal()"
  *ngIf="valueType === 'object' && modalVisible()"
>
  <div class="modal-content">
    <ng-container [ngTemplateOutlet]="formTemplate"></ng-container>
  </div>
  <ng-template #footer>
    <div class="dialog-footer">
      <hrdx-button
        [type]="'tertiary'"
        [title]="'Close'"
        [size]="'default'"
        (clicked)="closeModal()"
      >
      </hrdx-button>
    </div>
  </ng-template>
</hrdx-modal>

<ng-template #formTemplate>
  <dynamic-form
    [config]="config.formConfig?.fields ?? []"
    [sources]="config.formConfig?.sources ?? {}"
    [variables]="config.formConfig?.variables ?? {}"
    [formValue]="value"
    [readOnly]="true"
    [ppxClass]="'ppxm-style'"
    [extend]="{
      formType: 'view',
    }"
    #formObj
  ></dynamic-form>
</ng-template>
