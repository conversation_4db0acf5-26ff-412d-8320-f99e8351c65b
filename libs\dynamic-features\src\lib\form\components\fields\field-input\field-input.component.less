@import '../../../../../../../hrdx-design/src/themes/tokens.less';

dynamic-field-input {
  width: 100%;

  .dynamic-field {
    &--field-input {
      .ant-input {
        padding: 8px 0;
        font-size: 14px;
        line-height: 16px;
        background: none;
        border: none;
        box-shadow: none;
      }

      &.disabled {
        nz-input-group {
          background-color: var(--neutral-grey-5);
        }
      }
    }
  }
  .read-only-text.isInfo{
    display: flex;
    gap: @spacing-basis;
    align-items: center;
    margin-top: -@spacing-2;
    line-height: @font-line-height-base;
    color: @color-text-hint;
    span{
      color: @color-text-hint;
    }
  }
  .read-only {
    color: var(--neutral-grey-1);
  }

  .read-only.hyperlink {
    color: var(--info-color);
    cursor: pointer;
  }

  // .read-only-text {
  //   display: flex;
  //   gap: @spacing-2;
  //   align-items: center;
  // }
  .form-input-read-only {
    display: flex;
    align-items: center;
    gap: @spacing-2;
  }

  .custom-disabled {
    input::placeholder {
      color: @color-text-placeholder;
      font-weight: @font-weight-regular;
    }
  }

  .input-config {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: @spacing-2;
  }
}

input::placeholder {
  color: @color-text-placeholder;
}
