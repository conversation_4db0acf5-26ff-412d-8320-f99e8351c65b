import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { CommonModule } from '@angular/common';
import {
  Component,
  Input,
  OnChanges,
  SimpleChanges,
  ViewEncapsulation,
  computed,
  forwardRef,
  inject,
  signal,
  AfterViewInit,
  SkipSelf,
} from '@angular/core';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import {
  ButtonComponent,
  IconComponent,
  InputComponent,
  ModalComponent,
  ToastMessageComponent,
} from '@hrdx/hrdx-design';
import { isArray, isNil } from 'lodash';
import { NzInputModule } from 'ng-zorro-antd/input';
import {
  BehaviorSubject,
  Observable,
  Subscription,
  combineLatest,
  distinctUntilChanged,
  filter,
  map,
  of,
  switchMap,
  tap,
} from 'rxjs';
import {
  FieldInputConfig,
  FieldInputFormatFnType,
} from '../../../models/field-config.interface';
import { Field, Values } from '../../../models/field.interface';
import { DynamicFormService } from '../../../services/dynamic-form.service';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { FormComponent } from '../../../form.component';
import { NzSafeAny } from 'ng-zorro-antd/core/types';
import { FormControlService } from '../../../services/form-control.service';
import {
  codeForbiddenKeys,
  getHelperFnByName,
  mergeHelperFns,
} from '../../../utils/input.helper';

@Component({
  selector: 'dynamic-field-input',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    NzInputModule,
    InputComponent,
    IconComponent,
    NzToolTipModule,
    NzDividerModule,
    ModalComponent,
    ButtonComponent,
    forwardRef(() => FormComponent),
  ],
  templateUrl: './field-input.component.html',
  styleUrls: ['./field-input.component.less'],
  encapsulation: ViewEncapsulation.None,
})
export class FieldInputComponent implements Field, OnChanges, AfterViewInit {
  config!: FieldInputConfig;
  group!: FormGroup;
  @Input() values: Values = {};
  value$!: Observable<string>;
  service = inject(DynamicFormService);
  values$ = new BehaviorSubject<Values>({});
  additionalData$ = new BehaviorSubject<Values>({});
  value: string | null = '';
  customLabel = '';
  selectedActionValue = signal<NzSafeAny>(null);
  private readonly valueToSetNull = '_setValueNull' as const;
  formatFn?: (input: any) => any;
  forbiddenKeys?: string[];
  formatByKeydown?: boolean;
  toast = inject(ToastMessageComponent);
  @Input() disabled = false;

  constructor(@SkipSelf() private formControlService: FormControlService) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['values']) {
      this.values$.next(this.values);
    }
  }

  subscriptionSetValueForm!: Subscription;
  async ngAfterViewInit() {
    const config = this.config;
    this.value = config.value;
    this.group.get(config.name)?.setValue(this.value);
    this.setFormatFn(config.formatFn ?? []);

    if (!isNil(config.formatByKeydown)) {
      this.formatByKeydown = config.formatByKeydown;
    }

    //lắng nghe thay đổi value từ service handleAfterChange
    this.subscriptionSetValueForm =
      this.formControlService.setValueSubject$.subscribe((value: NzSafeAny) => {
        let currentPath = structuredClone(this.values.extend?.['path']);
        currentPath?.pop();
        currentPath = currentPath?.join('.');

        const checkPath = value.path ? value.path === currentPath : true;
        if (value.key === config.name && !isNil(value) && checkPath) {
          this.updateValue(value.value);
        }
      });

    if (config.formatType) {
      this.setConfigByFormatType(config.formatType);
    }

    const skip = config._value?.skip;
    let skipCount = 0;
    let valuesPair: Values[] = [];
    this.value$ = combineLatest({
      _value: this.values$.pipe(
        distinctUntilChanged((prev, curr) => {
          // save to use later;
          valuesPair = [prev, curr];
          return this.service.distinct(prev, curr, config._value);
        }),
        switchMap((values) => {
          const count = skip?.count ?? 1;
          if (!skip?.condition || skipCount >= count)
            return of({ skip: false, values });
          return this.service
            .getObservable(this.values.function, values, {
              transform: skip.condition,
            })
            .pipe(
              map((res) => {
                return { skip: res, values };
              }),
            );
        }),
        tap((values) => {
          if (values.skip) skipCount++;
        }),
        filter((values) => !values.skip),
        switchMap(({ values }) =>
          this.service.getObservable(
            this.values.function,
            values,
            this.config._value,
          ),
        ),
        tap((value) => {
          const onSuccess = config._value?.onSuccess;
          if (isNil(value) || !onSuccess) return;
          if (
            onSuccess.whenChangeFields &&
            this.service.distinct(valuesPair[0] ?? {}, valuesPair[1] ?? {}, {
              transform: onSuccess.whenChangeFields,
            })
          ) {
            return;
          }

          switch (onSuccess?.type) {
            case 'toast': {
              this.toast.showToast('success', '', onSuccess.message);
              break;
            }
          }
        }),
      ),
    }).pipe(
      map(({ _value }) => {
        return _value;
      }),
    );
    if (config._value) {
      this.value$.subscribe((value) => {
        if (config.isRecommend && !isNil(this.value)) return;
        if (value === this.valueToSetNull) {
          this.updateValue(null);
          return;
        }
        if (value) {
          this.updateValue(value);
        }
      });
    }

    if (this.config.additionalData) {
      combineLatest({
        _additional: this.additionalData$.pipe(
          distinctUntilChanged((prev, curr) => {
            return this.service.distinct(prev, curr, config._additionalData);
          }),
          switchMap(() =>
            this.service.getObservable(
              this.values.function,
              this.values,
              this.config._additionalData,
            ),
          ),
        ),
      })
        .pipe(
          map(({ _additional }) => {
            return _additional;
          }),
        )
        .subscribe((v) => {
          this.selectedActionValue.set(v);
        });
    }

    this.customLabel = await this.dynamicService.getJsonataExpression({})(
      this.config.labelTransform?.transform ?? '',
      this.value,
    );
  }
  dynamicService = inject(DynamicFormService);

  updateValue(value: string | null) {
    this.value = value;
    this.group.get(this.config.name)?.setValue(value);
  }

  getReadOnlyValue() {
    if (this.value || this.value == '0') {
      if (this.isValueArray) {
        return (this.value as NzSafeAny).join(', ');
      }
      return this.value;
    }
    return '--';
  }

  get valueType() {
    return typeof this.value;
  }

  get isValueArray() {
    return isArray(this.value);
  }

  modalVisible = signal(false);

  openModal() {
    this.modalVisible.set(true);
  }

  modalSize = computed(() => {
    return 'small';
  });

  closeModal() {
    this.modalVisible.set(false);
  }

  placeholder = computed(() => {
    const ph =
      this.config.placeholder ??
      'Enter ' + (this.config.label ?? '').toLowerCase();
    switch (ph) {
      case PlaceholderText.AUTOMATIC:
        return PlaceholderText.SYSTEMGENERATED;
      default:
        return ph;
    }
  });

  private setFormatFn(formatFnNames: FieldInputFormatFnType[]) {
    this.formatFn = mergeHelperFns(formatFnNames);
  }

  setConfigByFormatType<T extends FieldInputConfig['formatType']>(type: T) {
    switch (type) {
      case 'code': {
        this.forbiddenKeys = codeForbiddenKeys;
        this.setFormatFn(['removeAccent', 'upperCase']);
        // this.formatByKeydown = true;
      }
    }
  }
}

enum PlaceholderText {
  AUTOMATIC = 'Automatic',
  SYSTEMGENERATED = 'System - Generated',
}
