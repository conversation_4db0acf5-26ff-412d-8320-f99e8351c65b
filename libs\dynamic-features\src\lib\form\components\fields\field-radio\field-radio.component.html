<div
  class="dynamic-field form-input dynamic-field--field-radio"
  [ngClass]="{
    'dir-col': direction === 'column',
    'has-background': config.hasBackground,
    'radio-group-custom': radioGroupType === 'custom',
  }"
  [ngStyle]="{
    '--n-cols': n_cols,
  }"
  [formGroup]="group"
>
  @if (!config.readOnly) {
    <ng-container [ngSwitch]="radioGroupType">
      <ng-container *ngSwitchCase="'custom'">
        <nz-radio-group [formControlName]="config.name" [ngModel]="value">
          @for (option of optionList; track option.value) {
            <label
              [ngClass]="{
                'label-wrapper': config.hasBackground,
                'set-height':
                  value === option.value && checkFields(option.fields),
              }"
              nz-radio
              [nzValue]="option.value"
              [nzDisabled]="option.disabled || disabled"
              nz-tooltip
              [nzTooltipTitle]="option.tooltip ? tooltipTemplate : undefined"
              [nzTooltipTitleContext]="{ $implicit: option.tooltip }"
              nzTooltipPlacement="bottom"
              (click)="onChange($event, option.value)"
            >
              <span class="text">{{ option.label }}</span>
            </label>
            <ng-container
              *ngIf="value === option.value && checkFields(option.fields)"
            >
              <div class="radio-group-input">
                <dynamic-form
                  [config]="option.fields ?? []"
                  [sources]="config.sources ?? {}"
                  [variables]="config.variables"
                  [formValue]="{}"
                  [extend]="values?.extend ?? {}"
                  [readOnly]="false"
                  [ppxClass]="'ppxm-style'"
                  [id]="option.value"
                  (valueChanges)="
                    valueChange(config.name, option.value, $event.value)
                  "
                  [faceCode]="values.faceCode"
                  [authAction]="values.authAction"
                  #formObj
                ></dynamic-form>
              </div>
            </ng-container>
          }
        </nz-radio-group>
      </ng-container>

      <ng-container *ngSwitchDefault>
        <nz-radio-group [formControlName]="config.name" [ngModel]="value">
          <label
            [ngClass]="{
              'label-wrapper': config.hasBackground,
              'set-height':
                value === option.value && checkFields(option.fields),
            }"
            nz-radio
            [nzValue]="option.value"
            [nzDisabled]="option.disabled || disabled"
            nz-tooltip
            [nzTooltipTitle]="option.tooltip ? tooltipTemplate : undefined"
            [nzTooltipTitleContext]="{ $implicit: option.tooltip }"
            nzTooltipPlacement="bottom"
            *ngFor="let option of optionList"
            (click)="onChange($event, option.value)"
          >
            <span class="text">{{ option.label }}</span>

            <ng-container
              *ngIf="value === option.value && checkFields(option.fields)"
            >
              <div class="radio-group-input">
                <dynamic-form
                  [config]="option.fields ?? []"
                  [sources]="config.sources ?? {}"
                  [variables]="config.variables"
                  [formValue]="{}"
                  [extend]="values?.extend ?? {}"
                  [readOnly]="false"
                  [ppxClass]="'ppxm-style'"
                  [id]="option.value"
                  (valueChanges)="
                    valueChange(config.name, option.value, $event.value)
                  "
                  [faceCode]="values.faceCode"
                  [authAction]="values.authAction"
                  #formObj
                ></dynamic-form>
              </div>
            </ng-container>
          </label>
        </nz-radio-group>
      </ng-container>
    </ng-container>
  } @else {
    <span [ngClass]="getReadonlyClassNames(value)">
      {{ mappingLabel(value) ?? '--' }}
    </span>
  }
</div>

<ng-template #tooltipTemplate let-tooltip>
  <div class="tooltip">
    <div class="title">{{ tooltip.title }}</div>
    <hr />
    <div *ngFor="let description of tooltip.descriptionArray">
      - {{ description }}
    </div>
  </div>
</ng-template>
