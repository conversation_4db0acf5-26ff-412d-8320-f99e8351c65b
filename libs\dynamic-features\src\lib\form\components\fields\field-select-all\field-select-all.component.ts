import {
  Component,
  Input,
  AfterViewInit,
  inject,
  SimpleChanges,
  OnChanges,
  signal,
  computed,
  viewChild,
  SkipSelf,
  ChangeDetectorRef,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  AvatarComponent,
  AvatarSize,
  AvatarType,
  SelectComponent,
  SelectMode,
} from '@hrdx/hrdx-design';
import { Field, Values } from '../../../models/field.interface';
import { FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzTreeSelectModule } from 'ng-zorro-antd/tree-select';
import { NzIconModule } from 'ng-zorro-antd/icon';

import {
  BehaviorSubject,
  Observable,
  Subscription,
  combineLatest,
  connect,
  debounceTime,
  distinctUntilChanged,
  filter,
  finalize,
  map,
  merge,
  skip,
  switchMap,
  take,
  tap,
} from 'rxjs';
import { FieldSelect } from '../../../models/field-config.interface';
import { DynamicFormService } from '../../../services/dynamic-form.service';
import { cloneDeep, isArray, isEqual, isNil, uniqBy } from 'lodash';
import { NzSafeAny } from 'ng-zorro-antd/core/types';
import { toObservable } from '@angular/core/rxjs-interop';
import { FormControlService } from '../../../services/form-control.service';

@Component({
  selector: 'dynamic-field-select-all',
  standalone: true,
  imports: [
    CommonModule,
    SelectComponent,
    ReactiveFormsModule,
    NzSelectModule,
    NzTreeSelectModule,
    FormsModule,
    NzIconModule,
    AvatarComponent,
  ],
  templateUrl: './field-select-all.component.html',
  styleUrl: './field-select-all.component.less',
})
export class FieldSelectAllComponent
  implements Field, AfterViewInit, OnChanges
{
  config!: FieldSelect & FieldSelectAllConfig;
  group!: FormGroup;
  mode = SelectMode.Tags;
  @Input() values: Values = {};
  optionList = signal<any[]>([]);
  optionList$?: Observable<OptionAll[] | undefined>;
  notExistOptions: OptionAll[] = [];
  AvatarType = AvatarType;
  AvatarSize = AvatarSize;
  // value: string[] = [];
  value = signal<string[] | null>([]);
  placeholder = 'Select option';
  readOnly = false;
  value$ = new BehaviorSubject<any>(undefined);
  values$ = new BehaviorSubject<Values>({});
  service = inject(DynamicFormService);
  loading = false;
  cloneOptionList: NzSafeAny[] = [];
  isServerSearch = false;
  total = 0;
  prevSearch = '';
  listLoading = signal<boolean>(false);
  stopPaging = false;
  subscription?: Subscription;
  page = signal(1);
  oldPage = 0;
  pageSize = signal(10);
  page$ = toObservable(this.page);
  pageSize$ = toObservable(this.pageSize);
  searchChange$ = new BehaviorSubject('');
  maxTag?: number;
  selectRef = viewChild<SelectComponent>('selectRef');

  defaultValue$ = new BehaviorSubject<NzSafeAny>(undefined);
  defaultValue = signal<NzSafeAny>(undefined);

  constructor(
    @SkipSelf() private formControlService: FormControlService,
    private cdr: ChangeDetectorRef,
  ) {}

  searchText$ = this.searchChange$
    .asObservable()
    .pipe(
      connect((s) =>
        merge(
          s.pipe(take(1)),
          s.pipe(skip(1), debounceTime(300), distinctUntilChanged()),
        ),
      ),
    );
  filter$ = new BehaviorSubject<{ [k: string]: any }>({});

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['values']) {
      this.values$.next(this.values);
    }
  }

  _optionList = computed(() => {
    return isArray(this.optionList())
      ? this.optionList().map((option: NzSafeAny) => {
          return {
            ...option,
            value: this.getOutputValue(option),
          };
        })
      : [];
  });

  onScrollToBottom() {
    if (
      !this.stopPaging &&
      !this.listLoading() &&
      this.enabledLoadMore &&
      this.config.isLazyLoad
    ) {
      this.page.update((v) => v + 1);
    }
  }

  removeOptionNotExist() {
    const newOptions =
      this.optionList()?.filter((it) => {
        return !this.notExistOptions.find((option) => {
          return this.compare(option.value, it);
        });
      }) || [];
    this.optionList.set(newOptions);
    this.notExistOptions = [];
  }

  onOpenChange(open: boolean) {
    this.selectRef()?.updateMaxTagByFocusState(open);
    if (open && this.config.isLazyLoad) {
      this.stopPaging = false;

      if (this.config.isRemoveOptionNotExist) {
        this.removeOptionNotExist();
      }

      // setTimeout(() => {
      //   const el = document.querySelector(
      //     '.dynamic-field--field-select--dropdown .cdk-virtual-scrollable ',
      //   ) as HTMLDivElement;
      //   el?.addEventListener('scroll', () => {
      //     if (
      //       el.offsetHeight + el.scrollTop >= el.scrollHeight - 5 &&
      //       !this.stopPaging
      //     ) {
      //       this.page.update((v) => v + 1);
      //     }
      //   });
      // }, 1000);
    }
    if (this.subscription) {
      return;
    }
    if (this.config.isLazyLoad && open === true) {
      this.subscription = this.optionList$?.subscribe();
    }
  }

  onSearch(value: string): void {
    this.page.set(1);
    this.searchChange$.next(value);
  }
  onFilter(value: { [k: string]: any }) {
    // this.filterVisible.set(false);
    this.filter$.next(value);
  }

  private handlePaging(values: any): Observable<any> {
    this.listLoading.set(true);
    return this.service
      .getObservable(this.values.function, values, this.config._options)
      .pipe(
        tap((data) => {
          if (!data || !data.length) {
            this.stopPaging = true;
            return;
          }
          if (!isArray(data)) return;
          if (this.config.isLazyLoad) {
            this.optionList.set([
              ...this.optionList(),
              ...(data.map((it) => {
                return it;
              }) ?? []),
            ]);
          } else {
            this.optionList.set([
              ...(data.map((it) => {
                return it;
              }) ?? []),
            ]);
          }

          this.subscriptionOptionList$.next(this.optionList());
        }),
        tap(() => this.cdr.detectChanges()),
        finalize(() => this.listLoading.set(false)),
      );
  }

  private handleNotPaging(values: NzSafeAny): Observable<NzSafeAny> {
    this.loading = true;
    this.cdr.detectChanges();
    return this.service
      .getObservable(this.values.function, values, this.config._options)
      .pipe(
        tap((data) => {
          // console.log('not paging', data);
          if (!data || !data.length) {
            this.stopPaging = true;
            this.optionList.set(
              Array.from(this.config?.select || []).map((it) => {
                return it;
              }),
            );
            this.subscriptionOptionList$.next(this.optionList());
            return;
          }
          if (!isArray(data)) return;
          this.optionList.set(
            data.map((it) => {
              return it;
            }) ?? [],
          );
          this.subscriptionOptionList$.next(this.optionList());
        }),

        finalize(() => {
          this.loading = false;
          this.cdr.detectChanges();
        }),
      );
  }

  subscriptionOptionList$ = new BehaviorSubject<OptionAll[] | undefined>(
    undefined,
  );

  enabledLoadMore = true;

  subscriptionSetValueForm!: Subscription;

  ngAfterViewInit(): void {
    this.optionList.set(this.config.options);
    this.value.set(this.config.value);
    this.group.get(this.config.name)?.setValue(this.value());

    this.placeholder =
      this.config?.placeholder ??
      this.config?.placeHolder ??
      'Select ' + this.config?.label ??
      'Select option';
    this.readOnly = this.config.readOnly;

    const config = this.config;
    this.maxTag = config.settings?.maxTag ?? 1;

    this.enabledLoadMore = config.settings?.enabledLoadMore ?? true;

    //lắng nghe thay đổi value từ service handleAfterChange
    this.subscriptionSetValueForm =
      this.formControlService.setValueSubject$.subscribe((value: NzSafeAny) => {
        let currentPath = structuredClone(this.values.extend?.['path']);
        currentPath?.pop();
        currentPath = currentPath?.join('.');

        const checkPath = value.path ? value.path === currentPath : true;
        if (value.key === config.name && !isNil(value) && checkPath) {
          this.setValueData(value.value);
        }
      });

    if (config._options) {
      this.isServerSearch =
        config._options?.transform.includes('$.extend.search') ?? false;
    }
    const _total = config.selectSetting?._total;
    if (config.selectSetting?.total) {
      const value = config.selectSetting?.total;
      if (isNil(value)) return;
      if (typeof value !== 'number') return;
      this.total = value;
    } else if (_total) {
      combineLatest({
        total: this.values$.pipe(
          distinctUntilChanged((prev, curr) => {
            return this.service.distinct(prev, curr, _total);
          }),
          switchMap(() =>
            this.service.getObservable(
              this.values.function,
              this.values,
              _total,
            ),
          ),
          tap(() => this.cdr.detectChanges()),
        ),
      })
        .pipe(
          map(({ total }) => {
            return total;
          }),
          tap(() => this.cdr.detectChanges()),
        )
        .subscribe((value) => {
          if (isNil(value)) return;
          if (typeof value !== 'number') return;
          this.total = value;
        });
    }

    if (config) {
      this.value$.next(config.value);
      if (config._value)
        combineLatest({
          _value: this.values$.pipe(
            distinctUntilChanged((prev, curr) => {
              return this.service.distinct(prev, curr, config._value);
            }),
            switchMap(() =>
              this.service.getObservable(
                this.values.function,
                this.values,
                this.config._value,
              ),
            ),
            tap(() => this.cdr.detectChanges()),
          ),
        })
          .pipe(
            map(({ _value }) => {
              return _value;
            }),
            tap(() => this.cdr.detectChanges()),
          )
          .subscribe((value) => {
            // if (!isNil(value)) {
            if (config.isRecommend && !isNil(this.value)) return;
            this.value$.next(value);
            // }
          });

      if (config._defaultValue)
        combineLatest({
          _value: this.values$.pipe(
            distinctUntilChanged((prev, curr) => {
              return this.service.distinct(prev, curr, config._defaultValue);
            }),
            switchMap(() =>
              this.service.getObservable(
                this.values.function,
                this.values,
                this.config._defaultValue,
              ),
            ),
            tap(() => this.cdr.detectChanges()),
          ),
        })
          .pipe(
            map(({ _value }) => {
              return _value;
            }),
            tap(() => this.cdr.detectChanges()),
          )
          .subscribe((value) => {
            if (config.isRecommend && !isNil(this.value())) return;
            this.defaultValue$.next(value);
          });
      this.value$
        .pipe(
          tap((v) => {
            if (isNil(v)) return;
            if (v === '_setSelectValueNull') {
              // this._value.set(null);
              this.value.set([]);
              this.group.get(this.config.name)?.setValue(null);
              return;
            }

            this.setValueData(v);
          }),
          tap(() => this.cdr.detectChanges()),
        )
        .subscribe();

      this.defaultValue$
        .pipe(
          tap((v) => {
            if (isNil(v)) return;
            let validateValue = [];
            if (isArray(v)) {
              validateValue = v.filter((it) => {
                if (it.label && it.value) {
                  return true;
                }
                return false;
              });
            } else validateValue = [];
            this.notExistOptions = validateValue.filter((it) => {
              return !this.optionList().find((option) => {
                return this.compare(option.value, it);
              });
            });
            const oldOptionList = structuredClone(this.optionList());

            this.optionList.set([
              ...this.notExistOptions.map((it: NzSafeAny) => it),
              ...oldOptionList,
            ]);
            this.defaultValue.set(v);
            const defaultValue = this.defaultValue();
            const value = this.value() || [];
            if (isArray(defaultValue)) {
              const v1 = value.filter(
                (i) => !defaultValue.find((j) => this.compare(i, j)),
              );
              this.value.set(v1);
            }
          }),
          tap(() => this.cdr.detectChanges()),
        )
        .subscribe();

      if (config._options) {
        this.optionList$ = combineLatest({
          values: this.values$,
          search: this.searchText$,
          filter: this.filter$,
          page: this.page$,
          pageSize: this.pageSize$,
        }).pipe(
          map(({ values, search, filter, page, pageSize }) => ({
            fields: values.fields,
            variables: values.variables,
            extend: {
              ...values.extend,
              search: search?.trim(),
              filter: filter,
              limit: pageSize,
              page: page,
            },
          })),
          distinctUntilChanged((prev, curr) => {
            return this.service.distinct(prev, curr, config._options);
          }),
          switchMap((values) => {
            const isPaging =
              values.extend.page > 1 &&
              values.extend.search === this.prevSearch;
            if (values.extend.page === this.oldPage) {
              this.stopPaging = false; // Reset stopPaging when search term changes
              this.page.set(1); // Reset page to 1 for new search
              this.oldPage = 0;
            }
            this.prevSearch = values.extend.search;
            this.oldPage = values.extend.page;

            if (isPaging) {
              return this.handlePaging(values);
            } else {
              return this.handleNotPaging(values);
            }
          }),
          tap(() => this.cdr.detectChanges()),
        );
      }
      if (!config.isLazyLoad) this.subscription = this.optionList$?.subscribe();

      if (this.config._validateFn) {
        combineLatest({
          values: this.values$,
        })
          .pipe(
            map(({ values }) => ({
              ...values,
              value: this.value(),
            })),
            filter((values) => !!values.value),
            distinctUntilChanged((prev, curr) =>
              this.service.distinct(prev, curr, this.config._validateFn),
            ),
            switchMap((values) =>
              this.service.getObservable(
                values.function,
                values,
                this.config._validateFn,
              ),
            ),
            tap((v) => {
              if (isNil(v)) return;
              if (v === '_setSelectValueNull') {
                this.value.set(null);
                this.group.get(this.config.name)?.setValue(null);
                return;
              }
              let validateValue = [];
              if (isArray(v)) {
                validateValue = v
                  .filter((it) => {
                    if (it.label && it.value) {
                      return true;
                    } else if (typeof it === 'string') {
                      return true;
                    }
                    return false;
                  })
                  .map((it) => {
                    if (typeof it === 'string') {
                      return { label: it, value: it };
                    }
                    return it;
                  });
              } else validateValue = [];

              // this.notExistOptions = validateValue.filter((it) => {
              //   return !this.optionList()?.find((option) => {
              //     return this.compare(option.value, it);
              //   });
              // });

              const updateLabelExistOption =
                this.config._validateFn?.params?.['updateLabelExistOption'];

              this.notExistOptions = validateValue.filter((it) => {
                const existingOption = this.optionList().find((option) =>
                  this.compare(option.value, it),
                );
                if (existingOption && updateLabelExistOption) {
                  existingOption.label = it.label ?? existingOption.label;
                }
                return !existingOption;
              });

              const oldOptionList = structuredClone(this.optionList());

              this.optionList.set([
                ...this.notExistOptions.map((it: NzSafeAny) => it),
                ...oldOptionList,
              ]);

              this.value.set(v.map((it: NzSafeAny) => it.value ?? it));
            }),
            tap(() => this.cdr.detectChanges()),
          )
          .subscribe();
      }

      if (config._allowValues)
        combineLatest({
          values: this.values$,
          optionList: this.subscriptionOptionList$,
        })
          .pipe(
            map(({ values, optionList }) => {
              return {
                fields: values.fields,
                variables: values.variables,
                extend: {
                  ...values.extend,
                  optionList,
                },
              };
            }),
            distinctUntilChanged((prev, curr) => {
              return this.service.distinct(prev, curr, config._allowValues);
            }),
            connect((s) =>
              merge(
                s.pipe(
                  switchMap((values) => {
                    return this.service.getObservable(
                      this.values.function,
                      values,
                      this.config._allowValues,
                    );
                  }),
                  tap((data: Array<NzSafeAny>) => {
                    if (!data || !Array.isArray(data)) return;

                    const useNewValues = this.config.useDataAllowValue;

                    let newValues = this.value();
                    if (useNewValues) {
                      newValues = data;
                    } else {
                      newValues =
                        this.value()?.filter((item: NzSafeAny) => {
                          const valueCheck = this.config.allowValueKey
                            ? item[this.config.allowValueKey]
                            : item;
                          return data.includes(valueCheck);
                        }) || [];
                    }

                    this.value.set(newValues);
                    this.group.get(this.config.name)?.setValue(newValues);
                  }),
                  tap(() => this.cdr.detectChanges()),
                ),
              ),
            ),
          )
          .subscribe();
    }
  }

  setValueData(v: NzSafeAny) {
    let validateValue = [];

    if (isArray(v)) {
      validateValue = v
        .filter((it) => {
          if (it.label && it.value) {
            return true;
          } else if (typeof it === 'string') {
            return true;
          }
          return false;
        })
        .map((it) => {
          if (typeof it === 'string') {
            return { label: it, value: it };
          }
          return it;
        });
    } else validateValue = [];

    this.notExistOptions = validateValue.filter((it) => {
      return !this.optionList()?.find((option) => {
        return this.compare(option.value, it);
      });
    });

    const oldOptionList = structuredClone(this.optionList() ?? []);

    this.optionList.set([
      ...this.notExistOptions.map((it: NzSafeAny) => it),
      ...oldOptionList,
    ]);

    this.value.set(v);
  }
  isEmptyValue(value: any): boolean {
    if (value === null || value === undefined) return true;
    if (Array.isArray(value) && value.length === 0) return true;
    if (typeof value === 'object' && Object.keys(value).length === 0)
      return true;

    // Kiểm tra trường hợp value = { value: {} }
    if (
      typeof value === 'object' &&
      'value' in value &&
      typeof value.value === 'object' &&
      Object.keys(value.value).length === 0
    ) {
      return true;
    }

    return false;
  }
  compare = (o1: NzSafeAny, o2: NzSafeAny) => {
    if (isNil(o1) && isNil(o2)) return true;
    if (isNil(o1) || isNil(o2)) return false;
    const new1 = !isNil(o1.value) ? o1.value : o1;
    const new2 = !isNil(o2.value) ? o2.value : o2;
    const t = isEqual(new1, new2);
    return t;
  };

  tempValueMultiple: NzSafeAny[] = [];
  tempOption: NzSafeAny = {};

  findCurrentOption() {
    const key = this.config.inputValue ?? 'code';
    const tempOptions: NzSafeAny[] = [];
    this.tempValueMultiple.map((v: NzSafeAny) => {
      const option = this.cloneOptionList.find(
        (option) => option.value[key] === v?.[key],
      );
      option && tempOptions.push(option);
    });
    return uniqBy(tempOptions, 'id');
  }

  getOutputValue(option: NzSafeAny) {
    if (this.config.outputValue) {
      return this.service.getValue(option, this.config.outputValue.split('.'));
    }
    return option;
  }

  private isEqualValue = (value1: any, value2: any) => {
    const getRealValue = (value: any) => {
      if (isNil(value) || (value ?? []).length === 0) return null;
      return value;
    };

    return isEqual(getRealValue(value1), getRealValue(value2));
  };

  shouldClearFieldsAfterChange(value: any) {
    return (
      (this.config.clearFieldsAfterChange ?? []).length > 0 &&
      !this.isEqualValue(value, this.value())
    );
  }

  onChange(value: string[]): void {
    if (this.shouldClearFieldsAfterChange(value)) {
      this.clearFields(this.config.clearFieldsAfterChange ?? []);
    }
    this.value.set(value);
    this.cdr.detectChanges();
  }

  get isDisabled() {
    return this.group?.get(this.config?.name)?.disabled ?? false;
  }

  mappingLabel(value: NzSafeAny) {
    if (value?.label) return value.label;
    return this.optionList().find((v: NzSafeAny) => v.value === value)?.label;
  }

  clearFields(fields: string[]) {
    fields.forEach((field) => {
      const control = this.group.get(field);
      if (control) {
        control.setValue(null);
      } else {
        this.formControlService.updateFormControlValue(field, null);
      }
    });
  }
}

interface FieldSelectAllConfig {
  name: string;
  type: string;
  options: OptionAll[];
  placeHolder: string;
  readOnly: boolean;
  value: string[];
  selectAllFullItem: boolean;
  _value: string;
  _options: {
    transform: string;
  };
  settings?: {
    maxTag: number;
    enabledLoadMore?: boolean;
  };
}

interface OptionAll {
  label: string;
  value: string;
  avatar?: string;
  subLabel?: string;
  disabled?: boolean;
  icon?: string;
  checked?: boolean;
}
