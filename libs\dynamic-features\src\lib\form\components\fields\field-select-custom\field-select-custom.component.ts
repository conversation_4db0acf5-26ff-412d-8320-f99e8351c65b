import {
  FieldSelectCustomAction,
  FieldSelectCustomActionConfig,
  SourceField,
} from './../../../models/field-config.interface';
import { CommonModule } from '@angular/common';
import {
  Component,
  computed,
  effect,
  forwardRef,
  OnInit,
  output,
  signal,
  untracked,
  ViewChild,
} from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import {
  ButtonComponent,
  DescriptionsComponent,
  DisplayComponent,
  IconComponent,
  ModalComponent,
  NewTableModule,
  RadioComponent,
  SelectComponent,
  TooltipComponent,
} from '@hrdx/hrdx-design';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzSelectModule } from 'ng-zorro-antd/select';
import {
  Observable,
  combineLatest,
  distinctUntilChanged,
  map,
  of,
  switchMap,
  tap,
} from 'rxjs';
import { FieldSelectCustom } from '../../../models';
import { FieldSelectComponent } from '../field-select/field-select.component';
import { FormComponent } from '../../../form.component';
import { NzSafeAny } from 'ng-zorro-antd/core/types';
import { isArray, isEqual, isNil, isObject, uniqWith } from 'lodash';

type OptionCustomItem = {
  label?:
    | {
        title: string;
        color?: string;
        icon?: string;
      }
    | string;
  value?: any;
  additionalData?: Record<string, any>;
  description?: string;
};

@Component({
  selector: 'dynamic-field-select-custom',
  standalone: true,
  imports: [
    CommonModule,
    NzSelectModule,
    NzInputModule,
    ReactiveFormsModule,
    NzIconModule,
    NzButtonModule,
    NzModalModule,
    DescriptionsComponent,
    ButtonComponent,
    ModalComponent,
    SelectComponent,
    FormsModule,
    forwardRef(() => FormComponent),
    NewTableModule,
    RadioComponent,
    DisplayComponent,
    IconComponent,
    TooltipComponent,
  ],
  templateUrl: './field-select-custom.component.html',
  styleUrl: './field-select-custom.component.less',
})
export class FieldSelectCustomComponent
  extends FieldSelectComponent
  implements OnInit
{
  readonly actionsIcons: Record<string, string> = {
    view: 'icon-article-bold',
    create: 'icon-plus-bold',
    delete: 'icon-trash-bold',
    edit: 'pencil',
    search: 'icon-magnifying-glass-bold',
  } as const;

  override config!: FieldSelectCustom;
  override optionList: OptionCustomItem[] = [];
  override optionList$!: Observable<OptionCustomItem[] | undefined>;

  optionSelected = signal<OptionCustomItem | undefined>(undefined);
  descriptionsTitles = signal<any[]>([]);

  // 'view' | 'add' | 'delete' | 'edit' | 'search'
  selectedAction = signal<string | undefined>(undefined);
  selectedActionConfig = signal<FieldSelectCustomActionConfig | undefined>(
    undefined,
  );
  modalVisible = signal(false);
  @ViewChild('formObj') formObj?: FormComponent;

  selectedOption: string | string[] = '';
  changedOption = output<string | string[]>();

  actions: FieldSelectCustomAction[] = [];
  actionAddOnValue = signal<Record<string, NzSafeAny>>({});

  control = computed(() => this.group.get(this.config.name));
  isArray = isArray;

  isActionDisabled(id: string) {
    switch (id) {
      case 'view': {
        return this.value() ? false : true;
      }
      default: {
        return false;
      }
    }
  }

  temOptionList: OptionCustomItem[] = [];

  ngOnInit() {
    if (Array.isArray(this.config?.actions)) {
      this.actions = [...new Set(this.config.actions)];
    }

    const searchConfig = this.config?.actionsConfig?.['search'];
    if (searchConfig) {
      if (searchConfig.addOnValue) {
        this.actionAddOnValue.set(searchConfig.addOnValue);
      }

      if (searchConfig._addOnValue) {
        this.subscribeValueChange(searchConfig._addOnValue).subscribe(
          (value) => {
            this.actionAddOnValue.set(value);
            this.resetForm.update((prev) => !prev);
          },
        );
      }

      if (searchConfig.options?.autoSubmitSelection) {
        this.rowSelected.set(-1);
      }
    }

    if (this.config.value) {
      this.temOptionList.push(this.config.value);
    }

    if (this.config._detailData) {
      combineLatest({
        values: this.values$,
      })
        .pipe(
          map(({ values }) => ({
            ...values,
            value: this.value(),
          })),
          distinctUntilChanged((prev, curr) =>
            this.service.distinct(prev, curr, this.config._detailData),
          ),

          switchMap((values) =>
            this.service.getObservable(
              values.function,
              values,
              this.config._detailData,
            ),
          ),
          tap((v) => {
            if (v) {
              this.temOptionList = uniqWith(
                [v, ...this.temOptionList],
                (a: NzSafeAny, b: NzSafeAny) => {
                  return isEqual(a?.value, b?.value);
                },
              );
            }
          }),
        )
        .subscribe();
    }

    this.subscriptionOptionList$.subscribe((optionList) => {
      this.temOptionList =
        uniqWith(
          [...(optionList ?? []), ...this.temOptionList],
          (a: NzSafeAny, b: NzSafeAny) => {
            return isEqual(a?.value, b?.value);
          },
        ) ?? [];
    });
  }

  subscribeValueChange(transform: SourceField) {
    return combineLatest({
      _value: this.values$.pipe(
        map((_value) => ({
          ..._value,
          value: this.value(),
        })),
        distinctUntilChanged((prev, curr) => {
          return this.service.distinct(prev, curr, transform);
        }),
        switchMap(() =>
          this.service.getObservable(
            this.values.function,
            this.values,
            transform,
          ),
        ),
      ),
    }).pipe(
      map(({ _value }) => {
        return _value;
      }),
    );
  }

  getOptionSelected(value: NzSafeAny) {
    return this.temOptionList.find((option: NzSafeAny) => {
      const fieldName = this.config.outputValue?.split('.')[1] ?? 'code';
      const valueOption = option?.value[fieldName] ?? option?.value?.id;
      return isObject(option.value) && this.config.outputValue
        ? valueOption === (value?.code ?? value?.id ?? value)
        : option.value === value;
    });
  }

  getDescriptionsTitles(obj?: Record<string, any>) {
    if (!obj) return [];
    return Object.keys(obj).map((key) => ({
      code: key,
      title: this.transformKeyField(key),
    }));
  }

  onOptionClick(o: OptionCustomItem) {
    this.optionSelected.set(o);
    // this.descriptionsTitles.set(
    //   this.getDescriptionsTitles(this.optionSelected()?.additionalData),
    // );
  }

  selectedActionValue = signal<NzSafeAny>(null);
  openModal(action: FieldSelectCustomAction, item?: OptionCustomItem) {
    const actionId = this.getActionId(action);
    if (actionId === 'view') {
      const formValue = item ?? this.group.get(this.config.name)?.value;

      if (!formValue) return;

      this.optionSelected.set(
        this.getOptionSelected(
          typeof formValue === 'object'
            ? (formValue?.code ?? formValue?.id ?? formValue?.value)
            : formValue,
        ),
      );

      if (!this.optionSelected()) return;
      const isDiff =
        this.optionSelected()?.additionalData !== this.selectedActionValue();
      this.selectedActionValue.set(this.optionSelected()?.additionalData ?? {});
      if (isDiff) {
        this.resetForm.update((value) => !value);
      }
      // this.descriptionsTitles.set(
      //   this.getDescriptionsTitles(this.optionSelected()?.additionalData),
      // );
    } else {
      if (actionId !== 'search') {
        this.resetForm.update((value) => !value);
      }
    }
    const actionConfig = this.getActionConfig(actionId);

    this.selectedActionConfig.set(actionConfig);
    this.modalVisible.set(true);
    this.selectedAction.set(actionId);
  }

  modalTitle = computed(() => {
    const action = this.selectedAction();
    if (!action) return 'Modal';
    switch (action) {
      case 'view': {
        return (
          this.optionSelected()?.label?.toString() ??
          this.getActionTitle(action)
        );
      }
      case 'create':
      case 'edit':
      case 'search': {
        return this.getActionTitle(action);
      }
      default:
        return 'Modal';
    }
  });

  modalSize = computed(() => {
    const action = this.selectedAction();
    if (action === 'view') {
      return 'small';
    }
    return 'middle';
  });

  actionModalButtons = computed(() => {
    const config = this.selectedActionConfig();
    const action = this.selectedAction();
    if (!config) return {};
    const { cancelButton, okButton, searchButton, clearSearchButton } =
      config.options ?? {};
    let buttons = [];
    switch (action) {
      case 'search': {
        buttons = [
          {
            id: 'cancel',
            label: cancelButton?.label ?? 'Close',
            display: cancelButton?.display ?? true,
            type: cancelButton?.type ?? 'tertiary',
            size: cancelButton?.size ?? 'default',
          },
          {
            id: 'ok',
            label: okButton?.label ?? 'Select',
            display: okButton?.display ?? true,
            type: okButton?.type ?? 'primary',
            size: okButton?.size ?? 'default',
          },
          {
            id: 'search',
            label: searchButton?.label ?? 'Search',
            type: searchButton?.type ?? 'secondary',
            display: searchButton?.display ?? true,
            size: searchButton?.size ?? 'default',
          },
          {
            id: 'clearSearch',
            label: clearSearchButton?.label ?? 'Clear',
            type: clearSearchButton?.type ?? 'tertiary',
            display: clearSearchButton?.display ?? true,
            size: clearSearchButton?.size ?? 'default',
          },
        ];
        break;
      }
      default: {
        buttons = [
          {
            id: 'cancel',
            label: 'Close',
            display: true,
            type: 'tertiary',
            size: 'default',
          },
        ];
      }
    }

    return buttons
      .filter((button) => button.display)
      .reduce((acc: Record<string, NzSafeAny>, button) => {
        acc[button.id] = button;
        return acc;
      }, {});
  });

  getActionTitle(actionId: string) {
    const action = this.getAction(actionId);
    if (!action) return 'Modal';
    if (typeof action == 'object') return action.title;
    return action;
  }

  selectedActionFormConfig = computed(() => {
    return this.selectedActionConfig()?.formConfig;
  });

  readonlyButtons = computed(() => {
    const allowedButtons = ['view'];
    return this.actions.filter((action) =>
      allowedButtons.includes(this.getActionId(action)),
    );
  });

  resetForm = signal(false);

  closeModal() {
    this.modalVisible.set(false);
  }

  async onModalSave() {
    const actionId = this.selectedAction();
    switch (actionId) {
      case 'search': {
        // const value = this.listOfSelectedItemsChild()?.[0];
        // if (!value) return;
        // this.value = this.getOutputValue(value);
        // this.group.get(this.config.name)?.setValue(this.value);
        const value = this.getOutputValue(this.optionList[this.rowSelected()]);
        if (!value) return;
        this._value.set(value);
        this.group.get(this.config.name)?.setValue(value);
        // this.onOptionClick(value);
        this.closeModal();
        break;
      }
      case 'create': {
        await this.updateFormControl();
        this.closeModal();
        break;
      }
      default: {
        this.closeModal();
      }
    }
  }

  async transformFields() {
    const formValue = this.formObj?.value;
    if (!formValue) return;
    const actionConfig = this.getActionConfig(this.selectedAction());
    const transformFields = actionConfig?.transformFields;
    if (!transformFields) return;
    let { value, label } = transformFields;
    const expressionFunc = await this.service.getJsonataExpression({});
    [value, label] = await Promise.all([
      expressionFunc(value.transform, formValue ?? {}),
      expressionFunc(label.transform, formValue ?? {}),
    ]);
    return { value, label } as Record<string, unknown>;
  }

  getActionId(action: FieldSelectCustomAction) {
    if (typeof action == 'object') return action.id;
    return action;
  }

  getAction(actionId: string) {
    return this.actions.find((action) => {
      if (typeof action === 'object') return action.id === actionId;
      return action === actionId;
    });
  }

  getActionConfig(actionId?: string) {
    if (!actionId) return;
    const actionsConfig = this.config?.actionsConfig;
    let actionConfig = actionsConfig?.[actionId];
    if (actionConfig?.extendFrom) {
      actionConfig = actionsConfig[actionConfig.extendFrom];
    }

    return actionConfig;
  }

  async updateFormControl() {
    const formControl = this.group.get(this.config.name);
    if (!formControl) return;
    const newOption = await this.transformFields();
    if (newOption) {
      this.optionList = [...this.optionList, newOption];
    }
    formControl.patchValue(newOption);
  }

  // helper method
  transformKeyField(key: string) {
    if (!key) return key;
    return key
      .split(/(?=[A-Z])/)
      .map((word, idx) => {
        if (idx === 0) {
          return word[0].toUpperCase() + word.slice(1);
        }
        return word[0].toLowerCase() + word.slice(1);
      })
      .join(' ');
  }

  override keydown(e: KeyboardEvent) {
    if (e.code === 'Enter') {
      const value = this.searchChange$.value;
      const isFound = this.optionList.find((option) => {
        if (typeof option.label === 'string') {
          return option.label.includes(value);
        } else {
          return option.label?.title.includes(value);
        }
      });
      if (!isFound) {
        // call sources update optionList
        if (this.config._updateOption) {
          combineLatest({
            _updateOption: of(this.values).pipe(
              distinctUntilChanged((prev, curr) => {
                return this.service.distinct(
                  prev,
                  curr,
                  this.config._updateOption,
                );
              }),
              switchMap(() =>
                this.service.getObservable(
                  this.values.function,
                  {
                    ...this.values,
                    ...{
                      extend: {
                        search: this.searchChange$.value,
                      },
                    },
                  },
                  this.config._updateOption,
                ),
              ),
            ),
          })
            .pipe(
              map(({ _updateOption }) => {
                return _updateOption;
              }),
            )
            .subscribe((v) => {
              if (this.mode === 'default')
                this.optionList = (v ? [v] : []).map((it: NzSafeAny) => {
                  return it;
                });
              else
                this.optionList = (v ?? []).map((it: NzSafeAny) => {
                  return it;
                });
            });
        }
      }
    }
  }

  selectConfigs = signal<any[]>([]);

  transformSearchObj(obj: Record<string, NzSafeAny>) {
    return Object.keys(obj).reduce((acc: Record<string, NzSafeAny>, key) => {
      if (isNil(obj[key])) return acc;
      acc[key] = obj[key];
      return acc;
    }, {});
  }

  onValueChange(value: NzSafeAny) {
    if (this.selectedAction() !== 'search') return;
    if (typeof value === 'object') {
      const formValue = value?.value ?? {};
      const transformObj = this.transformSearchObj(formValue);
      this.onFilter(transformObj);
    } else {
      this.onFilter(value);
    }
  }

  filter(value: Record<string, NzSafeAny>) {
    const transformObj = this.transformSearchObj(value);
    this.onFilter(transformObj);
  }

  clearSearch() {
    this.resetForm.update((prev) => !prev);
    this.filter({});
  }

  rowSelected = signal<number>(0);

  haveSearchAction = computed(() =>
    this.actions?.some((action) => {
      if (typeof action === 'object') return action.id === 'search';
      return action === 'search';
    }),
  );

  valueEffect = effect(
    () => {
      if (!this.haveSearchAction()) return;
      const value = this.value();

      if (!value) return;

      const idxSelected = this.optionList.findIndex(
        (option) => option === value || option.value === value,
      );

      untracked(() => {
        if (idxSelected >= 0 && idxSelected !== this.rowSelected()) {
          this.rowSelected.set(idxSelected);
        }
      });
    },
    { allowSignalWrites: true },
  );

  onRowSelected(idx: number) {
    this.rowSelected.set(idx);
    const actionConfig = this.selectedActionConfig();
    if (!actionConfig?.options?.autoSubmitSelection) return;
    this.onModalSave();
  }

  getCellValue(item: Record<string, NzSafeAny>, field: string) {
    const additionalData = item?.['additionalData'];
    if (additionalData) return additionalData[field];
    return item?.['value']?.[field];
  }

  override loadOptionsOnModalVisible = effect(() => {
    if (this.subscription || !this.config.isLazyLoad || !this.modalVisible())
      return;

    this.subscription = this.optionList$.subscribe();
  });
}
