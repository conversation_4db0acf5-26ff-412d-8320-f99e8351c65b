import { CommonModule } from '@angular/common';
import {
  AfterViewInit,
  Component,
  ElementRef,
  Input,
  OnChanges,
  SimpleChanges,
  SkipSelf,
  TemplateRef,
  ViewChild,
  ViewEncapsulation,
  computed,
  effect,
  forwardRef,
  inject,
  signal,
  OnDestroy,
  viewChild,
  ChangeDetectorRef,
} from '@angular/core';
import { toObservable } from '@angular/core/rxjs-interop';
import { FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import {
  ButtonComponent,
  CalendarComponent,
  DataRenderComponent,
  IconComponent,
  ModalComponent,
  NewTableModule,
  RadioComponent,
  SelectMode,
  ToastMessageComponent,
  TooltipComponent,
} from '@hrdx/hrdx-design';
import * as _ from 'lodash';
import {
  concat,
  differenceWith,
  find,
  includes,
  isArray,
  isEmpty,
  isEqual,
  isNil,
  isObject,
  uniq,
  uniqBy,
  every,
} from 'lodash';
import * as moment from 'moment';
import { NzSafeAny } from 'ng-zorro-antd/core/types';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzSelectComponent, NzSelectModule } from 'ng-zorro-antd/select';
import {
  BehaviorSubject,
  Observable,
  Subject,
  Subscription,
  catchError,
  combineLatest,
  connect,
  debounceTime,
  distinctUntilChanged,
  filter,
  finalize,
  map,
  merge,
  of,
  skip,
  switchMap,
  take,
  takeUntil,
  tap,
} from 'rxjs';
import { FormComponent } from '../../../form.component';
import {
  FieldGroupConfig,
  FieldSelect,
  SourceField,
} from '../../../models/field-config.interface';
import { Field, Values } from '../../../models/field.interface';
import { DynamicFormService } from '../../../services/dynamic-form.service';
import { FormControlService } from '../../../services/form-control.service';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
type OptionItem = {
  label?:
    | {
        title: string;
        color?: string;
        icon?: string;
      }
    | string;
  value?: NzSafeAny;
  disabled?: boolean;
  description?: string;
};
@Component({
  selector: 'dynamic-field-select',
  standalone: true,
  imports: [
    CommonModule,
    IconComponent,
    NzSelectModule,
    NzInputModule,
    ReactiveFormsModule,
    NzIconModule,
    NzDividerModule,
    ModalComponent,
    ButtonComponent,
    FormsModule,
    NewTableModule,
    CalendarComponent,
    IconComponent,
    forwardRef(() => FormComponent),
    NzToolTipModule,
    TooltipComponent,
    DataRenderComponent,
    RadioComponent,
  ],
  providers: [ModalComponent],
  templateUrl: './field-select.component.html',
  styleUrls: ['./field-select.component.less'],
  encapsulation: ViewEncapsulation.None,
})
export class FieldSelectComponent
  implements Field, OnChanges, AfterViewInit, OnDestroy
{
  config!: FieldSelect;
  group!: FormGroup;
  isAddOn!: boolean;

  @ViewChild('modalContent') modalContent!: TemplateRef<''>;
  modalComponent = inject(ModalComponent);
  toast = inject(ToastMessageComponent);
  listOfSelectedItems = signal<NzSafeAny[]>([]);
  listOfSelectedItemsChild = signal<NzSafeAny[]>([]);

  @Input() values: Values = {};
  isActionBtn = false;
  Infinity = Infinity;
  service = inject(DynamicFormService);
  searchChange$ = new BehaviorSubject('');
  _mode = SelectMode.Multiple;
  loading = false;
  _value = signal<NzSafeAny>(undefined);
  optionList: OptionItem[] = [];
  extendSelectOptionList = signal<OptionItem[]>([]);
  notExistOptions: OptionItem[] = [];
  optionList$?: Observable<OptionItem[] | undefined>;
  ids$ = new BehaviorSubject<string[]>([]);
  subcription?: Subscription;
  isServerSearch = false;
  el = inject(ElementRef);
  value$ = new BehaviorSubject<NzSafeAny>(undefined);
  defaultValue$ = new BehaviorSubject<NzSafeAny>(undefined);
  defaultValue = signal<NzSafeAny>(undefined);
  searchText$ = this.searchChange$
    .asObservable()
    .pipe(
      connect((s) =>
        merge(
          s.pipe(take(1)),
          s.pipe(skip(1), debounceTime(300), distinctUntilChanged()),
        ),
      ),
    );
  filter$ = new BehaviorSubject<{ [k: string]: NzSafeAny }>({});
  optionListParent: NzSafeAny[] = [];
  maxTag?: number;

  private readonly destroy$ = new Subject<void>();

  private readonly minTagCount = 1 as const;
  constructor(
    @SkipSelf() private formControlService: FormControlService,
    private cdr: ChangeDetectorRef,
  ) {}

  value = computed(() => {
    if (this.mode === 'default') {
      return this._value() ?? this.defaultValue();
    }
    if (isArray(this._value()) && isArray(this.defaultValue())) {
      return uniq([...this.defaultValue(), ...this._value()]);
    }
    if (isArray(this._value())) {
      return this._value();
    }
    return this.defaultValue();
  });
  optionListView: NzSafeAny[] = [];
  valueChecked = effect(() => {
    const itemChecked = this.listOfSelectedItems().map((item) => item.value.id);
    if (this.isCheckboxTable && !this.config.showCreateDataTable) {
      this.group.get(this.config.name)?.setValue(itemChecked);
    }
  });

  onSearch(value: string): void {
    this.page.set(1);
    this.searchChange$.next(value);
  }

  tempFilterValue = signal<{ [k: string]: NzSafeAny }>({});
  isValidFilterValue = computed(() => {
    const filterValue = this.tempFilterValue();
    if (filterValue === null || filterValue === undefined) {
      return false;
    }

    if (Array.isArray(filterValue) && filterValue.length === 0) {
      return false;
    }

    if (typeof filterValue === 'object' && filterValue !== null) {
      if (Object.keys(filterValue).length === 0) {
        return false;
      }
      const hasNonEmptyValues = Object.values(filterValue).some(
        (value) =>
          (value !== null &&
            value !== '' &&
            value !== undefined &&
            !Array.isArray(value)) || // Handle string values
          (Array.isArray(value) && value.length > 0),
      );
      return hasNonEmptyValues;
    }

    return true;
  });

  onFilter(value: { [k: string]: NzSafeAny }) {
    this.filterVisible.set(false);
    this.tempFilterValue.set(value);
    this.filter$.next(value);
  }

  removedFilterItem = (event: NzSafeAny) => {
    this.tempFilterValue.set(event);
    this.filter$.next(event);
  };

  filterDataRenderValue = computed(() => {
    const filterValue = structuredClone(this.tempFilterValue());
    return filterValue;
  });

  filterConfigMapping = computed(() => {
    const fields = this.config.selectSetting?.filter?.fields as any;
    if (!fields) return {};
    const variables = {};
    const variablesMapping = this.filterOptionsLoadFromVariables();
    return mappingConfig(fields, variables, variablesMapping);
  });

  filterOptionsLoadFromVariables = computed(() => {
    const fields = this.config.selectSetting?.filter?.fields;
    if (!fields) return {};
    return fields.reduce((acc: Record<string, string>, field: NzSafeAny) => {
      const _radioTransform = field?._radio?.transform;
      if (_radioTransform) {
        acc[field.name] = _radioTransform.split('.').at(-1);
      }
      return acc;
    }, {});
  });

  values$ = new BehaviorSubject<Values>({});
  subscriptionOptionList$ = new BehaviorSubject<OptionItem[] | undefined>(
    undefined,
  );
  SelectMode = SelectMode;
  mode: SelectMode = SelectMode.Default;
  prevValue = signal<string | undefined>(undefined);

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['values']) {
      if (this.config.confirmPopup) {
        this.formControlService.setConfirm(false);
      }
      this.values$.next(this.values);
    }
  }

  keydown(e: KeyboardEvent) {
    if (e.code === 'Enter') {
      const value = this.searchChange$.value;
      const isFound = this.optionList.find((option) => {
        if (typeof option.label === 'string') {
          return option.label.includes(value);
        } else {
          return option.label?.title.includes(value);
        }
      });
      if (!isFound) {
        // call sources update optionList
        if (this.config._updateOption) {
          combineLatest({
            _updateOption: of(this.values).pipe(
              tap(() => this.cdr.detectChanges()),
              distinctUntilChanged((prev, curr) => {
                return this.service.distinct(
                  prev,
                  curr,
                  this.config._updateOption,
                );
              }),
              switchMap(() =>
                this.service.getObservable(
                  this.values.function,
                  {
                    ...this.values,
                    ...{
                      extend: {
                        search: this.searchChange$.value,
                      },
                    },
                  },
                  this.config._updateOption,
                ),
              ),
            ),
          })
            .pipe(
              tap(() => this.cdr.detectChanges()),
              map(({ _updateOption }) => {
                return _updateOption;
              }),
            )
            .subscribe((v) => {
              if (this.mode === 'default')
                this.optionList = (v ? [v] : []).map((it: NzSafeAny) => {
                  // const label = it.label;
                  // return {
                  //   label: label,
                  //   value: it.value,
                  //   disabled: it.disabled ?? false,
                  // };
                  return it;
                });
              else
                this.optionList = (v ?? []).map((it: NzSafeAny) => {
                  // const label = it.label;
                  // return {
                  //   label: label,
                  //   value: it.value,
                  //   disabled: it.disabled ?? false,
                  // };
                  return it;
                });
            });
        }
      }
    }
  }
  isCheckboxTable = false;
  total = 0;

  prevSearch = '';

  setMode(config: FieldSelect) {
    switch (config.mode) {
      case 'multiple':
        this.mode = SelectMode.Multiple;
        break;
      case 'tags':
        this.mode = SelectMode.Tags;
        break;
      default:
        this.mode = SelectMode.Default;
    }
  }
  listLoading = signal<boolean>(false);

  enabledLoadMore = true;

  subscriptionSetValueForm!: Subscription;
  clearedByUserClick = false;

  ngAfterViewInit() {
    const config = this.config;
    this.maxTag = config.options?.maxTag ?? 1;
    this.enabledLoadMore = config.options?.enabledLoadMore ?? true;

    //lắng nghe thay đổi value từ service handleAfterChange
    this.subscriptionSetValueForm =
      this.formControlService.setValueSubject$.subscribe((value: NzSafeAny) => {
        let currentPath = structuredClone(this.values.extend?.['path']);
        currentPath?.pop();
        currentPath = currentPath?.join('.');
        const checkPath = value.path ? value.path === currentPath : true;
        if (!isNil(value) && value.key === config.name && checkPath) {
          this.setValueData(value.value);
        }
      });

    this.group.get(config.name)?.valueChanges.subscribe((value) => {
      this.selectExtendValue.set(value);

      if (isNil(value)) {
        this.defaultValue.set(null);
        this._value.set(null);
      } else if (value === '_setSelectValueNull') {
        this._value.set(null);
        this.group.get(this.config.name)?.setValue(null);
      }
    });
    if (this.config.confirmPopup) this.updateConfirmStatus(false);
    if (config.isCheckboxTable) {
      this.isCheckboxTable = config.isCheckboxTable;
    }
    this.setMode(config);
    if (config._select) {
      this.isServerSearch =
        config._select?.transform.includes('$.extend.search') ?? false;
    }
    const _total = config.selectSetting?._total;
    if (config.selectSetting?.total) {
      const value = config.selectSetting?.total;
      if (isNil(value)) return;
      if (typeof value !== 'number') return;
      this.total = value;
    } else if (_total) {
      combineLatest({
        total: this.values$.pipe(
          distinctUntilChanged((prev, curr) => {
            return this.service.distinct(prev, curr, _total);
          }),
          switchMap(() =>
            this.service.getObservable(
              this.values.function,
              this.values,
              _total,
            ),
          ),
          tap(() => this.cdr.detectChanges()),
        ),
      })
        .pipe(
          map(({ total }) => {
            return total;
          }),
          tap(() => this.cdr.detectChanges()),
        )
        .subscribe((value) => {
          if (isNil(value)) return;
          if (typeof value !== 'number') return;
          this.total = value;
        });
    }

    if (this.isObjectEmptyValue(config.value)) {
      this._value.set(null);
      this.group.get(config.name)?.setValue(null);
    } else {
      this.value$.next(config.value);
    }

    if (config._value)
      combineLatest({
        _value: this.values$.pipe(
          distinctUntilChanged((prev, curr) => {
            return this.service.distinct(prev, curr, config._value);
          }),
          filter((values) => {
            if (config._value?.skipWhenClear && this.clearedByUserClick) {
              this.clearedByUserClick = false;
              return false;
            }
            return true;
          }),
          switchMap(() =>
            this.service.getObservable(
              this.values.function,
              this.values,
              this.config._value,
            ),
          ),
          finalize(() => this.cdr.detectChanges()),
        ),
      })
        .pipe(
          map(({ _value }) => {
            return _value;
          }),
          finalize(() => this.cdr.detectChanges()),
        )
        .subscribe((value) => {
          // if (!isNil(value)) {
          if (config.isRecommend && !isNil(this.value())) return;
          if (value === '_setNull') this.value$.next(null);
          this.value$.next(value);
          // }
        });
    if (config._defaultValue)
      combineLatest({
        _value: this.values$.pipe(
          distinctUntilChanged((prev, curr) => {
            return this.service.distinct(prev, curr, config._defaultValue);
          }),
          switchMap(() =>
            this.service.getObservable(
              this.values.function,
              this.values,
              this.config._defaultValue,
            ),
          ),
          tap(() => this.cdr.detectChanges()),
        ),
      })
        .pipe(
          map(({ _value }) => {
            return _value;
          }),
          tap(() => this.cdr.detectChanges()),
        )
        .subscribe((value) => {
          if (config.isRecommend && !isNil(this.value())) return;
          this.defaultValue$.next(value);
        });
    this.value$
      .pipe(
        tap((v) => {
          if (isNil(v)) return;
          if (v === '_setSelectValueNull') {
            this._value.set(null);
            this.group.get(this.config.name)?.setValue(null);
            return;
          }
          // if (isNil(v)) {
          //   this.value = null;
          //   this.group.get(config.name)?.setValue(null);
          //   return;
          // }
          this.setMode(config);
          // const isExistLis = this.optionList.find((option) => {
          //   if (this.mode === 'default') return this.compare(option.value, v);
          //   if (isArray(v))
          //     return v.find((it: NzSafeAny) => this.compare(option.value, it));
          //   return true;
          // })
          //   ? true
          //   : false;

          this.setValueData(v);

          // this._value.set(v)
        }),
        tap(() => this.cdr.detectChanges()),
      )
      .subscribe();
    this.defaultValue$
      .pipe(
        tap((v) => {
          if (isNil(v)) return;
          // if (isNil(v)) {
          //   this.value = null;
          //   this.group.get(config.name)?.setValue(null);
          //   return;
          // }
          this.setMode(config);
          // const isExistLis = this.optionList.find((option) => {
          //   if (this.mode === 'default') return this.compare(option.value, v);
          //   if (isArray(v))
          //     return v.find((it: NzSafeAny) => this.compare(option.value, it));
          //   return true;
          // })
          //   ? true
          //   : false;
          let validateValue = [];
          if (this.mode === 'default') {
            if (v.label && v.value) {
              validateValue = [v];
            } else {
              validateValue = [];
            }
          }
          if (this.mode === 'multiple' || this.mode === 'tags') {
            if (isArray(v)) {
              validateValue = v.filter((it) => {
                if (it.label && it.value) {
                  return true;
                }
                return false;
              });
            } else validateValue = [];
          }
          this.notExistOptions = validateValue.filter((it) => {
            return !this.optionList.find((option) => {
              return this.compare(option.value, it);
            });
          });
          const oldOptionList = structuredClone(this.optionList);

          this.optionList = [
            ...this.notExistOptions.map((it: NzSafeAny) => it),
            ...oldOptionList,
          ];

          if (
            this.config.outputValue &&
            isObject(v) &&
            this.config.isLazyLoad
          ) {
            this.defaultValue.set(this.getOutputValue(v));
          } else {
            this.defaultValue.set(v);
          }

          const defaultValue = this.defaultValue();
          const value = this._value();
          if (
            (this.mode === 'multiple' || this.mode === 'tags') &&
            isArray(value) &&
            isArray(defaultValue)
          ) {
            const v1 = value.filter(
              (i) => !defaultValue.find((j) => this.compare(i, j)),
            );
            this._value.set(v1);
          }
        }),
        tap(() => this.cdr.detectChanges()),
      )
      .subscribe();

    if (this.config?.select) {
      this.optionList = Array.from(this.config?.select || []).map((it) => {
        return it;
      });
      this.subscriptionOptionList$.next(this.optionList);
    }

    if (config._select) {
      this.optionList$ = combineLatest({
        values: this.values$,
        search: this.searchText$,
        filter: this.filter$,
        page: this.page$,
        pageSize: this.pageSize$,
      }).pipe(
        map(({ values, search, filter, page, pageSize }) => ({
          fields: values.fields,
          variables: values.variables,
          extend: {
            ...values.extend,
            search: search?.trim(),
            filter: filter,
            limit: pageSize,
            page: page,
          },
        })),
        distinctUntilChanged((prev, curr) => {
          return this.service.distinct(prev, curr, config._select);
        }),
        switchMap((values) => {
          const isPaging =
            values.extend.page > 1 && values.extend.search === this.prevSearch;
          if (values.extend.page === this.oldPage) {
            this.stopPaging = false; // Reset stopPaging when search term changes
            this.page.set(1); // Reset page to 1 for new search
            this.oldPage = 0;
            values.extend.page = 1;
          }
          this.prevSearch = values.extend.search;
          this.oldPage = values.extend.page;
          if (isPaging) {
            return this.handlePaging(values);
          } else {
            return this.handleNotPaging(values);
          }
        }),
        tap(() => this.cdr.detectChanges()),
      );
    }
    if (!config.isLazyLoad) this.subcription = this.optionList$?.subscribe();

    if (config._allowValues)
      combineLatest({
        values: this.values$,
        optionList: this.subscriptionOptionList$,
      })
        .pipe(
          map(({ values, optionList }) => {
            return {
              fields: values.fields,
              variables: values.variables,
              extend: {
                ...values.extend,
                optionList,
              },
            };
          }),
          distinctUntilChanged((prev, curr) => {
            return this.service.distinct(prev, curr, config._allowValues);
          }),
          takeUntil(this.destroy$),
          switchMap((values) => {
            return this.service.getObservable(
              this.values.function,
              values,
              this.config._allowValues,
            );
          }),
          tap(() => this.cdr.detectChanges()),
          tap((data: Array<NzSafeAny>) => {
            const formValue = this.group.get(this.config.name)?.value;
            const value = isNil(this.value()) ? formValue : this.value();

            if (!data || !Array.isArray(data)) return;
            if (
              (this.mode === 'multiple' || this.mode === 'tags') &&
              isArray(value)
            ) {
              const newValues = value.filter((item: NzSafeAny) => {
                const valueCheck = this.config.allowValueKey
                  ? item[this.config.allowValueKey]
                  : item;
                return data.includes(valueCheck);
              });
              this._value.set(newValues);
              this.group.get(this.config.name)?.setValue(newValues);
            } else if (value) {
              const valueCheck = this.config.allowValueKey
                ? value[this.config.allowValueKey]
                : value;

              if (!data.includes(valueCheck)) {
                this._value.set(null);
                this.group.get(this.config.name)?.setValue(null);
              }
            }
          }),
          tap(() => this.cdr.detectChanges()),
        )
        .subscribe();

    if (this.config.dependantOptionList) {
      combineLatest({
        values: this.values$,
      })
        .pipe(
          map(({ values }) => values),
          distinctUntilChanged((prev, curr) =>
            this.service.distinct(prev, curr, {
              transform: this.config.dependantOptionList || '',
            }),
          ),
          skip(2),
          tap((value) => {
            if (value) {
              this.page.set(1);
              this.stopPaging = false;
              this.optionList = [];
              this.optionListView = this.filterOptions(this.optionList);
              this.isCheckedItem() && this.checkedItem();
              this.subscriptionOptionList$.next(this.optionList);
            }
          }),
          tap(() => this.cdr.detectChanges()),
        )
        .subscribe();
    }

    if (this.config._validateFn) {
      combineLatest({
        values: this.values$,
      })
        .pipe(
          map(({ values }) => ({
            ...values,
            value: this.value(),
          })),
          filter((values) => {
            if (isNil(values.value)) {
              this.cacheValidateValue = undefined;
            }
            return values.value;
          }),
          distinctUntilChanged((prev, curr) =>
            this.service.distinct(prev, curr, this.config._validateFn),
          ),
          tap(() => (this.cacheValidateValue = undefined)),
          takeUntil(this.destroy$),
          switchMap((values) =>
            this.service.getObservable(
              values.function,
              values,
              this.config._validateFn,
            ),
          ),
          tap((v) => {
            // console.log('validate', v);
            if (isNil(v)) return;
            if (v === '_setSelectValueNull') {
              this._value.set(null);
              this.group.get(this.config.name)?.setValue(null);
              return;
            }
            this.setMode(config);
            let validateValue = [];
            if (this.mode === 'default') {
              if (v.label && v.value) {
                validateValue = [v];
              } else if (typeof v === 'string') {
                validateValue = [{ label: v, value: v }];
              } else {
                validateValue = [];
              }
            }
            if (this.mode === 'multiple' || this.mode === 'tags') {
              if (isArray(v)) {
                validateValue = v
                  .filter((it) => {
                    if (it.label && it.value) {
                      return true;
                    } else if (typeof it === 'string') {
                      return true;
                    }
                    return false;
                  })
                  .map((it) => {
                    if (typeof it === 'string') {
                      return { label: it, value: it };
                    }
                    return it;
                  });
              } else validateValue = [];
            }

            // this.notExistOptions = validateValue.filter((it) => {
            //   return !this.optionList.find((option) => {
            //     return this.compare(option.value, it);
            //   });
            // });
            this.cacheValidateValue = validateValue;

            const updateLabelExistOption =
              this.config._validateFn?.params?.['updateLabelExistOption'];

            this.notExistOptions = validateValue.filter((it) => {
              const existingOption = this.optionList.find((option) =>
                this.compare(option.value, it),
              );
              if (existingOption && updateLabelExistOption) {
                existingOption.label = it.label ?? existingOption.label;
              }
              return !existingOption;
            });

            const oldOptionList = structuredClone(this.optionList);

            this.optionList = [
              ...this.notExistOptions.map((it: NzSafeAny) => it),
              ...oldOptionList,
            ];
            if (
              this.config.outputValue &&
              isObject(v) &&
              this.config.isLazyLoad
            ) {
              this._value.set(this.getOutputValue(v));
            } else if (this.config.outputValue && isObject(v)) {
              this._value.set(this.getOutputValue(v));
            } else this._value.set(v);
          }),
          finalize(() => this.cdr.detectChanges()),
        )
        .subscribe();
    }

    config.showCreateDataTable &&
      config.readOnly &&
      this.group.get(this.config.name)?.setValue(this.optionList);

    if (this.config.selectSetting) {
      const checkValueSameSearchKey = (
        item: OptionItem,
        searchText?: string,
      ) => {
        if (!searchText) return true;
        const searchKey = searchText.toLowerCase();

        // Xử lý label
        let labelText = '';
        if (typeof item.label === 'string') {
          labelText = item.label.toLowerCase();
        } else if (item.label?.title) {
          labelText = item.label.title.toLowerCase();
        }

        // Xử lý value
        const valueText =
          item.value !== undefined && item.value !== null
            ? String(item.value).toLowerCase()
            : '';

        // Kiểm tra nếu label hoặc value khớp với từ khóa tìm kiếm
        return labelText.includes(searchKey) || valueText.includes(searchKey);
      };
      combineLatest({
        searchText: this.searchText$,
        optionList: this.subscriptionOptionList$,
        filterValue: this.filter$,
      })
        .pipe(takeUntil(this.destroy$))
        .subscribe(({ searchText, optionList = [], filterValue }) => {
          let newOptionList = structuredClone(optionList);
          if (!this.config.isLazyLoad) {
            if (!this.isObjectDeepEmpty(filterValue) || searchText) {
              const searchEntries = Object.entries(filterValue).filter(
                ([, val]) => val?.toLowerCase(),
              );
              const checkSearchEntries = (item: any) => {
                if (searchEntries.length > 0) {
                  return searchEntries.every(([key, val]) => {
                    const searchKey = val.toLowerCase();
                    return String(item[key] ?? '')
                      .toLowerCase()
                      .includes(searchKey);
                  });
                }
                return true;
              };
              newOptionList = newOptionList.filter((item: any) => {
                return (
                  checkValueSameSearchKey(item, searchText) &&
                  checkSearchEntries(item)
                );
              });
            }
          }

          this.extendSelectOptionList.set(newOptionList);
        });
    }
  }

  cacheValidateValue: OptionItem[] | undefined = undefined;

  isObjectDeepEmpty = (obj: Record<string, NzSafeAny>) => {
    // Kiểm tra nếu object rỗng hoặc tất cả các giá trị của nó là `undefined`
    return (
      isEmpty(obj) || every(obj, (value) => isNil(value) || isEmpty(value))
    );
  };

  ngOnDestroy() {
    if (this.subscriptionSetValueForm) {
      this.subscriptionSetValueForm.unsubscribe();
    }

    this.destroy$.next(); // Emit to signal teardown
    this.destroy$.complete(); // Complete the destroy observable
  }

  initOptionFromDefaultValue = false;
  setValueData(v: NzSafeAny) {
    let validateValue = [];
    const config = this.config;
    const defaultValue = this.values.extend?.['defaultValue'];
    if (this.mode === 'default') {
      if (
        config.initOptionFromDefaultValue &&
        !this.initOptionFromDefaultValue &&
        defaultValue &&
        !isNil(v)
      ) {
        const { label = '', value = '' } = config.initOptionFromDefaultValue;
        validateValue = [
          {
            label: this.service.compileText(label, defaultValue),
            value: this.service.getValue(defaultValue, value?.split('.')),
          },
        ];
        this.initOptionFromDefaultValue = true;
      } else if (v?.label && v?.value) {
        validateValue = [v];
      } else if (v?.value) {
        validateValue = [{ label: v?.value, value: v?.value }];
      } else if (typeof v === 'string') {
        validateValue = [{ label: v, value: v }];
      } else {
        validateValue = [];
      }
    }
    if (this.mode === 'multiple' || this.mode === 'tags') {
      if (isArray(v)) {
        validateValue = v
          .filter((it) => {
            if (it.label && it.value) {
              return true;
            } else if (typeof it === 'string') {
              return true;
            }
            return false;
          })
          .map((it) => {
            if (typeof it === 'string') {
              return { label: it, value: it };
            }
            return it;
          });
      } else validateValue = [];
    }

    this.notExistOptions = validateValue.filter((it) => {
      return !this.optionList.find((option) => {
        return this.compare(option.value, it);
      });
    });
    const oldOptionList = structuredClone(this.optionList);

    this.optionList = [
      ...this.notExistOptions.map((it: NzSafeAny) => it),
      ...oldOptionList,
    ];
    if (this.config.outputValue && isObject(v) && this.config.isLazyLoad) {
      this._value.set(this.getOutputValue(v));
    } else this._value.set(v);
  }
  getOptionList(): OptionItem[] {
    const formType = this.values.extend?.['formType'] ?? 'create';
    if (this.config.showCreateDataTable && formType === 'create') {
      return this.optionListParent;
    } else if (formType === 'view') {
      return this.optionListView;
    }
    return this.optionList;
  }

  showAddNew = computed(() => {
    return this.config.showAddNew ?? true;
  });

  isCheckedItem = computed(() => {
    return this.config.checkedItem ?? true;
  });

  showCheckbox = computed(() => {
    return this.config.showCheckbox ?? true;
  });

  showActionsMany = computed(() => {
    return this.config?.selectSetting?.layout_option?.show_actions_many ?? true;
  });

  showActionHeader = computed(() => {
    return (
      this.config?.selectSetting?.layout_option?.show_action_header ?? true
    );
  });

  compare = (o1: NzSafeAny, o2: NzSafeAny) => {
    if (isNil(o1) && isNil(o2)) return true;
    if (isNil(o1) || isNil(o2)) return false;
    const new1 = !isNil(o1.value) ? o1.value : o1;
    const new2 = !isNil(o2.value) ? o2.value : o2;
    const t = isEqual(new1, new2);
    return t;
  };

  getLabel(
    label:
      | string
      | { title: string; color?: string; icon?: string }
      | undefined,
  ) {
    if (typeof label === 'string') return label;
    return label?.title ?? '';
  }

  tempValue: NzSafeAny = {};
  tempOption: NzSafeAny = {};
  findCurrentOption() {
    const key = this.config.inputValue ?? 'code';
    return this.optionList.find(
      (option) => option.value[key] === this.tempValue?.[key],
    );
  }
  getOutputValue(option: NzSafeAny) {
    if (this.config.outputValue) {
      return this.service.getValue(option, this.config.outputValue.split('.'));
    }
    return option;
  }
  getDisabled(status: boolean, value: NzSafeAny) {
    const _value_all = this.config?.valueAll;
    if (this.mode !== 'multiple' || !_value_all) {
      return status;
    }

    const currentValue = this.value();
    if (!currentValue || currentValue.length === 0) {
      return status;
    }

    const firstValue = isObject(currentValue[0])
      ? (currentValue[0] as { value: NzSafeAny }).value
      : currentValue[0];
    const targetValue = isObject(value)
      ? (value as { value: NzSafeAny }).value
      : value;

    const isAllSelected = firstValue === _value_all;
    const isTargetAll = targetValue === _value_all;

    return (isAllSelected && !isTargetAll) || (!isAllSelected && isTargetAll);
  }

  mappingLabel(value: NzSafeAny) {
    const _value = value?.id ?? value;
    if (value?.label) return value.label;
    if (this.config.inputValue) return this.getLabelByValue(_value)?.label;
    return this.optionList.find((v: NzSafeAny) => v.value === _value)?.label;
  }

  getLabelProperty(label: NzSafeAny, key: string): string {
    return label?.[key] ?? '';
  }

  getLabelByValue(v: string) {
    return this.optionList.find(
      (option) =>
        option.value[this.config?.outputValue?.split('.')[1] ?? 'id'] === v,
    );
  }

  getLabelObjByValue(v: string) {
    const option = this.optionList.find(
      (option) =>
        option.value[this.config?.outputValue?.split('.')[1] ?? 'id'] ??
        option.value === v,
    );

    return isObject(option?.label) ? option?.label : undefined;
  }

  actionButton() {
    this.isActionBtn = true;
  }

  onCancel(isCancel?: boolean) {
    if (isCancel) {
      this.isActionBtn = false;
      return;
    }
    this.modalComponent.showDialog({
      nzTitle: 'Cancel creation',
      nzContent: this.modalContent,
      nzWrapClassName: 'popup popup-confirm hide-footer-btns',
      nzIconType: 'icons:file-arrow-down',
      nzOkText: null,
      nzCancelText: null,
      nzFooter: null,
    });
  }

  onSave() {
    this.isActionBtn = false;

    this.toast.showToast('success', '', `Successfully saved`);
  }

  selectExtendVisible = signal<boolean>(false);
  selectExtendValue = signal<NzSafeAny>(null);
  filterVisible = signal<boolean>(false);
  @ViewChild('filterForm') filterForm?: FormComponent;

  selectOption(value: OptionItem | NzSafeAny) {
    if (this.config.showCreateDataTable) return;
    if (this.mode === 'default') {
      const getDataValue =
        typeof value.value === 'object' ? value.value : value;
      this.selectExtendValue.set(getDataValue);

      // this.onChange(getDataValue);
      // this.group.get(this.config.name)?.setValue(this.value());
      // this.selectExtendVisible.set(false);
    }
  }
  isObjectEmptyValue(value: any): boolean {
    if (isNil(value)) return false;
    if (Array.isArray(value) && value.length === 0) return true;
    if (typeof value === 'object' && Object.keys(value).length === 0)
      return true;

    // Kiểm tra trường hợp value = { value: {} }
    if (
      typeof value === 'object' &&
      'value' in value &&
      typeof value.value === 'object' &&
      Object.keys(value.value).length === 0
    ) {
      return true;
    }

    return false;
  }
  onApplySelectExtendValue() {
    this.onChange(this.selectExtendValue());
    this.group.get(this.config.name)?.setValue(this.value());
    this.selectExtendVisible.set(false);
  }

  setCheckedItemInput = signal<Set<number>>(new Set());
  checkedItem() {
    const checked = new Set<number>();

    this.optionList.forEach((option, index) => {
      if (option.value?.['checked']) {
        checked.add(index);
      }
    });

    this.setCheckedItemInput.set(checked);
  }

  onCreateDataTable(isClick: boolean) {
    if (isClick) {
      this.selectExtendVisible.set(true);
    }
  }

  onAddSelectExtend() {
    // this.optionListParent = [
    //   ...this.optionListParent,
    //   ...this.listOfSelectedItemsChild(),
    // ];

    this.optionListParent = uniqBy(
      concat(this.optionListParent, this.listOfSelectedItemsChild()),
      'value.id',
    );
    const item = this.optionListParent.map((item) => item.value);
    this.group.get(this.config.name)?.setValue(item);
    this.selectExtendVisible.set(false);
    return;
  }

  deleteClickMany() {
    // const url = this.url();
    this.modalComponent.showDialog({
      nzContent: 'Are you sure you want to delete this information?',
      nzTitle: `Delete ${this.listOfSelectedItems().length} records`,
      nzWrapClassName: 'popup popup-error',
      nzIconType: 'icons:trash-bold',
      nzOkText: 'Delete',
      nzCancelText: 'Cancel',
      nzOnOk: () => {
        this.optionListParent = differenceWith(
          this.optionListParent,
          this.listOfSelectedItems(),
          isEqual,
        );
        this.group
          .get(this.config.name)
          ?.setValue(this.optionListParent.map((item) => item.value.id));
        this.toast.showToast('success', 'Success', 'Deleted Successfully');
      },
    });

    return;
  }

  calendarDialogVisiable = signal<boolean>(false);
  showCalendar() {
    this.calendarDialogVisiable.set(true);
  }

  isSelectOpen = false;
  onSuffixIconClick(event: Event) {
    event.stopPropagation();
    if (this.isSelectOpen) return;
    this.onOpenChange(true);
  }

  dynamicService = inject(DynamicFormService);

  filterOptions = (optionList: OptionItem[]) => {
    return optionList.length > 0
      ? optionList.filter((option) => option?.value?.['checked'])
      : optionList;
  };

  filterOption = (input: string, option: NzSafeAny): boolean => {
    return this.removeDiacritics(option?.nzLabel)
      .toLowerCase()
      .includes(this.removeDiacritics(input).toLowerCase());
  };

  // Function to remove diacritics
  removeDiacritics(str: string): string {
    return str.normalize('NFD').replace(/[\u0300-\u036f]/g, '');
  }

  calendarDataSource = computed(async () => {
    return await this.dynamicService.getJsonataExpression({})(
      this.config?.calendarOptions._value?.transform || '',
      this.values,
    );
  });

  calendarEffectiveDate = computed(async () => {
    return await this.dynamicService.getJsonataExpression({})(
      this.config?.calendarOptions._effectiveDate?.transform || '',
      this.values,
    );
  });

  calendarMethod = computed(async () => {
    return await this.dynamicService.getJsonataExpression({})(
      this.config?.calendarOptions._method?.transform || '',
      this.values,
    );
  });

  transformFieldTable() {
    return this.config?.selectSetting?.fieldsShow
      ? _.filter(this.config.selectSetting?.tableFields, (item) =>
          includes(this.config?.selectSetting?.fieldsShow, item?.name),
        )
      : this.config?.selectSetting?.tableFields;
  }

  transformData(item: NzSafeAny, field: NzSafeAny) {
    let data: NzSafeAny = '';
    if (item !== undefined) {
      let value = item.value;
      if (isNil(value?.[field?.name])) {
        value = item;
      }
      if (value[field?.name]?.default) {
        data = value[field?.name]?.default;
      } else data = value[field?.name];
    }

    if (field.dateFormat) {
      data = moment(data).format(field.dateFormat || 'DD/MM/YYYY') ?? '';
    }

    return data;
  }

  onClearClick(event: NzSafeAny) {
    this.clearedByUserClick = true;
    const clearFieldsAfterChange = this.config.clearFieldsAfterChange;
    const clearFieldsAfterClickClear =
      this.config.isclearFieldsAfterClickClear ?? true;
    if (clearFieldsAfterChange && clearFieldsAfterClickClear) {
      this.clearFields(clearFieldsAfterChange);
    }
    this.setPrevValue();
    if (
      this.config.confirmPopup &&
      !this.isCancelConfirm() &&
      this.formControlService.checkExistedValue(
        this.config.confirmPopup.listFieldsDependantName,
      )
    ) {
      this.showConfirmPopup();
    }
  }

  setPrevValue() {
    const currentValue = structuredClone(
      this._value() ?? this.group.get(this.config.name)?.value,
    );
    this.prevValue.set(currentValue);
    this.isCancelConfirm.set(false);
  }

  clearFields(fields: string[]) {
    fields.forEach((field) => {
      const control = this.group.get(field);
      if (control) {
        control.setValue(null);
      } else {
        this.formControlService.updateFormControlValue(field, null);
      }
    });
  }

  getStatusEnabledConfirmPopup(dataSource: SourceField, value?: NzSafeAny) {
    return new Promise((resolve) => {
      of(dataSource)
        .pipe(
          tap(() => this.cdr.detectChanges()),
          switchMap(() => {
            return this.service.getObservable(
              this.values.function,
              { ...this.values, fieldValue: value },
              dataSource,
            );
          }),
          catchError(() => of(false)),
        )
        .subscribe((d) => {
          resolve(d);
        });
    });
  }

  isCancelConfirm = signal<boolean>(false);
  async showConfirmPopup(value?: NzSafeAny) {
    const { content, title, _content, _title, _enabled } =
      this.config.confirmPopup ?? {};

    let enabled: NzSafeAny = null;
    if (_enabled) {
      enabled = await this.getStatusEnabledConfirmPopup(_enabled, value);
      if (!enabled) return;
    }

    this.updateConfirmStatus(false);
    const formValue = this.formControlService.getData()?.formGroup?.value;
    const convertValue = {
      ...formValue,
      _fieldValue: value,
      _enabled: enabled,
    };
    const nzContent = _content
      ? await this.buildCustomUrl(_content.transform, convertValue)
      : content;
    const nzTitle = _title
      ? await this.buildCustomUrl(_title.transform, convertValue)
      : title;

    this.modalComponent.showDialog(
      {
        nzContent: nzContent ?? 'Are you sure you want to change this value?',
        nzTitle: nzTitle ?? `Confirm`,
        nzWrapClassName: 'popup popup-confirm',
        nzIconType: 'icons:warning',
        nzOkText: 'Confirm',
        nzCancelText: 'Cancel',
        nzOnOk: () => {
          this.updateConfirmStatus(true);
        },
        nzOnCancel: () => {
          this.isCancelConfirm.set(true);
          // this.updateConfirmStatus(false);
          if (isNil(this.prevValue())) {
            this.value$.next('_setSelectValueNull');
          } else this.group.get(this.config.name)?.setValue(this.prevValue());
          const clearFieldsAfterChange = this.config.clearFieldsAfterChange;
          if (clearFieldsAfterChange) {
            this.clearFields(clearFieldsAfterChange);
          }
        },
      },
      'warning',
    );
  }

  updateConfirmStatus(status: boolean) {
    this.formControlService.setConfirm(status);
    this.formControlService.updateCPBehavior();
  }

  async buildCustomUrl(
    transformUrl: string,
    formValue: NzSafeAny | null | undefined,
  ) {
    return await this.dynamicService.getJsonataExpression({})(
      transformUrl,
      formValue,
    );
  }

  getValue(data: NzSafeAny, path: (string | number)[]): NzSafeAny {
    if (!data) {
      return undefined;
    }
    if (path.length < 1) return undefined;
    const tmp = path.shift() ?? '';
    if (typeof tmp === 'string' && new RegExp(/\([a-z0-9]*\)/, 'g').test(tmp)) {
      const key = tmp.replace('(', '').replace(')', '');
      if (isArray(data)) {
        return data.map((it) => it[key]);
      } else {
        return data[key];
      }
    }
    if (path.length <= 0) {
      return data[tmp];
    } else {
      return this.getValue(data[tmp], path);
    }
  }

  onHandleAfterChange(value: NzSafeAny) {
    if (value) {
      const handleAfterChange = this.config.handleAfterChange;
      if (!handleAfterChange) return;
      const dataSource = handleAfterChange.dataSource;
      const valueMapping = handleAfterChange.valueMapping ?? [];

      of(dataSource)
        .pipe(
          tap(() => this.cdr.detectChanges()),
          switchMap(() => {
            return this.service.getObservable(
              this.values.function,
              { ...this.values, fieldValue: value },
              dataSource,
            );
          }),
        )
        .subscribe((d) => {
          let currentPath = structuredClone(this.values.extend?.['path']);
          currentPath?.pop();
          currentPath = currentPath?.join('.');

          valueMapping.forEach(async (item: NzSafeAny) => {
            const control = this.group.get(item.field);
            const valueField = this.getValue(d, item.fieldValue.split('.'));
            let setNullValue = item._setNullValue;
            if (setNullValue) {
              setNullValue = await this.buildCustomUrl(setNullValue, d);
            }

            if (valueField || setNullValue) {
              if (control) {
                control.setValue(valueField, {
                  emitViewToModelChange: false,
                });
              } else {
                this.formControlService.updateFormControlValue(
                  item.field,
                  valueField,
                  {
                    emitViewToModelChange: false,
                  },
                );
              }

              //gửi sự kiện thay đổi value cho service
              this.formControlService.setValueFormEmit({
                key: item.field,
                value: valueField,
                path: currentPath,
              });
            }
          });
        });
    }
  }

  onChange(value: NzSafeAny) {
    if (isEqual(value, this.value()) || (isNil(value) && isNil(this.value())))
      return;
    const clearFieldsAfterChange = this.config.clearFieldsAfterChange;
    if (clearFieldsAfterChange && value) {
      this.clearFields(clearFieldsAfterChange);
    }

    this.onHandleAfterChange(value);
    if (this.mode === 'default') {
      if (this.config.inputValue) {
        this.optionList.map((option: NzSafeAny) => {
          option.value.id === value && (this.tempValue = option.value);
        });
      }
      //show confirm popup before change value
      if (
        this.config.confirmPopup &&
        !this.isCancelConfirm() &&
        this.formControlService.checkExistedValue(
          this.config.confirmPopup.listFieldsDependantName,
        )
      ) {
        this.showConfirmPopup(value);
      } else {
        this._value.set(value);
      }
    }
    if ((this.mode === 'multiple' || this.mode === 'tags') && isArray(value)) {
      this._value.set(
        value.filter((it) => {
          return !(this.defaultValue() as NzSafeAny[])?.find((i) =>
            this.compare(i, it),
          );
        }),
      );
    }
  }
  subscription?: Subscription;
  page = signal(1);
  oldPage = 0;
  pageSize = signal(10);
  allowValues = signal([]);
  stopPaging = false;
  page$ = toObservable(this.page);
  pageSize$ = toObservable(this.pageSize);
  allowValues$ = toObservable(this.allowValues);

  onScrollToBottom() {
    if (!this.stopPaging && !this.listLoading() && this.enabledLoadMore) {
      this.page.update((v) => v + 1);
    }
  }

  removeOptionNotExist() {
    this.optionList = this.optionList.filter((it) => {
      return !this.notExistOptions.find((option) => {
        return this.compare(option.value, it);
      });
    });

    this.notExistOptions = [];
  }

  updatePositionDropdown = () => {
    this.selectRef()?.cdkConnectedOverlay?.overlayRef?.updatePosition();
  };

  onOpenChange(open: boolean) {
    this.isSelectOpen = open;
    if (this.config.mode === 'multiple') {
      this.updateMaxTagByFocusState(open);
      if (!open) {
        this.selectRef()?.clearInput();
      }
    }

    const scrollables = [
      'mark-scroll-container',
      'right-side',
      'ant-modal-body',
      'scroll-page-container',
    ];

    let body = null;
    for (const className of scrollables) {
      const element = document.querySelector(`.${className}`);
      if (element) {
        body = element;
        break;
      }
    }
    if (open) {
      this.stopPaging = false;

      body?.addEventListener('scroll', this.updatePositionDropdown);
      // setTimeout(() => {
      //   const el = document.querySelector(
      //     '.dynamic-field--field-select--dropdown .cdk-virtual-scrollable ',
      //   ) as HTMLDivElement;
      //   el?.addEventListener('scroll', () => {
      //     if (
      //       el.offsetHeight + el.scrollTop >= el.scrollHeight - 5 &&
      //       !this.stopPaging
      //     ) {
      //       this.page.update((v) => v + 1);
      //     }
      //   });
      // }, 1000);

      this.setPrevValue();

      if (this.config.isRemoveOptionNotExist) {
        this.removeOptionNotExist();
      }
    } else {
      body?.removeEventListener('scroll', this.updatePositionDropdown);
    }
    if (this.subscription) {
      return;
    }
    if (this.config.isLazyLoad && open === true) {
      this.cacheValidateValue = undefined;
      this.subscription = this.optionList$?.subscribe();
    }
  }

  // updateMaxTagByFocusState(isFocus: boolean) {
  //   if (isFocus) {
  //     this.maxTag = Infinity;
  //   } else {
  //     this.maxTag = this.config?.options?.maxTag ?? 1;
  //   }
  // }

  openSelectExtend() {
    if (this.config.isLazyLoad && !this.subscription) {
      this.subscription = this.optionList$?.subscribe();
    }
    this.selectExtendVisible.set(true);
  }

  checkEqualValue(a: NzSafeAny, b: NzSafeAny) {
    return isEqual(a?.value, b?.value);
  }

  private handlePaging(values: NzSafeAny): Observable<NzSafeAny> {
    this.listLoading.set(true);
    return this.service
      .getObservable(this.values.function, values, this.config._select)
      .pipe(
        tap((data) => {
          // console.log('paging');
          const isLazyLoad = this.config?.isLazyLoad ?? false;
          if (!data || !data.length) {
            this.stopPaging = true;
            // when page = 1 reset option list to empty => to avoid old options still exist from the previous call
            if (this.page() === 1 && isLazyLoad) {
              this.optionList = [];
            }
            return;
          }
          if (!isArray(data)) return;
          if (isLazyLoad) {
            // this.optionList = [
            //   ...this.optionList,
            //   ...(data.map((it) => {
            //     return it;
            //   }) ?? []),
            // ];
            let newOptionList = data ?? [];
            if (this.page() > 1) {
              newOptionList = [...this.optionList, ...data];
            }
            this.optionList = _.uniqWith(newOptionList, this.checkEqualValue);
          } else {
            this.optionList =
              data.map((it) => {
                return it;
              }) ?? [];
          }
          this.optionListView = this.filterOptions(this.optionList);
          this.isCheckedItem() && this.checkedItem();
          this.subscriptionOptionList$.next(this.optionList);
        }),
        tap(() => this.cdr.detectChanges()),

        finalize(() => this.listLoading.set(false)),
      );
  }

  private handleNotPaging(values: NzSafeAny): Observable<NzSafeAny> {
    this.loading = true;
    this.cdr.detectChanges();
    const prevSelectedOption = find(
      this.optionList,
      (option) => option['value']['id'] === this.value(),
    );
    this.tempValue = prevSelectedOption?.value;

    return this.service
      .getObservable(this.values.function, values, this.config._select)
      .pipe(
        tap((data) => {
          // console.log('not paging', data);
          if (!data || !data.length) {
            this.stopPaging = true;
            this.optionList = Array.from(this.config?.select || []).map(
              (it) => {
                return it;
              },
            );
            this.subscriptionOptionList$.next(this.optionList);
            return;
          }
          if (!isArray(data)) return;

          this.optionList =
            data.map((it) => {
              return it;
            }) ?? [];

          if (this.cacheValidateValue) {
            const updateLabelExistOption =
              this.config._validateFn?.params?.['updateLabelExistOption'];

            this.notExistOptions = this.cacheValidateValue.filter((it) => {
              const existingOption = this.optionList.find((option) =>
                this.compare(option.value, it),
              );
              if (existingOption && updateLabelExistOption) {
                existingOption.label = it.label ?? existingOption.label;
              }
              return !existingOption;
            });

            const oldOptionList = structuredClone(this.optionList);

            this.optionList = [
              ...this.notExistOptions.map((it: NzSafeAny) => it),
              ...oldOptionList,
            ];
          }

          this.optionListView = this.filterOptions(this.optionList);
          this.isCheckedItem() && this.checkedItem();
          this.subscriptionOptionList$.next(this.optionList);
        }),
        tap(() => {
          if (this.config.isClearValue) {
            const key: NzSafeAny = this.config.inputValue ?? 'code';
            this.tempOption = this.findCurrentOption();
            if (!isEmpty(this.tempOption)) {
              if (
                !this.optionList?.some(
                  (opt: NzSafeAny) =>
                    opt.value[key] === this.tempOption?.value?.[key],
                )
              ) {
                this.value$.next('_setSelectValueNull');
              } else {
                const newValue = this.optionList.find(
                  (option) =>
                    option.value[key] === this.tempOption?.value?.[key],
                );
                // this.onChange(
                //   this.config?.outputValue?.split('.')[1]
                //     ? newValue?.['value']?.[
                //         this.config?.outputValue?.split('.')[1]
                //       ]
                //     : newValue?.['value'],
                // );
                const value = this.config?.outputValue?.split('.')[1]
                  ? newValue?.['value']?.[
                      this.config?.outputValue?.split('.')[1]
                    ]
                  : newValue?.['value'];
                this.value$.next(value);
              }
            } else if (
              this.config.inputValue &&
              !this.optionList?.some(
                (opt: NzSafeAny) =>
                  opt.value[this.config?.outputValue?.split('.')[1] ?? 'id'] ===
                  this.value(),
              )
            ) {
              this.value$.next('_setSelectValueNull');
            }
          }
        }),
        finalize(() => {
          this.loading = false;
          this.cdr.detectChanges();
        }),
      );
  }

  selectRef = viewChild<NzSelectComponent>('selectRef');
  getTagPlaceholderValue(selectedItem: NzSafeAny[]) {
    const listOfTopItem = this.selectRef()?.listOfTopItem;
    const filteredList = listOfTopItem?.filter((item) =>
      selectedItem.includes(item.key),
    );
    return filteredList?.map((item) => item.nzLabel);
  }

  updateMaxTagByFocusState(isFocus: boolean, focusValue = Infinity) {
    if (isFocus) {
      this.maxTag = focusValue;
    } else {
      const containerPadding = 32;
      const placeholderTagWidth = 50;
      const tagPadding = 70;
      const containerWidth =
        (this.el.nativeElement?.offsetWidth ?? 0) -
        containerPadding -
        placeholderTagWidth;
      const textList = this.selectRef()?.listOfTopItem?.map(
        (item) => item.nzLabel,
      ) as string[];
      const maxTag =
        this.config?.options?.maxTag ??
        this.calcMaxTag(
          textList,
          containerWidth,
          tagPadding,
          placeholderTagWidth,
        );
      this.maxTag = Math.max(maxTag, this.minTagCount);
    }
  }

  // use only for mode multiple
  private calcMaxTag(
    textList: string[],
    containerWidth: number,
    tagPadding = 0,
    placeholderTagWidth = 0,
  ) {
    if (!Array.isArray(textList) || textList.length <= 0) return 1;
    let tagCount = 0;
    let occupiedWidth = 0;
    for (const item of textList) {
      occupiedWidth += this.calcTextWidth(item) + tagPadding;
      if (
        tagCount === textList.length - 1 &&
        occupiedWidth < containerWidth + placeholderTagWidth
      ) {
        return tagCount + 1;
      }
      if (occupiedWidth > containerWidth) return tagCount;
      tagCount++;
    }

    return tagCount;
  }

  private calcTextWidth(text: string) {
    const font = '14px times new roman';
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    if (!context) return 0;
    context.font = font;
    const width = context?.measureText(text).width ?? 0;
    canvas.remove();
    return width;
  }

  loadOptionsOnModalVisible = effect(() => {
    if (
      this.subscription ||
      !this.config.isLazyLoad ||
      !this.selectExtendVisible()
    )
      return;

    this.subscription = this.optionList$?.subscribe();
  });
}

function mappingConfig(
  array: FieldGroupConfig[],
  variables: Record<string, NzSafeAny> = {},
  variablesMapping: Record<string, string> = {},
  obj: Record<
    string,
    {
      type: string;
      label: string;
      format: string;
      options?: { value: NzSafeAny; label: string }[];
    }
  > = {},
) {
  for (const field of array) {
    const item = field as NzSafeAny;
    if (item.name) {
      let options = item?.['options'];
      let format = item.setting?.format ?? item?.format;
      if (item.type === 'radio' && item.radio) {
        options = item.radio;
      }

      if (item.type === 'number') {
        format = item.number?.format;
      }

      const variablesMappingKey = variablesMapping[item.name];
      if (variablesMappingKey) {
        options = variables[variablesMappingKey];
      }

      obj[item.name] = {
        type: item.type,
        label: item.label ?? '',
        format,
        options,
      };
    }

    if (item?.fields) {
      mappingConfig(
        item.fields as FieldGroupConfig[],
        variables,
        variablesMapping,
        obj,
      );
    }
  }

  return obj;
}
