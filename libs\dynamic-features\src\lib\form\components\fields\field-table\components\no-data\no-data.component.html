<ng-content></ng-content>
<hrdx-modal
  [isVisible]="modalVisible()"
  [title]="title()"
  (canceled)="modalVisible.set(false)"
  [footer]="footer"
>
  <ng-container *ngIf="config()?.form && isRendered()">
    <dynamic-form
      [config]="config()?.form ?? []"
      [reset]="reset"
      [ppxClass]="'ppxm-style'"
      [sources]="config()?.sources ?? {}"
      [variables]="config()?.variables ?? {}"
      (valueChanges)="filterChange($event)"
      #formObj
    ></dynamic-form>
  </ng-container>
  <ng-container *ngIf="config()?.addSetup && isRendered()">
    <dynamic-form
      [config]="config()?.addSetup?.filter ?? []"
      [ppxClass]="'ppxm-style'"
      [sources]="config()?.sources ?? {}"
      [variables]="config()?.variables ?? {}"
      (valueChanges)="filterChange($event)"
      [extend]="formExtendData()"
      #formObj
    ></dynamic-form>
    <dynamic-field-select-table
      *ngIf="config()?.addSetup as config"
      (selectedItemChange)="selectedItems.set($event)"
      [APIsource]="filter()"
      [columns]="config.columns ?? []"
      [clientPagination]="config.clientPagination ?? false"
    />
  </ng-container>
  <ng-template #footer>
    <footer>
      <hrdx-button
        [type]="'ghost-gray'"
        [title]="'Cancel'"
        (clicked)="onCancel.emit(); modalVisible.set(false)"
      />
      <hrdx-button
        [type]="'primary'"
        [disabled]="config()?.form && !dynamicForm?.valid"
        [title]="'Confirm'"
        (clicked)="
          reset = !reset;
          config()?.form && onSave.emit(dynamicForm?.value);
          config()?.addSetup && tableSlected.emit(updateData());
          modalVisible.set(false)
        "
      />
    </footer>
  </ng-template>
</hrdx-modal>
