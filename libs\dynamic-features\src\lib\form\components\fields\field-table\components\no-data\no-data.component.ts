import {
  Component,
  effect,
  forwardRef,
  input,
  model,
  output,
  signal,
  ViewChild,
  computed,
  untracked,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { ButtonComponent, ModalComponent } from '@hrdx/hrdx-design';
import { FormComponent } from '../../../../../form.component';
import { FieldTableConfigI } from '../../field-table.models';
import { FieldSelectTableComponent } from '../../../field-select-table/field-select-table.component';
import { NzSafeAny } from 'ng-zorro-antd/core/types';
import { QueryFilter, RequestQueryBuilder } from '@nestjsx/crud-request';
import { isEmpty, isNil } from 'lodash';

@Component({
  selector: 'dynamic-no-data',
  standalone: true,
  imports: [
    forwardRef(() => FormComponent),
    CommonModule,
    ModalComponent,
    ButtonComponent,
    FieldSelectTableComponent,
  ],
  templateUrl: './no-data.component.html',
  styleUrl: './no-data.component.less',
  host: {
    '(click)': 'modalVisible.set(true)',
  },
})
export class NoDataComponent {
  modalVisible = model<boolean>(false);
  title = input<string>('Add Setup Details');
  config = input<FieldTableConfigI>();
  onCancel = output<void>();
  source = input<string>('/api/picklists/ELEMENTGROUP/values');
  filter = signal<string>('');
  defaultFilterValue = input<NzSafeAny>({});
  defaultData = input<NzSafeAny>({});
  selectedItems = signal<NzSafeAny[]>([]);
  isRendered = signal(false);
  formExtendData = input<NzSafeAny>();

  filterOnVisible = effect(
    () => {
      const visible = this.modalVisible();
      if (visible) {
        this.isRendered.set(true);
        this.filterChange({ value: this.filterValue ?? {} });
      }
    },
    { allowSignalWrites: true },
  );

  private getQueryFilter(filterValue: Record<string, any>) {
    const filterMapping =
      this.config()?.addSetup?.filterMapping ??
      Object.keys(filterValue).map((key) => ({
        valueField: key,
        field: key,
        operator: '$eq',
      }));
    return filterMapping
      .map((m) => ({
        field: m.field,
        operator: m.operator,
        value: filterValue[m.valueField],
      }))
      .filter((m) => {
        if (typeof m.value === 'string') {
          return !isEmpty(m.value);
        }
        return !isNil(m.value);
      }) as QueryFilter[];
  }

  private buildUrlString(url: string, filterValue: Record<string, any>) {
    const queryFilter = this.getQueryFilter(filterValue);
    if (queryFilter.length === 0) return url;
    const qb = RequestQueryBuilder.create();
    qb.setFilter(queryFilter);
    return `${url}?${qb.query()}`;
  }

  filterValue: Record<string, any> = {};
  filterChange(filter: NzSafeAny) {
    let filterValue = filter.value;
    const defaultFilter = this.defaultFilterValue();
    if (filterValue && defaultFilter && typeof defaultFilter === 'object') {
      filterValue = { ...defaultFilter, ...(filterValue ?? {}) };
    }
    if (!filterValue) return;
    this.filterValue = filterValue;
    untracked(() =>
      this.filter.set(this.buildUrlString(this.source(), filterValue)),
    );
  }

  onSave = output<Event>();
  tableSlected = output<NzSafeAny>({});

  updateData = computed(() => {
    const items = this.selectedItems() ?? [];
    let defaultData = this.config()?.addSetup?.defaultData ?? {};
    if (!isEmpty(this.defaultData())) {
      defaultData = this.defaultData();
    }
    return items.map((item: NzSafeAny) => ({
      ...item,
      ...defaultData,
    }));
  });
  reset = false;

  @ViewChild('formObj') dynamicForm?: FormComponent;
}
