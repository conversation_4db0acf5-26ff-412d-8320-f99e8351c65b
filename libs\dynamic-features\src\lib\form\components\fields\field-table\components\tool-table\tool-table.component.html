<ng-container *ngIf="filterVisible()">
  <div style="width: 300px">
    <nz-input-group [nzPrefix]="suffixIconSearch">
      <input
        type="text"
        nz-input
        placeholder="Search"
        [ngModel]="search()"
        (ngModelChange)="onSearchValueChange.emit($event)"
      />
    </nz-input-group>
    <ng-template #suffixIconSearch>
      <span nz-icon nzType="icons:magnifying-glass"></span>
    </ng-template>
  </div>
</ng-container>
<div class="buttons">
  <dynamic-no-data
    [config]="config()"
    (onSave)="addSetupChange.emit($event)"
    (tableSlected)="tableSlected.emit($event)"
    [source]="config()?.sources"
    [defaultFilterValue]="defaultFilterValue()"
    [defaultData]="defaultData()"
    *ngIf="addSetupVisible()"
  >
    <hrdx-button
      title="Add setup"
      [isLeftIcon]="true"
      [type]="'tertiary'"
      leftIcon="icon-plus"
    />
  </dynamic-no-data>
  <hrdx-button
    class="operations"
    title="Tổng hợp"
    (clicked)="operationsChange.emit()"
    [isLeftIcon]="true"
    [type]="operations() ? 'secondary' : 'tertiary'"
    leftIcon="icon-math-operations"
    *ngIf="operationsVisible()"
  />
  <hrdx-button
    title="Mở khóa"
    (clicked)="lockChange.emit()"
    [isLeftIcon]="true"
    [type]="lock() ? 'secondary' : 'tertiary'"
    leftIcon="icon-lock-open"
    *ngIf="lockVisible()"
  />
  <hrdx-button
    title="Xuất excel"
    (clicked)="exportChange.emit()"
    [isLeftIcon]="true"
    [type]="export() ? 'secondary' : 'tertiary'"
    leftIcon="icon-file-arrow-down"
    *ngIf="exportVisible()"
  />
  <hrdx-button
    title="Download Template"
    [isLeftIcon]="true"
    [type]="'tertiary'"
    leftIcon="icon-file-arrow-down"
    (clicked)="downloadTemplateChange.emit()"
    *ngIf="downloadTemplate()"
  />
  <hrdx-button
    title="Edit"
    [isLeftIcon]="true"
    [type]="'secondary'"
    leftIcon="icon-pencil-simple-bold"
    (clicked)="editChange.emit()"
    *ngIf="editVisible()"
  />
  <hrdx-adjust-display
    *ngIf="adjustHeaders()"
    [fields]="tableHeaders() ?? []"
    (displayChanged)="headersChanged.emit($event)"
  ></hrdx-adjust-display>
</div>
