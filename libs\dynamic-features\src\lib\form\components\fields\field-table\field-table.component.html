<div
  class="insert-data-section"
  *ngIf="!isReadOnly() && config?.insertDataConfig as insertDataConfig"
>
  <dynamic-form
    [config]="formConfig.fields ?? []"
    [sources]="formConfig.sources ?? {}"
    [variables]="formConfig.variables ?? {}"
    [formValue]="{}"
    [readOnly]="false"
    [ppxClass]="'ppxm-style'"
    [extend]="insertDataFormExtendData"
    [reset]="resetInsertDataForm()"
    (valueChanges)="insertDataConfig.validateOnValueChanges && insertDataFormValue$.next($event?.value ?? {})"
    *ngIf="insertDataConfig.formConfig as formConfig"
    #insertDataForm
  ></dynamic-form>
  <div class="validate-message" *ngIf="insertDataValidateMessage()">
    {{ insertDataValidateMessage() }}
  </div>
  <div class="actions">
    <hrdx-button
      [type]="'secondary'"
      [title]="'Insert data'"
      [leftIcon]="'icon-plus'"
      [isLeftIcon]="true"
      (clicked)="insertDataSubmit()"
    />
  </div>
</div>

<dynamic-field-calendar
  [values]="values"
  [_calendarOptions]="config?.calendarOptions"
  *ngIf="config?.calendarOptions"
/>
<dynamic-tool-table
  *ngIf="showToolTable()"
  [filterVisible]="config.config?.filter || false"
  [addSetupVisible]="(config.config?.addSetup && !config.readOnly) || false"
  [operationsVisible]="config.config?.operations || false"
  [lockVisible]="checkPermissionToolTable('lock')"
  [exportVisible]="checkPermissionToolTable('export')"
  [editVisible]="checkPermissionToolTable('edit')"
  [downloadTemplate]="checkPermissionToolTable('downloadTemplate', 'ExportTemplate')"
  (addSetupChange)="edditItem($event)"
  (tableSlected)="updateDataTable($event)"
  [config]="config"
  [defaultFilterValue]="defaultFilterValue()"
  [defaultData]="defaultData()"
  [search]="search()"
  (onSearchValueChange)="onSearchValueChange($event)"
  (editChange)="onEditTable()"
  (downloadTemplateChange)="onDownloadTemplate()"
  [adjustHeaders]="config.config?.adjustHeaders || false"
  (headersChanged)="adjustFields.set($event)"
  [tableHeaders]="transformHeaders()"
></dynamic-tool-table>

<ng-container class="field-table">
  <div class="tool-group" *ngIf="showTableFilter() || hiddenHeader()">
    <div class="tool-field-table">
      <div class="left">
        <hrdx-filter-group
          [showTableFilter]="showTableFilter()"
          [expandFilter]="expandFilter()"
          [showTableGroup]="showTableGroup()"
          [headers]="config.columns ?? []"
          (filterItem)="filterItem($event)"
          (radioEmit)="groupItem($event)"
          (removedGroupByKey)="removedGroupByKey()"
          [showTableSearch]="showTableSearch()"
        />
      </div>
      <div class="right" *ngIf="hiddenHeader()">
        <hrdx-button
          [type]="'tertiary'"
          [nzDropdownMenu]="menu"
          [nzTrigger]="'click'"
          [nzPlacement]="'bottomLeft'"
          nz-dropdown
          [onlyIcon]="true"
          [icon]="'gear'"
        >
        </hrdx-button>
        <nz-dropdown-menu #menu="nzDropdownMenu">
          <ul nz-menu class="dropdown-menu-action">
            @for (h of config.columns; track $index) {
              <li class="dropdown-list">
                <span>{{ h.title }}</span>
                <nz-switch
                  class="switch"
                  [ngModel]="!isOffHeader(h.code)"
                  (ngModelChange)="switchChangeHeader(h.code)"
                ></nz-switch>
              </li>
            }
          </ul>
        </nz-dropdown-menu>

        <hrdx-button
          [type]="'ghost-color'"
          [size]="'xsmall'"
          [leftIcon]="'plus'"
          [isLeftIcon]="true"
          [title]="'Add new value'"
          (clicked)="addNewValue($event)"
          *ngIf="showAddNewValue()"
        />
      </div>
    </div>
    <ng-container *ngIf="isValidFilterValue()">
      <div class="filter-data">
        <hrdx-data-render
          [filterLst]="filterValue()"
          (removedFilterItem)="removedFilterItem($event)"
        />
      </div>
    </ng-container>
  </div>

  <div
    class="hrdx-field-table"
    [class.field-table-no-data]="displayData().length === 0"
    [id]="tableId()"
  >
    <ng-container [ngTemplateOutlet]="headerTemplate"></ng-container>
    <hrdx-new-table
      [data]="displayData()"
      [total]="dataSource().length"
      [loading]="loading()"
      [pageIndex]="pageIndex()"
      [pageSize]="pageSize()"
      (selectedItemChange)="listOfSelectedItems.set($event)"
      [showCheckbox]="showCheckbox()"
      (pageSizeChange)="onPageSizeChange($event)"
      (pageIndexChange)="pageIndex.set($event)"
      [showPagination]="showPagination()"
      [pageSizeOptions]="pageSizeOptions()"
      [haveChildItemCheckbox]="haveChildItemCheckbox()"
      [_hideRowAction]="hideRowAction()"
      [scrollHeight]="scrollHeight()"
      [showCreateDataTable]="false"
      [headers]="displayColumns()"
      (orderChange)="orderChange($event)"
      [columnWidthPath]="['width']"
      [noDataSubText]="config.layout_option?.no_data_sub_text"
      [showRowIndex]="showRowIndex()"
      [showAddNew]="showAddNewItem()"
      [addNewBtnTitle]="config.layout_option?.add_new_button_title ?? 'Add New'"
      (createDataTable)="addNewItemVisible.set(true)"
      [resize]="config.layout_option?.resize"
    >
      <hrdx-thead>
        @for (column of displayColumns(); track column.code) {
          <hrdx-th
            [width]="column.width || 15"
            [fixedLeft]="column.pinned"
            [align]="column.align ?? 'left'"
            *ngIf="!column.unvisible"
          >
            {{ column.title }}
          </hrdx-th>
        }
      </hrdx-thead>

      <!-- collapse -->
      <ng-container *ngIf="showCollapse()">
        @for (row of displayData(); track $index) {
          @for (
            item of mapOfExpandedData[row[rowActionId()]];
            track $index;
            let rowIdx = $index
          ) {
            <hrdx-tbody
              *ngIf="
                (item['parent'] && item['parent'].expand) || !item['parent']
              "
              [ngClass]="{ 'ant-table-row-expanded': item.expand }"
              [hiddenAction]="true"
              [idx]="item.id"
              (clickRow)="handleClickRow(item)"
            >
              @for (column of displayColumns(); track column.code) {
                <hrdx-td
                  [nzIndentSize]="item.level! * 20"
                  [nzShowExpand]="!!item.children"
                  [(nzExpand)]="item.expand"
                  (nzExpandChange)="
                    collapse(
                      mapOfExpandedData[row[rowActionId()]],
                      item,
                      $event
                    )
                  "
                >
                  <ng-container *ngIf="$index === 0 && !!item['children']">
                    <hrdx-icon
                      [name]="item['expand'] ? 'chevron-up' : 'chevron-down'"
                      [fontStyle]="'light'"
                      [size]="'small'"
                      (click)="
                        toggleExpand(
                          mapOfExpandedData[row[rowActionId()]],
                          item
                        )
                      "
                      class="ant-table-expand-icon"
                    />
                  </ng-container>

                  <!-- <dynamic-display-item
                      [item]="item"
                      [column]="column"
                      [config]="config"
                      (valueChanges)="valueChange($event)"
                      (getFormValue)="getFormValue($event)"
                      [dynamicForms]="dynamicForms"
                    ></dynamic-display-item> -->
                  <ng-container
                    [ngTemplateOutlet]="displayItem"
                    [ngTemplateOutletContext]="{
                      item: item,
                      column: column,
                      config: config,
                      idx: rowIdx,
                      readOnly: isReadOnly(),
                    }"
                  ></ng-container>
                </hrdx-td>
              }
            </hrdx-tbody>
          }
        }
      </ng-container>

      <!-- default case -->
      <ng-container *ngIf="!showCollapse()">
        <ng-container *ngIf="groupedData?.length; else defaultCase">
          @for (data of groupedData; track $index) {
            <hrdx-tbody>
              <hrdx-td
                [colSpan]="displayColumns().length"
                className="group-header"
              >
                {{ groupLabel }}: {{ data.key }}
              </hrdx-td>
            </hrdx-tbody>
            @for (row of data.items; track $index; let rowIdx = $index) {
              <hrdx-tbody>
                @for (column of displayColumns(); track column.code) {
                  <hrdx-td *ngIf="!column.unvisible">
                    <!-- <dynamic-display-item
                      [item]="row"
                      [column]="column"
                      [config]="config"
                      (valueChanges)="valueChange($event)"
                    ></dynamic-display-item> -->
                    <ng-container
                      [ngTemplateOutlet]="displayItem"
                      [ngTemplateOutletContext]="{
                        item: row,
                        column: column,
                        config: config,
                        idx: rowIdx,
                        readOnly: isReadOnly(),
                      }"
                    ></ng-container>
                  </hrdx-td>
                }
                <ng-container
                  *ngIf="!hideRowAction()"
                  row-actions
                  [ngTemplateOutlet]="actionOne"
                  [ngTemplateOutletContext]="{ row: row, rowIdx: $index }"
                ></ng-container>
              </hrdx-tbody>
            } @empty {}
          } @empty {}
        </ng-container>
        <ng-template #defaultCase>
          @for (row of displayData(); track $index) {
            <ng-container
              *ngTemplateOutlet="tbody; context: { row: row, rowIdx: $index }"
            ></ng-container>
          }
        </ng-template>

        <ng-template #tbody let-row="row" let-rowIdx="rowIdx">
          <hrdx-tbody (clickRow)="handleClickRow(row)">
            @for (column of displayColumns(); track column.code) {
              <hrdx-td *ngIf="!column.unvisible">
                <!-- <dynamic-display-item
                  [item]="row"
                  [column]="column"
                  [config]="config"
                  (valueChanges)="valueChange($event)"
                ></dynamic-display-item> -->
                <ng-container
                  [ngTemplateOutlet]="displayItem"
                  [ngTemplateOutletContext]="{
                    item: row,
                    column: column,
                    config: config,
                    idx: rowIdx,
                    readOnly: isReadOnly(),
                  }"
                ></ng-container>
              </hrdx-td>
            }
            <ng-container
              *ngIf="!hideRowAction()"
              row-actions
              [ngTemplateOutlet]="actionOne"
              [ngTemplateOutletContext]="{ row: row, rowIdx }"
            ></ng-container>
          </hrdx-tbody>
        </ng-template>
      </ng-container>

      <ng-container selected-actions>
        @if (actionsMany()) {
          @for (action of actionsMany(); track action.id) {
            <hrdx-button
              [type]="action.type"
              [title]="action.title"
              [leftIcon]="action.icon"
              [isLeftIcon]="true"
              [size]="'xsmall'"
              (clicked)="onActionsManyClick(action.id)"
            />
          }
        } @else {
          <hrdx-button
            [type]="'secondary'"
            [size]="'xsmall'"
            [leftIcon]="'icon-trash-bold'"
            [isLeftIcon]="true"
            [title]="'Delete'"
            (clicked)="deleteManyRecords()"
            *ngIf="showActionsMany()"
          />
        }
      </ng-container>
      <ng-container add-data>
        <dynamic-field-no-data
          (onSave)="onSelectData()"
          [createFormTable]="config.createFormTable"
        />
      </ng-container>
    </hrdx-new-table>
    <hrdx-button
      [title]="config.add_label ?? 'add new item'"
      [leftIcon]="'add'"
      [isLeftIcon]="true"
      (clicked)="addRow()"
      [type]="config.add_btn_type ?? 'secondary'"
      style="margin-top: 8px; display: block"
      *ngIf="config?.canAddItem"
    >
    </hrdx-button>
  </div>
</ng-container>
<ng-template #headerTemplate>
  @if (indeterminate || allChecked) {
    <div class="selected-header">
      <div class="count-selected">
        {{ setOfSelectedItem.size }} item(s) selected
      </div>

      <div class="list-action">
        <ng-container
          [ngTemplateOutlet]="selectedActionTemplate"
        ></ng-container>
      </div>

      <hrdx-icon
        name="x"
        fontStyle="solid"
        [size]="'small'"
        (click)="clearChecked()"
      ></hrdx-icon>
    </div>
  }
</ng-template>
<ng-template #selectedActionTemplate>
  @if (actionsMany()) {
    @for (action of actionsMany(); track action.id) {
      <hrdx-button
        [type]="action.type"
        [title]="action.title"
        [leftIcon]="action.icon"
        [isLeftIcon]="true"
        [size]="'xsmall'"
        (clicked)="onActionsManyClick(action.id)"
      />
    }
  } @else {
    <hrdx-button
      [type]="'secondary'"
      [size]="'xsmall'"
      [leftIcon]="'icon-trash-bold'"
      [isLeftIcon]="true"
      [title]="'Delete'"
      (clicked)="deleteClickMany()"
    />
  }
</ng-template>

<hrdx-modal
  [size]="actionFormSize"
  [isVisible]="actionFormVisible"
  [title]="actionFormTitle"
  *ngIf="config.actionButton"
  [footer]="footer"
  (canceled)="onCancel()"
>
  <div class="dynamic-form view-detail-form" *ngIf="actionFormVisible">
    <p class="form-description">{{ actionFormDescription }}</p>
    <dynamic-form
      [config]="actionFormConfig ?? []"
      [sources]="config.sources ?? {}"
      [variables]="config.variables ?? {}"
      [formValue]="{}"
      [readOnly]="false"
      [ppxClass]="'ppxm-style'"
      [extend]="{
        formType: 'edit',
      }"
      #actionButtonForm
    ></dynamic-form>
  </div>
  <!--  -->
</hrdx-modal>

<ng-template #footer>
  <div class="modal__footer">
    <hrdx-button
      [type]="'ghost-gray'"
      [title]="'Cancel'"
      (clicked)="onCancel()"
    />
    <hrdx-button
      [type]="'primary'"
      [title]="'Save'"
      (clicked)="onHandleActionsMany()"
    />
  </div>
</ng-template>

<ng-template #modalContent let-ref="modalRef">
  <span>Confirm cancellation of creation?</span>
  <div class="modal-footer-action">
    <hrdx-button
      [type]="'tertiary'"
      [title]="'Cancel'"
      (clicked)="ref.destroy()"
      [size]="'default'"
    />
    <hrdx-button
      [type]="'primary'"
      (clicked)="onCancelClick(ref)"
      [title]="'Confirm'"
      [size]="'default'"
    />
  </div>
</ng-template>

<hrdx-modal
  [size]="actionFormSize"
  [isVisible]="dialogVisible()"
  [title]="nzModalTitle"
  *ngIf="dialogVisible()"
  [footer]="footerModal"
  (canceled)="onCancel()"
>
  <div [ngClass]="['dialog-content']" *ngIf="dialogVisible()">
    <ng-container [ngTemplateOutlet]="content"></ng-container>
  </div>
</hrdx-modal>

<ng-template #nzModalTitle>
  <div style="display: flex; justify-content: space-between; width: 100%">
    <span> {{ dialogTitle() }} </span>
    <hrdx-button
      [type]="'tertiary'"
      [nzDropdownMenu]="menu"
      [nzTrigger]="'click'"
      [nzPlacement]="'bottomLeft'"
      nz-dropdown
      [title]="'Settings'"
      [leftIcon]="'sliders-simple'"
      [isLeftIcon]="true"
      [ngClass]="'filter-btn'"
      *ngIf="dialogType() === 'filter'"
    />

    <nz-dropdown-menu #menu="nzDropdownMenu">
      <ul
        nz-menu
        style="
          max-height: 500px;
          overflow: auto;
          display: flex;
          flex-direction: column;
        "
      >
        @for (h of config.columns; track h.code) {
          <li
            style="
              width: 100%;
              display: flex;
              align-items: center;
              justify-content: space-between;
              padding: 8px 16px;
            "
          >
            <span>{{ h.title }}</span>
            <nz-switch
              class="switch"
              [ngModel]="!isOff(h.code)"
              (ngModelChange)="switchChange(h.code)"
            ></nz-switch>
          </li>
        } @empty {}
      </ul>
    </nz-dropdown-menu>
  </div>
</ng-template>
<ng-template #content>
  <dynamic-form
    [config]="realConfig()?.fields ?? []"
    [sources]="realConfig()?.sources ?? {}"
    [variables]="realConfig()?.variables ?? {}"
    [formValue]="_value()"
    [ppxClass]="'ppxm-style'"
    [readOnly]="dialogType() === 'view'"
    [extend]="{
      formType: dialogType(),
      disabledFields: mappingDisabledFields(),
      defaultValue: _value(),
    }"
    [reload]="reset()"
    #formObj
  ></dynamic-form>
</ng-template>

<ng-template #footerModal>
  <div class="dialog-tool-table-footer">
    <div>
      <!-- define reset button -->
      <hrdx-button
        [type]="'ghost-color'"
        [title]="button.label"
        (clicked)="onReset()"
        [size]="'default'"
        *ngIf="buttons()['reset'] as button"
      ></hrdx-button>
    </div>
    <div class="list-btn">
      <!-- define cancel and  ok button  -->
      <hrdx-button
        type="secondary"
        [title]="button.label"
        (clicked)="onCancel()"
        [size]="'default'"
        *ngIf="buttons()['cancel'] as button"
      ></hrdx-button>
      <hrdx-button
        type="primary"
        [title]="button.label"
        (clicked)="onDelete()"
        [size]="'default'"
        *ngIf="!showDeleteButton() && buttons()['delete'] as button"
      ></hrdx-button>
      <hrdx-button
        type="primary"
        [title]="button.label"
        (clicked)="onEdit()"
        [size]="'default'"
        *ngIf="buttons()['edit'] as button"
      ></hrdx-button>
      <hrdx-button
        type="secondary"
        [title]="button.label"
        (clicked)="onSaveAndAddNew()"
        [disabled]="!dynamicForm?.valid"
        [size]="'default'"
        *ngIf="showSaveAddButton() && buttons()['saveAndAddNew'] as button"
      ></hrdx-button>
      <hrdx-button
        type="primary"
        [title]="button.label"
        (clicked)="onOk()"
        [size]="'default'"
        [disabled]="!dynamicForm?.valid"
        [isLoading]="isButtonLoading()"
        *ngIf="buttons()['ok'] as button"
      ></hrdx-button>
    </div>
  </div>
</ng-template>

<ng-template
  #displayItem
  let-item="item"
  let-column="column"
  let-config="config"
  let-idx="idx"
  let-readOnly="readOnly"
>
  <ng-container *ngIf="item && column">
    <ng-container *ngIf="column.type === 'cell-field'; else test">
      <dynamic-cell-field [cellField]="column?.cellField" />
    </ng-container>
    <ng-template #test>
      <ng-container *ngIf="item[column.code]?.fields; else showDefaultTemplate">
        <ng-container [ngTemplateOutlet]="showDynamicForm"></ng-container>
      </ng-container>
    </ng-template>
  </ng-container>

  <ng-template #showDynamicForm>
    <dynamic-form
      [config]="item[column.code]?.fields ?? []"
      [sources]="config.sources ?? {}"
      [variables]="config.variables ?? {}"
      [readOnly]="false"
      [ppxClass]="'ppxm-style'"
      [extend]="{ formType: values?.extend?.['formType'] }"
      (valueChanges)="valueChange({ id: item.id, value: $event })"
      [formValue]="getFormValue(item.id)"
      [id]="item.id"
      #formObj
    ></dynamic-form>
  </ng-template>

  <ng-template #showDefaultTemplate>
    <hrdx-display
      [props]="column?.props"
      (changeValueHandler)="valueChange({ id: item.id, value: $event })"
      [mode]="values?.extend?.['formType']"
      [type]="
        (item &&
          (item[column.display_type?.conditionKey] ===
          column.display_type?.condition
            ? column.display_type?.customType
            : column?.display_type?.key)) ||
        column?.display_type?.key ||
        'Label'
      "
      [value]="item && item[column.code] && item[column.code]"
      [value$]="
        column.display_type?.value$
          ? column.display_type?.value$(idx)
          : undefined
      "
      [title]="column.title"
      [options$]="column.display_type?.options$"
      [readOnly$]="
        column.display_type?.readOnly$
          ? column.display_type?.readOnly$(idx)
          : undefined
      "
      [disabled$]="
        column.display_type?.disabled$
          ? column.display_type?.disabled$(idx)
          : undefined
      "
      [validators]="column.display_type?.validators"
      [values]="values"
      [code]="column.code"
      [idx]="idx"
      [extraConfig]="column.extra_config"
      (changeValue)="changeValue($event, item, column.code)"
      (clickHandler)="handleClickHyperlink(item, $event, column)"
      (invalidEmitter)="childInvalid($event, idx, column.code)"
      (actionSubmit)="handleCellAction($event, item)"
      [readOnly]="readOnly"
    ></hrdx-display>
  </ng-template>
</ng-template>

<ng-template #actionOne let-row="row" let-rowIdx="rowIdx">
  <ng-container>
    @if (actionRow()) {
      @for (action of mergeActions(row); track action.id; let idx = $index) {
        <hrdx-button
          [type]="action.type"
          [title]="getActionOneTitle(action)"
          [onlyIcon]="true"
          [icon]="action.icon"
          [size]="'xsmall'"
          (clicked)="onActionOneClick(row, action.id, $event, rowIdx)"
          [disabled]="recordDisabledActionsState()[rowIdx]?.[action.id]?.['disabled'] ?? false"
        />
      }
    } @else {
      <hrdx-button
        [type]="'ghost-gray'"
        [size]="'small'"
        [icon]="'eye'"
        [onlyIcon]="true"
        [title]="'View'"
        (clicked)="viewClickOne(row[rowActionId()], row, $event)"
      />
      <hrdx-button
        [type]="'ghost-gray'"
        [size]="'small'"
        [onlyIcon]="true"
        [icon]="'pencil'"
        [title]="'Edit'"
        (clicked)="editClickOne(row[rowActionId()], row, $event)"
      />
      <hrdx-button
        [type]="'ghost-gray'"
        [size]="'small'"
        [onlyIcon]="true"
        [title]="'Lock'"
        [icon]="'lock-keyhole'"
      />
      <hrdx-button
        [type]="'ghost-gray'"
        [size]="'small'"
        [onlyIcon]="true"
        [icon]="'icon-trash-bold'"
        [title]="'Delete'"
        (clicked)="deleteClickOne(row[rowActionId()], $event)"
      />
    }
  </ng-container>
</ng-template>

<!-- edit table modal -->
<hrdx-modal
  [size]="'largex'"
  [isVisible]="editTableModalVisible()"
  [title]="editTableModalTitle()"
  *ngIf="config.config?.edit"
  [footer]="editTableModalFooter"
  (canceled)="closeEditTableModal()"
>
  <hrdx-new-table
    [data]="dataTableToEdit()"
    [total]="dataTableToEdit().length"
    [rowLoadingSkeleton]="10"
    [showCheckbox]="true"
    [showRowIndex]="true"
    [colLoadingSkeleton]="10"
    [scrollHeight]="'500px'"
    [showCreateDataTable]="true"
    [showPagination]="false"
    [showAddNew]="true"
    [addNewBtnTitle]="editTableConfig().table?.addNewBtnTitle ?? 'Add New'"
    (createDataTable)="onAddNewItem()"
    (selectedItemChange)="selectedItemsEditTable.set($event)"
    #tableValue
  >
    <ng-container selected-actions>
      <hrdx-button
        [type]="'secondary'"
        [size]="'xsmall'"
        [leftIcon]="'icon-trash-bold'"
        [isLeftIcon]="true"
        [title]="'Delete'"
        (clicked)="onDeleteItems()"
      />
    </ng-container>
    <hrdx-thead>
      @for (column of columnTable(); track column.code) {
        <hrdx-th
          [width]="column.width || 15"
          [fixedLeft]="column.pinned"
          [align]="column.align ?? 'left'"
        >
          {{ column.title }}
        </hrdx-th>
      }
    </hrdx-thead>
    @for (item of dataTableToEdit(); track item; let rowIdx = $index) {
      <hrdx-tbody [hiddenAction]="false" [disabled]="editTableDisabledActionsState()[rowIdx]?.['delete']?.['disabled'] ?? false">
        @for (col of columnTable(); track $index) {
          <hrdx-td>
            <hrdx-display
              [type]="col.display_type?.key ?? 'Label'"
              [value]="item[col.code]"
              [props]="col.props"
              (changeValue)="
                onChangeValueItemEditTable($event, rowIdx, col.code)
              "
              [disabled$]="col.display_type?.disabled$?.(rowIdx)"
            ></hrdx-display>
          </hrdx-td>
        }
        <ng-container row-actions>
          <hrdx-button
            [type]="'ghost-gray'"
            [size]="'xsmall'"
            [icon]="'icon-trash-bold'"
            [onlyIcon]="true"
            [title]="'Delete'"
            (clicked)="onDeleteItem(rowIdx)"
            [disabled]="editTableDisabledActionsState()[rowIdx]?.['delete']?.['disabled'] ?? false"
          ></hrdx-button>
        </ng-container>
      </hrdx-tbody>
    }
  </hrdx-new-table>
  <ng-template #editTableModalFooter>
    <div class="edit-table-modal__footer">
      <div class="actions">
        <hrdx-button
          [type]="'tertiary'"
          [title]="'Cancel'"
          size="default"
          (clicked)="onCancelEditTable()"
        />
        <hrdx-button
          [type]="'secondary'"
          [title]="'Save & Download'"
          size="default"
          [isLoading]="actionsLoadingState()['saveAndDownload']"
          (clicked)="onSaveAndDownloadEditTable()"
        />
        <hrdx-button
          [type]="'primary'"
          [title]="'Save'"
          size="default"
          [isLoading]="actionsLoadingState()['save']"
          (clicked)="onSaveEditTable()"
        />
      </div>
    </div>
  </ng-template>
</hrdx-modal>

<!-- for edit modal -->
<dynamic-no-data
  *ngIf="config.config?.edit"
  [(modalVisible)]="addNewItemToEditVisible"
  [config]="config"
  (tableSlected)="onUpdateDataEditTable($event)"
  [source]="config.sources"
  [defaultData]="defaultData()"
  [defaultFilterValue]="defaultFilterValue()"
  [formExtendData]="{
    dataTable: dataTableToEdit(),
    isModalVisible: addNewItemToEditVisible()
  }"
></dynamic-no-data>

<!-- for main table -->
<dynamic-no-data
  *ngIf="showAddNewItem()"
  [(modalVisible)]="addNewItemVisible"
  [config]="config"
  (tableSlected)="updateDataTable($event)"
  [source]="config.sources"
  [defaultData]="defaultData()"
  [defaultFilterValue]="defaultFilterValue()"
></dynamic-no-data>

<dynamic-form-dialog
  *ngIf="formDialog() as dialog"
  [visible]="dialog.visible"
  (visibleChange)="onFormDialogVisibleChange($event)"
  [config]="dialog.config"
  [settings]="dialog.settings"
  [value]="dialog.value ?? {}"
  (dialogSubmit)="handleFormDialogSubmit($event)"
></dynamic-form-dialog>
