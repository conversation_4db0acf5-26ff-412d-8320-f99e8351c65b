@import '../../../../../../../hrdx-design/src/themes/tokens.less';

@hover-background-tr: var(--hover-background-tr);
@hover-background-tr-start: var(--hover-background-tr-start);

:host {
  display: block;
  height: 100%;

  hrdx-pagination {
    padding: @spacing-3 @spacing-4;
    border-bottom-left-radius: @spacing-2;
    border-bottom-right-radius: @spacing-2;
  }

  dynamic-tool-table {
    display: flex;
    gap: @spacing-2;
    margin-bottom: @spacing-3;
  }

  .field-table {
    ::ng-deep .dynamic-form {
      background-color: unset;
      border-radius: unset;
    }
  }

  .insert-data-section {
    border: @size-1 solid @color-border-secondary;
    border-radius: @spacing-2;
    padding: @spacing-4;
    margin-bottom: @spacing-4;

    .actions {
      display: flex;
      justify-content: flex-end;
      gap: @spacing-2;
      margin-top: @spacing-4;
    }

    .validate-message {
      color: @color-text-error;
      margin-top: @spacing-2;
      animation: slideIn 0.3s;
    }

    // &:has(.validate-message) {
    //   border-color: @color-text-error;
    // }
  }
}

// .field-table-no-data {
//   border-left: 1px solid @color-border-primary;
//   border-right: 1px solid @color-border-primary;
//   border-bottom: 1px solid @color-border-primary;
//   ::ng-deep .ant-table-thead > tr > th {
//     border-bottom: unset;
//   }
// }
.hrdx-field-table {
  --hover-background-tr: @color-bg-surface-secondary;
  --hover-background-tr-start: white;

  ::ng-deep .hrdx-table .ant-table-header {
    overflow: hidden auto !important;
    padding-right: 0;
  }

  // border: 1px solid @color-border-primary;
  border-radius: @spacing-2;

  ::ng-deep.ant-table-tbody > tr:last-child > td {
    border-bottom: unset;
    border-bottom-left-radius: unset;
    border-bottom-right-radius: unset;
  }

  ::ng-deep.ant-table-thead > tr > th.action {
    display: none;
  }

  .ant-table-cell-fix-left-last::after {
    box-shadow: none;
  }

  ::ng-deep .dynamic-form {
    background: transparent !important;
  }

  ::ng-deep.ant-table-tbody > tr {
    position: relative;

    &.selected-row,
    &.selected-row:hover {
      --hover-background-tr-start: @color-bg-surface-active;
      --hover-background-tr: @color-bg-surface-active;
      background-color: @color-bg-surface-active;

      > .ant-table-cell-fix-left,
      > .ant-table-cell-fix-right,
      > td,
      > .action {
        background-color: @color-bg-surface-active;
      }
    }

    &:hover {
      > .action {
        opacity: 1;
        background: @hover-background-tr;
        display: flex;
        animation: fadeIn 0.3s;
        height: 98%;
      }
    }

    > .action {
      display: none;
      align-items: center;
      opacity: 0;
      position: absolute;
      right: 6px;
      padding: @spacing-2 0 @spacing-2 @spacing-4;
      gap: @spacing-basis;
      background: white;
      transition: all 0.3s;
      width: fit-content;
    }

    .row-action {
      display: flex;
      gap: @spacing-basis;
      justify-content: flex-end;
    }
  }

  ::ng-deep table > col {
    min-width: @spacing-8 * 5;
  }

  ::ng-deep .group-header {
    background-color: @color-grey-light1 !important;
    border-right: none !important;
  }

  ::ng-deep .ant-table-tbody .ant-table-row-expand-icon {
    display: none;
  }

  .ant-table-expand-icon {
    margin-right: @spacing-2;
  }

  .row-table {
    width: fit-content;
  }
}

.no-data-container {
  display: table-cell;
  vertical-align: middle;
  border-bottom-left-radius: @border-radius-base;
  border-bottom-right-radius: @border-radius-base;
  border-bottom: 0;
}

.no-data {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: @size-40 * 10;
  gap: @spacing-4;

  img {
    margin-bottom: @spacing-basis;
  }

  .no-result-title {
    font-size: @font-size-base;
    line-height: @font-line-height-base;
    color: @color-text-secondary;
    font-weight: 500;
  }
}

.selected-header {
  display: flex;
  height: 40px;
  align-items: center;
  padding: @spacing-4;
  color: white;
  gap: @spacing-2;

  > .count-selected {
    font-size: @font-size-base;
    font-weight: 500;
    flex: none;

    &::after {
      content: '|';
      margin-left: @spacing-basis;
    }
  }

  > .list-action {
    display: flex;
    gap: @spacing-2;
    flex: auto;
  }

  background-color: @color-bg-surface-info-fill;
}

.tool-field-table {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex: none;
  gap: @spacing-2;
  margin-bottom: @spacing-4;

  > .left {
    display: flex;
    justify-content: flex-start;
  }

  > .right {
    display: flex;
    align-items: center;
    gap: @spacing-3;
    justify-content: flex-end;
    width: 100%;
  }

  .input-group {
    width: @size-32 * 10;
  }
}

.tool-group {
  .filter-data {
    margin-bottom: @spacing-4;
  }
}

.dropdown-menu-action {
  max-height: @size-48 * 10;
  overflow: auto;
  display: flex;
  flex-direction: column;

  .dropdown-list {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: @spacing-2 @spacing-4;
    gap: @spacing-5;
  }
}

.dialog-tool-table-footer {
  gap: @spacing-2;
  display: flex;
  justify-content: space-between;

  .list-btn {
    gap: @spacing-2;
    display: flex;
  }
}

.edit-table-modal__footer {
  display: flex;
  justify-content: flex-end;

  .actions {
    gap: @spacing-2;
    display: flex;
  }
}

.filter-btn {
  margin-right: @spacing-6;
}

@keyframes fadeIn {
  from {
    opacity: 0.5;
    background-color: @hover-background-tr-start;
  }

  to {
    opacity: 1;
    background: @hover-background-tr;
  }
}
