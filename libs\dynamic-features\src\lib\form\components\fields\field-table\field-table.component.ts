import { CommonModule } from '@angular/common';
import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  computed,
  effect,
  EventEmitter,
  forwardRef,
  inject,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  Output,
  signal,
  SimpleChanges,
  SkipSelf,
  TemplateRef,
  viewChild,
  ViewChild,
  ViewChildren,
} from '@angular/core';
import { FormGroup, FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import {
  AdjustDisplayField,
  ButtonComponent,
  DataRenderComponent,
  DisplayComponent,
  FilterGroupComponent,
  IconComponent,
  ModalComponent,
  NewTableComponent,
  NewTableModule,
  ToastMessageComponent,
} from '@hrdx/hrdx-design';
import * as HandleBars from 'handlebars';
import { isArray, isBoolean, isNil, merge, pick } from 'lodash';
import { NzSafeAny } from 'ng-zorro-antd/core/types';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzSwitchComponent } from 'ng-zorro-antd/switch';
import { NzTableModule } from 'ng-zorro-antd/table';
import {
  BehaviorSubject,
  catchError,
  combineLatest,
  debounceTime,
  distinctUntilChanged,
  filter,
  finalize,
  firstValueFrom,
  map,
  of,
  skip,
  Subject,
  Subscription,
  switchMap,
  takeUntil,
  tap,
} from 'rxjs';
import { mergeTableData } from '../../../../utilities';
import { FormComponent } from '../../../form.component';
import { Field, FieldConfig, Source, Values } from '../../../models';
import { DynamicFormService } from '../../../services/dynamic-form.service';
import { FormControlService } from '../../../services/form-control.service';
import { FieldCalendarComponent } from '../field-calendar/field-calendar.component';
import { FieldNoDataComponent } from '../field-no-data/field-no-data.component';
import { filterData } from '../field-tree-table/services/format-data';
import { ToolTableComponent } from './components/tool-table/tool-table.component';
import {
  ActionConfig,
  CellAction,
  DialogType,
  FieldTable,
  FieldTableConfigI,
  FormConfig,
  GroupedData,
  LayoutButton,
  RecordElementsState,
  SortOption,
  ToolTableOptions,
} from './field-table.models';
import { HttpClient } from '@angular/common/http';
import { NoDataComponent } from './components/no-data/no-data.component';
import { CellFieldComponent } from './cell-field/cell-field.component';
import { FormDialogComponent } from './components/form-dialog/form-dialog.component';
import {
  createResizeObserverObservable,
  getFirstElementInSelectorList,
} from '../../../utils';

@Component({
  selector: 'dynamic-field-table',
  standalone: true,
  imports: [
    CommonModule,
    NewTableModule,
    DisplayComponent,
    NzTableModule,
    ToolTableComponent,
    FormsModule,
    NzIconModule,
    IconComponent,
    ButtonComponent,
    ModalComponent,
    NzInputModule,
    NzDropDownModule,
    NzSwitchComponent,
    FilterGroupComponent,
    ButtonComponent,
    forwardRef(() => FormComponent),
    FieldNoDataComponent,
    DataRenderComponent,
    NewTableComponent,
    FieldCalendarComponent,
    NoDataComponent,
    CellFieldComponent,
    FormDialogComponent,
  ],
  providers: [ModalComponent],
  templateUrl: './field-table.component.html',
  styleUrl: './field-table.component.less',
})
export class FieldTableComponent
  implements Field, AfterViewInit, OnChanges, OnInit, OnDestroy
{
  readonly defaultRowActionTitle = {
    edit: 'Edit',
    delete: 'Delete',
    view: 'View',
    lock: 'Lock',
    unlock: 'Unlock',
    duplicate: 'Duplicate',
  } as Record<string, string>;

  constructor(
    @SkipSelf() private formControlService: FormControlService,
    private cdr: ChangeDetectorRef,
  ) {
    effect(
      async () => {
        if (!this.haveTransformDisabledRowActions()) return;
        const disabledState = await this.getDisabledStateByActions(
          this.actionRow() ?? [],
          this.dataTable(),
        );
        this.recordDisabledActionsState.set(disabledState);
      },
      { allowSignalWrites: true },
    );

    effect(
      async () => {
        if (!this.haveTransformDisabledRowActions() || !this.editTableConfig())
          return;
        const disabledState = await this.getDisabledStateByActions(
          this.actionRow() ?? [],
          this.dataTableToEdit(),
        );
        this.editTableDisabledActionsState.set(disabledState);
      },
      { allowSignalWrites: true },
    );
  }

  editTableDisabledActionsState = signal<Record<string, { disabled?: boolean }>[]>([]);

  haveTransformDisabledRowActions = computed(() => {
    const buttons = (this.actionRow() ?? []) as LayoutButton[];
    return buttons?.some((button) => !!button._disabled?.transform);
  });

  modalComponent = inject(ModalComponent);
  toast = inject(ToastMessageComponent);
  http = inject(HttpClient);

  @ViewChildren('formObj') dynamicForms?: FormComponent[];
  @ViewChild('formFilter') filterForm?: FormComponent;
  @ViewChild('actionButtonForm') actionButtonForm?: FormComponent;
  @ViewChild('modalContent') modalContent!: TemplateRef<''>;
  @Input() values: Values = {};
  config!: FieldTableConfigI;
  service = inject(DynamicFormService);

  dataTable = signal<FieldTable['dataSource'][]>([]);
  defaultDataTable = signal<FieldTable['dataSource'][]>([]);
  columnTable = signal<FieldTable['columns']>([]);
  defaultFilterValue = signal<NzSafeAny>({});
  defaultData = signal<NzSafeAny>({});

  search = signal<string>('');
  private searchSubject = new Subject<string>();

  ngOnChanges(changes: SimpleChanges) {
    if (changes['values']) {
      this.values$.next(this.values);
    }
  }

  checkPermissionToolTable = (
    action: keyof ToolTableOptions,
    permissionToCheck?: string,
  ) => {
    if (!this.config?.config?.[action]) return false;
    if (this.config?.checkPermissionEnabled)
      return (
        this.values.checkPermissionFn?.(permissionToCheck ?? action) ?? false
      );
    return true;
  };

  get insertDataFormExtendData() {
    return {
      ...this.values?.extend,
      values: this.values,
    };
  }

  private insertDataForm = viewChild<FormComponent>('insertDataForm');
  resetInsertDataForm = signal(false);
  insertDataValidateMessage = signal<string | null>(null);
  insertDataFormValue$ = new Subject<string>();

  onInsertDataFormValueChanges = async (value: NzSafeAny) => {
    const form = this.insertDataForm();
    if (!form?.valid) {
      this.insertDataValidateMessage.set(null);
      return;
    }

    const { transformDataExpression, checkDuplicate } =
      this.config?.insertDataConfig ?? {};

    const newData = await this.transformInsertData(
      value,
      transformDataExpression,
    );
    if (
      checkDuplicate?.by &&
      Array.isArray(newData) &&
      this.isDuplicateFromInsertData(newData, checkDuplicate.by)
    ) {
      this.setInsertDataValidateMessage(checkDuplicate?.validateMessage);
    } else {
      this.insertDataValidateMessage.set(null);
    }
  };

  transformInsertData = (value: NzSafeAny, tranformExpression?: string) => {
    return this.service.getJsonataExpression({})(
      tranformExpression ?? '$.formValue',
      { formValue: value, values: this.values },
    );
  };

  setInsertDataValidateMessage = (message?: string) => {
    this.insertDataValidateMessage.set(
      message ?? 'There are some duplicate data, please check the input again.',
    );
  };

  isDuplicateFromInsertData = (valueToCheck: NzSafeAny[], byKeys: string[]) => {
    const currentTableData = this.dataTable() ?? [];
    if (currentTableData.length <= 0 || valueToCheck.length <= 0) return false;
    return currentTableData.some((item) => {
      return valueToCheck.some((newItem) => {
        return byKeys.every((key) => newItem[key] === item[key]);
      });
    });
  };

  insertDataSubmit = async () => {
    const form = this.insertDataForm();
    const insertDataConfig = this.config?.insertDataConfig;
    if (!form || !insertDataConfig) return;
    if (!form.valid) {
      form.setFormTouched();
      return;
    }
    const {
      transformDataExpression,
      checkDuplicate,
      insertPosition = 'start',
    } = insertDataConfig;

    const newData = await this.transformInsertData(
      form.value,
      transformDataExpression,
    );
    if (!Array.isArray(newData)) {
      console.error('insert data after transform must be array', newData);
      return;
    }

    if (
      checkDuplicate?.by &&
      this.isDuplicateFromInsertData(newData, checkDuplicate.by)
    ) {
      this.setInsertDataValidateMessage(checkDuplicate?.validateMessage);
      return;
    } else {
      this.insertDataValidateMessage.set(null);
    }

    const currentTableData = this.dataTable() ?? [];
    if (this.config.sortOption) {
      this.dataTable.update(() =>
        this.sortData([...currentTableData, ...newData]),
      );
    } else if (insertPosition === 'end') {
      this.dataTable.update(() => [...currentTableData, ...newData]);
    } else {
      this.dataTable.update(() => [...newData, ...currentTableData]);
    }

    this.resetInsertDataForm.update((v) => !v);
    form.setFormUnTouched();
  };

  ngOnInit() {
    this.searchSubject.pipe(debounceTime(300)).subscribe((value) => {
      this.search.set(value);
    });

    this.insertDataFormValue$.pipe(debounceTime(300)).subscribe((value) => {
      this.onInsertDataFormValueChanges(value);
    });
  }

  private readonly destroy$ = new Subject<void>();
  ngOnDestroy() {
    this.destroy$.next(); // Emit to signal teardown
    this.destroy$.complete(); // Complete the destroy observable
    this.fieldsUnvisibleSubscription?.unsubscribe();
  }

  getActionOneTitle(action: LayoutButton) {
    if (action.title) return action.title;
    return this.defaultRowActionTitle[action.id] ?? null;
  }

  tableId = computed(() => {
    return this.config.tableId ?? `table-${Date.now()}`;
  });

  effectDataTable = effect(() => {
    this.group.get(this.config.name)?.setValue(this.dataTable());
  });

  private readonly destroySetDefaultValue$ = new Subject<void>();

  ngAfterViewInit(): void {
    this.dataTable.set(this.config.dataSource ?? this.config.value);
    this.columnTable.set(this.config.columns);

    const dependantField = this.config.dependantField;
    const formType = this.values?.extend?.['formType'];
    const defaultValue = this.values?.extend?.['defaultValue'];
    const dependantFieldSkip = this.config.dependantFieldSkip; // TODO: Refactor DependantField
    if (dependantField) {
      combineLatest({
        values: this.values$,
        confirmPopupChanges:
          this.formControlService?.confirmPopupBehavior$ ?? of(true),
      })
        .pipe(
          filter(({ confirmPopupChanges }) => confirmPopupChanges),
          map(({ values }) => values),

          distinctUntilChanged((prev, curr) =>
            this.service.distinct(prev, curr, {
              transform: dependantField,
            }),
          ),
          skip(
            this.formControlService.getSkip(
              formType,
              dependantField,
              defaultValue,
              dependantFieldSkip,
            ),
          ),
          tap(() => {
            this.dataTable.set([]);
          }),
          tap(() => this.cdr.detectChanges()),
        )
        .subscribe();
    }

    const _defaultFilter = this.config._defaultFilterValue;
    if (_defaultFilter) {
      this.values$
        .pipe(
          distinctUntilChanged((prev, curr) =>
            this.service.distinct(prev, curr, _defaultFilter),
          ),
          switchMap((values) => {
            return this.service.getObservable(
              values.function,
              values,
              _defaultFilter,
            );
          }),
          tap(() => this.cdr.detectChanges()),
        )
        .subscribe((value) => {
          this.defaultFilterValue.set(value);
        });
    }

    const _defaultData = this.config._defaultData;
    if (_defaultData) {
      this.values$
        .pipe(
          distinctUntilChanged((prev, curr) =>
            this.service.distinct(prev, curr, _defaultData),
          ),
          switchMap((values) => {
            return this.service.getObservable(
              values.function,
              values,
              _defaultData,
            );
          }),
          tap(() => this.cdr.detectChanges()),
        )

        .subscribe((value) => {
          this.defaultData.set(value);
        });
    }

    if (this.config._defaultDataSource)
      combineLatest({
        _value: this.values$.pipe(
          distinctUntilChanged((prev, curr) => {
            return this.service.distinct(
              prev,
              curr,
              this.config._defaultDataSource,
            );
          }),
          takeUntil(this.destroySetDefaultValue$),
          switchMap(() =>
            this.service.getObservable(
              this.values.function,
              this.values,
              this.config._defaultDataSource,
            ),
          ),
          tap(() => this.cdr.detectChanges()),
        ),
      })
        .pipe(
          map(({ _value }) => {
            return _value;
          }),
          tap(() => this.cdr.detectChanges()),
        )
        .subscribe((value) => {
          if (Array.isArray(value) && value?.length > 0) {
            this.dataTable.set(value);
            this.destroySetDefaultValue$.next();
            this.destroySetDefaultValue$.complete();
          }
        });
    // this.group.get(this.config.name)?.valueChanges.subscribe((value) => {
    //   this.dataTable.set(value ?? []);
    // });
    if (this.config._dataSource) {
      this.values$
        .pipe(
          distinctUntilChanged((prev, curr) =>
            this.service.distinct(prev, curr, this.config._dataSource),
          ),
          switchMap((values) => {
            return this.service.getObservable(
              values.function,
              {
                ...values,
                value: this.dataTable(),
              },
              this.config._dataSource,
            );
          }),
          tap((value) => {
            if (this.config._dataSourceRequestStatus) {
              const requestStatus =
                this.values.variables?.[
                  this.config._dataSourceRequestStatus + '_requestStatus'
                ].requestStatus;
              if (requestStatus === 'loading') this.loading.set(true);
              if (requestStatus === 'success' || requestStatus === 'error')
                this.loading.set(false);
            }
            if (!isNil(value)) {
              this.dataTable.set(value);
            }
          }),
          finalize(() => {
            this.cdr.detectChanges();
          }),
        )
        .subscribe();
    }
    if (this.config._column) {
      this.values$
        .pipe(
          distinctUntilChanged((prev, curr) =>
            this.service.distinct(prev, curr, this.config._column),
          ),
          switchMap((values) =>
            this.service.getObservable(
              values.function,
              values,
              this.config._column,
            ),
          ),
          tap(() => this.cdr.detectChanges()),
        )
        .subscribe((value) => {
          this.columnTable.set(value);
        });
    }
    if (this.config._row_actions) {
      this.values$
        .pipe(
          distinctUntilChanged((prev, curr) =>
            this.service.distinct(prev, curr, this.config._row_actions),
          ),
          switchMap((values) =>
            this.service.getObservable(
              values.function,
              values,
              this.config._row_actions,
            ),
          ),
          tap(() => this.cdr.detectChanges()),
        )
        .subscribe((value) => {
          this._actionRow.set(value);
        });
    }

    if (this.config.adjustHeight) {
      const { elementSelector, difference } = this.config.adjustHeight;
      this.adjustHeightBaseWrapperElement(elementSelector, difference);
    }
  }

  scrollHeight = signal('500px');
  private adjustHeightBaseWrapperElement(
    selectors: string | string[],
    difference = 0,
  ) {
    if (typeof selectors === 'string') {
      selectors = [selectors];
    }
    const wrapperElement = getFirstElementInSelectorList(selectors);
    if (!wrapperElement) return;
    createResizeObserverObservable(wrapperElement)
      .pipe(
        takeUntil(this.destroy$),
        debounceTime(200), // Debounce to limit the number of events
        map((entries) => entries[0]?.contentRect), // Extract contentRect for size info
        distinctUntilChanged((prev, curr) => prev?.height === curr?.height),
        map((contentRect) => contentRect.height),
      )
      .subscribe((height) => {
        this.scrollHeight.set(`${height + difference}px`);
      });
  }

  sortData(data: Record<string, unknown>[]) {
    if (this.config?.sortOption) {
      const sortOption: SortOption = this.config?.sortOption;
      const sortData = data.sort((a, b) => {
        const valueA = a[sortOption.key] as any;
        const valueB = b[sortOption.key] as any;
        if (
          !sortOption.orderOption ||
          sortOption.orderOption.trim().toLowerCase() == 'asc'
        ) {
          return valueA > valueB ? 1 : -1;
        }
        if (sortOption.orderOption.trim().toLowerCase() == 'desc') {
          return valueA < valueB ? 1 : -1;
        }
        return 0;
      });
      return sortData;
    }
    return data;
  }

  valueChange(event: NzSafeAny) {
    const { id, value } = event;

    const data = { [id]: value };

    const currentData = this.group.get(this.config.name)?.value || {};

    const updatedData = { ...currentData, ...data };

    this.group.get(this.config.name)?.setValue(updatedData);
  }

  group!: FormGroup;
  field!: FieldConfig;
  values$ = new BehaviorSubject<Values>(this.values);
  allChecked = false;
  indeterminate = false;
  searchValue = signal<string>('');
  setOfSelectedItem = new Set<string>();

  loading = signal(false);
  //action button
  isActionBtn = false;
  filterValue = signal<NzSafeAny>(null);
  dialogType = signal<DialogType>('create');
  dialogTitle = signal<string>('View');
  dialogConfig = signal<FormConfig>({});
  dialogVisible = signal(false);
  dialogValue = signal<FieldTable['dataSource'] | null | undefined>(null);

  listOfSelectedItems = signal<FieldTable['dataSource'][]>([]);
  selectedItem = signal<FieldTable['dataSource'] | null>(null);

  pageIndex = signal<number>(1);
  pageSize = signal<number>(25);

  @ViewChild('formObj') dynamicForm?: FormComponent;

  showCollapse = computed(() => {
    return this.config.layout_option?.tool_table?.collapse ?? false;
  });

  showSaveAddButton = computed(() => {
    return this.config.layout_option?.show_dialog_form_save_add_button ?? false;
  });

  showDeleteButton = computed(() => {
    return this.config.layout_option?.show_dialog_form_delete_button ?? false;
  });

  showAddNewValue = computed(() => {
    return this.config.add_new_value ?? false;
  });

  hiddenHeader = computed(() => {
    return this.config?.layout_option?.tool_table?.hidden_header ?? false;
  });

  hideRowAction = computed(() => {
    if (this.isReadOnly()) return true;
    return this.config?.layout_option?.hide_action_row ?? false;
  });

  showTableFilter = computed(() => {
    return this.config?.layout_option?.tool_table?.show_table_filter ?? false;
  });

  showTableSearch = computed(() => {
    return this.config?.layout_option?.tool_table?.show_table_search ?? false;
  });

  expandFilter = computed(() => {
    return this.config?.layout_option?.tool_table?.expand_filter ?? false;
  });

  showTableGroup = computed(() => {
    return this.config?.layout_option?.tool_table?.show_table_group ?? false;
  });

  showCheckbox = computed(() => {
    const { extend } = this.values ?? {};
    const formType = extend?.['formType'] || 'create';

    const isViewMode = formType === 'view';
    if (isViewMode) return false;
    return this.config?.layout_option?.tool_table?.show_table_checkbox ?? false;
  });

  _actionRow = signal<NzSafeAny[] | null>(null);

  actionRow = computed(() => {
    return this?._actionRow() ?? this.config?.layout_option?.action_row;
  });

  actionsMany = computed(() => {
    return this.config?.layout_option?.actions_many;
  });

  showActionsMany = computed(() => {
    return this.config?.layout_option?.show_actions_many ?? true;
  });

  hiddenActionRow = computed(() => {
    return this.config?.layout_option?.hide_action_row ?? false;
  });

  disabledClickRow = computed(() => {
    return this.config?.layout_option?.disabled_click_row ?? false;
  });

  showDetailHistory = computed<boolean>(() => {
    return this.config.layout_option?.show_detail_history ?? true;
  });

  isLocalPagination = computed(
    () => this.config.localPagination && !this.isReadOnly(),
  );

  showPagination = computed<boolean>(() => {
    if (this.isLocalPagination()) return true;
    return this.config.layout_option?.show_pagination ?? true;
  });

  pageSizeOptions = computed(() => {
    return this.config.localPagination?.pageSizeOptions ?? [25, 50, 100, 250];
  });

  showRowIndex = computed(
    () => this.config.layout_option?.show_row_index ?? false,
  );

  haveChildItemCheckbox = computed<boolean>(() => {
    return (
      this.config?.layout_option?.tool_table?.have_child_item_checkbox ?? false
    );
  });

  rowActionId = computed<string>(() => {
    return this.config.rowIdName ?? 'id';
  });

  actionRowDisabled = computed<string[]>(() => {
    return this.config?.layout_option?.action_row_disabled ?? [];
  });

  mapRowIds = computed<string[]>(() => {
    return this.config?.mapRowName ?? [];
  });

  showToolTable = computed(() => {
    const config = this.config?.config ?? {};
    const configKeys = Object.keys(
      config,
    ) as (keyof FieldTableConfigI['config'])[];
    return configKeys.some((key) => config[key]);
  });

  distinctByKey = computed(() => this.config?.distinctByKey ?? 'id');

  recordDisabledActionsState = signal<Record<string, { disabled?: boolean }>[]>([]);
  private getDisabledStateByActions = async (
    actionsConfig: LayoutButton[],
    data: any[] = [],
  ) => {
    const expressionFunc = this.service.getJsonataExpression({});
    const res = await Promise.all(
      data.map(
        async (item) =>
          await Promise.all(
            actionsConfig?.map((action) => {
              const transform = action._disabled?.transform;
              if (!transform) return false;
              return expressionFunc(transform, item) as Promise<boolean>;
            }),
          ),
      ),
    );

    return res.map((stateArr) =>
      actionsConfig.reduce(
        (acc: Record<string, { disabled?: boolean }>, action, index) => {
          acc[action.id] = { disabled: stateArr[index] ?? false };
          return acc;
        },
        {},
      ),
    );
  };

  onSearchValueChange(value: NzSafeAny) {
    this.searchSubject.next(value);
  }

  onPrecheck(
    transform: Source,
    values: NzSafeAny,
    onError?: (err: unknown) => void,
  ) {
    return this.service.getValueFromApi(transform, values, { onError });
  }

  handleClickHyperlink(
    data: NzSafeAny,
    event: Event,
    column: { actionRow: NzSafeAny },
  ) {
    event.stopPropagation();

    const actionRow = column?.actionRow;
    switch (actionRow?.action) {
      case 'download':
        this.service
          .downloadFileById(actionRow?.baseUrl ?? '', data['jobRequestId'])
          .subscribe();
        break;

      default: {
        const url = HandleBars.compile(actionRow?.baseUrl)(data);
        window.open(url);
        break;
      }
    }
  }

  // dialog action
  dialogSubmit(value: {
    type: DialogType | 'toDelete' | 'toEdit';
    value: NzSafeAny;
  }) {
    const rowId = this.rowActionId();
    const mapRowIds = this.mapRowIds();

    switch (value.type) {
      case 'create':
        this.toast.showToast('success', 'Success', 'Saved Successfully');
        break;
      case 'edit': {
        const data = this.dataTable();
        const selectedItem = this.selectedItem();
        const updatedItem = merge({}, selectedItem, value.value);

        const isMatchingRow = (item: FieldTable['dataSource']) => {
          if (Array.isArray(mapRowIds)) {
            return mapRowIds.every((key) => item[key] === updatedItem[key]);
          }

          return item[rowId] === updatedItem[rowId];
        };

        const updatedData = data.map((item) => {
          if (isMatchingRow(item) && item['isDuplicate']) {
            return { ...item, ...updatedItem };
          }
          return item;
        });

        this.dataTable.set([...updatedData]);
        this.group.get(this.config.name)?.setValue(updatedData);

        this.toast.showToast('success', 'Success', 'Saved Successfully');
        break;
      }
      case 'proceed':
        this.toast.showToast('success', 'Success', 'Saved Successfully');
        break;
      case 'filter':
        this.filterValue.set(value.value);
        break;
      case 'toEdit': {
        this.dialogType.set('edit');
        this.dialogTitle.set('Edit');
        break;
      }
      case 'toDelete': {
        const id = this.selectedItem()?.[rowId];
        if (id) this.deleteClickOne(id);
        break;
      }
    }
  }

  // Create new record, keep dialog show and reset form to user continue create another record
  onSaveAndAddNew = () => {
    this.dialogSubmit({
      value: this.dynamicForm?.value,
      type: this.dialogType(),
    });
    this.reset.update((val) => !val);
  };

  onEdit = () => {
    this.dialogSubmit({
      value: this.dialogValue(),
      type: 'toEdit',
    });
  };

  onDelete = () => {
    this.dialogSubmit({
      value: this.dialogValue(),
      type: 'toDelete',
    });
  };

  isButtonLoading = signal(false);
  onOk = () => {
    const handleOnOk = () => {
      this.dataTable.update((table) => {
        const index = table.findIndex(
          (i) =>
            i[this.rowActionId()] === this.selectedItem()?.[this.rowActionId()],
        );
        table[index] = { ...table[index], ...this.dynamicForm?.value };
        return this.sortData(table);
      });
      this.dialogSubmit({
        value: this.dynamicForm?.value,
        type: this.dialogType(),
      });
      this.dialogVisible.set(false);
    };

    const currFormType = this.dialogType();
    const precheckConfig = this.config?.layout_option?.action_row?.find(
      (row) => row.id === currFormType,
    )?.precheck;

    if (precheckConfig?.source) {
      const onError = (err: NzSafeAny) => {
        this.toast.showToast(
          'error',
          'Error',
          err.error?.message ?? err.error?.messageError ?? '',
        );
      };

      this.isButtonLoading.set(true);
      this.onPrecheck(
        precheckConfig.source,
        { ...this.dynamicForm?.value, onError },
        onError,
      ).subscribe((res) => {
        this.isButtonLoading.set(false);
        if (!res) return;
        handleOnOk();
      });
    } else {
      handleOnOk();
    }
  };
  // end define primary buttons

  deleteClickOne(id: string, e?: Event, row?: NzSafeAny, index = 0) {
    if (e) e.stopPropagation();
    this.modalComponent.showDialog({
      nzTitle: 'Delete the selected record?',
      nzContent: 'Are you sure you want to delete this information?',
      nzWrapClassName: 'popup popup-error',
      nzIconType: 'icons:trash-bold',
      nzOkText: 'Delete',
      nzCancelText: 'Cancel',

      nzOnOk: () => {
        const data = this.dataTable();
        const mapRowIds = this.mapRowIds();
        const rowId = this.rowActionId();
        const result = row ? pick(row, mapRowIds) : {};

        let filteredData: any[] = [];
        // if id is not defined, delete item base index
        if (isNil(id)) {
          filteredData = [...data.slice(0, index), ...data.slice(index! + 1)];
        } else {
          filteredData = data.filter((item) => {
            const isMatchingRoleId = Array.isArray(mapRowIds)
              ? mapRowIds.every((key) => item[key] === result[key as NzSafeAny])
              : item[rowId] === id;

            // const isDuplicate = item['isDuplicate'];
            // return !(isMatchingRoleId && isDuplicate);
            return !isMatchingRoleId;
          });
        }
        this.dataTable.set(filteredData);
        this.group.get(this.config.name)?.setValue(filteredData);
        this.toast.showToast('success', 'Success', 'Deleted Successfully');
      },
    });

    return;
  }

  shouldDisableDelete(row: FieldTable['dataSource']): boolean {
    return !row?.['isDuplicate'];
  }

  onActionOneClick(
    row: FieldTable['dataSource'],
    actionId: string,
    event: Event,
    index?: number,
  ) {
    const rowId = this.rowActionId();
    const id = row[rowId];

    switch (actionId) {
      case 'delete': {
        this.deleteClickOne(id, event, row, index);
        break;
      }
      case 'edit': {
        this.editClickOne(id, row, event);
        break;
      }
      case 'lock-keyhole': {
        break;
      }
      case 'unlock-keyhole': {
        break;
      }
      case 'view': {
        this.viewClickOne(id, row, event);
        break;
      }
      case 'duplicate': {
        this.duplicateClickOne(id, event, row);
        break;
      }
      default: {
        this.viewClickOne(id, row, event);
        break;
      }
    }
  }

  historyDialogVisible = false;
  showHistoryClickOne(value: FieldTable['dataSource']) {
    this.selectedItem.set(value);
    this.historyDialogVisible = true;
    return;
  }

  viewClickOne(id: string, value: NzSafeAny, e?: Event) {
    if (e) e.stopPropagation();
    this.selectedItem.set(value);

    this.dialogType.set('view');
    this.dialogTitle.set('View');
    this.dialogConfig.set(this.config?.form_config ?? {});
    this.dialogValue.set(value);

    this.dialogVisible.set(true);
    return;
  }

  addNewValue(e?: Event) {
    if (e) e.stopPropagation();

    this.dialogType.set('create');
    this.dialogTitle.set('Create');
    this.dialogConfig.set(this.config?.form_config ?? {});

    this.dialogVisible.set(true);

    return;
  }

  editClickOne(id: string, value: NzSafeAny, e?: Event) {
    if (e) e.stopPropagation();
    this.selectedItem.set(value);

    this.dialogType.set('edit');
    this.dialogTitle.set('Edit');
    this.dialogConfig.set(this.config?.form_config ?? {});
    this.dialogValue.set(value);

    this.dialogVisible.set(true);

    return;
  }
  router = inject(Router);

  duplicateClickOne(id: string, event: Event, row?: NzSafeAny) {
    event?.stopPropagation();

    const data = this.dataTable();
    const rowId = this.rowActionId();
    const mapRowIds = this.mapRowIds();
    const result = row ? pick(row, mapRowIds) : {};
    const action_row = this.config?.layout_option?.action_row;

    const duplicatedItem = data.find((item) => {
      if (Array.isArray(mapRowIds)) {
        return mapRowIds.every((key) => item[key] === result[key]);
      }
      return item[rowId] === id;
    });

    if (!duplicatedItem) return;

    const duplicateAction = action_row?.find(
      (action) => action.id === 'duplicate',
    );

    const duplicatedItemCopy = {
      ...duplicatedItem,
      ...(duplicateAction?.appendObject ?? {}),
      [this.rowActionId()]: `_${this.dataTable().length + 1}`,
      isDuplicate: true,
      ...(duplicateAction?.appendAction ?? {}),
    };

    this.dataTable.set([...data, duplicatedItemCopy]);
    this.group.get(this.config.name)?.setValue(this.dataTable);
  }

  mergeActions(row: NzSafeAny) {
    return [...(this.actionRow() ?? []), ...(row['action'] || [])];
  }

  handleClickRow(row: FieldTable['dataSource']) {
    if (this.disabledClickRow()) return;

    const actionRow = this.config?.actionClickRow;
    if (!actionRow) return;
    switch (actionRow?.action) {
      case 'download':
        if (actionRow.key && row[actionRow.key] === actionRow.condition) {
          this.service
            .downloadFileById(actionRow?.baseUrl ?? '', row['jobRequestId'])
            .subscribe();
        }
        break;

      default: {
        const url = HandleBars.compile(actionRow?.baseUrl)({ row });
        window.open(url);
        break;
      }
    }
  }

  reset = signal(false);
  onReset = () => {
    this.reset.set(true);
  };

  _value = computed(() => {
    if (this.reset()) return null;
    return this.dialogValue();
  });

  disabledFields = signal<string[]>([]);
  mappingDisabledFields = computed(() =>
    this.disabledFields().map((f) => ({ name: f })),
  );

  settingConfig = computed(() => this.dialogType() === 'filter');

  realConfig = computed(() => {
    if (!this.settingConfig()) return this.dialogConfig();
    const tmp = structuredClone(this.dialogConfig());
    if (!tmp?.fields) return undefined;
    tmp.fields = tmp.fields.map((f) => ({
      ...f,
      _condition: {
        transform: `$not($exists($.extend.disabledFields[name = '${f.name}']))`,
      },
    }));
    return tmp;
  });

  isValidFilterValue = computed(() => {
    const filterValue = this.filterValue();
    if (filterValue === null || filterValue === undefined) {
      return false;
    }

    if (Array.isArray(filterValue) && filterValue.length === 0) {
      return false;
    }

    if (typeof filterValue === 'object' && filterValue !== null) {
      if (Object.keys(filterValue).length === 0) {
        return false;
      }
      const hasNonEmptyValues = Object.values(filterValue).some(
        (value) =>
          value !== null &&
          value !== '' &&
          value !== undefined &&
          Array.isArray(value) &&
          value.length > 0,
      );
      return hasNonEmptyValues;
    }

    return true;
  });

  filterDialog = false;
  filterItem(value: NzSafeAny) {
    this.dialogType.set('filter');
    this.dialogConfig.set((this.config?.filter_config as NzSafeAny) ?? {});
    this.dialogVisible.set(true);
    this.dialogValue.set(value);
    this.dialogTitle.set('Filters');
    return;
  }

  groupLabel = '';
  groupItem(event: string) {
    const columns = this.columnTable();
    this.groupLabel =
      columns.find((column: { code: string }) => column.code === event)
        ?.title ?? '';
    this.groupTableData(event);
  }

  removedGroupByKey() {
    this.groupedData = [];
  }

  filterClickOne(value: NzSafeAny) {
    this.dialogType.set('filter');
    return;
  }

  fieldsUnvisibleSubscription?: Subscription;
  fieldsUnvisibleEffect = effect(() => {
    this.fieldsUnvisibleSubscription?.unsubscribe();
    this.fieldsUnvisibleSubscription = new Subscription();
    const dataTable = this.dataTable();
    const unvisibleFields =
      this.columns()?.filter((field) => field.unvisible) ?? [];
    // create subscription value change for unvisible fields
    dataTable?.forEach((item, idx) => {
      unvisibleFields.forEach((field) => {
        const _value = field?.display_type?._value;
        if (_value) {
          this.fieldsUnvisibleSubscription?.add(
            this.values$
              .pipe(
                distinctUntilChanged((prev, curr) => {
                  return this.service.distinct(prev, curr, _value);
                }),
                switchMap((values) =>
                  this.service.getObservable(
                    values.function,
                    {
                      ...values,
                      extend: {
                        ...values.extend,
                        index: idx,
                      },
                    },
                    _value,
                  ),
                ),
                // finalize(() => this.cdr.detectChanges()),
              )
              .subscribe((value) => {
                if (!isNil(value)) {
                  item[field.code] = value;
                  this.group.get(this.config.name)?.setValue(this.dataTable());
                }
              }),
          );
        }
      });
    });
  });

  headersVisible = signal<FieldTable['columns']>([]);
  columns = signal<FieldTable['columns']>([]);
  columnsEffect = effect(
    async () => {
      const columns = this.columnTable();

      const fieldsVisible = await Promise.all(
        columns.map(async (field) => {
          if (!field._condition) return true;
          const visible = await this.service.getJsonataExpression({})(
            field._condition.transform ?? '',
            this.values,
          );
          return visible;
        }),
      );

      const newColumns = columns.map((field, index) => {
        if (field?.display_type?._select) {
          field.display_type['options$'] = this.values$.pipe(
            distinctUntilChanged((prev, curr) => {
              return this.service.distinct(
                prev,
                curr,
                field?.display_type?._select,
              );
            }),
            switchMap((values) =>
              this.service.getObservable(
                values.function,
                values,
                field?.display_type?._select,
              ),
            ),
            map((value) => {
              if (isArray(value)) {
                return value;
              }
              return [];
            }),
            finalize(() => this.cdr.detectChanges()),
          );
        }
        if (field.display_type?._readOnly) {
          field.display_type['readOnly$'] = (idx: number) => {
            return this.values$.pipe(
              distinctUntilChanged((prev, curr) => {
                return this.service.distinct(
                  prev,
                  curr,
                  field?.display_type?._readOnly,
                );
              }),
              switchMap((values) =>
                this.service.getObservable(
                  values.function,
                  {
                    ...values,
                    extend: {
                      ...values.extend,
                      index: idx,
                    },
                  },
                  field?.display_type?._readOnly,
                ),
              ),
              map((value) => {
                if (isBoolean(value)) {
                  return value;
                }
                return field.display_type['readOnly'] ?? false;
              }),
              finalize(() => this.cdr.detectChanges()),
            );
          };
        }
        if (field.display_type?._disabled) {
          field.display_type['disabled$'] = (idx: number) => {
            return this.values$.pipe(
              distinctUntilChanged((prev, curr) => {
                return this.service.distinct(
                  prev,
                  curr,
                  field?.display_type?._disabled,
                );
              }),
              switchMap((values) =>
                this.service.getObservable(
                  values.function,
                  {
                    ...values,
                    extend: {
                      ...values.extend,
                      index: idx,
                    },
                  },
                  field?.display_type?._disabled,
                ),
              ),
              map((value) => {
                if (isBoolean(value)) {
                  return value;
                }
                return field.display_type['disabled'] ?? false;
              }),
              finalize(() => this.cdr.detectChanges()),
            );
          };
        }
        if (field.display_type?._value) {
          field.display_type['value$'] = (idx: number) => {
            return this.values$.pipe(
              distinctUntilChanged((prev, curr) => {
                return this.service.distinct(
                  prev,
                  curr,
                  field?.display_type?._value,
                );
              }),
              switchMap((values) =>
                this.service.getObservable(
                  values.function,
                  {
                    ...values,
                    extend: {
                      ...values.extend,
                      index: idx,
                    },
                  },
                  field?.display_type?._value,
                ),
              ),
              map((value) => {
                if (!isNil(value)) {
                  return value;
                }
                return null;
              }),
              finalize(() => this.cdr.detectChanges()),
            );
          };
        }
        return field;
      });

      this.columns.set(
        newColumns.filter(
          (field, index) =>
            fieldsVisible[index] && !this.headersVisible().includes(field.code),
        ),
      );
    },
    { allowSignalWrites: true },
  );

  transformHeaders = computed(() => {
    const fields = this.columns();
    return fields.map((field) => ({
      id: field.code,
      title: field.title,
      display: true,
      disabled: field.pinned ?? false,
    })) as AdjustDisplayField[];
  });

  displayColumns = signal<FieldTable['columns']>([]);
  adjustFields = signal<AdjustDisplayField[] | null>(null);
  displayColumnsEffect = effect(
    () => {
      const config = this.config.config;
      const columns = this.columns();
      const adjustFields = this.adjustFields();
      if (!config?.adjustHeaders || isNil(adjustFields)) {
        this.displayColumns.set(columns);
        return;
      }

      const fieldsObj = columns.reduce(
        (acc: Record<string, NzSafeAny>, field) => {
          acc[field.code] = field;
          return acc;
        },
        {},
      );

      this.displayColumns.set(
        adjustFields
          .filter((field) => field.display)
          .map((field) => fieldsObj[field.id]),
      );
    },
    { allowSignalWrites: true },
  );

  isOffHeader(code: string) {
    return this.headersVisible().includes(code);
  }

  switchChangeHeader(code: string) {
    const idx = this.headersVisible().findIndex((c) => c === code);
    if (idx === -1) {
      this.headersVisible.update((e) => {
        const v = structuredClone(e);
        v.push(code);
        return v;
      });
    } else {
      this.headersVisible.update((e) => {
        const v = structuredClone(e);
        v.splice(idx, 1);
        return v;
      });
    }
  }
  isOff(name: string) {
    return this.disabledFields().find((field) => name === field);
  }
  switchChange(name: string) {
    const idx = this.disabledFields().findIndex((field) => name === field);
    const tmp = structuredClone(this.disabledFields());
    if (idx === -1) {
      tmp.push(name);
      this.disabledFields.set(tmp);
    } else {
      tmp.splice(idx, 1);
      this.disabledFields.set(tmp);
    }
  }
  onActionsManyClick(actionId: string) {
    switch (actionId) {
      case 'deleteAll': {
        this.deleteClickMany();
        break;
      }
      case 'export': {
        this.handleActionsMany(actionId);
        break;
      }
      case 'quickAdd':
      case 'deleteBySelection':
        this.handleActionsManyForm(actionId);
        break;
      default: {
        this.handleActionsMany(actionId);
        break;
      }
    }
  }

  actionButtonFormValue?: FormComponent;

  // TODO: need to refactor
  actionFormTitle = 'Title';
  actionFormDescription = 'Description';
  actionFormConfig: NzSafeAny[] | undefined = [];
  actionFormVisible = false;
  actionFormSize: 'large' | 'small' | 'middle' = 'large';
  selectedAction: string | null = null;
  handleActionsManyForm(actionId: string) {
    this.actionFormVisible = true;
    const actionHandler = this.getActionHandler(actionId);
    if (!actionHandler) return;
    this.actionFormTitle = actionHandler?.title ?? 'Title';
    this.actionFormDescription = actionHandler?.descriptions ?? 'Description';
    this.actionFormConfig = actionHandler.extend
      ? this.getActionHandler(actionHandler.extend)?.fields
      : actionHandler?.fields;
    this.actionFormVisible = true;
    this.actionFormSize = 'small';
    this.selectedAction = actionId;
  }

  getActionHandler(actionId: string) {
    return this.config?.layout_option?.actions_many_handler?.[actionId];
  }

  handleActionsMany(actionId: string) {
    console.log('select item', this.setOfSelectedItem);
    console.log('actionId', actionId);
  }

  clearChecked(): void {
    this.setOfSelectedItem.clear();
    this.allChecked = false;
    this.indeterminate = false;
  }

  deleteClickMany() {
    this.modalComponent.showDialog({
      nzTitle: 'Remove All',
      nzContent:
        'These will be permanently removed and all associated data. Remove anyway?',
      nzWrapClassName: 'popup popup-error',
      nzIconType: 'icons:trash-bold',
      nzOkText: 'Delete',
      nzCancelText: 'Cancel',
      nzOnOk: () => {
        // TODO: hard code, need to refactor;
        // this.dynamicFormsValues?.set({});
        // this.dynamicForms?.forEach((form) => form.formGroup.reset({}));
        // this.clearSelectedItem();
        this.selectedAction = 'deleteAll';
        this.onHandleActionsMany();
      },
    });
  }

  deleteManyRecords() {
    this.modalComponent.showDialog({
      nzTitle: 'Delete the selected record?',
      nzContent: 'Are you sure you want to delete this information?',
      nzWrapClassName: 'popup popup-error',
      nzIconType: 'icons:trash-bold',
      nzOkText: 'Delete',
      nzCancelText: 'Cancel',
      nzOnOk: () => {
        this.selectedAction = 'delete';
        this.onHandleActionsMany();
      },
    });
  }

  updateAllChecked(): void {
    this.allChecked = this.dataSource().every((data) =>
      this.mapOfExpandedData[data['key']].every((item) =>
        this.setOfSelectedItem.has(data['key'] + '-' + item.key),
      ),
    );
    this.indeterminate =
      !this.allChecked &&
      this.dataSource().some((data) =>
        this.mapOfExpandedData[data['key']].some((item) =>
          this.setOfSelectedItem.has(data['key'] + '-' + item.key),
        ),
      );
  }

  onAllChecked(checked: boolean): void {
    this.dataSource().forEach((data) => {
      this.mapOfExpandedData[data['key']].forEach((item) => {
        if (checked) {
          this.setOfSelectedItem.add(data['key'] + '-' + item.key);
        } else {
          this.setOfSelectedItem.delete(data['key'] + '-' + item.key);
        }
      });
    });
    this.updateAllChecked();
  }

  onChecked(checked: boolean, key: string): void {
    if (checked) {
      this.setOfSelectedItem.add(key);
    } else {
      this.setOfSelectedItem.delete(key);
    }
    this.updateAllChecked();
  }

  onSelectData() {
    const rowId = this.rowActionId();
    const demodata = [
      {
        id: '1',
        list: 'HRM',
        permission: '',
      },
      {
        id: '2',
        list: 'Report Test',
        permission: 'Update',
      },
      {
        id: '3',
        list: 'Report Test',
        permission: 'Edit',
      },
    ];

    this.dataSource().push(...demodata);
    this.dataTable().push(...demodata);
    this.dataSource().forEach((item: Record<string, NzSafeAny>) => {
      this.mapOfExpandedData[item[rowId]] = this.convertTreeToList(item);
    });
  }

  getValue(value: FieldTable['dataSource'][keyof FieldTable['dataSource']]) {
    return value;
  }

  isString(value: FieldTable['dataSource']): boolean {
    return typeof value === 'string';
  }

  mapOfExpandedData: { [key: string]: FieldTable['columns'] } = {};

  toggleExpand(
    array: FieldTable['dataSource'][],
    item: FieldTable['dataSource'],
  ): void {
    item['expand'] = !item['expand'];
    this.collapse(array, item, item['expand']);
  }

  collapse(
    array: FieldTable['dataSource'][],
    data: FieldTable['dataSource'],
    $event: boolean,
  ): void {
    if (!$event) {
      const rowId = this.rowActionId();
      if (data['children']) {
        data['children'].forEach((d: { id: string }) => {
          const target = array.find((a) => a[rowId] === d.id);
          if (target) {
            target['expand'] = false;
            this.collapse(array, target, false);
          }
        });
      }
    }
  }

  convertTreeToList(
    root: FieldTable['dataSource'],
  ): FieldTable['dataSource'][] {
    const stack: FieldTable['dataSource'][] = [];
    const array: FieldTable['dataSource'][] = [];
    const hashMap = {};

    stack.push({ ...root, level: 0 });

    while (stack.length > 0) {
      const node = stack.pop();
      if (node) {
        this.visitNode(node, hashMap, array);
        if (node['children']) {
          for (let i = node['children'].length - 1; i >= 0; i--) {
            stack.push({
              ...node['children'][i],
              level: (node['level'] ?? 0) + 1,
              expand: node['expand'] ?? false,
              parent: node,
            });
          }
        }
      }
    }

    return array;
  }

  visitNode(
    node: FieldTable['dataSource'],
    hashMap: { [key: string]: boolean },
    array: FieldTable['dataSource'][],
  ): void {
    const rowId = this.rowActionId();
    if (!hashMap[node[rowId]]) {
      hashMap[node[rowId]] = true;
      array.push(node);
    }
  }

  displayData = computed(() => {
    if (!this.isLocalPagination()) return this.dataSource();
    return this.dataSource().slice(
      (this.pageIndex() - 1) * this.pageSize(),
      this.pageIndex() * this.pageSize(),
    );
  });

  onPageSizeChange(pageSize: number) {
    this.pageSize.set(pageSize);
    this.pageIndex.set(1);
  }

  dataSource = computed((): FieldTable['dataSource'][] => {
    const dataSource = this.dataTable() ?? [];
    const rowId = this.rowActionId();
    const temp = { ...this.mapOfExpandedData };

    dataSource?.forEach((item: Record<string, NzSafeAny>) => {
      if (item && typeof item === 'object' && rowId in item) {
        const key = item[rowId];
        if (key) {
          temp[key] = this.convertTreeToList(item);
        }
      }
    });

    this.mapOfExpandedData = temp;
    return filterData(dataSource, this.search());
  });

  groupedData: GroupedData[] = [];
  groupTableData(key: string) {
    const grouped = this.dataSource().reduce((acc, item) => {
      const groupKey = item[key];
      if (!acc[groupKey]) {
        acc[groupKey] = [];
      }
      acc[groupKey].push(item);
      return acc;
    }, {});

    this.groupedData = Object.keys(grouped).map((key) => ({
      key,
      items: grouped[key],
    }));
  }

  removedFilterItem = (event: NzSafeAny) => {
    this.filterValue.set(event);
  };

  onClose() {
    this.dialogVisible.set(false);
  }
  onCancel(isCancel?: boolean) {
    if (isCancel) {
      this.isActionBtn = false;
      return;
    }
    this.modalComponent.showDialog({
      nzTitle: 'Cancel creation',
      nzContent: this.modalContent,
      nzWrapClassName: 'popup popup-confirm hide-footer-btns',
      nzIconType: 'icons:file-arrow-down',
      nzOkText: null,
      nzCancelText: null,
      nzFooter: null,
    });
  }

  onCancelClick(ref: NzSafeAny): void {
    ref?.destroy();
    this.actionFormVisible && (this.actionFormVisible = false);
    this.dialogVisible() && this.dialogVisible.set(false);
  }

  dynamicFormsValues = signal<Record<string, NzSafeAny>>({});

  getFormValue(key: string) {
    return this.dynamicFormsValues()?.[key] ?? {};
  }

  isMatch = (
    item: FieldTable['dataSource'],
    removeItem: Record<string, NzSafeAny>,
  ) => {
    return Object.keys(removeItem).every(
      (key) => removeItem[key] === item[key],
    );
  };

  onHandleActionsMany() {
    this.actionFormVisible = false;
    const selectedAction = this.selectedAction;
    const actionFormValue = this.actionButtonForm?.value;
    const setOfSelectedItem = this.listOfSelectedItems() as Record<
      string,
      NzSafeAny
    >[];
    const data = this.dataTable();
    const rowId = this.rowActionId();
    // TODO: hard code and duplicate code, need to refactor
    switch (selectedAction) {
      case 'quickAdd': {
        setOfSelectedItem.forEach((item: NzSafeAny) => {
          const idx = Number(item[rowId]);
          if (idx) {
            const currentFormValue =
              this.dynamicFormsValues()?.[idx]?.value ?? {};
            const currentPermission = [
              ...(currentFormValue.permission ?? []),
              ...(actionFormValue?.permission ?? []),
            ];

            const allowPermision = item.permission.fields[0].options;
            const allowedValues = new Set(
              allowPermision.map((p: { value: string }) => p.value),
            );

            const updatedPermissions = currentPermission.filter((p) =>
              allowedValues.has(p),
            );

            this.dynamicFormsValues.update((prev) => ({
              ...prev,
              [idx]: { [idx]: updatedPermissions },
            }));

            this.dynamicForms?.forEach((form) => {
              const formGroupValue = form.formGroup.value ?? {};
              form.formGroup.patchValue({
                [idx]: [
                  ...(formGroupValue.permission ?? []),
                  ...(updatedPermissions ?? []),
                ],
              });
            });
          }
        });

        break;
      }
      case 'deleteBySelection': {
        setOfSelectedItem.forEach((item: NzSafeAny) => {
          const idx = Number(item[rowId]);
          if (idx) {
            const formValues = this.dynamicFormsValues()?.[idx]?.value;

            if (formValues) {
              const updatedPermissions = formValues[idx]?.filter(
                (permission: string) =>
                  !actionFormValue?.permission?.includes(permission),
              );

              this.dynamicFormsValues.update((prev) => ({
                ...prev,
                [idx]: { [idx]: updatedPermissions },
              }));
            }

            this.dynamicForms?.forEach((form) => {
              const formGroupValues = form.formGroup.value;
              const updatedFormGroupPermissions = formGroupValues[idx]?.filter(
                (permission: string) =>
                  !actionFormValue?.permission?.includes(permission),
              );

              form.formGroup.patchValue({
                [idx]: updatedFormGroupPermissions,
              });
            });
          }
        });

        break;
      }
      case 'deleteAll': {
        for (const item of setOfSelectedItem) {
          const idx = Number(item[rowId]);
          if (idx) {
            {
              const formValue = this.dynamicFormsValues()?.[idx];
              if (formValue) {
                this.dynamicFormsValues.update((prev) => ({
                  ...prev,
                  [idx]: [],
                }));
              }
            }
          }
          this.dynamicForms?.forEach((form) => {
            form.formGroup.patchValue({
              [idx]: [],
            });
          });
        }
        break;
      }
      case 'delete': {
        const tmp = data.filter(
          (item) =>
            !setOfSelectedItem.some((removeItem: Record<string, NzSafeAny>) =>
              this.isMatch(item, removeItem),
            ),
        );
        this.dataTable.set(tmp);
        this.group.get(this.config.name)?.setValue(tmp);
        break;
      }
    }

    this.toast.showToast('success', '', `Successfully saved`);
    this.clearSelectedItem();
    this.selectedAction = null;
  }

  clearSelectedItem() {
    this.setOfSelectedItem.clear();
    this.indeterminate = false;
    this.allChecked = false;
  }

  // condition for footer buttons

  buttons = computed(() => {
    switch (this.dialogType()) {
      case 'view':
        return { delete: { label: 'Delete' }, edit: { label: 'Edit' } };
      case 'create': {
        return {
          cancel: { label: 'Cancel' },
          ok: { label: 'Save' },
          saveAndAddNew: { label: 'Save & Add New' },
        };
      }
      case 'edit':
        return { cancel: { label: 'Cancel' }, ok: { label: 'Save' } };
      case 'proceed':
        return { cancel: { label: 'Cancel' }, ok: { label: 'Save' } };
      case 'filter':
        return {
          reset: { label: 'Clear' },
          cancel: { label: 'Cancel' },
          ok: { label: 'Apply' },
        };
      default:
        return {};
    }
  });

  edditItem(event: NzSafeAny) {
    const controlName = this.config.name;
    const control = this.group.get(controlName);
    if (control) {
      const currentValue = control.value || [];
      const newValue = event[controlName];

      if (Array.isArray(currentValue)) {
        control.setValue([...currentValue, newValue]);
      }
    }
  }
  updateDataTable(event: NzSafeAny) {
    this.dataTable.update((data) => {
      return this.sortData(mergeTableData(this.distinctByKey(), data, event));
    });
  }

  isReadOnly = computed(() => {
    return (
      this.config?.readOnly || this.values?.extend?.['formType'] === 'view'
    );
  });

  orderChange(newlist: NzSafeAny) {
    this.columnTable.set(newlist);
  }

  changeValue(value: NzSafeAny, item: Record<string, unknown>, code: string) {
    if (typeof item !== 'object') return;
    item[code] = value;
    this.group.get(this.config.name)?.setValue(this.dataTable());
  }

  errors = signal<Record<string, NzSafeAny>[]>([]);
  @Output() invalidEmitter: EventEmitter<boolean> = new EventEmitter();
  effectErrors = effect(() => {
    if (this.errors().filter((err) => err).length > 0) {
      this.invalidEmitter.emit(true);
    } else {
      this.invalidEmitter.emit(false);
    }
  });
  childInvalid(invalid: boolean, idx: number, code: string) {
    const errors = this.errors();

    if (invalid) {
      errors[idx] = { ...errors[idx], [code]: true };
    } else {
      delete errors[idx][code];
      if (Object.keys(errors[idx]).length === 0) {
        delete errors[idx];
      }
    }
    this.errors.set(structuredClone(errors));
  }
  addRow() {
    console.log('add row', this.dataTable());
    this.dataTable.update((data) => {
      return [...data, {}];
    });
  }

  showAddNewItem = computed(
    () => this.config?.layout_option?.show_add_new_item ?? false,
  );
  addNewItemVisible = signal(false);

  // section for edit table
  selectedItemsEditTable = signal<NzSafeAny[]>([]);
  dataTableToEdit = signal<NzSafeAny[]>([]);
  editTableConfig = computed(() => this.config.editTableConfig ?? {});
  editTableModalVisible = signal<boolean>(false);
  actionsLoadingState = signal<Record<string, boolean>>({});
  addNewItemToEditVisible = signal(false);
  editTableModalTitle = computed(
    () => this.editTableConfig().modal?.title ?? 'Edit Table',
  );
  editTableModalSize = computed(
    () => this.editTableConfig().modal?.size ?? 'largex',
  );
  async onEditTable() {
    const action = this.getEditAction('preEdit');
    let dataTable = structuredClone(this.dataSource() ?? []);
    if (action) {
      const res = await firstValueFrom(
        this.createRequestByAction({
          action,
          contextData: { data: dataTable, values: this.values },
        }).pipe(takeUntil(this.destroy$)),
      );
      if (res?.isError) return;
      if (res) dataTable = res;
    }
    this.dataTableToEdit.set(dataTable);
    this.editTableModalVisible.set(true);
  }

  onAddNewItem() {
    this.addNewItemToEditVisible.set(true);
  }

  onUpdateDataEditTable(newData: NzSafeAny) {
    this.dataTableToEdit.update((data) =>
      this.sortData(mergeTableData(this.distinctByKey(), data, newData)),
    );
  }

  closeEditTableModal() {
    this.editTableModalVisible.set(false);
  }

  getEditAction(id: string) {
    return this.editTableConfig().actions?.find((action) => action.id === id);
  }

  setEditTableLoadingState(id: string, value: boolean) {
    this.actionsLoadingState.update((prev) => ({
      ...prev,
      [id]: value,
    }));
  }

  showErrorMessage = async (err: any) => {
    if (err?.error instanceof Blob) {
      const jsonData = JSON.parse(await err.error.text());
      err = jsonData?.message;
    } else if (typeof err === 'object') {
      err = err.error?.message ?? err.error?.messageError ?? '';
    }
    this.toast.showToast('error', 'Error', err);
  };

  createRequestByAction(setup: {
    action: ActionConfig;
    contextData?: NzSafeAny;
    request?: {
      onSuccess?: (data: NzSafeAny) => void;
      onError?: (err: NzSafeAny) => void;
      defaultMethod?: 'POST' | 'PUT' | 'PATCH' | 'DELETE' | 'GET';
      body?: NzSafeAny;
    };
  }) {
    const { action, contextData = { values: this.values }, request } = setup;
    const { api, source } = action;
    if (api) {
      const url = HandleBars.compile(api.url)(contextData);
      if (!url) return of(undefined);
      const method = api.method ?? request?.defaultMethod ?? 'PATCH';
      const headers = this.service.setDataHeaders(
        undefined,
        api.authConfig?.actionCode,
      );
      return this.http
        .request(method, url, { body: request?.body, headers })
        .pipe(
          catchError((err) => {
            this.showErrorMessage(err);
            return of(undefined);
          }),
          finalize(() => this.cdr.detectChanges()),
        );
    } else if (source) {
      return this.service.getValueFromApi(source, contextData, {
        onError: request?.onError ?? this.showErrorMessage,
        returnError: true,
      });
    }
    return of(undefined);
  }

  doSaveAction() {
    const action = this.getEditAction('save');
    if (!action) return of(undefined);
    return this.createRequestByAction({
      action,
      contextData: { data: this.dataTableToEdit(), values: this.values },
      request: { body: this.dataTableToEdit() },
    });
  }

  doDownloadAction() {
    const action = this.getEditAction('download');
    if (!action) return of(undefined);
    const { api } = action;
    if (!api) return of(undefined);
    const url = HandleBars.compile(api.url)({ values: this.values });
    if (!url) return of(undefined);
    const headers = this.service.setDataHeaders(
      undefined,
      api.authConfig?.actionCode,
    );
    return this.service.downloadFile(url, { headers }).pipe(
      catchError((err) => {
        this.showErrorMessage(err);
        return of(undefined);
      }),
      finalize(() => this.cdr.detectChanges()),
    );
  }

  onCancelEditTable() {
    this.closeEditTableModal();
  }

  private updateDataFromEditForm(data: NzSafeAny[]) {
    if (this.config.editTableConfig?.reloadValueAfterSubmit) {
      this.values.reloadFormValueFn?.();
    } else {
      this.dataTable.set(data);
    }
  }

  async onSaveEditTable() {
    this.setEditTableLoadingState('save', true);
    const res = await firstValueFrom(this.doSaveAction());
    this.setEditTableLoadingState('save', false);
    if (res?.isError) return;
    this.toast.showToast('success', '', 'Save records successfully!');
    this.updateDataFromEditForm(this.dataTableToEdit());
    this.closeEditTableModal();
  }

  async onSaveAndDownloadEditTable() {
    this.setEditTableLoadingState('saveAndDownload', true);
    const res = await firstValueFrom(this.doSaveAction());
    this.setEditTableLoadingState('saveAndDownload', false);
    if (res?.isError) return;
    this.toast.showToast('success', '', 'Save records successfully!');
    await firstValueFrom(this.doDownloadAction());
    this.updateDataFromEditForm(this.dataTableToEdit());
    this.closeEditTableModal();
  }

  onDeleteItem(itemIdx: number) {
    const dataTable = [...this.dataTableToEdit()];
    dataTable.splice(itemIdx, 1);
    this.dataTableToEdit.set(dataTable);
  }

  onDeleteItems() {
    const selectedItems = this.selectedItemsEditTable();
    const distinctByKey = this.distinctByKey();
    const dataTable = this.dataTableToEdit();
    const filteredData = dataTable.filter((item) =>
      selectedItems.every(
        (removeItem) => removeItem[distinctByKey] !== item[distinctByKey],
      ),
    );

    this.dataTableToEdit.set(filteredData);
  }

  onChangeValueItemEditTable(value: NzSafeAny, idx: number, code: string) {
    const dataTable = [...this.dataTableToEdit()];
    const item = dataTable[idx];
    if (!item) return;
    item[code] = value;
  }

  onDownloadTemplate() {
    const { api } = this.config.downloadTemplateConfig ?? {};
    if (!api) return;
    const url = HandleBars.compile(api.url)({ values: this.values });
    if (!url) return;
    const headers = this.service.setDataHeaders(
      undefined,
      api.authConfig?.actionCode,
    );
    this.service
      .downloadFile(url, { headers })
      .pipe(
        catchError((err) => {
          this.showErrorMessage(err);
          return of(undefined);
        }),
        finalize(() => this.cdr.detectChanges()),
      )
      .subscribe();
  }
  //end section edit table

  formDialog = signal<{
    visible: boolean;
    config?: any;
    settings: any;
    value?: any;
  } | null>(null);
  cellActionSelected = signal<
    (CellAction & { callback?: (value: any) => void }) | null
  >(null);
  handleCellAction(
    e: {
      event: string;
      value?: any;
      callback?: (data?: NzSafeAny) => void;
    },
    rowValue?: any,
  ) {
    const { event, value, callback } = e;
    const actionConfig = this.config.cellAction?.[event];
    if (!actionConfig) return;
    this.cellActionSelected.set({ ...actionConfig, callback });
    switch (actionConfig.type) {
      case 'form-dialog': {
        const { config, settings } = actionConfig;
        this.formDialog.set({
          visible: true,
          config: {
            ...config,
            extendValue: { formType: event, value: rowValue },
          },
          value: rowValue,
          settings,
        });
      }
    }
  }

  async handleFormDialogSubmit(value: any) {
    const transform = this.cellActionSelected()?.config?.submitValue?.transform;
    if (transform) {
      value = await this.service.getJsonataExpression({})(transform, value);
    }
    this.cellActionSelected()?.callback?.(value);
    this.resetFormDialog();
    this.cellActionSelected.set(null);
  }

  onFormDialogVisibleChange(visible: boolean) {
    if (!visible) {
      this.resetFormDialog();
      this.cellActionSelected.set(null);
    }
  }

  resetFormDialog() {
    this.formDialog.set({
      visible: false,
      config: null,
      settings: null,
    });
  }
}
