import { ButtonSchema, ModalSize } from '@hrdx/hrdx-design';
import { NzSafeAny } from 'ng-zorro-antd/core/types';
import { z } from 'zod';
import {
  CalendarOptions,
  FormFieldsConfig,
  Source,
  SourceField,
} from '../../../models';

const ZSourceField = z.object({
  transform: z.string(),
});

const columnSchema: z.ZodType<any> = z.object({
  title: z.string(),
  code: z.string(),
  key: z.string(),
  id: z.string(),
  width: z.number().optional(),
  pinned: z.boolean().optional(),
  _condition: ZSourceField.optional(),
  data_type: z.object({
    key: z.string(),
  }),
  display_type: z.object({
    key: z.string(),
  }),
  actionRow: z.object({
    action: z.string(),
    baseUrl: z.string(),
  }),
  align: z.enum(['left', 'center', 'right']).optional(),
  children: z.array(z.lazy(() => columnSchema)).optional(),
  level: z.number().default(0),
  parent: z.lazy(() => columnSchema).optional(),
  type: z
    .enum(['text', 'number', 'date', 'select', 'checkbox', 'radio'])
    .optional(),
});

const fieldTableSchema = z.object({
  columns: z.array(columnSchema),
  dataSource: z.record(z.string(), z.any()),
});

const ActionManyHandlerSchema = z
  .object({
    title: z.string().optional(),
    descriptions: z.string().optional(),
    fields: z.array(z.any()).optional(),
    sources: z.object({}).optional(),
    variables: z.object({}).optional(),
    value: z.array(z.any()).optional(),
    extend: z.string().optional(),
  })
  .optional();

type FieldTable = z.infer<typeof fieldTableSchema>;
type ActionManyHandler = z.infer<typeof ActionManyHandlerSchema>;

export { FieldTable, fieldTableSchema };

export type DialogType =
  | 'create'
  | 'edit'
  | 'proceed'
  | 'filter'
  | 'view'
  | 'viewSchedule';

export type FormConfig = {
  fields?: FormFieldsConfig[];
  sources?: { [k: string]: Source };
  variables?: { [k: string]: SourceField };
};

export type SortOption = {
  key: string;
  orderOption?: string;
};

export type LayoutButton = {
  id: string;
  type: ButtonSchema['type'];
  title: string;
  icon: string;
  disabled: boolean;
  appendObject?: { [k: string]: any };
  precheck?: {
    source?: Source;
  };
  appendAction?: LayoutButton[];
  _disabled?: SourceField;
};

export type ActionConfig = {
  id: string;
  api?: { url: string; method: string; authConfig?: { actionCode: string } };
  source?: Source;
};

export type ToolTableOptions = {
  addSetup?: boolean;
  filter?: boolean;
  operations?: boolean;
  lock?: boolean;
  export?: boolean;
  edit?: boolean;
  downloadTemplate?: boolean;
  adjustHeaders?: boolean;
};
export interface FieldTableConfigI {
  tableId?: string;
  name: string;
  type: string;
  columns: FieldTable['columns'];
  dataSource: FieldTable['dataSource'][];
  filter: any;
  form: any;
  rowIdName?: string;
  mapRowName?: string[];
  _dataSourceRequestStatus?: string;
  value: string[];
  fields: FormFieldsConfig[];
  variables?: NzSafeAny;
  sources?: NzSafeAny;
  createFormTable?: {
    btnTitle: string;
    modalTitle: string;
    fields: FormFieldsConfig[];
  };
  config?: ToolTableOptions;
  actionClickRow?: {
    action?: string;
    baseUrl?: string; // sys/sys01?dialogType=view&id={{row.id}}
    key?: string;
    condition?: string | number;
  };
  control: {
    show_table_checkbox: boolean;
  };
  layout_option?: {
    actions_many?: LayoutButton[];
    actions_many_handler?: Record<string, ActionManyHandler>;
    action_row?: LayoutButton[];
    action_row_disabled?: string[];
    show_actions_many?: boolean;
    hide_action_row?: boolean;
    disabled_click_row?: boolean;
    tbody_row_click_enabled?: boolean;
    show_detail_history?: boolean;
    show_dialog_form_save_add_button?: boolean;
    show_dialog_form_delete_button?: boolean;
    tool_table?: {
      show_table_checkbox?: boolean;
      show_table_filter?: boolean;
      expand_filter?: boolean;
      show_table_group?: boolean;
      hidden_header?: boolean;
      collapse?: boolean;
      show_table_search?: boolean;
      have_child_item_checkbox?: boolean;
    };
    show_pagination?: boolean;
    no_data_sub_text?: string;
    show_row_index?: boolean;
    show_add_new_item?: boolean;
    add_new_button_title?: string;
    resize?: { width?: boolean; height?: boolean };
  };
  actionButton: ActionManyHandler;
  form_config: FormConfig;
  filter_config: any;
  add_new_value?: boolean;
  readOnly: boolean;
  sortOption?: SortOption;
  _dataSource?: SourceField;
  _column?: SourceField;
  _select?: SourceField;
  _row_actions?: SourceField;
  addSetup?: {
    columns: {
      code: string;
      title: string;
      type?: string;
      align?: string;
      width?: string;
    }[];
    filter: NzSafeAny;
    Source?: NzSafeAny;
    defaultData?: NzSafeAny;
    clientPagination?: boolean;
    filterMapping?: { field: string; operator: string; valueField: string }[];
  };
  dependantField?: string;
  _defaultFilterValue?: SourceField;
  _defaultData?: SourceField;
  _defaultDataSource?: SourceField;
  dependantFieldSkip?: number;
  distinctByKey?: string;
  calendarOptions?: CalendarOptions;
  canAddItem?: boolean;
  add_label?: string;
  add_btn_type?:
    | 'primary'
    | 'secondary'
    | 'tertiary'
    | 'link'
    | 'ghost-color'
    | 'ghost-gray';
  editTableConfig?: {
    modal?: {
      title?: string;
      size?: ModalSize;
    };
    table?: {
      addNewBtnTitle?: string;
    };
    actions?: ActionConfig[];
    reloadValueAfterSubmit?: boolean;
  };
  downloadTemplateConfig?: {
    api?: ActionConfig['api'];
  };
  localPagination?: {
    pageSizeOptions?: number[];
  };
  cellAction?: Record<string, CellAction>;
  // use this option to fix element into content viewport
  adjustHeight?: {
    elementSelector: string | string[];
    difference?: number;
  };
  checkPermissionEnabled?: boolean; // default false
  insertDataConfig?: {
    formConfig?: FormConfig;
    transformDataExpression: string;
    checkDuplicate?: {
      by: string[];
      validateMessage?: string;
    };
    sortBy?: string;
    insertPosition?: 'start' | 'end';
    validateOnValueChanges?: boolean;
  };
}

export type CellAction = {
  type: 'form-dialog';
  config?: {
    fields: any;
    sources?: any;
    variables?: any;
    submitValue?: {
      transform: string;
    };
  };
  settings: {
    size: any;
    title?: string;
  };
};

export interface GroupedData {
  key: string;
  items: FieldTableConfigI['dataSource'];
}

export type RecordElementsState = boolean[][];
