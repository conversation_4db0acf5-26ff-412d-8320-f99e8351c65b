import { CommonModule } from '@angular/common';
import {
  AfterViewInit,
  Component,
  Input,
  OnChanges,
  SimpleChanges,
  ViewEncapsulation,
  computed,
  inject,
} from '@angular/core';
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
} from '@angular/forms';
import {
  ButtonComponent,
  InputComponent,
  InputFieldType,
  ModalComponent,
} from '@hrdx/hrdx-design';
import { every, isEmpty, isNil } from 'lodash';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzModalModule } from 'ng-zorro-antd/modal';
import {
  BehaviorSubject,
  Observable,
  combineLatest,
  distinctUntilChanged,
  map,
  switchMap,
  tap,
} from 'rxjs';
import { FieldTranslationConfig } from '../../../models/field-config.interface';
import { Values } from '../../../models/field.interface';
import { DynamicFormService } from '../../../services/dynamic-form.service';
import { NzSafeAny } from 'ng-zorro-antd/core/types';

@Component({
  selector: 'dynamic-field-textarea-translation',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    NzInputModule,
    NzButtonModule,
    NzIconModule,
    NzModalModule,
    ModalComponent,
    ButtonComponent,
    FormsModule,
    InputComponent,
  ],
  templateUrl: './field-textarea-translation.component.html',
  styleUrl: './field-textarea-translation.component.less',
  encapsulation: ViewEncapsulation.None,
})
export class FieldTranslationTextAreaComponent
  implements OnChanges, AfterViewInit
{
  readonly inputConfig = {
    type: InputFieldType.TextArea,
  };
  config!: FieldTranslationConfig;
  group!: FormGroup;
  @Input() values: Values = {};
  value$!: Observable<{ default: string; 'en-US': string; 'vi-VN': string }>;
  service = inject(DynamicFormService);
  values$ = new BehaviorSubject<Values>({});
  value?: { default: string; 'en-US': string; 'vi-VN': string };
  _value?: string | null;
  actionConditionBehavior$?: Observable<boolean>;
  showAction = true;
  fb = new FormGroup({
    default: new FormControl(),
    'vi-VN': new FormControl(),
    'en-US': new FormControl(),
  });
  fields = [
    {
      type: 'text',
      label: 'Default Value',
      placeholder: 'Enter default value',
      name: 'default',
    },
    {
      type: 'text',
      label: 'English',
      placeholder: 'Enter English value',
      name: 'en-US',
    },
    {
      type: 'text',
      label: 'Vietnamese',
      placeholder: 'Enter Vietnamese value',
      name: 'vi-VN',
    },
  ];

  autoSize?:
    | {
        minRows?: number;
        maxRows?: number;
      }
    | undefined;

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['values']) {
      this.values$.next(this.values);
    }
  }

  getValueField(name: string) {
    if (name === 'default' || name === 'vi-VN' || name === 'en-US')
      return (
        (this.fb.controls[name].value &&
          this.fb.controls[name].value.replace(/\n/g, '<br>')) ??
        '--'
      );
    return '--';
  }

  isObjectDeepEmpty = (obj: NzSafeAny) => {
    // Kiểm tra nếu object rỗng hoặc tất cả các giá trị của nó là `undefined`
    return isNil(obj) || isEmpty(obj) || every(obj, (value) => isNil(value));
  };

  ngAfterViewInit() {
    const config = this.config;
    this.value = config.value;
    if (this.value) {
      this.fb.patchValue(this.value);
      this._value = this.value.default;
    }

    if (this.isObjectDeepEmpty(this.value) && config.readOnly) {
      this.showAction = false;
    }
    this.value$ = combineLatest({
      _value: this.values$.pipe(
        distinctUntilChanged((prev, curr) => {
          return this.service.distinct(prev, curr, config._value);
        }),
        switchMap(() =>
          this.service.getObservable(
            this.values.function,
            this.values,
            this.config._value,
          ),
        ),
      ),
    }).pipe(
      map(({ _value }) => {
        return _value;
      }),
    );
    if (config._value) {
      this.value$.subscribe((value) => {
        if (config.isRecommend && !isNil(this.value)) return;
        if (value) {
          this.fb.patchValue(value);
          this._value = value?.default;
        }
      });
    }

    // condition for action
    if (config._actionCondition) {
      this.actionConditionBehavior$ = this.values$.pipe(
        distinctUntilChanged((prev, curr) =>
          this.service.distinct(prev, curr, config._actionCondition),
        ),
        switchMap((values) =>
          this.service.getObservable(
            values.function,
            values,
            config._actionCondition,
          ),
        ),
      );

      this.actionConditionBehavior$?.subscribe((value) => {
        if (value && typeof value == 'boolean') {
          this.showAction = true;
        } else {
          this.showAction = false;
        }
      });
    }

    this.fb.valueChanges
      .pipe(
        tap((v) => {
          this.group.controls[this.config.name].setValue(v);
          this.markControlDirty();
        }),
      )
      .subscribe();
    this.autoSize = this.config.textarea?.autoSize;
  }

  getReadOnlyValue() {
    if (this.value?.default) return this.value.default.replace(/\n/g, '<br>');
    return '--';
  }

  translatePopup = false;
  private prevValue: NzSafeAny = null;

  openTranslatePopup() {
    this.prevValue = this.fb.value;
    this.translatePopup = true;
  }

  onCancel(isSave?: boolean) {
    !isSave && this.fb.patchValue(this.prevValue);
    this.translatePopup = false;
  }

  onChange(v: string | null) {
    const formValue = { default: v };
    this.fb.patchValue(formValue);
    this.markControlDirty();
  }

  get control() {
    return this.group.get(this.config.name);
  }

  private markControlDirty() {
    this.control?.markAsDirty();
    this.control?.markAsTouched();
  }

  onFieldChange(v: string | null, name: string) {
    if (name === 'default') {
      this._value = v;
    }
  }

  onTouch() {
    this.group.get(this.config.name)?.markAsTouched();
  }

  get isDisabled() {
    return this.group?.get(this.config?.name)?.disabled ?? false;
  }

  maxCharCount = computed(() => this.config?.textarea?.maxCharCount ?? 1000);
}
