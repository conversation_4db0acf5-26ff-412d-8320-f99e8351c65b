import { CommonModule } from '@angular/common';
import {
  AfterViewInit,
  Component,
  Input,
  OnChanges,
  OnDestroy,
  SimpleChanges,
  ViewEncapsulation,
  inject,
} from '@angular/core';
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
} from '@angular/forms';
import { ButtonComponent, ModalComponent } from '@hrdx/hrdx-design';
import { every, isEmpty, isEqual, isNil, trim } from 'lodash';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzModalModule } from 'ng-zorro-antd/modal';
import {
  BehaviorSubject,
  Observable,
  Subject,
  combineLatest,
  distinctUntilChanged,
  map,
  switchMap,
  takeUntil,
} from 'rxjs';
import { FieldTranslationConfig } from '../../../models/field-config.interface';
import { Values } from '../../../models/field.interface';
import { DynamicFormService } from '../../../services/dynamic-form.service';
import { NzSafeAny } from 'ng-zorro-antd/core/types';

@Component({
  selector: 'dynamic-field-translation',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    NzInputModule,
    NzButtonModule,
    NzIconModule,
    NzModalModule,
    ButtonComponent,
    ModalComponent,
    FormsModule,
  ],
  templateUrl: './field-translation.component.html',
  styleUrl: './field-translation.component.less',
  encapsulation: ViewEncapsulation.None,
})
export class FieldTranslationComponent
  implements OnChanges, AfterViewInit, OnDestroy
{
  config!: FieldTranslationConfig;
  group!: FormGroup;
  @Input() values: Values = {};
  value$!: Observable<{ default: string; english: string; vietnamese: string }>;
  service = inject(DynamicFormService);
  values$ = new BehaviorSubject<Values>({});
  actionConditionBehavior$?: Observable<boolean>;
  showAction = true;
  _value?: string;
  value?: { default: string; 'en-US': string; 'vi-VN': string };
  fb = new FormGroup({
    default: new FormControl(),
    'vi-VN': new FormControl(),
    'en-US': new FormControl(),
  });

  // Add destroy subject for clean unsubscription
  private destroy$ = new Subject<void>();

  fields = [
    {
      type: 'text',
      label: 'Default Value',
      placeholder: 'Enter default value',
      name: 'default',
      labelType: 'label-type-col',
    },
    {
      type: 'text',
      label: 'English',
      placeholder: 'Enter English value',
      name: 'en-US',
    },
    {
      type: 'text',
      label: 'Vietnamese',
      placeholder: 'Enter Vietnamese value',
      name: 'vi-VN',
    },
  ];

  // Track by function for ngFor to improve rendering performance
  trackByFieldName(index: number, field: any): string {
    return field.name;
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['values']) {
      this.values$.next(this.values);
    }
  }

  getValueField(name: string) {
    if (name === 'default' || name === 'vi-VN' || name === 'en-US')
      return this.fb.controls[name].value ?? '--';
    return '--';
  }

  isObjectDeepEmpty = (obj: NzSafeAny) => {
    // Kiểm tra nếu object rỗng hoặc tất cả các giá trị của nó là `undefined`
    return isNil(obj) || isEmpty(obj) || every(obj, (value) => isNil(value));
  };

  ngAfterViewInit() {
    const config = this.config;
    this.value = config.value;
    if (this.value) {
      this.fb.patchValue(this.value);
      this._value = this.value.default;
    }

    if (this.isObjectDeepEmpty(this.value) && config.readOnly) {
      this.showAction = false;
    }
    this.value$ = combineLatest({
      _value: this.values$.pipe(
        distinctUntilChanged((prev, curr) => {
          return this.service.distinct(prev, curr, config._value);
        }),
        switchMap(() =>
          this.service.getObservable(
            this.values.function,
            this.values,
            this.config._value,
          ),
        ),
      ),
    }).pipe(
      map(({ _value }) => {
        return _value;
      }),
    );
    if (config._value) {
      this.value$
        .pipe(
          takeUntil(this.destroy$), // Add takeUntil for proper unsubscription
        )
        .subscribe((value) => {
          if (config.isRecommend && !isNil(this.value)) return;
          if (value) {
            this.fb.patchValue(value);
            this._value = value?.default;
          }
        });
    }

    // condition for action
    if (config._actionCondition) {
      this.actionConditionBehavior$ = this.values$.pipe(
        distinctUntilChanged((prev, curr) =>
          this.service.distinct(prev, curr, config._actionCondition),
        ),
        switchMap((values) =>
          this.service.getObservable(
            values.function,
            values,
            config._actionCondition,
          ),
        ),
      );

      this.actionConditionBehavior$
        ?.pipe(
          takeUntil(this.destroy$), // Add takeUntil for proper unsubscription
        )
        .subscribe((value) => {
          if (value && typeof value == 'boolean') {
            this.showAction = true;
          } else {
            this.showAction = false;
          }
        });
    }

    // this.fb.valueChanges.subscribe((value) => {
    //   const _value = this.removeNullValue(value);
    //   this.group.controls[this.config.name].patchValue(_value);
    //   if (!isEqual(this.prevValue, _value)) {
    //     this.makeControlDirty();
    //   }
    // });
  }

  // Implement ngOnDestroy to clean up subscriptions
  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  getReadOnlyValue() {
    if (this.value?.default) return this.value.default;
    return '--';
  }

  onTouch() {
    this.group.get(this.config.name)?.markAsTouched();
  }

  get control() {
    return this.group.get(this.config.name);
  }

  private makeControlDirty() {
    this.control?.markAsDirty();
    this.control?.markAsTouched();
  }

  translatePopup = false;
  private prevValue: NzSafeAny = null;

  openTranslatePopup() {
    this.prevValue = this.removeNullValue(this.fb.value);
    this.translatePopup = true;
  }

  removeNullValue(value: NzSafeAny) {
    return Object.keys(value).reduce(
      (acc, key) => {
        if (value[key as keyof typeof value] !== null) {
          acc[key as string] = value[key as string];
        }
        return acc;
      },
      {} as { [key: string]: NzSafeAny },
    );
  }

  onCancel(isSave?: boolean) {
    this.translatePopup = false;
    if (isSave) {
      this._value = this._valueTranslation;
      const value = this.removeNullValue(this.fb.value);
      this.group.controls[this.config.name].setValue(value);
      this.makeControlDirty();
    } else {
      this.fb.patchValue(this.prevValue);
    }
  }

  trim = trim;
  onChange(v: string) {
    const formValue = { ...this.fb.value };
    formValue.default = v;
    this._value = v;
    // Only update the local form group, not the parent
    this.fb.patchValue(formValue, { emitEvent: false });
  }

  checkValid() {
    if (this.translatePopup) {
      return trim(this._valueTranslation) === '' ? true : false;
    }
    return false;
  }

  _valueTranslation = '';
  onFieldChange(v: string, name: string) {
    debugger
    if (name === 'default') {
      this._valueTranslation = trim(v);
    }
  }

  get isDisabled() {
    return this.group?.get(this.config?.name)?.disabled ?? false;
  }

  onBlur() {
    // Get the current value from the local form
    const value = this.removeNullValue(this.fb.value);

    // Only update the parent if the value has changed
    if (!isEqual(this.prevValue, value)) {
      // Update the previous value reference
      this.prevValue = { ...value };

      // Update the parent form control
      this.group.controls[this.config.name].setValue(value);

      // Mark as touched and dirty
      this.makeControlDirty();
    }

    // Always mark as touched on blur
    this.group.get(this.config.name)?.markAsTouched();
  }
}
