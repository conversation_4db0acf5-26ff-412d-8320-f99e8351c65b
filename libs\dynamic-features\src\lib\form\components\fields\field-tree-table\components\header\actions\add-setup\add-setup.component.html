<hrdx-button
  title="Add setup"
  [isLeftIcon]="true"
  [type]="'tertiary'"
  leftIcon="icon-plus"
  (clicked)="handleOpenDetailForm()"
/>
<hrdx-modal
  [isVisible]="isVisible"
  [title]="config().title"
  (canceled)="isVisible = false"
  [maskClosable]="false"
  [footer]="footer"
  *ngIf="isVisible"
>
  <dynamic-form
    [config]="config().form"
    [reset]="reset"
    [ppxClass]="'ppxm-style'"
    [sources]="config().sources"
    [variables]="config().variables"
    [extend]="{
      extendFormValues: extendDataDynamicForm,
    }"
    #formObj
  ></dynamic-form>
  <ng-template #footer>
    <footer>
      <hrdx-button
        [type]="'ghost-gray'"
        [title]="'Cancel'"
        (clicked)="isVisible = false"
      />
      <hrdx-button
        [type]="'primary'"
        [disabled]="!dynamicForm?.valid"
        [title]="'Confirm'"
        (clicked)="Confirm(dynamicForm?.value)"
      />
    </footer>
  </ng-template>
</hrdx-modal>
