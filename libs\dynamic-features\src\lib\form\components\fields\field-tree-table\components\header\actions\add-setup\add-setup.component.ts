import { ButtonComponent, ModalComponent } from '@hrdx/hrdx-design';
import {
  Component,
  forwardRef,
  inject,
  On<PERSON><PERSON>roy,
  OnInit,
  signal,
  ViewChild,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormComponent } from '../../../../../../../form.component';
import { AddSetupI } from './add-setup.models';
import { TableService } from '../../../../services/table/table.service';
import { DynamicFormService } from 'libs/dynamic-features/src/lib/form/services/dynamic-form.service';

@Component({
  selector: 'dynamic-add-setup',
  standalone: true,
  imports: [
    CommonModule,
    ButtonComponent,
    ModalComponent,
    forwardRef(() => FormComponent),
  ],
  templateUrl: './add-setup.component.html',
  styleUrl: './add-setup.component.less',
})
export class AddSetupComponent implements OnInit, <PERSON><PERSON><PERSON>roy {
  dynamicService = inject(DynamicFormService);
  treTableService = inject(TableService);
  config = signal<AddSetupI>({
    title: 'Add',
    form: {},
    sources: {},
    variables: {},
  });
  isVisible = false;
  reset = false;
  @ViewChild('formObj') dynamicForm?: FormComponent;

  ngOnInit(): void {
    let init: AddSetupI = {
      title: 'Add',
      form: {},
      sources: {},
      variables: {},
    };
    this.treTableService.currentConfig.subscribe((data) => {
      if (data?.actions?.addSetup) {
        init = { ...data.actions.addSetup };
      }
    });
    this.config.set({ ...init });
  }

  getJsonataExpression = async (expression: string) => {
    const res = await this.dynamicService.getJsonataExpression({})(
      expression,
      this.dynamicForm?.valueDef,
    );
    return this.treTableService.pushDatas(
      res,
      this.config().uniqueFields,
      this.config().addValidate,
    );
  };

  async Confirm(data: Record<string, unknown>) {
    if (this.config()?.specificData) {
      const isSuccess = await this.getJsonataExpression(
        this.config().specificData ?? '',
      );
      if (this.config().addValidate && !isSuccess) {
        this.isVisible = true;
      } else {
        this.isVisible = false;
      }
    } else {
      this.treTableService.pushData(data);
      this.isVisible = false;
    }
  }

  extendDataDynamicForm: Record<string, unknown> = {};

  handleOpenDetailForm() {
    const formValues = this.treTableService.getFormValues()?.value?.['fields'];
    this.extendDataDynamicForm = formValues as Record<string, unknown>;

    this.isVisible = true;
  }

  ngOnDestroy(): void {}
}
