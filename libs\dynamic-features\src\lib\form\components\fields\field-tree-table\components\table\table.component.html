<hrdx-new-table
  [data]="dataFilter()"
  [loading]="loading()"
  (selectedItemChange)="listOfSelectedItems.set($event)"
  [showCheckbox]="!readOnly"
  [scrollHeight]="'500px'"
  [showCreateDataTable]="false"
  [showPagination]="config.showPagination ?? false"
  (pageIndexChange)="pageIndex.set($event)"
  (pageSizeChange)="pageSize.set($event); pageIndex.set(1)"
  [total]="dataFilter().length"
  [pageIndex]="pageIndex()"
  [pageSize]="pageSize()"
  class="field-tree-table"
  [fixedActionColumn]="config.layout_option?.fixed_action_column && !readOnly"
>
  <hrdx-thead>
    <hrdx-th
      *ngFor="let col of columns()"
      [width]="col.width || 15"
      [fixedLeft]="col.pinned"
      [align]="col.align ?? 'left'"
    >
      {{ col.title }}
      <span *ngIf="isColRequired(col)" class="text-error">*</span>
    </hrdx-th>
  </hrdx-thead>

  <!-- group -->
  <ng-container *ngFor="let group of dataGroup() | keyvalue">
    <hrdx-tbody
      [hiddenAction]="true"
      [expand]="true"
      (expandChange)="ExpandRow(group!.key)"
    >
      <hrdx-td [colSpan]="columns().length" className="group-header">
        {{ keyTitle() ? group.value[0][keyTitle()] : group.key }}
      </hrdx-td>
    </hrdx-tbody>
    <ng-container *ngIf="expandKey()[group.key]">
      <hrdx-tbody [value]="row" *ngFor="let row of group.value; let i = index">
        <hrdx-td *ngFor="let col of columns()">
          @if (col?.display_type === 'currency') {
            @if (col?.readOnly) {
              <hrdx-display
                [type]="'Label'"
                [value]="formatter(row[col.code])"
                [title]="col.title"
                [href]="col.href"
              ></hrdx-display>
            } @else {
              <dynamic-input-lazy
                *ngIf="!readOnly"
                [value]="row[col.code]"
                (valueChanged)="inputChanged($event, col.code, row)"
                [placeholder]="col.placeholder"
                [type]="'Currency'"
                [touched]="
                  inputTouchedState()[i]?.[col.code] ?? allFieldsTouched()
                "
                (touchedChange)="onInputTouchedStateChange($event, i, col.code)"
                [validators]="col.validators ?? []"
                [settings]="col.inputSettings"
                (validChange)="onInputValidStateChange($event, i, col.code)"
              ></dynamic-input-lazy>
            }
          } @else if (col?.display_type) {
            <dynamic-input-lazy
              *ngIf="!readOnly"
              [value]="row[col.code]"
              (valueChanged)="inputChanged($event, col.code, row)"
              [placeholder]="col.placeholder"
              [type]="'Label'"
              [touched]="
                inputTouchedState()[i]?.[col.code] ?? allFieldsTouched()
              "
              (touchedChange)="onInputTouchedStateChange($event, i, col.code)"
              [validators]="col.validators ?? []"
              [formatType]="col.formatType"
              (validChange)="onInputValidStateChange($event, i, col.code)"
            ></dynamic-input-lazy>
          } @else if (col?.autoIndex) {
            <hrdx-display
              [type]="col?.display_type || 'Label'"
              [value]="i + 1"
              [title]="col.title"
              [href]="col.href"
            ></hrdx-display>
          } @else {
            <hrdx-display
              [type]="col?.display_type || 'Label'"
              [value]="row[col.code]"
              [title]="col.title"
              [href]="col.href"
            ></hrdx-display>
          }
        </hrdx-td>
        <dynamic-row-actions *ngIf="!readOnly" row-actions [data]="row" />
      </hrdx-tbody>
    </ng-container>
  </ng-container>

  <dynamic-selected-actions
    selected-actions
    [listOfSelectedItems]="listOfSelectedItems()"
  />
</hrdx-new-table>
