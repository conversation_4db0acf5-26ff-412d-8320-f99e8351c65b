import { FormControlService } from './../../../services/form-control.service';
import { CommonModule } from '@angular/common';
import {
  AfterViewInit,
  Component,
  EventEmitter,
  inject,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';
import { FormGroup } from '@angular/forms';
import {
  BehaviorSubject,
  distinctUntilChanged,
  forkJoin,
  of,
  switchMap,
  tap,
} from 'rxjs';
import { Field, FieldConfig, Values } from '../../../models';
import { DynamicFormService } from '../../../services/dynamic-form.service';
import { HeaderComponent } from './components/header/header.component';
import { TableSimpleComponent } from './components/table-simple/table-simple.component';
import { TableTreeMultipleLevelComponent } from './components/table-tree-multiple-level/table-tree-multiple-level.component';
import { TableComponent } from './components/table/table.component';
import { TreeFormComponent } from './components/tree-form/tree-form.component';
import { FieldTreeTableConfigI } from './field-tree-table.models';
import { TableService } from './services/table/table.service';
import { TreeFormService } from './services/tree-form/tree-form.service';

@Component({
  selector: 'dynamic-field-tree-table',
  standalone: true,
  imports: [
    CommonModule,
    TableComponent,
    HeaderComponent,
    TreeFormComponent,
    TableTreeMultipleLevelComponent,
    TableSimpleComponent,
  ],
  providers: [TableService, TreeFormService],
  templateUrl: './field-tree-table.component.html',
  styleUrl: './field-tree-table.component.less',
})
export class FieldTreeTableComponent
  implements Field, OnInit, AfterViewInit, OnChanges
{
  group!: FormGroup;
  field!: FieldConfig;
  config!: FieldTreeTableConfigI;
  @Input() values: Values = {};
  values$ = new BehaviorSubject<Values>(this.values);
  tableService = inject(TableService);
  service = inject(DynamicFormService);
  formControlService = inject(FormControlService);
  curId = '';

  @Output() invalidEmitter = new EventEmitter<boolean>();

  ngAfterViewInit(): void {
    if (this.config._dataSource) {
      this.values$
        .pipe(
          distinctUntilChanged((prev, curr) =>
            this.service.distinct(prev, curr, this.config._dataSource),
          ),
          switchMap((values) => {
            return this.service.getObservable(
              values.function,
              {
                ...values,
              },
              this.config._dataSource,
            );
          }),
        )
        .subscribe((value) => {
          if (this.config._dataSourceRequestStatus) {
            const requestStatus =
              this.values.variables?.[
                this.config._dataSourceRequestStatus + '_requestStatus'
              ].requestStatus;
            if (requestStatus === 'loading') this.tableService.setLoading(true);
            if (requestStatus === 'success' || requestStatus === 'error')
              this.tableService.setLoading(false);
          }
          if (this.config.updateDataSource === 'replace') {
            this.tableService.mergeData(value);
          } else this.tableService.pushDatas(value);
        });
    }

    if (this.config.actions?.export) {
      this.tableService.setExportInfo(
        this.config.actions?.export?.filterQuery,
        this.config.actions?.export?.backendUrl,
        this.formControlService.getData()?.formGroup?.value,
      );
    }
  }
  formatTableConfig() {
    const indexs: number[] = [];
    this.values$
      .pipe(
        switchMap((values) => {
          return forkJoin(
            this.config.columns.map((column, index) => {
              return of(values).pipe(
                distinctUntilChanged((prev, curr) =>
                  this.service.distinct(prev, curr, column._condition),
                ),
                switchMap((values) =>
                  this.service.getObservable(
                    values.function,
                    values,
                    column._condition,
                  ),
                ),
                tap((visible) => {
                  if (visible === false) {
                    indexs.findIndex((i) => i === index) === -1
                      ? indexs.push(index)
                      : null;
                  }
                  if (visible === true) {
                    indexs.findIndex((i) => i === index) !== -1
                      ? indexs.splice(
                          indexs.findIndex((i) => i === index),
                          1,
                        )
                      : null;
                  }
                }),
              );
            }),
          );
        }),
        tap(() => {
          const columns = this.config.columns.filter(
            (_, index) => !indexs.includes(index),
          );
          const configColumn = structuredClone(this.config);
          configColumn.columns = columns;
          //this.config.columns = columns;

          this.tableService.changeConfig({ ...configColumn });
        }),
      )
      .subscribe();
  }
  ngOnInit() {
    this.curId = this.values?.extend?.['defaultValue']?.id;
    this.formatTableConfig();
    this.tableService.currentData.subscribe((data) => {
      this.group.get(this.config.name)?.setValue(data);
    });

    this.tableService.invalid.subscribe((invalid) =>
      this.invalidEmitter.emit(invalid),
    );

    this.formControlService.formTouchedBehavior$.subscribe((touched) =>
      this.tableService.setAllFieldsAsTouched(touched),
    );
  }
  ngOnChanges(changes: SimpleChanges) {
    if (changes['values']) {
      this.values$.next(this.values);
      this.tableService.changeFormValues(
        this.values as Record<string, unknown>,
      );
    }
  }
}
