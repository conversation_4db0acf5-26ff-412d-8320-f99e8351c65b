import { TreeFormConfig } from './components/tree-form/tree-form.models';
import { SourceField } from '../../../models';
import { z } from 'zod';
import { RowActionsI } from './components/common/row-actions/row-actions.models';
import { ButtonI } from './components/common/selected-actions/selected-actions.models';
import { ActionsI } from './components/header/actions/actions.models';
import { QuickAddI } from './components/common/selected-actions/quickAdd/quickAdd.models';
import { NzSafeAny } from 'ng-zorro-antd/core/types';
import { InputSettings } from '@hrdx/hrdx-design';

export type FieldValidator = {
  type: 'required' | 'min' | 'max';
  args?: any;
  text?: string;
};

export interface Column {
  code: string;
  title: string;
  width?: number;
  align?: 'left' | 'right' | 'center';
  pinned?: boolean;
  href?: string;
  readOnly: boolean;
  required: boolean;
  autoIndex?: boolean;
  display_type?: string;
  unvisible?: boolean;
  _condition?: {
    transform: string;
  };
  placeholder?: string;
  validators?: FieldValidator[];
  formatType?: 'code';
  inputSettings?: InputSettings;
}

const columnSchema: z.ZodType<any> = z.object({
  title: z.string(),
  code: z.string(),
  key: z.string(),
  id: z.string(),
  width: z.number().optional(),
  pinned: z.boolean().optional(),
  data_type: z.object({
    key: z.string(),
  }),
  display_type: z.object({
    key: z.string(),
  }),
  align: z.enum(['left', 'center', 'right']).optional(),
  children: z.array(z.lazy(() => columnSchema)).optional(),
  level: z.number().default(0),
  parent: z.lazy(() => columnSchema).optional(),
  type: z
    .enum(['text', 'number', 'date', 'select', 'checkbox', 'radio'])
    .optional(),
});

const fieldTreeTableSchema = z.object({
  columns: z.array(columnSchema),
  dataSource: z.record(z.string(), z.any()),
});
type FieldTreeTable = z.infer<typeof fieldTreeTableSchema>;
export { FieldTreeTable, fieldTreeTableSchema };

export interface FieldTreeTableConfigI {
  name: string;
  type: string;
  columns: Column[];
  value: string[];
  dataSource: FieldTreeTable['dataSource'][];
  _dataSource?: SourceField;
  _dataSourceRequestStatus?: 'loading' | 'success' | 'error';
  layout_option?: {
    actions_many?: ButtonI[];
    actions_many_handler?: {
      quickAdd?: QuickAddI;
      deleteBySelection?: NzSafeAny;
    };
    action_row?: ButtonI[];
    action_row_disabled?: string[];
    show_actions_many?: boolean;
    hide_action_row?: boolean;
    disabled_click_row?: boolean;
    show_detail_history?: boolean;
    show_dialog_form_save_add_button?: boolean;
    show_dialog_form_delete_button?: boolean;
    tool_table?: {
      show_table_checkbox?: boolean;
      show_table_filter?: boolean;
      expand_filter?: boolean;
      show_table_group?: boolean;
      hidden_header?: boolean;
      collapse?: boolean;
      show_table_search?: boolean;
      have_child_item_checkbox?: boolean;
    };
    show_pagination?: boolean;
    fixed_action_column?: boolean;
  };
  groupMultiple?: {
    title: {
      key: string;
      label: string;
      onlyKey?: string;
      keyLabel?: string;
    }[];
    expandDefault?: boolean;
    expandRowsWithValues?: boolean;
    expandRowsWithValuesDefault?: boolean;
  };
  group?: {
    keyTitle: string;
    keyGroup: string;
  };
  actions?: ActionsI;
  treeForm?: TreeFormConfig;
  rowActions?: RowActionsI;
  readOnly?: boolean;
  showCheckbox?: boolean;
  showPagination?: boolean;
  hideRowAction?: boolean;
  updateDataSource?: 'replace' | 'merge';
}
