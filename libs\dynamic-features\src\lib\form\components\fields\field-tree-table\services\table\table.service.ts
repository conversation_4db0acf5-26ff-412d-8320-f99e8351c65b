import { Source } from './../../../../../models/field-config.interface';
import { DynamicFormService } from './../../../../../services/dynamic-form.service';
import { inject, Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { FieldTreeTableConfigI } from '../../field-tree-table.models';
import { QueryFilter } from '@nestjsx/crud-request';
import { ToastMessageComponent } from '@hrdx/hrdx-design';
import { NzSafeAny } from 'ng-zorro-antd/core/types';
import { size } from 'lodash';
import {
  AddValidate,
  TypeErrorEnum,
} from '../../components/header/actions/add-setup/add-setup.models';

@Injectable()
export class TableService {
  private dataTable = new BehaviorSubject<Record<string, unknown>[]>([]);
  private dynamicService = inject(DynamicFormService);
  private toast = inject(ToastMessageComponent);
  private invalidSubject = new BehaviorSubject<boolean>(false);
  private allTouchedSubject = new BehaviorSubject<boolean>(false);
  private formValues = new BehaviorSubject<Record<string, unknown>>({});

  changeFormValues(data: Record<string, unknown>) {
    this.formValues.next(data);
  }

  getFormValues() {
    return this.formValues;
  }

  currentData = this.dataTable.asObservable();
  changeData(data: Record<string, unknown>[]) {
    this.dataTable.next(data);
  }

  invalid = this.invalidSubject.asObservable();
  setInvalid(invalid: boolean) {
    this.invalidSubject.next(invalid);
  }

  isInvalid() {
    return this.invalidSubject.value;
  }

  allTouched = this.allTouchedSubject.asObservable();
  setAllFieldsAsTouched(touched: boolean) {
    this.allTouchedSubject.next(touched);
  }

  deleteItemsWithPrecheck(
    items: Record<string, unknown>[],
    precheckSource: Source,
  ) {
    let hasErrorWhenCallPrecheck = false;
    const onError = (err: NzSafeAny) => {
      hasErrorWhenCallPrecheck = true;
      const message = err?.error?.message ?? err?.error?.messageError ?? '';
      this.toast.showToast('error', 'Error', message);
    };

    // in case items has only one item then data transform is the only item else wrap items into object.
    const dataTransform = items.length === 1 ? items[0] : { items };
    this.dynamicService
      .getValueFromApi(precheckSource, dataTransform, { onError })
      .subscribe((res) => {
        if (!res && hasErrorWhenCallPrecheck) return;
        items.forEach((item) => this.deleteItem(item));
      });
  }

  deleteItem(item: Record<string, unknown>) {
    const currentData = this.dataTable.getValue();
    const index = currentData.indexOf(item);
    if (index > -1) {
      currentData.splice(index, 1);
      this.dataTable.next([...currentData]);
    }
  }

  pushData(data: Record<string, unknown>) {
    const currentData = this.dataTable.getValue();
    if (
      currentData.some((item) => JSON.stringify(item) === JSON.stringify(data))
    ) {
      return;
    }
    currentData.push(data);
    this.dataTable.next([...currentData]);
  }
  pushDatas(
    data: Record<string, unknown>[],
    uniqueFields?: string[],
    addValidate?: AddValidate,
  ) {
    const currentData = this.dataTable.getValue();
    const newData = data?.filter(
      (newItem) =>
        !currentData.some((existingItem) => {
          if (uniqueFields) {
            const existingItemObj: Record<string, unknown> = {};
            const newItemObj: Record<string, unknown> = {};
            uniqueFields.forEach((field) => {
              existingItemObj[field] = existingItem[field];
              newItemObj[field] = newItem[field];
            });
            return (
              JSON.stringify(existingItemObj) === JSON.stringify(newItemObj)
            );
          }
          return JSON.stringify(existingItem) === JSON.stringify(newItem);
        }),
    );
    if (addValidate) {
      let errorFlag = false;
      switch (addValidate.type) {
        case TypeErrorEnum.NoDataAdded:
          if (size(data) && !size(newData)) errorFlag = true;
          break;
      }

      if (errorFlag && addValidate.errorMessage) {
        this.toast.showToast('error', 'Error', addValidate.errorMessage);
        return false;
      }
    }

    if (newData) {
      currentData?.push(...newData);
      this.dataTable.next([...currentData]);
    }
    return true;
  }

  mergeData(data: Record<string, unknown>[]) {
    const currentData = this.dataTable.getValue();
    const mergedData = data.map((newItem) => {
      const existingItem = currentData.find(
        (item) => item['id'] === newItem['id'],
      );
      return existingItem ? existingItem : newItem;
    });
    this.dataTable.next(data);
  }

  editData(oldItem: Record<string, unknown>, newItem: Record<string, unknown>) {
    const currentData = this.dataTable.getValue();
    const index = currentData.findIndex(
      (item) => JSON.stringify(item) === JSON.stringify(oldItem),
    );
    if (index > -1) {
      currentData[index] = newItem;
      this.dataTable.next([...currentData]);
    }
  }

  editCellData(
    oldItem: Record<string, unknown>,
    value: NzSafeAny,
    colName: string,
  ) {
    const currentData = this.dataTable.getValue();
    const index = currentData.findIndex(
      (item) => JSON.stringify(item) === JSON.stringify(oldItem),
    );
    if (index > -1) {
      currentData[index][colName] = value;
      this.dataTable.next([...currentData]);
    }
  }

  private loading = new BehaviorSubject<boolean>(false);
  isLoading = this.loading.asObservable();
  setLoading(isLoading: boolean) {
    this.loading.next(isLoading);
  }

  private config = new BehaviorSubject<FieldTreeTableConfigI>({
    name: '',
    type: 'treeTable',
    columns: [],
    value: [],
    dataSource: [],
    actions: {},
    showPagination: true,
  });
  currentConfig = this.config.asObservable();
  changeConfig(config: FieldTreeTableConfigI) {
    this.config.next(config);
  }

  private actions = new BehaviorSubject<Record<string, NzSafeAny>>({
    expandRowsWithValues: false,
  });
  currentActions = this.actions.asObservable();
  changeActions(newactions: Record<string, unknown>) {
    const actions = this.actions.getValue();
    this.actions.next({ ...actions, ...newactions });
  }

  private treeData = new BehaviorSubject<Record<string, unknown>[]>([]);
  currentTreeData = this.treeData.asObservable();

  changeTreeData(data: Record<string, unknown>[]) {
    this.treeData.next(data);
  }

  private localSearch = new BehaviorSubject<string>('');
  currentLocalSearch = this.localSearch.asObservable();
  changeLocalSearch(search: string) {
    this.localSearch.next(search);
  }

  private exportFilterQuery: QueryFilter[] = [];
  private exportBackendUrl = '';
  private formValue: Record<string, unknown> = {};

  public getExportInfo() {
    return {
      filterQuery: this.exportFilterQuery,
      backendUrl: this.exportBackendUrl,
      formValue: this.formValue,
    };
  }

  public setExportInfo(
    filterQuery: QueryFilter[],
    backendUrl: string,
    formValue: Record<string, unknown>,
  ) {
    this.exportFilterQuery = filterQuery;
    this.exportBackendUrl = backendUrl;
    this.formValue = formValue;
  }
}
