@if (!config.readOnly) {
  <hrdx-upload
    type="file"
    width="100%"
    [multiple]="isMultiple"
    [(fileList)]="fileList"
    [beforeUpload]="beforeUpload"
    (change)="change($event)"
    [previewFile]="handlePreview"
    [fileType]="accept"
    [maxFileSize]="size ?? 5"
    [fileTypeLabel]="fileTypeLabel"
    (fileRemove)="fileRemove($event)"
    [customContentUpload]="customContentUpload"
    [hasDivider]="hasDivider"
    [paddingBottomNone]="paddingBottomNone"
  >
  </hrdx-upload>
} @else {
  @if (!config.canAction) {
    <div class="file-container">
      @for (file of currentFileList; track file) {
        <div class="file">
          <hrdx-display
            (click)="handleDownload(file)"
            [type]="'Hyperlink'"
            [value]="file.fileName"
          ></hrdx-display>
        </div>
      } @empty {
        --
      }
    </div>
  } @else {
    <div [ngClass]="'file-list'">
      @for (file of currentFileList; track file) {
        <div class="file">
          <hrdx-upload-icon-item [fileName]="file.fileName" />

          <div [ngClass]="'file-list-name'">
            <div class="file-name" (click)="handleDownload(file)">
              {{ file.fileName }}

              <div class="file-status">
                <hrdx-icon
                  class="file-status-icon"
                  [name]="'icon-check-circle-bold'"
                  [size]="'small'"
                >
                </hrdx-icon>

                <span class="file-status-name">Completed</span>
              </div>
            </div>
          </div>
          <div [ngClass]="'file-list-action'">
            <!-- <hrdx-icon *ngIf="file.status === 'done'" [name]="'check-circle'" />cc
            <hrdx-icon *ngIf="file.status === 'error'" [name]="'warning'" /> -->
            <div (click)="fileRemove(file.key)">
              <hrdx-icon [name]="'icon-trash-bold'" [size]="'small'" />
            </div>
          </div>
        </div>
      }
    </div>
  }
}
