import { CommonModule } from '@angular/common';
import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  inject,
  Input,
  signal,
  OnDestroy,
  SimpleChanges,
  OnChanges,
} from '@angular/core';
import { FormGroup } from '@angular/forms';

import { NzSafeAny } from 'ng-zorro-antd/core/types';
import { NzUploadChangeParam, NzUploadFile } from 'ng-zorro-antd/upload';

import { Field, FieldUpload, Values } from '../../../models';
import {
  IconComponent,
  UploadComponent,
  DisplayComponent,
  ToastMessageComponent,
  ModalComponent,
  UploadIconItemComponent,
} from '@hrdx/hrdx-design';
import {
  BehaviorSubject,
  catchError,
  combineLatest,
  distinctUntilChanged,
  map,
  Observable,
  of,
  Subject,
  switchMap,
  takeUntil,
  tap,
} from 'rxjs';
import { DynamicFormService } from '../../../services/dynamic-form.service';
import { isNil } from 'lodash';
export interface FileType {
  FieldValue?: 'file';
  uid: string;
  name: string;
  type?: string;
  data?: string;
  url?: string;
  fileField?: string;
  fileValue?: string;
}
@Component({
  selector: 'dynamic-field-upload',
  standalone: true,
  imports: [
    CommonModule,
    UploadComponent,
    IconComponent,
    DisplayComponent,
    UploadIconItemComponent,
  ],
  providers: [ToastMessageComponent, ModalComponent],
  templateUrl: './field-upload.component.html',
  styleUrls: ['./field-upload.component.less'],
})
export class FieldUploadComponent
  implements Field, AfterViewInit, OnDestroy, OnChanges
{
  config!: FieldUpload;
  group!: FormGroup;
  @Input() values: Values = {};
  accept?: string | string[] = [];
  fileTypeLabel = 'PDF, XLS, XLSX';
  customContentUpload = '';
  size?: number;
  fileList: FileType[] = [];
  currentFileList: AttachFileResults[] = [];
  fileControl: FileType[] | undefined = [];
  isMultiple = true;
  avatarSize = 80;
  mode?: string;
  openModal = false;
  toast = inject(ToastMessageComponent);
  hasDivider = false;
  paddingBottomNone = false;
  modalComponent = inject(ModalComponent);

  constructor(private cdr: ChangeDetectorRef) {}

  beforeUpload = (file: NzUploadFile): boolean => {
    let result = false;
    if (!this.isMultiple && this.fileList.length === 1) {
      const { content, title } = this.config.confirmPopup ?? {};

      this.modalComponent.showDialog(
        {
          nzContent:
            content ??
            'Uploading a new file will automatically overwrite the existing file. Do you want to proceed?',
          nzTitle: title ?? `File Change Confirmation`,
          nzWrapClassName: 'popup popup-confirm',
          nzIconType: 'icons:warning',
          nzOkText: 'Confirm',
          nzCancelText: 'Cancel',
          nzOnOk: () => {
            result = this.handleUpload(file);
          },
          nzOnCancel: () => {
            result = false;
          },
        },
        'warning',
      );
    } else result = this.handleUpload(file);
    return result;
  };

  get control() {
    return this.group.get(this.config.name);
  }

  private markControlDirty() {
    this.control?.markAsDirty();
    this.control?.markAsTouched();
  }

  handleUpload(file: NzUploadFile): boolean {
    this.getBase64(file as NzSafeAny).then((data) => {
      const newFile: FileType = {
        FieldValue: 'file',
        uid: file.uid,
        name: file.name,
        type: file.type,
        data: data?.toString(),
      };

      this.fileControl = this.isMultiple
        ? this.fileControl?.concat(newFile)
        : [newFile];
      this.setControl(this.fileControl);
    });
    this.fileList = this.isMultiple ? this.fileList.concat(file) : [file];
    return false;
  }

  getBase64(file: File): Promise<string | ArrayBuffer | null> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result);
      reader.onerror = (error) => reject(error);
    });
  }
  handlePreview(e: NzUploadFile): Observable<string> {
    if (!this.fileControl) return of('');
    const idx = this.fileControl?.findIndex((file) => e.uid === file.uid);
    const temporaryDownloadLink = document.createElement('a');
    temporaryDownloadLink.style.display = 'none';
    document.body.appendChild(temporaryDownloadLink);
    temporaryDownloadLink.setAttribute('target', '_blank');
    temporaryDownloadLink.setAttribute(
      'href',
      e.url ?? this.fileControl[idx].data ?? '',
    );
    temporaryDownloadLink.setAttribute('download', e.name ?? '');
    temporaryDownloadLink.click();
    document.body.removeChild(temporaryDownloadLink);
    return of('');
  }

  change(e: NzUploadChangeParam) {
    if (this.fileControl) {
      const idx = this.fileControl.findIndex((file) => e.file.uid === file.uid);
      this.fileControl.splice(idx, 1);
      this.setControl(this.fileControl);
    }
  }

  setControl(value: NzSafeAny) {
    this.group.get(this.config.name)?.setValue(value);
    this.cdr.detectChanges();
  }

  values$ = new BehaviorSubject<Values>({});
  private readonly destroy$ = new Subject<void>();

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['values']) {
      this.values$.next(this.values);
    }
  }

  ngAfterViewInit() {
    this.mode = this.config.mode;
    this.accept =
      this.mode === 'avatar'
        ? 'image/*'
        : (this.config.upload?.accept ?? [
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/pdf',
            'application/vnd.ms-excel',
          ]);
    this.size = this.config.upload?.size;
    if (this.config.upload?._size)
      combineLatest({
        values: this.values$,
      })
        .pipe(
          map(({ values }) => {
            return values;
          }),
          distinctUntilChanged((prev, curr) => {
            return this.service.distinct(prev, curr, this.config.upload?._size);
          }),
          takeUntil(this.destroy$),
          switchMap((values) => {
            return this.service.getObservable(
              this.values.function,
              values,
              this.config.upload?._size,
            );
          }),
          tap(() => this.cdr.detectChanges()),
          tap((size: number) => {
            if (typeof size === 'number') {
              this.size = size;
            }
          }),
          tap(() => this.cdr.detectChanges()),
        )
        .subscribe();

    this.hasDivider = this.config.hasDivider;
    this.paddingBottomNone = this.config.paddingBottomNone;
    this.isMultiple =
      this.mode === 'avatar' ? false : (this.config.upload?.isMultiple ?? true);
    this.avatarSize = this.config.upload?.avatarSize ?? 80;
    this.fileTypeLabel =
      this.config.upload?.fileTypeLabel ?? this.fileTypeLabel;
    this.customContentUpload =
      this.config.upload?.customContentUpload ?? this.customContentUpload;

    const value = this.config.value
      ? Array.isArray(this.config.value)
        ? this.config.value
        : [this.config.value]
      : [];

    this.fileList = (value ?? []).map((it: any, idx: number) => ({
      uid: it?.key ?? idx.toString(),
      name: it?.name ?? (it?.fileName || it),
      key: it?.key ?? '',
      url: it?.url,
      thumbUrl: it?.url,
      status: 'done',
      isCurrentFile: true,
      fileField: it.fileField,
      fileValue: it.fileValue,
    }));

    this.fileControl = (value ?? []).map((it: any, idx: number) => ({
      uid: idx.toString(),
      name: it?.name ?? it?.fileName,
      url: it?.url,
      type: it?.extension ?? it?.type,
    }));
    if (this.config.canAction || this.config.readOnly) {
      this.currentFileList = this.fileList.map((item) => ({
        fileName: item.name,
        key: item.uid,
        url: item.url,
        fileField: item.fileField,
        fileValue: item.fileValue,
      }));
      this.setControl(this.currentFileList);
    }
    if (this.config._value) {
      combineLatest({
        _value: this.values$.pipe(
          distinctUntilChanged((prev, curr) => {
            return this.service.distinct(prev, curr, this.config._value);
          }),
          switchMap(() =>
            this.service.getObservable(
              this.values.function,
              this.values,
              this.config._value,
            ),
          ),
        ),
      })
        .pipe(
          map(({ _value }) => {
            return _value;
          }),
        )
        .subscribe((_value) => {
          const valueArray = _value
            ? Array.isArray(_value)
              ? _value
              : [_value]
            : [];
          this.fileList = (valueArray ?? []).map((it: any, idx: number) => ({
            uid: it?.key ?? idx.toString(),
            name: it?.name || it,
            key: it?.key ?? '',
            url: it?.url,
            thumbUrl: it?.url,
            status: 'done',
            isCurrentFile: true,
            fileField: it.fileField,
            fileValue: it.fileValue,
          }));

          this.fileControl = (valueArray ?? []).map((it: any, idx: number) => ({
            uid: idx.toString(),
            name: it.name,
            url: it.url,
            type: it.extension ?? it.type,
          }));
          if (this.config.canAction || this.config.readOnly) {
            this.currentFileList = this.fileList.map((item) => ({
              fileName: item.name,
              key: item.uid,
              url: item.url,
              fileField: item.fileField,
              fileValue: item.fileValue,
            }));
            this.setControl(this.currentFileList);
          }
        });
    }
  }

  service = inject(DynamicFormService);
  isLoading = signal<boolean>(false);
  handleDownload(file: NzSafeAny) {
    //TODO - dynamic the url when the api is ready
    const url = file?.url || '/api/fo-files/download';

    let filter: NzSafeAny = [];
    if (file?.url && file?.fileValue && file?.fileField) {
      filter = [
        {
          field: file.fileField,
          operator: '$eq',
          value: file.fileValue,
        },
      ];
    } else if (isNil(file?.fileValue) && isNil(file?.fileField)) {
      filter = [
        {
          field: 'fileName',
          operator: '$eq',
          value: file.fileName ?? file.name,
        },
        {
          field: 'key',
          operator: '$eq',
          value: file.key,
        },
      ];
    } else {
      filter = [];
    }
    this.service
      .downloadAttachedFile(url, filter, file.fileName ?? file.name)
      .pipe(
        tap(() => {
          this.isLoading.set(true);
        }),
        catchError((err) => {
          let errorMessage: NzSafeAny = err.error;
          // throw err when 400 while res is blob type.
          if (err.status === 400) {
            errorMessage = { code: 400, message: 'File was not found.' };
          }
          this.toast.showToast('error', 'Error', errorMessage?.message);
          this.isLoading.set(false);
          return of(undefined);
        }),
        tap(() => {
          this.isLoading.set(false);
        }),
      )
      .subscribe();
    return;
  }

  fileRemove(fileId: string) {
    if (this.config.canAction) {
      this.currentFileList = this.currentFileList?.filter(
        (it) => it.key !== fileId,
      );
      this.setControl(this.currentFileList);
    } else {
      this.fileControl = this.fileControl?.filter((it) => it.uid !== fileId);
      this.setControl(this.fileControl);
    }
  }

  get disabled() {
    return this.group.get(this.config.name)?.disabled;
  }

  ngOnDestroy() {
    this.destroy$.next(); // Emit to signal teardown
    this.destroy$.complete(); // Complete the destroy observable
  }
}

interface AttachFileResults {
  key: string;
  fileName: string;
}
