import { Type } from '@angular/core';
import { Field } from '../../models/field.interface';
import { FieldButtonComponent } from './field-button/field-button.component';
import { FieldCalendarComponent } from './field-calendar/field-calendar.component';
import { FieldCascaderNewComponent } from './field-cascader-new/field-cascader-new.component';
import { FieldCascaderComponent } from './field-cascader/field-cascader.component';
import { FieldCheckboxPicklistComponent } from './field-checkbox-picklist/field-checkbox-picklist.component';
import { FieldCheckboxComponent } from './field-checkbox/field-checkbox.component';
import { FieldColorComponent } from './field-color/field-color.component';
import { FieldDatepickerComponent } from './field-datepicker/field-datepicker.component';
import { FieldEditorComponent } from './field-editor/field-editor.component';
import { FieldGroupCheckboxComponent } from './field-group-checkbox/field-group-checkbox.component';
import { FieldHeadingComponent } from './field-heading/field-heading.component';
import { FieldInputNumberComponent } from './field-input-number/field-input-number.component';
import { FieldInputComponent } from './field-input/field-input.component';
import { FieldJobComponent } from './field-job/field-job.component';
import { FieldListCollapseComponent } from './field-list-collapse/field-list-collapse.component';
import { FieldMultitypeComponent } from './field-multitype/field-multitype.component';
import { FieldRadioComponent } from './field-radio/field-radio.component';
import { FieldRatingComponent } from './field-rating/field-rating.component';
import { FieldSectionItemComponent } from './field-section-item/field-section-item.component';
import { FieldSelectAllComponent } from './field-select-all/field-select-all.component';
import { FieldSelectCustomComponent } from './field-select-custom/field-select-custom.component';
import { FieldSelectImportComponent } from './field-select-import/field-select-import.component';
import { FieldSelectPicklistComponent } from './field-select-picklist/field-select-picklist.component';
import { FieldSelectTabComponent } from './field-select-tab/field-select-tab.component';
import { FieldSelectComponent } from './field-select/field-select.component';
import { FieldSwitchComponent } from './field-switch/field-switch.component';
import { FieldTableSelectComponent } from './field-table-select/field-table-select.component';
import { FieldTableComponent } from './field-table/field-table.component';
import { FieldTelComponent } from './field-tel/field-tel.component';
import { FieldTextComponent } from './field-text/field-text.component';
import { FieldTranslationTextAreaComponent } from './field-textarea-translation/field-textarea-translation.component';
import { FieldTextareaComponent } from './field-textarea/field-textarea.component';
import { FieldTimepickerComponent } from './field-timepicker/field-timepicker.component';
import { FieldTranslationComponent } from './field-translation/field-translation.component';
import { FieldTreeSelectComponent } from './field-tree-select/field-tree-select.component';
import { FieldUploadComponent } from './field-upload/field-upload.component';
import { FieldUsersComponent } from './field-users/field-users.component';
import { FieldYearCalendarComponent } from './field-year-calendar/field-year-calendar.component';
import { FieldTreeTableComponent } from './field-tree-table/field-tree-table.component';
import { FieldRadioTableComponent } from './field-radio-table/field-radio-table.component';
import { FieldOverviewComponent } from './field-overview/field-overview.component';
import { FieldSelectWithActionComponent } from './field-select-with-action/field-select-with-action.component';
import { ShowTabSelectComponent } from './show-tab-select/show-tab-select.component';
import { FieldTagViewComponent } from './field-tag-view/field-tag-view.component';
import { FieldInputCurrencyComponent } from './field-input-currency/field-input-currency.component';

export * from './field-cascader/field-cascader.component';
export * from './field-checkbox/field-checkbox.component';
export * from './field-datepicker/field-datepicker.component';
export * from './field-input-number/field-input-number.component';
export * from './field-input/field-input.component';
export * from './field-select-all/field-select-all.component';
export * from './field-select/field-select.component';
export * from './field-textarea/field-textarea.component';
export * from './field-timepicker/field-timepicker.component';
export * from './field-upload/field-upload.component';
export * from './field-users/field-users.component';
export const supportedFields: { [type: string]: Type<Field> } = {
  text: FieldInputComponent,
  paragraph: FieldTextComponent,
  password: FieldInputComponent,
  email: FieldInputComponent,
  number: FieldInputNumberComponent,
  tel: FieldTelComponent,
  select: FieldSelectComponent,
  selectImport: FieldSelectImportComponent,
  users: FieldUsersComponent,
  cascader: FieldCascaderComponent,
  cascaderNew: FieldCascaderNewComponent,
  dateRange: FieldDatepickerComponent,
  timePicker: FieldTimepickerComponent,
  textarea: FieldTextareaComponent,
  checkbox: FieldCheckboxComponent,
  radioTable: FieldRadioTableComponent,
  radio: FieldRadioComponent,
  upload: FieldUploadComponent,
  rating: FieldRatingComponent,
  multitype: FieldMultitypeComponent,
  switch: FieldSwitchComponent,
  editor: FieldEditorComponent,
  translation: FieldTranslationComponent,
  selectCustom: FieldSelectCustomComponent,
  translationTextArea: FieldTranslationTextAreaComponent,
  groupCheckbox: FieldGroupCheckboxComponent,
  button: FieldButtonComponent,
  sectionItem: FieldSectionItemComponent,
  yearCalendar: FieldYearCalendarComponent,
  selectTab: FieldSelectTabComponent,
  selectAll: FieldSelectAllComponent,
  selectPicklist: FieldSelectPicklistComponent,
  table: FieldTableComponent,
  listCollapse: FieldListCollapseComponent,
  calendar: FieldCalendarComponent,
  color: FieldColorComponent,
  checkboxPicklist: FieldCheckboxPicklistComponent,
  job: FieldJobComponent,
  heading: FieldHeadingComponent,
  treeSelect: FieldTreeSelectComponent,
  tableSelect: FieldTableSelectComponent,
  treeTable: FieldTreeTableComponent,
  overview: FieldOverviewComponent,
  selectWithAction: FieldSelectWithActionComponent,
  showTabSelect: ShowTabSelectComponent,
  tagView: FieldTagViewComponent,
  currency: FieldInputCurrencyComponent,
};
