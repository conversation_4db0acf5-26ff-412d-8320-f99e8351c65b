@import url(../../../styles/theme.less);
@import '../../../../../../../hrdx-design/src/themes/tokens.less';

:host.label-type-flex-row {
  display: flex;
  align-items: center;
}

.field-config {
  position: relative;
  display: flex;
  width: calc(100% * var(--scale-width));
  z-index: 1;

  &.borderless {
    // .field-config__label {
    //   height: 40px;
    // }

    > .field-config__content-wrapper > .field-config__content {
      border-width: 0;
      background-color: transparent;
      padding: 0;

      .ant-select-selector,
      .ant-input {
        border-radius: @border-radius-input;
      }

      .ant-input {
        border-color: @color-border-primary;

        &:hover,
        &:focus {
          border-color: @color-border-active;
        }
      }

      &:not(:has(.dynamic-form)) {
        .ant-input:focus {
          box-shadow: @elevation-input_focus;
        }
      }
    }
  }

  &__content {
    transition: all 0.2s ease;
  }

  &__toast {
    width: 100%;
  }

  .field-config__add-on-bottom {
    margin-top: @spacing-4;
    padding-left: @spacing-4;
  }

  // &:has(.field-config__error) {
  //   .field-config__toast.bottom {
  //     margin-top: @spacing-4;
  //   }
  // }

  &.label-type-col:not(.readonly),
  &.label-type-col-read-only {
    align-items: flex-start;
    flex-direction: column;
    gap: @spacing-basis;
    height: 100%;

    .field-config__label {
      width: 100%;
    }
  }

  &.label-type-row-read-only {
    align-items: flex-start;
    flex-direction: row;
    gap: @spacing-basis;

    @label-width: @size-20 * 10;

    .field-config__label {
      width: @label-width;
    }

    .field-config__content-wrapper {
      max-width: calc(100% - @label-width);
    }
  }

  &.field-flex-end {
    height: @size-40 !important;

    &.odd {
      background-color: @color-bg-surface-secondary;
    }

    &.field-config {
      align-items: center;
      border-bottom: 1px solid @color-border-primary;
      justify-content: flex-end;

      .field-config__label {
        width: 70%;
        justify-content: flex-end;
      }

      .field-config__content-wrapper {
        width: 30%;

        .field-config__content {
          text-align: right;
          padding-right: @spacing-4;

          dynamic-field-input,
          dynamic-field-input-number {
            .form-input-read-only,
            .read-only-text {
              justify-content: flex-end;
            }

            input {
              text-align: right;
              padding: 0 @size-1*3;
            }
          }
        }
      }
    }
  }
}

&.label-type-row {
  align-items: center;
}

&.label-type-row:not(.readonly) {
  gap: 24px;

  .field-config__label {
    width: 176px;
  }

  .field-config__content-wrapper {
    max-width: calc(100% - 200px);
  }
}

&.label-type-flex-row {
  gap: 10px;
  align-items: center;
}

&:not(.disabled) .field-config__content:hover {
  border-color: @color-border-active;
}

.field-config__content {
  flex: auto;
  width: calc(100% * var(--scale-width));
}

.field-config__label {
  flex: none;
}

&.disabled {
  .field-config__content {
    background-color: var(--neutral-grey-5);
    pointer-events: none;
  }
}

&.readonly {
  align-items: flex-start;

  .field-config__label {
    color: @color-text-label;
    // height: 32px;
    width: 174px;
    margin-right: 16px;
    height: unset;
    min-height: @size-24;
    line-height: @size-24;
    // align-items: center;
    align-items: flex-start;
  }

  .field-config__content {
    border-width: 0;
    background-color: transparent;
    padding: 0;
    line-height: @size-24;

    &.text-right {
      text-align: right;
    }
  }

  &.label-type-flex-row {
    .field-config__label {
      height: unset;
      width: unset;
      margin-right: unset;
    }
  }
}

.field-config__label {
  height: @size-40;
  display: flex;
  align-items: center;

  .font-14-semi();
  font-size: @font-size-base;
  font-weight: @font-weight-semibold;
  color: @color-text-label;

  .optional-label {
    .font-12-semi();
    color: var(--neutral-grey-2);
    transform: translateX(4px);
    // margin-left: 4px;
  }
}

.field-config.label-type-col:not(.readonly),
.field-config.label-type-col-read-only {
  .field-config__label {
    // height: @size-20;
    height: unset;
  }
}

.field-config {
  width: 100%;

  .titleTableForm {
    color: @color-base-neutral-10;
  }

  &__content-wrapper {
    width: calc(100% * var(--scale-width));
    display: flex;
    gap: @spacing-1;
    align-items: center;
    overflow-wrap: anywhere;
    position: relative;
    // height: inherit;

    &:has(.field-config__action) {
      align-items: flex-start;
    }

    .hyperlink-text {
      display: inline-block;
      cursor: pointer;
      color: var(--primary-color);
      white-space: nowrap;
      width: max-content;
    }
  }

  &.readonly {
    .field-config__content-wrapper {
      height: 100%;
    }
  }

  &__content {
    // height: @size-40;
    box-sizing: border-box;
    border: 1px solid @color-border-primary;
    border-radius: var(--radius-base);
    background-color: white;
    display: flex;
    gap: @spacing-basis;

    .add-on {
      width: max-content;

      dynamic-field-select .dynamic-field--field-select {
        height: @size-30;

        .ant-select-single:not(.ant-select-customize-input)
          .ant-select-selector {
          height: @size-30;
          padding: 0;
        }

        .suffix-container,
        .ant-select-clear {
          right: 0;
        }

        .ant-select-clear {
          transform: translateY(-9px);
        }
      }

      .dynamic-field--field-select--dropdown {
        &.custom-dropdown {
          .cdk-virtual-scroll-content-wrapper {
            padding: @spacing-1;
          }
        }
      }

      .ant-select-single .ant-select-selector .ant-select-selection-item {
        padding-right: 0;
      }

      > * {
        max-width: 150px;
      }

      &-after {
        position: relative;

        .ant-select-selector {
          padding-right: 0;
        }

        border-left: 1px solid var(--neutral-grey-3);
        padding-left: 6px;

        .ant-select-arrow {
          right: 0;
        }
      }

      &-before {
        position: relative;

        .ant-select-selector {
          padding-left: 0;
        }

        padding-right: 6px;
        border-right: 1px solid var(--neutral-grey-3);
      }
    }
  }

  &:not(.paddingless):not(.borderless):not(.readonly) {
    .field-config__content {
      &:not(.field-config__select-wrapper) {
        padding: 3px 12px;
      }
    }
  }

  &:not(.paddingless):not(.borderless):not(.readonly) &__content:focus-within,
  &:not(.paddingless):not(.borderless):not(.readonly)
    &__content:has(.ant-select-focused) {
    border-color: @color-border-active;
    box-shadow: @elevation-input_focus;
  }

  &:has(.field-config__error):not(:hover, :focus-within):not(:has(.insert-data-section)) {
    .field-config__content {
      border-color: @color-border-destructive;

      .ant-input,
      .ant-select-selector,
      .textarea-wrapper {
        border-color: @color-border-destructive;
      }

      .hrdx-select-with-action {
        ul.select-list {
          li.select-item-wrapper {
            .select-item {
              .ant-select-selector {
                &:not(:hover) {
                  border-color: @color-border-destructive;
                }
              }
            }
          }
        }
      }
    }
  }

  &:has(.select--has-extend) {
    .field-config__content {
      border-width: 0;
      background-color: transparent;
      padding: 0;
    }
  }

  &.disabled:has(.select--has-extend) {
    dynamic-field-select .dynamic-field--select-wrapper {
      .ant-select-selector {
        background-color: var(--neutral-grey-5);
        pointer-events: none;
      }
    }
  }

  &:has(.select--has-extend):not(.paddingless):not(.borderless):not(.readonly)
    &__content:focus-within,
  &:has(.select--has-extend):not(.paddingless):not(.borderless):not(.readonly)
    &__content:has(.ant-select-focused) {
    box-shadow: none;
  }
}

.field-config__error {
  .text-error();
  word-break: break-word;
  animation: slideIn 0.3s;
  color: @color-text-error;
  .font-12-re();

  &-display-absolute {
    position: absolute;
    top: 100%;
    left: 0;
  }
}

.field-config__description {
  margin-left: 5px;
}

.field-config.prefix-label-config:not(.readonly) {
  flex-direction: row !important;
  align-items: center !important;

  .field-config__label {
    width: auto !important;
  }
}

::ng-deep .field-config__content.hyper-link {
  color: red;

  dynamic-field-input {
    span {
      color: blue;
    }
  }
}

.type-grid {
  display: grid;
  gap: @spacing-4;
}

/* Các thuộc tính CSS cho trường hợp có dynamic-field-overview */
.field-config:has(dynamic-field-overview) {
  min-height: 100%;
  display: flex;
  flex-direction: column;

  > .field-config__content-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;

    > .field-config__content {
      flex: 1;
      border: none;
      padding: @spacing-0 @spacing-5 @spacing-5 @spacing-6 !important;
      border-left: 1px solid @color-border-secondary;
      border-radius: 0 !important;

      &:hover {
        border-color: @color-border-secondary !important;
      }
    }
  }
}

@keyframes slideIn {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }

  to {
    transform: translateY(0);
    opacity: 1;
  }
}
