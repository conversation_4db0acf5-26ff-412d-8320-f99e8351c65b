import { CommonModule } from '@angular/common';
import {
  Component,
  ElementRef,
  EventEmitter,
  Host,
  HostListener,
  inject,
  Input,
  OnChanges,
  Output,
  SimpleChanges,
  OnInit,
  ViewChild,
  viewChild,
  computed,
  ChangeDetectorRef,
  output,
} from '@angular/core';
import {
  Form<PERSON>rray,
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
} from '@angular/forms';

// import { ContentComponent } from '@peoplex/ppx-design';
import * as _ from 'lodash';
import { isEqual } from 'lodash';
import {
  BehaviorSubject,
  catchError,
  combineLatest,
  distinctUntilChanged,
  forkJoin,
  map,
  of,
  Subscription,
  switchMap,
  tap,
} from 'rxjs';
import { Values } from '.';
import { DynamicFieldDirective } from './components/dynamic-field.directive';
import { FileType } from './components/fields/field-upload/field-upload.component';
import { FooterComponent } from './components/footer/footer.component';
import { FieldGroupComponent } from './components/group/field-group/field-group.component';
import { FormDirective } from './form.directive';
import {
  FieldGroupConfig,
  FormFieldsConfig,
  Source,
  SourceField,
  validatorTypes,
} from './models/field-config.interface';
import { DynamicFormService } from './services/dynamic-form.service';
import { FormConfigService } from './services/form-config.service';
import { FormControlService } from './services/form-control.service';
import { DynamicFormNewDirective } from './update/dynamicFormNew.directive';
import { NzSafeAny } from 'ng-zorro-antd/core/types';

export enum KEY_CODE {
  UP_ARROW = 38,
  DOWN_ARROW = 40,
  RIGHT_ARROW = 39,
  LEFT_ARROW = 37,
}

@Component({
  selector: 'dynamic-form',
  standalone: true,
  imports: [
    CommonModule,
    FieldGroupComponent,
    ReactiveFormsModule,
    DynamicFieldDirective,
    FormDirective,
    DynamicFormNewDirective,
    FooterComponent,
  ],
  providers: [FormControlService],
  templateUrl: './form.component.html',
  styleUrl: './form.component.less',
})
export class FormComponent implements OnChanges, OnInit {
  @HostListener('window:keydown.arrowdown', ['$event'])
  keyDownEvent(event: KeyboardEvent) {
    const cell = (event.target as HTMLDivElement).closest('td');
    if (!cell) return;
    const row = (event.target as HTMLDivElement).closest('tr');
    if (!row) return;

    cell.id = 'cell-focus';

    let idx = 0;
    for (idx; idx < (row as HTMLDivElement).children.length; idx++) {
      if (
        ((row as HTMLDivElement).children.item(idx) as HTMLDivElement).id ===
        'cell-focus'
      )
        break;
    }
    if (idx === (row as HTMLDivElement).children.length) return;

    cell.id = '';
    (
      (row.nextSibling as HTMLDivElement)
        .querySelectorAll(`td input, td textarea`)
        .item(idx) as HTMLInputElement
    ).focus();
  }
  @HostListener('window:keydown.arrowup', ['$event'])
  keyUpEvent(event: KeyboardEvent) {
    const cell = (event.target as HTMLDivElement).closest('td');
    if (!cell) return;
    const row = (event.target as HTMLDivElement).closest('tr');
    if (!row) return;

    cell.id = 'cell-focus';

    let idx = 0;
    for (idx; idx < (row as HTMLDivElement).children.length; idx++) {
      if (
        ((row as HTMLDivElement).children.item(idx) as HTMLDivElement).id ===
        'cell-focus'
      )
        break;
    }
    if (idx === (row as HTMLDivElement).children.length) return;

    cell.id = '';
    (
      (row.previousSibling as HTMLDivElement)
        .querySelectorAll(`td input, td textarea`)
        .item(idx) as HTMLInputElement
    ).focus();
  }

  service = inject(DynamicFormService);
  formService = inject(FormConfigService);
  private readonly fb = inject(FormBuilder);
  el = inject(ElementRef);
  @Input() sources: { [k: string]: Source } = {};
  @Input() variables: { [k: string]: SourceField } = {};
  @Input() ppxClass = 'ppx-style';
  @Input() space?: number;
  @Input()
  config: FormFieldsConfig[] = [];
  @Input()
  warningText = false;
  @Input()
  valueChangeTrigger = false;
  @Input() extend: { [k: string]: any } = {};
  @Input() formValue: any;
  @Input() reset: any;
  @Input() reload = false;
  @Input() disabled?: boolean;
  @Input() readOnly?: boolean;
  @Input() mode?: string;
  @Input() _mode?: SourceField;
  @Input() id?: string;
  @Input() faceCode?: string | null;
  @Input() authAction?: string;
  @Input() footer?: any;

  @Input() hideFooter = false;
  @Input() checkValueEmpty = false;

  // check if use new dynamic form
  @Input() isNewDynamicForm = false;
  @Output()
  submitted: EventEmitter<unknown> = new EventEmitter<unknown>();

  @Output()
  valueChanges: EventEmitter<{ value: any }> = new EventEmitter<{
    value: any;
  }>();

  reloadFormValue = output();

  @Output() invalidChange = new EventEmitter();
  form: FormGroup = this.fb.group({});
  validatorTypes = validatorTypes;
  @ViewChild('formObj') formRef!: ElementRef;
  dynamicFormDirective = viewChild(FormDirective);

  changes = new BehaviorSubject<any>(undefined);
  @Input() checkPermissionActionFn?: (action: string) => boolean;

  constructor(
    @Host() private formControlService: FormControlService,
    private cdr: ChangeDetectorRef,
  ) {
    this.formControlService.setUpdateFlagStateFn(this.updateFlagStatus);
  }

  private updateFlagStatus = (flag: string) => {
    this.flags = {
      ...(this.flags ?? {}),
      [flag]: !this.flags?.[flag],
    };
    // console.log('flags', this.flags);
  };

  private emitReloadFormValue = () => {
    this.reloadFormValue.emit();
  };

  get formGroup() {
    return this.form;
  }
  valid = true;

  isObjectDeepEmpty = (obj: Record<string, NzSafeAny>) => {
    // Kiểm tra nếu object rỗng
    return _.isEmpty(obj);
  };

  get value() {
    const prevForm = this.changes.value;
    const currForm = this.form?.getRawValue();
    if (this.checkValueEmpty && this.isObjectDeepEmpty(currForm))
      return prevForm;
    if (!_.isEqual(prevForm, currForm)) this.changes.next(currForm);
    return currForm;
  }
  get configGroup() {
    return { fields: this.config } as FieldGroupConfig;
  }
  values$ = new BehaviorSubject<Values>({});

  get valueDef() {
    const values = {
      fields: this.value,
      variables: this.variablesSource,
      extend: this.extend,
      function: this.functionSource,
      flags: this.flags,
      reloadFormValueFn: this.emitReloadFormValue,
      faceCode: this.faceCode,
      authAction: this.authAction ?? undefined,
      checkPermissionFn: this.checkPermissionActionFn,
    };
    // console.log('values', values);
    this.values$.next(values);
    return values;
  }

  subcription = new Subscription();
  newSources: {
    [k: string]: any;
  } = {};
  functionSource?: (transform: string, data: unknown) => Promise<any>;
  variablesSource: { [k: string]: any } = {};
  flags: { [k: string]: boolean } = {};
  ngOnInit() {
    this.form = this.createGroup();
    this.changes.subscribe((value) => this.valueChanges.emit({ value: value }));
  }

  firstLoad = true;

  getFinalValue() {
    const pathList = this.getPath(this.value);
    const newValue$ = pathList.map((path, idx) => {
      const value: FileType = this.service.getValue(this.value, path);

      //upload (value) => observable<{id}>
      return this.service
        .uploadFile(value)
        .pipe(map((data) => ({ ...data, path: path })));
    });
    return combineLatest({
      origin: of(this.value),
      newValue: newValue$.length ? forkJoin(newValue$) : of([]),
    }).pipe(map(this.mappingValue));
  }

  mappingValue(input: {
    origin: any;
    newValue: {
      extension?: string;
      name?: string;
      url?: string;
      path: (string | number)[];
    }[];
  }) {
    const { origin, newValue } = input;
    newValue.forEach((value) => {
      const setValue = (
        data: any,
        path: (string | number)[],
        newValue: any,
      ) => {
        if (!data) {
          return undefined;
        }
        if (path.length < 1) return undefined;
        if (path.length === 1) {
          data[path[0]] = newValue;
        } else {
          const thisPath = path[0];
          path.splice(0, 1);
          if (!thisPath) return;
          data[thisPath] = setValue(data[thisPath], path, newValue);
        }
        return data;
      };

      setValue(origin, value.path, {
        extension: value.extension,
        name: value.name,
        url: value.url,
      });
    });
    return origin;
  }

  getPath(value: any) {
    let path: (string | number)[][] = [];
    if (typeof value === 'object')
      Object.keys(value ?? {}).forEach((key) => {
        if (value[key]?.FieldValue === 'file') {
          path.push([key]);
        } else if (typeof value[key] === 'object' || _.isArray(value[key])) {
          const child = this.getPath(value[key]).map((it) => [key, ...it]);
          path = path.concat(child);
        }
      });
    else if (_.isArray(value))
      value.forEach((it: any, key: number) => {
        if (value[key]?.FieldValue === 'file') {
          path.push([key]);
        } else if (typeof value[key] === 'object' || _.isArray(value[key])) {
          const child = this.getPath(value[key]).map((it) => [key, ...it]);
          path = path.concat(child);
        }
      });

    return path;
  }

  ngOnChanges(changes: SimpleChanges) {
    if (
      changes['config'] &&
      !isEqual(changes['config'].currentValue, changes['config'].previousValue)
    ) {
      this.form = this.createGroup();
    }
    if (changes['reset']) {
      this.form?.reset(this.reset);
      this.formControlService.updateData({ reset: this.reset });
    }
    if (
      changes['sources'] &&
      !_.isEqual(
        changes['sources'].previousValue,
        changes['sources'].currentValue,
      )
    ) {
      this.getFunctionSources(this.sources);
      this.getVariables(this.variables);
    }
    if (
      changes['variables'] &&
      !_.isEqual(
        changes['variables'].previousValue,
        changes['variables'].currentValue,
      )
    ) {
      this.getFunctionSources(this.sources);
      this.getVariables(this.variables);
    }

    // if(changes['reload'] && !changes['reload'].firstChange) {
    //   this.reloadVariables();
    // }
  }
  markAllAsTouched() {
    this.form?.markAllAsTouched();
    this.formControlService.emitEvent({ key: 'markAllAsTouched', value: true });
  }

  reloadVariables() {
    this.variablesSource = {};
    this.getVariables(this.variables);
  }

  setFormTouched() {
    this.formControlService.setFormTouched(true);
    this.formControlService.updateformTouched();
    this.formControlService.emitEvent({ key: 'markAllAsTouched', value: true });
  }

  setFormUnTouched() {
    this.formControlService.setFormTouched(false);
    this.formControlService.updateformTouched();
  }

  createGroup = () =>
    this.formService.createGroup(
      {
        fields: this.config,
        name: '',
        type: 'group',
      } as FieldGroupConfig,
      () => this.valueDef,
    );

  handleSubmit(event: Event) {
    event.preventDefault();
    event.stopPropagation();
    this.submitted.emit(this.value);
  }

  getFunctionSources(sources: Record<string, Source>) {
    this.functionSource = this.service.getFunctionSource(
      sources,
      this.faceCode,
      this.authAction,
    );
  }

  sourceLoading = false;

  getVariables(variables: Record<string, SourceField>) {
    this.subcription?.unsubscribe();
    this.subcription = new Subscription();
    for (const key in variables) {
      const variable = variables[key];
      this.subcription?.add(
        this.values$
          .asObservable()
          .pipe(
            distinctUntilChanged((prev, curr) => _.isEqual(prev, curr)),
            distinctUntilChanged((prev, curr) =>
              this.service.distinct(prev, curr, variable),
            ),
            tap(() => (this.sourceLoading = true)),
            switchMap((values) => {
              const newObj: { [k: string]: unknown } = {};
              newObj[key + '_requestStatus'] = { requestStatus: 'loading' };
              this.variablesSource = {
                ...this.variablesSource,
                ...newObj,
              };
              this.cdr.detectChanges();
              return this.service
                .getObservable(this.functionSource, values, variable)
                .pipe(
                  catchError((err) => {
                    const newObj: { [k: string]: unknown } = {};
                    newObj[key + '_requestStatus'] = {
                      message: err,
                      requestStatus: 'error',
                    };
                    return of([]); // Return an empty array on error
                  }),
                );
            }),
            tap(() => (this.sourceLoading = false)),
            tap((value) => {
              const newObj: { [k: string]: unknown } = {};
              newObj[key] = value;
              newObj[key + '_requestStatus'] = {
                requestStatus: 'success',
              };
              this.variablesSource = {
                ...this.variablesSource,
                ...newObj,
              };
            }),
            tap(() => this.cdr.detectChanges()),
          )
          .subscribe(),
      );
    }
  }
  groupChange(group: FormGroup | FormArray) {
    const form = group as FormGroup;
    this.form = form;
    this.formControlService.updateData({ formGroup: form });
  }

  keyDown($event: KeyboardEvent) {
    if ($event.key === 'Enter') {
      $event.preventDefault();
    }
  }
  invalidEmitter(value: boolean) {
    this.valid = !value;
    this.invalidChange.emit(this.valid);
  }

  get dirty() {
    // const initValue = this.formValue ?? {};
    // const currValue = this.value ?? {};
    // const isDiffValue = !Object.keys(currValue)
    //   .filter(
    //     (key) => currValue[key] !== undefined && initValue[key] !== undefined,
    //   )
    //   .every((key) => {
    //     if (currValue[key] instanceof Date) {
    //       return isEqual(currValue[key], new Date(initValue[key]));
    //     }

    //     return isEqual(currValue[key], initValue[key]);
    //   });
    const subGroups = this.formControlService.subGroups ?? [];
    const someGroupIsDirty = subGroups.some(
      (group) => group.dirty && this.formControlService.getFormTouched(),
    );

    return someGroupIsDirty;

    // return someGroupIsDirty && isDiffValue
  }

  get isSourceLoading() {
    return Object.keys(this.variablesSource).some(
      (key: string) => this.variablesSource[key]?.requestStatus === 'loading',
    );
    // return this.sourceLoading;
  }

  scrollToFirstInvalidControl() {
    const form = this.formRef.nativeElement;

    if (form) {
      let ngInvalidElm = form.getElementsByClassName('field-invalid');

      ngInvalidElm = Array.from(ngInvalidElm).filter(
        (item: any) => !(item.offsetWidth === 0 && item.offsetHeight === 0),
      );
      const firstInvalidControl = ngInvalidElm[0];

      if (firstInvalidControl) {
        firstInvalidControl.scrollIntoView({
          behavior: 'smooth',
          block: 'center',
        });
        (firstInvalidControl as HTMLElement).focus();
      }
    }
  }

  // for form mode step;
  get groupInstance() {
    return this.dynamicFormDirective()?.component?.instance;
  }

  get formSteps() {
    return this.groupInstance?.steps();
  }

  focusErrorTab() {
    this.groupInstance?.focusTabHasError();
  }

  nextStep(stepIndex: number) {
    this.groupInstance?.setNextStep(stepIndex);
  }

  get currentStep() {
    return this.groupInstance?.currentStep();
  }

  isNextStepDisabled() {
    return this.groupInstance?.isNextStepDisabled();
  }
}
