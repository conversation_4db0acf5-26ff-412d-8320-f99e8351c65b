import {
  BtnSize,
  BtnType,
  ButtonSchema,
  ButtonSize,
  ButtonType,
  InputSuggestion,
  ModalSchema,
  ModalSize,
  TreeNode,
} from '@hrdx/hrdx-design';
import { NzSafeAny } from 'ng-zorro-antd/core/types';
import { oboolean, ostring, string, z } from 'zod';

const ZKeyValue = z.record(z.any()).and(
  z.object({
    id: z.string(),
  }),
);
export const validatorTypes = [
  'required',
  'minLength',
  'maxLength',
  'email',
  'pattern',
  'min',
  'max',
  'ppx-maxDuration',
  'ppx-null',
  'ppx-custom',
];

const ZAngularValidatorType = z.union([
  z.literal('required'),
  z.literal('minLength'),
  z.literal('maxLength'),
  z.literal('email'),
  z.literal('pattern'),
  z.literal('min'),
  z.literal('max'),
]);

const ZPeopleXValidatorType = z.union([
  z.literal('ppx-maxDuration'),
  z.literal('ppx-null'),
  z.literal('ppx-custom'),
]);

const ZValidatorType = ZAngularValidatorType.or(ZPeopleXValidatorType);
export type ZValidatorName = z.infer<typeof ZValidatorType>;

const ZFieldValidator = z.object({
  type: ZValidatorType,
  args: z.any().optional(),
  text: z.string().optional(),
  id: z.string().optional(),
});

const ZFieldSelectConfig = z.object({
  options: z.array(ZKeyValue).optional(),
  labelField: z.string().optional().default('id'),
  valueField: z.string().optional().default('name'),
});

const ZFieldRadioConfig = z.array(
  z.object({
    label: z.string(),
    value: z.any(),
  }),
);

const ZFieldButtonConfig = z.array(
  z.object({
    label: z.string(),
    value: z.any(),
    disable: z.boolean().optional().default(true),
    isLoading: z.boolean().optional().default(false),
    type: BtnType.optional().default(ButtonType.Secondary),
    icon: z.string().optional(),
    size: BtnSize.optional().default(ButtonSize.Default),
    action: z.literal('getDependentValue').optional(),
  }),
);

const ZFieldGroupCheckboxOption = z.object({
  label: z.string(),
  value: z.any(),
});

const ZOption = z.object({
  label: z.string(),
  value: z.any(),
});

const ZFieldSelectOption = z.object({
  label: z.string().or(
    z.object({
      title: z.string(),
      color: z.string().optional(),
      icon: z.string().optional(),
    }),
  ),
  value: z.any(),
  options: z.array(ZOption).optional(),
  disabled: z.boolean().optional().default(false),
});

const ZFieldCascaderConfig = z.object({
  options: z.array(ZKeyValue),
  labelField: z.string().optional().default('id'),
  valueField: z.string().optional().default('name'),
});

export type FieldCascaderConfig = z.infer<typeof ZFieldCascaderConfig>;

const ZSourceField = z.object({
  transform: z.string(),
  dependants: z.array(z.string()).optional(),
  params: z.record(z.string(), z.string()).optional(),
  skip: z.custom<{ condition: string; count?: number }>().optional(),
  // use this to show message after get resource done
  onSuccess: z
    .custom<{ type: 'toast'; whenChangeFields?: string; message: string }>()
    .optional(),
  // use this to skip when field is clear by user clicked
  skipWhenClear: z.boolean().optional(),
});

const ZDatePickerDisabledDate = z.object({
  value: z.union([z.string(), z.string().array(), z.enum(['today'])]),
  operator: z.enum([
    '$eq',
    '$in',
    '$lte',
    '$gte',
    '$lt',
    '$gt',
    '$between',
    '$nin',
    '$ne',
  ]),
});

export type DatePickerDisabledDate = z.infer<typeof ZDatePickerDisabledDate>;

const ZDatePickerSetting = z.object({
  type: z
    .enum([
      'date',
      'month',
      'year',
      'week',
      'decade',
      'time',
      'date-time',
      'date-time-full',
    ])
    .optional()
    .default('date'),
  format: z.string().optional(),
  hasTimePicker: z.boolean().optional(),
  timePickerFormat: z.string().optional(),
  autoFill: z.enum(['start-of', 'end-of']).optional().default('start-of'),
  disabledDate: ZDatePickerDisabledDate.or(
    ZDatePickerDisabledDate.array(),
  ).optional(),
  _disabledDate: ZSourceField,
  disabledTime: z.object({
    value: z.union([
      z.object({
        hours: z.number().array().optional(),
        minutes: z.number().array().optional(),
        seconds: z.number().array().optional(),
      }),
      z.enum(['now']),
    ]),
    operator: z.enum(['$eq', '$in', '$lte', '$gte', '$lt', '$gt', '$between']),
  }),
  _disabledTime: ZSourceField,
  emitValueFormat: z.string().optional(),
});
export type DatePickerSetting = z.infer<typeof ZDatePickerSetting>;

const ZSource = z.object({
  uri: z.string(),
  method: z.string(),
  queryTransform: z.string(),
  bodyTransform: z.string(),
  headerTransform: z.string(),
  resultTransform: z.string(),
  params: z.array(z.string()).optional(),
  disabledCache: z.boolean().optional(),
  updateCache: z.string().optional(),
});

const ZAutoSize = z.object({
  minRows: z.number().optional(),
  maxRows: z.number().optional(),
});
const ZTextarea = z.object({
  autoSize: ZAutoSize.optional(),
  maxCharCount: z.number().optional(),
});
const ZFunction = z.string().optional();
const ZFieldAbtract = z.object({
  name: z.string(),
  type: z.string(),
  label: z.string(),
  value: z.any(),
  _value: z.any(),
  __value: z.any(),
});
const ZCommonField = z.object({
  name: z.string(),
  _condition: ZSourceField.optional(),
  label: z.string().optional(),
  _label: ZSourceField.optional(),
  placeholder: z.string().optional(),
  _placeholder: ZSourceField.optional(),
  validators: z.array(ZFieldValidator).optional(),
  isOptional: z.boolean().optional(),
  _isOptional: ZSourceField.optional(),
  disabled: z.boolean().optional(),
  _disabled: ZSourceField.optional(),
  readOnly: z.boolean().optional(),
  _readOnly: ZSourceField.optional(),
  value: z.any(),
  _value: ZSourceField.optional(),
  description: z.string(),
  _description: ZSourceField.optional(),
  warningTexts: z.array(z.string().or(ZSourceField.optional())).optional(),
  col: z.number().optional(),
  isRecommend: z.boolean().optional(),
  dependantField: z.string().optional(),
  actionButton: z
    .object({
      title: z.string().optional(),
      fields: z.array(z.any()).optional(),
      sources: z.object({}).optional(),
      variables: z.object({}).optional(),
      value: z.array(z.any()).optional(),
    })
    .optional(),
});
const ZFieldInput = ZCommonField.and(
  z.object({
    type: z.union([
      z.literal('text'),
      z.literal('password'),
      z.literal('email'),
      z.literal('number'),
      z.literal('tel'),
      z.literal('textarea'),
      z.literal('maxCharCount'),
    ]),
    textarea: ZTextarea.optional(),
    _textarea: ZSourceField.optional(),
    updateType: z.union([z.literal('push'), z.literal('replace')]).optional(),
    suggestion: z.custom<InputSuggestion[]>().optional(),
  }),
);
export type FieldInput = z.infer<typeof ZFieldInput>;

const zFieldConfigBase = z.object({
  links: z
    .record(
      z.string(),
      z.object({
        path: z.string().optional(),
        name: z.string().optional(),
      }),
    )
    .optional(),
  _links: ZSourceField.optional(),

  tooltip: z
    .object({
      content: z.string().optional(),
      placement: z.string().optional(),
    })
    .optional(),
  _tooltip: ZSourceField.optional(),

  readOnly: z.boolean().optional(),
  _readOnly: ZSourceField.optional(),

  disabled: z.boolean().optional(),
  _disabled: ZSourceField.optional(),

  name: z.string(),

  isOptional: z.boolean().optional(),
  _isOptional: ZSourceField.optional(),

  label: z.string().optional(),
  _label: ZSourceField.optional(),

  labelType: z.string().optional(),
  _labelType: z.string().optional(),

  borderLess: z.boolean().optional(),
  _borderLess: z.boolean().optional(),

  placeholder: z.string().optional(),
  _placeholder: ZSourceField.optional(),

  type: z.string(),

  template: z.string().optional(),
  _template: ZSourceField.optional(),

  validators: z.array(ZFieldValidator).optional(),

  value: z.any(),
  _value: ZSourceField.optional(),

  mode: z.string().optional(),
  _mode: ZSourceField.optional(),

  unvisible: z.boolean().optional(),
  _unvisible: ZSourceField.optional(),

  _condition: ZSourceField.optional(),
  col: z.number().optional(),
  description: z.string().optional(),
  _description: ZSourceField.optional(),
  warningTexts: z.array(z.string().or(ZSourceField.optional())).optional(),
  width: z.string().optional(),
  padding: z.number().optional(),
  height: z.string().optional(),
  align: z.string().optional(),
  maxLevel: z.number().optional(),
  contains: z.number().optional(),
  isRecommend: z.boolean().optional(),
  color: z.string().optional(),
  dependantField: z.string().optional(),
  dependantFieldSkip: z.number().optional(),
  subLabel: z.string().optional(),
  scale: z.number().optional(),
  switch: z
    .object({
      options: z.array(z.string()).optional(),
    })
    .optional(),

  hiddenLabel: z.boolean().optional(),
  hiddenInput: z.boolean().optional(),
  showLabelCheckbox: z.boolean().optional(),
  prefixLabel: z.boolean().optional(),
  parentId: z.ostring(),
  class: z.ostring().optional(),
  border: z.ostring().optional(),
  borderRadius: z.number().optional(),
  validateDisabled: z.boolean().optional(),
});

const ZFieldText = zFieldConfigBase.and(
  z.object({
    name: z.string(),
    _condition: ZSourceField.optional(),
    type: z.literal('paragraph'),
    mode: z.union([z.literal('mode1'), z.literal('mode2')]).optional(),
    template: z.string().optional(),
    _template: ZSourceField.optional(),
    description: z.string(),
    _description: ZSourceField.optional(),
    warningTexts: z.array(z.string().or(ZSourceField.optional())).optional(),
    col: z.number().optional(),
    links: z
      .record(
        z.string(),
        z.object({
          path: z.string().optional(),
          name: z.string().optional(),
        }),
      )
      .optional(),
    _links: ZSourceField.optional(),
  }),
);
export type FieldText = z.infer<typeof ZFieldText>;

const ZFieldSelect = ZCommonField.and(
  z.object({
    type: z.literal('select'),
    outputValue: z.string().optional(),
    mode: z.string().optional(),
    _mode: ZSourceField.optional(),
    options: z
      .object({
        maxTag: z.number().optional(),
        allowClear: z.boolean().optional(),
        enabledLoadMore: z.boolean().optional(),
      })
      .optional(),
    select: z.array(ZFieldSelectOption).optional(),
    _select: ZSourceField.optional(),
    _updateOption: ZSourceField.optional(),
    selectOptions: z
      .object({
        renderButton: z
          .object({
            label: z.string().optional(),
            action: z
              .union([
                z.literal('create'),
                z.literal('edit'),
                z.literal('view'),
                z.string().optional(),
              ])
              .default('create'),
          })
          .optional(),
      })
      .optional(),
      initOptionFromDefaultValue: z.object({
        value: z.string().optional(),
        label: z.string().optional(),
      }).optional(),
  }),
);
export type FieldSelect = z.infer<typeof ZFieldSelect> & {
  selectSetting?: {
    filter?: {
      fields: FormFieldsConfig[];
    };
    layout_option?: {
      show_actions_many?: boolean;
      show_action_header?: boolean;
    };
    tableFields?: {
      label: string;
      name: string;
      width?: number;
      dateFormat?: string;
    }[];
    total: number;
    _total: SourceField;
    fieldsShow?: string[];
    tableTitle?: string;
  };
  confirmPopup?: {
    content: string;
    _content: SourceField;
    title: string;
    _title: SourceField;
    listFieldsDependantName: string[];
    _enabled: SourceField;
  };
  isLazyLoad?: boolean;
  isCheckboxTable?: boolean;
  showCreateDataTable?: boolean;
  showCalendar?: boolean;
  calendarOptions?: NzSafeAny;
  showAddNew?: boolean;
  checkedItem?: boolean;
  showCheckbox?: boolean;
  newSelect?: boolean;
  _defaultValue?: SourceField;
  isClearValue?: boolean;
  inputValue?: string;
  clearFieldsAfterChange?: string[];
  isclearFieldsAfterClickClear?: boolean;
  _allowValues?: SourceField;
  allowValueKey?: string;
  useDataAllowValue?: boolean;
  dependantOptionList?: string;
  allowSearch?: boolean;
  _validateFn?: SourceField;
  handleAfterChange?: {
    dataSource: SourceField;
    valueMapping: { field: string; fieldValue: string }[];
  };
  isRemoveOptionNotExist?: boolean;
  valueAll?: string;
};

// field select custom
export type FieldSelectCustomAction = string | { title: string; id: string };
type ActionModalButton = {
  display?: boolean;
  label?: string;
  type?: ButtonSchema['type'];
  size?: ButtonSchema['size'];
};
export type FieldSelectCustomActionConfig = {
  formConfig?: DynamicFormConfig;
  transformFields?: {
    value: z.infer<typeof ZSourceField>;
    label: z.infer<typeof ZSourceField>;
  };
  // use to extend from another action config
  extendFrom?: string;
  table?: {
    fields: {
      name: string;
      label: string;
      width?: number;
      displayType?: string;
    }[];
    mode: 'single' | 'multiple';
  };
  addOnValue?: Record<string, NzSafeAny>;
  _addOnValue?: SourceField;
  options?: {
    okButton?: ActionModalButton;
    cancelButton?: ActionModalButton;
    autoSubmitSelection?: boolean; // default false
    searchButton?: ActionModalButton;
    clearSearchButton?: ActionModalButton;
    showSearchResultTitle?: boolean; // default false
  };
};

const ZFieldSelectCustom = ZFieldSelect.and(
  z.object({
    actions: z.custom<FieldSelectCustomAction>().array().optional(),
    actionsConfig: z.record(
      z.string(),
      z.custom<FieldSelectCustomActionConfig>(),
    ),
    isLazyLoad: z.oboolean(),
    _detailData: ZSourceField.optional(),
  }),
);
export type FieldSelectCustom = z.infer<typeof ZFieldSelectCustom>;

const ZFieldUser = ZCommonField.and(
  z.object({
    type: z.literal('users'),
    mode: z.string(),
    options: z
      .object({
        maxTag: z.number().optional(),
      })
      .optional(),
    users: z.array(ZFieldSelectOption).optional(),
    _users: ZSourceField.optional(),
  }),
);
export type FieldUser = z.infer<typeof ZFieldUser>;

const ZFieldCascader = ZCommonField.and(
  z.object({
    type: z.literal('cascader'),
    mode: z.string().optional(),
    cascader: ZFieldCascaderConfig.optional(),
    _cascader: ZSourceField.optional(),
  }),
);
export type FieldCascader = z.infer<typeof ZFieldCascader>;

const ZFieldDatePicker = ZCommonField.and(
  z.object({
    type: z.literal('dateRange'),
    mode: z
      .union([z.literal('range-picker'), z.literal('date-picker')])
      .default('range-picker'),
  }),
);
export type FieldDatePicker = z.infer<typeof ZFieldDatePicker>;

const zTimePickerSetting = z.object({
  format: ostring(),
  useArrayValue: oboolean(),
  autoDisable: oboolean(),
});
export type TimePickerSetting = z.infer<typeof zTimePickerSetting>;

const ZFieldTimePicker = zFieldConfigBase.and(
  z.object({
    type: z.literal('timePicker'),
    mode: z.union([z.literal('single'), z.literal('range')]).optional(),
    setting: zTimePickerSetting.optional(),
    _setting: ZSourceField,
  }),
);
export type FieldTimePicker = z.infer<typeof ZFieldTimePicker>;

const ZFieldRadio = ZCommonField.and(
  z.object({
    type: z.literal('radio'),
    radio: ZFieldRadioConfig,
    _radio: ZSourceField,
  }),
);

const ZFieldUploadConfig = z.object({
  accept: z.string().optional().or(z.array(z.string())),
  size: z.number().optional(),
  _size: ZSourceField.optional(),
  isMultiple: z.boolean().optional(),
  avatarSize: z.number().optional(),
  fileTypeLabel: z.string().default('PDF, XLS, XLSX'),
  customContentUpload: z.string().optional(),
});
const ZFieldUpload = ZCommonField.and(
  z.object({
    type: z.literal('upload'),
    upload: ZFieldUploadConfig,
    mode: z.string().optional(),
    canAction: z.boolean().default(false),
    hasDivider: z.boolean().default(false),
    confirmPopup: z
      .object({ content: z.string(), title: z.string() })
      .optional(),
    paddingBottomNone: z.boolean().default(false),
  }),
);
export type FieldUpload = z.infer<typeof ZFieldUpload>;
export type FieldRadio = z.infer<typeof ZFieldRadio>;

const ZFieldTextLine = ZCommonField.and(
  z.object({
    type: z.union([
      z.literal('text-line'),
      z.literal('array'),
      z.literal('group'),
    ]),
    display: z.enum(['type1', 'type2']).default('type1'),
    hasOrgChart: z.oboolean().default(false),
    iconPlacement: z.enum(['before', 'after']).default('before'),
    iconName: z.ostring(),
    iconColor: z.ostring(),
    isHidden: z.boolean().default(false),
    isBold: z.oboolean().default(false),
    isItalic: z.oboolean().default(false),
    labelOnly: z.oboolean().default(false),
    nCols: z.onumber(),
    n_cols: z.onumber(),
  }),
);

const ZFieldTextLineMain = ZFieldTextLine.and(
  z.object({
    fields: z.array(ZFieldTextLine).optional(),
  }),
);

export type FieldTextLine = z.infer<typeof ZFieldTextLine>;

const Test = z.union([
  ZFieldInput,
  ZFieldSelect,
  ZFieldUser,
  ZFieldCascader,
  ZFieldDatePicker,
  ZFieldText,
]);
const Test1 = z.union([
  ZFieldInput,
  ZFieldSelect,
  ZFieldUser,
  ZFieldCascader,
  ZFieldDatePicker,
]);
export type TestType = z.infer<typeof Test>;

// TODO: replace with z.union when addon have more than one type
// const ZFieldConfigAddOn = z.union([ZFieldSelect])
const ZFieldConfigAddOn = zFieldConfigBase.and(
  z.object({
    select: z.array(ZFieldSelectOption).optional(),
    _select: ZSourceField.optional(),
    _number: ZSourceField.optional(),
    width: z.string().optional(),
  }),
);

export type FieldConfigAddOnType = z.infer<typeof ZFieldConfigAddOn>;

const ZFieldConfigAction = z.object({
  icon: z.string(),
  label: z.string(),
  type: BtnType,
  modal: z.object({
    title: z.string(),
    size: ModalSize.default('middle'),
  }),
  formConfig: z.custom<DynamicFormConfig>(),
  submitValue: ZSourceField,
  cacheValue: z.boolean().default(false),
  extendValues: z.boolean().default(false).optional(),
  extendFormConfig: z.string().optional(),
  field: zFieldConfigBase.optional(),
  actionType: z.enum(['flag', 'default']).optional(),
  config: z.custom<{ name: string }>().optional(),
  _condition: ZSourceField.optional(),
  confirm: z
    .object({
      title: z.string().optional(),
      content: z.string(),
    })
    .optional(),
});

export type FieldConfigAction = z.infer<typeof ZFieldConfigAction>;

const ZFieldConfigToast = z.object({
  type: z.enum(['success', 'error', 'warning', 'info']).default('info'),
  title: z.string().optional(),
  content: z.string(),
  position: z.enum(['top', 'bottom', 'left', 'right']).default('top'),
  icon: string().optional(),
  contentType: z.enum(['default', 'textarea']).optional(),
});

export type FieldConfigToast = z.infer<typeof ZFieldConfigToast>;

const ZFieldDisplay = z.object({
  component: z.string(),
  config: z.any().optional(),
  _config: ZSourceField.optional(),
  _condition: ZSourceField.optional(),
});

export type FieldDisplay = z.infer<typeof ZFieldDisplay>;

export type DisplaySetting = {
  type: string;
  _value: SourceField;
  extraConfig?: any;
  style?: any;
  elements: DisplaySetting[];
  name?: string;
};

const ZFieldConfig = zFieldConfigBase.and(
  z.object({
    id: z.string().optional(),
    number: z
      .object({
        min: z.number().optional(),
        max: z.number().optional(),
        format: z.string().optional(),
        precision: z.number().optional(),
        prefix: z.string().optional(),
        suffix: z.string().optional(),
        addOnAfter: ZFieldConfigAddOn,
        _suffix: ZSourceField.optional(),
        _prefix: ZSourceField.optional(),
        _precision: ZSourceField.optional(),
        _format: ZSourceField.optional(),
        alertOnMin: z.string().optional(),
      })
      .optional(),

    _number: ZSourceField.optional(),

    users: z.array(ZFieldSelectOption).optional(),
    _users: ZSourceField.optional(),

    cascader: ZFieldCascaderConfig.optional(),
    _cascader: ZSourceField.optional(),

    select: z.array(ZFieldSelectOption).optional(),
    _select: ZSourceField.optional(),

    radio: ZFieldRadioConfig.optional(),
    _radio: ZSourceField.optional(),

    setting: z.union([ZDatePickerSetting, zTimePickerSetting]).optional(),
    _setting: ZSourceField.optional(),

    textarea: ZTextarea.optional(),
    _textarea: ZSourceField.optional(),

    checkboxGroup: ZFieldGroupCheckboxOption.array().optional(),
    _checkboxGroup: ZSourceField.optional(),

    addOnAfter: ZFieldConfigAddOn.optional(),
    addOnBefore: ZFieldConfigAddOn.optional(),
    addOnBottom: ZFieldConfigAddOn.optional(),

    button: ZFieldButtonConfig.optional(),
    _button: ZSourceField.optional(),

    _createField: z.any().optional(),

    hyperlink: z
      .object({
        hyperlinkTitle: z.string().optional(),
        fields: z.array(z.any()).optional(),
        sources: z.object({}).optional(),
        variables: z.object({}).optional(),
        value: z.array(z.any()).optional(),
        hyperlinkText: z.string().optional(),
        _condition: ZSourceField.optional(),
        _value: ZSourceField.optional(),
      })
      .optional(),
    fieldFlexEnd: z.boolean().optional(),

    _padding: z.string().optional(),
    _border: z.string().optional(),

    inputReadonly: z.boolean().optional(),
    _inputReadonly: ZSourceField.optional(),

    n_cols: z.number().optional(),
    additionalData: z.custom<DynamicFormConfig>().optional(),
    _additionalData: ZSourceField.optional(),

    action: ZFieldConfigAction.optional(),

    _class: ZSourceField.optional(), // use to define dynamic class of field config
    toast: ZFieldConfigToast.optional(),
    _toast: ZSourceField.optional(),
    color: z.string().optional(),
    isNested: z.boolean().optional(),
    isInfo: z.boolean().optional(),
    displaySetting: z.custom<DisplaySetting>().optional(),
    isDuplicateKeyName: z.boolean().optional(),
    sources: z.any().optional(),
    variables: z.any().optional(),
    hasBackground: z.boolean().optional(),
    marginBottomErrorMsg: z.string().optional(),
    titleTableForm: z.boolean().optional(),
    positionContentTable: z.string().optional(),

    no_need_focus: z.boolean().optional(),
    customDisabledTitle: z.string().optional(),
    _customDisabledTitle: ZSourceField.optional(),
    status: z.boolean().optional(),

    customWrapperStyle: z.object({}).optional(),
    clearValueIfRemoveControl: z.boolean().optional(),
    defaultValue: z.any().optional(),
  }),
);

const ZFieldInputAffix = z.object({
  type: z.enum(['char-count']).default('char-count'),
});

export type FieldInputSuffix = z.infer<typeof ZFieldInputAffix>;

export type FieldInputFormatFnType =
  | 'removeAccent'
  | 'upperCase'
  | 'lowerCase'
  | 'removeSpaces';

const ZFieldInputConfig = ZFieldConfig.and(
  z.object({
    formConfig: z.custom<DynamicFormConfig>().optional(),
    labelTransform: ZSourceField.optional(),
    suffix: ZFieldInputAffix.optional(),
    prefix: ZFieldInputAffix.optional(),
    maxCharCount: z.onumber(),
    formatFn: z.custom<FieldInputFormatFnType>().array().optional(),
    formatType: z.enum(['code']).optional(),
    formatByKeydown: z.boolean().default(false).optional(),
  }),
);

const ZFieldTranslationConfig = ZFieldConfig.and(
  z.object({
    _actionCondition: ZSourceField,
  }),
);
export type FieldInputConfig = z.infer<typeof ZFieldInputConfig>;
export type FieldTranslationConfig = z.infer<typeof ZFieldTranslationConfig>;

const ZFieldTreeSelectConfig = ZFieldConfig.and(
  z.object({
    nodes: z.custom<TreeNode>().array().optional(),
    _nodes: ZSourceField.optional(),
    checkStrictly: z.boolean().optional(),
    checkNodesByLevel: z.oboolean(),
    _allowValues: ZSourceField.optional(),
    allowValueKey: z.string().optional(),
    clearFieldsAfterChange: z.string().array().optional(),
  }),
);

export type FieldTreeSelectConfig = z.infer<typeof ZFieldTreeSelectConfig>;

const ZFieldCheckboxPicklistConfig = ZFieldConfig.and(
  z.object({
    checkboxSection: z.object({
      title: z.string().optional(),
      searchConfig: z
        .object({
          placeholder: z.string().optional(),
          visible: z.boolean().default(false).optional(),
        })
        .optional(),
      checkAllConfig: z.object({
        label: z.string().optional(),
        visible: z.boolean().default(false).optional(),
      }),
    }),
    bindingSection: z.object({
      title: z.string().optional(),
      showClearAll: z.boolean().default(true).optional(),
      showSelectedNumber: z.boolean().default(true).optional(),
      emptyText: z.string().optional(),
    }),
  }),
);

export type FieldCheckboxPicklistConfig = z.infer<
  typeof ZFieldCheckboxPicklistConfig
>;

const ZFieldSwitchConfig = zFieldConfigBase.and(
  z.object({
    showSwitchLabel: z.boolean().default(false).optional(),
  }),
);

export type FieldSwitchConfig = z.infer<typeof ZFieldSwitchConfig>;

export interface FieldGroupActionConfig {
  name: string;
  icon?: string;
  isReset?: boolean;
  label?: string;
  type?: ButtonSchema['type'];
  typeLeftIcon?: ButtonSchema['icon'];
  isLeftIcon?: ButtonSchema['isLeftIcon'];
  shouldConfirm?: boolean;
  _shouldConfirm?: SourceField;
  confirmModal?: {
    title: string;
    content: string;
  };
  _confirmModal?: SourceField;
  validateBeforeDoAction?: {
    validateConfig: {
      args: SourceField;
      text: string;
    };
    toastConfig?: {
      type: 'error' | 'warning';
      title?: string;
    };
  };
}

export type FieldGroupMode = {
  name: string;
  showCollapseSection?: boolean;
  focusElementPerTab?: { index: number; elementId: string }[];
};

export interface FieldGroupConfig {
  id?: string;
  name: string;
  label?: string;
  _label?: SourceField;
  labelType?: string;
  borderLess?: boolean;
  disabled?: boolean;
  readOnly?: boolean;
  condition?: boolean;
  _condition?: SourceField;
  type: 'group';
  mode?: any;
  _mode?: SourceField;
  fields: FormFieldsConfig[];
  n_cols?: number;
  _n_cols?: SourceField;
  space?: number;
  padding?: number;
  col?: number;
  collapse?: boolean;
  disableEventCollapse?: boolean;
  labelActions?: any[];
  gridTemplateColumns?: string;
  width?: string;
  align?: string;
  maxLevel?: number;
  contains?: number;
  warningTexts?: (string | SourceField)[];
  validators?: FieldValidator[];
  lastGroupStyleOff?: boolean;
  _disabled?: SourceField;
  fieldBackground?: string;
  color?: string;
  gap?: string;
  border?: string;
  height?: string;
  border_top?: string;
  borderRadius?: number;
  groupEdit?: boolean;
  value?: any;
  _createField?: any;
  fieldFlexEnd?: boolean;
  parentId?: string;
  readonly_cols?: number;
  required?: boolean; // default false
  _required?: SourceField; // should use only for group tabset or step, when you want to dynamic required group
  actions?: FieldGroupActionConfig[];
  clearAll?: boolean;
  unvisible?: boolean;
  _unvisible?: SourceField;
  tabName?: string;
  dependantField?: string;
  isResetOnTabChange?: boolean;
  actionsStyle?: 'group' | 'config' | 'array'; // default group
  isLeftCollapse?: boolean;
  borderBottomLabel?: boolean;
  actionsCustomStyle?: string;
  fieldGroupContentStyle?: NzSafeAny;
  fieldGroupTitleStyle?: NzSafeAny;
  hostStyle?: NzSafeAny;
  _titleStyle?: SourceField;
  _hostStyle?: SourceField;
  _collapse?: SourceField;
  _disableEventCollapse?: SourceField;
  borderBottom?: boolean;
  isHideArrow?: boolean;
  styling_not_form_view?: any;
  description?: string;
  _description?: SourceField;
  customPadding?: string;
  titleTableForm?: boolean;
  labelFields?: FieldConfig[];
  isBorderTopNone?: boolean;
  isBorderTopLabel?: boolean;
  margin?: string;
  formValueAfterReset?: any;
  dependantFieldSkip?: number;
  renderChildFields?: boolean; // use for mode tabset in case don't need to wrap into another field-group ==> improve performance
}

export interface FieldArrayConfig {
  id: string;
  width?: string;
  name: string;
  label?: string;
  labelType?: string;
  borderLess?: boolean;
  disabled?: boolean;
  _disabled?: SourceField;
  readOnly?: boolean;
  mode?: string;
  condition?: boolean;
  _condition?: SourceField;
  type: 'array';
  field: FormFieldsConfig;
  size: number;
  _size: SourceField;
  minSize?: number;
  _minSize?: SourceField;
  defaultRowIndex?: number;
  _defaultRowIndex?: SourceField;
  value: any;
  _value: SourceField;
  n_cols?: number;
  fields?: FormFieldsConfig[];
  space?: number;
  col?: number;
  collapse?: boolean;
  arrayOptions?: {
    nextTable?: boolean;
    add_label?: string;
    delete_label?: string;
    canChangeSize?: boolean;
    _canChangeSize?: SourceField;
    canDeleteItem?: boolean;
    _canDeleteItem?: SourceField;
    maxLength?: number;
    canMoveItem?: boolean;
    moveup_label?: string;
    movedown_label?: string;
    canDragDrop?: boolean;
    canAddItem?: boolean;
    _canAddItem?: SourceField;
    isDialogTable?: boolean;
    isCheckboxTable?: boolean;
    canViewItem?: boolean;
    preCondition?: boolean;
    pickFields?: Record<string, boolean | Record<string, boolean>>;
    canEditItem?: boolean;
    add_btn_style?: string;
    searchBar?: boolean;
    groupTable?: boolean;
    filterTable?: boolean;
    add_btn_type?:
      | 'primary'
      | 'secondary'
      | 'tertiary'
      | 'link'
      | 'ghost-color'
      | 'ghost-gray';
    showCreateSection?: boolean;
    cloneAction?: boolean;
    enabledField?: string;
    cloneValues?: any;
    calendarOptions?: CalendarOptions;
    hiddenHeadTable?: boolean;
    showConfirmDelete?: boolean; // default true
    markAsDeletedByKey?: string;
    checkToDeletedByKey?: string;
    uniqueField?: string;
    _customFieldValueAddItem?: SourceField;
    rowTotal?: string[];
  };
  _arrayOptions?: SourceField;
  validators?: FieldValidator[];
  maxLevel?: number;
  contains?: number;
  showTableFilter?: boolean;
  showTableGroup?: boolean;
  expandFilter?: boolean;
  _createField?: any;
  parentId?: string;
  sources?: NzSafeAny;
  variables?: NzSafeAny;
  border?: string;
  borderRadius?: number;
  description?: string;
  _description?: SourceField;
  checkEmptyValue?: boolean;
}
export interface CalendarOptions {
  isActive?: boolean;
  hasModal?: boolean;
  button?: ButtonSchema;
  modal?: ModalSchema;
  _modalTitle: {
    transform: string;
  };
  _effectiveDate: {
    transform: string;
  };
  _method: {
    transform: string;
  };
  _value: {
    transform: string;
  };
}
export interface FieldMultitypeConfig {
  id?: string;
  type: 'multitype';
  name: string;
  label?: string;
  fields: (FormFieldsConfig & { key: string })[];
  value?: any & { key: string };
  _value?: SourceField;
  space?: number;
  col?: number;
  maxLevel?: number;
  contains?: number;
  disabled?: boolean;
  readOnly?: boolean;
  width?: string;
  validators?: FieldValidator[];
  isRecommend?: boolean;
  key: string;
  _key: SourceField;
  parentId?: string;
}

export interface TagView {
  key: string;
  value: any;
}

export interface FieldTagViewConfig {
  type: 'tagView';
  name: string;
  label?: string;
  value?: string;
  _value?: SourceField;
  col?: number;
  disabled?: boolean;
  readOnly?: boolean;
  width?: string;
  validators?: FieldValidator[];
  defaultDisplay?: TagView[];
  isRecommend?: boolean;
  customRegex?: RegExp;
}

export type SourceField = z.infer<typeof ZSourceField>;
export type Source = z.infer<typeof ZSource>;
export type FieldValidator = z.infer<typeof ZFieldValidator>;
export type FieldConfig = z.infer<typeof ZFieldConfig>;
export type FieldControlConfig = z.infer<typeof Test1>;

export type FormFieldsConfig =
  | FieldGroupConfig
  | FieldArrayConfig
  | FieldConfig
  | FieldMultitypeConfig;

export interface GroupName {
  name: string;
  label?: string;
  type: string;
  width?: string;
  align?: string;
  isRequired?: boolean;
  fields?: (GroupName | undefined)[];
}

export interface DynamicFormConfig {
  fields?: FormFieldsConfig[];
  variables?: Record<string, SourceField>;
  sources?: Record<string, Source>;
  formValue?: any;
}
