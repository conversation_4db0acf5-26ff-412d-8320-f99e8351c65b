import { EventEmitter } from '@angular/core';
import { FormArray, FormGroup } from '@angular/forms';
import { FieldConfig } from './field-config.interface';

export interface Field {
  config: FieldConfig;
  group: FormGroup | FormArray;
  values: Values;
  disabled?: boolean;
  isAddOn?: boolean;
  radioEmit?: EventEmitter<{ value: boolean | undefined; path: string[] }>;
  clickedCommonSetting?: EventEmitter<{ value: any }>;
  clickedSetting?: EventEmitter<{ value: any }>;
  invalidEmitter?: EventEmitter<boolean>;
  subInvalidEmitter?: EventEmitter<boolean>;
}

export interface Values {
  extend?: { [k: string]: any };
  variables?: { [k: string]: any };
  fields?: { [k: string]: any };
  function?: (transform: string, data: unknown) => Promise<any>;
  flags?: { [k: string]: any };
  reloadFormValueFn?: () => void;
  faceCode?: string | null;
  authAction?: string;
  checkPermissionFn?: (action: string) => boolean;
}
