import { CommonModule } from '@angular/common';
import {
  AfterViewChecked,
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  Output,
  SimpleChanges,
  SkipSelf,
  ViewChild,
  ViewEncapsulation,
  computed,
  contentChildren,
  effect,
  forwardRef,
  inject,
  input,
  model,
  output,
  signal,
} from '@angular/core';
import { FormArray, FormControl, FormGroup, FormsModule } from '@angular/forms';
import {
  ButtonComponent,
  DisplayComponent,
  IconComponent,
  ModalComponent,
  TooltipComponent,
  TooltipPosition,
  TooltipTrigger,
} from '@hrdx/hrdx-design';
import { isNil, isString, isEmpty, max } from 'lodash';
import { NzSafeAny } from 'ng-zorro-antd/core/types';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import {
  BehaviorSubject,
  Observable,
  Subject,
  Subscription,
  catchError,
  combineLatest,
  debounceTime,
  distinctUntilChanged,
  filter,
  fromEventPattern,
  map,
  of,
  skip,
  switchMap,
  takeUntil,
  tap,
} from 'rxjs';
import { FormComponent } from '../../../form.component';
import {
  DisplaySetting,
  DynamicFormConfig,
  FieldConfig,
  FieldConfigAction,
  FieldConfigAddOnType,
  FieldConfigToast,
  GroupName,
  SourceField,
  Values,
  validatorTypes,
} from '../../../models';
import { DynamicFormService } from '../../../services/dynamic-form.service';
import { FormConfigService } from '../../../services/form-config.service';
import { FormControlService } from '../../../services/form-control.service';

import { ToastComponent } from '../../../components/common/toast/toast.component';
import { supportedFields } from '../../../components/fields';
import { UtilsService } from '../../common/services/utils.service';
import { DynamicFieldDirective } from '../dynamic-field.directive';
import * as _ from 'lodash';
import {
  createResizeObserverObservable,
  getElementsInSameRow,
} from '../../../utils';

@Component({
  selector: 'dynamic-field-config',
  templateUrl: './field-config.component.html',
  styleUrls: [
    '../../../components/group/field-config/field-config.component.less',
  ],
  standalone: true,
  imports: [
    DynamicFieldDirective,
    NzInputModule,
    CommonModule,
    NzToolTipModule,
    NzIconModule,
    NzSelectModule,
    FormsModule,
    TooltipComponent,
    IconComponent,
    ModalComponent,
    ButtonComponent,
    forwardRef(() => FormComponent),
    ToastComponent,
    DisplayComponent,
    NzToolTipModule,
  ],
  encapsulation: ViewEncapsulation.None,
  // changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FieldConfigComponent
  implements OnInit, OnChanges, AfterViewChecked, OnDestroy, AfterViewInit
{
  el = inject(ElementRef);
  @Input()
  field!: FieldConfig;
  @Input()
  group: FormGroup | FormArray = new FormGroup({});
  @Output()
  groupChange = new EventEmitter<FormControl | undefined>();
  @Input() values: Values = {};
  disabled = model<boolean | undefined>(false);
  @Input() readOnly?: boolean;
  @Input() formValue: any;
  // get value for addOnAfter and addOnBefore from realFormValue not formValue
  @Input() realFormValue: any;
  isEmpty = _.isEmpty;
  // @Input() readOnly = false;

  @Input() isTableMode = false;
  @Input() widthCell?: {
    addon?: number;
    new?: number;
  };

  @Input() fieldFlexEnd?: {
    idx: number;
    status?: boolean;
  };

  viewDetailVisible = false;

  addOnAfter?: FieldConfigAddOnType | undefined;
  addOnBefore?: FieldConfigAddOnType | undefined;
  addOnBottom?: FieldConfigAddOnType | undefined;
  addOnMinWidth = '70px';
  action?: FieldConfigAction | undefined;
  toast?: FieldConfigToast | undefined;
  displaySetting?: FieldConfig['displaySetting'];
  private readonly destroy$ = new Subject<void>();

  readonly tooltipConfig = {
    trigger: TooltipTrigger.Hover,
    position: TooltipPosition.TopCenter,
    arrow: true,
  } as const;

  @Output() labelChange = new EventEmitter<GroupName | undefined>();
  hyperLinkIdx = input<number>(-1);
  inheritDisabled$ = new BehaviorSubject(false);
  fieldDisabled$ = new BehaviorSubject(false);
  refresh$ = new BehaviorSubject(false);
  disabled$ = combineLatest({
    inherit: this.inheritDisabled$,
    field: this.fieldDisabled$,
    refresh: this.refresh$,
  })
    .pipe(
      map(({ inherit, field }) => {
        if (inherit) return true;
        return field;
      }),
      tap((value) => {
        if (!this.field) return;
        // this.disabled.set(value);
        if (value) {
          // if (this.group instanceof FormGroup)
          //   this.group.get(this.field.name)?.disable();
          // else this.group.at(+this.field.name)?.disable();
        } else {
          // if (this.group instanceof FormGroup)
          //   this.group.get(this.field.name)?.enable();
          // else this.group.at(+this.field.name)?.enable();
        }
      }),
    )
    .subscribe();

  newValues: Values = {};
  isRequired = signal(false);
  private readonly formService = inject(FormConfigService);
  validatorTypes = validatorTypes;
  values$ = new BehaviorSubject<Values>(this.values);
  description?: string;
  warningText: (string | undefined)[] = [];
  hidden = signal(true);
  unvisible = signal(true);
  borderLess = false;
  paddingLess = false;
  absolute = false;
  labelType?: string;
  label = signal<string | undefined>(undefined);
  titleTableForm = signal<boolean>(false);
  positionContentTable = signal<string | undefined>('left');
  hiddenLabel = signal(false);
  prefixLabel = false;
  tooltip?: { content?: string; placement?: string };
  elem = inject(ElementRef);
  isDuplicateKeyName = signal(false);
  subInvalid = signal(false);

  constructor(
    @SkipSelf() private formControlService: FormControlService,
    private utils: UtilsService,
    private cdr: ChangeDetectorRef,
  ) {
    effect(() => this.setDisabledInput(this.disabled()));
  }

  private setDisabledInput(disabled = true) {
    const tabIndex = disabled ? '-1' : '0';
    const el = this.el.nativeElement as HTMLElement;
    const inputs = el.querySelectorAll('input');
    inputs?.forEach((input) => input?.setAttribute('tabindex', tabIndex));
  }

  UpdateValidateEverytimeValueChange$ = this.values$.pipe(
    distinctUntilChanged((prev, curr) =>
      (this.inputField()?.validators ?? [])
        .filter((validate) => validate.type === 'ppx-custom')
        .every((validator) =>
          this.service.distinct(prev, curr, validator.args),
        ),
    ),

    tap(() => {
      if (this.control) this.control?.updateValueAndValidity();
    }),
  );

  inputField = computed(() => {
    if (!this.field) return undefined;

    // Only clone the necessary properties that can change
    const newField = {
      ...this.field,
      readOnly: this.readOnly,
      value:
        this.shouldClearFormValue() ||
        (this.isDuplicateKeyName() &&
          this.formControlService.shouldClearFormValueByKey(this.field.name))
          ? undefined
          : (this.formValue ?? this.field.value),
      // Preserve other essential properties that might change
      // disabled: this.disabled(),
      hidden: this.hidden(),
      unvisible: this.unvisible(),
      placeholder: this.placeholder() ?? this.field.placeholder,
    };

    // Selectively deep clone only complex nested objects that need isolation
    if (this.field.validators) {
      newField.validators = this.field.validators.map((validator) => ({
        ...validator,
      }));
    }

    if (this.field.displaySetting) {
      newField.displaySetting = { ...this.field.displaySetting };
    }

    return newField as FieldConfig;
  });

  getAddOnField(field: FieldConfig) {
    if (!field?.name) return undefined;
    const realFormValue =
      typeof this.realFormValue === 'function'
        ? this.realFormValue()
        : this.realFormValue;
    // Only clone necessary properties
    const newField = {
      ...field,
      value: realFormValue?.[field.name] ?? field.value,
      readOnly: this.readOnly,
    };

    // Selectively clone nested objects if needed
    if (field.validators) {
      newField.validators = field.validators.map((validator) => ({
        ...validator,
      }));
    }

    return newField;
  }

  hiddenInput = computed(() => this.field?.hiddenInput ?? false);
  noNeedFocus = computed(() => this.field?.no_need_focus ?? false);
  toolTipWhenDisabled = signal<string | undefined>(undefined);

  private hasValuesChanged(changes: SimpleChanges): boolean {
    const valuesChange = changes['values'];
    const formValueChange = changes['formValue'];

    // Compare both values and formValue changes using enhanced shallowEqual
    if (valuesChange?.currentValue || formValueChange?.currentValue) {
      const prevValues = valuesChange?.previousValue || {};
      const currValues = valuesChange?.currentValue || {};
      const prevFormValue = formValueChange?.previousValue || {};
      const currFormValue = formValueChange?.currentValue || {};

      // Check if either values or formValue has changed
      if (
        !this.utils.shallowEqual(prevValues, currValues) ||
        !this.utils.shallowEqual(prevFormValue, currFormValue)
      ) {
        return true;
      }
    }

    // Check formValue in extend if needed
    if (valuesChange) {
      const curr = valuesChange.currentValue?.extend?.formValue;
      const prev = valuesChange.previousValue?.extend?.formValue;
      return !this.utils.shallowEqual(curr || {}, prev || {});
    }

    return false;
  }

  private isProficiencyLevelPath(path: any[]): boolean {
    return (
      Array.isArray(path) &&
      path.length === 3 &&
      path[0] === 'proficiencyLevelList' &&
      path[1] === 0 &&
      path[2] === 'basicInfo'
    );
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['values'] || changes['formValue']) {
      // Use hasValuesChanged to determine if we need to update
      if (!this.hasValuesChanged(changes)) return;

      let path: (string | number)[];
      if (!this.field.name) {
        path = [];
      } else if (this.group instanceof FormGroup) {
        path = [this.field.name];
      } else {
        path = [+this.field.name];
      }
      this.newValues = {
        fields: this.values.fields,
        variables: this.values.variables,
        extend: {
          ...this.values.extend,
          ...{
            path: [...(this.values.extend?.['path'] ?? []), ...path],
            formValue: this.formValue,
          },
        },
        function: this.values.function,
      };
      this.values$.next(this.newValues);
    }

    if (changes['disabled']) {
      if (
        this.isProficiencyLevelPath(this.values.extend?.['path'] ?? []) &&
        this.field?.name === 'proficiencyLevel'
      ) {
        this.inheritDisabled$.next(this.disabled() ?? false);
      }
    }
  }

  service = inject(DynamicFormService);
  conditionBehavior$?: Observable<boolean>;
  hyperlinkConditionBehavior$?: Observable<boolean>;
  showHyperlink = true;
  hyperLinkValue = null;
  reloadHyperlinkForm = signal(false);
  displayErrorType: 'block' | 'absolute' = 'block';
  placeholder = signal<string | undefined>(undefined);

  setActionFormConfig(config: FieldConfig['action']) {
    this.actionFormConfig = config?.formConfig;
    if (config?.extendFormConfig) {
      const subForms = this.values?.extend?.['subForms'];
      this.actionFormConfig = subForms[config.extendFormConfig];
    }
  }

  currentClass = null;

  ngOnInit() {
    this.addOnAfter = this.field.addOnAfter;
    this.addOnBefore = this.field.addOnBefore;
    this.addOnBottom = this.field.addOnBottom;
    this.action = this.field.action;
    this.placeholder.set(this.field.placeholder);

    const _placeholder = this.field._placeholder;
    if (_placeholder) {
      this.subscribeValueChange(_placeholder).subscribe((value) => {
        this.placeholder.set(value);
      });
    }

    this.formControlService.formTouchedBehavior$.subscribe((value) => {
      this.formTouched.set(value);
    });

    if (this.action) {
      this.setActionFormConfig(this.action);
    }
    this.hiddenLabel.set(this.field.hiddenLabel ?? false);
    this.titleTableForm.set(this.field.titleTableForm ?? false);
    this.positionContentTable.set(this.field.positionContentTable ?? 'left');
    this.prefixLabel = this.field.prefixLabel ?? false;
    this.toast = this.field.toast;
    this.displaySetting = this.field.displaySetting;
    this.isDuplicateKeyName.set(
      !!(this.field.isDuplicateKeyName && this.field.name),
    );
    if (this.isDuplicateKeyName()) {
      this.formControlService.registerDuplicateControlName(this.field.name);
    }
    (this.el.nativeElement as HTMLDivElement).style.padding =
      this.field?.padding + '' || '20';

    this.field?.height &&
      ((this.el.nativeElement as HTMLDivElement).style.height =
        this.field?.height);

    if (!this.realFormValue && this.formValue) {
      this.realFormValue = this.formValue;
    }
    if (this.isTableMode)
      (this.el.nativeElement as HTMLDivElement).style.width =
        this.field.width?.toString() ?? '';
    // (this.el.nativeElement as HTMLDivElement).style.width = '400px';
    if (!this.field?.type || !supportedFields[this.field.type]) {
      const supportedTypes = Object.keys(supportedFields).join(', ');
      this.hidden.set(true);
      this.unvisible.set(true);
      (this.el.nativeElement as HTMLDivElement).style.display = 'none';
      throw new Error(
        `Trying to use an unsupported type (${this.field?.type}).
        Supported types: ${supportedTypes}`,
      );
      return;
    }
    if (
      ['paragraph'].includes(this.field.type) ||
      this.field.validators?.find((validator) => validator.type === 'required')
    ) {
      this.isRequired.set(true);
    } else {
      this.isRequired.set(false);
    }
    if (
      [
        'paragraph',
        'switch',
        'radio',
        'radioTable',
        'upload',
        'checkbox',
        'selectCustom',
        'translationTextArea',
        'groupCheckbox',
        'button',
        'sectionItem',
        'listCollapse',
        'table',
        'checkboxPicklist',
        'job',
        'translation',
        'treeSelect',
        'tableSelect',
        'treeTable',
        'selectWithAction',
        'tagView',
        'textarea',
      ].includes(this.field.type)
    )
      this.borderLess = true;

    if (['translation'].includes(this.field.type)) {
      this.paddingLess = true;
    }

    if (this.field.borderLess) this.borderLess = this.field.borderLess;

    switch (this.field.labelType) {
      case 'type-row':
        this.labelType = 'label-type-row ';
        (this.el.nativeElement as HTMLDivElement).classList.add(
          'label-type-row',
        );
        break;
      case 'flex-row':
        this.labelType = 'label-type-flex-row';
        (this.el.nativeElement as HTMLDivElement).classList.add(
          'label-type-flex-row',
        );
        break;

      case 'type-col-readOnly':
        this.labelType = 'label-type-col-read-only ';
        (this.el.nativeElement as HTMLDivElement).classList.add(
          'label-type-col-read-only',
        );
        break;

      case 'type-row-readOnly':
        this.labelType = 'label-type-row-read-only ';
        (this.el.nativeElement as HTMLDivElement).classList.add(
          'label-type-row-read-only',
        );
        break;
      default:
        this.labelType = 'label-type-col ';
        (this.el.nativeElement as HTMLDivElement).classList.add(
          'label-type-col',
        );
        if (this.field?.labelType?.startsWith('type-grid')) {
          this.labelType = 'type-grid';
        }
    }

    if (['type-row', 'flex-row'].includes(this.field.labelType as string)) {
      this.displayErrorType = 'absolute';
    }

    this.conditionBehavior$ = this.field._condition
      ? this.values$.pipe(
          distinctUntilChanged((prev, curr) =>
            this.service.distinct(prev, curr, this.field._condition),
          ),
          switchMap((values) =>
            this.service.getObservable(
              values.function,
              values,
              this.field._condition,
            ),
          ),
          tap(() => {
            this.formControlService.checkExistsCondition(this.field.name);
          }),
        )
      : of(true);

    this.conditionBehavior$?.subscribe((value) => {
      if (value) {
        (this.el.nativeElement as HTMLDivElement).style.display = 'block';
        if (this.isTableMode)
          (this.el.nativeElement as HTMLDivElement).style.display = 'flex';
        if (
          !(
            (this.group instanceof FormGroup &&
              this.group.contains(this.field.name)) ||
            (this.group instanceof FormArray && this.group.at(+this.field.name))
          )
        ) {
          this.addControls();
        }
        this.hidden.set(false);
        this.unvisible.set(false);
        this.renderedByCondition = true;
      } else {
        this.hidden.set(true);
        this.unvisible.set(true);
        (this.el.nativeElement as HTMLDivElement).style.display = 'none';
        if (!this.renderedByCondition) return;
        if (this.isDuplicateKeyName() && !this.shouldClearFormValue) {
          this.formControlService.setShouldClearFormValue(
            this.field.name,
            true,
          );
        }
        this.shouldClearFormValue.set(true);
        this.removeControls();
      }
    });

    // condition for hyperlink
    this.hyperlinkConditionBehavior$ = this.field?.hyperlink?._condition
      ? this.values$.pipe(
          distinctUntilChanged((prev, curr) =>
            this.service.distinct(
              prev,
              curr,
              this.field?.hyperlink?._condition,
            ),
          ),
          switchMap((values) =>
            this.service.getObservable(
              values.function,
              values,
              this.field?.hyperlink?._condition,
            ),
          ),
        )
      : of(true);
    this.hyperlinkConditionBehavior$?.subscribe((value) => {
      if (value && typeof value == 'boolean') {
        this.showHyperlink = true;
      } else {
        this.showHyperlink = false;
      }
    });

    combineLatest({
      description: of(this.field.description),
      _description: this.field._description
        ? this.values$.pipe(
            distinctUntilChanged((prev, curr) => {
              return this.service.distinct(prev, curr, this.field._description);
            }),
            switchMap((values) =>
              this.service.getObservable(
                values.function,
                values,
                this.field._description,
              ),
            ),
            map((value) => {
              return value;
            }),
          )
        : of(undefined),
    })
      .pipe(
        map(({ description, _description }) => {
          if (_description) return _description as string;
          return description ?? '';
        }),
      )
      .subscribe((description) => (this.description = description));
    (this.field?.warningTexts ?? []).map((warning, idx) => {
      if (isString(warning)) {
        this.warningText[idx] = warning;
        return;
      }
      return this.values$
        .pipe(
          distinctUntilChanged((prev, curr) => {
            return this.service.distinct(prev, curr, warning);
          }),
          switchMap((values) =>
            this.service.getObservable(values.function, values, warning),
          ),
          catchError((err) => {
            console.error('error uyntq', err);
            return of(undefined);
          }),
          tap((data) => (this.warningText[idx] = data)),
        )
        .subscribe();
    });
    combineLatest({
      tooltip: of(this.field.tooltip),
      _tooltip: this.values$.pipe(
        distinctUntilChanged((prev, curr) => {
          return this.service.distinct(prev, curr, this.field._tooltip);
        }),
        switchMap((values) =>
          this.service.getObservable(
            values.function,
            values,
            this.field._tooltip,
          ),
        ),
        map((value) => {
          return value;
        }),
      ),
    })
      .pipe(
        map(({ tooltip, _tooltip }) => {
          if (_tooltip)
            return _tooltip as { content?: string; placement?: string };
          return tooltip ?? {};
        }),
      )
      .subscribe((tooltip) => (this.tooltip = tooltip));
    if (![''].includes(this.field.type)) this.label.set(this.field?.label);
    if (this.field?._label) {
      this.values$
        .pipe(
          distinctUntilChanged((prev, curr) =>
            this.service.distinct(prev, curr, this.field._label),
          ),
          switchMap((values) =>
            this.service.getObservable(
              values.function,
              values,
              this.field._label,
            ),
          ),
        )
        .subscribe((value) => {
          if (!['upload'].includes(this.field.type)) this.label.set(value);
        });
    }

    if (this.field.unvisible) {
      (this.el.nativeElement as HTMLDivElement).style.display = 'none';
      this.unvisible.set(true);
    }
    if (this.field._unvisible) {
      this.values$
        .pipe(
          distinctUntilChanged((prev, curr) =>
            this.service.distinct(prev, curr, this.field._unvisible),
          ),
          switchMap((values) =>
            this.service.getObservable(
              values.function,
              values,
              this.field._unvisible,
            ),
          ),
        )
        .subscribe((value) => {
          if (typeof value === 'boolean') {
            this.unvisible.set(value);
            if (value) {
              (this.el.nativeElement as HTMLDivElement).style.display = 'none';
            } else {
              (this.el.nativeElement as HTMLDivElement).style.display = 'block';
              if (this.isTableMode)
                (this.el.nativeElement as HTMLDivElement).style.display =
                  'flex';
            }
          }
        });
    }
    if (this.field.disabled) this.fieldDisabled$.next(true);
    if (this.field._disabled) {
      this.values$
        .pipe(
          distinctUntilChanged((prev, curr) =>
            this.service.distinct(prev, curr, this.field._disabled),
          ),
          switchMap((values) =>
            this.service.getObservable(
              values.function,
              values,
              this.field._disabled,
            ),
          ),
        )
        .subscribe((value) => {
          if (typeof value === 'boolean') {
            // this.fieldDisabled$.next(value);
            this.disabled.set(value);

            if (!value && this.group.get(this.field.name)?.disabled) {
              this.group.get(this.field.name)?.enable();
            }
          }
        });
    }
    if (this.field._readOnly) {
      this.values$
        .pipe(
          distinctUntilChanged((prev, curr) =>
            this.service.distinct(prev, curr, this.field._readOnly),
          ),
          switchMap((values) =>
            this.service.getObservable(
              values.function,
              values,
              this.field._readOnly,
            ),
          ),
        )
        .subscribe((value) => {
          if (typeof value === 'boolean' && value === true) {
            this.readOnly = value;
          }
        });
    }

    const dependantField = this.field.dependantField;
    const formType = this.values?.extend?.['formType'];
    const defaultValue = this.values?.extend?.['defaultValue'];
    const dependantFieldSkip = this.field.dependantFieldSkip; // TODO: Refactor DependantField
    if (dependantField) {
      combineLatest({
        condition: this.conditionBehavior$ ?? of(true),
        values: this.values$,
        confirmPopupChanges:
          this.formControlService?.confirmPopupBehavior$ ?? of(true),
      })
        .pipe(
          filter(({ condition }) => condition),
          filter(({ confirmPopupChanges }) => confirmPopupChanges),
          map(({ values }) => values),
          distinctUntilChanged((prev, curr) => {
            const rl = this.service.distinct(prev, curr, {
              transform: dependantField,
            });
            return rl;
          }),
          skip(
            this.formControlService.getSkip(
              formType,
              dependantField,
              defaultValue,
              dependantFieldSkip,
            ),
          ),
          tap(() => {
            this.control?.setValue(null);
          }),
        )
        .subscribe();
    }

    const addOnAfterDependantField = this.field.addOnAfter?.dependantField;
    if (addOnAfterDependantField) {
      combineLatest({
        condition: this.conditionBehavior$ ?? of(true),
        values: this.values$,
      })
        .pipe(
          filter(({ condition }) => condition),
          map(({ values }) => values),
          distinctUntilChanged((prev, curr) =>
            this.service.distinct(prev, curr, {
              transform: addOnAfterDependantField,
            }),
          ),
          skip(2),
          tap(() => {
            const addOnAfterName = this.field.addOnAfter?.name;
            if (addOnAfterName) {
              this.group?.get(addOnAfterName)?.setValue(null);
            }
          }),
        )
        .subscribe();
    }

    const _class = this.field._class;
    if (_class) {
      this.subscribeValueChange(_class).subscribe((value) => {
        this.currentClass = value;

        const allowClasses = ['required', 'unrequired', 'fakeRequired'];
        if (!allowClasses.includes(value)) return;
        switch (value) {
          case 'required': {
            this.isRequired.set(true);
            this.requiredSubscription = combineLatest({
              formValue: this.valueChange$,
              disabled: this.fieldDisabled$,
              // values: this.values$,
            })
              .pipe(
                // distinctUntilChanged((prev, curr) =>
                //    isEqual(prev.formValue, curr.formValue),
                // ),
                takeUntil(this.destroy$),
                tap(({ formValue, disabled }) => {
                  const errors = this.errors();
                  //if field is disabled, don't validate required, just display *
                  if (disabled) return;
                  if (!this.control) return;

                  if (this.currentClass !== 'required') return;

                  const validatorError = this.service.parseValidator(
                    { type: 'required' },
                    this.field.type,
                  )(this.control);
                  if (!validatorError && errors['required']) {
                    delete errors['required'];
                  }
                  if (validatorError) {
                    errors['required'] = validatorError['required'](
                      this.label(),
                    );
                  }
                  this.control.setErrors(errors);
                  this.errors.set(structuredClone(errors));
                }),
              )
              .subscribe();
            break;
          }
          case 'unrequired': {
            this.isRequired.set(false);
            this.errors.update((prev) => {
              delete prev['required'];
              return structuredClone(prev);
            });
            this.requiredSubscription?.unsubscribe();
            break;
          }
          case 'fakeRequired': {
            this.isRequired.set(true);
            this.errors.update((prev) => {
              delete prev['required'];
              return structuredClone(prev);
            });
            this.requiredSubscription?.unsubscribe();
            break;
          }
        }
      });
    }

    const _hyperLinkValue = this.field.hyperlink?._value;
    if (_hyperLinkValue) {
      this.subscribeValueChange(_hyperLinkValue).subscribe((value) => {
        this.hyperLinkValue = value;
        this.reloadHyperlinkForm.update((prev) => !prev);
      });
    }

    const _toast = this.field._toast;
    if (_toast) {
      this.subscribeValueChange(_toast).subscribe((value) => {
        this.toast = value;
      });
    }

    if (this.field.customDisabledTitle) {
      this.toolTipWhenDisabled.set(this.field.customDisabledTitle);
    }
    if (this.field._customDisabledTitle) {
      this.subscribeValueChange(this.field._customDisabledTitle).subscribe(
        (value) => {
          this.toolTipWhenDisabled.set(value);
        },
      );
    }

    const extendValues = this.field.action?.extendValues;
    if (extendValues) {
      this.values$.subscribe((values) => this.actionExtendValues.set(values));
    }

    // this.UpdateValidateEverytimeValueChange$.subscribe();

    //set hyperlink value
    this.mapValueHyperlinkField();

    // setup transform display value
    this.displayValue.set({ _default: this.formValue ?? this.field?.value });
    this.setupTransformDisplayValue(this.displaySetting);
  }
  requiredSubscription?: Subscription;

  subscribeValueChange(transform: SourceField) {
    return combineLatest({
      _value: this.values$.pipe(
        distinctUntilChanged((prev, curr) => {
          return this.service.distinct(prev, curr, transform);
        }),
        switchMap(() =>
          this.service.getObservable(
            this.values.function,
            this.values,
            transform,
          ),
        ),
      ),
    }).pipe(
      map(({ _value }) => {
        return _value;
      }),
    );
  }

  displayValue = signal<NzSafeAny>(null);
  private setupTransformDisplayValue(setting?: DisplaySetting) {
    if (!this.readOnly || !setting) return;
    if (setting._value) {
      const displayName = setting.name;
      this.subscribeValueChange(setting._value).subscribe((value) =>
        this.displayValue.update((prev) => {
          return { ...prev, [displayName ?? '_value']: value };
        }),
      );
    }

    const elements = setting.elements ?? [];
    for (const element of elements) {
      this.setupTransformDisplayValue(element);
    }
  }

  getDisplayValueKey(setting: DisplaySetting) {
    return setting.name ?? (setting._value ? '_value' : '_default');
  }

  renderedByCondition = false;
  shouldClearFormValue = signal(false);
  removeControls() {
    this.groupChange.emit(undefined);
    if (this.field.clearValueIfRemoveControl) {
      this.control?.setValue(null);
    }
    this.control = undefined;
    this.errors.set({});
  }

  addControls() {
    const inputField = this.inputField();
    if (!inputField) {
      console.log('error add Control');
      return;
    }
    const newControl = this.createControl(inputField);
    this.control = newControl;
    this.groupChange.emit(this.control);
    this.refresh$.next(false);
    this.initValidators();
  }

  createControl(config: FieldConfig) {
    const control = this.formService.createControl(config, () => this.values);
    return control;
  }

  // set max width element to avoid overflow content, especial in case of select all.
  private readonly fieldsToSetMaxWidth = ['selectAll'];
  contentWidth = signal<string | null>(null);
  private setMaxWidthContent() {
    const el = this.elem.nativeElement as HTMLElement;
    const contentWidth = el?.querySelector<HTMLElement>(
      '.field-config__content',
    )?.offsetWidth;
    if (!contentWidth) return;
    this.contentWidth.set(`${contentWidth}px`);
  }

  shouldSetMaxWidthContent() {
    return (
      this.fieldsToSetMaxWidth.includes(this.field.type) &&
      this.labelType === 'type-grid'
    );
  }

  ngAfterViewChecked() {
    this.emitLabel();
    if (this.shouldSetMaxWidthContent()) {
      this.setMaxWidthContent();
    }
    const el = this.elem.nativeElement as HTMLElement;

    const field = el.querySelector(
      '.ant-select-selection-item, .ant-input, .ant-input-number-input',
    ) as HTMLDivElement;
    if (this.field.color) {
      if (field) field.style.color = `var(--${this.field.color})`;
    }
    if (this.isTableMode) {
      if (this.widthCell) {
        const widthCell = {
          addon: this.widthCell.addon,
          new: this.widthCell.new,
        };
        if (widthCell.new && !this.field.width) {
          (this.el.nativeElement as HTMLDivElement).style.width =
            widthCell.new.toString() + 'px';
          return;
        }
        if (widthCell.addon && this.field.width) {
          (this.el.nativeElement as HTMLDivElement).style.width =
            `calc(${this.field.width} + ${widthCell.addon}px)`;
          return;
        }
      } else {
        (this.el.nativeElement as HTMLDivElement).style.width = `${
          this.field.width ?? '200px'
        }`;
      }
    }
  }

  get existControl() {
    if (this.group instanceof FormGroup)
      return this.group.get(this.field.name) ? true : false;
    return this.group.at(+this.field.name) ? true : false;
  }
  control: FormControl | undefined;
  // get control() {
  //   if (!this.group) return null;
  //   if (!this.inputField?.name) return null;
  //   if (this.group instanceof FormGroup) {
  //     if (this.group.contains(this.field.name))
  //       return this.group.controls[this.field.name];
  //     return null;
  //   } else {
  //     if (this.group.at(+this.field.name))
  //       return this.group.at(+this.field.name);
  //     return null;
  //   }
  // }

  emitLabel = computed(() => {
    if (this.unvisible()) return this.labelChange.emit(undefined);
    else
      return this.labelChange.emit({
        name: this.field.name,
        label: this.label(),
        isRequired: this.isRequired(),
        width: this.field.width,
        align: this.field.align,
        type: this.field.type,
      });
  });
  radioEmit = output<Event>();
  onRadioEmit(e: Event) {
    this.radioEmit.emit(e);
  }

  clickedCommonSetting = output<Event>();
  onCommonSetting(e: Event) {
    this.clickedCommonSetting.emit(e);
  }

  focusFn(isIn: boolean) {
    if (isIn) {
      this.configTouched.set(true);
      this.formControlService.setFormTouched(true);
    } else {
      this.control?.markAsUntouched();
    }
  }

  formTouched = signal(this.formControlService.getFormTouched());
  configTouched = signal(false);

  clickedSetting = output<string>();

  onSetting(event: string) {
    this.clickedSetting.emit(event);
  }

  isHyperLink = computed(() => {
    if (this.field.hyperlink) return true;
    return false;
  });

  get hyperlinkText() {
    return this.field?.hyperlink?.hyperlinkText;
  }

  onHyperlinkClick() {
    this.isHyperLink() && (this.viewDetailVisible = true);
  }

  async mapValueHyperlinkField() {
    // const valueHyperlink: NzSafeAny = [];
    // const _value = this.field?.hyperlink?._value;

    // if (_value) {
    //   valueHyperlink.push(
    //     await this.service.getJsonataExpression({})(
    //       _value.transform,
    //       this.actionForm?.value || {},
    //     ),
    //   );
    // }
    const valueHyperlink = this.field?.hyperlink?.value ?? [];
    valueHyperlink.length > 0 &&
      this.field?.hyperlink?.fields &&
      this.field.hyperlink.fields.map((field) => {
        valueHyperlink.map((v: NzSafeAny, idx: number) => {
          this.hyperLinkIdx() === idx && (field.value = v[field.name]);
        });
      });
  }

  shouldRenderActionForm = signal(false);
  onActionClick() {
    this.actionModalVisible.set(true);
    this.shouldRenderActionForm.set(true);
    // this.reloadActionForm.update((prev) => !prev);
  }

  actionFormConfig?: DynamicFormConfig;
  actionModalVisible = signal(false);
  reloadActionForm = signal(false);
  actionExtendValues = signal<NzSafeAny>({});
  _actionExtendValues = computed(() => {
    if (this.field.action?.extendValues) {
      return {
        formType: 'search',
        values: this.actionExtendValues(),
      };
    }
    return {
      formType: 'search',
    };
  });
  @ViewChild('actionForm') actionForm?: FormComponent;

  actionValue = signal<NzSafeAny>({});

  async onActionSubmit() {
    const formValue = this.actionForm?.value;
    const transform = this.action?.submitValue?.transform;
    const formControl = this.group?.get(this.field.name);
    const isNested = this.field?.isNested ?? false;

    if (this.action?.cacheValue) {
      this.actionValue.set(formValue);
    }

    if (transform && formValue && formControl) {
      const value = await this.service.getJsonataExpression({})(
        transform,
        formValue,
      );

      const nestedValue =
        isNil(formControl.value) || formControl.value.length === 0
          ? value
          : formControl.value + '\n' + value;
      formControl.patchValue(
        isNested ? this.formControlService.distinctValue(nestedValue) : value,
      );
    }
    this.actionModalVisible.set(false);
  }
  subscriptions: Subscription[] = [];
  errors = signal<Record<string, string | null>>({});
  @Output() invalidEmitter = new EventEmitter<boolean>();
  isInvalid = signal(false);
  effectInvalid = effect(
    () => {
      if (this.hidden() || (this.disabled() && !this.field.validateDisabled)) {
        this.invalidEmitter.emit(false);
        return;
      }
      const errors = this.errors();
      const invalid = !this._isEmpty(errors);
      this.isInvalid.set(invalid);
      this.invalidEmitter.emit(invalid || this.subInvalid());
    },
    { allowSignalWrites: true },
  );
  _isEmpty = isEmpty;
  valueChange$ = new BehaviorSubject<any>(null);
  initValidators() {
    const config = this.inputField();
    this.subscriptions.forEach((sub) => sub.unsubscribe());
    this.subscriptions = [];
    if (!this.control) return;
    this.subscriptions.push(
      this.control.valueChanges
        .pipe(tap((value) => this.valueChange$.next(value)))
        .subscribe(),
    );
    config?.validators?.forEach((validator) => {
      if (validator.type === 'ppx-custom') {
        this.subscriptions.push(
          combineLatest({
            formValue: this.control?.valueChanges ?? of(null),
            values: this.values$,
          })
            .pipe(
              distinctUntilChanged(
                (prev, curr) =>
                  prev.formValue === curr.formValue &&
                  this.service.distinct(
                    prev.values,
                    curr.values,
                    validator.args,
                  ),
              ),
              takeUntil(this.destroy$),
              switchMap(({ formValue, values }) => {
                if (
                  !this.control ||
                  (this.disabled() && !this.field.validateDisabled)
                )
                  return of(null);
                return this.service.parseAsyncValidator(validator, () => ({
                  ...values,
                  value: formValue,
                }))(this.control);
              }),
              tap((validatorError) => {
                const errors = this.errors();
                const name = validator.id ?? validator.type;
                if (!validatorError && errors[name]) {
                  delete errors[name];
                }
                if (validatorError) {
                  errors[name] = validatorError[validator.type](this.label());
                }
                if (!this.control) return;
                this.control.setErrors(errors);
                this.errors.set({ ...errors });
              }),
            )
            .subscribe(),
        );
      } else {
        this.subscriptions.push(
          combineLatest({
            formValue: this.valueChange$,
            // values: this.values$,
          })
            .pipe(
              // distinctUntilChanged((prev, curr) =>
              //    isEqual(prev.formValue, curr.formValue),
              // ),
              takeUntil(this.destroy$),
              tap(({ formValue }) => {
                const errors = this.errors();
                if (
                  !this.control ||
                  (this.disabled() && !this.field.validateDisabled)
                )
                  return;

                const validatorError = this.service.parseValidator(
                  validator,
                  config.type,
                )(this.control);

                if (this.field.name === 'requester') {
                  console.log(this.control.value, validatorError, 'errors');
                }
                if (!validatorError && errors[validator.type]) {
                  delete errors[validator.type];
                }
                if (validatorError) {
                  errors[validator.type] = validatorError[validator.type](
                    this.label(),
                  );
                }
                this.control.setErrors(errors);
                this.errors.set({ ...errors });
              }),
            )
            .subscribe(),
        );
      }
    });
  }

  get disabledTitle() {
    if (!this.disabled()) return null;
    if (this.toolTipWhenDisabled()) return this.toolTipWhenDisabled();
    const el = this.el.nativeElement as HTMLDivElement;
    if (this.field.type === 'select' && this.field.mode !== 'multiple') {
      return el
        .querySelector('.ant-select-selection-item')
        ?.getAttribute('title');
    } else {
      return null;
    }
  }

  // TODO: start calculate label height, remove if has better solution.
  labelHeight = signal<string | null>(null);
  calculateHeightLabel() {
    const el = this.el.nativeElement;
    createResizeObserverObservable(el);
    const getElementHeight = (
      el: HTMLElement | Element | null | undefined,
      selector: string,
    ) => {
      return el?.querySelector<HTMLElement>(selector)?.offsetHeight ?? 0;
    };

    const updateLabelHeight = (height: number) => {
      if (!height) {
        this.labelHeight.set('unset');
        return;
      }
      const el = this.el.nativeElement as HTMLElement;

      const currentLabelHeight = getElementHeight(el, '.field-config__label');
      // if (!currentLabelHeight) return;

      const parent = el.parentElement as HTMLElement;
      const elementsInSameRow = getElementsInSameRow(el.offsetTop, parent);
      const otherElementsLabel = elementsInSameRow
        .map((element) => element.querySelector('.field-config__label-text'))
        .filter(
          (element) =>
            element && element.getAttribute('data-label') !== this.label(),
        ) as HTMLElement[];
      if (otherElementsLabel.length <= 0) return;
      const listOfLabelHeight = otherElementsLabel.map(
        (element) => element.offsetHeight,
      );
      const newHeight = max(listOfLabelHeight) ?? 0;

      const currentLabelTextHeight = getElementHeight(
        el,
        '.field-config__label-text',
      );

      if (
        newHeight <= currentLabelTextHeight &&
        currentLabelTextHeight === currentLabelHeight
      )
        return;
      this.labelHeight.set(newHeight === 0 ? 'unset' : `${newHeight}px`);
    };

    const resize$ = createResizeObserverObservable(el).pipe(
      takeUntil(this.destroy$),
      debounceTime(100), // Debounce to limit the number of events
      map((entries) => entries[0]?.contentRect), // Extract contentRect for size info
      // distinctUntilChanged((prev, curr) => prev?.height === curr?.height),
    );

    resize$.subscribe((contentRect) => updateLabelHeight(contentRect.height));
  }

  shouldShowLabel = computed(
    () => this.label() && !this.isTableMode && !this.hiddenLabel(),
  );

  shouldCheckLabelHeight = computed(
    () =>
      this.shouldShowLabel() &&
      (!this.readOnly || ['label-type-col'].includes(this.labelType as string)),
  );

  ngAfterViewInit(): void {
    if (!this.shouldCheckLabelHeight()) return;
    this.calculateHeightLabel();
  }

  ngOnDestroy(): void {
    this.destroy$.next(); // Emit to signal teardown
    this.destroy$.complete(); // Complete the destroy observable

    // destroy all subscriptions
    this.subscriptions.forEach((sub) => {
      if (sub) {
        sub.unsubscribe();
      }
    });
  }

  get showValidateMessage() {
    const ct = this.control;
    if (!ct) return false;
    // TODO: need to refactor logic show validate message here
    return (
      !(this.disabled() && !this.field.validateDisabled) &&
      (this.noNeedFocus() ||
        (!this.noNeedFocus() &&
          (this.formTouched() || this.configTouched()))) &&
      !ct.touched &&
      ct.untouched &&
      !this.isEmpty(this.errors())
    );
  }
  // end calculate label height
}
