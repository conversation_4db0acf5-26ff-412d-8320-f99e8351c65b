import { isNil, replace, toLower, toUpper, trim } from 'lodash';
import { FieldInputFormatFnType } from '../models';

const codeForbiddenKeys = [
  '!',
  '@',
  '#',
  '$',
  '%',
  '^',
  '&',
  '*',
  '(',
  ')',
  '=',
  '+',
  '[',
  '{',
  ']',
  '}',
  '|',
  '\\',
  ';',
  ':',
  "'",
  '"',
  ',',
  '<',
  '>',
  '/',
  '?',
  ' ',
  '~',
  '`',
];

const removeAccent = (str: string) => {
  return str
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '')
    .replace(/Đ/g, 'D') // Thay Đ
    .replace(/đ/g, 'd');
};

const removeSpaces = (inputString: string) => {
  return replace(inputString, /\s+/g, '');
};

type HelperFn = (input: any) => any;

type HelperFnType = FieldInputFormatFnType;

const formatStringFnMapping = {
  lowerCase: toLower,
  upperCase: toUpper,
  removeAccent: removeAccent,
  removeSpaces: removeSpaces,
  trim: trim,
} as Record<HelperFnType, HelperFn>;

const getHelperFnByName = (fnName: HelperFnType) => {
  return formatStringFnMapping[fnName];
};

const mergeHelperFns = (fns: HelperFnType[]) => {
  if (fns.length <= 0) return;
  const formatFnList = fns
    .map((fnName) => getHelperFnByName(fnName))
    .filter((fn) => !isNil(fn));

  const formatFn = (value: any) => {
    formatFnList.forEach((fn) => {
      value = fn(value);
    });

    return value;
  };
  return formatFn;
};

export {
  getHelperFnByName,
  removeAccent,
  removeSpaces,
  mergeHelperFns,
  codeForbiddenKeys,
};
