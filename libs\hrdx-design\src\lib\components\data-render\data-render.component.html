<div class="data-render" *ngIf="objectKeys(validFilterList()).length > 0">
  <div class="clear-all">
    <hrdx-button
      title="Clear all"
      (clicked)="clearAll()"
      [size]="'xsmall'"
    ></hrdx-button>
  </div>

  <div class="data-render__list-wrapper" #dataRender>
    <div class="data-render__list">
      <ng-container [ngTemplateOutlet]="filterList"></ng-container>
    </div>
    <div class="slide-btns" *ngIf="isShowSlideBtns">
      <button (click)="slideTo('left')" *ngIf="isShowSlideLeftBtn">
        <hrdx-icon [icon]="'icon-caret-left-bold'"></hrdx-icon>
      </button>
      <button (click)="slideTo('right')" [disabled]="!isShowSlideRightBtn">
        <hrdx-icon [icon]="'icon-caret-right-bold'"></hrdx-icon>
      </button>
    </div>
  </div>
</div>

<ng-template #filterList>
  <ng-container *ngFor="let key of objectKeys(validFilterList())">
    @switch (getType(filterLst()[key])) {
      @case ('array') {
        @if (isISODateArray(filterLst()[key], key)) {
          <ng-container
            [ngTemplateOutlet]="filterItem"
            [ngTemplateOutletContext]="{
              key,
              label: getDateRangeLabel(key),
              value: getValue(filterLst()[key]),
            }"
          ></ng-container>
        } @else {
          <ng-container
            [ngTemplateOutlet]="filterItem"
            [ngTemplateOutletContext]="{
              key,
              label: getArrayLabel(key),
              value: filterLst()[key],
              type: 'array',
            }"
          ></ng-container>
        }
      }
      @case ('object') {
        <ng-container
          [ngTemplateOutlet]="filterItem"
          [ngTemplateOutletContext]="{
            key,
            label: getObjectValue(filterLst()[key], key),
            value: filterLst()[key],
          }"
        ></ng-container>
      }
      @case ('boolean') {
        <ng-container
          [ngTemplateOutlet]="filterItem"
          [ngTemplateOutletContext]="{
            key,
            label: transformValueByKey(key),
            value: filterLst()[key],
          }"
        ></ng-container>
      }
      @default {
        <ng-container
          [ngTemplateOutlet]="filterItem"
          [ngTemplateOutletContext]="{
            key,
            label: getDefaultValue(key),
            value: filterLst()[key],
          }"
        ></ng-container>
      }
    }
  </ng-container>
</ng-template>

<ng-template
  #filterItem
  let-key="key"
  let-label="label"
  let-value="value"
  let-type="type"
>
  <div class="filter-result">
    <div>
      <span>{{ transform(key) }}:</span>
      @if (type === 'array' && value?.length > 2) {
        <span class="filter-result__array">
          &nbsp;
          <ng-container
            [ngTemplateOutlet]="arrayLabel"
            [ngTemplateOutletContext]="{
              label: getArrayLabel(key),
              values: getArrayValuesByKey(key),
            }"
          ></ng-container>
        </span>
      } @else {
        <span>&nbsp;{{ label }}</span>
      }
    </div>
    <hrdx-icon
      (click)="removeItemByValue(key, value)"
      [name]="'icon-x-bold'"
      [size]="'small'"
      [fontStyle]="'light'"
    />
  </div>
</ng-template>

<ng-template #arrayLabel let-label="label" let-values="values">
  <hrdx-popover
    [content]="popoverContent"
    [arrow]="popoverConfig.arrow"
    [position]="popoverConfig.position"
  >
    <span>{{ label }}</span>
  </hrdx-popover>

  <ng-template #popoverContent>
    <ul class="data-render__popover-content">
      @for (value of values; track $index) {
        <li>{{ value }}</li>
      }
    </ul>
  </ng-template>
</ng-template>
