import {
  AfterViewInit,
  Component,
  computed,
  ElementRef,
  inject,
  input,
  OnChanges,
  OnInit,
  output,
  SimpleChanges,
  viewChild,
} from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { IconComponent } from '../icon';
import { NzSafeAny } from './data-render.model';
import {
  cloneDeep,
  isBoolean,
  isEmpty,
  isNil,
  isNumber,
  mapValues,
} from 'lodash';
import { ButtonComponent } from '../button';
import { PopoverComponent, PopoverPosition } from '../popover';
import { auditTime, distinctUntilChanged, fromEvent, map } from 'rxjs';
import { formatCurrency } from '../../shared';
import * as moment from 'moment';

@Component({
  selector: 'hrdx-data-render',
  standalone: true,
  imports: [
    CommonModule,
    IconComponent,
    ButtonComponent,
    PopoverComponent,
    IconComponent,
  ],
  templateUrl: './data-render.component.html',
  styleUrl: './data-render.component.less',
  providers: [DatePipe],
})
export class DataRenderComponent implements OnInit, AfterViewInit, OnChanges {
  filterLst = input<NzSafeAny>({});
  filterConfig = input<
    Record<
      string,
      {
        label: string;
        type: string;
        format: string;
        options?: { label: string; value: NzSafeAny }[];
      }
    >
  >({});
  removedFilterItem = output<NzSafeAny>();

  datePipe = inject(DatePipe);
  readonly popoverConfig = {
    arrow: true,
    position: PopoverPosition.BottomCenter,
  };
  dataRenderRef = viewChild<ElementRef<HTMLElement>>('dataRender');

  ngOnChanges(changes: SimpleChanges) {
    if (changes['filterLst']) {
      this.resetTranslateX();
      setTimeout(() => {
        this.updateIsShowSlideBtns();
      }, 1);
    }
  }

  ngOnInit() {
    fromEvent(window, 'resize')
      .pipe(
        auditTime(100),
        map(() => window.innerWidth),
        distinctUntilChanged(),
      )
      .subscribe(() => this.onResize());
  }

  ngAfterViewInit() {
    this.updateIsShowSlideBtns();
  }

  private get filterList(): NzSafeAny {
    return cloneDeep(this.filterLst());
  }

  removeItem(key: string) {
    let list: NzSafeAny = {};
    const filterList = this.filterList;

    if (key in filterList) {
      filterList[key] = null;
    }

    list = filterList;
    return list;
  }

  validate(key: string) {
    const filterList = this.filterLst();
    const value = filterList?.[key];
    return this.isValidValue(value);
  }

  validFilterList = computed(() => {
    const filterList = this.filterLst();
    if (isEmpty(filterList)) return [];
    return this.filterObject(filterList);
  });

  filterObject(obj: Record<string, NzSafeAny>) {
    const keys = Object.keys(obj);
    return keys.reduce((acc: Record<string, NzSafeAny>, key) => {
      const value = obj[key];
      if (this.isValidValue(value)) {
        acc[key] = obj[key];
      }
      return acc;
    }, {});
  }

  isValidObjectValue(value: Record<string, NzSafeAny>) {
    return Object.keys(value).some((key) => this.isValidValue(value[key]));
  }

  isValidValue(value: NzSafeAny): boolean {
    if (isNumber(value) || isBoolean(value) || value instanceof Date)
      return true;
    if (typeof value === 'object')
      return value && this.isValidObjectValue(value);
    return !isEmpty(value);
  }

  isISODateArray = (arr: NzSafeAny[], key?: any) => {
    const config = this.filterConfig()?.[key];
    return (
      config?.type === 'dateRange' ||
      arr.every((item) => item instanceof Date && !isNaN(item.getTime()))
    );
  };

  updateArrayValues(
    currentValue: NzSafeAny,
    key: string,
    value: NzSafeAny | string,
  ) {
    if (typeof value === 'string') {
      currentValue[key] = currentValue[key].filter((item: any) =>
        this.shouldKeepItem(item, value),
      );
      return currentValue;
    }
    return this.removeItem(key);
  }

  shouldKeepItem(item: any, value: string): boolean {
    return item?.label ? item?.label !== value : item !== value;
  }

  removeItemByValue(key: string, value: NzSafeAny[] | string): void {
    let currentValue = this.filterList;

    const isArray = Array.isArray(currentValue[key]);
    if (isArray) {
      currentValue = this.updateArrayValues(currentValue, key, value);
    } else {
      currentValue = this.removeItem(key);
    }

    this.removedFilterItem.emit(currentValue);
  }

  transform(key: string): string {
    const label = this.filterConfig()?.[key]?.label;
    if (label) return label;
    return key
      .replace(/([a-z])([A-Z])/g, '$1 $2')
      .replace(/^./, (str) => str.toUpperCase());
  }

  getArrayValue(data: NzSafeAny) {
    return data?.['label'] ?? data;
  }

  getArrayLabel(key: string) {
    const values = this.getArrayValuesByKey(key);

    if (values.length <= 2) return values.join(', ');

    return `${values[0]}, ${values[1].slice(0, 3)}... +${values.length - 2}`;
  }

  getArrayValuesByKey(key: string) {
    const value = this.filterLst()?.[key];
    return value.map((item: NzSafeAny) => this.getArrayValue(item));
  }

  getValue(item: NzSafeAny) {
    return item?.['label'] ?? this.formatDate(item);
  }

  getObjectValue(value: Record<string, NzSafeAny>, key: string) {
    if (value['label']) {
      return value['label'];
    }

    const config = this.filterConfig()?.[key];
    if (value instanceof Date || config?.type === 'dateRange') {
      const format = config?.format ?? 'dd/MM/yyyy HH:mm:ss';
      return this.formatDate(value, format);
    }

    return Object.values(value)
      .filter((value: NzSafeAny) => {
        if (typeof value === 'string') return value !== '';
        return !isNil(value);
      })
      .join(' - ');
  }

  formatDate(date: NzSafeAny, format = 'dd/MM/yyyy HH:mm:ss') {
    if (date instanceof Date || moment(date).isValid()) {
      return (
        this.datePipe.transform(date as Date | string | number, format) ?? '-'
      );
    }
    return date;
  }

  objectKeys(obj: NzSafeAny): string[] {
    return Object.keys(obj);
  }

  isArrayValue(value: NzSafeAny): boolean {
    return Array.isArray(value) && value.length > 0;
  }

  isObject(value: NzSafeAny): boolean {
    return value !== null && typeof value === 'object' && !Array.isArray(value);
  }

  getType(value: NzSafeAny): string {
    if (Array.isArray(value)) {
      return 'array';
    }

    const type = typeof value;

    if (this.isObject(value)) {
      return 'object';
    } else if (type === 'string') {
      return 'string';
    } else if (type === 'boolean') {
      return 'boolean';
    }

    return 'unknown';
  }

  transformValueByKey(key: string) {
    switch (key) {
      case 'status':
        return this.filterLst()[key] ? 'Active' : 'Inactive';
      default: {
        return this.getDefaultValue(key);
      }
    }
  }

  clearAll() {
    const currentList = this.filterLst();
    const tranformInitial = mapValues(currentList, () => null);
    this.removedFilterItem.emit(tranformInitial);
  }

  getDateRangeLabel(key: string) {
    const dates: Date[] = this.filterLst()[key];
    if (!dates || dates.length <= 0) return '';
    const config = this.filterConfig()?.[key];
    const format = config?.format ?? 'dd/MM/yyyy HH:mm:ss';
    let label = 'From ' + this.formatDate(dates[0], format);
    if (dates[1]) {
      label += ` - To ${this.formatDate(dates[1], format)}`;
    }
    return label;
  }

  getDefaultValue(key: string) {
    const radioLabel = this.getLabelTypeRadio(key);
    if (radioLabel) return radioLabel;
    const value = this.filterLst()[key];
    const config = this.filterConfig()?.[key];
    if (value instanceof Date || config?.type === 'dateRange') {
      const format = config?.format ?? 'dd/MM/yyyy';
      return this.formatDate(value, format);
    }
    return this.formatValue(value?.['label'] ?? value, config?.format);
  }

  private formatValue(value: any, format: string) {
    switch (format) {
      case 'currency': {
        return formatCurrency(value);
      }
      default: {
        return value;
      }
    }
  }

  getLabelTypeRadio(key: string) {
    const config = this.filterConfig()?.[key];
    const value = this.filterLst()[key];
    if (config.type === 'radio' && config.options) {
      const label = config.options.find((v) => v.value === value)?.label;
      if (label) return label;
    }

    return null;
  }

  currentTranslateX = 0;
  slideTo(direction: 'left' | 'right' = 'right') {
    const wrapperElement = this.dataRenderRef()?.nativeElement;
    const listElement = wrapperElement?.querySelector(
      '.data-render__list',
    ) as HTMLElement;
    const listWidth = listElement?.offsetWidth ?? 0;
    const wrapperWidth = wrapperElement?.offsetWidth ?? 0;
    if (direction === 'left') {
      if (this.currentTranslateX <= 0) return;
      this.currentTranslateX -= wrapperWidth / 2;
      if (this.currentTranslateX < 0) this.currentTranslateX = 0;
    } else {
      if (listWidth - this.currentTranslateX <= wrapperWidth) return;
      this.currentTranslateX += Math.min(wrapperWidth / 2);
    }

    listElement?.style.setProperty(
      'transform',
      `translateX(-${this.currentTranslateX}px)`,
    );
  }

  private resetTranslateX() {
    this.currentTranslateX = 0;
    const listElement = this.dataRenderRef()?.nativeElement?.querySelector(
      '.data-render__list',
    ) as HTMLElement;
    listElement?.style.setProperty('transform', `translateX(0px)`);
  }

  get isShowSlideRightBtn() {
    return !(this.differentBetweenListAndWrapper() <= this.currentTranslateX);
  }

  get isShowSlideLeftBtn() {
    return this.currentTranslateX > 0;
  }

  isShowSlideBtns = false;
  isListOverflow() {
    return this.differentBetweenListAndWrapper() > 0;
  }

  private differentBetweenListAndWrapper() {
    const wrapperElement = this.dataRenderRef()?.nativeElement;
    const listElement = wrapperElement?.querySelector(
      '.data-render__list',
    ) as HTMLElement;
    const listWidth = listElement?.offsetWidth ?? 0;
    const wrapperWidth = wrapperElement?.offsetWidth ?? 0;
    return listWidth - wrapperWidth;
  }

  updateIsShowSlideBtns() {
    this.isShowSlideBtns = this.isListOverflow();
  }

  onResize() {
    this.updateIsShowSlideBtns();
  }
}
