import { CommonModule } from '@angular/common';
import {
  Component,
  computed,
  input,
  model,
  ModelSignal,
  OnChanges,
  output,
  OutputEmitterRef,
  viewChild,
} from '@angular/core';
import {
  ControlValueAccessor,
  FormsModule,
  NG_VALUE_ACCESSOR,
} from '@angular/forms';
import {
  NzDatePickerComponent,
  NzDatePickerModule,
} from 'ng-zorro-antd/date-picker';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { DatePicker } from './date-picker.model';
import { NzTimePickerModule } from 'ng-zorro-antd/time-picker';
import { ControlComponent } from '../control/control.component';
import { en_US, NZ_I18N, NzI18nService } from 'ng-zorro-antd/i18n';
import * as moment from 'moment';
import { NzSafeAny } from 'ng-zorro-antd/core/types';

@Component({
  selector: 'hrdx-date-picker',
  standalone: true,
  imports: [
    CommonModule,
    NzDatePickerModule,
    FormsModule,
    NzIconModule,
    NzTimePickerModule,
  ],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      multi: true,
      useExisting: DatePickerComponent,
    },
    { provide: NZ_I18N, useValue: en_US },
  ],
  templateUrl: './date-picker.component.html',
  styleUrl: './date-picker.component.less',
})
export class DatePickerComponent
  extends ControlComponent
  implements OnChanges, ControlValueAccessor
{
  NZ_I18N = NZ_I18N;
  en_US = en_US;
  override value!: ModelSignal<DatePicker['date']>;
  override valueChange!: OutputEmitterRef<DatePicker['date']>;
  type = input<DatePicker['type']>('date-picker');
  date = model<DatePicker['date']>('');
  placeHolder = input<DatePicker['placeHolder']>('');
  allowClear = input<DatePicker['allowClear']>(true);
  format = input<DatePicker['format']>('DD/MM/yyyy');
  mode = input<DatePicker['mode']>('date');
  showTime = input<DatePicker['showTime']>(false);
  time = input<DatePicker['time']>('');
  hideSuffixIcon = input<boolean>(false);
  dateChanged = output<
    Date | (Date | undefined)[] | undefined | string | string[]
  >();
  timeChanged = output<DatePicker['time']>();
  touched = output<DatePicker['touch']>();
  dateRangeChanged = output<DatePicker['initialDateRangeChange']>();
  _disabled = input<DatePicker['disabled']>(false);
  inputReadonly = input<DatePicker['inputReadonly']>(false);
  disabledDate = input<DatePicker['disabledDate']>();
  disabledTime = input<DatePicker['disabledTime']>();
  datePicker = viewChild<NzDatePickerComponent>('datePicker');

  constructor(private i18n: NzI18nService) {
    super();
  }

  updatePositionDropdown = () => {
    this.datePicker()?.cdkConnectedOverlay?.overlayRef?.updatePosition();
  };

  updateInput = () => {
    this.setValueInput(
      this.datePicker()?.pickerInput?.nativeElement.value,
      true,
    );
  };

  onOpenChange($event: NzSafeAny) {
    this.setValueInput(this.value());
    this.touched.emit($event);

    const scrollables = [
      'mark-scroll-container',
      'right-side',
      'ant-modal-body',
      'scroll-page-container',
    ];

    let body = null;
    for (const className of scrollables) {
      const element = document.querySelector(`.${className}`);
      if (element) {
        body = element;
        break;
      }
    }

    const inputRef = this.datePicker()?.pickerInput?.nativeElement;

    if ($event) {
      body?.addEventListener('scroll', this.updatePositionDropdown);
      inputRef?.addEventListener('blur', this.updateInput);
    } else {
      body?.removeEventListener('scroll', this.updatePositionDropdown);
      inputRef?.removeEventListener('blur', this.updateInput);
    }
  }

  formatComputed = computed(() => this.format() ?? 'DD/MM/YYYY');

  keydown(e: any) {
    if (e.key === 'Enter') {
      e.preventDefault();
      this.setValueInput(e?.target?.['value'], true);
    } else if (e.key === 'Tab') {
      this.setValueInput(e?.target?.['value']);
    }
  }

  isValidDate(input: string) {
    // Regex kiểm tra định dạng DDMMYYYY hoặc DD/MM/YYYY
    const dateRegex = /^(?:\d{1,2}\/\d{1,2}\/\d{4}|\d{8})$/;
    if (!dateRegex.test(input)) {
      return false; // Không khớp định dạng
    }

    // Chuẩn hóa dữ liệu thành DD/MM/YYYY để dễ kiểm tra
    const normalizedDate = input.includes('/')
      ? input
      : input.replace(/(\d{1,2})(\d{1,2})(\d{4})/, '$1/$2/$3');

    const [day, month, year] = normalizedDate.split('/').map(Number);

    // Kiểm tra giá trị hợp lệ của ngày, tháng, năm
    if (month < 1 || month > 12) return false; // Tháng không hợp lệ
    if (day < 1 || day > 31) return false; // Ngày không hợp lệ
    if (year < 1000 || year > 9999) return false; // Năm không hợp lệ

    // Kiểm tra ngày hợp lệ theo tháng (bao gồm năm nhuận)
    const daysInMonth = new Date(year, month, 0).getDate(); // Số ngày trong tháng
    return day <= daysInMonth;
  }

  setValueInput(value: NzSafeAny, clearIfInvalid = false) {
    if (!value || value.toString().trim() === '') {
      this.value.set(undefined);
      this.dateChanged.emit(this.value());
      return;
    }

    if (typeof value === 'string') {
      value = value.trim();
    }

    if (!this.isValidDate(value) && !this.showTime()) {
      if (clearIfInvalid) {
        this.datePicker()?.updateInputValue();
      }
      return;
    }

    const format = this.format().replace(/(dd|yyyy)/g, (match) =>
      match.toUpperCase(),
    );
    const isValidFormat = moment(value, format).isValid();
    if (!isValidFormat) return;
    // only emit when value changed
    const currentValue = this.value() as string;
    if(currentValue && moment(value, format).isSame(moment(new Date(currentValue), format), 'date')) return;
    this.value.set(moment(value, format).toDate());
    this.dateChanged.emit(this.value());
  }

  get defaultOpenValue() {
    const momentNow = moment();
    momentNow.set('hour', 0);
    momentNow.set('minute', 0);
    momentNow.set('second', 0);
    return momentNow.toDate();
  }
}
