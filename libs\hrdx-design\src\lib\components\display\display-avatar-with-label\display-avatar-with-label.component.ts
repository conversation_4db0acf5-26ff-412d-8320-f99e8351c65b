import { CommonModule } from '@angular/common';
import { Component, computed } from '@angular/core';
import { DisplayCommonComponent } from '../display-common/display-common.component';
import { isEmpty, isNumber } from 'lodash';
import { TooltipComponent } from '../../tooltip';
import {
  AvatarComponent,
  AvatarShape,
  AvatarSize,
  AvatarType,
} from '../../avatar';

@Component({
  selector: 'hrdx-display-avatar-with-label',
  standalone: true,
  imports: [CommonModule, TooltipComponent, AvatarComponent],
  templateUrl: './display-avatar-with-label.component.html',
  styleUrl: './display-avatar-with-label.component.less',
})
export class DisplayAvatarWithLabelComponent extends DisplayCommonComponent {
  avatarSize = AvatarSize;
  avatarShape = AvatarShape;
  avatarType = AvatarType;

  isEmpty = computed(() => {
    const value = this.value();
    if (isNumber(value)) return false;
    return isEmpty(value);
  });

  avatarUrl = computed(() => {
    const extraData = this.extraData();
    // if (!extraData?.avatarFile) return '';
    // return `/api/personals/avatar/basic-infomation/avatar/${extraData?.avatarFile}`;
    if (!extraData?.avatarFile || !extraData?.avatarLink) return '';
    return extraData?.avatarLink;
  });
}
