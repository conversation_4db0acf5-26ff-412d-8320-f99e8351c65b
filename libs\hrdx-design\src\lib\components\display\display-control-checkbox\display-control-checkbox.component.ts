import { Component, computed, effect, forwardRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DisplayCommonComponent } from '../display-common/display-common.component';
import { DisplayComponent } from '../display.component';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { FormsModule } from '@angular/forms';
import { isNil } from 'lodash';

type ConditionOperator = '$eq' | '$ne' | '$in' | '$nin';

type DisplayControlCheckboxProps = {
  mappingValue?: { input: boolean; output: any }[];
  disabledByInputValue?: {
    value: any;
    operator: ConditionOperator;
  };
};

@Component({
  selector: 'hrdx-display-control-checkbox',
  standalone: true,
  imports: [
    CommonModule,
    forwardRef(() => DisplayComponent),
    NzCheckboxModule,
    FormsModule,
  ],
  templateUrl: './display-control-checkbox.component.html',
  styleUrl: './display-control-checkbox.component.less',
})
export class DisplayControlCheckboxComponent extends DisplayCommonComponent {
  _props = computed(() => this.props() as DisplayControlCheckboxProps);

  getEmitValue = (value: boolean) => {
    const mapValueConfig = this._props()?.mappingValue;
    if (mapValueConfig) {
      return mapValueConfig.find((item) => item.input === value)?.output;
    }
    return value;
  };

  getFieldValue = (value: boolean) => {
    const mapValueConfig = this._props()?.mappingValue;
    if (mapValueConfig) {
      return mapValueConfig.find((item) => item.output === value)?.input;
    }
    return value;
  };

  fieldValue = computed(() => this.getFieldValue(this.value()));

  disabledEffect = effect(() => {
    const { value: compareValue, operator } =
      this._props()?.disabledByInputValue ?? {};
    if (isNil(compareValue)) return;
    this._disabled = this.checkValueByConditionOperator(
      this.value(),
      compareValue,
      operator ?? '$eq',
    );
  });

  private checkValueByConditionOperator = (
    value: any,
    compareValue: any,
    operator: ConditionOperator,
  ) => {
    switch (operator) {
      case '$eq':
        return value === compareValue;
      case '$ne':
        return value !== compareValue;
      case '$in':
        return compareValue.includes(value);
      case '$nin':
        return !compareValue.includes(value);
      default:
        return false;
    }
  };

  override handleChange(e: boolean) {
    console.log('change ne', e)
    const valueToEmit = this.getEmitValue(e);
    super.handleChange(valueToEmit);
  }
}
