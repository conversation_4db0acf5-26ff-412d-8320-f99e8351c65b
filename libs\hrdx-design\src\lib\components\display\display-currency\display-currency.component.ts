import { CommonModule } from '@angular/common';
import { Component, computed } from '@angular/core';
import { isNil } from 'lodash';
import { DisplayCommonComponent } from '../display-common/display-common.component';
import { formatCurrency } from '../../../shared';

@Component({
  selector: 'hrdx-display-currency',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './display-currency.component.html',
  styleUrl: './display-currency.component.less',
})
export class DisplayCurrencyComponent extends DisplayCommonComponent<{precision?: number}> {
  valueFormatted = computed(() => {
    const value = this.value();
    if (!value || value.toString().includes('/')) return value;
    return formatCurrency(value, this.extraConfig());
  });

  isValidValue = computed(() => {
    const valueFormatted = this.valueFormatted();
    if (typeof valueFormatted === 'string') return valueFormatted.length > 0;
    return !isNil(valueFormatted);
  });
}
