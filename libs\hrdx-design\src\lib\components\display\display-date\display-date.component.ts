import { CommonModule } from '@angular/common';
import { Component, computed } from '@angular/core';
import { DisplayCommonComponent } from '../display-common/display-common.component';

import * as moment from 'moment';
import { TooltipComponent } from '../../tooltip';
import { isEmpty, isNil } from 'lodash';
@Component({
  selector: 'hrdx-display-date',
  standalone: true,
  imports: [CommonModule, TooltipComponent],
  templateUrl: './display-date.component.html',
  styleUrl: './display-date.component.less',
})
export class DisplayDateComponent extends DisplayCommonComponent {
  formattedDate() {
    if (
      isNil(this.value()) ||
      (typeof this.value() === 'string' && isEmpty(this.value()?.trim()))
    )
      return '';
    let value = this.value();
    if (typeof value == 'number' && this.checkIsSecondTimestamp(value)) {
      value = value * 1000;
    }
    return moment(value)
      .utcOffset(moment().utcOffset())
      .format(this.props().type);
  }

  checkIsSecondTimestamp(number: number) {
    return number < 1e12;
  }
}
