<!-- <hrdx-select
  [optionList]="options()"
  [value]="value()"
  (valueChange)="handleChange($event)"
  [placeHolder]="''"
  [borderLess]="false"
>
</hrdx-select> -->
@if (_readOnly) {
  <hrdx-display-tooltip [value]="value()?.label ?? value()"></hrdx-display-tooltip>
} @else {
  @if (focus) {
    <nz-select
      class="display-select"
      nzPlaceHolder="Please select"
      [ngModel]="value()"
      (ngModelChange)="handleChange($event)"
      [nzAllowClear]="true"
      [nzAutoFocus]="true"
      [nzOpen]="focus"
      (nzOpenChange)="openChange($event)"
      [compareWith]="compare"
      [nzShowSearch]="true"
      [nzSuffixIcon]="suffixIcon"
      [nzClearIcon]="closeIcon"
    >
      @for (item of _options(); track item) {
        <nz-option
          [nzLabel]="item.label"
          [nzValue]="getOutputValue(item)"
          [nzCustomContent]="true"
        >
          <div class="display-select__custom-option">
            <span class="label">{{ item.label }}</span>
            <hrdx-icon
              [icon]="'icon-check-bold'"
              class="selected-icon"
            ></hrdx-icon>
          </div>
        </nz-option>
      }
    </nz-select>

    <ng-template #suffixIcon>
      <hrdx-icon
        [icon]="'icon-caret-down-bold'"
        class="suffix-icon"
      ></hrdx-icon>
    </ng-template>
    <ng-template #closeIcon>
      <hrdx-icon [icon]="'icon-x-bold'" class="suffix-icon"></hrdx-icon>
    </ng-template>
  } @else {
    <span (click)="openChange(true)" class="display-select-value">{{
      value() ? (value().label ? value().label : value()) : '--'
    }}</span>
  }
}
