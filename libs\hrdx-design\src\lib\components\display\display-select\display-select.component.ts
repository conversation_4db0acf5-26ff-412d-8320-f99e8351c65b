import { CommonModule } from '@angular/common';
import { Component, effect, OnInit, signal, untracked } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { isEqual, isNil } from 'lodash';
import { NzSafeAny } from 'ng-zorro-antd/core/types';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { DisplayCommonComponent } from '../display-common/display-common.component';
import { IconComponent } from '../../icon';
import { DisplayTooltipComponent } from '../display-tooltip/display-tooltip.component';
function getValue(data: NzSafeAny, path: (string | number)[]) {
  return path.reduce((res, it) => {
    if (!isNaN(+it) && +it < 0) {
      return res?.[res.length - 1 + +it] ?? undefined;
    }
    return res?.[it] ?? undefined;
  }, data);
}
@Component({
  selector: 'hrdx-display-select',
  standalone: true,
  imports: [CommonModule, FormsModule, NzSelectModule, IconComponent, DisplayTooltipComponent],
  templateUrl: './display-select.component.html',
  styleUrl: './display-select.component.less',
})
export class DisplaySelectComponent
  extends DisplayCommonComponent
  implements OnInit
{
  focus = false;
  _options = signal<{ label: string; value: NzSafeAny }[]>([]);
  isOpen = false;

  override ngOnInit() {
    super.ngOnInit();
    if (!this.props()?.syncValueWithOptions || this.optionsSubscription) return;
    let synced = false;
    this.optionsSubscription = this.options$()?.subscribe((options) => {
      this._options.set(options);
      const currentValue = this.value();
      if (synced || !currentValue) return;
      const optionFound = options.find(
        (o) => o?.value === currentValue || o === currentValue,
      );
      if (!optionFound) return;
      this.value.set(this.getOutputValue(optionFound));
      this.handleChange(this.value());
      synced = true;
    });
  }
  optionsEffect = effect(
    () => {
      this._options.set(this.options());
    },
    { allowSignalWrites: true },
  );

  effectValue = effect(() => {
    let validateValue: { label: string; value: NzSafeAny }[] = [];
    const v = this.value();
    const mode = 'default';
    if (mode === 'default') {
      if (v?.label && v?.value) {
        validateValue = [v];
      } else if (typeof v === 'string') {
        validateValue = [{ label: v, value: v }];
      } else {
        validateValue = [];
      }
    }
    const oldOptionList = untracked(() => structuredClone(this._options() ?? [])); ;
    const notExistOptions = validateValue.filter((it) => {
      return !oldOptionList?.find((option) => {
        return this.compare(option.value, it);
      });
    });

    if(notExistOptions.length > 0){
      this._options.set([
        ...notExistOptions,
        ...oldOptionList,
      ]);
    }
  }, { allowSignalWrites: true });
  compare = (o1: NzSafeAny, o2: NzSafeAny) => {
    if (isNil(o1) && isNil(o2)) return true;
    if (isNil(o1) || isNil(o2)) return false;
    const new1 = !isNil(o1.value) ? o1.value : o1;
    const new2 = !isNil(o2.value) ? o2.value : o2;
    const t = isEqual(new1, new2);
    return t;
  };
  openChange(isOpen: boolean) {
    this.focus = isOpen;
    if (isOpen) {
      if (!this.isOpen && !this.optionsSubscription)
        this.optionsSubscription = this.options$()?.subscribe((options) => {
          this._options.set(options);
        });
      this.isOpen = true;
    }
  }

  getOutputValue(option: { label: string; value: NzSafeAny }) {
    if (this.props()?.outputValue) {
      return getValue(option, this.props().outputValue.split('.'));
    }
    return option;
  }
}
