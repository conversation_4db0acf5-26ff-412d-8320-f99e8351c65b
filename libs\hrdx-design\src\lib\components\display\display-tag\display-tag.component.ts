import { CommonModule } from '@angular/common';
import { Component, computed } from '@angular/core';
import { DisplayCommonComponent } from '../display-common/display-common.component';
import { isNil } from 'lodash';
import { IconComponent } from '../../icon';
import { TooltipComponent } from '../../tooltip';

type TagSize = 'small' | 'medium' | 'large' | 'default';

interface ITag {
  value: string;
  class?: string;
  style?: {
    color: string;
    background_color: string;
  };
  icon?: string;
  label?: string;
  size?: TagSize;
}

type TTagExtraConfig = {
  tags?: ITag[];
  size?: TagSize; // if size is not set in tag item, use size in extraConfig, other wise use size default.
};

enum TagType {
  Success = 'success',
  Warning = 'warning',
  Error = 'error',
  Infor = 'infor',
  Orange = 'orange',
  Violet = 'violet',
}

@Component({
  selector: 'hrdx-display-tag',
  standalone: true,
  imports: [CommonModule, IconComponent, TooltipComponent],
  templateUrl: './display-tag.component.html',
  styleUrl: './display-tag.component.less',
})
export class DisplayTagComponent extends DisplayCommonComponent<TTagExtraConfig> {
  tag = computed<ITag>(() => {
    if (this.props()?.mode === 'boolean') {
      return this.formattedBooleanTag() as ITag;
    }
    if (this.props()?.mode === 'custom') {
      return this.formattedCustomTag() as ITag;
    }
    const tags = this.extraConfig()?.tags;

    if (tags) {
      return this.getTagByExtraConfig(tags) as ITag;
    }
    return this.formattedDefaultTag() as ITag;
  });

  tagClass = computed<string>(() =>
    ['tag-item', this.tag()?.class, this.getSizeClass(this.tag())].join(' '),
  );

  getTagByExtraConfig(tags: ITag[]) {
    const value =
      !isNil(this.value()) && typeof this.value() === 'object'
        ? this.value().value
        : this.value();

    const getTagByValue = tags.find((tag) => tag.value === value);
    const defaultTag = {
      value: value,
      class: isNil(value) ? '' : 'default',
    };
    return getTagByValue ?? defaultTag;
  }

  static getDefaultTagConfig(type: TagType) {
    switch (type) {
      case TagType.Error:
        return {
          class: 'error',
        };
      case TagType.Success:
        return {
          class: 'success',
        };
      case TagType.Infor:
        return {
          class: 'infor',
        };
      case TagType.Warning:
        return {
          class: 'warning',
        };
      case TagType.Orange:
        return {
          class: 'orange',
        };
      case TagType.Violet:
        return {
          class: 'violet',
        };
      default:
        return {
          class: 'default',
        };
    }
  }

  formattedDefaultTag() {
    const value = this.value() ?? {};
    return {
      ...DisplayTagComponent.getDefaultTagConfig(value.type),
      ...value,
      label: value.label || '--',
    };
  }

  formattedCustomTag() {
    return {
      value: this.value()?.label ?? this.value() ?? '--',
      class:
        this.value()?.class ??
        (!this.value() || this.value()?.background_color ? '' : 'success'),
      style: {
        color: this.value()?.color,
        background_color: this.value()?.background_color,
      },
      icon: '',
    };
  }

  formattedBooleanTag() {
    switch (this.value()) {
      case true:
      case 'Active':
        return {
          value: 'Active',
          class: 'success',
          icon: '',
        };
      case false:
      case 'Inactive':
        return {
          value: 'Inactive',
          class: 'default',
          icon: '',
        };
      default:
        return {
          value: '',
          class: '',
          icon: '',
        };
    }
  }

  getSizeClass(tag: ITag) {
    const prefix = 'tag-size';
    const size = tag?.size ?? this.extraConfig()?.size ?? 'default';
    return `${prefix}-${size}`;
  }
}
