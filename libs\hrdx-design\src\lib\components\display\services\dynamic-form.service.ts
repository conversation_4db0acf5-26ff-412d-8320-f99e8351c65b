import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { ChangeDetectorRef, Injectable, inject } from '@angular/core';
import {
  AbstractControl,
  AsyncValidatorFn,
  FormArray,
  ValidationErrors,
  ValidatorFn,
  Validators,
} from '@angular/forms';
import {
  QueryFilter,
  QuerySortOperator,
  RequestQueryBuilder,
} from '@nestjsx/crud-request';
import { Buffer } from 'buffer';
import * as Handlebars from 'handlebars';
import * as jsonata from 'jsonata';
import * as _ from 'lodash';
import { isArray, isEmpty, isEqual, isNil, get } from 'lodash';
import * as moment from 'moment';
import { NzSafeAny } from 'ng-zorro-antd/core/types';
import * as b64 from 'b64-to-blob';

import {
  Observable,
  catchError,
  combineLatest,
  distinctUntilChanged,
  firstValueFrom,
  forkJoin,
  from,
  map,
  mergeMap,
  of,
  switchMap,
  tap,
  share,
  finalize,
} from 'rxjs';
import {
  FieldControlConfig,
  FieldValidator,
  Source,
  SourceField,
} from './field-config.interface';
import { Values } from './field.interface';
import {
  NzNotificationDataOptions,
  NzNotificationService,
} from 'ng-zorro-antd/notification';
interface FileType {
  FieldValue?: 'file';
  uid: string;
  name: string;
  type?: string;
  data?: string;
  url?: string;
  fileField?: string;
  fileValue?: string;
}
@Injectable({
  providedIn: 'root',
})
export class CommonDynamicFormService {
  http = inject(HttpClient);
  notificationService = inject(NzNotificationService);

  private apiCache = new Map<
    string,
    {
      data: any;
      timestamp: number;
    }
  >();

  // Track in-flight requests
  private inFlightRequests = new Map<string, Observable<any>>();

  // Helper to create cache key
  private createCacheKey(
    url: string,
    body: any,
    resultTransform?: string,
  ): string {
    return `${url}-${JSON.stringify(body)}-${resultTransform ?? ''}`;
  }

  setDataHeaders(faceCode?: string | null, authAction?: string) {
    const headers: any = {};
    if (faceCode) {
      headers['Ppx-function-code'] = faceCode;
    }
    if (authAction) {
      headers['ppx-action-code'] = authAction;
    }
    return headers;
  }

  registerFunction(
    expression: jsonata.Expression,
    funcName: string,
    source: Source,
    faceCode?: string | null,
    authAction?: string,
  ) {
    expression.registerFunction(funcName, async (...values) => {
      const data: { [k: string]: NzSafeAny } = {};
      source.params?.forEach((param, idx) => {
        data[param] = values[idx];
      });
      const res = await firstValueFrom(
        this.getValueFromApi(source, data, undefined, faceCode, authAction),
      );
      return res;
    });
  }

  getJsonataExpression(
    sources: { [k: string]: Source },
    faceCode?: string | null,
    authAction?: string,
  ) {
    return async (transform: string, data: unknown) => {
      const newData: { [k: string]: NzSafeAny } = _.cloneDeep(data) as {
        [k: string]: NzSafeAny;
      };
      if (newData?.['function']) delete newData['function'];
      if (!transform) transform = 'null';
      const expressionBody = jsonata(transform);
      expressionBody.registerFunction(
        'getFieldGroup',
        (path: (string | number)[], fields: NzSafeAny, parentNode = 0) => {
          let newPath;
          if (parentNode > 0) newPath = path?.slice(0, -parentNode) || 0;
          else newPath = path.slice(0);
          const value = this.getValue(fields, newPath);
          return value;
        },
      );
      expressionBody.registerFunction(
        'pop',
        (array: Array<NzSafeAny>, start?: number, end?: number) => {
          if (!array) return undefined;
          return array.slice(start, end);
        },
      );
      expressionBody.registerFunction('ParseInt', (num: string) => {
        if (isNil(num)) return undefined;
        return +num;
      });
      expressionBody.registerFunction('ParseInt2', (num: string) => {
        if (isNil(num)) return undefined;
        return (+num).toFixed(1);
      });
      expressionBody.registerFunction(
        'CalDate',
        (
          date: Date | string | undefined,
          amount: number,
          unit: moment.DurationInputArg2,
        ) => {
          const newDate = moment(date).add(amount, unit).toISOString();
          return newDate;
        },
      );
      expressionBody.registerFunction(
        'GetDatePart',
        (date: Date | string | undefined, unit: moment.DurationInputArg2) => {
          const part = moment(date).get(unit);
          return part;
        },
      );
      expressionBody.registerFunction(
        'DateDiff',
        (
          start: Date | string | undefined,
          end: Date | string | undefined,
          unit: moment.DurationInputArg2,
          round = true,
        ) => {
          const diff = moment(start).diff(end, unit, true);
          if (round) return Math.round(diff);
          return diff;
        },
      );
      expressionBody.registerFunction(
        'DateDiffNotRound',
        (
          start: Date | string | undefined,
          end: Date | string | undefined,
          unit: moment.DurationInputArg2,
          fractionDigits?: number,
        ) => {
          if (!start || !end) return 0;
          const num = moment(start).diff(end, unit, true);

          return Number(num.toFixed(fractionDigits));
        },
      );
      expressionBody.registerFunction(
        'DateTimeHourDuration',
        (start: Date | string | undefined, end: Date | string | undefined) => {
          if (!start || !end) return 0;
          const startTime = moment(start, 'HH:mm');
          const endTime = moment(end, 'HH:mm');
          const duration = moment.duration(endTime.diff(startTime));
          let hours = duration.asHours();
          hours = Math.round(hours * 2) / 2;
          return hours;
        },
      );
      expressionBody.registerFunction(
        'DateFormat',
        (date: Date | string | undefined, format: string) => {
          if (!date) return undefined;
          return moment(date).format(format);
        },
      );

      expressionBody.registerFunction(
        'DateToTimestamp',
        (date: Date | string | undefined, format: string) => {
          if (!date) return undefined;
          return moment(date).unix();
        },
      );

      expressionBody.registerFunction(
        'DateToTimestampUTC',
        (
          date: Date | string | undefined,
          isDateHour?: boolean,
          isNewVersion?: boolean,
        ) => {
          if (!date) return undefined;
          const currentDate = new Date(date);
          let startOfDay = currentDate;

          if (isNewVersion) {
            startOfDay = new Date(startOfDay.toUTCString());
          } else {
            startOfDay = new Date(
              Date.UTC(
                currentDate?.getFullYear(),
                currentDate?.getMonth(),
                currentDate?.getDate(),
                isDateHour ? currentDate.getHours() : 0,
                isDateHour ? currentDate.getMinutes() : 0,
                isDateHour ? currentDate.getSeconds() : 0,
              ),
            );
          }
          const seconds = Math.floor(startOfDay.getTime() / 1000);
          return seconds;
        },
      );

      expressionBody.registerFunction(
        'weekday',
        (date: Date | string | undefined, number: number) => {
          if (!date) return undefined;
          return moment(date).clone().weekday(number);
        },
      );

      expressionBody.registerFunction(
        'timestamp',
        (timestamp: number, format: string) => {
          return moment.unix(timestamp).format(format);
        },
      );

      expressionBody.registerFunction(
        'timestampToDateUTC',
        (timestamp: number) => {
          return moment.unix(timestamp).toISOString();
        },
      );

      expressionBody.registerFunction(
        'splitIntoWeeks',
        (
          fromDate: Date | string | undefined,
          toDate: Date | string | undefined,
        ) => {
          const weeks: { start: moment.Moment; end: moment.Moment }[] = [];
          if (!fromDate || !toDate) return undefined;
          let currentWeekStart = moment(fromDate).startOf('week');
          const endDate = moment(toDate);
          while (currentWeekStart.isBefore(endDate)) {
            const week = {
              start: currentWeekStart.clone(),
              end: currentWeekStart.clone().endOf('week'),
            };

            weeks.push(week);
            currentWeekStart = currentWeekStart
              .clone()
              .add(1, 'week')
              .startOf('week');
          }

          return weeks;
        },
      );

      expressionBody.registerFunction(
        'getIdx',
        (arr: NzSafeAny[], path: number) => {
          return arr[path];
        },
      );

      expressionBody.registerFunction(
        'roundInt',
        (num: number, post: number) => {
          if (!num) return undefined;
          return +num.toFixed(post);
        },
      );
      expressionBody.registerFunction(
        'resultPattern',
        (
          num: number | undefined,
          board: {
            min: number | undefined;
            max: number | undefined;
            value: number | string;
          }[],
        ) => {
          if (!num) return 0;
          let res: number | string = 0;
          let check = false;
          board.forEach((row) => {
            if (check) return;
            if (row.min === undefined) row.min = -Infinity;
            if (row.max === undefined) row.max = Infinity;
            if (row.min === row.max && num === row.min) {
              res = row.value;
              check = true;
            }
            if (row.min !== row.max && row.min < num && num < row.max) {
              res = row.value;
              check = true;
            }
          });
          return res;
        },
      );
      expressionBody.registerFunction(
        'GetBoundaryDate',
        (
          date: Date | string | undefined,
          unit: moment.DurationInputArg2,
          position,
        ) => {
          if (!date) return undefined;
          const momentDate = moment(date);
          if (!momentDate.isValid()) {
            throw new Error('Invalid date');
          }
          return position === 'start'
            ? momentDate.startOf(unit).toISOString()
            : momentDate.endOf(unit).toISOString();
        },
      );
      expressionBody.registerFunction('isNilorEmpty', (value: NzSafeAny) => {
        if (isNil(value)) return true;
        if (typeof value === 'string' && value.trim() === '') {
          return true;
        }

        if (typeof value === 'object' && Object.keys(value).length === 0) {
          if (!(value instanceof Date)) return true;
        }

        if (Array.isArray(value) && value.length === 0) {
          return true;
        }
        if (Array.isArray(value) && value.length > 0) {
          return value.every((item) => isNil(item));
        }

        return false;
      });

      expressionBody.registerFunction(
        'indicativeRate',
        (
          arr: number[] | undefined,
          board: { code: number; value: number[] }[],
        ) => {
          if (!arr) return 0;
          const n_miss = arr.filter((it) => it === 1).length;
          const n_hit = arr.filter((it) => it === 2).length;
          const n_indicative = arr.length;
          const idx = n_miss ? n_indicative - n_miss : 2 * n_indicative - n_hit;
          return board.find((item) => item.code === n_indicative)?.value[idx];
        },
      );
      expressionBody.registerFunction('randomText', (n: number) => {
        const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890';
        let text = '';
        for (let i = 0; i < n; i++) {
          text += possible.charAt(Math.floor(Math.random() * possible.length));
        }
        return text;
      });
      expressionBody.registerFunction('test', (num: any) => {
        console.log(num, 'ptest');
        return num;
      });

      expressionBody.registerFunction('tempFunc', (arr: any[], value: any) => {
        return arr.find((it) => {
          return (
            value?._functionGroupPermissonIds?.includes(
              it.componentsGroupValue,
            ) && value?._dataAreaIds?.includes(it.dataAreaValue)
          );
        });
      });
      expressionBody.registerFunction(
        'validInsertData',
        (arr: any[], value: any) => {
          const insuranceSalaryStepCodeSelect = value?.insuranceSalaryStep?.map(
            (el: any) => el.value,
          );
          return arr.find((it) => {
            return (
              value?.insuranceSalaryClass?.value ==
                it.insuranceSalaryClassCode &&
              ((value?.insuranceRegion == null &&
                it.insuranceRegionCode == null) ||
                value?.insuranceRegion?.value == it.insuranceRegionCode) &&
              ((value?.insuranceSalaryLevel == null &&
                it.insuranceSalaryLevelCode == null) ||
                value?.insuranceSalaryLevel?.value ==
                  it.insuranceSalaryLevelCode) &&
              insuranceSalaryStepCodeSelect?.includes(
                it.insuranceSalaryStepCode,
              )
            );
          });
        },
      );
      expressionBody.registerFunction(
        'buildTree',
        (
          items: {
            [key: string]: any;
          }[],
          directKey: string,
        ) => {
          let tree: any = [];
          const lookup: any = {};
          if (!items) {
            return null;
          }
          items.forEach((item) => {
            lookup[item['id']] = { ...item, children: [], isLeaf: true };
            tree.push(lookup[item['id']]);
          });
          items.forEach((item) => {
            if (lookup[item[directKey]]) {
              lookup[item[directKey]]?.children?.push(lookup[item['id']]);
              lookup[item[directKey]].isLeaf = false;
              const remove = tree.filter((i: any) => {
                return i[directKey] !== lookup[item[directKey]].id;
              });
              tree = remove;
            }
          });
          return tree;
        },
      );
      expressionBody.registerFunction('isDuplicateInArray', (arr: any[]) => {
        const seen = new Set();
        for (const item of arr) {
          if (
            !item ||
            (typeof item === 'object' &&
              (Object.keys(item).length === 0 ||
                Object.values(item).every((value) => !value))) ||
            (Array.isArray(item) && item.length === 0)
          ) {
            continue;
          }
          const normalizedValue = JSON.stringify(
            this.normalize(item),
            (key, value) => {
              return value === null ? undefined : value;
            },
          ); // Normalize and convert each element to a JSON string
          if (seen.has(normalizedValue)) {
            return true; // Return true if the element already exists in the set
          }
          seen.add(normalizedValue); // Add the element to the set
        }
        return false; // Return false if no duplicates are found
      });

      expressionBody.registerFunction(
        'queryInArray',
        (
          arr: any[],
          filterField: { [key: string]: any },
          outputFieldName?: string,
        ) => {
          if (isNil(arr) || !Array.isArray(arr)) return;
          const fieldName = Object.keys(filterField)[0]; // Get the field name from filterCondition
          const fieldValue = filterField[fieldName]; // Get the corresponding value for the field
          if (isNil(fieldValue)) return;
          // Find the elements in the array that match the filter condition
          const result = arr.filter(
            (item) =>
              JSON.stringify(this.normalize(item[fieldName])) ===
              JSON.stringify(this.normalize(fieldValue)),
          );

          // If there's only one matching element, return the value directly
          if (result.length === 1) {
            return !outputFieldName ? result[0] : result[0][outputFieldName];
          }

          // Otherwise, return an array of values for the outputFieldName from the matched elements
          return result.map((item) =>
            !outputFieldName ? item : item[outputFieldName],
          );
        },
      );
      expressionBody.registerFunction(
        'dateToTimestamp',
        (date: string | Date) => {
          return moment(date).unix();
        },
      );

      expressionBody.registerFunction(
        'NumberToCurrency',
        (number: number, precision?: number) => {
          if (typeof number !== 'number') return;
          if (!isNil(precision)) {
            number = _.round(number, precision);
          }
          return number.toString()?.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
        },
      );

      expressionBody.registerFunction(
        'getValueFieldByPosition',
        (
          dataByPositon?: Record<string, any> | null,
          fieldName?: string | string[],
          isValid?: boolean,
        ) => {
          if (dataByPositon) {
            let data: Record<string, any> | null = null;
            if (Array.isArray(fieldName)) {
              for (const name of fieldName) {
                if (dataByPositon?.[name]) {
                  data = dataByPositon?.[name];
                  break;
                }
              }
            } else {
              data = dataByPositon?.[fieldName || ''];
            }
            if (isValid && fieldName && data) {
              return data ?? '_setSelectValueNull';
            }
            return '_setSelectValueNull';
          }
          return null;
        },
      );

      expressionBody.registerFunction('getDataTreeTable', (value: any) => {
        if (
          value?.adminModulesList &&
          value?.rawfunctionGroupPermissionDetails
        ) {
          const list: any[] = value.adminModulesList;
          const data: any[] = value.rawfunctionGroupPermissionDetails;
          data.forEach((element: any) => {
            const found = list.find(
              (item: any) => item.functionId === element.functionId,
            );
            if (found) {
              if (!isArray(found.permission.fields[0].value)) {
                found.permission.fields[0].value = [];
              }
              found.permission.fields[0].value.push(element.actionId);
            } else {
              console.log('not found', element);
            }
          });
          value = list;
        }
        return value;
      });

      expressionBody.registerFunction(
        'uniqBy',
        (array: Record<string, any>[], key: string) => {
          return _.uniqBy(array, key);
        },
      );

      expressionBody.registerFunction(
        'getArrayValueOfRadioJobdata',
        async (isHasData: boolean, permission: Record<string, any>) => {
          const arrayData = [];
          if (isHasData && permission['isAddActionJobdata']) {
            arrayData.push('1');
          }
          if (permission['isAddOIRJobdata']) {
            arrayData.push('2');
          }
          if (isHasData && permission['isAddERNJobdata']) {
            arrayData.push('3');
          }
          return arrayData;
        },
      );

      expressionBody.registerFunction(
        'difference',
        (originalArray: any[], updatedArray: any[]) => {
          return _.difference(originalArray, updatedArray);
        },
      );

      expressionBody.registerFunction(
        'isEqualObject',
        (
          obj1: NzSafeAny,
          obj2: NzSafeAny,
          argumentsField: { key1: string; key2: string }[],
        ) => {
          return argumentsField.every((it) => {
            const value1 = get(obj1, it.key1); // Lấy giá trị từ obj1 theo key1
            const value2 = get(obj2, it.key2); // Lấy giá trị từ obj2 theo key2
            if (!isNil(value1) && !isNil(value2)) {
              return isEqual(value1, value2);
            } else {
              return isNil(value1) && isNil(value2);
            }
          });
        },
      );
      // expressionBody.registerFunction('jsonToBase64', this.jsonToBase64);

      // expressionBody.registerFunction('buildBodyBase64', this.buildBodyBase64);

      for (const source_name in sources) {
        // if (!source.params || source.params.length <= 0) continue;
        this.registerFunction(
          expressionBody,
          source_name,
          sources[source_name],
          faceCode,
          authAction,
        );
      }
      const res = await expressionBody.evaluate(newData);
      return res;
    };
  }

  buildBodyBase64 = (base64string: string) => {
    return { base64_string: base64string };
  };

  jsonToBase64 = (jsonObject: NzSafeAny): string => {
    // const utf8Bytes = new TextEncoder().encode(JSON.stringify(jsonObject));
    // return btoa(String.fromCharCode(...utf8Bytes));
    const utf8Bytes = new TextEncoder().encode(JSON.stringify(jsonObject));
    let binary = '';
    const len = utf8Bytes.length;

    for (let i = 0; i < len; i++) {
      binary += String.fromCharCode(utf8Bytes[i]);
    }

    return btoa(binary);
  };

  normalize(item: any): any {
    if (Array.isArray(item)) {
      const normalizedArray = item.map((element) => {
        return this.normalize(element); //  Recursively normalize each element
      });

      return normalizedArray.sort(); // Sort the normalized array
    } else if (typeof item === 'object' && item !== null) {
      return Object.keys(item)
        .sort()
        .reduce((result, key) => {
          result[key] = this.normalize(item[key]); // Normalize object values based on sorted keys
          return result;
        }, {} as any);
    } else {
      return item; // Return the item itself for other data types
    }
  }
  getFunctionSource(
    sources: { [k: string]: Source },
    faceCode?: string | null,
    authAction?: string,
  ) {
    return this.getJsonataExpression(sources, faceCode, authAction);
  }

  private showToast(
    type: string,
    title: string,
    content: string,
    options?: NzNotificationDataOptions & { size: 'default' | 'lg' },
  ) {
    const classes = [`toast-message-${type}`];
    classes.push(`toast-message__size-${options?.size ?? 'default'}`);
    if (!title || title.length === 0) {
      classes.push('no-title');
    }

    this.notificationService.create(type, title, content, {
      nzPlacement: 'topRight',
      nzClass: classes.join(' '),
      ...options,
    });
  }

  getValueFromApi(
    source: Source,
    values: NzSafeAny,
    options?: {
      onError?: (err: unknown) => void;
      returnError?: boolean;
      showError?: boolean;
    },
    faceCode?: string | null,
    authAction?: string,
  ): Observable<NzSafeAny> {
    const onError = (err: any) => {
      options?.onError?.(err);
      if (options?.showError || source.showError) {
        this.showToast('error', 'Error', err.error.message ?? err.error);
      }
      if (options?.returnError || source.returnError) {
        return of({ err, isError: true });
      }
      return of(undefined);
    };

    const finalAuthAction: string | undefined = source.authAction ?? authAction;
    return combineLatest({
      uri: source.uri
        ? from(jsonata(source.uri).evaluate({ ...values }))
        : of(''),
      query: source.queryTransform
        ? from(jsonata(source.queryTransform).evaluate({ ...values }))
        : of({}),
      body: source.bodyTransform
        ? from(jsonata(source.bodyTransform).evaluate({ ...values }))
        : of({}),
    }).pipe(
      switchMap(({ uri, query, body }) => {
        const qb = RequestQueryBuilder.create();
        const url = uri;
        if (source.isBase64) {
          body = this.buildBodyBase64(this.jsonToBase64(body ?? {}));
        }
        switch (source.method) {
          case 'GET': {
            qb.setPage(query.page);
            qb.setLimit(query.limit);

            const sortQuery = query.sort?.map(
              (sortObj: { field: string; order: string }) => ({
                field: sortObj.field,
                order: sortObj.order === 'ascend' ? 'ASC' : 'DESC',
              }),
            );

            qb.sortBy(sortQuery);
            const filterSearch = query.search
              ? [
                  {
                    field: 'search',
                    operator: '$eq',
                    value: query.search,
                  } as QueryFilter,
                ]
              : [];
            const filter = query.filter;
            const filterQuery = isArray(filter)
              ? filter.filter((it) => !isNil(it.value) && it.value !== '')
              : [];
            const newFilter = this.transferFilterToSearch([
              ...filterQuery,
              ...filterSearch,
            ]);
            qb.search({ $and: newFilter });
            const queryString = qb.query();
            const finalUrl = `${url}${queryString ? (url.includes('?') ? '&' + queryString : '?' + queryString) : ''}`;

            // Create cache key including resultTransform
            const cacheKey = this.createCacheKey(
              finalUrl,
              body,
              source.resultTransform,
            );

            // Check cache for GET requests
            if (!source.disabledCache) {
              const cached = this.apiCache.get(cacheKey);
              if (cached && this.isApiCacheValid(cached.timestamp)) {
                return of(cached.data).pipe(
                  mergeMap((data) =>
                    from(
                      this.getJsonataExpression({})(
                        source.resultTransform ?? '',
                        data,
                      ),
                    ),
                  ),
                );
              }
            }

            // Check for in-flight request
            const inFlight = this.inFlightRequests.get(cacheKey);
            if (inFlight) {
              return inFlight;
            }

            // Create new request
            const request = this.http
              .get(finalUrl, {
                headers: this.setDataHeaders(faceCode, finalAuthAction),
              })
              .pipe(
                catchError((err) => onError(err)),
                mergeMap((rawResult: any) => {
                  if (
                    rawResult?.['isError'] &&
                    rawResult?.['err'] instanceof HttpErrorResponse
                  )
                    return of(rawResult);
                  return from(
                    this.getJsonataExpression({})(
                      source.resultTransform ?? '',
                      rawResult,
                    ),
                  );
                }),
                tap((data) => {
                  if (!source.disabledCache) {
                    this.apiCache.set(cacheKey, {
                      data,
                      timestamp: Date.now(),
                    });
                  }
                  // Remove from in-flight requests when complete
                  this.inFlightRequests.delete(cacheKey);
                }),
                // Share the same response to all subscribers
                share(),
              );

            // Store the in-flight request
            this.inFlightRequests.set(cacheKey, request);
            return request;
          }
          case 'PATCH': {
            return !isEmpty(body)
              ? this.http
                  .patch(url, body, {
                    headers: this.setDataHeaders(faceCode, finalAuthAction),
                  })
                  .pipe(
                    catchError((err) => onError(err)),
                    tap((data) => {
                      if (source.updateCache)
                        sessionStorage.setItem(
                          source.updateCache,
                          JSON.stringify(data),
                        );
                    }),
                  )
              : of(undefined);
          }
          case 'PUT':
            return !isEmpty(body)
              ? this.http
                  .put(url, body, {
                    headers: this.setDataHeaders(faceCode, finalAuthAction),
                  })
                  .pipe(catchError((err) => onError(err)))
              : of(undefined);
          case 'POST': {
            const isFormData = source.isFormData;
            return !isEmpty(body)
              ? this.http
                  .post(
                    url,
                    isFormData ? this.transformJsonToFormData(body) : body,
                    {
                      headers: this.setDataHeaders(faceCode, finalAuthAction),
                    },
                  )
                  .pipe(
                    catchError((err) => onError(err)),
                    mergeMap((rawResult) =>
                      from(
                        this.getJsonataExpression({})(
                          source.resultTransform ?? '',
                          rawResult,
                        ),
                      ),
                    ),
                  )
              : of(undefined);
          }
        }
        return this.http.get(url, {
          headers: this.setDataHeaders(faceCode, finalAuthAction),
        });
      }),
    );
  }

  private transformJsonToFormData(body: NzSafeAny): FormData {
    const formData = new FormData();
    body = this.transformValues(body);
    Object.keys(body).forEach((key) => {
      if (isArray(body[key])) {
        let check = false;
        body[key].forEach((v: any) => {
          if (v?.FieldValue === 'file') {
            check = true;
            const blob = b64.default(v?.data.split(',')[1], v?.type);
            formData.append(key, blob, encodeURIComponent(v?.name));
            return;
          }
        });
        if (!check) formData.append(key, JSON.stringify(body[key]));
        return;
      }

      if (!isNil(body[key])) formData.append(key, JSON.stringify(body[key]));
      console.log(formData, body, isArray(body[key]), isNil(body[key]));
    });
    return formData;
  }

  private transformValues<
    T extends Record<string, unknown> | Record<string, unknown>[] | unknown,
  >(value: T): T {
    if (Array.isArray(value)) {
      return value.map((item) => this.transformValues(item)) as T;
    } else if (_.isDate(value)) {
      return value.toISOString() as T;
    } else if (_.isObject(value)) {
      const res = _.mapValues(value, (v) => this.transformValues(v)) as T;
      return res;
    } else {
      if (_.isString(value)) {
        return _.trim(_.toString(value)) as T;
      } else {
        return value;
      }
    }
  }

  private isApiCacheValid(timestamp: number): boolean {
    const CACHE_TTL = 5 * 60 * 1000; // 5 minutes
    return Date.now() - timestamp < CACHE_TTL;
  }

  setSessionStorageWithTimeout(key: string, value: NzSafeAny, timeout: number) {
    const data = {
      value: value,
      expiration: new Date().getTime() + timeout * 1000,
    };
    sessionStorage.setItem(key, JSON.stringify(data));
  }
  getSessionStorageWithTimeout(key: string) {
    const item = sessionStorage.getItem(key);

    if (!item) return null;
    const data = JSON.parse(item);
    if (new Date().getTime() > data.expiration) {
      sessionStorage.removeItem(key);
      return null;
    }
    return data.value;
  }

  transferFilterToSearch(filter: QueryFilter[]): any {
    return filter.map((it) => {
      if (it.operator === '$and' || it.operator === '$or') {
        return {
          [it.operator]: this.transferFilterToSearch(it.value as QueryFilter[]),
        };
      }
      return {
        [it.field]: {
          [it.operator]: it.value,
        },
      };
    });
  }

  distinct(prev: Values, curr: Values, source: SourceField | undefined) {
    const regex = /\$((\.\w*)|(\[[0-9]*\]))+/g;
    if (source?.dependants && source?.dependants.length > 0) {
      // assign params to dependants
      let dependants = source.dependants;
      if (source.params) {
        const resParams = Object.keys(source.params).map((key) => {
          const arrMatch = source
            .params![key].split(/[.[\]]+/)
            .slice(1)
            .filter((it) => it !== '');
          return {
            key: key,
            value: this.getValue(curr, arrMatch),
          };
        });

        dependants = dependants.map((it) => {
          return resParams.reduce((res, param) => {
            return res.replace(new RegExp(`\\${param.key}`, 'g'), param.value);
          }, it);
        });
      }

      // check current dependants and prev dependants are equal
      return dependants.every((dependant) => {
        const arrMatch = dependant
          .split(/[.[\]]+/)
          .slice(1)
          .filter((it) => it !== '');
        const prevValue = this.getValue(prev, arrMatch);
        const currValue = this.getValue(curr, arrMatch);
        if (isNil(prevValue) && isNil(currValue)) return true;
        return isEqual(prevValue, currValue);
      });
    }
    const pathArr = ((source?.transform ?? '').match(regex) ?? []).map((text) =>
      text.split(/[.[\]]+/).slice(1),
    );
    return pathArr.every((path) => {
      const prevValue = this.getValue(prev, path);
      const currValue = this.getValue(curr, path);
      if (isNil(prevValue) && isNil(currValue)) return true;
      return isEqual(prevValue, currValue);
    });
  }
  getValue(data: NzSafeAny, path: (string | number)[]) {
    return path.reduce((res, it) => {
      if (!isNaN(+it) && +it < 0) {
        return res?.[res.length - 1 + +it] ?? undefined;
      }
      return res?.[it] ?? undefined;
    }, data);
  }

  //refactor getValue
  // getValue(data: NzSafeAny, path: (string | number)[]): NzSafeAny {
  //   if (!data) {
  //     return undefined;
  //   }
  //   if (path.length < 1) return undefined;
  //   if (path.length === 1) {
  //     const tmp = path[0];
  //     if (
  //       typeof tmp === 'string' &&
  //       new RegExp(/\([a-z0-9]*\)/, 'g').test(tmp)
  //     ) {
  //       if (isArray(data)) {
  //         return data.map((it) => it[tmp.replace('(', '').replace(')', '')]);
  //       }
  //     }
  //     return data[tmp];
  //   } else {
  //     const thisPath = path.shift();
  //     if (!thisPath) return undefined;
  //     const tmp = thisPath;
  //     if (
  //       typeof tmp === 'string' &&
  //       new RegExp(/\([a-z0-9]*\)/, 'g').test(tmp)
  //     ) {
  //       if (isArray(data)) {
  //         return this.getValue(
  //           data.map((it) => it[tmp.replace('(', '').replace(')', '')]),
  //           path,
  //         );
  //       }
  //     }
  //     return this.getValue(data[thisPath], path);
  //   }
  // }

  setValue(data: NzSafeAny, path: (string | number)[], newValue: NzSafeAny) {
    if (!data) {
      return;
    }
    if (path.length < 1) return;
    if (path.length === 1) {
      data[path[0]] = newValue;
    } else {
      const thisPath = path.pop();
      if (!thisPath) return;
      this.setValue(data[thisPath], path, newValue);
    }
  }
  getObservable(
    func:
      | ((transform: string, data: unknown) => Promise<NzSafeAny>)
      | undefined,
    values: unknown,
    source: SourceField | undefined,
  ) {
    if (!source) return of(undefined);
    return func
      ? from(func(source.transform, values)).pipe(
          catchError((err) => {
            console.error(err, 'source', source);
            return of(undefined);
          }),
        )
      : of(undefined);
  }

  compileText(text: string, contextData: Record<string, unknown>) {
    return Handlebars.compile(text)(contextData);
  }

  customValidators(args: NzSafeAny, values: () => Values, text?: string) {
    return (control: AbstractControl): Observable<ValidationErrors | null> => {
      const v = control.getRawValue();
      return of({ ...values(), value: v }).pipe(
        distinctUntilChanged((prev, curr) => this.distinct(prev, curr, args)),
        switchMap((values) =>
          this.getObservable(values.function, values, args),
        ),
        map((value) => {
          return value
            ? {
                'ppx-custom': (label: string) =>
                  Handlebars.compile(text ?? '')({ label, values: values() }),
              }
            : null;
        }),
        // take(1),
      );
    };
  }

  fieldTypeChecked = ['translation', 'translationTextArea'];
  ValidatorDefaultCustom(
    type: FieldValidator['type'],
    args: NzSafeAny,
    fieldType: string,
    text?: string,
  ) {
    switch (type) {
      // SYS.FS.FR.12_01
      case 'row-option-required':
        return (controls: AbstractControl) => {
          if (!controls.value) return null; // Nếu không có dữ liệu, trả về null ngay lập tức
          // Lấy tất cả các giá trị của các field trong bảng
          const values = controls.value
            .flatMap((row: NzSafeAny) =>
              Object.values(row).flatMap((col: NzSafeAny) => col?.fields ?? []),
            )
            .map((field: NzSafeAny) => field?.value ?? '');
          // Kiểm tra nếu có ít nhất một giá trị và không chứa `args`
          if (
            values.some((v: any) => v) &&
            !values.some((v: any) => v.includes(args))
          ) {
            return {
              'row-option-required': () => Handlebars.compile(text)({}),
            } as ValidationErrors;
          }
          return null; // Hợp lệ
        };
      // return (controls: AbstractControl) => {
      //   let value = '';
      //   const hasValue = controls.value?.some((row: NzSafeAny) =>
      //     Object.keys(row).some((key) =>
      //       row[key]?.fields?.some((field: NzSafeAny) => {
      //         value = field?.value;
      //         return !!field?.value;
      //       }),
      //     ),
      //   );
      //   if (hasValue && !value?.includes(args) && value != '') {
      //     return {
      //       'row-option-required': () => Handlebars.compile(text)({}),
      //     } as ValidationErrors;
      //   } else return null;
      // };
      case 'required':
        return (controls: AbstractControl) => {
          const check =
            this.fieldTypeChecked.includes(fieldType) &&
            !controls.value?.default
              ? true
              : false;
          if (check || Validators.required(controls)) {
            return {
              required: (label: string) =>
                Handlebars.compile(
                  !label || label.length === 0
                    ? 'Cannot be empty'
                    : (text ?? 'Cannot be empty'),
                )({
                  label: label,
                }),
            } as ValidationErrors;
          } else return null;
        };
      case 'email':
        return (controls: AbstractControl) => {
          if (Validators.email(controls)) {
            return {
              email: (label: string) =>
                Handlebars.compile(text ?? '{{ label }} Invalid email format.')(
                  {
                    label: label,
                    args: args,
                  },
                ),
            } as ValidationErrors;
          } else return null;
        };
      case 'minLength':
        return (controls: AbstractControl) => {
          if (controls instanceof FormArray) {
            const value = this.clearEmptyField(
              _.cloneDeep(controls.getRawValue()),
            );
            if ((value?.length ?? 0) < args) {
              return {
                minLength: (label: string) =>
                  Handlebars.compile(
                    text ??
                      '{{ label }} must contain at least {{ args }} groups.',
                  )({
                    label: label,
                    args: args,
                  }),
              } as ValidationErrors;
            }
            return null;
          }
          if (Validators.minLength(args)(controls)) {
            return {
              minLength: (label: string) =>
                Handlebars.compile(
                  text ??
                    '{{ label }} must contain at least {{ args }} characters.',
                )({
                  label: label,
                  args: args,
                }),
            } as ValidationErrors;
          } else return null;
        };
      case 'maxLength':
        return (controls: AbstractControl) => {
          const check =
            this.fieldTypeChecked.includes(fieldType) &&
            +controls.value?.default?.length > args
              ? true
              : false;
          if (check || Validators.maxLength(args)(controls)) {
            return {
              maxLength: (label: string) =>
                Handlebars.compile(
                  text ?? '{{label }} must not exceed {{args}} characters.',
                )({
                  label: label,
                  args: args,
                }),
            } as ValidationErrors;
          } else return null;
        };
      case 'pattern':
        return (controls: AbstractControl) => {
          if (Validators.pattern(args)(controls)) {
            return {
              pattern: (label: string) =>
                Handlebars.compile(
                  text ?? '{{ label }} is in the wrong format.',
                )({
                  label: label,
                }),
            } as ValidationErrors;
          } else return null;
        };
      case 'min':
        return (controls: AbstractControl) => {
          if (Validators.min(args)(controls)) {
            return {
              min: (label: string) =>
                Handlebars.compile(text ?? '{{ label }} cannot be empty.')({
                  label: label,
                }),
            } as ValidationErrors;
          } else return null;
        };
      case 'max':
        return (controls: AbstractControl) => {
          if (Validators.max(args)(controls)) {
            return {
              max: (label: string) =>
                Handlebars.compile(text ?? '{{ label }} cannot be empty.')({
                  label: label,
                }),
            } as ValidationErrors;
          } else return null;
        };
      case 'ppx-maxDuration':
      case 'ppx-null':
      default:
        return Validators.nullValidator;
    }
  }
  parseValidator(validator: FieldValidator, type: string): ValidatorFn {
    return this.ValidatorDefaultCustom(
      validator.type,
      validator.args,
      type,
      validator.text,
    );
  }
  parseAsyncValidator(
    validator: FieldValidator,
    values: () => Values,
  ): AsyncValidatorFn {
    switch (validator.type) {
      case 'ppx-custom':
        return this.customValidators(validator.args, values, validator.text);
      default:
        return () => of(null);
    }
  }

  parseValidators(
    validators: FieldControlConfig['validators'],
    values: () => Values,
    type: string,
  ) {
    return {
      validators: (validators || [])
        .filter((validator) => validator.type !== 'ppx-custom')
        .map((validator) => {
          return this.parseValidator(validator, type);
        }),
      asyncValidators: (validators || [])
        .filter((validator) => validator.type === 'ppx-custom')
        .map((validator) => {
          return this.parseAsyncValidator(validator, values);
        }),
    };
  }

  private base64toBlob(base64Data: string, contentType: string): Blob {
    contentType = contentType || '';
    const sliceSize = 1024;
    const byteCharacters = Buffer.from(base64Data, 'base64').toString('latin1');
    const bytesLength = byteCharacters.length;
    const slicesCount = Math.ceil(bytesLength / sliceSize);
    const byteArrays = new Array(slicesCount);

    for (let sliceIndex = 0; sliceIndex < slicesCount; ++sliceIndex) {
      const begin = sliceIndex * sliceSize;
      const end = Math.min(begin + sliceSize, bytesLength);

      const bytes = new Array(end - begin);
      for (let offset = begin, i = 0; offset < end; ++i, ++offset) {
        bytes[i] = byteCharacters[offset].charCodeAt(0);
      }
      byteArrays[sliceIndex] = new Uint8Array(bytes);
    }
    return new Blob(byteArrays, { type: contentType });
  }

  uploadFile(
    file: FileType,
    option?: { uri?: string; bufferName?: string },
  ): Observable<{ extension?: string; name?: string; url?: string }> {
    const formData = new FormData();
    if (!file.data || !file.type) return of({});
    const tmp = file.data.replace('data:' + file.type + ';base64,', '');
    const data = this.base64toBlob(tmp, file.type);
    formData.append(option?.bufferName ?? 'file', data, file.name);

    return this.http
      .post<
        { extension?: string; name?: string; url?: string }[]
      >(option?.uri ?? 'api/file-manager', formData)
      .pipe(
        catchError(() => of([])),
        map((data) => data[0]),
      );
  }

  clearEmptyField(object: NzSafeAny): NzSafeAny {
    if (isArray(object)) {
      let test = object.map((it) => this.clearEmptyField(it));
      test = test.filter((it) => {
        if (it && !isEmpty(it)) return true;
        return false;
      });
      if (test.length === 0) return undefined;
      return test;
    }
    if (typeof object === 'object') {
      const newObject: NzSafeAny = {};
      if (isEmpty(object)) return undefined;
      Object.keys(object).map((key) => {
        const check = this.clearEmptyField(object[key]);
        if (check) {
          newObject[key] = check;
        }
      });
      if (isEmpty(newObject)) return undefined;
      return newObject;
    }
    return object;
  }

  downloadFileById(url: string, id: string) {
    return this.downloadFile(`${url}/${id}`);
  }

  downloadFile(url: string, options?: { headers?: Record<string, string> }) {
    return this.http
      .get(`${url}`, {
        responseType: 'blob',
        observe: 'response',
        headers: options?.['headers'],
      })
      .pipe(
        tap((res: NzSafeAny) => {
          this.getFilenameFromHeader(res);
        }),
      );
  }

  downloadAttachedFile(url: string, filter: NzSafeAny[], fileName: string) {
    const qb = RequestQueryBuilder.create();
    qb.setFilter(filter);
    const queryString = qb.query();
    return this.http
      .get(`${url}?${queryString}`, {
        responseType: 'blob',
        observe: 'response',
      })
      .pipe(
        tap((res: NzSafeAny) => {
          this.getFilenameFromHeader(res, fileName);
        }),
      );
  }

  getFilenameFromHeader(res: NzSafeAny, fileName?: string, type?: number) {
    //Get filename from Content-Disposition header
    const header = res.headers.get('Content-Disposition');
    // const filenameRegex = /filename[^;=\n]*=(([‘"]).*?\2|[^;\n]*)/;
    // const matches = filenameRegex.exec(header);
    // let filename = fileName + '.xlsx';
    // if (matches != null && matches[1]) {
    //   filename = matches[1].replace(/['"]/g, '');
    // }

    // Check both `filename*=` and `filename=` in order of priority
    const filenameStarRegex = /filename\*=(([‘"]).*?\2|[^;\n]*)/;
    const filenameRegex = /filename[^;=\n]*=(([‘"]).*?\2|[^;\n]*)/;
    let filename = fileName + '.xlsx';
    let matches;

    // Prioritize `filename*=`
    if ((matches = filenameStarRegex.exec(header)) !== null) {
      let extractedFilename = matches[1].trim();

      // Check if `filename*=` has the format `UTF-8''encoded-filename` and decode
      if (extractedFilename.startsWith("UTF-8''")) {
        extractedFilename = decodeURIComponent(
          extractedFilename.replace("UTF-8''", ''),
        );
      }

      filename = extractedFilename;
    } else if ((matches = filenameRegex.exec(header)) !== null) {
      // If `filename*=` is not found, check for `filename=`
      filename = matches[1].trim().replace(/['"]/g, ''); // Remove surrounding quotes if present
    }
    const url = window.URL.createObjectURL(res.body);
    const a = document.createElement('a');
    document.body.appendChild(a);
    a.href = url;
    a.download = filename;
    a.click();
    window.URL.revokeObjectURL(url);
    a.remove();
  }

  host = '';
  exportFileByGet(
    url: string,
    page = 0,
    pageSize = 0,
    filter: QueryFilter[] = [],
    search = '',
    sortOrder: Record<string, string | null> = {},
    customUrl?: string,
  ) {
    const qb = RequestQueryBuilder.create();
    const filterSearch = search
      ? [{ field: 'search', operator: '$eq', value: search } as QueryFilter]
      : [];
    qb.setPage(page);
    qb.setLimit(pageSize);
    const newFilter = this.transferFilterToSearch([...filter, ...filterSearch]);
    qb.search({ $and: newFilter });

    // qb.setFilter([...filter, ...filterSearch]);
    const sortQuery = Object.keys(sortOrder)
      .filter((key) => sortOrder[key])
      .map((key) => ({
        field: key,
        order:
          sortOrder[key] === 'ascend' ? 'ASC' : ('DESC' as QuerySortOperator),
      }));
    qb.sortBy(sortQuery);
    const queryString = qb.query();
    const _url = customUrl ?? url + '/export';

    return this.http
      .get(`${this.host}${_url}?${queryString}`, {
        responseType: 'blob',
        observe: 'response',
      })
      .pipe(
        tap((res) => {
          this.getFilenameFromHeader(res);
        }),
      );
  }

  // Add cache as class property
  private pathCache = new Map<string, string[][]>();

  // optimize distinctValue
  distinctValue(prev: Values, curr: Values, source: SourceField | undefined) {
    // Early exit if no source
    if (!source) return true;

    // Helper function for path extraction with caching
    const getPathsFromTransform = (transform: string) => {
      const cacheKey = transform;
      if (this.pathCache.has(cacheKey)) {
        return this.pathCache.get(cacheKey)!;
      }

      const regex = /\$((\.\w*)|(\[[0-9]*\]))+/g;
      const paths = (transform.match(regex) ?? []).map((text) =>
        text.split(/[.[\]]+/).slice(1),
      );

      this.pathCache.set(cacheKey, paths);
      return paths;
    };

    // Optimized value comparison
    const areValuesEqual = (prev: any, curr: any): boolean => {
      // Fast equality checks first
      if (prev === curr) return true;
      if (!prev !== !curr) return false;

      // Handle null/undefined
      if (isNil(prev) && isNil(curr)) return true;

      // Only do deep comparison if necessary
      if (typeof prev === 'object' && typeof curr === 'object') {
        if (Array.isArray(prev) !== Array.isArray(curr)) return false;
        return JSON.stringify(prev) === JSON.stringify(curr);
      }

      return false;
    };

    // Handle dependants case
    if (source.dependants?.length) {
      if (source.params) {
        const resParams = Object.keys(source.params).map((key) => {
          const path = source
            .params![key].split(/[.[\]]+/)
            .slice(1)
            .filter((it) => it !== '');
          return {
            key,
            value: this.getValue(curr, path),
          };
        });

        const dependants = source.dependants.map((it) =>
          resParams.reduce(
            (res, param) =>
              res.replace(new RegExp(`\\${param.key}`, 'g'), param.value),
            it,
          ),
        );

        return dependants.every((dependant) => {
          const path = dependant
            .split(/[.[\]]+/)
            .slice(1)
            .filter((it) => it !== '');
          const prevValue = this.getValue(prev, path);
          const currValue = this.getValue(curr, path);
          return areValuesEqual(prevValue, currValue);
        });
      }
    }

    // Handle transform case
    const paths = getPathsFromTransform(source.transform ?? '');
    return paths.every((path) => {
      const prevValue = this.getValue(prev, path);
      const currValue = this.getValue(curr, path);
      return areValuesEqual(prevValue, currValue);
    });
  }
}
