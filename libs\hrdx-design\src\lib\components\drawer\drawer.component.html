<nz-drawer
  [(nzVisible)]="visible"
  (nzVisibleChange)="onVisibleChange()"
  [nzPlacement]="placement()!"
  [nzClosable]="false"
  [nzWidth]="Size[width()] ?? width()"
  [nzTitle]="defaultDrawerHeaderTemplate"
  [nzFooter]="footer()"
  [nzWrapClassName]="wrapClassNameComputed()"
  [nzMaskClosable]="maskClosable()"
  (nzOnClose)="onCancel()"
  [nzMask]="maskVisible()"
  [nzMaskStyle]="getMaskStyle()"
  #drawer
>
  <ng-container *nzDrawerContent>
    <ng-content></ng-content>
  </ng-container>
</nz-drawer>

<ng-template #defaultDrawerHeaderTemplate>
  <div class="custom-drawer__header" #header>
    @if (isStringField(title())) {
      <span class="custom-drawer__header-title">{{ title() }}</span>
    } @else {
      <ng-container [ngTemplateOutlet]="getTemplateRef(title())"></ng-container>
    }

    <div class="header-right">
      <div class="custom-drawer__header-label" *ngIf="label()">
        @if (isStringField(label())) {
          {{ label() }}
        } @else {
          <ng-container
            [ngTemplateOutlet]="getTemplateRef(label())"
          ></ng-container>
        }
      </div>
      <div class="custom-drawer__header-close" *ngIf="closable()">
        <hrdx-button
          icon="icon-x-bold"
          [onlyIcon]="true"
          type="ghost-gray"
          (clicked)="onCancel()"
          size="default"
        >
        </hrdx-button>
      </div>
    </div>
  </div>
</ng-template>
