@import '../../../themes/tokens.less';

::ng-deep .custom-drawer {
  max-width: 100%;

  &:not(.drawer-fixed) {
    &.ant-drawer-content-wrapper {
      top: @spacing-12;
    }
  }

  &.padding-less {
    .ant-drawer-content>.ant-drawer-wrapper-body>.ant-drawer-body {
      padding: @size-0;
    }
  }

  &__header {
    display: flex;
    align-items: center;

    &-title {
      color: @color-text-primary;
      font-weight: @font-weight-semibold;
      font-size: @font-size-large;
    }

    .header-right {
      margin-left: auto;
      display: flex;
      gap: @size-12;
      justify-content: flex-end;
    }

    &-label {
      font-size: @font-size-base;
      color: @color-text-secondary;
    }
  }

  .ant-drawer-header,
  .ant-drawer-footer {
    border-color: @color-border-secondary;
    height: @size-72;
    box-sizing: border-box;
  }

  .dialog--footer {
    &:has(.reset-btn:empty):has(.list-btn:empty) {
      display: none;
    }
  }

  .ant-drawer-footer {
    &:has(.reset-btn:empty):has(.list-btn:empty) {
      display: none;
    }
  }

  .ant-drawer-footer:empty {
    display: none;
  }
}

::ng-deep .drawer-view {
  .ant-drawer-body {
    background-color: @color-bg-surface-secondary;
  }
}

::ng-deep .ant-drawer-mask {
  background: @color-base-opacity-overlay;
}

::ng-deep {
  dynamic-mark-scroll {
    height: 100%;
  }
}