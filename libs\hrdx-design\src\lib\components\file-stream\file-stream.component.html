<!-- <div style="text-align: center;">
  <h2>Excel Data Loaded from Stream</h2>
</div>

<hr />

<h3>Excel Data:</h3>
<table *ngIf="data.length > 0" border="1" style="margin: auto;">
  <tr *ngFor="let row of data">
    <td *ngFor="let cell of row">
      {{ cell }}
    </td>
  </tr>
</table> -->

<!-- should set height for wrapper to avoid height is added more 6px -->
<div *ngIf="pdfDataSource" class="file-stream" [style.height.px]="windowHeight - 291">
  <ng2-pdfjs-viewer
    [pdfSrc]="pdfDataSource"
    [print]="false"
    [download]="false"
    [useOnlyCssZoom]="false"
    [fullScreen]="false"
    [openFile]="false"
    [viewBookmark]="false"
    [showSpinner]="false"
    [viewerId]="'fileStreamViewer'"
    style="width: 100%; height: 100%"
    [fullScreen]="false"
  ></ng2-pdfjs-viewer>
</div>
<div *ngIf="!pdfDataSource" style="text-align: center; padding-top: 50px">
  <!-- Optional: Show a loading message or placeholder -->
  <p>Loading PDF data...</p>
</div>
