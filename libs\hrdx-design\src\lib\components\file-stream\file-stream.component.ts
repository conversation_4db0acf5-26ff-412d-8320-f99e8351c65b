import { CommonModule } from '@angular/common';
import {
  AfterViewInit,
  Component,
  HostListener,
  input,
  viewChild,
} from '@angular/core';
import { NzSafeAny } from 'ng-zorro-antd/core/types';
import { PdfJsViewerComponent, PdfJsViewerModule } from 'ng2-pdfjs-viewer';

@Component({
  selector: 'hrdx-file-stream',
  standalone: true,
  imports: [CommonModule, PdfJsViewerModule],
  templateUrl: './file-stream.component.html',
  styleUrl: './file-stream.component.less',
})
export class FileStreamComponent implements AfterViewInit {
  streamData = input.required<NzSafeAny>();
  pdfDataSource: Blob | undefined;

  windowHeight = window.innerHeight;

  @HostListener('window:resize')
  onResize() {
    this.windowHeight = window.innerHeight;
  }

  ngAfterViewInit(): void {
    this.loadPdf();
    this.windowHeight = window.innerHeight;
  }

  loadPdf(): void {
    const rawData = this.streamData();
    if (rawData) {
      this.pdfDataSource = new Blob([rawData], { type: 'application/pdf' });
    } else {
      this.pdfDataSource = undefined;
    }
  }
}
