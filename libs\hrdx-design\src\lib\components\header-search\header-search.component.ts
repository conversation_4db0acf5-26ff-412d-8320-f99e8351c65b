import { AccountInfo } from '@azure/msal-browser';
import {
  Component,
  ElementRef,
  HostListener,
  inject,
  input,
  output,
  ViewChild,
  OnInit,
  computed,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { NzGridModule } from 'ng-zorro-antd/grid';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { FormsModule } from '@angular/forms';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { ButtonComponent } from '../button';
import { NzAvatarModule } from 'ng-zorro-antd/avatar';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { InputComponent, InputFieldType } from '../input';
import { IconComponent } from '../icon';
import {
  AvatarBadgeDotColor,
  AvatarBadgeType,
  AvatarComponent,
  AvatarShape,
  AvatarSize,
  AvatarType,
} from '../avatar';
// eslint-disable-next-line @nx/enforce-module-boundaries

@Component({
  selector: 'hrdx-header-search',
  standalone: true,
  imports: [
    CommonModule,
    NzGridModule,
    NzInputModule,
    NzSelectModule,
    FormsModule,
    NzIconModule,
    NzAvatarModule,
    ButtonComponent,
    NzDropDownModule,
    InputComponent,
    IconComponent,
    AvatarComponent,
    NzDropDownModule,
    IconComponent,
  ],
  templateUrl: './header-search.component.html',
  styleUrl: './header-search.component.less',
})
export class HeaderSearchComponent {
  avatarSize = AvatarSize;
  avatarShape = AvatarShape;
  avatarType = AvatarType;
  avatarBadgeType = AvatarBadgeType;
  avatarBadgeColor = AvatarBadgeDotColor;
  // #userStore = inject(UserStore);
  // _service = inject(BffService);

  inputValue = 'my site';

  isShow = false;
  InputFieldType = InputFieldType;
  placeHolder = 'Enter keywords to search...';
  value = '';
  count = 0;

  userActionClicked = output<string>();
  account = input<AccountInfo | null>(null);
  userInfo = input<UserState | undefined>(undefined);
  avatarLink = input<string>('');

  userActions = [
    {
      id: 'password',
      title: 'Password',
      icon: 'icon-password',
    },
    {
      id: 'language',
      title: 'Language',
      icon: 'icon-translate',
    },
    {
      id: 'logout',
      title: 'Logout',
      icon: 'icon-arrow-line-right',
    },
  ];

  filterItems = [
    { label: 'Mails', icon: 'envelope', active: true },
    { label: 'People', icon: 'user', active: false },
    { label: 'Products', icon: 'box', active: true },
    { label: 'Attachment', icon: 'paperclip', active: false },
  ];

  expandSections = [
    {
      title: 'Recent searched',
      items: [
        { label: 'hungld22', icon: 'clock-rotate-left' },
        { label: 'hungpq43', icon: 'clock-rotate-left' },
        { label: 'anhnd', icon: 'clock-rotate-left' },
      ],
    },
  ];
  @ViewChild('searchBarExpand') searchBarExpand!: ElementRef;
  @HostListener('document:click', ['$event'])
  profileData = computed(() => {
    const data = this.userInfo();
    return {
      name: data?.fullName,
      email: data?.email,
      employeeId: data?.employeeCode,
      userName: data?.userName,
    };
  });

  // avatarLink = computed<string>(() => {
  //   const data = this.userInfo();
  //   if (data && data?.avatarFile) {
  //     return `/api/personals/${data?.employeeCode}/basic-infomation/avatar/${data?.avatarFile}`;
  //   }
  //   return '';
  // });

  onDocumentClick(event: MouseEvent) {
    const clickedInside = this.searchBarExpand
      ? this.searchBarExpand.nativeElement.contains(event.target)
      : false;
    if (this.searchBarExpand) {
      this.count++;
      if (this.count > 1) {
        this.isShow = clickedInside;
      }
    }
    if (!this.isShow) {
      this.count = 0;
    }
  }

  showSearchBar() {
    this.isShow = !this.isShow;
  }

  handledFilterBy(label: string) {
    console.log('fitler by', label);
  }

  selectChanged(value: string | string[]) {
    console.log('select changed', value);
  }
}

interface UserState {
  avatarFile?: string;
  userId?: string;
  userName?: string;
  email?: string;
  firstName?: string;
  lastName?: string;
  fullName?: string;
  companyCode?: string;
  requestingLanguageCode?: string;
  employeeCode?: string;
  departmentCode?: string;
  divisionCode?: string;
  businessTitleCode?: string;
  legalEntityCode?: string;
}
