<nz-input-group
  [nzSuffix]="suffix()"
  [nzPrefix]="prefix()"
  [ngClass]="{ borderless: borderless(), 'hrdx-input-currency': true }"
  [ngStyle]="customStyle()"
>
  <input
    [ngModel]="value"
    type="text"
    nz-input
    placeholder="{{ placeholder() }}"
    [disabled]="disabled"
    (ngModelChange)="onChangeValue($event)"
    [nzBorderless]="borderless()"
    (focus)="markAsTouched()"
    (keydown)="keydown($event)"
    (focusout)="updateValue()"
    (paste)="onPasteEvent($event)"
    #inputModel="ngModel"
  />
</nz-input-group>
