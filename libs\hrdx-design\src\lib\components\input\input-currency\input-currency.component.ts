import { Component, input, viewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NzInputModule } from 'ng-zorro-antd/input';
import { InputCurrency } from './input-currency.model';
import {
  ControlValueAccessor,
  FormsModule,
  NG_VALUE_ACCESSOR,
  NgModel,
} from '@angular/forms';
import { formatCurrency } from '../../../shared';
import { isNil } from 'lodash';
import * as _ from 'lodash';

@Component({
  selector: 'hrdx-input-currency',
  standalone: true,
  imports: [CommonModule, NzInputModule, FormsModule],
  templateUrl: './input-currency.component.html',
  styleUrl: './input-currency.component.less',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      multi: true,
      useExisting: InputCurrencyComponent,
    },
  ],
})
export class InputCurrencyComponent implements ControlValueAccessor {
  precision = input<InputCurrency['precision']>();
  prefix = input<InputCurrency['prefix']>();
  suffix = input<InputCurrency['suffix']>();
  placeholder = input<InputCurrency['placeholder']>('Enter amount');
  borderless = input<InputCurrency['borderless']>(false);
  outputType = input<InputCurrency['outputType']>('number');
  customStyle = input<InputCurrency['customStyle']>();
  max = input<InputCurrency['max']>();
  min = input<InputCurrency['min']>();
  value?: string;
  inputModel = viewChild<NgModel>('inputModel');

  onChange = (value: any) => value;
  onTouched = () => false;
  touched = false;
  disabled = false;

  writeValue(value: string | number) {
    if (isNil(value)) {
      this.value = value;
      return;
    }
    if (typeof value === 'number') value = value.toString();
    this.value = this.getFormattedValue(value);
  }

  registerOnChange(onChange: any) {
    this.onChange = onChange;
  }

  registerOnTouched(onTouched: any) {
    this.onTouched = onTouched;
  }

  markAsTouched() {
    if (!this.touched) {
      this.onTouched();
      this.touched = true;
    }
  }

  setDisabledState(disabled: boolean) {
    this.disabled = disabled;
  }

  onChangeValue(value: any) {
    const parsedValue = this.getParsedValue(value);
    const formattedValue = this.getFormattedValue(parsedValue.toString());
    this.value = formattedValue;
  }

  updateValue() {
    const value = this.value;
    if (isNil(value)) {
      this.onChange(value);
      return;
    }

    let parsedValue: string | number | null = this.getParsedValue(value);

    const numberValue = +parsedValue;
    const max = this.max();
    const min = this.min();
    if (!isNil(max) && numberValue > max) parsedValue = max;
    if (!isNil(min) && numberValue < min) parsedValue = min;

    const formattedValue = this.getFormattedValue(parsedValue.toString());
    this.value = formattedValue;

    // set value for control to force update viewModel when value is 0
    const inputModel = this.inputModel();
    if(this.value === '0' && inputModel) {
      inputModel.control.setValue('0');
    }

    if (parsedValue.toString().trim() === '') parsedValue = null;
    if (this.outputType() === 'number' && !isNil(parsedValue)) {
      try {
        parsedValue = +parsedValue;
      } catch (error) {
        console.error(error);
      }
    }

    this.onChange(parsedValue);
  }

  private getParsedValue(value: string) {
    const leadingZeroReg = /^0+(?=\d)/;
    const val = value
      .replace(/^0{2,}/, '0')
      .replace(leadingZeroReg, '')
      ?.replace(/\$\s?|(,*)/g, '');

    return val;
  }

  private getFormattedValue(value: string) {
    return formatCurrency(value);
  }

  onPasteEvent(event: ClipboardEvent) {
    const clipboardValue = event.clipboardData?.getData('Text');
    if (_.isNil(clipboardValue) || _.isNaN(Number(clipboardValue))) {
      event.preventDefault();
    }
  }

  readonly allowKeys: string[] = [
    'Backspace',
    'Delete',
    'Tab',
    'Enter',
    'ArrowLeft',
    'ArrowRight',
  ];
  keydown($event: KeyboardEvent) {
    const key = $event.key;
    const regex = /^[\d.,-]+$/;

    const ctrlOrMeta = $event.ctrlKey || $event.metaKey;

    if (ctrlOrMeta) return;

    if (this.allowKeys.includes(key)) return;
    const precision = this.precision();

    if (!regex.test($event.key) || (precision === 0 && key === '.')) {
      $event.preventDefault();
      return;
    }

    if (precision && precision > 0) {
      const currentValue = ($event.target as HTMLInputElement)?.value;
      const [integer, fractional = ''] = currentValue.toString().split('.');
      if (fractional.length < precision) return;
      $event.preventDefault();
    }
  }
}
