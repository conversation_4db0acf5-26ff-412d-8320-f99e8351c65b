import { NgStyleInterface } from "ng-zorro-antd/core/types";
import { z } from "zod";

const inputCurrencySchema = z.object({
  precision: z.onumber(),
  prefix: z.ostring(),
  suffix: z.ostring(),
  placeholder: z.ostring(),
  borderless: z.boolean(),
  max: z.onumber(),
  min: z.onumber(),
  outputType: z.enum(['number', 'string']).default('number').optional(),
  customStyle: z.custom<NgStyleInterface>().optional(),
});

type InputCurrency = z.infer<typeof inputCurrencySchema>;

export { inputCurrencySchema, InputCurrency };
