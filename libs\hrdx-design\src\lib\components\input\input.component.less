@import '../../../themes/tokens.less';

::ng-deep .suggestion-list-popover {
  .ant-popover-arrow {
    display: none;
  }
  .ant-popover-inner-content {
    padding: @spacing-2;
    padding-right: @spacing-1;
    width: 100%;
  }

  .searching-text {
    word-break: break-all;
    white-space: normal;
  }
  ul.suggestion-list {
    list-style-type: none;
    padding: @spacing-0;
    max-height: 200px;
    overflow-y: auto;
    overflow-x: hidden;
    padding-right: @spacing-1;
    margin: @spacing-0;
    li {
      word-break: break-all;
      white-space: normal;
      padding: @spacing-2 @spacing-3;
      border-radius: @border-radius-base;
      transition: all 0.2s;
      &:not(.skeleton):hover {
        background-color: @color-bg-surface-hover;
      }

      & + li {
        margin-top: @spacing-1;
      }
    }
  }
}

input,
nz-input-group,
textarea {
  font-size: @font-size-base;
  line-height: @font-line-height-medium;
  font-weight: @font-weight-regular;

  // padding: @size-8 @size-12 @size-8 @size-12;
  // gap: @size-4;
  border-radius: @size-8;

  &.disabled {
    pointer-events: none;
  }
}

::ng-deep .button-icon-right,
.group-button-right {
  display: flex;
  justify-content: space-around;

  input {
    margin: @size-0 @size-4 @size-0 @size-0;
  }
}

::ng-deep .ng-untouched {
  .disabled .field-config__content {
    .textarea-wrapper {
      background: var(--neutral-grey-5);
    }
  }
}

::ng-deep .textarea-wrapper {
  --min-height: 88px;
  --textarea-count-height: @size-16;
  --textarea-count-bottom: @spacing-2;
  resize: vertical;
  min-height: var(--min-height);
  overflow-y: auto;
  border: @size-1 solid @color-border-primary;
  border-radius: @border-radius-input;

  &.error {
    border-color: @color-border-destructive;
  }

  &:focus-within {
    border-color: @color-border-active;
    box-shadow: @elevation-input_focus;
  }

  &.ant-input-textarea-show-count {
    &::after {
      position: absolute;
      right: @spacing-5;
      bottom: var(--textarea-count-bottom);
      color: @color-text-hint;
      // background-color: @color-bg-surface;
      width: 98%;
      text-align: right;
      height: var(--textarea-count-height);
    }
  }

  .ant-input {
    height: calc(
      var(--min-height) - var(--textarea-count-height) -
        var(--textarea-count-bottom)
    );
    line-height: @size-20;
    padding-right: @spacing-5;
    min-height: calc(
      100% - var(--textarea-count-height) - var(--textarea-count-bottom)
    );
    resize: none;
    &:focus {
      box-shadow: unset !important;
    }
    @slick-scrollbar();
  }
}

.mr-8 {
  margin-right: @size-4;
}

.dropdown-right,
.dropdown-left {
  .ant-input-group {
    display: flex;
    align-items: center;

    input {
      width: 70%;
    }

    hrdx-select {
      width: 30%;
    }
  }
}

.input-unit {
  .ant-input-group {
    display: flex;
    align-items: center;

    .text-unit {
      border-left: @size-1 solid @color-border-secondary;
      text-align: center;
      padding-left: @size-12;
      color: @color-text-primary;
      font-size: @size-16;
      font-weight: @font-weight-regular;
      line-height: @size-20;
    }
  }
}

:host {
  .hrdx-input-group {
    padding: @spacing-basis @spacing-2;

    .affix-icon {
      font-size: @font-size-x-large;
      color: @color-icon-secondary;
      &::before {
        padding-left: 5px;
      }
    }

    &.border-less {
      border-width: @size-0;
      padding: @size-0;

      &:focus-within {
        box-shadow: unset;
      }
    }

    &:focus-within {
      border-color: @color-border-active;
      box-shadow: @elevation-input_focus;
    }

    .char-count {
      color: @color-char-count;
      font-size: @font-size-small;
    }
  }

  .ant-input:not(.ant-input-borderless) {
    &:focus {
      border-color: @color-border-active;
      // box-shadow: @elevation-input_focus;
    }
  }

  ::ng-deep .hrdx-input-number {
    .ant-input-number {
      width: 100%;
      border-radius: @size-8;
      padding: @spacing-1 @spacing-0;

      &.ant-input-number-focused {
        border-color: @color-border-active;
        box-shadow: @elevation-input_focus;
      }
    }
    .ant-input-number-handler-wrap {
      display: none;
    }
  }
}
