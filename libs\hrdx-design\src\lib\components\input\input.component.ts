import {
  AfterViewInit,
  Component,
  computed,
  ElementRef,
  inject,
  input,
  ModelSignal,
  OnChanges,
  OnD<PERSON>roy,
  output,
  OutputEmitterRef,
  Renderer2,
  signal,
  TemplateRef,
  viewChild,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  InputAffix,
  InputAffixConfig,
  InputField,
  InputFieldType,
  InputSuggestion,
} from './input.model';
import {
  ControlValueAccessor,
  FormsModule,
  NG_VALUE_ACCESSOR,
} from '@angular/forms';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzInputModule } from 'ng-zorro-antd/input';
import { SelectComponent, SelectMode } from '../select';
import { ButtonComponent } from '../button';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { ControlComponent } from '../control/control.component';
import { IconComponent } from '../icon';
import { isNil, trim } from 'lodash';
import {
  NzInputNumberComponent,
  NzInputNumberModule,
} from 'ng-zorro-antd/input-number';
import {
  BehaviorSubject,
  catchError,
  debounceTime,
  distinctUntilChanged,
  filter,
  of,
  Subject,
  Subscription,
  takeUntil,
  tap,
} from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { QueryFilter, RequestQueryBuilder } from '@nestjsx/crud-request';
import { NzMentionModule } from 'ng-zorro-antd/mention';
import { NzSkeletonModule } from 'ng-zorro-antd/skeleton';
import * as Handlebars from 'handlebars';
import { NzPopoverModule } from 'ng-zorro-antd/popover';

@Component({
  selector: 'hrdx-input',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    NzIconModule,
    NzInputModule,
    NzInputNumberModule,
    SelectComponent,
    ButtonComponent,
    NzSelectModule,
    IconComponent,
    NzMentionModule,
    NzSkeletonModule,
    NzPopoverModule,
  ],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      multi: true,
      useExisting: InputComponent,
    },
  ],
  templateUrl: './input.component.html',
  styleUrl: './input.component.less',
})
export class InputComponent
  extends ControlComponent
  implements OnChanges, ControlValueAccessor, AfterViewInit, OnDestroy
{
  override value!: ModelSignal<InputField['value']>;
  override valueChange!: OutputEmitterRef<InputField['value']>;
  InputFieldType = InputFieldType;
  SelectMode = SelectMode;
  passwordVisible = false;
  field = input<InputField['field']>(InputFieldType.Default);
  type = input<InputField['type']>('text');
  placeHolder = input<InputField['placeHolder']>('');
  icon = input<InputField['icon']>('user');
  borderLess = input<InputField['borderLess']>(false);
  textUnit = input<InputField['textUnit']>('');
  optionList = input<InputField['optionList']>([]);
  autoSize = input<InputField['autoSize']>({
    minRows: 2,
    maxRows: 4,
  });
  maxCharCount = input<InputField['maxCharCount']>(1000);
  prefix = input<InputField['prefix']>();
  suffix = input<InputField['suffix']>();
  charCountTemplate = viewChild<TemplateRef<void>>('charCountTemplate');
  formatFn = input<InputField['formatFn']>();
  forbiddenKeys = input<InputField['forbiddenKeys']>();
  formatByKeydown = input<InputField['formatByKeydown']>();
  size = input<InputField['size']>('large');
  settings = input<InputField['settings']>();
  inputRef = viewChild<ElementRef<HTMLInputElement>>('inputRef');
  inputNumberRef = viewChild<NzInputNumberComponent>('inputNumberRef');
  textareaRef = viewChild<ElementRef<HTMLTextAreaElement>>('textarea');

  onKeydown?: (e: KeyboardEvent) => void;
  onPaste?: (
    e: ClipboardEvent,
    element: HTMLInputElement | HTMLAreaElement,
  ) => void;
  autoFocus = input<InputField['autoFocus']>(false);

  clickedButton = output<Event>();
  groupButtonLabel = input<InputField['groupButtonLabel'][]>([
    {
      label: 'label',
      type: 'tertiary',
    },
    {
      label: 'label',
      type: 'primary',
    },
  ]);
  readOnly = input<boolean>(false);
  disabledInput = input<boolean>(false);

  clickedGroupButton = output<{ event: Event; label: string }>();
  selectedOption: string | string[] = '';
  changedOption = output<string | string[]>();

  suggestion = input<InputField['suggestion']>();
  private mentionQuery$ = new BehaviorSubject<string | null>(null);
  private destroy$ = new Subject<void>();
  showSuggestionsDropdown = signal(false);
  suggestionList = signal<{ label: string; value: any }[]>([]);
  httpClient = inject(HttpClient);
  renderer = inject(Renderer2);
  private documentClickUnlistener: (() => void) | null = null;
  private suggestionSubscription?: Subscription;

  ngAfterViewInit(): void {
    if (this.autoFocus()) {
      this.focusInput();
    }
    const suggestion = this.suggestion();
    if (suggestion) {
      this.mentionQuery$
        .pipe(
          takeUntil(this.destroy$),
          debounceTime(300),
          // distinctUntilChanged(),
          filter((q) => !isNil(q)),
        )
        .subscribe((searchText) => {
          this.suggestionPageIndex.set(1);
          this.getSuggestionList(searchText, 1);
        });
    }
  }

  isLoadingMoreSuggestions = signal(false);
  suggestionPageIndex = signal(1);
  suggestionSkeletons = new Array(5).fill(null);
  getSuggestionList(searchText: string, page = 1, limit = 20) {
    const suggestion = this.currSelectedSuggestionSetting;
    if (!suggestion) return;
    this.suggestionSubscription?.unsubscribe();
    if (page > 1) {
      this.isLoadingMoreSuggestions.set(true);
    }
    this.suggestionSubscription = this.httpClient
      .get(buildUrl(suggestion.url, { search: searchText, page, limit }))
      .pipe(
        catchError((err) => {
          return of({ items: [] });
        }),
      )
      .subscribe((results: any) => {
        const option = suggestion.option;
        const items = (results?.items ?? results?.data ?? results ?? []).map(
          (item: any) => {
            if (!option) return item;
            return {
              value: item[option.valueField],
              label: transformText(option.label, item),
            };
          },
        );
        this.isLoadingMoreSuggestions.set(false);
        this.searchingSuggestion.set(false);
        this.suggestionList.update((prev) =>
          page > 1 ? [...prev, ...items] : items,
        );
        this.showSuggestionsDropdown.set(this.suggestionList().length > 0);
        if (this.showSuggestionsDropdown()) {
          if (page > 1 && items.length <= 0) {
            this.suggestionPageIndex.set(1);
          }
          this.listenToDocumentClick();
        }
      });
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
    this.suggestionSubscription?.unsubscribe();
    this.removeDocumentClickListener();
  }

  private listenToDocumentClick(): void {
    this.documentClickUnlistener = this.renderer.listen(
      'document',
      'click',
      (event: MouseEvent) => {
        this.showSuggestionsDropdown.set(false);
        this.removeDocumentClickListener();
      },
    );
  }

  private removeDocumentClickListener(): void {
    if (this.documentClickUnlistener) {
      this.documentClickUnlistener();
      this.documentClickUnlistener = null;
    }
  }

  get suggestionDropdownPosition() {
    return {
      left: 0,
      top: this.textareaRef()?.nativeElement?.clientHeight ?? 10,
    };
  }

  focusInput() {
    switch (this.field()) {
      case InputFieldType.Number: {
        this.inputNumberRef()?.inputElement?.nativeElement.focus();
        break;
      }
      case InputFieldType.TextArea: {
        this.textareaRef()?.nativeElement?.focus();
        break;
      }
      default: {
        this.inputRef()?.nativeElement.focus();
      }
    }
  }

  showpasswordVisible() {
    this.passwordVisible = !this.passwordVisible;
  }

  formatter = (value: number): string | number => {
    if (isNil(value)) return '';
    return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  };

  selectChanged(value: string | string[]) {
    this.changedOption.emit(value);
  }

  isInputGroup = computed(() => this.suffix() || this.prefix());

  getTemplate(affix?: InputAffix) {
    if (!affix) return undefined;
    if (typeof affix === 'object') {
      const affixConfig = affix as InputAffixConfig;
      switch (affixConfig.type) {
        case 'char-count': {
          return this.charCountTemplate();
        }
        default: {
          return undefined;
        }
      }
    } else if (typeof affix === 'string') {
      return affix as string;
    } else {
      return affix as TemplateRef<void>;
    }
  }

  private allowedKeys = ['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight'];
  onKeydownTextarea(e: KeyboardEvent) {
    if (this.isAllowedKey(e)) return;
    const maxCharCount = this.maxCharCount() ?? Infinity;
    const value = (e.target as HTMLInputElement)?.value ?? '';
    if (value.length < maxCharCount) return;
    e.preventDefault();
  }

  onInputTextArea(e: Event) {
    const value = (e.target as HTMLInputElement)?.value ?? '';
    this.checkSearchKeyword(value);
  }

  onSuggestionDropdownScroll(e: Event) {
    if (
      !isScrollToBottom(e.target as HTMLElement) ||
      this.isLoadingMoreSuggestions()
    )
      return;
    this.suggestionPageIndex.update((prev) => prev + 1);
    this.getSuggestionList(
      this.mentionQuery$.value ?? '',
      this.suggestionPageIndex(),
    );
  }

  currSelectedSuggestionSetting?: InputSuggestion;
  searchText?: string;
  keywordMatchedPos: [number, number] = [0, 0];
  searchingSuggestion = signal(false);
  private checkSearchKeyword(text: string) {
    let match: RegExpExecArray | null = null;
    const caret =
      this.textareaRef()?.nativeElement.selectionStart ?? text.length;
    const textToCheck = text.slice(0, caret);
    for (const s of this.suggestion() ?? []) {
      const regexp = s.regexp
        ? new RegExp(s.regexp.patternText, s.regexp.flags)
        : new RegExp(`${s.keyword}[a-zA-Z0-9_.-]*$`);
      match = regexp.exec(textToCheck);
      if (match) {
        this.currSelectedSuggestionSetting = s;
        break;
      }
    }
    if (!match || !this.currSelectedSuggestionSetting) {
      this.showSuggestionsDropdown.set(false);
      this.suggestionSubscription?.unsubscribe();
      return;
    }
    this.keywordMatchedPos = [match.index, match.index + match[0].length];
    this.searchText = (
      match[0].slice(this.currSelectedSuggestionSetting.keyword.length) ?? ''
    ).trim();
    if (this.suggestionList.length <= 0) {
      this.searchingSuggestion.set(true);
      this.showSuggestionsDropdown.set(true);
    }
    this.mentionQuery$.next(this.searchText); // Trigger async search
  }

  onSugesstionItemClick(value: string) {
    this.value.update((prev) => {
      const text = prev ?? '';
      return (
        text.slice(0, this.keywordMatchedPos[0]) +
        value +
        text.slice(this.keywordMatchedPos[1] + 1)
      );
    });
    this.onChangeValue(this.value());
    this.suggestionList.set([]);
    const textareaEl = this?.textareaRef()?.nativeElement;
    if (!textareaEl) return;
    const selectionStart = this.keywordMatchedPos[0] + value.length;
    setTimeout(() => {
      if (textareaEl) {
        textareaEl.selectionStart = selectionStart;
        textareaEl.selectionEnd = selectionStart;
        textareaEl.focus();
      }
    }, 1);
  }

  private isAllowedKey(e: KeyboardEvent) {
    return this.allowedKeys.includes(e.key) || e.shiftKey || e.ctrlKey;
  }

  onPasteTextarea(e: ClipboardEvent, element: HTMLTextAreaElement) {
    const clipboardData = this.getClipboardData(e) ?? '';
    const maxCharCount = this.maxCharCount() ?? Infinity;
    const value = this.value() ?? '';
    const valueAfterPaste =
      value.slice(0, element.selectionStart) +
      clipboardData +
      value.slice(element.selectionEnd);
    if (valueAfterPaste.length < maxCharCount) return;
    e.preventDefault();
    const valueAfterSlice = valueAfterPaste.slice(0, maxCharCount);
    this.value.set(valueAfterSlice);
    this.onChangeValue(valueAfterSlice);
  }

  private getClipboardData(e: ClipboardEvent) {
    return e.clipboardData?.getData('Text');
  }

  preChangeValue(value: any) {
    const formatFn = this.formatFn();
    if (formatFn) {
      value = formatFn(value);
    }

    const inputRef = this.inputRef()?.nativeElement;

    if (inputRef) {
      inputRef.value = value;
    }

    // if (value === this.value()) return;

    this.onChangeValue(value);
  }

  onKeydownInput(e: KeyboardEvent) {
    const forbiddenKeys = this.forbiddenKeys() ?? [];
    if (forbiddenKeys.length <= 0 && !this.formatByKeydown()) return;
    if (forbiddenKeys.includes(e.key)) {
      e.preventDefault();
      return;
    }
    if (this.formatByKeydown() && this.formatFn()) {
      const value = (e.target as HTMLInputElement)?.value;
      const valueAfterFormat = this.formatFn()?.(value);
      const inputRef = this.inputRef()?.nativeElement;
      if (inputRef) {
        inputRef.value = valueAfterFormat;
      }
    }
  }

  cleanValue(value: string) {
    if (typeof value !== 'string') return value;

    return (
      value
        // Loại bỏ BOM và các ký tự không in được phổ biến
        .replace(/[\uFEFF\u200B\u200E\u202A-\u202E\u2060]/g, '')
        // Loại bỏ các ký tự xuống dòng và carriage return
        .replace(/[\r\n]/g, '')
        // Loại bỏ khoảng trắng đầu/cuối chuỗi
        .trim()
    );
  }

  onPasteInput(e: ClipboardEvent, element: HTMLInputElement) {
    const forbiddenKeys = this.forbiddenKeys() ?? [];
    const formatFn = this.formatFn();
    if (forbiddenKeys.length <= 0 && !formatFn) return;
    const clipboardData = this.cleanValue(this.getClipboardData(e) ?? '');
    const value = element.value ?? '';
    let valueAfterPaste =
      value.slice(0, element?.selectionStart ?? 0) +
      clipboardData +
      value.slice(element?.selectionEnd ?? 0);

    if (forbiddenKeys.length > 0) {
      valueAfterPaste = valueAfterPaste
        .split('')
        .filter((c) => !forbiddenKeys.includes(c))
        .join('');
    }

    if (formatFn) {
      valueAfterPaste = formatFn(valueAfterPaste);
    }
    e.preventDefault();
    // element.value = valueAfterPaste;
    this.value.set(valueAfterPaste);
    this.onChangeValue(valueAfterPaste);
  }

  readonly inputNumberAllowKeys: string[] = [
    'Backspace',
    'Delete',
    'Tab',
    'Enter',
    'ArrowLeft',
    'ArrowRight',
  ];
  onKeydownNumber($event: KeyboardEvent) {
    const key = $event.key;
    if (this.inputNumberAllowKeys.includes(key)) return;

    const { precision } = this.settings() ?? {};
    if (!/^[\d.,-]+$/.test($event.key) || (precision === 0 && key === '.')) {
      $event.preventDefault();
      return;
    }

    if (precision && precision > 0) {
      const currentValue = ($event.target as HTMLInputElement)?.value;
      const [integer, fractional = ''] = currentValue.toString().split('.');
      if (fractional.length < precision) return;
      $event.preventDefault();
    }
  }
}

function buildUrl(
  url: string,
  {
    search,
    limit,
    page,
  }: { search?: string; limit?: number; page?: number } = {},
) {
  const qb = RequestQueryBuilder.create();
  const filterSearch = search
    ? [
        {
          field: 'search',
          operator: '$eq',
          value: trim(search).replace(/\t/g, ''),
        } as QueryFilter,
      ]
    : [];
  page && qb.setPage(page);
  limit && qb.setLimit(limit);
  qb.search({
    $and: filterSearch.map((f) => ({ [f.field]: { [f.operator]: f.value } })),
  });
  const queryString = qb.query();
  return `${url}?${queryString}`;
}

function isScrollToBottom(el: HTMLElement) {
  return el.scrollHeight - el.scrollTop === el.clientHeight;
}

function transformText(text: string, variables: any) {
  return Handlebars.compile(text)(variables);
}
