@if (contentLoading) {
  <div
    class="hrdx-table"
    [class.fixed-height]="isFixedHeight()"
    [class.grouping-table]="isGroupingTable()"
    [class.fixed-action-column]="fixedActionColumn()"
    [class.has-scroll]="fixedActionColumn() ? isTableHasScroll : false"
    [ngStyle]="styles"
    #table
  >
    <ng-container [ngTemplateOutlet]="headerTemplate"></ng-container>
    <nz-table
      [nzScroll]="{
        x: 'scroll',
        y: _scrollHeight(),
      }"
      [nzFooter]="showFooter() ? footerTemplate : null"
      [nzShowPagination]="false"
      [nzTotal]="total()"
      [nzTableLayout]="'fixed'"
      [nzTemplateMode]="true"
      [nzNoResult]="noResultData"
      cdkDropList
      (cdkDropListDropped)="onDrop($event)"
      cdkDropListOrientation="horizontal"
    >
      <thead>
        <tr>
          @if (loading()) {
            <th *ngFor="let item of COL_LOADING_SKELETON()">
              <nz-skeleton
                [nzActive]="true"
                [nzTitle]="true"
                [nzParagraph]="false"
              ></nz-skeleton>
            </th>
          } @else {
            @if (showCheckbox()) {
              <th
                [nzChecked]="allChecked"
                nzLeft
                nzWidth="60px"
                [nzIndeterminate]="indeterminate"
                (nzCheckedChange)="onAllChecked($event)"
                [rowSpan]="getMaxRowSpan()"
                [nzDisabled]="total() === 0 || data()?.length === 0"
              ></th>
            }
            @if (showRowIndex()) {
              <th nzLeft nzWidth="60px">No</th>
            }
            @if (showCustomExpand()) {
              <th nzLeft nzWidth="60px"></th>
            }

            @for (th of thead()?.listOfTh(); track th; let index = $index) {
              <!-- auto set width of th to 100% when do not have width in th -->
              <th
                class="header-cell"
                [cdkDragDisabled]="isDragable(th)"
                cdkDrag
                [nzAlign]="th.align()"
                [nzWidth]="
                  isGroupingTable()
                    ? null
                    : th.width()
                      ? th.width()?.toString() + 'rem'
                      : '10rem'
                "
                [nzLeft]="th.fixedLeft() ?? false"
                [nzShowSort]="false"
                [colSpan]="th.colSpan()"
                [rowSpan]="th.rowSpan()"
                [ngClass]="{
                  'multi-col-span': th.colSpan() !== 0,
                  'active-column': activeColumnIndex === index,
                  'th-min-width': true,
                }"
                [nzRight]="th.fixedRight()"
                [style.width]="
                  isGroupingTable() ? (th.colSpan() || 1) * 12 + 'rem' : null
                "
                *ngIf="!th.isGrouped()"
              >
                <span
                  class="header-cell-content"
                  [ngStyle]="{ justifyContent: th.align() }"
                >
                  <ng-container [ngTemplateOutlet]="th.content"> </ng-container>

                  <ng-container
                    [ngTemplateOutlet]="actionInThead"
                    [ngTemplateOutletContext]="{ th: th, index: index }"
                  >
                  </ng-container>
                </span>

                <div
                  *cdkDragPreview
                  [ngStyle]="{
                    width: th.width()
                      ? th.width()?.toString() + 'rem'
                      : '10rem',
                  }"
                >
                  <div class="preview-header">
                    {{ th?.title() }}
                    <span
                      nz-icon
                      [nzType]="'icons:ic-sort'"
                      class="sort-icon"
                    ></span>
                  </div>
                </div>
              </th>
            }
            @if (fixedActionColumn()) {
              <th class="th-action" nzRight nzWidth="4rem" nzAlign="center">
                Action
              </th>
            }
          }
        </tr>
        @for (i of rowSpanRange(); track i) {
          <tr *ngIf="!loading()">
            @for (th of groupingColumns(); track th; let index = $index) {
              <th
                [nzAlign]="th.align()"
                [nzLeft]="th.fixedLeft() ?? false"
                [nzShowSort]="th.showSort()"
                [nzSortOrder]="th.sortOrder()"
                (nzSortOrderChange)="th.sortOrderChange.emit($event)"
                [colSpan]="th.colSpan()"
                [rowSpan]="th.rowSpan()"
                [ngClass]="{
                  'th-colspan-child': true,
                  'th-min-width': true,
                }"
              >
                <ng-container [ngTemplateOutlet]="th.content"></ng-container>
              </th>
            }
          </tr>
        }
      </thead>
      <tbody>
        @if (loading()) {
          <tr *ngFor="let item of ROW_LOADING_SKELETON()">
            <td *ngFor="let item of COL_LOADING_SKELETON()">
              <nz-skeleton
                [nzActive]="true"
                [nzTitle]="true"
                [nzParagraph]="false"
              ></nz-skeleton>
            </td>
          </tr>
        } @else {
          @for (tbody of listOfTbody; track tbody; let rowIdx = $index) {
            <tr
              (click)="onRowClick(tbody, rowIdx)"
              [ngClass]="{
                'selected-row': checkedBoxValue(
                  tbody.antiThacSi(),
                  tbody.selected(),
                  tbody,
                  $index
                ),
                'expand-row': tbody.haveExpand(),
                active: !tbody.haveExpand() && rowActive() === rowIdx,
              }"
            >
              <ng-container *ngIf="!tbody.noCheckBox()">
                <td
                  *ngIf="
                    showCheckbox() && !tbody.haveExpand() && !tbody.loading()
                  "
                  [nzChecked]="
                    checkedBoxValue(
                      tbody.antiThacSi(),
                      tbody.selected(),
                      tbody,
                      $index
                    )
                  "
                  [nzDisabled]="tbody.disabled()"
                  nzLeft
                  (nzCheckedChange)="
                    tbody.isGroupSelected()
                      ? tbody.onChechBoxChange.emit($event)
                      : onChecked(
                          $event,
                          haveChildItemCheckbox() ? tbody.idx() : $index
                        )
                  "
                ></td>
                @if (showRowIndex()) {
                  <td nzLeft>{{ getRowIndex(rowIdx) }}</td>
                }
                <td *ngIf="tbody.haveExpand()" nzLeft class="expand">
                  <button [class.open]="tbody.expand()" title="Expand/Collapse">
                    <hrdx-icon icon="icon-caret-down"> </hrdx-icon>
                  </button>
                </td>
              </ng-container>
              @for (
                td of tbody.listOfTd;
                track td;
                let index = $index;
                let last = $last
              ) {
                @if (tbody.loading()) {
                  <td>
                    <nz-skeleton
                      [nzActive]="true"
                      [nzTitle]="true"
                      [nzParagraph]="false"
                    ></nz-skeleton>
                  </td>
                } @else {
                  <td
                    [nzLeft]="
                      thead()?.listOfTh()?.[$index]?.fixedLeft() ?? false
                    "
                    [nzAlign]="thead()?.listOfTh()?.[$index]?.align() ?? 'left'"
                    [nzIndentSize]="td.nzIndentSize()"
                    [nzShowExpand]="td.nzShowExpand()"
                    [nzExpand]="td.nzExpand()"
                    (nzExpandChange)="td.nzExpandChange.emit($event)"
                    [attr.colspan]="td.colSpan()"
                    [class]="td.className()"
                    *ngIf="!thead()?.listOfTh()?.[$index]?.colSpan()"
                    [ngClass]="{
                      'td-drag': isColumnDraging && activeColumnIndex === index,
                      'td-resize':
                        !isColumnDraging && activeColumnIndex === index,
                      'td-last': last,
                    }"
                    [nzRight]="
                      thead()?.listOfTh()?.[$index]?.fixedRight() ?? false
                    "
                  >
                    <ng-container
                      [ngTemplateOutlet]="td.content"
                    ></ng-container>
                    <ng-container *ngIf="last && !fixedActionColumn()">
                      <div
                        class="action"
                        [ngClass]="{
                          'action-active':
                            tbody.isActionVisible() &&
                            isHovered === tbody.tbodyId(),
                          'hide-action': _hideRowAction(),
                        }"
                        *ngIf="!tbody.hiddenAction()"
                        (mouseenter)="onMouseEnter(tbody.tbodyId())"
                      >
                        <ng-container
                          [ngTemplateOutlet]="tbody.actions ?? null"
                        ></ng-container>
                      </div>
                    </ng-container>
                  </td>
                }
              }
              @if (fixedActionColumn()) {
                <td class="td-last td-action" nzRight nzAlign="center">
                  <div class="action-wrapper">
                    <ng-container
                      [ngTemplateOutlet]="tbody.actions ?? null"
                    ></ng-container>
                  </div>
                </td>
              }
            </tr>
          } @empty {
            <!-- <ng-container
              *ngIf="!hiddenNoResult()"
              [ngTemplateOutlet]="noResultData"
            ></ng-container> -->
          }
        }
        @if (loadingMore()) {
          <tr *ngFor="let item of ROW_LOADING_SKELETON()">
            <td *ngFor="let column of COL_LOADING_SKELETON()">
              <nz-skeleton
                [nzActive]="true"
                [nzTitle]="true"
                [nzParagraph]="false"
              ></nz-skeleton>
            </td>
          </tr>
        }
      </tbody>
      <div
        class="height-resizer"
        [class.visible]="isResizingScrollHeight()"
        (mousedown)="onResizeScrollHeight($event)"
        *ngIf="resize()?.height"
      ></div>
    </nz-table>
    <ng-container
      *ngIf="!hiddenNoResult() && !loading() && listOfTbody?.length === 0"
      [ngTemplateOutlet]="noResultData"
    ></ng-container>
  </div>
  <ng-template #actionInThead let-th="th" let-index="index">
    <ng-container *ngIf="th.showSort()">
      <!-- Sort Icon -->
      <span
        nz-icon
        [nzType]="'icons:' + getSortIcon(index)"
        class="sort-icon"
        (click)="onSortOrder(th, index)"
      ></span>
      <!-- <span (click)="onSortOrder(th, index)" class="sort-icon">
        <i class="fa fa-sort-up" [class.active]="isAscending(index)"></i>
        <div class="divider" style="height: 2px"></div>
        <i class="fa fa-sort-down" [class.active]="isDescending(index)"></i>
      </span> -->
    </ng-container>

    <!-- Resize width -->
    <span
      class="resize-handle"
      (mousedown)="onResizeStart($event, index)"
      (mouseenter)="onEnterResize(index)"
      (mouseleave)="onLeaveResize()"
      *ngIf="th.resizable()"
    ></span>

    <!-- Drag drop icon -->
    <div class="drag-icon-wrapper" *ngIf="th.dragable() && !th.fixedLeft()">
      <hrdx-icon
        (mousedown)="onHoldStart(index)"
        (mouseup)="onHoldEnd()"
        icon="icon-dots-six-vertical drag-icon"
        [fontStyle]="'solid'"
      />
    </div>
  </ng-template>
  <ng-template #footerTemplate>
    @if (showPagination()) {
      <div class="pagination">
        <hrdx-pagination
          [total]="total()"
          [pageIndex]="pageIndex()"
          [pageSize]="pageSize()"
          (pageIndexChange)="pageIndexChange.emit($event)"
          (pageSizeChange)="pageSizeChange.emit($event)"
          [pageSizeOptions]="pageSizeOptions()"
          [isShowLength]="isShowLengthPaginaton()"
          [isShowRowPerPage]="isShowRowPerPage()"
        />
      </div>
    } @else {
      <hrdx-button
        [type]="'secondary'"
        [title]="addNewBtnTitle() ?? 'Add New'"
        leftIcon="icon-plus-bold"
        [isLeftIcon]="true"
        (clicked)="createDataTable.emit(true)"
        size="xsmall"
      />
    }
  </ng-template>
  <ng-template #headerTemplate>
    @if (
      indeterminate ||
      allChecked ||
      listOfSelectedItems() ||
      selectedItemsMap.size > 0
    ) {
      <div class="selected-header" *ngIf="showActionHeader()">
        <ng-container *ngIf="listOfSelectedItems(); else default">
          <div class="count-selected">
            {{ listOfSelectedItems() }}
            {{ listOfSelectedItems() > 1 ? 'items' : 'item' }} selected
            <ng-container [ngTemplateOutlet]="unselect" *ngIf="showUnselect()">
            </ng-container>
          </div>
        </ng-container>
        <ng-template #default>
          <div class="count-selected">
            {{ getSelectedItemsText() }}
            <ng-container
              [ngTemplateOutlet]="unselect"
              *ngIf="showUnselect() || this.storeSelectedItems()"
            >
            </ng-container>
          </div>
        </ng-template>

        <ng-template #unselect>
          <span (click)="onUnSelectAll()" class="unselect-text">
            Unselect all
          </span>
        </ng-template>

        <div class="list-action">
          <ng-container
            [ngTemplateOutlet]="selectedActionTemplate"
          ></ng-container>
        </div>

        <hrdx-icon
          name="icon-x-bold"
          fontStyle="solid"
          [size]="'small'"
          (click)="onUnSelectAll()"
          class="close-icon"
        ></hrdx-icon>
      </div>
    }
  </ng-template>
  <ng-template #selectedActionTemplate>
    <ng-content select="[selected-actions]"></ng-content>
  </ng-template>
}
<ng-template #noResultData>
  <div class="no-data">
    <hrdx-illustrations
      [type]="noDataIllustrationConfig().type"
      [size]="noDataIllustrationConfig().size"
      [isSubDescription]="false"
    ></hrdx-illustrations>
    <div class="no-result-title">
      <div class="main">
        @if (isFiltering()) {
          <span>No matching results found</span>
        } @else if (searchValue()) {
          <span>
            No results for
            <span class="highlight">{{ searchValue() }}</span>
          </span>
        } @else {
          <span>Nothing in here yet 👀</span>
        }
      </div>
      <div class="sub">
        <span *ngIf="_noDataSubText() as subText">{{ subText }}</span>
      </div>
    </div>
    <ng-content select="[add-data]"></ng-content>
    <div
      class="no-result-btn"
      *ngIf="searchValue() || isFiltering(); else elseContent"
    >
      <hrdx-button
        [size]="'default'"
        [type]="'tertiary'"
        [title]="'Clear search'"
        [isLeftIcon]="true"
        [leftIcon]="'icon-arrow-clockwise-bold'"
        (click)="clearSearch.emit(true)"
        *ngIf="searchValue()"
      />
      <hrdx-button
        [size]="'default'"
        [type]="'primary'"
        [title]="'Add Filter'"
        [isLeftIcon]="true"
        [leftIcon]="'icon-plus-bold'"
        (click)="addFilter.emit(true)"
        *ngIf="isFiltering() && searchValue()"
      />
    </div>

    <ng-template #elseContent>
      <hrdx-button
        *ngIf="showCreateDataTable()"
        [size]="'default'"
        [type]="'primary'"
        [title]="addNewBtnTitle() ?? 'Add new'"
        [isLeftIcon]="true"
        [leftIcon]="'plus'"
        (click)="createDataTable.emit(true)"
      />
    </ng-template>
  </div>
</ng-template>
