@import url(../../../themes/tokens.less);

@hover-background-tr: var(--hover-background-tr);
@hover-background-tr-start: var(--hover-background-tr-start);

.hrdx-table {
  --hover-background-tr: @color-bg-surface-secondary;
  --hover-background-tr-start: white;
  --selected-header-height: @size-40;

  &.has-scroll {
    ::ng-deep .th-action {
      &::before {
        content: '';
        width: 6px;
        background-color: inherit;
        height: 100%;
        position: absolute;
        right: -6px;
        top: 0px;
      }
    }
  }

  &.fixed-action-column:not(.has-scroll) {
    ::ng-deep .ant-table-header {
      padding-right: unset;
    }
  }

  ::ng-deep
    .ant-checkbox-wrapper.cdk-focused
    .ant-checkbox
    .ant-checkbox-inner {
    box-shadow: none;
  }

  &.grouping-table {
    ::ng-deep .ant-table-tbody > tr > td:not(.td-last) {
      width: 12rem;
    }

    // ::ng-deep .ant-table-header {
    //   thead > tr > th {
    //     width: 12rem;
    //   }
    // }
  }

  ::ng-deep {
    .ant-table-body {
      overflow: auto auto !important;
    }

    .ant-table-ping-left:not(.ant-table-has-fix-left)
      > .ant-table-container::before,
    .ant-table-ping-right:not(.ant-table-has-fix-right)
      > .ant-table-container::after {
      box-shadow: none;
    }
  }

  &.fixed-height {
    &:not(:has(.no-data)) {
      --calc-height: calc(
        var(--height) - var(--footer-height) - var(--header-height)
      );

      ::ng-deep .ant-table-body {
        height: var(--calc-height);
        overflow-y: auto !important;
      }

      &:has(.selected-header) {
        ::ng-deep .ant-table-body {
          height: calc(var(--calc-height) - var(--selected-header-height));
        }
      }
    }

    .no-data {
      height: calc(var(--height) - var(--header-height));
      min-height: unset;
    }
  }

  .selected-header {
    display: flex;
    height: var(--selected-header-height);
    align-items: center;
    padding: 16px;
    color: white;
    gap: 16px;
    animation: slideOut 0.3s;

    .close-icon {
      cursor: pointer;
    }

    > .count-selected {
      font-size: @font-size-base;
      font-weight: 500;
      flex: none;

      // &::after {
      //   content: '|';
      //   margin-left: 4px;
      // }

      .unselect-text {
        color: @color-text-inverse-title;
        font-size: @font-size-base;
        line-height: @font-line-height-base;
        text-decoration: underline;
        font-weight: @font-weight-medium;
        cursor: pointer;
      }
    }

    > .list-action {
      display: flex;
      gap: 8px;
      flex: auto;
      padding-left: @size-16;
      border-left: 1px solid @color-primary-sub;
    }

    > .fa-shape {
      font-size: 16px;
    }

    background-color: @color-bg-surface-info-fill;
  }

  &:has(.selected-header) {
    ::ng-deep .ant-table-body {
      max-height: calc(
        var(--scroll-height) - var(--selected-header-height)
      ) !important;
    }
  }

  border: 1px solid @color-border-primary;
  overflow: hidden;
  border-radius: 8px;

  // ::ng-deep .ant-table-header {
  // overflow-x: auto !important;
  // overflow-y: hidden !important;
  // }
  ::ng-deep .ant-table-header {
    //TODO: set padding right header to avoid th and td misalignment when scrolling horizontally to the end
    @scroll-bar-width: @size-6;
    // TODO: hidden overflow to avoid misalignment when table don't have scrolling horizontally
    overflow: hidden !important;
    padding-right: @scroll-bar-width;
    border-bottom: @size-1 solid @color-border;
    background-color: @color-bg-surface-hover;

    .nz-table-hide-scrollbar {
      overflow-y: auto !important;
      // scrollbar-color: unset !important;
    }
  }

  ::ng-deep.ant-table-thead > tr {
    > th.action {
      display: none;
    }

    > th {
      // border-bottom: @size-1 solid @color-border;
      border-bottom: unset;
      cursor: pointer;
      padding: @size-12 @size-16;
      line-height: @size-12;

      & * {
        line-height: 16px;
      }

      span {
        // line-height: @font-line-height-base;
        font-size: @font-size-base;
        font-weight: @font-weight-semibold;
        color: @color-text-primary;

        &.text-error {
          color: @color-text-error;
        }
      }

      &:first-child {
        nz-table-selection {
          height: 16px;
          line-height: 18px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }

    > th:not(:last-child):not(.ant-table-selection-column):not(
        .ant-table-row-expand-icon-cell
      ):not([colspan])::before {
      width: unset;
    }

    .multi-col-span {
      border: 1px solid @color-border-primary;
      border-top: unset;
    }

    .th-colspan-child {
      &.th-min-width {
        min-width: 100px;
      }
    }

    .th-colspan-child:first-of-type {
      border-left: 1px solid @color-border-primary;
    }

    .th-colspan-child:last-of-type {
      border-right: 1px solid @color-border-primary;
    }
  }

  ::ng-deep.ant-table-tbody > tr {
    cursor: pointer;
    position: relative;
    td {
      border-top: @size-1 solid transparent;
    }
    &.active {
      td {
        @border: @size-1 solid @color-border-active;
        border-top: @border;
        border-bottom: @border;
        box-sizing: border-box;

        &:first-child {
          border-left: @border;
        }

        &:last-child {
          border-right: @border;
        }

        // &::after {
        //   content: '';
        //   position: absolute;
        //   top: 0;
        //   height: 1px;
        //   width: 100%;
        //   background-color: @color-border-active;
        // }
      }
    }
    &.selected-row,
    &.selected-row:hover {
      --hover-background-tr-start: @color-bg-surface-active;
      --hover-background-tr: @color-bg-surface-active;
      background-color: @color-bg-surface-active;

      > .ant-table-cell-fix-left,
      > .ant-table-cell-fix-right,
      > td,
      .action {
        background-color: @color-bg-surface-active;
      }
    }

    &.expand-row {
      td {
        // background-color: @color-grey-light1;
        background-color: #f7f8fa;
      }
    }

    &:hover {
      .action {
        opacity: 1;
        background: @hover-background-tr;
        display: flex;
        animation: fadeIn 0.3s;
        height: 98%;
        max-height: @size-48;

        &.hide-action {
          display: none;
        }
      }
    }

    .action {
      display: none;
      align-items: center;
      opacity: 0;
      position: absolute;
      right: 6px;
      padding: 7px @size-16 7px 18px;
      gap: 4px;
      background: @hover-background-tr;
      z-index: 10;
      transition: all 0.3s;
      width: fit-content;
      top: 50%;
      transform: translateY(-50%);

      hrdx-button {
        hrdx-icon {
          font-size: @size-1 * 16;
        }
      }
    }

    .action-active {
      opacity: 1;
      display: flex;
      animation: fadeIn 0.3s;
      height: 98%;
      background: @hover-background-tr;
    }

    > td {
      line-height: @font-line-height-2x-large;
      padding: @spacing-2 @spacing-4;

      &.td-action {
        .action-wrapper {
          display: flex;
          justify-content: center;
        }
      }

      &:not(.td-last) {
        white-space: nowrap;
        text-overflow: ellipsis;
        width: 100%;
        overflow: hidden;
      }

      &.ant-table-selection-column {
        font-size: 20px;
      }

      .ant-checkbox-wrapper {
        .ant-checkbox-input {
          overflow: visible;
          text-overflow: clip;
        }
      }

      > * {
        max-height: @font-line-height-base;
        line-height: @font-line-height-base;

        hrdx-display {
          display: block;
        }
      }

      color: @color-text-primary;
    }
  }

  ::ng-deep table > col:not(:first-child) {
    // must have to use important for overrice style inline attribute nzWidth of antd.
    &:not(.not-set-min-width) {
      min-width: @spacing-1 * 25 !important;
    }

    max-width: @spacing-10 * 10 !important;
  }

  // // TODO: FIX ME
  // ::ng-deep table > col:nth-child(22) {
  //   min-width: @spacing-8 * 5;
  //   width: unset !important;
  // }

  ::ng-deep .ant-table-footer {
    border-top: 1px solid @color-border-primary;
  }

  .pagination {
    // TODO: for responsive
    container-name: paginationContainer;
    container-type: inline-size;
  }

  @container paginationContainer (max-width: 500px) {
    hrdx-pagination {
      flex-direction: column;
      gap: @spacing-2;
      align-items: flex-end;
    }
  }

  ::ng-deep .header-cell {
    position: relative;
    white-space: normal;

    .th-min-width {
      min-width: 100px;
    }

    .header-cell-content {
      display: inline-flex;
      align-items: center;
      width: 96%;
      height: 16px;
    }

    &.cdk-drag,
    .cdk-drag-placeholder {
      &:hover {
        .drag-icon-wrapper {
          visibility: visible;
          opacity: 1;
        }
      }

      .drag-icon-wrapper {
        visibility: hidden;
        opacity: 0;
        position: absolute;
        right: @spacing-3;
        top: 0px;
        width: 5px;
        height: 100%;
        cursor: pointer;
        z-index: 1;
        transition:
          visibility 0.2s,
          opacity 0.2s ease-in-out;
      }

      .drag-icon {
        color: @color-primary-main;
        display: flex;
        height: 100%;
        align-items: center;
      }
    }

    &:hover {
      .drag-icon-wrapper {
        visibility: visible;
        opacity: 1;
      }
    }

    .drag-icon-wrapper {
      visibility: hidden;
      opacity: 0;
      position: absolute;
      right: @spacing-5;
      top: 14px;
      width: 5px;
      height: 100%;
      cursor: pointer;
      z-index: 1;
      transition:
        visibility 0.2s,
        opacity 0.2s ease-in-out;
    }

    .drag-icon {
      color: @color-primary-main;
    }

    .resize-handle {
      position: absolute;
      right: 0;
      top: 0;
      width: 2px;
      height: 100%;
      cursor: col-resize;
      z-index: 1;
    }
  }

  .sort-icon {
    font-size: @font-size-medium;
    padding-left: 8px;

    .fa-sort-up,
    .fa-sort-down {
      color: @color-icon-label;
      line-height: 0;

      &.active {
        color: @color-icon-primary;
      }
    }
  }

  .sort-active {
    color: @color-icon-primary;
  }

  .active-column {
    background-color: @color-bg-button-primary-disabled;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      top: 5%;
      right: 0;
      width: 2px;
      height: 90%;
      background-color: @color-primary-main;
    }

    &.ant-table-cell-fix-left-last {
      &::after {
        right: 2px;
      }
    }
  }

  .active-column:last-of-type {
    background-color: @color-bg-button-primary-disabled;
    position: relative;

    &::after {
      width: 0;
    }
  }

  .td-drag {
    background-color: @color-bg-surface-hover;
  }

  .td-resize {
    border-right: 2px solid @color-primary-main;
  }

  .td-resize:last-of-type {
    border-right: unset;
  }

  ::ng-deep .ant-table-ping-left {
    .ant-table-cell-fix-left-last {
      border-right: 1px solid @color-border-primary;
      // box-shadow: 0px @size-8 @size-16 -@size-12 @color-overlay-1;
      z-index: 1;

      &::after {
        box-shadow: none;
      }
    }
  }
}

.no-data-container {
  display: table-cell;
  vertical-align: middle;
  border-bottom-left-radius: @border-radius-base;
  border-bottom-right-radius: @border-radius-base;
  border-bottom: 0;
}

.no-data {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: @size-36 * 10;
  gap: @spacing-4;

  img {
    margin-bottom: @spacing-basis;
  }

  .no-result-title {
    text-align: center;

    .main {
      margin-bottom: @spacing-basis;
      color: @color-text-secondary;
      text-align: center;
      font-family: @font-typeface-brand;
      font-size: @font-size-medium;
      font-style: normal;
      font-weight: @font-weight-semibold;
      line-height: @font-line-height-medium;

      .highlight {
        color: @color-text-active;
      }
    }

    .sub {
      color: @color-text-secondary;
      text-align: center;
      font-family: @font-typeface-brand;
      font-size: @font-size-base;
      font-style: normal;
      font-weight: @font-weight-regular;
      line-height: @font-line-height-base;
    }
  }
}

.no-result-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: @spacing-2;
}

@keyframes fadeIn {
  from {
    opacity: 0.5;
    background-color: @hover-background-tr-start;
  }

  to {
    opacity: 1;
    background: @hover-background-tr;
  }
}

@keyframes slideOut {
  from {
    transform: translateY(100%);
  }

  to {
    transform: translateY(0);
  }
}

.expand {
  // background-color: @color-grey-light1;
  background-color: #f7f8fa;

  button {
    color: @color-primary;
    background: transparent;
    border: none;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0;
    text-align: center;
    margin: auto;
    cursor: pointer;

    hrdx-icon {
      display: block;
      transition: 0.3s ease-in-out;
      color: @color-base-neutral-6;
      font-weight: bold;
      font-size: @spacing-5;
    }

    &.open {
      hrdx-icon {
        transform: rotate(180deg);
      }
    }
  }
}

.cdk-drag-preview {
  background-color: @color-bg-button-primary-disabled;
  padding: @spacing-3;
  font-weight: @font-weight-semibold;
  color: @color-text-title;

  .preview-header {
    .sort-icon {
      display: inline-flex;
      flex-direction: column;

      .fa-sort-down,
      .fa-sort-up {
        color: @color-icon-label;
        line-height: 0;
      }
    }
  }
}

.height-resizer {
  width: 100%;
  height: @size-1 * 3;
  cursor: row-resize;
  background: @color-border-active;
  position: absolute;
  bottom: 0;
  z-index: 1;
  transition: all 0.3s;
  opacity: 0;

  &:hover,
  &.visible {
    opacity: 1;
  }
}
