import { CommonModule } from '@angular/common';
import {
  AfterContentInit,
  Component,
  computed,
  ContentChild,
  ContentChildren,
  effect,
  ElementRef,
  HostListener,
  inject,
  input,
  OnChanges,
  output,
  QueryList,
  SimpleChanges,
  viewChild,
  ViewChild,
  signal,
  OnDestroy,
  AfterViewInit,
  AfterViewChecked,
  model,
  contentChild,
} from '@angular/core';
import { NzSafeAny } from 'ng-zorro-antd/core/types';
import { NzSkeletonModule } from 'ng-zorro-antd/skeleton';
import { NzTableModule } from 'ng-zorro-antd/table';
import { ButtonComponent } from '../button';
import { IconComponent } from '../icon';
import { PaginationComponent } from '../pagination';
import { TbodyComponent } from './tbody/tbody.component';
import { TheadComponent } from './thead/thead.component';
import { CdkDragDrop, DragDropModule } from '@angular/cdk/drag-drop';
import { chain, cloneDeep, flatten, get, isNil, set } from 'lodash';
import { NzIconModule } from 'ng-zorro-antd/icon';
import {
  IllustrationsComponent,
  IllustrationsSize,
  IllustrationsType,
} from '../illustrations';
import { findItemById } from './new-table.utils';
import { Item } from './new-table.models';

@Component({
  selector: 'hrdx-new-table',
  standalone: true,
  imports: [
    CommonModule,
    NzTableModule,
    PaginationComponent,
    IconComponent,
    NzSkeletonModule,
    ButtonComponent,
    DragDropModule,
    NzIconModule,
    IllustrationsComponent,
  ],
  templateUrl: './new-table.component.html',
  styleUrl: './new-table.component.less',
})
export class NewTableComponent<T>
  implements
    AfterContentInit,
    OnChanges,
    OnDestroy,
    AfterViewInit,
    AfterViewChecked
{
  thead = contentChild(TheadComponent);
  @ContentChildren(TbodyComponent) listOfTbody?: QueryList<TbodyComponent>;
  @ViewChild('myTable') myTable!: ElementRef;
  customClass = input<string>('');
  addNewBtnTitle = input<string | undefined>(undefined);
  el = inject(ElementRef);
  isRowExpand = input<boolean>(false);
  data = input<T[]>([]);
  disabledData = input<T[]>([]);
  isShowLengthPaginaton = input<boolean>(false);
  isShowRowPerPage = input<boolean>(true);
  total = input(0);
  pageIndex = input(0, {
    transform: (value: number) => {
      if (!value) return 1;
      return value;
    },
  });
  pageSize = input(0, {
    transform: (value: number) => {
      if (!value) return this.pageSizeOptions()[0];
      return value;
    },
  });
  headers = input<NzSafeAny[]>([]);
  orderChange = output<NzSafeAny[]>();
  scrollWidth = 0;
  clientWidth = 0;
  scrollLeft = 0;
  pageSizeOptions = input([25, 50, 100, 250]);
  loading = input(false);
  showPagination = input(true);
  pageIndexChange = output<number>();
  pageSizeChange = output<number>();
  contentLoading = false;
  showCheckbox = input(false);
  hiddenNoResult = input(false);
  isFullScreen = input(false);
  showCustomExpand = input(false);
  colLoadingSkeleton = input(10);
  rowLoadingSkeleton = input(10);
  setCheckedItemInput = input<Set<number>>(new Set());
  listOfSelectedItems = input(0);

  setOfSelectedItem = new Set<number | string>();
  showUnselect = input<boolean>(false);
  scrollHeight = model<string | null>();
  height = input<number | null>(null);

  selectedItemChange = output<T[]>();
  showCreateDataTable = input<boolean>(true);
  showAddNew = input<boolean>(false);
  isFiltering = input<boolean>(false);
  searchValue = input<string>();
  showActionHeader = input<boolean>(true);
  haveChildItemCheckbox = input<boolean>(false);
  _hideRowAction = input<boolean>(false);
  columnWidthPath = input<string[]>(['options', 'tabular', 'column_width']);
  sizeConfig = input<NzSafeAny>('default');
  // use this options to tell table exactly how many items in current page
  pageCount = input<number | null>(null);
  setCheckedAll = input(false);
  showRowIndex = input<boolean>(false);
  fixedActionColumn = input<boolean>(false);

  clearSearch = output<boolean>();
  addFilter = output<boolean>();

  tableRef = viewChild<ElementRef<HTMLElement>>('table');

  allChecked = false;
  indeterminate = false;
  ROW_LOADING_SKELETON = computed(() =>
    Array.from({ length: this.rowLoadingSkeleton() }),
  );
  COL_LOADING_SKELETON = computed(() =>
    Array.from({ length: this.colLoadingSkeleton() }),
  );
  ROWS_HIDE_PAGINATION = 25;

  showGetStartedMessage = input<boolean>(true);
  noDataSubText = input<string>();

  storedCheckedPerPageState = new Map<number, NzSafeAny[]>();

  scrollToBottom = output();
  // use this option to show more row loading skeleton to the end of table
  loadingMore = input(false);
  resize = input<{ height?: boolean; width?: boolean }>();
  activeRowPressed = input(false);
  storeSelectedItems = input(null, {
    transform: (v: boolean | { keyToCheck: string }) => {
      if (typeof v === 'object') return v;
      return v ? { keyToCheck: 'id' } : null;
    },
  });
  selectedItemsMap = new Map<string, NzSafeAny>();
  private _setSelectedOnChangesData = signal<string[] | null>(null);

  setSelectedOnChangesData(ids: string[]) {
    this._setSelectedOnChangesData.set(ids);
  }

  setSelectedItemsByIds(ids: string[]) {
    this.setOfSelectedItem.clear();
    (this.data() as Record<string, unknown>[])?.forEach((item, idx) => {
      if (ids.includes(item['id'] as string)) {
        this.setOfSelectedItem.add(idx);
      }
    });
    this._setSelectedOnChangesData.set(null);
    this.refreshCheckedStatus();
  }

  constructor() {
    effect(() => {
      // every time the data change update visualIndexMap for drag and drop
      this.data();
      this.getRealIndex();
    });
  }

  effectRowId = effect(
    () => {
      this.loading();
      this.isHovered = '-1';
    },
    { allowSignalWrites: true },
  );

  clearChecked(): void {
    this.setOfSelectedItem.clear();
    this.allChecked = false;
    this.indeterminate = false;
    this.selectedItemChange.emit([]);
    this.isCheckedAll = false;
  }

  clearStoredChecked() {
    this.storedCheckedPerPageState.clear();
  }

  clearSelectedItemsMap() {
    this.selectedItemsMap.clear();
    this.refreshCheckedStatusBaseSelectedMap();
    this.selectedItemChange.emit([]);
  }

  onUnSelectAll() {
    if (this.storeSelectedItems()) {
      this.clearSelectedItemsMap();
      return;
    }
    this.clearChecked();
    this.clearStoredChecked();
  }

  createDataTable = output<boolean>();
  isCheckedAll = false;

  keyToDistinctItems = computed(
    () => this.storeSelectedItems()?.keyToCheck ?? 'id',
  );

  private updateSelectedItems(checked: boolean, items: NzSafeAny[]) {
    const keyToCheck = this.keyToDistinctItems();
    items.forEach((item) => {
      const key = item?.[keyToCheck];
      if (!isNil(key)) {
        if (checked) {
          this.selectedItemsMap.set(item[keyToCheck], item);
        } else {
          this.selectedItemsMap.delete(item[keyToCheck]);
        }
      }
      const children = item.children;
      if (Array.isArray(children)) {
        this.updateSelectedItems(checked, children);
      }
    });

    this.selectedItemChange.emit([...this.selectedItemsMap.values()]);
    this.refreshCheckedStatusBaseSelectedMap();
  }

  private getRealData() {
    if (this.haveChildItemCheckbox()) {
      let data: NzSafeAny[] = [];
      (this.data() as NzSafeAny[])?.forEach(
        (item) => (data = data.concat(item['children'] ?? [])),
      );
      return data;
    }
    return this.data() ?? ([] as NzSafeAny[]);
  }

  private updateSelectedItemsMapWhenDataChanges() {
    const keyToCheck = this.keyToDistinctItems();
    this.getRealData().forEach((item) => {
      const key = item?.[keyToCheck];
      if (!isNil(key) && this.selectedItemsMap.has(key)) {
        this.selectedItemsMap.set(key, item);
      }
    });
    this.selectedItemChange.emit([...this.selectedItemsMap.values()]);
    this.refreshCheckedStatusBaseSelectedMap();
  }

  private refreshCheckedStatusBaseSelectedMap() {
    const keyToCheck = this.keyToDistinctItems();
    const data = this.getRealData();
    const currentSelectedItemsCount = data.reduce(
      (acc, item) =>
        acc + (this.selectedItemsMap.has(item[keyToCheck]) ? 1 : 0),
      0,
    );
    if (currentSelectedItemsCount === 0) {
      this.allChecked = false;
      this.indeterminate = false;
    } else if (currentSelectedItemsCount === data.length) {
      this.allChecked = true;
      this.indeterminate = false;
    } else {
      this.allChecked = false;
      this.indeterminate = true;
    }
  }

  getSelectedItemsText() {
    const selectedItemsCount = this.storeSelectedItems()
      ? this.selectedItemsMap.size
      : this.setOfSelectedItem.size;
    return `${selectedItemsCount} item(s) selected`;
  }

  onAllChecked(checked: boolean): void {
    if (this.storeSelectedItems()) {
      this.updateSelectedItems(checked, this.data());
      return;
    }
    const checkAllItems = (items: Item[]) => {
      items.forEach((item) => {
        if (this.haveChildItemCheckbox() && item.id) {
          if (checked) {
            this.setOfSelectedItem.add(item.id);
          } else {
            this.setOfSelectedItem.delete(item.id);
          }
        }
        if (item.children) {
          checkAllItems(item.children);
        }
      });
    };

    if (this.haveChildItemCheckbox()) {
      checkAllItems(this.data() as Item[]);
      this.isCheckedAll = !this.isCheckedAll;
    } else {
      this.setOfSelectedItem = checked
        ? new Set(
            this.listOfTbody &&
              (chain(this.listOfTbody.toArray() as TbodyComponent[])
                .map((item, i) => (!item?.disabled() && i) as number | false)
                .filter((i) => i !== false)
                .value() as (string | number)[]),
          )
        : new Set();
    }

    this.onSavedCheckedPerPage();
    this.refreshCheckedStatus();
  }

  onSavedCheckedPerPage() {
    this.storedCheckedPerPageState.set(this.pageIndex(), this.selectedItem);
  }

  private getItemByIdx(idx: string | number) {
    if (typeof idx === 'number') return this.data()?.[idx];
    return findItemById(this.data() as Item[], idx);
  }

  onChecked(checked: boolean, idx: number | string): void {
    if (this.storeSelectedItems()) {
      this.updateSelectedItems(checked, [this.getItemByIdx(idx)]);
      return;
    }
    const checkItems = (items: Item[]) => {
      items.forEach((item) => {
        if (checked) {
          this.setOfSelectedItem.add(item.id);
        } else {
          this.setOfSelectedItem.delete(item.id);
        }

        if (item.children) {
          checkItems(item.children);
        }
      });
    };

    const haveChildrenCase = (items: Item[], id: string | number) => {
      for (const item of items) {
        if (item.id === id) {
          checkItems([item]);
          if (item.children) {
            checkItems(item.children);
          }
          return;
        }
        if (item.children) {
          haveChildrenCase(item.children, id);
        }
      }
    };

    const normalCase = (checked: boolean, idx: string | number) => {
      if (checked) {
        this.setOfSelectedItem.add(idx);
      } else {
        this.setOfSelectedItem.delete(idx);
      }
    };

    // refresh state of isCheckedAll
    this.isCheckedAll = false;

    this.haveChildItemCheckbox()
      ? haveChildrenCase(this.data() as Item[], idx)
      : normalCase(checked, idx);

    this.onSavedCheckedPerPage();

    this.refreshCheckedStatus();
  }

  flattenData = (data: T[], selectedItem: NzSafeAny[]) => {
    const flattened: T[] = [];

    const traverse = (items: NzSafeAny[]) => {
      items.forEach((item) => {
        if (selectedItem.includes(item.id)) {
          flattened.push({
            ...item,
            id: item.id,
          });
        }

        if (item.children) {
          traverse(item.children);
        }
      });
    };

    traverse(data);
    return flattened;
  };

  totalDataCount = computed(() => {
    const data = (this.data() ?? []) as Record<string, any>[];
    if (this.haveChildItemCheckbox()) {
      return data.reduce(
        (acc, item) => acc + (item?.['children']?.length ?? 0),
        0,
      );
    }
    return this.listOfTbody?.length ?? 0;
  });

  refreshCheckedStatus(): void {
    const selectedCount = this.setOfSelectedItem.size;
    const totalCount = this.totalDataCount();
    const data = this.data();
    const isCheckedAll = this.isCheckedAll;
    if (selectedCount === totalCount || isCheckedAll) {
      this.allChecked = true;
      this.indeterminate = false;
    } else if (selectedCount === 0) {
      this.clearChecked();
      return;
    } else {
      this.allChecked = false;
      this.indeterminate = true;
    }

    const emitSelectedItems = () => {
      const tbodyList = (this.listOfTbody?.toArray() ?? []) as TbodyComponent[];
      const selectedItem = this.haveChildItemCheckbox()
        ? this.flattenData(data, [...this.setOfSelectedItem])
        : [...this.setOfSelectedItem].map((i) => {
            if (this.isRowExpand()) {
              return data[Number(i) - 1];
            }
            if (tbodyList[i as number]?.value()) {
              return tbodyList[i as number]?.value();
            }
            return data[i as number];
          });
      this.selectedItemChange.emit(selectedItem);
    };

    emitSelectedItems();
  }

  get selectedItem() {
    if (this.haveChildItemCheckbox()) {
      const selectedItem = this.flattenData(this.data(), [
        ...this.setOfSelectedItem,
      ]);
      return selectedItem;
    }
    return [...this.setOfSelectedItem].map((i) => this.data()[i as number]);
  }

  get selectedItemPerPage() {
    return flatten([...this.storedCheckedPerPageState.values()]);
  }

  calcTableHeight(): string | null {
    if (!this.isFullScreen()) return 'scroll';
    return (
      (
        (this.el.nativeElement as HTMLDivElement).clientHeight -
        52 -
        56
      ).toString() + 'px'
    );
  }

  _scrollHeight = computed(() => {
    return this.scrollHeight() ?? this.calcTableHeight();
  });

  checkedItem = effect(() => {
    if (this.setCheckedItemInput().size > 0) {
      this.setOfSelectedItem = this.setCheckedItemInput();
      this.refreshCheckedStatus();
    }
  });

  updateTableActionPosition() {
    const els = this.el.nativeElement as HTMLElement;
    const table = els.querySelector('.ant-table-body');
    if (table) {
      this.clientWidth = table.clientWidth;
      this.scrollWidth = table.scrollWidth;
      this.scrollLeft = table.scrollLeft;
    }
    const fields = els.querySelectorAll(
      '.ant-table-tbody > tr .action',
    ) as NodeListOf<HTMLDivElement>;

    if (fields) {
      fields.forEach((field) => {
        if (field)
          field.style.right = `${this.scrollWidth - this.clientWidth - this.scrollLeft}px`;
      });
    }
  }

  onScroll = output();

  rewriteMinWidthColTable() {
    this.thead()
      ?.listOfTh()
      ?.forEach((th, index) => {
        if (th.isNotSetMinWidth) {
          let newIndexChild = index + 1;
          if (this.showCheckbox()) {
            newIndexChild += 1;
          }
          if (this.showRowIndex()) {
            newIndexChild += 1;
          }
          const getCols = (
            this.el.nativeElement as HTMLDivElement
          ).querySelectorAll(`table>col:nth-child(${newIndexChild})`);
          if (getCols) {
            getCols.forEach((col) => {
              col.classList.add('not-set-min-width');
            });
          }
        }
      });
  }

  ngAfterViewInit() {
    this.rewriteMinWidthColTable();

    const tableBody = (this.el.nativeElement as HTMLDivElement).querySelector(
      '.ant-table-body',
    );

    tableBody?.addEventListener('scroll', (event: Event) => {
      this.onScroll.emit();
      if (this.isScrollToBottom(event)) {
        this.scrollToBottom.emit();
      }
      this.updateTableActionPosition();
    });
  }

  private isScrollToBottom(event: Event) {
    const target = event?.target as HTMLElement;
    if (!target) return false;
    return target.offsetHeight + target.scrollTop >= target.scrollHeight;
  }

  @HostListener('window:resize', ['$event'])
  onResize(event: any) {
    this.updateTableActionPosition();
  }

  ngAfterContentInit() {
    this.contentLoading = true;
  }

  ngAfterViewChecked(): void {
    this.updateTableActionPosition();
    if (this.shouldCheckedAll() && !this.allChecked) {
      this.onAllChecked(true);
      this.shouldCheckedAll.set(false);
    }
  }

  removeSelectedItemsMapByIds(ids: string[]) {
    ids.forEach((id) => this.selectedItemsMap.delete(id));
    this.selectedItemChange.emit([...this.selectedItemsMap.values()]);
  }

  shouldCheckedAll = signal(false);
  ngOnChanges(changes: SimpleChanges) {
    if (changes['data']) {
      this.resetRowActive();
      if (this.storeSelectedItems()) {
        this.updateSelectedItemsMapWhenDataChanges();
        return;
      }

      const selectedIds = this._setSelectedOnChangesData() ?? [];
      if (selectedIds.length > 0) {
        this.setSelectedItemsByIds(selectedIds);
        return;
      }

      this.clearChecked();
      if (this.setCheckedAll()) {
        this.shouldCheckedAll.set(true);
      }
    }
  }

  isDestroyed = false;
  ngOnDestroy() {
    this.isDestroyed = true;
  }

  isHovered = '-1';

  onMouseEnter(idx: string): void {
    this.isHovered = idx;
  }

  getMaxRowSpan(): number {
    const listOfTh = this.thead()?.listOfTh();
    if (listOfTh) {
      return Math.max(...listOfTh.map((th) => th.rowSpan() || 1));
    }
    return 1;
  }

  rowSpanRange = computed(() => {
    return Array.from({ length: this.getMaxRowSpan() - 1 }, (_, i) => i + 1);
  });

  isGroupingTable = computed(() =>
    this.thead()
      ?.listOfTh()
      ?.some((th) => th.isGrouped()),
  );

  groupingColumns = computed(() =>
    this.thead()
      ?.listOfTh()
      ?.filter((th) => th.isGrouped()),
  );

  visualIndexMap: number[] = [];

  getRealIndex() {
    this.thead()
      ?.listOfTh()
      ?.forEach((col, index) => {
        // If the column doesn't have a grouped colspan, its index is added to visualIndexMap.
        if (!col.isGrouped()) {
          this.visualIndexMap.push(index);
        }
      });
  }

  onDrop(event: CdkDragDrop<NzSafeAny>): void {
    const { previousIndex, currentIndex } = event;
    const columns = this.thead()?.listOfTh();

    if (columns && previousIndex !== currentIndex) {
      //  Calculate the realIndex when the table columns have colspan.
      // If not, we can get the previous or current index directly from the event.

      const realPreviousIndex = this.visualIndexMap[previousIndex];
      const realCurrentIndex = this.visualIndexMap[currentIndex];

      this.updateHeaderTable(realPreviousIndex, realCurrentIndex);
    }
  }

  updateHeaderTable(previousIndex: number, currentIndex: number): void {
    const header = cloneDeep(this.headers());
    // prevent drag drop on column is pinned
    if (header[currentIndex].pinned) return;
    [header[previousIndex], header[currentIndex]] = [
      header[currentIndex],
      header[previousIndex],
    ];
    this.activeColumnIndex = null;
    this.isColumnDraging = false;
    this.orderChange.emit(header);
  }

  trackByIndex(index: number) {
    return index;
  }

  resizingColumn: NzSafeAny = {};
  startX = 0;
  startWidth = 0;

  isResizing = false;
  isResizeStart = false;
  sortOrder: 'ascend' | 'descend' | null = null;
  sortedColumn = 0;

  onSortOrder(th: NzSafeAny, index: number) {
    // Cycle through 'desc', 'asc', and null
    this.sortOrder =
      this.sortOrder === 'ascend'
        ? 'descend'
        : this.sortOrder === 'descend'
          ? null
          : 'ascend';

    this.sortedColumn = index;

    if (this.isResizeStart) {
      this.isResizeStart = false;
      return;
    }
    th.sortOrderChange.emit(this.sortOrder);
  }

  getSortIcon(index: number) {
    if (this.isAscending(index)) {
      return 'ic-sort-up';
    }
    if (this.isDescending(index)) {
      return 'ic-sort-down';
    }
    return 'ic-sort';
  }
  isDragable(th: NzSafeAny): boolean {
    // Disable dragging when the column is fixed to the left, has a colspan, or is being resized.
    // Enable dragging only when the dragging process has started.
    return (
      th.fixedLeft() ||
      th.colSpan() !== 0 ||
      this.isResizing ||
      !this.isColumnDraging
    );
  }

  setValue(obj: NzSafeAny, path: string[], value: NzSafeAny): void {
    return set(obj, path, value);
  }

  getValue(obj: NzSafeAny, path: string[]): NzSafeAny {
    return get(obj, path, undefined);
  }

  onResizeStart(event: MouseEvent, index: number) {
    const columnWidthPath = this.columnWidthPath();

    this.isResizing = true;
    this.isResizeStart = true;

    event.preventDefault();

    const header = this.headers();

    this.resizingColumn = header[index];
    // convert px to rem
    this.startX = event?.pageX / 16;
    this.startWidth = this.getValue(header[index], columnWidthPath) ?? 10;

    const onResizing = (event: MouseEvent) => {
      if (this.resizingColumn) {
        const dx = event.pageX / 16 - this.startX;
        let newWidth = Number(this.startWidth + dx);

        // Apply min and max width constraints, default minWidth = 100px, maxWidth = 400px
        newWidth = Math.max(6.25, newWidth);
        newWidth = Math.min(25, newWidth);

        this.resizingColumn = this.setValue(
          this.resizingColumn,
          columnWidthPath,
          newWidth,
        );
      }
    };

    const onResizeEnd = () => {
      const headers = this.headers();
      // Update headers with the updated this.resizingColumn
      const index = headers.findIndex(
        (item) => item.code === this.resizingColumn.code,
      );
      headers[index] = { ...headers[index], ...this.resizingColumn };
      if (!this.isDestroyed) {
        this.orderChange.emit(headers);
      }

      document.removeEventListener('mousemove', onResizing);
      document.removeEventListener('mouseup', onResizeEnd);

      this.resizingColumn = null;
      this.isResizing = false;
    };

    document.addEventListener('mousemove', onResizing);
    document.addEventListener('mouseup', onResizeEnd);
  }

  activeColumnIndex: number | null = null;
  isColumnDraging = false;

  onHoldStart(index: number): void {
    this.isColumnDraging = true;
    this.activeColumnIndex = this.activeColumnIndex === index ? null : index;
  }

  onHoldEnd() {
    this.isColumnDraging = false;
    this.activeColumnIndex = null;
  }

  onEnterResize(index: number): void {
    this.activeColumnIndex = this.activeColumnIndex === index ? null : index;
  }

  onLeaveResize() {
    this.activeColumnIndex = null;
  }

  isAscending(index: number) {
    return this.sortOrder === 'ascend' && this.sortedColumn === index;
  }
  isDescending(index: number) {
    return this.sortOrder === 'descend' && this.sortedColumn === index;
  }

  showFooter = computed(() => {
    if (this.showAddNew() && this.total() > 0) return true;
    if (!this.showPagination()) return false;
    if (isNil(this.total()) || this.total() <= 0) return false;
    return true;
  });

  isFixedHeight = computed(() => {
    return !isNil(this.height());
  });

  private getElementBySelector(selector: string) {
    return this.tableRef()?.nativeElement.querySelector(
      selector,
    ) as HTMLElement | null;
  }

  get headerRef() {
    return this.getElementBySelector('.ant-table-header');
  }

  get footerRef() {
    return this.getElementBySelector('.ant-table-footer');
  }

  get footerHeight() {
    return (this.footerRef?.offsetHeight ?? 0) + 'px';
  }

  get headerHeight() {
    return (this.headerRef?.offsetHeight ?? 0) + 'px';
  }

  get styles() {
    if (this.isFixedHeight()) {
      return {
        '--height': this.height() + 'px',
        '--header-height': this.headerHeight,
        '--footer-height': this.footerHeight,
      };
    }

    return {
      '--scroll-height': this._scrollHeight(),
    };
  }

  noDataIllustrationConfig = computed(() => ({
    type:
      this.isFiltering() || this.searchValue()
        ? IllustrationsType.NoResult
        : IllustrationsType.Empty,
    size: IllustrationsSize.Small,
  }));

  _noDataSubText = computed(() => {
    const isFiltering = this.isFiltering();
    const searchValue = this.searchValue();
    if (isFiltering && searchValue)
      return 'Try different keywords or adjust filters to see more results';
    if (searchValue) return 'Try different keywords to see more results.';
    if (isFiltering) return 'Adjust filters to see more results';
    if (this.showCreateDataTable() || this.showGetStartedMessage())
      return (
        this.noDataSubText() ??
        'Get started by adding your first piece of data.'
      );
    return null;
  });

  getRowIndex(idx: number) {
    idx = idx + 1;
    const pageIndex = this.pageIndex() ?? 0;
    const pageSize = this.pageSize() ?? 0;
    return idx + (pageIndex - 1) * pageSize;
  }
  checkedBoxValue(
    def: boolean,
    value: boolean,
    tbody: NzSafeAny,
    index: number,
  ) {
    if (this.storeSelectedItems()) {
      return this.selectedItemsMap.has(
        this.haveChildItemCheckbox()
          ? tbody.idx()
          : (this.data()[index] as NzSafeAny)?.[this.keyToDistinctItems()],
      );
    }
    switch (def) {
      case false:
        return value
          ? true
          : this.setOfSelectedItem.has(
              this.haveChildItemCheckbox() ? tbody.idx() : index,
            );
      default:
        return value;
    }
    return false;
  }

  isResizingScrollHeight = signal(false);
  onResizeScrollHeight(event: MouseEvent) {
    event.preventDefault();
    this.isResizingScrollHeight.set(true);
    const scrollHeight = Number(this.scrollHeight()?.match(/\d+/)?.[0]);
    const startY = event?.pageY;
    const onResizing = (event: MouseEvent) => {
      if (this.resizingColumn) {
        const dy = event.pageY - startY;
        let newHeight = Number(scrollHeight + dy);
        // min height will be about 100px
        newHeight = Math.max(100, newHeight);

        this.scrollHeight.set(`${newHeight}px`);
      }
    };

    const onResizeEnd = () => {
      document.removeEventListener('mousemove', onResizing);
      document.removeEventListener('mouseup', onResizeEnd);
      this.isResizingScrollHeight.set(false);
    };

    document.addEventListener('mousemove', onResizing);
    document.addEventListener('mouseup', onResizeEnd);
  }

  @HostListener('document:click', ['$event'])
  handleClickOutside(event: MouseEvent) {
    if (!this.activeRowPressed()) return;
    // TODO: If there is an overlay on the table, keep the row active, remove it if there is a better solution.
    if (document.querySelector('.cdk-global-overlay-wrapper')) return;
    const el = event.target as HTMLElement;
    const tableEl = this.tableRef()?.nativeElement;
    if (el.tagName !== 'TD' || !tableEl?.contains(el)) {
      this.resetRowActive();
    }
  }
  rowActive = signal(-1);
  resetRowActive() {
    this.rowActive.set(-1);
  }
  onRowClick(tbody: TbodyComponent, idx: number) {
    tbody.clickRow.emit();
    tbody.toggleExpand();
    if (!this.activeRowPressed()) return;
    this.rowActive.set(idx);
  }

  get isTableHasScroll() {
    const tableEle = this.tableRef()?.nativeElement.querySelector(
      '.ant-table-body',
    ) as HTMLElement;
    if (!tableEle) return false;
    return tableEle.scrollHeight > tableEle.clientHeight;
  }
}
