import { CommonModule } from '@angular/common';
import {
  Component,
  computed,
  ContentChildren,
  input,
  model,
  output,
  QueryList,
  signal,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { TdComponent } from '../td/td.component';

@Component({
  selector: 'hrdx-tbody',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './tbody.component.html',
  styleUrl: './tbody.component.less',
})
export class TbodyComponent {
  @ContentChildren(TdComponent) listOfTd?: QueryList<TdComponent>;
  @ViewChild('actions') actions?: TemplateRef<unknown>;
  clickRow = output<void>();
  hiddenAction = input<boolean>(false);
  isActionVisible = input<boolean>(false);
  idx = input<string>('');
  expand = model<boolean | undefined>();
  expandChange = output<boolean>();
  disabled = input<boolean>(false);
  selected = input<boolean>(false);
  antiThacSi = input<boolean>(false);
  noCheckBox = input<boolean>(false);
  onChechBoxChange = output<boolean>();
  isGroupSelected = input<boolean>(false);
  loading = input<boolean>(false);

  tbodyId = signal<string>(Math.random().toString() + Date.now().toString());
  value = input<any>();

  haveExpand = computed(() => typeof this.expand() === 'boolean');
  toggleExpand() {
    if (!this.haveExpand()) return;
    this.expand.update((prev) => !prev);
    this.expandChange.emit(this.expand() as boolean);
  }
}
