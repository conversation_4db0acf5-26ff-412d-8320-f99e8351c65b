<div [ngClass]="classesComputed()" *ngIf="showPageHeader()">
  @if (showBreadCrumb()) {
    <hrdx-breadcrumb>
      <ng-container *ngFor="let item of breadcrumbItems(); let i = index">
        <hrdx-breadcrumb-item [children]="item.children">
          <span (click)="naviagteToRoute(item.route)">{{
            item?.title ?? '...'
          }}</span>
        </hrdx-breadcrumb-item>
      </ng-container>
    </hrdx-breadcrumb>
    <nz-dropdown-menu #menu="nzDropdownMenu">
      <ul nz-menu nzSelectable>
        <ng-container *ngFor="let item of breadcrumbItems(); let i = index">
          @if (i >= 2 && i <= breadcrumbItems().length - 3) {
            <li nz-menu-item [style.paddingLeft]="(i - 2) * 16 + 'px'">
              <hrdx-breadcrumb-item>
                {{ item?.title }}
              </hrdx-breadcrumb-item>
            </li>
          }
        </ng-container>
      </ul>
    </nz-dropdown-menu>
  }
  <header>
    <aside class="page-header__text">
      <ng-container *ngIf="title() && showPageHeader()">
        <h1 [ngClass]="getPageTitle()" *ngIf="showPageTitle()">
          <hrdx-button
            *ngIf="isBackBtn()"
            [size]="'xsmall'"
            [circle]="true"
            [icon]="'icon-arrow-left-bold'"
            [type]="'ghost-gray'"
            (clicked)="backClicked.emit()"
            [onlyIcon]="true"
          >
          </hrdx-button>
          {{ title() }}
        </h1>
      </ng-container>
      <span *ngIf="description()" class="description">{{ description() }}</span>
    </aside>
    <aside
      class="page-header__buttons"
      [ngClass]="headerStyle() === 'widget' ? 'is_widget' : ''"
    >
      <nz-space *ngIf="showButtons()">
        @for (button of buttons(); track button.id) {
          @switch (headerStyle()) {
            @case ('widget') {
              <hrdx-button
                *nzSpaceItem
                [title]="button.title ?? ''"
                [type]="'ghost-gray'"
                (clicked)="buttonClicked.emit(button.id)"
                [size]="'xsmall'"
                [icon]="button.leftIcon"
                [onlyIcon]="button.isLeftIcon"
              >
              </hrdx-button>
            }
            @default {
              <hrdx-button
                *nzSpaceItem
                [title]="button.title ?? ''"
                [type]="button.type"
                (clicked)="buttonClicked.emit(button.id)"
                [size]="button.size"
                [leftIcon]="button.leftIcon"
                [isLeftIcon]="button.isLeftIcon && !!button.title"
                [onlyIcon]="!!button.leftIcon && !button.title"
                [icon]="button.leftIcon"
                [disabled]="button.disabled"
                [isLoading]="button.id === btnLoading()"
              >
              </hrdx-button>
            }
          }
        }
      </nz-space>
    </aside>
  </header>
  <!-- <main>
    <ng-content></ng-content>
  </main> -->
</div>
