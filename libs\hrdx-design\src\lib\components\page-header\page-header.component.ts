import { CommonModule } from '@angular/common';
import {
  Component,
  computed,
  inject,
  input,
  output,
  signal,
} from '@angular/core';
import { NzBreadCrumbModule } from 'ng-zorro-antd/breadcrumb';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzPageHeaderModule } from 'ng-zorro-antd/page-header';
import { NzSpaceModule } from 'ng-zorro-antd/space';
import { BreadCrumbModule } from '../breadcrumb';
import { ButtonComponent } from '../button';
import { PageHeader } from './page-header.models';
import { Router, Routes } from '@angular/router';

@Component({
  selector: 'hrdx-page-header',
  standalone: true,
  imports: [
    CommonModule,
    NzPageHeaderModule,
    NzBreadCrumbModule,
    NzSpaceModule,
    NzIconModule,
    ButtonComponent,
    BreadCrumbModule,
    NzDropDownModule,
  ],
  templateUrl: './page-header.component.html',
  styleUrl: './page-header.component.less',
})
export class PageHeaderComponent {
  title = input<PageHeader['title']>('');
  description = input<PageHeader['description']>('');
  breadcrumbItems = input<PageHeader['breadcrumb']>([]);
  borderLess = input<PageHeader['borderLess']>(false);
  headerStyle = input<PageHeader['headerStyle']>('master');
  titleSize = input<PageHeader['titleSize']>('default');
  buttons = input<PageHeader['buttons']>([]);
  options = input<PageHeader['options']>({
    breadcrumb: true,
    title: true,
    buttons: true,
    visible: true,
    border: true,
  });

  isLayoutProfile = input<boolean>(false);
  btnLoading = input<string>();
  buttonClicked = output<string>();
  backClicked = output();
  isBackBtn = input<boolean>(false);

  router = inject(Router);

  checkRouteExists(routeString?: string) {
    if (!routeString) return false;
    let flag = true;
    routeString
      .split('/')
      .filter((path) => path)
      .reduce((acc: Routes | undefined, path) => {
        if (!flag) return undefined;
        if (!path) {
          flag = false;
          return undefined;
        }
        const route = acc?.find((r) => r.path === path);
        if (route) {
          if (route.children) {
            return route.children;
          } else {
            return undefined;
          }
        } else {
          flag = false;
          return undefined;
        }
      }, this.router.config);

    return flag;
  }

  naviagteToRoute(route?: string | string[]) {
    if (!route) return;
    if (typeof route === 'string') {
      if (!this.checkRouteExists(route)) return;
    }
    route = typeof route === 'string' ? [route] : route;
    this.router.navigate(route);
  }

  HrdxButton = ButtonComponent;

  classesComputed = computed(() => {
    return {
      'page-header': true,
      'border-less': !this.showBorder(),
      'no-padding-bottom': this.isBackBtn(),
      'has-actions':
        (this.showButtons() && (this.buttons() ?? []).length > 0) ||
        this.description(),
      'layout-profile': this.isLayoutProfile(),
    };
  });

  showBreadCrumb = computed(() => {
    // return (
    // this.breadcrumbItems().length > 0 &&
    // (this.headerStyle() === 'master' || !this.headerStyle())
    // );
    if (this.headerStyle() === 'widget') return false;

    return (
      this.breadcrumbItems().length > 0 && (this.options()?.breadcrumb ?? true)
    );
  });

  showPageHeader = computed(() => {
    // return this.headerStyle() !== 'tab';
    return this.options()?.visible ?? true;
  });

  showPageTitle = computed(() => {
    return this.options()?.title ?? true;
  });

  showButtons = computed(() => {
    return this.options()?.buttons ?? true;
  });

  showBorder = computed(() => {
    return this.options()?.border ?? true;
  });

  getPageTitle() {
    const classess = ['title'];
    classess.push('title-' + this.titleSize());
    if (this.isBackBtn()) {
      classess.push('title-back__btn');
    }
    return classess.join(' ');
  }
}
