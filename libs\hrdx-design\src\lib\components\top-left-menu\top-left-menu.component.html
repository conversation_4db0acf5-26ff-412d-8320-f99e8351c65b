<ng-container>
  <div class="top-left-menu">
    <div class="expand">
      <hrdx-popover
        [content]="expandTemplate"
        [trigger]="popoverTrigger.Click"
        [position]="popoverPosition.BottomLeft"
        [arrow]="false"
        [(visible)]="popoverVisible"
      >
        <hrdx-button
          [icon]="'icon-dots-nine-bold'"
          [onlyIcon]="true"
          (clicked)="expand($event)"
          [size]="'small'"
          [type]="'ghost-color'"
        ></hrdx-button>
      </hrdx-popover>
    </div>
    @if (!isCollapsed()) {
      <div
        [ngClass]="{
          department: true,
          collapsed: isCollapsed(),
        }"
        (click)="routing(departmentIcon())"
      >
        <img
          class="department-icon"
          nz-image
          nzSrc="/assets/modules/{{ departmentIcon() }}.png"
          nzDisablePreview="true"
          alt=""
        />
        <h3 class="department-name">
          <hrdx-tooltip
            [title]="departmentName()"
            [arrow]="true"
            [position]="tooltipPostion.Right"
          >
            <ng-container [ngSwitch]="departmentIcon()">
              <span *ngSwitchCase="'HR'">HRM</span>
              <span *ngSwitchCase="'PIT'">PIT</span>
              <span *ngSwitchDefault>{{ departmentName() }}</span>
            </ng-container>
          </hrdx-tooltip>
        </h3>
      </div>
    }
  </div>
</ng-container>

<ng-template #expandTemplate>
  <div class="department-expand">
    @for (module of modules(); track $index) {
      <a
        [ngClass]="[
          'department-item',
          module.id === selectedModuleId() ? 'active' : '',
          module.disabled ? 'disabled' : '',
        ]"
        nzMatchRouter
        [routerLink]="module.disabled ? null : ['/', module.id]"
        (click)="changeModule(module, $event)"
      >
        <img
          nz-image
          nzSrc="/assets/modules/{{ module.id }}{{
            module.disabled ? '-disabled' : ''
          }}.png"
          alt=""
          nzDisablePreview="true"
        />
        <h5 class="department-name">{{ module.name }}</h5>
      </a>
    }
  </div>
</ng-template>

<!-- <ng-container *ngIf="isExpand && !isCollapsed()">
  <div class="department-expand">
    @for (module of modules(); track $index) {
      <a
        class="department-item"
        nzMatchRouter
        [routerLink]="['/', module.id]"
        (click)="changeModule(module)"
      >
        <img
          nz-image
          nzSrc="/assets/modules/{{ module.id }}{{
            module.disabled ? '-disabled' : ''
          }}.png"
          alt=""
          nzDisablePreview="true"
        />
        <h5 class="department-name">{{ module.name }}</h5>
      </a>
    }
  </div>
</ng-container> -->
