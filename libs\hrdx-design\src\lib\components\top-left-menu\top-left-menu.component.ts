import { CommonModule } from '@angular/common';
import { Component, effect, input, output, signal } from '@angular/core';
import { RouterModule } from '@angular/router';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzImageModule } from 'ng-zorro-antd/image';
import { HeaderMenuItem } from '../header-menu';
import { PopoverComponent, PopoverPosition, PopoverTrigger } from '../popover';
import { ButtonComponent } from '../button';
import { TooltipComponent, TooltipPosition } from '../tooltip';

@Component({
  selector: 'hrdx-top-left-menu',
  standalone: true,
  imports: [
    CommonModule,
    NzIconModule,
    NzImageModule,
    RouterModule,
    PopoverComponent,
    ButtonComponent,
    TooltipComponent,
  ],
  templateUrl: './top-left-menu.component.html',
  styleUrl: './top-left-menu.component.less',
})
export class TopLeftMenuComponent {
  popoverPosition = PopoverPosition;
  popoverTrigger = PopoverTrigger;
  tooltipPostion = TooltipPosition;

  modules = input.required<HeaderMenuItem[]>();
  selectedModuleId = input<HeaderMenuItem['id']>();
  isCollapsed = input<boolean>(false);
  departmentClicked = output<string>();
  changeModuleId = output<string>();

  popoverVisible = signal(false);

  moduleIdEffect = effect(
    () => {
      this.modules().forEach((module) => {
        if (module.id === this.selectedModuleId()) {
          this.departmentName.set(module.name);
          this.departmentIcon.set(module.id);
        }
      });
    },
    { allowSignalWrites: true },
  );

  departmentName = signal('Foundation');
  departmentIcon = signal('FO');
  departmentShortName = signal('FO');
  isExpand = false;

  expand(e: Event) {
    e.stopPropagation();
    this.isExpand = !this.isExpand;
    this.popoverVisible.update((prev) => !prev);
  }

  changeModule(module: HeaderMenuItem, event: MouseEvent) {
    if (module.disabled || event.ctrlKey || event.metaKey) return;
    this.popoverVisible.set(false);
    this.isExpand = false;
    this.departmentIcon.set(module.id);
    this.departmentName.set(module.name);
    this.changeModuleId.emit(module.id);
  }

  routing(route: string | undefined) {
    if (route) this.departmentClicked.emit(route);
  }
}
