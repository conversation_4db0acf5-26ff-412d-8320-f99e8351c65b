<ng-container>
  @if (fileName?.includes('.xlsx')) {
    <hrdx-icon [fixIon]="'fix-TypeXLS'" />
  } @else if (fileName?.includes('.csv')) {
    <hrdx-icon [fixIon]="'fix-TypeCSV'" />
  } @else if (fileName?.includes('.pdf')) {
    <hrdx-icon [fixIon]="'fix-TypePDF'" />
  } @else if (fileName?.includes('.svg')) {
    <hrdx-icon [fixIon]="'fix-TypeSVG'" />
  } @else if (
    fileName?.includes('.png') ||
    fileName?.includes('.jpg') ||
    fileName?.includes('.jpeg')
  ) {
    <hrdx-icon [fixIon]="'fix-TypeImage'" />
  } @else if (fileName?.includes('.docx') || fileName?.includes('.doc')) {
    <hrdx-icon [fixIon]="'fix-TypeDOC'" />
  } @else {
    <hrdx-icon [fixIon]="'fix-TypeWord'" />
  }
</ng-container>
