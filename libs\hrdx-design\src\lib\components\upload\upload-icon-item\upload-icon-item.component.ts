import { Component, Input, input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconComponent } from '../../icon';

@Component({
  selector: 'hrdx-upload-icon-item',
  standalone: true,
  imports: [CommonModule, IconComponent],
  templateUrl: './upload-icon-item.component.html',
  styleUrl: './upload-icon-item.component.less',
})
export class UploadIconItemComponent {
  @Input() fileName?: string;
}
