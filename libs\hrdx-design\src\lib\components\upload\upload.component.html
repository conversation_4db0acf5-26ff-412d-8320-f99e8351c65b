<nz-upload
  [nzAccept]="fileType()"
  [nzShowButton]="true"
  [nzDisabled]="state() === 'disabled'"
  [nzMultiple]="multiple()"
  (nzChange)="change.emit($event)"
  [nzFileList]="fileList()"
  (nzFileList)="fileListChange.emit()"
  [nzBeforeUpload]="_beforeUpload"
  [nzPreviewFile]="previewFile()"
  [nzIconRender]="customIcon"
  [nzFileListRender]="fileListRender"
  [ngClass]="{
    has_divider: hasDivider(),
    'padding-bottom-none': paddingBottomNone(),
  }"
>
  <!-- [nzFileListRender]="fileListRender" -->
  <div class="default-container" *ngIf="type() === 'default'">
    <div [ngClass]="'content'">
      <hrdx-icon
        name="upload"
        size="large"
        color="color-base-green"
        [ngClass]="'icon-' + state()"
      ></hrdx-icon>
      <div>
        <p class="file-name">Imagine.jpg</p>
        <p class="file-size" [ngClass]="'file-size file-size-' + state()">
          Size-100.99 MB
        </p>
      </div>
    </div>
    <ng-container>
      <hrdx-button
        *ngIf="state() === 'error'; else ellipsis"
        [type]="'secondary'"
        [title]="'Download'"
      />
      <ng-template #ellipsis>
        <hrdx-icon
          name="ellipsis-vertical"
          size="large"
          classes="icon-ellipsis-vertical"
        ></hrdx-icon>
      </ng-template>
    </ng-container>
  </div>
  <hrdx-upload-file
    *ngIf="type() === 'file'"
    [required]="required()"
    [error]="state() === 'error'"
    [errorMessage]="errorMsg"
    [disabled]="state() === 'disabled'"
    [maxFileSize]="maxFileSize()"
    [fileTypeLabel]="fileTypeLabel()"
    [customContentUpload]="customContentUpload()"
  />
  <div
    [ngClass]="'box-container box-container-' + state()"
    *ngIf="type() === 'box'"
  >
    <div [ngClass]="'box box-' + state()">
      <hrdx-icon
        name="upload"
        size="large"
        color="color-base-green"
        classes="icon-upload"
      ></hrdx-icon>
      <ng-container *ngIf="state() === 'uploading'; else noLoading">
        <div class="uploading-text">Uploading</div>
      </ng-container>
      <ng-template #noLoading>
        <p class="label">Drop your file here or</p>
        <hrdx-button [type]="'secondary'" title="Upload" class="button" />
        <p class="support">Support JPG, PNG and GIF</p>
      </ng-template>
    </div>
    <div *ngIf="state() === 'error'" class="error-text">Error Text</div>
  </div>
  <hrdx-upload-label
    *ngIf="type() === 'label'"
    [required]="required()"
    [error]="state() === 'error'"
    [errorMessage]="errorMsg"
    [disabled]="state() === 'disabled'"
  />
  <ng-template #fileListRender let-list>
    <ng-container *ngIf="list.length > 0">
      <!-- <nz-collapse nzGhost class="custom-collapse">
        <nz-collapse-panel [nzActive]="true" [nzHeader]="headerCollapse">
        <div *ngFor="let item of list">
          <div [ngClass]="'file-list'">
            <div [ngClass]="'file-list-name'">
              <ng-container
                *ngTemplateOutlet="customIcon; context: { $implicit: item }"
              ></ng-container>
              <div [ngClass]="'file-name-' + item.status">
                {{ item.name }}
              </div>
            </div>
            <div [ngClass]="'file-list-action'">
              <hrdx-icon
                *ngIf="item.status === 'done'"
                [name]="'check-circle'"
              />
              <hrdx-icon *ngIf="item.status === 'error'" [name]="'warning'" />
              <div (click)="removeFile(list, item)">
                <hrdx-icon [name]="'x'" [size]="'small'" />
              </div>
            </div>
          </div>
          <div [ngClass]="'add-more unable-upload'">Unable to upload</div>
          <div *ngIf="item.status === 'uploading'" [ngClass]="'add-more'">
            <nz-progress [nzPercent]="item.percent"></nz-progress>
          </div>
        </div>
      </nz-collapse-panel>
      </nz-collapse> -->

      <div [ngClass]="'file-list'">
        @for (item of list; track item) {
          <div class="file">
            <ng-container
              *ngTemplateOutlet="customIcon; context: { $implicit: item }"
            ></ng-container>
            <div [ngClass]="'file-list-name'">
              <div class="file-name">
                {{ item.name }}

                <div class="file-status">
                  <hrdx-icon
                    class="file-status-icon"
                    [name]="'icon-check-circle-bold'"
                    [size]="'small'"
                  >
                  </hrdx-icon>

                  <span class="file-status-name">Completed</span>
                </div>
              </div>
            </div>
            <div [ngClass]="'file-list-action'">
              <!-- <hrdx-icon *ngIf="item.status === 'done'" [name]="'check-circle'" />cc
              <hrdx-icon *ngIf="item.status === 'error'" [name]="'warning'" /> -->
              <div (click)="removeFile(list, item)">
                <hrdx-icon [name]="'icon-trash-bold'" [size]="'small'" />
              </div>
            </div>
          </div>
        }
      </div>
    </ng-container>
  </ng-template>
</nz-upload>

<!-- <ng-template #headerCollapse>
  <div class="more-info">
    <ng-container *ngIf="completedUpload(); else uploadInProgress">
      <hrdx-icon
        [name]="'check'"
        [size]="'medium'"
        [color]="'color-icon-success'"
      />
    </ng-container>
    <ng-template #uploadInProgress>
      <nz-progress
        class="circle-progress"
        [nzPercent]="'40'"
        [nzType]="'circle'"
        [nzStrokeWidth]="20"
      ></nz-progress>
    </ng-template>
    <div class="file-uploaded-info">
      <div>
        {{ loadingFilesCount() }} of {{ fileList().length }} File uploaded
      </div>
      <div>Description</div>
    </div>
  </div>
</ng-template> -->
<ng-template #customIcon let-file>
  <hrdx-upload-icon-item [fileName]="file.name" />
</ng-template>
