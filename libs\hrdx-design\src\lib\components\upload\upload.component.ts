import {
  Component,
  effect,
  HostBinding,
  input,
  model,
  OnDestroy,
  output,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  NzUploadChangeParam,
  NzUploadFile,
  NzUploadModule,
} from 'ng-zorro-antd/upload';
import { IconComponent } from '../icon';
import { ButtonComponent } from '../button';
import { UploadLabelComponent } from './upload-label/upload-label.component';
import { UploadFileComponent } from './upload-file/upload-file.component';
import { NzImageModule } from 'ng-zorro-antd/image';
import { NzProgressModule } from 'ng-zorro-antd/progress';
import { Upload } from './upload.models';
import { Observable } from 'rxjs';
import { NzCollapseModule } from 'ng-zorro-antd/collapse';
import { DisplayComponent } from '../display';
import { UploadIconItemComponent } from './upload-icon-item/upload-icon-item.component';

@Component({
  selector: 'hrdx-upload',
  standalone: true,
  imports: [
    CommonModule,
    NzUploadModule,
    IconComponent,
    ButtonComponent,
    UploadLabelComponent,
    UploadFileComponent,
    NzImageModule,
    NzProgressModule,
    NzCollapseModule,
    DisplayComponent,
    UploadIconItemComponent,
  ],
  templateUrl: './upload.component.html',
  styleUrl: './upload.component.less',
  host: {
    class: 'container',
  },
})
export class UploadComponent implements OnDestroy {
  @HostBinding('style.width') width = input<Upload['width']>('329px');
  @HostBinding('class.error') state = model<Upload['state']>('default');

  type = input<Upload['type']>('default');
  customContentUpload = input<string>('');

  fileType = input<string | string[] | undefined>([
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/pdf',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  ]);

  required = input<boolean>(false);
  multiple = input<boolean>(true);
  fileList = input<NzUploadFile[]>([]);
  fileListChange = output();
  change = output<NzUploadChangeParam>();
  previewFile = input<(file: NzUploadFile) => Observable<string>>();
  beforeUpload = input<(file: NzUploadFile) => boolean>();
  preview = output();
  fileTypeLabel = input<string>('PDF, XLS, XLSX');
  fileRemove = output<string>();
  hasDivider = input<boolean>(false);
  paddingBottomNone = input<boolean>(false);

  private resetStateTimeoutId: any;

  constructor() {
    // effect to change state from error to default
    effect(
      () => {
        const state = this.state();
        if (state !== 'error') return;
        const timeoutMs = 3000;
        this.resetStateTimeoutId = setTimeout(() => {
          this.state.set('default');
        }, timeoutMs);
      },
      { allowSignalWrites: true },
    );
  }

  ngOnDestroy(): void {
    if (this.resetStateTimeoutId) {
      clearTimeout(this.resetStateTimeoutId);
    }
  }

  removeFile(list: NzUploadFile[], file: NzUploadFile): void {
    const index = list.indexOf(file);
    if (index !== -1) {
      list.splice(index, 1);
    }

    this.fileRemove.emit(file.uid);
  }

  completedUpload(): boolean {
    return this.fileList().every((item) => item.status === 'done');
  }

  loadingFilesCount(): number {
    return this.fileList().filter((item) => item.status === 'uploading').length;
  }

  currentType = '';
  errorMsg = 'Error Message';
  maxFileSize = input<number>(100);
  stateChecked: Upload['state'] = 'default';
  _beforeUpload = (file: NzUploadFile) => {
    const fn = this.beforeUpload();
    if (!fn) {
      return false;
    }

    this.currentType = file.type ?? '';
    if (!this.fileType()?.includes(this.currentType)) {
      this.state.set('error');
      this.removeFile(this.fileList(), file);
      this.errorMsg = ERROR_MESSAGE.FILE_TYPE;
      return false;
    }

    if (this.convertKbToMb(file.size ?? 0) > this.maxFileSize()) {
      this.state.set('error');
      this.removeFile(this.fileList(), file);
      this.errorMsg = ERROR_MESSAGE.FILE_SIZE.replace(
        '{{size}}',
        `${this.maxFileSize()}`,
      );
      return false;
    }
    this.state.set('success');
    return fn(file);
  };

  convertKbToMb(fileSize: number) {
    return fileSize / 1024 / 1024;
  }

  formatBytes(bytes?: number, decimals = 2) {
    if (bytes) {
      if (!+bytes) return '0 Bytes';

      const k = 1024;
      const dm = decimals < 0 ? 0 : decimals;
      const sizes = [
        'Bytes',
        'KB',
        'MB',
        'GiB',
        'TiB',
        'PiB',
        'EiB',
        'ZiB',
        'YiB',
      ];

      const i = Math.floor(Math.log(bytes) / Math.log(k));

      return `${parseFloat((bytes / Math.pow(k, i)).toFixed(dm))} ${sizes[i]}`;
    }
    return '';
  }
}

enum ERROR_MESSAGE {
  FILE_SIZE = 'The file size exceeds the maximum allowed {{size}} MB',
  FILE_TYPE = 'Some thing wrong!, please check your file format',
}
