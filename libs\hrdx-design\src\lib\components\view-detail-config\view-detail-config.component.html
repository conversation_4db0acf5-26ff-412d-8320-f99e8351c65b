<div class="view-detail-config">
  <div class="details-table">
    <h3>Element result</h3>
    <div class="details-tool">
      <nz-input-group
        class="input-group"
        [nzPrefix]="suffixIconSearch"
        [nzSuffix]="inputClearTpl"
      >
        <input
          title="Search"
          class="search-input"
          type="text"
          nz-input
          [placeholder]="'Search'"
          [ngModel]="searchValue()"
          (ngModelChange)="onSearchValueChange($event)"
        />
      </nz-input-group>
      <ng-template #suffixIconSearch>
        <span class="search-icon">
          <hrdx-icon [icon]="'icon-magnifying-glass'"></hrdx-icon>
        </span>
      </ng-template>

      <ng-template #inputClearTpl>
        @if (searchValue()) {
          <hrdx-icon
            [icon]="'icon-close'"
            (click)="onSearchValueChange('')"
          ></hrdx-icon>
        }
      </ng-template>

      <!-- <div class="action">
        <hrdx-button
          [icon]="'icon-caret-left-bold'"
          [onlyIcon]="true"
          [type]="'ghost-gray'"
          (clicked)="onAction(true)"
          [disabled]="number() === 1"
        ></hrdx-button>
        <div class="action-change-tab">
          <span class="active-number">{{ number() }}</span
          >/<span>{{ details().length }}</span>
        </div>
        <hrdx-button
          [icon]="'icon-caret-right-bold'"
          [onlyIcon]="true"
          [type]="'ghost-gray'"
          (clicked)="onAction(false)"
          [disabled]="number() === details().length"
        ></hrdx-button>
      </div> -->
      <!-- <div class="tabset">
        <hrdx-tabs>
          @for (tab of data(); track $index) {
            <hrdx-tab
              [title]="getTabTitle($index)"
              [disabled]="false"
              (clicked)="onTabChange($index)"
            >
            </hrdx-tab>
          }
        </hrdx-tabs>
      </div> -->
      <nz-tabset [nzSize]="'small'" class="tabset">
        @for (tab of data(); track $index) {
          <nz-tab
            [nzTitle]="getTabTitle($index)"
            [nzDisabled]="false"
            (nzClick)="onTabChange($index)"
          >
          </nz-tab>
        }
      </nz-tabset>
    </div>
    <hrdx-tabs [selectedIndex]="selectedTabIndex()" [showTabBar]="false">
        @for (tab of data(); track $index) {
          <hrdx-tab>
            <ng-container
              [ngTemplateOutlet]="table"
              [ngTemplateOutletContext]="{ data: tab, headers: header() }"
            ></ng-container>
          </hrdx-tab>
        }
    </hrdx-tabs>
    <!-- <nz-carousel
      [nzEffect]="effect"
      [nzEnableSwipe]="false"
      #carousel
      [nzDots]="false"
    >
      @for (tab of data(); track $index) {
        <div nz-carousel-content>
          <ng-container
            [ngTemplateOutlet]="table"
            [ngTemplateOutletContext]="{ data: tab, headers: header() }"
          ></ng-container>
        </div>
      }
    </nz-carousel> -->

    <!--
    [total]="data.length"
        [pageIndex]="pageIndex()"
        [pageSize]="pageSize()"
        (pageSizeChange)="pageSize.set($event)"
        (pageIndexChange)="pageIndex.set($event)"
         -->
    <ng-template #table let-data="data" let-headers="headers">
      <hrdx-new-table
        [data]="data"
        [_hideRowAction]="true"
        [total]="data.length"
        [headers]="headers"
        [pageIndex]="pageIndex()"
        [pageSize]="pageSize()"
        (pageSizeChange)="pageIndex.set(1); pageSize.set($event)"
        (pageIndexChange)="pageIndex.set($event)"
        [showCreateDataTable]="false"
        (orderChange)="onOrderChange($event)"
        [scrollHeight]="'400px'"
        [showRowIndex]="true"
      >
        <hrdx-thead>
          @for (column of headers; track column.code) {
            <hrdx-th
              [resizable]="true"
              [width]="column.options?.tabular?.column_width"
              [align]="column.options?.tabular?.align ?? 'left'"
            >
              {{ column.title }}
            </hrdx-th>
          }
        </hrdx-thead>
        @for (item of setDataPagination(data); track $index) {
          <hrdx-tbody>
            @for (column of headers; track $index) {
              <hrdx-td>
                <hrdx-display
                  [type]="
                    column?.display_type?.key === 'Condition'
                      ? getDisplayType(item.code)
                      : column?.display_type?.key || 'Label'
                  "
                  [value]="item[column.code]"
                  [title]="column.title"
                  [href]="column.href"
                ></hrdx-display>
              </hrdx-td>
            }
          </hrdx-tbody>
        }
      </hrdx-new-table>
    </ng-template>
  </div>
</div>
