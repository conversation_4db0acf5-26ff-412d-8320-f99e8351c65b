@import '../../../themes/tokens.less';

.view-detail-config {
  display: flex;
  flex-direction: column;
  gap: @spacing-3;

  h3 {
    color: @color-text-primary;
    font-size: @font-size-medium;
    font-weight: @font-weight-semibold;
    line-height: @font-line-height-medium;
    margin: @spacing-0;
  }

  .details-table {
    height: 600px;
    margin-top: @spacing-3;
    display: flex;
    flex-direction: column;
    gap: @spacing-3;

    .details-tool {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: @spacing-4;

      .input-group {
        width: clamp(200px, 30%, 320px);
      }

      .action {
        display: flex;
        gap: @spacing-2;
        justify-content: center;
        align-items: center;

        .action-change-tab {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: @spacing-2;

          span {
            color: @color-text-primary;
            font-size: @font-size-base;
            font-style: normal;
            font-weight: @font-weight-regular;
            line-height: @font-line-height-base;

            &.active-number {
              display: flex;
              width: @font-line-height-2x-large;
              padding: @size-6 @size-12;
              flex-direction: column;
              justify-content: center;
              align-items: center;
              gap: @font-size-x-small;
              border-radius: @size-6;
              border: 1px solid @color-border-active;
              background: @color-bg-surface;
            }
          }
        }
      }
      .tabset {
        ::ng-deep {
          .ant-tabs-nav {
            margin-bottom: @spacing-0;
          }
          .ant-tabs-small > .ant-tabs-nav .ant-tabs-tab {
            padding-top: @spacing-0;
          }
          .ant-tabs-tab + .ant-tabs-tab {
            margin-left: @spacing-0;
          }
          .ant-tabs-tab {
            padding-left: @spacing-2;
            padding-right: @spacing-2;
          }
        }
      }
    }
  }
}
