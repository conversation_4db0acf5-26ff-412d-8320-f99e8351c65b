import {
  Component,
  input,
  model,
  signal,
  viewChild,
  OnInit,
  computed,
  effect, ChangeDetectionStrategy
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { PreviewTableComponent } from '../preview-table';
import { DisplayComponent } from '../display';
import { NzSafeAny } from 'ng-zorro-antd/core/types';
import { NzCarouselComponent, NzCarouselModule } from 'ng-zorro-antd/carousel';
import { NewTableModule } from '../new-table/new-table.module';
import { ButtonComponent } from '../button';
import { NzInputModule } from 'ng-zorro-antd/input';
import { FormsModule } from '@angular/forms';
import { IconComponent } from '../icon';
import {
  cloneDeep,
  filter,
  find,
  fromPairs,
  some,
  sortBy,
  toString,
} from 'lodash';
import { debounceTime, Subject } from 'rxjs';
import { TabsModule } from '../tabs';
import { NzTabsModule } from 'ng-zorro-antd/tabs';

@Component({
  selector: 'hrdx-view-detail-config',
  standalone: true,
  imports: [
    CommonModule,
    PreviewTableComponent,
    DisplayComponent,
    NzCarouselModule,
    NewTableModule,
    ButtonComponent,
    NzInputModule,
    FormsModule,
    IconComponent,
    TabsModule,
    NzTabsModule
  ],
  templateUrl: './view-detail-config.component.html',
  styleUrl: './view-detail-config.component.less',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ViewDetailConfigComponent implements OnInit {
  details = model<NzSafeAny[]>([]);
  settings = input<NzSafeAny[]>([]);
  searchValue = signal<string>('');

  carousel = viewChild<NzCarouselComponent>('carousel');
  effect = 'scrollx';
  number = signal<number>(1);
  pageIndex = signal<number>(1);
  pageSize = signal<number>(25);
  total = signal<number>(0);

  header = model<NzSafeAny[]>([
    {
      code: 'shortName',
      title: 'Element Short Name',
      data_type: {
        key: 'String',
        collection: 'data_types',
      },
      display_type: {
        key: 'Label',
        collection: 'field_types',
      },
    },
    {
      code: 'name',
      title: 'Element Name',
      data_type: {
        key: 'String',
        collection: 'data_types',
      },
      display_type: {
        key: 'Label',
        collection: 'field_types',
      },
    },
    {
      code: 'value',
      title: 'Amount',
      data_type: {
        key: 'Nmberic',
        collection: 'data_types',
      },
      display_type: {
        key: 'Condition',
        collection: 'field_types',
      },
    },
    {
      code: 'description',
      title: 'Description',
      data_type: {
        key: 'String',
        collection: 'data_types',
      },
      display_type: {
        key: 'Label',
        collection: 'field_types',
      },
    },
    {
      code: 'formula',
      title: 'Formula',
      data_type: {
        key: 'String',
        collection: 'data_types',
      },
      display_type: {
        key: 'Label',
        collection: 'field_types',
      },
    },
  ]);

  searchTerm = new Subject<string>();

  _details = signal<NzSafeAny[]>([]);

  constructor() {
    this.searchTerm
      .pipe(
        debounceTime(1500), // Wait 500ms after last emission
      )
      .subscribe((value) => {
        this.triggerSearch(value); // Call search function when user stops typing
      });
  }

  ngOnInit() {
    this._details.set(this.details());
  }

  data = computed(() => {
    const sortOrderMap = fromPairs(
      this.settings().map((s) => [s.code, s.listSortOrder]),
    );

    return this.details().map((detail) => {
      return sortBy(detail, (d) => sortOrderMap[d.code] || 0);
    });
  });

  // dataEffect = effect(() => {
  //   this.number();
  //   this.onSearchValueChange(this.searchValue());
  // }, { allowSignalWrites: true });

  onOrderChange(headers: NzSafeAny[]) {
    this.header.set(headers);
  }

  setDataPagination(data: NzSafeAny[]) {
    const startIndex = (this.pageIndex() - 1) * this.pageSize();
    const endIndex = startIndex + this.pageSize();
    const paginatedData = data.slice(startIndex, endIndex);
    return paginatedData;
  }

  searchComputed = effect(
    () => {
      this.onSearchValueChange(this.searchValue());
    },
    { allowSignalWrites: true },
  );

  triggerSearch = (_value: string) => {
    this.searchValue.set(_value);

    if (_value.length === 0) {
      this.details.set(this._details());
      this.total.set(this._details().length);
    } else {
      const _details = cloneDeep(this._details());
      const filteredDetails = _details.map((detail) => {
        return filter(detail, (row) =>
          some([row.shortName, row.name], (value) =>
            toString(value).toLowerCase().includes(_value.toLowerCase()),
          ),
        );
      });

      this.details.set(filteredDetails);
      // this.total.set(filteredDetails.length);
    }

    // Apply pagination after filtering
    const paginatedData = this.setDataPagination(this.details());
    this.details.set(paginatedData);
  };

  onSearchValueChange(value: string) {
    this.searchTerm.next(value);
    // this.pageIndex.set(1); // Reset to first page when searching
  }

  onAction(isPre: boolean) {
    if (isPre) {
      this.carousel()?.pre();
      this.number() > 1 && this.number.set(this.number() - 1);
    } else {
      this.carousel()?.next();
      this.number() < this.details().length &&
        this.number.set(this.number() + 1);
    }
  }

  selectedTabIndex = signal(0);
  onTabChange(index: number) {
    this.selectedTabIndex.set(index)
  }

  getTabTitle(index: number) {
    if(index === 0) return 'Master';
    return `Segment ${index}`;
  }

  getDisplayType(code: string) {
    const dataType = find(this.settings(), (d) => d.code === code)?.dataType;
    switch (dataType) {
      case 't004_datetime':
        return 'DD/MM/yyyy';
      case 't002_text':
        return 'Label';
      case 't001_num_dec_text':
        return 'Currency';
      default:
        return 'Label';
    }
  }
}
