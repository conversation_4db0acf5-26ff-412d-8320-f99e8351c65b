const formatCurrency = (value: string | number, options?: { precision?: number }) => {
  const { precision } = options ?? {};
  if(typeof precision === 'number') {
    value = Number(value).toFixed(precision);
  }
  let [integer, fractional] = value.toString().split('.') as (string | null)[];
  // if convert fractional part equal 0 than remove fractional part
  if (fractional && +fractional === 0) {
    fractional = null;
  }
  // only format integer part and keep fractional part
  integer = integer?.replace(/\B(?=(\d{3})+(?!\d))/g, ',') ?? null;
  return [integer, fractional].filter((part) => part).join('.');
};

export { formatCurrency };
