@import '../../../../node_modules/ng-zorro-antd/ng-zorro-antd.less';
@import './tokens.less';
@import './scroll.less';
// @tailwind base;
// @tailwind components;
// @tailwind utilities;

@font-family: 'Inter', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji',
  'Segoe UI Symbol', 'Noto Color Emoji';

// Override ng-zorro-antd variables
// https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/components/style/themes/default.less
@primary-color: @color-primary;
@border-radius-base: 6px;
@border-radius-action: @size-8;
@border-radius-button: @size-8;
@border-radius-card: @size-8;
@border-radius-input: @size-8;
@border-radius-popup: @size-8;
@border-radius-tag: @size-32;

@background-color-light: @color-grey-light1-white;

// Button
@btn-font-weight: 600;

// @btn-border-radius-base: @border-radius-base;
@btn-border-radius-base: @border-radius-button;

// Collapse
@collapse-panel-border-radius: @border-radius-card;

// Avatar
@avatar-size-base: @size-40;
@avatar-size-lg: @size-64;
@avatar-size-sm: @size-24;
@avatar-border-radius: @border-radius-card;
@avatar-bg: @color-bg-surface-info-fill;

// Tabs
@tabs-active-color: @color-text-info;
@tabs-card-active-color: @color-text-info;
@tabs-hover-color: @color-text-info;

// Card
@card-radius: @size-12;
@card-padding-base: @size-24;

//Tag
@warning-color: @color-text-warning;
@error-color: @color-text-error;
@processing-color: @color-icon-info;

// Tooltip
// Tooltip max width
// @tooltip-max-width: 250px;
// Tooltip text color
// @tooltip-color: @color-text-primary;
// Tooltip background color
// @tooltip-bg: @color-bg-surface;
// Tooltip arrow color
// @tooltip-arrow-color: @tooltip-bg;
@tooltip-border-radius: @size-6;

// Drawer
@drawer-header-padding: @size-16 @space-popup-margin;
@drawer-body-padding: @space-popup-margin;
@drawer-bg: @color-bg-surface;
@drawer-footer-padding-vertical: @size-16 @space-popup-margin;
@drawer-footer-padding-horizontal: @size-16 @space-popup-margin;
@drawer-header-close-size: @size-40;
@drawer-title-font-size: @font-size-large;
@drawer-title-line-height: @font-line-height-large;

// Card
@card-head-color: @color-text-title;
@card-head-background: @color-bg-surface;
@card-head-font-size: @font-size-medium;
@card-head-font-size-sm: @font-size-base;
@card-head-padding: @size-16;
@card-radius: @size-12;

// table
@table-row-hover-bg: @color-bg-surface-secondary;
@table-border-color: @color-border-primary;
@table-padding-vertical: 12px;
@table-footer-bg: white;
@table-header-bg: @color-bg-surface-hover;

.popup {
  .ant-modal-body {
    border-radius: @size-8;
  }

  .ant-modal-confirm-warning,
  .ant-modal-confirm-info,
  .ant-modal-confirm-success {
    .ant-modal-confirm-body {
      gap: 0;
      display: flex;
      flex-direction: column;
      align-items: center;

      > .anticon {
        margin-right: 0;
      }

      > *:first-child {
        font-size: @font-size-x-large * 2;
        padding: @spacing-4;
        border-radius: 100%;
      }

      .ant-modal-confirm-title {
        margin-top: @spacing-4;
        margin-bottom: @spacing-2;
        font-weight: @font-weight-semibold;
        font-size: @font-size-large;
      }

      .ant-modal-confirm-content {
        color: @color-text-secondary;
        font-size: @font-size-base;
        text-align: center;
        width: 100%;
        margin-left: 0;
        margin-top: 0;
      }
    }

    .ant-modal-confirm-btns {
      .ant-btn-primary {
        background-color: @color-bg-button-primary;
        border-color: @color-bg-button-primary;
        color: white;

        &:hover {
          background-color: @color-bg-button-primary-hover;
          border-color: @color-bg-button-primary-hover;
          color: white;
        }
      }
    }
  }

  .ant-modal-confirm-error {
    .ant-modal-confirm-body {
      gap: 0;
      display: flex;
      flex-direction: column;
      align-items: center;

      > .anticon {
        margin-right: 0;
      }

      > *:first-child {
        font-size: @font-size-x-large * 2;
        padding: @spacing-4;
        border-radius: 100%;
      }

      .ant-modal-confirm-title {
        margin-top: @spacing-4;
        margin-bottom: @spacing-2;
        font-weight: @font-weight-semibold;
        font-size: @font-size-large;
      }

      .ant-modal-confirm-content {
        color: @color-text-secondary;
        font-size: @font-size-base;
        text-align: center;
        width: 100%;
        margin-left: 0;
        margin-top: 0;
      }
    }

    .ant-modal-confirm-btns {
      .ant-btn-primary {
        background-color: @color-bg-button-destruction;
        border-color: @color-bg-button-destruction;
        color: white;

        &:hover {
          background-color: @color-bg-button-destruction-hover;
          border-color: @color-bg-button-destruction-hover;
          color: white;
        }
      }
    }
  }

  .popup-got-it {
    .ant-modal-confirm-btns {
      justify-content: center;

      button {
        flex: 1;
      }
    }
  }

  .ant-modal-confirm-btns {
    display: flex;
    margin-top: @spacing-6;
    justify-content: space-between;

    // gap: @spacing-2;
    button {
      flex: auto;
      height: @size-40;
      width: @size-1 * 178;

      &:hover,
      &:focus {
        border-color: @color-border-primary;
        color: @color-text-secondary;
        background-color: @color-bg-surface-hover;
      }
    }
  }
}

.popup-error {
  .ant-modal-confirm-body {
    > *:first-child {
      background-color: @color-bg-surface-error;
      color: @color-error-main;
    }
  }
}

.popup-confirm {
  .ant-modal-confirm-body {
    > *:first-child {
      background-color: @color-bg-surface-warning;
      color: @color-secondary1-main;
    }
  }
}

.popup-success {
  .ant-modal-confirm-body {
    > *:first-child {
      background-color: @color-bg-surface-success;
      color: @color-secondary2-main;
    }
  }
}

.popup-info {
  .ant-modal-confirm-body {
    > *:first-child {
      background-color: @color-bg-surface-info;
      color: @color-primary;
    }
  }
}

.hide-footer-btns .ant-modal-confirm-btns {
  margin-top: unset;
}

.custom {
  .ant-modal-confirm-title {
    display: none;
  }

  .ant-modal-confirm-body {
    .anticon {
      display: none;
    }
  }

  .ant-modal-confirm {
    .ant-modal-body {
      padding: @spacing-5 !important;
    }
  }

  .ant-modal-confirm-btns {
    display: none;
  }
}

/* BPMN */
:root {
  --color-disableAll: Silver;
  --color-highlight: LightSeaGreen;
  --color-possibleNext: MediumVioletRed;
}

.bpmn-type-event:hover,
.bpmn-type-gateway:hover,
.bpmn-type-activity:hover,
.bpmn-type-flow:hover {
  cursor: pointer;
}

.disablePointer.bpmn-type-event:hover,
.disablePointer.bpmn-type-gateway:hover,
.disablePointer.bpmn-type-activity:hover,
.disablePointer.bpmn-type-flow:hover {
  cursor: default;
}

/* ------------------------------------------------ DISABLE EVERYTHING ------------------------------------------------ */
/* SHAPE & EDGE */
.disableAll.bpmn-type-activity > *,
.disableAll.bpmn-type-event > *,
.disableAll.bpmn-type-gateway > *,
.disableAll.bpmn-type-flow > * {
  stroke: var(--color-disableAll);
}

/* ICON */
.disableAll.bpmn-type-gateway > path:nth-child(2),
.disableAll.bpmn-type-flow > path:nth-child(3) {
  fill: var(--color-disableAll);
}

/* LABEL */
.disableAll.bpmn-type-activity > g > foreignObject > div > div > div,
.disableAll.bpmn-type-event > g > foreignObject > div > div > div,
.disableAll.bpmn-type-gateway > g > foreignObject > div > div > div,
.disableAll.bpmn-type-flow > g > foreignObject > div > div > div {
  color: var(--color-disableAll) !important;
}

/* ------------------------------------------------ HIGHLIGHT ------------------------------------------------ */
/* SHAPE */
.highlight.bpmn-type-activity > rect:first-child,
.highlight.bpmn-type-event > ellipse:first-child,
.highlight.bpmn-type-gateway > path:first-child,
.highlight.bpmn-type-flow > * {
  stroke: var(--color-highlight);
  filter: drop-shadow(0 0 0.75rem var(--color-highlight));
}

/* ICON */
.highlight.bpmn-type-gateway > :not(:first-child) {
  stroke: var(--color-highlight);
}

.highlight.bpmn-type-gateway > path:nth-child(2),
.highlight.bpmn-type-flow > path:nth-child(3) {
  fill: var(--color-highlight);
}

/* ------------------------------------------------ POSSIBLE NEXT ------------------------------------------------ */
/* SHAPE & EDGE */
.possibleNext.bpmn-type-activity > *,
.possibleNext.bpmn-type-event > *,
.possibleNext.bpmn-type-gateway > *,
.possibleNext.bpmn-type-flow > * {
  stroke: var(--color-possibleNext) !important;
}

/* ICON */
.possibleNext.bpmn-type-gateway > path:nth-child(2),
.possibleNext.bpmn-type-flow > path:nth-child(3) {
  fill: var(--color-possibleNext) !important;
}

/* LABEL */
.possibleNext.bpmn-type-activity > g > foreignObject > div > div > div,
.possibleNext.bpmn-type-event > g > foreignObject > div > div > div,
.possibleNext.bpmn-type-gateway > g > foreignObject > div > div > div,
.possibleNext.bpmn-type-flow > g > foreignObject > div > div > div {
  color: var(--color-possibleNext) !important;
}

// Radio
@radio-size: @size-16;
@radio-dot-size: @size-6;
@radio-dot-color: @color-base-neutral-white;
@radio-dot-disabled-color: @color-bg-button-text-disabled;

// Checkbox
@checkbox-size: @size-16;
@checkbox-border-width: @size-1;
@checkbox-border-radius: @size-4;

.ant-checkbox {
  .ant-checkbox-inner {
    &:after {
      font-family: 'icomoon' !important;
      font-style: normal;
      font-weight: normal;
      font-variant: normal;
      text-transform: none;
      line-height: 1;
      -webkit-font-smoothing: antialiased;
      font-size: @font-size-base;
      color: @color-base-neutral-1;
      border: none;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }
}

.ant-checkbox-checked {
  &:hover {
    .ant-checkbox-inner {
      background-color: @color-primary-dark;
      border-color: @color-primary-dark;
    }
  }

  .ant-checkbox-inner {
    background-color: @color-primary-main;
    border-color: @color-primary-main;

    &:after {
      content: '\ebc2';
    }
  }
}

.ant-checkbox-indeterminate {
  &:hover {
    .ant-checkbox-inner {
      background-color: @color-primary-dark;
      border-color: @color-primary-dark;
    }
  }

  .ant-checkbox-inner {
    background-color: @color-primary-main;
    border-color: @color-primary-main;

    &:after {
      content: '\f025';
      background-color: @color-primary-main;
    }
  }
}

.ant-checkbox {
  &:not(.ant-checkbox-checked, .ant-checkbox-indeterminate) {
    &:hover {
      .ant-checkbox-inner {
        border-color: @color-border-active;
      }
    }

    .ant-checkbox-inner {
      border-color: @color-border-primary;
    }
  }

  .ant-checkbox-inner {
    width: @size-20;
    height: @size-20;
  }

  &.ant-checkbox-disabled {
    .ant-checkbox-inner {
      border-color: @color-border-secondary;
      background-color: @color-bg-surface-disabled;

      &:after {
        // border-color: @color-icon-disabled;
        color: @color-icon-disabled;
      }
    }

    + span {
      color: @color-text-disabled;
    }
  }
}

.ant-checkbox-wrapper {
  color: @color-text-primary;

  &.cdk-focused {
    .ant-checkbox .ant-checkbox-inner {
      box-shadow: @elevation-checkbox_focus;
    }
  }
}

/// ---------- Datepicker ---------- ///

.cdk-overlay-container {
  .ant-picker-dropdown.dynamic-field--field-datepicker--range-picker-dropdown {
    margin-left: -@size-12 !important;
    margin-top: @size-4 + 1 !important;
    margin-bottom: @size-4 + 1 !important;
    margin-right: -@spacing-3;

    .ant-picker-cell-in-view.ant-picker-cell-today
      .ant-picker-cell-inner::before {
      border: none;
    }

    .ant-picker-cell-in-view.ant-picker-cell-today .ant-picker-cell-inner {
      color: @color-primary-main;
      font-weight: @font-weight-semibold;
      font-size: @font-size-base;
    }

    .ant-picker-cell-inner {
      font-weight: @font-weight-medium;
    }

    .ant-picker-today-btn {
      font-weight: @font-weight-semibold;
      color: @color-primary-main;
    }

    .ant-picker-cell-in-range::before,
    .ant-picker-cell-range-start::before,
    .ant-picker-cell-range-end::before {
      display: none;
    }

    .ant-picker-cell-in-range {
      background-color: @color-bg-surface-active;
    }

    .ant-picker-cell-in-range:hover,
    .ant-picker-cell-range-hover-end {
      .ant-picker-cell-inner::after {
        display: none;
      }
    }

    .ant-picker-cell-selected,
    .ant-picker-cell-range-end {
      background-color: @color-primary-main;
      color: @color-base-neutral-white;

      .ant-picker-cell-inner {
        color: @color-base-neutral-white;
      }

      &::after {
        display: none;
      }
    }

    .ant-picker-content th,
    .ant-picker-content td.ant-picker-cell {
      width: @size-40;
      height: @size-36;

      .ant-picker-cell-inner {
        background-color: unset;
      }
    }

    .ant-picker-cell:not(
        .ant-picker-cell-range-start,
        .ant-picker-cell-range-end,
        .ant-picker-cell-in-range
      ):hover {
      border-radius: @border-radius-button;
      background-color: @color-bg-surface-hover;
    }

    .ant-picker-cell-selected {
      border-radius: @border-radius-button;
    }

    &:has(.ant-picker-cell-range-end) {
      .ant-picker-cell-range-start {
        border-radius: @border-radius-button @size-0 @size-0
          @border-radius-button;
      }
    }

    .ant-picker-cell-range-end {
      border-radius: @size-0 @border-radius-button @border-radius-button @size-0;
    }

    .ant-picker-content td.ant-picker-cell-in-range {
      border-radius: unset;
    }

    .ant-picker-date-panel {
      min-width: 280px;
      width: unset;
    }

    .ant-picker-cell-in-view.ant-picker-cell-selected .ant-picker-cell-inner {
      color: @color-base-neutral-white;
    }

    .ant-picker-panel {
      border: unset;

      .ant-picker-footer {
        border-top: unset;
      }
    }

    .ant-picker-header {
      border-bottom: unset;
    }

    .ant-picker-content th {
      font-weight: @font-weight-medium;
    }

    .ant-picker-header-view {
      font-weight: @font-weight-semibold;
    }

    .ant-picker-panel-container {
      border-radius: @border-radius-button;
    }

    // .ant-picker-cell-range-end::before,
    // .ant-picker-cell-range-start::before {
    //   display: none;
    // }

    .ant-picker-year-panel,
    .ant-picker-month-panel {
      width: @size-1 * 312;
      padding: @spacing-4;
      .ant-picker-body {
        padding: unset;
        table.ant-picker-content {
          height: @size-1 * 160;

          .ant-picker-cell {
            padding: @spacing-1 @spacing-0;
            background: unset;
            border-radius: @border-radius-action_item;

            &-inner {
              width: 100%;
              height: 100%;
              display: flex;
              align-items: center;
              justify-content: center;
            }

            &.ant-picker-cell-selected {
              .ant-picker-cell-inner {
                background-color: @color-primary-main;
              }
            }
          }
        }
      }
    }
  }
}

// Input styles for placeholder
.ant-input,
// Regular inputs
.ant-picker-input>input,
// Datepicker input
.ant-input-number-input,
// Number input
.ant-input-textarea textarea,
// Textarea
.ant-select-selection-placeholder,
// Select placeholder
.ant-select-selection-item // Selected item in select
{
  &::placeholder {
    color: @color-text-hint;
    font-size: @font-size-base;
    font-weight: @font-weight-regular;
    opacity: 1;
  }

  // For older browsers
  &::-webkit-input-placeholder {
    color: @color-text-hint;
    font-size: @font-size-base;
    font-weight: @font-weight-regular;
  }

  &:-ms-input-placeholder {
    color: @color-text-hint;
    font-size: @font-size-base;
    font-weight: @font-weight-regular;
  }

  &::-moz-placeholder {
    color: @color-text-hint;
    font-size: @font-size-base;
    font-weight: @font-weight-regular;
    opacity: 1;
  }
}

// Additional styles specifically for select placeholder
.ant-select-selection-placeholder {
  color: @color-text-hint !important;
  font-size: @font-size-base !important;
  font-weight: @font-weight-regular !important;
}

// Input
@input-placeholder-color: @color-text-hint;
@input-color: @color-text-primary;
@input-border-color: @color-border-primary;
@input-hover-border-color: @color-border-active;

// Form control styles
.hrdx-form {
  // Input styles
  .ant-input,
  //.ant-input-number,
  .ant-picker {
    height: @size-40 !important;
    line-height: @size-40 !important;
    padding: @spacing-2 @spacing-3;
    border-radius: @size-8;
  }

  .ant-input-number {
    height: @size-40 !important;
    line-height: @size-40 !important;
    padding: unset !important;
    border-radius: @size-8;
  }

  // Select styles
  .ant-select:not(.ant-select-customize-input) {
    .ant-select-selector {
      height: @size-40 !important;
      padding: 0 @spacing-3 !important;
      border-radius: @size-8;
      display: flex;
      align-items: center;
      border: 1px solid @color-border-primary;

      .ant-select-selection-search-input,
      .ant-select-selection-item,
      .ant-select-selection-placeholder {
        height: 100%;
        line-height: @size-40 !important;
        display: flex;
        align-items: center;
      }
    }
  }

  // Multiple select
  .ant-select-multiple .ant-select-selector {
    min-height: @size-40 !important;
    height: auto !important;
    padding: @spacing-1 @spacing-3;

    .ant-select-selection-item {
      height: @size-24 !important;
      line-height: @size-22 !important;
      margin-top: @spacing-1;
      margin-bottom: @spacing-1;
    }
  }

  .ant-select-focused:not(.ant-select-disabled).ant-select:not(
      .ant-select-customize-input
    )
    .ant-select-selector {
    border-color: @color-border-active;
    box-shadow: @elevation-input_focus;
  }

  // label
  .ant-form-item-label {
    color: @color-text-secondary;
    font-size: @font-size-base;
    font-weight: @font-weight-semibold;
    line-height: @font-line-height-small;
  }

  // disabled input
  .ant-input-disabled {
    border-radius: @size-8;
    background-color: @color-bg-surface-disabled;
  }

  // disable input
  .ant-input[disabled] {
    background-color: @color-bg-surface-secondary;
    color: @color-text-primary;
  }

  label {
    color: @color-text-label;
  }
}

// Datepicker header buttons customization
.ant-picker-header {
  // Previous year button (double left)
  .ant-picker-header-super-prev-btn {
    &::before {
      font-family: 'icomoon' !important;
      content: '\eb44'; // icon-caret-double-left-bold
    }

    // Hide default icon
    > span {
      display: none;
    }
  }

  // Previous month button (single left)
  .ant-picker-header-prev-btn {
    &::before {
      font-family: 'icomoon' !important;
      content: '\eb4c'; // icon-caret-left-bold
    }

    // Hide default icon
    > span {
      display: none;
    }
  }

  // Next month button (single right)
  .ant-picker-header-next-btn {
    &::before {
      font-family: 'icomoon' !important;
      content: '\eb56'; // icon-caret-right-bold
    }

    // Hide default icon
    > span {
      display: none;
    }
  }

  // Next year button (double right)
  .ant-picker-header-super-next-btn {
    &::before {
      font-family: 'icomoon' !important;
      content: '\eb46'; // icon-caret-double-right-bold
    }

    // Hide default icon
    > span {
      display: none;
    }
  }

  // Common styles for all header buttons
  .ant-picker-header-super-prev-btn,
  .ant-picker-header-prev-btn,
  .ant-picker-header-next-btn,
  .ant-picker-header-super-next-btn {
    color: @color-text-secondary;
    display: flex;
    align-items: center;
    justify-content: center;
    visibility: visible;

    &:hover {
      color: @color-text-active;
    }

    font-size: @font-size-medium;
  }
}

// Datepicker header buttons customization
.ant-picker-header {
  // Previous year button (double left)
  .ant-picker-header-super-prev-btn {
    &::before {
      font-family: 'icomoon' !important;
      content: '\eb44'; // icon-caret-double-left-bold
    }

    // Hide default icon
    > span {
      display: none;
    }
  }

  // Previous month button (single left)
  .ant-picker-header-prev-btn {
    &::before {
      font-family: 'icomoon' !important;
      content: '\eb4c'; // icon-caret-left-bold
    }

    // Hide default icon
    > span {
      display: none;
    }
  }

  // Next month button (single right)
  .ant-picker-header-next-btn {
    &::before {
      font-family: 'icomoon' !important;
      content: '\eb56'; // icon-caret-right-bold
    }

    // Hide default icon
    > span {
      display: none;
    }
  }

  // Next year button (double right)
  .ant-picker-header-super-next-btn {
    &::before {
      font-family: 'icomoon' !important;
      content: '\eb46'; // icon-caret-double-right-bold
    }

    // Hide default icon
    > span {
      display: none;
    }
  }

  // Common styles for all header buttons
  .ant-picker-header-super-prev-btn,
  .ant-picker-header-prev-btn,
  .ant-picker-header-next-btn,
  .ant-picker-header-super-next-btn {
    color: @color-text-secondary;
    display: flex;
    align-items: center;
    justify-content: center;
    visibility: visible;

    &:hover {
      color: @color-text-active;
    }

    font-size: @font-size-medium;
  }
}
