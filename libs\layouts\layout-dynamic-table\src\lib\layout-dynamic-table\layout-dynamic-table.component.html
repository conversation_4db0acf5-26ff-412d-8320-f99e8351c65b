<nz-layout>
  <nz-content class="layout-dynamic-table__content" #content>
    <!-- expand filter -->
    <div class="expand-filter">
      <ng-container [ngTemplateOutlet]="expandFilterTemplate"></ng-container>
    </div>

    <ng-container *ngIf="filterApplied()">
      <!-- tool table -->

      <div class="tool-table">
        <div class="left">Matching Results</div>
        <div class="right">
          @for (tool of toolTable(); track tool) {
            <hrdx-button
              [type]="tool.type ?? 'tertiary'"
              [onlyIcon]="tool.title ? false : true"
              [title]="tool.title"
              [isLeftIcon]="tool.title ? true : false"
              [leftIcon]="tool.icon"
              [icon]="tool.icon"
              (clicked)="onToolTableClick(tool.id)"
              [disabled]="tool.disabled"
            >
            </hrdx-button>
          }
        </div>
      </div>

      <!-- table toasts -->
      <div
        class="toasts"
        *ngIf="filterApplied() && data() && data().length > 0"
      >
        @for (toast of tableToasts(); track $index) {
          <div [ngClass]="['toast', toast.type ?? 'info']">
            <div class="toast-icon">
              <hrdx-icon [icon]="toast.icon ?? 'icon-info'"></hrdx-icon>
            </div>

            <div class="toast-content">
              <h3 *ngIf="toast.title">{{ toast.title }}</h3>
              <span>{{ toast.text }}</span>
            </div>
          </div>
        }
      </div>

      <!-- table -->
      <hrdx-new-table
        [data]="data()"
        [total]="total()"
        [loading]="loading()"
        [pageIndex]="pageIndex()"
        [pageSize]="pageSize()"
        (selectedItemChange)="listOfSelectedItems.set($event)"
        (pageSizeChange)="onPageSizeChange($event)"
        (pageIndexChange)="pageIndex.set($event)"
        [showCheckbox]="true"
        [scrollHeight]="'500px'"
        [showActionHeader]="true"
        [showCreateDataTable]="false"
        [_hideRowAction]="true"
        *ngIf="filterApplied()"
        (onScroll)="onScroll()"
        #new_table
      >
        <hrdx-thead>
          @for (column of customHeaders(); track column.code) {
            <hrdx-th
              [width]="column.options?.tabular?.column_width"
              [fixedLeft]="column.pinned"
              [align]="column.options?.tabular?.align ?? 'left'"
              [showSort]="column.show_sort ?? false"
              class="table-thead"
            >
              {{ column.title }}
            </hrdx-th>
          }
        </hrdx-thead>

        <ng-container>
          @for (row of data(); track $index; let rowIdx = $index) {
            <hrdx-tbody>
              @for (
                column of customHeaders();
                track $index;
                let colIdx = $index
              ) {
                <hrdx-td>
                  <hrdx-display
                    [type]="'Tooltip'"
                    [value]="holiday"
                    [title]="column.title"
                    *ngIf="
                      isHoliday(row, column) as holiday;
                      else defaultDisplay
                    "
                  ></hrdx-display>
                  <ng-template #defaultDisplay>
                    <hrdx-display
                      [type]="column?.display_type?.key || 'Label'"
                      [value]="getCellValue(row, column)"
                      [title]="column.title"
                      (changeValue)="onChangeValueCell($event, row, column)"
                      [options$]="
                        column?.display_type?.options$
                          ? column?.display_type?.options$(rowIdx, colIdx)
                          : undefined
                      "
                      [href]="column.href"
                      [props]="getCellDisplayProps(column?.display_type?.key)"
                      #defaultDisplay
                    ></hrdx-display>
                  </ng-template>
                </hrdx-td>
              }
            </hrdx-tbody>
          }
        </ng-container>

        <ng-container selected-actions [ngTemplateOutlet]="selectedActions">
        </ng-container>
      </hrdx-new-table>
    </ng-container>
  </nz-content>
</nz-layout>

<ng-template #selectedActions>
  @if (actionsMany()) {
    @for (action of actionsMany(); track action.id) {
      <hrdx-button
        [type]="action.type"
        [title]="action.title"
        [leftIcon]="action.icon"
        [isLeftIcon]="true"
        [size]="'xsmall'"
        (clicked)="onActionsManyClick(action.id)"
        [isLoading]="currentActionLoading() === action.id"
      />
    }
  }
</ng-template>

<!-- expand filter template -->
<ng-template #expandFilterTemplate>
  <div class="expand-filter-wrapper">
    <lib-layout-expand-filter
      [config]="functionSpec().filter_config"
      (submitValue)="filterSubmit($event)"
      [isGroupCollapse]="false"
    ></lib-layout-expand-filter>
  </div>
</ng-template>
