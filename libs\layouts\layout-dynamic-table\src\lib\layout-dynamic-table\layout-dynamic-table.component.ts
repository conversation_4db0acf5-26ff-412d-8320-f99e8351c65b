import { CommonModule } from '@angular/common';
import {
  Component,
  computed,
  effect,
  inject,
  signal,
  viewChild,
} from '@angular/core';
import { Router } from '@angular/router';
import { DynamicFormService } from '@hrdx-fe/dynamic-features';
import {
  LayoutExpandFilterComponent,
  LayoutTableStore,
} from '@hrdx-fe/layout-simple-table';
import {
  ApiConfig,
  BffService,
  Data,
  FunctionSpec,
  getValue,
  LayoutCommon,
  LayoutCommonComponent,
  PaginateData,
} from '@hrdx-fe/shared';
import {
  ButtonComponent,
  DisplayComponent,
  IconComponent,
  ModalComponent,
  NewTableComponent,
  NewTableModule,
  ToastMessageComponent,
  TooltipComponent,
} from '@hrdx/hrdx-design';
import { CondOperator, QueryFilter } from '@nestjsx/crud-request';
import { cloneDeep, isArray, isEmpty, isNil, uniqueId } from 'lodash';
import * as moment from 'moment';
import { NzSafeAny } from 'ng-zorro-antd/core/types';
import { NzLayoutModule } from 'ng-zorro-antd/layout';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { catchError, map, of, switchMap, tap } from 'rxjs';

@Component({
  selector: 'lib-layout-dynamic-table',
  standalone: true,
  imports: [
    CommonModule,
    NzLayoutModule,
    NewTableModule,
    LayoutExpandFilterComponent,
    ButtonComponent,
    DisplayComponent,
    NzModalModule,
    IconComponent,
    TooltipComponent,
  ],
  providers: [ModalComponent, ToastMessageComponent],
  templateUrl: './layout-dynamic-table.component.html',
  styleUrl: './layout-dynamic-table.component.less',
})
export class LayoutDynamicTableComponent
  extends LayoutCommonComponent
  implements LayoutCommon
{
  #store = inject(LayoutTableStore);
  modalComponent = inject(ModalComponent);
  dynamicService = inject(DynamicFormService);
  toast = inject(ToastMessageComponent);
  router = inject(Router);

  bffService = inject(BffService);
  loading = signal(false);
  filterValue = signal<Record<string, NzSafeAny> | null>(null);

  expandFilter = viewChild(LayoutExpandFilterComponent);

  // set current function spec
  effectFunctionSpec = effect(
    () => this.#store.setCurrentFunctionSpec(this.functionSpec()),
    { allowSignalWrites: true },
  );

  // layout options
  layoutOptions = computed(() => this.functionSpec().layout_options);
  toolTable = computed(() => {
    const tools = this.layoutOptions()?.tool_table ?? [];
    return tools.filter(tool => this.checkPermission(tool.id))
  });
  actionsMany = computed(() => {
    const actions = this.layoutOptions()?.actions_many ?? [];
    return actions.filter(action => this.checkPermission(action.id))
  });
  actionsManyHandler = computed(
    () => this.layoutOptions()?.actions_many_handler,
  );
  headers = computed(() => this.functionSpec().local_fields ?? []);
  customGetListApi = computed(() => this.layoutOptions()?.custom_get_list_api);
  isGetListByPost = computed(() => this.customGetListApi()?.method === 'POST');
  tableToasts = computed(() => this.layoutOptions()?.table_toasts ?? []);
  customUpdateApi = computed(() => this.layoutOptions()?.custom_update_api);

  // build filter queries
  filterQuery = computed(() => {
    const filterMapping = this.functionSpec()?.filter_config?.filterMapping;
    const filterValue = this.filterValue() ?? {};

    const res = this.buildFilterQuery(filterMapping, filterValue);
    if (this.defaultFilter().length > 0) {
      res.push(...this.defaultFilter());
    }

    return res;
  });

  buildFilterQuery(
    mapping: { field: string; operator: CondOperator; valueField: string }[],
    value: Record<string, NzSafeAny>,
  ) {
    const filterQueries = mapping?.map((f) =>
      this.mappingQueryFilter(f, value),
    );

    return filterQueries.filter(this.isValidQueryFilter) as QueryFilter[];
  }

  mappingQueryFilter(
    mapping: { field: string; operator: CondOperator; valueField: string },
    value: Record<string, NzSafeAny>,
  ) {
    if (!mapping || !mapping?.valueField) return null;
    const { field, operator, valueField } = mapping;
    return {
      field,
      operator,
      value: getValue(value, valueField.split('.')),
    } as QueryFilter;
  }

  isValidQueryFilter(query: QueryFilter | null) {
    if (!query) return false;
    const { value } = query;
    if (!isArray(value) && !isNil(value) && value !== '') return true;
    if (isArray(value) && value.length > 0) return true;
    return false;
  }

  // get data from backend
  data = signal<Data[]>([]);
  total = signal(0);
  pageIndex = signal<number>(1);
  pageSize = signal<number>(25);

  onPageSizeChange = (pageSize: number) => {
    this.pageSize.set(pageSize);
    this.pageIndex.set(1);
  };

  refreshData = signal(false);

  dataEffect = effect(
    () => {
      if (!this.filterApplied()) return;
      this.refreshData();
      const url = this.customGetListApi()?.url ?? this.url();
      if (!url) {
        this.expandFilter()?.isLoading.set(false);
        return;
      }
      if (this.isGetListByPost()) {
        const body = {
          ...(this.filterValue() ?? {}),
          pageIndex: this.pageIndex(),
          pageSize: this.pageSize(),
        };
        this.getListDataByPOST(url, body);
      } else {
        this.getListDataByGET(
          url,
          this.pageIndex(),
          this.pageSize(),
          this.filterQuery(),
        );
      }
    },
    { allowSignalWrites: true },
  );

  getListDataByPOST(url: string, body: Record<string, NzSafeAny>) {
    of(url)
      .pipe(
        tap(() => this.loading.set(true)),
        switchMap((url) => this.bffService.Post<PaginateData>(url, body)),
        catchError((err) => {
          return this.handleGetListError(err);
        }),
        tap(() => {
          this.expandFilter()?.isLoading.set(false);
          this.loading.set(false);
        }),
      )
      .subscribe((d) => {
        this.handleGetListComplete(d);
      });
  }

  getListDataByGET(
    url: string,
    pageIndex = 1,
    pageSize = 25,
    filterQuery: QueryFilter[],
  ) {
    of(url)
      .pipe(
        tap(() => this.loading.set(true)),
        switchMap((url) =>
          this.bffService.getPaginate(url, pageIndex, pageSize, filterQuery),
        ),
        catchError((err) => {
          return this.handleGetListError(err);
        }),
        tap(() => {
          this.expandFilter()?.isLoading.set(false);
          this.loading.set(false);
        }),
      )
      .subscribe((d) => {
        this.handleGetListComplete(d);
      });
  }

  handleGetListError(err: NzSafeAny) {
    this.toast.showToast('error', 'Error', err?.error?.message ?? err);
    return of({ data: [], total: 0, count: 0 });
  }

  originalData = signal<Data[]>([]);
  handleGetListComplete(data: PaginateData) {
    let _data = [];
    if (Array.isArray(data) && !data.data) {
      _data = data;
      this.total.set(data.length);
    } else {
      _data = data.data;
      this.total.set(data.total);
    }
    // if data don't have id to check reset, should add _id to data
    if (isNil(_data[0]?.id)) {
      _data = _data.map((item) => {
        return {
          ...item,
          _id: uniqueId(),
        };
      });
    }
    this.originalData.set(structuredClone(_data));
    this.data.set(_data);
    // this.getOptionListPerRecord();
  }

  // TODO: should define an interface for custom headers
  customHeaders = computed(() => {
    const data = this.data()?.[0];
    if (!data) return [];

    console.log(this.filterValue(), this.originalData());

    const headers: FunctionSpec['local_fields'] = [
      {
        combine_keys: ['employeeCode', 'employeeRecordNumber', 'employeeName'],
        code: 'employeeInfo',
        title: 'Employee Information (ID - ERD - Name)',
        description: '',
        pinned: true,
        data_type: {
          key: 'string',
        },
        display_type: {
          key: 'Label',
        },
        options: {
          tabular: {
            column_width: 20,
          },
        },
      },
    ];
    const workingDaysHeaders = data?.['days'].map(
      (day: Record<string, NzSafeAny>) => {
        return {
          code: day?.['day'],
          key: 'COL' + timestampToDate(day?.['day'], 'yyyyMMDD'),
          title: timestampToDate(day?.['day']),
          description:
            day?.['tssettingHolidayCalendarName'] ??
            day?.['tssettingHolidayCalendarShortName'],
          data_type: {
            key: 'string',
          },
          display_type: {
            key: 'Select',
            options$: (idx: number, colIdx: number) => {
              return of({
                filterValue: this.filterValue(),
                data: this.data(),
              }).pipe(
                switchMap(({ filterValue, data }) => {
                  if (!filterValue?.['startDate']) return of(undefined);

                  const url = '/api/ca-working-hours/list-data';
                  const filterMapping = [
                    {
                      field: 'startDate',
                      operator: '$eq',
                      valueField: 'startDate',
                    },
                    {
                      field: 'endDate',
                      operator: '$eq',
                      valueField: 'endDate',
                    },
                    {
                      field: 'employeeCode',
                      operator: '$eq',
                      valueField: 'employeeCode',
                    },
                    {
                      field: 'employeeRecordNumber',
                      operator: '$eq',
                      valueField: 'employeeRecordNumber',
                    },
                  ] as NzSafeAny;

                  const startDate = filterValue?.['startDate'];
                  const endDate = filterValue?.['endDate'];
                  const query = this.buildFilterQuery(filterMapping, {
                    startDate,
                    endDate,
                    employeeCode: data[idx]?.['employeeCode'],
                    employeeRecordNumber: data[idx]?.['employeeRecordNumber'],
                  });
                  return this.bffService.getPaginate(url, 1, 1000, query).pipe(
                    map((d) => {
                      const data = (d ?? []) as NzSafeAny;
                      const item1 = data[colIdx]?.['listData'];
                      return item1.map((item2: NzSafeAny) => ({
                        label: `${item2.shortName} (${item2.code})`,
                        value: item2.code,
                      }));
                    }),
                  );
                }),
              );
            },
          },
          options: {
            tabular: {
              column_width: 15,
            },
          },
        };
      },
    );
    return [...headers, ...workingDaysHeaders];
  });

  getCombineKeysValue(data: Record<string, NzSafeAny>, keys: string[]) {
    return keys
      .map((key) => data[key])
      .filter((v) => {
        if (typeof v === 'number') return v;
        return !isEmpty(v);
      })
      .join(' - ');
  }

  getCellValue(data: Record<string, NzSafeAny>, col: NzSafeAny) {
    if (col?.combine_keys) {
      return this.getCombineKeysValue(data, col?.combine_keys);
    } else if (col?.display_type?.key === 'Select') {
      // TODO: hard code should allow define key in tool
      const value = data?.['days']?.find(
        (item: Record<string, NzSafeAny>) => item?.['day'] === col?.code,
      );
      return value?.['caWorkingHourId'];
    } else {
      return data[col?.code];
    }
  }

  isHoliday(data: Record<string, NzSafeAny>, col: NzSafeAny) {
    if (col?.display_type?.key !== 'Select') return null;
    const value = data?.['days']?.find(
      (item: Record<string, NzSafeAny>) => item?.['day'] === col?.code,
    );
    return (
      value?.['tssettingHolidayCalendarName'] ??
      value?.['tssettingHolidayCalendarShortName']
    );
  }

  // TODO: should define load option list in tool
  optionListPerRecord = signal<
    Record<number, Record<string, { label: string; value: NzSafeAny }[]>>
  >({});

  // for tool table
  onToolTableClick(toolId: string) {
    const btn = this.toolTable()?.find((btn) => btn.id === toolId);
    if (btn?.href) {
      this.router.navigate([btn.href]);
    }
    switch (toolId) {
      case 'export': {
        this.handleExport();
        break;
      }
      default: {
        break;
      }
    }
  }

  // select items
  selectedItem = signal<Data | null>(null);
  selectedId = computed(() => this.selectedItem()?.id);
  listOfSelectedItems = signal<Data[]>([]);

  // action many
  onActionsManyClick(actionId: string) {
    this.currentActionLoading.set(actionId);
    switch (actionId) {
      case 'edit': {
        this.onSave(this.listOfSelectedItems());
        break;
      }
      case 'reset': {
        this.onResetItems(this.listOfSelectedItems(), this.originalData());
        // after reset set empty list of selected items.
        this.listOfSelectedItems.set([]);
        break;
      }
      default: {
        // this.handleActionsMany(actionId);
        break;
      }
    }
  }

  currentActionLoading = signal<string | null>(null);
  async onSave(items: Data[]) {
    let url = this.url();
    // TODO: should transform items into body here
    // const transformItems = items.map((item) => {
    //   const transformItem: NzSafeAny = {
    //     ...item,
    //     days: item['days'].filter(
    //       (day: { day: string; caWorkingHourId: string }) =>
    //         day?.caWorkingHourId,
    //     ),
    //   };
    //   return transformItem;
    // });

    const customApi = this.customUpdateApi();
    if (customApi) {
      url = customApi.url;
    }

    const body = { tsAjustWorkingHours: items };

    if (!url) {
      this.currentActionLoading.set(null);
      return;
    }

    this.bffService.Post(url, body).subscribe({
      next: (data) => {
        this.currentActionLoading.set(null);
        this.toast.showToast('success', '', 'Saved Successfully');
        this.refreshData.update((prev) => !prev);
      },
      error: (err) => {
        this.currentActionLoading.set(null);
        this.toast.showToast('error', 'Error', err.error?.message);
      },
    });
  }

  onResetItems(items: Data[], data: Data[]) {
    const keyCheck = items[0]?.['_id'] ? '_id' : 'id';
    const itemsIds = items.map((item) => item[keyCheck]);
    const foundItems = data.reduce((acc: Record<string, Data>, item) => {
      if (itemsIds.includes(item[keyCheck])) {
        acc[item[keyCheck]] = item;
      }
      return acc;
    }, {});

    const resetData = this.data()?.map((item) => {
      if (itemsIds.includes(item[keyCheck])) {
        return structuredClone(foundItems[item[keyCheck]]);
      }
      return item;
    });
    this.data.set(resetData);
    this.currentActionLoading.set(null);
  }

  onChangeValueCell(e: NzSafeAny, row: Data, col: Record<string, NzSafeAny>) {
    if (col?.['display_type']?.key === 'Select') {
      const workingDay = row?.['days']?.find(
        (item: Record<string, NzSafeAny>) => item?.['day'] === col?.['code'],
      );
      if (workingDay) {
        workingDay.caWorkingHourId = e;
      }
    } else {
      row[col?.['code']] = e;
    }
  }

  filterApplied = signal(false);
  filterSubmit(value: { type: string; value: NzSafeAny }) {
    if (value.type === 'reset') {
      this.filterApplied.set(false);
      this.pageIndex.set(1);
      return;
    }
    if (!this.filterApplied()) {
      this.filterApplied.set(true);
    }
    this.filterValue.set(value.value);
    this.expandFilter()?.isLoading.set(true);
  }

  getCustomUrl(config: ApiConfig, extendValue: Record<string, NzSafeAny>) {
    if (config?._url) {
      return this.buildCustomUrl(config._url.transform, extendValue);
    }
    return config.url;
  }

  async buildCustomUrl(
    transformUrl: string,
    formValue: Record<string, NzSafeAny>,
  ) {
    return await this.dynamicService.getJsonataExpression({})(
      transformUrl,
      formValue,
    );
  }

  getCellDisplayProps(displayType?: string) {
    switch (displayType) {
      case 'Select': {
        return {
          outputValue: 'value',
        };
      }
      default: {
        return {};
      }
    }
  }

  handleExport() {
    const body = {
      pageIndex: this.pageIndex(),
      pageSize: this.pageSize(),
      ...(this.filterValue() ?? {}),
    };
    this.modalComponent.showDialog({
      nzTitle: `Download ${this.functionSpec()?.title}`,
      nzContent: '',
      nzWrapClassName: 'popup popup-confirm',
      nzIconType: 'icons:file-arrow-down',
      nzOkText: 'Save',
      nzOnOk: () => {
        const url = this.url();
        if (!url) return;
        // this.bffService
        //   .exportFileByGet(
        //     url,
        //     this.pageIndex(),
        //     this.pageSize(),
        //     this.filterQuery(),
        //   )
        //   .subscribe();
        this.bffService.exportFile(url, body).subscribe({
          // next: (data: any) => {},
          error: (err: { error: { message: string } }) => {
            this.toast.showToast('error', 'Error', err.error?.message);
          },
        });
      },
      nzCancelText: 'Cancel',
      nzFooter: null,
    });
  }
  getOptionList(op: any, idx: number, key: string) {
    const op1 = (op[idx] ?? []).filter((item: any) => item.key === key)[0]
      ?.listData;
    return op1;
  }

  closeDropdown = signal<boolean | undefined>(undefined);
  // private scrollTimeout: any;
  new_table = viewChild<NewTableComponent<NzSafeAny>>('new_table');
  onScroll() {
    const tableHTML = this.new_table()?.tableRef()
      ?.nativeElement as HTMLElement;
    tableHTML.click();
  }
}

function timestampToDate(timestamp: number, format = 'DD/MM/yyyy') {
  return moment.unix(timestamp).format(format);
}
