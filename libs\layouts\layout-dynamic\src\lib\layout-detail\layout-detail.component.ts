import { CommonModule } from '@angular/common';
import {
  Component,
  computed,
  effect,
  inject,
  input,
  output,
  signal,
  ViewChild,
} from '@angular/core';
import { DynamicFormService, FormComponent } from '@hrdx-fe/dynamic-features';
import {
  BffService,
  ChildrenActionPermission,
  Data,
  FormConfig,
  FunctionSpec,
  MasterdataService,
} from '@hrdx-fe/shared';
import {
  ButtonComponent,
  DrawerComponent,
  ModalFooterButtons,
  ToastMessageComponent,
} from '@hrdx/hrdx-design';
import { catchError, of } from 'rxjs';
import { LayoutDynamicComponent } from '../layout-dynamic/layout-dynamic.component';
import { LayoutDynamicService } from '../services/layout-dynamic.service';
import { NzSafeAny } from 'ng-zorro-antd/core/types';

type DialogSize = 'small' | 'xsmall' | 'middle' | 'large' | 'largex'

@Component({
  selector: 'lib-layout-detail',
  standalone: true,
  imports: [
    CommonModule,
    DrawerComponent,
    ButtonComponent,
    LayoutDynamicComponent,
    FormComponent,
  ],
  providers: [ToastMessageComponent],
  templateUrl: './layout-detail.component.html',
  styleUrl: './layout-detail.component.less',
})
export class LayoutDetailComponent {
  dialogVisible = input<boolean>(false);
  inheritDefault = input<boolean>(false);
  config = input.required<
    FormConfig & {
      _formTitle?: any;
    }
  >();
  id = input.required<string | null>();
  value = input<Data | null | undefined>(null);
  dialogVisibleChange = output<boolean>();
  #masterdataService = inject(MasterdataService);
  dynamicService = inject(DynamicFormService);
  title = input<string>();
  fsId = input<string>();
  footerButtonsCustom = input<ModalFooterButtons[]>([]);
  clickedModalButton = output<any>();
  functionSpec = signal<FunctionSpec | undefined>(undefined);
  bffService = inject(BffService);
  url = input<string | null>(null);
  toast = inject(ToastMessageComponent);
  disabledButtons = input<string[]>([]);
  childrenActions = input<ChildrenActionPermission[]>([]);

  @ViewChild('formObj') dynamicForm?: FormComponent;
  effectFs = effect(() => {
    const id = this.fsId();
    if (!id) {
      return;
    }
    this.#masterdataService.getFunctionSpecById(id).subscribe((res) => {
      this.functionSpec.set(res);
    });
  });

  constructor(private readonly _layoutDynamicService: LayoutDynamicService) {
    this._layoutDynamicService.initLayoutDetail(this);
  }

  isLoadDetailFromAPI = computed(() => this.url() && this.value()?.id);

  realData = computed(() => {
    const apiData = this.apiData();
    if (this.isLoadDetailFromAPI()) return apiData;
    return this.value();
  });

  _title = signal<string | undefined>(undefined);

  titleEffect = effect(
    async () => {
      const customTitle = this.functionSpec()?.layout_options?.custom_title;
      if (customTitle) {
        const title = await this.buildCustomUrl(
          customTitle.transform,
          this.value(),
        );
        this._title.set(title);
      } else {
        this._title.set(undefined);
      }
    },
    {
      allowSignalWrites: true,
    },
  );

  formTitle = signal<string>('');

  formTitleEffect = effect(
    async () => {
      let getFormTitle =
        this.config()?.formTitle?.['view'] ?? `${this.title()}`;
      const transformFormTitle = this.config()?._formTitle?.['view'];
      if (transformFormTitle) {
        getFormTitle = await this.dynamicService.getJsonataExpression({})(
          transformFormTitle ?? '',
          this.value(),
        );
      }
      this.formTitle.set(getFormTitle);
    },
    { allowSignalWrites: true },
  );

  async buildCustomUrl(
    transformUrl: string,
    formValue: Data | null | undefined,
  ) {
    return await this.dynamicService.getJsonataExpression({})(
      transformUrl,
      formValue,
    );
  }

  refreshData = signal(false);
  loading = signal(false);
  reset = signal(false);
  apiData = signal<Data | null>(null);

  _footerButtonsCustom = signal<ModalFooterButtons[]>([]);

  footerButtonsEffect = effect(
    async () => {
      let buttons = this.footerButtonsCustom() ?? [];
      const expressionFunc = this.dynamicService.getJsonataExpression({});
      buttons = await Promise.all(
        buttons.map(async (button: NzSafeAny) => {
          let condition = true;
          if (button.condition_func) {
            condition = await expressionFunc(
              button.condition_func,
              this.realData(),
            );
          }
          return {
            ...button,
            condition,
          };
        }),
      );
      buttons = buttons.filter((button: NzSafeAny) => {
        return button.condition;
      });
      this._footerButtonsCustom.set(buttons);
    },
    { allowSignalWrites: true },
  );

  onRefreshData = () => {
    this.refreshData.update((prev) => !prev);
  };

  dataEffect = effect(
    () => {
      const url = this.url();
      const id = this.id();
      this.refreshData();
      if (!url || !id) {
        return;
      }
      this.loading.set(true);

      this.bffService
        .getItem(url, id)
        .pipe(
          catchError((err) => {
            this.toast.showToast('error', 'Error', err?.error?.message ?? err);
            return of(null);
          }),
        )
        .subscribe((d) => {
          this.apiData.set(d);
          this.loading.set(false);
          this.reset.update((prev) => !prev);
        });
    },
    { allowSignalWrites: true },
  );

  refresh = output();
  onCancel() {
    this.dialogVisibleChange.emit(false);
    this.refresh.emit();
  }

  onClickModalButton(id: string) {
    switch (id) {
      case 'cancel':
        this.onCancel();
        break;
      case 'back':
        this.onCancel();
        break;
      case 'edit':
        this.clickedModalButton.emit({
          id: id,
          value: this.value(),
        });
        break;
      case 'deactive':
        this.clickedModalButton.emit({
          id: id,
          value: this.dynamicForm?.value,
          callback: (status: boolean) => {
            if (status) {
              this.refreshData.update((e) => !e);
            }
          },
        });

        break;
      case 'active':
        this.clickedModalButton.emit({
          id: id,
          value: this.value(),
          callback: (status: boolean) => {
            if (status) {
              this.refreshData.update((e) => !e);
            }
          },
        });
        break;

      case 'generateReport':
        break;
      default: {
        this.clickedModalButton.emit({ id: id, value: this.value() });
      }
    }
  }

  // Handle drawer size by dialogType
  size = signal<DialogSize>('small');
  _size = effect(
    () => {
      const formSize = this.config()?.formSize;
      if (formSize) {
        const getValueByDialogType = formSize ? formSize['view'] : 'middle';
        if (
          getValueByDialogType === 'small' ||
          getValueByDialogType === 'xsmall' ||
          getValueByDialogType === 'middle' ||
          getValueByDialogType === 'large' ||
          getValueByDialogType === 'largex'
        ) {
          this.size.set(getValueByDialogType);
        } else {
          console.warn('size not support, you define size is: ', formSize);
          this.size.set(formSize['view'] as DialogSize);
        }
      } else {
        this.size.set('middle');
      }
    },
    { allowSignalWrites: true },
  );

  shouldDisabledButton(id: string) {
    return this.disabledButtons()?.includes(id);
  }
}
