import { CommonModule, NgComponentOutlet } from '@angular/common';
import {
  Component,
  computed,
  effect,
  inject,
  input,
  model,
  OnDestroy,
  OnInit,
  output,
  signal,
  Type,
  ViewChild,
} from '@angular/core';
import { ActivatedRoute, Router, Routes } from '@angular/router';
import { LayoutFormComponent } from '@hrdx-fe/layout-form';
import { LayoutOrgChartComponent } from '@hrdx-fe/layout-org-chart';
import { LayoutProcedureComponent } from '@hrdx-fe/layout-procedure';
import {
  ActionPermission,
  ChildrenActionPermission,
} from './../../../../../shared/src/lib/services/masterdata.service';
// import { LayoutProfileComponent } from '@hrdx-fe/layout-profile';
import { toObservable } from '@angular/core/rxjs-interop';
import { DynamicFormService } from '@hrdx-fe/dynamic-features';
import { LayoutDynamicTableComponent } from '@hrdx-fe/layout-dynamic-table';
import { LayoutEmployeeManageComponent } from '@hrdx-fe/layout-employee-manage';
import { LayoutProfileDragNDropComponent } from '@hrdx-fe/layout-profile-drag-n-drop';
import { LayoutSimpleTableComponent } from '@hrdx-fe/layout-simple-table';
import { LayoutSplitTableComponent } from '@hrdx-fe/layout-split-table';
import { LayoutUserRequestComponent } from '@hrdx-fe/layout-user-request';
import { LayoutWidgetComponent } from '@hrdx-fe/layout-widgets';
import {
  LayoutWorkflowComponent,
  LayoutWorkflowEditComponent,
} from '@hrdx-fe/layout-workflow';
import {
  AccountPermission,
  ACTION_TYPE,
  BffService,
  Data,
  FunctionSpec,
  getDisabledPermission,
  getReAuthPermission,
  getValue,
  HeaderStyle,
  LayoutCommon,
  LayoutDataService,
  LayoutStore,
  mappingUrl,
  MasterdataService,
  overwritePermissionAction,
  PermissionKey,
} from '@hrdx-fe/shared';
import {
  ButtonSchema,
  LoadingComponent,
  PageHeader,
  PageHeaderComponent,
} from '@hrdx/hrdx-design';
import { isArray, isBoolean, isEmpty, isEqual, isNil } from 'lodash';
import { NzSafeAny } from 'ng-zorro-antd/core/types';
import { injectParams } from 'ngxtension/inject-params';
import {
  BehaviorSubject,
  catchError,
  combineLatest,
  distinctUntilChanged,
  filter,
  map,
  of,
  Subscription,
  switchMap,
  take,
  tap,
} from 'rxjs';
import {
  LayoutFinalizeComponent,
  LayoutProfileComponent,
  LayoutTabsetComponent,
  LayoutTabsetCustomComponent,
} from '../layout-group';
import { LayoutTableComponent } from '../layout-table/layout-table.component';
import { LayoutTableProgressingComponent } from '../layout-table-progressing/layout-table-progressing.component';
import { LayoutDynamicService } from '../services/layout-dynamic.service';
import { QueryFilter } from '@nestjsx/crud-request';

@Component({
  selector: 'lib-layout-dynamic',
  standalone: true,
  imports: [CommonModule, PageHeaderComponent, LoadingComponent],
  templateUrl: './layout-dynamic.component.html',
  styleUrl: './layout-dynamic.component.less',
})
export class LayoutDynamicComponent implements OnInit, OnDestroy {
  #route = inject(ActivatedRoute);
  #layoutStore = inject(LayoutStore);
  #masterdataService = inject(MasterdataService);
  layout = signal<string>('');
  params = injectParams();
  _param = input<string | null>(null);
  _parent = input<Data | null>(null);
  _dataLayout = input<Record<string, NzSafeAny> | null>(null);
  _functionSpecId = input<string | null>(null);
  _options = input<Partial<FunctionSpec['layout_options']>>();
  _childrenActions = input<ChildrenActionPermission[]>([]);
  leftSpace = input<number | null>(null);
  eventSubjects = input<Record<string, BehaviorSubject<any>>>();
  // use to track level from parent => current level of this layout
  parentLevel = input(-1);
  currentLevel = computed(() => (this.parentLevel() ?? -1) + 1);
  isRootLayout = computed(() => this.currentLevel() === 0);

  parentData = input<Data | null>(null);

  constructor(private readonly _layoutDynamicService: LayoutDynamicService) {}
  __childrenActions = computed(() => {
    if (this._childrenActions().length > 0) return this._childrenActions();
    return this.childrenActions();
  });

  _actionsPermission = input<ActionPermission[]>([]);
  _defaultActionsPermission = input<ActionPermission[] | undefined>(undefined);
  isLazyLoad = input<boolean>(false);
  emptyContent = output<boolean>();
  refresh = input<boolean>();
  allParams = computed(() => {
    return {
      ...this.params(),
      id: this._param() ?? '',
    };
  });
  disabledActionLst = signal<ACTION_TYPE[]>([]);
  reAuthActionLst = signal<ACTION_TYPE[]>([]);
  subscription?: Subscription;
  effectFunctionSpec = effect(
    () => {
      const fsId = this._functionSpecId();
      if (fsId && !this.subscription) {
        this.subscription = this.#masterdataService
          .getFunctionSpecById(fsId)
          .pipe(take(1))
          .subscribe((functionSpec) => {
            this.layout.set(functionSpec.layout ?? 'simple-table');
            this.functionSpec.set(functionSpec);
          });
      }
    },
    { allowSignalWrites: true },
  );
  actionId = model<string | undefined>(undefined);
  actionId$ = toObservable(this.actionId);
  afterChangeFunctionSpec = effect(
    () => {
      const fs = this.functionSpec();
      const module = this.currentModule();
      if (!fs?.id || !module) return;
      const actionId = this.#masterdataService.getMenuByFunctionSpecId(
        fs.id,
        module,
      )?.faceCode;
      if (actionId) {
        this.actionId.set(actionId);
      }
    },
    { allowSignalWrites: true },
  );

  defaultFilter = computed(() => {
    return (
      this.functionSpec()
        ?.default_filter?.map((f) => {
          if (
            f.fieldValue.charAt(0) === '"' &&
            f.fieldValue.charAt(f.fieldValue.length - 1) === '"'
          ) {
            return {
              field: f.name,
              operator: f.operator,
              value: f.fieldValue.slice(1, -1),
            };
          } else if (f.fieldValue.split('.')[1]) {
            return {
              field: f.name,
              operator: f.operator,
              value: getValue(
                { parent: this._parent() },
                f.fieldValue.split('.'),
              ),
            };
          }
          return {
            field: f.name,
            operator: f.operator,
            value: isBoolean(JSON.parse(f.fieldValue))
              ? JSON.parse(f.fieldValue)
              : getValue({ parent: this._parent() }, f.fieldValue.split('.')),
          };
        })
        .filter(
          (f) =>
            (!isArray(f.value) && !isNil(f.value) && f.value !== '') ||
            (isArray(f.value) && f.value.length),
        ) ?? []
    );
  });
  functionSpec = signal<FunctionSpec | undefined>(undefined);
  _functionSpec = input<FunctionSpec | undefined>(undefined);
  url = computed(() => {
    const fs = this.functionSpec();
    const backendUrl = fs?.backend_url;
    return backendUrl
      ? mappingUrl(
          backendUrl,
          { ...this.params(), id: this._param() ?? '' },
          { parent: this._parent() },
          { noEscape: true },
        )
      : null;
  });
  _headerVisible = input<boolean>(true);

  _headerStyle = input<HeaderStyle>(undefined);

  dynamicService = inject(DynamicFormService);
  // share data between layouts in the same route
  layoutDataService = inject(LayoutDataService);

  router = inject(Router);

  @ViewChild(NgComponentOutlet, { static: false })
  layoutTemplate?: NgComponentOutlet;
  functionSpec$ = toObservable(this.functionSpec);

  permissionLoading = signal(true);
  isLayoutDetail = signal(false);
  isLayoutProfile = signal(false);
  detailData = signal<any>(undefined);
  detailData$ = toObservable(this.detailData);

  accountPermissions = signal<AccountPermission>(undefined);

  isLoadDetailDataError = false;

  bffService = inject(BffService);
  getDetailDataEffect = effect(
    () => {
      if (
        !this.isRootLayout() ||
        (!this.isLayoutDetail() && !this.isLayoutProfile())
      )
        return;
      const url = this.url();
      if (!url) return;
      this.bffService
        .getObject(url)
        .pipe(
          tap((d) => {
            this.isLoadDetailDataError = false;
          }),
          catchError((err) => {
            console.log('fail to load detail data: ', err);
            this.isLoadDetailDataError = true;
            return of(null);
          }),
          tap((d) => {
            this.detailData.set(d);
          }),
        )
        .subscribe();
    },
    { allowSignalWrites: true },
  );

  private buildQueryFilterFromPermissionKeys(
    permissionKeys: PermissionKey[],
    data: Record<string, unknown>,
  ) {
    return permissionKeys
      .map((obj) => {
        const value = data[obj.name];
        if (isNil(value)) return null;
        return {
          field: obj.defaultName,
          operator: '$eq',
          value: value,
        };
      })
      .filter((item) => !isNil(item)) as QueryFilter[];
  }

  private subscriptionLayoutEvent!: Subscription;

  effectActionDefault = effect(
    () => {
      if (this._defaultActionsPermission()) {
        const tmp = getDisabledPermission(
          this._defaultActionsPermission() as ActionPermission[],
        );

        this.disabledActionLst.set(tmp);
      }
    },
    {
      allowSignalWrites: true,
    },
  );

  async ngOnInit(): Promise<void> {
    const fs = this._functionSpec();
    if (fs) {
      this.layout.set(fs.layout ?? 'simple-table');
      this.functionSpec.set(fs);
    } else if (!this.isLazyLoad()) {
      this.#route.data
        .pipe(
          filter((data) => data['fsId']),
          map((data) => data['fsId'] as string),
          switchMap((id) => {
            return this.#masterdataService.getFunctionSpecById(id);
          }),
        )
        .subscribe((functionSpec) => {
          this.layout.set(functionSpec.layout ?? 'simple-table');
          this.functionSpec.set(functionSpec);
        });
    }

    combineLatest({
      rawActionId: this.actionId$,
      functionSpec: this.functionSpec$,
      storageActionId: this.#route.data.pipe(
        filter((data) => data['permissionActionId']),
        map((data) => data['permissionActionId'] as string | null),
        tap((data) => {
          if (data) this.#layoutStore.setFaceCode(data);
        }),
      ),
      detailData: this.detailData$,
    })
      .pipe(
        map(({ rawActionId, storageActionId, detailData, ...restData }) => {
          return {
            data: rawActionId ?? storageActionId,
            detailData:
              this.isLayoutDetail() || this.isLayoutProfile()
                ? detailData
                : undefined,
            ...restData,
          };
        }),
        distinctUntilChanged((prev, curr) => isEqual(prev, curr)),
        switchMap(({ data, functionSpec, detailData }) => {
          const id = data;
          if (!id || !functionSpec) return of(null);
          if (functionSpec.layout_options?.is_layout_detail) {
            this.isLayoutDetail.set(true);
            this.#layoutStore.setPermissionKeys(
              (functionSpec.permission_key ?? []) as any,
            );
          }

          this.permissionLoading.set(true);

          if (this._defaultActionsPermission()) {
            if (
              functionSpec.layout_options?.get_children_action_permission &&
              this.parentData()
            ) {
              return combineLatest({
                actions: of(
                  this._defaultActionsPermission() as ActionPermission[],
                ),
                children: this.#masterdataService.getChidrenFunction(
                  id,
                  this.buildQueryFilterFromPermissionKeys(
                    (functionSpec.permission_key ?? []) as any,
                    this.parentData() as any,
                  ),
                ),
                authActions: of(undefined),
              });
            } else {
              return of({
                actions: this._defaultActionsPermission() as ActionPermission[],
                children: [],
              });
            }
          }

          if (this.isLayoutDetail()) {
            if (!detailData) {
              if (this.isLoadDetailDataError) {
                this.permissionLoading.set(false);
              }

              return of(null);
            }

            return this.#masterdataService.getPermissionActionForDetail(
              id,
              this.buildQueryFilterFromPermissionKeys(
                this.#layoutStore.permissionKeys() ?? [],
                detailData as any,
              ),
            );
          } else if (functionSpec.layout_options?.is_layout_profile) {
            this.isLayoutProfile.set(true);
            this.#layoutStore.setPermissionKeys(
              (functionSpec.permission_key ?? []) as any,
            );

            if (!detailData) {
              if (this.isLoadDetailDataError) {
                this.permissionLoading.set(false);
              }

              return of(null);
            }

            return this.#masterdataService.getPermissionActionForEmployeeProfile(
              id,
              this.buildQueryFilterFromPermissionKeys(
                this.#layoutStore.permissionKeys() ?? [],
                detailData as any,
              ),
              this.params()?.['id1'],
            );
          } else {
            return this.#masterdataService.getPermissionActionById(id);
          }
        }),
        filter((res) => !isNil(res)),
      )
      .subscribe((permissionAction) => {
        if (!(this._actionsPermission().length > 0)) {
          let actions = permissionAction.actions;
          if ('accessType' in permissionAction) {
            actions = overwritePermissionAction(
              permissionAction.actions,
              permissionAction.accessType as string,
            );
          }
          const tmp = getDisabledPermission(actions);

          this.disabledActionLst.set(tmp);
          const reAuthActionLst = getReAuthPermission(actions);
          this.reAuthActionLst.set(reAuthActionLst);
          if ('authActions' in permissionAction) {
            this.#layoutStore.setAuthActions(permissionAction.authActions);
          }
        }

        const ignoreCheckAccessType =
          this.functionSpec()?.layout_options?.ignore_check_accessType ?? [];

        if ('accessType' in permissionAction) {
          const newChildrenAction = permissionAction.children.map((child) => {
            if (ignoreCheckAccessType.includes(child.fsdFE)) {
              return child;
            }
            const childActions = overwritePermissionAction(
              child.actions,
              permissionAction.accessType as string,
            );
            return {
              ...child,
              actions: childActions,
            };
          });
          this.childrenActions.set(newChildrenAction);
        } else {
          this.childrenActions.set(permissionAction.children);
        }

        if (this.isLayoutProfile()) {
          this.childrenActionsOrigin.set(permissionAction.children);
        }

        if ('accountPermissions' in permissionAction) {
          this.accountPermissions.set(permissionAction.accountPermissions);
        }

        this.permissionLoading.set(false);
      });

    // clear layout data on route change
    this.layoutDataService.clear();

    this.subscriptionLayoutEvent =
      this.layoutDataService.layoutEventEmiter$.subscribe(
        (event: NzSafeAny) => {
          if (event.key === 'refreshProfile' && this.isLayoutProfile()) {
            this.bffService
              .getObject(
                `/api/personals/${this.params()?.['id1']}/get-access-type`,
                undefined,
                this.actionId() as string,
              )
              .subscribe((res) => {
                const accessType = res?.['accessType'];
                const ignoreCheckAccessType =
                  this.functionSpec()?.layout_options
                    ?.ignore_check_accessType ?? [];
                const newChildrenAction = this.childrenActionsOrigin().map(
                  (child) => {
                    if (ignoreCheckAccessType.includes(child.fsdFE)) {
                      return child;
                    }
                    const childActions = overwritePermissionAction(
                      child.actions,
                      accessType as string,
                    );
                    return {
                      ...child,
                      actions: childActions,
                    };
                  },
                );
                this.childrenActions.set(newChildrenAction);
              });
          }
        },
      );
  }

  effectDisabledActionLst = effect(
    () => {
      const permission = this._actionsPermission();
      const tmp = getDisabledPermission(permission);
      this.disabledActionLst.set(tmp);
      const reAuthActionLst = getReAuthPermission(permission);
      this.reAuthActionLst.set(reAuthActionLst);
    },
    { allowSignalWrites: true },
  );
  childrenActions = signal<ChildrenActionPermission[]>([]);
  childrenActionsOrigin = signal<ChildrenActionPermission[]>([]);
  currentModule = this.#layoutStore.currentModuleId;

  capitalizeAndSplit(string: string) {
    if (!string) return '';
    const spacedString = string.replace(/([A-Z])/g, ' $1').trim();
    return spacedString.charAt(0).toUpperCase() + spacedString.slice(1);
  }

  checkRouteExists(routeString?: string) {
    if (!routeString) return false;
    let flag = true;
    routeString
      .split('/')
      .filter((path) => path)
      .reduce((acc: Routes | undefined, path) => {
        if (!flag) return undefined;
        if (!path) {
          flag = false;
          return undefined;
        }
        const route = acc?.find((r) => r.path === path);
        if (route) {
          if (route.children) {
            return route.children;
          } else {
            return undefined;
          }
        } else {
          flag = false;
          return undefined;
        }
      }, this.router.config);

    return flag;
  }

  pageHeader = computed<PageHeader>(() => {
    const fs = this.functionSpec();
    const module = this.currentModule();
    if (!fs) return { title: '', breadcrumb: [], buttons: [] };
    let breadcrumb = fs.id
      ? this.#masterdataService.getPathByFunctionSpecId(fs.id, module)
      : ([] as PageHeader['breadcrumb']);

    breadcrumb = breadcrumb.map((item) => {
      if (item.title === 'Manage Report') {
        return {
          ...item,
          children: [],
        };
      }
      if (item.children && item.children.length > 0) {
        const newChildren = item.children.filter((child) => {
          return this.checkRouteExists(child.route);
        });

        return {
          ...item,
          children: newChildren,
        };
      }

      return item;
    });

    const buttons = (fs.layout_options?.header_buttons ?? []).map((btn) => ({
      id: btn.id,
      type: btn.type,
      size: (btn?.size ?? 'default') as ButtonSchema['size'],
      state: 'default' as ButtonSchema['state'],
      title: btn.title
        ? this.capitalizeAndSplit(btn.title)
        : this.capitalizeAndSplit(btn.id),
      leftIcon: btn.icon,
      isLeftIcon: btn.icon ? true : false,
      href: btn.href,
    }));
    return {
      title: fs.title ?? '',
      breadcrumb: breadcrumb,
      buttons: buttons,
      options: fs.layout_options?.page_header_options,
      description: fs.layout_options?.page_header_description,
    };
  });

  isBackBtn = computed(() => {
    const fs = this.functionSpec();
    if (!fs) return false;
    const layout_option = fs.layout_options;
    return layout_option?.page_header_options?.back_btn ?? false;
  });

  _dynamicPageHeaderTitle = computed<Promise<string>>(async () => {
    const fs = this.functionSpec();
    const transformData = {
      parentData:
        this._layoutDynamicService.getParentData() ?? this.detailData(),
    };

    if (fs) {
      const layout_option = fs.layout_options;
      const expressionFunc = this.dynamicService.getJsonataExpression({});
      return (
        (await expressionFunc(
          layout_option?.custom_title?.transform ?? '',
          transformData,
        )) ?? this.pageHeader().title
      );
    }
  });

  component = computed((): Type<LayoutCommon<NzSafeAny>> | null => {
    // Intl.Locale
    switch (this.layout()) {
      case 'layout-table':
        return LayoutTableComponent;
      case 'layout-table-progressing':
        return LayoutTableProgressingComponent;
      case 'layout-simple-table':
        return LayoutSimpleTableComponent;
      case 'layout-split-table':
        return LayoutSplitTableComponent;
      case 'layout-form':
        return LayoutFormComponent;

      case 'layout-drag-drop-profile':
        return LayoutProfileDragNDropComponent;

      case 'layout-org-chart':
        return LayoutOrgChartComponent;

      case 'layout-procedure':
        return LayoutProcedureComponent;
      case 'layout-tabs':
        return LayoutTabsetComponent;
      case 'layout-widget':
        return LayoutWidgetComponent;
      case 'layout-profile':
        return LayoutProfileComponent;
      case 'layout-finalize':
        return LayoutFinalizeComponent;
      case 'layout-workflow':
        return LayoutWorkflowComponent;
      case 'layout-workflow-edit':
        return LayoutWorkflowEditComponent;
      case 'layout-user-request':
        return LayoutUserRequestComponent;
      case 'layout-employee-manage':
        return LayoutEmployeeManageComponent;
      case 'layout-dynamic-table':
        return LayoutDynamicTableComponent;
      case 'layout-tabset-custom':
        return LayoutTabsetCustomComponent;
      default:
        return null;
    }
  });

  pageHeaderButtonClicked(id: string) {
    // this.layoutTemplate?.pageHeaderButtonClicked(id);
    // in case button has href then navigate to it
    const button = this.pageHeader().buttons.find((btn) => btn.id === id);
    if (button?.href) {
      this.router.navigate([button.href]);
      return;
    }
    this.layoutTemplate?.['_componentRef']?.instance?.pageHeaderButtonClicked(
      id,
    );
  }

  viewSelectedItem() {
    switch (this.layout()) {
      case 'layout-widget': {
        const item =
          this.layoutTemplate?.['_componentRef']?.instance?.dataWidget();
        this.layoutTemplate?.['_componentRef']?.instance?.viewItem(item);
      }
    }
  }

  // handle page header buttons condition
  layoutOptions = computed(() => {
    return this.functionSpec()?.layout_options;
  });

  btnLoading = computed(() => {
    return this.layoutDataService.data()?.['btnLoading'];
  });

  isHandleNext = computed(() => {
    return this.layoutDataService.data()?.['isHandleNext'] ?? null;
  });
  isEditMode = computed(() => {
    return this.layoutDataService.data()?.['isEditMode'] ?? null;
  });

  headerButtonsCondition = computed(() => {
    return this.layoutOptions()?.header_buttons_condition;
  });

  childData = signal<NzSafeAny>(null);

  _pageHeaderButtons = signal<NzSafeAny[]>([]);

  pageHeaderButtonsEffect = effect(
    async () => {
      const buttonsCondition = this.headerButtonsCondition();
      const pageHeaderButtons = this.pageHeader().buttons;
      let hiddenButtons: string[] = [];

      const childData = this.childData();

      if (isNil(childData) || isEmpty(childData)) {
        hiddenButtons = ['history'];
      } else {
        hiddenButtons = [];
      }
      // console.log('hiddenButtons', this.disabledActionLst(), pageHeaderButtons);
      const filterButtons = pageHeaderButtons
        .filter((btn) => !hiddenButtons.includes(btn.id))
        .filter((btn) => !this.disabledActionLst().find((pa) => pa === btn.id));

      const data = {
        ...childData,
        isHandleNext: this.isHandleNext(),
        isEditMode: this.isEditMode(),
      };

      if (!data || !buttonsCondition) {
        this._pageHeaderButtons.set(filterButtons);
        return;
      }
      const expressionFunc = await this.dynamicService.getJsonataExpression({});
      const buttons = await Promise.all(
        filterButtons.map(async (button) => {
          const btn = button as NzSafeAny;
          const cond = buttonsCondition[btn['id']];
          if (!cond) return btn;
          if (cond['_disabled']) {
            btn['disabled'] = await expressionFunc(cond['_disabled'], data);
          }
          if (cond['_unvisible']) {
            btn['unvisible'] = await expressionFunc(cond['_unvisible'], data);
          }

          return btn;
        }),
      );
      this._pageHeaderButtons.set(buttons.filter((btn) => !btn['unvisible']));
    },
    { allowSignalWrites: true },
  );

  handleBackClicked() {
    const fs = this.functionSpec();
    if (!fs) return false;
    const layout_option = fs.layout_options;
    const parentPath = layout_option?.parent_path;
    this.router.navigate([parentPath]);
    return;
  }

  ngOnDestroy() {
    if (this.subscriptionLayoutEvent) {
      this.subscriptionLayoutEvent.unsubscribe();
    }
  }
}
