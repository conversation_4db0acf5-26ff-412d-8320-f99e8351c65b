<nz-layout class="layout-finalize">
  <!-- <hrdx-page-header
    [title]="pageHeader().title"
    [breadcrumbItems]="pageHeader().breadcrumb"
    [buttons]="pageHeader().buttons"
    [options]="pageHeaderOptions()"
    (buttonClicked)="pageHeaderButtonClicked($event)"
  ></hrdx-page-header> -->

  <div class="tabs-container">
    <div class="form-wrapper">
      <dynamic-form
        [config]="functionSpec()?.form_config?.fields ?? []"
        [sources]="functionSpec()?.form_config?.sources ?? {}"
        [variables]="functionSpec()?.form_config?.variables ?? {}"
        [formValue]="formValue()"
        [readOnly]="false"
        [ppxClass]="'ppxm-style'"
        [extend]="{
          formType: 'edit',
          dataRedirect: dataRedirect,
        }"
        [reload]="resetForm()"
        [_mode]="formMode()"
        (invalidChange)="formValueChanges($event)"
        [authAction]="AuthActions.Read"
        #formObj
      ></dynamic-form>
    </div>
    <div class="wrapper">
      @for (fs of fsChildren(); track $index) {
        <lib-layout-dynamic [_functionSpec]="fs"></lib-layout-dynamic>
      }
    </div>
  </div>
</nz-layout>
