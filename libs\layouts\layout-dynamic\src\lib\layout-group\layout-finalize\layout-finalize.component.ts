import { CommonModule } from '@angular/common';
import {
  AfterViewInit,
  Component,
  computed,
  effect,
  forwardRef,
  inject,
  input,
  OnD<PERSON>roy,
  signal,
  ViewChild,
} from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { FormComponent } from '@hrdx-fe/dynamic-features';
// eslint-disable-next-line @nx/enforce-module-boundaries
import { LayoutDialogFormComponent } from '@hrdx-fe/layout-simple-table';
import {
  AuthActions,
  BffService,
  Data,
  FilterService,
  FunctionSpec,
  LayoutCommon,
  LayoutCommonComponent,
  LayoutStore,
  MenuItem,
} from '@hrdx-fe/shared';
import {
  ButtonComponent,
  ButtonSchema,
  ContainerComponent,
  ModalComponent,
  PageHeader,
  PageHeaderComponent,
  ToastMessageComponent,
} from '@hrdx/hrdx-design';
import { NzLayoutModule } from 'ng-zorro-antd/layout';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzSpaceModule } from 'ng-zorro-antd/space';
import { LayoutDynamicComponent } from '../../layout-dynamic/layout-dynamic.component';
import { LayoutTabsetStore } from './layout-finalize.store';
import { QueryFilter, RequestQueryBuilder } from '@nestjsx/crud-request';
import { of, switchMap } from 'rxjs';
import { NzSafeAny } from 'ng-zorro-antd/core/types';

@Component({
  selector: 'lib-layout-finalize',
  standalone: true,
  imports: [
    CommonModule,
    NzLayoutModule,
    PageHeaderComponent,
    FormComponent,
    ButtonComponent,
    ModalComponent,
    NzModalModule,
    ToastMessageComponent,
    LayoutDialogFormComponent,
    NzSpaceModule,
    ContainerComponent,
    forwardRef(() => LayoutDynamicComponent),
  ],
  templateUrl: './layout-finalize.component.html',
  styleUrl: './layout-finalize.component.less',
  providers: [LayoutTabsetStore, ModalComponent, ToastMessageComponent],
})
export class LayoutFinalizeComponent
  extends LayoutCommonComponent
  implements LayoutCommon, AfterViewInit, OnDestroy
{
  router = inject(Router);
  #store = inject(LayoutTabsetStore);
  #layoutStore = inject(LayoutStore);
  filterService = inject(FilterService);
  _service = inject(BffService);
  AuthActions = AuthActions;

  override functionSpec = input.required<FunctionSpec>();
  effectFunctionSpec = effect(
    () => {
      this.#store.setCurrentFunctionSpecId(this.functionSpec().id ?? '');
    },
    { allowSignalWrites: true },
  );
  currentModule = this.#layoutStore.currentModuleId;

  @ViewChild('formObj') dynamicForm?: FormComponent;
  modalComponent = inject(ModalComponent);
  toast = inject(ToastMessageComponent);

  resetForm = signal(false);

  fsChildren = computed(() => {
    return this.functionSpec().children as FunctionSpec[];
  });

  formMode = computed(() => {
    return this.functionSpec()?.form_config?._mode;
  });

  formValue = computed(() => {
    return this.functionSpec()?.mock_data?.[0] ?? {};
  });

  layoutOptions = computed(() => {
    return this.functionSpec().layout_options;
  });

  pageHeaderOptions = computed(() => {
    return this.layoutOptions()?.page_header_options;
  });

  isReloadForm = computed(() => {
    return this.functionSpec()?.layout_options?.is_reload_form ?? true;
  });

  onActivate(e: NzSafeAny, id: string | undefined) {
    if (!id) return;
    e.subId.set(id);
  }

  menus = computed(() => {
    return this.functionSpec().children as NzSafeAny[];
  });

  pageHeader = computed<PageHeader>(() => {
    const fs = this.functionSpec();
    const module = this.currentModule();
    if (!fs) return { title: '', breadcrumb: [], buttons: [] };
    const breadcrumb = [
      module,
      (fs.menu_item?.parent as MenuItem)?.title ?? '',
      fs.menu_item?.title ?? '',
    ].map((title) => ({ title }));

    const buttons = (fs.layout_options?.header_buttons ?? []).map((btn) => ({
      id: btn.id,
      type: btn.type,
      size: 'default' as ButtonSchema['size'],
      state: 'default' as ButtonSchema['state'],
      title: btn.title,
      leftIcon: btn.icon,
      isLeftIcon: btn.icon ? true : false,
      href: btn.href,
    }));
    return {
      title: fs.title ?? '',
      breadcrumb: breadcrumb,
      buttons: buttons,
      options: fs.layout_options?.page_header_options,
    };
  });

  toolTable = computed(() => {
    return this.layoutOptions()?.tool_table;
  });

  override pageHeaderButtonClicked = (id: string) => {
    switch (id) {
      case 'export-template':
        this.handleDownloadTemplate(id);
        break;
      case 'import':
        this.handleImport(id);
        break;
      case 'export-data':
        this.handleExport(id);
        break;
    }
  };

  constructor(private route: ActivatedRoute) {
    super();
  }

  dataRedirect: NzSafeAny;
  ngAfterViewInit(): void {
    // const queryParams: NzSafeAny = this.route.snapshot.queryParams;
    const param = this.layoutDataService.getData();
    this.dataRedirect = param['cm_002'];
  }

  handleDownloadTemplate(btnId?: string) {
    const url = this.url();
    if (!this.dynamicForm?.valid) {
      this.toast.showToast(
        'error',
        'Error',
        // 'Required fields have not been filled in completely (fields marked with *)',
        'Please check the format of the fields',
      );
      this.dynamicForm?.setFormTouched();
      // this.dynamicForm?.form.markAllAsTouched();
      return;
    }
    if (!url) return;
    const formValue = this.dynamicForm?.value;
    const filter = Object.keys(formValue).reduce((acc: QueryFilter[], key) => {
      const value = formValue[key];
      if (value) {
        acc.push({
          field: key,
          operator: '$eq',
          value: String(value).trim().replace(/\t/g, ''),
        });
      }
      return acc;
    }, []);
    const qb = RequestQueryBuilder.create();
    qb.setFilter(filter);
    this.layoutDataService.update({ btnLoading: btnId });
    this._service
      .getTemplate(url, undefined, qb.query(), this.faceCode() as string)
      .subscribe({
        next: () => {
          this.handleNext(
            'success',
            'Success',
            'Download Template Successfully',
          );
        },
        error: (err) => {
          this.layoutDataService.update({ btnLoading: null });
          this.toast.showToast(
            'error',
            'Error',
            err.error?.message ?? 'Download Template failed',
          );
        },
      });
  }

  handleImport(btnId?: string) {
    console.log('handleImport', this.faceCode());
    const url = this.url();
    if (!url) return;
    if (!this.dynamicForm?.valid) {
      this.toast.showToast(
        'error',
        'Error',
        // 'Required fields have not been filled in completely (fields marked with *)',
        'Please check the format of the fields',
      );
      // this.dynamicForm?.form.markAllAsTouched();
      return;
    }
    if (url) {
      of(undefined)
        .pipe(
          switchMap(() => {
            this.layoutDataService.update({ btnLoading: btnId });
            return this._service.createFormData(
              url,
              this.dynamicForm?.value,
              this.faceCode() as string,
              { authAction: AuthActions.Read },
            );
          }),
        )
        .subscribe({
          next: () => {
            this.handleNext('success', 'Success', 'Import Job created');
          },
          error: (err) => {
            this.toast.showToast('error', 'Error', err.error?.message);
            this.layoutDataService.update({ btnLoading: null });
          },
        });
    } else {
      this.toast.showToast('success', 'Success', 'Saved Successfully');
    }
  }

  handleExport(btnId?: string) {
    const url = this.url();
    if (!url) return;
    if (!this.dynamicForm?.valid) {
      this.toast.showToast(
        'error',
        'Error',
        // 'Required fields have not been filled in completely (fields marked with *)',
        'Please check the format of the fields',
      );
      this.dynamicForm?.form.markAllAsTouched();
      return;
    }
    if (url) {
      of(undefined)
        .pipe(
          switchMap(() => {
            this.layoutDataService.update({ btnLoading: btnId });
            return this._service.exportFile(
              url,
              this.dynamicForm?.value,
              false,
              this.faceCode() as string,
            );
          }),
        )
        .subscribe({
          next: () => {
            this.handleNext('success', 'Success', 'Export Job created');
          },
          error: (err) => {
            this.toast.showToast('error', 'Error', err.error?.message);
            this.layoutDataService.update({ btnLoading: null });
          },
        });
    } else {
      this.toast.showToast('success', 'Success', 'Export Successfully');
    }
  }

  handleNext(type: string, title: string, subTitle: string) {
    this.isReloadForm() && this.resetForm.update((e) => !e);
    this.toast.showToast(type, title, subTitle);
    this.layoutDataService.update({ btnLoading: null, isHandleNext: true });
  }

  formValueChanges(value: NzSafeAny) {
    this.childData()?.set({
      isValidData: value && !this.dynamicForm?.isSourceLoading,
    });
  }

  ngOnDestroy(): void {
    this.layoutDataService.clearData();
  }
}
