<nz-layout class="profile-layout">
  @if (loading() && !isRefreshDataFromEvent()) {
    <div class="loading-container">
      <hrdx-loading></hrdx-loading>
    </div>
  } @else {
    @if (hasEmployeeCode) {
      <div class="profile-header-container" *ngIf="hasEmployeeCode">
        <div class="profile-container">
          <nz-space nzSize="large">
            <hrdx-profile-image
              [imageUrl]="avatarLink()"
              [name]="profileCard().name"
              (onUpload)="onUploadAvatar($event)"
              (onDelete)="onDeleteAvatar($event)"
              [canUpload]="checkPermission('edit', profileImageFsData())"
              [canDelete]="checkPermission('delete', profileImageFsData())"
              *nzSpaceItem
            ></hrdx-profile-image>
            <div class="wrapper" *nzSpaceItem>
              <hrdx-profile-card
                [name]="profileCard().name"
                [job]="profileCard().job"
                [company]="profileCard().company"
                [email]="profileCard().email"
                [employeeId]="profileCard().employeeId"
                [optionList]="employeeRecordNumbers()"
                (onChange)="onEmployeeRecordNumberChange($event)"
                [selectedOptions]="selectedERN()"
                [showRecordNumber]="showEmployeeRecordNumber()"
              ></hrdx-profile-card>
              <hrdx-descriptions [columns]="0" [fields]="[]" [titles]="[]">
              </hrdx-descriptions>
            </div>
          </nz-space>
        </div>

        <div class="tabset-wrapper">
          @if (this.menus()) {
            <hrdx-tabbar
              [menus]="menus() ?? []"
              (tabItemClicked)="onTabClick($event)"
            ></hrdx-tabbar>
          } @else {
            <hrdx-tabs [selectedIndex]="indexStart">
              @for (child of fsChildren(); track child) {
                <hrdx-tab [title]="'thonghm2'" (clicked)="onTabClick(child)">
                </hrdx-tab>
              }
            </hrdx-tabs>
          }
        </div>
      </div>
      <div
        class="scroll-page-container"
        (scroll)="scroll($event)"
        (scrollend)="onScrollEnd()"
      >
        <nz-content>
          <hrdx-container>
            <div [ngClass]="{ 'widgets-layout columns-2': true }">
              @for (child of fsChildren(); track child) {
                <div
                  [ngClass]="{
                    'scroll-page-widget-wrapper': true,
                    'widget-wrapper': true,
                    active: selectedTab() === child,
                    'full-width': isFullWidth(child),
                  }"
                  id="{{ child }}"
                  (mouseenter)="onMouseEnter(child)"
                >
                  <lib-widget [height]="345">
                    <lib-layout-dynamic
                      [_functionSpecId]="checkScroll($index) ? child : null"
                      [_defaultActionsPermission]="getActionsByFsId(child)"
                      [_headerStyle]="'widget'"
                      [isLazyLoad]="true"
                      [_dataLayout]="_dataLayout()"
                      [parentData]="data()"
                      [_options]="{
                        table_scroll_height: 500,
                        empty_state_style: 'illustration',
                      }"
                    ></lib-layout-dynamic>
                  </lib-widget>
                </div>
              }
            </div>
          </hrdx-container>
        </nz-content>
      </div>
    } @else {
      <div class="layout-profile__empty-state">
        <hrdx-empty-state
          [type]="emptyStateConfig().type"
          [size]="emptyStateConfig().size"
          [title]="emptyStateConfig().title"
          [subTitle]="emptyStateConfig().subTitle"
          [showButton]="false"
        ></hrdx-empty-state>
      </div>
    }
  }
</nz-layout>
