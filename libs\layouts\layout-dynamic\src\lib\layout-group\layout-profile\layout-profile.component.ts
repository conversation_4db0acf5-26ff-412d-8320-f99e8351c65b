import { CommonModule } from '@angular/common';
import {
  AfterViewInit,
  Component,
  computed,
  effect,
  forwardRef,
  inject,
  OnInit,
  signal,
  OnDestroy,
  ChangeDetectorRef,
} from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
// eslint-disable-next-line @nx/enforce-module-boundaries
import { LibWidgetComponent } from '@hrdx-fe/layout-widgets';
import {
  ActionPermission,
  AuthActions,
  BffService,
  Data,
  debouncedSignal,
  LayoutCommon,
  LayoutCommonComponent,
  LayoutStore,
  MasterdataService,
  MenuItem,
  UserStore,
  UtilService,
} from '@hrdx-fe/shared';
import {
  ContainerComponent,
  Descriptions,
  DescriptionsComponent,
  EmptyStateComponent,
  LoadingComponent,
  PageHeader,
  PageHeaderComponent,
  ProfileCard,
  ProfileCardComponent,
  ProfileImageComponent,
  RecordNumbers,
  StateSizeEnum,
  StateTypeEnum,
  TabbarComponent,
  TabsModule,
  ToastMessageComponent,
} from '@hrdx/hrdx-design';
import { NzSafeAny } from 'ng-zorro-antd/core/types';
import { NzLayoutModule } from 'ng-zorro-antd/layout';
import { NzSpaceModule } from 'ng-zorro-antd/space';
import { catchError, of, Subscription, switchMap, tap } from 'rxjs';
import { LayoutDynamicComponent } from '../../layout-dynamic/layout-dynamic.component';
import { LayoutProfileStore } from './layout-profile.store';
import * as _ from 'lodash';

@Component({
  selector: 'lib-layout-profile',
  standalone: true,
  imports: [
    CommonModule,
    ContainerComponent,
    NzLayoutModule,
    LoadingComponent,
    ProfileCardComponent,
    NzSpaceModule,
    ProfileImageComponent,
    DescriptionsComponent,
    TabsModule,
    forwardRef(() => LayoutDynamicComponent),
    TabbarComponent,
    LibWidgetComponent,
    EmptyStateComponent,
  ],
  providers: [LayoutProfileStore, ToastMessageComponent],
  templateUrl: './layout-profile.component.html',
  styleUrl: './layout-profile.component.less',
})
export class LayoutProfileComponent
  extends LayoutCommonComponent
  implements LayoutCommon, OnInit, OnDestroy
{
  router = inject(Router);
  constructor(private route: ActivatedRoute) {
    super();
  }
  readonly emptyStateConfigDefault = {
    type: StateTypeEnum.NoResult,
    subTitle: 'Employee not found',
    size: StateSizeEnum.L,
  };
  readonly emptyStateConfigNoPer = {
    type: StateTypeEnum.NoPermission,
    title: 'Access Denied',
    subTitle: `You don't have permission to access this resource.`,
    size: StateSizeEnum.L,
  };
  emptyStateConfig = signal<{
    type: StateTypeEnum;
    title?: string;
    subTitle: string;
    size: StateSizeEnum;
  }>(this.emptyStateConfigDefault);
  #userStore = inject(UserStore);
  #store = inject(LayoutProfileStore);
  #layoutStore = inject(LayoutStore);
  metaDataService = inject(MasterdataService);
  toast = inject(ToastMessageComponent);
  _service = inject(BffService);

  private readonly cd = inject(ChangeDetectorRef);
  effectFunctionSpec = effect(
    () => {
      this.#store.setCurrentFunctionSpecId(this.functionSpec().id ?? '');
    },
    { allowSignalWrites: true },
  );

  currentModule = this.#layoutStore.currentModuleId;

  hasEmployeeCode = true;
  private subscriptionLayoutEvent!: Subscription;
  isRefreshDataFromEvent = signal(false);

  selectedERN = signal('');
  ngOnInit() {
    //lắng nghe sự kiện khi cập nhật Jobdata cần refresh lại ERN trên header
    this.subscriptionLayoutEvent =
      this.layoutDataService.layoutEventEmiter$.subscribe(
        (event: NzSafeAny) => {
          if (event.key === 'refreshERN') {
            this.refreshDataERN.update((e) => !e);
          } else if (event.key === 'refreshProfile') {
            this.isRefreshDataFromEvent.set(true);
            this.refreshDataProfile.update((e) => !e);
          } else if (event.key === 'selectERN') {
            this.onEmployeeRecordNumberChange(event.value);
          }
        },
      );

    this.route.paramMap.subscribe((params) => {
      const id = params.get('id1');
      this.employeeRecordNumbers.set(null);
      this.data.set(undefined);
      if (
        (!id || id === 'undefined') &&
        !this.#userStore.user()?.employeeCode
      ) {
        this.hasEmployeeCode = false;
      } else {
        this.hasEmployeeCode = true;
      }
    });
  }

  ngOnDestroy() {
    //xóa trình nghe sự kiện khi thoát khỏi trang
    if (this.subscriptionLayoutEvent) {
      this.subscriptionLayoutEvent.unsubscribe();
    }
  }

  pageHeader = computed<PageHeader>(() => {
    const fs = this.functionSpec();
    const module = this.currentModule();

    const breadcrumb = [
      module,
      (fs.menu_item?.parent as MenuItem)?.title ?? '',
      fs.menu_item?.title ?? '',
    ].map((title) => ({ title }));
    return {
      title: fs.title ?? '',
      breadcrumb: breadcrumb,
      buttons: [],
    };
  });
  data = signal<NzSafeAny>(undefined);
  employeeRecordNumbers = signal<NzSafeAny>(undefined);
  loading = signal<boolean>(true);

  refreshDataProfile = signal(false);
  dataEffect = effect(
    () => {
      const fs = this.functionSpec();
      const url = this.url();

      this.refreshDataProfile();

      if (!url)
        return this.data.set(
          this.metaDataService.generateMockData(
            fs.local_fields,
            fs.mock_data,
          )[0],
        );
      else {
        of(url)
          .pipe(
            tap(() => this.loading.set(true)),
            switchMap((url) =>
              this._service
                .getObject(url, undefined, this.faceCode() as string)
                .pipe(
                  tap((d) => {
                    this.data.set(d);
                  }),
                ),
            ),
            catchError((err) => {
              // this.toast.showToast(
              //   'error',
              //   'Error',
              //   err?.error?.message ?? err,
              // );
              // this.router.navigate(['not-found']);
              if (err?.error?.message === 'No Authorized.') {
                this.emptyStateConfig.set(this.emptyStateConfigNoPer);
              } else {
                this.emptyStateConfig.set(this.emptyStateConfigDefault);
              }

              this.hasEmployeeCode = false;
              return of(null);
            }),
            tap(() => {
              this.loading.set(false);
              this.isRefreshDataFromEvent.set(false);
              this.cd.detectChanges();
            }),
          )
          .subscribe((d) => {
            this.data.set(d);
            this.scrollToWidgetByQueryParams();
          });
      }
    },
    { allowSignalWrites: true },
  );

  profileCard = computed<ProfileCard>(() => {
    const data = this.data();

    return {
      name: data?.fullName,
      job: data?.jobName,
      company: data?.companyName,
      email: data?.email,
      employeeId: data?.employeeId,
    };
  });

  avatarFile = computed(() => this.data()?.avatarFile);
  // create avatar link
  avatarLink = signal<string>('');
  avatarLinkEffect = effect(async () => {
    if (this.avatarFile()) {
      const url = await this._service.generateAvatarLink(
        this.avatarFile(),
        this.faceCode() as string,
      );
      this.avatarLink.set(url);
    }
  });

  _dataLayout = computed<Record<string, NzSafeAny> | null>(() => {
    return {
      dataProfile: { ...this.profileCard(), avatarLink: this.avatarLink() },
    };
  });

  descriptionsFields = computed<Descriptions['fields']>(() => {
    const data = this.data();
    return {
      employeeId: data?.employeeId,
      job: data?.job,
      company: data?.company,
      fullName: data?.fullName,
      department: data?.department,
      mobilePhone: data?.mobilePhone,
      account: data?.account,
      employeeType: data?.employeeType,
      email: data?.email,
    };
  });

  fsChildren = computed(() => {
    const fs = this.functionSpec();
    if (!fs.id) return [];
    const menus = this.metaDataService.getChildrenMenuByFunctionSpecId(
      fs.id,
      this.currentModule(),
    );
    return this.functionSpec().children?.filter((id) =>
      menus.find((menu) => menu.fsdFE === id),
    ) as string[];
  });

  selectedTab = signal<string | undefined>(undefined);

  private sectionObserver?: IntersectionObserver;
  private sectionVisibility = new Map<string, number>();
  private isManualScroll = false;

  onTabClick(id?: string) {
    if (!id) return;
    const element = document.getElementById(id);
    if (element) {
      this.enabledHoverActiveWidget = false;
      element.scrollIntoView({ behavior: 'smooth', block: 'start' });

      this.updateMenuHighlight(id);
      this.selectedTab.set(id);
    }
  }

  onUploadAvatar({
    file,
    callback,
  }: {
    file: File;
    callback?: (res: { status: boolean; url?: string }) => void;
  }) {
    // const url = this.url();
    // if (!url) return;
    const userId = this.data()?.id;
    if (!userId) return;
    const url = `/api/personals/${userId}/basic-infomation`;
    of(url)
      .pipe(
        switchMap((url) =>
          this._service.uploadAvatar(url, file, this.faceCode() as string).pipe(
            tap((d) => {
              this.toast.showToast(
                'success',
                'Success',
                'Update Avatar Successfully',
              );
              callback?.({ status: true, url: d as string });
            }),
          ),
        ),
        catchError((err) => {
          this.toast.showToast('error', 'Error', err?.error?.message ?? err);
          callback?.({ status: false });
          return of(null);
        }),
      )
      .subscribe();
  }

  onDeleteAvatar({
    callback,
  }: {
    callback?: (res: { status: boolean }) => void;
  }) {
    // const url = this.url();
    // if (!url) return;
    const userId = this.data()?.id;
    if (!userId) return;
    const url = `/api/personals/${userId}/basic-infomation`;
    of(url)
      .pipe(
        switchMap((url) =>
          this._service.deleteAvatar(url, this.faceCode() as string).pipe(
            tap((d) => {
              this.toast.showToast(
                'success',
                'Success',
                'Delete Avatar Successfully',
              );
              callback?.({ status: true });
            }),
          ),
        ),
        catchError((err) => {
          this.toast.showToast('error', 'Error', err?.error?.message ?? err);
          callback?.({ status: false });
          return of(null);
        }),
      )
      .subscribe();
  }

  showEmployeeRecordNumber = computed<boolean>(() => {
    const fs = this.functionSpec();
    return fs.layout_options?.show_employee_record_number ?? false;
  });

  employeeRecordNumberApiUrl = computed<string>(() => {
    const fs = this.functionSpec();
    return fs.layout_options?.employee_record_number_api_url ?? '';
  });

  ernTransfromData = computed(() => {
    const fs = this.functionSpec();
    return fs.layout_options?.ern_transfrom_data;
  });

  profileImageFsId = computed(() => {
    const fs = this.functionSpec();
    return fs.layout_options?.profile_image_fsId;
  });

  utilService = inject(UtilService);

  refreshDataERN = signal(false);

  isFirstLoadERN = signal(true);

  originEmployeeRecordNumberList: Data[] = [];

  loademployeeRecordNumbers = effect(
    async () => {
      this.refreshDataERN();
      if (!this.showEmployeeRecordNumber() || !this.data()) return;
      let url = this.url() + '/record-numbers';

      if (this.employeeRecordNumberApiUrl()) {
        const { id1 } = this.params();
        url = await this.utilService.transformRedirectTo(
          { employeeId: id1 },
          this.employeeRecordNumberApiUrl(),
        );
      }

      of(url)
        .pipe(
          switchMap((url) =>
            this._service.getList(
              url,
              undefined,
              undefined,
              undefined,
              this.faceCode() as string,
            ),
          ),
          catchError((err) => {
            // this.toast.showToast('error', 'Error', err?.error?.message ?? err);
            return of(null);
          }),
        )
        .subscribe(async (d) => {
          this.originEmployeeRecordNumberList = d ?? [];
          let listData = d as Record<string, unknown>[];
          if (this.ernTransfromData()) {
            listData = await this.utilService.transformArrayList(
              d as Record<string, unknown>[],
              this.ernTransfromData(),
            );
          }

          this.employeeRecordNumbers.set(listData);
          const checkSelectedERN = this.selectedERN()
            ? listData.find(
                (item: any) => item?.['value'] === this.selectedERN(),
              )
            : undefined;
          if (this.isFirstLoadERN() || !checkSelectedERN) {
            this.initDefaultERN();
            this.isFirstLoadERN.set(false);
          }
        });
    },
    { allowSignalWrites: true },
  );

  initDefaultERN() {
    const employeeRecordNumbers =
      this.employeeRecordNumbers() as RecordNumbers[];

    if (employeeRecordNumbers?.length > 0) {
      const queryParams: any = this.route.snapshot.queryParams;
      const ern = queryParams.ern;
      let getERN = employeeRecordNumbers[0]?.value;
      if (ern) {
        getERN =
          employeeRecordNumbers.find((item) => item.value === ern)?.value || '';
      }
      this.selectedERN.set(getERN);
    }
  }

  onEmployeeRecordNumberChange(event: NzSafeAny) {
    if (event) {
      const employeeRecordNumbers =
        this.employeeRecordNumbers() as RecordNumbers[];
      const item = employeeRecordNumbers.find((item) => item.value === event);
      const filterParam = {
        employeeRecordNumber: event,
        organizationalInstanceRcd: item?.['organizationalInstanceRcd'],
      };

      this.layoutDataService.updateData({
        'HR.FS.FR.045': filterParam,
        'HR.FS.FR.009': filterParam,
        'HR.FS.FR.010': filterParam,
      });
      this.selectedERN.set(event);
    }
  }

  indexStart = 0;

  scrollToWidgetByQueryParams() {
    const queryParams: any = this.route.snapshot.queryParams;
    const tab = queryParams.tab;
    const tabIndex = this.fsChildren().findIndex((child) => child === tab);
    this.indexStart = tabIndex;
    if (tab) {
      setTimeout(() => {
        this.onTabClick(tab);
      }, 100);
    }
    this.onScroll();
  }

  menus = computed(() => {
    const fs = this.functionSpec();
    if (!fs.id) return [];
    const menus = this.metaDataService.getChildrenMenuByFunctionSpecId(
      fs.id,
      this.currentModule(),
    );
    // console.log('menus', menus);

    return this.functionSpec()?.layout_options?.tabset_menu?.map((it) => {
      return {
        title: it.title,
        children: it.children?.filter((child) =>
          menus.find((menu) => menu.fsdFE === child.id),
        ),
      } as any;
    });
  });

  profileImageFsData = computed(() => {
    const fs = this.functionSpec();
    if (!fs.id || !this.profileImageFsId()) return undefined;
    const menus = this.metaDataService.getChildrenMenuByFunctionSpecId(
      fs.id,
      this.currentModule(),
    );
    const childrenActions = this.childrenActions();

    const d = menus.find((item) => item.fsId === this.profileImageFsId());
    if (d && childrenActions?.length > 0) {
      const faceCode = d?.['faceCode'] as string;

      const actions =
        childrenActions.find((action) => action.faceCode === faceCode)
          ?.actions ?? [];
      return {
        ...d,
        faceCode,
        actions: actions,
      } as Data;
    }

    return undefined;
  });

  scrollEvent = signal<number>(0);
  debouncedScroll = debouncedSignal(this.scrollEvent, 300);

  getVisibleHeight(element: HTMLElement) {
    const container = document.querySelector('.scroll-page-container');
    if (!container) return;
    const scrollTop = container.scrollTop;
    const scrollBot = scrollTop + container.clientHeight;
    const containerRect = container.getBoundingClientRect();
    const eleRect = element.getBoundingClientRect();
    const rect: {
      top?: number;
      right?: number;
      bottom?: number;
      left?: number;
    } = {};
    (rect.top = eleRect.top - containerRect.top),
      (rect.right = eleRect.right - containerRect.right),
      (rect.bottom = eleRect.bottom - containerRect.bottom),
      (rect.left = eleRect.left - containerRect.left);
    const eleTop = rect.top + scrollTop;
    const eleBot = eleTop + element.offsetHeight;
    const visibleTop = eleTop < scrollTop ? scrollTop : eleTop;
    const visibleBot = eleBot > scrollBot ? scrollBot : eleBot;

    return visibleBot - visibleTop;
  }
  loadListIdx = new Set<number>();

  goingScroll = effect(() => {
    if (this.debouncedScroll()) {
      console.log('scrolling'); // don't remove this line
    }
    this.onScroll();
  });
  scroll(e: Event) {
    this.scrollEvent.set((e.target as HTMLElement).scrollTop);
  }
  onScroll() {
    const container = document.querySelector('.scroll-page-container');
    const divs = document.querySelectorAll('.scroll-page-widget-wrapper');
    if (!container) return;
    // const containerHeight = container.clientHeight;

    for (let i = 0; i < divs.length; i++) {
      // Gets the amount of pixels currently visible within the container
      const visiblePageHeight =
        this.getVisibleHeight(divs[i] as HTMLElement) ?? 0;
      // If the amount of visible pixels is bigger or equal to half the container size, set page
      if (visiblePageHeight >= 0) {
        this.loadListIdx.add(i);
      }
    }

    this.cd.detectChanges();
  }
  checkScroll(index: number) {
    if (this.loadListIdx.has(index)) return true;
    return false;
  }

  enabledHoverActiveWidget = true;
  timeOutSetEnabledHover: any;

  onScrollEnd() {
    clearTimeout(this.timeOutSetEnabledHover);
    this.timeOutSetEnabledHover = setTimeout(() => {
      this.enabledHoverActiveWidget = true;
    }, 1000);

    this.cd.detectChanges();
  }

  childrenFullWidth = computed(
    () => this.functionSpec()?.layout_options?.children_full_width ?? [],
  );

  isFullWidth(id: string) {
    return this.childrenFullWidth()?.includes(id);
  }

  onMouseEnter(key: string) {
    if (this.enabledHoverActiveWidget) {
      this.selectedTab.set(key);
    }
  }
  // Add effect to observe new sections
  observeSections = effect(() => {
    // Trigger when scroll event happens or when sections are loaded
    if (this.debouncedScroll() || this.loadListIdx.size > 0) {
      this.setupIntersectionObserver();
    }
  });

  private setupIntersectionObserver() {
    if (this.sectionObserver) {
      this.sectionObserver.disconnect();
    }

    this.sectionObserver = new IntersectionObserver(
      (entries) => {
        if (this.isManualScroll) return;

        entries.forEach((entry) => {
          const element = entry.target as HTMLElement;
          const container = document.querySelector('.scroll-page-container');
          if (!container) return;

          const visibleHeight = this.getVisibleHeight(element) ?? 0;
          const totalHeight = element.offsetHeight;
          const visibilityRatio = visibleHeight / totalHeight;

          // Store visibility ratio for the section ID
          this.sectionVisibility.set(entry.target.id, visibilityRatio);
        });

        // Find most visible section
        let maxRatio = 0;
        let mostVisibleId = '';
        this.sectionVisibility.forEach((ratio, id) => {
          if (ratio > maxRatio) {
            maxRatio = ratio;
            mostVisibleId = id;
          }
        });

        // Update selected tab if section is significantly visible
        if (mostVisibleId && maxRatio > 0.1) {
          // console.log('mostVisibleId', mostVisibleId);

          // Find the menu item and its parent submenu
          const parentMenu = this.menus()?.find((menu) =>
            menu.children?.some((child: any) => child.id === mostVisibleId),
          );

          if (parentMenu) {
            // First remove all active classes and arrows
            document.querySelectorAll('li[nz-submenu]').forEach((submenu) => {
              submenu.classList.remove('ant-menu-submenu-horizontal');
              submenu.classList.remove('ng-star-inserted');
              submenu.classList.remove('ant-menu-submenu-selected');
              submenu.classList.remove('ant-menu-submenu-open');

              // Remove any existing arrows
              const arrow = submenu.querySelector('.ant-menu-submenu-arrow');
              if (arrow) {
                arrow.remove();
              }
            });

            // Find and highlight only the parent submenu
            const submenuElement = Array.from(
              document.querySelectorAll(
                'div.tabset-wrapper span.ant-menu-title-content',
              ),
            )
              .find((span) => span.textContent?.trim() === parentMenu.title)
              ?.closest('li[nz-submenu]');
            if (submenuElement) {
              // Remove any existing highlight first
              const currentSelected = document.querySelector(
                'li[nz-submenu].ant-menu-submenu-selected',
              );
              if (currentSelected && currentSelected !== submenuElement) {
                currentSelected.classList.remove('ant-menu-submenu-selected');
              }

              submenuElement.classList.add('ant-menu-submenu');
              submenuElement.classList.add('ant-menu-submenu-horizontal');
              submenuElement.classList.add('ng-star-inserted');
              submenuElement.classList.add('ant-menu-submenu-selected');

              // Add arrow if needed
              if (!submenuElement.querySelector('.ant-menu-submenu-arrow')) {
                const arrow = document.createElement('span');
                arrow.className = 'ant-menu-submenu-arrow ng-star-inserted';
                submenuElement
                  .querySelector('.ant-menu-submenu-title')
                  ?.appendChild(arrow);
              }
            }

            // this.selectedTab.set(mostVisibleId);
          }
        }
      },
      {
        root: document.querySelector('.scroll-page-container'),
        threshold: 0,
        rootMargin: '-48px 0px 0px 0px',
      },
    );

    document
      .querySelectorAll('.scroll-page-widget-wrapper')
      .forEach((section) => this.sectionObserver?.observe(section));
  }

  private updateMenuHighlight(id: string) {
    // Find the menu item and its parent submenu
    const parentMenu = this.menus()?.find((menu) =>
      menu.children?.some((child: any) => child.id === id),
    );

    if (parentMenu) {
      // First remove all active classes and arrows
      document.querySelectorAll('li[nz-submenu]').forEach((submenu) => {
        submenu.classList.remove('ant-menu-submenu-horizontal');
        submenu.classList.remove('ng-star-inserted');
        submenu.classList.remove('ant-menu-submenu-selected');
        submenu.classList.remove('ant-menu-submenu-open');

        // Remove any existing arrows
        const arrow = submenu.querySelector('.ant-menu-submenu-arrow');
        if (arrow) {
          arrow.remove();
        }
      });

      // Find and highlight only the parent submenu
      const submenuElement = Array.from(
        document.querySelectorAll(
          'div.tabset-wrapper span.ant-menu-title-content',
        ),
      )
        .find((span) => span.textContent?.trim() === parentMenu.title)
        ?.closest('li[nz-submenu]');
      if (submenuElement) {
        submenuElement.classList.add('ant-menu-submenu');
        submenuElement.classList.add('ant-menu-submenu-horizontal');
        submenuElement.classList.add('ng-star-inserted');
        submenuElement.classList.add('ant-menu-submenu-selected');

        // Add arrow if needed
        if (!submenuElement.querySelector('.ant-menu-submenu-arrow')) {
          const arrow = document.createElement('span');
          arrow.className = 'ant-menu-submenu-arrow ng-star-inserted';
          submenuElement
            .querySelector('.ant-menu-submenu-title')
            ?.appendChild(arrow);
        }
      }
    }
  }

  getActionsByFsId(fsId: string) {
    const currentModule = this.#layoutStore.currentModuleId();
    const actionId = this.metaDataService.getMenuByFunctionSpecId(
      fsId,
      currentModule,
    )?.faceCode;

    const childrenActions = this.childrenActions();
    const actions =
      childrenActions.find((action) => action.faceCode === actionId)?.actions ??
      [];
    return actions as ActionPermission[];
  }
}
