<nz-layout class="layout-tabset">
  <!-- <hrdx-page-header
    [title]="pageHeader().title"
    [breadcrumbItems]="pageHeader().breadcrumb"
    [buttons]="pageHeader().buttons"
    [headerStyle]="headerStyle()"
    (buttonClicked)="pageHeaderButtonClicked($event)"
    *ngIf="headerVisible()"
  ></hrdx-page-header> -->
  <nz-content>
    <div class="tabs-container" [ngClass]="{ border: isBorder() }">
      <div class="form-wrapper" *ngIf="showForm()">
        <dynamic-form
          [config]="formConfig()?.fields"
          [sources]="formConfig()?.sources ?? {}"
          [variables]="formConfig()?.variables ?? {}"
          [formValue]="formValue()"
          [readOnly]="formType() === 'view'"
          [ppxClass]="'ppxm-style'"
          [extend]="{
            formType: formType(),
            defaultValue: formValue(),
          }"
          [reload]="resetForm()"
          [_mode]="formMode()"
          #formObj
        ></dynamic-form>
      </div>
      <div class="wrapper">
        <hrdx-tabs [(selectedIndex)]="indexStart">
          @for (tab of fsChildren(); track $index) {
            <!-- @if (checkChildPermission(tab)) { -->
            <hrdx-tab
              [title]="tab.title || tab.id"
              (clicked)="onTabClick($index)"
              *ngIf="!setDisabled(tab.title)"
            >
              <lib-layout-dynamic
                [_functionSpecId]="$index === indexStart ? tab.id : null"
                [_headerStyle]="'tab'"
                [_headerVisible]="true"
                [_param]="params()['id']"
                [_parent]="data() ?? parent()"
                [refresh]="refreshTab()[$index]"
                [isLazyLoad]="true"
                [_actionsPermission]="tab.actions"
                [leftSpace]="leftSpace()"
                [eventSubjects]="_eventSubjects()"
                [parentLevel]="currentLevel()"
                [actionId]="isLayoutDetail() ? tab.functionCode : faceCode() ?? undefined"
              ></lib-layout-dynamic>
            </hrdx-tab>
            <!-- } -->
          }
        </hrdx-tabs>
      </div>
    </div>
  </nz-content>
</nz-layout>

<lib-layout-dialog
  [dialogVisible]="dialogVisible()"
  (dialogVisibleChange)="dialogVisible.set($event)"
  [config]="dialogConfig()"
  [dialogType]="dialogType()"
  [url]="url()"
  [value]="dialogValue()"
  [title]="dialogTitle()"
  [showSaveAddButton]="showDialogSaveAddBtn()"
  [checkPermission]="checkPermission"
  [disabledActionLst]="disabledActionLst()"
  (submitValue)="dialogSubmit($event)"
  *ngIf="dialogVisible()"
>
</lib-layout-dialog>
