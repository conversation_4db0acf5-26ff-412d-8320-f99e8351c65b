import { CommonModule } from '@angular/common';
import {
  AfterViewInit,
  Component,
  computed,
  effect,
  ElementRef,
  forwardRef,
  inject,
  OnInit,
  signal,
  viewChild,
  ViewChild,
} from '@angular/core';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { FormComponent } from '@hrdx-fe/dynamic-features';
// eslint-disable-next-line @nx/enforce-module-boundaries
import {
  DialogType,
  LayoutDialogComponent,
} from '@hrdx-fe/layout-simple-table';
import {
  BffService,
  ChildrenActionPermission,
  Data,
  FilterService,
  FormConfig,
  LayoutCommon,
  LayoutCommonComponent,
  LayoutStore,
  MenuItem,
} from '@hrdx-fe/shared';
import {
  ModalComponent,
  PageHeader,
  TabsModule,
  ToastMessageComponent,
} from '@hrdx/hrdx-design';
import {
  forOwn,
  isArray,
  isDate,
  isNil,
  isNumber,
  isObject,
  isString,
  mapValues,
  toString,
  trim,
} from 'lodash';
import { NzSafeAny } from 'ng-zorro-antd/core/types';
import { NzContentComponent, NzLayoutModule } from 'ng-zorro-antd/layout';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzSpaceModule } from 'ng-zorro-antd/space';
import {
  BehaviorSubject,
  catchError,
  distinctUntilChanged,
  map,
  of,
  Subject,
  switchMap,
  takeUntil,
  tap,
} from 'rxjs';
import { LayoutDynamicComponent } from '../../layout-dynamic/layout-dynamic.component';
import { LayoutTabsetStore } from './layout-tabset.store';

@Component({
  selector: 'lib-layout-tabset',
  standalone: true,
  imports: [
    CommonModule,
    NzLayoutModule,
    FormComponent,
    NzModalModule,
    NzSpaceModule,
    RouterModule,
    TabsModule,
    forwardRef(() => LayoutDynamicComponent),
    LayoutDialogComponent,
  ],
  templateUrl: './layout-tabset.component.html',
  styleUrl: './layout-tabset.component.less',
  providers: [LayoutTabsetStore, ModalComponent, ToastMessageComponent],
})
export class LayoutTabsetComponent
  extends LayoutCommonComponent<'tabset'>
  implements LayoutCommon<'tabset'>, AfterViewInit, OnInit
{
  router = inject(Router);
  #store = inject(LayoutTabsetStore);
  #layoutStore = inject(LayoutStore);
  filterService = inject(FilterService);
  bffService = inject(BffService);
  content = viewChild(NzContentComponent, { read: ElementRef });
  private readonly destroy$ = new Subject<void>();
  _eventSubjects = signal<Record<string, BehaviorSubject<any>> | undefined>(
    undefined,
  );
  private _refresh = signal(false);

  constructor(private route: ActivatedRoute) {
    super();
  }

  refreshData = () => {
    this._refresh.update((prev) => !prev);
  };

  effectFunctionSpec = effect(
    () => {
      this.#store.setCurrentFunctionSpec(this.functionSpec());
    },
    { allowSignalWrites: true },
  );
  currentModule = this.#layoutStore.currentModuleId;

  @ViewChild('formObj') dynamicForm?: FormComponent;
  modalComponent = inject(ModalComponent);
  toast = inject(ToastMessageComponent);
  _service = inject(BffService);
  loading = signal(false);
  data = signal<Data | null>(null);
  dataEffect = effect(
    () => {
      const url = this.url();
      this._refresh();
      if (!url) return;
      else {
        of(url)
          .pipe(
            tap(() => this.loading.set(true)),
            switchMap((url) =>
              this._service.getObject(url).pipe(
                tap((d) => {
                  this.data.set(d);
                  if (this.showForm()) {
                    this.resetForm.update((prev) => !prev);
                  }
                }),
              ),
            ),
            catchError((err) => {
              // this.toast.showToast(
              //   'error',
              //   'Error',
              //   err?.error?.message ?? err,
              // );
              // this.router.navigate(['not-found']);
              return of(null);
            }),
            tap(() => this.loading.set(false)),
          )
          .subscribe();
      }
    },
    { allowSignalWrites: true },
  );

  resetForm = signal(false);

  formMode = computed(() => {
    return this.functionSpec()?.form_config?._mode;
  });

  formValue = computed(() => {
    return this.data() ?? this.functionSpec()?.mock_data?.[0];
  });

  isBorder = computed(() => {
    return this.functionSpec()?.layout_options?.is_border;
  });

  tabsTitle = computed(() => {
    return this.functionSpec()?.layout_options?.tabs_title ?? [];
  });

  showForm = computed(
    () =>
      this.layoutOptions()?.show_details_form ||
      this.layoutOptions()?.show_create_form,
  );

  formConfig = computed(() => {
    if (this.layoutOptions()?.show_details_form) {
      return this.functionSpec().form_config;
    }
    if (this.layoutOptions()?.show_create_form) {
      return this.functionSpec().create_form;
    }

    return {};
  });

  formType = computed(() => {
    if (this.layoutOptions()?.show_details_form) return 'view';
    return 'create';
  });

  disabledTabKey = computed(() => {
    return (
      this.functionSpec()?.layout_options?.disabled_tab_key ?? {
        key: 'reportTypeId',
        tabs: [],
      }
    );
  });

  onActivate(e: any, id: string | undefined) {
    if (!id) return;
    e.subId.set(id);
  }

  fsChildren = computed(() => {
    const childrenActions = this.childrenActions();
    return (this.functionSpec().children as string[])
      .map((c, idx) => ({
        id: c,
        title: this.tabsTitle()[idx],
      }))
      ?.filter((c) => this.checkChildPermission(c.id, childrenActions))
      ?.map((c) => {
        const childrenInfo = this.getChildrenInfo(c.id, childrenActions);
        return {
          id: c.id,
          title: c.title,
          actions: childrenInfo?.actions ?? [],
          functionCode: childrenInfo?.functionCode ?? '',
        };
      });
  });

  isLayoutDetail = computed(() => this.layoutOptions()?.is_layout_detail);

  pageHeader = computed<PageHeader>(() => {
    const fs = this.functionSpec();
    const module = this.currentModule();
    const breadcrumb = [
      module,
      (fs.menu_item?.parent as MenuItem)?.title ?? '',
      fs.menu_item?.title ?? '',
    ].map((title) => ({ title }));

    return {
      title: fs.title ?? '',
      breadcrumb: breadcrumb,
      buttons: [],
    };
  });

  ngOnInit() {
    this.setupEventSubject();
    this.setupResizeObserver();
  }

  indexStart = 0;
  ngAfterViewInit(): void {
    const queryParams: NzSafeAny = this.route.snapshot.queryParams;
    const tab = queryParams?.tab ?? this.layoutOptions()?.default_selected_tab_index;
    if(isNil(tab)) return;
    this.indexStart = +tab;
  }

  ngOnDestroy(): void {
    this.destroy$.next(); // Emit to signal teardown
    this.destroy$.complete(); // Complete the destroy observable
  }

  private setupEventSubject() {
    const subjectsConfig = this.layoutOptions()?.child_event_subjects ?? [];
    if (subjectsConfig.length <= 0) return;
    const subjects: Record<string, BehaviorSubject<any>> = {};
    for (const config of subjectsConfig) {
      const subject = new BehaviorSubject(null);
      subjects[config.type] = subject;
      const action = config.action;
      subject.subscribe((value) => {
        switch (action) {
          case 'refresh': {
            this.refreshData();
          }
        }
      });
    }

    this._eventSubjects.set(subjects);
  }

  leftSpace = signal<number | null>(null);
  private setupResizeObserver() {
    const content = this.content()?.nativeElement as HTMLElement;

    if (!content) return;

    const updateLeftSpace = (height: number) => {
      const tabsElement = content.querySelector(
        '.ant-tabs-nav-wrap',
      ) as HTMLElement | null;
      this.leftSpace.set(height - (tabsElement?.offsetHeight ?? 0));
    };

    updateLeftSpace(content.offsetHeight);

    const resize$ = LayoutCommonComponent.createResizeObserverObservable(
      content,
    ).pipe(
      takeUntil(this.destroy$),
      // debounceTime(200), // Debounce to limit the number of events
      map((entries) => entries[0]?.contentRect), // Extract contentRect for size info
      distinctUntilChanged((prev, curr) => prev?.height === curr?.height),
    );

    resize$.subscribe((contentRect) => updateLeftSpace(contentRect.height));
  }

  selectedTab = 0;
  refreshTab = signal<Record<number, boolean>>({});
  onTabClick(idx: number) {
    this.selectedTab = idx;
    this.handleRefreshTab(idx);
  }

  handleRefreshTab(idx: number) {
    this.refreshTab.update((prev) => ({ ...(prev ?? {}), [idx]: !prev[idx] }));
  }

  layoutOptions = computed(() => {
    return this.functionSpec()?.layout_options;
  });

  showDialogSaveAddBtn = computed(() => {
    return this.layoutOptions()?.show_dialog_form_save_add_button || false;
  });

  isUploadFile = computed(() => {
    return this.layoutOptions()?.is_upload_file ?? false;
  });

  keySetTabCount = computed(() => {
    return this.layoutOptions()?.key_set_tab_count;
  });

  dialogVisible = signal(false);
  dialogConfig = signal<FormConfig>({});
  dialogType = signal<DialogType>('create');
  dialogValue = signal<Data | null | undefined>(null);

  dialogTitle = computed(() => {
    const fs = this.functionSpec();
    switch (this.dialogType()) {
      case 'view':
      case 'viewSchedule':
        return `View ${fs.title}`;
      case 'create':
        return `Create ${fs.title}`;
      case 'duplicate':
        return `Duplicate ${fs.title}`;
      case 'toDuplicate':
        return `Duplicate ${fs.title}`;
      case 'edit':
        return `Edit ${fs.title}`;
      case 'proceed':
        return `Insert New Record ${fs.title}`;
      case 'proceedCustom':
        return `Insert New Record ${fs.title}`;
      case 'filter':
        return `Filters`;
    }
  });

  override pageHeaderButtonClicked = (id: string) => {
    switch (id) {
      case 'create': {
        this.createClick();
        break;
      }
    }
  };

  createClick(value?: NzSafeAny) {
    this.dialogType.set('create');

    this.dialogConfig.set(this.functionSpec()?.form_config ?? {});
    this.dialogValue.set(value);
    this.dialogVisible.set(true);

    return;
  }

  // dialog action
  dialogSubmit(value: {
    type: DialogType | 'toDelete' | 'toEdit' | 'saveAndAddNew' | 'toSubmit';
    value: NzSafeAny;
    callback?: (status: boolean) => void;
  }) {
    const url = this.url();
    const { callback } = value ?? {};
    switch (value.type) {
      case 'create':
      case 'saveAndAddNew': {
        if (url)
          of(undefined)
            .pipe(
              switchMap(() => {
                if (this.isUploadFile())
                  return this.bffService.createFormData(
                    url,
                    trimObjectValues(value.value),
                  );
                return this.bffService.createItem(
                  url,
                  trimObjectValues(value.value),
                );
              }),
            )
            .subscribe({
              next: (val: NzSafeAny) => {
                callback?.(true);
                this.toast.showToast(
                  'success',
                  '',
                  'Record saved',
                );
                this.handleRefreshTab(this.selectedTab);
              },
              error: (err) => {
                callback?.(false);
                this.toast.showToast('error', 'Error', err.error?.message);
              },
            });
        else this.toast.showToast('success', 'Success', 'Saved Successfully');

        break;
      }
    }
  }

  setDisabled(title: string) {
    return !(this.data() ?? this.parent())?.[this.disabledTabKey().key] &&
      this.disabledTabKey()?.tabs?.includes(title)
      ? true
      : false;
  }

  setBadgeCount(title: string): number | undefined {
    const keySetTabCount = this.keySetTabCount();
    let count = undefined;
    if (keySetTabCount) {
      forOwn(keySetTabCount, (value, key) => {
        if (title === value) {
          count = isNumber(this.parent()?.[key]) ? this.parent()?.[key] : 0;
        }
        // count = undefined;
      });
      return count;
    }
    return count;
  }

  getChildrenInfo(
    fsdFE: string,
    childrenActions: ChildrenActionPermission[],
  ) {
    return childrenActions.find((c) => c.fsdFE === fsdFE);
  }

  checkChildPermission(
    fsdFE: string,
    childrenActions: ChildrenActionPermission[],
  ) {
    // console.log(childrenActions, 'alo');
    if (!childrenActions) return true;
    if (childrenActions.length === 0) return true;
    const childrenAction = childrenActions.find((c) => c.fsdFE === fsdFE);
    return childrenAction?.actions;
  }
}
function trimObjectValues(
  obj: Record<string, NzSafeAny>,
): Record<string, NzSafeAny> {
  return mapValues(obj, (value) => {
    if (isString(value) || isDate(value)) {
      return trim(toString(value));
    } else if (isObject(value) && !isArray(value)) {
      return trimObjectValues(value); // Recursively trim nested objects
    } else {
      return value;
    }
  });
}
