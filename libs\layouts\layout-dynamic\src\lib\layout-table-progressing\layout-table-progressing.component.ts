import { LayoutTableComponent } from '../layout-table/layout-table.component';
import { CommonModule, Location } from '@angular/common';
import {
  Component,
  computed,
  effect,
  forwardRef,
  Inject,
  inject,
  signal,
  TemplateRef,
  viewChild,
  OnInit,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute, NavigationEnd } from '@angular/router';
import { FormComponent } from '@hrdx-fe/dynamic-features';
// eslint-disable-next-line @nx/enforce-module-boundaries
import {
  LayoutDialogComponent,
  LayoutExpandFilterComponent,
  LayoutHistoryComponent,
  LayoutModalDialogComponent,
  LayoutStepDialogComponent,
  LayoutTableStore,
} from '@hrdx-fe/layout-simple-table';
// eslint-disable-next-line @nx/enforce-module-boundaries
import {
  ActionCodeProgressLayout,
  AuthActions,
  BffService,
  Data,
  ErrorCode,
  LayoutButton,
  Progressing,
} from '@hrdx-fe/shared';
import {
  BadgeComponent,
  ButtonComponent,
  CalculationProgressService,
  DATA_SERVICE,
  DataRenderComponent,
  DisplayComponent,
  IconComponent,
  IllustrationsComponent,
  ModalComponent,
  NewTableComponent,
  TbodyComponent,
  TdComponent,
  ThComponent,
  TheadComponent,
  ToastMessageComponent,
} from '@hrdx/hrdx-design';
import { QueryFilter } from '@nestjsx/crud-request';
import * as _ from 'lodash';
import {
  cloneDeep,
  every,
  find,
  forEach,
  isEqual,
  sortBy,
  uniqWith,
} from 'lodash';
import { NzSafeAny } from 'ng-zorro-antd/core/types';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzLayoutModule } from 'ng-zorro-antd/layout';
import { NzProgressModule } from 'ng-zorro-antd/progress';
import { NzRadioModule } from 'ng-zorro-antd/radio';
import { NzSwitchModule } from 'ng-zorro-antd/switch';
import {
  catchError,
  filter,
  finalize,
  forkJoin,
  interval,
  Observable,
  of,
  Subscription,
  switchMap,
  takeWhile,
  tap,
} from 'rxjs';
import { LayoutDetailComponent } from '../layout-detail/layout-detail.component';
import { LayoutHistoryDetailComponent } from '../layout-history-detail/layout-history-detail.component';
import { LayoutDynamicService } from '../services/layout-dynamic.service';
import { MixinsService } from '../layout-table/mixins/mixins.service';
import { AdjustDisplayComponent } from '../layout-table/adjust-display/adjust-display.component';
import { DeleteSummaryModalComponent } from '../layout-table/components/delete-summary';

@Component({
  selector: 'lib-layout-table-progressing',
  standalone: true,
  imports: [
    CommonModule,
    NewTableComponent,
    TdComponent,
    ThComponent,
    TbodyComponent,
    TheadComponent,
    DisplayComponent,
    ButtonComponent,
    NzLayoutModule,
    NzInputModule,
    ButtonComponent,
    NzIconModule,
    LayoutDialogComponent,
    ModalComponent,
    LayoutHistoryComponent,
    NzDropDownModule,
    NzSwitchModule,
    FormsModule,
    NzInputModule,
    DataRenderComponent,
    FormComponent,
    NzProgressModule,
    LayoutModalDialogComponent,
    LayoutExpandFilterComponent,
    NzRadioModule,
    IconComponent,
    LayoutStepDialogComponent,
    AdjustDisplayComponent,
    IllustrationsComponent,
    BadgeComponent,
    forwardRef(() => LayoutDetailComponent),
    forwardRef(() => LayoutHistoryDetailComponent),
  ],
  providers: [
    ModalComponent,
    ToastMessageComponent,
    MixinsService,
    {
      provide: DATA_SERVICE,
      useExisting: BffService,
    },
    DeleteSummaryModalComponent,
  ],
  templateUrl: './layout-table-progressing.component.html',
  styleUrls: [
    './layout-table-progressing.component.less',
    '../../lib/layout-table/layout-table.component.less',
  ],
})
export class LayoutTableProgressingComponent
  extends LayoutTableComponent
  implements OnInit
{
  //fsdId have progressing
  fsdIdProgressing = '';
  #store = inject(LayoutTableStore);

  override ngOnInit() {
    super.ngOnInit();
    const layout_options = this.functionSpec()?.layout_options;
    this.fsdIdProgressing = layout_options?.fsdIdProgressingPath ?? '';
  }

  constructor(
    override route: ActivatedRoute,
    @Inject(LayoutDynamicService)
    override _layoutDynamicService: LayoutDynamicService,
    override location: Location,
  ) {
    super(route, _layoutDynamicService, location);
    //init layout table in service
    this._layoutDynamicService.initLayoutTable(() => this);
    effect(
      () => {
        this.isProgressing();
        this.progressingSubscription?.unsubscribe();
        this.progressingSubscription = interval(5000)
          .pipe(
            tap(() => this.isSilentGetList.set(false)),
            takeWhile(() => this._layoutDynamicService.getProgressListening()), // Continue while the flag is true
            switchMap(() => this.getProgressList()), // Call your service
          )
          .subscribe();
      },
      { allowSignalWrites: true },
    );
    effect(
      () => {
        if (
          this.progressingInfo() &&
          this.fsdIdProgressing.includes(this.location.path())
        ) {
          this.getProgressInfoList();
        }

        this.routerSubscription?.unsubscribe();
        this.routerSubscription = this.router.events
          .pipe(filter((event) => event instanceof NavigationEnd))
          .subscribe((event: NavigationEnd) => {
            this._layoutDynamicService.setProgressListening(false);
            this.progressingService.close();
          });
      },
      { allowSignalWrites: true },
    );
  }

  modalDialogReset = signal<boolean>(false);
  calculate = (value: NzSafeAny) => {
    const url = cloneDeep(
      this.functionSpec()?.form_config?.calculateBackendUrl,
    );
    const codeKey = this.functionSpec()?.form_config?.codeCalculateKey;
    if (url) {
      this.loading.set(true);
      this._service
        .updateList(url, value, undefined, {
          authAction: AuthActions.Calculation,
        })
        .subscribe({
          next: () => {
            this.loading.set(false);
            this._layoutDynamicService.setProgressListening(false);
          },
          error: (err) => {
            this.loading.set(false);
            this.toast.showToast('error', 'Error', err.error?.message);
          },
          complete: () => {
            this.calcSettingCode.set(value?.[codeKey]);
            // this.modalDialogVisible.set(false);
            this.modalDialogReset.update((e) => !e);
            this.getProgressInfoList();
          },
        });
    } else {
      this.toast.showToast('error', 'Error', 'Incorrect backend URL');
    }
  };
  calcValue = signal<NzSafeAny>({});
  onActionShowPopupNote(isOk: boolean) {
    if (this.calcValue()?.value) {
      this.calcValue()?.isCalc
        ? this.calculate({ ...this.calcValue()?.value, isClearNote: isOk })
        : this.reCalculate({ ...this.calcValue()?.value, isClearNote: isOk });
    }
  }

  override async onActionOneClick(row: Data, action: NzSafeAny, event: Event) {
    if (event) event.stopPropagation();
    const { id } = action;

    switch (id) {
      case 'delete': {
        this.deleteClickOne(row.id, row, event);
        break;
      }
      case 'edit': {
        this.editClickOne(row.id, row, event);
        break;
      }
      case 'edit-custom':
        this.editClickOne(row.id, row, event, true);
        break;
      case 'lock': {
        this.lockClickOne(row, true, event);
        break;
      }
      case 'unlock': {
        this.lockClickOne(row, false, event);
        break;
      }
      case 'note': {
        if (this.isDynamicConfigTable()) {
          this._service
            .createItem('/api/payroll-employees/info', {
              payrollPeriodSettingCode: row?.['PayrollPeriodSettingCode'],
              payrollPeriodCode: row?.['PayrollPeriodCode'],
              employeeRecordNumber: row?.['EmployeeRecordNumber'],
              employeeId: row?.['EmployeeId'],
              isImport: this.parent()?.['isImport'],
            })
            .subscribe((data) => {
              this.listOfSelectedItems.set([{ ...data, ...row }]);
              this.setEventNote(true, data);
              return;
            });
        }
        this.getNoteBackendUrl.set(action?.href);
        this.listOfSelectedItems.set([row]);
        this.setEventNote(true, row);
        break;
      }
      case 'view': {
        this.viewClickOne(row.id, row, event);
        break;
      }
      case 'sub-detail': {
        this.subDetailClickOne(row.id, row, event);
        break;
      }
      case 'duplicate': {
        this.duplicateClick(structuredClone(row));
        break;
      }
      default: {
        this.handleActionOneClick(id, row);
        break;
      }
    }
  }

  modalClearNote = viewChild<TemplateRef<unknown>>('modalClearNote');
  showPopupNote(calcValue: NzSafeAny, isCalc: boolean) {
    this.calcValue.set({ value: calcValue, isCalc: isCalc });
    this.modalComponent.showDialog({
      nzTitle: 'Clear All Notes',
      nzContent: this.modalClearNote(),
      nzWrapClassName: `popup popup-confirm`,
      nzIconType: `icons:warning`,
      nzWidth: '400px',
      nzClosable: false,
      nzCentered: true,
      nzOkText: null,
    });
  }

  getNote = signal('');
  getPaymentDate = signal(undefined);
  getNoteBackendUrl = signal(undefined);

  noteFormValue = computed(() => {
    return {
      note: this.getNote(),
      paymentDate: this.getPaymentDate() ?? this.parent()?.['paymentDate'],
    };
  });
  noteForm = viewChild<FormComponent>('noteForm');
  async handleNote(backendUrl?: string, action = 'note') {
    let body: NzSafeAny = {};
    let url = backendUrl ?? this.noteBtnAttribute?.backendUrl ?? '';
    const selectedItem = this.listOfSelectedItems().length;

    //TODO - need to refactor later
    if (selectedItem > 0 || backendUrl) {
      body = {
        payrollEmployees: await this.appendBodyByAction(action),
        note: this.noteForm()?.value?.note,
        paymentDate: this.noteForm()?.value?.paymentDate,
      };
    } else {
      body = {
        payrollPeriodSettingCode: this.parent()?.['payrollPeriodSettingCode'],
        payrollPeriodCode: this.parent()?.['payrollPeriodCode'],
        calculationStatus:
          this.actionOneHandler()?.['note']?.keyValue?.['calculationStatus'],
        note: this.noteForm()?.value?.note,
        paymentDate: this.noteForm()?.value?.paymentDate,
      };
    }
    url = url + (selectedItem === 0 && !backendUrl ? '-all' : '');

    if (url)
      this._service
        .Patch(url, body ?? {}, undefined, {
          authAction: AuthActions.Update,
        })
        .subscribe({
          next: () => {
            this.toast.showToast('success', 'Success', `Note Successfully`);
            this.refreshData.update((e) => !e);
            this.getDetailByUrl(this.selectedItem());
            this.notePopupVisible.set(false);
            return;
          },
          error: (err) =>
            this.toast.showToast('error', 'Error', err.error?.message),
        });
  }

  override async clickedModalButton(item: NzSafeAny) {
    const url = this.url();
    switch (item.id) {
      case 'next':
        // if dialogType is edit, then update the item
        this.nextStep(
          item.value,
          item.dialogType === 'edit' ? 'edit-schedule' : 'new-schedule',
        );
        break;
      case 'save-draft':
        this.toast.showToast('success', '', 'Saved draft successfully');
        break;
      case 'generateReport':
        this.onGenerateReport(
          item,
          this.selectedItem()?.['url'],
          // '/api/labor-contract-due-date-without-salary-report',
        );
        break;
      case 'edit':
        this.editClickOne(item?.value?.id, item?.value);
        break;
      case 'edit-custom':
        this.editClickOne(item?.value?.id, item?.value, undefined, true);
        break;
      case 'deactive':
        if (url)
          of(undefined)
            .pipe(
              switchMap(() => {
                return this._service.updateItem(
                  url,
                  item?.value?.id,
                  {
                    ...item.value,
                    status: false,
                  },
                  undefined,
                  this.getFaceCodeForService(),
                );
              }),
            )
            .subscribe({
              next: () => {
                this.toast.showToast(
                  'success',
                  'Success',
                  'Saved Successfully',
                );
                // after create or edit success, view detail item

                this.refreshData.update((e) => !e);
              },
              error: (err) => {
                this.toast.showToast('error', 'Error', err.error?.message);
              },
            });
        else this.toast.showToast('success', 'Success', 'Saved Successfully');

        break;
      case 'active':
        if (url) {
          this._service
            .updateItem(
              url,
              item?.value?.id,
              { status: 'Active' },
              undefined,
              this.getFaceCodeForService(),
            )
            .subscribe();
        }
        break;
      case 'lock': {
        this.listOfSelectedItems.set([item?.value]);
        this.lockClickOne(item.value, true);
        break;
      }
      case 'unlock': {
        this.listOfSelectedItems.set([item?.value]);
        this.lockClickOne(item.value, false);
        break;
      }
      case 'note': {
        this.listOfSelectedItems.set([this.selectedItem() ?? item?.value]);
        // this.selectedItem.set(item?.value);
        this.getNoteBackendUrl.set(item?.backendUrl);
        this.setEventNote(true, item);
        break;
      }
      default:
        this.handleActionOneClick(item.id, item.value);
        break;
    }
  }

  // for tool table
  override async onToolTableClick(tool: LayoutButton) {
    const toolId = tool.id;
    switch (toolId) {
      case 'lock': {
        this.preAction(tool, ActionCodeProgressLayout.Lock);
        break;
      }
      case 'unlock': {
        this.preAction(tool, ActionCodeProgressLayout.Unlock);
        break;
      }
      case 'synthesize': {
        this.synthesizeClickMany(tool);
        break;
      }
      case 'integrate': {
        this.dialogConfig.set(this.functionSpec()?.create_form);
        this.integrateClick();
        break;
      }
      case 'reCalculate': {
        this.preAction(tool, ActionCodeProgressLayout.Calculation);
        // this.reCalculate(tool);
        break;
      }
      case 'note': {
        this.preAction(tool, ActionCodeProgressLayout.Note);
        break;
      }
      case 'calculate': {
        this.openProgressPopup();
        break;
      }
      case 'create': {
        this.createClick();
        break;
      }
      case 'export': {
        await this.handleExport();
        break;
      }
      case 'import': {
        // this.showImportDialog();
        this.router.navigate([tool.href ?? this.link_get_template_redirect()], {
          queryParams: tool?.paramsRedirect,
          queryParamsHandling: 'merge',
        });
        break;
      }
      default: {
        const btn = this.toolTable()?.find((btn) => btn.id === toolId);
        if (btn?.href) {
          const params = btn?.paramsRedirect;
          this.router.navigate([btn.href], {
            queryParams: params,
            queryParamsHandling: 'merge',
          });
        }
        break;
      }
    }
  }

  calcSettingCode = signal<string>('');
  override async modalDialogSubmit(item: { type: string; value: NzSafeAny }) {
    switch (item?.type) {
      case 'calculate':
        {
          const url = cloneDeep(
            this.functionSpec()?.form_config?.checkNoteBackendUrl,
          );

          // const codeKey = this.functionSpec()?.form_config?.codeCalculateKey;
          const calcValue = item?.value ?? {};
          if (!url) {
            this.calculate(calcValue);
            return;
          }
          of(url)
            .pipe(
              tap(() => this.loading.set(true)),
              switchMap((url) =>
                this._service.createItem(
                  url,
                  item?.value,
                  this.getFaceCodeForService(),
                  {
                    authAction: AuthActions.Calculation,
                  },
                ),
              ),
              catchError((err) => {
                this.toast.showToast(
                  'error',
                  'Error',
                  err?.error?.message ?? err,
                );
                return of(undefined);
              }),
              tap(() => this.loading.set(false)),
            )
            .subscribe((d) => {
              if (d?.hasNote) {
                this.showPopupNote(calcValue, true);
              } else this.calculate(calcValue);
            });
        }
        break;

      case 'identify':
        {
          const ivalue = item?.value;
          const custom_api = find(this.actionBtnFooterModalDialog, {
            id: 'identify',
          }).custom_api;
          const { backendUrl, method, body_list_keys } = custom_api;

          const body = _.pick(ivalue, body_list_keys);
          this.identity(backendUrl, method, body);
        }
        break;
      case 'integrate': {
        const url = await this.buildCustomUrl(
          cloneDeep(this.functionSpec()?.create_form?.integrateBackendUrl),
          this.parent() ?? {},
        );
        if (url) {
          this.loading.set(true);
          this._service
            .createItem(url, item?.value, this.getFaceCodeForService(), {
              authAction: AuthActions.Calculation,
            })
            .pipe(
              catchError((err) => {
                this.loading.set(false);

                this.toast.showToast('error', 'Error', err.error?.message);
                // return of(undefined);
                throw err;
              }),
              tap(() => {
                this.loading.set(false);
                this.refreshData.update((e) => !e);
              }),
              tap(() => {
                //handling progressing.
                this.modalDialogVisible.set(false);
                this.reloadPage();
              }),
            )
            .subscribe();
        }
        break;
      }
    }
  }

  identity(url: string, method: string, body: NzSafeAny) {
    switch (method) {
      case 'POST':
        if (url) {
          this.loading.set(true);
          this._service
            .createItem(url, body, undefined, {
              authAction: AuthActions.Calculation,
            })
            .pipe(
              catchError((err) => {
                this.loading.set(false);
                this.toast.showToast('error', 'Error', err.error?.message);
                throw err; // Rethrow error to stop subsequent operations
                // return of(undefined);
              }),
              tap(() => {
                this.loading.set(false);
                this._layoutDynamicService.setProgressListening(false);
              }),
              tap(() => {
                // this.modalDialogVisible.set(false);
                // this.modalDialogReset.update((e) => !e);
                this.getProgressInfoList();
              }),
            )
            .subscribe();
        }
        break;

      default:
        break;
    }
  }

  async integrateClick() {
    //key to show confirm popup.
    const {
      confirmConditionKey,
      title,
      content,
      body_confirm,
      filter_api,
      sort_order,
    } = this.dialogConfig()?.confirmPopup ?? {};
    const url = await this.buildCustomUrl(
      cloneDeep(this.dialogConfig()?.integrateBackendUrl),
      this.parent() ?? {},
    );
    //hanlding confirm popup
    if (
      url &&
      confirmConditionKey &&
      title &&
      content &&
      this.parent()?.[confirmConditionKey]
    ) {
      this.showConfirm(
        title,
        content,
        url,
        {
          ...body_confirm,
        },
        {
          value: this.parent(),
          filteredApi: filter_api,
          sortOrder: sort_order,
        },
      );
    } else {
      this.modalDialogType.set('create');
      this.modalDialogConfig.set(this.dialogConfig() ?? {});
      this.modalDialogValue.set(this.handleSetValueFromParent());
      this.modalDialogVisible.set(true);
      this.viewDialog = 'modal';
      this.modalDialogTitle.set(
        this.dialogConfig()?.dialog_title ?? `Start to Integrate`,
      );
      this.actionBtnFooterModalDialog =
        this.modalDialogConfig()?.btnModalDialogFooter;
    }
  }

  // progressingList = signal<Progressing[]>([]);
  progressingService = inject(CalculationProgressService);
  getProgressInfoListSubscription!: Subscription;
  //get progressing info
  getProgressInfoList() {
    const url = this.progressingInfo()?.getProgressingListApi;
    const filter = this.progressingInfo()?.filterMappingProgressing ?? [];
    if (url) {
      this.getProgressInfoListSubscription?.unsubscribe();
      this.getProgressInfoListSubscription = this._service
        .getItemCustom(url, filter)
        .pipe(
          catchError((err) => {
            this.toast.showToast('error', 'Error', err.error?.message);
            this._layoutDynamicService.setProgressListening(false);
            return of(this._layoutDynamicService.getProgressingList());
          }),
          tap((res) => {
            this.transformResProgressing(res);
          }),
        )
        .subscribe({
          complete: () => {
            this.setProgressingProperties();
          },
        });
    }
  }

  noteBtnAttribute?: LayoutButton;
  openNotePopup() {
    this.notePopupVisible.set(true);
  }

  setEventNote(isOne = false, item?: NzSafeAny, tool?: LayoutButton) {
    if (isOne) {
      this.getNote.set(item?.value?.['note'] ?? item?.['note']);
      this.getPaymentDate.set(
        item?.value?.['paymentDate'] ?? item?.['paymentDate'],
      );
    } else {
      this.getNote.set('');
      this.noteBtnAttribute = tool;
    }
    this.openNotePopup();
  }

  setProgressingProperties() {
    // this.refreshData.update((e) => !e);
    this.isProgressing.update((val) => !val);
    this._layoutDynamicService.setProgressListening(true);
    this.progressingService.setProgressingInfo(this.progressingInfo());
    this.progressingService.calculate({
      data: this._layoutDynamicService.getProgressingList(),
      cancel: this.handleCancelProgressing.bind(this),
      actionClicked: this.handlePopoverActionClicked.bind(this),
    });
  }

  getBodyProgresses() {
    const progresses = this._layoutDynamicService
      .getProgressingList()
      .filter((p: NzSafeAny) => p.id && p.progress !== 100)
      .map((p) => _.pick(p, ['id', 'type']));
    // const filter = [
    //   {
    //     field: 'pollIds',
    //     operator: '$eq',
    //     value: [...new Set(progresses)].join(','),
    //   },
    // ] as QueryFilter[];

    // return filter;
    const body = {
      getProgressRequestModels: [...progresses],
    };
    return body;
  }
  //get polling
  getProgressList(): Observable<NzSafeAny> {
    const updateProgressingApi =
      this.progressingService.progressingInfo?.['updateProgressingApi'];
    // const filter =
    //   this.progressingService.progressingInfo?.['filterMappingProgressing'] ??
    //   this.getFilterPollIds();
    // const noFilter =
    //   this.progressingService.progressingInfo?.['noFilterNeeded'];
    const currentProgressingList = structuredClone(
      this._layoutDynamicService.getProgressingList(),
    );
    const progressingList: Progressing[] = [];
    // if (!noFilter && filter.length === 0) {
    //   this._layoutDynamicService.setProgressListening(false);
    // }

    if (_.isObject(updateProgressingApi)) {
      const { url, method } = updateProgressingApi as {
        url: string;
        method: string;
      };

      const body = this.getBodyProgresses();

      return this.updateProgressingList(
        progressingList,
        currentProgressingList,
        url,
        method,
        body,
      );
    }
    const url = updateProgressingApi as string;
    return this.updateProgressingList(
      progressingList,
      currentProgressingList,
      url,
    );
  }

  completedSet = new Set<string>();
  updateProgressingList(
    progressingList: Progressing[],
    currentProgressingList: Progressing[],
    url: string,
    method?: string,
    body?: NzSafeAny,
  ) {
    return of(url).pipe(
      switchMap((url) => {
        return method === 'POST'
          ? this._service.createItem(url, body, this.getFaceCodeForService(), {
              authAction: AuthActions.Read,
            })
          : this._service.getList(url, 0, 0);
      }),
      catchError((err) => {
        this.toast.showToast('error', 'Error', err.error?.message);
        this._layoutDynamicService.setProgressListening(false);
        return of(undefined);
      }),
      tap((res) => {
        const completedList = res
          .filter((p: NzSafeAny) => p.progress === 100)
          .map((p: NzSafeAny) => p.id);
        const isSomeNewCompleted = completedList.some(
          (p: NzSafeAny) => !this.completedSet.has(p),
        );
        // if some new progress completed then get progress info list to remove progress is cancelled
        if (isSomeNewCompleted) {
          this.getProgressInfoList();
          this.completedSet = new Set(completedList);
          return;
        }
        this.afterGetProgressingList(
          res,
          progressingList,
          currentProgressingList,
        );
      }),
      tap((res) => {
        if (res?.length <= 0) {
          this.isSilentGetList.set(false);
          return;
        }
        this.isSilentGetList.set(true);
        this.refreshData.update((e) => !e);
      }),
    );
  }

  afterGetProgressingList(
    res: NzSafeAny,
    progressingList: Progressing[],
    currentProgressingList: Progressing[],
  ) {
    if (res.length > 0) {
      this.transformResProgressing(res);
      _.map(
        this._layoutDynamicService.getProgressingList(),
        (item: Polling) => {
          _.map(currentProgressingList, (progressing: Progressing) => {
            if (progressing.progress === 100) {
              progressingList.push({
                ...progressing,
                status: this.getStatus(100),
                progress: 100,
                type: progressing.type,
              });
              return;
            }
            if (item.id === progressing.id) {
              progressingList.push({
                ...progressing,
                status: this.getStatus(item.progress),
                progress: item.progress,
                type: progressing.type,
              });
            }
          });
        },
      );
      // update new Progressing list to dynamic service.
      this._layoutDynamicService.setProgressingList(
        sortBy(uniqWith(progressingList, isEqual), ['createdAt']).reverse(),
      );
      // update new Progressing list to popover component.
      this.progressingService.update(
        this._layoutDynamicService.getProgressingList(),
      );

      //refresh data when progressing is completed
      this.refreshDataProgressing(
        currentProgressingList,
        this._layoutDynamicService.getProgressingList(),
      );
    }
    // check if all progressing is completed, stop the subscription
    this.checkProgressListening(
      this._layoutDynamicService.getProgressingList(),
    );
  }

  checkProgressListening(list: NzSafeAny[]) {
    every(list, { progress: 100 }) &&
      this._layoutDynamicService.setProgressListening(false);
  }

  refreshDataProgressing(prevList: NzSafeAny[], list: NzSafeAny[]) {
    forEach(list, (currentItem) => {
      const progressingItem = find(prevList, { id: currentItem.id });
      if (
        progressingItem &&
        progressingItem.progress !== currentItem.progress &&
        currentItem.progress === 100
      ) {
        // Your logic here
        this.refreshData.update((e) => !e);
      }
    });
  }

  progressingInfo = computed(() => {
    const progressingInfo =
      this.functionSpec()?.layout_options?.progressing_info;
    return {
      bodyProgressing: progressingInfo?.['body_progressing'],
      titleProgressing:
        progressingInfo?.['title_progressing'] ?? 'Calculating salaries',
      isCancel: progressingInfo?.['is_cancel'] ?? true,
      isViewResult: progressingInfo?.['is_view_result'] ?? true,
      getProgressingListApi: progressingInfo?.['get_progressing_list_api'],
      updateProgressingApi: progressingInfo?.['update_progressing_list_api'],
      noFilterNeeded: progressingInfo?.['no_filter_needed'] ?? false,
      filterMappingProgressing: progressingInfo?.['filter_mapping_progressing'],
      uniqueBy: progressingInfo?.['unique_by'] ?? 'id',
    };
  });

  transformResProgressing(res: NzSafeAny) {
    const { id, title, subTitle, code, progress, createdAt, type, status } =
      this.progressingService?.progressingInfo?.['bodyProgressing'] ??
      this.progressingInfo().bodyProgressing;
    if (res) {
      res = _.map(res, (item: NzSafeAny) => {
        return {
          id: item[id],
          title: item[title],
          subTitle: item[subTitle] ?? null,
          code: item[code] ?? '',
          status: this.getStatus(item[progress], item[status]),
          progress: item[progress],
          type: item[type] ?? '',
          createdAt: item[createdAt] ?? 0,
        };
      });
      this._layoutDynamicService.setProgressingList(res);
    }
    return;
  }

  getStatus(percent: number, status?: string) {
    if (status === 'Deprecated') {
      return 'deprecated';
    }
    if (percent >= 0 && percent < 100) {
      return 'progressing';
    } else if (percent === 100) {
      return 'completed';
    } else {
      return 'failed';
    }
  }

  handleClosePopover() {
    const url = '/api/payroll-polling/un-register';
    const progressingList = structuredClone(
      this._layoutDynamicService.getProgressingList(),
    );
    const progresses = progressingList.map((p: NzSafeAny) => {
      return p.id;
    });
    this._service
      .update(url, {
        pollIds: progresses,
      })
      .subscribe({
        error: (err) => {
          this.toast.showToast('error', 'Error', err.error?.message);
        },
        complete: () => {
          // this.isProgressing.set(false);
          // this.isProgressListening = false;
          this._layoutDynamicService.setProgressListening(false);
        },
      });
  }

  handlePopoverActionClicked(action: {
    id: string;
    p_item: Record<string, NzSafeAny>;
  }) {
    const { id, p_item } = action;
    switch (id) {
      case 'view-result':
        this.viewResult(p_item?.['code']);
        break;
      case 'view-identify':
        this.viewIdentify(p_item?.['id']);
        break;
    }
  }

  modalIdentified = viewChild<TemplateRef<unknown>>('modalIdentified');

  identifyItem = signal<NzSafeAny | undefined>(undefined); //identify item
  viewIdentify(poll_id: string) {
    const url = '/api/identity-payroll-employee-polling/detail';

    const filter = [
      {
        field: 'pollId',
        operator: '$eq',
        value: poll_id,
      },
    ] as QueryFilter[];

    of(url)
      .pipe(
        switchMap(() =>
          this._service.getObjectByQuery(
            url,
            filter,
            undefined,
            this.getFaceCodeForService(),
          ),
        ),
        catchError((err) => {
          this.toast.showToast('error', 'Error', err.error?.message);
          return of(undefined);
        }),
        tap((res) => {
          if (res) this.identifyItem.set(res);
        }),
        finalize(() => {
          this.modalComponent.showDialog({
            nzTitle: `Payroll Identified`,
            nzContent: this.modalIdentified(),
            nzWrapClassName: 'popup popup-success schedule',
            nzIconType: 'icons:check-circle-bold',
            nzFooter: null,
            nzOkText: null,
            nzWidth: '400px',
            nzClosable: false,
            nzCentered: true,
            nzAutofocus: null,
          });
        }),
      )
      .subscribe();
  }

  handleCancelProgressing(id: number | undefined): void {
    //TODO - refactor url later
    const url = '/api/payroll-polling/cancel';
    const filter = [
      {
        field: 'pollId',
        operator: '$eq',
        value: id,
      },
    ] as QueryFilter[];
    const progressingList = structuredClone(
      this._layoutDynamicService.getProgressingList(),
    );

    this._service
      .update(url, {}, filter, this.getFaceCodeForService())
      .subscribe({
        error: (err) => {
          this.toast.showToast('error', 'Error', err.error?.message);
        },
        complete: () => {
          // const newList = remove(progressingList, (item) => item.id === id);
          const newList = progressingList.filter((item) => item.id !== id);
          this._layoutDynamicService.setProgressingList(newList);
          this.progressingService.update(newList);
          this.refreshData.update((e) => !e);
          this.toast.showToast(
            'success',
            'Success',
            'Cancel progressing successfully.',
          );
          if (progressingList.length === 0) {
            // this.isProgressing.set(false);
            // this.isProgressListening = false;
            this._layoutDynamicService.setProgressListening(false);
            this.progressingService.close();
          }
          return;
        },
      });
  }

  async preReCalculate(tool: LayoutButton) {
    this.noteBtnAttribute = tool;
    let body: NzSafeAny = {};
    const selectedItem = this.listOfSelectedItems().length;
    //TODO - need to refactor body later
    if (selectedItem > 0) {
      body = {
        payrollEmployees: await this.appendBodyByAction('reCalculate'),
      };
    } else {
      body = {
        payrollPeriodSettingCode: this.parent()?.['payrollPeriodSettingCode'],
        payrollPeriodCode: this.parent()?.['payrollPeriodCode'],
        calculationStatus:
          this.actionOneHandler()?.['reCalculate']?.keyValue?.[
            'calculationStatus'
          ],
      };
    }

    // const checkNoteUrl =

    const checkNoteUrl = structuredClone(
      this.functionSpec()?.layout_options?.checkNoteBackendUrl,
    );
    of(checkNoteUrl)
      .pipe(
        tap(() => this.loading.set(true)),
        switchMap((checkNoteUrl) =>
          this._service.createItem(
            checkNoteUrl + (selectedItem === 0 ? '-all' : ''),
            body,
            this.getFaceCodeForService(),
            { authAction: AuthActions.Calculation },
          ),
        ),
        catchError((err) => {
          this.toast.showToast('error', 'Error', err?.error?.message ?? err);
          return of(err);
        }),
        tap(() => this.loading.set(false)),
      )
      .subscribe((d) => {
        if (d?.error) return;
        if (d?.hasNote) {
          this.showPopupNote(body, false);
        } else this.reCalculate(body);
      });
  }

  async preAction(tool: LayoutButton, action: ActionCodeProgressLayout) {
    let params = {};
    console.log(this.tabCalculationStatus(), 'this.tabCalculationStatus()');
    params = {
      payrollPeriodSettingCode: this.parent()?.['payrollPeriodSettingCode'],
      payrollPeriodCode: this.parent()?.['payrollPeriodCode'],
      calculationStatus: this.tabCalculationStatus() ?? '',
      toolAction: action,
    };
    const checkEmployeeUrl = structuredClone(
      this.functionSpec()?.layout_options?.checkEmployeeBackendUrl,
    );

    if (checkEmployeeUrl) {
      of(checkEmployeeUrl)
        .pipe(
          tap(() => this.loading.set(true)),
          switchMap((checkEmployeeUrl) =>
            this._service.getItemCustom(
              checkEmployeeUrl,
              _.map(params, (value, field) => {
                return {
                  field,
                  operator: '$eq',
                  value,
                };
              }),
              this.getFaceCodeForService(),
              { authAction: this.getAuthAction(action) },
            ),
          ),
          catchError((err) => {
            this.toast.showToast('error', 'Error', err?.error?.message ?? err);
            return of(err);
          }),
          tap(() => this.loading.set(false)),
        )
        .subscribe((d) => {
          if (d?.error) return;
          if (d['hasEmployee']) {
            this.action(tool, action);
          } else {
            this.toast.showToast(
              'error',
              'Error',
              'There are no employees on the list',
            );
          }
        });
    } else {
      this.action(tool, action);
    }
  }

  getAuthAction(actionCode: ActionCodeProgressLayout) {
    let code: AuthActions = AuthActions.Read;
    switch (actionCode) {
      case ActionCodeProgressLayout.Calculation:
        code = AuthActions.Calculation;
        break;
      case ActionCodeProgressLayout.Note:
        code = AuthActions.Update;
        break;
      case ActionCodeProgressLayout.Lock:
        code = AuthActions.Lock;
        break;
      case ActionCodeProgressLayout.Unlock:
        code = AuthActions.Unlock;
        break;
      default:
        break;
    }
    return code;
  }

  async action(tool: LayoutButton, actionCode: ActionCodeProgressLayout) {
    switch (actionCode) {
      case ActionCodeProgressLayout.Calculation:
        this.preReCalculate(tool);
        break;

      case ActionCodeProgressLayout.Note:
        this.preNote(tool);
        break;
      case ActionCodeProgressLayout.Lock:
        this.isDynamicConfigTable()
          ? this.lockClickMany(tool.backendUrl ?? '', true)
          : this.lockunlockClickMany(tool);
        break;
      case ActionCodeProgressLayout.Unlock:
        this.isDynamicConfigTable()
          ? this.lockClickMany(tool.backendUrl ?? '', false)
          : this.lockunlockClickMany(tool);
        break;
      default:
        break;
    }
  }

  async preNote(tool: LayoutButton) {
    this.noteBtnAttribute = tool;
    let body: NzSafeAny = {};
    const selectedItem = this.listOfSelectedItems().length;
    //TODO - need to refactor body later
    if (selectedItem > 0) {
      body = {
        payrollEmployees: await this.appendBodyByAction('reCalculate'),
      };
    } else {
      body = {
        payrollPeriodSettingCode: this.parent()?.['payrollPeriodSettingCode'],
        payrollPeriodCode: this.parent()?.['payrollPeriodCode'],
        calculationStatus:
          this.actionOneHandler()?.['reCalculate']?.keyValue?.[
            'calculationStatus'
          ],
      };
    }

    // const checkNoteUrl =

    const checkNoteUrl = structuredClone(
      this.functionSpec()?.layout_options?.checkNoteBackendUrl,
    );
    of(checkNoteUrl)
      .pipe(
        tap(() => this.loading.set(true)),
        switchMap((checkNoteUrl) =>
          this._service.createItem(
            checkNoteUrl + (selectedItem === 0 ? '-all' : ''),
            body,
            this.getFaceCodeForService(),
            { authAction: AuthActions.Update },
          ),
        ),
        catchError((err) => {
          this.toast.showToast('error', 'Error', err?.error?.message ?? err);
          return of(err);
        }),
        tap(() => this.loading.set(false)),
      )
      .subscribe((d) => {
        if (d?.error) return;
        this.getNoteBackendUrl.set(undefined);
        this.setEventNote(false, {}, tool);
      });
  }

  onSaveNotePopup() {
    if (!this.noteForm()?.valid) {
      this.noteForm()?.setFormTouched();
      return;
    }
    this.modalComponent.showDialog(
      {
        nzTitle:
          'The new notes will replace the old notes. Are you sure you want to perform this action?',
        nzWrapClassName: `popup popup-confirm`,
        nzIconType: `icons:warning`,
        nzOkText: 'OK',
        nzCancelText: 'Cancel',
        nzWidth: '400px',
        nzClosable: false,
        nzCentered: true,
        nzFooter: null,
        nzOnOk: () => {
          this.handleNote(this.getNoteBackendUrl());
        },
      },
      'warning',
    );
  }

  reCalculate = (body: NzSafeAny) => {
    let url = this.noteBtnAttribute?.backendUrl;
    const selectedItem = this.listOfSelectedItems().length;
    url = url + (selectedItem === 0 ? '-all' : '');
    if (url)
      this._service
        .Patch(url, body ?? {}, undefined, {
          authAction: AuthActions.Calculation,
        })
        .pipe(
          catchError((err) => {
            this.toast.showToast('error', 'Error', err.error?.message);
            return of(err);
          }),
          tap(() => {
            // this.isProgressListening = false;
            this._layoutDynamicService.setProgressListening(false);
          }),
        )
        .subscribe({
          next: (res) => {
            // if there are no employees on the list when re-calculate payroll then not reload page.
            if (res?.error) return;

            this.refreshData.update((e) => !e);
            this.calcSettingCode.set(
              this.parent()?.['payrollPeriodSettingCode'],
            );
            this.reloadPage();
          },
        });
    else {
      this.toast.showToast('error', 'Error', 'Incorrect backend URL');
    }
  };

  async setViewData(value: NzSafeAny) {
    //TODO - refactor the url and filter.
    const url = `/api/report-types/${value.ReportTypeId ?? value.reportTypeId}/calculate-employee-detail/${value.EmployeeId ?? value.employeeId}/${value.MonthSalary ?? value.monthSalary}`;
    const filter = [
      {
        field: 'employeeRecordNumber',
        operator: '$eq',
        value: value.EmployeeRecordNumber ?? value.employeeRecordNumber,
      },
      {
        field: 'companyCode',
        operator: '$eq',
        value: value.CompanyCode ?? value.companyCode,
      },
      {
        field: 'payrollPeriodCode',
        operator: '$eq',
        value: value.PayrollPeriodCode ?? value.payrollPeriodCode,
      },
      {
        field: 'payrollPeriodSettingCode',
        operator: '$eq',
        value: value.PayrollPeriodSettingCode ?? value.payrollPeriodSettingCode,
      },
    ] as QueryFilter[];

    if (url) {
      this.dialogType.set('view');
      this.isViewDetailConfig.set(true);
      this.dialogVisible.set(true);
      this.loadingDetailSchedule.set(true);
      forkJoin([
        this._service.getObjectByQuery(
          url,
          filter,
          undefined,
          this.getFaceCodeForService(),
        ),
        this._service.Post(
          '/api/payroll-employees/info',
          {
            payrollPeriodSettingCode:
              value.PayrollPeriodSettingCode ?? value.payrollPeriodSettingCode,
            payrollPeriodCode:
              value.PayrollPeriodCode ?? value.payrollPeriodCode,
            employeeRecordNumber:
              value.EmployeeRecordNumber ?? value.employeeRecordNumber,
            employeeId: value.EmployeeId ?? value.employeeId,
            isImport: this.parent()?.['isImport'],
          },
          this.getFaceCodeForService(),
        ),
      ]).subscribe({
        next: ([data, detail]) => {
          if (data) {
            this.dataViewDetailConfig.set({
              //need to refactor three of funcs
              details: data?.details,
              settings: data?.settings,
            });
          }
          const _detail = detail as NzSafeAny;
          if (detail) {
            this.customDialogTitle.set(
              `View detail: ${_detail?.['employeeId']} - ${_detail?.['employeeGroupCode']} - ${_detail?.['employeeRecordNumber']} - ${_detail?.['fullName']}`,
            );
            this.dialogValue.set(detail);
          }
        },
        error: (err) => {
          this.loading.set(false);
          this.toast.showToast('error', 'Error', err.error?.message);
        },
        complete: () => {
          this.loadingDetailSchedule.set(false);
        },
      });
    }
  }
  getDetailByUrl(value: NzSafeAny) {
    if (value) {
      this.layoutDialog()?.loading.set(true);
      forkJoin([
        this._service.Post(
          '/api/payroll-employees/info',
          {
            payrollPeriodSettingCode:
              value.PayrollPeriodSettingCode ?? value.payrollPeriodSettingCode,
            payrollPeriodCode:
              value.PayrollPeriodCode ?? value.payrollPeriodCode,
            employeeRecordNumber:
              value.EmployeeRecordNumber ?? value.employeeRecordNumber,
            employeeId: value.EmployeeId ?? value.employeeId,
            isImport: this.parent()?.['isImport'],
          },
          this.getFaceCodeForService(),
        ),
      ]).subscribe({
        next: ([data]) => {
          const _data = data as NzSafeAny;
          if (_data) {
            this.dialogValue.set(_data);
          }
        },
        error: (err) => {
          this.layoutDialog()?.loading.set(false);
          this.toast.showToast('error', 'Error', err.error?.message);
        },
        complete: () => {
          this.layoutDialog()?.loading.set(false);
        },
      });
    }
  }

  override async viewClickOne(id: string, value: NzSafeAny, e?: Event) {
    if (e) e.stopPropagation();

    this.dialogConfig.set(this.functionSpec()?.form_config ?? {});
    if (this.isDynamicConfigTable() || this.viewDetailByConfig()?.is_show) {
      if (
        this.viewDetailByConfig()?.is_show &&
        !this.viewDetailByConfig()?.value_to_show?.includes(
          value[this.viewDetailByConfig()?.show_view_detail_by_key ?? ''],
        )
      ) {
        return;
      }
      this.selectedItem.set(value);
      await this.setViewData(value);
      return;
    } else {
      const { actionId, params } = this.actionClickRow() || {};

      if (actionId && params) {
        switch (actionId) {
          case 'routing': {
            const href = this.location.path() + '/' + value[params];
            this._layoutDynamicService.setParentData(value);
            this.router.navigate([href]);
            return;
          }
        }
      }

      this.selectedItem.set(value);
      this.#store.setDetailId(value?.id || '');
      this.#store.setDataDetail(value);

      this.dialogType.set('view');
      // this.dialogConfig.set(this.functionSpec()?.form_config ?? {});
      this.dialogValue.set(value);
      if (this.functionSpec().detail_function_spec) {
        this.dialogVisible.set(true);
      } else {
        this.detailDialogVisible.set(true);
      }
    }
  }
}

// interface Progressing {
//   id: number;
//   title: string;
//   subTitle: string;
//   code: string;
//   status: 'progressing' | 'pending' | 'completed' | 'failed';
//   progress: number;
//   createdAt?: number;
//   version?: string;
// }

interface Polling {
  id: number;
  progress: number;
}

interface PayrollIdentify {
  payrollPeriodSettingName: string;
  payrollPeriodName: string;
  totalPaidEmployees: number;
  totalAddNew: number;
  totalRemoved: number;
}
