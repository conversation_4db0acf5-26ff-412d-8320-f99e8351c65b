<div class="add-person-container">
  <div class="steps-navigation">
    <nz-steps
      class="steps-container"
      [nzCurrent]="currentStep"
      nzType="navigation"
    >
      <nz-step class="biographical-detail-step" nzTitle="Biographical Detail">
        <ng-template nzStepIcon>1</ng-template>
      </nz-step>
      <div class="step-separator">
        <hrdx-icon name="icon-caret-right-bold"></hrdx-icon>
      </div>
      <nz-step class="org-relationship-step" nzTitle="Org Relationship">
        <ng-template nzStepIcon>2</ng-template>
      </nz-step>
    </nz-steps>
  </div>

  <lib-biographical-details
    [hidden]="currentStep !== 0"
    #biographicalDetails
    (formReady)="onBiographicalFormReady()"
    (formValid)="onBiographicalFormValidityChange($event)"
    [basicInfoPermission]="basicInfoPermission()"
  >
  </lib-biographical-details>

  <lib-org-relationship
    #orgRelationship
    [hidden]="currentStep !== 1"
    (formReady)="onOrgRelationshipFormReady()"
    (formValid)="onOrgRelationshipFormValidityChange($event)"
    (jobDataEffectiveDateChanged)="onJobDataEffectiveDateChanged($event)"
  >
  </lib-org-relationship>
</div>

<ng-template #addPersonFooter>
  <div class="dialog-footer">
    <div class="equal-width-buttons">
      <hrdx-button
        *ngIf="!isFormSubmissionSuccessful"
        [title]="currentStep === 0 ? 'Cancel' : 'Back'"
        (clicked)="currentStep === 0 ? handleCancelForm() : moveToPrevious()"
        [type]="'tertiary'"
        [size]="'default'"
      >
      </hrdx-button>
      <hrdx-button
        *ngIf="!isFormSubmissionSuccessful"
        [type]="'primary'"
        [size]="'default'"
        [isLoading]="isLoading"
        [title]="currentStep === 1 ? 'Save' : 'Next'"
        (clicked)="currentStep === 1 ? submitForm() : moveToNext()"
      >
      </hrdx-button>

      <hrdx-button
        *ngIf="isFormSubmissionSuccessful"
        [type]="'primary'"
        [size]="'default'"
        [title]="'Go to Employee Profile'"
        (clicked)="goToEmployeeProfile()"
      >
      </hrdx-button>
    </div>
  </div>
</ng-template>

<!-- <ng-template #contentCancel let-ref="modalRef">
  <hrdx-modal-action
    [type]="'warning'"
    [title]="'Please confirm'"
    [content]="'You have unsaved change. All your changes will be lost.'"
    [iconName]="'icon-warning-circle-bold'"
  ></hrdx-modal-action>
  <div class="modal-footer-action">
    <hrdx-button (clicked)="handleCancelConfirmation(false)" [title]="'Back'"></hrdx-button>
    <hrdx-button [type]="'primary'" (clicked)="handleCancelConfirmation(true)" [title]="'Confirm'"></hrdx-button>
  </div>
</ng-template>-->

<ng-template #modalTitle>
  <app-employeeid-title
    [personId]="personId"
    [title]="titleModal"
  ></app-employeeid-title>
</ng-template>

<!-- Add at the bottom of the file -->
<ng-template #contentCancel let-ref="modalRef">
  <hrdx-modal-action [type]="'warning'">
    <span
      >You have unsaved changes. Do you want to save changes before
      continuing?</span
    >
  </hrdx-modal-action>
  <div class="modal-footer-action">
    <hrdx-button
      [type]="'tertiary'"
      [title]="'Back'"
      [size]="'default'"
      (clicked)="handleCancelConfirmation(false)"
    />
    <hrdx-button
      [type]="'tertiary'"
      [title]="'Don\'t save'"
      [size]="'default'"
      (clicked)="handleCancelConfirmation(true)"
    />
    <hrdx-button
      [type]="'primary'"
      (clicked)="handleSaveConfirmation()"
      [title]="'Save'"
      [size]="'default'"
      [disabled]="false"
    />
  </div>
</ng-template>
