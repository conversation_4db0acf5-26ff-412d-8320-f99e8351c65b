import {
  Component,
  ElementRef,
  inject,
  OnInit,
  signal,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, ReactiveFormsModule } from '@angular/forms';
import { NZ_MODAL_DATA, NzModalModule, NzModalRef } from 'ng-zorro-antd/modal';
import { NzStepsModule } from 'ng-zorro-antd/steps';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzAnchorModule } from 'ng-zorro-antd/anchor';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzGridModule } from 'ng-zorro-antd/grid';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzUploadModule } from 'ng-zorro-antd/upload';
import { NzRadioModule } from 'ng-zorro-antd/radio';
import {
  ButtonComponent,
  IconComponent,
  ModalActionComponent,
  ToastMessageComponent,
} from '@hrdx/hrdx-design';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { BiographicalDetailsComponent } from '../biographical-details/biographical-details.component';
import { OrgRelationshipComponent } from '../org-relationship/org-relationship.component';
import { HrFsBp001Service } from '../services/hr.fs.bp.001.service';
import { NzModalService } from 'ng-zorro-antd/modal';
import { NzButtonComponent } from 'ng-zorro-antd/button';
import { Router } from '@angular/router';
import { EmployeeIdTitleComponent } from '../employeeid-title.component';
import { BasicInfoFunctionCode } from '@hrdx-fe/shared';
import { combineLatest, firstValueFrom } from 'rxjs';
@Component({
  selector: 'lib-add-person',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    NzStepsModule,
    NzFormModule,
    NzAnchorModule,
    NzDatePickerModule,
    NzInputModule,
    NzSelectModule,
    NzUploadModule,
    NzRadioModule,
    ButtonComponent,
    IconComponent,
    ModalActionComponent,
    NzCardModule,
    NzCheckboxModule,
    BiographicalDetailsComponent,
    OrgRelationshipComponent,
    NzModalModule,
    NzButtonComponent,
    EmployeeIdTitleComponent,
    NzGridModule,
  ],
  providers: [HrFsBp001Service, ToastMessageComponent],
  templateUrl: './add-person.component.html',
  styleUrl: './add-person.component.less',
})
export class AddPersonComponent implements OnInit {
  // ref to the footer template
  @ViewChild('addPersonFooter', { static: true }) modalFooter!: TemplateRef<{}>;

  @ViewChild(BiographicalDetailsComponent)
  biographicalDetails!: BiographicalDetailsComponent;
  @ViewChild(OrgRelationshipComponent)
  orgRelationship!: OrgRelationshipComponent;

  @ViewChild('contentCancel') contentCancel!: TemplateRef<''>;
  @ViewChild('modalTitle', { static: true }) modalTitle!: TemplateRef<any>;

  currentStep = 0;
  isBiographicalFormValid = false;
  isOrgRelationshipFormValid = false;
  hrFsBp001Service: HrFsBp001Service = inject(HrFsBp001Service);
  toast: ToastMessageComponent = inject(ToastMessageComponent);
  isNextButtonEnabled = false;
  isCancelConfirmVisible = false;

  // ref to the modal service
  modalService: NzModalService = inject(NzModalService);
  private closeIntercepted = false;

  // a flag to check if the form submission is successful
  isFormSubmissionSuccessful = false;

  readonly nzModalData: any = inject(NZ_MODAL_DATA);

  // a flag to check if the form is submitted
  isLoading = false;
  router: Router = inject(Router);

  // newly created employee id
  newEmployeeId = '';

  isStep0Valid = false;
  isStep1Valid = false;

  personId = '';
  readonly defaultTitleModal: string = 'Add New Person';
  titleModal: string = this.defaultTitleModal;

  basicInfoPermission = signal<Record<string, any> | null>(null);

  constructor(
    private fb: FormBuilder,
    private modalRef: NzModalRef,
    private elementRef: ElementRef,
  ) {}

  ngOnInit(): void {
    // get person id from modal data
    this.personId = this.nzModalData.personId ?? 'NEW';
    this.modalRef.updateConfig({
      nzTitle: this.modalTitle,
    });
    this.getAllPermissionForField();
  }

  async getAllPermissionForField() {
    const allPermission = await firstValueFrom(
      combineLatest({
        socialNamePermission: this.hrFsBp001Service.getPermissionByFunctionCode(
          BasicInfoFunctionCode.SOCIAL_NAME,
        ),
        specialNamePermission:
          this.hrFsBp001Service.getPermissionByFunctionCode(
            BasicInfoFunctionCode.SPECIAL_NAME,
          ),
      }),
    );

    this.basicInfoPermission.set(allPermission);
  }

  onBiographicalFormReady() {}

  onBiographicalFormValidityChange(isValid: boolean) {
    this.isBiographicalFormValid = isValid;
    this.isStep0Valid = isValid;

    this.updateNextButtonState();
  }

  onOrgRelationshipFormReady() {}

  onOrgRelationshipFormValidityChange(isValid: boolean) {
    this.isOrgRelationshipFormValid = isValid;
    this.isStep1Valid = isValid;

    this.updateNextButtonState();
  }

  // get form biographicalDetails validity
  getBiographicalFormValidity() {
    return this.biographicalDetails.isFormValid();
  }

  // get form orgRelationship validity
  getOrgRelationshipFormValidity() {
    return this.orgRelationship.isFormValid();
  }

  moveToNext() {
    if (this.currentStep === 0 && this.biographicalDetails) {
      const validationResult =
        this.biographicalDetails.validateFormWithMessage();

      if (!validationResult.isValid) {
        // scroll to the first error
        HrFsBp001Service.scrollToFirstError();

        // Show all validation messages if there are any
        if (validationResult.messages && validationResult.messages.length > 0) {
          this.toast.showToast(
            'warning',
            'Validation Error',
            validationResult.messages.join('\n'),
          );
        } else {
          this.toast.showToast(
            'warning',
            'Validation Error',
            'Please fill in all required fields correctly',
          );
        }
        return;
      }

      // get the effective date from biographical details form
      const biographicalData =
        this.biographicalDetails.getFormValue().biographicalData;
      // only set effective date to org relationship form if it is not set yet or if jobData is empty
      if (
        biographicalData &&
        biographicalData.effectiveDateFrom &&
        (!this.orgRelationship.getEffectiveDate() ||
          this.orgRelationship.isJobDataEmpty() ||
          this.orgRelationship.canUpdateJobDataEffectiveDate)
      ) {
        // set the effective date to org relationship form
        this.orgRelationship.setEffectiveDate(
          biographicalData.effectiveDateFrom,
        );
      }

      // set the min effective date to org relationship form
      this.orgRelationship.setMinEffectiveDate(
        biographicalData.effectiveDateFrom,
      );

      // Cleanup subscriptions before moving to next step
      this.biographicalDetails.cleanupSubscriptions();

      // change current step to 1
      this.currentStep++;

      // update the title modal = full name
      const fullName =
        this.biographicalDetails.getFormValue().biographicalData.fullName;
      this.titleModal = fullName;

      // set person id to org relationship form
      this.orgRelationship.setPersonId(this.personId);
      // set full name to org relationship form
      this.orgRelationship.setFullName(fullName);

      // update the next button state
      this.updateNextButtonState();
    }
  }

  moveToPrevious() {
    if (this.currentStep > 0) {
      this.currentStep--;
    }
    // if current step is 0, reset the title modal
    if (this.currentStep === 0) {
      this.titleModal = this.defaultTitleModal;

      // resubscribe to changes
      this.biographicalDetails.resubscribeToChanges();

      // // compare effective date of biographical details and org relationship
      // const biographicalEffectiveDate = this.biographicalDetails.getEffectiveDate();
      // const orgRelationshipEffectiveDate = this.orgRelationship.getEffectiveDate();
      // if (biographicalEffectiveDate && orgRelationshipEffectiveDate && biographicalEffectiveDate.getTime() !== orgRelationshipEffectiveDate.getTime()) {
      //   // set the effective date to biographical details form
      //   this.biographicalDetails.setEffectiveDate(orgRelationshipEffectiveDate);
      // }
    }
  }

  submitForm() {
    if (this.biographicalDetails && this.orgRelationship) {
      // Validate both forms before submission
      const biographicalValidation =
        this.biographicalDetails.validateFormWithMessage();
      const orgRelationshipValidation =
        this.orgRelationship.validateFormWithMessage();

      if (
        !biographicalValidation.isValid ||
        !orgRelationshipValidation.isValid
      ) {
        // scroll to the first error
        HrFsBp001Service.scrollToFirstError();

        this.toast.showToast(
          'warning',
          'Validation Error',
          'Please fill in all required fields correctly',
        );
        return;
      }

      // start loading
      this.isLoading = true;

      // get the form data
      const biographicalFormData = this.biographicalDetails.getFormValue();
      const orgRelationshipFormData = this.orgRelationship.getFormValue();

      const formData = {
        biographicalDetails: biographicalFormData.biographicalData,
        taxInfo: biographicalFormData.taxInfo,
        orgRelationship: orgRelationshipFormData,
      };

      // get employeeid from modal data
      const personId = this.nzModalData.personId;

      // convert formData to FormData
      const outputFormData =
        personId && personId.trim() !== ''
          ? this.hrFsBp001Service.convertJsonToFormData(
              formData,
              false,
              personId,
            )
          : this.hrFsBp001Service.convertJsonToFormData(formData, true, 'T');

      // Handle form submission

      this.hrFsBp001Service.createEmployee(outputFormData).subscribe({
        next: (res: any) => {
          // reset loading
          this.isLoading = false;

          // get the employee id from the response
          this.newEmployeeId = res.employeeId;

          // update the person id
          this.personId = this.newEmployeeId;

          // show toast message
          this.toast.showToast('success', 'Success', 'Record saved');
          // form submit okie
          this.isFormSubmissionSuccessful = true;
          // disable the current form
          this.orgRelationship.disableForm();
        },
        error: (err: any) => {
          // reset loading
          this.isLoading = false;
          // show toast message
          this.toast.showToast('error', 'Error', err.error?.message);
        },
      });
    } else {
      console.log(
        'Form is invalid or biographical details component is not available',
      );
    }
  }

  updateNextButtonState() {
    if (this.currentStep === 0) {
      this.isNextButtonEnabled = this.isStep0Valid;
    } else if (this.currentStep === 1) {
      this.isNextButtonEnabled = this.isStep1Valid;
    }
  }

  // cancel modalref
  cancelModalRef: NzModalRef | null = null;

  // show the confirmation dialog
  showCancelConfirmation(): void {
    this.cancelModalRef = this.modalService.confirm({
      nzContent: this.contentCancel,
      nzWrapClassName:
        'popup popup-confirm hide-footer-btns custom modal-centered',
      nzOkText: null,
      nzFooter: null,
      nzCancelText: null,
      nzClosable: false,
      nzMaskClosable: false,
      nzWidth: '400px',
      nzCentered: true,
      nzOnCancel: () => {
        this.closeIntercepted = false;
      },
    });
  }

  handleCancelConfirmation(confirm: boolean) {
    // if confirm is true, close the modal
    if (confirm) {
      this.modalRef.close();
      this.cancelModalRef?.close();
    } else {
      this.cancelModalRef?.close();
    }
  }

  handleSaveConfirmation() {
    this.currentStep === 1 ? this.submitForm() : this.moveToNext();
    this.cancelModalRef?.close();
  }

  hasFormChanges(): boolean {
    // Check if there are any changes in the biographical form
    const biographicalFormData = this.biographicalDetails?.getFormValue();
    if (!biographicalFormData) return false;

    // Define default fields to exclude for each block
    const defaultFields = {
      basicInfo: [
        'effectiveDateFrom',
        'birthCountryCode',
        'nationalityCode',
        'nationalIds', // nested array
        'phoneInfos', // nested array
        'taxInfo', // nested object
      ],
      nationalIds: ['countryCode', 'enabled', 'isPrimary'],
      pitCode: ['countryCode', 'status'],
      phoneInfo: ['startDate', 'isPrimary'],
    };

    const hasBasicInfoChanges = Object.keys(
      biographicalFormData.biographicalData,
    ).some((key) => {
      // Skip default fields, computed fields, and nested arrays
      if (defaultFields.basicInfo.includes(key)) {
        return false;
      }
      const value = biographicalFormData.biographicalData[key];
      // Check if value is not an array and has actual value
      return value !== null && value !== '' && !Array.isArray(value);
    });

    const hasNationalIdChanges =
      biographicalFormData.biographicalData.nationalIds?.some(
        (nationalId: Record<string, any>) =>
          Object.keys(nationalId).some((key) => {
            if (defaultFields.nationalIds.includes(key)) {
              return false;
            }
            const value = nationalId[key];
            return value !== null && value !== '';
          }),
      );

    const hasPitCodeChanges = Object.keys(biographicalFormData.taxInfo).some(
      (key) => {
        if (defaultFields.pitCode.includes(key)) {
          return false;
        }
        const value = biographicalFormData.taxInfo[key];
        return value !== null && value !== '';
      },
    );

    const hasPhoneChanges =
      biographicalFormData.biographicalData.phoneInfos?.some(
        (phone: Record<string, any>) =>
          Object.keys(phone).some((key) => {
            if (defaultFields.phoneInfo.includes(key)) {
              return false;
            }
            const value = phone[key];
            return value !== null && value !== '';
          }),
      );

    return (
      hasBasicInfoChanges ||
      hasNationalIdChanges ||
      hasPitCodeChanges ||
      hasPhoneChanges
    );
  }

  handleModalClose(): boolean {
    // if form is submitted, do not show the confirmation dialog
    if (this.isFormSubmissionSuccessful) {
      return true;
    }

    const hasChanges = this.hasFormChanges();

    // Only show cancel confirmation if there are changes
    if (!this.closeIntercepted && hasChanges) {
      this.showCancelConfirmation();
    } else {
      // If no changes, just close the modal
      return true;
    }
    return false;
  }

  goToEmployeeProfile() {
    // get the employee profile link from the modal data
    const employeeProfileLink = this.nzModalData.employeeProfileLink;
    if (employeeProfileLink) {
      // close dialog before navigating
      this.modalRef.close();
      // navigate to the employee profile link
      // user router to navigate to the employee profile link
      this.router.navigate([`${employeeProfileLink}/${this.newEmployeeId}`]);
    }
  }

  handleCancelForm() {
    // if form is submitted, do not show the confirmation dialog
    if (this.isFormSubmissionSuccessful) {
      this.handleCancelConfirmation(true);
      return;
    }

    const hasChanges = this.hasFormChanges();

    // Only show cancel confirmation if there are changes
    if (!this.closeIntercepted && hasChanges) {
      this.showCancelConfirmation();
    } else {
      // If no changes, just close the modal
      this.handleCancelConfirmation(true);
    }
  }

  onJobDataEffectiveDateChanged(effectiveDate: Date) {
    // set the effective date to biographical details form
    this.biographicalDetails.setJobDataEffectiveDate(effectiveDate);
  }
}
