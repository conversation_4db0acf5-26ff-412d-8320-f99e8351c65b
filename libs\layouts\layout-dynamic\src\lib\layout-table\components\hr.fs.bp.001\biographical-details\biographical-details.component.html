<div class="content-wrapper">
  <div class="quick-nav">
    <nz-anchor [nzAffix]="false">
      <nz-link nz-href="#basic-info" (click)="scrollToSection('basic-info')">
        <div class="menu-item">
          <span class="menu-number">1</span>
          <span class="menu-title">Basic Information</span>
          <span class="menu-required">*</span>
        </div>
      </nz-link>
      <nz-link nz-href="#national-id" (click)="scrollToSection('national-id')">
        <div class="menu-item">
          <span class="menu-number">2</span>
          <span class="menu-title">National ID</span>
          <span class="menu-required">*</span>
        </div>
      </nz-link>
      <nz-link nz-href="#ptt-code" (click)="scrollToSection('ptt-code')">
        <div class="menu-item">
          <span class="menu-number">3</span>
          <span class="menu-title">PIT Code</span>
        </div>
      </nz-link>
      <nz-link nz-href="#phone-info" (click)="scrollToSection('phone-info')">
        <div class="menu-item">
          <span class="menu-number">4</span>
          <span class="menu-title">Phone Info</span>
        </div>
      </nz-link>
    </nz-anchor>
  </div>
  <div class="main-content">
    <form
      nz-form
      [formGroup]="biographicalDetailForm"
      nzLayout="vertical"
      preventEnterSubmit
      class="hrdx-form"
    >
      <div id="basic-info" class="section">
        <h3>Basic Information</h3>
        <div nz-row [nzGutter]="16">
          <div nz-col [nzSpan]="12">
            <nz-form-item>
              <nz-form-label nzRequired nzNoColon>Effective Date</nz-form-label>
              <nz-form-control [nzErrorTip]="effectiveDateErrorTpl">
                <nz-date-picker
                  datePickerBlur
                  (datePickerBlur)="
                    selectDate($event.event, 'effectiveDateFrom', true)
                  "
                  [nzSuffixIcon]="nzSuffixIcon"
                  formControlName="effectiveDateFrom"
                  nzFormat="dd/MM/yyyy"
                  nzPlaceHolder="DD/MM/YYYY"
                  [nzDisabledDate]="disabledFutureDate"
                >
                </nz-date-picker>
                <ng-template #effectiveDateErrorTpl let-control>
                  <ng-container *ngIf="control.hasError('required')"
                    >Cannot be empty</ng-container
                  >
                  <ng-container *ngIf="control.hasError('jobDataDateOrder')">{{
                    control.errors?.['jobDataDateOrderMessage']
                  }}</ng-container>
                </ng-template>
              </nz-form-control>
            </nz-form-item>
          </div>
        </div>
        <nz-card nzTitle="Name Information">
          <div nz-row [nzGutter]="16">
            <div nz-col [nzSpan]="12">
              <nz-form-item>
                <nz-form-label nzRequired nzNoColon>First Name</nz-form-label>
                <nz-form-control nzErrorTip="Cannot be empty">
                  <input
                    [maxlength]="40"
                    nz-input
                    formControlName="firstName"
                    placeholder="Enter First Name"
                  />
                </nz-form-control>
              </nz-form-item>
            </div>
            <div nz-col [nzSpan]="12">
              <nz-form-item>
                <nz-form-label nzNoColon>Middle Name</nz-form-label>
                <nz-form-control>
                  <input
                    [maxlength]="40"
                    nz-input
                    formControlName="middleName"
                    placeholder="Enter Middle Name"
                  />
                </nz-form-control>
              </nz-form-item>
            </div>
          </div>
          <div nz-row [nzGutter]="16">
            <div nz-col [nzSpan]="12">
              <nz-form-item>
                <nz-form-label nzRequired nzNoColon>Last Name</nz-form-label>
                <nz-form-control nzErrorTip="Cannot be empty">
                  <input
                    [maxlength]="40"
                    nz-input
                    formControlName="lastName"
                    placeholder="Enter Last Name"
                  />
                </nz-form-control>
              </nz-form-item>
            </div>
            <div nz-col [nzSpan]="12">
              <nz-form-item>
                <nz-form-label nzNoColon>Full Name</nz-form-label>
                <nz-form-control>
                  <input
                    nz-input
                    formControlName="fullName"
                    [disabled]="true"
                  />
                </nz-form-control>
              </nz-form-item>
            </div>
          </div>
          <div nz-row [nzGutter]="16">
            <div
              nz-col
              [nzSpan]="12"
              *ngIf="basicInfoPermission()?.['socialNamePermission']?.Read"
            >
              <nz-form-item>
                <nz-form-label nzNoColon>Social Name</nz-form-label>
                <nz-form-control>
                  <input
                    [maxlength]="120"
                    nz-input
                    formControlName="socialName"
                    placeholder="Enter Social Name"
                  />
                </nz-form-control>
              </nz-form-item>
            </div>
            <div
              nz-col
              [nzSpan]="12"
              *ngIf="basicInfoPermission()?.['specialNamePermission']?.Read"
            >
              <nz-form-item>
                <nz-form-label nzNoColon>Special Name</nz-form-label>
                <nz-form-control>
                  <input
                    [maxlength]="120"
                    nz-input
                    formControlName="specialName"
                    placeholder="Enter Special Name"
                  />
                </nz-form-control>
              </nz-form-item>
            </div>
          </div>
          <div nz-row [nzGutter]="16">
            <div nz-col [nzSpan]="12">
              <nz-form-item>
                <nz-form-label nzNoColon>Common Name</nz-form-label>
                <nz-form-control>
                  <input
                    [maxlength]="120"
                    nz-input
                    formControlName="commonName"
                    placeholder="Enter Common Name"
                  />
                </nz-form-control>
              </nz-form-item>
            </div>
          </div>
        </nz-card>
        <nz-card nzTitle="Biographic Information">
          <div nz-row [nzGutter]="16">
            <div nz-col [nzSpan]="12">
              <nz-form-item>
                <nz-form-label nzRequired nzNoColon
                  >Date of Birth</nz-form-label
                >
                <nz-form-control
                  [nzValidateStatus]="getBirthDateValidateStatus()"
                  [nzErrorTip]="getBirthDateErrorMessage()"
                >
                  <nz-date-picker
                    datePickerBlur
                    (datePickerBlur)="
                      selectDate($event.event, 'birthDate', true)
                    "
                    [nzSuffixIcon]="nzSuffixIcon"
                    formControlName="birthDate"
                    nzFormat="dd/MM/yyyy"
                    nzPlaceHolder="DD/MM/YYYY"
                    [nzDisabledDate]="disabledFutureDate"
                    (ngModelChange)="onBirthDateChange($event)"
                  >
                  </nz-date-picker>
                </nz-form-control>
              </nz-form-item>
            </div>
            <div nz-col [nzSpan]="12">
              <nz-form-item>
                <nz-form-label nzNoColon>Age</nz-form-label>
                <nz-form-control>
                  <input nz-input formControlName="age" [disabled]="true" />
                </nz-form-control>
              </nz-form-item>
            </div>
          </div>
          <div nz-row [nzGutter]="16">
            <div nz-col [nzSpan]="12">
              <nz-form-item>
                <nz-form-label nzNoColon>Date of Dead</nz-form-label>
                <nz-form-control>
                  <nz-date-picker
                    [nzSuffixIcon]="nzSuffixIcon"
                    formControlName="dateOfDeath"
                    nzFormat="dd/MM/yyyy"
                    nzPlaceHolder="DD/MM/YYYY"
                    (keyup.enter)="selectDate($event, 'dateOfDeath')"
                  >
                  </nz-date-picker>
                </nz-form-control>
              </nz-form-item>
            </div>
            <div nz-col [nzSpan]="12">
              <nz-form-item>
                <nz-form-label nzRequired nzNoColon
                  >Country of Birth</nz-form-label
                >
                <nz-form-control nzErrorTip="Cannot be empty">
                  <nz-select
                    formControlName="birthCountryCode"
                    nzPlaceHolder="Select Country of Birth"
                    nzShowSearch
                    nzAllowClear
                    (ngModelChange)="onCountryChange($event)"
                    [nzDropdownClassName]="'add-person-select'"
                    [nzOptionHeightPx]="44"
                  >
                    @for (option of countryList; track option) {
                      <nz-option
                        [nzValue]="option.value"
                        [nzLabel]="option.label"
                        nzCustomContent
                      >
                        <span class="ant-select-selection-item-content">{{
                          option.label
                        }}</span>
                        <hrdx-icon
                          icon="icon-check-bold"
                          class="selected-icon"
                        />
                      </nz-option>
                    }
                  </nz-select>
                </nz-form-control>
              </nz-form-item>
            </div>
          </div>
          <div nz-row [nzGutter]="16">
            <div nz-col [nzSpan]="12">
              <nz-form-item>
                <nz-form-label nzNoColon>City/Province of Birth</nz-form-label>
                <nz-form-control nzErrorTip="Cannot be empty">
                  <nz-select
                    formControlName="birthStateCode"
                    nzShowSearch
                    nzAllowClear
                    [nzDropdownClassName]="'add-person-select'"
                    nzPlaceHolder="Select City/Province of Birth"
                    [nzOptionHeightPx]="44"
                  >
                    @for (option of provinceList; track option) {
                      <nz-option
                        [nzValue]="option.value"
                        [nzLabel]="option.label"
                        nzCustomContent
                      >
                        <span class="ant-select-selection-item-content">{{
                          option.label
                        }}</span>
                        <hrdx-icon
                          icon="icon-check-bold"
                          class="selected-icon"
                        />
                      </nz-option>
                    }
                  </nz-select>
                </nz-form-control>
              </nz-form-item>
            </div>
            <div nz-col [nzSpan]="12">
              <nz-form-item>
                <nz-form-label nzNoColon>Birth Location</nz-form-label>
                <nz-form-control>
                  <input
                    [maxlength]="120"
                    nz-input
                    formControlName="birthTown"
                    placeholder="Enter Birth Location"
                  />
                </nz-form-control>
              </nz-form-item>
            </div>
          </div>
          <div nz-row [nzGutter]="16">
            <div nz-col [nzSpan]="12">
              <nz-form-item>
                <nz-form-label nzNoColon>Religion</nz-form-label>
                <nz-form-control [nzErrorTip]="religionErrorTpl">
                  <nz-select
                    formControlName="religionCode"
                    nzPlaceHolder="Select Religion"
                    nzShowSearch
                    nzAllowClear
                    [nzDropdownClassName]="'add-person-select'"
                    [nzOptionHeightPx]="44"
                  >
                    @for (option of religionList; track option) {
                      <nz-option
                        [nzValue]="option.value"
                        [nzLabel]="option.label"
                        nzCustomContent
                      >
                        <span class="ant-select-selection-item-content">{{
                          option.label
                        }}</span>
                        <hrdx-icon
                          icon="icon-check-bold"
                          class="selected-icon"
                        />
                      </nz-option>
                    }
                  </nz-select>
                  <ng-template #religionErrorTpl let-control>
                    <ng-container
                      *ngIf="control.hasError('invalidSelection')"
                      >{{
                        control.errors?.['invalidSelection']?.message
                      }}</ng-container
                    >
                  </ng-template>
                </nz-form-control>
              </nz-form-item>
            </div>
            <div nz-col [nzSpan]="12">
              <nz-form-item>
                <nz-form-label nzNoColon>Ethnic</nz-form-label>
                <nz-form-control [nzErrorTip]="ethnicErrorTpl">
                  <nz-select
                    formControlName="ethnicCode"
                    nzPlaceHolder="Select Ethnic"
                    nzShowSearch
                    nzAllowClear
                    [nzDropdownClassName]="'add-person-select'"
                    [nzOptionHeightPx]="44"
                  >
                    @for (option of ethnicList; track option) {
                      <nz-option
                        [nzValue]="option.value"
                        [nzLabel]="option.label"
                        nzCustomContent
                      >
                        <span class="ant-select-selection-item-content">{{
                          option.label
                        }}</span>
                        <hrdx-icon
                          icon="icon-check-bold"
                          class="selected-icon"
                        />
                      </nz-option>
                    }
                  </nz-select>
                  <ng-template #ethnicErrorTpl let-control>
                    <ng-container
                      *ngIf="control.hasError('invalidSelection')"
                      >{{
                        control.errors?.['invalidSelection']?.message
                      }}</ng-container
                    >
                  </ng-template>
                </nz-form-control>
              </nz-form-item>
            </div>
          </div>
          <div nz-row [nzGutter]="16">
            <div nz-col [nzSpan]="12">
              <nz-form-item>
                <nz-form-label nzRequired nzNoColon>Nationality</nz-form-label>
                <nz-form-control nzErrorTip="Cannot be empty">
                  <nz-select
                    formControlName="nationalityCode"
                    nzPlaceHolder="Select Nationality"
                    nzShowSearch
                    nzAllowClear
                    [nzDropdownClassName]="'add-person-select'"
                    [nzOptionHeightPx]="44"
                  >
                    @for (option of nationalityList; track option) {
                      <nz-option
                        [nzValue]="option.value"
                        [nzLabel]="option.label"
                        nzCustomContent
                      >
                        <span class="ant-select-selection-item-content">{{
                          option.label
                        }}</span>
                        <hrdx-icon
                          icon="icon-check-bold"
                          class="selected-icon"
                        />
                      </nz-option>
                    }
                  </nz-select>
                </nz-form-control>
              </nz-form-item>
            </div>
            <div nz-col [nzSpan]="12">
              <nz-form-item>
                <nz-form-label nzNoColon>Other Nationality</nz-form-label>
                <nz-form-control
                  [nzValidateStatus]="getOtherNationalityValidateStatus()"
                  [nzErrorTip]="otherNationalityErrorTpl"
                >
                  <nz-select
                    formControlName="otherNationality"
                    nzPlaceHolder="Select Other Nationality"
                    nzShowSearch
                    nzAllowClear
                    [nzDropdownClassName]="'add-person-select'"
                    [nzOptionHeightPx]="44"
                  >
                    @for (option of nationalityList; track option) {
                      <nz-option
                        [nzValue]="option.value"
                        [nzLabel]="option.label"
                        nzCustomContent
                      >
                        <span class="ant-select-selection-item-content">{{
                          option.label
                        }}</span>
                        <hrdx-icon
                          icon="icon-check-bold"
                          class="selected-icon"
                        />
                      </nz-option>
                    }
                  </nz-select>
                </nz-form-control>
              </nz-form-item>
            </div>
          </div>
        </nz-card>

        <nz-card nzTitle="Biographic History">
          <div nz-row [nzGutter]="16">
            <div nz-col [nzSpan]="12">
              <nz-form-item>
                <nz-form-label nzRequired nzNoColon>Gender</nz-form-label>
                <nz-form-control [nzErrorTip]="genderErrorTpl">
                  <nz-select
                    formControlName="genderCode"
                    nzPlaceHolder="Select Gender"
                    nzShowSearch
                    nzAllowClear
                    [nzDropdownClassName]="'add-person-select'"
                    [nzOptionHeightPx]="44"
                  >
                    @for (option of genderList; track option) {
                      <nz-option
                        [nzValue]="option.value"
                        [nzLabel]="option.label"
                        nzCustomContent
                      >
                        <span class="ant-select-selection-item-content">{{
                          option.label
                        }}</span>
                        <hrdx-icon
                          icon="icon-check-bold"
                          class="selected-icon"
                        />
                      </nz-option>
                    }
                  </nz-select>
                  <ng-template #genderErrorTpl let-control>
                    <ng-container *ngIf="control.hasError('required')"
                      >Cannot be empty</ng-container
                    >
                    <ng-container
                      *ngIf="control.hasError('invalidSelection')"
                      >{{
                        control.errors?.['invalidSelection']?.message
                      }}</ng-container
                    >
                  </ng-template>
                </nz-form-control>
              </nz-form-item>
            </div>
            <div nz-col [nzSpan]="12">
              <nz-form-item>
                <nz-form-label nzNoColon
                  >Highest Educational Level</nz-form-label
                >
                <nz-form-control>
                  <input
                    nz-input
                    formControlName="highestEducationLevelCode"
                    [disabled]="true"
                  />
                </nz-form-control>
              </nz-form-item>
            </div>
          </div>
          <div nz-row [nzGutter]="16">
            <div nz-col [nzSpan]="12">
              <nz-form-item>
                <nz-form-label nzRequired nzNoColon
                  >Marrital Status</nz-form-label
                >
                <nz-form-control [nzErrorTip]="errorTplMaritalStatusCode">
                  <nz-select
                    formControlName="maritalStatusCode"
                    nzPlaceHolder="Select Marrital Status"
                    nzShowSearch
                    nzAllowClear
                    [nzDropdownClassName]="'add-person-select'"
                    [nzOptionHeightPx]="44"
                  >
                    @for (option of maritalStatusList; track option) {
                      <nz-option
                        [nzValue]="option.value"
                        [nzLabel]="option.label"
                        nzCustomContent
                      >
                        <span class="ant-select-selection-item-content">{{
                          option.label
                        }}</span>
                        <hrdx-icon
                          icon="icon-check-bold"
                          class="selected-icon"
                        />
                      </nz-option>
                    }
                  </nz-select>
                  <ng-template #errorTplMaritalStatusCode let-control>
                    <ng-container *ngIf="control.hasError('required')"
                      >Cannot be empty</ng-container
                    >
                    <ng-container
                      *ngIf="control.hasError('invalidSelection')"
                      >{{
                        control.errors?.['invalidSelection']?.message
                      }}</ng-container
                    >
                  </ng-template>
                </nz-form-control>
              </nz-form-item>
            </div>
            <div nz-col [nzSpan]="12">
              <nz-form-item>
                <nz-form-label nzNoColon>As of</nz-form-label>
                <nz-form-control>
                  <nz-date-picker
                    datePickerBlur
                    (datePickerBlur)="
                      selectDate($event.event, 'maritalDate', true)
                    "
                    [nzSuffixIcon]="nzSuffixIcon"
                    formControlName="maritalDate"
                    nzPlaceHolder="DD/MM/YYYY"
                    nzFormat="dd/MM/yyyy"
                  ></nz-date-picker>
                </nz-form-control>
              </nz-form-item>
            </div>
          </div>
          <div nz-row>
            <div nz-col [nzSpan]="24">
              <nz-form-item>
                <nz-form-label nzNoColon>Attachment</nz-form-label>
                <nz-form-control>
                  <nz-upload
                    nzType="drag"
                    [nzMultiple]="true"
                    [nzAccept]="acceptFileTypes"
                    [nzLimit]="5"
                    [nzSize]="acceptFileSize"
                    [nzFileList]="fileList"
                    [nzFileListRender]="fileListRender"
                    [nzBeforeUpload]="beforeUpload"
                    nzListType="picture"
                    class="upload-container"
                    [ngClass]="{ error: errorMsg }"
                    (nzChange)="handleUpload($event)"
                  >
                    <ng-container *ngIf="!errorMsg">
                      <p class="ant-upload-drag-icon">
                        <i class="fa-regular icon-illustration icon"></i>
                      </p>
                      <p class="ant-upload-text">
                        Upload files or drop them here
                      </p>
                      <p class="ant-upload-hint">
                        PDF, DOC, DOCX, XLS, XLSX, CSV, JPEG, PNG only (Max 5MB)
                      </p>
                    </ng-container>
                    <ng-container *ngIf="errorMsg">
                      <div>
                        <div class="error-icon">
                          <i class="fa-regular icon-warning"></i>
                        </div>
                        <div class="error-message">
                          {{ errorMsg }}
                        </div>
                      </div>
                    </ng-container>
                  </nz-upload>
                </nz-form-control>
              </nz-form-item>
            </div>
          </div>
        </nz-card>
      </div>

      <div id="national-id" class="section">
        <h3>National ID</h3>
        <div formArrayName="nationalIds" preventEnterSubmit>
          <nz-card
            *ngFor="let nationalId of nationalIds.controls; let i = index"
            [formGroupName]="i"
          >
            <div class="card-title-wrapper">
              <h3>National ID {{ i + 1 }}</h3>

              <hrdx-button
                [type]="'ghost-gray'"
                [size]="'small'"
                [onlyIcon]="true"
                [icon]="'icon-trash-bold'"
                *ngIf="nationalIds.length > 1"
                (clicked)="removeNationalId(i)"
              />
            </div>
            <div nz-row [nzGutter]="16">
              <div nz-col [nzSpan]="12">
                <nz-form-item>
                  <nz-form-label nzRequired nzNoColon>Country</nz-form-label>
                  <nz-form-control>
                    <nz-select
                      formControlName="countryCode"
                      (ngModelChange)="onNIdCountryCodeChange(i)"
                      nzPlaceHolder="Enter Country"
                      nzShowSearch
                      nzAllowClear
                      [nzDropdownClassName]="'add-person-select'"
                      [nzOptionHeightPx]="44"
                    >
                      @for (option of countryList; track option) {
                        <nz-option
                          [nzValue]="option.value"
                          [nzLabel]="option.label"
                          nzCustomContent
                        >
                          <span class="ant-select-selection-item-content">{{
                            option.label
                          }}</span>
                          <hrdx-icon
                            icon="icon-check-bold"
                            class="selected-icon"
                          />
                        </nz-option>
                      }
                    </nz-select>
                  </nz-form-control>
                </nz-form-item>
              </div>
              <div nz-col [nzSpan]="12">
                <nz-form-item>
                  <nz-form-label nzRequired nzNoColon
                    >National ID Type</nz-form-label
                  >
                  <nz-form-control [nzErrorTip]="nationalIdTypeErrorTpl">
                    <nz-select
                      formControlName="identityDocumentTypeCode"
                      nzPlaceHolder="Select National ID Type"
                      nzShowSearch
                      nzAllowClear
                      [nzDropdownClassName]="'add-person-select'"
                      [nzOptionHeightPx]="44"
                      (ngModelChange)="onNationalIdTypeChange(i)"
                    >
                      @for (option of nationalIdTypeList; track option) {
                        <nz-option
                          [nzValue]="option.value"
                          [nzLabel]="option.label"
                          nzCustomContent
                        >
                          <span class="ant-select-selection-item-content">{{
                            option.label
                          }}</span>
                          <hrdx-icon
                            icon="icon-check-bold"
                            class="selected-icon"
                          />
                        </nz-option>
                      }
                    </nz-select>
                    <ng-template #nationalIdTypeErrorTpl let-control>
                      <ng-container *ngIf="control.hasError('required')"
                        >Cannot be empty</ng-container
                      >
                      <ng-container
                        *ngIf="control.hasError('invalidSelection')"
                        >{{
                          control.errors?.['invalidSelection']?.message
                        }}</ng-container
                      >
                    </ng-template>
                  </nz-form-control>
                </nz-form-item>
              </div>
            </div>
            <div nz-row [nzGutter]="16">
              <div nz-col [nzSpan]="24">
                <nz-form-item>
                  <nz-form-label nzRequired nzNoColon
                    >National ID</nz-form-label
                  >
                  <nz-form-control
                    [nzValidateStatus]="getNationalIdValidateStatus(i)"
                    [nzErrorTip]="getNationalIdErrorMessage(i)"
                  >
                    <input
                      nz-input
                      formControlName="number"
                      placeholder="Enter National ID"
                      [appMaxlength]="getNationalIdMaxLength(i)"
                      (blur)="onNationalIdBlur(i)"
                    />
                  </nz-form-control>
                </nz-form-item>
              </div>
            </div>
            <div nz-row [nzGutter]="16">
              <div nz-col [nzSpan]="12">
                <nz-form-item>
                  <nz-form-label nzRequired nzNoColon>Issue Date</nz-form-label>
                  <nz-form-control
                    [nzValidateStatus]="getIssueDateValidateStatus(i)"
                    [nzErrorTip]="getIssueDateErrorMessage(i)"
                  >
                    <nz-date-picker
                      datePickerBlur
                      (datePickerBlur)="
                        selectDateInArray(
                          $event.event,
                          'nationalIds',
                          i,
                          'issueDate'
                        )
                      "
                      [nzSuffixIcon]="nzSuffixIcon"
                      formControlName="issueDate"
                      nzFormat="dd/MM/yyyy"
                      nzPlaceHolder="DD/MM/YYYY"
                      [nzDisabledDate]="disabledFutureDate"
                    >
                    </nz-date-picker>
                    <ng-template #issueDateErrorTpl let-control>
                      <ng-container *ngIf="control.errors?.['required']">
                        Cannot be empty
                      </ng-container>
                      <ng-container
                        *ngIf="control.errors?.['issueDateEarlier']"
                      >
                        {{ control.errors?.['issueDateEarlier'] }}
                      </ng-container>
                    </ng-template>
                  </nz-form-control>
                </nz-form-item>
              </div>
              <div nz-col [nzSpan]="12">
                <nz-form-item>
                  <nz-form-label nzNoColon>End Date</nz-form-label>
                  <nz-form-control>
                    <nz-date-picker
                      [nzSuffixIcon]="nzSuffixIcon"
                      formControlName="endDate"
                      nzFormat="dd/MM/yyyy"
                      nzPlaceHolder="DD/MM/YYYY"
                      datePickerBlur
                      (datePickerBlur)="
                        selectDateInArray(
                          $event.event,
                          'nationalIds',
                          i,
                          'endDate'
                        )
                      "
                    ></nz-date-picker>
                    <div
                      *ngIf="getEndDateErrorMessage(i)"
                      class="ant-form-item-explain ant-form-item-explain-error"
                    >
                      {{ getEndDateErrorMessage(i) }}
                    </div>
                  </nz-form-control>
                </nz-form-item>
              </div>
            </div>
            <div nz-row [nzGutter]="16">
              <div nz-col [nzSpan]="24">
                <nz-form-item>
                  <nz-form-label nzRequired nzNoColon>Issue By</nz-form-label>
                  <nz-form-control [nzErrorTip]="issueByNationalIdErrorTpl">
                    <nz-select
                      formControlName="issueByCode"
                      nzPlaceHolder="Select Issue By"
                      nzShowSearch
                      nzAllowClear
                      [nzDropdownClassName]="'add-person-select'"
                      [nzOptionHeightPx]="44"
                    >
                      @for (option of issueByList; track option) {
                        <nz-option
                          [nzValue]="option.value"
                          [nzLabel]="option.label"
                          nzCustomContent
                        >
                          <span class="ant-select-selection-item-content">{{
                            option.label
                          }}</span>
                          <hrdx-icon
                            icon="icon-check-bold"
                            class="selected-icon"
                          />
                        </nz-option>
                      }
                    </nz-select>
                    <ng-template #issueByNationalIdErrorTpl let-control>
                      <ng-container *ngIf="control.hasError('required')"
                        >Cannot be empty</ng-container
                      >
                      <ng-container
                        *ngIf="control.hasError('invalidSelection')"
                        >{{
                          control.errors?.['invalidSelection']?.message
                        }}</ng-container
                      >
                    </ng-template>
                  </nz-form-control>
                </nz-form-item>
              </div>
            </div>
            <div nz-row [nzGutter]="16">
              <div nz-col [nzSpan]="12">
                <nz-form-item>
                  <nz-form-label nzNoColon>Status</nz-form-label>
                  <nz-form-control>
                    <nz-radio-group formControlName="enabled">
                      <label nz-radio nzValue="Y">Active</label>
                      <label nz-radio nzValue="N">Inactive</label>
                    </nz-radio-group>
                  </nz-form-control>
                </nz-form-item>
              </div>
              <div nz-col [nzSpan]="12">
                <nz-form-item>
                  <nz-form-label nzRequired nzNoColon>Priority</nz-form-label>
                  <nz-form-control>
                    <label
                      nz-checkbox
                      formControlName="isPrimary"
                      (ngModelChange)="updatePrimaryStatus(nationalIds, i)"
                      >Is Primary</label
                    >
                  </nz-form-control>
                </nz-form-item>
              </div>
            </div>
            <nz-form-item>
              <nz-form-label nzNoColon>Note</nz-form-label>
              <nz-form-control>
                <nz-textarea-count [nzMaxCharacterCount]="1000">
                  <textarea
                    [maxlength]="1000"
                    nz-input
                    formControlName="note"
                    placeholder="Enter Note"
                    [nzAutosize]="{ minRows: 3, maxRows: 5 }"
                  ></textarea>
                </nz-textarea-count>
              </nz-form-control>
            </nz-form-item>
            <!-- hide the attachment section -->
            <!-- <nz-form-item>
                <nz-form-label nzNoColon>Attachment</nz-form-label>
                <nz-form-control>
                  <nz-upload
                    nzType="drag"
                    [nzMultiple]="false"
                    [nzAccept]="acceptFileTypes"
                    [nzLimit]="1"
                    [nzSize]="acceptFileSize"
                    [nzFileList]="nationalIdFileLists[i]"
                    [nzBeforeUpload]="beforeNationalIdUpload.bind(this, i)"
                    (nzChange)="handleNationalIdUpload($event, i)"
                    >
                    <p class="ant-upload-drag-icon">
                      <i class="fa-regular fa-cloud-arrow-up icon"></i>
                    </p>
                    <p class="ant-upload-text">Upload file or drop it here</p>
                    <p class="ant-upload-hint">PDF, DOC, DOCX, JPEG, PNG only (Max 5MB)</p>
                  </nz-upload>
                </nz-form-control>
              </nz-form-item> -->
          </nz-card>

          <hrdx-button
            class="add-new-button"
            [title]="'Add New'"
            (clicked)="addNationalId()"
            [size]="'small'"
            [leftIcon]="'plus'"
            [onlyIcon]="false"
            [isLeftIcon]="true"
          >
          </hrdx-button>
        </div>
      </div>

      <div id="ptt-code" class="section">
        <h3>PIT Code</h3>
        <nz-card [formGroup]="taxInfoForm">
          <div nz-row [nzGutter]="16">
            <div nz-col [nzSpan]="12">
              <nz-form-item>
                <nz-form-label nzRequired nzNoColon>Country</nz-form-label>
                <nz-form-control>
                  <nz-select
                    formControlName="countryCode"
                    nzPlaceHolder="Select Country"
                    [nzDropdownClassName]="'add-person-select'"
                    [nzOptionHeightPx]="44"
                    (ngModelChange)="onPITCountryCodeChange()"
                    nzShowSearch
                    nzAllowClear
                  >
                    @for (option of countryList; track option) {
                      <nz-option
                        [nzValue]="option.value"
                        [nzLabel]="option.label"
                        nzCustomContent
                      >
                        <span class="ant-select-selection-item-content">{{
                          option.label
                        }}</span>
                        <hrdx-icon
                          icon="icon-check-bold"
                          class="selected-icon"
                        />
                      </nz-option>
                    }
                  </nz-select>
                </nz-form-control>
              </nz-form-item>
            </div>
            <div nz-col [nzSpan]="12">
              <nz-form-item>
                <nz-form-label nzRequired nzNoColon>Number</nz-form-label>
                <nz-form-control
                  [nzValidateStatus]="getTaxNumberValidateStatus()"
                  [nzErrorTip]="taxNumberErrorTpl"
                >
                  <input
                    nz-input
                    formControlName="number"
                    placeholder="Enter Number"
                    (blur)="onTaxNumberBlur()"
                  />
                  <ng-template #taxNumberErrorTpl>
                    <ng-container
                      *ngIf="taxInfoForm.get('number')?.errors?.['duplicate']"
                    >
                      {{
                        taxInfoForm.get('number')?.errors?.['duplicateMessage']
                      }}
                    </ng-container>
                    <ng-container
                      *ngIf="
                        taxInfoForm.get('number')?.errors?.['invalidLength']
                      "
                    >
                      {{
                        taxInfoForm.get('number')?.errors?.[
                          'invalidLengthMessage'
                        ]
                      }}
                    </ng-container>
                    <ng-container
                      *ngIf="taxInfoForm.get('number')?.errors?.['required']"
                    >
                      Cannot be empty
                    </ng-container>
                  </ng-template>
                </nz-form-control>
              </nz-form-item>
            </div>
          </div>
          <div nz-row [nzGutter]="16">
            <div nz-col [nzSpan]="12">
              <nz-form-item>
                <nz-form-label nzRequired nzNoColon>Issue Date</nz-form-label>
                <nz-form-control
                  [nzValidateStatus]="getTaxInfoIssueDateValidateStatus()"
                  [nzErrorTip]="getTaxInfoIssueDateErrorMessage()"
                >
                  <nz-date-picker
                    datePickerBlur
                    (datePickerBlur)="
                      selectDateInForm($event.event, 'taxInfoForm', 'issueDate')
                    "
                    [nzSuffixIcon]="nzSuffixIcon"
                    formControlName="issueDate"
                    nzFormat="dd/MM/yyyy"
                    nzPlaceHolder="DD/MM/YYYY"
                  >
                  </nz-date-picker>
                </nz-form-control>
              </nz-form-item>
            </div>
            <div nz-col [nzSpan]="12">
              <nz-form-item>
                <nz-form-label nzNoColon>Issue By</nz-form-label>
                <nz-form-control [nzErrorTip]="issueByErrorTpl">
                  <!-- <nz-select
                    formControlName="issueByCode"
                    nzPlaceHolder="Select Issue By"
                    nzShowSearch
                    nzAllowClear
                    [nzDropdownClassName]="'add-person-select'"
                    [nzOptionHeightPx]="44"
                  >
                    @for (option of issueByPITList; track option) {
                      <nz-option
                        [nzValue]="option.value"
                        [nzLabel]="option.label"
                        nzCustomContent
                      >
                        <span class="ant-select-selection-item-content">{{
                          option.label
                        }}</span>
                        <hrdx-icon
                          icon="icon-check-bold"
                          class="selected-icon"
                        />
                      </nz-option>
                    }
                  </nz-select> -->
                  <nz-select
                      formControlName="issueByCode"
                      nzPlaceHolder="Select Issue By"
                      nzShowSearch
                      nzAllowClear
                      [nzDropdownClassName]="'add-person-select'"
                      [nzOptionHeightPx]="44"
                      (nzScrollToBottom)="onSelectScroll('issueByCode')"
                      (nzOnSearch)="onSelectSearch('issueByCode', $event)"
                      (ngModelChange)="onSelectChange('issueByCode')"
                      [nzLoading]="selectManager.getState('issueByCode')?.isLoading">
                        @for (item of selectManager.getState('issueByCode')?.data; track item) {
                            <nz-option [nzValue]="item.value" [nzLabel]="item.label" nzCustomContent>
                                <span class="ant-select-selection-item-content">{{ item.label }}</span>
                                <hrdx-icon icon="icon-check-bold" class="selected-icon" />
                            </nz-option>
                        }
                        @if (selectManager.getState('issueByCode')?.isLoading) {
                            <nz-option nzDisabled nzCustomContent>
                                <span nz-icon nzType="loading" class="loading-icon"></span>
                                Loading Data...
                            </nz-option>
                        }
                    </nz-select>
                  <ng-template #issueByErrorTpl let-control>
                    <ng-container
                      *ngIf="control.hasError('invalidSelection')"
                      >{{
                        control.errors?.['invalidSelection']?.message
                      }}</ng-container
                    >
                  </ng-template>
                </nz-form-control>
              </nz-form-item>
            </div>
          </div>
          <nz-form-item>
            <nz-form-label nzNoColon>Status</nz-form-label>
            <nz-form-control>
              <nz-radio-group formControlName="status">
                <label nz-radio nzValue="active">Active</label>
                <label nz-radio nzValue="inactive">Inactive</label>
              </nz-radio-group>
            </nz-form-control>
          </nz-form-item>
          <nz-form-item>
            <nz-form-label nzNoColon>Note</nz-form-label>
            <nz-form-control>
              <nz-textarea-count [nzMaxCharacterCount]="1000">
                <textarea
                  [maxlength]="1000"
                  nz-input
                  formControlName="note"
                  placeholder="Enter Note"
                  [nzAutosize]="{ minRows: 3, maxRows: 5 }"
                ></textarea>
              </nz-textarea-count>
            </nz-form-control>
          </nz-form-item>
          <!-- hide the attachment section -->
          <!-- <nz-form-item>
              <nz-form-label nzNoColon>Attachment</nz-form-label>
              <nz-form-control>
                <nz-upload
                  nzType="drag"
                  [nzMultiple]="false"
                  [nzAccept]="acceptFileTypes"
                  [nzLimit]="1"
                  [nzSize]="acceptFileSize"
                  [nzFileList]="filePitCodeList"
                  [nzBeforeUpload]="beforeUploadPitCode"
                  (nzChange)="handlePitCodeUpload($event)">
                  <p class="ant-upload-drag-icon">
                    <i class="fa-regular fa-cloud-arrow-up icon"></i>
                  </p>
                  <p class="ant-upload-text">Upload file or drop it here</p>
                  <p class="ant-upload-hint">PDF, DOC, DOCX, JPEG, PNG only (Max 5MB)</p>
                </nz-upload>
              </nz-form-control>
            </nz-form-item> -->
        </nz-card>
      </div>
      <div id="phone-info" class="section">
        <h3>Phone Info</h3>
        <div formArrayName="phoneInfos">
          <nz-card
            *ngFor="let phoneInfo of phoneInfos.controls; let i = index"
            [formGroupName]="i"
          >
            <div class="card-title-wrapper">
              <h3>Phone Info {{ i + 1 }}</h3>
              <hrdx-button
                [type]="'ghost-gray'"
                [size]="'small'"
                [onlyIcon]="true"
                [icon]="'icon-trash-bold'"
                *ngIf="phoneInfos.length > 1"
                (clicked)="removePhoneInfo(i)"
              />
            </div>
            <div nz-row [nzGutter]="16">
              <div nz-col [nzSpan]="12">
                <nz-form-item>
                  <nz-form-label nzRequired nzNoColon
                    >Effective Date</nz-form-label
                  >
                  <nz-form-control>
                    <nz-date-picker
                      [nzSuffixIcon]="nzSuffixIcon"
                      formControlName="startDate"
                      nzFormat="dd/MM/yyyy"
                      nzPlaceHolder="DD/MM/YYYY"
                      (keyup.enter)="
                        selectDateInArray($event, 'phoneInfos', i, 'startDate')
                      "
                    >
                    </nz-date-picker>
                  </nz-form-control>
                </nz-form-item>
              </div>
              <div nz-col [nzSpan]="12">
                <nz-form-item>
                  <nz-form-label nzRequired nzNoColon>Phone Type</nz-form-label>
                  <nz-form-control [nzErrorTip]="phoneTypeErrorTpl">
                    <nz-select
                      formControlName="phoneTypeCode"
                      nzPlaceHolder="Select Phone Type"
                      [nzDropdownClassName]="'add-person-select'"
                      [nzOptionHeightPx]="44"
                      nzShowSearch
                      nzAllowClear
                    >
                      @for (option of phoneTypeList; track option) {
                        <nz-option
                          [nzValue]="option.value"
                          [nzLabel]="option.label"
                          nzCustomContent
                        >
                          <span class="ant-select-selection-item-content">{{
                            option.label
                          }}</span>
                          <hrdx-icon
                            icon="icon-check-bold"
                            class="selected-icon"
                          />
                        </nz-option>
                      }
                    </nz-select>
                    <ng-template #phoneTypeErrorTpl let-control>
                      <ng-container *ngIf="control.hasError('required')"
                        >Cannot be empty</ng-container
                      >
                      <ng-container
                        *ngIf="control.hasError('invalidSelection')"
                        >{{
                          control.errors?.['invalidSelection']?.message
                        }}</ng-container
                      >
                    </ng-template>
                  </nz-form-control>
                </nz-form-item>
              </div>
            </div>
            <div nz-row [nzGutter]="16">
              <div nz-col [nzSpan]="12">
                <nz-form-item>
                  <nz-form-label nzRequired nzNoColon
                    >Phone Number</nz-form-label
                  >
                  <nz-form-control
                    [nzValidateStatus]="getPhoneNumberValidateStatus(i)"
                    [nzErrorTip]="phoneNumberErrorTpl"
                  >
                    <input
                      [maxlength]="20"
                      nz-input
                      formControlName="phoneNumber"
                      placeholder="Enter Phone Number"
                      (blur)="onPhoneNumberBlur(i)"
                    />
                    <ng-template #phoneNumberErrorTpl>
                      <ng-container
                        *ngIf="
                          phoneInfos.at(i)?.get('phoneNumber')?.errors?.[
                            'duplicate'
                          ]
                        "
                      >
                        {{
                          phoneInfos.at(i)?.get('phoneNumber')?.errors?.[
                            'duplicateMessage'
                          ] || 'This phone number is already registered'
                        }}
                      </ng-container>
                      <ng-container
                        *ngIf="
                          phoneInfos.at(i)?.get('phoneNumber')?.errors?.[
                            'required'
                          ]
                        "
                      >
                        Cannot be empty
                      </ng-container>
                    </ng-template>
                  </nz-form-control>
                </nz-form-item>
              </div>
              <div nz-col [nzSpan]="12">
                <nz-form-item>
                  <nz-form-label nzNoColon>Extension</nz-form-label>
                  <nz-form-control>
                    <input
                      [maxlength]="20"
                      nz-input
                      formControlName="extensionNumber"
                      placeholder="Enter Extension"
                    />
                  </nz-form-control>
                </nz-form-item>
              </div>
            </div>
            <div nz-row [nzGutter]="16">
              <div nz-col [nzSpan]="12">
                <nz-form-item>
                  <nz-form-label nzNoColon>Time to Call</nz-form-label>
                  <nz-form-control [nzErrorTip]="timeToCallErrorTpl">
                    <nz-select
                      formControlName="callingTimeCode"
                      nzPlaceHolder="Select Time to Call"
                      [nzDropdownClassName]="'add-person-select'"
                      [nzOptionHeightPx]="44"
                      nzShowSearch
                      nzAllowClear
                    >
                      @for (option of timeToCallList; track option) {
                        <nz-option
                          [nzValue]="option.value"
                          [nzLabel]="option.label"
                          nzCustomContent
                        >
                          <span class="ant-select-selection-item-content">{{
                            option.label
                          }}</span>
                          <hrdx-icon
                            icon="icon-check-bold"
                            class="selected-icon"
                          />
                        </nz-option>
                      }
                    </nz-select>
                    <ng-template #timeToCallErrorTpl let-control>
                      <ng-container
                        *ngIf="control.hasError('invalidSelection')"
                        >{{
                          control.errors?.['invalidSelection']?.message
                        }}</ng-container
                      >
                    </ng-template>
                  </nz-form-control>
                </nz-form-item>
              </div>
              <div nz-col [nzSpan]="12">
                <nz-form-item>
                  <nz-form-label nzRequired nzNoColon
                    >Primary Contact</nz-form-label
                  >
                  <nz-form-control>
                    <label
                      nz-checkbox
                      formControlName="isPrimary"
                      (ngModelChange)="updatePrimaryStatus(phoneInfos, i)"
                      >Is Primary</label
                    >
                  </nz-form-control>
                </nz-form-item>
              </div>
            </div>
          </nz-card>

          <hrdx-button
            class="add-new-button"
            [title]="'Add New'"
            (clicked)="addPhoneInfo()"
            [size]="'small'"
            [leftIcon]="'plus'"
            [onlyIcon]="false"
            [isLeftIcon]="true"
          >
          </hrdx-button>
        </div>
        <!-- Phone Info form fields -->
      </div>
    </form>
  </div>
</div>

<ng-template #nzSuffixIcon>
  <span
    nz-icon
    nzType="icons:calendar-blank-bold"
    nzTheme="outline"
    class="suffixIcon"
    [ngClass]="''"
  ></span>
</ng-template>

<ng-template #optionTemplate let-label="label">
  <span class="ant-select-selection-item-content">{{ label }}</span>
  <hrdx-icon icon="icon-check-bold" class="selected-icon" />
</ng-template>

<ng-template #otherNationalityErrorTpl>
  <ng-container
    *ngIf="
      biographicalDetailForm.get('otherNationality')?.errors?.[
        'duplicateNationality'
      ]
    "
  >
    {{
      biographicalDetailForm.get('otherNationality')?.errors?.[
        'duplicateNationality'
      ]
    }}
  </ng-container>
  <ng-container
    *ngIf="
      biographicalDetailForm.get('otherNationality')?.errors?.[
        'invalidSelection'
      ]
    "
  >
    {{
      biographicalDetailForm.get('otherNationality')?.errors?.[
        'invalidSelection'
      ]?.message
    }}
  </ng-container>
</ng-template>

<ng-template #fileListRender let-list>
  <div class="file-list">
    @for (item of list; track item) {
      @if (item.status !== 'error') {
        <div class="file" [class.error]="item.status === 'error'">
          <ng-container [ngSwitch]="getFileType(item.name)">
            <hrdx-icon *ngSwitchCase="'excel'" [fixIon]="'fix-TypeXLS'" />
            <hrdx-icon *ngSwitchCase="'csv'" [fixIon]="'fix-TypeXLS'" />
            <hrdx-icon *ngSwitchCase="'pdf'" [fixIon]="'fix-TypePDF'" />
            <hrdx-icon *ngSwitchCase="'docx'" [fixIon]="'fix-TypeDOC'" />
            <hrdx-icon *ngSwitchCase="'doc'" [fixIon]="'fix-TypeDOC'" />
            <hrdx-icon *ngSwitchDefault [name]="'paperclip'" [size]="'small'" />
          </ng-container>
          <div class="file-list-name">
            <div class="file-name">
              {{ item.name }}
            </div>
            @if (item.status === 'error') {
              <div class="file-error">
                {{ item.response }}
              </div>
            }
          </div>
          <div class="file-list-action">
            <div (click)="removeFile(list, item)">
              <hrdx-icon [name]="'icon-trash'" [size]="'small'" />
            </div>
          </div>
        </div>
      }
    }
  </div>
</ng-template>
