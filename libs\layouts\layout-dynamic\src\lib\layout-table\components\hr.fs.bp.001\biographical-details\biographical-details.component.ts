import {
  AfterViewInit,
  Component,
  ElementRef,
  EventEmitter,
  inject,
  Input,
  OnDestroy,
  OnInit,
  Output,
  ViewChildren,
  QueryList,
  input,
  effect,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  AbstractControl,
  FormArray,
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  ValidationErrors,
  ValidatorFn,
  Validators,
} from '@angular/forms';
import {
  NzUploadChangeParam,
  NzUploadFile,
  NzUploadModule,
} from 'ng-zorro-antd/upload';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import {
  NzDatePickerModule,
  NzDatePickerComponent,
} from 'ng-zorro-antd/date-picker';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzGridModule } from 'ng-zorro-antd/grid';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzRadioModule } from 'ng-zorro-antd/radio';
import { NzSelectModule } from 'ng-zorro-antd/select';
import {
  ButtonComponent,
  DatePickerComponent,
  IconComponent,
  ModalComponent,
  SelectComponent,
} from '@hrdx/hrdx-design';
import { NzAnchorModule } from 'ng-zorro-antd/anchor';
import {
  EmployeeDuplicateCheckType,
  HrFsBp001Service,
} from '../services/hr.fs.bp.001.service';
import { PicklistConstants } from '../constants/picklist.constants';
import {
  catchError,
  debounceTime,
  distinctUntilChanged,
  of,
  map,
  Observable,
  Subscription,
} from 'rxjs';
import { NzAlertModule } from 'ng-zorro-antd/alert';
import { PreventEnterSubmitDirective } from '../directive/prevententersubmit.directive';
import { Constants, ERROR_MESSAGE } from '../constants/constants';
import { BiographicalDetailsValidators } from './biographical-details.validators';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { DatePickerScrollService } from '../services/date-picker-scroll.service';
import { DatePickerBlurDirective } from '../directive/datepickerblur.directive';
import { MaxlengthDirective } from '../directive/maxlength.directive';
import { SelectManager } from '../services/select-manager.service';
@Component({
  selector: 'lib-biographical-details',
  standalone: true,
  imports: [
    CommonModule,
    NzUploadModule,
    ReactiveFormsModule,
    NzFormModule,
    NzDatePickerModule,
    ButtonComponent,
    NzAnchorModule,
    DatePickerComponent,
    IconComponent,
    NzAlertModule,
    PreventEnterSubmitDirective,
    NzIconModule,
    NzInputModule,
    NzSelectModule,
    NzRadioModule,
    NzCardModule,
    NzCheckboxModule,
    NzGridModule,
    SelectComponent,
    DatePickerBlurDirective,
    MaxlengthDirective,
  ],
  templateUrl: './biographical-details.component.html',
  styleUrl: './biographical-details.component.less',
  providers: [HrFsBp001Service, ModalComponent, SelectManager],
})
export class BiographicalDetailsComponent
  implements OnInit, OnDestroy, AfterViewInit
{
  @Output() formReady = new EventEmitter<void>();
  @Output() formValid = new EventEmitter<boolean>();
  @ViewChildren(NzDatePickerComponent)
  datePickers!: QueryList<NzDatePickerComponent>;
  basicInfoPermission = input<Record<string, any> | null>(null);

  basicInfoPermissionEffect = effect(() => {
    if (this.basicInfoPermission()?.['socialNamePermission']?.Create) {
      this.biographicalDetailForm.get('socialName')?.enable();
    } else {
      this.biographicalDetailForm.get('socialName')?.disable();
    }
    if (this.basicInfoPermission()?.['specialNamePermission']?.Create) {
      this.biographicalDetailForm.get('specialName')?.enable();
    } else {
      this.biographicalDetailForm.get('specialName')?.disable();
    }
  });

  private subscriptions: Subscription = new Subscription();

  // Add variable to store max length for each national ID
  nationalIdMaxLengths: { [key: number]: number } = {};

  biographicalDetailForm!: FormGroup;
  taxInfoForm!: FormGroup;

  // accept file types to upload
  acceptFileTypes: string[] = [
    '.pdf',
    '.docx',
    '.doc',
    '.jpeg',
    '.png',
    '.xls',
    '.xlsx',
    '.csv',
  ];
  // accept file size
  acceptFileSize = 5 * 1024 * 1024;

  // ref to modal component
  modalComponent = inject(ModalComponent);

  private isScrolling = false;
  private scrollTimeout: any;

  // Add the jobDataEffectiveDate property
  private jobDataEffectiveDate: Date | null = null;

  // Add the isProgrammaticUpdateInProgress property
  isProgrammaticUpdateInProgress = false;

  // Update the setJobDataEffectiveDate method to apply the validator
  setJobDataEffectiveDate(effectiveDate: Date): void {
    this.jobDataEffectiveDate = effectiveDate;

    // Get the effectiveDateFrom control
    const effectiveDateControl =
      this.biographicalDetailForm.get('effectiveDateFrom');
    if (effectiveDateControl) {
      // Clear existing validators
      effectiveDateControl.clearValidators();

      // Apply both the required validator and our custom validator
      effectiveDateControl.setValidators([
        Validators.required,
        BiographicalDetailsValidators.effectiveDateNotLaterThanJobDataEffectiveDate(
          this.jobDataEffectiveDate,
        ),
      ]);

      // Trigger validation
      effectiveDateControl.updateValueAndValidity();
    }
  }

  getJobDataEffectiveDate(): Date | null {
    return this.jobDataEffectiveDate;
  }

  // default country code
  defaultCountryCode = Constants.DEFAULT_COUNTRY_CODE;

  private isUnder18DialogShowing = false;
  private currentBirthDateWarningShown: string | null = null; // Store birth date as ISO string when warning is shown

  // Add tracking object for name duplication validation
  private lastValidatedNameData = {
    firstName: null as string | null,
    middleName: null as string | null,
    lastName: null as string | null,
    birthDateNormalized: null as number | null,
  };

  // Helper function to check if name data has changed
  private hasNameDataChanged(currentData: {
    firstName: string | null;
    middleName: string | null;
    lastName: string | null;
    birthDate: Date | null;
  }): boolean {
    const currentDateNormalized =
      this.hrFsBp001Service.normalizeDateToUTCTimestamp(currentData.birthDate);

    // If any of the current values are empty strings, treat them as null
    const currentFirstName =
      currentData.firstName === ''
        ? null
        : currentData.firstName?.toLowerCase();
    const currentMiddleName =
      currentData.middleName === ''
        ? null
        : currentData.middleName?.toLowerCase();
    const currentLastName =
      currentData.lastName === '' ? null : currentData.lastName?.toLowerCase();

    // Compare with last validated data, treating empty strings as null
    return (
      currentFirstName !== this.lastValidatedNameData.firstName ||
      currentMiddleName !== this.lastValidatedNameData.middleName ||
      currentLastName !== this.lastValidatedNameData.lastName ||
      currentDateNormalized !== this.lastValidatedNameData.birthDateNormalized
    );
  }

  // Helper function to update last validated data
  private updateLastValidatedNameData(data: {
    firstName: string | null;
    middleName: string | null;
    lastName: string | null;
    birthDate: Date | null;
  }) {
    this.lastValidatedNameData = {
      firstName: data.firstName?.toLowerCase() ?? null,
      middleName: data.middleName?.toLowerCase() ?? null,
      lastName: data.lastName?.toLowerCase() ?? null,
      birthDateNormalized: this.hrFsBp001Service.normalizeDateToUTCTimestamp(
        data.birthDate,
      ),
    };
  }

  constructor(
    private fb: FormBuilder,
    private elementRef: ElementRef,
    private datePickerScrollService: DatePickerScrollService,
  ) {}

  ngOnInit(): void {
    if (!this.biographicalDetailForm) {
      this.biographicalDetailForm = this.fb.group(
        {
          effectiveDateFrom: [new Date(), Validators.required],
          firstName: ['', Validators.required],
          middleName: [''],
          lastName: ['', Validators.required],
          fullName: [{ value: '', disabled: true }],
          socialName: [''],
          specialName: [''],
          commonName: [''],

          // biographic information
          birthDate: [
            null,
            [
              Validators.required,
              BiographicalDetailsValidators.birthDateNotLaterThanEffectiveDate(),
            ],
          ],
          age: [{ value: null, disabled: true }],
          dateOfDeath: [{ value: null, disabled: true }],
          birthCountryCode: [null, Validators.required],
          birthStateCode: [null],
          birthTown: [null],
          religionCode: [null],
          ethnicCode: [null],
          nationalityCode: [null, Validators.required],
          otherNationality: [null],

          // bigraphic history
          genderCode: [null, Validators.required],
          highestEducationLevelCode: [{ value: null, disabled: true }],
          maritalStatusCode: [null, Validators.required],
          maritalDate: [null],

          // from arrays of national id
          nationalIds: this.fb.array([]),

          // Add an initial Phone Info form
          phoneInfos: this.fb.array([]),

          // attachment
          files: [null],

          // tax info
          taxInfo: this.fb.group({
            number: [null],
            issueDate: [null],
            countryCode: [null],
            status: ['active'],
            note: [null],
            issueByCode: [null],
            file: [null],
          }),
        },
        {
          validators: [BiographicalDetailsValidators.uniqueNationalities()],
        },
      );

      // Add an initial National ID form
      this.addNationalId();
      // Add an initial Phone Info form
      this.addPhoneInfo();

      // include tax info form
      this.taxInfoForm = this.biographicalDetailForm.get(
        'taxInfo',
      ) as FormGroup;

      // Subscribe to changes in the birthDate control
      this.subscribeToBirthDateChange();
    }

    // get picklist values
    this.getPicklistValues();

    this.biographicalDetailForm.statusChanges.subscribe(() => {
      this.formValid.emit(this.biographicalDetailForm.valid);
    });

    // subscribe to name changes to update full name
    this.subscribeToNameChanges();

    // if birthCountryCode is not null, set province list
    if (this.biographicalDetailForm.get('birthCountryCode')?.value) {
      this.onCountryChange(
        this.biographicalDetailForm.get('birthCountryCode')?.value,
      );
    }

    // Add listeners
    this.taxInfoForm.valueChanges
      .pipe(debounceTime(300), distinctUntilChanged())
      .subscribe(() => this.updateTaxInfoValidations());

    // register selects
    this.registerSelects();
  }

  // get picklist values
  getPicklistValues() {
    // set flag to true
    this.isProgrammaticUpdateInProgress = true;

    // set picklist values
    this.setCountryPicklistValues();
    this.setReligionPicklistValues();
    this.setGenderPicklistValues();
    this.setMaritalStatusPicklistValues();
    this.setEthnicPicklistValues();
    this.setPhoneTypePicklistValues();
    this.setTimeToCallPicklistValues();
    this.setNationalityPicklistValues();
  }

  getFileType(fileName: string): string {
    return HrFsBp001Service.getFileType(fileName);
  }

  removeFile(list: NzUploadFile[], file: NzUploadFile): void {
    const index = list.indexOf(file);
    if (index > -1) {
      list.splice(index, 1);
      this.errorMsg = '';
    }
  }

  ngAfterViewInit(): void {
    this.setupScrollListener();

    // set default active link
    setTimeout(() => {
      this.updateActiveLink('basic-info');
    }, 100);

    // set test data
    // this.fillTestData();

    // Setup date picker open change listeners
    this.datePickers.forEach((picker) => {
      picker.nzOnOpenChange.subscribe((isOpen: boolean) => {
        this.datePickerScrollService.registerDatePicker(picker, isOpen);
      });
    });
  }

  disabledFutureDate = (current: Date): boolean => {
    // Can't select days after today
    return current > new Date();
  };

  getFormValue(): any {
    // get full name
    return {
      biographicalData: this.biographicalDetailForm.getRawValue(),
      taxInfo: this.biographicalDetailForm.get('taxInfo')?.getRawValue(),
    };
  }

  isFormValid(): boolean {
    return this.biographicalDetailForm.valid;
  }

  get nationalIds() {
    return this.biographicalDetailForm.get('nationalIds') as FormArray;
  }

  // update the national id type by country code change
  onNIdCountryCodeChange(index: number): void {
    // check if the isProgrammaticUpdateInProgress flag is true
    // if (this.isProgrammaticUpdateInProgress) {
    //   return;
    // }

    // get national id type by country
    this.getNationalIDTypeByCountry(index);

    // get issue by list by country
    this.getIssueByByCountry(index);
  }

  addNationalId() {
    const nationalIdForm = this.fb.group(
      {
        issueDate: [
          null,
          [
            Validators.required,
            BiographicalDetailsValidators.issueDateNotEarlierThanBirthDate(),
          ],
        ],
        endDate: [null], // end date is not required
        enabled: ['Y'],
        countryCode: [this.getDefaultCountryCode(), Validators.required],
        identityDocumentTypeCode: [null, Validators.required],
        number: [null, Validators.required],
        issueByCode: [null, Validators.required],
        isPrimary: [false, Validators.required],
        note: [null],
        attachFile: [null],
      },
      { validators: this.dateRangeValidator },
    );

    this.nationalIds.push(nationalIdForm);
    this.updatePrimaryStatus(this.nationalIds, this.nationalIds.length - 1);
    this.getNationalIDTypeByCountry(this.nationalIds.length - 1);
    this.getIssueByByCountry(this.nationalIds.length - 1);
    const index = this.nationalIds.length - 1;
    const countryCode = nationalIdForm.get('countryCode')?.value || '';
    const identityDocumentTypeCode =
      nationalIdForm.get('identityDocumentTypeCode')?.value || '';
    if (
      this.isCountryCodeDefault(countryCode) &&
      this.isIdentificationTypeCodeDefault(identityDocumentTypeCode)
    ) {
      this.nationalIdMaxLengths[index] = Constants.IDENTIFICATION_MAX_LENGTH;
    } else {
      this.nationalIdMaxLengths[index] = 0; // No restriction
    }
    this.nationalIdFileLists.push([]);
    // Always restore the uniqueNationalId validator after add
    this.nationalIds.setValidators([
      BiographicalDetailsValidators.uniqueNationalId(),
    ]);
    this.nationalIds.updateValueAndValidity({ emitEvent: false });
  }

  removeNationalId(index: number) {
    this.nationalIds.removeAt(index);
    this.nationalIdFileLists.splice(index, 1);
    // Always restore the uniqueNationalId validator after remove
    this.nationalIds.setValidators([
      BiographicalDetailsValidators.uniqueNationalId(),
    ]);
    this.nationalIds.updateValueAndValidity({ emitEvent: false });
  }

  fileList: NzUploadFile[] = [];

  errorMsg = '';
  private resetErrorTimeoutId: any;

  beforeUpload = (file: NzUploadFile): boolean => {
    // check if the file type is accepted
    const isAcceptedType = this.hrFsBp001Service.checkFileType(file);

    // check if the file size is within the limit
    const isLessThanMaxSize = file.size! <= this.acceptFileSize;

    if (!isAcceptedType) {
      file.status = 'error';
      file.response = ERROR_MESSAGE.FILE_TYPE; // Add error message to file object
      this.errorMsg = ERROR_MESSAGE.FILE_TYPE;
      // Keep existing valid files and add the error file
      const validFiles = this.fileList.filter((f) => f.status !== 'error');
      this.fileList = [...validFiles, file];
      this.startErrorResetTimer();
      return false;
    }

    if (!isLessThanMaxSize) {
      file.status = 'error';
      file.response = ERROR_MESSAGE.FILE_SIZE; // Add error message to file object
      this.errorMsg = ERROR_MESSAGE.FILE_SIZE;
      // Keep existing valid files and add the error file
      const validFiles = this.fileList.filter((f) => f.status !== 'error');
      this.fileList = [...validFiles, file];
      this.startErrorResetTimer();
      return false;
    }

    // File is valid
    file.status = 'done';
    // Add to fileList if not exceeding limit
    if (this.fileList.length < 5) {
      // Filter out any error files and add the new valid file
      const validFiles = this.fileList.filter((f) => f.status !== 'error');
      this.fileList = [...validFiles, file];
      // Update form control with all files
      this.biographicalDetailForm.patchValue({
        files: this.fileList,
      });
    }
    return false;
  };

  handleUpload(info: NzUploadChangeParam): void {
    if (info.type === 'removed') {
      // Only remove if it's not an error file
      if (info.file.status !== 'error') {
        this.fileList = this.fileList.filter((f) => f.uid !== info.file.uid);
        // Update form control
        this.biographicalDetailForm.patchValue({
          files: this.fileList.length > 0 ? this.fileList : null,
        });
      }
    }
  }

  createNormalizedFile(file: File): File {
    const normalizedName = file.name.normalize('NFC');
    return new File([file], normalizedName, { type: file.type });
  }

  // national id upload handler
  nationalIdFileLists: NzUploadFile[][] = [];

  beforeNationalIdUpload(
    index: number,
    file: NzUploadFile,
  ): boolean | Observable<boolean> {
    // check if the file type is accepted
    const isAcceptedType = this.checkFileType(file);

    // check if the file size is within the limit
    const isLessThanMaxSize = file.size! <= this.acceptFileSize;

    if (!isAcceptedType) {
      file.status = 'error';
      file.error = {
        message: 'You can only upload PDF, DOC, DOCX, JPEG, or PNG file!',
      };
    } else if (!isLessThanMaxSize) {
      file.status = 'error';
      file.error = { message: 'File must be smaller than 5MB!' };
    } else {
      file.status = 'done';
    }

    this.nationalIdFileLists[index] = [file];
    this.nationalIds.at(index).patchValue({
      attachFile: file,
    });
    return false; // Returning false prevents actual upload
  }

  handleNationalIdUpload(info: NzUploadChangeParam, index: number): void {
    if (info.type === 'removed') {
      this.nationalIdFileLists[index] = [];
      this.nationalIds.at(index).patchValue({
        file: null,
      });
    }
  }

  // ptt code upload handler
  filePitCodeList: NzUploadFile[] = [];

  beforeUploadPitCode = (file: NzUploadFile): boolean => {
    // check if the file type is accepted
    const isAcceptedType = this.checkFileType(file);

    // check if the file size is within the limit
    const isLessThanMaxSize = file.size! <= this.acceptFileSize;

    if (!isAcceptedType) {
      file.status = 'error';
      file.error = {
        message: 'You can only upload PDF, DOC, DOCX, JPEG, or PNG file!',
      };
    } else if (!isLessThanMaxSize) {
      file.status = 'error';
      file.error = { message: 'File must be smaller than 5MB!' };
    } else {
      file.status = 'done';
    }

    this.filePitCodeList = [file];
    const taxInfoForm = this.biographicalDetailForm.get('taxInfo');
    if (taxInfoForm) {
      taxInfoForm.patchValue({
        file: file,
      });
    } else {
      console.error('taxInfoForm is not available');
    }
    return false; // Returning false prevents actual upload
  };

  handlePitCodeUpload(info: NzUploadChangeParam): void {
    if (info.type === 'removed') {
      this.filePitCodeList = [];
      const taxInfoForm = this.biographicalDetailForm.get('taxInfo');
      if (taxInfoForm) {
        taxInfoForm.patchValue({
          file: null,
        });
      } else {
        console.error('taxInfoForm is not available when removing file');
      }
    }
  }

  get phoneInfos() {
    return this.biographicalDetailForm.get('phoneInfos') as FormArray;
  }

  addPhoneInfo() {
    const phoneInfoForm = this.fb.group({
      startDate: [{ value: null, disabled: true }],
      phoneTypeCode: [null],
      phoneNumber: [null],
      extensionNumber: [null],
      callingTimeCode: [null],
      isPrimary: [false],
    });

    // if effective date is not null, set the start date to effective date
    const effectiveDate =
      this.biographicalDetailForm.get('effectiveDateFrom')?.value;
    if (effectiveDate) {
      phoneInfoForm.get('startDate')?.setValue(effectiveDate);
    }

    this.phoneInfos.push(phoneInfoForm);
    this.updatePrimaryStatus(this.phoneInfos, this.phoneInfos.length - 1);

    // Apply the uniquePhoneNumber validator to the FormArray
    this.phoneInfos.setValidators([
      BiographicalDetailsValidators.uniquePhoneNumber(),
    ]);
    this.phoneInfos.updateValueAndValidity({ emitEvent: false });

    // subscribe to phone info form value changes
    phoneInfoForm.valueChanges
      .pipe(debounceTime(300), distinctUntilChanged())
      .subscribe(() =>
        this.updateFormValidations(phoneInfoForm, [
          'phoneTypeCode',
          'phoneNumber',
        ]),
      );
  }

  removePhoneInfo(index: number) {
    this.phoneInfos.removeAt(index);
  }

  //#region scroll to section

  scrollToSection(sectionId: string): void {
    this.isScrolling = true;
    clearTimeout(this.scrollTimeout);

    const element = this.elementRef.nativeElement.querySelector(
      `#${sectionId}`,
    );
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'start' });
      this.updateActiveLink(sectionId);
    }
  }

  private setupScrollListener() {
    const sections = ['basic-info', 'national-id', 'ptt-code', 'phone-info'];
    const observer = new IntersectionObserver(
      (entries) => {
        if (!this.isScrolling) {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              const id = entry.target.id;
              this.updateActiveLink(id);
            }
          });
        }
      },
      { threshold: 0.2 },
    ); // Adjust threshold as needed

    sections.forEach((id) => {
      const element = this.elementRef.nativeElement.querySelector(`#${id}`);
      if (element) {
        observer.observe(element);
      }
    });
  }

  private updateActiveLink(id: string) {
    const links = this.elementRef.nativeElement.querySelectorAll('nz-link');

    // First, remove the active class from all links
    links.forEach((link: HTMLElement) => {
      link.classList.remove('ant-anchor-link-active');
    });

    // Then, add the active class to the target link
    const targetLink: any = Array.from(links).find(
      (link: any) => link.getAttribute('nz-href') === `#${id}`,
    );

    if (targetLink) {
      targetLink.classList.add('ant-anchor-link-active');
    }

    // Reset isScrolling after a delay
    this.scrollTimeout = setTimeout(() => {
      this.isScrolling = false;
    }, 500); // Adjust this value if needed
  }

  //#endregion

  // make ref to hr.fs.bp.001.service
  hrFsBp001Service: HrFsBp001Service = inject(HrFsBp001Service);

  // a country list
  countryList: any[] = [];
  // a province list
  provinceList: any[] = [];
  // a religion list
  religionList: any[] = [];
  // a gender list
  genderList: any[] = [];
  // a marital status list
  maritalStatusList: any[] = [];
  // a identification type list
  identificationTypeList: any[] = [];
  // a education level list
  educationLevelList: any[] = [];
  // a ethnic list
  ethnicList: any[] = [];
  // national id type list
  nationalIdTypeList: any[] = [];
  // a phone type list
  phoneTypeList: any[] = [];
  // a time to call list
  timeToCallList: any[] = [];
  // a nationality list
  nationalityList: any[] = [];

  // issue by list
  issueByList: any[] = [];
  // issue by pit list
  // issueByPITList: any[] = [];

  setCountryPicklistValues(): void {
    this.hrFsBp001Service
      .getPicklistByCode(
        PicklistConstants.COUNTRY_LIST,
        undefined,
        this.getEffectiveDateString(),
      )
      .subscribe((countryList) => {
        this.countryList = countryList;

        // set default country code for birth country
        this.biographicalDetailForm.patchValue(
          {
            birthCountryCode: this.getDefaultCountryCode(),
          },
          { emitEvent: false },
        );

        // set default country code for the first national id item
        this.nationalIds.at(0).patchValue(
          {
            countryCode: this.getDefaultCountryCode(),
          },
          { emitEvent: false },
        );

        // set default country code for PTT code
        this.biographicalDetailForm.get('taxInfo')?.patchValue(
          {
            countryCode: this.getDefaultCountryCode(),
          },
          { emitEvent: false },
        );

        // get issue by pit list
        // this.getIssueByPITByCountry();
      });
  }

  setReligionPicklistValues(): void {
    this.hrFsBp001Service
      .getPicklistByCode(
        PicklistConstants.RELIGION_LIST,
        undefined,
        this.getEffectiveDateString(),
      )
      .subscribe((religionList) => {
        this.religionList = religionList;
      });
  }

  setGenderPicklistValues(): void {
    this.hrFsBp001Service
      .getPicklistByCode(
        PicklistConstants.GENDER_LIST,
        undefined,
        this.getEffectiveDateString(),
      )
      .subscribe((genderList) => {
        this.genderList = genderList;
      });
  }

  setEthnicPicklistValues(): void {
    this.hrFsBp001Service
      .getPicklistByCode(
        PicklistConstants.ETHNIC_LIST,
        undefined,
        this.getEffectiveDateString(),
      )
      .subscribe((ethnicList) => {
        this.ethnicList = ethnicList;
      });
  }

  setPhoneTypePicklistValues(): void {
    this.hrFsBp001Service
      .getPicklistByCode(
        PicklistConstants.PHONE_TYPE_LIST,
        undefined,
        this.getEffectiveDateString(),
      )
      .subscribe((phoneTypeList) => {
        this.phoneTypeList = phoneTypeList;
      });
  }

  setTimeToCallPicklistValues(): void {
    this.hrFsBp001Service
      .getPicklistByCode(
        PicklistConstants.TIMETOCALL_LIST,
        undefined,
        this.getEffectiveDateString(),
      )
      .subscribe((timeToCallList) => {
        this.timeToCallList = timeToCallList;
      });
  }

  setMaritalStatusPicklistValues(): void {
    this.hrFsBp001Service
      .getPicklistByCode(
        PicklistConstants.MARITAL_STATUS_LIST,
        undefined,
        this.getEffectiveDateString(),
      )
      .subscribe((maritalStatusList) => {
        this.maritalStatusList = maritalStatusList;
      });
  }

  onCountryChange(countryCode: string): void {
    this.hrFsBp001Service
      .getPicklistByCode(
        PicklistConstants.PROVINCE_LIST,
        countryCode,
        this.getEffectiveDateString(),
      )
      .subscribe((provinceList) => {
        this.provinceList = provinceList;
      });

    // Update maxLength for all national IDs when country changes
    this.nationalIds.controls.forEach((control, index) => {
      const identityDocumentTypeCode = control.get(
        'identityDocumentTypeCode',
      )?.value;

      if (
        this.isCountryCodeDefault(countryCode) &&
        this.isIdentificationTypeCodeDefault(identityDocumentTypeCode)
      ) {
        this.nationalIdMaxLengths[index] = Constants.IDENTIFICATION_MAX_LENGTH;
      } else {
        this.nationalIdMaxLengths[index] = 0; // No restriction
      }
    });
  }

  setNationalityPicklistValues(): void {
    this.hrFsBp001Service
      .getPicklistByCode(
        PicklistConstants.NATIONALITY_LIST,
        undefined,
        this.getEffectiveDateString(),
      )
      .subscribe((nationalityList) => {
        this.nationalityList = nationalityList;

        // set default nationality code
        this.biographicalDetailForm.patchValue(
          {
            nationalityCode: this.getDefaultNationalityCode(),
          },
          { emitEvent: false },
        );

        // reset the isProgrammaticUpdateInProgress flag
        this.isProgrammaticUpdateInProgress = false;
      });
  }

  // get national id type
  getNationalIDTypeByCountry(index: number) {
    // get country name from
    const countryCode = this.nationalIds.at(index).get('countryCode')?.value;

    // if isProgrammaticUpdateInProgress flag is true, don't clear the national id type code
    if (!this.isProgrammaticUpdateInProgress) {
      this.nationalIds.at(index).patchValue(
        {
          identityDocumentTypeCode: null,
        },
        { emitEvent: false },
      );
    }

    if (countryCode) {
      this.hrFsBp001Service
        .getPicklistByCodeAndParent(
          PicklistConstants.NATIONAL_ID_TYPE_LIST,
          countryCode,
          undefined,
          this.getEffectiveDateString(),
        )
        .subscribe((nationalIdTypeList) => {
          this.nationalIdTypeList = nationalIdTypeList;
        });
    } else {
      this.nationalIdTypeList = [];
    }

    // trigger validation when country changes
    this.nationalIds.at(index).get('number')?.updateValueAndValidity();
  }

  // get issue by list by country
  getIssueByByCountry(index: number) {
    const countryCode = this.nationalIds.at(index).get('countryCode')?.value;

    // if isProgrammaticUpdateInProgress flag is true, don't clear the issue by code
    if (!this.isProgrammaticUpdateInProgress) {
      this.nationalIds.at(index).patchValue(
        {
          issueByCode: null,
        },
        { emitEvent: false },
      );
    }

    if (countryCode) {
      this.hrFsBp001Service
        .getPicklistByCodeAndParent(
          PicklistConstants.ISSUEBY_LIST,
          countryCode,
          undefined,
          this.getEffectiveDateString(),
        )
        .subscribe((issueByList) => {
          this.issueByList = issueByList;
        });
    } else {
      this.issueByList = [];
    }
  }

  // get issue by PIT list by country
  // getIssueByPITByCountry() {
  //   const countryCode = this.taxInfoForm.get('countryCode')?.value;
  //   if (countryCode) {
  //     // clear the issue by code
  //     this.issueByPITList = [];
  //     // this.taxInfoForm.patchValue({
  //     //   issueByCode: null
  //     // }, { emitEvent: false });

  //     this.hrFsBp001Service
  //       .getPicklistByCodeAndParent(
  //         PicklistConstants.ISSUEBYPIT_LIST,
  //         undefined,
  //         undefined,
  //         this.getEffectiveDateString(),
  //       )
  //       .subscribe((issueByList) => {
  //         this.issueByPITList = issueByList;
  //       });
  //   }
  // }

  calculateAge(birthDate: Date): { years: number; months: number } {
    const today = new Date();
    let years = today.getFullYear() - birthDate.getFullYear();
    let months = today.getMonth() - birthDate.getMonth();

    // Get days difference
    const currentDate = today.getDate();
    const birthDay = birthDate.getDate();

    // Adjust months and years based on day comparison
    if (currentDate < birthDay) {
      months--;
    }

    // Handle negative months
    if (months < 0) {
      years--;
      months += 12;
    }

    // Ensure months stay within 0-11 range
    if (months === 12) {
      years++;
      months = 0;
    }

    return { years, months };
  }

  // get accept mine types
  private checkFileType(file: NzUploadFile): boolean {
    const acceptedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'image/jpeg',
      'image/png',
    ];
    return acceptedTypes.includes(file.type!);
  }

  // make a function to join first name, middle name, and last name, trims the extra spaces
  joinName() {
    const firstName =
      this.biographicalDetailForm.get('firstName')?.value?.trim() || '';
    const middleName =
      this.biographicalDetailForm.get('middleName')?.value?.trim() || '';
    const lastName =
      this.biographicalDetailForm.get('lastName')?.value?.trim() || '';

    // Join the names, removing any extra spaces between them
    const fullName = [lastName, middleName, firstName]
      .filter(Boolean)
      .join(' ');

    // set to full name
    this.biographicalDetailForm.patchValue({
      fullName: fullName,
    });
  }

  dateRangeValidator(group: AbstractControl): ValidationErrors | null {
    const issueDate = group.get('issueDate')?.value;
    const endDate = group.get('endDate')?.value;

    if (issueDate && endDate && new Date(issueDate) >= new Date(endDate)) {
      return { dateRange: 'Issue Date must be earlier than End Date' };
    }

    return null;
  }

  getEndDateErrorMessage(index: number): string {
    const nationalIdForm = this.nationalIds.at(index);
    const endDateControl = nationalIdForm.get('endDate');

    // Only show errors if the control has been touched
    if (endDateControl?.touched) {
      const dateRangeError = nationalIdForm.errors?.['dateRange'];
      if (dateRangeError) {
        return dateRangeError;
      }
      if (endDateControl.errors?.['required']) {
        return 'End Date is required!';
      }
    }
    return '';
  }

  private subscribeToNameChanges() {
    const nameControls = ['firstName', 'middleName', 'lastName'];

    nameControls.forEach((controlName) => {
      this.subscriptions.add(
        this.biographicalDetailForm
          .get(controlName)!
          .valueChanges.pipe(debounceTime(300), distinctUntilChanged())
          .subscribe(() => {
            this.joinName();
            this.checkNameDuplication();
          }),
      );
    });
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
    if (this.resetErrorTimeoutId) {
      clearTimeout(this.resetErrorTimeoutId);
    }

    // Make sure to clean up any remaining listeners
    this.datePickers?.forEach((picker) => {
      this.datePickerScrollService.registerDatePicker(picker, false);
    });

    // clear timeout
    if (this.searchDebounceTimer) {
      clearTimeout(this.searchDebounceTimer);
    }
  }

  updatePrimaryStatus(formArray: FormArray, index: number) {
    const currentValue = formArray.at(index).get('isPrimary')?.value;

    if (currentValue) {
      // If the current item is being set to primary, uncheck all others
      formArray.controls.forEach((control, i) => {
        if (i !== index) {
          control.get('isPrimary')?.setValue(false);
        }
      });
    } else {
      // If the current item is being unchecked, ensure at least one is primary
      const anyPrimary = formArray.controls.some(
        (control, i) => i !== index && control.get('isPrimary')?.value,
      );
      if (!anyPrimary && formArray.length > 0) {
        // If no other is primary, set the first one as primary
        formArray.at(0)?.get('isPrimary')?.setValue(true);
      }
    }
  }

  selectDate(event: any, controlName: string, noFutureDate = false): void {
    let inputDate: string;

    // Check if event has the captured value from our directive
    if (event && event.event && event.event.currentValue) {
      inputDate = event.event.currentValue;
    }
    // Check if event is from our directive without captured value
    else if (event && event.input && event.event && event.event.target) {
      const input = event.event.target as HTMLInputElement;
      inputDate = input.value;
    }
    // Check if it's a direct DOM event
    else if (event && event.target) {
      const input = event.target as HTMLInputElement;
      inputDate = input.value;
      input.blur(); // This will close the date picker
    }
    // Check if it's a string
    else if (typeof event === 'string') {
      inputDate = event;
    } else {
      console.warn('Unhandled event type in selectDate:', event);
      return;
    }

    // Parse the date
    let parsedDate = this.hrFsBp001Service.parseDate(inputDate);

    if (parsedDate) {
      if (noFutureDate && parsedDate > new Date()) {
        parsedDate = new Date();
      }

      // For the effectiveDateFrom field, we need to handle the job data effective date constraint
      if (controlName === 'effectiveDateFrom' && this.jobDataEffectiveDate) {
        const control = this.biographicalDetailForm.get(controlName);

        // Set the new value
        control?.setValue(parsedDate);
      } else {
        // For other fields, just set the value
        this.biographicalDetailForm.get(controlName)?.setValue(parsedDate);
      }

      // if effectivedatefrom control
      if (controlName === 'effectiveDateFrom') {
        // clear invalid selection errors
        this.clearInvalidSelectionErrors();

        // reload picklist values
        this.getPicklistValues();

        // reload issue by PIT
        if (this.selectManager.hasBeenOpened('issueByCode')) {
          // if issueByCode is selected, the search with that value
          const issueByCode = this.selectManager.getState('issueByCode')?.searchValue?? ''

          this.selectManager.search('issueByCode', issueByCode, {
            searchValue: issueByCode,
          }).subscribe();
        }

        this.onEffectiveDateChange(parsedDate);
      }
    } else {
      this.biographicalDetailForm.get(controlName)?.setValue(null);
    }
  }

  selectDateInArray(
    event: any,
    arrayName: string,
    index: number,
    controlName: string,
  ): void {
    let inputDate: string;

    // Check if event has the captured value from our directive
    if (event && event.event && event.event.currentValue) {
      inputDate = event.event.currentValue;
    }
    // Check if event is from our directive without captured value
    else if (event && event.input && event.event && event.event.target) {
      const input = event.event.target as HTMLInputElement;
      inputDate = input.value;
    }
    // Check if it's a direct DOM event
    else if (event && event.target) {
      const input = event.target as HTMLInputElement;
      inputDate = input.value;
      input.blur(); // This will close the date picker
    }
    // Check if it's a string
    else if (typeof event === 'string') {
      inputDate = event;
    } else {
      console.warn('Unhandled event type in selectDateInArray:', event);
      return;
    }

    // Parse the date
    const parsedDate = this.hrFsBp001Service.parseDate(inputDate);

    if (parsedDate) {
      const formArray = this.biographicalDetailForm.get(arrayName) as FormArray;
      formArray.at(index).get(controlName)?.setValue(parsedDate);
    }
  }

  selectDateInForm(event: any, formName: string, controlName: string): void {
    let inputDate: string;

    // Check if event has the captured value from our directive
    if (event && event.event && event.event.currentValue) {
      inputDate = event.event.currentValue;
    }
    // Check if event is from our directive without captured value
    else if (event && event.input && event.event && event.event.target) {
      const input = event.event.target as HTMLInputElement;
      inputDate = input.value;
    }
    // Check if it's a direct DOM event
    else if (event && event.target) {
      const input = event.target as HTMLInputElement;
      inputDate = input.value;
      input.blur(); // This will close the date picker
    }
    // Check if it's a string
    else if (typeof event === 'string') {
      inputDate = event;
    } else {
      console.warn('Unhandled event type in selectDateInForm:', event);
      return;
    }

    // Parse the date
    const parsedDate = this.hrFsBp001Service.parseDate(inputDate);

    if (parsedDate) {
      const form = this[formName as keyof this] as FormGroup;
      form.get(controlName)?.setValue(parsedDate);
    }
  }

  isFormEmpty(form: AbstractControl): boolean {
    if (form instanceof FormGroup) {
      return Object.entries(form.value).every(([key, value]) => {
        // Consider 'status' with value 'active' as empty
        if (key === 'status' && value === 'active') {
          return true;
        } else if (key === 'startDate') {
          // if key is startDate, consider it as empty
          return true;
        }
        return value === null || value === '';
      });
    } else if (form instanceof FormArray) {
      return form.controls.every((control) => this.isFormEmpty(control));
    }
    return true;
  }

  onEffectiveDateChange(event: any) {
    const effectiveDate = event;
    // fill the date to effective date of the phone info form array
    const phoneInfos = this.biographicalDetailForm.get(
      'phoneInfos',
    ) as FormArray;
    phoneInfos.controls.forEach((control) => {
      control.get('startDate')?.setValue(effectiveDate);
    });
    // fill the date to effective date of the tax info form
    this.biographicalDetailForm
      .get('taxInfo')
      ?.get('issueDate')
      ?.setValue(effectiveDate);

    // validate the birth date control
    const birthDateControl = this.biographicalDetailForm.get('birthDate');
    if (birthDateControl) {
      birthDateControl.updateValueAndValidity({ emitEvent: false });
    }
  }

  // update form validations with phone info and tax info
  updateFormValidations(
    form: FormGroup,
    requiredFields: string[],
    ignoreFields: string[] = ['startDate', 'isPrimary'],
  ) {
    const hasValue = Object.entries(form.value).some(
      ([key, value]) =>
        !ignoreFields.includes(key) && value !== null && value !== '',
    );

    // Check for validation errors
    const phoneNumberControl = form.get('phoneNumber');
    const hasValidationErrors = phoneNumberControl?.errors?.['duplicate'];

    requiredFields.forEach((field) => {
      const control = form.get(field);
      if (control) {
        if (hasValue || hasValidationErrors) {
          control.setValidators(Validators.required);
          if (field === 'phoneNumber' && hasValidationErrors) {
            // Keep existing errors and touched state
            const currentErrors = control.errors;
            if (currentErrors) {
              control.markAsTouched();
              // Set form array level validator to prevent moving next
              this.phoneInfos.setValidators([
                (formArray: AbstractControl): ValidationErrors | null => {
                  return { invalid: true };
                },
              ]);
              this.phoneInfos.updateValueAndValidity({ emitEvent: false });
              return;
            }
          }
        } else {
          control.clearValidators();
          if (!hasValidationErrors) {
            control.reset(null, { emitEvent: false });
            control.setErrors(null);
            control.markAsPristine();
            control.markAsUntouched();
          }
        }
        // control.updateValueAndValidity({ emitEvent: false });
      }
    });

    // Always keep isPrimary if it exists
    const isPrimaryControl = form.get('isPrimary');
    if (isPrimaryControl) {
      isPrimaryControl.setValidators(Validators.required);
      isPrimaryControl.updateValueAndValidity({ emitEvent: false });
    }
  }

  updateFormArrayValidations(formArray: FormArray, requiredFields: string[]) {
    formArray.controls.forEach((control) => {
      if (control instanceof FormGroup) {
        this.updateFormValidations(control, requiredFields);
      }
    });

    // Trigger validation update on the main form
    this.biographicalDetailForm.updateValueAndValidity();
  }

  // update tax info validations
  updateTaxInfoValidations(): void {
    const defaultEffectiveDate =
      this.biographicalDetailForm.get('effectiveDateFrom')?.value;
    const issueDateValue = this.taxInfoForm.get('issueDate')?.value;
    const countryCodeValue = this.taxInfoForm.get('countryCode')?.value;
    const numberValue = this.taxInfoForm.get('number')?.value;
    const numberControl = this.taxInfoForm.get('number');

    const hasNonDefaultValue =
      (issueDateValue && issueDateValue !== defaultEffectiveDate) ||
      (countryCodeValue && countryCodeValue !== this.getDefaultCountryCode()) ||
      (numberValue !== null && numberValue !== '');

    // Check for validation errors
    const hasValidationErrors =
      numberControl?.errors?.['invalidLength'] ||
      numberControl?.errors?.['duplicate'];

    ['countryCode', 'number'].forEach((field) => {
      const control = this.taxInfoForm.get(field);
      if (control) {
        // Special handling for number field - if it's empty, clear everything regardless of validation errors
        if (field === 'number' && (numberValue === null || numberValue === '')) {
          control.clearValidators();
          control.reset(null, { emitEvent: false });
          control.setErrors(null);
          control.markAsPristine();
          control.markAsUntouched();
        } else if (hasNonDefaultValue || hasValidationErrors) {
          control.setValidators(Validators.required);
          // Don't clear errors if there are validation errors
          if (field === 'number' && hasValidationErrors) {
            // Keep existing errors and touched state
            const currentErrors = control.errors;
            if (currentErrors) {
              control.markAsTouched();
              // Don't update validity to preserve error state
              return;
            }
          }
        } else {
          control.clearValidators();
          if (field === 'countryCode') {
            control.setValue(this.getDefaultCountryCode(), {
              emitEvent: false,
            });
          } else {
            control.reset(null, { emitEvent: false });
            control.setErrors(null);
            control.markAsPristine();
            control.markAsUntouched();
          }
        }
        control.updateValueAndValidity({ emitEvent: false });
      }
    });

    // Handle issueDate separately to preserve birth date validation
    const issueDateControl = this.taxInfoForm.get('issueDate');
    if (issueDateControl) {
      if (hasNonDefaultValue || hasValidationErrors) {
        issueDateControl.setValidators([
          Validators.required,
          BiographicalDetailsValidators.issueDatePITNotEarlierThanBirthDate(),
        ]);
      } else {
        issueDateControl.clearValidators();
        issueDateControl.setErrors(null);
        issueDateControl.markAsPristine();
        issueDateControl.markAsUntouched();
      }
      issueDateControl.updateValueAndValidity({ emitEvent: false });
    }

    // Set form-level validator if there are validation errors
    if (hasValidationErrors) {
      this.taxInfoForm.setValidators([
        (formGroup: AbstractControl): ValidationErrors | null => {
          return { invalid: true };
        },
      ]);
      // Don't update validity of the form to preserve error state
      return;
    }
    this.taxInfoForm.updateValueAndValidity({ emitEvent: false });
  }

  onTaxNumberBlur(): void {
    const taxInfoForm = this.biographicalDetailForm.get('taxInfo') as FormGroup;
    const countryCode = taxInfoForm.get('countryCode')?.value;
    const number = taxInfoForm.get('number')?.value;
    const numberControl = taxInfoForm.get('number');

    // Store current errors
    const currentErrors = numberControl?.errors;
    const hasInvalidLength = currentErrors?.['invalidLength'];
    const hasDuplicate = currentErrors?.['duplicate'];

    // If field is cleared, clean up all validation errors and re-evaluate form
    if (!number) {
      numberControl?.setErrors(null);
      numberControl?.markAsPristine();
      numberControl?.markAsUntouched();
      this.taxInfoForm.clearValidators();
      this.taxInfoForm.updateValueAndValidity({ emitEvent: false });
      // Re-evaluate whether fields should be required based on other form values
      this.updateTaxInfoValidations();
      return;
    }

    // check if the country code is default & the number is exactly 10 digits long
    if (
      this.isCountryCodeDefault(countryCode) &&
      number &&
      number.length !== 10
    ) {
      const errors = {
        invalidLength: true,
        invalidLengthMessage: 'PIT numbers must be either 10',
      };

      // Preserve duplicate error if exists
      if (hasDuplicate) {
        Object.assign(errors, {
          duplicate: currentErrors?.['duplicate'],
          duplicateMessage: currentErrors?.['duplicateMessage'],
        });
      }

      numberControl?.setErrors(errors);
      numberControl?.markAsTouched();

      this.taxInfoForm.setValidators([
        (formGroup: AbstractControl): ValidationErrors | null => {
          return { invalid: true };
        },
      ]);

      this.taxInfoForm.updateValueAndValidity();
      return;
    }

    if (countryCode && number) {
      this.hrFsBp001Service
        .checkDuplicateEmployee(
          undefined,
          undefined,
          undefined,
          undefined,
          countryCode,
          undefined,
          number,
          undefined,
          EmployeeDuplicateCheckType.PIT,
        )
        .pipe(
          map((response) => {
            if (hasInvalidLength) {
              // Keep invalid length error if it exists
              numberControl?.setErrors({
                invalidLength: currentErrors?.['invalidLength'],
                invalidLengthMessage: currentErrors?.['invalidLengthMessage'],
              });
            } else {
              numberControl?.setErrors(null);
              this.taxInfoForm.clearValidators();
            }
            numberControl?.markAsTouched();
            this.taxInfoForm.updateValueAndValidity();
          }),
          catchError((error) => {
            if (error.status === 400 && error.error?.message) {
              const errors = {
                duplicate: true,
                duplicateMessage: error.error.message,
              };

              // Preserve invalid length error if it exists
              if (hasInvalidLength) {
                Object.assign(errors, {
                  invalidLength: currentErrors?.['invalidLength'],
                  invalidLengthMessage: currentErrors?.['invalidLengthMessage'],
                });
              }

              numberControl?.setErrors(errors);
              numberControl?.markAsTouched();

              this.taxInfoForm.setValidators([
                (formGroup: AbstractControl): ValidationErrors | null => {
                  return { invalid: true };
                },
              ]);

              this.taxInfoForm.updateValueAndValidity();
            } else {
              if (hasInvalidLength) {
                // Keep invalid length error if it exists
                numberControl?.setErrors({
                  invalidLength: currentErrors?.['invalidLength'],
                  invalidLengthMessage: currentErrors?.['invalidLengthMessage'],
                });
              } else {
                numberControl?.setErrors(null);
                this.taxInfoForm.clearValidators();
              }
              numberControl?.markAsTouched();
              this.taxInfoForm.updateValueAndValidity();
            }
            return of(null);
          }),
        )
        .subscribe();
    }
  }

  // #region custom validation functions
  getPhoneNumberValidateStatus(index: number): string {
    const control = this.phoneInfos.at(index)?.get('phoneNumber');
    if (!control) return '';
    return control.errors && control.touched ? 'error' : '';
  }

  onPhoneNumberBlur(index: number) {
    const phoneInfoForm = this.phoneInfos.at(index);
    const phoneNumber = phoneInfoForm.get('phoneNumber')?.value;

    if (phoneNumber && phoneInfoForm.get('phoneNumber')?.valid) {
      this.hrFsBp001Service
        .checkDuplicateEmployee(
          undefined,
          undefined,
          undefined,
          undefined,
          undefined,
          undefined,
          phoneNumber,
          undefined,
          EmployeeDuplicateCheckType.PHONE_NUMBER,
        )
        .pipe(
          map((response) => {
            phoneInfoForm.get('phoneNumber')?.setErrors(null);
            phoneInfoForm.get('phoneNumber')?.markAsTouched();
            // Clear the form array validator when phone number is unique
            this.phoneInfos.clearValidators();
            this.phoneInfos.updateValueAndValidity();
          }),
          catchError((error) => {
            if (error.status === 400 && error.error?.message) {
              phoneInfoForm.get('phoneNumber')?.setErrors({
                duplicate: true,
                duplicateMessage: error.error.message,
              });
              phoneInfoForm.get('phoneNumber')?.markAsTouched();

              // Set form array level validator to prevent moving next
              this.phoneInfos.setValidators([
                (formArray: AbstractControl): ValidationErrors | null => {
                  return { invalid: true };
                },
              ]);
              this.phoneInfos.updateValueAndValidity();
            } else {
              phoneInfoForm.get('phoneNumber')?.setErrors(null);
              phoneInfoForm.get('phoneNumber')?.markAsTouched();
              // Clear the form array validator when phone number is unique
              this.phoneInfos.clearValidators();
              this.phoneInfos.updateValueAndValidity();
            }
            return of(null);
          }),
        )
        .subscribe();
    }
  }

  getTaxNumberValidateStatus(): string {
    const control = this.taxInfoForm?.get('number');
    if (!control) return '';
    return control.errors && control.touched ? 'error' : '';
  }

  // update the national id validation when the type changes
  onNationalIdTypeChange(index: number): void {
    const nationalIdForm = this.nationalIds.at(index);
    const identityDocumentTypeCode = nationalIdForm.get(
      'identityDocumentTypeCode',
    )?.value;
    const countryCode = nationalIdForm.get('countryCode')?.value;

    // Set maxlength based on conditions
    if (
      this.isCountryCodeDefault(countryCode) &&
      this.isIdentificationTypeCodeDefault(identityDocumentTypeCode)
    ) {
      this.nationalIdMaxLengths[index] = Constants.IDENTIFICATION_MAX_LENGTH;
    } else {
      this.nationalIdMaxLengths[index] = 0; // No restriction
    }

    this.onNationalIdBlur(index);
  }

  // Add method to get max length for a specific index
  getNationalIdMaxLength(index: number): number {
    return this.nationalIdMaxLengths[index] || 0;
  }

  onNationalIdBlur(index: number): void {
    const nationalIdForm = this.nationalIds.at(index);
    const countryCode = nationalIdForm.get('countryCode')?.value;
    const identityDocumentTypeCode = nationalIdForm.get(
      'identityDocumentTypeCode',
    )?.value;
    const number = nationalIdForm.get('number')?.value;
    const enabled = nationalIdForm.get('enabled')?.value;
    const numberControl = nationalIdForm.get('number');
    const restoreUniqueNationalIdValidator = () => {
      this.nationalIds.setValidators([
        BiographicalDetailsValidators.uniqueNationalId(),
      ]);
      this.nationalIds.updateValueAndValidity({ emitEvent: false });
    };
    const duplicateClientError = numberControl?.errors?.['duplicateClient'];
    const duplicateClientMessage =
      numberControl?.errors?.['duplicateClientMessage'];
    const duplicateServerError = numberControl?.errors?.['duplicateServer'];
    const duplicateServerMessage =
      numberControl?.errors?.['duplicateServerMessage'];
    if (!number) {
      restoreUniqueNationalIdValidator();
      return;
    }
    if (
      this.isCountryCodeDefault(countryCode) &&
      this.isIdentificationTypeCodeDefault(identityDocumentTypeCode)
    ) {
      // Helper to set invalid length error and return
      const setInvalidLengthError = (message: string) => {
        numberControl?.setErrors({
          invalidLength: true,
          invalidLengthMessage: message,
          ...(duplicateClientError
            ? { duplicateClient: duplicateClientError, duplicateClientMessage }
            : {}),
          ...(duplicateServerError
            ? { duplicateServer: duplicateServerError, duplicateServerMessage }
            : {}),
        });
        numberControl?.markAsTouched();
        restoreUniqueNationalIdValidator();
      };

      if (
        Constants.IDENTIFICATION_TYPE_CODES_12_CHARS.includes(
          identityDocumentTypeCode,
        )
      ) {
        if (number.length !== Constants.IDENTIFICATION_MAX_LENGTH) {
          setInvalidLengthError(
            `National ID Number only allow ${Constants.IDENTIFICATION_MAX_LENGTH} characters`,
          );
          return;
        }
      } else if (
        Constants.IDENTIFICATION_TYPE_CODES_9_12_CHARS.includes(
          identityDocumentTypeCode,
        )
      ) {
        if (
          number.length !== Constants.IDENTIFICATION_MIN_LENGTH &&
          number.length !== Constants.IDENTIFICATION_MAX_LENGTH
        ) {
          setInvalidLengthError(
            `National ID Number only allow ${Constants.IDENTIFICATION_MIN_LENGTH} or ${Constants.IDENTIFICATION_MAX_LENGTH} characters`,
          );
          return;
        }
      } else {
        if (
          number.length !== Constants.IDENTIFICATION_MIN_LENGTH &&
          number.length !== Constants.IDENTIFICATION_MAX_LENGTH
        ) {
          setInvalidLengthError(
            `National ID and Citizen ID numbers must be either ${Constants.IDENTIFICATION_MIN_LENGTH} or ${Constants.IDENTIFICATION_MAX_LENGTH}`,
          );
          return;
        }
      }
    }
    if (countryCode && identityDocumentTypeCode && number) {
      this.hrFsBp001Service
        .checkDuplicateEmployee(
          undefined,
          undefined,
          undefined,
          undefined,
          countryCode,
          identityDocumentTypeCode,
          number,
          enabled,
          EmployeeDuplicateCheckType.NATIONAL_ID,
        )
        .pipe(
          map((response) => {
            if (response === null) {
              numberControl?.setErrors(null);
            } else if (duplicateServerError) {
              numberControl?.setErrors({
                duplicateServer: duplicateServerError,
                duplicateServerMessage,
              });
            } else {
              numberControl?.setErrors(null);
            }
            numberControl?.markAsTouched();
            restoreUniqueNationalIdValidator();
          }),
          catchError((error) => {
            if (error.status === 400 && error.error?.message) {
              numberControl?.setErrors({
                duplicateServer: true,
                duplicateServerMessage: error.error.message,
                ...(duplicateClientError
                  ? {
                      duplicateClient: duplicateClientError,
                      duplicateClientMessage,
                    }
                  : {}),
              });
              numberControl?.markAsTouched();
              // Set a blocking validator, but always restore uniqueNationalId after
              this.nationalIds.setValidators([
                (formArray: AbstractControl): ValidationErrors | null => {
                  return { invalid: true };
                },
              ]);
              this.nationalIds.updateValueAndValidity();
              setTimeout(restoreUniqueNationalIdValidator, 0); // Restore after a tick
            } else {
              if (duplicateClientError) {
                numberControl?.setErrors({
                  duplicateClient: duplicateClientError,
                  duplicateClientMessage,
                });
              } else {
                numberControl?.setErrors(null);
              }
              numberControl?.markAsTouched();
              restoreUniqueNationalIdValidator();
            }
            return of(null);
          }),
        )
        .subscribe();
    } else {
      restoreUniqueNationalIdValidator();
    }
  }

  checkNameDuplication() {
    const firstName = this.biographicalDetailForm.get('firstName')?.value;
    const middleName = this.biographicalDetailForm.get('middleName')?.value;
    const lastName = this.biographicalDetailForm.get('lastName')?.value;
    const birthDate = this.biographicalDetailForm.get('birthDate')?.value;

    // require that first name, last name and birth date are not empty
    if (!firstName || !lastName || !birthDate) {
      return;
    }

    // Use ISO string for birthDate for reliable comparison
    const birthDateIso =
      birthDate instanceof Date ? birthDate.toISOString() : birthDate;
    const currentData = {
      firstName,
      middleName,
      lastName,
      birthDate: birthDateIso,
    };

    // If the combination has changed, reset the guard
    if (
      !this.lastDuplicateWarningData ||
      this.lastDuplicateWarningData.firstName !== firstName ||
      this.lastDuplicateWarningData.middleName !== middleName ||
      this.lastDuplicateWarningData.lastName !== lastName ||
      this.lastDuplicateWarningData.birthDate !== birthDateIso
    ) {
      this.lastDuplicateWarningData = null;
    }

    // Prevent duplicate warning for the same data
    if (
      this.lastDuplicateWarningData &&
      this.lastDuplicateWarningData.firstName === firstName &&
      this.lastDuplicateWarningData.middleName === middleName &&
      this.lastDuplicateWarningData.lastName === lastName &&
      this.lastDuplicateWarningData.birthDate === birthDateIso
    ) {
      // console.log('[checkNameDuplication] duplicate warning already shown for this data, skipping');
      return;
    }

    const hasChanged = this.hasNameDataChanged({
      firstName,
      middleName,
      lastName,
      birthDate,
    });
    if (!hasChanged) {
      return;
    }

    // convert birthdate to UTC seconds since epoch, stripping time components
    const birthDateUtc =
      this.hrFsBp001Service.normalizeDateToUTCTimestamp(birthDate)! / 1000;
    const firstNameLowerCase = firstName?.toLowerCase();
    const middleNameLowerCase = middleName?.toLowerCase();
    const lastNameLowerCase = lastName?.toLowerCase();

    if (firstNameLowerCase && lastNameLowerCase && birthDateUtc !== undefined) {
      this.hrFsBp001Service
        .checkDuplicateEmployee(
          firstNameLowerCase,
          middleNameLowerCase,
          lastNameLowerCase,
          birthDateUtc,
          undefined,
          undefined,
          undefined,
          undefined,
          EmployeeDuplicateCheckType.NAME_AND_DOB,
        )
        .pipe(
          map((response) => {
            this.updateLastValidatedNameData({
              firstName,
              middleName,
              lastName,
              birthDate,
            });
          }),
          catchError((error) => {
            if (error.status === 400 && error.error?.message) {
              this.modalComponent.showDialog({
                nzTitle: 'Warning',
                nzContent:
                  error.error.message ||
                  'Full Name and Date of Birth already exist in the system.',
                nzWrapClassName: 'popup popup-confirm duplicateWarning',
                nzIconType: 'icons:warning',
                nzOkText: 'Confirm',
              });
              // Mark that we've shown the warning for this data
              this.lastDuplicateWarningData = { ...currentData };
            }
            this.updateLastValidatedNameData({
              firstName,
              middleName,
              lastName,
              birthDate,
            });
            return of(null);
          }),
        )
        .subscribe();
    }
  }

  // check if the country code is default
  isCountryCodeDefault(countryCode: string): boolean {
    return (
      countryCode === Constants.DEFAULT_COUNTRY_CODE ||
      countryCode === Constants.DEFAULT_COUNTRY_CODE_UAT
    );
  }

  // check if the identification type code is default
  isIdentificationTypeCodeDefault(identificationTypeCode: string): boolean {
    return (
      Constants.IDENTIFICATION_TYPE_CODES.includes(identificationTypeCode) ||
      Constants.IDENTIFICATION_TYPE_CODES_UAT.includes(identificationTypeCode)
    );
  }

  // #endregion

  // #region set default values
  // get default country code
  getDefaultCountryCode(): string {
    // prefer country code
    const preferredCountryCodes = [
      Constants.DEFAULT_COUNTRY_CODE,
      Constants.DEFAULT_COUNTRY_CODE_UAT,
    ];
    for (const countryCode of preferredCountryCodes) {
      if (this.countryList.find((option) => option.value === countryCode)) {
        this.defaultCountryCode = countryCode;
        return countryCode;
      }
    }
    return Constants.DEFAULT_COUNTRY_CODE;
  }

  getDefaultNationalityCode(): string {
    // prefer nationality code
    const preferredNationalityCodes = [
      Constants.DEFAULT_NATIONALITY_CODE,
      Constants.DEFAULT_NATIONALITY_CODE_UAT,
    ];
    for (const nationalityCode of preferredNationalityCodes) {
      if (
        this.nationalityList.find((option) => option.value === nationalityCode)
      ) {
        return nationalityCode;
      }
    }
    return Constants.DEFAULT_NATIONALITY_CODE;
  }
  // #endregion

  onPITCountryCodeChange(): void {
    const taxInfoForm = this.biographicalDetailForm.get('taxInfo') as FormGroup;
    const number = taxInfoForm.get('number');

    // Only validate if there's already a number value
    if (number?.value) {
      // Add a small delay to allow the country value to update
      setTimeout(() => {
        this.onTaxNumberBlur();
      }, 500);
    }

    // reload issue by picklist values
    // this.getIssueByPITByCountry();
  }

  // set effective date
  setEffectiveDate(effectiveDate: Date): void {
    this.biographicalDetailForm
      .get('effectiveDateFrom')
      ?.setValue(effectiveDate);
  }

  // get effective date
  getEffectiveDate(): Date {
    return this.biographicalDetailForm.get('effectiveDateFrom')?.value;
  }

  // get effective date as string
  getEffectiveDateString(): string {
    return this.biographicalDetailForm.get('effectiveDateFrom')?.value;
  }

  //#region "birth date validator"

  onBirthDateChange(event: any): void {
    const birthDateControl = this.biographicalDetailForm.get('birthDate');
    if (!birthDateControl) return;

    // The validation will be handled by the validator function
    // Just trigger validation of all issue dates when birth date changes
    this.nationalIds.controls.forEach((control) => {
      const issueDateControl = control.get('issueDate');
      if (issueDateControl) {
        issueDateControl.updateValueAndValidity();
      }
    });
  }

  //#endregion

  validateFormWithMessage(): { isValid: boolean; messages?: string[] } {
    // Validate selected dropdown values
    this.validateSelectedValues();

    // Mark all fields as touched to show validation messages
    this.markAllFieldsAsTouched(this.biographicalDetailForm);

    const messages: string[] = [];

    // Check for duplicate nationalities
    const otherNationalityControl =
      this.biographicalDetailForm.get('otherNationality');
    if (otherNationalityControl?.errors?.['duplicateNationality']) {
      messages.push(otherNationalityControl.errors['duplicateNationality']);
    }

    // Check for duplicate national IDs
    this.nationalIds.controls.forEach((control, index) => {
      const numberControl = control.get('number');
      if (numberControl?.errors?.['duplicateClient']) {
        messages.push(numberControl.errors['duplicateClientMessage']);
      }
      if (numberControl?.errors?.['duplicateServer']) {
        messages.push(numberControl.errors['duplicateServerMessage']);
      }
      if (numberControl?.errors?.['invalidLength']) {
        messages.push(numberControl.errors['invalidLengthMessage']);
      }
    });

    // Check for duplicate phone numbers
    this.phoneInfos.controls.forEach((control) => {
      const phoneNumberControl = control.get('phoneNumber');
      if (phoneNumberControl?.errors?.['duplicate']) {
        messages.push(phoneNumberControl.errors['duplicateMessage']);
      }
    });

    // Check tax number validation
    const taxNumberControl = this.taxInfoForm?.get('number');
    if (taxNumberControl?.errors?.['duplicate']) {
      messages.push(taxNumberControl.errors['duplicateMessage']);
    }
    if (taxNumberControl?.errors?.['invalidLength']) {
      messages.push(taxNumberControl.errors['invalidLengthMessage']);
    }

    // Check birth date validation
    const birthDateControl = this.biographicalDetailForm.get('birthDate');
    if (birthDateControl?.errors?.['dateOrder']) {
      messages.push(birthDateControl.errors['dateOrderMessage']);
    }

    // Check issue dates for national IDs
    this.nationalIds.controls.forEach((control) => {
      const issueDateControl = control.get('issueDate');
      if (issueDateControl?.errors?.['issueDateEarlier']) {
        messages.push(issueDateControl.errors['issueDateEarlier']);
      }
    });

    // Check tax info issue date
    const taxIssueDateControl = this.taxInfoForm?.get('issueDate');
    if (taxIssueDateControl?.errors?.['issueDateEarlier']) {
      messages.push(taxIssueDateControl.errors['issueDateEarlier']);
    }

    // Check for invalid selection errors in main form controls
    Object.values(this.biographicalDetailForm.controls).forEach((control) => {
      if (control?.errors?.['invalidSelection']?.message) {
        messages.push(control.errors['invalidSelection'].message);
      }
    });

    // Check for invalid selection errors in phone info array
    this.phoneInfos.controls.forEach((control) => {
      Object.values((control as FormGroup).controls).forEach((subControl) => {
        if (subControl?.errors?.['invalidSelection']?.message) {
          messages.push(subControl.errors['invalidSelection'].message);
        }
      });
    });

    // Check for invalid selection errors in national IDs array
    this.nationalIds.controls.forEach((control) => {
      Object.values((control as FormGroup).controls).forEach((subControl) => {
        if (subControl?.errors?.['invalidSelection']?.message) {
          messages.push(subControl.errors['invalidSelection'].message);
        }
      });
    });

    // check for error jobDataDateOrder in effectiveDateFrom
    const effectiveDateFromControl =
      this.biographicalDetailForm.get('effectiveDateFrom');
    if (effectiveDateFromControl?.errors?.['jobDataDateOrder']) {
      messages.push(effectiveDateFromControl.errors['jobDataDateOrderMessage']);
    }

    // Form is valid only if:
    // 1. The Angular form is valid (all controls pass their validators)
    // 2. There are no custom validation messages (like duplicate errors or invalidSelection)
    // Note: Setting errors on controls with control.setErrors() will automatically mark the form as invalid
    return {
      isValid: this.biographicalDetailForm.valid && messages.length === 0,
      messages: messages.length > 0 ? messages : undefined,
    };
  }

  private markAllFieldsAsTouched(formGroup: FormGroup): void {
    Object.values(formGroup.controls).forEach((control) => {
      // Store existing custom errors before validation
      const existingCustomErrors = {
        duplicateClient: control.errors?.['duplicateClient'],
        duplicateClientMessage: control.errors?.['duplicateClientMessage'],
        duplicateServer: control.errors?.['duplicateServer'],
        duplicateServerMessage: control.errors?.['duplicateServerMessage'],
        invalidLength: control.errors?.['invalidLength'],
        invalidLengthMessage: control.errors?.['invalidLengthMessage'],
        dateOrder: control.errors?.['dateOrder'],
        dateOrderMessage: control.errors?.['dateOrderMessage'],
        issueDateEarlier: control.errors?.['issueDateEarlier'],
        invalidSelection: control.errors?.['invalidSelection'],
      };

      if (control instanceof FormGroup) {
        this.markAllFieldsAsTouched(control);
      } else if (control instanceof FormArray) {
        control.controls.forEach((arrayControl) => {
          if (arrayControl instanceof FormGroup) {
            this.markAllFieldsAsTouched(arrayControl);
          } else {
            const arrayControlCustomErrors = {
              duplicateClient: arrayControl.errors?.['duplicateClient'],
              duplicateClientMessage:
                arrayControl.errors?.['duplicateClientMessage'],
              duplicateServer: arrayControl.errors?.['duplicateServer'],
              duplicateServerMessage:
                arrayControl.errors?.['duplicateServerMessage'],
              invalidLength: arrayControl.errors?.['invalidLength'],
              invalidLengthMessage:
                arrayControl.errors?.['invalidLengthMessage'],
              invalidSelection: arrayControl.errors?.['invalidSelection'],
            };
            arrayControl.markAsTouched();
            arrayControl.updateValueAndValidity();
            // Restore custom errors if they existed
            if (
              Object.values(arrayControlCustomErrors).some(
                (error) => error !== undefined,
              )
            ) {
              const currentErrors = arrayControl.errors || {};
              arrayControl.setErrors({
                ...currentErrors,
                ...arrayControlCustomErrors,
              });
            }
          }
        });
      } else {
        control.markAsTouched();
        control.updateValueAndValidity();
        // Restore custom errors if they existed
        if (
          Object.values(existingCustomErrors).some(
            (error) => error !== undefined,
          )
        ) {
          const currentErrors = control.errors || {};
          control.setErrors({
            ...currentErrors,
            ...existingCustomErrors,
          });
        }
      }
    });
  }

  //#region "Validators"
  // General validation status helper
  getControlValidateStatus(control: AbstractControl | null): string {
    if (!control) return '';
    return control.errors && control.touched ? 'error' : '';
  }

  // General error message helper
  getControlErrorMessage(
    control: AbstractControl | null,
    fieldName: string,
  ): string {
    if (!control?.errors || !control.touched) return '';

    if (control.errors['required']) {
      return `Cannot be empty`;
    }
    if (control.errors['invalidLength']) {
      return control.errors['invalidLengthMessage'];
    }
    if (control.errors['duplicateClient']) {
      return control.errors['duplicateClientMessage'];
    }
    if (control.errors['duplicateServer']) {
      return control.errors['duplicateServerMessage'];
    }
    if (control.errors['dateOrder']) {
      return control.errors['dateOrderMessage'];
    }
    return '';
  }

  // Usage for birthDate
  getBirthDateValidateStatus(): string {
    return this.getControlValidateStatus(
      this.biographicalDetailForm.get('birthDate'),
    );
  }

  getBirthDateErrorMessage(): string {
    return this.getControlErrorMessage(
      this.biographicalDetailForm.get('birthDate'),
      'Date of Birth',
    );
  }

  // Usage for National ID
  getNationalIdValidateStatus(index: number): string {
    return this.getControlValidateStatus(
      this.nationalIds.at(index)?.get('number'),
    );
  }

  getNationalIdErrorMessage(index: number): string {
    return this.getControlErrorMessage(
      this.nationalIds.at(index)?.get('number'),
      'National ID',
    );
  }

  getIssueDateValidateStatus(index: number): string {
    const control = this.nationalIds.at(index)?.get('issueDate');
    if (!control) return '';
    return control.errors && control.touched ? 'error' : '';
  }

  getIssueDateErrorMessage(index: number): string {
    const control = this.nationalIds.at(index)?.get('issueDate');
    if (!control?.errors || !control.touched) return '';

    if (control.errors['required']) {
      return 'Cannot be empty';
    }
    if (control.errors['issueDateEarlier']) {
      return control.errors['issueDateEarlier'];
    }
    return '';
  }

  getNationalIdNumberValidateStatus(index: number): string {
    const control = this.nationalIds.at(index)?.get('number');
    if (!control) return '';

    if (control.errors && (control.touched || control.dirty)) {
      return 'error';
    }
    return '';
  }

  getNationalIdNumberErrorMessage(index: number): string {
    const control = this.nationalIds.at(index)?.get('number');
    if (!control?.errors || !control.touched) return '';
    if (control.errors['required']) {
      return 'ID Number is required';
    }
    if (control.errors['duplicateClient']) {
      return control.errors['duplicateClientMessage'];
    }
    if (control.errors['duplicateServer']) {
      return control.errors['duplicateServerMessage'];
    }
    return '';
  }

  getTaxInfoIssueDateValidateStatus(): string {
    const control = this.taxInfoForm?.get('issueDate');
    if (!control) return '';
    return control.errors && control.touched ? 'error' : '';
  }

  getTaxInfoIssueDateErrorMessage(): string {
    const control = this.biographicalDetailForm
      .get('taxInfo')
      ?.get('issueDate');
    if (!control?.errors || !control.touched) return '';

    if (control.errors['required']) {
      return 'Issue Date is required';
    }
    if (control.errors['issueDateEarlier']) {
      return control.errors['issueDateEarlier'];
    }
    return '';
  }

  getOtherNationalityValidateStatus(): string {
    const control = this.biographicalDetailForm.get('otherNationality');
    if (control?.errors?.['duplicateNationality']) {
      return 'error';
    }
    return '';
  }

  //#endregion

  

  private startErrorResetTimer() {
    if (this.resetErrorTimeoutId) {
      clearTimeout(this.resetErrorTimeoutId);
    }
    this.resetErrorTimeoutId = setTimeout(() => {
      this.errorMsg = '';
      // Only clear error files, keep valid ones
      const validFiles = this.fileList.filter((f) => f.status !== 'error');
      this.fileList = validFiles;
    }, 3000);
  }

  public cleanupSubscriptions() {
    this.subscriptions.unsubscribe();
    // Create a new Subscription object for future subscriptions
    this.subscriptions = new Subscription();
  }

  public resubscribeToChanges() {
    // Only reset the dialog showing flag, keep the warning shown state
    this.isUnder18DialogShowing = false;
    // subscribe to birth date change
    this.subscribeToBirthDateChange();
    // subscribe to name changes
    this.subscribeToNameChanges();
  }

  // subscribe to birth date change
  private subscribeToBirthDateChange() {
    this.subscriptions.add(
      this.biographicalDetailForm
        .get('birthDate')!
        .valueChanges.pipe(debounceTime(300), distinctUntilChanged())
        .subscribe((date: any) => {
          if (date) {
            const age = this.calculateAge(date);
            this.biographicalDetailForm.patchValue(
              {
                age: `${age.years} years ${age.months} months`,
              },
              { emitEvent: false },
            );

            // Check for name duplication when birth date changes
            this.checkNameDuplication();

            // Show under 18 warning dialog only if not already showing and hasn't been shown for this birth date
            const birthDateString = new Date(date).toISOString();
            if (
              age.years < 18 &&
              !this.isUnder18DialogShowing &&
              this.currentBirthDateWarningShown !== birthDateString
            ) {
              this.isUnder18DialogShowing = true;
              this.currentBirthDateWarningShown = birthDateString;

              this.modalComponent.showDialog({
                nzTitle: 'Warning',
                nzContent: 'Employee is under 18 years old.',
                nzWrapClassName: 'popup popup-confirm under18warning',
                nzIconType: 'icons:warning',
                nzOkText: 'Confirm',
                nzOnOk: () => {
                  this.isUnder18DialogShowing = false;
                },
                nzOnCancel: () => {
                  this.isUnder18DialogShowing = false;
                },
              });
            }

            // update tax info issue date validation
            this.taxInfoForm?.get('issueDate')?.updateValueAndValidity();
          } else {
            this.biographicalDetailForm.patchValue({ age: '' });
            this.currentBirthDateWarningShown = null; // Reset when birth date is cleared
          }
        }),
    );
  }

  /**
   * Validates selected dropdown values against their option lists.
   * Sets 'invalidSelection' error on fields with invalid values.
   */
  private validateSelectedValues(): void {
    // Helper function to validate a control against its option list
    const validateDropdownValue = (
      control: AbstractControl | null,
      optionsList: any[],
      customMessage?: string,
    ) => {
      if (
        !control ||
        !control.value ||
        !optionsList ||
        optionsList.length === 0
      )
        return;

      const value = control.value;
      const isValid = optionsList.some((option) => option.value === value);

      if (!isValid) {
        const existingErrors = control.errors || {};
        const fieldName = customMessage || 'Selected value';
        const errorMessage = `${fieldName} is not correct.`;

        control.setErrors({
          ...existingErrors,
          invalidSelection: {
            valid: false,
            message: errorMessage,
          },
        });
        control.markAsTouched();
      }
    };

    // Define mapping of form controls to their option lists
    const mainFormControlsMap = [
      {
        control: this.biographicalDetailForm.get('maritalStatusCode'),
        optionsList: this.maritalStatusList,
        message: 'Marital Status',
      },
      {
        control: this.biographicalDetailForm.get('religionCode'),
        optionsList: this.religionList,
        message: 'Religion',
      },
      {
        control: this.biographicalDetailForm.get('genderCode'),
        optionsList: this.genderList,
        message: 'Gender',
      },
      {
        control: this.biographicalDetailForm.get('ethnicCode'),
        optionsList: this.ethnicList,
        message: 'Ethnic Group',
      },
      {
        control: this.biographicalDetailForm.get('otherNationality'),
        optionsList: this.nationalityList,
        message: 'Other Nationality',
      },
    ];

    // Validate main form controls
    mainFormControlsMap.forEach((item) => {
      validateDropdownValue(item.control, item.optionsList, item.message);
    });

    // Validate phone info array
    if (this.phoneInfos) {
      this.phoneInfos.controls.forEach((control) => {
        const phoneFormControlsMap = [
          {
            control: control.get('phoneTypeCode'),
            optionsList: this.phoneTypeList,
            message: 'Phone Type',
          },
          {
            control: control.get('callingTimeCode'),
            optionsList: this.timeToCallList,
            message: 'Time to Call',
          },
        ];

        phoneFormControlsMap.forEach((item) => {
          validateDropdownValue(item.control, item.optionsList, item.message);
        });
      });
    }

    // validate national id type and issue by in array
    if (this.nationalIds) {
      this.nationalIds.controls.forEach((control) => {
        validateDropdownValue(
          control.get('identityDocumentTypeCode'),
          this.nationalIdTypeList,
          'National ID Type',
        );
        validateDropdownValue(
          control.get('issueByCode'),
          this.issueByList,
          'Issue By',
        );
      });
    }

    // validate pit issue by
    validateDropdownValue(
      this.taxInfoForm?.get('issueByCode'),
      //this.issueByPITList,
      this.selectManager.getState('issueByCode')?.data ?? [],
      'Issue By',
    );
  }

  // clear invalid selection errors
  private clearInvalidSelectionErrors() {
    // Clear invalid selection errors from main form controls
    Object.values(this.biographicalDetailForm.controls).forEach((control) => {
      if (control?.errors?.['invalidSelection']) {
        const errors = { ...control.errors };
        delete errors['invalidSelection'];
        control.setErrors(Object.keys(errors).length ? errors : null);
        control.updateValueAndValidity();
      }

      // Handle FormArrays
      if (control instanceof FormArray) {
        control.controls.forEach((arrayControl) => {
          if (arrayControl instanceof FormGroup) {
            Object.values(arrayControl.controls).forEach((groupControl) => {
              if (groupControl?.errors?.['invalidSelection']) {
                const errors = { ...groupControl.errors };
                delete errors['invalidSelection'];
                groupControl.setErrors(
                  Object.keys(errors).length ? errors : null,
                );
                groupControl.updateValueAndValidity();
              }
            });
          }
        });
      }

      // Handle FormGroups
      if (control instanceof FormGroup) {
        Object.values(control.controls).forEach((groupControl) => {
          if (groupControl?.errors?.['invalidSelection']) {
            const errors = { ...groupControl.errors };
            delete errors['invalidSelection'];
            groupControl.setErrors(Object.keys(errors).length ? errors : null);
            groupControl.updateValueAndValidity();
          }
        });
      }
    });
  }

  // Get the validation status for effectiveDateFrom
  getEffectiveDateValidateStatus(): string {
    const control = this.biographicalDetailForm.get('effectiveDateFrom');
    return this.getControlValidateStatus(control);
  }

  // Get any error messages for effectiveDateFrom
  getEffectiveDateErrorMessage(): string {
    const control = this.biographicalDetailForm.get('effectiveDateFrom');

    // Check for required error
    if (control?.errors?.['required']) {
      return 'Cannot be empty';
    }

    // Check for job data date order error
    if (control?.errors?.['jobDataDateOrder']) {
      return (
        control.errors?.['jobDataDateOrderMessage'] ||
        'Effective Date cannot be later than Job Data Effective Date'
      );
    }

    return this.getControlErrorMessage(control, 'Effective Date');
  }

  private lastDuplicateWarningData: {
    firstName: string | null;
    middleName: string | null;
    lastName: string | null;
    birthDate: string | null;
  } | null = null;


  //#region "infinite scroll"

  selectManager = inject(SelectManager);
  private readonly SEARCH_DEBOUNCE = 300;
  private searchDebounceTimer: any;

  private registerSelects(): void {
    // Register issueByCode select
    this.selectManager.registerSelect({
      fieldName: 'issueByCode',
      serviceMethod: (params) => this.hrFsBp001Service.getPicklistByCodeAndParentWithPagination(
        PicklistConstants.ISSUEBYPIT_LIST, undefined, 
        params.searchValue,
        this.getEffectiveDate()?.toString(),
        params.page,
        params.pageSize
      ),
      transformResponse: (response: any) => {
        return response.map((item: any) => ({
          value: item.code,
          label: item.name?.default
        }));
      },
      dependsOn: ['effectiveDateFrom']
    });
  }

  onSelectScroll(fieldName: string): void {
    const filters = this.getFiltersForField(fieldName);
    this.selectManager.loadMore(fieldName, filters).subscribe(data => {
      // Update your component's data array if needed
    });
  }

  private getFiltersForField(fieldName: string): Record<string, any> {
    const filters: Record<string, any> = {
      effectiveDate: this.getEffectiveDate()
    };

    return filters;
  }

  onSelectSearch(fieldName: string, searchValue: string): void {
    // Skip empty searches during selection
    if (searchValue === '' && this.selectManager.isSelecting.get(fieldName)) {
      return;
    }

    // Debounce the search
    if (this.searchDebounceTimer) {
      clearTimeout(this.searchDebounceTimer);
    }

    this.searchDebounceTimer = setTimeout(() => {
      const filters = this.getFiltersForField(fieldName);
      this.selectManager.search(fieldName, searchValue, filters).subscribe();
    }, this.SEARCH_DEBOUNCE);
  }

  onSelectChange(fieldName: string): void {
    this.selectManager.onSelectChange(fieldName);
  }
  //#endregion
}
