export class Constants {
  static readonly DEFAULT_COUNTRY_CODE = 'VNM';
  static readonly DEFAULT_COUNTRY_CODE_UAT = 'VNM';

  static readonly DEFAULT_NATIONALITY_CODE = 'VNM';
  static readonly DEFAULT_NATIONALITY_CODE_UAT = 'VNM';

  // the identification type codes that are used to apply rule
  static readonly IDENTIFICATION_TYPE_CODES = ['VNM_IC4', 'VNM_IC', 'VNM_IC3', 'VNM_IC2', 'CIC'];
  static readonly IDENTIFICATION_TYPE_CODES_UAT = ['VNM_IC4', 'VNM_IC', 'VNM_IC3', 'VNM_IC2', 'VNM_IC05', 'CIC'];

  // 12 characters identification type codes
  static readonly IDENTIFICATION_TYPE_CODES_12_CHARS = ['VNM_IC4', 'VNM_IC05', 'CIC'];
  // 9 or 12 characters identification type codes
  static readonly IDENTIFICATION_TYPE_CODES_9_12_CHARS = ['VNM_IC2', 'VNM_IC3', 'VNM_IC'];

  static readonly IDENTIFICATION_MIN_LENGTH = 9;
  static readonly IDENTIFICATION_MAX_LENGTH = 12;

  static readonly RULE_AUTOMATIC = 'Automatic';
  static readonly RULE_MANUAL = 'Manual';
}

export enum ERROR_MESSAGE {
  FILE_SIZE = 'The file attachment exceeds the maximum allowed capacity. Please try again!',
  FILE_TYPE = 'Some thing wrong!, please check your file format',
}