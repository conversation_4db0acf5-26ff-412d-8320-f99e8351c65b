<div class="content-wrapper">
    <div class="quick-nav">
        <nz-anchor [nzAffix]="false">
            <nz-link nz-href="#action" (click)="scrollToSection('action')">
                <div class="menu-item">
                    <span class="menu-number">1</span>
                    <span class="menu-title">Action</span>
                    <span class="menu-required">*</span>
                </div>
            </nz-link>
            <nz-link nz-href="#assignment" (click)="scrollToSection('assignment')">
                <div class="menu-item">
                    <span class="menu-number">2</span>
                    <span class="menu-title">Assignment</span>
                    <span class="menu-required">*</span>
                </div>
            </nz-link>
            <nz-link nz-href="#employment" (click)="scrollToSection('employment')">
                <div class="menu-item">
                    <span class="menu-number">3</span>
                    <span class="menu-title">Employment</span>
                    <span class="menu-required">*</span>
                </div>
            </nz-link>
        </nz-anchor>
    </div>
    <div class="main-content">
        <form nz-form [formGroup]="jobDataForm" nzLayout="vertical" preventEnterSubmit class="hrdx-form">
            <div class="section">
                <nz-row [nzGutter]="16">
                    <nz-col [nzSpan]="12">
                        <nz-form-item>
                            <nz-form-label nzNoColon>Organizational Instance Rcd</nz-form-label>
                            <nz-form-control>
                                <input nz-input formControlName="organizationalInstanceRcd" placeholder="0" />
                            </nz-form-control>
                        </nz-form-item>
                    </nz-col>
                    <nz-col [nzSpan]="12">
                        <nz-form-item>
                            <nz-form-label nzNoColon>Employee Record Number</nz-form-label>
                            <nz-form-control>
                                <input nz-input formControlName="employeeRecordNumber" placeholder="0" />
                            </nz-form-control>
                        </nz-form-item>
                    </nz-col>
                </nz-row>
            </div>

            <div id="action" class="section">
                <h3>Action</h3>
                <nz-row [nzGutter]="16">
                    <nz-col [nzSpan]="12">
                        <nz-form-item>
                            <nz-form-label nzRequired nzNoColon>Effective Date</nz-form-label>
                            <nz-form-control nzErrorTip="Cannot be empty">
                                <nz-date-picker formControlName="effectiveDateFrom"
                                    nzPlaceHolder="DD/MM/YYYY"
                                    [nzSuffixIcon]="nzSuffixIcon"
                                    [nzDisabledDate]="disablePastDate"
                                    datePickerBlur
                                    (datePickerBlur)="selectDate($event.event, 'effectiveDateFrom')"
                                    nzFormat="dd/MM/yyyy"></nz-date-picker>
                            </nz-form-control>
                        </nz-form-item>
                    </nz-col>
                    <nz-col [nzSpan]="12">
                        <nz-form-item>
                            <nz-form-label nzRequired nzNoColon>Action</nz-form-label>
                            <nz-form-control [nzErrorTip]="errorTplAction">
                                <nz-select formControlName="actionCode" nzPlaceHolder="Select Action" 
                                nzShowSearch nzAllowClear 
                                [nzDropdownClassName]="'add-person-select'"
                                [nzOptionHeightPx]="44"
                                (ngModelChange)="onActionChange($event)">
                                    @if (!isNZSelectLoading) {
                                        @for (o of actionList; track o) {
                                            <nz-option [nzValue]="o.value" [nzLabel]="o.label" nzCustomContent>
                                                <span class="ant-select-selection-item-content">{{ o.label }}</span>
                                                <hrdx-icon icon="icon-check-bold" class="selected-icon" />
                                            </nz-option>
                                        }
                                    } @else {
                                        <nz-option nzDisabled nzCustomContent>
                                            <span nz-icon nzType="loading" class="loading-icon"></span>
                                            Loading Data...
                                        </nz-option>
                                    }
                                </nz-select>
                                <ng-template #errorTplAction let-control>
                                    <ng-container *ngIf="control.hasError('required')">Cannot be empty</ng-container>
                                    <ng-container *ngIf="control.hasError('invalidSelection')">{{ control.errors?.['message'] }}</ng-container>
                                    <ng-container *ngIf="control.hasError('serverError')">{{ control.errors?.['message'] }}</ng-container>
                                </ng-template>
                            </nz-form-control>
                        </nz-form-item>
                    </nz-col>
                </nz-row>
                <nz-row [nzGutter]="16">
                    <nz-col [nzSpan]="12">
                        <nz-form-item>
                            <nz-form-label nzRequired nzNoColon>Reason</nz-form-label>
                            <nz-form-control [nzErrorTip]="errorTplReason">
                                <nz-select formControlName="actionReasonCode" nzPlaceHolder="Select Reason" 
                                nzShowSearch nzAllowClear
                                [nzDropdownClassName]="'add-person-select'"
                                [nzOptionHeightPx]="44">
                                    @for (option of reasonList; track option) {
                                        <nz-option [nzValue]="option.value" [nzLabel]="option.label" nzCustomContent>
                                            <span class="ant-select-selection-item-content">{{ option.label }}</span>
                                            <hrdx-icon icon="icon-check-bold" class="selected-icon" />
                                        </nz-option>
                                    }
                                </nz-select>
                                <ng-template #errorTplReason let-control>
                                    <ng-container *ngIf="control.hasError('required')">Cannot be empty</ng-container>
                                    <ng-container *ngIf="control.hasError('serverError')">{{ control.errors?.['message'] }}</ng-container>
                                </ng-template>
                            </nz-form-control>
                        </nz-form-item>
                    </nz-col>
                    <nz-col [nzSpan]="12">
                        <nz-form-item>
                            <nz-form-label nzRequired nzNoColon>Effective Sequence</nz-form-label>
                            <nz-form-control>
                                <input nz-input formControlName="effectiveSequence" placeholder="0" [disabled]="true" />
                            </nz-form-control>
                        </nz-form-item>
                    </nz-col>
                </nz-row>
                <nz-row [nzGutter]="16">
                    <nz-col [nzSpan]="12">
                        <nz-form-item>
                            <nz-form-label nzRequired nzNoColon>HR Status</nz-form-label>
                            <nz-form-control>
                                <nz-radio-group formControlName="hrStatusCode">
                                    <label nz-radio *ngFor="let status of hrStatusList"
                                    [nzDisabled]="status.value != 'A'"
                                        [nzValue]="status.value">{{status.label}}</label>
                                </nz-radio-group>
                            </nz-form-control>
                        </nz-form-item>
                    </nz-col>
                    <nz-col [nzSpan]="12">
                        <nz-form-item>
                            <nz-form-label nzRequired nzNoColon>Payroll Status</nz-form-label>
                            <nz-form-control nzErrorTip="Cannot be empty">
                                <nz-select formControlName="prStatusCode" nzPlaceHolder="Select Payroll Status" 
                                nzShowSearch nzAllowClear
                                [nzDropdownClassName]="'add-person-select'"
                                [nzOptionHeightPx]="44">
                                    @for (option of payrollStatusList; track option) {
                                        <nz-option [nzValue]="option.value" [nzLabel]="option.label" nzCustomContent>
                                            <span class="ant-select-selection-item-content">{{ option.label }}</span>
                                            <hrdx-icon icon="icon-check-bold" class="selected-icon" />
                                        </nz-option>
                                    }
                                </nz-select>
                            </nz-form-control>
                        </nz-form-item>
                    </nz-col>
                </nz-row>
                <nz-row [nzGutter]="16">
                    <nz-col [nzSpan]="12">
                        <nz-form-item>
                            <nz-form-label nzNoColon>Employee Group</nz-form-label>
                            <nz-form-control [nzErrorTip]="errorTplEmployeeGroup">
                                <input nz-input [value]="employeeGroupName" placeholder="Employee Group" [disabled]="true" />
                                <ng-template #errorTplEmployeeGroup let-control>
                                    <ng-container *ngIf="control.hasError('serverError')">{{ control.errors?.['message'] }}</ng-container>
                                </ng-template>
                            </nz-form-control>
                        </nz-form-item>
                    </nz-col>
                    <nz-col [nzSpan]="12">
                        <nz-form-item>
                            <nz-form-label nzRequired nzNoColon>Job Indicator</nz-form-label>
                            <nz-form-control>
                                <nz-radio-group formControlName="jobIndicatorCode">
                                    <label nz-radio *ngFor="let indicator of jobIndicatorList"
                                        [nzValue]="indicator.value">{{indicator.label}}</label>
                                </nz-radio-group>
                            </nz-form-control>
                        </nz-form-item>
                    </nz-col>
                </nz-row>
                <nz-row [nzGutter]="16">
                    <nz-col [nzSpan]="12">
                        <nz-form-item>
                            <nz-form-label nzRequired nzNoColon>Employee Sub Group</nz-form-label>
                            <nz-form-control [nzErrorTip]="errorTplEmployeeSubGroup">
                                <!-- <input nz-input [value]="employeeSubGroupName" placeholder="Employee Sub Group"
                                    [disabled]="true" /> -->

                                <nz-select formControlName="employeeSubGroupCode" nzPlaceHolder="Select Employee Sub Group" nzShowSearch nzAllowClear
                                    [nzDropdownClassName]="'add-person-select'" [nzOptionHeightPx]="44">
                                      @for (option of employeeSubGroupList; track option) {
                                        <nz-option [nzValue]="option.value" [nzLabel]="option.label" nzCustomContent>
                                          <span class="ant-select-selection-item-content">{{ option.label }}</span>
                                          <hrdx-icon icon="icon-check-bold" class="selected-icon" />
                                        </nz-option>
                                      }
                                </nz-select>
                                <ng-template #errorTplEmployeeSubGroup let-control>
                                    <ng-container *ngIf="control.hasError('required')">Cannot be empty</ng-container>
                                    <ng-container *ngIf="control.hasError('invalidSelection')">{{ control.errors?.['message'] }}</ng-container>
                                    <ng-container *ngIf="control.hasError('serverError')">{{ control.errors?.['message'] }}</ng-container>
                                </ng-template>
                            </nz-form-control>
                        </nz-form-item>
                    </nz-col>
                    <nz-col [nzSpan]="12">
                        <nz-form-item>
                            <nz-form-label nzRequired nzNoColon>Manager?</nz-form-label>
                            <nz-form-control nzErrorTip="Cannot be empty">
                                <nz-select formControlName="isManagerCode" nzPlaceHolder="Select Manager" 
                                nzShowSearch nzAllowClear
                                [nzDropdownClassName]="'add-person-select'"
                                [nzOptionHeightPx]="44">
                                    @for (option of managerList; track option) {
                                        <nz-option [nzValue]="option.value" [nzLabel]="option.label" nzCustomContent>
                                            <span class="ant-select-selection-item-content">{{ option.label }}</span>
                                            <hrdx-icon icon="icon-check-bold" class="selected-icon" />
                                        </nz-option>
                                    }
                                </nz-select>
                            </nz-form-control>
                        </nz-form-item>
                    </nz-col>
                </nz-row>
                <nz-row [nzGutter]="16">
                    <nz-col [nzSpan]="12">
                        <nz-form-item>
                            <nz-form-label nzNoColon>Level of Decision</nz-form-label>
                            <nz-form-control>
                                <nz-select formControlName="levelDecisionCode" nzPlaceHolder="Select Level of Decision" 
                                nzShowSearch nzAllowClear
                                [nzDropdownClassName]="'add-person-select'"
                                [nzOptionHeightPx]="44">
                                    @for (option of levelOfDecisionList; track option) {
                                        <nz-option [nzValue]="option.value" [nzLabel]="option.label" nzCustomContent>
                                            <span class="ant-select-selection-item-content">{{ option.label }}</span>
                                            <hrdx-icon icon="icon-check-bold" class="selected-icon" />
                                        </nz-option>
                                    }
                                </nz-select>
                            </nz-form-control>
                        </nz-form-item>
                    </nz-col>
                    <nz-col [nzSpan]="12">
                        <nz-form-item>
                            <nz-form-label nzNoColon>Decision Number</nz-form-label>
                            <nz-form-control>
                                <input nz-input formControlName="decisionNumber" placeholder="Enter Decision Number" />
                            </nz-form-control>
                        </nz-form-item>
                    </nz-col>
                </nz-row>
                <nz-row [nzGutter]="16">
                    <nz-col [nzSpan]="12">
                        <nz-form-item>
                            <nz-form-label nzNoColon>Sign Date</nz-form-label>
                            <nz-form-control>
                                <nz-date-picker nzPlaceHolder="DD/MM/YYYY" [nzSuffixIcon]="nzSuffixIcon"
                                datePickerBlur
                                (datePickerBlur)="selectDate($event.event, 'signDate')"
                                formControlName="signDate" nzFormat="dd/MM/yyyy"></nz-date-picker>
                            </nz-form-control>
                        </nz-form-item>
                    </nz-col>
                    <nz-col [nzSpan]="12">
                        <nz-form-item>
                            <nz-form-label nzNoColon>Expected End Date</nz-form-label>
                            <nz-form-control>
                                <nz-date-picker nzPlaceHolder="DD/MM/YYYY" [nzSuffixIcon]="nzSuffixIcon"  [nzDisabledDate]="disabledExpectedEndDate"
                                datePickerBlur
                                (datePickerBlur)="selectDate($event.event, 'expectedEndDate')"
                                (keyup.enter)="selectDate($event, 'expectedEndDate')" formControlName="expectedEndDate"
                                    nzFormat="dd/MM/yyyy"></nz-date-picker>
                            </nz-form-control>
                        </nz-form-item>
                    </nz-col>
                </nz-row>
                <nz-form-item>
                    <nz-form-label nzNoColon>Note</nz-form-label>
                    <nz-form-control>
                        <nz-textarea-count [nzMaxCharacterCount]="1000">
                            <textarea [maxlength]="1000" nz-input formControlName="note" placeholder="Enter Note" [nzAutosize]="{ minRows: 3, maxRows: 5 }"></textarea>
                        </nz-textarea-count>
                    </nz-form-control>
                </nz-form-item>
            </div>


            <div id="assignment" class="section">
                <h3>Assignment</h3>
                <nz-row [nzGutter]="16">
                    <nz-col [nzSpan]="12">
                        <nz-form-item>
                            <nz-form-label nzNoColon>Position</nz-form-label>
                            <nz-form-control [nzErrorTip]="errorTplPosition">
                                <nz-select formControlName="positionCode" 
                                nzPlaceHolder="Select Position" 
                                nzShowSearch nzAllowClear
                                [nzDropdownClassName]="'add-person-select'"
                                [nzOptionHeightPx]="44"
                                (nzScrollToBottom)="onSelectScroll('positionCode')"
                                (nzOnSearch)="onSelectSearch('positionCode', $event)"
                                (ngModelChange)="onSelectChange('positionCode')"
                                [nzLoading]="selectManager.getState('positionCode')?.isLoading">
                                    @for (item of selectManager.getState('positionCode')?.data; track item) {
                                        <nz-option [nzValue]="item.value" 
                                        [nzLabel]="item.label + ' (' + item.value + ')'"
                                        nzCustomContent>
                                            <span class="ant-select-selection-item-content">{{ item.label + ' (' + item.value + ')' }}</span>
                                            <hrdx-icon icon="icon-check-bold" class="selected-icon" />
                                        </nz-option>
                                    }
                                    @if (selectManager.getState('positionCode')?.isLoading) {
                                        <nz-option nzDisabled nzCustomContent>
                                            <span nz-icon nzType="loading" class="loading-icon"></span>
                                            Loading Data...
                                        </nz-option>
                                    }
                                </nz-select>
                                <ng-template #errorTplPosition let-control>
                                    <ng-container *ngIf="control.hasError('serverError')">{{ control.errors?.['message'] }}</ng-container>
                                </ng-template>
                            </nz-form-control>
                        </nz-form-item>
                    </nz-col>
                    <nz-col [nzSpan]="12">
                        <nz-form-item>
                            <nz-form-label nzNoColon>Cost Center</nz-form-label>
                            <nz-form-control [nzErrorTip]="errorTplCostCenter">
                                <nz-select formControlName="costCenterCode"
                                nzPlaceHolder="Select Cost Center"
                                nzShowSearch nzAllowClear
                                [nzDropdownClassName]="'add-person-select'"
                                [nzOptionHeightPx]="44"
                                (nzScrollToBottom)="onSelectScroll('costCenterCode')"
                                (nzOnSearch)="onSelectSearch('costCenterCode', $event)"
                                (ngModelChange)="onSelectChange('costCenterCode')"
                                [nzLoading]="selectManager.getState('costCenterCode')?.isLoading">
                                    @for (item of selectManager.getState('costCenterCode')?.data; track item) {
                                        <nz-option [nzValue]="item.value" 
                                        [nzLabel]="item.label + ' (' + item.value + ')'"
                                        nzCustomContent>
                                            <span class="ant-select-selection-item-content">{{ item.label + ' (' + item.value + ')' }}</span>
                                            <hrdx-icon icon="icon-check-bold" class="selected-icon" />
                                        </nz-option>
                                    }
                                    @if (selectManager.getState('costCenterCode')?.isLoading) {
                                        <nz-option nzDisabled nzCustomContent>
                                            <span nz-icon nzType="loading" class="loading-icon"></span>
                                            Loading Data...
                                        </nz-option>
                                    }
                                </nz-select>
                                <ng-template #errorTplCostCenter let-control>
                                    <ng-container *ngIf="control.hasError('serverError')">{{ control.errors?.['message'] }}</ng-container>
                                  </ng-template>
                            </nz-form-control>
                        </nz-form-item>
                    </nz-col>
                </nz-row>
                <nz-row [nzGutter]="16">
                    <nz-col [nzSpan]="12">
                        <nz-form-item>
                            <nz-form-label nzRequired nzNoColon>Company</nz-form-label>
                            <nz-form-control [nzErrorTip]="errorTplCompany">
                                <nz-select formControlName="companyCode" nzPlaceHolder="Select Company" [nzDisabled]="true">
                                    <nz-option *ngFor="let o of companyList" [nzValue]="o.value" [nzLabel]="o.label + ' (' + o.value + ')'"></nz-option>
                                </nz-select>
                                <ng-template #errorTplCompany let-control>
                                    <ng-container *ngIf="control.hasError('serverError')">{{ control.errors?.['message'] }}</ng-container>
                                </ng-template>
                            </nz-form-control>
                        </nz-form-item>
                    </nz-col>
                    <nz-col [nzSpan]="12">
                        <nz-form-item>
                            <nz-form-label nzNoColon>Report To Pos</nz-form-label>
                            <nz-form-control [nzErrorTip]="errorTplReportPosition">
                                <nz-select formControlName="reportPosition"
                                nzPlaceHolder="Select Report To Pos"
                                nzShowSearch nzAllowClear
                                [nzDropdownClassName]="'add-person-select'"
                                [nzOptionHeightPx]="44"
                                (nzScrollToBottom)="onSelectScroll('reportPosition')"
                                (nzOnSearch)="onSelectSearch('reportPosition', $event)"
                                (ngModelChange)="onSelectChange('reportPosition')"
                                [nzLoading]="selectManager.getState('reportPosition')?.isLoading">
                                    @for (item of selectManager.getState('reportPosition')?.data; track item) {
                                        <nz-option [nzValue]="item.value" 
                                        [nzLabel]="item.label + ' (' + item.value + ')'"
                                        nzCustomContent>
                                            <span class="ant-select-selection-item-content">{{ item.label + ' (' + item.value + ')' }}</span>
                                            <hrdx-icon icon="icon-check-bold" class="selected-icon" />
                                        </nz-option>
                                    }
                                    @if (selectManager.getState('reportPosition')?.isLoading) {
                                        <nz-option nzDisabled nzCustomContent>
                                            <span nz-icon nzType="loading" class="loading-icon"></span>
                                            Loading Data...
                                        </nz-option>
                                    }
                                </nz-select>
                                <ng-template #errorTplReportPosition let-control>
                                    <ng-container *ngIf="control.hasError('serverError')">{{ control.errors?.['message'] }}</ng-container>
                                </ng-template>
                            </nz-form-control>
                        </nz-form-item>
                    </nz-col>
                </nz-row>
                <nz-row [nzGutter]="16">
                    <nz-col [nzSpan]="12">
                        <nz-form-item>
                            <nz-form-label nzRequired nzNoColon>Legal Entity</nz-form-label>
                            <nz-form-control [nzErrorTip]="errorTplLegalEntity">
                                <nz-select formControlName="legalEntityCode" 
                                nzPlaceHolder="Select Legal Entity" 
                                nzShowSearch nzAllowClear
                                [nzDropdownClassName]="'add-person-select'"
                                [nzOptionHeightPx]="44"
                                (nzScrollToBottom)="onSelectScroll('legalEntityCode')"
                                (nzOnSearch)="onSelectSearch('legalEntityCode', $event)"
                                (ngModelChange)="onSelectChange('legalEntityCode')"
                                [nzLoading]="selectManager.getState('legalEntityCode')?.isLoading">
                                    @for (item of selectManager.getState('legalEntityCode')?.data; track item) {
                                        <nz-option [nzValue]="item.value" 
                                        [nzLabel]="item.label + ' (' + item.value + ')'"
                                        nzCustomContent>
                                            <span class="ant-select-selection-item-content">{{ item.label + ' (' + item.value + ')' }}</span>
                                            <hrdx-icon icon="icon-check-bold" class="selected-icon" />
                                        </nz-option>
                                    }
                                    @if (selectManager.getState('legalEntityCode')?.isLoading) {
                                        <nz-option nzDisabled nzCustomContent>
                                            <span nz-icon nzType="loading" class="loading-icon"></span>
                                            Loading Data...
                                        </nz-option>
                                    }
                                </nz-select>
                                <ng-template #errorTplLegalEntity let-control>
                                    <ng-container *ngIf="control.hasError('required')">Cannot be empty</ng-container>
                                    <ng-container *ngIf="control.hasError('serverError')">{{ control.errors?.['message'] }}</ng-container>
                                  </ng-template>
                            </nz-form-control>
                        </nz-form-item>
                    </nz-col>
                    <nz-col [nzSpan]="12">
                        <nz-form-item>
                            <nz-form-label nzNoColon>Supervisor</nz-form-label>
                            <nz-form-control [nzErrorTip]="errorTplSupervisor">
                                <nz-select formControlName="supervisor"
                                nzPlaceHolder="Select Supervisor"
                                nzShowSearch nzAllowClear
                                [nzDropdownClassName]="'add-person-select'"
                                [nzOptionHeightPx]="44"
                                (nzScrollToBottom)="onSelectScroll('supervisor')"
                                (nzOnSearch)="onSelectSearch('supervisor', $event)"
                                (ngModelChange)="onSelectChange('supervisor')"
                                [nzLoading]="selectManager.getState('supervisor')?.isLoading">
                                    @for (item of selectManager.getState('supervisor')?.data; track item) {
                                        <nz-option [nzValue]="item.value" 
                                        [nzLabel]="item.label + ' (' + item.value + ')'"
                                        nzCustomContent>
                                            <span class="ant-select-selection-item-content">{{ item.label + ' (' + item.value + ')' }}</span>
                                            <hrdx-icon icon="icon-check-bold" class="selected-icon" />
                                        </nz-option>
                                    }
                                    @if (selectManager.getState('supervisor')?.isLoading) {
                                        <nz-option nzDisabled nzCustomContent>
                                            <span nz-icon nzType="loading" class="loading-icon"></span>
                                            Loading Data...
                                        </nz-option>
                                    }
                                </nz-select>
                                <ng-template #errorTplSupervisor let-control>
                                    <ng-container *ngIf="control.hasError('serverError')">{{ control.errors?.['message'] }}</ng-container>
                                </ng-template>
                            </nz-form-control>
                        </nz-form-item>
                    </nz-col>
                </nz-row>
                <nz-row [nzGutter]="16">
                    <nz-col [nzSpan]="12">
                        <nz-form-item>
                            <nz-form-label nzNoColon>Business Unit</nz-form-label>
                            <nz-form-control [nzErrorTip]="errorTplBusinessUnit">
                                <nz-select formControlName="businessUnitCode"
                                nzPlaceHolder="Select Business Unit" 
                                nzShowSearch nzAllowClear
                                [nzDropdownClassName]="'add-person-select'"
                                [nzOptionHeightPx]="44"
                                (nzScrollToBottom)="onSelectScroll('businessUnitCode')"
                                (nzOnSearch)="onSelectSearch('businessUnitCode', $event)"
                                (ngModelChange)="onSelectChange('businessUnitCode')"
                                [nzLoading]="selectManager.getState('businessUnitCode')?.isLoading">
                                    @for (item of selectManager.getState('businessUnitCode')?.data; track item) {
                                        <nz-option [nzValue]="item.value" 
                                        [nzLabel]="item.label + ' (' + item.value + ')'"
                                        nzCustomContent>
                                            <span class="ant-select-selection-item-content">{{ item.label + ' (' + item.value + ')' }}</span>
                                            <hrdx-icon icon="icon-check-bold" class="selected-icon" />
                                        </nz-option>
                                    }
                                    @if (selectManager.getState('businessUnitCode')?.isLoading) {
                                        <nz-option nzDisabled nzCustomContent>
                                            <span nz-icon nzType="loading" class="loading-icon"></span>
                                            Loading Data...
                                        </nz-option>
                                    }
                                </nz-select>
                                <ng-template #errorTplBusinessUnit let-control>
                                    <ng-container *ngIf="control.hasError('serverError')">{{ control.errors?.['message'] }}</ng-container>
                                  </ng-template>
                            </nz-form-control>
                        </nz-form-item>
                    </nz-col>
                    <nz-col [nzSpan]="12">
                        <nz-form-item>
                            <nz-form-label nzNoColon>Matrix Report To</nz-form-label>
                            <nz-form-control [nzErrorTip]="errorTplMatrixReportPosition">
                                <nz-select formControlName="matrixReportPositionCode"
                                class="select-all"
                                nzPlaceHolder="Select Matrix Report To"
                                nzShowSearch nzAllowClear
                                [nzDropdownClassName]="'add-person-select-all'"
                                [nzOptionHeightPx]="44"
                                [nzMode]="'multiple'"
                                [nzMaxTagCount]="1"
                                nzShowArrow="true"
                                [nzMaxTagPlaceholder]="tagPlaceHolder"
                                (nzScrollToBottom)="onSelectScroll('matrixReportPositionCode')"
                                (nzOnSearch)="onSelectSearch('matrixReportPositionCode', $event)"
                                (ngModelChange)="onMultiSelectChange($event, 'matrixReportPositionCode')"
                                [nzLoading]="selectManager.getState('matrixReportPositionCode')?.isLoading"
                                [nzMenuItemSelectedIcon]="selectedIcon"
                                [nzDropdownRender]="renderDropdown">
                                    @for (item of selectManager.getState('matrixReportPositionCode')?.data; track item) {
                                        <nz-option [nzValue]="item.value" 
                                        [nzLabel]="item.label + ' (' + item.value + ')'"
                                        (click)="onOptionClick(item.value, 'matrixReportPositionCode')"
                                        nzCustomContent>
                                            <div class="option-container">
                                                <label nz-checkbox 
                                                    [nzChecked]="isOptionSelected(item.value, 'matrixReportPositionCode')" 
                                                    (click)="$event.stopPropagation()" 
                                                    (nzCheckedChange)="onOptionCheckboxChange(item.value, $event, 'matrixReportPositionCode')">
                                                    <span class="ant-select-selection-item-content">{{ item.label + ' (' + item.value + ')' }}</span>
                                                    <span class="selected-icon" nz-icon [nzType]="'icons:check-bold'"></span>
                                                </label>
                                            </div>
                                        </nz-option>
                                    }
                                    @if (selectManager.getState('matrixReportPositionCode')?.isLoading) {
                                        <nz-option nzDisabled nzCustomContent>
                                            <span nz-icon nzType="loading" class="loading-icon"></span>
                                            Loading Data...
                                        </nz-option>
                                    }
                                </nz-select>
                                
                                <ng-template #renderDropdown let-menu>
                                    <div class="select-all-header">
                                        <div class="select-all-header-left">
                                            <label nz-checkbox 
                                                [nzChecked]="areAllOptionsSelected('matrixReportPositionCode')" 
                                                (nzCheckedChange)="onCheckAllOptions($event, 'matrixReportPositionCode')">
                                                {{ selectedMatrixReportPositions.length }} items selected
                                            </label>
                                        </div>
                                        <div class="select-all-header-right">
                                            <button nz-button nzType="link" (click)="deleteSelectedOptions('matrixReportPositionCode')">Delete</button>
                                        </div>
                                    </div>
                                    <nz-divider style="margin: 0"></nz-divider>
                                    {{ menu }}
                                </ng-template>
                                <ng-template #selectedIcon> </ng-template>
                                
                                <ng-template #tagPlaceHolder let-selectedList>
                                    +{{ selectedList.length }}
                                </ng-template>
                                <ng-template #errorTplMatrixReportPosition let-control>
                                    <ng-container *ngIf="control.hasError('serverError')">{{ control.errors?.['message'] }}</ng-container>
                                </ng-template>
                            </nz-form-control>
                        </nz-form-item>
                    </nz-col>
                </nz-row>
                <nz-row [nzGutter]="16">
                    <nz-col [nzSpan]="12">
                        <nz-form-item>
                            <nz-form-label nzNoColon>Division</nz-form-label>
                            <nz-form-control [nzErrorTip]="errorTplDivision">
                                <nz-select formControlName="divisionCode" 
                                nzPlaceHolder="Select Division" 
                                nzShowSearch nzAllowClear
                                [nzDropdownClassName]="'add-person-select'"
                                [nzOptionHeightPx]="44"
                                (nzOnSearch)="onNZSelectSearch($event, 'divisionCode')"
                                (nzOpenChange)="onSelectOpenChange($event, 'divisionCode')">
                                    @if (!isNZSelectLoading) {
                                        @for (o of divisionList; track o) {
                                            <nz-option [nzValue]="o.value" 
                                            [nzLabel]="o.label + ' (' + o.value + ')'"
                                            nzCustomContent>
                                                <span class="ant-select-selection-item-content">{{ o.label + ' (' + o.value + ')' }}</span>
                                                <hrdx-icon icon="icon-check-bold" class="selected-icon" />
                                            </nz-option>
                                        }
                                    } @else {
                                        <nz-option nzDisabled nzCustomContent>
                                            <span nz-icon nzType="loading" class="loading-icon"></span>
                                            Loading Data...
                                        </nz-option>
                                    }
                                </nz-select>
                                <ng-template #errorTplDivision let-control>
                                    <ng-container *ngIf="control.hasError('serverError')">{{ control.errors?.['message'] }}</ng-container>
                                  </ng-template>
                            </nz-form-control>
                        </nz-form-item>
                    </nz-col>
                    <nz-col [nzSpan]="12">
                        <nz-form-item>
                            <nz-form-label nzNoColon>Matrix Manager</nz-form-label>
                            <nz-form-control [nzErrorTip]="errorTplMatrixManager">
                                <nz-select formControlName="matrixManagers"
                                class="select-all"
                                nzPlaceHolder="Select Matrix Manager"
                                nzShowSearch nzAllowClear
                                [nzDropdownClassName]="'add-person-select-all'"
                                [nzOptionHeightPx]="44"
                                [nzMode]="'multiple'"
                                [nzMaxTagCount]="1"
                                nzShowArrow="true"
                                [nzMaxTagPlaceholder]="matrixManagersTagPlaceHolder"
                                (nzScrollToBottom)="onSelectScroll('matrixManagers')"
                                (nzOnSearch)="onSelectSearch('matrixManagers', $event)"
                                (ngModelChange)="onMultiSelectChange($event, 'matrixManagers')"
                                [nzLoading]="selectManager.getState('matrixManagers')?.isLoading"
                                [nzMenuItemSelectedIcon]="selectedIcon"
                                [nzDropdownRender]="matrixManagersDropdown">
                                    @for (item of selectManager.getState('matrixManagers')?.data; track item) {
                                        <nz-option [nzValue]="item.value" 
                                        [nzLabel]="item.label + ' (' + item.value + ')'"
                                        (click)="onOptionClick(item.value, 'matrixManagers')"
                                        nzCustomContent>
                                            <div class="option-container">
                                                <label nz-checkbox 
                                                    [nzChecked]="isOptionSelected(item.value, 'matrixManagers')" 
                                                    (click)="$event.stopPropagation()"
                                                    (nzCheckedChange)="onOptionCheckboxChange(item.value, $event, 'matrixManagers')">
                                                    <span class="ant-select-selection-item-content">{{ item.label + ' (' + item.value + ')' }}</span>
                                                    <span class="selected-icon" nz-icon [nzType]="'icons:check-bold'"></span>
                                                </label>
                                            </div>
                                        </nz-option>
                                    }
                                    @if (selectManager.getState('matrixManagers')?.isLoading) {
                                        <nz-option nzDisabled nzCustomContent>
                                            <span nz-icon nzType="loading" class="loading-icon"></span>
                                            Loading Data...
                                        </nz-option>
                                    }
                                </nz-select>
                                
                                <ng-template #matrixManagersDropdown let-menu>
                                    <div class="select-all-header">
                                        <div class="select-all-header-left">
                                            <label nz-checkbox 
                                                [nzChecked]="areAllOptionsSelected('matrixManagers')" 
                                                (nzCheckedChange)="onCheckAllOptions($event, 'matrixManagers')">
                                                {{ selectedMatrixManagers.length }} items selected
                                            </label>
                                        </div>
                                        <div class="select-all-header-right">
                                            <button nz-button nzType="link" (click)="deleteSelectedOptions('matrixManagers')">Delete</button>
                                        </div>
                                    </div>
                                    <nz-divider style="margin: 0"></nz-divider>
                                    {{ menu }}
                                </ng-template>
                                <ng-template #matrixManagersTagPlaceHolder let-selectedList>
                                    +{{ selectedList.length }}
                                </ng-template>
                                
                                <ng-template #errorTplMatrixManager let-control>
                                    <ng-container *ngIf="control.hasError('serverError')">{{ control.errors?.['message'] }}</ng-container>
                                </ng-template>
                            </nz-form-control>
                        </nz-form-item>
                    </nz-col>
                </nz-row>
                <nz-row [nzGutter]="16">
                    <nz-col [nzSpan]="12">
                        <nz-form-item>
                            <nz-form-label nzRequired nzNoColon>Department</nz-form-label>
                            <nz-form-control [nzErrorTip]="errorTplDepartment">
                                <nz-select formControlName="departmentCode" 
                                nzPlaceHolder="Select Department" 
                                nzShowSearch nzAllowClear
                                [nzDropdownClassName]="'add-person-select'"
                                [nzOptionHeightPx]="44"
                                (nzScrollToBottom)="onSelectScroll('departmentCode')"
                                (nzOnSearch)="onSelectSearch('departmentCode', $event)"
                                (ngModelChange)="onSelectChange('departmentCode')"
                                [nzLoading]="selectManager.getState('departmentCode')?.isLoading">
                                    @for (item of selectManager.getState('departmentCode')?.data; track item) {
                                        <nz-option [nzValue]="item.value"
                                        [nzLabel]="item.label + ' (' + item.value + ')'"
                                        nzCustomContent>
                                            <span class="ant-select-selection-item-content">{{ item.label + ' (' + item.value + ')' }}</span>
                                            <hrdx-icon icon="icon-check-bold" class="selected-icon" />
                                        </nz-option>
                                    }
                                    @if (selectManager.getState('departmentCode')?.isLoading) {
                                        <nz-option nzDisabled nzCustomContent>
                                            <span nz-icon nzType="loading" class="loading-icon"></span>
                                            Loading Data...
                                        </nz-option>
                                    }
                                </nz-select>
                                <ng-template #errorTplDepartment let-control>
                                    <ng-container *ngIf="control.hasError('required')">Cannot be empty</ng-container>
                                    <ng-container *ngIf="control.hasError('serverError')">{{ control.errors?.['message'] }}</ng-container>
                                  </ng-template>
                            </nz-form-control>
                        </nz-form-item>
                    </nz-col>
                    <nz-col [nzSpan]="12">
                        <nz-form-item>
                            <nz-form-label nzNoColon>Contract Number</nz-form-label>
                            <nz-form-control>
                                <input nz-input formControlName="contractNumber" />
                            </nz-form-control>
                        </nz-form-item>
                    </nz-col>
                </nz-row>
                <nz-row [nzGutter]="16">
                    <nz-col [nzSpan]="12">
                        <nz-form-item>
                            <nz-form-label nzRequired nzNoColon>Location</nz-form-label>
                            <nz-form-control [nzErrorTip]="errorTplLocation">
                                <nz-select formControlName="locationCode"
                                nzPlaceHolder="Select Location"
                                nzShowSearch nzAllowClear
                                [nzDropdownClassName]="'add-person-select'"
                                [nzOptionHeightPx]="44"
                                (nzScrollToBottom)="onSelectScroll('locationCode')"
                                (nzOnSearch)="onSelectSearch('locationCode', $event)"
                                (ngModelChange)="onSelectChange('locationCode')"
                                [nzLoading]="selectManager.getState('locationCode')?.isLoading">
                                    @for (item of selectManager.getState('locationCode')?.data; track item) {
                                        <nz-option [nzValue]="item.value" 
                                        [nzLabel]="item.label + ' (' + item.value + ')'"
                                        nzCustomContent>
                                            <span class="ant-select-selection-item-content">{{ item.label + ' (' + item.value + ')' }}</span>
                                            <hrdx-icon icon="icon-check-bold" class="selected-icon" />
                                        </nz-option>
                                    }
                                    @if (selectManager.getState('locationCode')?.isLoading) {
                                        <nz-option nzDisabled nzCustomContent>
                                            <span nz-icon nzType="loading" class="loading-icon"></span>
                                            Loading Data...
                                        </nz-option>
                                    }
                                </nz-select>
                                <ng-template #errorTplLocation let-control>
                                    <ng-container *ngIf="control.hasError('required')">Cannot be empty</ng-container>
                                    <ng-container *ngIf="control.hasError('serverError')">{{ control.errors?.['message'] }}</ng-container>
                                  </ng-template>
                            </nz-form-control>
                        </nz-form-item>
                    </nz-col>
                    <nz-col [nzSpan]="12">
                        <nz-form-item>
                            <nz-form-label nzNoColon>Contract Type</nz-form-label>
                            <nz-form-control>
                                <input nz-input formControlName="contractType"/>
                            </nz-form-control>
                        </nz-form-item>
                    </nz-col>
                </nz-row>
                <nz-row [nzGutter]="16">
                    <nz-col [nzSpan]="12">
                        <nz-form-item>
                            <nz-form-label nzNoColon>Region</nz-form-label>
                            <nz-form-control [nzErrorTip]="errorTplRegion">
                                <nz-select formControlName="regionCode" 
                                nzPlaceHolder="Select Region" 
                                nzShowSearch nzAllowClear
                                [nzDropdownClassName]="'add-person-select'"
                                [nzOptionHeightPx]="44">
                                    @for (o of regionList; track o) {
                                        <nz-option [nzValue]="o.value" 
                                        [nzLabel]="o.label + ' (' + o.value + ')'"
                                        nzCustomContent>
                                            <span class="ant-select-selection-item-content">{{ o.label + ' (' + o.value + ')' }}</span>
                                            <hrdx-icon icon="icon-check-bold" class="selected-icon" />
                                        </nz-option>
                                    }
                                </nz-select>
                                <ng-template #errorTplRegion let-control>
                                    <ng-container *ngIf="control.hasError('serverError')">{{ control.errors?.['message'] }}</ng-container>
                                  </ng-template>
                            </nz-form-control>
                        </nz-form-item>
                    </nz-col>
                    <nz-col [nzSpan]="12">
                        <nz-form-item>
                            <nz-form-label nzRequired nzNoColon>Full/Part</nz-form-label>
                            <nz-form-control nzErrorTip="Cannot be empty">
                                <nz-select formControlName="fullPartCode" 
                                nzPlaceHolder="Select Full/Part" 
                                nzShowSearch nzAllowClear
                                [nzDropdownClassName]="'add-person-select'"
                                [nzOptionHeightPx]="44">
                                    @for (option of fullPartList; track option) {
                                        <nz-option [nzValue]="option.value" [nzLabel]="option.label" nzCustomContent>
                                            <span class="ant-select-selection-item-content">{{ option.label }}</span>
                                            <hrdx-icon icon="icon-check-bold" class="selected-icon" />
                                        </nz-option>
                                    }
                                </nz-select>
                            </nz-form-control>
                        </nz-form-item>
                    </nz-col>
                </nz-row>
                <nz-row [nzGutter]="16">
                    <nz-col [nzSpan]="12">
                        <nz-form-item>
                            <nz-form-label nzNoColon>Department Entry Date</nz-form-label>
                            <nz-form-control>
                                <nz-date-picker formControlName="departmentEntryDate" nzPlaceHolder="DD/MM/YYYY"
                                [nzSuffixIcon]="nzSuffixIcon"
                                datePickerBlur
                                (datePickerBlur)="selectDate($event.event, 'departmentEntryDate')"
                                    nzFormat="dd/MM/yyyy" [nzDisabledDate]="disableDateBeforeEffectiveDate"></nz-date-picker>
                            </nz-form-control>
                        </nz-form-item>
                    </nz-col>
                    <nz-col [nzSpan]="12">
                        <nz-form-item>
                            <nz-form-label nzNoColon>Group First Start Date</nz-form-label>
                            <nz-form-control>
                                <nz-date-picker formControlName="groupFirstStartDate" nzFormat="dd/MM/yyyy" 
                                [nzSuffixIcon]="nzSuffixIcon"
                                (keyup.enter)="selectDate($event, 'groupFirstStartDate')"
                                    nzPlaceHolder="DD/MM/YYYY"></nz-date-picker>
                            </nz-form-control>
                        </nz-form-item>
                    </nz-col>
                </nz-row>
                <nz-row [nzGutter]="16">
                    <nz-col [nzSpan]="12">
                        <nz-form-item>
                            <nz-form-label nzRequired nzNoColon>Job</nz-form-label>
                            <nz-form-control [nzErrorTip]="errorTplJob">
                                <nz-select formControlName="jobCode" 
                                nzPlaceHolder="Select Job" 
                                nzShowSearch nzAllowClear
                                [nzDropdownClassName]="'add-person-select'"
                                [nzOptionHeightPx]="44"
                                (nzOpenChange)="onSelectOpenChange($event, 'jobCode')"
                                (nzOnSearch)="onNZSelectSearch($event, 'jobCode')">
                                    @if (!isNZSelectLoading) {
                                        @for (o of jobList; track o) {
                                            <nz-option [nzValue]="o.value" 
                                            [nzLabel]="o.label + ' (' + o.value + ')'"
                                            nzCustomContent>
                                                <span class="ant-select-selection-item-content">{{ o.label + ' (' + o.value + ')' }}</span>
                                                <hrdx-icon icon="icon-check-bold" class="selected-icon" />
                                            </nz-option>
                                        }
                                    } @else {
                                        <nz-option nzDisabled nzCustomContent>
                                            <span nz-icon nzType="loading" class="loading-icon"></span>
                                            Loading Data...
                                        </nz-option>
                                    }
                                </nz-select>
                                <ng-template #errorTplJob let-control>
                                    <ng-container *ngIf="control.hasError('required')">Cannot be empty</ng-container>
                                    <ng-container *ngIf="control.hasError('serverError')">{{ control.errors?.['message'] }}</ng-container>
                                  </ng-template>
                            </nz-form-control>
                        </nz-form-item>
                    </nz-col>
                    <nz-col [nzSpan]="12">
                        <nz-form-item>
                            <nz-form-label nzNoColon>Job Seniority</nz-form-label>
                            <nz-form-control>
                                <input nz-input formControlName="jobSeniorityDisplay" placeholder="0" />
                            </nz-form-control>
                        </nz-form-item>
                    </nz-col>
                </nz-row>
                <nz-row [nzGutter]="16">
                    <nz-col [nzSpan]="12">
                        <nz-form-item>
                            <nz-form-label nzNoColon>Business Title</nz-form-label>
                            <nz-form-control [nzErrorTip]="errorTplBusinessTitle">
                                <nz-select formControlName="businessTitleCode"
                                nzPlaceHolder="Select Business Title"
                                nzShowSearch nzAllowClear
                                [nzDropdownClassName]="'add-person-select'"
                                [nzOptionHeightPx]="44"
                                (nzScrollToBottom)="onSelectScroll('businessTitleCode')"
                                (nzOnSearch)="onSelectSearch('businessTitleCode', $event)"
                                (ngModelChange)="onSelectChange('businessTitleCode')"
                                [nzLoading]="selectManager.getState('businessTitleCode')?.isLoading">
                                    @for (item of selectManager.getState('businessTitleCode')?.data; track item) {
                                        <nz-option [nzValue]="item.value" 
                                        [nzLabel]="item.label + ' (' + item.value + ')'"
                                        nzCustomContent>
                                            <span class="ant-select-selection-item-content">{{ item.label + ' (' + item.value + ')' }}</span>
                                            <hrdx-icon icon="icon-check-bold" class="selected-icon" />
                                        </nz-option>
                                    }
                                    @if (selectManager.getState('businessTitleCode')?.isLoading) {
                                        <nz-option nzDisabled nzCustomContent>
                                            <span nz-icon nzType="loading" class="loading-icon"></span>
                                            Loading Data...
                                        </nz-option>
                                    }
                                </nz-select>
                                <ng-template #errorTplBusinessTitle let-control>
                                    <ng-container *ngIf="control.hasError('serverError')">{{ control.errors?.['message'] }}</ng-container>
                                  </ng-template>
                            </nz-form-control>
                        </nz-form-item>
                    </nz-col>
                    <nz-col [nzSpan]="12">
                        <nz-form-item>
                            <nz-form-label nzNoColon>Time Zone</nz-form-label>
                            <nz-form-control>
                                <nz-select formControlName="timeZoneCode" 
                                nzPlaceHolder="Select Time Zone" 
                                nzShowSearch nzAllowClear
                                [nzDropdownClassName]="'add-person-select'"
                                [nzOptionHeightPx]="44">
                                    @for (option of timeZoneList; track option) {
                                        <nz-option [nzValue]="option.value" [nzLabel]="option.label" nzCustomContent>
                                            <span class="ant-select-selection-item-content">{{ option.label }}</span>
                                            <hrdx-icon icon="icon-check-bold" class="selected-icon" />
                                        </nz-option>
                                    }
                                </nz-select>
                            </nz-form-control>
                        </nz-form-item>
                    </nz-col>
                </nz-row>
                <nz-row [nzGutter]="16">
                    <nz-col [nzSpan]="12">
                        <nz-form-item>
                            <nz-form-label nzNoColon>Career Stream</nz-form-label>
                            <nz-form-control [nzErrorTip]="errorTplCareerStream">
                                <nz-select formControlName="careerStreamCode"
                                nzPlaceHolder="Select Career Stream"
                                nzShowSearch nzAllowClear
                                [nzDropdownClassName]="'add-person-select'"
                                [nzOptionHeightPx]="44"
                                (nzScrollToBottom)="onSelectScroll('careerStreamCode')"
                                (nzOnSearch)="onSelectSearch('careerStreamCode', $event)"
                                (ngModelChange)="onSelectChange('careerStreamCode')"
                                [nzLoading]="selectManager.getState('careerStreamCode')?.isLoading">
                                    @for (item of selectManager.getState('careerStreamCode')?.data; track item) {
                                        <nz-option [nzValue]="item.value" 
                                        [nzLabel]="item.label + ' (' + item.value + ')'"
                                        nzCustomContent>
                                            <span class="ant-select-selection-item-content">{{ item.label + ' (' + item.value + ')' }}</span>
                                            <hrdx-icon icon="icon-check-bold" class="selected-icon" />
                                        </nz-option>
                                    }
                                    @if (selectManager.getState('careerStreamCode')?.isLoading) {
                                        <nz-option nzDisabled nzCustomContent>
                                            <span nz-icon nzType="loading" class="loading-icon"></span>
                                            Loading Data...
                                        </nz-option>
                                    }
                                </nz-select>
                                <ng-template #errorTplCareerStream let-control>
                                    <ng-container *ngIf="control.hasError('serverError')">{{ control.errors?.['message'] }}</ng-container>
                                  </ng-template>
                            </nz-form-control>
                        </nz-form-item>
                    </nz-col>
                    <nz-col [nzSpan]="12">
                        <nz-form-item>
                            <nz-form-label nzNoColon>Career Band</nz-form-label>
                            <nz-form-control [nzErrorTip]="errorTplCareerBand">
                                <nz-select formControlName="careerBandCode"
                                nzPlaceHolder="Select Career Band"
                                nzShowSearch nzAllowClear
                                [nzDropdownClassName]="'add-person-select'"
                                [nzOptionHeightPx]="44"
                                (nzScrollToBottom)="onSelectScroll('careerBandCode')"
                                (nzOnSearch)="onSelectSearch('careerBandCode', $event)"
                                (ngModelChange)="onSelectChange('careerBandCode')"
                                [nzLoading]="selectManager.getState('careerBandCode')?.isLoading">
                                    @for (item of selectManager.getState('careerBandCode')?.data; track item) {
                                        <nz-option [nzValue]="item.value" 
                                        [nzLabel]="item.label + ' (' + item.value + ')'"
                                        nzCustomContent>
                                            <span class="ant-select-selection-item-content">{{ item.label + ' (' + item.value + ')' }}</span>
                                            <hrdx-icon icon="icon-check-bold" class="selected-icon" />
                                        </nz-option>
                                    }
                                    @if (selectManager.getState('careerBandCode')?.isLoading) {
                                        <nz-option nzDisabled nzCustomContent>
                                            <span nz-icon nzType="loading" class="loading-icon"></span>
                                            Loading Data...
                                        </nz-option>
                                    }
                                </nz-select>
                                <ng-template #errorTplCareerBand let-control>
                                    <ng-container *ngIf="control.hasError('serverError')">{{ control.errors?.['message'] }}</ng-container>
                                  </ng-template>
                            </nz-form-control>
                        </nz-form-item>
                    </nz-col>
                </nz-row>
                <nz-row [nzGutter]="16">
                    <nz-col [nzSpan]="12">
                        <nz-form-item>
                            <nz-form-label nzRequired nzNoColon>FTE</nz-form-label>
                            <nz-form-control nzErrorTip="Cannot be empty">
                                <nz-input-number
                                    formControlName="fte"
                                    [nzMin]="0"
                                    [nzMax]="2"
                                    [nzStep]="0.00001"
                                    [nzPrecision]="5"
                                    class="full-width"
                                    [nzFormatter]="fteFormatter"
                                    [nzParser]="fteParser"
                                    [nzPlaceHolder]="'FTE'"
                                    ></nz-input-number>
                            </nz-form-control>
                        </nz-form-item>
                    </nz-col>
                    <nz-col [nzSpan]="12">
                        <nz-form-item>
                            <nz-form-label nzRequired nzNoColon>Emp Level</nz-form-label>
                            <nz-form-control [nzErrorTip]="errorTplEmpLevel">
                                <nz-select formControlName="empLevelCode" 
                                nzPlaceHolder="Select Emp Level" 
                                nzShowSearch nzAllowClear
                                [nzDropdownClassName]="'add-person-select'"
                                [nzOptionHeightPx]="44">
                                    @for (o of empLevelList; track o) {
                                        <nz-option [nzValue]="o.value" 
                                        [nzLabel]="o.label + ' (' + o.value + ')'"
                                        nzCustomContent>
                                            <span class="ant-select-selection-item-content">{{ o.label + ' (' + o.value + ')' }}</span>
                                            <hrdx-icon icon="icon-check-bold" class="selected-icon" />
                                        </nz-option>
                                    }
                                </nz-select>
                                <ng-template #errorTplEmpLevel let-control>
                                    <ng-container *ngIf="control.hasError('required')">Cannot be empty</ng-container>
                                    <ng-container *ngIf="control.hasError('serverError')">{{ control.errors?.['message'] }}</ng-container>
                                  </ng-template>
                            </nz-form-control>
                        </nz-form-item>
                    </nz-col>
                </nz-row>
                <nz-form-item>
                    <nz-form-label nzNoColon>Attachment</nz-form-label>
                    <nz-form-control>
                        <nz-upload
                            nzType="drag"
                            [nzMultiple]="true"
                            [nzAccept]="acceptFileTypes"
                            [nzLimit]="5"
                            [nzSize]="acceptFileSize"
                            [nzFileList]="fileList"
                            [nzFileListRender]="fileListRender"
                            [nzBeforeUpload]="beforeUpload"
                            nzListType="picture"
                            class="upload-container"
                            [ngClass]="{'error': errorMsg}"
                            (nzChange)="handleUpload($event)">
                            <ng-container *ngIf="!errorMsg">
                                <p class="ant-upload-drag-icon">
                                    <i class="fa-regular icon-illustration icon"></i>
                                </p>
                                <p class="ant-upload-text">Upload files or drop them here</p>
                                <p class="ant-upload-hint">PDF, DOC, DOCX, XLS, XLSX, CSV, JPEG, PNG only (Max 5MB)</p>
                            </ng-container>
                            <ng-container *ngIf="errorMsg">
                                <div>
                                    <div class="error-icon">
                                        <i class="fa-regular icon-warning"></i>
                                    </div>
                                    <div class="error-message">
                                        {{ errorMsg }}
                                    </div>
                                </div>
                            </ng-container>
                        </nz-upload>
                    </nz-form-control>
                </nz-form-item>
            </div>


            <div id="employment" class="section">
                <h3>Employment</h3>
                <nz-row [nzGutter]="16">
                    <nz-col [nzSpan]="12">
                        <nz-form-item>
                            <nz-form-label nzRequired nzNoColon>Group Original Start Date</nz-form-label>
                            <nz-form-control nzErrorTip="Cannot be empty">
                                <nz-date-picker formControlName="groupOriginalStartDate" nzPlaceHolder="DD/MM/YYYY"
                                [nzSuffixIcon]="nzSuffixIcon"
                                datePickerBlur
                                (datePickerBlur)="selectDate($event.event, 'groupOriginalStartDate')"
                                    nzFormat="dd/MM/yyyy"></nz-date-picker>
                            </nz-form-control>
                        </nz-form-item>
                    </nz-col>
                    <nz-col [nzSpan]="12">
                        <nz-form-item>
                            <nz-form-label nzNoColon>Group First Start Date</nz-form-label>
                            <nz-form-control>
                                <nz-date-picker formControlName="employeeGroupFirstStartDate" nzPlaceHolder="DD/MM/YYYY"
                                [nzSuffixIcon]="nzSuffixIcon"
                                (keyup.enter)="selectDate($event, 'employeeGroupFirstStartDate')"
                                    nzFormat="dd/MM/yyyy"></nz-date-picker>
                            </nz-form-control>
                        </nz-form-item>
                    </nz-col>
                </nz-row>
                <nz-row [nzGutter]="16">
                    <nz-col [nzSpan]="12">
                        <nz-form-item>
                            <nz-form-label nzNoColon>Group Last Start Date</nz-form-label>
                            <nz-form-control>
                                <nz-date-picker formControlName="groupLastStartDate" nzPlaceHolder="DD/MM/YYYY"
                                [nzSuffixIcon]="nzSuffixIcon"
                                (keyup.enter)="selectDate($event, 'groupLastStartDate')"
                                    nzFormat="dd/MM/yyyy"></nz-date-picker>
                            </nz-form-control>
                        </nz-form-item>
                    </nz-col>
                    <nz-col [nzSpan]="12">
                        <nz-form-item>
                            <nz-form-label nzNoColon>Group Last Terminate</nz-form-label>
                            <nz-form-control>
                                <input nz-input formControlName="groupLastTerminate" placeholder="" />
                            </nz-form-control>
                        </nz-form-item>
                    </nz-col>
                </nz-row>
                <nz-row [nzGutter]="16">
                    <nz-col [nzSpan]="12">
                        <nz-form-item>
                            <nz-form-label nzRequired nzNoColon>OIR Original Start Date</nz-form-label>
                            <nz-form-control nzErrorTip="Cannot be empty">
                                <nz-date-picker formControlName="oirOriginalStartDate" nzPlaceHolder="DD/MM/YYYY"
                                [nzSuffixIcon]="nzSuffixIcon"
                                datePickerBlur
                                (datePickerBlur)="selectDate($event.event, 'oirOriginalStartDate')"
                                    nzFormat="dd/MM/yyyy"></nz-date-picker>
                            </nz-form-control>
                        </nz-form-item>
                    </nz-col>
                    <nz-col [nzSpan]="12">
                        <nz-form-item>
                            <nz-form-label nzNoColon>OIR First Start Date</nz-form-label>
                            <nz-form-control>
                                <nz-date-picker formControlName="oirFirstStartDate" nzPlaceHolder="DD/MM/YYYY"
                                [nzSuffixIcon]="nzSuffixIcon"
                                (keyup.enter)="selectDate($event, 'oirFirstStartDate')"
                                    nzFormat="dd/MM/yyyy"></nz-date-picker>
                            </nz-form-control>
                        </nz-form-item>
                    </nz-col>
                </nz-row>
                <nz-row [nzGutter]="16">
                    <nz-col [nzSpan]="12">
                        <nz-form-item>
                            <nz-form-label nzNoColon>OIR Last Start Date</nz-form-label>
                            <nz-form-control>
                                <nz-date-picker formControlName="oirLastStartDate" nzPlaceHolder="DD/MM/YYYY"
                                [nzSuffixIcon]="nzSuffixIcon"
                                (keyup.enter)="selectDate($event, 'oirLastStartDate')"
                                    nzFormat="dd/MM/yyyy"></nz-date-picker>
                            </nz-form-control>
                        </nz-form-item>
                    </nz-col>
                    <nz-col [nzSpan]="12">
                        <nz-form-item>
                            <nz-form-label nzNoColon>OIR Last Terminate</nz-form-label>
                            <nz-form-control>
                                <input nz-input formControlName="oirLastTerminate" placeholder="" />
                            </nz-form-control>
                        </nz-form-item>
                    </nz-col>
                </nz-row>
                <nz-row [nzGutter]="16">
                    <nz-col [nzSpan]="12">
                        <nz-form-item>
                            <nz-form-label nzNoColon>Group Seniority</nz-form-label>
                            <nz-form-control>
                                <input nz-input formControlName="groupSeniority" />
                            </nz-form-control>
                        </nz-form-item>
                    </nz-col>
                    <nz-col [nzSpan]="12">
                        <nz-form-item>
                            <nz-form-label nzNoColon>Organizational Instance Seniority</nz-form-label>
                            <nz-form-control>
                                <input nz-input formControlName="organizationalInstanceSeniority" />
                            </nz-form-control>
                        </nz-form-item>
                    </nz-col>
                </nz-row>
                <nz-row [nzGutter]="16">
                    <nz-col [nzSpan]="12">
                        <nz-form-item>
                            <nz-form-label nzNoColon>External Experience</nz-form-label>
                            <nz-form-control>
                                <input nz-input formControlName="externalExperience" />
                            </nz-form-control>
                        </nz-form-item>
                    </nz-col>
                </nz-row>
                <nz-form-item>
                    <nz-form-label nzNoColon>Note</nz-form-label>
                    <nz-form-control>
                        <nz-textarea-count [nzMaxCharacterCount]="1000">
                            <textarea [maxlength]="1000" nz-input formControlName="employmentNote" placeholder="Enter Note" [nzAutosize]="{ minRows: 3, maxRows: 5 }"></textarea>
                        </nz-textarea-count>
                    </nz-form-control>
                </nz-form-item>
            </div>


        </form>
    </div>
</div>
<ng-template #jobDataFooter>
    <div class="dialog-footer ">
        <div class="equal-width-buttons">
            <hrdx-button title="Cancel" (clicked)="cancel()" [type]="'tertiary'" [size]="'default'">
            </hrdx-button>
            <hrdx-button [isLoading]="isFormValidating" [type]="'primary'" title="Apply"  (clicked)="apply()" [size]="'default'">
            </hrdx-button>
        </div>
    </div>

</ng-template>


<ng-template #jobDataModalTitle>
    <app-employeeid-title [personId]="personId" [title]="titleModal" [fullName]="fullName"></app-employeeid-title>
  </ng-template>

  <ng-template #nzSuffixIcon>
    <span
      nz-icon
      nzType="icons:calendar-blank-bold"
      nzTheme="outline"
      class="suffixIcon"
      [ngClass]="''"
    ></span>
  </ng-template>

  <ng-template #yearsSuffix>
    <span>Years</span>
  </ng-template>

  <ng-template #monthsSuffix>
    <span>Months</span>
  </ng-template>

  <ng-template #daysSuffix>
    <span>Days</span>
  </ng-template>

  <ng-template #fileListRender let-list>
    <div class="file-list">
        @for (item of list; track item) {
            @if (item.status !== 'error') {
                <div class="file" [class.error]="item.status === 'error'">
                    <ng-container [ngSwitch]="getFileType(item.name)">
                        <hrdx-icon *ngSwitchCase="'excel'" [fixIon]="'fix-TypeXLS'" />
                        <hrdx-icon *ngSwitchCase="'csv'" [fixIon]="'fix-TypeXLS'" />
                        <hrdx-icon *ngSwitchCase="'pdf'" [fixIon]="'fix-TypePDF'" />
                        <hrdx-icon *ngSwitchCase="'docx'" [fixIon]="'fix-TypeDOC'" />
                        <hrdx-icon *ngSwitchCase="'doc'" [fixIon]="'fix-TypeDOC'" />
                        <hrdx-icon *ngSwitchDefault [name]="'paperclip'" [size]="'small'" />
                    </ng-container>
                    <div class="file-list-name">
                        <div class="file-name">
                            {{ item.name }}
                        </div>
                        @if (item.status === 'error') {
                            <div class="file-error">
                                {{ item.response }}
                            </div>
                        }
                    </div>
                    <div class="file-list-action">
                        <div (click)="removeFile(list, item)">
                            <hrdx-icon [name]="'icon-trash'" [size]="'small'" />
                        </div>
                    </div>
                </div>
            }
        }
    </div>
</ng-template>