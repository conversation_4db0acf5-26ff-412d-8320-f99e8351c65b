import { AfterViewInit, ChangeDetectorRef, Component, ElementRef, inject, On<PERSON><PERSON>roy, OnInit, TemplateRef, ViewChild, ViewChildren, QueryList } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormArray, FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzSelectComponent, NzSelectModule } from 'ng-zorro-antd/select';
import { NzAnchorModule } from 'ng-zorro-antd/anchor';
import { NzGridModule } from 'ng-zorro-antd/grid';
import { NzUploadChangeParam, NzUploadFile, NzUploadModule } from 'ng-zorro-antd/upload';
import { NzRadioModule } from 'ng-zorro-antd/radio';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { ButtonComponent, DatePickerComponent, IconComponent, ModalComponent, SelectComponent, ToastMessageComponent } from '@hrdx/hrdx-design';
import { HrFsBp001Service } from '../services/hr.fs.bp.001.service';
import { PicklistConstants } from '../constants/picklist.constants';
import { NZ_MODAL_DATA, NzModalRef } from 'ng-zorro-antd/modal';
import { UploadComponent } from '@hrdx/hrdx-design';
import { NzInputNumberModule } from 'ng-zorro-antd/input-number';
import * as moment from 'moment';
import { QueryFilter } from '@nestjsx/crud-request';
import { FormDependencyManager } from '../services/formdependency.manager';
import { EmployeeIdTitleComponent } from '../employeeid-title.component';
import { SelectManager } from '../services/select-manager.service';
import { Observable, take } from 'rxjs';
import { ERROR_MESSAGE } from '../constants/constants';
import { DatePickerScrollService } from '../services/date-picker-scroll.service';
import { NzDatePickerComponent } from 'ng-zorro-antd/date-picker';
import { DatePickerBlurDirective } from '../directive/datepickerblur.directive';
@Component({
  selector: 'lib-job-data',
  standalone: true,
  imports: [
    CommonModule,
    NzFormModule,
    NzDatePickerModule,
    NzAnchorModule,
    NzInputModule,
    NzButtonModule,
    NzSelectModule,
    NzIconModule,
    ReactiveFormsModule,
    NzGridModule,
    NzRadioModule,
    NzUploadModule,
    NzCheckboxModule,
    NzDividerModule,
    ButtonComponent,
    SelectComponent,
    DatePickerComponent,
    UploadComponent,
    NzInputNumberModule,
    EmployeeIdTitleComponent,
    IconComponent,
    DatePickerBlurDirective
  ],
  templateUrl: './job-data.component.html',
  styleUrl: './job-data.component.less',
  providers: [HrFsBp001Service, SelectManager, ToastMessageComponent, ModalComponent]
})
export class JobDataComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('jobDataFooter', { static: true }) modalFooter!: TemplateRef<{}>;
  @ViewChild('jobDataModalTitle', { static: true }) jobDataModalTitle!: TemplateRef<any>;
  @ViewChildren(NzDatePickerComponent) datePickers!: QueryList<NzDatePickerComponent>;

  jobDataForm!: FormGroup;
  currentStep = 0;
  hrFsBp001Service: HrFsBp001Service = inject(HrFsBp001Service);

  modalRef: NzModalRef = inject(NzModalRef);
  readonly nzModalData: any = inject(NZ_MODAL_DATA);

  // employee group and sub group name
  employeeGroupName: string = '';
  employeeSubGroupName: string = '';
  employeeSubGroupList: any[] = [];

  // a loading flag when select search
  isNZSelectLoading = false;

  // accept file types to upload
  acceptFileTypes: string[] = ['.pdf', '.docx', '.doc', '.jpeg', '.png', '.xlsx', '.xls', '.csv'];
  // accept file size
  acceptFileSize = 5 * 1024 * 1024;

  // selected company 
  selectedCompany: any;

  // for handling field dependencies
  private dependencyManager!: FormDependencyManager;

  // loading states
  isPositionLoading = false;

  // person id
  personId: string = '';
  readonly titleModal: string = 'Job Data';
  fullName: string = '';
  
  // Array to store selected matrix report positions
  selectedMatrixReportPositions: string[] = [];
  // Array to store selected matrix managers
  selectedMatrixManagers: string[] = [];

  employeeGroup: string = '';
  toast: ToastMessageComponent = inject(ToastMessageComponent);

  modalComponent: ModalComponent = inject(ModalComponent);

  private isEffectiveDateChanging = false;

  // Store selected names for multi-select 
  selectedMatrixReportPositionNames: string[] = [];
  // Store selected names for matrix managers
  selectedMatrixManagerNames: string[] = [];

  constructor(private fb: FormBuilder, private elementRef: ElementRef, private datePickerScrollService: DatePickerScrollService) { }

  // Generic method to check if a specific option is selected
  isOptionSelected(value: string, fieldName: string): boolean {
    if (fieldName === 'matrixReportPositionCode') {
      return this.selectedMatrixReportPositions.includes(value);
    } else if (fieldName === 'matrixManagers') {
      return this.selectedMatrixManagers.includes(value);
    }
    return false;
  }

  // Generic method to check if all options are selected
  areAllOptionsSelected(fieldName: string): boolean {
    const allOptions = this.selectManager.getState(fieldName)?.data || [];
    if (fieldName === 'matrixReportPositionCode') {
      return allOptions.length > 0 && 
             allOptions.every(item => this.selectedMatrixReportPositions.includes(item.value));
    } else if (fieldName === 'matrixManagers') {
      return allOptions.length > 0 && 
             allOptions.every(item => this.selectedMatrixManagers.includes(item.value));
    }
    return false;
  }

  // Generic method to handle check all options
  onCheckAllOptions(checked: boolean, fieldName: string): void {
    const allOptions = this.selectManager.getState(fieldName)?.data || [];
    
    if (fieldName === 'matrixReportPositionCode') {
      if (checked) {
        // Select all positions
        this.selectedMatrixReportPositions = allOptions.map(item => item.value);
      } else {
        // Deselect all positions
        this.selectedMatrixReportPositions = [];
      }
      
      // Update form control value
      this.jobDataForm.get('matrixReportPositionCode')?.setValue([...this.selectedMatrixReportPositions]);
    } else if (fieldName === 'matrixManagers') {
      if (checked) {
        // Select all managers
        this.selectedMatrixManagers = allOptions.map(item => item.value);
      } else {
        // Deselect all managers
        this.selectedMatrixManagers = [];
      }
      
      // Update form control value
      this.jobDataForm.get('matrixManagers')?.setValue([...this.selectedMatrixManagers]);
    }
  }

  // Generic method to handle selection change
  onMultiSelectChange(values: string[], fieldName: string): void {
    if (fieldName === 'matrixReportPositionCode') {
      this.selectedMatrixReportPositions = values || [];
      this.updateSelectedNames('matrixReportPositionCode', 'selectedMatrixReportPositionNames');
    } else if (fieldName === 'matrixManagers') {
      this.selectedMatrixManagers = values || [];
      this.updateSelectedNames('matrixManagers', 'selectedMatrixManagerNames');
    }
  }

  // Generic method to delete all selected options
  deleteSelectedOptions(fieldName: string): void {
    if (fieldName === 'matrixReportPositionCode') {
      this.selectedMatrixReportPositions = [];
      this.jobDataForm.get('matrixReportPositionCode')?.setValue([]);
    } else if (fieldName === 'matrixManagers') {
      this.selectedMatrixManagers = [];
      this.jobDataForm.get('matrixManagers')?.setValue([]);
    }
  }

  // Generic method to handle checkbox change
  onOptionCheckboxChange(value: string, checked: boolean, fieldName: string): void {
    let currentValues: string[] = [];
    let newValues: string[];
    
    if (fieldName === 'matrixReportPositionCode') {
      currentValues = this.jobDataForm.get('matrixReportPositionCode')?.value || [];
    } else if (fieldName === 'matrixManagers') {
      currentValues = this.jobDataForm.get('matrixManagers')?.value || [];
    }
    
    if (checked) {
      // Add the value if it's not already included
      if (!currentValues.includes(value)) {
        newValues = [...currentValues, value];
      } else {
        newValues = [...currentValues]; // No change needed
      }
    } else {
      // Remove the value
      newValues = currentValues.filter((v: string) => v !== value);
    }
    
    // Update form control with new values
    this.jobDataForm.get(fieldName)?.setValue(newValues);
    
    // Update tracking array
    if (fieldName === 'matrixReportPositionCode') {
      this.selectedMatrixReportPositions = [...newValues];
    } else if (fieldName === 'matrixManagers') {
      this.selectedMatrixManagers = [...newValues];
    }
  }

  // Generic method to handle option click
  onOptionClick(value: string, fieldName: string): void {
    let currentValues: string[] = [];
    let newValues: string[];
    
    if (fieldName === 'matrixReportPositionCode') {
      currentValues = this.jobDataForm.get('matrixReportPositionCode')?.value || [];
    } else if (fieldName === 'matrixManagers') {
      currentValues = this.jobDataForm.get('matrixManagers')?.value || [];
    }
    
    if (currentValues.includes(value)) {
      // Remove the value if already selected
      newValues = currentValues.filter((v: string) => v !== value);
    } else {
      // Add the value if not selected
      newValues = [...currentValues, value];
    }
    
    // Update form control value
    this.jobDataForm.get(fieldName)?.setValue(newValues);
    
    // Update tracking array
    if (fieldName === 'matrixReportPositionCode') {
      this.selectedMatrixReportPositions = [...newValues];
    } else if (fieldName === 'matrixManagers') {
      this.selectedMatrixManagers = [...newValues];
    }
    
    // Force change detection
    setTimeout(() => {}, 0);
  }
  
  // Keep old methods for backward compatibility
  isMatrixReportPositionSelected(value: string): boolean {
    return this.isOptionSelected(value, 'matrixReportPositionCode');
  }

  isAllMatrixReportPositionsSelected(): boolean {
    return this.areAllOptionsSelected('matrixReportPositionCode');
  }

  onCheckAllMatrixReportPositions(checked: boolean): void {
    this.onCheckAllOptions(checked, 'matrixReportPositionCode');
  }

  onMatrixReportPositionChange(values: string[]): void {
    this.onMultiSelectChange(values, 'matrixReportPositionCode');
  }

  deleteSelectedMatrixReportPositions(): void {
    this.deleteSelectedOptions('matrixReportPositionCode');
  }

  onMatrixCheckboxChange(value: string, checked: boolean): void {
    this.onOptionCheckboxChange(value, checked, 'matrixReportPositionCode');
  }

  onClickMatrixReportOption(value: string): void {
    this.onOptionClick(value, 'matrixReportPositionCode');
  }

  ngOnInit(): void {
    // register selects first
    this.registerSelects();

    // get person id from modal data
    this.personId = this.nzModalData.personId ?? 'NEW';
    this.fullName = this.nzModalData.fullName;
    // update the modal title
    this.modalRef.updateConfig({
      nzTitle: this.jobDataModalTitle
    });

    this.jobDataForm = this.fb.group({
      organizationalInstanceRcd: [{ value: '0', disabled: true }], // not require but disable
      employeeRecordNumber: [{ value: '0', disabled: true }], // not require but disable

      // Action Section
      effectiveDateFrom: [null, [Validators.required]],
      actionCode: [null, [Validators.required]],
      actionReasonCode: [null, [Validators.required]],
      effectiveSequence: [{ value: 0, disabled: true }, [Validators.required]],
      hrStatusCode: [[Validators.required]],
      prStatusCode: [null, [Validators.required]],
      employeeGroupCode: [null],
      jobIndicatorCode: [null, [Validators.required]],
      isManagerCode: [null, [Validators.required]],
      employeeSubGroupCode: [null, [Validators.required]],
      levelDecisionCode: [null],
      decisionNumber: [null],
      signDate: [null],
      expectedEndDate: [null],
      note: [null],

      // Assignment Section
      positionCode: [null],
      companyCode: [null],
      legalEntityCode: [null, [Validators.required]],
      businessUnitCode: [null],
      divisionCode: [null],
      departmentCode: [null, [Validators.required]],
      locationCode: [null, [Validators.required]],
      regionCode: [null],
      departmentEntryDate: [null],
      jobCode: [null, [Validators.required]],
      businessTitleCode: [null],
      careerStreamCode: [null],
      careerBandCode: [null],
      fte: [1.00000, [Validators.required, Validators.min(0), Validators.max(2)]],
      empLevelCode: [null, [Validators.required]],
      costCenterCode: [null],
      reportPosition: [null],
      supervisor: [null],
      matrixReportPositionCode: [[]],
      matrixManagers: [[]],
      contractNumber: [{ value: null, disabled: true }],
      contractType: [{ value: null, disabled: true }],
      fullPartCode: [null, [Validators.required]],
      groupFirstStartDate: [{ value: null, disabled: true }],
      jobSeniority: [{ value: null}],
      jobSeniorityDisplay: [{ value: null, disabled: true }],
      timeZoneCode: [null],
      files: [null],

      // Employment Section
      groupOriginalStartDate: [null, [Validators.required]],
      employeeGroupFirstStartDate: [{ value: null, disabled: true }],
      groupLastStartDate: [{ value: null, disabled: true }],
      groupLastTerminate: [{ value: null, disabled: true }],
      oirOriginalStartDate: [null, [Validators.required]],
      oirFirstStartDate: [{ value: null, disabled: true }],
      oirLastStartDate: [{ value: null, disabled: true }],
      oirLastTerminate: [{ value: null, disabled: true }],

      groupSeniorityYears: [{ value: 0, disabled: true }],
      groupSeniorityMonths: [{ value: 0, disabled: true }],
      groupSeniorityDays: [{ value: 0, disabled: true }],
      groupSeniority: [{ value: null, disabled: true }],

      organizationalInstanceSeniorityYears: [{ value: 0, disabled: true }],
      organizationalInstanceSeniorityMonths: [{ value: 0, disabled: true }],
      organizationalInstanceSeniorityDays: [{ value: 0, disabled: true }],
      organizationalInstanceSeniority: [{ value: null, disabled: true }],

      externalExperienceYears: [{ value: 0, disabled: true }],
      externalExperienceMonths: [{ value: 0, disabled: true }],
      externalExperienceDays: [{ value: 0, disabled: true }],
      externalExperience: [{ value: null, disabled: true }],

      employmentNote: [null]
    });

    // Initialize dependency manager
    this.dependencyManager = new FormDependencyManager(
      this.jobDataForm,
      this,
      this.selectManager
    );

    // set effective date 
    const effectiveDate = this.nzModalData.effectiveDate;
    if (effectiveDate) {
      this.jobDataForm.get('effectiveDateFrom')?.patchValue(effectiveDate);
      this.updateRelatedDates(effectiveDate);

      // initialize form data
      this.initializeFormData();
    }

    // get job data from parent
    if (this.nzModalData.jobData) {
      this.jobDataForm.patchValue(this.nzModalData.jobData, { emitEvent: false });

      // Check if there are files in the jobData
      if (this.nzModalData.jobData.files && Array.isArray(this.nzModalData.jobData.files)) {
        // Convert each file to NzUploadFile format
        this.fileList = this.nzModalData.jobData.files.map((file: any, index: number) => ({
          uid: `-${index + 1}`, // Generate unique IDs
          name: file.name || 'Unknown file',
          status: 'done',
          url: file.url, // If available
          size: file.size,
          type: file.type
        }));
      }

      // get employee sub group
      this.employeeGroup = this.nzModalData.jobData.employeeGroupCode ?? "EMP"; // default value is EMP

      // get action list
      this.setActionPicklistValues(this.employeeGroup).then();

      // init NZSelect values
      this.initNZSelectValues();

      // load select options
      this.loadSelectOptions(this.nzModalData.jobData);
    }

    // initialize employee group and sub group information
    this.initializeEmployeeGroupInfo();

    // get current effective date
    let currentEffectiveDate = this.nzModalData.effectiveDate;

    // Subscribe to changes in effectiveDateFrom - replace the existing subscription
    // this.jobDataForm.get('effectiveDateFrom')?.valueChanges.subscribe(
    //   (value) => {
    //     // Use the consolidated handler for value changes
    //     this.handleEffectiveDateChange(value);
    //   }
    // );

    // subscribe to changes in groupOriginalStartDate, oirOriginalStartDate
    ['groupOriginalStartDate', 'oirOriginalStartDate'].forEach(field => {
      this.jobDataForm.get(field)?.valueChanges.subscribe(() => {
        // this.updateGroupSeniority();
        // this.updateOrganizationalInstanceSeniority();

        // calculate employment
        this.calculateEmployment();
      });
    });

    // set multual exclusion
    this.setupMutualExclusion('reportPosition', 'supervisor');
    this.setupMutualExclusion('matrixReportPositionCode', 'matrixManagers');



    // subscribe to sign date changes
    this.jobDataForm.get('signDate')?.valueChanges.subscribe(date => {
      this.onSignDateChange(date);
    });
  }

  removeFile(list: NzUploadFile[], file: NzUploadFile): void {
    // Filter out only the selected file
    this.fileList = this.fileList.filter(f => f.uid !== file.uid);
    // Update form control
    this.jobDataForm.patchValue({
      files: this.fileList.length > 0 ? this.fileList : null
    });
  }

  getFileType(fileName: string): string {
    return HrFsBp001Service.getFileType(fileName);
  }

  // initialize form data
  initializeFormData(): void {
    try {
      // initialize picklist values
      this.getPicklistValues();

      // initialize selected company
      this.initSelectedCompany();
    } catch (error) {
      console.error('Error in initializeFormData:', error);
    }
  }

  // get picklist values
  getPicklistValues(): void {
      this.setHrStatusPicklistValues();
      this.setJobIndicatorPicklistValues();
      this.setManagerPicklistValues();
      this.setLevelOfDecisionPicklistValues();
      this.setFullPartPicklistValues();
      this.setRegionPicklistValues();
      this.setEmpLevelPicklistValues();
      this.setTimeZonePicklistValues();
  }

  // initialize employee group and sub group information
  private initializeEmployeeGroupInfo(): void {
    if (this.nzModalData.employeeGroupAndSubGroupInfo) {
      const info = this.nzModalData.employeeGroupAndSubGroupInfo;

      // set employee group 
      this.jobDataForm.get('employeeGroupCode')?.patchValue(info.employeeGroupCode);
      // set employee sub group
      this.jobDataForm.get('employeeSubGroupCode')?.patchValue(info.employeeSubGroupCode);

      // set employee group and sub group name
      this.employeeGroupName = info.employeeGroupName;
      this.employeeSubGroupName = info.employeeSubGroupName;

      // set employee sub group list
      this.employeeSubGroupList = info.employeeSubGroupList;
    }
  }

  // init selected company
  initSelectedCompany() {
    if (this.nzModalData.selectedCompany) {
      this.selectedCompany = this.nzModalData.selectedCompany;

      // add this selected company to the company list
      this.companyList = [this.selectedCompany];

      // Use setTimeout to ensure the options are rendered before setting the value
      setTimeout(() => {
        this.jobDataForm.get('companyCode')?.patchValue(this.selectedCompany.value);
      });
    }
  }

  // Track if company code has value
  private hasCompanyCode = false;

  onSelectOpenChange(open: boolean, fieldName: string): void {
    if (!open) return; // Only care about when select opens

    const companyCode = this.jobDataForm.get('companyCode')?.value;
    const currentHasCompany = !!companyCode;

    // Always trigger search for departmentCode when opening the dropdown
    if (fieldName === 'departmentCode') {
      this.onNZSelectSearch('', fieldName);
    }
    // For other fields, only trigger if company state changed
    else if (this.hasCompanyCode !== currentHasCompany) {
      this.onNZSelectSearch('', fieldName);
      this.hasCompanyCode = currentHasCompany;
    }
  }

  // reset company and dependent data
  resetCompanyDependentData() {
    // get current company code
    // Reset all dependent fields in assignment section
    this.dependencyManager.resetFields([
      'legalEntityCode',
      'businessUnitCode',
      'divisionCode',
      'departmentCode',
      'locationCode',
      'jobCode',
      'businessTitleCode',
      'careerStreamCode',
      'careerBandCode',
      'costCenterCode',
      'reportPosition',
      'supervisor',
      'matrixReportPositionCode',
      'matrixManagers',
      'positionCode'
    ]);

    // Clear the options lists
    this.legalEntityList = [];
    this.businessUnitList = [];
    this.divisionList = [];
    this.departmentList = [];
    this.locationList = [];
    this.jobList = [];
    this.businessTitleList = [];
    this.careerStreamList = [];
    this.careerBandList = [];
    this.costCenterList = [];
    this.reportPositionList = [];
    this.personalList = []; // for supervisor and matrix managers
    this.matrixReportPositionList = [];
    this.positionList = [];

    // Reset company state tracker
    this.hasCompanyCode = false;
  }

  // reload select options if has value
  private reloadSelectOptionsIfHasValue(): void {
    // Get all form controls that need to be checked
    const controlsToCheck = [
      {
        name: 'legalEntityCode',
        reloadMethod: () => this.selectManager.search('legalEntityCode', '', {})
      },
      {
        name: 'businessUnitCode',
        reloadMethod: () => this.selectManager.search('businessUnitCode', '', {})
      },
      {
        name: 'divisionCode',
        reloadMethod: () => this.setDivisionPicklistValues(undefined, '')
      },
      {
        name: 'departmentCode',
        reloadMethod: () => this.selectManager.search('departmentCode', '', {})
      },
      {
        name: 'locationCode',
        reloadMethod: () => this.selectManager.search('locationCode', '', {})
      },
      {
        name: 'jobCode',
        reloadMethod: () => this.setJobPicklistValues(undefined, '')
      },
      {
        name: 'businessTitleCode',
        reloadMethod: () => this.selectManager.search('businessTitleCode', '', {})
      },
      {
        name: 'careerStreamCode',
        reloadMethod: () => {
          const currentValue = this.jobDataForm.get('careerStreamCode')?.value;
          return this.selectManager.search('careerStreamCode', currentValue, {})
        }
      },
      {
        name: 'careerBandCode',
        reloadMethod: () => {
          const currentValue = this.jobDataForm.get('careerBandCode')?.value;
          return this.selectManager.search('careerBandCode', currentValue, {})
        }
      },
      {
        name: 'costCenterCode',
        reloadMethod: () => this.selectManager.search('costCenterCode', '', {})
      },
      {
        name: 'reportPosition',
        reloadMethod: () => this.selectManager.search('reportPosition', '', {})
      },
      {
        name: 'supervisor',
        reloadMethod: () => this.selectManager.search('supervisor', '', {})
      },
      {
        name: 'matrixReportPositionCode',
        reloadMethod: () => this.selectManager.search('matrixReportPositionCode', '', {})
      },
      {
        name: 'matrixManagers',
        reloadMethod: () => this.selectManager.search('matrixManagers', '', {})
      },
      {
        name: 'positionCode',
        reloadMethod: () => {
          const currentValue = this.jobDataForm.get('positionCode')?.value;
          return this.selectManager.search('positionCode', currentValue, {})
        }
      },
      {
        name: 'employeeSubGroupCode',
        reloadMethod: () => this.getEmployeeSubGroupList()
      }
    ];

    // Check each control and reload if it has a value
    controlsToCheck.forEach(control => {
      const formControl = this.jobDataForm.get(control.name);


      // For SelectManager controls
      if (control.name.match(/^(positionCode|locationCode|costCenterCode|businessTitleCode|careerStreamCode|careerBandCode|reportPosition|supervisor|matrixReportPositionCode|matrixManagers)$/)) {
        if (formControl && (formControl.value || this.selectManager.hasBeenOpened(control.name))) {
          const result = control.reloadMethod();
          if (result instanceof Promise) {
            result.then();
          } else {
            result.subscribe();
          }
        }
      }
      // For direct list controls (legalEntityCode, businessUnitCode, divisionCode, departmentCode, jobCode, etc.)
      else {
        if (formControl) {
          const result = control.reloadMethod();
          if (result instanceof Promise) {
            result.then();
          } else {
            result.subscribe();
          }
        }
      }
    });
  }

  ngAfterViewInit(): void {
    this.setupScrollListener();

    // set default active link
    setTimeout(() => {
      this.updateActiveLink('action');
    }, 100);

    // Setup date picker open change listeners
    this.datePickers.forEach(picker => {
      picker.nzOnOpenChange.subscribe((isOpen: boolean) => {
        this.datePickerScrollService.registerDatePicker(picker, isOpen);
      });
    });
  }

  fteFormatter = (value: number): string => {
    return value.toFixed(5);
  };

  fteParser = (value: string): string => {
    return value;
  };

  // init data for the selected values that in nz-select form
  initNZSelectValues(): void {
    const jobData = this.nzModalData.jobData;

    // if (jobData.positionCode) {
    //   this.loadPositionList(jobData.positionCode).then(() => {
    //     this.jobDataForm.get('positionCode')?.patchValue(jobData.positionCode);
    //   });
    // }

    // division
    if (jobData.divisionCode) {
      this.setDivisionPicklistValues(undefined, jobData.divisionCode).then(() => {
        this.jobDataForm.get('divisionCode')?.patchValue(jobData.divisionCode);
      });
    }

    // legal entity
    // if (jobData.legalEntityCode) {
    //   this.setLegalEntityPicklistValues().then();
    //   this.jobDataForm.get('legalEntityCode')?.patchValue(jobData.legalEntityCode);
    // }

    // business unit
    // if (jobData.businessUnitCode) {
    //   this.setBusinessUnitPicklistValues().then();
    //   this.jobDataForm.get('businessUnitCode')?.patchValue(jobData.businessUnitCode);
    // }

    // department
    // if (jobData.departmentCode) {
    //   this.setDepartmentPicklistValues().then();
    //   this.jobDataForm.get('departmentCode')?.patchValue(jobData.departmentCode);
    // }

    // job
    if (jobData.jobCode) {
      this.setJobPicklistValues(jobData.jobCode, '').then();
      this.jobDataForm.get('jobCode')?.patchValue(jobData.jobCode);
    }

    // prStatusCode
    if (jobData.prStatusCode) {
      this.jobDataForm.get('prStatusCode')?.patchValue(jobData.prStatusCode);
    }
  }


  submitForm(): void {
    if (this.jobDataForm.valid) {
      // Handle form submission
      console.log(this.jobDataForm.value);
    } else {
      // Handle form validation errors
      Object.values(this.jobDataForm.controls).forEach(control => {
        if (control.invalid) {
          control.markAsDirty();
          control.updateValueAndValidity();
        }
      });
    }
  }

  //#region scroll to section

  private isScrolling = false;
  private scrollTimeout: any;

  scrollToSection(sectionId: string): void {
    this.isScrolling = true;
    clearTimeout(this.scrollTimeout);

    const element = this.elementRef.nativeElement.querySelector(`#${sectionId}`);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'start' });
      this.updateActiveLink(sectionId);


    }
  }

  private setupScrollListener() {
    const sections = ['action', 'assignment', 'employment'];
    const observer = new IntersectionObserver((entries) => {
      if (!this.isScrolling) {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const id = entry.target.id;
            this.updateActiveLink(id);
          }
        });
      }
    }, { threshold: 0.2 }); // Adjust threshold as needed

    sections.forEach((id) => {
      const element = this.elementRef.nativeElement.querySelector(`#${id}`);
      if (element) {
        observer.observe(element);
      }
    });
  }

  private updateActiveLink(id: string) {
    const links = this.elementRef.nativeElement.querySelectorAll('nz-link');

    // First, remove the active class from all links
    links.forEach((link: HTMLElement) => {
      link.classList.remove('ant-anchor-link-active');
    });

    // Then, add the active class to the target link
    const targetLink: any = Array.from(links).find((link: any) =>
      link.getAttribute('nz-href') === `#${id}`
    );

    if (targetLink) {
      targetLink.classList.add('ant-anchor-link-active');
    }

    // Reset isScrolling after a delay
    this.scrollTimeout = setTimeout(() => {
      this.isScrolling = false;
    }, 500); // Adjust this value if needed
  }
  //#endregion

  cancel(): void {
    // close modal
    this.modalRef.close({
      jobData: this.jobDataForm.getRawValue(),
      jobIndicatorList: this.jobIndicatorList,
      managerList: this.managerList,
      isJobDataFormValid: this.isJobDataFormValid(),
      matrixReportPositionNames: this.selectedMatrixReportPositionNames,
      matrixManagerNames: this.selectedMatrixManagerNames
    });
  }


  isFormValidating = false;


  apply(): void {
    // the form is validating
    this.isFormValidating = true;

    this.validateFormWithMessage(['matrixReportPositionCode', 'matrixManagers']).then(result => {

      // the form is not validating
      this.isFormValidating = false;

      if (!result.isValid) {
        // scroll to the first error
        HrFsBp001Service.scrollToFirstError();

        if (result.errors) {
          // Show specific server validation errors
          const errorMessages = result.errors.map(e => e.message).join('\n');
          this.toast.showToast(
            'warning',
            'Validation Error',
            errorMessages
          );
        }
        return;
      }

      // close modal and send data to parent
      this.modalRef.close({
        jobData: this.jobDataForm.getRawValue(),
        isJobDataFormValid: true,
        jobIndicatorList: this.jobIndicatorList,
        managerList: this.managerList,
        matrixReportPositionNames: this.selectedMatrixReportPositionNames,
        matrixManagerNames: this.selectedMatrixManagerNames
      });
    });
  }

  // reason list
  reasonList: any[] = [];
  // hr status list
  hrStatusList: any[] = [];
  // payroll status list
  payrollStatusList: any[] = [];
  // job indicator list
  jobIndicatorList: any[] = [];
  // full part list
  fullPartList: any[] = [];
  // action list
  actionList: any[] = [];
  // manager list
  managerList: any[] = [];
  // level of decision
  levelOfDecisionList: any[] = [];
  // position list
  positionList: any[] = [];
  // report position list
  reportPositionList: any[] = [];
  // matrix report position list
  matrixReportPositionList: any[] = [];
  // company list
  companyList: any[] = [];
  // legal entity list
  legalEntityList: any[] = [];
  // business unit list
  businessUnitList: any[] = [];
  // business title list
  divisionList: any[] = [];
  // job list
  jobList: any[] = [];
  // department list
  departmentList: any[] = [];
  // location list
  locationList: any[] = [];
  // region list
  regionList: any[] = [];
  // business title list
  businessTitleList: any[] = [];
  // career stream list
  careerStreamList: any[] = [];
  // career band list
  careerBandList: any[] = [];
  // fte list
  fteList: any[] = [];
  // emp level list
  empLevelList: any[] = [];
  // time zone list
  timeZoneList: any[] = [];
  // cost center list
  costCenterList: any[] = [];
  // personal list
  personalList: any[] = [];


  // get effective date as string
  getEffectiveDateString(): string {
    return this.jobDataForm.get('effectiveDateFrom')?.value;
  }


  // get reason picklist values
  setReasonPicklistValues(actionReasonCodeList: any[] = []): void {
    if (actionReasonCodeList.length > 0) {
      this.hrFsBp001Service.getPicklistByCodeWithFilter(PicklistConstants.ACTIONREASON_LIST, '', [{
        field: 'code',
        operator: '$in',
        value: actionReasonCodeList
      }, {
        field: 'effectiveDate',
        operator: '$eq',
        value: this.getEffectiveDateString()
      }, {
        field: 'status',
        operator: '$eq',
        value: true
      }]).subscribe((reasonList) => {
        this.reasonList = reasonList;
        // incase of reopen dialog, set the selected reason
        const jobData = this.nzModalData.jobData;
        if (jobData.actionReasonCode) {
          this.jobDataForm.patchValue({
            "actionReasonCode": jobData.actionReasonCode
          });
        }
      });
    } else {
      this.reasonList = [];
    }
  }

  setHrStatusPicklistValues(): void {
    this.hrFsBp001Service.getPicklistByCode(PicklistConstants.HRSTATUS_LIST, undefined, this.getEffectiveDateString()).subscribe((hrStatusList) => {
      this.hrStatusList = hrStatusList;
      // set default hr status to A
      this.jobDataForm.patchValue({
        "hrStatusCode": "A"
      })
    });
  }

  setPayrollStatusPicklistValues(): void {
    this.hrFsBp001Service.getPicklistByCode(PicklistConstants.PAYROLLSTATUS_LIST, undefined, this.getEffectiveDateString()).subscribe((payrollStatusList) => {
      this.payrollStatusList = payrollStatusList;
    });
  }

  setJobIndicatorPicklistValues(): void {
    this.hrFsBp001Service.getPicklistByCode(PicklistConstants.JOBINDICATOR_LIST, undefined, this.getEffectiveDateString()).subscribe((jobIndicatorList) => {
      this.jobIndicatorList = jobIndicatorList;
      // if has data set the first value as default
      if (this.jobIndicatorList.length > 0) {
        this.jobDataForm.patchValue({
          "jobIndicatorCode": this.jobIndicatorList[0].value
        });
      }
    });
  }

  setManagerPicklistValues(): void {
    this.hrFsBp001Service.getPicklistByCode(PicklistConstants.ISMANAGER_LIST, undefined, this.getEffectiveDateString()).subscribe((managerList) => {
      this.managerList = managerList;
      // if manager list has data, set the item with code = 'IM_002' if not found set the first value as default
      const im002 = this.managerList.find((item) => item.code === 'IM_002');
      if (im002) {
        this.jobDataForm.patchValue({
          "isManagerCode": im002.value
        });
      } else {
        this.jobDataForm.patchValue({
          "isManagerCode": this.managerList[0].value
        });
      }
    });
  }

  setLevelOfDecisionPicklistValues(): void {
    this.hrFsBp001Service.getPicklistByCode(PicklistConstants.LEVELOFDECISION_LIST, undefined, this.getEffectiveDateString()).subscribe((levelOfDecisionList) => {
      this.levelOfDecisionList = levelOfDecisionList;
    });
  }

  // region list
  setRegionPicklistValues(): void {
    this.hrFsBp001Service.getPicklistByCode(PicklistConstants.REGION_LIST, undefined, this.getEffectiveDateString()).subscribe((regionList) => {
      this.regionList = regionList;
    });
  }

  // emp level list
  setEmpLevelPicklistValues(): void {
    this.hrFsBp001Service.getPicklistByCode(PicklistConstants.EMPLEVEL_LIST, undefined, this.getEffectiveDateString()).subscribe((empLevelList) => {
      this.empLevelList = empLevelList;
    });
  }

  // full part list
  setFullPartPicklistValues(): void {
    this.hrFsBp001Service.getPicklistByCode(PicklistConstants.FULLPART_LIST, undefined, this.getEffectiveDateString()).subscribe((fullPartList) => {
      this.fullPartList = fullPartList;
      // check in the fullPartList if has item with code = "F" then set fullPartCode of the form to "F"
      const f = this.fullPartList.find((item) => item.value === "F");
      if (f) {
        this.jobDataForm.patchValue({
          "fullPartCode": f.value
        });
      }
    });
  }

  // time zone list
  setTimeZonePicklistValues(): void {
    this.hrFsBp001Service.getPicklistByCode(PicklistConstants.TIMEZONE_LIST, undefined, this.getEffectiveDateString()).subscribe((timeZoneList) => {
      this.timeZoneList = timeZoneList;
    });
  }

  // action list
  setActionPicklistValues(employeeGroup: string, searchValue: string = ''): Promise<void> {
    this.isNZSelectLoading = true;

    // get the effective date 

    const effectiveDate = this.jobDataForm.get('effectiveDateFrom')?.value;
    // get utc time in seconds since epoch
    const effectiveDateUtc = this.hrFsBp001Service.getUTCTimeInSecond(effectiveDate);

    return new Promise((resolve) => {
      this.hrFsBp001Service.getActionListByTypeAndEmployeeGroup('2', employeeGroup, searchValue, effectiveDateUtc).subscribe((actionList) => {
        this.actionList = actionList;

        // if action code has data (in case of reopen dialog) --> load the corresponding reason picklist values
        const actionCode = this.jobDataForm.get('actionCode')?.value;
        if (actionCode) {
          this.onActionChange(actionCode);
        }

        this.isNZSelectLoading = false;
        resolve();
      });
    });
  }

  // Add a new property to store the action-reason map
  actionReasonMap: Map<string, any[]> = new Map();


  onActionChange(selectedActionCode: string): void {
    // reset the reason picklist values
    this.reasonList = [];
    // reset the selected reason
    this.jobDataForm.patchValue({
      "actionReasonCode": null
    });

    // reset the payroll status list
    this.payrollStatusList = [];
    // reset the selected payroll status
    this.jobDataForm.patchValue({
      "prStatusCode": null
    });


    // get the selection action
    const selectionAction = this.actionList.find((action) => action.value === selectedActionCode);
    if (selectionAction && selectionAction.actionReasons) {
      // get the reason picklist values
      this.setReasonPicklistValues(selectionAction.actionReasons);
    }
    if (selectionAction && selectionAction.setStatusField) {
      // set the payroll status
      this.setPayrollStatus(selectionAction.setStatusField, selectionAction.prStatusCode, selectionAction.prStatusName);
    } else {
      // load all the payroll status
      this.setPayrollStatusPicklistValues();
    }

    // calculate employment
    this.calculateEmployment();
  }

  // set payroll status base on the setStatusField
  setPayrollStatus(setStatusField: boolean = false, prStatusCode: string = '', prStatusName: string = '') {
    if (setStatusField) {
      this.jobDataForm.patchValue({
        "prStatusCode": prStatusCode
      });

      // set the prstatus list
      this.payrollStatusList = [{
        value: prStatusCode,
        label: prStatusName
      }];
    }
  }

  // #region Position

  // position list
  loadPositionList(searchValue: string = '', positionId?: string): Promise<void> {
    // if company code is null, reset position list
    if (this.isCompanyCodeNull()) {
      this.positionList = [];
      // stop loading
      this.isNZSelectLoading = false;
      return Promise.resolve();
    }

    // set loading
    this.isNZSelectLoading = true;

    // department code
    const departmentCode = this.jobDataForm.get('departmentCode')?.value;

    // job code code
    const jobCodeCode = this.jobDataForm.get('jobCode')?.value;

    // get effective date
    // const effectiveDate = this.jobDataForm.get('effectiveDateFrom')?.value;

    return new Promise((resolve) => {
      this.hrFsBp001Service.getPositionList(searchValue, positionId, jobCodeCode, departmentCode, this.selectedCompany?.value ?? '', undefined).subscribe({
        next: (positionList) => {
          this.positionList = positionList;
          // set report position list
          this.reportPositionList = positionList;
          // set matrix report position list
          this.matrixReportPositionList = positionList;
          // stop loading
          this.isNZSelectLoading = false;
          resolve();
        },
        error: (error) => {
          this.isNZSelectLoading = false;
          resolve();
        }
      });
    });
  }

  onPositionChange(event: any): void {
    // get selected position
    const selectedPosition = this.positionList.find((position) => position.value === event);
    if (!selectedPosition) {
      // clear dependent fields
      this.jobDataForm.patchValue({
        locationCode: null,
        costCenterCode: null,
        legalEntityCode: null,
        businessUnitCode: null,
        divisionCode: null,
        departmentCode: null,
        jobCode: null
      });
      return;
    }

    // Update form with position data
    this.jobDataForm.patchValue({
      departmentCode: selectedPosition.departmentId,
      locationCode: selectedPosition.locationId,
      costCenterCode: selectedPosition.costCenterId,
      jobCode: selectedPosition.jobCodeId,
      legalEntityCode: selectedPosition.legalEntityId,
      businessUnitCode: selectedPosition.businessUnitId,
      divisionCode: selectedPosition.divisionId
    });

    console.log('Position changed:', selectedPosition);
    console.log('Updated form values:', this.jobDataForm.value);
  }

  // #endregion

  // get company list
  getCompanyList(searchValue: string = '', companyCode?: string): Promise<void> {
    // get effective date 
    const effectiveDate = this.jobDataForm.get('effectiveDateFrom')?.value;

    return new Promise((resolve) => {
      this.hrFsBp001Service.getCompanyList(searchValue, companyCode, effectiveDate).subscribe((companyList) => {
        // if company list has data
        if (companyList.length > 0) {
          // set the company code
          this.jobDataForm.get('companyCode')?.patchValue(companyList.length > 0 ? companyList[0].value : null)
        }
        // stop loading
        this.isNZSelectLoading = false;
        resolve();
      });
    });
  }

  selectedCompanyId: string | null = null;


  onCompanyChange(event: any): void {

    // reset legal entity list
    this.legalEntityList = [];
    // reset business unit list
    this.businessUnitList = [];
    // reset coressponding formcontrol
    this.divisionList = [];
    this.departmentList = [];

    this.jobDataForm.patchValue({
      legalEntityCode: null,
      businessUnitCode: null,
      divisionCode: null,
      departmentCode: null
    });


    // this.setLegalEntityPicklistValues(event.value);
    // find company id in company list by company code
    const company = this.companyList.find((company) => company.value === event);
    if (company) {
      this.selectedCompanyId = company.id;
      // this.setLegalEntityPicklistValues(company.id);
      this.setBusinessUnitPicklistValues(company.id).then();
    } else {
      this.selectedCompanyId = null;
    }
  }

  onBusinessUnitChange(event: any): void {
    // clear division and department
    // this.divisionList = [];
    this.departmentList = [];

    // reset corresponding formcontrol
    // this.jobDataForm.get('divisionCode')?.reset();
    this.jobDataForm.get('departmentCode')?.reset();
    // update value and validity
    // this.jobDataForm.get('divisionCode')?.updateValueAndValidity();
    this.jobDataForm.get('departmentCode')?.updateValueAndValidity();

    // find business unit id in business unit list by business unit code
    const businessUnit = this.businessUnitList.find((businessUnit) => businessUnit.value === event);
    console.log(businessUnit, 'selected business unit');
    if (businessUnit) {
      // this.setDivisionPicklistValues(businessUnit.id);
      // set department list
      this.setDepartmentPicklistValues(businessUnit.id);
    }
  }


  // #region Legal Entity

  // on legal entity change
  onLegalEntityChange(event: any): void {
    // get selected legal entity
    const legalEntity = this.legalEntityList.find((legalEntity) => legalEntity.value === event);
    if (legalEntity) {
      this.setDepartmentPicklistValues().then();
    }
  }

  // clear legal entity list
  onLegalEntityClear(): void {
    this.legalEntityList = [];
  }

  // legal entity list
  setLegalEntityPicklistValues(legalId?: string, searchValue: string = ''): Promise<void> {
    // if company code is null, reset legal entity list
    if (this.isCompanyCodeNull()) {
      this.legalEntityList = [];
      // stop loading
      this.isNZSelectLoading = false;
      return Promise.resolve();
    }

    // set loading
    this.isNZSelectLoading = true;
    return new Promise((resolve) => {
      // get effective date
      const effectiveDate = this.jobDataForm.get('effectiveDateFrom')?.value;

      this.hrFsBp001Service.getLegalEntityList(searchValue, legalId, this.selectedCompany?.value ?? '', effectiveDate).subscribe((legalEntityList) => {
        this.legalEntityList = legalEntityList;
        // if find by legal Id, set the selected value
        if (legalId && legalEntityList.length > 0) {
          this.jobDataForm.patchValue({
            legalEntityCode: legalEntityList[0].value
          });
        }
        // stop loading
        this.isNZSelectLoading = false;
        resolve();
      });
    });
  }

  // #endregion

  // business unit list
  setBusinessUnitPicklistValues(businessUnitId?: string, searchValue: string = ''): Promise<void> {
    // if company code is null, reset business unit list
    if (this.isCompanyCodeNull()) {
      this.businessUnitList = [];
      // stop loading
      this.isNZSelectLoading = false;
      return Promise.resolve();
    }

    // set loading
    this.isNZSelectLoading = true;
    return new Promise((resolve) => {
      // get effective date
      const effectiveDate = this.jobDataForm.get('effectiveDateFrom')?.value;

      this.hrFsBp001Service.getBusinessUnitList(searchValue, businessUnitId, this.selectedCompany?.value ?? '', effectiveDate).subscribe((businessUnitList) => {
        this.businessUnitList = businessUnitList;
        // if find by business unit id, set the selected value
        if (businessUnitId && businessUnitList.length > 0) {
          this.jobDataForm.patchValue({
            businessUnitCode: businessUnitList[0].value
          });
        }
        // stop loading
        this.isNZSelectLoading = false;
        resolve();
      });
    });
  }

  // division list
  setDivisionPicklistValues(divisionId?: string, searchValue: string = ''): Promise<void> {
    // if company code is null, reset division list
    if (this.isCompanyCodeNull()) {
      this.divisionList = [];
      // stop loading
      this.isNZSelectLoading = false;
      return Promise.resolve();
    }


    // get business unit id 
    const businessUnitId = this.jobDataForm.get('businessUnitCode')?.value;

    // get effective date
    const effectiveDate = this.jobDataForm.get('effectiveDateFrom')?.value;

    return new Promise((resolve) => {
      this.hrFsBp001Service.getDivisionList(searchValue, divisionId, businessUnitId, this.selectedCompany?.value ?? '', effectiveDate).subscribe((divisionList) => {
        this.divisionList = divisionList;
        // if find by division id, set the selected value
        if (divisionId && divisionList.length > 0) {
          this.jobDataForm.patchValue({
            divisionCode: divisionList[0].value
          });
        }
        // stop loading
        this.isNZSelectLoading = false;
        resolve();
      });
    });
  }

  // #region Department

  // department list
  setDepartmentPicklistValues(departmentId?: string, searchValue: string = ''): Promise<void> {
    // if company code is null, reset department list
    if (this.isCompanyCodeNull()) {
      this.departmentList = [];
      // stop loading
      this.isNZSelectLoading = false;
      return Promise.resolve();
    }


    // start loading
    this.isNZSelectLoading = true;
    // find the selected legal entity id from the legal entity list
    const legalEntityCode = this.jobDataForm.get('legalEntityCode')?.value;
    const businessUnitCode = this.jobDataForm.get('businessUnitCode')?.value;
    const divisionCode = this.jobDataForm.get('divisionCode')?.value;
    // get effective date
    const effectiveDate = this.jobDataForm.get('effectiveDateFrom')?.value;

    return new Promise((resolve, reject) => {
      this.hrFsBp001Service.getDepartmentList(searchValue, departmentId, legalEntityCode, businessUnitCode, divisionCode, this.selectedCompany?.value ?? '', effectiveDate).subscribe({
        next: (departmentList) => {
          this.departmentList = departmentList;
          // stop loading 
          this.isNZSelectLoading = false;
          resolve();
        },
        error: (error) => {
          console.error('Error in department list:', error);
          this.isNZSelectLoading = false;
          reject(error);
        }
      });
    });
  }



  // #endregion

  // disabled the day before min effective date
  disablePastDate = (current: Date): boolean => {
    const minEffectiveDate = this.nzModalData.minEffectiveDate;
    if (minEffectiveDate) {
      // Reset time portions to midnight for accurate date comparison
      const currentDate = new Date(current.getFullYear(), current.getMonth(), current.getDate());
      const minEffectiveDateMidnight = new Date(minEffectiveDate.getFullYear(), minEffectiveDate.getMonth(), minEffectiveDate.getDate());

      // Can't select days before effectiveDate
      return currentDate < minEffectiveDateMidnight;
    }
    return false;
  };

  // location list
  setLocationPicklistValues(locationId?: string, searchValue: string = ''): Promise<void> {
    // get effective date
    const effectiveDate = this.jobDataForm.get('effectiveDateFrom')?.value;

    return new Promise((resolve) => {
      this.hrFsBp001Service.getLocationList(locationId, searchValue, this.selectedCompany?.value, effectiveDate).subscribe((locationList) => {
        // transform to location list
        if (locationList && locationList['data']) {
          this.locationList = locationList['data'].map((item: any) => ({
            value: item.code,
            label: item.longName.default || item.longName.en_US
          }));
        }
        // if find by location id, set the selected value
        if (this.locationList.length > 0) {
          this.jobDataForm.patchValue({
            locationCode: this.locationList[0].value
          });
        }
        // stop loading
        this.isNZSelectLoading = false;
        resolve();
      });
    });
  }

  // job list
  setJobPicklistValues(jobCode?: string, searchValue: string = ''): Promise<void> {
    // if company code is null, reset job list
    if (this.isCompanyCodeNull()) {
      this.jobList = [];
      // stop loading
      this.isNZSelectLoading = false;
      return Promise.resolve();
    }

    // get effective date
    const effectiveDate = this.jobDataForm.get('effectiveDateFrom')?.value;


    return new Promise((resolve) => {
      this.hrFsBp001Service.getJobList(searchValue, jobCode, this.selectedCompany?.value ?? '', effectiveDate).subscribe((jobList) => {
        this.jobList = jobList;
        // stop loading
        this.isNZSelectLoading = false;
        resolve();
      });
    });
  }

  // career stream list
  setCareerStreamPicklistValues(searchValue: string = ''): Promise<void> {
    // get effective date
    const effectiveDate = this.jobDataForm.get('effectiveDateFrom')?.value;

    return new Promise((resolve) => {
      this.hrFsBp001Service.getCareerStreamList(searchValue, effectiveDate).subscribe((careerStreamList) => {
        this.careerStreamList = careerStreamList.data;
        // stop loading
        this.isNZSelectLoading = false;
        resolve();
      });
    });
  }

  // business title list
  setBusinessTitlePicklistValues(searchValue: string = ''): Promise<void> {
    return new Promise((resolve) => {
      // get effective date
      const effectiveDate = this.jobDataForm.get('effectiveDateFrom')?.value;

      this.hrFsBp001Service.getBusinessTitleList(searchValue, effectiveDate, this.selectedCompany?.value).subscribe((businessTitleList) => {
        this.businessTitleList = businessTitleList.data;
        // stop loading
        this.isNZSelectLoading = false;
        resolve();
      });
    });
  }

  // career band list
  setCareerBandPicklistValues(searchValue: string = ''): Promise<void> {
    // get effective date
    const effectiveDate = this.jobDataForm.get('effectiveDateFrom')?.value;

    // get career stream id
    const careerStreamCode = this.jobDataForm.get('careerStreamCode')?.value;

    return new Promise((resolve) => {
      this.hrFsBp001Service.getCareerBandList(careerStreamCode, searchValue, effectiveDate, 1, 25).subscribe((careerBandList) => {
        this.careerBandList = careerBandList.data;
        // stop loading
        this.isNZSelectLoading = false;
        resolve();
      });
    });
  }

  // cost center list
  setCostCenterPicklistValues(costCenterId?: string, searchValue: string = ''): Promise<void> {
    return new Promise((resolve) => {
      // get effective date
      const effectiveDate = this.jobDataForm.get('effectiveDateFrom')?.value;

      this.hrFsBp001Service.getCostCenterList(costCenterId, searchValue, effectiveDate, this.selectedCompany?.value).subscribe((costCenterList) => {
        this.costCenterList = costCenterList.data;
        // stop loading
        this.isNZSelectLoading = false;
        resolve();
      });
    });
  }


  onNZSelectSearch(event: any, fromControlName: string) {
    this.isNZSelectLoading = true;

    // case of formControlName
    switch (fromControlName) {
      // case 'businessTitleCode':
      //   this.setBusinessTitlePicklistValues(event).then();
      //   break;
      // case 'careerStreamCode':
      //   this.setCareerStreamPicklistValues(event).then();
      //   break;
      // case 'careerBandCode':
      //   this.setCareerBandPicklistValues(event).then();
      //   break;
      // case 'costCenterCode':
      //   this.setCostCenterPicklistValues(event).then();
      //   break;
      // case 'positionCode':
      // case 'reportPosition':
      // case 'matrixReportPositionCode':
      //   this.loadPositionList(event).then();
      //   break;
      // case 'supervisor':
      // case 'matrixManagers':
      //   this.setPersonalPicklistValues(event).then();
      //   break;
      // case 'actionCode':
      //   this.setActionPicklistValues(event).then();
      //   break;
      // case 'companyCode':
      //   this.getCompanyList(event).then();
      //   break;
      case 'legalEntityCode':
        // if legal entity code is null, allow to search 
        this.setLegalEntityPicklistValues(undefined, event).then();
        break;
      case 'businessUnitCode':
        this.setBusinessUnitPicklistValues(undefined, event).then();
        break;
      case 'divisionCode':
        this.setDivisionPicklistValues(undefined, event).then();
        break;
      case 'departmentCode':
        this.setDepartmentPicklistValues(undefined, event).then();
        break;
      case 'locationCode':
        this.setLocationPicklistValues(undefined, event).then();
        break;
      case 'jobCode':
        this.setJobPicklistValues(undefined, event).then();
        break;
    }
  }


  fileList: NzUploadFile[] = [];

  beforeUpload = (file: NzUploadFile): boolean => {
    // check if the file type is accepted
    const isAcceptedType = this.hrFsBp001Service.checkFileType(file);

    // check if the file size is within the limit
    const isLessThanMaxSize = file.size! <= this.acceptFileSize;

    if (!isAcceptedType) {
      file.status = 'error';
      file.response = ERROR_MESSAGE.FILE_TYPE; // Add error message to file object
      this.errorMsg = ERROR_MESSAGE.FILE_TYPE;
      // Keep existing valid files and add the error file
      const validFiles = this.fileList.filter(f => f.status !== 'error');
      this.fileList = [...validFiles, file];
      this.startErrorResetTimer();
      return false;
    }

    if (!isLessThanMaxSize) {
      file.status = 'error';
      file.response = ERROR_MESSAGE.FILE_SIZE; // Add error message to file object
      this.errorMsg = ERROR_MESSAGE.FILE_SIZE;
      // Keep existing valid files and add the error file
      const validFiles = this.fileList.filter(f => f.status !== 'error');
      this.fileList = [...validFiles, file];
      this.startErrorResetTimer();
      return false;
    }

    // File is valid
    file.status = 'done';
    // Add to fileList if not exceeding limit
    if (this.fileList.length < 5) {
      // Filter out any error files and add the new valid file
      const validFiles = this.fileList.filter(f => f.status !== 'error');
      this.fileList = [...validFiles, file];
      // Update form control with all files
      this.jobDataForm.patchValue({
        files: this.fileList
      });
    }
    return false;
  }

  errorMsg = '';
  private resetErrorTimeoutId: any;

  private startErrorResetTimer() {
    if (this.resetErrorTimeoutId) {
      clearTimeout(this.resetErrorTimeoutId);
    }
    this.resetErrorTimeoutId = setTimeout(() => {
      this.errorMsg = '';
      // Only clear error files, keep valid ones
      const validFiles = this.fileList.filter(f => f.status !== 'error');
      this.fileList = validFiles;
    }, 3000);
  }

  handleUpload(info: NzUploadChangeParam): void {
    if (info.type === 'removed') {
      // Only remove if it's not an error file
      if (info.file.status !== 'error') {
        this.fileList = this.fileList.filter(f => f.uid !== info.file.uid);
        // Update form control
        this.jobDataForm.patchValue({
          files: this.fileList.length > 0 ? this.fileList : null
        });
      }
    }
  }



  updateRelatedDates(effectiveDate: Date) {
    if (effectiveDate) {
      const systemDate = new Date();
      const oneDay = 24 * 60 * 60 * 1000; // milliseconds in a day
      const diffDays = Math.floor((systemDate.getTime() - effectiveDate.getTime()) / oneDay) + 1;

      // calculate job seniority display
      const jobSeniorityDisplay = this.convertDaysToVietnameseDuration(effectiveDate, systemDate);


      this.jobDataForm.patchValue({
        employeeGroupFirstStartDate: effectiveDate,
        groupLastStartDate: effectiveDate,
        oirFirstStartDate: effectiveDate,
        oirLastStartDate: effectiveDate,
        oirOriginalStartDate: effectiveDate,
        groupOriginalStartDate: effectiveDate,
        groupFirstStartDate: effectiveDate,
        departmentEntryDate: effectiveDate,
        jobSeniority: diffDays,
        jobSeniorityDisplay: jobSeniorityDisplay,
      }, { emitEvent: false }); // Prevent infinite loop
    }
  }

  isJobDataFormValid(): boolean {
    return this.jobDataForm.valid;
  }

  // update group seniority
  updateGroupSeniority(seniorityTime: string) {
    if (seniorityTime) {
      const { years, months, days } = this.extractSeniorityTime(seniorityTime);

      this.jobDataForm.patchValue({
        groupSeniorityYears: years,
        groupSeniorityMonths: months,
        groupSeniorityDays: days,
        groupSeniority: seniorityTime
      }, { emitEvent: false });
    }
  }

  /**
   * Calculate employment
   */
  calculateEmployment() {
    const effectiveDate = this.jobDataForm.get('effectiveDateFrom')?.value;
    const companyCode = this.selectedCompany?.value;
    const employeeGroupCode = this.jobDataForm.get('employeeGroupCode')?.value;
    const organizationalInstanceRcd = this.jobDataForm.get('organizationalInstanceRcd')?.value;
    const groupOriginalStartDate = this.jobDataForm.get('groupOriginalStartDate')?.value;
    const oirOriginalStartDate = this.jobDataForm.get('oirOriginalStartDate')?.value;
    const actionCode = this.jobDataForm.get('actionCode')?.value;
    const empSubGroupCode = this.jobDataForm.get('employeeSubGroupCode')?.value;
    const empRecordNumber = this.jobDataForm.get('employeeRecordNumber')?.value;


    // check required fields
    if (!actionCode || !groupOriginalStartDate || !oirOriginalStartDate || !effectiveDate) {
      return;
    }


    this.hrFsBp001Service.calculateEmployment(actionCode, employeeGroupCode, organizationalInstanceRcd, groupOriginalStartDate, oirOriginalStartDate,
      companyCode, effectiveDate, empSubGroupCode, empRecordNumber).subscribe((response) => {
        this.updateGroupSeniority(response.groupSeniority);
        this.updateOrganizationalInstanceSeniority(response.organizationalInstanceSeniority);
        this.updateExternalExperience(response.externalExperience);
      });
  }

  // update organizational instance seniority
  updateOrganizationalInstanceSeniority(seniorityTime: string) {
    if (seniorityTime) {
      const { years, months, days } = this.extractSeniorityTime(seniorityTime);

      this.jobDataForm.patchValue({
        organizationalInstanceSeniorityYears: years,
        organizationalInstanceSeniorityMonths: months,
        organizationalInstanceSeniorityDays: days,
        organizationalInstanceSeniority: seniorityTime
      }, { emitEvent: false });
    }
  }

  // update external experience
  updateExternalExperience(seniorityTime: string) {
    if (seniorityTime) {
      const { years, months, days } = this.extractSeniorityTime(seniorityTime);

      this.jobDataForm.patchValue({
        externalExperienceYears: years,
        externalExperienceMonths: months,
        externalExperienceDays: days,
        externalExperience: seniorityTime
      }, { emitEvent: false });
    }
  }

  // calculate date difference  
  calculateDateDifference(startDate: string | Date, endDate: string | Date) {
    const start = moment(startDate);
    const end = moment(endDate);

    if (end.isSameOrAfter(start)) {
      const duration = moment.duration(end.diff(start)).add(1, 'days');
      return {
        years: duration.years(),
        months: duration.months(),
        days: duration.days()
      };
    } else {
      return { years: 0, months: 0, days: 0 };
    }
  }

  /**
   * Extract seniority time from the string
   * @param seniorityTime 
   * @returns 
   */
  extractSeniorityTime(seniorityTime: string) {
    if (!seniorityTime) {
      return { years: 0, months: 0, days: 0 };
    }

    let years = 0;
    let months = 0;
    let days = 0;

    // Match for years (năm)
    const yearMatch = seniorityTime.match(/(\d+)\s+năm/);
    if (yearMatch) {
      years = parseInt(yearMatch[1]);
    }

    // Match for months (tháng)
    const monthMatch = seniorityTime.match(/(\d+)\s+tháng/);
    if (monthMatch) {
      months = parseInt(monthMatch[1]);
    }

    // Match for days (ngày)
    const dayMatch = seniorityTime.match(/(\d+)\s+ngày/);
    if (dayMatch) {
      days = parseInt(dayMatch[1]);
    }

    return { years, months, days };
  }

  // set mutual exclusion controls
  private setupMutualExclusion(fromControl: string, toControl: string) {
    this.jobDataForm.get(fromControl)?.valueChanges.subscribe(value => {
      if (value) {
        this.jobDataForm.get(toControl)?.setValue(null, { emitEvent: false });
      }
    });

    this.jobDataForm.get(toControl)?.valueChanges.subscribe(value => {
      if (value) {
        this.jobDataForm.get(fromControl)?.setValue(null, { emitEvent: false });
      }
    });
  }



  //#region date handlers


  // disable expected end date if sign date is not set
  disabledExpectedEndDate = (current: Date): boolean => {
    const signDate = this.jobDataForm.get('signDate')?.value;
    return signDate && current < signDate;
  };

  // on sign date change, reset expected end date if it is before sign date
  onSignDateChange(date: Date) {
    const currentEndDate = this.jobDataForm.get('expectedEndDate')?.value;
    if (currentEndDate && date && currentEndDate < date) {
      this.jobDataForm.patchValue({ expectedEndDate: null });
    }
  }

  // handle the date input manually
  selectDate(event: any, controlName: string, noBeforeEffectiveDate: boolean = false): void {
    // If this is for effectiveDateFrom and we're already processing a change, skip
    if (controlName === 'effectiveDateFrom' && this.isEffectiveDateChanging) {
      return;
    }

    const input = event.target as HTMLInputElement;
    const inputDate = input.value;
    let parsedDate = this.hrFsBp001Service.parseDate(inputDate);

    if (parsedDate) {
      if (noBeforeEffectiveDate) {
        // get effective date from nzModalData
        const effectiveDate = this.nzModalData.effectiveDate;
        if (effectiveDate && parsedDate < effectiveDate) {
          parsedDate = effectiveDate;
          return;
        }
      }

      // handle expected end date validation
      if (controlName === 'expectedEndDate') {
        const signDate = this.jobDataForm.get('signDate')?.value;
        if (signDate && parsedDate && parsedDate < signDate) {
          parsedDate = signDate;
        }
      }

      // Special handling for effectiveDateFrom
      if (controlName === 'effectiveDateFrom') {
        this.handleEffectiveDateChange(parsedDate, event);
      } else {
        // For other date fields, use normal handling
        this.jobDataForm.get(controlName)?.setValue(parsedDate);
      }

      input.blur(); // This will close the date picker
    } else {
      this.jobDataForm.get(controlName)?.setValue(null);
    }
  }

  // disable date before effective date
  disableDateBeforeEffectiveDate = (current: Date): boolean => {
    const effectiveDate = this.jobDataForm.get('effectiveDateFrom')?.value;
    return effectiveDate ? current < effectiveDate : false;
  };


  //#endregion



  //#region "Handle infinite scoll"

  selectManager = inject(SelectManager);
  // Add debounce time constant
  private readonly SEARCH_DEBOUNCE = 300;
  private searchDebounceTimer: any;


  private registerSelects(): void {
    // Register location select
    this.selectManager.registerSelect({
      fieldName: 'locationCode',
      serviceMethod: (params) => this.hrFsBp001Service.getLocationList(
        undefined,
        params.searchValue,
        this.selectedCompany?.value,
        this.jobDataForm.get('effectiveDateFrom')?.value,
        params.page,
        params.pageSize
      ),
      transformResponse: (response: any) => {
        return response.map((item: any) => ({
          value: item.code,
          label: item.longName.default || item.longName.en_US
        }));
      },
      dependsOn: ['departmentCode', 'effectiveDateFrom']
    });

    // Report Position registration
    this.selectManager.registerSelect({
      fieldName: 'reportPosition',
      serviceMethod: (params) => this.hrFsBp001Service.getPositionBy(
        this.selectedCompany?.value,
        this.jobDataForm.get('effectiveDateFrom')?.value
      ),

      dependsOn: []
    });

    // Supervisor registration
    this.selectManager.registerSelect({
      fieldName: 'supervisor',
      serviceMethod: (params) => this.hrFsBp001Service.getPersonalList(
        params.searchValue,
        this.jobDataForm.get('effectiveDateFrom')?.value,
        params.page,
        params.pageSize
      ),
      dependsOn: ['effectiveDateFrom']
    });

    // Matrix Report Position registration
    this.selectManager.registerSelect({
      fieldName: 'matrixReportPositionCode',
      serviceMethod: () => this.hrFsBp001Service.getPositionBy(
        this.selectedCompany?.value,
        this.jobDataForm.get('effectiveDateFrom')?.value
      ),
      dependsOn: ['effectiveDateFrom']
    });

    // Matrix Managers registration
    this.selectManager.registerSelect({
      fieldName: 'matrixManagers',
      serviceMethod: (params) => this.hrFsBp001Service.getPersonalList(
        params.searchValue,
        this.jobDataForm.get('effectiveDateFrom')?.value,
        params.page,
        params.pageSize
      ),

      dependsOn: ['effectiveDateFrom']
    });

    // register business title select
    this.selectManager.registerSelect({
      fieldName: 'businessTitleCode',
      serviceMethod: (params) => this.hrFsBp001Service.getBusinessTitleList(
        params.searchValue,
        this.jobDataForm.get('effectiveDateFrom')?.value,
        this.selectedCompany?.value,
        params.page,
        params.pageSize
      ),
      transformResponse: (response: any) => {
        return response.map((item: any) => ({
          value: item.code,
          label: item.longName.default || item.longName.en_US
        }));
      },
      dependsOn: ['effectiveDateFrom', 'companyCode']
    });

    // register cost center select
    this.selectManager.registerSelect({
      fieldName: 'costCenterCode',
      serviceMethod: (params) => this.hrFsBp001Service.getCostCenterList(
        params.filters?.['code'],
        params.searchValue,
        this.jobDataForm.get('effectiveDateFrom')?.value,
        this.selectedCompany?.value,
        params.page,
        params.pageSize
      ),
      transformResponse: (response: any) => {
        return response.map((item: any) => ({
          value: item.code,
          label: item.longName.default || item.longName.en_US
        }));
      },
      dependsOn: ['effectiveDateFrom', 'companyCode']
    });

    // register career stream select
    this.selectManager.registerSelect({
      fieldName: 'careerStreamCode',
      serviceMethod: (params) => this.hrFsBp001Service.getCareerStreamList(
        params.searchValue,
        this.jobDataForm.get('effectiveDateFrom')?.value,
        params.page,
        params.pageSize
      ),
      transformResponse: (response: any) => {
        return response.map((item: any) => ({
          value: item.code,
          label: item.longName.default || item.longName.en_US
        }));
      },
      dependsOn: ['effectiveDateFrom']
    });

    // register career band select
    this.selectManager.registerSelect({
      fieldName: 'careerBandCode',
      serviceMethod: (params) => this.hrFsBp001Service.getCareerBandList(
        this.jobDataForm.get('careerStreamCode')?.value,
        params.searchValue,
        this.jobDataForm.get('effectiveDateFrom')?.value,
        params.page,
        params.pageSize
      ),
      transformResponse: (response: any) => {
        return response.map((item: any) => ({
          value: item.code,
          label: item.longName?.default || item.longName?.en_US,
          careerStreamCode: item.careerStreamCode,
          careerStreamId: item.careerStreamId
        }));
      },
      dependsOn: ['effectiveDateFrom']
    });

    // Position registration
    this.selectManager.registerSelect({
      fieldName: 'positionCode',
      serviceMethod: (params) => this.hrFsBp001Service.getPositionList(
        params.searchValue,
        undefined,
        this.jobDataForm.get('jobCode')?.value,
        this.jobDataForm.get('departmentCode')?.value,
        this.jobDataForm.get('legalEntityCode')?.value,
        this.selectedCompany?.value ?? '',
        this.jobDataForm.get('effectiveDateFrom')?.value
      ),
      dependsOn: ['jobCode', 'departmentCode', 'legalEntityCode', 'effectiveDateFrom']
    });

    // register legal entity select
    this.selectManager.registerSelect({
      fieldName: 'legalEntityCode',
      serviceMethod: (params) => this.hrFsBp001Service.getLegalEntityList(
        params.searchValue,
        undefined,
        this.selectedCompany?.value ?? '',
        this.jobDataForm.get('effectiveDateFrom')?.value,
        params.page,
        params.pageSize
      ),
      dependsOn: ['companyCode', 'effectiveDateFrom']
    });

    // register business unit select
    this.selectManager.registerSelect({
      fieldName: 'businessUnitCode',
      serviceMethod: (params) => this.hrFsBp001Service.getBusinessUnitList(
        params.searchValue,
        undefined,
        this.selectedCompany?.value ?? '',
        this.jobDataForm.get('effectiveDateFrom')?.value,
        params.page,
        params.pageSize
      ),
      dependsOn: ['companyCode', 'effectiveDateFrom']
    });
    
    // register department select
    this.selectManager.registerSelect({
      fieldName: 'departmentCode',
      serviceMethod: (params) => this.hrFsBp001Service.getDepartmentList(
        params.searchValue,
        undefined,
        this.jobDataForm.get('legalEntityCode')?.value,
        this.jobDataForm.get('businessUnitCode')?.value,
        this.jobDataForm.get('divisionCode')?.value,
        this.selectedCompany?.value ?? '',
        this.jobDataForm.get('effectiveDateFrom')?.value,
        params.page,
        params.pageSize
      ),
      dependsOn: ['legalEntityCode', 'businessUnitCode', 'divisionCode', 'companyCode', 'effectiveDateFrom']
    });
  }

  onSelectScroll(fieldName: string): void {
    const filters = this.getFiltersForField(fieldName);
    this.selectManager.loadMore(fieldName, filters).subscribe(data => {
      // Update your component's data array if needed
    });
  }

  onSelectSearch(fieldName: string, searchValue: string): void {
    // Skip empty searches during selection
    if (searchValue === '' && this.selectManager.isSelecting.get(fieldName)) {
      return;
    }

    // Debounce the search
    if (this.searchDebounceTimer) {
      clearTimeout(this.searchDebounceTimer);
    }

    this.searchDebounceTimer = setTimeout(() => {
      const filters = this.getFiltersForField(fieldName);
      this.selectManager.search(fieldName, searchValue, filters).subscribe();
    }, this.SEARCH_DEBOUNCE);
  }

  // Add selection handler
  onSelectChange(fieldName: string): void {
    this.selectManager.onSelectChange(fieldName);
  }

  private getFiltersForField(fieldName: string): Record<string, any> {
    const filters: Record<string, any> = {
      companyCode: this.selectedCompany?.value,
      effectiveDate: this.jobDataForm.get('effectiveDateFrom')?.value,
      legalEntityId: ''
    };

    // Add field-specific filters
    switch (fieldName) {
      case 'departmentCode':
        filters['legalEntityId'] = this.jobDataForm.get('legalEntityCode')?.value;
        break;
      // Add other field-specific filters
    }

    return filters;
  }


  /**
   * Load select options when start up
   * @param savedData 
   */
  private loadSelectOptions(savedData: any): void {
    // Load location options
    if (savedData.locationCode) {
      const filters = this.getFiltersForField('locationCode');
      this.selectManager.search('locationCode', savedData.locationCode, filters)
        .pipe(take(1))
        .subscribe(() => {
          this.jobDataForm.get('locationCode')?.patchValue(savedData.locationCode);
        });
    }

    // Load report position options
    if (savedData.reportPosition) {
      const filters = this.getFiltersForField('reportPosition');
      this.selectManager.search('reportPosition', savedData.reportPosition, filters)
        .pipe(take(1))
        .subscribe(() => {
          this.jobDataForm.get('reportPosition')?.patchValue(savedData.reportPosition);
        });
    }

    // Load supervisor options
    if (savedData.supervisor) {
      const filters = this.getFiltersForField('supervisor');
      this.selectManager.search('supervisor', savedData.supervisor, filters)
        .pipe(take(1))
        .subscribe(() => {
          this.jobDataForm.get('supervisor')?.patchValue(savedData.supervisor);
        });
    }

    // Initialization for matrix report positions and matrix managers
    if (savedData.matrixReportPositionCode && savedData.matrixReportPositionNames && Array.isArray(savedData.matrixReportPositionCode)) {
      const matrixState = this.selectManager.getState('matrixReportPositionCode');
      if (matrixState) {
        matrixState.data = savedData.matrixReportPositionCode.map((value: string, idx: number) => ({
          value,
          label: savedData.matrixReportPositionNames[idx] || value
        }));
        this.selectManager.setState('matrixReportPositionCode', matrixState);
        this.jobDataForm.get('matrixReportPositionCode')?.patchValue(savedData.matrixReportPositionCode);
        // Make sure we update the selected options tracking array
        const matrixReportPositions = savedData.matrixReportPositionCode || [];
        this.selectedMatrixReportPositions = Array.isArray(matrixReportPositions) ? matrixReportPositions : [matrixReportPositions];
      }
    }

    if (savedData.matrixManagers && savedData.matrixManagerNames && Array.isArray(savedData.matrixManagers)) {
      const managerState = this.selectManager.getState('matrixManagers');
      if (managerState) {
        managerState.data = savedData.matrixManagers.map((value: string, idx: number) => ({
          value,
          label: savedData.matrixManagerNames[idx] || value
        }));
        this.selectManager.setState('matrixManagers', managerState);
        this.jobDataForm.get('matrixManagers')?.patchValue(savedData.matrixManagers);
        // Make sure we update the selected options tracking array
        const matrixManagers = savedData.matrixManagers || [];
        this.selectedMatrixManagers = Array.isArray(matrixManagers) ? matrixManagers : [matrixManagers];
      }
    }

    // Load business title options
    if (savedData.businessTitleCode) {
      const filters = this.getFiltersForField('businessTitleCode');
      this.selectManager.search('businessTitleCode', savedData.businessTitleCode, filters)
        .pipe(take(1))
        .subscribe(() => {
          this.jobDataForm.get('businessTitleCode')?.patchValue(savedData.businessTitleCode);
        });
    }

    // Load cost center options
    if (savedData.costCenterCode) {
      const filters = this.getFiltersForField('costCenterCode');
      // add code param to  filters
      filters['code'] = savedData.costCenterCode;
      this.selectManager.search('costCenterCode', '', filters)
        .pipe(take(1))
        .subscribe(() => {
          this.jobDataForm.get('costCenterCode')?.patchValue(savedData.costCenterCode);
        });
    }

    // Load career stream options
    if (savedData.careerStreamCode) {
      const filters = this.getFiltersForField('careerStreamCode');
      this.selectManager.search('careerStreamCode', savedData.careerStreamCode, filters)
        .pipe(take(1))
        .subscribe(() => {
          this.jobDataForm.get('careerStreamCode')?.patchValue(savedData.careerStreamCode);
        });
    }

    // Load career band options
    if (savedData.careerBandCode) {
      const filters = this.getFiltersForField('careerBandCode');
      this.selectManager.search('careerBandCode', savedData.careerBandCode, filters)
        .pipe(take(1))
        .subscribe(() => {
          this.jobDataForm.get('careerBandCode')?.patchValue(savedData.careerBandCode);
        });
    }

    // Load position options
    if (savedData.positionCode) {
      const filters = this.getFiltersForField('positionCode');
      this.selectManager.search('positionCode', savedData.positionCode, filters)
        .pipe(take(1))
        .subscribe(() => {
          this.jobDataForm.get('positionCode')?.patchValue(savedData.positionCode);
        });
    }

    if (savedData.matrixReportPositionNames) {
      this.selectedMatrixReportPositionNames = savedData.matrixReportPositionNames;
    }
    if (savedData.matrixManagerNames) {
      this.selectedMatrixManagerNames = savedData.matrixManagerNames;
    }

    // load legal entity
    if (savedData.legalEntityCode) {
      this.selectManager.search('legalEntityCode', savedData.legalEntityCode, {}).subscribe();
    }

    // load business unit
    if (savedData.businessUnitCode) {
      this.selectManager.search('businessUnitCode', savedData.businessUnitCode, {}).subscribe();
    }

    // load department
    if (savedData.departmentCode) {
      this.selectManager.search('departmentCode', savedData.departmentCode, {}).subscribe();
    }
  }





  //#endregion


  ngOnDestroy(): void {
    if (this.searchDebounceTimer) {
      clearTimeout(this.searchDebounceTimer);
    }
    if (this.resetErrorTimeoutId) {
      clearTimeout(this.resetErrorTimeoutId);
    }
    // Make sure to clean up any remaining listeners
    this.datePickers?.forEach(picker => {
      this.datePickerScrollService.registerDatePicker(picker, false);
    });
  }


  isCompanyCodeNull(): boolean {
    return this.jobDataForm.get('companyCode')?.value === null;
  }

  private readonly serverToFormControlMapping: Record<string, string> = {
    'LegalEntityCode': 'legalEntityCode',
    'BusinessUnitCode': 'businessUnitCode',
    'DivisionCode': 'divisionCode',
    'DepartmentCode': 'departmentCode',
    'CostCenterCode': 'costCenterCode',
    'LocationCode': 'locationCode',
    'CareerStreamCode': 'careerStreamCode',
    'CareerBandCode': 'careerBandCode',
    'ReportPosition': 'reportPosition',
    'JobCode': 'jobCode',
    'CompanyCode': 'companyCode',
    'Supervisor': 'supervisor',
    'RegionCode': 'regionCode',
    'MatrixManager': 'matrixManagers',
    'BusinessTitleCode': 'businessTitleCode',
    'ActionCode': 'actionCode',
    'ActionReasonCode': 'actionReasonCode',
    'EmployeeGroupCode': 'employeeGroupName',
    'EmployeeSubGroupCode': 'employeeSubGroupCode',
    'PositionCode': 'positionCode',
    'MatrixReportPositionCodes': 'matrixReportPositionCode',
    'EmployeeLevelCode': 'empLevelCode',

    // Add more mappings as needed
  };

  private readonly controlToLabelMapping: Record<string, string> = {
    'employeeSubGroupCode': 'Employee Sub Group',
    'actionCode': 'Action'
  };


  /**
   * Validate the form and show server input error for the fields specified in the fieldsToShowServerInputError array
   * @param fieldsToShowServerInputError 
   * @returns 
   */
  validateFormWithMessage(fieldsToShowServerInputError: string[] = []): Promise<{ isValid: boolean, errors?: any[] }> {
    return new Promise((resolve) => {
      // Set validation mode before validating
      this.dependencyManager.setValidationMode(true);

      // Mark fields that need validation
      const fieldsToValidate = [
        'actionCode',
        'actionReasonCode',
        'prStatusCode',
        'legalEntityCode',
        'departmentCode',
        'jobCode',
        'empLevelCode',
        'fullPartCode',
        'groupOriginalStartDate',
        'oirOriginalStartDate',
        'employeeSubGroupCode',
        'businessUnitCode',
        'divisionCode',
        'positionCode',
        'businessTitleCode',
        'costCenterCode',
        'locationCode',
        'careerStreamCode',
        'careerBandCode',
        'companyCode',
        'reportPosition',
        'matrixManagers',
        'supervisor',
        'matrixReportPositionCode',
        'regionCode'
      ];



      // Mark fields as touched and validate
      fieldsToValidate.forEach(field => {
        const control = this.jobDataForm.get(field);
        if (control) {
          control.markAsTouched();
          control.updateValueAndValidity();
        }
      });

      // Check for client-side validation errors (excluding server errors)
      const hasClientValidationErrors = Object.keys(this.jobDataForm.controls)
        .some(key => {
          const control = this.jobDataForm.get(key);
          return control?.errors &&
            control.touched &&
            !control.errors['serverError']; // Ignore server errors for this check
        });

      // Clear any previous server validation errors before validating again
      this.clearServerValidationErrors();

      // If client-side validation fails, return immediately
      if (hasClientValidationErrors) {
        this.dependencyManager.setValidationMode(false);
        resolve({ isValid: false });
        return;
      }

      // Validate selected values
      const selectedValuesValidation = this.validateSelectedValues();
      const invalidSelections = Object.entries(selectedValuesValidation)
        .filter(([_, isValid]) => !isValid)
        .map(([field]) => field);

      if (invalidSelections.length > 0) {
        // Create validation errors for invalid selections
        invalidSelections.forEach(field => {
          const control = this.jobDataForm.get(field);
          if (control) {
            control.setErrors({
              invalidSelection: true,
              message: `${this.controlToLabelMapping[field]} is not correct`
            });
            control.markAsTouched();
          }
        });

        this.dependencyManager.setValidationMode(false);
        resolve({ isValid: false });
        return;
      }

      // Proceed with server-side validation
      this.validateJobData().subscribe({
        next: (errors) => {
          if (errors && errors.length > 0) {
            // Set server validation errors
            errors.forEach(error => {
              // Map the server field name to form control name
              const formControlName = this.serverToFormControlMapping[error.note] || error.note;
              const control = this.jobDataForm.get(formControlName);

              if (control) {
                control.setErrors({
                  serverError: true,
                  message: error.message + (fieldsToShowServerInputError.includes(formControlName) ? `: ${error.inputError}` : '')
                });
                control.markAsTouched();
                control.markAsDirty();  // Also mark as dirty to ensure error state
              } else {
                console.warn(`No form control found for server field: ${error.note}, mapped to: ${formControlName}`);
              }
            });
            resolve({ isValid: false });
          } else {
            resolve({ isValid: true });
          }
        },
        error: () => {
          resolve({ isValid: false, errors: [{ message: 'Server validation failed' }] });
        },
        complete: () => {
          this.dependencyManager.setValidationMode(false);
        }
      });
    });
  }


  // get employee sub group list
  getEmployeeSubGroupList(): Promise<void> {
    return new Promise((resolve, reject) => {
      // get the employee group code
      const employeeGroupCode = this.jobDataForm.get('employeeGroupCode')?.value;

      // get the employee sub group list
      this.hrFsBp001Service.getEmployeeSubGroupList(
        employeeGroupCode,
        this.jobDataForm.get('effectiveDateFrom')?.value
      ).subscribe((data) => {
        this.employeeSubGroupList = data;
        resolve();
      });
    });
  }


  // validate job data
  validateJobData(): Observable<any[]> {
    const effectiveDate = this.jobDataForm.get('effectiveDateFrom')?.value;
    const companyCode = this.jobDataForm.get('companyCode')?.value;
    const legalEntityCode = this.jobDataForm.get('legalEntityCode')?.value;
    const businessUnitCode = this.jobDataForm.get('businessUnitCode')?.value;
    const division = this.jobDataForm.get('divisionCode')?.value;
    const department = this.jobDataForm.get('departmentCode')?.value;
    const costCenter = this.jobDataForm.get('costCenterCode')?.value;
    const location = this.jobDataForm.get('locationCode')?.value;
    const careerStream = this.jobDataForm.get('careerStreamCode')?.value;
    const careerBand = this.jobDataForm.get('careerBandCode')?.value;
    const reportPosition = this.jobDataForm.get('reportPosition')?.value;
    const jobCode = this.jobDataForm.get('jobCode')?.value;
    const positionCode = this.jobDataForm.get('positionCode')?.value;
    const businessTitleCode = this.jobDataForm.get('businessTitleCode')?.value;
    const actionCode = this.jobDataForm.get('actionCode')?.value;
    const actionReasonCode = this.jobDataForm.get('actionReasonCode')?.value;
    const employeeGroupCode = this.jobDataForm.get('employeeGroupCode')?.value;
    const employeeSubGroupCode = this.jobDataForm.get('employeeSubGroupCode')?.value;
    const matrixManager = this.jobDataForm.get('matrixManagers')?.value;
    const supervisor = this.jobDataForm.get('supervisor')?.value;
    const regionCode = this.jobDataForm.get('regionCode')?.value;
    const matrixReportPositionCodes = this.jobDataForm.get('matrixReportPositionCode')?.value;
    const employeeLevelCode = this.jobDataForm.get('empLevelCode')?.value;

    return this.hrFsBp001Service.validateJobData(effectiveDate, companyCode, legalEntityCode,
      businessUnitCode, division, department, costCenter, location, careerStream, careerBand, reportPosition, jobCode, positionCode, businessTitleCode, actionCode, actionReasonCode,
      employeeGroupCode, employeeSubGroupCode, matrixManager, supervisor, regionCode, matrixReportPositionCodes, employeeLevelCode);
  }


  /**
   * Validates if the selected values exist in their corresponding option lists
   * @returns Object containing validation results for each field
   */
  private validateSelectedValues(): { [key: string]: boolean } {
    const validationResults: { [key: string]: boolean } = {};

    // Validate actionCode against actionList
    const actionCode = this.jobDataForm.get('actionCode')?.value;
    if (actionCode) {
      validationResults['actionCode'] = this.actionList.some(option => option.value === actionCode);
    }

    // Validate employeeSubGroupCode against employeeSubGroupList
    const employeeSubGroupCode = this.jobDataForm.get('employeeSubGroupCode')?.value;
    if (employeeSubGroupCode) {
      validationResults['employeeSubGroupCode'] = this.employeeSubGroupList.some(option => option.value === employeeSubGroupCode);
    }

    // // For fields managed by SelectManager, check if they exist in the manager's options
    // const selectManagerFields = ['locationCode', 'reportPosition', 'supervisor', 
    //   'matrixReportPositionCode', 'matrixManagers', 'businessTitleCode', 
    //   'costCenterCode', 'careerStreamCode', 'careerBandCode', 'positionCode'];

    // selectManagerFields.forEach(fieldName => {
    //   const value = this.jobDataForm.get(fieldName)?.value;
    //   if (value) {
    //     validationResults[fieldName] = this.selectManager.isValueInOptions(fieldName, value);
    //   }
    // });

    return validationResults;
  }


  /**
   * Clears server validation errors for specified fields
   * @param fields Array of field names to clear errors for. If not provided, clears all server errors.
   */
  private clearServerValidationErrors(fields?: string[]) {
    const controlsToClear = fields || Object.values(this.serverToFormControlMapping);

    controlsToClear.forEach(fieldName => {
      const control = this.jobDataForm.get(fieldName);
      if (control && control.errors) {
        // If the control only has serverError, clear all errors
        if (Object.keys(control.errors).length === 1 && control.errors['serverError']) {
          control.setErrors(null);
        }
        // If the control has multiple errors, remove only the serverError
        else if (control.errors['serverError']) {
          const { serverError, ...remainingErrors } = control.errors;
          control.setErrors(Object.keys(remainingErrors).length ? remainingErrors : null);
        }
      }
    });
  }

  /**
 * Resets form controls - both values and validation states
 * @param fields Array of field names to reset. If not provided, resets all mapped fields
 * @param options Configuration options for the reset
 */
  private resetFormControls(fields?: string[], options: {
    clearValues?: boolean;       // Whether to clear the control values
    clearErrors?: boolean;       // Whether to clear validation errors
    excludeFields?: string[];    // Fields to exclude from reset
    onlyServerErrors?: boolean;  // If true, only clears server validation errors
  } = {}) {
    const {
      clearValues = true,
      clearErrors = true,
      excludeFields = [],
      onlyServerErrors = false
    } = options;

    const controlsToReset = fields || Object.values(this.serverToFormControlMapping);

    controlsToReset
      .filter(fieldName => !excludeFields.includes(fieldName))
      .forEach(fieldName => {
        const control = this.jobDataForm.get(fieldName);
        if (control) {
          // Clear values if requested
          if (clearValues) {
            control.patchValue(null, { emitEvent: false });
          }

          // Handle error clearing
          if (clearErrors) {
            if (onlyServerErrors && control.errors) {
              // If only clearing server errors and control has errors
              if (control.errors['serverError']) {
                // If serverError is the only error, clear all errors
                control.setErrors(null);
              }
            } else {
              // Clear all errors
              control.setErrors(null);
            }
          }

          // Reset control states
          control.markAsUntouched();
          control.markAsPristine();
        }
      });
  }

  // Add this method to handle effective date changes
  handleEffectiveDateChange(newDate: Date | null, event: any): void {
    console.log('handleEffectiveDateChange', newDate, event);
    // Skip if already processing a change or no actual change
    if (this.isEffectiveDateChanging || newDate === null || newDate === undefined) {
      return;
    }

    // Get current value for comparison
    const currentValue = event.previousValue ?
      this.hrFsBp001Service.parseDate(event.previousValue) :
      this.jobDataForm.get('effectiveDateFrom')?.value;

    // Skip if the date hasn't actually changed (same date object or same date value)
    if (currentValue && newDate &&
      (currentValue === newDate ||
        currentValue.getTime() === newDate.getTime())) {
      return;
    }

    // Store for potential reversion
    const previousDate = currentValue;

    // Set flag to prevent recursive trigger
    this.isEffectiveDateChanging = true;

    // Show confirmation dialog
    const confirmModal = this.modalComponent.showDialog({
      nzTitle: 'Warning',
      nzContent: 'When changing the effective date, please ensure to check the relevant job data information.',
      nzWrapClassName: 'popup popup-confirm effectiveDateChange',
      nzIconType: 'icons:warning',
      nzOkText: 'Confirm',
      nzCancelText: 'Cancel',
      nzOnOk: () => {
        // Close the modal
        confirmModal.close();

        // Update the form with the new date WITHOUT triggering valueChanges
        this.jobDataForm.patchValue({
          effectiveDateFrom: newDate
        }, { emitEvent: false }); // Important: prevent valueChanges from firing again

        // Perform all the related updates
        this.updateRelatedDates(newDate);

        // Clear only server validation errors
        this.resetFormControls(undefined, {
          clearValues: false,
          clearErrors: true,
          onlyServerErrors: true
        });

        // Reload company list and other dependent data
        this.getCompanyList(undefined, this.selectedCompany.value).then(() => {
          this.reloadSelectOptionsIfHasValue();
        });

        // Reload action list
        this.setActionPicklistValues(this.employeeGroup).then();

        // reload picklist values
        this.getPicklistValues();

        // Reset flag after all updates with a slight delay
        setTimeout(() => {
          this.isEffectiveDateChanging = false;
        }, 100);
      },
      nzOnCancel: () => {
        // Revert back to the previous date without triggering events
        this.jobDataForm.patchValue({
          effectiveDateFrom: previousDate
        }, { emitEvent: false });

        // Reset flag
        this.isEffectiveDateChanging = false;
      }
    });
  }

  /**
   * Converts date range to Vietnamese formatted duration string by calculating the exact difference
   * @param startDate Start date of the period
   * @param endDate End date of the period
   * @returns Formatted string like "X năm Y tháng Z ngày (N ngày)"
   */
  private convertDaysToVietnameseDuration(startDate: Date | string, endDate: Date | string): string {
    // Convert to moment objects if they are strings
    const start = moment(startDate);
    const end = moment(endDate);

    // Calculate total days (including both start and end dates)
    const totalDays = end.diff(start, 'days') + 1;

    // Calculate the duration
    const duration = moment.duration(end.diff(start)).add(1, 'days');

    // Extract years, months and days
    const years = duration.years();
    const months = duration.months();
    const days = duration.days();

    return `${years} năm ${months} tháng ${days} ngày (${totalDays} ngày)`;
  }

  // Reusable function to update selected names for a multi-select field
  private updateSelectedNames(fieldName: string, stateKey: string): void {
    const selectedValues: string[] = this.jobDataForm.get(fieldName)?.value || [];
    const optionList = this.selectManager.getState(fieldName)?.data || [];
    const names = selectedValues
      .map(value => optionList.find((item: any) => item.value === value)?.label)
      .filter((label: string | undefined): label is string => !!label);
    switch (stateKey) {
      case 'selectedMatrixReportPositionNames':
        this.selectedMatrixReportPositionNames = names;
        break;
      case 'selectedMatrixManagerNames':
        this.selectedMatrixManagerNames = names;
        break;
    }
  }
}