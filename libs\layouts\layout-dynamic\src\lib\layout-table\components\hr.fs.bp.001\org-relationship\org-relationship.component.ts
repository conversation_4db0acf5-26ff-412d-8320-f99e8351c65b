import { Component, ElementRef, EventEmitter, inject, OnInit, Output, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormArray, FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzButtonComponent, NzButtonModule } from 'ng-zorro-antd/button';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzRadioModule } from 'ng-zorro-antd/radio';
import { NzGridModule } from 'ng-zorro-antd/grid';
import { NzModalService } from 'ng-zorro-antd/modal';
import { JobDataComponent } from '../job-data/job-data.component';
import { ButtonComponent, IconComponent, ModalComponent, ToastMessageComponent, TooltipComponent, TooltipPosition, TooltipTrigger } from '@hrdx/hrdx-design';
import { DialogSize, HrFsBp001Service } from '../services/hr.fs.bp.001.service';
import { catchError, finalize, map, Observable, of } from 'rxjs';
import { SelectManager } from '../services/select-manager.service';

@Component({
  selector: 'lib-org-relationship',
  standalone: true,
  imports: [CommonModule,
    ReactiveFormsModule,
    NzFormModule,
    NzSelectModule,
    NzRadioModule, 
    NzInputModule, IconComponent, TooltipComponent,
    NzCheckboxModule, ButtonComponent,
    NzButtonModule, NzGridModule],
  templateUrl: './org-relationship.component.html',
  styleUrl: './org-relationship.component.less',
  providers: [ModalComponent, ToastMessageComponent, SelectManager]
})
export class OrgRelationshipComponent implements OnInit {
  @Output() formReady = new EventEmitter<void>();
  @Output() formValid = new EventEmitter<boolean>();

  @Output() jobDataEffectiveDateChanged = new EventEmitter<Date>();



  orgRelationshipForm!: FormGroup;
  modalService: NzModalService = inject(NzModalService);
  showValidationMessage: boolean = false;
  
  jobData: any = {};

  isShowWarning: boolean = false;

  // check if job data is not empty
  canUpdateJobDataEffectiveDate: boolean = false;

  readonly tooltipConfig = {
    trigger: TooltipTrigger.Hover,
    position: TooltipPosition.TopCenter,
    arrow: true,
  } as const;

  private personId: string = '';  
  setPersonId(personId: string): void {
    this.personId = personId;
  }

  fullName: string = '';
  setFullName(fullName: string): void {
    this.fullName = fullName;
  }

  // define employee group list
  employeeGroupList: any[] = [
    { label: 'Employee', value: 'EMP', checklistCode: 'Thêm mới nhân viên' },
    { label: 'Contingent Worker', value: 'CWR', checklistCode: ' Thêm mới nhân viên dự bị' },
    { label: 'Person of Interest', value: 'POI', checklistCode: 'Thêm mới mối quan hệ POI' }
  ];

  // define employee group and sub group information
  employeeGroupAndSubGroupInfo: any = {};

  isDisableButton: boolean = false;
  
  isJobDataFormValid: boolean = false;

  bioEffectiveDate: Date | null = null;

  // min effective date
  minEffectiveDate: Date | null = null;

  // ref to modal component
  modalComponent = inject(ModalComponent);

  // ref to job indicator list
  jobIndicatorList: any[] = [];

  constructor(private fb: FormBuilder) {}

  ngOnInit(): void {
    const defaultEmployeeGroup = "EMP";
    const defaultChecklistCode = this.employeeGroupList.find(group => group.value === defaultEmployeeGroup)?.checklistCode;

    this.orgRelationshipForm = this.fb.group({
      employeeGroupCode: [defaultEmployeeGroup, Validators.required],
      employeeSubGroupCode: [null, Validators.required],
      employeeRecordNumber: [{value: 0, disabled: true}],
      withoutJob: [{value: false, disabled: true}],
      company: [null, Validators.required],
      checklistCode: [{value: defaultChecklistCode, disabled: true}]
    });

    
    
    this.formReady.emit();
    
    this.orgRelationshipForm.statusChanges.subscribe(() => {
      // only when employee group is POI, the employee sub group has value, the without job is true, the company is selected, then emit the form is valid
      this.updateFormValidity();
    });

    // update employee group label
    this.updateEmployeeGroupLabel();

    // get the selected company information
    this.selectedCompany = this.companyList.find(company => company.value === this.orgRelationshipForm.value.company);

    // register selects
    this.registerSelects();
  }

  // #region "Effective Date"
  setEffectiveDate(effectiveDate: Date): void {
    // if bioEffectiveDate is null then set it to effectiveDate, --> this is the first time to set effective date
    if (!this.bioEffectiveDate) {
      this.bioEffectiveDate = effectiveDate;
    } else if (this.bioEffectiveDate.getTime() !== effectiveDate.getTime() || this.canUpdateJobDataEffectiveDate) {
      // if change the effective date, then reset the company list and employee sub group if they are selected before
      this.companyList = [];
      this.pageIndex = 1; 
      // reset employee sub group list
      this.employeeSubGroupList = [];
      this.fullEmployeeSubGroupList = [];

      // this.orgRelationshipForm.patchValue({
      //   company: null,
      //   employeeSubGroupCode: null
      // });

      // update effective date
      this.bioEffectiveDate = effectiveDate;
      
      // reload company
      this.loadMoreCompanies();
      
      // reload employee sub group based on current employee group
      const currentEmployeeGroup = this.orgRelationshipForm.get('employeeGroupCode')?.value;
      if (currentEmployeeGroup) {
        this.getEmployeeSubGroupList(currentEmployeeGroup);
      }
    }
  }

  getEffectiveDate(): Date | null {
    return this.bioEffectiveDate;
  }

  // set min effective date
  setMinEffectiveDate(minEffectiveDate: Date): void {
    this.minEffectiveDate = minEffectiveDate;
  }

  getMinEffectiveDate(): Date | null {
    return this.minEffectiveDate;
  }

  // check if jobData is empty
  isJobDataEmpty(): boolean {
    // if jobData is undefined or null, return true
    if (!this.jobData) {
      return true;
    }

    // check if any property in jobData has a value
    return Object.keys(this.jobData).length === 0 || 
           Object.values(this.jobData).every(value => 
             value === null || value === undefined || value === '' || 
             (Array.isArray(value) && value.length === 0) ||
             (typeof value === 'object' && Object.keys(value).length === 0)
           );
  }

  // #endregion "Effective Date"
  
  
  getFormValue(): any {
    return {
      orgRelationship: this.orgRelationshipForm.value,
      jobData: this.jobData
    }
  }

  isFormValid(): boolean {
    return this.orgRelationshipForm.valid;
  }

  addRelationships(): void {

    // open job data as a modal
    this.openJobDataModal();
  }

  selectedCompany: any = null;

  openJobDataModal(): void {
    // update employee sub group list to employeeGroupAndSubGroupInfo
    this.getSelectedEmployeeGroupAndSubGroup();

    // if jobdata is not null and can update job data effective date is true then update the effective date in job data
    if (this.jobData && this.canUpdateJobDataEffectiveDate) {
      this.jobData.effectiveDateFrom = this.getEffectiveDate();
    }

    // set employee group code to job data
    this.jobData.employeeGroupCode = this.orgRelationshipForm.value.employeeGroupCode;

    // send this.jobData to modal 
    const modalRef = this.modalService.create({
      nzContent: JobDataComponent,
      nzTitle: '',
      nzFooter: null,
      nzWidth: DialogSize.EXTRA_LARGE,
      nzCentered: true,
      nzData: {
        jobData: this.jobData,
        employeeGroupAndSubGroupInfo: this.employeeGroupAndSubGroupInfo,
        effectiveDate: this.getEffectiveDate(),
        minEffectiveDate: this.getMinEffectiveDate(),
        selectedCompany: this.selectedCompany,
        personId: this.personId,
        fullName: this.fullName
      },
      nzMaskClosable: false,
      nzOnCancel: () => {
        const instance = modalRef.getContentComponent();
        instance.cancel();
        return false; // P
      }
    });

    modalRef.afterOpen.subscribe(() => {
      const instance = modalRef.getContentComponent();
      modalRef.updateConfig({
        nzFooter: instance.modalFooter,
      });
    });

    // get data from modal
    modalRef.afterClose.subscribe((data) => {
      if (data) {
        // Check if effective date has changed
        const newEffectiveDate = data.jobData.effectiveDateFrom;

        // update job data to the main form
        this.jobData = data.jobData;
        // Persist matrixReportPositionNames if present
        if (data.matrixReportPositionNames) {
          this.jobData.matrixReportPositionNames = data.matrixReportPositionNames;
        }

        // Persist matrixManagerNames if present
        if (data.matrixManagerNames) {
          this.jobData.matrixManagerNames = data.matrixManagerNames;
        }

        // update isJobDataFormValid to the main form
        this.isJobDataFormValid = data.isJobDataFormValid;

        // Hide validation message if form becomes valid
        if (this.isJobDataFormValid && this.orgRelationshipForm.valid) {
          this.showValidationMessage = false;
        }

        // if effective date changed, trigger company search
        if (this.getEffectiveDate()!.getTime() !== newEffectiveDate.getTime()) {
          // update the effective date
          this.bioEffectiveDate = newEffectiveDate;

          // Get the current search value from the select manager state
          const companyState = this.selectManager.getState('company');
          const lastSearchValue = companyState?.searchValue || '';

          // reset the company list using select manager with the last search condition
          this.selectManager.search('company', lastSearchValue, {}).subscribe();

          // reload employee sub group list with current value
          const employeeGroup = this.orgRelationshipForm.get('employeeGroupCode')?.value;
          if (employeeGroup) {
            const currentSubGroupCode = this.orgRelationshipForm.get('employeeSubGroupCode')?.value;
            this.getEmployeeSubGroupList(employeeGroup, currentSubGroupCode);
          }
        }

        // check if employee sub group is changed in job data modal
        const newEmployeeSubGroupCode = data.jobData.employeeSubGroupCode;
        if (newEmployeeSubGroupCode && newEmployeeSubGroupCode !== this.orgRelationshipForm.value.employeeSubGroupCode) {
          // update employee sub group code
          this.orgRelationshipForm.get('employeeSubGroupCode')?.setValue(newEmployeeSubGroupCode);
        }

        // show warning if job data is not empty
        this.isShowWarning = !this.isJobDataEmpty();

        // check if job data is not empty
        if (this.isJobDataNotEmpty(data.jobIndicatorList, data.managerList)) {
          // emit event
          this.jobDataEffectiveDateChanged.emit(newEffectiveDate);

          // can not update job data effective date
          this.canUpdateJobDataEffectiveDate = false;
        } else {
          // job data is empty, so we can update its effective date
          this.canUpdateJobDataEffectiveDate = true;
        }

        // update form validity
        this.updateFormValidity();
      }
      
    });
  }

  updateFormValidity(): void {
    // check if employee group is POI, without job is true, company is selected, and employee sub group is selected
    if (this.orgRelationshipForm.value.employeeGroupCode === "POI" && 
        this.orgRelationshipForm.value.withoutJob) {
      // When POI and withoutJob is true, form should be valid regardless of company and employeeSubGroup
      this.formValid.emit(true);
    } else {
      const isOrgRelationshipFormValid = this.isFormValid();
      const isOverallFormValid = isOrgRelationshipFormValid && this.isJobDataFormValid;
      this.formValid.emit(isOverallFormValid);
    }
  }

  // employeeSubGroupList
  employeeSubGroupList: any[] = [];
  hrFsBp001Service: HrFsBp001Service = inject(HrFsBp001Service);

  // handle when employee group is changed
  onEmployeeGroupChange(employeeGroup: string) {
    this.getEmployeeSubGroupList(employeeGroup);
  
    // get checklist code
    const checklistCode = this.employeeGroupList.find(group => group.value === employeeGroup)?.checklistCode;
    
    if (employeeGroup === "POI") {
      // // First disable and clear validators before setting values
      // // This prevents validation from triggering during the transition
      // this.orgRelationshipForm.get('employeeSubGroupCode')?.disable();
      // this.orgRelationshipForm.get('company')?.disable();
      
      // // clear validators
      // this.orgRelationshipForm.get('company')?.clearValidators();
      // this.orgRelationshipForm.get('employeeSubGroupCode')?.clearValidators();

      // // Update validators immediately
      // this.orgRelationshipForm.get('company')?.updateValueAndValidity({ emitEvent: false });
      // this.orgRelationshipForm.get('employeeSubGroupCode')?.updateValueAndValidity({ emitEvent: false });

      // // enable without job and set values
      // this.orgRelationshipForm.get('withoutJob')?.enable();
      
      // set all values at once to minimize form updates
      this.orgRelationshipForm.patchValue({
        checklistCode: checklistCode,
        withoutJob: true,
        employeeRecordNumber: null,
        employeeSubGroupCode: null,
        company: null
      }, { emitEvent: false });

      // // disable the add relationships button
      // this.isDisableButton = true;

      // hide the isShowWarning
      this.isShowWarning = false;
    } else {
      // // For EMP and CWR cases
      // // First enable fields and set validators
      // this.orgRelationshipForm.get('employeeSubGroupCode')?.enable();
      // this.orgRelationshipForm.get('company')?.enable();
      
      // // Set validators
      // this.orgRelationshipForm.get('company')?.setValidators(Validators.required);
      // this.orgRelationshipForm.get('employeeSubGroupCode')?.setValidators(Validators.required);

      // // Update validators
      // this.orgRelationshipForm.get('company')?.updateValueAndValidity({ emitEvent: false });
      // this.orgRelationshipForm.get('employeeSubGroupCode')?.updateValueAndValidity({ emitEvent: false });

      // Then set values
      this.orgRelationshipForm.patchValue({
        checklistCode: checklistCode,
        employeeRecordNumber: 0,
        withoutJob: false,
        employeeSubGroupCode: null
      }, { emitEvent: false });

      // this.orgRelationshipForm.get('withoutJob')?.disable();

      // enable the button
      // this.isDisableButton = false;

      // show the isShowWarning based on the job data
      this.isShowWarning = this.isJobDataEmpty();
    }

    // Finally, update form validity once all changes are complete
    this.updateFormValidity();
  }

  // handle when without job is changed
  onWithoutJobChange(withoutJob: boolean) {
    const isPOI = this.orgRelationshipForm.value.employeeGroupCode === "POI";

    if (withoutJob) {
      // Clear values
      this.orgRelationshipForm.patchValue({
        employeeRecordNumber: isPOI ? null : this.orgRelationshipForm.value.employeeRecordNumber,
        employeeSubGroupCode: null,
        company: null
      });

      // disable fields when withoutJob is true
      this.orgRelationshipForm.get('employeeSubGroupCode')?.disable();
      this.orgRelationshipForm.get('company')?.disable();
      
      // clear validators
      this.orgRelationshipForm.get('company')?.clearValidators();
      this.orgRelationshipForm.get('employeeSubGroupCode')?.clearValidators();

      // disable the add relationships button
      this.isDisableButton = true;

      // hide the isShowWarning
      this.isShowWarning = false;
    } else {
      // here without job is uncheck, enable the company and employee sub group
      this.orgRelationshipForm.get('company')?.enable();
      this.orgRelationshipForm.get('employeeSubGroupCode')?.enable();

      // Set required validators back
      this.orgRelationshipForm.get('company')?.setValidators(Validators.required);
      this.orgRelationshipForm.get('employeeSubGroupCode')?.setValidators(Validators.required);

      // enable the add relationships button
      this.isDisableButton = false;

      // show the isShowWarning based on the job data
      this.isShowWarning = this.isJobDataEmpty();
    }

    // Update validators
    this.orgRelationshipForm.get('company')?.updateValueAndValidity();
    this.orgRelationshipForm.get('employeeSubGroupCode')?.updateValueAndValidity();

    // Update overall form validity
    this.updateFormValidity();
  }

  onEmployeeSubGroupChange(employeeSubGroup: string) {
    this.getSelectedEmployeeGroupAndSubGroup();
  }

  // make a function to get selected employee group and employee sub group
  getSelectedEmployeeGroupAndSubGroup(): void {
    const employeeGroup = this.orgRelationshipForm.value.employeeGroupCode;
    const employeeSubGroup = this.orgRelationshipForm.value.employeeSubGroupCode;

    // get employee group name and employee sub group name
    const employeeGroupName = this.employeeGroupList.find(group => group.value === employeeGroup)?.label;
    const employeeSubGroupName = this.employeeSubGroupList.find(subGroup => subGroup.value === employeeSubGroup)?.label;

    // store those values to a variable and then send to job data modal
    this.employeeGroupAndSubGroupInfo = {
      employeeGroupCode: employeeGroup,
      employeeGroupName: employeeGroupName,
      employeeSubGroupCode: employeeSubGroup,
      employeeSubGroupName: employeeSubGroupName,
      employeeSubGroupList: this.employeeSubGroupList
    };
  }

  //#region "Company"

  // company list
  companyList: any[] = [];
  isNZSelectLoading: boolean = false;
  pageIndex: number = 1;
  pageSize: number = 25;


  // get company list
  getCompanyList(searchValue: string = '', companyCode: string = ''): Observable<any[]> {
    // get effective date
    const effectiveDate = this.getEffectiveDate();

    return this.hrFsBp001Service.getCompanyList(searchValue, companyCode, effectiveDate?.toString(), this.pageIndex, this.pageSize).pipe(
      catchError(() => of([]))
    );
  }

  // load more company
  loadMoreCompanies(searchValue: string = '', companyCode?: string, reloadCompany: boolean = false): void {
    if (!this.isNZSelectLoading) {
      this.isNZSelectLoading = true;
      this.getCompanyList(searchValue, companyCode).pipe(
        finalize(() => this.isNZSelectLoading = false)
      ).subscribe(companies => {
        // if not reload company 
        if (!reloadCompany) {
          this.companyList = [...this.companyList, ...companies];
        } else {
          this.companyList = companies;
        }
        this.pageIndex++;

        // if company list is empty, then reset the company code
        if (this.companyList.length === 0 && reloadCompany) {
          this.orgRelationshipForm.patchValue({
            company: null
          });
        }

        // Update selectedCompany label if it exists in the new company list
        if (this.selectedCompany) {
          const updatedCompany = companies.find(company => company.value === this.selectedCompany.value);
          if (updatedCompany) {
            this.selectedCompany = updatedCompany;
          }
        }
      });
    }
  }

  // on company search 
  onCompanySearch(searchValue: string): void {
    // Skip if we're in the middle of selection
    if (this.isSelecting) {
      return;
    }
    this.pageIndex = 1;
    this.companyList = [];
    this.lastSearchTerm = searchValue;
    this.loadMoreCompanies(searchValue);
  }

  // handle when company dropdown is open or close
  onCompanyOpenChange(open: boolean) {
    if (open) {
      // Store the current value before any changes
      this.previousCompanyValue = this.orgRelationshipForm.get('company')?.value;
      
      if (this.companyList.length === 0) {
        // Refresh the company list if it's empty when the select is opened
        this.loadMoreCompanies();
      }
    }
  }

  // track search state
  private lastSearchTerm = '';
  private isSelecting = false;

  

  private isReverting: boolean = false;
  private previousCompanyValue: any = null;
  private previousSearchTerm = ''; // for reverting company selection

  
  // Add selection handler
  onCompanySelect(value: any): void {
    // skip if in the middle of reverting
    if (this.isReverting) {
      this.isReverting = false;
      return;
    }

    // Update selected company for job data modal
    const selectedCompany = this.companyList.find(company => company.value === value);

    // check if it's the first section and has related assignment data
    if (this.previousCompanyValue  && this.hasAssignmentData()) {
      // show confirm modal
      this.modalComponent.showDialog({
        nzTitle: 'Warning',
        nzContent: 'When selecting a different company, related department Information will be automatically cleared.',
        nzWrapClassName: 'popup popup-confirm existing-data-confirm',
        nzIconType: 'icons:warning',
        nzOkText: 'Confirm',
        nzOnOk: () => {
          this.clearAssignmentData();

          // Store current values before handling change
          this.previousCompanyValue = this.orgRelationshipForm.get('company')?.value;
          this.previousSearchTerm = String(this.lastSearchTerm || '');
          
          
          this.selectedCompany = selectedCompany;

          this.handleCompanyChange(value);
        },
        nzOnCancel: () => {
          // Reset company list and revert selection
          this.isReverting = true;
          this.pageIndex = 1;
          this.companyList = [];
          
          // Make new API call with the last search term
          this.getCompanyList(this.previousSearchTerm).pipe(
            finalize(() => {
              this.orgRelationshipForm.patchValue({
                company: this.previousCompanyValue
              }, { emitEvent: false });
              this.isReverting = false;
              // handle company change
              this.handleCompanyChange(this.previousCompanyValue);
            })
          ).subscribe(companies => {
            this.companyList = companies;
          });
        }
      });
      return;
    }
    this.previousSearchTerm = String(this.lastSearchTerm || '');

    // Update selected company with the stored value
    this.selectedCompany = selectedCompany;

    // handle company change
    this.handleCompanyChange(value);
  }

  
  //#endregion "Company"

  // disable form
  disableForm(): void {
    this.orgRelationshipForm.disable();
    // disable the add relationships button
    this.isDisableButton = true;
  }

  // get employee group list and update name to the employee group list
  updateEmployeeGroupLabel(): void {
    this.hrFsBp001Service.getEmployeeGroup().subscribe((empGroupList) => {
      // if empGroupList is not empty, 
      if (empGroupList.length > 0) {
        // update label if each item in employeeGroupList that has code is in empGroupList, then update the label to the name of the item in empGroupList
        this.employeeGroupList.forEach((employeeGroup) => {
          const code = employeeGroup.value;
          const label = empGroupList.find(item => item.value === code)?.label;
          if (label) {
            employeeGroup.label = label;
          }
        });

        
      }
    });
  }

  // a function that check job data form, employment sections has valu or not
  private hasAssignmentData(): boolean {
    // List of assignment section fields to check
    const assignmentFields = [
      'positionCode',
      'legalEntityCode',
      'businessUnitCode',
      'divisionCode',
      'departmentCode',
      'locationCode',
      'jobCode',
      'businessTitleCode',
      'careerStreamCode',
      'careerBandCode',
      'costCenterCode',
      'reportPosition',
      'supervisor',
      'matrixReportPositionCode',
      'matrixManagers'
    ];
  
    return assignmentFields.some(field => 
      this.jobData[field] !== null && 
      this.jobData[field] !== undefined && 
      this.jobData[field] !== ''
    );
  }

  // a function that check if job data is not empty
  private isJobDataNotEmpty(jobIndicatorList: any[], managerList: any[]): boolean {
    // check if date fields are not default
    const dateFields = [
      'effectiveDateFrom',
      'departmentEntryDate',
      'groupOriginalStartDate',
      'oirOriginalStartDate'
    ];

    // check if any date field is not equal to getEffectiveDate
    const isDateFieldNotDefault = dateFields.some(field => this.jobData[field] !== this.getEffectiveDate());
    
    // check if jobIndicatorCode is not equal to the first item of jobIndicatorList
    const isJobIndicatorCodeNotDefault = jobIndicatorList && jobIndicatorList.length > 0 && this.jobData.jobIndicatorCode !== jobIndicatorList[0].value;

    // check if manager is not the same as the first item of managerList
    const isManagerNotDefault = managerList && managerList.length > 0 && this.jobData.isManagerCode !== managerList[0].value;

    // check if fte is not 1.00000
    const isFteNotDefault = this.jobData.fte !== 1.00000;

    // check if employeeSubGroupCode is not the same as the selected employee sub group in the form
    const isEmployeeSubGroupCodeNotDefault = this.orgRelationshipForm.value.employeeSubGroupCode !== this.jobData.employeeSubGroupCode;


    // list of other fields to check
    const otherFields = [
      'actionCode',
      'prStatusCode',
      'levelDecisionCode',
      'decisionNumber',
      'signDate',
      'expectedEndDate',
      'note',
      'positionCode',
      'costCenterCode',
      'reportPosition',
      'legalEntityCode',
      'supervisor',
      'businessUnitCode',
      'matrixReportPositionCode',
      'divisionCode',
      'matrixManagers',
      'departmentCode',
      'locationCode',
      'regionCode',
      'fullPartCode',
      'jobCode',
      'businessTitleCode',
      'timeZoneCode',
      'careerStreamCode',
      'careerBandCode',
      'empLevelCode',
      'employmentNote'
    ];

    // check if other fields are not 
    const isOtherFieldsNotDefault = otherFields.some(field => {
      const value = this.jobData[field];
      if (Array.isArray(value)) {
        return value.length > 0;
      }
      return value !== null && value !== undefined && value !== '';
    });

    return isOtherFieldsNotDefault || isDateFieldNotDefault || isJobIndicatorCodeNotDefault || isFteNotDefault || isEmployeeSubGroupCodeNotDefault || isManagerNotDefault;
  }

  private clearAssignmentData(): void {
    // List of fields to clear
    const assignmentFields = [
      'positionCode',
      'legalEntityCode',
      'businessUnitCode',
      'divisionCode',
      'departmentCode',
      'locationCode',
      'jobCode',
      'businessTitleCode',
      'careerStreamCode',
      'careerBandCode',
      'costCenterCode',
      'reportPosition',
      'supervisor',
      'matrixReportPositionCode',
      'matrixManagers'
    ];
  
    // Clear only assignment section fields
    assignmentFields.forEach(field => {
      this.jobData[field] = null;
    });
  }

  private handleCompanyChange(value: any): void {
    this.isSelecting = true;
    setTimeout(() => {
      this.isSelecting = false;
      this.lastSearchTerm = '';
    }, 0);
  }


  validateFormWithMessage(): { isValid: boolean } {
    // Mark all fields as touched to show validation messages
    this.markAllFieldsAsTouched(this.orgRelationshipForm);

    // when without job is check and POI, then skip the job data 
    if (this.orgRelationshipForm.value.withoutJob === true && this.orgRelationshipForm.value.employeeGroupCode === 'POI') {
      this.isJobDataFormValid = true;
    } else {
      // Show validation message if job data is invalid
      this.showValidationMessage = !this.isJobDataFormValid;
    }
    
    // Consider both main form and job data form validity
    const isValid = this.orgRelationshipForm.valid && this.isJobDataFormValid;
    
    return {
      isValid: isValid
    };
  }
  
  private markAllFieldsAsTouched(formGroup: FormGroup): void {
    Object.values(formGroup.controls).forEach(control => {
      if (control instanceof FormGroup) {
        this.markAllFieldsAsTouched(control);
      } else if (control instanceof FormArray) {
        control.controls.forEach(arrayControl => {
          if (arrayControl instanceof FormGroup) {
            this.markAllFieldsAsTouched(arrayControl);
          } else {
            arrayControl.markAsTouched();
            arrayControl.updateValueAndValidity();
          }
        });
      }
      control.markAsTouched();
      control.updateValueAndValidity();
    });
  }


  //#region "New company infinite scroll"
  selectManager = inject(SelectManager);
  private readonly SEARCH_DEBOUNCE = 300;
  private searchDebounceTimer: any;


  private registerSelects(): void {
    // Register company select
    this.selectManager.registerSelect({
      fieldName: 'company',
      serviceMethod: (params) => this.hrFsBp001Service.getCompanyListByConditions(
        params.searchValue,
        this.getEffectiveDate()?.toString(),
        params.page,
        params.pageSize
      ),
      transformResponse: (response: any) => {
        return response.map((item: any) => ({
          value: item.code,
          label: (item.longName?.default ?? item.name?.default) ?? '',
          id: item.id
        }));
      }
    });
  }

  onSelectScroll(fieldName: string): void {
    const filters = this.getFiltersForField(fieldName);
    this.selectManager.loadMore(fieldName, filters).subscribe(data => {
      // Update your component's data array if needed
    });
  }

  private getFiltersForField(fieldName: string): Record<string, any> {
    const filters: Record<string, any> = {
      effectiveDate: this.getEffectiveDate()
    };

    // Add field-specific filters
    switch (fieldName) {
      case 'company':
        // Add any company-specific filters
        break;
      // Add other field-specific filters
    }

    return filters;
  }

  private showCompanyChangeWarning(value: any, selectedCompany: any): void {
    this.modalComponent.showDialog({
      nzTitle: 'Warning',
      nzContent: 'When selecting a different company, related department Information will be automatically cleared.',
      nzWrapClassName: 'popup popup-confirm existing-data-confirm',
      nzIconType: 'icons:warning',
      nzOkText: 'Confirm',
      nzOnOk: () => {
        this.clearAssignmentData();
        this.updateCompanySelection(value, selectedCompany);
      },
      nzOnCancel: () => {
        this.revertCompanySelection();
      }
    });
  }

  private updateCompanySelection(value: any, selectedCompany: any): void {
    this.previousCompanyValue = value;
    this.selectedCompany = selectedCompany;
    this.handleCompanyChange(value);
  }

  private revertCompanySelection(): void {
    this.isReverting = true;
    this.orgRelationshipForm.patchValue({
      company: this.previousCompanyValue
    }, { emitEvent: false });
  }

  ngOnDestroy(): void {
    if (this.searchDebounceTimer) {
      clearTimeout(this.searchDebounceTimer);
    }
  }

  onSelectChange(fieldName: string): void {
    if (fieldName === 'company') {
      const value = this.orgRelationshipForm.get('company')?.value;
      
      // Skip if in the middle of reverting
      if (this.isReverting) {
        this.isReverting = false;
        return;
      }

      // get selected company from select manager
      const selectedCompany = this.selectManager.getState('company')?.data.find(company => company.value === value);

      // Check if it's the first selection and has related assignment data
      if (this.previousCompanyValue && this.hasAssignmentData()) {
        this.showCompanyChangeWarning(value, selectedCompany);
        return;
      }

      this.updateCompanySelection(value, selectedCompany);
    }

    this.selectManager.onSelectChange(fieldName);
  }

  onSelectSearch(fieldName: string, searchValue: string): void {
    // Skip empty searches during selection
    if (searchValue === '' && this.selectManager.isSelecting.get(fieldName)) {
      return;
    }

    // Debounce the search
    if (this.searchDebounceTimer) {
      clearTimeout(this.searchDebounceTimer);
    }

    this.searchDebounceTimer = setTimeout(() => {
      const filters = this.getFiltersForField(fieldName);
      this.selectManager.search(fieldName, searchValue, filters).subscribe();
    }, this.SEARCH_DEBOUNCE);
  }

  onSelectClear(control: string): void {
    this.selectManager.clear(control);
    // Reset the form control value
    this.orgRelationshipForm.get(control)?.reset();
  }

  onCompanyChange(value: any): void {
    if (value === null) {
      // Handle clear
      // this.selectManager.clear('company');
      // this.orgRelationshipForm.patchValue({
      //   company: null
      // });
    } else {
      // Handle normal selection
      this.onSelectChange('company');
    }
  }

  onSelectOpen(open: boolean, control: string): void {
    if (open && !this.selectManager.getState(control)?.data?.length) {
      // If opening the dropdown and no data exists, perform initial search
      const filters = this.getFiltersForField(control);
      this.selectManager.search(control, '', filters).subscribe();
    }
  }


  //#endregion "New company infinite scroll"

  
  //#region "Employee Sub Group"
  private fullEmployeeSubGroupList: any[] = [];
  isLoadingSubGroups = false;

  // Update the existing getEmployeeSubGroupList method
  getEmployeeSubGroupList(employeeGroup: string, preserveValue?: string) {
    this.isLoadingSubGroups = true;
    
    this.hrFsBp001Service.getEmployeeSubGroupList(
      employeeGroup, 
      this.getEffectiveDate()?.toString()
    ).pipe(
      finalize(() => this.isLoadingSubGroups = false)
    ).subscribe((data) => {
      // Store full list
      this.fullEmployeeSubGroupList = data;
      // Display full list initially
      this.employeeSubGroupList = data;
      
      // if (this.employeeSubGroupList.length > 0) {
      //   if (preserveValue) {
      //     // Check if preserved value exists in new list
      //     const valueExists = this.employeeSubGroupList.some(
      //       item => item.code === preserveValue
      //     );
      //     // Use preserved value if it exists, otherwise use first item
      //     this.orgRelationshipForm.patchValue({
      //       employeeSubGroupCode: valueExists ? preserveValue : this.employeeSubGroupList[0].code
      //     });
      //   } else {
      //     // No value to preserve, use first item
      //     this.orgRelationshipForm.patchValue({
      //       employeeSubGroupCode: this.employeeSubGroupList[0].code
      //     });
      //   }
      // } 
      // else {
      //   // Empty list, clear selection
      //   this.orgRelationshipForm.patchValue({
      //     employeeSubGroupCode: null
      //   });
      // }
    });
  }

  // Add client-side search method
  searchEmployeeSubGroups(searchTerm: string): void {
    if (!searchTerm) {
      this.employeeSubGroupList = [...this.fullEmployeeSubGroupList];
      return;
    }

    searchTerm = searchTerm.toLowerCase();
    this.employeeSubGroupList = this.fullEmployeeSubGroupList.filter(item => 
      item.label.toLowerCase().includes(searchTerm) || 
      item.code.toLowerCase().includes(searchTerm)
    );
  }

  onEmployeeSubGroupOpenChange(open: boolean): void {
    if (open && this.fullEmployeeSubGroupList.length === 0) {
      const employeeGroup = this.orgRelationshipForm.get('employeeGroupCode')?.value;
      if (employeeGroup) {
        this.getEmployeeSubGroupList(employeeGroup);
      }
    }
  }

  //#endregion "Employee Sub Group"
}
