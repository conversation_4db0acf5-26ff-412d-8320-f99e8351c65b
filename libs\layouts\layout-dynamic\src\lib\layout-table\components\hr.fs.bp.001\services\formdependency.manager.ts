import { FormGroup } from "@angular/forms";
import { JobDataComponent } from "../job-data/job-data.component";
import { firstValueFrom } from "rxjs";
import { SelectManager } from "./select-manager.service";
import { switchMap, map, of } from "rxjs";

interface FieldDependency {
    dependencies: string[];
    canLoadIndependently: boolean;
    clearFields?: string[];
    clearOptions?: string[];
    loadAction?: (value: any) => Promise<void>;
    clearAction?: () => Promise<void>;
    preserveOnClear?: boolean;
}

export class FormDependencyManager {
    private isHandlingChange = false;
    private isValidating = false;

    private readonly fieldRelationships: Record<string, FieldDependency> = {
        positionCode: {
            dependencies: ['departmentCode', 'locationCode', 'costCenterCode', 'jobCode', 'reportPosition', 'matrixReportPositionCode'],
            canLoadIndependently: false,
            preserveOnClear: true, // When position is cleared, preserve other values
            loadAction: this.handlePositionChange.bind(this)
        },
        departmentCode: {
            dependencies: ['legalEntityCode', 'businessUnitCode', 'divisionCode', 'locationCode', 'costCenterCode'],
            canLoadIndependently: true,
            clearFields: ['positionCode'],
            loadAction: this.handleDepartmentChange.bind(this),
            clearAction: async () => {
                // reload department list
                this.selectManager.search('departmentCode', ' ').subscribe();
            }
        },
        legalEntityCode: {
            dependencies: ['departmentCode', 'businessUnitCode', 'divisionCode'],
            canLoadIndependently: true,
            clearFields: ['departmentCode', 'positionCode'],
            loadAction: this.handleLegalEntityChange.bind(this),
            clearAction: async () => {
                // reload position list
                this.selectManager.search('positionCode', ' ').subscribe();
                // reload department list
                this.selectManager.search('departmentCode', ' ').subscribe();
                // reload legal entity list
                this.selectManager.search('legalEntityCode', ' ').subscribe();
            }
        },
        businessUnitCode: {
            dependencies: ['departmentCode', 'divisionCode'],
            canLoadIndependently: true,
            clearFields: ['departmentCode', 'divisionCode', 'positionCode'],
            clearOptions: ['divisionCode'],
            loadAction: this.handleBusinessUnitChange.bind(this),
            clearAction: async () => {
                // Load all picklists in parallel
                await Promise.all([
                    this.component.setDivisionPicklistValues(),
                    
                    this.selectManager.search('departmentCode', ' ').subscribe(),
                    this.selectManager.search('businessUnitCode', ' ').subscribe()
                ]);
            }
        },
        jobCode: {
            dependencies: ['careerStreamCode', 'careerBandCode'],
            canLoadIndependently: true,
            clearFields: ['positionCode'],
            loadAction: this.handleJobChange.bind(this),
            clearAction: async () => {
                // filter position list
                this.selectManager.search('positionCode', ' ').subscribe();
            }
        },
        careerStreamCode: {
            dependencies: ['careerBandCode'],
            canLoadIndependently: true,
            clearFields: ['careerBandCode'],
            loadAction: this.handleCareerStreamChange.bind(this),
            clearAction: async () => {
                // reload career band with select manager
                this.selectManager.search('careerBandCode', ' ').subscribe();
            }
        },
        divisionCode: {
            dependencies: [],
            canLoadIndependently: true,
            clearFields: ['departmentCode'],
            loadAction: this.handleDivisionChange.bind(this),
            clearAction: async () => {
                // reload department list
                this.selectManager.search('departmentCode', ' ').subscribe();
            }
        },
        careerBandCode: {
            dependencies: [],
            canLoadIndependently: true,
            clearFields: [],
            loadAction: this.handleCareerBandChange.bind(this)
        }
    };

    constructor(
        private form: FormGroup,
        private component: JobDataComponent,
        private selectManager: SelectManager,
        private callbacks: {
            onBeforeChange?: () => void;
            onAfterChange?: () => void;
        } = {}
    ) {
        this.setupFieldSubscriptions();
    }

    private setupFieldSubscriptions(): void {
        Object.keys(this.fieldRelationships).forEach(fieldName => {
            this.form.get(fieldName)?.valueChanges.subscribe(value => {
                this.handleFieldChange(fieldName, value);
            });
        });
    }

    private clearFieldOptions(fields: string[]): void {
        fields.forEach(field => {
            const optionsProperty = `${field.replace('Code', '')}List` as keyof JobDataComponent;
            const list = this.component[optionsProperty];
            if (Array.isArray(list)) {
                (this.component[optionsProperty] as any[]) = [];
            }
        });
    }

    private async handleFieldChange(fieldName: string, value: any): Promise<void> {
        if (this.isHandlingChange) return;

        try {
            this.callbacks.onBeforeChange?.();
            this.isHandlingChange = true;

            const field = this.fieldRelationships[fieldName];
            if (!field) return;

            if (this.isValidating) {
                return;
            }

            if (value === null) {
                if (!field.preserveOnClear && field.clearFields) {
                    this.clearFields(field.clearFields);
                    if (field.clearOptions) {
                        this.clearFieldOptions(field.clearOptions);
                    }
                }
                if (field.clearAction) {
                    await field.clearAction();
                }
            } else if (field.loadAction) {
                await field.loadAction(value);
            }
        } catch (error) {
            console.error(`Error handling change for field ${fieldName}:`, error);
        } finally {
            this.isHandlingChange = false;
            this.callbacks.onAfterChange?.();
        }
    }

    private clearFields(fields: string[]): void {
        const updates: Record<string, null> = {};
        fields.forEach(field => {
            updates[field] = null;
        });
        this.form.patchValue(updates, { emitEvent: false });
    }

    private async handlePositionChange(positionValue: string): Promise<void> {
        if (!positionValue) return;
    
        try {
            // get position from select manager by position value
            const positionState = this.selectManager.getState('positionCode');
            if (!positionState) return;

            const position = positionState.data.find(p => p.value === positionValue);
            if (!position) return;


            // Handle tree organization
            const treeOrganization = await this.handleTreeOrganization('Position', positionValue);
            if (!treeOrganization?.length) return;
    
            // Add items to their respective lists if they don't exist
            treeOrganization.forEach((item: any) => {
                switch (item.type) {
                    case 'Department':
                        const departmentState = this.selectManager.getState('departmentCode');
                        if (departmentState && !departmentState.data.some(d => d.value === item.code)) {
                            // Add to SelectManager state
                            departmentState.data.push({ 
                                value: item.code, 
                                label: item.name,
                                id: item.id 
                            });
                        }
                        // get department details
                        this.getDepartmentDetails(item.id).then();
                        break;
                    case 'JobCode':
                        if (!this.component.jobList.some(j => j.value === item.code)) {
                            this.component.jobList.push({ 
                                value: item.code, 
                                label: item.name,
                                id: item.id 
                            });
                        }
                        break;
                    case 'CareerStream':
                        // get career stream from select manager
                        const careerStreamState = this.selectManager.getState('careerStreamCode');
                        if (careerStreamState && !careerStreamState.data.some(cs => cs.value === item.code)) {
                            // Add to SelectManager state
                            careerStreamState.data.push({ value: item.code, label: item.name, id: item.id });
                        }
                        break;
                    case 'Band':
                        // get career band from select manager
                        const careerBandState = this.selectManager.getState('careerBandCode');
                        if (careerBandState && !careerBandState.data.some(cb => cb.value === item.code)) {
                            // Add to SelectManager state
                            careerBandState.data.push({ value: item.code, label: item.name, id: item.id });
                        }
                        break;
                    case 'Location':
                        const locationState = this.selectManager.getState('locationCode');
                        if (locationState && !locationState.data.some(l => l.value === item.code)) {
                            // Add to SelectManager state
                            locationState.data.push({ 
                                value: item.code, 
                                label: item.name,
                                id: item.id 
                            });
                        }
                        break;
                    case 'LegalEntity':
                        const legalEntityState = this.selectManager.getState('legalEntityCode');
                        if (legalEntityState && !legalEntityState.data.some(le => le.value === item.code)) {
                            // Add to SelectManager state
                            legalEntityState.data.push({ 
                                value: item.code, 
                                label: item.name,
                                id: item.id 
                            });
                        }
                        break;
                    case 'BusinessUnit':
                        const businessUnitState = this.selectManager.getState('businessUnitCode');
                        if (businessUnitState && !businessUnitState.data.some(bu => bu.value === item.code)) {
                            // Add to SelectManager state
                            businessUnitState.data.push({ 
                                value: item.code, 
                                label: item.name,
                                id: item.id 
                            });
                        }
                        break;
                    case 'DirectPosition':
                        // get report position from select manager
                        const reportPositionState = this.selectManager.getState('reportPosition');
                        if (reportPositionState && !reportPositionState.data.some(rp => rp.value === item.code)) {
                            // Add to SelectManager state
                            reportPositionState.data.push({ value: item.code, label: item.name, id: item.id });
                        }
                        break;
                    case 'Division':
                        if (!this.component.divisionList.some(d => d.value === item.code)) {
                            this.component.divisionList.push({ 
                                value: item.code, 
                                label: item.name,
                                id: item.id 
                            });
                        }
                        break;
                    case 'CostCenter':
                        const costCenterState = this.selectManager.getState('costCenterCode');
                        if (costCenterState && !costCenterState.data.some(cc => cc.value === item.code)) {
                            // Add to SelectManager state
                            costCenterState.data.push({ 
                                value: item.code, 
                                label: item.name,
                                id: item.id 
                            });
                        }
                        break;
                    case 'BusinessTitle':
                        // get business title from select manager
                        const businessTitleState = this.selectManager.getState('businessTitleCode');
                        if (businessTitleState && !businessTitleState.data.some(bt => bt.value === item.code)) {
                            // Add to SelectManager state
                            businessTitleState.data.push({ value: item.code, label: item.name, id: item.id });
                        }
                        break;
                    case 'Region':
                        if (!this.component.regionList.some(r => r.value === item.code)) {
                            this.component.regionList.push({ 
                                value: item.code, 
                                label: item.name,
                                id: item.id 
                            });
                        }
                        break;
                }
            });
    
            // Update form with values from tree organization
            const updates = treeOrganization.reduce((acc: Record<string, string>, item: any) => {
                switch (item.type) {
                    case 'Position': acc['positionCode'] = item.code; break;
                    case 'JobCode': acc['jobCode'] = item.code; break;
                    case 'CareerStream': acc['careerStreamCode'] = item.code; break;
                    case 'Band': acc['careerBandCode'] = item.code; break;
                    case 'Department': acc['departmentCode'] = item.code; break;
                    case 'LegalEntity': acc['legalEntityCode'] = item.code; break;
                    case 'BusinessUnit': acc['businessUnitCode'] = item.code; break;
                    case 'DirectPosition': acc['reportPosition'] = item.code; break;
                    case 'Division': acc['divisionCode'] = item.code; break;
                    case 'CostCenter': acc['costCenterCode'] = item.code; break;
                    case 'Location': acc['locationCode'] = item.code; break;
                    case 'BusinessTitle': acc['businessTitleCode'] = item.code; break;
                    case 'Region': acc['regionCode'] = item.code; break;
                }
                return acc;
            }, {});

            // Check if BusinessUnit and Division were not found in tree organization
            const hasBusinessUnit = treeOrganization.some((item: any) => item.type === 'BusinessUnit');
            const hasDivision = treeOrganization.some((item: any) => item.type === 'Division');
            if (!hasBusinessUnit) {
                updates['businessUnitCode'] = null;
            }
            if (!hasDivision) {
                updates['divisionCode'] = null;
            }

            // Update form values
            this.form.patchValue(updates, { emitEvent: false });

            // Update form validity for each changed control
            Object.keys(updates).forEach(controlName => {
                const control = this.form.get(controlName);
                if (control) {
                    // Clear server validation errors
                    if (control.errors && control.errors['serverError']) {
                        const errors = { ...control.errors };
                        delete errors['serverError'];
                        control.setErrors(Object.keys(errors).length ? errors : null);
                    }
                    control.markAsTouched();
                    control.updateValueAndValidity({ onlySelf: true });
                }
            });

            // find matrix report position
            const matrixReportPosition = await firstValueFrom(this.component.hrFsBp001Service.getEntityById('positions', position.id));
            if (matrixReportPosition && matrixReportPosition.matrixPositions?.[0]?.id) {
                // bind the first element to matrix report position in select manager
                const matrixReportPositionState = this.selectManager.getState('matrixReportPositionCode');
                if (matrixReportPositionState && !matrixReportPositionState.data.some(mrp => mrp.value === matrixReportPosition.matrixPositions?.[0]?.value)) {
                    // Add to SelectManager state
                    matrixReportPositionState.data.push({ value: matrixReportPosition.matrixPositions?.[0]?.value, label: matrixReportPosition.matrixPositions?.[0]?.label, id: matrixReportPosition.matrixPositions?.[0]?.id });
                }
                // update form values
                this.form.patchValue({ matrixReportPositionCode: matrixReportPosition.matrixPositions?.[0]?.value }, { emitEvent: false });
            }

            // filter position list
            this.selectManager.search('positionCode', ' ').subscribe();
    
        } catch (error) {
            console.error('Error handling position change:', error);
        }
    }

    /**
     * handle the tree organization
     * @param pickType 
     * @param code 
     * @returns 
     */
    private async handleTreeOrganization(pickType: 'Position' | 'Department', code: string): Promise<any> {
        // get the effective date
        const effectiveDate = this.component.jobDataForm.get('effectiveDateFrom')?.value;
        if (!effectiveDate) return;

        // get the UTC time in seconds since epoch
        const effectiveDateUtc = this.component.hrFsBp001Service.getUTCTimeInSecond(effectiveDate);


        const treeOrganization = await firstValueFrom(this.component.hrFsBp001Service.getTreeOrganization(pickType, code, effectiveDateUtc));
        if (!treeOrganization) return;

        return treeOrganization;
    }

    private async handleJobAndDependencies(jobId: string): Promise<void> {
        const jobDetails = this.component.jobList.find(job => job.id === jobId);
        if (!jobDetails) return;

        // update form values
        this.form.patchValue({ 
            jobCode: jobDetails.value,
        }, { emitEvent: false });

        // get career bands
        const careerBands = await firstValueFrom(this.component.hrFsBp001Service.getEntityById('bands', jobDetails.bandId));
        if (careerBands) {
            const careerBandState = this.selectManager.getState('careerBandCode');
            if (careerBandState) {
                // Find existing item with same value
                const existingIndex = careerBandState.data.findIndex(cb => cb.value === careerBands.value);
                if (existingIndex !== -1) {
                    // Update existing item with new label
                    careerBandState.data[existingIndex] = {
                        value: careerBands.value,
                        label: careerBands.label,
                        id: careerBands.id
                    };
                } else {
                    // Add new item if it doesn't exist
                    careerBandState.data.push({
                        value: careerBands.value,
                        label: careerBands.label,
                        id: careerBands.id
                    });
                }
            }

            // update form values
            this.form.patchValue({ 
                jobCode: jobDetails.value,
                careerBandCode: careerBands.value
            }, { emitEvent: false });
        }

        // get career streams
        await this.getCareerStream(jobDetails.careerStreamId);

        // reset related fields
        this.clearFields(this.fieldRelationships['jobCode'].clearFields ?? []);

        // Trigger fresh position search with current filters
        this.selectManager.search('positionCode', ' ').subscribe();
    }

    /**
     * Get career stream
     * @param careerStreamId 
     */
    private async getCareerStream(careerStreamId: string): Promise<void> {
        const careerStreams = await firstValueFrom(this.component.hrFsBp001Service.getEntityById('career-streams', careerStreamId));
        if (careerStreams) {
            const careerStreamState = this.selectManager.getState('careerStreamCode');
            if (careerStreamState) {
                // Find existing item with same value
                const existingIndex = careerStreamState.data.findIndex(cs => cs.value === careerStreams.value);
                if (existingIndex !== -1) {
                    // Update existing item with new label
                    careerStreamState.data[existingIndex] = {
                        value: careerStreams.value,
                        label: careerStreams.label,
                        id: careerStreams.id
                    };
                } else {
                    // Add new item if it doesn't exist
                    careerStreamState.data.push({
                        value: careerStreams.value,
                        label: careerStreams.label,
                        id: careerStreams.id
                    });
                }
            }
            
            // update form values
            this.form.patchValue({ 
                careerStreamCode: careerStreams.value
            }, { emitEvent: false });
        }
    }

    // Now we can reuse handleDepartmentAndDependencies in department change handler
    private async handleDepartmentChange(departmentValue: string): Promise<void> {
        if (!departmentValue) return;

        try {

            // get department from select manager
            const departmentState = this.selectManager.getState('departmentCode');
            if (!departmentState) return;

            // get department from select manager
            const department = departmentState.data.find(d => d.value === departmentValue);
            if (!department) return;

            // Handle tree organization
            const treeOrganization = await this.handleTreeOrganization('Department', department.value);
            if (!treeOrganization?.length) return;

            // Add items to their respective lists if they don't exist
            treeOrganization.forEach((item: any) => {
                switch (item.type) {
                    case 'LegalEntity':
                        // get legal entity from select manager
                        const legalEntityState = this.selectManager.getState('legalEntityCode');
                        if (legalEntityState && !legalEntityState.data.some(le => le.value === item.code)) {
                            // Add to SelectManager state
                            legalEntityState.data.push({ value: item.code, label: item.name, id: item.id });
                        }
                        break;
                    case 'BusinessUnit':
                        // get business unit from select manager
                        const businessUnitState = this.selectManager.getState('businessUnitCode');
                        if (businessUnitState && !businessUnitState.data.some(bu => bu.value === item.code)) {
                            // Add to SelectManager state
                            businessUnitState.data.push({ 
                                value: item.code, 
                                label: item.name,
                                id: item.id 
                            });
                        }
                        break;
                    case 'Division':
                        if (!this.component.divisionList.some(d => d.value === item.code)) {
                            this.component.divisionList.push({ 
                                value: item.code, 
                                label: item.name,
                                id: item.id 
                            });
                        }
                        break;
                    case 'Location':
                        const locationState = this.selectManager.getState('locationCode');
                        if (locationState && !locationState.data.some(l => l.value === item.code)) {
                            // Add to SelectManager state
                            locationState.data.push({ 
                                value: item.code, 
                                label: item.name,
                                id: item.id 
                            });
                        }
                        break;
                    case 'CostCenter':
                        const costCenterState = this.selectManager.getState('costCenterCode');
                        if (costCenterState && !costCenterState.data.some(cc => cc.value === item.code)) {
                            // Add to SelectManager state
                            costCenterState.data.push({ 
                                value: item.code, 
                                label: item.name,
                                id: item.id 
                            });
                        }
                        break;
                    case 'Region':
                        if (!this.component.regionList.some(r => r.value === item.code)) {
                            this.component.regionList.push({
                                value: item.code,
                                label: item.name,
                                id: item.id
                            });
                        }
                        break;
                }
            });

            // Update form with values from tree organization
            const updates = treeOrganization.reduce((acc: Record<string, string>, item: any) => {
                switch (item.type) {
                    case 'Department': acc['departmentCode'] = item.code; break;
                    case 'LegalEntity': acc['legalEntityCode'] = item.code; break;
                    case 'BusinessUnit': acc['businessUnitCode'] = item.code; break;
                    case 'Division': acc['divisionCode'] = item.code; break;
                    case 'Location': acc['locationCode'] = item.code; break;
                    case 'CostCenter': acc['costCenterCode'] = item.code; break;
                    case 'Region': acc['regionCode'] = item.code; break;
                }
                return acc;
            }, {});

            // Check if BusinessUnit and Division were not found in tree organization
            const hasBusinessUnit = treeOrganization.some((item: any) => item.type === 'BusinessUnit');
            const hasDivision = treeOrganization.some((item: any) => item.type === 'Division');
            if (!hasBusinessUnit) {
                updates['businessUnitCode'] = null;
            }
            if (!hasDivision) {
                updates['divisionCode'] = null;
            }

            // clear related fields using clearFields
            this.clearFields(this.fieldRelationships['departmentCode'].clearFields ?? []);

            // reload position in select manager
            this.selectManager.search('positionCode', ' ').subscribe();

            // Update form values
            this.form.patchValue(updates, { emitEvent: false });

            // Update form validity for each changed control
            Object.keys(updates).forEach(controlName => {
                const control = this.form.get(controlName);
                if (control) {
                    // Clear server validation errors
                    if (control.errors && control.errors['serverError']) {
                        const errors = { ...control.errors };
                        delete errors['serverError'];
                        control.setErrors(Object.keys(errors).length ? errors : null);
                    }
                    control.markAsTouched();
                    control.updateValueAndValidity({ onlySelf: true });
                }
            });

            // handle department details
            this.getDepartmentDetails(department.id).then();

            // filter department list
            this.component.setDepartmentPicklistValues().then();
        } catch (error) {
            console.error('Error handling department change:', error);
        }
    }
    
    private async handleLegalEntityChange(legalEntityValue: string): Promise<void> {
        // const legalEntity = this.component.legalEntityList.find(l => l.value === legalEntityValue);
        // if (!legalEntity) return;

        try {
            // Load all legal entity-related data in parallel
            await Promise.all([
                // reload department list
                this.selectManager.search('departmentCode', ' ').subscribe(),
                // reload position in select manager
                this.selectManager.search('positionCode', ' ').subscribe()
            ]);
            // clear related fields using clearFields
            this.clearFields(this.fieldRelationships['legalEntityCode'].clearFields ?? []);
        } catch (error) {
            console.error('Error in legal entity change:', error);
        }
    }

    private async handleBusinessUnitChange(businessUnitValue: string): Promise<void> {
        const businessUnitState = this.selectManager.getState('businessUnitCode');
        if (!businessUnitState) return;

        // get business unit from select manager
        const businessUnit = businessUnitState.data.find(b => b.value === businessUnitValue);
        if (!businessUnit) return;

        try {
            // clear related fields using clearFields
            this.clearFields(this.fieldRelationships['businessUnitCode'].clearFields ?? []);

            // Load all business unit-related data in parallel
            await Promise.all([
                // reload division list
                this.component.setDivisionPicklistValues(),
                // reload department list
                // this.component.setDepartmentPicklistValues()
                this.selectManager.search('departmentCode', ' ').subscribe()
            ]);

            
        } catch (error) {
            console.error('Error in business unit change:', error);
        }
    }

    private async handleJobChange(jobValue: string): Promise<void> {
        const job = this.component.jobList.find(j => j.value === jobValue);
        if (!job) return;

        await this.handleJobAndDependencies(job.id);
    }

    private async handleCareerStreamChange(careerStreamValue: string): Promise<void> {
        // reset related fields
        //this.clearFields(this.fieldRelationships['careerStreamCode'].clearFields ?? []);

        // reload career band list
        this.selectManager.search('careerBandCode', ' ').subscribe();
    }

    private async handleCareerBandChange(careerBandValue: string): Promise<void> {
        // get career band item from select manager
        const careerBandState = this.selectManager.getState('careerBandCode');
        if (careerBandState) {
            // find the career band item in the select manager state
            const careerBandItem = careerBandState.data.find(cb => cb.value === careerBandValue);
            if (careerBandItem) {
                // get the career stream object
                await this.getCareerStream(careerBandItem.careerStreamId);
            }
        }
    }

    /**
     * Resets multiple form fields to null
     * @param fields Array of form control names to reset
     */
    resetFields(fields: string[]): void {
        const updates: { [key: string]: any } = {};
        
        fields.forEach(field => {
        if (this.form.contains(field)) {
            updates[field] = null;
        }
        });

        this.form.patchValue(updates, { emitEvent: true });
    }


    /**
     * Get department details
     * @param departmentId 
     */
    private getDepartmentDetails(departmentId: string): Promise<void> {
        return new Promise((resolve, reject) => {
            this.component.hrFsBp001Service.getEntityById('departments', departmentId)
                .pipe(
                    switchMap(department => {
                        if (!department) {
                            return of(null);
                        }

                        if (department.managerType === MANAGER_TYPE.EMPLOYEE) {
                            return this.component.hrFsBp001Service.getPersonalDetails(department.headOfDepartment)
                                    .pipe(
                                    map(supervisorDetails => ({
                                        type: MANAGER_TYPE.EMPLOYEE,
                                        details: Array.isArray(supervisorDetails) ? supervisorDetails[0] : supervisorDetails
                                    }))
                                );
                        } else if (department.managerType === MANAGER_TYPE.POSITION) {
                            // if reportPosition is empty or null, then find the position by positionId
                            return this.component.hrFsBp001Service.getEntityById('positions', department.managerPositionId)
                                    .pipe(
                                        map(positionDetails => ({
                                            type: MANAGER_TYPE.POSITION,
                                            details: positionDetails
                                        }))
                                    );
                        }
                        return of(null);
                    })
                )
                .subscribe({
                    next: (result) => {
                        if (result) {
                            // if manager type is employee, set value to supervisor select manager
                            if (result.type === MANAGER_TYPE.EMPLOYEE) {
                                const supervisorState = this.selectManager.getState('supervisor');
                                if (supervisorState && result.details) {
                                    supervisorState.data.push({
                                        value: result.details.employeeId,
                                        label: result.details.fullName
                                    });

                                    // patch value to form and clear reportPosition because it is multual exclusive
                                    this.form.patchValue({ 
                                        supervisor: result.details.employeeId,
                                        reportPosition: null
                                    }, { emitEvent: false });
                                }
                            } else if (result.type === MANAGER_TYPE.POSITION) {
                                // apply to report to position select manager
                                const reportPositionState = this.selectManager.getState('reportPosition');
                                if (reportPositionState && result.details) {
                                    reportPositionState.data.push({
                                        value: result.details.value,
                                        label: result.details.label
                                    });

                                    // patch value to form and clear supervisor because it is multual exclusive
                                    this.form.patchValue({ 
                                        reportPosition: result.details.value,
                                        supervisor: null
                                    }, { emitEvent: false });
                                }
                            }
                        }
                        resolve();
                    },
                    error: (error) => {
                        console.error('Error getting department details:', error);
                        reject(error);
                    }
                });
        });
    }


    // handle division change
    private async handleDivisionChange(divisionValue: string): Promise<void> {
        const division = this.component.divisionList.find(d => d.value === divisionValue);
        if (!division) return;

        // get the division details
        const divisionDetails = await firstValueFrom(this.component.hrFsBp001Service.getEntityById('divisions', division.id));
        if (divisionDetails && divisionDetails.businessUnitCode) {
            // Check if the business unit code exists in current list
            const businessUnitState = this.selectManager.getState('businessUnitCode');
            if (businessUnitState && !businessUnitState.data.some(bu => bu.value === divisionDetails.businessUnitCode)) {
                // Add to SelectManager state
                businessUnitState.data.push({
                    value: divisionDetails.businessUnitCode,
                    label: divisionDetails.businessUnitName,
                    id: divisionDetails.businessUnitId
                });
            }

            // patch value to form
            this.form.patchValue({ businessUnitCode: divisionDetails.businessUnitCode }, { emitEvent: false });
        } else {
            // clear business unit code
            this.form.patchValue({ businessUnitCode: null }, { emitEvent: false });
        }

        // reload department
        this.selectManager.search('departmentCode', ' ').subscribe();
        
    }

    setValidationMode(validating: boolean) {
        this.isValidating = validating;
    }


    // Add this new method to the FormDependencyManager class

    /**
     * Clears validation errors for the specified form controls and updates their validity state
     * @param fieldNames Array of form control names to clear errors for
     */
    private clearValidationErrors(fieldNames: string[]): void {
        fieldNames.forEach(fieldName => {
        const control = this.form.get(fieldName);
        if (control) {
            // Clear server validation errors
            if (control.errors && control.errors['serverError']) {
            const errors = { ...control.errors };
            delete errors['serverError'];
            control.setErrors(Object.keys(errors).length ? errors : null);
            }
            
            // Mark as touched to ensure UI updates
            control.markAsTouched();
            control.updateValueAndValidity({ onlySelf: true });
        }
        });
    }
}


// create constants of employee types
const MANAGER_TYPE = {
    EMPLOYEE: 'Employee',
    POSITION: 'Position'
} as const;
