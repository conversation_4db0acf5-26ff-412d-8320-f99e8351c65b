import { BffService, Data } from '@hrdx-fe/shared';
import { QueryFilter, RequestQueryBuilder } from '@nestjsx/crud-request';
import { NzUploadFile } from 'ng-zorro-antd/upload';
import { Observable, of } from 'rxjs';
import { map, catchError, switchMap } from 'rxjs/operators';

export class HrFsBp001Service extends BffService {
  /**
   * Normalize date to UTC timestamp with time components stripped
   * @param date Date to normalize
   * @returns UTC timestamp in milliseconds, or null if date is null
   */
  normalizeDateToUTCTimestamp(date: Date | null): number | null {
    if (!date) return null;
    return new Date(
      Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()),
    ).getTime();
  }

  getListItem(
    url: string,
    page = 0,
    pageSize = 0,
    searchValue?: string,
    filter: QueryFilter[] = [],
  ) {
    const qb = RequestQueryBuilder.create();
    qb.setPage(page);
    qb.setLimit(pageSize);
    qb.setFilter(filter);

    const queryString = qb.query();

    return of(undefined).pipe(
      switchMap(() =>
        this.http.get<{
          data: Data[];
          count: number;
          total: number;
        }>(`${this.host}${url}?${queryString}`),
      ),
    );
  }

  getActionList() {
    const url = `/api/actions`;
    return this.getListItem(url, 1, 1000, undefined).pipe(
      map((response) => {
        if (response && response['data'] && Array.isArray(response['data'])) {
          return response['data'].map((item: any) => ({
            value: item.code,
            label: item.name.default || item.name.en_US,
          }));
        }
        return [];
      }),
      catchError(() => {
        // Handle errors and return an empty array
        return of([]);
      }),
    );
  }

  // get action by type and employee sub group for create new employee
  getActionListByTypeAndEmployeeGroup(
    type: string,
    employeeSubGroup: string,
    searchValue = '',
    effectiveDateQuery?: number,
  ) {
    const url = `/api/actions/dropdown`;

    const filter: QueryFilter[] = [
      {
        field: 'status',
        operator: '$eq',
        value: true,
      } as QueryFilter,
      {
        field: 'processType',
        operator: '$eq',
        value: type,
      } as QueryFilter,
      {
        field: 'employeeGroupCodes',
        operator: '$eq',
        value: employeeSubGroup,
      } as QueryFilter,
    ];

    // if searchValue is provided, add a filter to the query
    if (searchValue) {
      filter.push({
        field: 'search',
        operator: '$eq',
        value: searchValue,
      } as QueryFilter);
    }

    if (effectiveDateQuery) {
      filter.push({
        field: 'effectiveDateQuery',
        operator: '$eq',
        value: effectiveDateQuery,
      } as QueryFilter);
    }

    return this.getListItem(url, 1, 25, undefined, filter).pipe(
      map((response) => {
        if (response && response['data'] && Array.isArray(response['data'])) {
          return response['data'].map((item: any) => ({
            value: item.code,
            label: item.name.default || item.name.en_US,
            actionReasons: item.actionReasons
              ?.flatMap((reason: any) =>
                reason.actionReasonCode ? reason.actionReasonCode : [],
              )
              .filter(Boolean),
            setStatusField: item.setStatusField,
            prStatusCode: item.prStatusCode,
            prStatusName: item.prStatusName,
            hrStatusCode: item.hrStatusCode,
            hrStatusName: item.hrStatusName,
          }));
        }
        return [];
      }),
      catchError(() => {
        // Handle errors and return an empty array
        return of([]);
      }),
    );
  }

  /**
   * get position list
   * @param searchValue
   * @param pageIndex
   * @param pageSize
   * @returns
   */
  getPositionList(
    searchValue = '',
    positionId?: string,
    jobCodeCode?: string,
    departmentCode?: string,
    legalEntityCode?: string,
    companyCode?: string,
    effectiveDate?: string,
    pageIndex = 1,
    pageSize = 25,
  ) {
    const url = `/api/positions/get-dropdown-list-by`;

    const filter: QueryFilter[] = [
      {
        field: 'status',
        operator: '$eq',
        value: true,
      } as QueryFilter,
    ];

    // if searchValue is provided, add a filter to the query
    if (searchValue) {
      filter.push({
        field: 'search',
        operator: '$eq',
        value: searchValue,
      } as QueryFilter);
    }

    if (companyCode) {
      filter.push({
        field: 'companyCode',
        operator: '$eq',
        value: companyCode,
      } as QueryFilter);
    }

    if (departmentCode) {
      filter.push({
        field: 'departmentCode',
        operator: '$eq',
        value: departmentCode,
      } as QueryFilter);
    }

    if (positionId) {
      filter.push({
        field: 'id',
        operator: '$eq',
        value: positionId,
      } as QueryFilter);
    }

    if (jobCodeCode) {
      filter.push({
        field: 'jobCodeCode',
        operator: '$eq',
        value: jobCodeCode,
      } as QueryFilter);
    }

    if (effectiveDate) {
      filter.push({
        field: 'effectiveDate',
        operator: '$eq',
        value: effectiveDate,
      } as QueryFilter);
    }

    if (legalEntityCode) {
      filter.push({
        field: 'legalEntityCode',
        operator: '$eq',
        value: legalEntityCode,
      } as QueryFilter);
    }

    return this.getListItem(url, pageIndex, pageSize, undefined, filter).pipe(
      map((response) => {
        if (response && Array.isArray(response)) {
          return response.map((item: any) => ({
            value: item.code,
            label: item.longName?.default ?? item.name?.default ?? '',
            id: item.id,
            departmentId: item.departmentId,
            jobCodeId: item.jobCodeId,
            locationId: item.locationId,
            costCenterId: item.costCenterId,
            legalEntityId: item.legalEntityId,
            businessUnitId: item.businessUnitId,
            divisionId: item.divisionId,
            directPositionId: item.directPositionId,
            matrixPositionId: item.matrixPositions?.[0]?.id,
          }));
        }
        return [];
      }),
      catchError(() => {
        // Handle errors and return an empty array
        return of([]);
      }),
    );
  }

  getPositionBy(companyCode?: string, effectiveDate?: string) {
    const url = `/api/positions/get-dropdown-list-by`;

    const filter: QueryFilter[] = [
      {
        field: 'status',
        operator: '$eq',
        value: true,
      } as QueryFilter,
    ];

    if (effectiveDate) {
      filter.push({
        field: 'effectiveDate',
        operator: '$eq',
        value: effectiveDate,
      } as QueryFilter);
    }

    if (companyCode) {
      filter.push({
        field: 'companyCode',
        operator: '$eq',
        value: companyCode,
      } as QueryFilter);
    }

    return this.getListItem(url, undefined, undefined, undefined, filter).pipe(
      map((response) => {
        if (response && Array.isArray(response)) {
          return response.map((item: any) => ({
            value: item.code,
            label: item.longName?.default ?? item.name?.default ?? '',
          }));
        }
        return [];
      }),
      catchError(() => {
        // Handle errors and return an empty array
        return of([]);
      }),
    );
  }

  /**
   * get company list by conditions
   * @param searchValue
   * @param effectiveDate
   * @param page
   * @param pageSize
   * @returns
   */
  getCompanyListByConditions(
    searchValue = '',
    effectiveDate?: string,
    page = 1,
    pageSize = 25,
  ) {
    const url = `/api/companies/get-by`;

    const filter: QueryFilter[] = [
      {
        field: 'status',
        operator: '$eq',
        value: true,
      } as QueryFilter,
    ];

    // if searchValue is provided, add a filter to the query
    if (searchValue) {
      filter.push({
        field: 'search',
        operator: '$eq',
        value: searchValue,
      } as QueryFilter);
    }

    if (effectiveDate) {
      filter.push({
        field: 'effectiveDate',
        operator: '$eq',
        value: effectiveDate,
      } as QueryFilter);
    }

    return this.getListItem(url, page, pageSize, undefined, filter).pipe(
      map((response) => {
        return response;
      }),
      catchError(() => {
        // Handle errors and return an empty array
        return of([]);
      }),
    );
  }

  getCompanyList(
    searchValue = '',
    companyCode?: string,
    effectiveDate?: string,
    page = 1,
    pageSize = 25,
  ) {
    const url = `/api/companies/get-by`;

    const filter: QueryFilter[] = [
      {
        field: 'status',
        operator: '$eq',
        value: true,
      } as QueryFilter,
    ];

    // if searchValue is provided, add a filter to the query
    if (searchValue) {
      filter.push({
        field: 'search',
        operator: '$eq',
        value: searchValue,
      } as QueryFilter);
    }

    if (companyCode) {
      filter.push({
        field: 'code',
        operator: '$eq',
        value: companyCode,
      } as QueryFilter);
    }

    if (effectiveDate) {
      filter.push({
        field: 'effectiveDate',
        operator: '$eq',
        value: effectiveDate,
      } as QueryFilter);
    }

    return this.getListItem(url, page, pageSize, undefined, filter).pipe(
      map((response) => {
        if (response && response['data'] && Array.isArray(response['data'])) {
          return response['data'].map((item: any) => ({
            value: item.code,
            // label: item.longName.default,
            label: item.longName?.default ?? item.name?.default ?? '',
            id: item.id,
          }));
        }
        return [];
      }),
      catchError(() => {
        // Handle errors and return an empty array
        return of([]);
      }),
    );
  }

  // get picklist by code and parent
  getPicklistByCodeAndParent(
    code: string,
    parentCode?: string,
    searchValue?: string,
    effectiveDate?: string,
  ): Observable<any> {
    let url = `/api/picklists/${code}/values`;
    if (searchValue) {
      url += `/${searchValue}`;
    }

    const filter: QueryFilter[] = [
      {
        field: 'status',
        operator: '$eq',
        value: true,
      } as QueryFilter,
    ];

    // if searchValue is provided, add a filter to the query
    if (parentCode) {
      // append parentCode to the url
      url += `/${parentCode}`;
    }

    if (effectiveDate) {
      filter.push({
        field: 'effectiveDate',
        operator: '$eq',
        value: effectiveDate,
      } as QueryFilter);
    }

    return this.getListItem(
      url,
      undefined,
      undefined,
      searchValue,
      filter,
    ).pipe(
      map((response) => {
        if (response && response['data'] && Array.isArray(response['data'])) {
          // with each item in the array, extract the code and name.default, if name.default is not available, use name.en-US as the name
          return response['data'].map((item: any) => ({
            value: item.code,
            label: item.name.default || item.name.en_US,
          }));
        }
        return [];
      }),
      catchError(() => {
        // Handle errors and return an empty array
        return of([]);
      }),
    );
  }

  /**
   * get picklist by code and parent with pagination
   * @param code 
   * @param parentCode 
   * @param searchValue 
   * @param effectiveDate 
   * @param page 
   * @param pageSize 
   * @returns 
   */
  getPicklistByCodeAndParentWithPagination(
    code: string,
    parentCode?: string,
    searchValue?: string,
    effectiveDate?: string,
    page = 1,
    pageSize = 25,
  ): Observable<any> {
    let url = `/api/picklists/${code}/values/pagination`;
    

    const filter: QueryFilter[] = [
      {
        field: 'status',
        operator: '$eq',
        value: true,
      } as QueryFilter,
    ];

    // if searchValue is provided, add a filter to the query
    if (parentCode) {
      // append parentCode to the url
      url += `/${parentCode}`;
    }

    if (effectiveDate) {
      filter.push({
        field: 'effectiveDate',
        operator: '$eq',
        value: effectiveDate,
      } as QueryFilter);
    }

    if (searchValue) {
      filter.push({
        field: 'search',
        operator: '$eq',
        value: searchValue,
      } as QueryFilter);
    }

    return this.getListItem(
      url,
      page,
      pageSize,
      searchValue,
      filter,
    ).pipe(
      map((response) => {
        if (response && response['data'] && Array.isArray(response['data'])) {
          // return response['data']
          //   .filter((item: any) => item.name)
          //   .map((item: any) => ({
          //     value: item.code,
          //     label: item.name.default || item.name.en_US,
          //   }));
          return response;
        }
        return [];
      }),
      catchError(() => {
        // Handle errors and return an empty array
        return of([]);
      }),
    );
  }

  // get picklist by code
  getPicklistByCodeWithFilter(
    code: string,
    searchValue?: string,
    filter: QueryFilter[] = [],
  ): Observable<any> {
    let url = `/api/picklists/${code}/values`;
    if (searchValue) {
      url += `/${searchValue}`;
    }
    return this.getListItem(
      url,
      undefined,
      undefined,
      searchValue,
      filter,
    ).pipe(
      map((response) => {
        if (response && response['data'] && Array.isArray(response['data'])) {
          // with each item in the array, extract the code and name.default, if name.default is not available, use name.en-US as the name
          return response['data'].map((item: any) => ({
            value: item.code,
            label: item.name.default || item.name.en_US,
          }));
        }
        return [];
      }),
      catchError(() => {
        // Handle errors and return an empty array
        return of([]);
      }),
    );
  }

  // get picklist by code
  getPicklistByCode(
    code: string,
    searchValue?: string,
    effectiveDate?: string,
  ): Observable<any> {
    let url = `/api/picklists/${code}/values`;
    if (searchValue) {
      url += `/${searchValue}`;
    }
    const filter: QueryFilter[] = [
      {
        field: 'status',
        operator: '$eq',
        value: true,
      } as QueryFilter,
    ];

    if (effectiveDate) {
      filter.push({
        field: 'effectiveDate',
        operator: '$eq',
        value: effectiveDate,
      } as QueryFilter);
    }
    return this.getListItem(
      url,
      undefined,
      undefined,
      searchValue,
      filter,
    ).pipe(
      map((response) => {
        if (response && response['data'] && Array.isArray(response['data'])) {
          // with each item in the array, extract the code and name.default, if name.default is not available, use name.en-US as the name
          return response['data'].map((item: any) => ({
            value: item.code,
            label: item.name.default || item.name.en_US,
          }));
        }
        return [];
      }),
      catchError(() => {
        // Handle errors and return an empty array
        return of([]);
      }),
    );
  }

  /**
   * get legal entity list
   * @param searchValue
   * @param legalId
   * @param companyCode
   * @param effectiveDate
   * @param page
   * @param pageSize
   * @returns
   */
  getLegalEntityList(
    searchValue = '',
    legalId?: string,
    companyCode?: string,
    effectiveDate?: string,
    page = 1,
    pageSize = 25,
  ) {
    const url = `/api/legal-entities/get-by`;

    const filter: QueryFilter[] = [
      {
        field: 'status',
        operator: '$eq',
        value: true,
      } as QueryFilter,
    ];

    // if searchValue is provided, add a filter to the query
    if (searchValue) {
      filter.push({
        field: 'search',
        operator: '$eq',
        value: searchValue,
      } as QueryFilter);
    }

    if (legalId) {
      filter.push({
        field: 'id',
        operator: '$eq',
        value: legalId,
      } as QueryFilter);
    }

    if (companyCode) {
      filter.push({
        field: 'companyCode',
        operator: '$eq',
        value: companyCode,
      } as QueryFilter);
    }

    if (effectiveDate) {
      filter.push({
        field: 'effectiveDate',
        operator: '$eq',
        value: effectiveDate,
      } as QueryFilter);
    }

    return this.getListItem(url, page, pageSize, undefined, filter).pipe(
      map((response) => {
        if (response && response['data'] && Array.isArray(response['data'])) {
          // with each item in the array, extract the code and name.default, if name.default is not available, use name.en-US as the name
          return response['data'].map((item: any) => ({
            value: item.code,
            label: item.longName?.default ?? item.name?.default ?? '',
          }));
        }
        return [];
      }),
      catchError(() => {
        // Handle errors and return an empty array
        return of([]);
      }),
    );
  }

  /**
   * get business unit list
   * @param searchValue
   * @param businessUnitId
   * @param companyIds
   * @param page
   * @param pageSize
   * @returns
   */
  getBusinessUnitList(
    searchValue = '',
    businessUnitId?: string,
    companyCode?: string,
    effectiveDate?: string,
    page = 1,
    pageSize = 25,
  ) {
    const url = `/api/business-units/get-list`;

    const filter: QueryFilter[] = [
      {
        field: 'status',
        operator: '$eq',
        value: true,
      } as QueryFilter,
    ];

    // if searchValue is provided, add a filter to the query
    if (searchValue) {
      filter.push({
        field: 'search',
        operator: '$eq',
        value: searchValue,
      } as QueryFilter);
    }

    if (businessUnitId) {
      filter.push({
        field: 'id',
        operator: '$eq',
        value: businessUnitId,
      } as QueryFilter);
    }

    if (companyCode) {
      filter.push({
        field: 'companyCode',
        operator: '$eq',
        value: companyCode,
      } as QueryFilter);
    }

    if (effectiveDate) {
      filter.push({
        field: 'effectiveDate',
        operator: '$eq',
        value: effectiveDate,
      } as QueryFilter);
    }

    return this.getListItem(url, page, pageSize, undefined, filter).pipe(
      map((response) => {
        if (response && Array.isArray(response)) {
          return response.map((item: any) => ({
            value: item.code,
            //label: item.longName.default || item.longName.en_US
            label: item.longName?.default ?? item.name?.default ?? '',
            id: item.id,
          }));
        }
        return [];
      }),
      catchError(() => {
        // Handle errors and return an empty array
        return of([]);
      }),
    );
  }

  logFormData(formData: FormData): void {
    console.log('FormData contents:');
    formData.forEach((value, key) => {
      console.log(`${key}: ${value}`);
    });
  }

  createEmployee(formData: FormData): Observable<any> {
    const url = `/api/personals/create-employee`;
    return this.http.post(url, formData);
  }

  // Add new helper method to normalize filenames
  private normalizeFileName(fileName: string): string {
    try {
      // Normalize the string first
      const normalizedName = fileName.normalize('NFC');
      // Encode the normalized string
      return encodeURIComponent(normalizedName);
    } catch (e) {
      console.warn('Failed to normalize filename:', e);
      return fileName;
    }
  }

  /**
   * convert json to form data
   * @param json
   * @param generateEmpId
   * @param employeeId
   * @returns
   */
  convertJsonToFormData(
    rawJson: any,
    generateEmpId = true,
    employeeId = '',
  ): FormData {
    const formData = new FormData();
    const self = this; // Store reference to class instance

    if (generateEmpId && !employeeId) {
      throw new Error('employeeId is required when generateEmpId is true');
    }

    function setFormData(prefix: string, key: string, value: any) {
      const formattedValue = formatValue(value);
      formData.set(`${prefix}${key}`, formattedValue);
      if (key === 'issueDate') {
        formData.set(`${prefix}startDate`, formattedValue);
      } else if (key === 'isPrimary' || key === 'withoutJob') {
        formData.set(`${prefix}${key}`, value === true ? 'Y' : 'N');
      }
    }

    // Helper function to format values
    const formatValue = (value: any): any => {
      if (value === null || value === undefined || value === '') {
        return '';
      } else {
        if (value instanceof Date) {
          const truncatedDate = new Date(
            Date.UTC(value.getFullYear(), value.getMonth(), value.getDate()),
          );
          return Math.floor(truncatedDate.getTime() / 1000).toString();
        } else if (value instanceof File) {
          const normalizedName = self.normalizeFileName(value.name);
          return new File([value], normalizedName, { type: value.type });
        } else if (value?.buffer?.data || value?.buffer) {
          const bufferData =
            value?.buffer?.data ?? Object.values(value?.buffer);
          const normalizedName = self.normalizeFileName(
            value.originalName || value.name,
          );
          return {
            ...value,
            originalName: normalizedName,
            name: normalizedName,
          };
        }
        return String(value);
      }
    };

    // Set EmployeeId and GenerateEmployeeId
    formData.set('EmployeeId', employeeId);
    formData.set('GenerateEmployeeId', generateEmpId ? 'Y' : 'N');

    // Sanitize the JSON data
    // const json = HrFsBp001Service.sanitize(rawJson);
    const json = rawJson;

    // Map biographicalDetails to Personal
    if (json.biographicalDetails) {
      Object.entries(json.biographicalDetails).forEach(([key, value]) => {
        if (
          key !== 'nationalIds' &&
          !key.startsWith('phoneInfos') &&
          key !== 'taxInfo' &&
          key !== 'files'
        ) {
          setFormData('Personal.', key, value);
        } else if (key === 'files' && Array.isArray(value)) {
          // Handle files array
          value.forEach((file: any, index: number) => {
            if (file instanceof File) {
              const normalizedName = self.normalizeFileName(file.name);
              const newFile = new File([file], normalizedName, {
                type: file.type,
              });
              formData.append(`Personal.files[${index}]`, newFile);
            } else if (file?.buffer?.data || file?.buffer) {
              const bufferData =
                file?.buffer?.data ?? Object.values(file?.buffer);
              const normalizedName = self.normalizeFileName(
                file.originalName || file.name,
              );
              formData.append(
                `Personal.files[${index}]`,
                JSON.stringify({
                  ...file,
                  originalName: normalizedName,
                  name: normalizedName,
                }),
              );
            }
          });
        }
      });
    }

    // set employeeId for Personal
    formData.set('Personal.EmployeeId', 'T');

    // Map nationalIds to PersonalIdentityDocumentCommands
    if (json.biographicalDetails && json.biographicalDetails.nationalIds) {
      json.biographicalDetails.nationalIds.forEach((id: any, index: number) => {
        Object.entries(id).forEach(([key, value]) => {
          setFormData(
            `PersonalIdentityDocumentCommands[${index}].`,
            key,
            value,
          );
        });
      });
    }

    // map phoneInfos to PersonalPhoneInfoCommands
    if (json.biographicalDetails && json.biographicalDetails.phoneInfos) {
      json.biographicalDetails.phoneInfos.forEach(
        (phone: any, index: number) => {
          // if all of the phone fields are empty or isPrimary is 'Y', then don't set the form data
          if (
            !Object.entries(phone).every(
              ([key, value]) =>
                value === '' ||
                value === null ||
                value === undefined ||
                (key === 'isPrimary' && value === true) ||
                key === 'startDate', // exclude start date
            )
          ) {
            Object.entries(phone).forEach(([key, value]) => {
              setFormData(`PhoneContactCommands[${index}].`, key, value);
            });
          }
        },
      );
    }

    // Map pit fields to PersonalIncomeTaxInfoCommand
    if (json.taxInfo) {
      // Check if any non-default fields have values
      const hasNonDefaultValues = Object.entries(json.taxInfo).some(
        ([key, value]) => {
          // Skip checking default fields
          if (['issueDate', 'countryCode', 'status'].includes(key)) {
            return false;
          }
          // If any other field has a non-empty value, it's not a default state
          return value !== '' && value !== null && value !== undefined;
        },
      );

      const isDefaultState =
        !hasNonDefaultValues &&
        json.taxInfo.issueDate === json.biographicalDetails.effectiveDateFrom &&
        (json.taxInfo.countryCode === 'CTR_VN' ||
          json.taxInfo.countryCode === 'VNM') &&
        json.taxInfo.status === 'active';

      const allFieldsEmpty = Object.entries(json.taxInfo).every(
        ([key, value]) =>
          value === '' ||
          value === null ||
          value === undefined ||
          (key === 'status' && value === 'active') ||
          (key === 'countryCode' && (value === 'CTR_VN' || value === 'VNM')) ||
          (key === 'issueDate' && value === json.taxInfo.effectiveDateFrom),
      );

      if (!isDefaultState && !allFieldsEmpty) {
        Object.entries(json.taxInfo).forEach(([key, value]) => {
          setFormData('PersonalIncomeTaxInfoCommand.', key, value);
        });
        // set PersonalIncomeTaxInfoCommand.Enabled
        formData.set('PersonalIncomeTaxInfoCommand.Enabled', 'Y');
      }
    }

    // get withoutJob
    const withoutJob = json.orgRelationship.orgRelationship.withoutJob ?? false;

    // Map jobData to JobDataCommand.JobData
    if (
      !withoutJob &&
      json.orgRelationship.jobData &&
      Object.keys(json.orgRelationship.jobData).length > 0
    ) {
      const reportPosition = json.orgRelationship.jobData.reportPosition;
      const supervisor = json.orgRelationship.jobData.supervisor;
      const matrixReportPositionCode =
        json.orgRelationship.jobData.matrixReportPositionCode;
      const matrixManagers = json.orgRelationship.jobData.matrixManagers;

      Object.entries(json.orgRelationship.jobData).forEach(([key, value]) => {
        if (
          ![
            'organizationalInstanceRcd',
            'groupOriginalStartDate',
            'oirOriginalStartDate',
            'Note',
            'files',
            'matrixReportPositionCode',
            'matrixManagers',
            'employmentNote',
          ].includes(key)
        ) {
          if (
            (key === 'reportPosition' && reportPosition) ||
            (key === 'supervisor' && supervisor && !reportPosition)
          ) {
            setFormData('JobDataCommand.JobData.', key, value);
          } else if (key !== 'reportPosition' && key !== 'supervisor') {
            setFormData('JobDataCommand.JobData.', key, value);
          }
        } else if (key === 'files' && Array.isArray(value)) {
          // Handle files array
          value.forEach((file: any, index: number) => {
            if (file instanceof File) {
              const normalizedName = self.normalizeFileName(file.name);
              const newFile = new File([file], normalizedName, {
                type: file.type,
              });
              formData.append(
                `JobDataCommand.JobData.files[${index}]`,
                newFile,
              );
            } else if (file?.buffer?.data || file?.buffer) {
              const bufferData =
                file?.buffer?.data ?? Object.values(file?.buffer);
              const normalizedName = self.normalizeFileName(
                file.originalName || file.name,
              );
              formData.append(
                `JobDataCommand.JobData.files[${index}]`,
                JSON.stringify({
                  ...file,
                  originalName: normalizedName,
                  name: normalizedName,
                }),
              );
            }
          });
        } else if (
          key === 'matrixReportPositionCode' &&
          Array.isArray(matrixReportPositionCode)
        ) {
          matrixReportPositionCode.forEach((item: any, index: number) => {
            setFormData(
              'JobDataCommand.JobData.',
              `matrixReportPositionCodes[${index}]`,
              item,
            );
          });
        } else if (key === 'matrixManagers' && Array.isArray(matrixManagers)) {
          matrixManagers.forEach((item: any, index: number) => {
            setFormData(
              'JobDataCommand.JobData.',
              `matrixManagers[${index}]`,
              item,
            );
          });
        } else if (key === 'employmentNote') {
          setFormData('JobDataCommand.Employment.', 'note', value);
        } else {
          setFormData('JobDataCommand.Employment.', key, value);
        }
      });

      // set EmployeeRecordNumber
      formData.set('JobDataCommand.JobData.EmployeeRecordNumber', '0');
      // set OrganizationalInstanceRcd
      formData.set('JobDataCommand.JobData.OrganizationalInstanceRcd', '0');
    }

    // Map OrganizeRelationshipInfoCommand
    if (json.orgRelationship.orgRelationship) {
      Object.entries(json.orgRelationship.orgRelationship).forEach(
        ([key, value]) => {
          setFormData('OrganizeRelationshipInfoCommand.', key, value);
        },
      );
    }

    return formData;
  }

  getEmployeeSubGroupList(employeeGroup: string, effectiveDate?: string) {
    const url = `/api/employee-sub-groups`;

    const filter: QueryFilter[] = [
      {
        field: 'linkCatalogDataCode',
        operator: '$eq',
        value: employeeGroup,
      } as QueryFilter,
      {
        field: 'status',
        operator: '$eq',
        value: true,
      } as QueryFilter,
    ];

    if (effectiveDate) {
      filter.push({
        field: 'effectiveDate',
        operator: '$eq',
        value: effectiveDate,
      } as QueryFilter);
    }

    return this.getListItem(url, 1, 1000, undefined, filter).pipe(
      map((response) => {
        if (response && response['data'] && Array.isArray(response['data'])) {
          return response['data'].map((item: any) => ({
            value: item.code,
            label: item.longName?.default ?? item.name?.default ?? '',
          }));
        }
        return [];
      }),
      catchError(() => {
        // Handle errors and return an empty array
        return of([]);
      }),
    );
  }

  // get division list
  getDivisionList(
    searchValue = '',
    divisionId?: string,
    businessUnitId?: string,
    companyCode?: string,
    effectiveDate?: string,
    page = 1,
    pageSize = 25,
  ) {
    const url = `/api/divisions/get-list`;

    const filter: QueryFilter[] = [
      {
        field: 'status',
        operator: '$eq',
        value: true,
      } as QueryFilter,
    ];

    // if searchValue is provided, add a filter to the query
    if (searchValue) {
      filter.push({
        field: 'search',
        operator: '$eq',
        value: searchValue,
      } as QueryFilter);
    }

    if (divisionId) {
      filter.push({
        field: 'id',
        operator: '$eq',
        value: divisionId,
      } as QueryFilter);
    }

    if (businessUnitId) {
      filter.push({
        field: 'businessUnitCode',
        operator: '$eq',
        value: businessUnitId,
      } as QueryFilter);
    }

    if (companyCode) {
      filter.push({
        field: 'companyCode',
        operator: '$eq',
        value: companyCode,
      } as QueryFilter);
    }

    if (effectiveDate) {
      filter.push({
        field: 'effectiveDate',
        operator: '$eq',
        value: effectiveDate,
      } as QueryFilter);
    }

    return this.getListItem(url, page, pageSize, undefined, filter).pipe(
      map((response) => {
        if (response && Array.isArray(response)) {
          return response.map((item: any) => ({
            value: item.code,
            //label: item.longName.default || item.longName.en_US
            label: item.longName?.default ?? item.name?.default ?? '',
            id: item.id,
          }));
        }
        return [];
      }),
      catchError(() => {
        // Handle errors and return an empty array
        return of([]);
      }),
    );
  }

  // get department list
  getDepartmentList(
    searchValue = '',
    departmentId?: string,
    legalEntityCode?: string,
    businessUnitCode?: string,
    divisionCode?: string,
    companyCode?: string,
    effectiveDate?: string,
    page = 1,
    pageSize = 25,
  ) {
    let url = `/api/departments/get-dropdown-list-by`;

    const filter: QueryFilter[] = [
      {
        field: 'status',
        operator: '$eq',
        value: true,
      } as QueryFilter,
    ];

    if (searchValue) {
      filter.push({
        field: 'search',
        operator: '$eq',
        value: searchValue,
      } as QueryFilter);
    }

    if (departmentId) {
      // change the url to get-by
      url = `/api/departments/get-by`;
      filter.push({
        field: 'id',
        operator: '$eq',
        value: departmentId,
      } as QueryFilter);
    }

    if (legalEntityCode) {
      filter.push({
        field: 'legalEntityCode',
        operator: '$eq',
        value: legalEntityCode,
      } as QueryFilter);
    }

    if (divisionCode) {
      filter.push({
        field: 'divisionCode',
        operator: '$eq',
        value: divisionCode,
      } as QueryFilter);
    } 
    if (businessUnitCode) {
      filter.push({
        field: 'businessUnitCode',
        operator: '$eq',
        value: businessUnitCode,
      } as QueryFilter);
    }

    if (companyCode) {
      filter.push({
        field: 'companyCode',
        operator: '$eq',
        value: companyCode,
      } as QueryFilter);
    }

    if (effectiveDate) {
      filter.push({
        field: 'effectiveDate',
        operator: '$eq',
        value: effectiveDate,
      } as QueryFilter);
    }

    return this.getListItem(url, page, pageSize, undefined, filter).pipe(
      map((response) => {
        if (response && Array.isArray(response)) {
          return response.map((item: any) => ({
            value: item.code,
            label: item.longName?.default ?? item.name?.default ?? '',
            location: item.location,
            constCenter: item.costCenter,
            businessUnitId: item.businessUnitId,
            legalEntityId: item.legalEntityId,
            id: item.id,
          }));
        }
        return [];
      }),
      catchError(() => {
        // Handle errors and return an empty array
        return of([]);
      }),
    );
  }

  // get department list
  getDepartmentListBy(legalEntityId?: string, searchValue = '') {
    const url = `/api/departments/get-by`;

    const filter: QueryFilter[] = [
      {
        field: 'status',
        operator: '$eq',
        value: true,
      } as QueryFilter,
    ];

    if (searchValue) {
      filter.push({
        field: 'search',
        operator: '$eq',
        value: searchValue,
      } as QueryFilter);
    }

    if (legalEntityId) {
      filter.push({
        field: 'legalEntityId',
        operator: '$eq',
        value: legalEntityId,
      } as QueryFilter);
    }

    return this.getListItem(url, 1, 25, undefined, filter).pipe(
      map((response) => {
        if (response && response['data'] && Array.isArray(response['data'])) {
          return response['data'].map((item: any) => ({
            value: item.code,
            label: item.longName?.default ?? item.name?.default ?? '',
          }));
        }
        return [];
      }),
      catchError(() => {
        // Handle errors and return an empty array
        return of([]);
      }),
    );
  }

  // get location
  getLocationList(
    locationId?: string,
    searchValue = '',
    companyCode?: string,
    effectiveDate?: string,
    page = 1,
    pageSize = 25,
  ) {
    const url = `/api/locations/get-by`;

    const filter: QueryFilter[] = [
      {
        field: 'status',
        operator: '$eq',
        value: true,
      } as QueryFilter,
    ];

    if (searchValue) {
      filter.push({
        field: 'search',
        operator: '$eq',
        value: searchValue,
      } as QueryFilter);
    }

    if (locationId) {
      filter.push({
        field: 'id',
        operator: '$eq',
        value: locationId,
      } as QueryFilter);
    }

    if (companyCode) {
      filter.push({
        field: 'companyCode',
        operator: '$eq',
        value: companyCode,
      } as QueryFilter);
    }

    if (effectiveDate) {
      filter.push({
        field: 'effectiveDate',
        operator: '$eq',
        value: effectiveDate,
      } as QueryFilter);
    }

    return this.getListItem(url, page, pageSize, undefined, filter).pipe(
      map((response) => {
        // if (response && response['data'] && Array.isArray(response['data'])) {
        //     return response['data'].map((item: any) => ({
        //         value: item.code,
        //         label: item.longName.default || item.longName.en_US
        //     }));
        // }
        // return [];
        return response;
      }),
      catchError(() => {
        // Handle errors and return an empty array
        // return of([]);
        return of({
          data: [],
          count: 0,
          page: 1,
          total: 0,
        });
      }),
    );
  }

  // get job list
  getJobList(
    searchValue = '',
    jobCode?: string,
    companyCode?: string,
    effectiveDate?: string,
    pageIndex = 1,
    pageSize = 25,
  ) {
    const url = `/api/job-codes/get-dropdown-list-by`;

    const filter: QueryFilter[] = [
      {
        field: 'status',
        operator: '$eq',
        value: true,
      } as QueryFilter,
    ];

    if (jobCode) {
      filter.push({
        field: 'code',
        operator: '$eq',
        value: jobCode,
      } as QueryFilter);
    }

    if (searchValue) {
      filter.push({
        field: 'search',
        operator: '$eq',
        value: searchValue,
      } as QueryFilter);
    }

    if (companyCode) {
      filter.push({
        field: 'companyCode',
        operator: '$eq',
        value: companyCode,
      } as QueryFilter);
    }

    if (effectiveDate) {
      filter.push({
        field: 'effectiveDate',
        operator: '$eq',
        value: effectiveDate,
      } as QueryFilter);
    }

    return this.getListItem(url, pageIndex, pageSize, undefined, filter).pipe(
      map((response) => {
        if (response && response['data'] && Array.isArray(response['data'])) {
          return response['data'].map((item: any) => ({
            value: item.code,
            label: item.longName?.default ?? item.name?.default ?? '',
            id: item.id,
            bandId: item.bandId,
            bandCode: item.bandCode,
            bandName: item.bandName,
            careerStreamId: item.careerStreamId,
            careerStreamCode: item.careerStreamCode,
            careerStreamName: item.careerStreamName,
          }));
        }
        return [];
      }),
      catchError(() => {
        // Handle errors and return an empty array
        return of([]);
      }),
    );
  }

  // get business title list
  getBusinessTitleList(
    businessTitleCode = '',
    effectiveDate?: string,
    companyCode?: string,
    page = 1,
    pageSize = 25,
  ) {
    const url = `/api/business-titles/get-by`;
    const filter: QueryFilter[] = [
      {
        field: 'status',
        operator: '$eq',
        value: true,
      } as QueryFilter,
    ];
    // if businessTitleCode is provided, add a filter to the query
    if (businessTitleCode) {
      filter.push({
        field: 'search',
        operator: '$eq',
        value: businessTitleCode,
      } as QueryFilter);
    }

    if (companyCode) {
      filter.push({
        field: 'companyCode',
        operator: '$eq',
        value: companyCode,
      } as QueryFilter);
    }

    if (effectiveDate) {
      filter.push({
        field: 'effectiveDate',
        operator: '$eq',
        value: effectiveDate,
      } as QueryFilter);
    }

    return this.getListItem(url, page, pageSize, undefined, filter).pipe(
      map((response) => {
        return response;
      }),
      catchError(() => {
        // Handle errors and return an empty array
        return of({
          data: [],
          count: 0,
          page: 1,
          total: 0,
        });
      }),
    );
  }

  // get career stream list
  getCareerStreamList(
    searchValue = '',
    effectiveDate = '',
    page = 1,
    pageSize = 25,
  ) {
    const url = `/api/career-streams/get-by`;
    const filter: QueryFilter[] = [
      {
        field: 'status',
        operator: '$eq',
        value: true,
      } as QueryFilter,
    ];
    // if searchValue is provided, add a filter to the query
    if (searchValue) {
      filter.push({
        field: 'search',
        operator: '$eq',
        value: searchValue,
      } as QueryFilter);
    }

    if (effectiveDate) {
      filter.push({
        field: 'effectiveDate',
        operator: '$eq',
        value: effectiveDate,
      } as QueryFilter);
    }
    return this.getListItem(url, page, pageSize, undefined, filter).pipe(
      map((response) => {
        return response;
      }),
      catchError(() => {
        // Handle errors and return an empty array
        return of({
          data: [],
          count: 0,
          page: 1,
          total: 0,
        });
      }),
    );
  }

  // get career band list
  getCareerBandList(
    careerStreamCode?: string,
    searchValue = '',
    effectiveDate = '',
    page = 1,
    pageSize = 25,
  ) {
    const url = `/api/bands/get-by`;

    const filter: QueryFilter[] = [
      {
        field: 'status',
        operator: '$eq',
        value: true,
      } as QueryFilter,
    ];

    if (careerStreamCode) {
      filter.push({
        field: 'careerStreamCode',
        operator: '$eq',
        value: careerStreamCode,
      } as QueryFilter);
    }

    // if effectiveDate is provided, add a filter to the query
    if (effectiveDate) {
      filter.push({
        field: 'effectiveDate',
        operator: '$eq',
        value: effectiveDate,
      } as QueryFilter);
    }
    // if searchValue is provided, add a filter to the query
    if (searchValue) {
      filter.push({
        field: 'search',
        operator: '$eq',
        value: searchValue,
      } as QueryFilter);
    }

    return this.getListItem(url, page, pageSize, undefined, filter).pipe(
      map((response) => {
        return response;
      }),
      catchError(() => {
        // Handle errors and return an empty array
        return of({
          data: [],
          count: 0,
          page: 1,
          total: 0,
        });
      }),
    );
  }

  // get cost center list
  getCostCenterList(
    costCenterCode?: string,
    searchValue = '',
    effectiveDate?: string,
    companyCode?: string,
    page = 1,
    pageSize = 25,
  ) {
    const url = `/api/cost-centers/get-by`;
    const filter: QueryFilter[] = [
      {
        field: 'status',
        operator: '$eq',
        value: true,
      } as QueryFilter,
    ];

    if (costCenterCode) {
      filter.push({
        field: 'code',
        operator: '$eq',
        value: costCenterCode,
      } as QueryFilter);
    }
    // if searchValue is provided, add a filter to the query
    if (searchValue) {
      filter.push({
        field: 'search',
        operator: '$eq',
        value: searchValue,
      } as QueryFilter);
    }

    if (companyCode) {
      filter.push({
        field: 'companyCode',
        operator: '$eq',
        value: companyCode,
      } as QueryFilter);
    }

    if (effectiveDate) {
      filter.push({
        field: 'effectiveDate',
        operator: '$eq',
        value: effectiveDate,
      } as QueryFilter);
    }

    return this.getListItem(url, page, pageSize, undefined, filter).pipe(
      map((response) => {
        return response;
      }),
      catchError(() => {
        // Handle errors and return an empty array
        return of({
          data: [],
          count: 0,
          page: 1,
          total: 0,
        });
      }),
    );
  }

  // get personal list
  getPersonalList(
    searchValue?: any,
    effectiveDate?: string,
    page = 1,
    pageSize = 25,
  ) {
    const url = `/api/personals/all-employees-job-datas`;
    const filter: QueryFilter[] = [
      {
        field: 'hrStatus',
        operator: '$eq',
        value: 'A',
      } as QueryFilter,
    ];

    if (searchValue) {
      if (typeof searchValue === 'string' && searchValue.trim() !== '') {
        filter.push({
          field: 'search',
          operator: '$eq',
          value: searchValue,
        } as QueryFilter);
      } else if (Array.isArray(searchValue) && searchValue.length > 0) {
        filter.push({
          field: 'search',
          operator: '$in',
          value: searchValue,
        } as QueryFilter);
      }
    }

    if (effectiveDate) {
      filter.push({
        field: 'effectiveDate',
        operator: '$eq',
        value: effectiveDate,
      } as QueryFilter);
    }
    return this.getListItem(url, page, pageSize, undefined, filter).pipe(
      map((response: any) => {
        if (response) {
          return response.map((item: any) => ({
            value: item.employeeId,
            label: item.fullName,
          }));
        }
        return [];
      }),
      catchError(() => {
        // Handle errors and return an empty array
        return of([]);
      }),
    );
  }

  // get personal details
  getPersonalDetails(employeeId: string) {
    // if employeeId is not provided, return an empty array
    if (!employeeId) {
      return of([]);
    }
    const url = `/api/personals/all-employees-job-datas`;
    const filter: QueryFilter[] = [
      {
        field: 'employeeId',
        operator: '$eq',
        value: employeeId,
      } as QueryFilter,
    ];

    return this.getListItem(url, 1, 10, undefined, filter).pipe(
      map((response: any) => {
        if (response) {
          return response;
        }
        return [];
      }),
      catchError(() => {
        // Handle errors and return an empty array
        return of([]);
      }),
    );
  }

  // get accept mine types
  checkFileType(file: NzUploadFile): boolean {
    const acceptedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'image/jpeg',
      'image/png',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel.sheet.macroEnabled.12',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.macroEnabled.12',
    ];
    return acceptedTypes.includes(file.type!);
  }

  parseDate(dateString: string): Date | null {
    // Remove any non-digit characters
    const cleanDateString = dateString.replace(/\D/g, '');

    let day: number, month: number, year: number;

    if (cleanDateString.length === 8) {
      // Format: ddMMyyyy
      day = parseInt(cleanDateString.slice(0, 2), 10);
      month = parseInt(cleanDateString.slice(2, 4), 10) - 1; // Month is 0-indexed
      year = parseInt(cleanDateString.slice(4), 10);
    } else {
      // Existing format: dd/MM/yyyy
      const parts = dateString.split('/');
      if (parts.length === 3) {
        day = parseInt(parts[0], 10);
        month = parseInt(parts[1], 10) - 1; // Month is 0-indexed
        year = parseInt(parts[2], 10);
      } else {
        return null; // Invalid format
      }
    }

    const date = new Date(year, month, day);
    if (
      date.getFullYear() === year &&
      date.getMonth() === month &&
      date.getDate() === day
    ) {
      return date;
    }
    return null;
  }

  getEmployeeIdConfigure() {
    const url = `api/configuration-sequences`;
    return this.getObject(url);
  }

  // get employee group list
  getEmployeeGroup() {
    const url = `/api/employee-groups`;
    const filter: QueryFilter[] = [
      {
        field: 'status',
        operator: '$eq',
        value: true,
      } as QueryFilter,
    ];
    return this.getListItem(url, 1, 25, undefined, filter).pipe(
      map((response) => {
        if (response && response['data'] && Array.isArray(response['data'])) {
          return response['data'].map((item: any) => ({
            value: item.code,
            label: item.longName?.default ?? item.name?.default ?? '',
          }));
        }
        return [];
      }),
      catchError(() => {
        // Handle errors and return an empty array
        return of([]);
      }),
    );
  }

  // check duplicate employee
  checkDuplicateEmployee(
    firstName: string | undefined,
    middleName: string | undefined,
    lastName: string | undefined,
    dateOfBirth: number | undefined,
    countryCode: string | undefined,
    type: string | undefined,
    numberInfo: string | undefined,
    enabled: string | undefined,
    validationType: EmployeeDuplicateCheckType,
  ) {
    const url = `/api/personals/valid-create-full`;
    const filter: QueryFilter[] = [];

    // if any of the fields are provided
    if (firstName) {
      filter.push({
        field: 'firstName',
        operator: '$eq',
        value: firstName.trim(),
      } as QueryFilter);
    }
    if (middleName) {
      filter.push({
        field: 'middleName',
        operator: '$eq',
        value: middleName.trim(),
      } as QueryFilter);
    }
    if (lastName) {
      filter.push({
        field: 'lastName',
        operator: '$eq',
        value: lastName.trim(),
      } as QueryFilter);
    }
    if (dateOfBirth !== null && dateOfBirth !== undefined) {
      filter.push({
        field: 'birthDate',
        operator: '$eq',
        value: dateOfBirth,
      } as QueryFilter);
    }
    if (countryCode) {
      filter.push({
        field: 'countryCode',
        operator: '$eq',
        value: countryCode,
      } as QueryFilter);
    }
    if (type) {
      filter.push({
        field: 'type',
        operator: '$eq',
        value: type,
      } as QueryFilter);
    }
    if (numberInfo) {
      filter.push({
        field: 'number',
        operator: '$eq',
        value: numberInfo,
      } as QueryFilter);
    }
    if (enabled) {
      filter.push({
        field: 'enabled',
        operator: '$eq',
        value: enabled,
      } as QueryFilter);
    }
    if (validationType) {
      filter.push({
        field: 'validationType',
        operator: '$eq',
        value: validationType,
      } as QueryFilter);
    }

    return this.getObjectByQuery(url, filter);
  }

  // get entity by id
  getEntityById(
    entityType:
      | 'legal-entities'
      | 'departments'
      | 'business-units'
      | 'divisions'
      | 'bands'
      | 'career-streams'
      | 'positions',
    id: string,
  ) {
    const url = `/api/${entityType}`;

    return this.getItem(url, id).pipe(
      map((response: any) => {
        if (response) {
          const item = response;
          return {
            value: item.code,
            label: item.longName?.default ?? item.name?.default ?? '',
            // Common fields
            id: item.id,
            companyId: item.companyId,
            // Entity specific fields based on type
            ...(entityType === 'legal-entities' && {
              businessUnitId: item.businessUnitId,
              departmentId: item.departmentId,
              divisionId: item.divisionId,
            }),
            ...(entityType === 'departments' && {
              legalEntityId: item.legalEntityId,
              businessUnitId: item.businessUnitId,
              location: item.location,
              costCenterId: item.costCenter?.id,
              code: item.code,
              divisionId: item.divisionId,
              managerType: item.managerType,
              headOfDepartment: item.headOfDepartment,
              managerPositionCode: item.managerPositionCode,
              managerPositionId: item.managerPositionId,
            }),
            ...(entityType === 'business-units' && {
              legalEntityId: item.legalEntityId,
              divisionId: item.divisionId,
            }),
            ...(entityType === 'divisions' && {
              legalEntityId: item.legalEntityId,
              businessUnitId: item.businessUnitId,
              businessUnitCode: item.businessUnitCode,
              businessUnitName: item.businessUnitName,
            }),
            ...(entityType === 'positions' && {
              matrixPositions: item.matrixPositions,
            }),
          };
        }
        return null;
      }),
      catchError((error) => {
        console.error(`Error fetching ${entityType} details:`, error);
        return of(null);
      }),
    );
  }

  /**
   * get the tree organization
   * @param pickType
   * @param code
   * @param effectiveDate
   * @returns
   */
  getTreeOrganization(
    pickType: 'Position' | 'Department',
    code: string,
    effectiveDate: number,
  ) {
    const url = `/api/trees/organization/pick/${pickType}/${code}/${effectiveDate}`;
    return this.getListItem(url).pipe(catchError(() => of([])));
  }

  /**
   * get the UTC time in seconds since epoch
   * @param inputDate
   * @returns
   */
  getUTCTimeInSecond(inputDate: string | Date) {
    let dateValue: Date;

    if (typeof inputDate === 'string') {
      // Try to parse the string date in multiple formats
      const parsedDate = this.parseDate(inputDate) || new Date(inputDate);
      dateValue = parsedDate;
    } else {
      dateValue = inputDate;
    }
    const dateUtc =
      new Date(
        Date.UTC(
          dateValue.getFullYear(),
          dateValue.getMonth(),
          dateValue.getDate(),
        ),
      ).getTime() / 1000;
    return dateUtc;
  }

  /**
   * Scrolls to the first error element on the screen
   * @returns void
   */
  static scrollToFirstError(): void {
    setTimeout(() => {
      const firstError = document.querySelector('.ant-form-item-explain-error');
      if (firstError) {
        firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
    }, 100);
  }

  /**
   * calculate employment
   * @param actionCode
   * @param employmentGroupCode
   * @param orgInstanceRcd
   * @param groupOriginalStartDate
   * @param oirOriginalStartDate
   * @param companyCode
   * @param effectiveDate
   * @param empSubGroupCode
   * @param empRecordNumber
   */
  calculateEmployment(
    actionCode: string,
    employmentGroupCode: string,
    orgInstanceRcd: string,
    groupOriginalStartDate: string,
    oirOriginalStartDate: string,
    companyCode: string,
    effectiveDate: string,
    empSubGroupCode: string,
    empRecordNumber: string,
  ) {
    const url = `/api/personals/calculate-employment`;
    const filter: QueryFilter[] = [
      {
        field: 'actionCode',
        operator: '$eq',
        value: actionCode,
      } as QueryFilter,
      {
        field: 'employmentGroupCode',
        operator: '$eq',
        value: employmentGroupCode,
      } as QueryFilter,
      {
        field: 'organizationalInstanceRcd',
        operator: '$eq',
        value: orgInstanceRcd,
      } as QueryFilter,
      {
        field: 'groupOriginalStartDate',
        operator: '$eq',
        value: groupOriginalStartDate,
      } as QueryFilter,
      {
        field: 'oirOriginalStartDate',
        operator: '$eq',
        value: oirOriginalStartDate,
      } as QueryFilter,
      {
        field: 'companyCode',
        operator: '$eq',
        value: companyCode,
      } as QueryFilter,
      {
        field: 'effectiveDate',
        operator: '$eq',
        value: effectiveDate,
      } as QueryFilter,
      {
        field: 'employeeSubGroupCode',
        operator: '$eq',
        value: empSubGroupCode,
      } as QueryFilter,
      {
        field: 'employeeRecordNumber',
        operator: '$eq',
        value: empRecordNumber,
      } as QueryFilter,
    ];

    return this.getObjectByQuery(url, filter).pipe(
      map((response: any) => {
        if (response) {
          return response;
        }
        return null;
      }),
      catchError(() => {
        return of(null);
      }),
    );
  }

  static getFileType(fileName: string): string {
    const extension = fileName.split('.').pop()?.toLowerCase();
    if (extension === 'xlsx' || extension === 'xls') {
      return 'excel';
    } else if (extension === 'csv') {
      return 'csv';
    }
    return 'default';
  }

  // validate job-data
  validateJobData(
    effectiveDate: string,
    companyCode: string,
    legalEntityCode: string,
    businessUnitCode: string,
    division: string,
    department: string,
    costCenter: string,
    location: string,
    careerStream: string,
    careerBand: string,
    reportPosition: string,
    jobCode: string,
    positionCode: string,
    businessTitleCode: string,
    actionCode: string,
    actionReasonCode: string,
    employeeGroupCode: string,
    employeeSubGroupCode: string,
    matrixManager: string,
    supervisor: string,
    regionCode: string,
    matrixReportPositionCodes: string,
    employeeLevelCode: string,
  ) {
    const url = `/api/personals/new/job-datas/validate-job-datas`;
    const filter: QueryFilter[] = [];

    // if any of the fields are provided
    if (effectiveDate) {
      filter.push({
        field: 'effectiveDate',
        operator: '$eq',
        value: effectiveDate,
      } as QueryFilter);
    }
    if (companyCode) {
      filter.push({
        field: 'companyCode',
        operator: '$eq',
        value: companyCode.trim(),
      } as QueryFilter);
    }
    if (legalEntityCode) {
      filter.push({
        field: 'legalEntityCode',
        operator: '$eq',
        value: legalEntityCode.trim(),
      } as QueryFilter);
    }
    if (businessUnitCode) {
      filter.push({
        field: 'businessUnitCode',
        operator: '$eq',
        value: businessUnitCode,
      } as QueryFilter);
    }
    if (division) {
      filter.push({
        field: 'division',
        operator: '$eq',
        value: division,
      } as QueryFilter);
    }
    if (department) {
      filter.push({
        field: 'department',
        operator: '$eq',
        value: department,
      } as QueryFilter);
    }
    if (costCenter) {
      filter.push({
        field: 'costCenter',
        operator: '$eq',
        value: costCenter,
      } as QueryFilter);
    }
    if (location) {
      filter.push({
        field: 'location',
        operator: '$eq',
        value: location,
      } as QueryFilter);
    }
    if (careerStream) {
      filter.push({
        field: 'careerStream',
        operator: '$eq',
        value: careerStream,
      } as QueryFilter);
    }
    if (careerBand) {
      filter.push({
        field: 'careerBand',
        operator: '$eq',
        value: careerBand,
      } as QueryFilter);
    }
    if (reportPosition) {
      filter.push({
        field: 'reportPosition',
        operator: '$eq',
        value: reportPosition,
      } as QueryFilter);
    }
    if (jobCode) {
      filter.push({
        field: 'jobCode',
        operator: '$eq',
        value: jobCode,
      } as QueryFilter);
    }
    if (positionCode) {
      filter.push({
        field: 'positionCode',
        operator: '$eq',
        value: positionCode,
      } as QueryFilter);
    }
    if (businessTitleCode) {
      filter.push({
        field: 'businessTitleCode',
        operator: '$eq',
        value: businessTitleCode,
      } as QueryFilter);
    }
    if (actionCode) {
      filter.push({
        field: 'actionCode',
        operator: '$eq',
        value: actionCode,
      } as QueryFilter);
    }
    if (actionReasonCode) {
      filter.push({
        field: 'actionReasonCode',
        operator: '$eq',
        value: actionReasonCode,
      } as QueryFilter);
    }
    if (employeeGroupCode) {
      filter.push({
        field: 'employeeGroupCode',
        operator: '$eq',
        value: employeeGroupCode,
      } as QueryFilter);
    }
    if (employeeSubGroupCode) {
      filter.push({
        field: 'employeeSubGroupCode',
        operator: '$eq',
        value: employeeSubGroupCode,
      } as QueryFilter);
    }
    if (matrixManager) {
      filter.push({
        field: 'matrixManager',
        operator: '$eq',
        value: matrixManager,
      } as QueryFilter);
    }
    if (matrixReportPositionCodes) {
      filter.push({
        field: 'matrixReportPositionCodes',
        operator: '$eq',
        value: matrixReportPositionCodes,
      } as QueryFilter);
    }
    if (supervisor) {
      filter.push({
        field: 'supervisor',
        operator: '$eq',
        value: supervisor,
      } as QueryFilter);
    }
    if (regionCode) {
      filter.push({
        field: 'regionCode',
        operator: '$eq',
        value: regionCode,
      } as QueryFilter);
    }
    if (employeeLevelCode) {
      filter.push({
        field: 'employeeLevelCode',
        operator: '$eq',
        value: employeeLevelCode,
      } as QueryFilter);
    }

    return this.getList(url, 0, 0, filter);
  }

  // permission
  getPermissionByFunctionCode(functionCode: string) {
    const url = `api/menus/${functionCode}/actions`;
    return this.getList(url).pipe(
      map((response: any) => {
        if (response) {
          return response.reduce((acc: any, cur: any) => {
            acc[cur.code] = cur.isActive;
            return acc;
          }, {});
        }
        return null;
      }),
      catchError(() => {
        return of(null);
      }),
    );
  }

  /**
   * Checks if a selected value exists in an options list
   * @param value The value to check
   * @param optionsList The list of options to check against
   * @returns Boolean indicating if the value exists in the options list
   */
  validateSelectedValue(value: any, optionsList: any[]): boolean {
    if (!value || !optionsList || !optionsList.length) {
      return true; // If there's no value or no options list, consider it valid
    }
    return optionsList.some((option) => option.value === value);
  }

  /**
   * Validates multiple selected values against their corresponding options lists
   * @param valuesToValidate Object with field names as keys and an object containing the value and its options list as values
   * @returns Object with field names as keys and validation result as values
   */
  validateSelectedValues(valuesToValidate: {
    [key: string]: { value: any; optionsList: any[] };
  }): { [key: string]: boolean } {
    const validationResults: { [key: string]: boolean } = {};

    for (const [fieldName, fieldInfo] of Object.entries(valuesToValidate)) {
      if (fieldInfo.value) {
        validationResults[fieldName] = this.validateSelectedValue(
          fieldInfo.value,
          fieldInfo.optionsList,
        );
      }
    }

    return validationResults;
  }

  // For FormArrays like nationalIds or phoneInfos
  validateFormArraySelectedValues(
    formArray: any[],
    fieldConfigs: { controlName: string; optionsListName: string }[],
    optionsLists: { [key: string]: any[] },
  ): { [key: string]: { [key: string]: boolean } } {
    const validationResults: { [key: string]: { [key: string]: boolean } } = {};

    if (!formArray || !formArray.length) {
      return validationResults;
    }

    formArray.forEach((formGroup, index) => {
      validationResults[index] = {};

      fieldConfigs.forEach((config) => {
        const value = formGroup.get(config.controlName)?.value;
        if (value) {
          validationResults[index][config.controlName] =
            this.validateSelectedValue(
              value,
              optionsLists[config.optionsListName],
            );
        }
      });
    });

    return validationResults;
  }

  /**
   * Recursively sanitize all string fields in an object or array by escaping HTML special characters.
   * @param obj The object, array, or value to sanitize
   * @returns The sanitized object, array, or value
   */
  static sanitize(obj: any): any {
    function stripDangerousPatterns(str: string): string {
      return str
        .replace(/on\w+\s*=\s*(['"]).*?\1/gi, '')
        .replace(/on\w+\s*=\s*[^\s>]+/gi, '')
        .replace(/javascript:/gi, '')
        .replace(/data:text\/html/gi, '')
        .replace(/<script.*?>.*?<\/script>/gis, '')
        .replace(/\[.*?\]\([^)]*base64,[^)]*\)/gi, '');
    }
    function escapeHtml(str: string): string {
      return str.replace(/[&<>"']/g, function (m) {
        switch (m) {
          case '&':
            return '&amp;';
          case '<':
            return '&lt;';
          case '>':
            return '&gt;';
          case '"':
            return '&quot;';
          case "'":
            return '&#39;';
          case '/':
            return '&#47;';
          default:
            return m;
        }
      });
    }
    if (typeof obj === 'string') {
      return escapeHtml(stripDangerousPatterns(obj));
    } else if (obj instanceof Date) {
      return obj; // Do not sanitize Date objects
    } else if (typeof File !== 'undefined' && obj instanceof File) {
      return obj; // Do not sanitize File objects
    } else if (Array.isArray(obj)) {
      return obj.map(HrFsBp001Service.sanitize);
    } else if (obj && typeof obj === 'object') {
      const sanitized: any = {};
      for (const key in obj) {
        if (Object.prototype.hasOwnProperty.call(obj, key)) {
          sanitized[key] = HrFsBp001Service.sanitize(obj[key]);
        }
      }
      return sanitized;
    }
    return obj;
  }
}

export enum EmployeeDuplicateCheckType {
  NATIONAL_ID = 1,
  PIT = 2,
  PHONE_NUMBER = 3,
  NAME_AND_DOB = 4,
}

// define the dialog size
export enum DialogSize {
  MIDDLE = '800px',
  LARGE = '1000px',
  EXTRA_LARGE = '1200px',
}
