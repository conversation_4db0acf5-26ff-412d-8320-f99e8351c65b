<hrdx-new-table
  [data]="data()"
  [total]="total()"
  [loading]="loading()"
  [pageIndex]="pageIndex()"
  [pageSize]="pageSize()"
  (selectedItemChange)="listOfSelectedItems.emit($event)"
  (pageSizeChange)="pageSizeChange.emit($event)"
  (pageIndexChange)="pageIndexChange.emit($event)"
  [showCheckbox]="showCheckbox()"
  [showCreateDataTable]="showCreateDataTable()"
  (createDataTable)="createDataTable.emit()"
  [class.group-table]="groupedData()?.length"
  [isFiltering]="isFiltering()"
  [searchValue]="searchValue()"
  (clearSearch)="clearSearch.emit()"
  (addFilter)="addFilter.emit()"
  [scrollHeight]="scrollHeight()"
  [showActionHeader]="showActionHeader()"
  [haveChildItemCheckbox]="true"
  [height]="height()"
  [rowLoadingSkeleton]="25"
  [activeRowPressed]="true"
  [storeSelectedItems]="layoutOptions()?.store_selected_items ?? false"
  class="expand-table"
  [showCustomExpand]="isPreviewReport()"
>
  <hrdx-thead>
    @for (column of headers(); track $index) {
      <hrdx-th
        [width]="column.options?.tabular?.column_width"
        [fixedLeft]="column.pinned && allowFixedLeftColumn()"
        [align]="column.options?.tabular?.align ?? 'left'"
        [showSort]="column.show_sort ?? false"
        (sortOrderChange)="onSortOrderChange($event, column.code)"
      >
        <!-- {{ column.title }} -->
        <hrdx-display
          class="header-title"
          [type]="'Label'"
          [value]="column.title"
          [title]="column.title"
        ></hrdx-display>
      </hrdx-th>
    }
  </hrdx-thead>

  <!-- row-expand -->
  <ng-container *ngFor="let data of data(); let i = index">
    <hrdx-tbody
      [hiddenAction]="true"
      [expand]="expandState()[i]"
      (expandChange)="expandRow(i)"
    >
      @if (isPreviewReport()) {
        @for (column of headers(); track $index) {
          <hrdx-td>
            <hrdx-display
              [type]="column?.display_type?.key || 'Label'"
              [value]="getValueColumn(data, column)"
              [title]="column.title"
              [href]="data.href"
              [extraConfig]="column?.extra_config"
            ></hrdx-display>
          </hrdx-td>
        }
      } @else {
        <hrdx-td [colSpan]="headers().length" className="group-header">
          <span class="sticky-content">{{ getRowExpandValue(data) }}</span>
        </hrdx-td>
      }
    </hrdx-tbody>
    <ng-container *ngIf="expandState()[i]">
      @if (isGetDetailsByApi()) {
        @if (loadingDetails()[i]) {
          <hrdx-tbody [loading]="true" *ngFor="let item of rowsLoading()">
            @for (column of headers(); track $index) {
              <hrdx-td> </hrdx-td>
            }
          </hrdx-tbody>
        } @else {
          <hrdx-tbody
            *ngFor="let item of groupsDetails()[i] ?? []"
            (clickRow)="handleClickRow.emit(item)"
            [idx]="item.id"
          >
            @for (column of headers(); track $index) {
              <hrdx-td>
                <hrdx-display
                  [type]="column?.display_type?.key || 'Label'"
                  [value]="item[column.code]"
                  [title]="column.title"
                  [href]="item['href']"
                ></hrdx-display>
              </hrdx-td>
            }
            <ng-container
              *ngIf="!hideRowAction()"
              row-actions
              [ngTemplateOutlet]="rowAction"
              [ngTemplateOutletContext]="{ item }"
            ></ng-container>
          </hrdx-tbody>
        }
      } @else {
        <hrdx-tbody
          *ngFor="let item of data?.['children']"
          (clickRow)="handleClickRow.emit(item)"
          [idx]="item.id"
        >
          @if (isPreviewReport()) {
            <hrdx-td> </hrdx-td>
          }
          @for (column of headers(); track $index) {
            <hrdx-td>
              <hrdx-display
                [type]="column?.display_type?.key || 'Label'"
                [value]="getValueColumn(item, column)"
                [title]="column.title"
                [href]="item.href"
                [extraConfig]="column?.extra_config"
              ></hrdx-display>
            </hrdx-td>
          }
          <ng-container
            *ngIf="!hideRowAction()"
            row-actions
            [ngTemplateOutlet]="rowAction"
            [ngTemplateOutletContext]="{ item }"
          ></ng-container>
        </hrdx-tbody>
      }
    </ng-container>
  </ng-container>
  <ng-container selected-actions [ngTemplateOutlet]="SelectedActions">
  </ng-container>
</hrdx-new-table>

<ng-template #SelectedActions>
  <ng-content select="[selected-actions]"></ng-content>
</ng-template>

<ng-template #rowAction let-item="item">
  <lib-action
    [actionOne]="actionOne()"
    [actionOneCondition]="actionOneCondition()"
    (onDropdownClick)="onDropdownClick.emit($event)"
    (onActionOneClick)="
      onActionOneClick.emit({
        row: item,
        id: $event.action,
        event: $event.event,
      })
    "
    (viewClickOne)="
      viewClickOne.emit({
        id: item.id,
        row: item,
        event: $event,
      })
    "
    (editClickOne)="
      editClickOne.emit({
        id: item.id,
        row: item,
        event: $event,
      })
    "
    (deleteClickOne)="
      deleteClickOne.emit({
        id: item.id,
        row: item,
        event: $event,
      })
    "
  />
</ng-template>
