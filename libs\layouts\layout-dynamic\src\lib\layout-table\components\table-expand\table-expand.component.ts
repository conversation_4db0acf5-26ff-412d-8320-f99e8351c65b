import {
  Component,
  input,
  output,
  signal,
  computed,
  effect,
  inject,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  DisplayComponent,
  NewTableComponent,
  TbodyComponent,
  TdComponent,
  ThComponent,
  TheadComponent,
  ToastMessageComponent,
} from '@hrdx/hrdx-design';
import { NzSafeAny } from 'ng-zorro-antd/core/types';
import { isEmpty, isNil } from 'lodash';
import { ActionComponent } from '../../action/action.component';
import { BffService, Data, mappingUrl } from '@hrdx-fe/shared';
import { catchError, of, switchMap, tap } from 'rxjs';
import { QueryFilter } from '@nestjsx/crud-request';

@Component({
  selector: 'lib-table-expand',
  standalone: true,
  imports: [
    CommonModule,
    NewTableComponent,
    TbodyComponent,
    TdComponent,
    ThComponent,
    TheadComponent,
    DisplayComponent,
    ActionComponent,
  ],
  templateUrl: './table-expand.component.html',
  styleUrl: './table-expand.component.less',
})
export class TableExpandComponent {
  static readonly ROWS_LOADING = 3;
  data = input<NzSafeAny>([]);
  total = input(0);
  loading = input(false);
  pageIndex = input(1);
  pageSize = input(25);
  headers = input<NzSafeAny>([]);
  layoutOptions = input<NzSafeAny>([]);
  functionSpec = input<NzSafeAny>([]);
  showCheckbox = input(true);
  showCreateDataTable = input(true);
  groupedData = input<NzSafeAny>([]);
  isFiltering = input<NzSafeAny>();
  searchValue = input<string>('');
  scrollHeight = input<string | null>('');
  showActionHeader = input<boolean>(true);
  hideRowAction = input<boolean>(false);
  actionOne = input<NzSafeAny>([]);
  actionOneCondition = input<Record<string, Record<string, boolean>>>({});
  allowFixedLeftColumn = input<boolean>(true);
  sortOrderChange = output<{ type: string | null; column: string }>();
  height = input<number | null>(null);
  getGroupDetailsApi = input<{ url: string }>();
  bffService = inject(BffService);
  loadingDetails = signal<Record<number, boolean>>({});
  toast = inject(ToastMessageComponent);
  queryFilter = input<QueryFilter[]>();

  isPreviewReport = input<boolean>(false);
  // pageCount = computed(() => {
  //   const data = this.data() as NzSafeAny[];
  //   return data?.reduce((acc, item) => acc + item?.children.length, 0);
  // });

  rowsLoading = computed(() => new Array(TableExpandComponent.ROWS_LOADING));

  isGetDetailsByApi = computed(() => !!this.getGroupDetailsApi());

  updateLoadingDetails(rowIndex: number, isLoading: boolean) {
    this.loadingDetails.update((prev) => ({ ...prev, [rowIndex]: isLoading }));
  }

  updateGroupDetails(rowIndex: number, data: Data[]) {
    this.groupsDetails.update((prev) => ({ ...prev, [rowIndex]: data }));
    const groupData = this.data();
    if (groupData[rowIndex]) {
      groupData[rowIndex]['children'] = data;
    }
  }

  groupsDetails = signal<Record<number, Data[]>>({});

  getGroupDetails(rowIdx: number) {
    const groupData = this.data()[rowIdx];
    if (!groupData) return;
    // // if group data have defined children data => don't have to get group datails
    // if (groupData.children) return;
    const apiConfig = this.getGroupDetailsApi();
    if (!apiConfig) return;

    const url = mappingUrl(apiConfig.url, {}, groupData);
    of(url)
      .pipe(
        tap(() => this.updateLoadingDetails(rowIdx, true)),

        switchMap((url) =>
          this.bffService.getPaginate(
            url,
            0,
            0,
            this.queryFilter(),
            this.searchValue(),
            this.sortOrder(),
          ),
        ),
        catchError((err) => {
          this.toast.showToast('error', 'Error', err?.error?.message ?? err);
          return of({ data: [] } as { data: Data[] });
        }),
        tap(() => this.updateLoadingDetails(rowIdx, false)),
      )
      .subscribe((res) => {
        const data = Array.isArray(res) ? res : res.data;
        this.updateGroupDetails(rowIdx, data);
        // this.updateLoadingDetails(rowIdx, false);
      });
  }

  getRowExpandValue(data: Record<string, NzSafeAny>) {
    const dataCombine = this.layoutOptions()?.row_data_combine;
    if (!dataCombine) return '--';
    const values = [];
    for (const key of dataCombine) {
      const value = data[key];
      if ((!isNil(value) && !isEmpty(value)) || typeof value === 'number') {
        values.push(value);
      }
    }

    return values.join(' - ');
  }
  expandState = signal<boolean[]>([]);

  expandEffect = effect(
    () => {
      // default collpase all row when data is changed
      this.expandState.set(Array(this.data()?.length ?? 0).fill(false));
      this;
    },
    { allowSignalWrites: true },
  );

  expandRow(i: number) {
    const expandState = this.expandState();
    expandState[i] = !expandState[i];
    this.expandState.set(expandState);
    if (!this.isGetDetailsByApi()) return;
    if (expandState[i]) {
      this.getGroupDetails(i);
    }
  }
  listOfSelectedItems = output<NzSafeAny>();
  pageSizeChange = output<number>();
  pageIndexChange = output<number>();
  createDataTable = output();
  clearSearch = output();
  addFilter = output();
  onDropdownClick = output<Event>();
  onActionOneClick = output<{
    id: string;
    row: NzSafeAny;
    event: Event;
  }>();
  viewClickOne = output<{
    id: string;
    row: NzSafeAny;
    event: Event;
  }>();
  editClickOne = output<{
    id: string;
    row: NzSafeAny;
    event: Event;
  }>();
  deleteClickOne = output<{
    id: string;
    row: NzSafeAny;
    event: Event;
  }>();
  handleClickRow = output<NzSafeAny>();

  sortOrder = signal<Record<string, string | null>>({});

  onSortOrderChange(type: string | null, column: string) {
    this.sortOrder.set({ [column]: type });
    this.sortOrderChange.emit({ type, column });
  }

  getValueColumn(data: NzSafeAny, column: NzSafeAny) {
    const value = data[column.code];
    if (
      isNil(value) &&
      this.isPreviewReport() &&
      column?.display_type?.key !== 'Tag'
    )
      return ' ';
    return value;
  }
}
