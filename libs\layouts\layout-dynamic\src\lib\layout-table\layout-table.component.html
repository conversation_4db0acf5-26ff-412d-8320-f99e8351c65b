<nz-layout class="layout-table">
  <div
    class="tabset-wrapper"
    *ngIf="tabset().length > 0 && customStyleTabset()"
  >
    <hrdx-tabs [selectedIndex]="defaultTabsetIndex()">
      <hrdx-tab
        *ngFor="let tab of tabset(); let idx = index"
        [title]="tab.title"
        (clicked)="onTabClick(idx)"
      ></hrdx-tab
    ></hrdx-tabs>
  </div>
  <nz-content class="content" #content [ngStyle]="customStyleContent()">
    <!-- TODO: create another layout for expand filter if needed -->
    <div class="expand-filter" *ngIf="expandFilter()">
      <ng-container [ngTemplateOutlet]="expandFilterTemplate"></ng-container>
    </div>

    <!-- put elements in there to track height to fixed table in viewport  -->
    <div class="elements" #elements>
      <div class="inline-filter" *ngIf="checkShowPreconditionFilter()">
        <div class="form-wrapper">
          <ng-container
            [ngTemplateOutlet]="inlineFilterTemplate"
          ></ng-container>
        </div>
      </div>

      <div
        class="create-form"
        *ngIf="expandCreateFormConfig() && checkPermission('calculate')"
      >
        <div class="form-wrapper">
          <ng-container [ngTemplateOutlet]="expandCreateForm"></ng-container>
        </div>
      </div>

      <div
        class="tabset-wrapper"
        *ngIf="tabset().length > 0 && !customStyleTabset()"
      >
        <hrdx-tabs [selectedIndex]="defaultTabsetIndex()">
          <hrdx-tab
            *ngFor="let tab of tabset(); let idx = index"
            [title]="tab.title"
            (clicked)="onTabClick(idx)"
          ></hrdx-tab
        ></hrdx-tabs>
      </div>
      <h3 class="matching-title" *ngIf="expandFilter() && showTableSearch()">
        Matching Results
      </h3>
      <div class="tool-table" *ngIf="showToolTable()">
        <div class="left">
          <h3
            class="matching-title expand"
            *ngIf="expandFilter() && !showTableSearch()"
          >
            Matching Results
          </h3>
          <div style="width: 300px" *ngIf="showTableSearch()">
            <nz-input-group
              class="input-group"
              [nzPrefix]="suffixIconSearch"
              [nzSuffix]="inputClearTpl"
            >
              <input
                title="search"
                class="search-input"
                type="text"
                nz-input
                [placeholder]="placeholderSearch()"
                [ngModel]="searchValue()"
                (ngModelChange)="onSearchValueChange($event)"
              />
            </nz-input-group>
            <ng-template #suffixIconSearch>
              <span class="search-icon">
                <hrdx-icon [icon]="'icon-magnifying-glass'"></hrdx-icon>
              </span>
              <!-- <span nz-icon nzType="icons:magnifying-glass"></span> -->
            </ng-template>
            <ng-template #inputClearTpl>
              @if (searchValue()) {
                <hrdx-icon
                  [icon]="'icon-close'"
                  (click)="onSearchValueChange('')"
                ></hrdx-icon>
              }
            </ng-template>
          </div>
          <hrdx-badge
            [status]="4"
            [count]="filterCount()"
            *ngIf="!expandFilter()"
          >
            <hrdx-button
              [type]="filterCount() > 0 ? 'secondary' : 'tertiary'"
              [onlyIcon]="true"
              icon="icon-funnel-simple-bold"
              (clicked)="filterClickOne(filterValue())"
              *ngIf="showTableFilter()"
              [title]="'Filters'"
            />
          </hrdx-badge>
        </div>
        <div class="right" *ngIf="showTableHeaderAction()">
          @for (tool of _toolTable() | async; track tool) {
            <hrdx-button
              [type]="tool.type ?? 'tertiary'"
              [onlyIcon]="tool.title ? false : true"
              [isLeftIcon]="tool.title ? true : false"
              [leftIcon]="tool.icon"
              [icon]="getToolIcon(tool)"
              (clicked)="onToolTableClick(tool)"
              [disabled]="tool.disabled"
              [title]="tool.title ?? getDefaultToolTableTitle(tool.id)"
              *ngIf="checkPermission(tool.id)"
            >
            </hrdx-button>
          }
          <hrdx-button
            [nzDropdownMenu]="groupMenu"
            [nzTrigger]="'click'"
            [nzPlacement]="'bottomLeft'"
            nz-dropdown
            [title]="groupInfo().title"
            [isLeftIcon]="true"
            [leftIcon]="groupInfo().icon"
            [type]="groupInfo().type"
            [isRightIcon]="groupLabel.length > 0"
            [rightIcon]="'x'"
            (clickedRightIcon)="removeGroupByKey($event)"
            *ngIf="showTableGroup()"
          />
          <nz-dropdown-menu #groupMenu="nzDropdownMenu">
            <ul nz-menu class="list-dropdown-group">
              <nz-radio-group
                [(ngModel)]="selectedGroupKey"
                (ngModelChange)="groupItem($event)"
              >
                @for (h of allHeaders(); track $index) {
                  <li class="list-radio">
                    <label nz-radio [nzValue]="h?.code">
                      {{ h?.title }}
                    </label>
                  </li>
                }
              </nz-radio-group>
            </ul>
          </nz-dropdown-menu>
          <lib-adjust-display
            [allHeaders]="headers()"
            (orderChange)="allHeaders.set($event)"
            #adjustDisplay
          />
        </div>
      </div>

      <div *ngIf="isValidFilterValue() && !expandFilter()">
        <hrdx-data-render
          [filterLst]="filterDataRenderValue()"
          [filterConfig]="filterConfigMapping()"
          (removedFilterItem)="removedFilterItem($event)"
          #filterDataRender
        />
      </div>
      <span class="filter-result-messages" *ngIf="showFilterResultsMessage()">
        {{ total() ?? 0 }} record(s) was found
      </span>
    </div>

    @if (emptyStateStyle() === 'illustration' && !loading() && total() <= 0) {
      <ng-container [ngTemplateOutlet]="illustrationEmptyState"></ng-container>
    } @else {
      <!-- expand table -->
      <ng-container
        *ngIf="functionSpec()?.layout_options?.row_type === 'expand'"
      >
        <lib-table-expand
          [data]="data()"
          [total]="total()"
          [loading]="loading()"
          [pageIndex]="pageIndex()"
          [pageSize]="pageSize()"
          [headers]="headers()"
          [functionSpec]="functionSpec()"
          [layoutOptions]="functionSpec()?.layout_options"
          (listOfSelectedItems)="listOfSelectedItems.set($event)"
          (pageSizeChange)="onPageSizeChange($event)"
          (pageIndexChange)="pageIndex.set($event)"
          [showCheckbox]="showTableCheckbox()"
          [showCreateDataTable]="showCreateDataButton()"
          (createDataTable)="createClick()"
          [isFiltering]="isFiltering()"
          [searchValue]="searchValue()"
          (clearSearch)="clearSearch()"
          (addFilter)="addFilter()"
          [scrollHeight]="tableScrollHeight()"
          [height]="tableHeight()"
          [showActionHeader]="showActionsMany()"
          [groupedData]="groupedData"
          (handleClickRow)="handleClickRow($event)"
          [hideRowAction]="hideRowAction()"
          [actionOne]="actionExtend()"
          (onActionOneClick)="
            onActionOneClick($event.row, $event, $event.event)
          "
          (viewClickOne)="viewClickOne($event.id, $event.row, $event.event)"
          (editClickOne)="editClickOne($event.id, $event.row, $event.event)"
          (deleteClickOne)="deleteClickOne($event.id, $event.row, $event.event)"
          [allowFixedLeftColumn]="allowFixedLeftColumn()"
          [getGroupDetailsApi]="layoutOptions()?.data_group?.group_details_api"
          (sortOrderChange)="sortOrderChange($event.type, $event.column)"
          [queryFilter]="filterQuery()"
        >
          <ng-container
            selected-actions
            [ngTemplateOutlet]="SelectedActions"
          ></ng-container>
        </lib-table-expand>
      </ng-container>

      <ng-container
        *ngIf="functionSpec()?.layout_options?.row_type !== 'expand'"
      >
        <hrdx-new-table
          [rowLoadingSkeleton]="!tableScrollHeightOption() ? 25 : 10"
          [data]="data()"
          [total]="total()"
          [loading]="loading()"
          [pageIndex]="pageIndex()"
          [pageSize]="pageSize()"
          (selectedItemChange)="listOfSelectedItems.set($event)"
          (pageSizeChange)="onPageSizeChange($event)"
          (pageIndexChange)="pageIndex.set($event)"
          [showCheckbox]="showTableCheckbox()"
          [showCreateDataTable]="showCreateDataButton()"
          (createDataTable)="createClick()"
          [class.group-table]="groupedData?.length"
          [isFiltering]="isFiltering()"
          [searchValue]="searchValue()"
          (clearSearch)="clearSearch()"
          (addFilter)="addFilter()"
          [scrollHeight]="tableScrollHeight()"
          [height]="tableHeight()"
          [showActionHeader]="showActionsMany()"
          [showPagination]="showTablePagination()"
          [isShowLengthPaginaton]="isShowLengthPaginaton()"
          [headers]="headers()"
          (orderChange)="orderChange($event)"
          [activeRowPressed]="true"
          [storeSelectedItems]="layoutOptions()?.store_selected_items ?? false"
          #table
        >
          <hrdx-thead>
            @for (column of headers(); track $index) {
              <hrdx-th
                [width]="column.options?.tabular?.column_width"
                [fixedLeft]="column.pinned && allowFixedLeftColumn()"
                [align]="column.options?.tabular?.align ?? 'left'"
                [showSort]="column.show_sort ?? false"
                [resizable]="column.resizable ?? true"
                [dragable]="column.dragable ?? true"
                [sortOrder]="
                  sortOrder()[column.extra_config?.sortByCode ?? column.code]
                "
                (sortOrderChange)="
                  sortOrderChange(
                    $event,
                    column.extra_config?.sortByCode ?? column.code
                  )
                "
                [colSpan]="column.extra_config?.colspan ?? 0"
                [rowSpan]="column.extra_config?.rowspan"
                [isGrouped]="column.extra_config?.isGrouped"
                [isNotSetMinWidth]="
                  column.extra_config?.not_set_min_width ?? false
                "
                [title]="column.title"
              >
                <!-- {{
                  column.extra_config?.hidden_column_title ? '' : column.title
                }} -->

                <hrdx-display
                  class="header-title"
                  [type]="'Label'"
                  [value]="
                    column.extra_config?.hidden_column_title
                      ? ' '
                      : column.title
                  "
                  [title]="
                    column.extra_config?.hidden_column_title
                      ? ' '
                      : column.title
                  "
                ></hrdx-display>
              </hrdx-th>
            }
          </hrdx-thead>

          <!-- row group -->
          <ng-container *ngIf="groupedData?.length">
            @for (data of groupedData; track data.key) {
              <hrdx-tbody [hiddenAction]="true">
                <hrdx-td [colSpan]="headers().length" className="group-header">
                  {{ groupLabel }}:
                  <hrdx-display
                    [type]="
                      getColumnBy(data.colCode).display_type?.key || 'Label'
                    "
                    [value]="data.realValue"
                    [title]="getColumnBy(data.colCode).title"
                  ></hrdx-display>
                </hrdx-td>
              </hrdx-tbody>
              @for (row of data.items; track $index) {
                <hrdx-tbody (clickRow)="handleClickRow(row)">
                  @for (column of headers(); track column.code) {
                    <hrdx-td>
                      <hrdx-display
                        [type]="column?.display_type?.key || 'Label'"
                        [value]="row[column.code]"
                        [title]="column.title"
                        [href]="column.href"
                        [extraConfig]="column.extra_config"
                        [extraData]="row"
                      ></hrdx-display>
                    </hrdx-td>
                  }
                  <ng-container
                    *ngIf="!hideRowAction()"
                    row-actions
                    [ngTemplateOutlet]="actionRow"
                    [ngTemplateOutletContext]="{ row: row }"
                  ></ng-container>
                </hrdx-tbody>
              } @empty {}
            } @empty {}
          </ng-container>

          <!-- row default -->
          <ng-container
            #defaultCase
            *ngIf="
              !groupedData?.length &&
              (!functionSpec()?.layout_options?.row_type ||
                functionSpec()?.layout_options?.row_type === 'default')
            "
          >
            @for (row of data(); track $index) {
              <hrdx-tbody
                (clickRow)="handleClickRow(row)"
                [isActionVisible]="isActionVisible"
                [disabled]="row?.['disabled']"
                [hiddenAction]="hideRowAction()"
              >
                @for (column of headers(); track $index) {
                  <hrdx-td>
                    <hrdx-display
                      [type]="column?.display_type?.key || 'Label'"
                      [value]="row[column.code]"
                      [title]="column.title"
                      (changeValue)="
                        onChangeValueCell($event, row, column.code)
                      "
                      [href]="column.href"
                      [extraConfig]="column.extra_config"
                      [extraData]="row"
                      [readOnly]="!editMode()"
                      (clickHandler)="
                        onDisplayClickHandler($event, row, column.extra_config)
                      "
                    ></hrdx-display>
                  </hrdx-td>
                }

                <ng-container
                  *ngIf="!hideRowAction()"
                  row-actions
                  [ngTemplateOutlet]="actionRow"
                  [ngTemplateOutletContext]="{ row: row }"
                ></ng-container>
              </hrdx-tbody>
            }
          </ng-container>

          <ng-container selected-actions [ngTemplateOutlet]="SelectedActions">
          </ng-container>
        </hrdx-new-table>
      </ng-container>
    }
  </nz-content>
  <nz-footer *ngIf="pageFooterOptions()?.visible">
    <ng-container [ngTemplateOutlet]="footer"></ng-container>
  </nz-footer>
</nz-layout>

<ng-template #SelectedActions>
  @if (actionsMany()) {
    @for (action of _actionsMany() | async; track action.id) {
      <hrdx-button
        [type]="action.type"
        [title]="action.title"
        [leftIcon]="action.icon"
        [isLeftIcon]="true"
        [size]="'xsmall'"
        (clicked)="onActionsManyClick(action.id)"
        *ngIf="checkPermission(action.id)"
      />
    }
  } @else {
    <hrdx-button
      [type]="'secondary'"
      [size]="'xsmall'"
      [leftIcon]="'icon-trash-bold'"
      [isLeftIcon]="true"
      [title]="'Delete'"
      (clicked)="deleteClickMany()"
      *ngIf="
        showActionsMany() && checkPermission('delete') && showActionsDelete()
      "
    />
  }
</ng-template>
<ng-template #footer>
  <!-- TODO: should move to hrdx-design component -->
  <div class="footer-wrapper" *ngIf="pageFooterButtons()?.length">
    <div class="footer-btns">
      @for (button of pageFooterButtons(); track button.id) {
        <hrdx-button
          [title]="button.title ?? ''"
          [type]="button.type"
          (clicked)="pageFooterButtonClicked(button.id)"
          [size]="button.size"
          [leftIcon]="button.leftIcon"
          [isLeftIcon]="button.isLeftIcon"
          [isLoading]="
            (button.id === 'proceed' || button.id === 'save') && isLoading()
          "
        >
        </hrdx-button>
      }
    </div>
  </div>
</ng-template>

<ng-template #actionRow let-row="row">
  <ng-container>
    @if (actionOne()) {
      @for (action of actionOne(); track action.id) {
        @if (checkPermission(action.id, row)) {
          @if (
            action.children &&
            filterRowActionChildrenWithPermission(row, action.children).length >
              0
          ) {
            <hrdx-button
              [type]="'ghost-gray'"
              [nzDropdownMenu]="rowActions"
              [nzTrigger]="'click'"
              [nzPlacement]="'bottomLeft'"
              [size]="'xsmall'"
              nz-dropdown
              [onlyIcon]="true"
              [icon]="'icon-dots-three-vertical-bold'"
              [title]="'More'"
              (mouseenter)="onDropdownClick($event)"
            >
            </hrdx-button>

            <nz-dropdown-menu #rowActions="nzDropdownMenu">
              <ul
                nz-menu
                class="dropdown-action"
                (mouseleave)="onDropdownLeave($event)"
              >
                <li
                  class="list-item"
                  *ngFor="
                    let c of filterRowActionChildrenWithPermission(
                      row,
                      action.children
                    );
                    let i = index
                  "
                >
                  <hrdx-button
                    [type]="'ghost-gray'"
                    [size]="'small'"
                    [isLeftIcon]="true"
                    (click)="onActionOneClick(row, c, $event)"
                    [leftIcon]="c.icon"
                    [title]="c.title"
                    [disabled]="disabledRowAction(row, c)"
                    *ngIf="showRowAction(row, c)"
                  ></hrdx-button>
                </li>
              </ul>
            </nz-dropdown-menu>
          } @else {
            <hrdx-button
              [type]="'ghost-gray'"
              [icon]="actionOneIcon()?.[action.id]"
              [onlyIcon]="true"
              [size]="'xsmall'"
              (clicked)="onActionOneClick(row, action, $event)"
              [title]="action.title ?? getDefaultRowActionTitle(action.id)"
              [disabled]="disabledRowAction(row, action)"
              *ngIf="showRowAction(row, action)"
            />
          }
        }
      }
    } @else {
      @if (checkPermission('read')) {
        <hrdx-button
          [type]="'ghost-gray'"
          [size]="'xsmall'"
          [icon]="'icon-eye-bold'"
          [onlyIcon]="true"
          (clicked)="viewClickOne(row.id, row, $event)"
          [title]="getDefaultRowActionTitle('view')"
        />
      }
      @if (checkPermission('edit')) {
        <hrdx-button
          [type]="'ghost-gray'"
          [size]="'xsmall'"
          [onlyIcon]="true"
          [icon]="'icon-pencil-simple-bold'"
          (clicked)="editClickOne(row.id, row, $event)"
          [title]="getDefaultRowActionTitle('edit')"
        />
      }
      @if (checkPermission('lock')) {
        <hrdx-button
          [type]="'ghost-gray'"
          [size]="'xsmall'"
          [onlyIcon]="true"
          [icon]="'icon-lock-bold'"
          [title]="getDefaultRowActionTitle('lock')"
        />
      }
      @if (checkPermission('delete')) {
        <hrdx-button
          [type]="'ghost-gray'"
          [size]="'xsmall'"
          [onlyIcon]="true"
          [icon]="'icon-trash-bold'"
          (clicked)="deleteClickOne(row.id, row, $event)"
          [title]="getDefaultRowActionTitle('delete')"
        />
      }
    }
  </ng-container>
</ng-template>

<!-- detail dialog with detail_function_spec -->
<lib-layout-detail
  [dialogVisible]="layoutDetailDialogVisible()"
  [config]="dialogConfig()"
  [value]="dialogValue()"
  [id]="dialogValue()?.[fsKey()]"
  [inheritDefault]="functionSpec().inherited_default_detail ?? false"
  (dialogVisibleChange)="layoutDetailDialogVisible.set($event)"
  [title]="dialogTitle()"
  [fsId]="functionSpec().detail_function_spec"
  [footerButtonsCustom]="layoutDetailFooterButtonsCustom()"
  (clickedModalButton)="clickedModalButton($event)"
  (refresh)="refreshDataTable()"
  [url]="url()"
  [childrenActions]="childrenActions()"
  [disabledButtons]="disabledDialogButtons()"
  *ngIf="functionSpec().detail_function_spec && layoutDetailDialogVisible()"
  #layoutDetailDialog
>
</lib-layout-detail>

@if (functionSpec().detail_function_spec && dialogType() === 'view') {
  <!-- <lib-layout-detail
    [dialogVisible]="dialogVisible()"
    [config]="dialogConfig()"
    [value]="dialogValue()"
    [id]="dialogValue()?.[fsKey()]"
    [inheritDefault]="functionSpec().inherited_default_detail ?? false"
    (dialogVisibleChange)="dialogVisible.set($event)"
    [title]="dialogTitle()"
    [fsId]="functionSpec().detail_function_spec"
    [footerButtonsCustom]="layoutDetailFooterButtonsCustom()"
    (clickedModalButton)="clickedModalButton($event)"
    (refresh)="refreshDataTable()"
    [url]="url()"
    [childrenActions]="childrenActions()"
    [disabledButtons]="disabledDialogButtons()"
  >
  </lib-layout-detail> -->
} @else {
  @if (isGroupEdit()) {
    <!-- step horizontal modal -->
    <lib-layout-step-dialog
      [(dialogVisible)]="dialogVisible"
      [stepTitle]="dialogTitle()"
      [config]="functionSpec()?.create_form"
      [value]="dialogValue() ?? {}"
      [typeDialog]="'create'"
      (clickedModalButton)="clickedModalButton($event)"
      [isSaveDraft]="true"
      *ngIf="dialogVisible()"
    >
    </lib-layout-step-dialog>
    <!-- step horizontal modal -->
  } @else {
    <lib-layout-dialog
      [dialogVisible]="dialogVisible()"
      (dialogVisibleChange)="onDialogVisibleChange($event)"
      [config]="dialogConfig()"
      [dialogType]="dialogType()"
      [id]="dialogValue()?.[fsKey()]"
      [url]="_url()"
      [customDetailBackendUrl]="customDetailBackendUrl()"
      [addOnValue]="addOnValue()"
      [noNeedConfirm]="noNeedConfirm()"
      [value]="dialogValue()"
      [title]="dialogTitle()"
      [showSaveAddButton]="showDialogSaveAddBtn()"
      [showDeleteButton]="showDeleteButton()"
      [footerButtonsCustom]="footerButtonsCustom()"
      (submitValue)="dialogSubmit($event)"
      (clickedModalButton)="clickedModalButton($event)"
      [isSubmitBtnLoading]="isLoading()"
      [showFooter]="showDialogFooter() | async"
      [overrideValue]="dialogOverrideValue()"
      [isViewDetailConfig]="isViewDetailConfig()"
      [dataViewDetailConfig]="dataViewDetailConfig()"
      [showSubmitButton]="showDialogSubmitBtn()"
      [checkPermission]="checkPermission"
      [disabledActionLst]="disabledActionLst()"
      [disabledActions]="disabledDetailDialogButtons()"
      [maskClosable]="false"
      [isPopup]="isPopup()"
      [dataLayout]="dataLayout()"
      [showAvatarInfo]="!!dataLayout()?.['dataProfile']"
      [isNewDynamicForm]="isNewDynamicForm()"
      [loadingDetailSchedule]="loadingDetailSchedule()"
      [faceCode]="_faceCodeByRecord() ?? faceCode()"
      #layoutDialog
      [centered]="true"
      [dialogFooter]="dialogFooter()"
      [customValueBeforeEdit]="customValueBeforeEdit()"
      [authAction]="authAction()"
      [valueStorageConfig]="formValueStorage()"
      [accountPermissions]="accountPermissions()"
      [extraDataSchedule]="extraDataSchedule()"
      *ngIf="dialogVisible()"
    >
    </lib-layout-dialog>
  }
}

<!-- detail dialog -->
<lib-layout-dialog
  [dialogVisible]="detailDialogVisible()"
  (dialogVisibleChange)="detailDialogVisible.set($event)"
  [config]="functionSpec()?.form_config"
  [dialogType]="'view'"
  [id]="dialogValue()?.[fsKey()]"
  [url]="_url()"
  [customDetailBackendUrl]="customDetailBackendUrl()"
  [addOnValue]="addOnValue()"
  [value]="dialogValue()"
  [title]="detailDialogTitle()"
  [showDeleteButton]="showDeleteButton()"
  [footerButtonsCustom]="footerButtonsCustom()"
  (submitValue)="dialogSubmit($event)"
  (clickedModalButton)="clickedModalButton($event)"
  [showFooter]="showDialogFooter() | async"
  [checkPermission]="checkPermission"
  [maskClosable]="false"
  [dataLayout]="dataLayout()"
  [showAvatarInfo]="!!dataLayout()?.['dataProfile']"
  [isNewDynamicForm]="isNewDynamicForm()"
  [disabledActionLst]="disabledActionLst()"
  [disabledActions]="disabledDetailDialogButtons()"
  [dialogFooter]="detailDialogFooter()"
  *ngIf="detailDialogVisible()"
  [permissionKey]="permissionKey()"
  [checkPermissionDetail]="checkPermissionDetail"
  [showDuplicateButton]="layoutOptions()?.show_dialog_duplicate_button ?? true"
  [isCheckPermissionWithAccessType]="
    layoutOptions()?.is_check_permission_with_accessType ?? false
  "
  #detailDialog
>
</lib-layout-dialog>

<!-- proceed form -->

<lib-layout-dialog
  [(dialogVisible)]="proceedDialogVisible"
  (dialogVisibleChange)="proceedDialogVisible.set($event)"
  [config]="dialogProceedConfig()"
  [dialogType]="'proceed'"
  [value]="proceedDialogValue()"
  [title]="functionSpec()?.create_form?.title ?? title()"
  (submitValue)="onProceedSubmit($event, 'create', true)"
  [showSaveAddButton]="false"
  [url]="url()"
  [checkPermission]="checkPermission"
  [disabledActionLst]="disabledActionLst()"
  *ngIf="proceedDialogVisible()"
  [footerButtonsCustom]="proceedFooterButtonsCustom"
  [centered]="true"
></lib-layout-dialog>

<!-- proceedCustom form -->
<lib-layout-dialog
  [(dialogVisible)]="proceedCustomDialogVisible"
  (dialogVisibleChange)="proceedCustomDialogVisible.set($event)"
  [config]="dialogProceedCustomConfig()"
  [dialogType]="'proceedCustom'"
  [value]="proceedCustomDialogValue()"
  [title]="functionSpec()?.create_form?.title ?? title()"
  (submitValue)="onProceedSubmit($event)"
  [showSaveAddButton]="false"
  [url]="url()"
  [checkPermission]="checkPermission"
  [disabledActionLst]="disabledActionLst()"
  *ngIf="proceedCustomDialogVisible()"
  [footerButtonsCustom]="proceedFooterButtonsCustom"
  [centered]="true"
></lib-layout-dialog>

<!-- Hire form -->
<lib-layout-dialog
  #hireForm
  [(dialogVisible)]="isActionHireVisible"
  (dialogVisibleChange)="closeDialogHire()"
  [config]="hireFormCustomConfig()"
  [dialogType]="'proceed'"
  [title]="functionSpec()?.create_form?.title ?? title()"
  (submitValue)="onHireSubmit($event)"
  [showSaveAddButton]="false"
  [url]="url()"
  [checkPermission]="checkPermission"
  [disabledActionLst]="disabledActionLst()"
  [footerButtonsCustom]="proceedFooterButtonsCustom"
  *ngIf="isActionHireVisible"
></lib-layout-dialog>

<!-- Sub Detail form -->
<lib-layout-dialog
  [(dialogVisible)]="isSubDetailVisible"
  (dialogVisibleChange)="isSubDetailVisible = false"
  [config]="dialogConfig()"
  [id]="dialogValue()?.[fsKey()]"
  [dialogType]="'view'"
  [title]="dialogTitle()"
  [showSaveAddButton]="false"
  [url]="url()"
  [checkPermission]="checkPermission"
  [disabledActionLst]="disabledActionLst()"
  [showFooter]="false"
  *ngIf="isSubDetailVisible"
></lib-layout-dialog>

<!-- define data history -->
@if (historyDialogVisible) {
  @if (
    functionSpec().detail_function_spec &&
    functionSpec().layout_options?.show_history_detail_function
  ) {
    <lib-layout-history-detail
      [(dialogVisible)]="historyDialogVisible"
      (dialogVisibleChange)="historyDialogVisible = $event"
      [config]="functionSpec()?.form_config"
      [menuItemName]="functionSpec()?.title"
      [cancelBtn]="false"
      [url]="url()"
      [selectedData]="historyData()"
      (navigateEmit)="
        onNavigateEmitHistory(
          $event.type,
          $event.item,
          $event.overrideValue,
          $event.cb
        )
      "
      [showInsertNewRecord]="showHistoryInsertBtn()"
      [showSearchBar]="showHistorySearchBar()"
      [isCollapsContainer]="isCollapsContainer()"
      [showArrowCollaps]="showArrowCollaps()"
      [disabledEventCollaps]="disabledEventCollaps()"
      [widgetHeaderOptions]="historyWidgetHeaderOptions()"
      [selectedHistoryId]="selectedHistoryId()"
      [fsId]="functionSpec().detail_function_spec ?? null"
      [fsKey]="fsKey()"
      [options]="historyDialogOptions()"
    >
    </lib-layout-history-detail>
  } @else {
    <lib-layout-history
      [(dialogVisible)]="historyDialogVisible"
      (dialogVisibleChange)="historyDialogVisible = $event"
      [config]="functionSpec()?.form_config"
      [menuItemName]="functionSpec()?.title"
      [cancelBtn]="false"
      [url]="url()"
      [selectedData]="historyData()"
      (navigateEmit)="
        onNavigateEmitHistory(
          $event.type,
          $event.item,
          $event.overrideValue,
          $event.cb
        )
      "
      [showInsertNewRecord]="showHistoryInsertBtn()"
      [showSearchBar]="showHistorySearchBar()"
      [isCollapsContainer]="isCollapsContainer()"
      [showArrowCollaps]="showArrowCollaps()"
      [disabledEventCollaps]="disabledEventCollaps()"
      [widgetHeaderOptions]="historyWidgetHeaderOptions()"
      [selectedHistoryId]="selectedHistoryId()"
      [isCustomInsertNewProceed]="isCustomInsertNewProceed()"
      [filterConfig]="historyFilterConfig()"
      [filterMethod]="historyFilterMethod()"
      [addOnValue]="addOnValue()"
      [options]="historyDialogOptions()"
      [functionSpec]="functionSpec()"
      [checkPermission]="checkPermission"
      [checkPermissionDetail]="checkPermissionDetail"
      [actionDetailLst]="actionDetailLst()"
      [disabledActionLst]="disabledActionLst()"
      [dataLayout]="dataLayout()"
      [showAvatarInfo]="!!dataLayout()?.['dataProfile']"
      [skipInsertNewProceed]="skipInsertNewProceedHistory()"
      [isLayoutWidget]="isLayoutWidget()"
      [isNewDynamicForm]="isNewDynamicForm()"
      [faceCode]="faceCode()"
      [actions]="historyDialogActions()"
      [isCheckPermissionWithAccessType]="
        layoutOptions()?.is_check_permission_with_accessType ?? false
      "
      #layoutHistory
    ></lib-layout-history>
  }
}
<!-- modal dialog for actions many -->
<lib-layout-modal-dialog
  [(dialogVisible)]="modalDialogVisible"
  [dialogType]="modalDialogType()"
  [config]="modalDialogConfig()"
  [title]="modalDialogTitle()"
  [dialogSize]="modalDialogSize()"
  [value]="modalDialogValue()"
  (submitValue)="modalDialogSubmit($event)"
  [actionBtnFooter]="actionBtnFooterModalDialog"
  [isLoading]="loading()"
  [extendData]="modalExtendData()"
  *ngIf="modalDialogVisible()"
>
</lib-layout-modal-dialog>

<!-- expand filter template -->
<ng-template #expandFilterTemplate>
  <div class="expand-filter-wrapper" *ngIf="expandFilter()">
    <lib-layout-expand-filter
      [config]="functionSpec().filter_config"
      (submitValue)="filterSubmit($event)"
      [value]="expandFilterValue()"
    ></lib-layout-expand-filter>
  </div>
</ng-template>

<!-- inline filter template -->
<ng-template #inlineFilterTemplate>
  <div class="inline-filter-wrapper" *ngIf="preconditionFilter()">
    <lib-inline-form
      [value]="preconditionFilterValue()"
      [config]="preconditionFilterConfig()"
      [showActionButton]="isAutoFilter() === false"
      (submitValue)="onPreconditionFilter($event.value)"
      [addOnValue]="addOnValue()"
      [reload]="reloadPreconditionFilter()"
      (valueChanges)="
        isAutoFilter()
          ? onPreconditionFilter($event.value)
          : handlePreconditionFilterValueChanges($event.value)
      "
      [options]="{
        submitButton: preconditionFilter()?.form_settings?.submit_button,
        formSize: preconditionFilter()?.form_settings?.size,
        border: preconditionFilter()?.form_settings?.border,
        padding: preconditionFilter()?.form_settings?.padding,
      }"
      [faceCode]="faceCode()"
    ></lib-inline-form>
  </div>
</ng-template>

<ng-template #expandCreateForm>
  <lib-inline-form
    [config]="functionSpec()?.form_config"
    [options]="{
      submitButton: { title: expandCreateFormConfig()?.submit_button_title },
      formSize: 'auto-fit',
    }"
    (submitValue)="
      dialogSubmit({
        type: 'create',
        value: $event.value,
        callback: $event.callback,
        authAction: functionSpec().form_config?.authAction,
      })
    "
    [reload]="reloadExpandCreateForm()"
    [formType]="'create'"
    [faceCode]="faceCode()"
  ></lib-inline-form>
</ng-template>

<!-- modal for run action in table tool -->
<!-- TODO: should remove and config on tool -->
<hrdx-modal
  [size]="'small'"
  [isVisible]="runPopupVisible()"
  [title]="'Confirmation required'"
  (canceled)="runPopupVisible.set(false)"
  [footer]="runPopupFooter"
  [wrapClassName]="'run-popup'"
>
  <ng-container>
    <dynamic-form
      [config]="runPopupConfig().fields ?? []"
      #runForm
    ></dynamic-form>
  </ng-container>
</hrdx-modal>

<ng-template #runPopupFooter>
  <div class="dialog--footer">
    <hrdx-button
      [title]="'Cancel'"
      [type]="'tertiary'"
      (clicked)="runPopupVisible.set(false)"
    />
    <hrdx-button
      [type]="'primary'"
      [title]="'Confirm'"
      (clicked)="runPopupVisible.set(false)"
    />
  </div>
</ng-template>

<!-- modal for note action in table tool -->
<!-- TODO: should remove and config on tool -->

<hrdx-modal
  [isVisible]="paymentDialogVisible()"
  [title]="'Payment confirm'"
  (canceled)="paymentDialogVisible.set(false)"
  [footer]="paymentFooter"
  [wrapClassName]="'payment-popup'"
>
  <ng-container>
    <dynamic-form
      [config]="functionSpec().payment_config?.fields ?? []"
      #paymentForm
    ></dynamic-form>
  </ng-container>
</hrdx-modal>

<ng-template #paymentFooter>
  <div class="dialog--footer">
    <hrdx-button
      [title]="'Cancel'"
      [type]="'tertiary'"
      (clicked)="paymentDialogVisible.set(false)"
    />
    <hrdx-button
      [type]="'primary'"
      [title]="'Save'"
      (clicked)="paymentDialogVisible.set(false)"
    />
  </div>
</ng-template>

<hrdx-modal
  [isVisible]="multiDialogVisible()"
  [title]="'Multi typing - Employee Termination Settlement Information'"
  (canceled)="multiDialogVisible.set(false)"
  [footer]="multiTypingFooter"
  [wrapClassName]="'multi-popup'"
>
  <ng-container>
    <dynamic-form
      [config]="functionSpec()?.multi_typing_config?.fields ?? []"
      [sources]="{}"
      [variables]="{}"
      [readOnly]="false"
      [ppxClass]="'ppxm-style'"
      [extend]="{ formType: 'edit' }"
      #multiForm
    ></dynamic-form>
  </ng-container>
</hrdx-modal>

<ng-template #multiTypingFooter>
  <div class="dialog--footer">
    <hrdx-button
      [title]="'Cancel'"
      [type]="'tertiary'"
      (clicked)="multiDialogVisible.set(false)"
    />
    <hrdx-button
      [type]="'primary'"
      [title]="'Save'"
      (clicked)="multiDialogVisible.set(false)"
    />
  </div>
</ng-template>

<!-- Generate report -->
<hrdx-modal
  [isVisible]="isPreview"
  [title]="reportPreviewTitleTemplate"
  [size]="'large'"
  (canceled)="onCancel()"
  [centered]="true"
  [wrapClassName]="
    typePreviewPopup() === PREVIEW_TYPE.CV ? 'cv' : 'popup-report'
  "
  [footer]="generateFooter"
  *ngIf="isPreview"
  [maskClosable]="false"
>
  <div class="report-content">
    <div class="report-subheader">
      {{ reportCompanyName() }}
    </div>
    <div *ngIf="!isLoadPdf()" class="report-title">
      <div class="report-title-name">{{ reportTitle() }}</div>
      <div class="report-title-subname">
        {{ reportSubtitle() }}
      </div>
    </div>
    @switch (typePreviewPopup()) {
      @case (PREVIEW_TYPE.CV) {
        <!-- preview CV -->
        <ng-container>
          <hrdx-preview-cv
            [cvData]="functionSpec()?.layout_options?.cvData ?? []"
          >
          </hrdx-preview-cv>
        </ng-container>
      }
      @case (PREVIEW_TYPE.TAB_TABLE) {
        <!-- preview TAB_TABLE -->
        <ng-container>
          <hrdx-preview-table
            [isTab]="true"
            [previewTabTable]="
              functionSpec()?.layout_options?.previewTabTable ?? []
            "
            [scrollValue]="
              functionSpec()?.layout_options?.scrollValue ?? {
                x: '2000px',
                y: '400px',
              }
            "
          >
          </hrdx-preview-table>
        </ng-container>
      }
      @case (PREVIEW_TYPE.FORM) {
        <ng-container>
          <dynamic-form
            [config]="functionSpec().layout_options?.previewForm.fields ?? []"
            [sources]="functionSpec().layout_options?.previewForm.sources ?? {}"
            [variables]="
              functionSpec().layout_options?.previewForm.variables ?? {}
            "
            [readOnly]="false"
            [ppxClass]="'ppxm-style'"
            [extend]="{
              formType: 'edit',
            }"
            #previewForm
          ></dynamic-form>
        </ng-container>
      }
      @case (PREVIEW_TYPE.TABLE_EXPAND) {
        <!-- preview table expand -->
        <ng-container>
          <lib-table-expand
            [data]="previewTableData()"
            [total]="previewTableTotal()"
            [loading]="contentLoading()"
            [pageIndex]="previewTablePageIndex()"
            [pageSize]="previewTablePageSize()"
            [headers]="previewTableCol()"
            (pageSizeChange)="onPreviewTablePageSizeChange($event)"
            (pageIndexChange)="onPreviewTablePageIndexChange($event)"
            [showCheckbox]="false"
            [showCreateDataTable]="false"
            [isFiltering]="false"
            [scrollHeight]="'52vh'"
            [showActionHeader]="false"
            [hideRowAction]="true"
            [isPreviewReport]="true"
          >
          </lib-table-expand>
        </ng-container>
      }
      @default {
        <!-- preview table -->
        <ng-container>
          @if (isLoadPdf()) {
            <hrdx-file-stream [streamData]="streamData"> </hrdx-file-stream>
          } @else {
            <hrdx-preview-table
              [previewTableCol]="this.previewTableCol()"
              [rawData]="this.previewTableData()"
              [previewTableTransformConfig]="this.previewTableTransformConfig()"
              [previewTableDynamicHeaderConfig]="
                this.previewTableDynamicHeaderConfig()
              "
              [total]="this.previewTableTotal()"
              [pageSize]="this.previewTablePageSize()"
              [pageIndex]="this.previewTablePageIndex()"
              (pageSizeChange)="onPreviewTablePageSizeChange($event)"
              (pageIndexChange)="onPreviewTablePageIndexChange($event)"
              [contentLoading]="contentLoading()"
              [groupingConfig]="this.previewTableGroupingConfig()"
            >
            </hrdx-preview-table>
          }
        </ng-container>
      }
    }
  </div>
</hrdx-modal>

<!-- report preview title  -->
<ng-template #reportPreviewTitleTemplate>
  <div class="report-header">
    <span class="report-header-title">
      {{ customDialogTitle() }}
    </span>
    <div class="report-header-box-right"></div>
  </div>
</ng-template>

<ng-template #downloadBtn>
  <hrdx-button
    [type]="'secondary'"
    size="default"
    [nzDropdownMenu]="menu"
    [nzTrigger]="'click'"
    [nzPlacement]="'bottomLeft'"
    nz-dropdown
    [title]="'Download'"
    [rightIcon]="'icon-caret-down-bold'"
    [isRightIcon]="true"
    [ngClass]="'download-btn'"
    [nzOverlayClassName]="'download-btn'"
    [isLoading]="isLoading()"
  />

  <nz-dropdown-menu #menu="nzDropdownMenu">
    <ul nz-menu class="download-dropdown">
      @for (option of downloadOptions(); track option) {
        <li
          nz-menu-item
          [ngStyle]="{ cursor: 'pointer', padding: '8px' }"
          (click)="handleDownload(option.value)"
        >
          <span>{{ option.label }}</span>
        </li>
      }
    </ul>
  </nz-dropdown-menu>
</ng-template>

<ng-template #generateFooter>
  <div class="preview-modal__footer">
    <ng-container [ngTemplateOutlet]="downloadBtn"></ng-container>

    <hrdx-button
      [type]="'primary'"
      [title]="'Returns to Report'"
      size="default"
      (clicked)="handleReturnReport()"
    /></div
></ng-template>

<hrdx-dialog-import
  title="Import data"
  titleExport="Template file"
  descriptionBoldLine1="Click to upload"
  descriptionLine1="or drag and drop"
  descriptionLine2=".xls, .xlsx (Maximum 5MB)"
  [formatFile]="['xlsx', 'xls', 'csv']"
  [maxFileSize]="5"
  [dialogVisible]="importVisible()"
  (submitDialog)="importSubmit($event)"
  (dialogVisibleChange)="importVisibleChange($event)"
  (getTemplate)="onGetTemplate()"
  [loading]="isImportLoading()"
  [loadingMessage]="importLoadingMessage()"
  [tabs]="functionSpec()?.layout_options?.tabs ?? []"
/>

<!-- step horizontal modal -->
<lib-layout-step-dialog
  [dialogVisible]="stepHorizontalModalVisible()"
  (dialogVisibleChange)="onStepHorizontalModalVisibleChange($event)"
  [stepTitle]="title()"
  [config]="newScheduleFormConfig()"
  [value]="dialogType() === 'edit' ? dialogValue() : newScheduleFormValue()"
  [dialogType]="dialogType()"
  [isLoading]="isLoading()"
  [loadingData]="loadingDataSchedule()"
  [selectedItem]="selectedItem()"
  (clickedModalButton)="clickedModalButton($event)"
  [authAction]="authAction()"
  [faceCode]="_faceCodeByRecord() ?? faceCode()"
  *ngIf="stepHorizontalModalVisible()"
>
</lib-layout-step-dialog>
<!-- step horizontal modal -->

<!-- folder category -->

<!-- <hrdx-category
  [folderDisplayFormFields]="folderFields"
  [modalVisible]="categoryDialogVisible()"
  (modalVisibleChanged)="categoryDialogVisible.set($event)"
  *ngIf="categoryDialogVisible()"
>
</hrdx-category> -->
<!-- end folder category -->

<ng-template #modalScheduled let-ref="modalRef">
  <span>Your report has been scheduled</span>
  <div class="modal-footer-action">
    <hrdx-button
      [type]="'tertiary'"
      [title]="'View Schedules'"
      size="default"
      (clicked)="this.router.navigate([this.link_redirect()]); ref.destroy()"
    />
    <hrdx-button
      [type]="'primary'"
      size="default"
      (clicked)="ref.destroy()"
      [title]="'OK'"
    />
  </div>
</ng-template>

<ng-template #illustrationEmptyState>
  <hrdx-illustrations
    class="layout-table__illustrations"
    [size]="illustrationConfig.size"
    [type]="illustrationConfig.type"
    [subAction]="false"
    [subTextAction]="checkPermission('create')"
    (subTextClicked)="createClick()"
    [subText]="getSubText(functionSpec()?.title ?? '')"
  ></hrdx-illustrations>
</ng-template>

<ng-template #nzModalCategoriesTitle>
  <div class="modal-header">
    <span class="modal-title">
      {{ this.categoryDialogConfig().title }}
    </span>
    <div style="width: 40px; height: 40px"></div>
  </div>
</ng-template>

<ng-template #nzModalCategoriesCloseIcon>
  <hrdx-icon icon="icon-x-bold" />
</ng-template>

<lib-deletion-details
  [(visible)]="showDeletionDetails"
  [items]="deletionDetailsConfig().items"
  [errors]="deletionDetailsConfig().errors"
  [fields]="deletionDetailsConfig().fields"
  *ngIf="showDeletionDetails()"
></lib-deletion-details>

<ng-template #deleteErrorPermissionContent>
  <div class="delete-error-permission-content">
    <span>
      You do not have the required permission to delete the selected records
    </span>
    <br />
    <br />
    <span>Please contact your system administrator.</span>
  </div>
</ng-template>

<ng-template #deleteErrorPermissionContentOne>
  <div class="delete-error-permission-content">
    <span>
      You do not have the required permission to delete the record
    </span>
    <br />
    <br />
    <span>Please contact your system administrator.</span>
  </div>
</ng-template>

<lib-schedule-details
  [(visible)]="showCalendar"
  [selectedDateRange]="calendarConfig().selectedDateRange"
  [dataSource]="calendarConfig().dataSource"
  [apiConfig]="calendarConfig().apiConfig"
  [extendValue]="calendarConfig().extendValue"
  [calendarSettings]="calendarConfig().calendarSettings"
  *ngIf="showCalendar()"
></lib-schedule-details>
