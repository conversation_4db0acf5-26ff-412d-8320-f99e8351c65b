import { CommonModule, Location } from '@angular/common';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import {
  AfterViewInit,
  Component,
  computed,
  effect,
  ElementRef,
  forwardRef,
  inject,
  OnDestroy,
  OnInit,
  signal,
  TemplateRef,
  untracked,
  viewChild,
  ViewChild,
  ViewChildren,
  ViewContainerRef,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute, Params, Router } from '@angular/router';
import {
  DynamicFormService,
  FieldGroupConfig,
  FormComponent,
  Source,
} from '@hrdx-fe/dynamic-features';
// eslint-disable-next-line @nx/enforce-module-boundaries
import {
  DialogType,
  LayoutDialogComponent,
  LayoutExpandFilterComponent,
  LayoutHistoryComponent,
  LayoutModalDialogComponent,
  LayoutModalExtendData,
  LayoutStepDialogComponent,
  LayoutTableStore,
} from '@hrdx-fe/layout-simple-table';
// eslint-disable-next-line @nx/enforce-module-boundaries
import {
  ACTION_TYPE,
  ActionManyHandler,
  ActionPermission,
  ApiConfig,
  AuthActions,
  BffService,
  ContextDataOptions,
  Data,
  debouncedSignal,
  DeleteErrorCode,
  FormConfig,
  FunctionSpec,
  getDisabledPermission,
  isValidAuthAction,
  LayoutButton,
  LayoutCommon,
  LayoutCommonComponent,
  LayoutStore,
  LocalField,
  mappingUrl,
  MasterdataService,
  MenuItem,
  overwritePermissionAction,
  PrecheckDeleteApiConfig,
  RecordCompositeKey,
  RowActionsHandler,
  ServiceFunction,
  TApiMethod,
  UserStore,
  UtilService,
} from '@hrdx-fe/shared';
import {
  BadgeComponent,
  ButtonComponent,
  ButtonSchema,
  DATA_SERVICE,
  DataRenderComponent,
  DialogImportComponent,
  DisplayComponent,
  FileStreamComponent,
  IconComponent,
  IllustrationsComponent,
  IllustrationsSize,
  IllustrationsType,
  ModalComponent,
  ModalFooterButtons,
  ModalType,
  NewTableComponent,
  PageHeader,
  PreviewCvComponent,
  PreviewTableComponent,
  RowActionsData,
  TabsModule,
  TbodyComponent,
  TdComponent,
  ThComponent,
  TheadComponent,
  ToastMessageComponent,
} from '@hrdx/hrdx-design';
import { QueryFilter } from '@nestjsx/crud-request';
import * as _ from 'lodash';
import {
  attempt,
  capitalize,
  cloneDeep,
  forEach,
  isArray,
  isDate,
  isEmpty,
  isNil,
  isObject,
  isString,
  mapValues,
  omit,
  toString,
  trim,
  trimEnd,
  uniqueId,
  every,
} from 'lodash';
import * as moment from 'moment';
import { NzSafeAny } from 'ng-zorro-antd/core/types';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzContentComponent, NzLayoutModule } from 'ng-zorro-antd/layout';
import { ModalOptions, NzModalService } from 'ng-zorro-antd/modal';
import { NzProgressModule } from 'ng-zorro-antd/progress';
import { NzRadioModule } from 'ng-zorro-antd/radio';
import { NzSwitchModule } from 'ng-zorro-antd/switch';
import { injectRouteData } from 'ngxtension/inject-route-data';
import {
  catchError,
  combineLatest,
  delay,
  filter,
  finalize,
  firstValueFrom,
  map,
  map as mapRxjs,
  Observable,
  of,
  OperatorFunction,
  Subject,
  Subscription,
  switchMap,
  takeUntil,
  tap,
} from 'rxjs';
import { LayoutDetailComponent } from '../layout-detail/layout-detail.component';
import { LayoutHistoryDetailComponent } from '../layout-history-detail/layout-history-detail.component';
import { LayoutDynamicService } from '../services/layout-dynamic.service';
import { AdjustDisplayComponent } from './adjust-display/adjust-display.component';
import { PersonidInputComponent } from './components/hr.fs.bp.001/personid-input/personid-input.component';
import {
  DialogTableComponent,
  DialogTableConfig,
} from './components/report-categories';
import { TableExpandComponent } from './components/table-expand/table-expand.component';
import { InlineFormComponent } from './inline-form/inline-form.component';
import { MixinsService } from './mixins/mixins.service';
import { DeleteSummaryModalComponent } from './components/delete-summary/delete-summary-modal.component';
import { ErrorType } from './components/delete-summary';
import { DeletionDetailsComponent } from './components/deletion-details/deletion-details.component';
import { ScheduleDetailsComponent } from './components/schedule-details/schedule-details.component';
import { ScheduleDetails } from './components/schedule-details/schedule-details.models';
import * as HandleBars from 'handlebars';

export interface GroupedData {
  key: string;
  colCode: string;
  realValue: NzSafeAny;
  items: Data[];
}

type FilterType = {
  field: string;
  operator: string;
  valueField: NzSafeAny | FilterType[];
};

type DeleteErrorMapping = {
  code: DeleteErrorCode;
  message: string;
  id: string;
};

interface GroupedItem {
  [key: string]: NzSafeAny[];
}

const initialGroupInfo: {
  icon: string;
  title: string;
  type: ButtonSchema['type'];
} = {
  icon: 'icon-stack-bold',
  title: 'Group',
  type: 'tertiary',
};

function getValue(data: NzSafeAny, path: (string | number)[]): NzSafeAny {
  if (!data) {
    return undefined;
  }
  if (path.length < 1) return undefined;
  const objExp = new RegExp(/\([a-zA-Z0-9]*\)/, 'g');
  const arrExp = new RegExp(/\[\d+\]/, 'g');
  const tmp = path.shift() ?? '';
  if (typeof tmp === 'string') {
    let key = '';
    if (objExp.test(tmp)) {
      key = tmp.replace('(', '').replace(')', '');
      if (isArray(data)) {
        return data.map((it) => it[key]);
      }
      return data[key];
    } else if (arrExp.test(tmp)) {
      key = tmp.replace('[', '').replace(']', '');
      return data[key];
    }
  }

  if (path.length <= 0) {
    return data[tmp];
  } else {
    return getValue(data[tmp], path);
  }
}

@Component({
  selector: 'lib-layout-table',
  standalone: true,
  imports: [
    CommonModule,
    NewTableComponent,
    TdComponent,
    ThComponent,
    TbodyComponent,
    TheadComponent,
    DisplayComponent,
    ButtonComponent,
    NzLayoutModule,
    NzInputModule,
    ButtonComponent,
    NzIconModule,
    LayoutDialogComponent,
    ModalComponent,
    LayoutHistoryComponent,
    NzDropDownModule,
    NzSwitchModule,
    FormsModule,
    NzInputModule,
    DataRenderComponent,
    FormComponent,
    NzProgressModule,
    LayoutModalDialogComponent,
    LayoutExpandFilterComponent,
    NzRadioModule,
    IconComponent,
    LayoutStepDialogComponent,
    PreviewCvComponent,
    PreviewTableComponent,
    DialogImportComponent,
    AdjustDisplayComponent,
    BadgeComponent,
    forwardRef(() => LayoutDetailComponent),
    forwardRef(() => LayoutHistoryDetailComponent),
    TabsModule,
    InlineFormComponent,
    TableExpandComponent,
    FileStreamComponent,
    IllustrationsComponent,
    DeletionDetailsComponent,
    ScheduleDetailsComponent,
  ],
  providers: [
    ModalComponent,
    ToastMessageComponent,
    MixinsService,
    {
      provide: DATA_SERVICE,
      useExisting: BffService,
    },
    DeleteSummaryModalComponent,
  ],
  templateUrl: './layout-table.component.html',
  styleUrl: './layout-table.component.less',
})
export class LayoutTableComponent
  extends LayoutCommonComponent
  implements LayoutCommon, OnDestroy, OnInit, AfterViewInit
{
  static DELETE_ERROR_TYPE_MAPPING: Record<DeleteErrorCode, ErrorType> = {
    [DeleteErrorCode.BusinessLogic]: ErrorType.BusinessLogic,
    [DeleteErrorCode.Permission]: ErrorType.Permission,
  };
  readonly illustrationConfig = {
    size: IllustrationsSize.Small,
    type: IllustrationsType.Empty,
  };

  readonly defaultToolTableTitle = {
    export: 'Export',
    import: 'Import',
    note: 'Note',
    synthesize: 'Synthesize',
    lock: 'Lock',
    unlock: 'Unlock',
    run: 'Run',
    preview: 'Preview',
    create: 'Create',
    folder: 'Category As',
  } as Record<string, string>;

  readonly defaultRowActionTitle = {
    edit: 'Edit',
    delete: 'Delete',
    view: 'View',
    note: 'Note',
    lock: 'Lock',
    unlock: 'Unlock',
    duplicate: 'Duplicate',
  } as Record<string, string>;

  static ACTION_MAPPING_PERMISSION = {
    toDelete: 'delete',
    saveAndAddNew: 'create',
    toSubmit: 'create',
    proceedCustom: 'proceed',
    toEdit: 'edit',
    download: 'export',
  } as Record<string, string>;

  #store = inject(LayoutTableStore);
  #layoutStore = inject(LayoutStore);
  modalComponent = inject(ModalComponent);
  dynamicService = inject(DynamicFormService);
  toast = inject(ToastMessageComponent);
  utilService = inject(UtilService);
  mixinsService = inject(MixinsService);
  currentModule = this.#layoutStore.currentModuleId;
  deleteSummaryModal = inject(DeleteSummaryModalComponent);

  @ViewChild('previewForm') previewForm!: FormComponent;
  content = viewChild(NzContentComponent, { read: ElementRef });
  @ViewChild('footerTemplate', { static: true })
  footerTemplate!: TemplateRef<NzSafeAny>;
  isProgressing = signal<boolean>(false);
  isChangeValueCell = false;
  filterDataRender = viewChild<DataRenderComponent>('filterDataRender');
  adjustDisplayRef = viewChild<AdjustDisplayComponent>('adjustDisplay');
  @ViewChild('nzModalCategoriesTitle', { static: true })
  modalCategoriesTitle!: TemplateRef<NzSafeAny>;
  @ViewChild('nzModalCategoriesCloseIcon', { static: true })
  modalCategoriesCloseIcon!: TemplateRef<NzSafeAny>;

  layoutDetailDialog = viewChild<LayoutDetailComponent>('layoutDetailDialog');
  tableRef = viewChild<NewTableComponent<Data>>('table');

  static getPermissionMapping(action: string) {
    return LayoutTableComponent.ACTION_MAPPING_PERMISSION[action] ?? action;
  }

  tableScrollHeightOption = computed(() => {
    const scrollHeight =
      this.layoutOptions()?.table_scroll_height ??
      this.options()?.table_scroll_height;
    if (isNil(scrollHeight)) return null;
    if (typeof scrollHeight === 'number') return `${scrollHeight}px`;
    return scrollHeight;
  });

  tableScrollHeight = computed(() => {
    const scrollHeightOption = this.tableScrollHeightOption();
    if (scrollHeightOption) return scrollHeightOption;
    if (this.expandFilter()) return '500px';
    return null;
  });

  tableHeight = computed(() => {
    const leftSpace = this.leftSpace();
    if (this.expandFilter() || !leftSpace) return null;
    return leftSpace;
  });

  private readonly destroy$ = new Subject<void>();
  elementsRef = viewChild<ElementRef<HTMLElement>>('elements');
  leftSpace = signal<number | null>(null);
  // contentHeight = computed(() => {
  //   if (this.leftHeight()) return this.leftHeight();
  //   return this.content()?.nativeElement?.offsetHeight ?? 0;
  // });

  leftSpaceEffect = effect(
    () => {
      this.updateLeftSpace();
    },
    { allowSignalWrites: true },
  );

  protected getFaceCodeForService() {
    return this.faceCode() ?? undefined;
  }

  private updateLeftSpace() {
    const elementsRef = this.elementsRef()?.nativeElement as HTMLElement;
    const padding = 2 * 20;
    const gap = 12;
    const adjustHeight = 3;

    const elementsHeight = elementsRef.offsetHeight;
    const contentHeight = this.leftHeight()
      ? this.leftHeight()
      : (this.content()?.nativeElement?.offsetHeight ?? 0); //đặt ở đây vì contentHeight có sự thay đổi không đồng bộ
    this.leftSpace.set(
      contentHeight - elementsHeight - gap - padding - adjustHeight,
    );
  }

  private setupResizeObserver() {
    const elementsRef = this.elementsRef()?.nativeElement as HTMLElement;

    const elements = [this.content()?.nativeElement, elementsRef].filter(
      (element) => !isNil(element),
    );
    elements.forEach((element) => {
      const resize$ = LayoutCommonComponent.createResizeObserverObservable(
        element,
      ).pipe(
        takeUntil(this.destroy$),
        // debounceTime(200), // Debounce to limit the number of events
        map((entries) => entries[0]?.contentRect), // Extract contentRect for size info
        // distinctUntilChanged((prev, curr) => prev?.height === curr?.height),
      );

      resize$.subscribe((contentRect) => this.updateLeftSpace());
    });
  }

  placeholderSearch = computed(() => {
    return (
      this.functionSpec()?.layout_options?.table_search_placeholder ?? 'Search'
    );
  });

  emptyStateStyle = computed(() => {
    return (
      this.layoutOptions()?.empty_state_style ??
      this.options()?.empty_state_style
    );
  });

  actionClickRow = computed(() => {
    return this.functionSpec()?.layout_options?.action_click_row ?? undefined;
  });

  // config for new dynamic form
  isNewDynamicForm = computed(() => {
    const layoutOptions = this.functionSpec()?.layout_options;
    return (layoutOptions as NzSafeAny)?.is_new_dynamic_form ?? false;
  });

  _url = computed(() => {
    return this.isDynamicConfigTable() || this.viewDetailByConfig()?.is_show
      ? null
      : this.url();
  });

  filterQueryCustom() {
    return this.defaultFilter();
  }

  //preview data report
  PREVIEW_TYPE = PREVIEW_TYPE;
  isPreview = false;

  typePreviewPopup = signal<string>(PREVIEW_TYPE.TABLE);

  typePreviewEffect = effect(
    () => {
      this.typePreviewPopup.set(
        this.functionSpec()?.layout_options?.typePreviewPopup ??
          PREVIEW_TYPE.TABLE,
      );
    },
    { allowSignalWrites: true },
  );

  noNeedConfirm = computed(() => {
    return this.functionSpec()?.layout_options?.no_need_confirm ?? false;
  });

  customDeleteBackendUrl = computed(() => {
    return this.functionSpec()?.layout_options?.custom_delete_backend_url || '';
  });

  customDetailBackendUrl = computed(() => {
    return this.functionSpec()?.layout_options?.custom_detail_backend_url || '';
  });

  customHistoryBackendUrl = computed(() => {
    return (
      this.functionSpec()?.layout_options?.custom_history_backend_url || ''
    );
  });

  customExportApi = computed(() => this.layoutOptions()?.custom_export_api);

  isDynamicConfigTable = computed(() => {
    return (
      this.functionSpec()?.layout_options?.is_dynamic_config_table ?? false
    );
  });

  isExportGrid = computed(() => {
    return this.functionSpec()?.layout_options?.is_export_grid ?? false;
  });

  exportAllConfig = computed(() => this.layoutOptions()?.export_all);

  isCustomInsertNewProceed = computed(() => {
    return (
      this.functionSpec()?.layout_options?.is_custom_insert_new_proceed ?? false
    );
  });

  isShowLengthPaginaton = computed(() => {
    return (
      this.functionSpec()?.layout_options?.is_show_length_pagination ?? false
    );
  });

  viewDetailByConfig = computed(() => {
    return this.functionSpec()?.layout_options?.view_detail_by_config;
  });

  isCopyDataInsertNew = computed(() => {
    return this.layoutOptions()?.is_copy_data_insert_new ?? true;
  });

  customValueBeforeEdit = computed(() => {
    return this.layoutOptions()?.custom_value_before_edit;
  });

  titlePreviewPopup = computed(() => {
    // return (
    //   (this.functionSpec()?.layout_options?.previewTableTitle ||
    //     this.functionSpec()?.layout_options?.titlePreviewPopup) ??
    //   ''
    // );

    return this.selectedItem()?.['name'] ?? '';
  });

  tabset = computed(() => {
    return this.functionSpec()?.layout_options?.tabset ?? [];
  });

  defaultTabsetIndex = computed(() => {
    return this.functionSpec()?.layout_options?.defaultTabsetIndex ?? 0;
  });

  localFieldsPerTab = computed(() => {
    const localFields = this.functionSpec()?.local_fields ?? [];
    const tabset = this.tabset();
    if (
      tabset?.length <= 0 ||
      localFields.length <= 0 ||
      tabset.every((tab) => !tab.pick_local_fields)
    ) {
      return null;
    }

    return tabset.map((tab) => {
      let tabLocalFields = [];
      if (!tab.pick_local_fields) {
        tabLocalFields = structuredClone(localFields);
      } else {
        for (const field of localFields) {
          if (tab.pick_local_fields?.includes(field.code)) {
            tabLocalFields.push(structuredClone(field));
          }
        }
      }
      return tabLocalFields;
    });
  });

  // a flag to show table avatar
  showTableAvatar = computed(() => {
    const layoutOptions = this.layoutOptions();
    if (layoutOptions) {
      return (layoutOptions as NzSafeAny).show_table_avatar ?? false;
    }
    return false;
  });

  customUpdateApi = computed(() => this.layoutOptions()?.custom_update_api);
  customCreateApi = computed(() => this.layoutOptions()?.custom_create_api);
  customSubmitApi = computed(() => this.layoutOptions()?.custom_submit_api);
  customViewDetailApi = computed(
    () => this.layoutOptions()?.custom_view_detail_api,
  );
  dialogActions = computed(() => this.layoutOptions()?.dialog_actions ?? []);
  showFilterResultsMessage = computed(() => {
    const allowShow =
      this.layoutOptions()?.show_filter_results_message ?? false;
    if (!allowShow) return false;
    return this.isFiltered() || this.isSearched();
  });

  // downloadOptions = [{ label: 'Excel', value: 'excel' }];
  @ViewChild('modalScheduled') modalScheduled!: TemplateRef<''>;

  router = inject(Router);

  //end preview data report

  _service = inject(BffService);
  metaDataService = inject(MasterdataService);
  routeData = injectRouteData();
  loading = signal(false);
  isSilentGetList = signal(false);
  fsKey = computed(() => this.functionSpec().key_detail ?? 'id');
  effectFunctionSpec = effect(
    () => this.#store.setCurrentFunctionSpec(this.functionSpec()),
    { allowSignalWrites: true },
  );
  pageIndex = signal<number>(1);
  pageSize = signal<number>(25);
  filterValue = signal<NzSafeAny>(null);
  searchValue = signal<string>('');
  debouncedSearchValue = debouncedSignal(this.searchValue, 300);
  // get data from backend
  data = signal<Data[]>([]);
  total = signal(0);
  originTotal = signal(0);
  // _url = signal<string | null>(this.url());
  addOnValue = computed(() => {
    const newParams = this.params();
    return {
      ...newParams,
      detailId: (this.parent()?.id ?? this.#store.detailId()) as string,
      dataDetail: (this.parent() ?? this.#store.dataDetail()) as NzSafeAny,
    };
  });

  filterCount = computed(() => {
    const validFilterList =
      this.filterDataRender()?.validFilterList() ??
      ({} as Record<string, NzSafeAny>);
    return Object.keys(validFilterList).length;
  });

  folderFields = [
    {
      name: 'categoryName',
      label: 'Category Name',
      placeholder: 'Enter Category name',
      type: 'translation',
      validators: [
        {
          type: 'required',
        },
      ],
    },
  ];

  groupInfo = signal<{
    icon: string;
    title: string;
    type: ButtonSchema['type'];
  }>(initialGroupInfo);
  selectedGroupKey = '';

  @ViewChildren('paymentForm') paymentForm?: FormComponent[];
  @ViewChildren('multiForm') multiForm?: FormComponent[];

  groupLabel = '';
  groupItem = (event: string) => {
    const groupedData = this.headers().find(
      (item: { code: string }) => item.code === event,
    );
    this.groupInfo.set({
      icon: 'icon-funnel-simple',
      title: `Group: ${groupedData?.title}`,
      type: 'secondary',
    });

    this.groupLabel = groupedData?.title ?? '';
    this.selectedGroupKey = event;
    this.groupTableData(event);
  };

  removeGroupByKey(event: Event) {
    this.groupInfo.set(initialGroupInfo);
    this.groupLabel = '';
    this.groupedData = [];
    this.selectedGroupKey = '';
  }

  groupedData: GroupedData[] = [];
  groupTableData(colCode: string) {
    const dataSource = this.data();
    const grouped: GroupedItem = dataSource.reduce(
      (acc: GroupedItem, item: NzSafeAny) => {
        const groupKey = item[colCode]?.default ?? item[colCode];
        if (!acc[groupKey]) {
          acc[groupKey] = [];
        }
        acc[groupKey].push(item);
        return acc;
      },
      {},
    );

    this.groupedData = Object.keys(grouped).map((key) => ({
      key,
      realValue: grouped[key][0][colCode],
      colCode,
      items: grouped[key],
    }));
  }
  isValidFilterValue = computed(() => {
    const filterValue = this.filterDataRenderValue();
    if (filterValue === null || filterValue === undefined) {
      return false;
    }

    if (Array.isArray(filterValue) && filterValue.length === 0) {
      return false;
    }

    if (typeof filterValue === 'object' && filterValue !== null) {
      if (Object.keys(filterValue).length === 0) {
        return false;
      }
      const hasNonEmptyValues = Object.values(filterValue).some(
        (value) =>
          (value !== null &&
            value !== '' &&
            value !== undefined &&
            !Array.isArray(value)) || // Handle string values
          (Array.isArray(value) && value.length > 0),
      );
      return hasNonEmptyValues;
    }

    return true;
  });

  customPath = computed(() => {
    return this.preconditionFilter()?.custom_path;
  });

  travelField(
    field: { field: string; operator: string; valueField: NzSafeAny },
    filterValue: NzSafeAny,
  ) {
    if (field.operator === '$and' || field.operator === '$or') {
      return {
        field: field.field,
        operator: field.operator,
        value: field.valueField.map((f: FilterType) =>
          this.travelField(f, filterValue),
        ),
      };
    }
    if (field.field === '$') {
      const _filterValue = getValue(filterValue, [
        field.valueField.split('.')[0],
      ]);
      if (!_filterValue) return;
      if (Array.isArray(_filterValue))
        return {
          operator: '$or',
          value: _filterValue.map((f: NzSafeAny) => {
            const _v = getValue(f, field.valueField.split('.').slice(1));
            return this.objectToFilterObject(_v);
          }),
        };
      return this.objectToFilterObject(_filterValue);
    }
    return {
      field: field.field,
      operator: field.operator,
      value: getValue(filterValue, field.valueField.split('.')),
    };
  }

  objectToFilterObject(obj: NzSafeAny) {
    return {
      operator: '$and',
      value: Object.keys(obj).map((key) => {
        const field = {
          field: key,
          operator: '$eq',
          value: obj[key],
        };
        return field;
      }),
    };
  }

  tabsetFilter = signal<NzSafeAny>(null);

  tabsetFilterEffect = effect(
    () => {
      const tab = this.tabset()[this.defaultTabsetIndex()];
      if (tab?.filter) {
        this.tabsetFilter.set(tab.filter);
      }
    },
    { allowSignalWrites: true },
  );

  filterQuery = computed(() => {
    let filterMapping = this.functionSpec()?.filter_config?.filterMapping;
    let filterValue = this.filterValue();

    const preconditionFilterValue = this.preconditionFilterValue();
    if (
      preconditionFilterValue &&
      this.preconditionFilter() &&
      !this.customPath()
    ) {
      const preconditionFilterMapping = this.isAutoFilter()
        ? null
        : this.functionSpec()?.create_form?.filterMapping;
      filterValue = {
        ...(filterValue ?? {}),
        ...(preconditionFilterValue ?? {}),
      };
      filterMapping = [
        ...(filterMapping ?? []),
        ...(preconditionFilterMapping ?? []),
      ];
    }
    const tabsetFilter = this.tabsetFilter();
    if (tabsetFilter) {
      const tabsetFilterMapping = tabsetFilter.filterMapping ?? [];
      const tabsetFilterValue = tabsetFilter.filterValue ?? {};
      filterValue = {
        ...(filterValue ?? {}),
        ...(tabsetFilterValue ?? {}),
      };
      filterMapping = [
        ...(filterMapping ?? []),
        ...(tabsetFilterMapping ?? []),
      ];
    }

    const res =
      filterMapping
        ?.map((f: NzSafeAny) => {
          if (!f.valueField) return null;
          return this.travelField(f, filterValue);
        })
        .filter(
          (f: NzSafeAny) =>
            f &&
            ((!isArray(f.value) && !isNil(f.value) && f.value !== '') ||
              (isArray(f.value) && f.value.length)),
        ) ?? [];

    if (this.defaultFilter().length > 0) {
      res.push(...this.defaultFilter());
    }

    return res;
  });
  refreshData = signal(false);
  fsEffect = effect(
    () => {
      if (this.functionSpec()) {
        this.pageIndex.set(1);
        this.pageSize.set(25);
        this.filterValue.set([]); // reset filter
        this.searchValue.set(''); // reset search
      }
    },
    { allowSignalWrites: true },
  );

  onPageSizeChange = (pageSize: number) => {
    this.pageSize.set(pageSize);
    this.pageIndex.set(1);
  };

  onSearchValueChange(value: NzSafeAny) {
    this.searchValue.set(value);
    this.pageIndex.set(1);
    this.clearSelectedItems();
  }

  latestRequestTime = 0;

  // when parent refresh child set page index to 1;
  parentRefreshEffect = effect(
    () => {
      this.refresh();
      this.pageIndex.set(1);
    },
    { allowSignalWrites: true },
  );
  isSearched = signal(false);
  isFiltered = signal(false);

  getListApiMethod = computed(
    () =>
      (
        this.layoutOptions()?.custom_get_list_api?.method ?? 'GET'
      ).toUpperCase() as TApiMethod,
  );

  dataEffect = effect(
    () => {
      const fs = this.functionSpec();
      let url = this.url();
      const dataGroup = this.layoutOptions()?.data_group;
      if (dataGroup) {
        url = dataGroup.group_api.url;
      }

      if (this.customPath()) {
        url = this._customUrl();
      }
      // refresh data when parent layout trigger event
      this.refresh();
      this.refreshData(); // do not remove this line, use to refresh when create, edit or delete...
      if (!this.allowRefreshData()) return;
      if (!url) {
        const shouldLock = this.isLocked();
        let updatedData = fs.mock_data ?? [];

        if (shouldLock !== null) {
          const selectedIndexes = this.listItemChecked;

          const selectIds = selectedIndexes.map((item) => item['no']);

          updatedData = updatedData.map((item) =>
            selectIds.includes(item.no) ? { ...item, lock: !shouldLock } : item,
          );
        }

        this.data.set(
          this.metaDataService.generateMockData(fs.local_fields, updatedData),
        );
        // share data between layouts in the same route for handle page header buttons condition
        this.layoutDataService?.update({ data: this.data() });
      } else {
        let requestTime = 0;
        of(url)
          .pipe(
            tap(() => {
              // skip loading when flag slientGetList is true
              const silentGetList = untracked(() => this.isSilentGetList());
              if (silentGetList) return;
              this.loading.set(true);
            }),
            tap(() => {
              requestTime = Date.now(); // Ghi lại thời điểm gọi API
              this.latestRequestTime = Math.max(
                this.latestRequestTime,
                requestTime,
              );
            }),
            switchMap((url) => {
              const method = this.getListApiMethod();
              if (method === 'GET') {
                return this._service.getPaginate(
                  url,
                  this.pageIndex(),
                  this.pageSize(),
                  this.filterQuery(),
                  this.debouncedSearchValue(),
                  this.sortOrder(),
                  this.functionSpec()?.layout_options?.support_search_date,
                  this.getFaceCodeForService(),
                );
              } else {
                const serviceFunc = this.getServiceMethod(method);
                const filterValue = this.filterValue() ?? {};
                const body = {
                  ...filterValue,
                  pageIndex: this.pageIndex(),
                  pageSize: this.pageSize(),
                };
                return (
                  serviceFunc?.(
                    this.layoutOptions()?.custom_get_list_api?.url ?? url,
                    body,
                  ) ?? of({ data: [], total: 0 })
                );
              }
            }),
            catchError((err) => {
              this.toast.showToast(
                'error',
                'Error',
                err?.error?.message ?? err,
              );
              return of({ data: [], total: 0 });
            }),
            filter(() => requestTime === this.latestRequestTime),
            map((d) => {
              this.dataPermission = d;
            }),
            tap(() => this.loading.set(false)),
            tap(() => {
              this.isSearched.set(!!this.searchValue());
              this.isFiltered.set(!!this.isFiltering());
            }),
            tap(() => {
              this.setDataWithChildAction();
            }),
            tap(() => {
              if (this.showTableAvatar()) {
                this.generateAvatarLinksForRows();
              }
            }),
          )
          .subscribe();

        if (this.hidePreconditionFilterIfEmpty()) {
          of(url)
            .pipe(
              switchMap((url) => {
                return this._service.getObject(
                  url,
                  undefined,
                  this.getFaceCodeForService(),
                );
              }),

              catchError((err) => {
                return of(null);
              }),
            )
            .subscribe(async (d) => {
              if (d) {
                this.dataOriginFoFilterInline.set(d as Data);
              } else {
                this.dataOriginFoFilterInline.set(null);
              }
            });
        }
      }
    },
    { allowSignalWrites: true },
  );

  checkShowPreconditionFilter = computed(() => {
    if (this.hidePreconditionFilterIfEmpty()) {
      return (
        !isNil(this.dataOriginFoFilterInline()) &&
        !isEmpty(this.dataOriginFoFilterInline())
      );
    }
    return !!this.preconditionFilter();
  });

  removedFilterItem = (event: NzSafeAny) => {
    this.filterValue.set({ ...event, ...this.syncFilterValue() });
  };

  pageHeader = computed<PageHeader>(() => {
    const fs = this.functionSpec();
    const module = this.currentModule();
    const breadcrumb = [
      module,
      (fs.menu_item?.parent as MenuItem)?.title ?? '',
      fs.menu_item?.title ?? '',
    ].map((title) => ({ title }));

    const buttons = (fs.layout_options?.header_buttons ?? []).map((btn) => ({
      id: btn.id,
      type: btn.type,
      size: 'default' as ButtonSchema['size'],
      state: 'default' as ButtonSchema['state'],
      title: btn.title,
      leftIcon: btn.icon,
      isLeftIcon: btn.icon ? true : false,
    }));

    return {
      title: fs.title ?? '',
      breadcrumb: breadcrumb,
      buttons: buttons,
    };
  });

  actionBtnFooterModalDialog: NzSafeAny[] = [];
  override pageHeaderButtonClicked = async (id: string) => {
    const reAuthRes = await this.checkReAuthentication(id);
    if (!reAuthRes) return;
    switch (id) {
      case 'create':
        if (
          this.functionSpec()?.create_form?.fields &&
          !this.layoutOptions()?.skip_create_form
        ) {
          this.openProceedDialog();
        } else {
          this.createClick();
        }
        break;
      case 'export':
        await this.handleExport();
        break;

      case 'import':
        this.showImportDialog();
        break;
      case 'add':
        //   this.insertItem();
        break;
      case 'redirect':
        //   if (this.link_redirect()) {
        //     this.router.navigate([this.link_redirect()]);
        //   }
        break;
      case 'calculate':
        this.checkPermissionCalculateClick();
        break;
      case 'synthesize':
        this.actionBtnFooterModalDialog = [
          {
            id: 'synthesize',
            title: 'Synthesize',
            type: 'primary',
          },
        ];
        this.synthesizeClick();
        break;
      case 'payment':
        this.openPaymentDialog();
        break;
      case 'lock':
        this.updateLockStatus();
        break;
      case 'unlock':
        this.updateUnLockStatus();
        break;
      case 'multi-typing':
        this.openMultiDialog();
        break;
      case 'history': {
        this.showHistoryClickOne();
        break;
      }
      case 'addperson':
        this.openAddEmployeeDialog();
        break;
      case 'refresh':
        this.refreshDataTable();
        break;
      case 'edit':
        this.switchEditMode(true);
        break;
      case 'cancel':
        this.handleCancelButtonClick();
        break;
      case 'save':
        this.handleSaveButtonClick();
        break;
    }
  };

  editMode = signal(false);
  switchEditMode(stt: boolean) {
    this.editMode.set(stt);
    this.layoutDataService.update({ isEditMode: stt, btnLoading: false });
  }

  handleCancelButtonClick() {
    this.switchEditMode(false);
    this.isChangeValueCell && this.refreshData.update((e) => !e);
    this.isChangeValueCell = false;
  }

  handleSaveButtonClick() {
    if (!this.isChangeValueCell) return;
    const url = this.url();
    const formValue = this.data();
    if (url) {
      this.isLoading.set(true);
      this.layoutDataService.update({ btnLoading: true });
      this._service
        .updateList(url, formValue, this.getFaceCodeForService())
        .subscribe({
          next: () => {
            this.handleSuccessFooterButtonClicked();
          },
          error: (err) => {
            this.layoutDataService.update({ btnLoading: false });
            this.toast.showToast('error', '', err.error?.message);
          },
        });
    } else {
      this.handleSuccessFooterButtonClicked();
    }
  }
  async handleExport(items?: Data[]) {
    const body = this.isExportGrid()
      ? this.mixinsService.getExportColumnsBody(this.headers())
      : undefined;

    let url = null;
    const customApi = this.customExportApi();
    if (customApi) {
      const filterFormValue = this.preconditionFilterValue();
      url = await this.getCustomUrl(customApi, filterFormValue ?? {});
    }
    url = url ?? this.url();
    let filterQuery = this.filterQuery();

    if (items && items.length > 0) {
      const {
        field = 'id',
        operator = '$in',
        valueField = 'id',
      } = customApi?.selected_items_query_config ?? {};

      filterQuery = [
        ...filterQuery,
        {
          field,
          operator: operator,
          value: items.map((item) => item[valueField]),
        },
      ];
    }

    const exportAllConfig = this.exportAllConfig();
    let pageIdx = this.pageIndex();
    let pageSize = this.pageSize();
    let searchValue = this.searchValue();

    if (!items && exportAllConfig) {
      switch (exportAllConfig.type) {
        case 'base_total': {
          pageIdx = 1;
          pageSize = this.total() ?? 1000;
          searchValue = '';
          filterQuery = [];
        }
      }
    }
    this.toast.showTemplate();

    this._service
      .exportFileByGet(
        url,
        pageIdx,
        pageSize,
        filterQuery,
        searchValue,
        this.sortOrder(),
        body,
        {
          faceCode: this.getFaceCodeForService(),
          authAction: AuthActions.ExportGrid,
        },
      )
      .subscribe({
        next: (data) => {
          this.toast.removeToast();
        },
        error: (err) => {
          this.toast.removeToast();
          this.toast.showToast('error', 'Error', err.error?.message);
        },
        complete: () => {
          // this.toast.showToast('success', 'File export', 'Export complete');
        },
      });
  }

  showTableFilter = computed(() => {
    return this.functionSpec()?.layout_options?.show_table_filter ?? true;
  });

  showTableGroup = computed(() => {
    return this.functionSpec()?.layout_options?.show_table_group ?? false;
  });

  showTableHeaderAction = computed(() => {
    return (
      this.functionSpec()?.layout_options?.show_table_header_action ?? true
    );
  });
  showToolTable = computed(() => {
    return this.functionSpec()?.layout_options?.show_tool_table ?? true;
  });

  skipInsertNewProceedHistory = computed(() => {
    return (
      this.functionSpec()?.layout_options?.skip_insert_new_proceed_history ??
      false
    );
  });
  isLayoutWidget = computed(() => {
    return this.functionSpec()?.layout_options?.is_layout_widget ?? false;
  });

  customStyleTabset = computed(() => {
    return this.functionSpec()?.layout_options?.customStyleTabset ?? false;
  });

  showDetailHistory = computed<boolean>(() => {
    const fs = this.functionSpec();
    if (fs) {
      return fs.layout_options?.show_detail_history ?? true;
    }
    return true;
  });

  showDialogFooter = computed(async () => {
    const transform = this.layoutOptions()?._show_dialog_footer?.transform;
    const valueTransform = {
      parentData: this.parent(),
      data: this.data(),
      extend: {
        formType: this.dialogType(),
      },
    };
    if (transform) {
      return (await this.transformValue(
        transform,
        valueTransform,
      )) as Promise<boolean>;
    }

    const config = this.layoutOptions()?.show_dialog_footer ?? true;
    if (typeof config === 'boolean') {
      return config;
    } else {
      const dialogType = this.dialogType();
      const value = config[dialogType];
      // if not define type then return default true to show footer.
      if (isNil(value)) return true;

      if (typeof value === 'boolean') return value;
      return (await this.transformValue(
        value?.transform,
        valueTransform,
      )) as Promise<boolean>;
    }
  });

  dialogFooter = computed(() => {
    const footers = this.layoutOptions()?.dialog_footer ?? [];
    return footers.find((footer) => footer?.for.includes(this.dialogType()));
  });
  detailDialogFooter = computed(() => {
    const footers = this.layoutOptions()?.dialog_footer ?? [];
    return footers.find((footer) => footer?.for.includes('view'));
  });

  hidePreconditionFilterIfEmpty = computed(() => {
    return this.layoutOptions()?.hide_precondition_filter_if_empty ?? false;
  });

  getDefaultToolTableTitle(id: string) {
    return this.defaultToolTableTitle[id];
  }

  getDefaultRowActionTitle(id: string) {
    return this.defaultRowActionTitle[id];
  }

  headersVisible = signal<string[]>([]);
  allHeaders = signal<NzSafeAny>([]);
  expandFilterValue = signal<QueryFilter[]>([]);

  configLocalFields: NzSafeAny[] = [];

  dataOriginFoFilterInline = signal<Data | Data[] | null>(null);

  protected progressingSubscription!: Subscription;
  protected routerSubscription!: Subscription;
  private layoutDataSubscription!: Subscription;
  private subscriptionLayoutEvent!: Subscription;
  // private isProgressListening = false;
  constructor(
    protected route: ActivatedRoute,
    protected readonly _layoutDynamicService: LayoutDynamicService,
    protected location: Location,
  ) {
    super();
    //init layout table in service
    this._layoutDynamicService.initLayoutTable(() => this);
    effect(
      () => {
        if (this.hidePreconditionFilterIfEmpty()) {
          this.childData()?.set(this.dataOriginFoFilterInline());
        } else {
          this.childData()?.set(this.data());
        }
      },
      { allowSignalWrites: true },
    );
  }

  reloadPreconditionFilter = signal<boolean>(false);

  ngOnInit() {
    if (this.customStyleTabset() && this.tabset().length > 0) {
      document.querySelector('body')?.classList.add('tabset-body-wrapper');
    } else {
      document.querySelector('body')?.classList.remove('tabset-body-wrapper');
    }

    const queryParams: NzSafeAny = this.route.snapshot.queryParams;
    const localFields = this.functionSpec()?.local_fields ?? [];
    if (this.localFieldsPerTab()) {
      this.allHeaders.set(
        this.localFieldsPerTab()?.[this.defaultTabsetIndex()],
      );
    } else {
      this.allHeaders.set(localFields);
    }

    this._layoutDynamicService.setInitialHeader(localFields);

    if (this.isDynamicConfigTable()) {
      this.setDynamicConfigData(this.parent());
    }
    if (queryParams?.openForm) {
      switch (queryParams.name) {
        case 'create': {
          // Chuẩn bị dữ liệu trước
          const initialData: NzSafeAny = {
            directPositionId: queryParams.directPositionId,
          };
          const orgChartParent = localStorage.getItem('orgChartParentAdd');
          if (orgChartParent) {
            try {
              const orgChartParentData = JSON.parse(orgChartParent);
              console.log('orgChartParentData:', orgChartParentData);
              switch (orgChartParentData.type) {
                case 'Group': {
                  initialData.groupObj = {
                    id: orgChartParentData.id,
                    code: orgChartParentData.code,
                    label:
                      orgChartParentData.longName.default +
                      ' (' +
                      orgChartParentData.code +
                      ')',
                    additionalData: orgChartParentData,
                    value: {
                      id: orgChartParentData.id,
                      code: orgChartParentData.code,
                    },
                  };
                  break;
                }
                case 'Company': {
                  initialData.companyObj = {
                    id: orgChartParentData.id,
                    code: orgChartParentData.code,
                    label:
                      orgChartParentData.longName.default +
                      ' (' +
                      orgChartParentData.code +
                      ')',
                    additionalData: orgChartParentData,
                    value: {
                      id: orgChartParentData.id,
                      code: orgChartParentData.code,
                    },
                  };
                  break;
                }
                case 'LegalEntity': {
                  initialData.legalEntityObj = {
                    id: orgChartParentData.id,
                    code: orgChartParentData.code,
                    label:
                      orgChartParentData.longName.default +
                      ' (' +
                      orgChartParentData.code +
                      ')',
                    additionalData: orgChartParentData,
                    value: {
                      id: orgChartParentData.id,
                      code: orgChartParentData.code,
                    },
                  };
                  initialData.parentLegalEntitys = {
                    id: orgChartParentData.id,
                    code: orgChartParentData.code,
                    label:
                      orgChartParentData.longName.default +
                      ' (' +
                      orgChartParentData.code +
                      ')',
                    additionalData: orgChartParentData,
                    value: {
                      id: orgChartParentData.id,
                      code: orgChartParentData.code,
                    },
                  };
                  break;
                }
                case 'BusinessUnit': {
                  initialData.parentBusinessUnit = {
                    id: orgChartParentData.id,
                    code: orgChartParentData.code,
                    label:
                      orgChartParentData.longName.default +
                      ' (' +
                      orgChartParentData.code +
                      ')',
                    additionalData: orgChartParentData,
                    value: {
                      id: orgChartParentData.id,
                      code: orgChartParentData.code,
                    },
                  };
                  initialData.parentBusinessUnits = {
                    label:
                      orgChartParentData.longName.default +
                      ' (' +
                      orgChartParentData.code +
                      ')',
                    additionalData: orgChartParentData,
                    value: {
                      id: orgChartParentData.id,
                      code: orgChartParentData.code,
                    },
                  };
                  initialData.parentObjFromOrgChart = orgChartParentData.id;
                  initialData.businessUnitObj = {
                    id: orgChartParentData.id,
                    code: orgChartParentData.code,
                    label:
                      orgChartParentData.longName.default +
                      ' (' +
                      orgChartParentData.code +
                      ')',
                    additionalData: orgChartParentData,
                    value: {
                      id: orgChartParentData.id,
                      code: orgChartParentData.code,
                    },
                  };
                  break;
                }
                case 'Department': {
                  initialData.parentDepartments = {
                    label:
                      orgChartParentData.longName.default +
                      ' (' +
                      orgChartParentData.code +
                      ')',
                    additionalData: orgChartParentData,
                    value: {
                      id: orgChartParentData.id,
                      code: orgChartParentData.code,
                    },
                  };
                  break;
                }
                case 'Division': {
                  initialData.parentObj = {
                    id: orgChartParentData.id,
                    code: orgChartParentData.code,
                    label:
                      orgChartParentData.longName.default +
                      ' (' +
                      orgChartParentData.code +
                      ')',
                    additionalData: orgChartParentData,
                    value: {
                      id: orgChartParentData.id,
                      code: orgChartParentData.code,
                    },
                  };
                  break;
                }
              }
            } catch (error) {
              console.error('Error parsing orgChartParent:', error);
            }
          }

          // check permission create
          if (!this.checkPermission('create')) {
            this.toast.showToast(
              'error',
              'Error',
              `Permission denied for ${this.faceCode()} with action Create.`,
            );
            return;
          }

          // Gọi createClick với dữ liệu đã chuẩn bị
          this.createClick(initialData);
          break;
        }
        case 'edit': {
          const prevDataJson = localStorage.getItem(
            'editData' + queryParams.id,
          );
          let prevData = {};
          if (prevDataJson) {
            prevData = JSON.parse(prevDataJson);
          }
          this.editClickOne(queryParams.id, {
            ...prevData,
            id: queryParams.id,
          });
          break;
        }
        case 'view': {
          this.viewClickOne(queryParams.id, {
            id: queryParams.id,
          });
          break;
        }
        case 'duplicate':
          {
            const prevDataJson = localStorage.getItem(
              'editData' + queryParams.id,
            );
            let prevData = {};
            if (prevDataJson) {
              prevData = JSON.parse(prevDataJson);
            }
            this.duplicateClick(prevData, { code: null });
          }
          break;
        case 'createSame':
          {
            this.pageHeaderButtonClicked('create');
            const prevDataJson = localStorage.getItem(
              'createSame' + queryParams.id,
            );
            let prevData: NzSafeAny = {};
            if (prevDataJson) {
              prevData = JSON.parse(prevDataJson);
            }
            this._service
              .getItem(String(this.url()), queryParams.id)
              .pipe(
                catchError((err) => {
                  this.toast.showToast(
                    'error',
                    'Error',
                    err?.error?.message ?? err,
                  );
                  return of(null);
                }),
              )
              .subscribe((data: NzSafeAny) => {
                this.dialogValue.set({
                  ...data,
                  code: '',
                  directPositionObj_extended: true,
                  directPositionObj: {
                    value: {
                      id: prevData.parentManagement?.id,
                      code: prevData.parentManagement?.positionCode,
                    },
                  },
                });
              });
          }
          break;
        case 'delete': {
          this.viewClickOne(queryParams.id, {
            id: queryParams.id,
          });
          setTimeout(() => {
            this.deleteClickOne(queryParams.id, {
              id: queryParams.id,
            });
          }, 100);
          break;
        }
        default:
          break;
      }
    }

    this.layoutDataSubscription = this.layoutDataService
      .getDataSubject()
      .subscribe((data) => {
        if (this.functionSpec()?.id) {
          const filterFromData = data[`${this.functionSpec().id}`];
          if (filterFromData && this.isAutoSyncPreconditionFilter()) {
            this.preconditionFilterValue.set(filterFromData);
            this.reloadPreconditionFilter.update((value) => !value);
          }
        }
      });

    this.subscriptionLayoutEvent =
      this.layoutDataService.layoutEventEmiter$.subscribe(
        (event: NzSafeAny) => {
          if (
            event.key === 'refreshData' &&
            event.value === this.functionSpec().id
          ) {
            this.refreshData.update((e) => !e);
            this.reloadPreconditionFilter.update((value) => !value);
          }
        },
      );
  }
  headers = computed(() => {
    return this.allHeaders();
    // return this.allHeaders().filter(
    //   (h: NzSafeAny) => !this.headersVisible().includes(h.code),
    // );
  });
  isOff(code: string) {
    return this.headersVisible().includes(code);
  }

  setDynamicConfigData(parent: NzSafeAny) {
    of(parent)
      .pipe(
        switchMap((parent) =>
          this._service.getItemCustom(
            `/api/report-types/${parent?.['reportTypeId']}/result-setting`,
            undefined,
            this.getFaceCodeForService(),
          ),
        ),
        catchError((err) => {
          console.log(err);
          this.toast.showToast('error', 'Error', err?.error?.message ?? err);
          return of({ data: [], total: 0 });
        }),
      )
      .subscribe((d) => {
        d && this.allHeaders.set(this.mixinsService.setConfigLocalFields(d));
        if (this.functionSpec()) {
          this.functionSpec().local_fields = this.allHeaders();
        }

        this.adjustDisplayRef()?.registerHeaders(this.allHeaders());
      });
  }

  switchChange(code: string) {
    const idx = this.headersVisible().findIndex((c) => c === code);
    if (idx === -1) {
      this.headersVisible.update((e) => {
        const v = cloneDeep(e);
        v.push(code);
        return v;
      });
    } else {
      this.headersVisible.update((e) => {
        const v = cloneDeep(e);
        v.splice(idx, 1);
        return v;
      });
    }
  }
  orderChange(newlist: NzSafeAny) {
    this.allHeaders.set(newlist);
    this.adjustDisplayRef()?.updateOrder(newlist);
  }

  allowFixedLeftColumn = computed(() => {
    return this.headers()?.some((h: NzSafeAny) => !h.pinned);
  });
  // ------------------------------
  // action by single data
  dialogVisible = signal(false);

  _faceCodeByRecord = signal<string | undefined>(undefined);

  effectDialogVisible = effect(
    () => {
      if (!this.dialogVisible()) {
        const fc = this.faceCode();
        if (fc) {
          this.#layoutStore.setFaceCode(fc);
          this._faceCodeByRecord() && this._faceCodeByRecord.set(undefined);
        }
      } else {
        const value = this.dialogValue();
        if (value?.faceCode) {
          this.#layoutStore.setFaceCode(value.faceCode);
          this._faceCodeByRecord.set(value.faceCode);
        }
      }
    },
    { allowSignalWrites: true },
  );
  dialogGroupEditVisible = signal(false);
  dialogConfig = signal<FormConfig | NzSafeAny>({});
  dialogType = signal<DialogType>('create');
  dialogValue = signal<Data | null | undefined | NzSafeAny>(
    this.route.snapshot.queryParams?.['id'] || null,
  );
  dialogTitle = computed(() => {
    if (this.customDialogTitle() && this.dialogType() !== 'filter')
      return this.customDialogTitle();

    const actionOneTitle = this.actionOneSelected()?.form?.title;
    if (actionOneTitle) return actionOneTitle;

    const fs = this.functionSpec();
    switch (this.dialogType()) {
      case 'view':
      case 'viewSchedule':
        return `View ${fs.title}`;
      case 'create':
        return `Add New ${fs.title}`;
      case 'duplicate':
        return `Duplicate ${fs.title}`;
      case 'toDuplicate':
        return `Duplicate ${fs.title}`;
      case 'edit':
        return `Edit ${fs.title}`;
      case 'proceed':
        return `Insert New Record ${fs.title}`;
      case 'proceedCustom':
        return `Insert New Record ${fs.title}`;
      case 'filter':
        return `Filters`;
    }
  });
  selectedItem = signal<Data | null>(null);
  selectedId = computed(() => this.selectedItem()?.id);
  listOfSelectedItems = signal<Data[]>([]);

  private getInitialDialogValue(fromAction: string) {
    const config = this.layoutOptions()?.initial_dialog_value?.find(
      (item) => item.fromAction === fromAction,
    );
    if (!config) return null;
    return this.dynamicService.getJsonataExpression({})(config.valueTransform, {
      selectedItems: this.listOfSelectedItems() ?? [],
    });
  }

  viewDialog = 'drawer';
  isCalculate = false;
  isSynthesize = false;

  async checkPermissionCalculateClick() {
    const preCheckUrl = this.layoutOptions()?.pre_check_calculate;
    if (!preCheckUrl) {
      this.calculateClick();
    } else {
      const dialogValue = await this.getInitialDialogValue('calculate');
      if (preCheckUrl && dialogValue) {
        const compiled = HandleBars.compile(preCheckUrl);
        const finalUrl = compiled({ values: dialogValue });
        this._service
          .Get(finalUrl, this.getFaceCodeForService(), {
            authAction: AuthActions.Calculation,
          })
          .pipe(
            catchError((err) => {
              this.toast.showToast('error', 'Error', err.error?.message);
              return of(undefined);
            }),
          )
          .subscribe((res) => {
            if (!res) return;
            this.calculateClick();
          });
      } else {
        this.calculateClick();
      }
    }
  }

  async calculateClick() {
    this.selectedItem.set(null);
    this.modalDialogType.set('create');
    this.modalDialogConfig.set(this.functionSpec()?.form_config ?? {});
    // this.dialogValue.set(value);
    this.actionBtnFooterModalDialog = this.modalDialogConfig()
      ?.btnModalDialogFooter ?? [
      {
        id: 'run',
        title: 'Run',
        type: 'primary',
      },
    ];
    const dialogValue = await this.getInitialDialogValue('calculate');
    this.modalDialogValue.set(dialogValue);
    this.#layoutStore.setCurrentAuthAction(AuthActions.Calculation);
    this.modalDialogVisible.set(true);
    this.viewDialog = 'modal';
    this.modalDialogTitle.set(`Calculate ${this.functionSpec()?.title}`);
    return;
  }

  modalVisibleEffect = effect(
    () => {
      const visible = this.modalDialogVisible();
      if (visible) return;
      this.modalExtendData.set(null);
    },
    { allowSignalWrites: true },
  );

  modalExtendData = signal<LayoutModalExtendData | null>(null);
  synthesizeClick() {
    this.selectedItem.set(null);
    this.modalDialogType.set('create');
    this.dialogConfig.set(this.functionSpec()?.form_config ?? {});
    this.modalDialogConfig.set(this.functionSpec()?.form_config ?? {});
    // this.dialogValue.set(value);
    this.actionBtnFooterModalDialog = this.dialogConfig()
      ?.btnModalDialogFooter ?? [
      {
        id: 'run',
        title: 'Run',
        type: 'primary',
      },
    ];
    // this.dialogValue.set(value);
    this.modalDialogVisible.set(true);
    this.viewDialog = 'modal';
    this.modalDialogTitle.set(`${this.functionSpec()?.title}`);
    this.modalExtendData.set({
      authAction: AuthActions.Calculation,
    });
  }

  createClick(value?: NzSafeAny) {
    this.selectedItem.set(null);
    this.dialogType.set('create');
    this.dialogConfig.set(this.functionSpec()?.form_config ?? {});

    // Log để debug
    console.log('createClick with value:', value);

    // Chỉ thiết lập dialogValue nếu value không phải undefined
    if (value !== undefined) {
      this.dialogValue.set(value);

      // Thêm timeout để đảm bảo UI được cập nhật
      setTimeout(() => {
        console.log('dialogValue after timeout:', this.dialogValue());
      }, 300);
    } else {
      this.dialogValue.set({});
    }

    this.dialogVisible.set(true);
    return;
  }

  isFiltering = computed(() => this.isValidFilterValue());

  currentFilter = computed(() => this.filterValue());

  clearSearch() {
    this.searchValue.set('');
    this.pageIndex.set(1);
  }

  addFilter() {
    this.filterClickOne(this.currentFilter() || this.filterValue());
  }

  duplicateValueTransform = computed(() => {
    return this.functionSpec()?.layout_options?.duplicate_value_transform;
  });

  dialogOverrideValue = signal<NzSafeAny>(null);
  async duplicateClick(value?: NzSafeAny, overrideValue?: NzSafeAny) {
    if (!this.viewHistoryAfterCreated()) {
      this.selectedItem.set(null);
    }
    this.dialogType.set('duplicate');
    this.dialogConfig.set(this.functionSpec()?.form_config ?? {});

    if (this.duplicateValueTransform()?.fields?.length) {
      const overrideValue = await this.utilService.transformDuplicateValues(
        value,
        this.duplicateValueTransform()?.fields,
        this.duplicateValueTransform()?.transform,
      );
      this.dialogOverrideValue.set(overrideValue);
    } else {
      if (overrideValue) {
        this.dialogOverrideValue.set(overrideValue);
      }
    }
    this.dialogValue.set(value);
    this.dialogVisible.set(true);

    return;
  }

  async onProceed(
    value?: NzSafeAny,
    type?: DialogType,
    callback?: (success: boolean) => void,
    isCallApi?: boolean,
  ) {
    let backendUrl = this.functionSpec()?.create_form?.backendUrl;
    if (backendUrl && isCallApi) {
      backendUrl = mappingUrl(backendUrl, value);

      let filterMapping = this.functionSpec()?.create_form?.filterMapping;
      if (filterMapping) {
        filterMapping = await this.utilService.transformRedirectTo(
          value,
          filterMapping,
        );
      }
      this._service
        .getObject(backendUrl, filterMapping, this.getFaceCodeForService())
        .subscribe({
          next: (data: NzSafeAny) => {
            if (data?.success) {
              callback?.(true);
              this.selectedItem.set(value);
              this.dialogType.set(type ?? 'proceed');
              this.dialogConfig.set(this.functionSpec()?.form_config ?? {});
              this.dialogValue.set(value);
              this.dialogVisible.set(true);
            } else {
              callback?.(false);
              const proceedErrorMessage =
                this.functionSpec()?.create_form?.proceedErrorMessage;
              this.toast.showToast('error', 'Error', proceedErrorMessage);
            }
          },
          error: (err: { error: { message: string } }) => {
            callback?.(false);
            this.toast.showToast('error', 'Error', err.error?.message);
          },
        });
    } else {
      callback?.(true);
      this.selectedItem.set(value);
      this.dialogType.set(type ?? 'proceed');
      this.dialogConfig.set(this.functionSpec()?.form_config ?? {});
      this.dialogValue.set(value);
      this.dialogVisible.set(true);
    }
  }

  proceedCustomDialogVisible = signal(false);

  proceedCustomDialogValue = signal<NzSafeAny | null>(null);
  dialogProceedCustomConfig = signal<FormConfig | NzSafeAny>({}); // formConfig

  onProceedCustom(value?: NzSafeAny) {
    this.selectedItem.set(value);
    this.proceedCustomDialogValue.set(value);
    this.dialogProceedCustomConfig.set(this.functionSpec()?.create_form ?? {});
    this.proceedCustomDialogVisible.set(true);

    return;
  }

  // payment form
  paymentDialogVisible = signal(false);

  openPaymentDialog() {
    this.paymentDialogVisible.set(true);
  }

  closePaymentDialog() {
    this.paymentDialogVisible.set(false);
  }

  //multi typing form
  multiDialogVisible = signal(false);

  openMultiDialog() {
    this.multiDialogVisible.set(true);
  }

  closeMultiDialog() {
    this.multiDialogVisible.set(false);
  }

  isLocked = signal<boolean | null>(null);
  listItemChecked: Data[] = [];
  updateLockStatus() {
    this.isLocked.set(true);
    this.listItemChecked = this.listOfSelectedItems();
  }

  updateUnLockStatus() {
    this.isLocked.set(false);
    this.listItemChecked = this.listOfSelectedItems();
  }

  permissionKey = computed(() => {
    return this.functionSpec()?.permission_key ?? undefined;
  });

  detailDialogVisible = signal(false);
  layoutDetailDialogVisible = signal(false);
  detailDialogTitle = computed(() => `View ${this.functionSpec().title}`);
  detailDialog = viewChild<LayoutDialogComponent>('detailDialog');
  async viewClickOne(id: string, value: NzSafeAny, e?: Event) {
    if (e) e.stopPropagation();

    this.dialogConfig.set(this.functionSpec()?.form_config ?? {});
    const { actionId, params } = this.actionClickRow() || {};

    if (actionId && params) {
      switch (actionId) {
        case 'routing': {
          const href = this.location.path() + '/' + value[params];
          this.router.navigate([href]);
          return;
        }
      }
    }

    this.selectedItem.set(value);
    this.#store.setDetailId(value?.id || '');
    this.#store.setDataDetail(value);

    this.dialogType.set('view');
    // this.dialogConfig.set(this.functionSpec()?.form_config ?? {});
    this.dialogValue.set(value);
    if (this.functionSpec().detail_function_spec) {
      // this.dialogVisible.set(true);
      this.layoutDetailDialogVisible.set(true);
    } else {
      this.detailDialogVisible.set(true);
    }
  }

  isSubDetailVisible = false;
  subDetailClickOne(id: string, value: NzSafeAny, e?: Event) {
    if (e) e.stopPropagation();
    this.selectedItem.set(value);
    this.#store.setDetailId(value?.id || '');
    this.#store.setDataDetail(value);

    this.dialogType.set('view');
    this.dialogConfig.set(this.functionSpec()?.create_form ?? {});
    this.dialogValue.set(value);
    this.isSubDetailVisible = true;
    return;
  }

  isPopup = signal<boolean | undefined>(undefined);
  layoutHistory = viewChild(LayoutHistoryComponent);
  async editClickOne(
    id: string,
    value: NzSafeAny,
    e?: Event,
    isCustom = false,
  ) {
    // if layout options has edit_step_layout and set to true, then show step layout
    const layoutOptions = this.layoutOptions() as NzSafeAny;
    if (layoutOptions?.edit_step_layout) {
      this.onEditSchedule(value);
      return;
    }

    // do normal edit
    if (e) e.stopPropagation();
    this.selectedItem.set(value);

    // set history id to show exacly history
    this.selectedHistoryId.set(id);
    this.dialogType.set('edit');

    if (isCustom) {
      this.isPopup.set(false);
      this._editFooterButtonsCustom.set(this.editFooterButtonsCustom());
    }
    // this.dialogConfig.set(this.functionSpec()?.form_config ?? {});

    this.dialogConfig.set(this.functionSpec()?.form_config ?? {});
    if (this.customValueBeforeEdit()) {
      const newValue = await this.utilService.transformRedirectTo(
        value,
        this.customValueBeforeEdit(),
      );
      this.dialogValue.set(newValue);
    } else {
      this.dialogValue.set(value);
    }
    this.dialogVisible.set(true);

    return;
  }

  deleteMultiItems = computed(() => {
    return this.functionSpec()?.layout_options?.delete_multi_items ?? false;
  });

  applyDeleteMultiItemsToDeleteOne = computed(
    () =>
      this.functionSpec()?.layout_options
        ?.apply_delete_multi_items_to_delete_one ?? false,
  );

  refreshDataTable() {
    this.refreshData.update((e) => !e);
  }

  async precheckDelete(
    config: PrecheckDeleteApiConfig,
    value: Record<string, NzSafeAny>,
  ) {
    const precheckApiUrl = await this.getCustomUrl(config, value);

    if (!precheckApiUrl) return null;
    return firstValueFrom(
      this._service.Get(precheckApiUrl, this.getFaceCodeForService()).pipe(
        catchError((err) => {
          this.toast.showToast('error', 'Error', err.error?.message);
          return of(null);
        }),
      ),
    );
  }

  precheckDeleteApi = computed(() => this.layoutOptions()?.precheck_delete_api);
  customDeleteBody = computed(() => this.layoutOptions()?.custom_delete_body);
  applyCustomDeleteUrlToDeleteInHistory = computed(
    () =>
      this.layoutOptions()?.apply_custom_delete_url_to_delete_in_history ??
      true,
  );

  private getErrorType(code: DeleteErrorCode) {
    return (
      LayoutTableComponent.DELETE_ERROR_TYPE_MAPPING[code] ??
      ErrorType.BusinessLogic
    );
  }

  private getDeleteErrorStats(errors: DeleteErrorMapping[]) {
    const errorsGroup = errors.reduce(
      (acc, item) => {
        const type = this.getErrorType(item.code);
        if (acc[type]) {
          acc[type].push(item);
        } else {
          acc[type] = [item];
        }
        return acc;
      },
      {} as Record<ErrorType, DeleteErrorMapping[]>,
    );

    return Object.keys(errorsGroup).map((key) => {
      const type = key as ErrorType;
      return {
        type,
        number: errorsGroup[type].length ?? 0,
      };
    });
  }

  deleteErrorPermissionContent = viewChild<TemplateRef<HTMLElement>>(
    'deleteErrorPermissionContent',
  );

  deleteErrorPermissionContentOne = viewChild<TemplateRef<HTMLElement>>(
    'deleteErrorPermissionContentOne',
  );

  showDeletionDetails = signal(false);
  deletionDetailsConfig = signal<{
    items: Data[];
    errors: DeleteErrorMapping[];
    fields: LocalField[];
  }>({ items: [], errors: [], fields: [] });
  showMessageDeleteError(
    error: { message: string; code?: string } | DeleteErrorMapping[],
    status: number,
    cb?: (status: boolean) => void,
  ) {
    if (Array.isArray(error)) {
      const items = this.listOfSelectedItems();
      const selectedIds = items.map((item) => item.id);
      // TODO: in case one item have multiple validate errors, remove if find a better idea
      error = _.uniqBy(error, 'id');
      // in case the result return have id not in the selected list, just to make sure
      error = error.filter((err) => selectedIds.includes(err.id));

      const errorsStats = this.getDeleteErrorStats(error);

      // TODO: if all items cannot delete because don't have permission then show popup got it, should move into a function to check
      if (
        error.length === items.length &&
        errorsStats.every((stat) => stat.type === ErrorType.Permission)
      ) {
        this.modalComponent.showDialog(
          {
            nzTitle: 'You can not delete these records',
            nzContent: this.deleteErrorPermissionContent(),
            nzWrapClassName: 'popup popup-error',
            nzIconType: 'icons:x-circle',
            nzOkText: 'Got it',
            nzCancelText: null,
            nzWidth: '400px',
            nzClosable: false,
            nzCentered: true,
          },
          'warning',
        );
        return;
      }

      this.deleteSummaryModal.create(
        {
          total: this.listOfSelectedItems().length,
          errorsStats,
        },
        {
          onDetails: () => {
            this.deletionDetailsConfig.set({
              items,
              errors: error as DeleteErrorMapping[],
              fields: this.functionSpec().local_fields ?? [],
            });
            this.showDeletionDetails.set(true);
          },
        },
      );
      // TODO: only refresh data when some records deleted, maybe it's not a good idea to refresh data here!!!
      if (items.length !== error.length) {
        this.refreshData.update((e) => !e);
        const errorIds = error.map((err) => err.id);
        if (this.layoutOptions()?.store_selected_items) {
          const deletedIds = selectedIds.filter((id) => !errorIds.includes(id));
          this.clearSelectedItems(deletedIds);
        } else {
          this.tableRef()?.setSelectedOnChangesData(errorIds);
        }
      }
      return;
    }

    const serverErrorList = [
      500, 501, 502, 503, 504, 505, 506, 507, 508, 510, 511,
    ];
    if (!serverErrorList.includes(status)) {
      this.modalComponent.showDialog(
        {
          nzTitle: 'Cannot Delete',
          nzContent:
            error.code == DeleteErrorCode.Permission
              ? this.deleteErrorPermissionContentOne()
              : error.message,
          nzWrapClassName: 'popup popup-confirm',
          nzIconType: 'icons:warning',
          nzOkText: 'Got it',
          nzCancelText: null,
          nzWidth: '400px',
          nzClosable: false,
          nzCentered: true,
          nzClassName: 'popup-got-it',
          nzOnOk: () => {
            cb?.(false);
          },
        },
        'warning',
      );
    } else {
      this.toast.showToast('error', 'Error', error.message);
      cb?.(false);
    }
  }

  async deleteClickOne(
    id: string,
    value?: NzSafeAny,
    e?: Event | null,
    cb?: (status: boolean) => void,
    isHistory?: boolean,
  ) {
    if (e) e.stopPropagation();
    let title = 'Delete record?';
    let content =
      'This will permanently delete the record and all associated data. Delete anyway?';

    // for precheck delete
    const precheckDeleteConfig = this.precheckDeleteApi();
    if (precheckDeleteConfig) {
      const precheckRes = await this.precheckDelete(
        precheckDeleteConfig,
        value,
      );

      if (isNil(precheckRes)) {
        cb?.(false);
        return;
      }

      const confirmConfig = precheckDeleteConfig.confirm;
      const extendValue = { precheckRes, record: value };
      if (confirmConfig) {
        const newTitle = confirmConfig?._title
          ? await this.transformValue(
              confirmConfig?._title?.transform,
              extendValue,
            )
          : confirmConfig.title;
        const newContent = confirmConfig?._content
          ? await this.transformValue(
              confirmConfig?._content?.transform,
              extendValue,
            )
          : confirmConfig.content;

        if (!isNil(newTitle)) title = newTitle;
        if (!isNil(newContent)) content = newContent;
      }
    }

    const url = this.url();
    let customUrl: string;

    if (
      (isHistory &&
        this.applyCustomDeleteUrlToDeleteInHistory() &&
        this.customDeleteBackendUrl()) ||
      (!isHistory && this.customDeleteBackendUrl())
    ) {
      customUrl = mappingUrl(this.customDeleteBackendUrl(), value);
    }

    let customBodyDelete = {};
    if (this.customDeleteBody() && this.applyDeleteMultiItemsToDeleteOne()) {
      customBodyDelete = await this.transformValue(
        this.customDeleteBody() || '',
        { data: [value], isHistory },
      );
    }

    this.modalComponent.showDialog({
      nzTitle: title,
      nzContent: content,
      nzWrapClassName: 'popup popup-error',
      nzIconType: 'icons:trash-bold',
      nzOkText: 'Delete',
      nzCancelText: 'Cancel',
      nzWidth: '400px',
      nzClosable: false,
      nzCentered: true,
      nzOnOk: () => {
        if (url) {
          this.#layoutStore.setLoading(true);
          const delete$ = this.applyDeleteMultiItemsToDeleteOne()
            ? this._service.deleteMultiItems(
                url,
                [id],
                customBodyDelete,
                this.getFaceCodeForService(),
              )
            : this._service.deleteItem(
                url,
                id,
                customUrl,
                this.getFaceCodeForService(),
              );

          delete$.subscribe({
            next: () => {
              this.#layoutStore.setLoading(false);
              this.isDidSetOriginTotal = false;
              this.toast.showToast('success', '', 'Record deleted');
              this.refreshData.update((e) => !e);
              if (this.detailDialogVisible()) {
                this.detailDialogVisible.set(false);
              } else if (this.layoutDetailDialogVisible()) {
                this.layoutDetailDialogVisible.set(false);
              } else {
                this.closeDialog();
              }
              this.refreshDataWidget('delete', value);
              cb?.(true);
              return;
            },
            error: (err) => {
              //TODO - change when BE ready for dynamic error code.
              const errResponse = Array.isArray(err.error)
                ? err.error[0]
                : err.error;
              this.#layoutStore.setLoading(false);
              this.showMessageDeleteError(errResponse, err.status, cb);
            },
          });
        } else this.toast.showToast('success', '', 'Record deleted');
      },
    });

    return;
  }

  async lockClickOne(
    item: NzSafeAny,
    isLock: boolean,
    e?: Event,
    callback?: (success: boolean) => void,
  ) {
    if (e) e.stopPropagation();
    // get fields to update
    const actionID = isLock ? 'lock' : 'unlock';
    const customActionId = isLock ? 'lockOne' : 'unlockOne';
    const _actionID = this.isDynamicConfigTable() ? customActionId : actionID;
    const actionHandler = this.actionOneHandler()?.[_actionID];
    const url = this.url();
    const customUrl = actionHandler?._backendUrl
      ? mappingUrl(actionHandler?._backendUrl ?? '', this.parent() ?? {})
      : actionHandler?.backendUrl;
    const method = actionHandler?.method;
    // get confirm content
    const confirmSetting = actionHandler?.confirm;
    const {
      title = `${isLock ? 'Lock' : 'Unlock'} the selected record?`,
      content = `Are you sure you want to ${isLock ? 'lock' : 'unlock'} this record?`,
    } = confirmSetting ?? {};

    //TODO - refactor the hard body for PR_054
    const update_fields =
      actionHandler?._update_fields && isNil(method)
        ? {
            payrollEmployees: await this.appendBodyByAction(_actionID, [item]),
          }
        : method
          ? await this.handleBody(actionHandler, item)
          : actionHandler?.update_fields;

    const authAction = this.getActionCodeByAction(actionID);
    this.modalComponent.showDialog(
      {
        nzTitle: title,
        nzContent: content,
        nzWrapClassName: 'popup popup-confirm',
        nzIconType: isLock ? 'icons:lock' : 'icons:lock-key-open',
        nzOkText: 'Confirm',
        nzCancelText: 'Cancel',
        nzWidth: '400px',
        nzClosable: false,
        nzCentered: true,
        nzOnOk: () => {
          if (url)
            this._service
              .lockItem(
                url,
                item.id,
                update_fields ?? {},
                customUrl,
                method,
                this.getFaceCodeForService(),
                { authAction },
              )
              .subscribe({
                next: () => {
                  this.toast.showToast(
                    'success',
                    'Success',
                    `${isLock ? 'Lock' : 'Unlock'} Successfully`,
                  );
                  callback?.(true);
                  this.refreshData.update((e) => !e);
                  this.closeDialog();
                  this.eventSubjects()?.['lock']?.next(null);
                  return;
                },
                error: (err) => {
                  this.toast.showToast('error', 'Error', err.error?.message);
                  callback?.(false);
                },
              });
          else
            this.toast.showToast(
              'success',
              'Success',
              `${isLock ? 'Lock' : 'Unlock'} Successfully`,
            );
        },
      },
      'warning',
    );
  }

  filterClickOne(value: NzSafeAny) {
    this.isViewDetailConfig.set(false);
    this.dataViewDetailConfig.set({});
    this.dialogType.set('filter');
    this.dialogConfig.set(
      (this.functionSpec()?.filter_config as NzSafeAny) ?? {},
    );
    this.dialogVisible.set(true);
    this.dialogValue.set(value);
    return;
  }

  onChangeValueCell(e: NzSafeAny, row: Data, column: string) {
    row[column] = e;
    this.isChangeValueCell = true;
  }

  // display click handler
  async onDisplayClickHandler(e: NzSafeAny, row: Data, extraConfig: NzSafeAny) {
    // if generate report
    if (
      extraConfig?.generate_report === true &&
      this.checkPermission('generateReport', row)
    ) {
      const reAuthRes = await this.checkReAuthentication('generateReport', row);
      if (!reAuthRes) return;

      this.playClickOne(row.id, row, e);
    } else if (extraConfig?.add_favourite) {
      if (!row['star']) {
        this.addFavouriteClickOne(row);
      } else {
        this.removeFavouriteClickOne(row);
      }
      // console.log('add_favourite', row);
    } else if (extraConfig?.openEditDialog && this.checkPermission('edit')) {
      this.editClickOne(row.id, row, e);
    }
  }

  addFavouriteClickOne(row: Data) {
    const url = `${this.url()}/add-favorite`;
    const body = {
      reportCodes: [
        {
          reportCode: row['functionCode'],
        },
      ],
    };
    this._service.Post(url, body, this.getFaceCodeForService()).subscribe({
      next: () => {
        this.toast.showToast('success', '', `Added favourite successfully`);
        this.refreshData.update((e) => !e);
        return;
      },
      error: (err) =>
        this.toast.showToast('error', 'Error', err.error?.message),
    });
  }

  removeFavouriteClickOne(row: Data) {
    if (row?.id) {
      const body = {
        reportCode: row['functionCode'],
      };
      const url = `${this.url()}/remove-favorite`;
      // console.log('url', url);

      this._service.Post(url, body, this.getFaceCodeForService()).subscribe({
        next: () => {
          this.toast.showToast('success', '', `Removed favourite successfully`);
          this.refreshData.update((e) => !e);
          return;
        },
        error: (err) =>
          this.toast.showToast('error', 'Error', err.error?.message),
      });
    }
  }

  async customBodyActionMany(action: string) {
    switch (action) {
      case 'delete':
        if (this.customDeleteBody()) {
          return await this.transformValue(
            this.customDeleteBody() || '',
            this.listOfSelectedItems(),
          );
        }
        return {};
      case 'lock':
      case 'unlock':
        return await this.transformValue('', this.listOfSelectedItems());
    }
  }

  getActionCodeByAction(action: string) {
    switch (action) {
      case 'edit':
        return AuthActions.Update;
      case 'lock':
        return AuthActions.Lock;
      case 'unlock':
        return AuthActions.Unlock;
      default:
        return AuthActions.Read;
    }
  }

  async actionClickMany(action: string) {
    const actionsHandler = this.actionsManyHandler();
    const actionHandler = actionsHandler?.[action];
    if (!actionHandler) return;
    const url = mappingUrl(actionHandler.url ?? '', this.parent() ?? {});
    const {
      content,
      icon,
      type,
      title = `${capitalize(action)} ${this.listOfSelectedItems().length} records`,
    } = actionHandler.confirm ?? {};

    let body = { ids: this.listOfSelectedItems().map((item) => item.id) };
    if (actionHandler.custom_body) {
      body = await this.transformValue(actionHandler.custom_body, {
        selectedItems: this.listOfSelectedItems(),
      });
    }

    const popupClassName = `popup popup-${type === 'warning' ? 'confirm' : type}`;
    const popupType = (type ?? 'warning') as Exclude<ModalType, ''>;

    const authAction = this.getActionCodeByAction(actionHandler.action);

    this.modalComponent.showDialog(
      {
        nzContent: content,
        nzTitle: title,
        nzWrapClassName: popupClassName,
        nzIconType: `icons:${icon ?? 'warning'}`,
        nzOkText: `${capitalize(action)}`,
        nzCancelText: 'Cancel',
        nzWidth: '400px',
        nzClosable: false,
        nzOnOk: () => {
          const itemCount = this.listOfSelectedItems().length;
          if (url) {
            this._service
              .createItem(url, body, this.getFaceCodeForService(), {
                authAction,
              })
              .pipe(
                catchError((err) => {
                  this.toast.showToast('error', 'Error', err.error?.message);
                  return of(undefined);
                }),
              )
              .subscribe({
                complete: () => {
                  this.toast.showToast(
                    'success',
                    '',
                    `${capitalize(action)} ${itemCount} records`,
                  );
                  this.refreshData.update((e) => !e);
                  this.eventSubjects()?.[action]?.next(null);
                },
              });
          }
        },
      },
      popupType,
    );
  }

  // ------------------------------
  // action by multiple data
  async deleteClickMany() {
    const url = this.url();

    let customBodyDelete = null;
    if (this.customDeleteBody()) {
      customBodyDelete = await this.transformValue(
        this.customDeleteBody() || '',
        { data: this.listOfSelectedItems() },
      );
    }
    const selectedItemsCount = this.listOfSelectedItems().length ?? 0;
    const title =
      selectedItemsCount === 1
        ? 'Delete record'
        : `Delete ${selectedItemsCount} records`;
    this.modalComponent.showDialog({
      nzContent:
        'This will permanently delete the record and all associated data. Delete anyway?',
      nzTitle: title,
      nzWrapClassName: 'popup popup-error',
      nzIconType: 'icons:trash-bold',
      nzOkText: 'Delete',
      nzCancelText: 'Cancel',
      nzWidth: '400px',
      nzClosable: false,
      nzCentered: true,
      nzOnOk: () => {
        this.#layoutStore.setLoading(true);
        const deleteCount = this.listOfSelectedItems().length;
        if (url) {
          if (this.deleteMultiItems()) {
            this._service
              .deleteMultiItems(
                url,
                this.listOfSelectedItems().map((item) => item.id),
                customBodyDelete,
              )
              .subscribe({
                next: () => {
                  this.#layoutStore.setLoading(false);
                  this.toast.showToast(
                    'success',
                    '',
                    `${deleteCount} records deleted`,
                  );
                  this.clearSelectedItems();
                  this.refreshData.update((e) => !e);
                  return;
                },
                error: (err) => {
                  this.#layoutStore.setLoading(false);
                  this.showMessageDeleteError(err.error, err.status);
                },
              });
          } else {
            this._service
              .deleteItems(
                url,
                this.listOfSelectedItems().map((item) => {
                  return {
                    id: item.id,
                    customUrl: this.customDeleteBackendUrl()
                      ? mappingUrl(this.customDeleteBackendUrl(), item)
                      : null,
                  };
                }),
              )
              .subscribe((result) => {
                this.#layoutStore.setLoading(false);
                const errorObject =
                  Object.values(result).filter(
                    (item) => item.status === 'error',
                  ) || [];
                const successCount = Object.values(result).filter(
                  (item) => item.status === 'success',
                ).length;
                const errorCount = errorObject.length;
                if (deleteCount === successCount && successCount > 0) {
                  this.toast.showToast(
                    'success',
                    '',
                    `${deleteCount} records deleted`,
                  );
                }
                if (
                  deleteCount !== successCount &&
                  successCount > 0 &&
                  errorCount > 0
                ) {
                  this.toast.showToast(
                    'warning',
                    'Warning',
                    `${successCount} records deleted, ${errorCount} records failed`,
                  );
                }
                if (
                  deleteCount !== successCount &&
                  successCount === 0 &&
                  errorCount > 0
                ) {
                  const errorMessage =
                    errorCount === 1
                      ? errorObject[0]?.value?.message
                      : `${errorCount} records failed`;
                  //TODO - change when BE ready for dynamic error code.
                  this.showMessageDeleteError(
                    { message: errorMessage },
                    errorObject[0].statusCode,
                  );

                  // this.toast.showToast(
                  //   'error',
                  //   'Error',
                  //   errorCount === 1
                  //     ? errorObject[0]?.value?.message
                  //     : `${errorCount} records failed`,
                  // );
                }
                if (successCount > 0) {
                  this.clearSelectedItems();
                  this.refreshData.update((e) => !e);
                }
              });
          }
        } else
          this.toast.showToast('success', 'Success', 'Deleted Successfully');
      },
    });
  }

  lockunlockClickMany(tool: LayoutButton) {
    const url = this.url();
    const content = tool?.confirm?.content
      ? tool?.confirm?.content
      : `${'Are you sure you want to lock this information?'}`;
    const title = tool?.confirm?.title
      ? tool?.confirm?.title
      : `${tool?.id === 'lock' ? 'Lock ' : 'Unlock '} ${this.listOfSelectedItems().length > 0 ? this.listOfSelectedItems().length : 'All'} records`;
    const icon = tool?.confirm?.icon
      ? tool?.confirm?.icon
      : `${tool?.id === 'lock' ? 'icons:lock' : 'icons:lock-open'}`;
    const okText = tool?.confirm?.okText
      ? tool?.confirm?.okText
      : `${tool?.id === 'lock' ? 'Lock ' : 'Unlock '}`;
    const cancelText = tool?.confirm?.cancelText
      ? tool?.confirm?.cancelText
      : 'Cancel';

    const authAction =
      tool?.id === 'lock' ? AuthActions.Lock : AuthActions.Unlock;
    this.modalComponent.showDialog(
      {
        nzContent: content,
        nzTitle: title,
        nzWrapClassName: 'popup popup-info',
        nzIconType: icon,
        nzOkText: okText,
        nzCancelText: cancelText,
        nzWidth: '400px',
        nzClosable: false,
        nzCentered: true,
        nzOnOk: () => {
          if (url)
            this._service
              .createItem(
                url + '/' + tool?.id,
                this.listOfSelectedItems().map((item) => {
                  return {
                    id: item.id,
                    customUrl: this.customDeleteBackendUrl()
                      ? mappingUrl(this.customDeleteBackendUrl(), item)
                      : null,
                  };
                }),
                this.getFaceCodeForService(),
                { authAction },
              )
              .subscribe({
                next: () => {
                  this.toast.showToast(
                    'success',
                    '',
                    `${this.listOfSelectedItems().length > 0 ? this.listOfSelectedItems().length : 'All'} records ${tool?.id === 'lock' ? 'Locked' : 'Unlocked'}`,
                  );
                  this.refreshData.update((e) => !e);

                  return;
                },
                error: (err) =>
                  this.toast.showToast('error', 'Error', err.error?.message),
              });
          else
            this.toast.showToast(
              'success',
              'Success',
              `${tool?.id === 'lock' ? 'Locked' : 'Unlocked'} Successfully`,
            );
        },
      },
      'info',
    );

    return;
  }

  async handleBody(actionHandler: NzSafeAny, item: NzSafeAny) {
    return actionHandler?._update_fields
      ? await this.buildCustomUrl(actionHandler?._update_fields, item)
      : actionHandler?.update_fields;
  }

  async appendBodyByAction(actionID: string, list: NzSafeAny[] = []) {
    const actionHandler = this.actionOneHandler()?.[actionID];
    const selectedItems = list.length > 0 ? list : this.listOfSelectedItems();
    return await Promise.all(
      selectedItems.map(async (item) => {
        return await this.handleBody(actionHandler, item);
      }),
    );
  }

  async lockClickMany(url: string, isLock: boolean) {
    // TODO: hard for PR_054, refactor later
    const actionID = isLock ? 'lock' : 'unlock';
    const {
      title = `${isLock ? 'Lock' : 'Unlock'} all record?`,
      content = `Are you sure you want to ${isLock ? 'lock' : 'unlock'} all this record?`,
    } = {};
    let body: NzSafeAny = {};
    const selectedItem = this.listOfSelectedItems().length;

    if (selectedItem > 0) {
      body = {
        payrollEmployees: await this.appendBodyByAction(actionID),
      };
    } else {
      body = {
        payrollPeriodSettingCode: this.parent()?.['payrollPeriodSettingCode'],
        payrollPeriodCode: this.parent()?.['payrollPeriodCode'],
      };
    }

    url = url + (selectedItem === 0 ? '-all' : '');

    const authAction = isLock ? AuthActions.Lock : AuthActions.Unlock;

    this.modalComponent.showDialog({
      nzTitle: title,
      nzContent: content,
      nzWrapClassName: 'popup popup-confirm',
      nzIconType: isLock ? 'icons:lock' : 'icons:lock-key-open',
      nzOkText: 'Confirm',
      nzCancelText: 'Cancel',
      nzWidth: '400px',
      nzClosable: false,
      nzCentered: true,
      nzOnOk: () => {
        if (url)
          this._service
            .lockItem(
              '',
              'lock',
              body ?? {},
              url,
              undefined,
              this.getFaceCodeForService(),
              { authAction },
            )
            .subscribe({
              next: () => {
                this.toast.showToast(
                  'success',
                  'Success',
                  `${isLock ? 'Lock' : 'Unlock'} Successfully`,
                );
                this.refreshData.update((e) => !e);
                this.refresh();
                this.closeDialog();
                this.historyDialogVisible = false;
                this.eventSubjects()?.['lock']?.next(null);
                return;
              },
              error: (err) =>
                this.toast.showToast('error', 'Error', err.error?.message),
            });
      },
    });

    return;
  }

  handleSetValueFromParent() {
    const mappingValueFromParent =
      this.functionSpec()?.create_form?.mappingValueFromParent ?? undefined;
    let valueFromParent = {};
    if (mappingValueFromParent) {
      forEach(mappingValueFromParent, (value, key) => {
        valueFromParent = { ...valueFromParent, [key]: this.parent()?.[value] };
      });
    }

    return valueFromParent;
  }

  synthesizeClickMany(tool: LayoutButton) {
    const url = this.url();
    const content = tool?.confirm?.content
      ? tool?.confirm?.content
      : 'Are you sure you want to synthesize this information?';
    const title = tool?.confirm?.title
      ? tool?.confirm?.title
      : `Synthesize ${this.listOfSelectedItems().length > 0 ? this.listOfSelectedItems().length : 'All'} records`;
    const icon = tool?.confirm?.icon
      ? tool?.confirm?.icon
      : 'icons:monitor-play';
    const okText = tool?.confirm?.okText ? tool?.confirm?.okText : 'Synthesize';
    const cancelText = tool?.confirm?.cancelText
      ? tool?.confirm?.cancelText
      : 'Cancel';
    const defaultQuery = this.filterQueryCustom();
    this.modalComponent.showDialog(
      {
        nzContent: content,
        nzTitle: title,
        nzWrapClassName: 'popup popup-info',
        nzIconType: icon,
        nzOkText: okText,
        nzCancelText: cancelText,
        nzWidth: '400px',
        nzClosable: false,
        nzCentered: true,
        nzOnOk: () => {
          // const lockCount = this.listOfSelectedItems().length;
          if (url)
            this._service
              .createItem(
                url +
                  '/' +
                  tool?.id +
                  (defaultQuery && defaultQuery.length > 0
                    ? '?filter[0]=' +
                      defaultQuery[0].field +
                      '||' +
                      defaultQuery[0].operator +
                      '||' +
                      defaultQuery[0].value
                    : ''),
                this.listOfSelectedItems().map((item) => {
                  return {
                    id: item.id,
                    customUrl: this.customDeleteBackendUrl()
                      ? mappingUrl(this.customDeleteBackendUrl(), item)
                      : null,
                  };
                }),
                this.getFaceCodeForService(),
                { authAction: AuthActions.Calculation },
              )
              .subscribe({
                next: () => {
                  this.toast.showToast(
                    'success',
                    '',
                    `${this.listOfSelectedItems().length > 0 ? this.listOfSelectedItems().length : 'All'} records synthesized`,
                  );
                  this.refreshData.update((e) => !e);

                  return;
                },
                error: (err) =>
                  this.toast.showToast('error', 'Error', err.error?.message),
              });
          else
            this.toast.showToast(
              'success',
              'Success',
              'synthesized Successfully',
            );
        },
      },
      'info',
    );

    return;
  }

  trimObjectValues(obj: NzSafeAny): NzSafeAny {
    const res = mapValues(obj, (value) => {
      if (isString(value)) {
        return trim(toString(value));
      } else if (isDate(value)) {
        return value.toISOString();
      } else if (isObject(value) && !isArray(value)) {
        return this.trimObjectValues(value); // Recursively trim nested objects
      } else {
        return value;
      }
    });
    return res;
  }

  getApiValueByKey<K extends keyof ApiConfig>(
    config: ApiConfig,
    key: K,
    extendValue: Record<string, NzSafeAny>,
  ) {
    const transformKey = `_${key}` as K;
    const transformObj = config[transformKey] as { transform: string };
    if (transformObj?.transform)
      return this.transformValue(transformObj.transform, extendValue);
    return config[key];
  }

  getCustomUrl(config: ApiConfig, extendValue: Record<string, NzSafeAny>) {
    return this.getApiValueByKey(config, 'url', extendValue);
  }

  async getCustomBodyValue(
    extendValue: Record<string, NzSafeAny>,
    transform?: string,
  ) {
    let value = extendValue;

    if (transform) {
      value = await this.utilService.transformRedirectTo(
        extendValue,
        transform,
      );
    }

    return value;
  }

  jsonToBase64(jsonObject: NzSafeAny): string {
    // const utf8Bytes = new TextEncoder().encode(JSON.stringify(jsonObject));
    // return btoa(String.fromCharCode(...utf8Bytes));
    const utf8Bytes = new TextEncoder().encode(JSON.stringify(jsonObject));
    let binary = '';
    const len = utf8Bytes.length;

    for (let i = 0; i < len; i++) {
      binary += String.fromCharCode(utf8Bytes[i]);
    }

    return btoa(binary);
  }

  private clearSelectedItems(ids?: string[]) {
    if (!this.layoutOptions()?.store_selected_items) return;

    if (ids) {
      this.tableRef()?.removeSelectedItemsMapByIds(ids);
    } else {
      this.tableRef()?.clearSelectedItemsMap();
    }
  }

  private checkByExpression = async (expression: string, value: any) => {
    const expressionRes = await this.dynamicService.getJsonataExpression({})(
      expression,
      value,
    );
    // console.log('checkByExpression', expression, value, expressionRes);
    if (typeof expressionRes === 'boolean') {
      return expressionRes;
    }

    const { title, message, type = 'confirm' } = expressionRes ?? {};
    // no need to confirm if no message, so return true
    if (!message) return true;

    let isConfirm = false;

    let modalOptions: ModalOptions = {
      nzTitle: title,
      nzContent: message,
      nzCancelText: 'Cancel',
      nzWrapClassName: 'popup popup-confirm',
      nzOkText: 'Confirm',
      nzClosable: false,
      nzCentered: true,
      nzIconType: 'icons:warning',
      nzOnOk: () => (isConfirm = true),
    };

    if (type === 'info') {
      modalOptions = {
        ...modalOptions,
        nzOkText: 'Got it',
        nzClassName: 'popup-got-it',
        nzCancelText: null,
      };
    }
    const modalRef = this.modalComponent.showDialog(modalOptions, 'warning');

    if (type === 'info') {
      return false;
    }

    await firstValueFrom(modalRef.afterClose.asObservable());
    return isConfirm;
  };

  private precheckAction = async (
    action: string,
    value: Record<string, unknown>,
  ) => {
    const precheckConfig = this.layoutOptions()?.precheck_action?.find(
      (item) => item.action === action,
    );
    if (!precheckConfig) return true;

    if (precheckConfig.api) {
      const url = await this.getApiValueByKey(precheckConfig.api, 'url', value);
      if (!url) return true;
      const method = (precheckConfig.api.method ?? 'GET').toUpperCase();
      let observable: Observable<any> | null | undefined = null;
      if (method === 'GET') {
        observable = this._service.Get(url, this.getFaceCodeForService());
      } else {
        observable = this.getServiceMethod(method as TApiMethod)?.(url, value);
      }
      if (!observable) return true;

      const apiRes = await firstValueFrom(
        observable.pipe(
          catchError((err) => {
            console.error('failed to call api precheck, submit anyway', err);
            return of(true);
          }),
        ),
      );

      return this.checkByExpression(precheckConfig.expression, apiRes);
    }

    return this.checkByExpression(precheckConfig.expression, value);
  };

  refreshDataWidget(type?: string, extraData?: NzSafeAny) {
    if (this.widgetOptions()?.refreshHeader) {
      //refresh data select ERN for layout Employee profile
      this.layoutDataService.layoutEventEmit({
        key: 'refreshERN',
      });
    }

    //refresh data for block Employment Seniority and Contract
    if (this.widgetOptions()?.refeshDependent) {
      this.widgetOptions()?.refeshDependent?.forEach((item) => {
        this.layoutDataService.layoutEventEmit({
          key: 'refreshData',
          value: item,
          extraData,
        });
      });
    }

    const refreshProfileIfDelete =
      this.widgetOptions()?.refreshProfileIfDelete && type === 'delete';

    //refresh employee profile data
    if (this.widgetOptions()?.refreshProfile || refreshProfileIfDelete) {
      this.layoutDataService.layoutEventEmit({
        key: 'refreshProfile',
      });
    }
  }

  // dialog action
  async dialogSubmit(value: {
    type:
      | DialogType
      | 'toDelete'
      | 'toEdit'
      | 'saveAndAddNew'
      | 'toSubmit'
      | 'export'
      | 'toDuplicate';
    value: NzSafeAny;
    callback?: (status: boolean) => void;
    overrideValue?: NzSafeAny;
    authAction?: AuthActions;
  }) {
    const reAuthRes = await this.checkReAuthentication(
      LayoutTableComponent.getPermissionMapping(value.type),
    );
    if (!reAuthRes) {
      value.callback?.(false);
      return;
    }
    if (this.actionOneSelected()) {
      this.handleSubmitActionOne(value.value, value.callback);
      return;
    }
    const editCustom =
      this._editFooterButtonsCustom().length > 0 ? true : false;
    let url = this.url() ?? '';
    const { callback } = value ?? {};
    let valueForm = value.value;
    const originFormValue = value.value;

    const form_value_transform =
      this.functionSpec().form_config?.form_value_transform;
    const form_value_to_base_64 = this.dialogConfig().form_value_to_base_64;
    if (form_value_transform) {
      valueForm = await this.getCustomBodyValue(
        valueForm,
        form_value_transform,
      );
    }

    if (form_value_to_base_64) {
      valueForm = { base64_string: this.jsonToBase64(valueForm) };
    }

    let { authAction } = value;
    if (authAction && !isValidAuthAction(authAction)) {
      authAction = undefined;
    }

    switch (value.type) {
      case 'create':
      case 'saveAndAddNew': {
        const customApi = this.customCreateApi();
        if (customApi) {
          url = await this.getCustomUrl(customApi, originFormValue);
        }
        if (url) {
          of(undefined)
            .pipe(
              switchMap(() => {
                if (this.functionSpec().layout_options?.is_upload_file)
                  return this._service.createFormData(
                    url,
                    valueForm,
                    this.getFaceCodeForService(),
                    { authAction },
                  );
                return this._service.createItem(
                  url,
                  valueForm,
                  this.getFaceCodeForService(),
                  { authAction },
                );
              }),
            )
            .subscribe({
              next: (val: NzSafeAny) => {
                this.isDidSetOriginTotal = false;
                callback?.(true);
                this.toast.showToast('success', '', 'Record saved');
                // after create or edit success, view detail item
                if (this.historyDialogVisible) {
                  if (val?.id) {
                    this.selectedHistoryId.set(val.id);
                  }
                  this.layoutHistory()?.refreshHistoryData();
                } else if (val?.id && value.type !== 'saveAndAddNew') {
                  if (this.viewHistoryAfterCreated()) {
                    this.showHistoryClickOne(
                      { ...value.value, ...val },
                      val.id,
                      true,
                    );
                  } else if (this.viewAfterCreated()) {
                    setTimeout(() => {
                      this.viewClickOne('', val);
                    }, 500);
                  }
                }
                this.refreshData.update((e) => !e);
                this.pageIndex.set(1);
                if (this.expandCreateFormConfig()) {
                  this.reloadExpandCreateForm.update((prev) => !prev);
                }
                this.refreshDataWidget('create', value.value);
              },
              error: (err) => {
                callback?.(false);
                this.toast.showToast('error', 'Error', err.error?.message);
              },
            });
        } else {
          this.toast.showToast('success', '', 'Record saved');
        }

        break;
      }

      case 'edit': {
        const customApi = this.customUpdateApi();
        let method = undefined;
        if (customApi) {
          url = await this.getCustomUrl(customApi, originFormValue);
          method = await this.getApiValueByKey(
            customApi,
            'method',
            originFormValue,
          );
        }
        if (url)
          of(undefined)
            .pipe(
              switchMap(() => {
                if (customApi) {
                  if (method === 'POST') {
                    return this._service.createItem(
                      url,
                      valueForm,
                      this.getFaceCodeForService(),
                    );
                  }

                  if (method === 'PATCH') {
                    return this._service.Patch(
                      url,
                      valueForm,
                      this.getFaceCodeForService(),
                    );
                  }
                }

                if (this.functionSpec().layout_options?.is_upload_file) {
                  return this._service.updateFormData(
                    url,
                    this.selectedId(),
                    valueForm,
                    this.getFaceCodeForService(),
                  );
                }
                return this._service.updateItem(
                  url,
                  this.selectedId(),
                  valueForm,
                  editCustom ? url : undefined,
                  this.getFaceCodeForService(),
                );
              }),
            )
            .subscribe({
              next: (val: NzSafeAny) => {
                callback?.(true);
                this.toast.showToast('success', '', 'Record saved');
                editCustom && this._editFooterButtonsCustom.set([]);
                // after create or edit success, view detail item
                if (this.historyDialogVisible) {
                  this.layoutHistory()?.refreshHistoryData();
                } else if (this.viewHistoryAfterUpdated()) {
                  this.showHistoryClickOne(
                    this.selectedItem(),
                    this.selectedId(),
                    true,
                  );
                } else {
                  if (this.detailDialogVisible()) {
                    this.detailDialog()?.refreshData();
                  } else if (this.layoutDetailDialogVisible()) {
                    this.layoutDetailDialog()?.onRefreshData();
                  } else {
                    this.viewAfterUpdated() &&
                      this.viewClickOne('', {
                        ...this.trimObjectValues(valueForm),
                        id: this.selectedId(),
                      });
                  }
                }
                this.refreshData.update((e) => !e);
                this.resetPageIndexByAction('edit');
                this.refreshDataWidget('edit', value.value);
              },
              error: (err) => {
                callback?.(false);
                this.toast.showToast('error', 'Error', err.error?.message);
              },
            });
        else this.toast.showToast('success', '', 'Record saved');
        break;
      }

      case 'toSubmit': {
        const customApi = this.customSubmitApi();
        if (customApi) {
          url = await this.getCustomUrl(customApi, valueForm);
        }
        if (url)
          of(undefined)
            .pipe(
              switchMap(() => {
                if (this.functionSpec().layout_options?.is_upload_file)
                  return this._service.createFormData(
                    url,
                    valueForm,
                    this.getFaceCodeForService(),
                  );
                return this._service.createItem(
                  url,
                  valueForm,
                  this.getFaceCodeForService(),
                );
              }),
            )
            .subscribe({
              next: (val: NzSafeAny) => {
                callback?.(true);
                this.toast.showToast('success', 'Success', 'Record saved');
                this.refreshData.update((e) => !e);
              },
              error: (err) => {
                callback?.(false);
                this.toast.showToast('error', 'Error', err.error?.message);
              },
            });
        else this.toast.showToast('success', '', 'Record saved');
        break;
      }

      case 'proceed': {
        const customHistoryUrl = this.customHistoryBackendUrl();
        const mergeValue = {
          ...(this.selectedItem() ?? {}),
          ...(value?.value ?? {}),
        };
        const backendUrl = customHistoryUrl
          ? mappingUrl(customHistoryUrl, mergeValue)
          : url;
        if (backendUrl)
          of(undefined)
            .pipe(
              switchMap(() => {
                if (this.isUploadFile())
                  return this._service.createFormData(
                    backendUrl,
                    valueForm,
                    this.getFaceCodeForService(),
                  );
                return this._service.createItem(
                  backendUrl,
                  valueForm,
                  this.getFaceCodeForService(),
                );
              }),
            )
            .subscribe({
              next: (val: NzSafeAny) => {
                callback?.(true);
                this.toast.showToast('success', '', 'Record saved');
                if (this.historyDialogVisible) {
                  of(null)
                    .pipe(delay(200))
                    .subscribe(() => {
                      if (val?.id) {
                        this.selectedHistoryId.set(val?.id);
                      }
                      this.layoutHistory()?.refreshHistoryData();
                    });
                }
                this.refreshData.update((e) => !e);
              },
              error: (err) => {
                callback?.(false);
                this.toast.showToast('error', 'Error', err.error?.message);
              },
            });
        else this.toast.showToast('success', '', 'Record saved');

        break;
      }

      case 'duplicate': {
        const { id, ...data } = value?.value || {};
        if (url)
          of(undefined)
            .pipe(
              switchMap(() => {
                if (this.isUploadFile())
                  return this._service.createFormData(
                    url,
                    valueForm,
                    this.getFaceCodeForService(),
                  );
                return this._service.createItem(
                  url,
                  valueForm,
                  this.getFaceCodeForService(),
                );
              }),
            )
            .subscribe({
              next: (val: NzSafeAny) => {
                callback?.(true);
                this.toast.showToast('success', '', 'Record saved');
                if (val?.id) {
                  if (
                    this.viewHistoryAfterCreated() ||
                    this.historyDialogVisible
                  ) {
                    this.showHistoryClickOne(val as Data, val.id, true);
                  } else if (this.viewAfterCreated()) {
                    setTimeout(() => {
                      this.viewClickOne('', val);
                    }, 500);
                  }
                }
                this.dialogOverrideValue.set(null);
                this.refreshData.update((e) => !e);
              },
              error: (err) => {
                callback?.(false);
                this.toast.showToast('error', 'Error', err.error?.message);
              },
            });
        else this.toast.showToast('success', '', 'Record saved');
        break;
      }
      case 'toDuplicate': {
        if (value.value) this.duplicateClick(value.value, value.overrideValue);
        break;
      }
      case 'filter': {
        this.updateLayoutDialogVariables();
        this.filterValue.set(valueForm);
        this.pageIndex.set(1);
        this.clearSelectedItems();
        this.closeDialog();

        break;
      }

      case 'toEdit': {
        this.dialogVisible.set(true);
        this.dialogType.set('edit');
        if (authAction) {
          this.authAction.set(authAction);
        }
        break;
      }
      case 'toDelete': {
        const id = this.selectedId();
        if (id) {
          this.deleteClickOne(id, valueForm);
        }
        break;
      }
      case 'export': {
        const customApi = this.customExportApi();
        let method = undefined;
        if (customApi) {
          url = await this.getCustomUrl(customApi, originFormValue);
          method = await this.getApiValueByKey(
            customApi,
            'method',
            originFormValue,
          );
        }
        this._service
          .getFile(url, method, null, {
            onError: (err: string) =>
              this.toast.showToast('error', 'Error', err),
          })
          .subscribe();
        break;
      }
      default: {
        const action = this.dialogActions().find(
          (action) => action.id === value.type,
        );
        if (!action) {
          callback?.(false);
          return;
        }
        const { type, config } = action;
        switch (type) {
          case 'api': {
            if (!config) {
              callback?.(false);
              return;
            }

            const onError = (err: any) => {
              callback?.(false);
              const message =
                typeof err === 'string' ? err : err.error?.message;
              this.toast.showToast('error', 'Error', message);
            };

            const onOk = async () => {
              url = await this.getCustomUrl(config, originFormValue);
              if (!url) {
                callback?.(false);
                return;
              }
              let method = await this.getApiValueByKey(
                config,
                'method',
                originFormValue,
              );

              method = ((method ?? 'POST') as string).toUpperCase();

              let operationFunc: OperatorFunction<unknown, any> | null = null;
              if (config.isGetFile) {
                operationFunc = switchMap(() =>
                  this._service.getFile(url, method, undefined, {
                    onError,
                    faceCode: this.getFaceCodeForService(),
                  }),
                );
              } else {
                const fn = this.getServiceMethod(method as TApiMethod);
                if (fn) {
                  operationFunc = switchMap(() =>
                    fn(url, valueForm, this.getFaceCodeForService()),
                  );
                }
              }
              if (!operationFunc) {
                callback?.(false);
                return;
              }
              of(url)
                .pipe(operationFunc)
                .subscribe({
                  next: () => {
                    callback?.(true);
                    this.toast.showToast(
                      'success',
                      '',
                      config.messages?.success ?? 'Record saved',
                    );
                    if (this.detailDialogVisible()) {
                      this.detailDialog()?.refreshData();
                    }
                    this.refreshData.update((e) => !e);
                  },
                  error: onError,
                });
            };

            const { confirm } = action;
            if (confirm) {
              this.modalComponent.showDialog(
                {
                  nzContent: confirm.content,
                  nzTitle: confirm.title,
                  nzWrapClassName: 'popup popup-confirm',
                  nzIconType: 'icons:warning',
                  nzOkText: 'Confirm',
                  nzCancelText: 'Cancel',
                  nzClosable: false,
                  nzCentered: true,
                  nzOnOk: () => {
                    onOk();
                  },
                  nzOnCancel: () => {
                    callback?.(false);
                  },
                },
                'warning',
              );
            } else {
              onOk();
            }
          }
        }
      }
    }
  }
  layoutDialog = viewChild<LayoutDialogComponent>('layoutDialog');
  layoutDialogVariables = signal<Record<string, NzSafeAny>>({});
  updateLayoutDialogVariables() {
    if (isEmpty(this.filterOptionsLoadFromVariables())) return;
    const dialogRef = this.layoutDialog();
    if (!dialogRef) return;
    const variables = dialogRef.dynamicForm?.variablesSource;
    this.layoutDialogVariables.set(variables ?? {});
  }

  private resetPageIndexByAction(action: string) {
    if (!this.shouldResetPageIndexByAction(action)) return;
    this.pageIndex.set(1);
  }

  private shouldResetPageIndexByAction(action: string) {
    const resetPageIndexState =
      this.layoutOptions()?.reset_page_index_after_do_action ?? {};
    return resetPageIndexState[action];
  }

  // ------------------------------
  // history dialog form

  historyDialogVisible = false;
  selectedHistoryId = signal<string | null>(null);
  historyData = signal<Data | null>(null);
  showHistoryClickOne(
    value: Data | null = null,
    selectedId: string | null = null,
    passiveOpen = false,
  ) {
    if (
      !this.checkPermission('history') &&
      !this.functionSpec()?.layout_options?.show_history_detail_function
    )
      return;

    if (passiveOpen && value) this.preCheckDetailAction(value);
    this.historyData.set(value);
    this.selectedHistoryId.set(selectedId);
    this.historyDialogVisible = true;
    return;
  }

  isShowAfterCreate = signal<boolean>(false);
  dataShowAfterCreate = signal<NzSafeAny[]>([]);
  isCollapsContainer = computed(() => {
    return (
      this.functionSpec().layout_options?.collaps_options
        ?.history_collaps_container ?? false
    );
  });

  showArrowCollaps = computed(() => {
    return (
      this.functionSpec().layout_options?.collaps_options?.show_arrow_collaps ??
      false
    );
  });

  disabledEventCollaps = computed(() => {
    return (
      this.functionSpec().layout_options?.collaps_options
        ?.disabled_event_collaps ?? false
    );
  });

  showHistoryInsertBtn = computed(() => {
    return (
      this.functionSpec()?.layout_options?.show_history_insert_button ?? true
    );
  });

  showHistorySearchBar = computed(() => {
    return (
      this.functionSpec()?.layout_options?.show_history_search_bar ?? false
    );
  });

  showHistoryFilterForm = computed(() => {
    return this.layoutOptions()?.show_history_filter_form ?? false;
  });

  showTablePagination = computed(() => {
    return this.layoutOptions()?.show_table_pagination ?? true;
  });

  historyFilterConfig = computed(() => {
    if (!this.showHistoryFilterForm()) return {};
    return this.functionSpec()?.filter_config;
  });

  historyFilterMethod = computed(
    () => this.layoutOptions()?.filter_history_method ?? 'api',
  );

  layoutOptions = computed(() => this.functionSpec()?.layout_options);

  isUploadFile = computed(() => this.layoutOptions()?.is_upload_file ?? false);

  filteredActions(actions: RowActionsData[]): RowActionsData[] {
    return actions.filter((action) => !action.group);
  }

  transformedData(data: RowActionsData[]) {
    return data.reduce((acc, item) => {
      if (item.group) {
        let groupItem = acc.find((i: RowActionsData) => i.id === item.group);

        if (!groupItem) {
          groupItem = {
            ...item,
            id: item.group,
            title: item.group,
            icon: item.icon,
            children: [],
            type: item?.type,
          };
          acc.push(groupItem);
        }

        if (!groupItem.children) {
          groupItem.children = [];
        }

        groupItem.children.push(item);
      } else {
        acc.push(item);
      }
      return acc;
    }, [] as RowActionsData[]);
  }

  actionOne = computed(() => {
    const rowAction = this.layoutOptions()?.row_actions;
    // ?.filter((action) => {
    //   return this.checkPermission(action.id);
    // });
    return rowAction ? this.transformedData(rowAction) : rowAction;
  });

  actionExtend = computed(() => {
    const rowAction = this.layoutOptions()?.row_actions?.filter((action) => {
      return this.checkPermission(action.id);
    });
    return rowAction ? this.transformedData(rowAction) : rowAction;
  });

  actionsMany = computed(() => {
    return this.layoutOptions()?.actions_many;
  });

  showActionsDelete = computed(() => {
    return this.layoutOptions()?.show_actions_delete ?? true;
  });

  actionsManyHandler = computed(() => {
    return this.layoutOptions()?.actions_many_handler;
  });

  showActionsMany = computed(() => {
    return this.layoutOptions()?.show_actions_many ?? true;
  });

  showDialogSaveAddBtn = computed(() => {
    return this.layoutOptions()?.show_dialog_form_save_add_button || false;
  });

  showDialogSubmitBtn = computed(() => {
    return this.layoutOptions()?.show_dialog_submit_button ?? false;
  });

  showDeleteButton = computed(() => {
    return this.layoutOptions()?.show_dialog_form_delete_button || false;
  });

  disabledClickRow = computed(() => {
    return this.layoutOptions()?.disabled_click_row ?? false;
  });

  pageHeaderOptions = computed(() => {
    return this.layoutOptions()?.page_header_options;
  });
  pageFooterOptions = computed(() => {
    return this.layoutOptions()?.page_footer_options;
  });

  historyWidgetHeaderOptions = computed(() => {
    return this.layoutOptions()?.history_widget_header_options;
  });

  historyDialogOptions = computed(() => {
    return this.layoutOptions()?.history_dialog_options ?? {};
  });

  isGroupEdit = computed(() => {
    return this.layoutOptions()?.is_group_edit ?? false;
  });

  preCheckActionClickConfig = computed(() => {
    return this.layoutOptions()?.pre_check_action_click_config;
  });

  _editFooterButtonsCustom = signal<NzSafeAny[]>([]);

  editFooterButtonsCustom = computed(() => {
    return this.layoutOptions()?.edit_modal_footer_buttons ?? [];
  });

  footerButtonsCustom = computed(() => {
    return this._editFooterButtonsCustom().length > 0
      ? this._editFooterButtonsCustom()
      : (this.layoutOptions()?.modal_footer_buttons ?? []);
  });

  customStyleContent = computed(() => {
    return this.layoutOptions()?.customStyleContent ?? {};
  });

  layoutDetailFooterButtonsCustom = computed(() => {
    return (
      this.layoutOptions()?.layout_detail_modal_footer_buttons ?? []
    ).filter((button) => {
      return this.checkPermission(button.id);
    });
  });

  exportReportMethod = computed(() => {
    return this.layoutOptions()?.export_report_method ?? 'GET';
  });

  _actionsMany = computed(async () => {
    const definedActions = this.actionsMany() ?? [];
    if (isEmpty(definedActions)) return null;
    const expressionFunc = this.dynamicService.getJsonataExpression({});
    const transformData = { parentData: this.parent() };
    const visibleState = await Promise.all(
      definedActions?.map(async (action) => {
        if (!action.condition_func) return true;
        const res = await expressionFunc(action.condition_func, transformData);
        if (typeof res === 'boolean') return res;
        return false;
      }),
    );

    return definedActions.filter((_, idx) => visibleState[idx]);
  });

  actionOneCondition = signal<Record<string, Record<string, boolean>>>({});
  setActionOneCondition = effect(
    async () => {
      const actions = this.layoutOptions()?.row_actions;
      const data = (this.data() ?? []).map((item) => ({
        ...item,
        parentData: this.parent(),
        _selectedTab: this.selectedTab(),
      }));
      const conditions = await this.utilService.transformRowsActionsCondition(
        data,
        actions,
        this.recordCompositeKeyName(),
      );

      if (conditions) {
        const conditionsObj = this.utilService.buildConditionObject(conditions);
        this.actionOneCondition.set(
          conditionsObj as Record<string, Record<string, boolean>>,
        );
      }
    },
    { allowSignalWrites: true },
  );

  disabledDialogButtons = computed(() => {
    const selectedItem = this.selectedItem();
    const actionOneDisabled = this.actionOneDisabled();
    if (!selectedItem || !actionOneDisabled) return [];
    const disabledObj =
      actionOneDisabled?.[selectedItem[this.recordCompositeKeyName()]] ?? {};
    return Object.keys(disabledObj).filter((key) => disabledObj[key]);
  });

  disabledDetailDialogButtons = computed(() => {
    if (!this.layoutOptions()?.mapping_row_actions_state) return [];
    return this.disabledDialogButtons();
  });

  historyDialogActions = computed(() => {
    if (!this.layoutOptions()?.mapping_one_actions?.history) return null;
    return this.actionOne() ?? null;
  });

  actionOneDisabled = signal<Record<string, Record<string, boolean>>>({});
  setActionOneDisabled = effect(
    async () => {
      const actions = this.layoutOptions()?.row_actions;
      const data = (this.data() ?? []).map((item) => ({
        ...item,
        parentData: this.parent(),
      }));
      const disabled = await this.utilService.transformRowsActionsDisabled(
        data,
        actions,
        this.recordCompositeKeyName(),
      );

      if (disabled) {
        const disabledObj = this.utilService.buildDisabledObject(disabled);
        this.actionOneDisabled.set(
          disabledObj as Record<string, Record<string, boolean>>,
        );
      }
    },
    { allowSignalWrites: true },
  );

  readonly defaultFooterButtons: (ButtonSchema & { id: string })[] = [
    {
      id: 'cancel',
      type: 'secondary',
      size: 'default' as ButtonSchema['size'],
      state: 'default' as ButtonSchema['state'],
      title: 'Cancel',
    },
    {
      id: 'save',
      type: 'primary',
      size: 'default' as ButtonSchema['size'],
      state: 'default' as ButtonSchema['state'],
      title: 'Save',
    },
  ];
  pageFooterButtons = computed<(ButtonSchema & { id: string })[]>(() => {
    const buttons = this.functionSpec()?.layout_options?.footer_buttons;
    if (!buttons) return this.defaultFooterButtons;
    return buttons.map((btn) => ({
      id: btn.id,
      type: btn.type,
      size: 'default' as ButtonSchema['size'],
      state: 'default' as ButtonSchema['state'],
      title: btn.title,
      leftIcon: btn.icon,
      isLeftIcon: btn.icon ? true : false,
    }));
  });

  pageFooterButtonClicked(id: string) {
    const formValue = this.data();
    switch (id) {
      case 'cancel': {
        this.isChangeValueCell && this.refreshData.update((e) => !e);
        this.isChangeValueCell = false;
        break;
      }
      case 'save': {
        if (!this.isChangeValueCell) return;
        const url = this.url();
        if (url) {
          this.isLoading.set(true);
          this._service
            .updateList(url, formValue, this.getFaceCodeForService())
            .subscribe({
              next: () => {
                this.handleSuccessFooterButtonClicked();
              },
              error: (err) => {
                this.toast.showToast('error', '', err.error?.message);
              },
            });
        } else {
          this.handleSuccessFooterButtonClicked();
        }
        break;
      }
      case 'proceed': {
        this.onProceed();
        break;
      }
    }
  }

  handleSuccessFooterButtonClicked() {
    this.toast.showToast('success', '', 'Saved Successfully');
    this.refreshData.update((e) => !e);
    this.isChangeValueCell = false;
    this.isLoading.set(false);
    this.switchEditMode(false);
  }

  expandFilter = computed(() => {
    return this.layoutOptions()?.expand_filter;
  });

  preconditionFilter = computed(() => {
    return this.layoutOptions()?.precondition_filter;
  });

  expandCreateFormConfig = computed(
    () => this.layoutOptions()?.expand_create_form,
  );
  reloadExpandCreateForm = signal(false);

  isAutoFilter = computed(() => {
    return this.preconditionFilter()?.is_auto_filter ?? false;
  });

  border = computed(
    () => this.preconditionFilter()?.form_settings?.border ?? false,
  );

  padding = computed(
    () => this.preconditionFilter()?.form_settings?.padding ?? '',
  );

  isAutoSyncPreconditionFilter = computed(() => {
    return this.preconditionFilter()?.is_auto_sync_filter ?? true;
  });

  toolTable = computed(() => {
    return this.layoutOptions()?.tool_table;
  });

  showCreateDataButton = computed(() => {
    if (!this.checkPermission('create')) return false;
    return this.layoutOptions()?.show_create_data_table ?? true;
  });

  showTableSearch = computed(() => {
    return this.layoutOptions()?.show_table_search ?? true;
  });

  showTableCheckbox = computed(() => {
    return this.layoutOptions()?.show_table_checkbox ?? true;
  });

  viewAfterUpdated = computed(() => {
    return this.layoutOptions()?.view_after_updated ?? false;
  });

  viewAfterCreated = computed(() => {
    return this.layoutOptions()?.view_after_created ?? false;
  });

  viewHistoryAfterCreated = computed(() => {
    return this.layoutOptions()?.view_history_after_created ?? false;
  });

  viewHistoryAfterUpdated = computed(() => {
    return (
      this.layoutOptions()?.view_history_after_updated ??
      this.viewHistoryAfterCreated()
    );
  });

  downloadOptionsByItem = signal<NzSafeAny[]>([]);
  downloadOptions = computed(() => {
    return (
      this.downloadOptionsByItem() ??
      this.layoutOptions()?.export_dropdown ?? [
        {
          label: 'Excel',
          value: 0,
        },
        {
          label: 'CSV',
          value: 1,
        },
        {
          label: 'PDF',
          value: 2,
        },
      ]
    );
  });

  hideRowAction = computed(() => {
    return this.layoutOptions()?.hide_action_row ?? false;
  });

  actionOneHandler = computed(() => {
    return this.layoutOptions()?.row_actions_handler;
  });

  tabCalculationStatus = computed(() => {
    return this.layoutOptions()?.tab_calculation_status;
  });

  customRowIdForViewDetail = computed(() => {
    return this.layoutOptions()?.custom_rowId_for_view_detail;
  });

  async handleClickRow(row: Data) {
    if (this.disabledClickRow()) return;
    this.preCheckDetailAction(row);
    const rowId = this.customRowIdForViewDetail()
      ? (row[this.customRowIdForViewDetail() as string] ?? row.id)
      : row.id;

    if (this.showDetailHistory()) {
      const reAuthRes = await this.checkReAuthentication('history');
      if (!reAuthRes) return;
      this.showHistoryClickOne(row, rowId);
    } else {
      const reAuthRes = await this.checkReAuthentication('view');
      if (!reAuthRes) return;
      await this.viewClickOne(rowId, row);
    }
  }

  preCheckDetailAction(row: Data) {
    const facecode = this.faceCode();
    if (!facecode) return;
    const dataTypeKeys = this.functionSpec()?.permission_key ?? [];
    const key = _.map(dataTypeKeys, 'name');
    const keyMapping = _.keyBy(dataTypeKeys, 'name');

    // Replace keys in result with their corresponding defaultName
    const result = _.pick(row, key);
    const dataTypeValues = _.mapKeys(
      result,
      (value, key) => keyMapping[key]?.['defaultName'] || key,
    );
    this.getDetailActions(dataTypeValues, facecode);
  }

  actionDetailLst = signal<ActionPermission[]>([]);
  checkPermissionDetail = (action: string, accessType?: string) => {
    const list = overwritePermissionAction(
      cloneDeep(this.actionDetailLst()),
      accessType,
    );
    // use getDisabledPermission to get the list of disabled actions
    const disabledActions = getDisabledPermission(list);
    if (disabledActions.includes(action as ACTION_TYPE)) {
      return false;
    }
    return true;
  };

  getDetailActions(dataTypeValues: Record<string, string>, faceCode: string) {
    const filterQuery = _.map(dataTypeValues, (value, key) => ({
      field: key,
      operator: '$eq',
      value: value,
    })) as QueryFilter[];
    //TODO - fix the hard url later
    const url = `/api/actions-sys/detail-actions/${faceCode}`;
    of(url)
      .pipe(
        switchMap((url) => {
          return this._service.getItemCustom(
            url,
            filterQuery,
            this.getFaceCodeForService(),
          );
        }),
        catchError((err) => {
          this.toast.showToast('error', 'Error', err.error?.message);
          return of(undefined);
        }),
        tap((res) => {
          this.actionDetailLst.set(res);
        }),
      )
      .subscribe();
  }

  private getContextData(options: ContextDataOptions) {
    const contextData: Record<string, unknown> = {};
    if (options.parent_data) {
      contextData['parentData'] = this.parent();
    }
    if (options.selected_items) {
      contextData['selectedItems'] = this.listOfSelectedItems() ?? [];
    }
    return contextData;
  }

  _toolTable = computed(async () => {
    const btns = this.toolTable() ?? [];
    if (btns.length === 0) return btns;

    // only listen changes to what is needed by get the correct context, avoid unnecessary re-computations
    const contextData = this.getContextData(
      this.layoutOptions()?.tool_table_context_data_options ?? {
        parent_data: true,
      },
    );

    const expressionFunc = this.dynamicService.getJsonataExpression({});

    const transformedBtns = await Promise.all(
      btns?.map(async (btn) => {
        let display = true;
        let disabled = false;
        if (btn._condition) {
          display = await expressionFunc(btn._condition, contextData);
        }
        if (btn._disabled) {
          disabled = await expressionFunc(btn._disabled.transform, contextData);
        }
        return { ...btn, display, disabled };
      }),
    );

    return transformedBtns.filter((btn) => btn.display);
  });

  confirmClickOne(actionId: string, e: Event) {
    if (e) e.stopPropagation();

    const row_actions: RowActionsData[] =
      this.functionSpec()?.layout_options?.row_actions ?? [];
    const target = row_actions.filter((item) => item.id === actionId)[0];
    const modal = target?.modal;

    const {
      title = 'Title',
      subTitle = 'SubTitle',
      type = 'confirm',
      okText = 'Confirm',
      cancelText = 'Cancel',
      iconName = 'info',
    } = modal || {};

    this.modalComponent.showDialog({
      nzTitle: title,
      nzContent: subTitle,
      nzWrapClassName: `popup popup-${type}`,
      nzIconType: `icons:${iconName}`,
      nzOkText: okText,
      nzCancelText: cancelText,
      nzFooter: null,
      nzWidth: '400px',
      nzClosable: false,
      nzCentered: true,
    });
  }

  isActionVisible = false;
  isActionHireVisible = false;

  onDropdownClick(event: Event) {
    event.stopPropagation();
    this.isActionVisible = true;
  }

  onDropdownLeave(event: Event) {
    event.stopPropagation();
    this.isActionVisible = false;
  }

  showCalendar = signal(false);
  calendarConfig = signal<
    Pick<
      ScheduleDetails,
      | 'selectedDateRange'
      | 'apiConfig'
      | 'dataSource'
      | 'extendValue'
      | 'calendarSettings'
    >
  >({});
  onViewCalendar(items?: Data[]) {
    this.showCalendar.set(true);
    const filterValue = this.filterValue();
    const { api, calendarSettings } =
      this.layoutOptions()?.schedule_config ?? {};
    this.calendarConfig.set({
      extendValue: {
        selectedItems: items,
        filterValue: this.filterValue(),
      },
      selectedDateRange: [filterValue?.startDate, filterValue?.endDate],
      dataSource: [],
      apiConfig: api,
      calendarSettings: calendarSettings,
    });
  }

  preCheckActionClick = async (id: string, data: Data) => {
    const precheckConfig = this.preCheckActionClickConfig();
    if (!precheckConfig) return true;

    if (precheckConfig.api) {
      const url = await this.getApiValueByKey(precheckConfig.api, 'url', data);
      if (!url) return true;
      const method = (precheckConfig.api.method ?? 'GET').toUpperCase();
      let observable: Observable<any> | null | undefined = null;
      if (method === 'GET') {
        observable = this._service.Get(url, this.getFaceCodeForService());
      } else {
        observable = this.getServiceMethod(method as TApiMethod)?.(url, data);
      }
      if (!observable) return true;

      this.#layoutStore.setLoading(true);
      const apiRes = await firstValueFrom(
        observable.pipe(
          catchError((err) => {
            // console.error('failed to call api precheck action click,', err);
            return of(err.error);
          }),
        ),
      );

      this.#layoutStore.setLoading(false);
      return this.checkByExpression(precheckConfig.expression, {
        apiResponse: apiRes,
        actionId: id,
        rowData: data,
      });
    }

    return this.checkByExpression(precheckConfig.expression, {
      actionId: id,
      rowData: data,
    });
  };

  async onActionOneClick(row: Data, action: NzSafeAny, event: Event) {
    if (event) event.stopPropagation();
    const { id } = action;

    const reAuthRes = await this.checkReAuthentication(id, row);
    if (!reAuthRes) return;

    const preCheckRes = await this.preCheckActionClick(id, row);
    if (!preCheckRes) return;
    switch (id) {
      case 'delete': {
        this.deleteClickOne(row.id, row, event);
        break;
      }
      case 'edit': {
        this.editClickOne(row.id, row, event);
        break;
      }
      case 'edit-custom':
        this.editClickOne(row.id, row, event, true);
        break;
      case 'lock': {
        this.lockClickOne(row, true, event);
        break;
      }
      case 'unlock': {
        this.lockClickOne(row, false, event);
        break;
      }
      case 'view': {
        this.viewClickOne(row.id, row, event);
        break;
      }
      case 'generate-report': {
        this.playClickOne(row.id, row, event);
        break;
      }
      case 'sub-detail': {
        this.subDetailClickOne(row.id, row, event);
        break;
      }
      case 'new-schedule': {
        this.onNewSchedule(row);
        break;
      }
      case 'confirm': {
        this.confirmClickOne(id, event);
        break;
      }
      case 'duplicate': {
        this.duplicateClick(structuredClone(row));
        break;
      }
      case 'cancel-job-requests': {
        this.cancelJobRequestsClickOne(row.id, event);
        break;
      }
      // rehire case
      case 'rehire': {
        // if action has href then update data and then navigate to href
        if (action.href) {
          const item: NzSafeAny = row;
          if (item.blacklistBlocklist) {
            this.handleCheckBlackBlockList(item, id, action.href);
          } else {
            this.layoutDataService.updateData({
              rehire: row,
            });
            this.router.navigate([action.href]);
          }
        }
        break;
      }
      case 'addJob': {
        const item: NzSafeAny = row;
        const url = action?.href + item?.employeeId || '';
        const params = {
          tab: 'HR.FS.FR.009',
        };
        if (item.blacklistBlocklist) {
          this.handleCheckBlackBlockList(item, id, url, params);
        } else {
          this.router.navigate([url], {
            queryParams: params,
            queryParamsHandling: 'merge',
          });
          return;
        }
        break;
      }
      case 'hire': {
        const item: NzSafeAny = row;
        if (item.blacklistBlocklist) {
          this.handleCheckBlackBlockList(item, id);
        } else {
          this.openHireForm(item);
        }
        break;
      }
      case 'run-schedule': {
        this.runSchedule(row);
        break;
      }
      case 'cancel-schedule': {
        this.cancelSchedule(row);
        break;
      }
      case 'view-schedule': {
        this.viewSchedule(row);
        break;
      }
      case 'download-schedule': {
        this.downloadSchedule(row);
        break;
      }
      case 'add-category': {
        this.handleAssignReportCategories(row);
        break;
      }
      case 'calendar': {
        this.onViewCalendar([row]);
        break;
      }
      default: {
        this.handleActionOneClick(id, row);
        break;
      }
    }
  }

  onDialogVisibleChange(visible: boolean) {
    this.dialogVisible.set(visible);
    this.authAction.set(undefined);
    this.resetConfig();
    if (!visible) {
      this.actionOneSelected.set(null);
    }
  }

  actionOneSelected = signal<RowActionsHandler[keyof RowActionsHandler] | null>(
    null,
  );
  handleActionOneClick(actionId: string, data: Data) {
    const action = this.actionOneHandler()?.[actionId];
    if (!action) return;
    this.actionOneSelected.set(action);
    this.dialogValue.set(data);
    this.selectedItem.set(data);
    this.dialogVisible.set(true);
    this.dialogConfig.set(action.form?.config);
    this.dialogType.set('edit');
  }

  private getServiceMethod(method?: TApiMethod): ServiceFunction | null {
    switch (method) {
      case 'POST': {
        return this._service.Post;
      }
      case 'PUT': {
        return this._service.Put;
      }
      case 'PATCH': {
        return this._service.Patch;
      }
      default: {
        return null;
      }
    }
  }

  async handleSubmitActionOne(
    data: NzSafeAny,
    callback?: (status: boolean) => void,
  ) {
    const action = this.actionOneSelected();
    if (!action) return;
    let url = action.backendUrl;
    if (action.apiConfig) {
      url = await this.getCustomUrl(action.apiConfig, data);
    }
    if (!url) return;
    const serviceMethod = this.getServiceMethod(action.method);
    if (!serviceMethod) return;

    serviceMethod(url, data).subscribe({
      next: () => {
        callback?.(true);
        this.actionOneSelected.set(null);
        this.toast.showToast('success', '', 'Record saved');
        this.refreshData.update((e) => !e);
      },
      error: (err) => {
        callback?.(false);
        this.toast.showToast('error', 'Error', err.error?.message);
      },
    });
  }

  handleCheckBlackBlockList(
    item: NzSafeAny,
    actionId: string,
    url?: string,
    paramsUrl?: NzSafeAny,
  ) {
    if (item.blacklistBlocklist === 'BlackList') {
      this.modalComponent.showDialog({
        nzTitle: 'Warning',
        nzContent: 'Blacklist employees are not encouraged to be recruited.',
        nzWrapClassName: 'popup popup-confirm',
        nzIconType: 'icons:warning',
        nzOkText: 'Confirm',
        nzCancelText: 'Cancel',
        nzWidth: '400px',
        nzClosable: false,
        nzCentered: true,
        nzOnOk: () => {
          if (actionId === 'hire') {
            this.openHireForm(item);
            return;
          }
          actionId === 'rehire' &&
            this.layoutDataService.updateData({
              rehire: item,
            });
          this.router.navigate([url], {
            queryParams: paramsUrl,
            queryParamsHandling: 'merge',
          });
        },
      });
    }
    if (item.blacklistBlocklist === 'BlockList') {
      this.modalComponent.showDialog({
        nzTitle: 'Warning',
        nzContent:
          'Blocklist employees are not recruited according to regulations.',
        nzWrapClassName: 'popup popup-confirm',
        nzIconType: 'icons:warning',
        nzOkText: 'Confirm',
        nzCancelText: 'Cancel',
        nzWidth: '400px',
        nzClosable: false,
        nzCentered: true,
      });
    }
  }

  hireFormCustomConfig = signal<FormConfig | NzSafeAny>({});
  openHireForm(item: NzSafeAny) {
    this.hireDialogValue.set(item);
    this.hireFormCustomConfig.set(this.functionSpec()?.create_form ?? {});
    this.isActionHireVisible = true;
    this.layoutDataService.updateData({
      hire: item,
    });
  }

  http = inject(HttpClient);
  customDialogTitle = signal<string>('');
  newScheduleFormConfig = signal<NzSafeAny>({});

  // report title
  reportTitle = signal<string>('');
  reportSubtitle = signal<string>('');
  reportCompanyName = signal<string>('');

  authAction = signal<string | undefined>(undefined);
  playClickOne(id: string, value: NzSafeAny, e?: Event) {
    if (e) e.stopPropagation();
    this.selectedItem.set(value);
    this.#store.setDetailId(value?.id || '');
    this.#store.setDataDetail(value);

    this.dialogType.set('create');
    this.customDialogTitle.set(value.code + ' - ' + value.name);

    //set auth action
    this.authAction.set(AuthActions.GenerateReport);
    // set report title
    this.reportTitle.set(value.name);

    const filterGenerateForm = value.reportFilter
      ? this.parseStringToJson(value.reportFilter)
      : {};
    const reportHeaderConfiguration = value.reportHeaderConfiguration
      ? this.parseStringToJson(value.reportHeaderConfiguration)
      : {};
    const downloadListConfig = value.downloadOptionConfiguration
      ? this.parseStringToJson(value.downloadOptionConfiguration)
      : undefined;
    this.downloadOptionsByItem.set(
      downloadListConfig?.options ?? downloadListConfig ?? undefined,
    );

    // Set header from reportHeaderConfiguration

    this.previewTableCol.set(reportHeaderConfiguration.header ?? []);

    // Set transform and header configs
    if (reportHeaderConfiguration.dynamicColumnsConfig) {
      this.previewTableTransformConfig.set(
        reportHeaderConfiguration.dynamicColumnsConfig,
      );
      this.previewTableDynamicHeaderConfig.set(
        reportHeaderConfiguration.dynamicColumnsConfig,
      );
    } else {
      // reset config
      this.previewTableTransformConfig.set(null);
      this.previewTableDynamicHeaderConfig.set(null);
    }

    this.dialogConfig.set(filterGenerateForm ?? {});
    this.dialogValue.set(value);
    this.dialogVisible.set(true);
    return;
  }

  cancelJobRequestsClickOne(id: string, e?: Event) {
    if (e) e.stopPropagation();

    this.modalComponent.showDialog({
      nzTitle: 'Cancel Job Request',
      nzContent:
        'Are you sure you want to cancel this job? This execution of the job will be canceled and its status will be changed to Interrupted',
      nzWrapClassName: `popup popup-confirm`,
      nzIconType: `icons:warning`,
      nzOkText: 'Confirm',
      nzCancelText: 'Cancel',
      nzWidth: '400px',
      nzClosable: false,
      nzCentered: true,
      nzFooter: null,
      nzOnOk: () => {
        const url = `${this.url()}/cancel-job-requests`;
        this._service
          .Post(url, {
            id: id ?? id,
          })
          .subscribe({
            next: () => {
              this.toast.showToast('success', '', 'Record cancelled');
              this.refreshData.update((e) => !e);
              return;
            },
            error: (err) => {
              this.toast.showToast('error', 'Error', err.error?.message);
            },
          });
      },
    });
  }

  parseStringToJson(value: string) {
    return attempt(JSON.parse, value);
  }

  parseJsonToString(value: NzSafeAny) {
    return JSON.stringify(value).replace(/"/g, '\\"');
  }

  handleDownload(value: number) {
    const item: NzSafeAny = this.selectedItem();
    const backendUrl = item?.['urlExport'] ?? null;
    const formValue = this.formValueGenerateReport();

    const isMethodPOST = this.dialogConfig()?.settingPreview?.method === 'POST';

    this.downloadReport(backendUrl, formValue, value, isMethodPOST);
  }

  fileName = signal<string>('');
  getQueryParams(reportType?: number, fileName?: string) {
    const qs = trimEnd(this.queryString, '&');
    const userName = this.user()?.userName;
    const exportFileName = isNil(this.tranformFilename())
      ? `${fileName}_${userName}_${new Date().getTime()}`
      : this.tranformFilename();
    this.fileName.set(exportFileName ?? '');
    const defaultQueryString = `reportType=${reportType}&FileNameExport=${exportFileName}`;
    return qs.length > 0 ? `${qs}&${defaultQueryString}` : defaultQueryString;
  }

  buildBodyExportReport(reportType?: number, fileName?: string) {
    const userName = this.user()?.userName;
    const exportFileName = isNil(this.tranformFilename())
      ? `${fileName}_${userName}_${new Date().getTime()}`
      : this.tranformFilename();
    this.fileName.set(exportFileName ?? '');
    const { Page, PageSize, ...resp } = this.queryObject;
    const queryObject = {
      ...resp,
      reportType: reportType,
      FileNameExport: exportFileName,
    };

    return queryObject;
  }

  #userStore = inject(UserStore);
  user = computed(() => this.#userStore.user());

  downloadReport(
    backendUrl: string,
    formValue: NzSafeAny,
    type: number,
    isMethodPOST?: boolean,
  ) {
    if (backendUrl) {
      const obverableService = isMethodPOST
        ? this._service.exportReportForPOST(
            backendUrl,
            this.buildBodyExportReport(type, this.customDialogTitle()),
            this.fileName(),
            type,
          )
        : this._service.exportReport(
            backendUrl,
            this.buildFilterQuery([], formValue),
            this.getQueryParams(type, this.customDialogTitle()),
            this.fileName(),
            this.exportReportMethod(),
            type,
          );
      this.isLoading.set(true);
      obverableService
        .pipe(
          catchError(async (err) => {
            let errorMessage = err?.message;
            if (err?.error instanceof Blob) {
              const jsonData = JSON.parse(await err.error.text());
              errorMessage = jsonData?.message ?? jsonData?.title;
            }

            // call direct to API not through bff.
            this.toast.showToast('error', 'Error', errorMessage);
            this.isLoading.set(false);
            return of(null);
          }),
          tap(() => {
            this.isLoading.set(false);
          }),
        )
        .subscribe();
    }
  }

  onCancel() {
    this.isPreview = false;
  }

  handleReturnReport() {
    this.onCancel();
    this.closeDialog();
  }

  onNavigateEmitHistory(
    e: string,
    item?: NzSafeAny,
    overrideValue?: NzSafeAny,
    cb?: (status: boolean) => void,
  ) {
    switch (e) {
      case 'create':
        if (item)
          this.createClick(this.isCopyDataInsertNew() ? item : undefined);
        break;
      case 'duplicate':
        if (item) this.duplicateClick(item, overrideValue);
        break;
      case 'edit':
        this.editClickOne(item.id, item);
        break;
      case 'delete':
        this.deleteClickOne(item.id, item, null, cb, true);
        break;
      case 'proceed': {
        this.onProceed(item);
        break;
      }
      case 'insertNew': {
        this.onProceedCustom(item);
        break;
      }
      case 'lock': {
        this.lockClickOne(item, true, undefined, cb);
        break;
      }
      case 'unlock': {
        this.lockClickOne(item, false, undefined, cb);
        break;
      }
    }
  }

  // for modal dialog actions many
  modalDialogVisible = signal(false);
  modalDialogValue = signal<NzSafeAny>({});
  modalDialogConfig = signal<FormConfig | NzSafeAny>({});
  modalDialogType = signal('edit');
  modalDialogTitle = signal('');
  modalDialogSize = signal<'small' | 'middle' | 'large' | 'xsmall'>('middle');
  actionConfigMapping = signal<Record<string, FormConfig>>({});

  private navigateByHref(
    href: string,
    routeData: Record<string, NzSafeAny> = {},
  ) {
    const [commands, queryParamsString] = href.split('?');
    let queryParams = {};
    if (queryParamsString) {
      queryParams =
        this.utilService.convertStringToQueryParams(queryParamsString);
    }
    this.router.navigate([commands], {
      queryParams: queryParams,
      state: routeData,
    });
  }

  async onActionsManyClick(actionId: string) {
    const reAuthRes = await this.checkReAuthentication(actionId);
    if (!reAuthRes) return;
    const action = this.actionsMany()?.find((action) => action.id === actionId);
    const actionHref = action?.href;
    if (actionHref) {
      this.navigateByHref(actionHref, {
        selectedItems: structuredClone(this.listOfSelectedItems()),
      });
      return;
    }
    switch (actionId) {
      case 'delete': {
        this.deleteClickMany();
        break;
      }
      case 'export': {
        await this.handleExport(this.listOfSelectedItems());
        break;
      }
      case 'lock': {
        this.actionClickMany(actionId);
        break;
      }
      case 'unlock': {
        this.actionClickMany(actionId);
        break;
      }
      case 'addCategory': {
        this.handleAssignReportCategories();
        break;
      }
      case 'calendar': {
        this.onViewCalendar(this.listOfSelectedItems());
        break;
      }
      default: {
        this.handleActionsMany(actionId);
        break;
      }
    }
  }

  actionManyHandlerSelected = signal<ActionManyHandler | null>(null);
  handleActionsMany(actionId: string) {
    const actionsHandler = this.actionsManyHandler();
    const actionHandler = actionsHandler?.[actionId];
    if (!actionHandler) return;
    this.actionManyHandlerSelected.set(actionHandler);
    const { form_config, pick_field } = actionHandler;
    const {
      fields: defaultFormFields,
      sources,
      variables,
    } = this.functionSpec().form_config;

    switch (actionHandler?.action) {
      case 'edit': {
        const actionFormConfig = this.actionConfigMapping()?.[actionId];
        if (actionFormConfig) {
          this.modalDialogConfig.set(actionFormConfig);
        } else if (form_config) {
          this.modalDialogConfig.set(form_config);
        } else if (pick_field) {
          const formFields = this.utilService.mappingPickFieldFromFormFields(
            pick_field,
            defaultFormFields,
          );
          this.modalDialogConfig.set({
            fields: formFields,
            sources,
            variables,
          });
        } else {
          const confirm = actionHandler.confirm;
          if (!confirm) return;
          const { content, title } = confirm;
          this.modalComponent.showDialog(
            {
              nzContent: content,
              nzTitle: title,
              nzWrapClassName: 'popup popup-confirm',
              nzIconType: 'icons:warning',
              nzOkText: 'Confirm',
              nzCancelText: 'Cancel',
              nzClosable: false,
              nzCentered: true,
              nzOnOk: () => {
                this.actionsManySubmit({ value: null });
              },
            },
            'warning',
          );
          return;
        }

        if (!this.actionConfigMapping()?.[actionId]) {
          this.actionConfigMapping.update((val) => ({
            ...val,
            [actionId]: this.modalDialogConfig(),
          }));
        }

        this.modalDialogTitle.set(actionHandler.title ?? '');
        this.modalDialogVisible.set(true);
        this.modalDialogType.set('edit');
        break;
      }
    }
  }

  async actionsManySubmit(e: {
    value: NzSafeAny;
    callback?: (success: boolean) => void;
  }) {
    const handler = this.actionManyHandlerSelected();
    const { value, callback } = e;
    const { api_config: apiConfig } = handler ?? {};
    if (!apiConfig) {
      callback?.(false);
      return;
    }

    const contextValue = {
      formValue: value,
      selectedItems: this.listOfSelectedItems(),
    };

    const url = await this.getCustomUrl(apiConfig, contextValue);
    if (!url) {
      callback?.(false);
      return;
    }

    let method = await this.getApiValueByKey(apiConfig, 'method', contextValue);

    method = ((method ?? 'POST') as string).toUpperCase();
    const fn = this.getServiceMethod(method as TApiMethod);
    if (!fn) {
      callback?.(false);
      return;
    }

    const body = await this.getApiValueByKey(apiConfig, 'body', contextValue);
    of(url)
      .pipe(
        switchMap(() =>
          fn(
            url,
            body ?? {
              ids: (this.listOfSelectedItems() ?? []).map((item) => item.id),
            },
          ),
        ),
      )
      .subscribe({
        next: () => {
          callback?.(true);
          this.toast.showToast(
            'success',
            '',
            apiConfig.messages?.success ?? 'Record saved',
          );
          this.refreshData.update((e) => !e);
        },
        error: (err) => {
          callback?.(false);
          this.toast.showToast(
            'error',
            'Error',
            apiConfig.messages?.error ?? err.error?.message,
          );
        },
      });
  }

  async modalDialogSubmit(item: {
    type: string;
    value: NzSafeAny;
    callback?: (success: boolean) => void;
  }) {
    const precheckActionRes = await this.precheckAction(item.type, item.value);
    if (!precheckActionRes) {
      item.callback?.(false);
      return;
    }
    switch (item?.type) {
      case 'synthesize': {
        const url = await this.buildCustomUrl(
          structuredClone(this.dialogConfig()?.integrateBackendUrl),
          this.parent() ?? {},
        );
        if (url) {
          this.loading.set(true);
          this._service
            .createItem(url, item?.value, this.getFaceCodeForService(), {
              authAction: AuthActions.Calculation,
            })
            .pipe(
              catchError((err) => {
                item.callback?.(false);
                this.loading.set(false);
                this.toast.showToast('error', 'Error', err.error?.message);
                // return of(undefined);
                throw err; // Rethrow error to stop subsequent operations
              }),
              tap(() => {
                item.callback?.(true);
                this.loading.set(false);
                this.refreshData.update((e) => !e);
              }),
              tap(() => {
                //handling progressing.
                this.modalDialogVisible.set(false);
              }),
            )
            .subscribe();
        }
        break;
      }
      default: {
        this.actionsManySubmit(item);
      }
    }
  }

  showConfirm(
    title?: string,
    content?: string,
    url?: string,
    body?: NzSafeAny,
    additional?: Record<string, NzSafeAny>,
  ) {
    this.modalComponent.showDialog(
      {
        nzContent: content,
        nzTitle: title,
        nzWrapClassName: 'popup popup-confirm',
        nzIconType: 'icons:warning',
        nzOkText: 'Confirm',
        nzCancelText: 'Cancel',
        nzWidth: '400px',
        nzClosable: false,
        nzCentered: true,
        nzOnOk: () => {
          // handle add new revision
          if (url) {
            this._service
              .createItem(url, body)
              .pipe(
                tap(() => {
                  this.loading.set(true);
                }),
                catchError((err) => {
                  this.loading.set(false);
                  this.toast.showToast('error', 'Error', err.error?.message);
                  return of(undefined);
                }),
                tap(() => {
                  this.loading.set(false);
                  this.reloadPage(additional);
                }),
              )
              .subscribe();
          }
        },
      },
      'warning',
    );
  }

  reloadPage(additional: NzSafeAny = undefined) {
    let queryString = '';
    const currentPath = this.location.path();
    if (additional) {
      //view detail after reload.
      const { value, filteredApi, sortOrder } = additional;
      queryString = `?dialogType=view-result&code=${value.code}&filteredApi=${filteredApi}&sortOrder=${sortOrder}`;
    }
    const parentPath = this.removeLastSegment(currentPath);
    this.location.go(parentPath + queryString);
    window.location.reload();

    // this.router.navigate([parentPath]);
  }

  removeLastSegment(url: string) {
    const segments = _.split(url, '/');
    segments.pop();
    return _.join(segments, '/');
  }

  preconditionChecked = signal(false);
  filterSubmit(value: { type: string; value: NzSafeAny }) {
    if (!this.preconditionChecked()) {
      this.preconditionChecked.set(true);
    }
    this.filterValue.set(value.value);
  }

  allowRefreshData = computed(() => {
    if (!this.expandFilter()) return true;
    return this.preconditionChecked();
  });

  showTableSection = computed(() => this.allowRefreshData());

  preconditionFilterValue = signal<Record<string, NzSafeAny>>({});

  preconditionFilterConfig = computed(() => {
    if (!this.preconditionFilter()) return null;
    const fs: FunctionSpec = this.functionSpec();
    const preconditionConfig = fs?.pre_condition_config;
    if (!isEmpty(preconditionConfig) && !isNil(preconditionConfig)) {
      return preconditionConfig;
    }

    if (this.isAutoFilter()) return fs?.filter_config;

    return fs.create_form;
  });

  resetConfig() {
    this._editFooterButtonsCustom.set([]);
  }

  private isStandardIcon(icon: string) {
    const standardIconPrefix = 'icon';
    const standardIconSuffix = 'bold';
    return (
      icon.startsWith(standardIconPrefix) && icon.endsWith(standardIconSuffix)
    );
  }

  actionOneIcon = computed(() => {
    const actions = this.actionOne();
    return actions?.reduce(
      (acc, action) => {
        acc[action.id] = this.getToolIcon(action);
        return acc;
      },
      {} as Record<string, string>,
    );
  });

  getToolIcon(tool: LayoutButton) {
    const icon = tool?.icon;
    if (icon && this.isStandardIcon(icon)) {
      return icon;
    }

    switch (tool.id) {
      case 'import':
        return 'icon-download-simple-bold';
      case 'export':
        return 'icon-upload-simple-bold';
      case 'edit':
        return 'icon-pencil-simple-bold';
      case 'delete':
        return 'icon-trash-bold';
      case 'view':
        return tool.icon === 'icon-eye' ? 'icon-eye-bold' : tool.icon;
      default:
        return tool.icon;
    }
  }

  // for tool table
  async onToolTableClick(tool: LayoutButton) {
    const toolId = tool.id;
    const reAuthRes = await this.checkReAuthentication(toolId);
    if (!reAuthRes) return;
    switch (toolId) {
      case 'lock': {
        this.isDynamicConfigTable()
          ? this.lockClickMany(tool.backendUrl ?? '', true)
          : this.lockunlockClickMany(tool);
        break;
      }
      case 'unlock': {
        this.isDynamicConfigTable()
          ? this.lockClickMany(tool.backendUrl ?? '', false)
          : this.lockunlockClickMany(tool);
        break;
      }
      case 'synthesize': {
        this.synthesizeClickMany(tool);
        break;
      }

      case 'run': {
        // this.openRunPopup();
        break;
      }
      case 'folder': {
        this.onOpenCategoryDialog();
        break;
      }
      case 'calculate': {
        this.openProgressPopup();
        break;
      }
      case 'create': {
        this.createClick();
        break;
      }
      case 'export': {
        await this.handleExport();
        break;
      }
      case 'import': {
        // this.showImportDialog();
        this.router.navigate([tool.href ?? this.link_get_template_redirect()]);
        this.layoutDataService.updateData({
          cm_002: tool?.paramsRedirect,
        });
        break;
      }
      case 'calendar': {
        this.onViewCalendar(this.listOfSelectedItems());
        break;
      }
      default: {
        const btn = this.toolTable()?.find((btn) => btn.id === toolId);
        if (btn?.href) {
          const params = btn?.paramsRedirect;
          this.router.navigate([btn.href], {
            queryParams: params,
            queryParamsHandling: 'merge',
          });
        }
        break;
      }
    }
  }

  // TODO: should remove and config on tool
  progressPopupVisible = signal(false);
  openProgressPopup() {
    this.progressPopupVisible.set(true);
  }
  runPopupVisible = signal(false);
  runPopupConfig = signal<NzSafeAny>({
    fields: [
      {
        type: 'radio',
        label: 'Are you sure want to [Run/Note/Lock/Unlock]?',
        name: 'runType',
        value: '[Run/Note/Lock/Unlock] selected items',
        direction: 'column',
        radio: [
          {
            value: '[Run/Note/Lock/Unlock] selected items',
            label: '[Run/Note/Lock/Unlock] selected items',
          },
          {
            value: '[Run/Note/Lock/Unlock] all sheets',
            label: '[Run/Note/Lock/Unlock] all sheets',
          },
        ],
      },
    ],
  });
  openRunPopup() {
    this.runPopupVisible.set(true);
  }

  notePopupVisible = signal(false);
  notePopupConfig = signal<FormConfig>({
    fields: [
      {
        type: 'dateRange',
        label: 'Payment Date',
        name: 'paymentDate',
        mode: 'date-picker',
        setting: {
          format: 'dd/MM/yyyy',
          // _disabledDate: {
          //   transform:
          //     "[{'operator': '$ne', 'value': $DateFormat($.extend.defaultValue.paymentDate, 'DD/MM/yyyy')}, {'operator': '$lt', 'value': 'today'}]",
          // },
        },
        // validators: [
        //   {
        //     type: 'ppx-custom',
        //     args: {
        //       transform:
        //         "$not($isNilorEmpty($.fields.paymentDate)) and $DateDiff($DateFormat($.fields.paymentDate, 'yyyy-MM-DD'), $DateFormat($now(), 'yyyy-MM-DD'), 'd') < 0 and ($not($isNilorEmpty($.extend.defaultValue.paymentDate)) ? $DateDiff($DateFormat($.fields.paymentDate, 'yyyy-MM-DD'), $DateFormat($.extend.defaultValue.paymentDate, 'yyyy-MM-DD'), 'd') != 0 : true)",
        //     },
        //     text: 'Payment date must be greater than or equal to current date',
        //   },
        // ],
      },
      {
        type: 'textarea',
        label: 'Note',
        name: 'note',
        placeholder: 'Enter Note',
        textarea: {
          autoSize: {
            minRows: 3,
          },
          maxCharCount: 1000,
        },
        validators: [
          {
            type: 'maxLength',
            args: '1000',
            text: 'Maximum length is 1000 characters.',
          },
        ],
      },
    ],
  });

  progressPercentDialog = 0;

  viewResult(code: string, _url?: string, sortOrder?: NzSafeAny) {
    // const filter = [
    //   {
    //     field: 'code',
    //     operator: '$eq',
    //     value: code,
    //   },
    // ] as QueryFilter[];
    // this.refreshData.update((e) => !e);
    // this.progressPopupVisible.set(false);
    // // this.viewClickOne(this.data()[0].id, this.data()[0]);
    // const url = this.customPath() ? this._customUrl() : this.url();
    // if (url || _url) {
    //   this._service
    //     .getPaginate(
    //       _url ?? url ?? '',
    //       this.pageIndex(),
    //       this.pageSize(),
    //       filter,
    //       '',
    //       sortOrder ?? this.sortOrder(),
    //     )
    //     .subscribe((d) => {
    //       //TODO - refactor later
    //       this.viewClickOne(d.data[0].id, d.data[0]);
    //     });
    // }
    this.router.navigate([this.router.url, code]);
  }

  ngOnDestroy(): void {
    if (this.progressingSubscription) {
      this.progressingSubscription.unsubscribe();
    }

    if (this.layoutDataSubscription) {
      this.layoutDataSubscription.unsubscribe();
    }

    if (this.subscriptionLayoutEvent) {
      this.subscriptionLayoutEvent.unsubscribe();
    }

    this.destroy$.next(); // Emit to signal teardown
    this.destroy$.complete(); // Complete the destroy observable
  }

  categoryDialogVisible = signal(false);

  private viewContainerRef = inject(ViewContainerRef);

  categoryDialogConfig = signal<NzSafeAny>({});

  // open up the dialog table
  onOpenCategoryDialog() {
    // get the config from layout_options
    const layoutOptions = this.functionSpec()?.layout_options as NzSafeAny;
    const tableConfig = layoutOptions?.dialogTable;
    this.categoryDialogConfig.set(tableConfig);

    if (tableConfig) {
      // open up the dialog table
      const modalRef = this.modalService.create({
        nzContent: DialogTableComponent,
        nzMaskClosable: false,
        nzWidth: '600px',
        nzTitle: this.modalCategoriesTitle,
        nzCloseIcon: this.modalCategoriesCloseIcon,
        nzWrapClassName: `category-dialog`,
        nzData: {
          config: {
            columns: tableConfig.columns,
            data: [],
            url: tableConfig.api_url,
            showCheckbox: tableConfig.show_table_checkbox,
            showPagination: tableConfig.show_pagination,
            actions: tableConfig.actions,
            form: tableConfig.form,
          } as DialogTableConfig,
        },
        nzFooter: null,
        nzCentered: true,
        nzOnCancel: () => {
          const component = modalRef.getContentComponent();
          if (component) {
            component.onCancel();
          }
          return false;
        },
        nzViewContainerRef: this.viewContainerRef,
      });

      const modalInstance = modalRef.getContentComponent();
      modalInstance.setConfig(modalRef.getConfig().nzData.config);

      modalRef.afterClose.subscribe((e: NzSafeAny) => {
        if (e?.isSubmit) {
          this.refreshData.update((e) => !e);
        }
      });
    }
  }

  async clickedModalButton(item: NzSafeAny) {
    const reAuthRes = await this.checkReAuthentication(item.id);
    if (!reAuthRes) return;
    switch (item.id) {
      case 'next':
        // if dialogType is edit, then update the item
        this.nextStep(
          item.value,
          item.dialogType === 'edit' ? 'edit-schedule' : 'new-schedule',
        );
        break;
      case 'save-draft':
        this.toast.showToast('success', '', 'Saved draft successfully');
        break;
      case 'generateReport':
        this.onGenerateReport(
          item,
          this.selectedItem()?.['url'],
          // '/api/labor-contract-due-date-without-salary-report',
        );
        break;
      case 'edit':
        this.editClickOne(item?.value?.id, item?.value);
        break;
      case 'edit-custom':
        this.editClickOne(item?.value?.id, item?.value, undefined, true);
        break;
      case 'deactive':
        this.handleChangeStatusClickOne(item, false);
        break;
      case 'active':
        this.handleChangeStatusClickOne(item, true);
        break;
      case 'lock': {
        this.listOfSelectedItems.set([item?.value]);
        this.lockClickOne(item.value, true);
        break;
      }
      case 'unlock': {
        this.listOfSelectedItems.set([item?.value]);
        this.lockClickOne(item.value, false);
        break;
      }
      case 'delete': {
        this.deleteClickOne(item.value.id, item.value);
        break;
      }
      default:
        this.handleActionOneClick(item.id, item.value);
        break;
    }
  }

  handleChangeStatusClickOne(item: NzSafeAny, status: boolean) {
    const url = this.url();
    if (url) {
      const serviceMethod$ = status
        ? this._service.updateItem(
            url,
            item?.value?.id,
            {
              status,
            },
            undefined,
            this.getFaceCodeForService(),
          )
        : this._service.deactiveItem(
            url,
            item?.value?.id,
            undefined,
            undefined,
            this.getFaceCodeForService(),
          );
      this.#layoutStore.setLoading(true);
      serviceMethod$.subscribe({
        next: () => {
          item.callback?.(true);
          this.toast.showToast('success', 'Success', 'Saved Successfully');
          // after create or edit success, view detail item
          this.#layoutStore.setLoading(false);
          this.refreshData.update((e) => !e);
        },
        error: (err) => {
          item.callback?.(false);
          this.#layoutStore.setLoading(false);
          this.toast.showToast('error', 'Error', err.error?.message);
        },
      });
    } else {
      this.toast.showToast('success', 'Success', 'Saved Successfully');
    }
  }

  // generate report company name
  generateReportCompanyName(
    reportParam: NzSafeAny,
    config?: NzSafeAny,
  ): string {
    let companyName = '';

    if (config?.settingPreview) {
      const previewCompany = config.settingPreview.previewCompany;
      if (reportParam[previewCompany]?.label) {
        companyName = reportParam[previewCompany]?.label;
      }
    }

    if (reportParam.Company?.label) {
      companyName = reportParam.Company.label;
    } else if (reportParam.CompanyCode?.label) {
      companyName = reportParam.CompanyCode.label;
    }

    // Remove content inside parentheses, including the parentheses
    return companyName.replace(/\s*\([^)]*\)\s*$/, '').trim();
  }

  // generate report subtitle
  generateReportSubtitle(reportParam: NzSafeAny, config?: NzSafeAny): string {
    if (config?.settingPreview) {
      const previewFromDate = config.settingPreview.previewFromDate;
      const previewToDate = config.settingPreview.previewToDate;
      const previewDateLabel = config.settingPreview.previewDateLabel;

      let fromDate = reportParam[previewFromDate];
      let toDate = reportParam[previewToDate];

      if (fromDate && toDate) {
        fromDate = moment(fromDate).format('DD/MM/YYYY');
        toDate = moment(toDate).format('DD/MM/YYYY');
        return `From ${fromDate} To ${toDate}`;
      } else if (fromDate) {
        fromDate = moment(fromDate).format('DD/MM/YYYY');
        return previewDateLabel ? `${previewDateLabel} ${fromDate}` : fromDate;
      } else if (toDate) {
        toDate = moment(toDate).format('DD/MM/YYYY');
        return previewDateLabel ? `${previewDateLabel} ${toDate}` : toDate;
      }
      return '';
    }
    if (reportParam.FromDate && reportParam.ToDate) {
      const fromDate = moment(reportParam.FromDate).format('DD/MM/YYYY');
      const toDate = moment(reportParam.ToDate).format('DD/MM/YYYY');
      return `From ${fromDate} To ${toDate}`;
    } else if (reportParam.StartDate && reportParam.EndDate) {
      const fromDate = moment(reportParam.StartDate).format('DD/MM/YYYY');
      const toDate = moment(reportParam.EndDate).format('DD/MM/YYYY');
      return `From ${fromDate} To ${toDate}`;
    } else if (reportParam.ReportDate || reportParam.ReportedDate) {
      const reportDate = moment(
        reportParam.ReportDate || reportParam.ReportedDate,
      ).format('DD/MM/YYYY');
      return `Report Date: ${reportDate}`;
    }
    return '';
  }

  tranformFilename = signal<string | undefined>(undefined);
  async handleAfterGetDataReport(item: NzSafeAny) {
    // generate report subtitle
    this.reportSubtitle.set(
      this.generateReportSubtitle(item.value, item.config),
    );
    // generate report company name
    this.reportCompanyName.set(
      this.generateReportCompanyName(item.value, item.config),
    );

    // transform file name by config
    const fileNameConfig = item.config?.settingPreview?._filename;
    if (fileNameConfig) {
      const userName = this.user()?.userName;
      const filename = await this.transformValue(fileNameConfig.transform, {
        ...item.value,
        username: userName,
      });
      this.tranformFilename.set(filename);
    }
    // set grouping config
    this.previewTableGroupingConfig.set(item.config?.groupConfig);

    if (PREVIEW_TYPE)
      switch (this.typePreviewPopup()) {
        case PREVIEW_TYPE.CV:
          this.isPreview = true;
          break;

        default:
          //TODO - need to change, hard code to pass demo for FO report
          if (
            item.value.run?.value === 'online' ||
            item.value.run === 'online'
          ) {
            this.isPreview = true;
          } else {
            this.toast.showToast('success', 'Record Exported', 'View Schedule');
            this.closeDialog();
            setTimeout(() => {
              if (this.link_redirect()) {
                this.router.navigate([this.link_redirect()]);
              }
            }, 500);
          }
          break;
      }
  }

  previewTableCol = signal<NzSafeAny[]>([]);
  previewTableData = signal<NzSafeAny[]>([]);
  isLoading = signal<boolean>(false);
  queryString = '';
  queryObject: Record<string, NzSafeAny> = {};
  formValueGenerateReport = signal<NzSafeAny>({});

  // preview table transform config
  previewTableTransformConfig = signal<NzSafeAny>(null);

  previewTableDynamicHeaderConfig = signal<NzSafeAny>(null);

  setLoadPdf = signal<boolean | undefined>(undefined);
  isLoadPdf = computed(() => {
    return (
      this.setLoadPdf() ?? this.functionSpec()?.layout_options?.is_load_pdf
    );
  });
  streamData: NzSafeAny = null;

  // grouping config
  previewTableGroupingConfig = signal<NzSafeAny>({});

  setUTCDate(currentDate: Date) {
    return Math.floor(
      new Date(
        Date.UTC(
          currentDate?.getFullYear(),
          currentDate?.getMonth(),
          currentDate?.getDate(),
          0,
          0,
          0,
        ),
      ).getTime() / 1000,
    );
  }

  processQueryByFilterMapping(filterMapping: NzSafeAny[], data: NzSafeAny) {
    const queryParts: string[] = [];
    const processItem = (
      fieldName: string,
      valueField: NzSafeAny,
      operator: string,
    ) => {
      switch (operator) {
        case '$eq':
          queryParts.push(
            `${encodeURIComponent(fieldName)} = ${encodeURIComponent(
              valueField,
            )}`,
          );
          break;
        case '$ne':
          queryParts.push(
            `${encodeURIComponent(fieldName)} != ${encodeURIComponent(
              valueField,
            )}`,
          );
          break;
        case '$gt':
          queryParts.push(
            `${encodeURIComponent(fieldName)} > ${encodeURIComponent(
              valueField,
            )}`,
          );
          break;
        case '$lt':
          queryParts.push(
            `${encodeURIComponent(fieldName)} < ${encodeURIComponent(
              valueField,
            )}`,
          );
          break;
        case '$gte':
          queryParts.push(
            `${encodeURIComponent(fieldName)} >= ${encodeURIComponent(
              valueField,
            )}`,
          );
          break;
        case '$lte':
          queryParts.push(
            `${encodeURIComponent(fieldName)} <= ${encodeURIComponent(
              valueField,
            )}`,
          );
          break;
        case '$cont':
          queryParts.push(
            `${encodeURIComponent(fieldName)} =* ${encodeURIComponent(
              valueField,
            )}`,
          );
          break;
        case '$between':
          if (valueField.length < 2) {
            queryParts.push(
              `${encodeURIComponent(fieldName)} >= ${encodeURIComponent(
                valueField,
              )}`,
            );
          } else {
            queryParts.push(
              `${encodeURIComponent(fieldName)} >= ${encodeURIComponent(
                valueField[0],
              )}, ${encodeURIComponent(fieldName)} <= ${encodeURIComponent(
                valueField[1],
              )}`,
            );
          }

          break;
        case '$in':
          if (Array.isArray(valueField)) {
            const filterStr = valueField
              .map(
                (v: NzSafeAny) =>
                  `${encodeURIComponent(fieldName)} = ${encodeURIComponent(v)}`,
              )
              .join('|');
            queryParts.push(`(${filterStr})`);
          } else {
            queryParts.push(
              `${encodeURIComponent(fieldName)} =* ${encodeURIComponent(
                valueField,
              )}`,
            );
          }

          break;
        case '$nin':
          break;
        default:
          queryParts.push(
            `${encodeURIComponent(fieldName)} = ${encodeURIComponent(
              valueField,
            )}`,
          );
          break;
      }
    };

    filterMapping.forEach((filter: NzSafeAny) => {
      if (filter.valueField && data[filter.valueField]) {
        const fieldName = filter.field;
        const valueField = data[filter.valueField];
        processItem(fieldName, valueField, filter.operator);
      }
    });

    return queryParts;
  }

  showErrorResponseMessage = async (error: HttpErrorResponse) => {
    const errMessage = await this._service.getErrorMessage(error);
    this.toast.showToast('error', 'Error', errMessage);
  };

  async onGenerateReport(item: NzSafeAny, backendUrl: string) {
    this.queryString = '';
    this.queryObject = {};
    const formValue = item.value[' '] ? item.value[' '] : item.value;
    const formConfig = item.config;

    const { settingPreview } = item.config || {};
    this.typePreviewPopup.set(
      settingPreview?.typePreview ?? PREVIEW_TYPE.TABLE,
    );

    const isMethodPOST = settingPreview?.method === 'POST';

    if (formValue['run'] === 'offline') {
      this.createNewSchedule(
        'api/report-parameter',
        formValue,
        true,
        formConfig,
      );
    } else {
      let processedFormValue = formValue;
      if (formConfig) {
        // if there is NzSafeAny configure for the output api
        processedFormValue = this.processFormValue(formValue, formConfig);
      }

      this.formValueGenerateReport.set(processedFormValue);

      let queryParts: string[] = [];

      const filterMapping = settingPreview?.filterMapping;
      if (filterMapping) {
        const isUseFilterQuery = settingPreview?.useFilterQuery ?? false;
        if (isUseFilterQuery) {
          queryParts = this.processQueryByFilterMapping(
            filterMapping,
            processedFormValue,
          );

          this.queryString =
            queryParts.length > 0 ? `Filter=(${queryParts.join(',')})` : '';
        } else {
          filterMapping.forEach((filter: NzSafeAny) => {
            if (filter.valueField && processedFormValue[filter.valueField]) {
              const fieldName = filter.field;
              const valueField = processedFormValue[filter.valueField];

              if (isMethodPOST) {
                this.queryObject[fieldName] = valueField;
              }
              if (Array.isArray(valueField)) {
                valueField.forEach((item) => {
                  if (item != null && item.toString().length > 0) {
                    queryParts.push(
                      `${encodeURIComponent(fieldName)}=${encodeURIComponent(item)}`,
                    );
                  }
                });
              } else if (
                valueField != null &&
                valueField.toString().length > 0
              ) {
                queryParts.push(
                  `${encodeURIComponent(fieldName)}=${encodeURIComponent(valueField)}`,
                );
              }
            }
          });
          this.queryString = queryParts.join('&');
        }
      } else {
        for (const [key, value] of Object.entries(
          _.omit(processedFormValue, ['run']),
        )) {
          let val = value;

          if (val instanceof Date) {
            val = moment(val).unix();
          }

          if (isMethodPOST) {
            this.queryObject[key] = val;
          }
          if (Array.isArray(val)) {
            val.forEach((item) => {
              if (item != null && item.toString().length > 0) {
                queryParts.push(
                  `${encodeURIComponent(key)}=${encodeURIComponent(item)}`,
                );
              }
            });
          } else if (val != null && val.toString().length > 0) {
            queryParts.push(
              `${encodeURIComponent(key)}=${encodeURIComponent(val)}`,
            );
          }
        }
        this.queryString = queryParts.join('&');
      }

      if (backendUrl) {
        this.isLoading.set(true);

        // set the current backend url and report item
        this.currentBackendUrl = backendUrl;
        this.currentReportItem = item;

        this.previewTablePageSize.set(20);
        this.previewTablePageIndex.set(1);

        if (this.typePreviewPopup() !== PREVIEW_TYPE.TABLE_EXPAND) {
          const transformColumnsTitle =
            await this.utilService.transformColumnTitle(
              this.previewTableCol(),
              processedFormValue,
            );

          // console.log('transformColumnsTitle', transformColumnsTitle);
          if (transformColumnsTitle) {
            this.previewTableCol.set(transformColumnsTitle);
          }
        }

        //set load PDF for preview online
        this.setLoadPdf.set(settingPreview?.loadPdf ?? undefined);

        if (this.isLoadPdf()) {
          this._service
            .getStreamByQuery(backendUrl, this.queryString, undefined, {
              authAction: AuthActions.GenerateReport,
            })
            .pipe(
              catchError((err) => {
                this.isLoading.set(false);
                // this.toast.showToast('error', 'Error', err.error?.message);
                // call direct to API not through bff.
                this.showErrorResponseMessage(err);
                return of(undefined);
              }),
              tap((data) => {
                this.streamData = data;
              }),
              tap((data) => {
                if (isNil(data)) return;
                this.handleAfterGetDataReport(item);
              }),
              finalize(() => {
                this.isLoading.set(false);
              }),
            )
            .subscribe();
        } else {
          this.loadReportData(
            backendUrl,
            formValue,
            this.previewTablePageSize(),
            this.previewTablePageIndex(),
            item,
          );
        }
      } else {
        this.toast.showToast('error', 'Error', 'Url not found');
      }
    }
  }

  //#region -- preview paging
  previewTableTotal = signal<number>(0);
  previewTablePageSize = signal<number>(20);
  previewTablePageIndex = signal<number>(1);
  contentLoading = signal<boolean>(false);
  // load data report with paging
  loadReportData(
    backendUrl: string,
    formValue: NzSafeAny,
    pageSize: number,
    pageIndex: number,
    item: NzSafeAny,
  ) {
    const { settingPreview } = item.config || {};

    const isMethodPOST = settingPreview?.method === 'POST';
    if (isMethodPOST) {
      this.contentLoading.set(true);
      this.queryObject = {
        ...this.queryObject,
        Page: pageIndex,
        PageSize: pageSize,
      };
      this._service
        .Post(backendUrl, this.queryObject, undefined, {
          authAction: AuthActions.GenerateReport,
        })
        .pipe(
          catchError((err: NzSafeAny) => {
            this.toast.showToast('error', 'Error', err.error?.message);
            return of(undefined);
          }),
        )
        .subscribe({
          next: async (data: NzSafeAny) => {
            if (this.typePreviewPopup() !== PREVIEW_TYPE.FORM) {
              const transformDataConfig = settingPreview?.transformData;

              if (transformDataConfig) {
                const newDataReport = await this.transformValue(
                  transformDataConfig,
                  data['items'],
                );
                this.previewTableData.set(newDataReport);
              } else {
                this.previewTableData.set(data['items']);
              }

              this.previewTableTotal.set(data['totalCount']);
              this.previewTablePageSize.set(pageSize);
              this.previewTablePageIndex.set(pageIndex);
              this.isLoading.set(false);
              this.handleAfterGetDataReport(item);
            } else {
              this.isLoading.set(false);
              this.handleAfterGetDataReport(item);
            }
            this.contentLoading.set(false);
          },
          error: (err) => {
            this.toast.showToast('error', 'Error', err.error?.message);
            this.contentLoading.set(false);
            this.isLoading.set(false);
          },
        });
    } else {
      // Append pagination parameters to the query string
      const paginatedQueryString = `${this.queryString}&Page=${pageIndex}&PageSize=${pageSize}`;
      this.contentLoading.set(true);
      this._service
        .getObjectByQuery(
          backendUrl,
          this.buildFilterQuery([], formValue),
          paginatedQueryString,
          undefined,
          { authAction: AuthActions.GenerateReport },
        )
        .subscribe({
          next: async (data) => {
            if (this.typePreviewPopup() !== PREVIEW_TYPE.FORM) {
              const transformDataConfig = settingPreview?.transformData;

              if (transformDataConfig) {
                const newDataReport = await this.transformValue(
                  transformDataConfig,
                  data['items'],
                );
                this.previewTableData.set(newDataReport);
              } else {
                this.previewTableData.set(data['items']);
              }

              this.previewTableTotal.set(data['totalCount']);
              this.previewTablePageSize.set(pageSize);
              this.previewTablePageIndex.set(pageIndex);
              this.isLoading.set(false);
              this.handleAfterGetDataReport(item);
            } else {
              this.isLoading.set(false);
              this.handleAfterGetDataReport(item);
            }
            this.contentLoading.set(false);
          },
          error: (err) => {
            this.toast.showToast('error', 'Error', err.error?.message);
            this.contentLoading.set(false);
            this.isLoading.set(false);
          },
        });
    }
  }

  onPreviewTablePageSizeChange(newPageSize: number) {
    this.previewTablePageSize.set(newPageSize);
    this.previewTablePageIndex.set(1);
    this.loadReportData(
      this.currentBackendUrl,
      this.formValueGenerateReport(),
      newPageSize,
      1,
      this.currentReportItem,
    );
  }

  onPreviewTablePageIndexChange(newPageIndex: number) {
    this.previewTablePageIndex.set(newPageIndex);
    this.loadReportData(
      this.currentBackendUrl,
      this.formValueGenerateReport(),
      this.previewTablePageSize(),
      newPageIndex,
      this.currentReportItem,
    );
  }

  // store the current backend url and report item
  currentBackendUrl = '';
  currentReportItem: NzSafeAny;

  //#endregion

  buildFilterQuery(filterMapping: NzSafeAny, formValue: NzSafeAny) {
    const res =
      filterMapping
        ?.map((f: NzSafeAny) => {
          return this.travelField(f, formValue);
        })
        .filter(
          (f: NzSafeAny) =>
            (!isArray(f.value) && !isNil(f.value) && f.value !== '') ||
            (isArray(f.value) && f.value.length),
        ) ?? [];

    return res;
  }

  async buildCustomUrl(
    transformUrl: string,
    formValue: Record<string, NzSafeAny>,
  ) {
    return await this.transformValue(transformUrl, formValue);
  }

  async transformValue(transform: string, value: Record<string, NzSafeAny>) {
    return await this.dynamicService.getJsonataExpression({})(transform, value);
  }

  link_redirect = computed<string | undefined>(() => {
    const fs = this.functionSpec();
    if (fs) {
      return fs.layout_options?.link_redirect ?? undefined;
    }
    return undefined;
  });

  link_get_template_redirect = computed<string>(() => {
    const fs = this.functionSpec();
    return fs.layout_options?.link_get_template_redirect ?? '/GE/HR.FS.FR.092';
  });

  async nextStep(formValue: NzSafeAny, action: string) {
    const reAuthRes = await this.checkReAuthentication(action);
    if (!reAuthRes) return;
    switch (action) {
      case 'new-schedule':
        this.createNewSchedule('api/report-parameter', formValue);
        break;
      case 'edit-schedule':
        this.updateSchedule('api/report-parameter', formValue);
        break;

      default:
        break;
    }
    return;
  }

  createNewSchedule(
    backendUrl: string,
    formValue: NzSafeAny,
    isOffline = false,
    formConfig?: NzSafeAny,
  ) {
    const body = this.transformBody(formValue, isOffline, formConfig);
    // console.log('body', body);
    // return;
    this.isLoading.set(true);
    this._service
      .createItem(backendUrl, body, undefined, {
        authAction: AuthActions.Schedule,
      })
      .subscribe({
        next: (data) => {
          this.isLoading.set(false);
        },
        error: (err) => {
          this.toast.showToast('error', 'Error', err.error?.message);
          this.isLoading.set(false);
        },
        complete: () => {
          if (isOffline) {
            this.closeDialog();
            this.modalComponent.showDialog(
              {
                nzTitle: `Schedule Report Offline`,
                nzContent: 'Your report has been scheduled',
                nzIconType: 'icons:check-circle-bold',
                nzWrapClassName: 'popup popup-confirm',
                nzOkText: 'OK',
                nzCancelText: 'View Schedules',
                nzWidth: '400px',
                nzClosable: false,
                nzCentered: true,
                nzAutofocus: null,
                nzOnCancel: () => {
                  this.router.navigate([this.link_redirect()]);
                },
              },
              'success',
            );
          } else {
            this.stepHorizontalModalVisible.set(false);
            this.modalComponent.showDialog(
              {
                nzTitle: 'Successfully saved',
                nzContent:
                  'To view the status, please visit the "View Schedules" page',
                nzIconType: 'icons:check-circle-bold',
                nzWrapClassName: 'popup popup-confirm',
                nzOkText: 'OK',
                nzWidth: '400px',
                nzClosable: false,
                nzCentered: true,
                nzAutofocus: null,
                nzCancelText: 'View Schedules',
                nzOnCancel: () => {
                  this.router.navigate([this.link_redirect()]);
                },
              },
              'success',
            );
            // this.toast.showToast(
            //   'success',
            //   '',
            //   'Successfully saved. To view the status, please visit the "View Schedules" page',
            // );
          }
          return;
        },
      });
  }

  // update schedule
  updateSchedule(backendUrl: string, formValue: NzSafeAny, isOffline = false) {
    const body = this.transformBody(formValue, isOffline);
    this.isLoading.set(true);
    this._service
      .updateItem(backendUrl, this.selectedItemSchedule()?.id, body)
      .subscribe({
        next: (data) => {
          this.isLoading.set(false);
        },
        error: (err) => {
          this.toast.showToast('error', 'Error', err.error?.message);
          this.isLoading.set(false);
        },
        complete: () => {
          if (isOffline) {
            this.closeDialog();
            this.modalComponent.showDialog(
              {
                nzTitle: `Schedule Report Offline`,
                nzContent: 'Your report has been scheduled',
                nzIconType: 'icons:check-circle-bold',
                nzWrapClassName: 'popup popup-confirm',
                nzOkText: 'OK',
                nzCancelText: 'View Schedules',
                nzWidth: '400px',
                nzClosable: false,
                nzCentered: true,
                nzAutofocus: null,
                nzOnCancel: () => {
                  this.router.navigate([this.link_redirect()]);
                },
              },
              'success',
            );
          } else {
            this.refreshData.update((e) => !e);
            this.stepHorizontalModalVisible.set(false);

            this.toast.showToast('success', '', 'Record saved');
          }
          return;
        },
      });
  }

  // Helper function to convert timestamps
  convertTimestampsToMilliseconds(obj: NzSafeAny): NzSafeAny {
    if (typeof obj !== 'object' || obj === null) {
      return obj;
    }

    if (Array.isArray(obj)) {
      return obj.map((item) => this.convertTimestampsToMilliseconds(item));
    }

    const result: NzSafeAny = {};
    for (const [key, value] of Object.entries(obj)) {
      if (typeof value === 'number' && this.isLikelyUnixTimestamp(value)) {
        result[key] = new Date(+value * 1000); // Convert seconds to milliseconds
      } else if (typeof value === 'object') {
        result[key] = this.convertTimestampsToMilliseconds(value);
      } else {
        result[key] = value;
      }
    }
    return result;
  }

  // Helper function to check if a number is likely a Unix timestamp
  isLikelyUnixTimestamp(value: number): boolean {
    // Assuming timestamps between 31/Jan/1970 and 01/01/2500
    const minTimestamp = 2678400; // 31/Jan/1970 00:00:00 UTC
    const maxTimestamp = 16725225600; // 01/01/2500 00:00:00 UTC
    return value >= minTimestamp && value <= maxTimestamp;
  }

  convertDateToTimestamp(value: NzSafeAny) {
    let newValue = structuredClone(value);
    for (const key in value) {
      if (value[key] instanceof Date) {
        newValue = {
          ...newValue,
          [key]: moment(value[key].toISOString()).unix(),
        };
      }
    }

    return newValue;
  }

  transformBody(
    formValue: NzSafeAny,
    isOffline: boolean,
    formConfigItem?: NzSafeAny,
  ) {
    let body: NzSafeAny = {};

    if (isOffline) {
      let processedFormValue = { ...formValue };
      if (formConfigItem) {
        // if there is NzSafeAny configure for the output api
        processedFormValue = this.processFormValue(formValue, formConfigItem);
      }

      const reportType =
        formValue['scheduleFormat']?.value ?? formValue['scheduleFormat'];

      let jsonParam: NzSafeAny = {};
      const filterMapping = formConfigItem?.settingPreview?.filterMapping;

      if (filterMapping) {
        const isUseFilterQuery =
          formConfigItem.settingPreview?.useFilterQuery ?? false;

        if (isUseFilterQuery) {
          const queryParts: string[] = this.processQueryByFilterMapping(
            filterMapping,
            processedFormValue,
          );

          jsonParam = this.convertDateToTimestamp({
            reportType,
            Filter: queryParts.length > 0 ? `(${queryParts.join(',')})` : null,
          });
        } else {
          filterMapping.forEach((filter: NzSafeAny) => {
            if (filter.valueField && processedFormValue[filter.valueField]) {
              const fieldName = filter.field;
              const valueField = processedFormValue[filter.valueField];
              jsonParam[fieldName] = valueField;
            }
          });
          jsonParam = this.convertDateToTimestamp({
            ...jsonParam,
            reportType,
          });
        }
      } else {
        jsonParam = this.convertDateToTimestamp(
          omit(
            {
              ...processedFormValue,
              reportType,
            },
            ['run', 'scheduleFormat'],
          ),
        );
      }

      body = {
        code: this.selectedItem()?.['functionCode']
          ? this.selectedItem()?.['functionCode']
          : this.selectedItem()?.['code'],
        scheduleName: this.customDialogTitle(),
        occurrenceCode: 'Once',
        // recurringPatternCode: 'Daily',
        // listDayCode: 'Monday',
        isSendNotiOnCompletion: false,
        isSendNotiOnStart: false,
        firstOccurrence: moment().add(1, 'minute').toDate(),
        jsonParam: this.parseJsonToString(jsonParam),
        jsonParamDetailFE: this.parseJsonToString(
          this.convertDateToTimestamp(
            omit({ ...formValue, reportType }, ['run', 'scheduleFormat']),
          ),
        ),
        scheduleFormat: reportType,
      };
    } else {
      const formConfig = this.newScheduleFormConfig();

      let jsonParam: NzSafeAny = {};
      formValue.map((e: NzSafeAny, index: number) => {
        const value = structuredClone(e['value']);
        if (index === 1) {
          jsonParam = value;
        } else {
          body = { ...body, ...value };
        }
      });
      const reportType = body.scheduleFormat?.value ?? body.scheduleFormat;

      const jsonParamDetailFE = this.parseJsonToString({
        ...jsonParam,
        reportType,
      });

      if (formConfig) {
        // if there is NzSafeAny configure for the output api
        jsonParam = this.processFormValue(jsonParam, formConfig);

        const filterMapping = formConfig?.settingPreview?.filterMapping;

        if (filterMapping) {
          const isUseFilterQuery =
            formConfig.settingPreview?.useFilterQuery ?? false;

          if (isUseFilterQuery) {
            const queryParts: string[] = this.processQueryByFilterMapping(
              filterMapping,
              jsonParam,
            );

            jsonParam = this.convertDateToTimestamp({
              reportType,
              Filter:
                queryParts.length > 0 ? `(${queryParts.join(',')})` : null,
            });
          } else {
            filterMapping.forEach((filter: NzSafeAny) => {
              if (filter.valueField && jsonParam[filter.valueField]) {
                const fieldName = filter.field;
                const valueField = jsonParam[filter.valueField];
                jsonParam[fieldName] = valueField;
              }
            });

            jsonParam = {
              ...jsonParam,
              reportType,
            };
          }
        } else {
          jsonParam = {
            ...jsonParam,
            reportType,
          };
        }
      }

      body = {
        ...body,
        jsonParamDetailFE,
        jsonParam: this.parseJsonToString(jsonParam),
        code: this.selectedItem()?.['functionCode'],
      };
    }

    delete body.undefined;

    return body;
  }

  closeDialog() {
    this.dialogVisible.set(false);
  }

  importVisible = signal<boolean>(false);

  showImportDialog = () => {
    this.importVisible.set(true);
  };

  cancelImportDialog = () => {
    this.importVisible.set(false);
  };
  modalService = inject(NzModalService);
  importSubmit = (dataImport: NzSafeAny) => {
    const url = this.url();
    if (url)
      this._service
        .importData(url, { file: dataImport }, this.getFaceCodeForService())
        .subscribe({
          next: (res: NzSafeAny) => {
            if (res.code === 200) {
              this.toast.showToast(
                'success',
                'Success',
                'Imported Successfully',
              );
              this.refreshData.update((e) => !e);
              this.cancelImportDialog();
            } else {
              this.modalService.warning({
                nzTitle: 'Do you want to download the error file?',
                nzContent: `${res.message}. To troubleshoot the issue, please download the error log for more details.`,
                nzCancelText: 'Cancel',
                nzWrapClassName: 'popup popup-confirm',
                nzOkText: 'Download',
                nzOnOk: () => {
                  this._service
                    .getFileValidate(res.key, {
                      faceCode: this.getFaceCodeForService(),
                    })
                    .subscribe();
                },
              });

              this.toast.showToast('error', 'Error', res?.message);
              this.refreshData.update((e) => !e);
              this.cancelImportDialog();
            }
          },
          error: (err) => {
            if (err.status === 201 || err.status === 200) {
              this.toast.showToast(
                'success',
                'Success',
                'Imported Successfully',
              );
              this.refreshData.update((e) => !e);
              this.cancelImportDialog();
            } else {
              this.toast.showToast('error', 'Error', err.error?.message);
            }
          },
        });
  };

  importVisibleChange = (visible: boolean) => {
    this.importVisible.set(visible);
  };

  calculateScrollValue() {
    return {
      x:
        this.previewTableCol()[0]['row'] &&
        this.previewTableCol()[0]['row'].length > 10
          ? this.previewTableCol()[0]['row'].length *
              this.previewTableCol()[0]['row'].length *
              10 +
            'px'
          : '2000px',
      y:
        this.previewTableData() && this.previewTableData().length > 10
          ? this.previewTableData().length * 30 + 'px'
          : '400px',
    };
  }

  title = signal<string>('');
  stepHorizontalModalVisible = signal<boolean>(false);
  onStepHorizontalModalVisibleChange(visible: boolean) {
    this.stepHorizontalModalVisible.set(visible);
    this.authAction.set(undefined);
  }
  newScheduleFormValue = signal<NzSafeAny>({});
  onNewSchedule(item: NzSafeAny) {
    const cloneItem = structuredClone(item);
    this.authAction.set(AuthActions.Schedule);
    //mapping form config for new-schedule
    const filterGenerateForm = item.reportFilter
      ? this.parseStringToJson(item.reportFilter)
      : {};

    const downloadOptionConfiguration = item.downloadOptionConfiguration
      ? this.parseStringToJson(item.downloadOptionConfiguration)
      : undefined;

    if (downloadOptionConfiguration) {
      cloneItem.downloadOptionConfiguration = downloadOptionConfiguration;
    }

    const newScheduleFormConfig =
      structuredClone(this.functionSpec()?.create_form) ?? {};
    if (newScheduleFormConfig?.fields) {
      newScheduleFormConfig.fields.splice(1, 0, {
        ...this.transformReportParametersJson(filterGenerateForm.fields[1]),
        variables: filterGenerateForm.variables,
        sources: filterGenerateForm.sources,
      });

      this.newScheduleFormConfig.set(newScheduleFormConfig);
    }

    this.selectedItem.set(cloneItem);
    this.newScheduleFormValue.set({
      scheduleName: `${item?.code} - ${item?.name}`,
      ...item,
    });
    this.dialogType.set('create');
    this.title.set(`Report Schedule: ${item?.code} - ${item?.name}`);
    this.stepHorizontalModalVisible.set(true);
  }

  loadingDataSchedule = signal<boolean>(false);

  selectedItemSchedule = signal<Data | null>(null);

  // edit schedule
  onEditSchedule(item: NzSafeAny) {
    this.loadingDataSchedule.set(true);
    this.authAction.set(AuthActions.Schedule);
    this.title.set(`Report Schedule: ${item?.scheduleName}`);
    this.stepHorizontalModalVisible.set(true);
    // get report filter by code
    this._service
      .getObject('/api/report-storages', [
        {
          field: 'search',
          operator: '$eq',
          value: item.code,
        },
      ])
      .subscribe({
        next: (reportItem: NzSafeAny) => {
          this.loadingDataSchedule.set(false);
          if (reportItem) {
            // console.log('reportItem', reportItem);

            //mapping form config for new-schedule
            const filterGenerateForm = reportItem.reportFilter
              ? this.parseStringToJson(reportItem.reportFilter)
              : {};

            const newScheduleFormConfig =
              structuredClone(this.functionSpec()?.form_config) ?? {};
            if (newScheduleFormConfig?.fields) {
              newScheduleFormConfig.fields.splice(1, 0, {
                ...this.transformReportParametersJson(
                  filterGenerateForm.fields[1],
                ),
                variables: filterGenerateForm.variables,
                sources: filterGenerateForm.sources,
              });
              this.newScheduleFormConfig.set(newScheduleFormConfig);
            }

            const downloadOptionConfiguration =
              reportItem.downloadOptionConfiguration
                ? this.parseStringToJson(reportItem.downloadOptionConfiguration)
                : undefined;

            if (downloadOptionConfiguration) {
              reportItem.downloadOptionConfiguration =
                downloadOptionConfiguration;
            }

            this.selectedItem.set(reportItem);
            this.selectedItemSchedule.set(item);
            this.dialogType.set('edit');

            // Replace escaped quotes with regular quotes
            const cleanedString = (
              item?.jsonParamDetailFE ??
              item?.jsonParam ??
              ''
            ).replace(/\\"/g, '"');

            // Parse the cleaned string as JSON
            const jsonObject = JSON.parse(cleanedString);

            // in this jsonObject, I need to convert the date inform of seconds to milliseconds
            const newJsonObject =
              this.convertTimestampsToMilliseconds(jsonObject);

            // set value
            this.dialogValue.set({
              ...item,
              ...newJsonObject,
            });
          }
        },
        error: (err) => {
          this.toast.showToast('error', 'Error', err.error?.message);
          this.loadingDataSchedule.set(false);
        },
      });
  }

  // transform report parameters json
  transformReportParametersJson(input: NzSafeAny): NzSafeAny {
    const transformDateRangeFields = (fields: NzSafeAny[]) => {
      fields.forEach((field) => {
        if (field.type === 'dateRange') {
          field.addOnBottom = {
            type: 'checkbox',
            label: 'Recalculated base on frequency',
            name: `${field.name}_recalculatedBaseOnFrequency`,
            value: false,
            prefixLabel: false,
            hiddenLabel: true,
            description:
              'When selecting recalculate, this parameter is recalculated based on the configured date and frequency. Please set the frequency in the next step',
          };
        } else if (field.fields) {
          transformDateRangeFields(field.fields);
        }
      });
    };

    const dataFields = structuredClone(input.fields);
    transformDateRangeFields(dataFields); //save me!
    // Create the new structure
    const transformedJson = {
      type: 'group',
      label: 'Parameters',
      collapsed: false,
      disableEventCollapse: true,
      fields: [
        {
          type: 'group',
          collapsed: false,
          disableEventCollapse: true,
          label: '2. Parameters',
          n_cols: input.n_cols ?? 1,
          fields: dataFields,
        },
      ],
    };

    return transformedJson;
  }

  sortOrder = signal<Record<string, string | null>>({});
  sortOrderChange(e: string | null, key: string) {
    this.sortOrder.update((val) => ({
      // ...val,
      [key]: e,
    }));
  }

  isImportLoading = signal(false);
  importLoadingMessage = signal('');
  onGetTemplate() {
    // const url = this.url();
    // if (!url) return;
    // this.isImportLoading.set(true);
    // this.importLoadingMessage.set('Downloading Template...');
    // this._service.getTemplate(url).subscribe({
    //   next: (res) => {
    //     this.isImportLoading.set(false);
    //     this.toast.showToast(
    //       'success',
    //       'Success',
    //       'Download Template successfully',
    //     );
    //     // this.refreshData.update((e) => !e);
    //     // this.cancelImportDialog();
    //   },
    //   error: (err) => {
    //     this.isImportLoading.set(false);
    //     this.toast.showToast('error', 'Error', err.error?.message);
    //   },
    // });
    // this.router.navigate( + '?tab=2');

    this.navigateToPage([this.link_get_template_redirect()], {
      tab: 2,
    });

    // this.utilService.navigateToPage(
    //   [this.link_get_template_redirect()],
    //   { tab: 2 },
    //   this.router,
    //   window.location.origin,
    // );
  }
  baseUrl = window.location.origin;

  navigateToPage(path: string[], queryParams: Params) {
    const newPath = this.router.createUrlTree(path, { queryParams }).toString();
    // Construct the full URL
    const fullUrl = `${this.baseUrl}${newPath}`;

    // Open the new URL in a new tab or window
    // console.log('full url', fullUrl);
    window.open(fullUrl, '_blank');
  }

  proceedFooterButtonsCustom: ModalFooterButtons[] = [
    { id: 'cancel', title: 'Cancel', type: 'tertiary' },
    { id: 'save', title: 'Proceed', type: 'primary' },
  ];

  async handleActionByParam() {
    const queryParams: NzSafeAny = this.route.snapshot.queryParams;

    if (queryParams) {
      switch (queryParams.dialogType) {
        case 'view': {
          const id = queryParams.id;
          const url = this.url();
          if (id && url) {
            // get detail info from api to set detail action
            const itemData = await firstValueFrom(
              this._service.getItem(
                url,
                id,
                undefined,
                this.getFaceCodeForService(),
              ),
            );
            if (itemData) {
              this.preCheckDetailAction(itemData);
            }
          }

          this.viewClickOne(queryParams.id, { id: queryParams.id });
          break;
        }
        case 'view-result':
          this.viewResult(
            queryParams.code,
            queryParams.filteredApi,
            queryParams.sortOrder,
          );
          break;
        default:
          break;
      }
      this.changeQuery();
    }

    return;
  }

  changeQuery() {
    this.router.navigate(['.'], { relativeTo: this.route, queryParams: {} });
  }

  proceedDialogVisible = signal(false);
  dialogProceedConfig = signal<FormConfig | NzSafeAny>({});

  openProceedDialog(e?: NzSafeAny) {
    this.proceedDialogValue.set(e);
    this.dialogProceedConfig.set(this.functionSpec()?.create_form ?? {});
    this.proceedDialogVisible.set(true);
  }

  closeProceedDialog() {
    this.proceedDialogVisible.set(false);
  }

  proceedDialogValue = signal<Data | null | undefined>(null);
  onProceedSubmit(e: NzSafeAny, type?: DialogType, isCallApi?: boolean) {
    const obj = e.value ? structuredClone(e.value) : {};

    this.onProceed(obj, type, e.callback, isCallApi);
  }

  hireDialogValue = signal<Data | null | undefined>(null);
  onHireSubmit(e: NzSafeAny) {
    const obj = e.value ? structuredClone(e.value) : {};
    const hireRowItem: NzSafeAny = this.hireDialogValue();
    const url = '/api/ern-oir-employment/' + hireRowItem?.employeeId;
    const filterHire = this.functionSpec()?.create_form?.filterMapping;

    const queryFilter =
      filterHire
        ?.map((f: NzSafeAny) => {
          if (!f.valueField) return null;
          return {
            field: f.field,
            operator: f.operator,
            value: getValue(obj, f.valueField.split('.')),
          } as QueryFilter;
        })
        .filter(
          (f: NzSafeAny) =>
            f &&
            ((!isArray(f.value) && !isNil(f.value) && f.value !== '') ||
              (isArray(f.value) && f.value.length)),
        ) ?? [];
    this._service
      .getObjectByQuery(
        url,
        queryFilter,
        undefined,
        this.getFaceCodeForService(),
      )
      .subscribe({
        next: (data) => {
          if (data && data.jobDataMatchOIR) {
            const params = {
              tab: 'HR.FS.FR.009',
            };
            this.modalComponent.showDialog({
              nzTitle: 'Update existing OIR',
              nzContent:
                'Organizational Instance Record already exists, do you want to navigate to Employee Profile to update the existing Organizational Instance Record.',
              nzWrapClassName: 'popup popup-confirm',
              nzIconType: 'icons:warning',
              nzOkText: 'Confirm',
              nzWidth: '400px',
              nzClosable: false,
              nzCentered: true,
              nzCancelText: 'Cancel',
              nzOnOk: () => {
                this.router.navigate([
                  '/HR/employees/' + hireRowItem?.employeeId,
                  {
                    queryParams: params,
                    queryParamsHandling: 'merge',
                  },
                ]);
              },
            });
            e.callback(false);
            return;
          } else {
            this.layoutDataService.updateData({
              hire: { ...obj, employeeId: hireRowItem?.employeeId },
            });
            switch (obj.employeeGroupCode) {
              case 'EMP':
                this.router.navigate(['/HR/HR.FS.FR.046_01']);
                break;
              case 'CWR':
                this.router.navigate(['/HR/HR.FS.FR.046_02']);
                break;
              case 'POI':
                this.router.navigate(['/HR/HR.FS.FR.046_03']);
                break;
              default:
                break;
            }
          }
        },
        error: (err) => {
          this.toast.showToast('error', 'Error', err.error?.message);
          this.isLoading.set(false);
          e.callback(false);
        },
      });
  }

  closeDialogHire() {
    this.isActionHireVisible = false;
    this.layoutDataService.clearData();
  }

  getColumnBy(code: string) {
    const listHeader: NzSafeAny[] = this.headers();
    return listHeader.find((item) => item.code === code);
  }

  selectedTabEffect = effect(
    () => {
      if (this.tabset()?.length <= 0) return;
      const defaultTabIdx = this.defaultTabsetIndex();
      const tab = this.tabset()?.[defaultTabIdx];
      if (!tab) return;
      this.selectedTab.set(tab.id);
    },
    { allowSignalWrites: true },
  );

  selectedTab = signal<string | null>(null);
  onTabClick(idx: number) {
    const tab = this.tabset()?.[idx];
    if (!tab) return;
    this.selectedTab.set(tab.id);
    if (tab.url) {
      //TODO: for call api, handle when backend is ready
    } else {
      if (tab.filter) {
        this.tabsetFilter.set(tab.filter);
        this.pageIndex.set(1);
      } else {
        this.tabsetFilter.set(null);
      }
    }
    if (this.localFieldsPerTab()) {
      const localFields = this.localFieldsPerTab()?.[idx];
      this.allHeaders.set(localFields);
      this.adjustDisplayRef()?.registerHeaders(localFields);
    }
  }

  openAddEmployeeDialog() {
    // get layout options
    const layoutOptions = this.functionSpec()?.layout_options as NzSafeAny;

    const modalRef = this.modalService.create({
      nzTitle: 'Add a Person',
      nzContent: PersonidInputComponent,
      nzFooter: null, // use custom footer
      nzMaskClosable: false,
      // add search matching person redirect link
      nzData: {
        searchMatchingLink: layoutOptions?.search_matching_link ?? '#',
        employeeProfileLink: layoutOptions?.employee_profile_link ?? '#',
      },
      nzCentered: true,
    });

    modalRef.afterOpen.subscribe(() => {
      const instance = modalRef.getContentComponent();
      modalRef.updateConfig({
        nzFooter: instance.modalFooter,
      });
    });
  }
  hasValueOrIsZero(value: NzSafeAny): boolean {
    return value === 0 || (!isNil(value) && !isEmpty(value));
  }

  filterConfigMapping = computed(() => {
    const fields = this.functionSpec()?.filter_config?.fields;
    if (!fields) return {};
    const variables = this.layoutDialogVariables();
    const variablesMapping = this.filterOptionsLoadFromVariables();
    return mappingConfig(fields, variables, variablesMapping);
  });

  filterOptionsLoadFromVariables = computed(() => {
    const fields = this.functionSpec()?.filter_config?.fields;
    if (!fields) return {};
    return fields.reduce((acc: Record<string, string>, field: NzSafeAny) => {
      const _radioTransform = field?._radio?.transform;
      if (_radioTransform) {
        acc[field.name] = _radioTransform.split('.').at(-1);
      }
      return acc;
    }, {});
  });

  _customUrl = signal<string | null>(null);
  syncFilterValue = signal<Record<string, NzSafeAny>>({});
  async onPreconditionFilter(value: Record<string, NzSafeAny>) {
    const checkValueFilterEmpty =
      isEmpty(value) || every(value, (v: NzSafeAny) => v === undefined);
    if (checkValueFilterEmpty && this.isAutoFilter()) return;

    const customPath = this.customPath()?.transform ?? '';
    const syncFilterFields = this.preconditionFilter()?.sync_filter_fields;
    if (customPath) {
      const url = await this.buildCustomUrl(customPath, value);
      this._customUrl.set(url);
    } else if (syncFilterFields) {
      this.filterValue.update((prev) => ({ ...prev, ...value }));
      this.syncFilterValue.set(value);
    } else {
      this.preconditionFilterValue.set(value);
    }
  }

  filterDataRenderValue = computed(() => {
    const filterValue = structuredClone(this.filterValue());
    const hiddenFields = this.preconditionFilter()?.sync_filter_fields ?? [];
    hiddenFields?.forEach((key) => {
      delete filterValue?.[key];
    });
    return filterValue;
  });

  autoFilterTake = computed(
    () => this.layoutOptions()?.precondition_filter?.auto_filter_take,
  );
  autoFilterTaken = 0;
  handlePreconditionFilterValueChanges(value: Record<string, unknown>) {
    const take = this.autoFilterTake();
    if (isNil(take) || isEmpty(value)) return;
    if (this.autoFilterTaken < take) {
      this.autoFilterTaken++;
      this.onPreconditionFilter(value);
    }
  }

  // process value with apiOutputProperty
  processFormValue(
    formValue: NzSafeAny,
    formConfig: NzSafeAny,
    isNewVersion?: boolean,
  ): NzSafeAny {
    const processedValue: NzSafeAny = {};

    const formatDate = (
      date: Date | string | number,
      isDateHour?: boolean,
      field?: NzSafeAny,
    ) => {
      if (field?.setting?.type === 'year') {
        return date;
      }

      let startOfDay = new Date(date);
      if (isNewVersion) {
        startOfDay = new Date(
          startOfDay?.getFullYear(),
          startOfDay?.getMonth(),
          startOfDay?.getDate(),
          isDateHour ? startOfDay.getHours() : 0,
          isDateHour ? startOfDay.getMinutes() : 0,
          isDateHour ? startOfDay.getSeconds() : 0,
        );

        startOfDay = new Date(startOfDay.toUTCString());
      } else {
        startOfDay = new Date(
          Date.UTC(
            startOfDay?.getFullYear(),
            startOfDay?.getMonth(),
            startOfDay?.getDate(),
            isDateHour ? startOfDay.getHours() : 0,
            isDateHour ? startOfDay.getMinutes() : 0,
            isDateHour ? startOfDay.getSeconds() : 0,
          ),
        );
      }
      return Math.floor(startOfDay.getTime() / 1000);
    };

    const processField = (field: NzSafeAny, value: NzSafeAny) => {
      if (value instanceof Date || field.type === 'dateRange') {
        //xử lý date chỉ lấy ngày ko lấy giờ theo UTC.
        const isDateHour = field.setting?.autoFill === 'end-of';

        if (Array.isArray(value)) {
          return value.map((date) => formatDate(date, isDateHour, field));
        }

        return formatDate(value, isDateHour, field);
      }
      if (field.apiOutputProperty && value && typeof value === 'object') {
        if (isArray(value)) {
          return value.length > 0
            ? value.map((item) => item[field.apiOutputProperty])
            : null;
        }
        return value[field.apiOutputProperty];
      }
      return value;
    };

    const processFields = (fields: NzSafeAny[], values: NzSafeAny) => {
      fields.forEach((field) => {
        if (field.type === 'group' && field.fields) {
          processFields(field.fields, values);
        } else if (
          field.name &&
          Object.prototype.hasOwnProperty.call(values, field.name)
        ) {
          const fieldValue = processField(field, values[field.name]);
          if (fieldValue) {
            processedValue[field.name] = fieldValue;
          }

          if (field.addOnBottom) {
            const fieldConfig = field.addOnBottom;
            const fieldAddOnValue = processField(
              field.addOnBottom,
              values[fieldConfig.name],
            );
            if (fieldAddOnValue) {
              processedValue[fieldConfig.name] = fieldAddOnValue;
            }
          }
        }
      });
    };

    if (formConfig.fields) {
      processFields(formConfig.fields, formValue);
    }

    return processedValue;
  }

  // #region "Report related methods"

  handleAssignReportCategories(report?: NzSafeAny) {
    // get the config from layout_options
    const layoutOptions = this.functionSpec()?.layout_options as NzSafeAny;
    const tableConfig = layoutOptions?.dialogTable;

    if (tableConfig) {
      // open up the dialog table
      const modalRef = this.modalService.create({
        nzContent: DialogTableComponent,
        nzMaskClosable: false,
        nzWidth: '30%',
        nzTitle: 'Category As',
        nzData: {
          config: {
            columns: tableConfig.columns,
            data: [],
            url: tableConfig.api_url,
            showCheckbox: true,
            showCheckAllCheckbox: false,
            showPagination: false,
            actions: undefined,
            form: tableConfig.form,
            formType: 'selecteItem',
            orginReportCategory: report?.reportCategory?.map(
              (item: NzSafeAny) => item.id,
            ),
          } as DialogTableConfig,
        },
        nzFooter: null,
        nzViewContainerRef: this.viewContainerRef,
      });

      const modalInstance = modalRef.getContentComponent();
      modalInstance.setConfig(modalRef.getConfig().nzData.config);

      const reportIds = report
        ? [report.id]
        : this.listOfSelectedItems().map((item) => item.id);

      modalRef.afterClose.subscribe((selectedCategories: NzSafeAny[]) => {
        // console.log('selectedCategories', selectedCategories);

        // return;
        if (selectedCategories) {
          // call the api to assign the categories
          const categoryIds = selectedCategories;
          this._service
            .Post('/api/report-storages/join-categories', {
              reportIds: reportIds,
              categoryIds: categoryIds,
            })
            .subscribe({
              next: (res) => {
                // show toast
                this.toast.showToast('success', '', 'Record saved');
                // refresh the data
                this.refreshData.update((e) => !e);
              },
              error: (err) => {
                this.toast.showToast('error', 'Error', err.error?.message);
              },
            });
        }
      });
    }
  }

  // run report schedule
  runSchedule(row: NzSafeAny) {
    const body = {
      id: row.id,
      isBaseJob: row.isBaseJob,
    };
    this.#layoutStore.setLoading(true);
    // call api
    this._service
      .createItem(
        `/api/report-parameter/start-now-report-status`,
        body,
        undefined,
        { authAction: AuthActions.Update },
      )
      .subscribe({
        next: (res) => {
          this.#layoutStore.setLoading(false);
          this.toast.showToast('success', '', 'Schedule sent for running now');
          this.refreshData.update((e) => !e);
        },
        error: (err) => {
          this.#layoutStore.setLoading(false);
          this.toast.showToast('error', 'Error', err.error?.message);
        },
      });
  }

  // cancel report schedule
  cancelSchedule(row: NzSafeAny) {
    const body = {
      id: row.id,
      isBaseJob: row.isBaseJob,
    };
    this.#layoutStore.setLoading(true);
    this._service
      .createItem(
        `/api/report-parameter/cancel-report-status`,
        body,
        undefined,
        { authAction: AuthActions.Update },
      )
      .subscribe({
        next: (res) => {
          this.#layoutStore.setLoading(false);
          this.toast.showToast('success', '', 'Schedule canceled');
          this.refreshData.update((e) => !e);
        },
        error: (err) => {
          this.#layoutStore.setLoading(false);
          this.toast.showToast('error', 'Error', err.error?.message);
        },
      });
  }
  //define variables for table and view detail config
  isViewDetailConfig = signal<boolean>(false);
  dataViewDetailConfig = signal<NzSafeAny>({});
  loadingDetailSchedule = signal<boolean>(false);

  formatValueFieldDetailSchedule = (
    jsonParam: NzSafeAny,
    fields: NzSafeAny[],
  ) => {
    fields.forEach((field) => {
      if (field.fields?.length > 0) {
        this.formatValueFieldDetailSchedule(jsonParam, field.fields);
      } else if (field.type === 'dateRange') {
        if (Array.isArray(jsonParam[field.name])) {
          jsonParam[field.name] = jsonParam[field.name].map(
            (it: number | string) =>
              typeof it === 'number' ? new Date(+it * 1000) : it,
          );
        } else {
          jsonParam[field.name] =
            typeof jsonParam[field.name] === 'number'
              ? new Date(+jsonParam[field.name] * 1000)
              : jsonParam[field.name];
        }
      }
    });
  };

  extraDataSchedule = signal<NzSafeAny>(null);

  // view schedule
  viewSchedule(row: NzSafeAny) {
    this.loadingDetailSchedule.set(true);
    // this.dialogValue.set(row);
    this.dialogType.set('viewSchedule');
    this.customDialogTitle.set('Details');
    this.dialogVisible.set(true);

    this.selectedItem.set(row);

    this.getReportConfigFields(row).subscribe((res: NzSafeAny) => {
      const filterConfigFields = res?.dataConfig?.fields || [];
      if (filterConfigFields.length > 1) {
        const jsonParam = res.dataValue?.jsonParamDetailFE
          ? JSON.parse(res.dataValue.jsonParamDetailFE.replace(/\\"/g, '"'))
          : {};

        this.formatValueFieldDetailSchedule(
          jsonParam,
          filterConfigFields[1].fields,
        );

        const downloadOptionConfiguration = res.dataConfig
          ?.downloadOptionConfiguration
          ? this.parseStringToJson(res.dataConfig.downloadOptionConfiguration)
          : undefined;

        if (downloadOptionConfiguration) {
          res.dataConfig.downloadOptionConfiguration =
            downloadOptionConfiguration;
        }

        this.extraDataSchedule.set(res.dataConfig);
        this.selectedItem.set(res.dataValue);

        this.dialogValue.set({
          ...res.dataValue,
          ...jsonParam,
        });
        const config = this.functionSpec()?.create_form;
        //#region "get field group has key isDynamicConfig and assign fields to it"
        const groupDynamic = config.fields.find(
          (field: NzSafeAny) => field.isDynamicConfig,
        );

        const transformFieldConfig = (fieldArray: NzSafeAny[]): NzSafeAny[] => {
          return fieldArray.map((field: NzSafeAny) => {
            //#region "remove key col and n_cols from field"
            const { col, n_cols, ...resp } = field;

            if (resp.fields?.length > 0) {
              return {
                ...resp,
                fields: transformFieldConfig(resp.fields),
              };
            }

            if (resp.type === 'dateRange') {
              const recalculatedBaseOnFrequency =
                jsonParam[`${resp.name}_recalculatedBaseOnFrequency`];
              if (recalculatedBaseOnFrequency) {
                return {
                  type: 'group',
                  n_cols: 2,
                  space: 0,
                  fields: [
                    resp,
                    {
                      type: 'text',
                      name: `${resp.name}_recalculatedBaseOnFrequencyView`,
                      value: '( Recalculated base on frequency )',
                    },
                  ],
                };
              }
            }
            return resp;
          });
        };

        groupDynamic.fields = transformFieldConfig(
          filterConfigFields[1].fields ?? [],
        );

        // #region "assign sources and variables to config"
        config.sources = res?.dataConfig?.sources;
        config.variables = res?.dataConfig?.variables;

        this.dialogConfig.set(config);

        this.dialogVisible.set(true);
      }
      this.loadingDetailSchedule.set(false);
    });
  }

  downloadSchedule(row: NzSafeAny) {
    // get report config
    if (row?.fileId) {
      const url = 'api/report-storages/files';
      const filter: QueryFilter[] = [
        {
          field: 'fileId',
          operator: '$eq',
          value: row.fileId,
        },
      ];

      this._service
        .exportReport(url, filter)
        .pipe(
          catchError(async (err) => {
            let errorMessage = err?.message;
            if (err?.error instanceof Blob) {
              const jsonData = JSON.parse(await err.error.text());
              errorMessage = jsonData?.message;
            }

            // call direct to API not through bff.
            this.toast.showToast('error', 'Error', errorMessage);
            this.isLoading.set(false);
            return of(null);
          }),
        )
        .subscribe();
    }
  }

  // get report config fields
  getReportConfigFields(item: NzSafeAny): Observable<NzSafeAny> {
    const filterDataValue: QueryFilter[] = [];
    if (item.triggerJobId) {
      filterDataValue.push({
        field: 'reportStatusId',
        operator: '$eq',
        value: item.triggerJobId,
      });
    }

    return combineLatest({
      dataConfig: this._service
        .getList(`/api/report-storages`, undefined, undefined, [
          {
            field: 'search',
            operator: '$eq',
            value: item.code,
          },
        ])
        .pipe(
          mapRxjs((res: NzSafeAny) => {
            if (!res) return [];
            const getItem = res.find(
              (it: NzSafeAny) => it.functionCode === item.code,
            );

            if (!getItem) return [];
            try {
              const reportFilter = getItem['reportFilter'];
              const jsonParam = reportFilter ? JSON.parse(reportFilter) : {};
              return jsonParam;
            } catch (error) {
              return {};
            }
          }),
          catchError(() => of({})),
        ),
      dataValue: this._service
        .getObject(
          `/api/report-parameter/detail/${item.sourceJobId}`,
          filterDataValue,
        )
        .pipe(
          mapRxjs((res: NzSafeAny) => {
            if (!res) return item ?? {};
            return res;
          }),
          catchError(() => of(item ?? {})),
        ),
    });
    // return this._service
    //   .getList(`/api/report-storages`, undefined, undefined, [
    //     {
    //       field: 'search',
    //       operator: '$eq',
    //       value: reportCode,
    //     },
    //   ])
    //   .pipe(
    //     mapRxjs((res: NzSafeAny) => {
    //       if (!res) return [];
    //       const getItem = res.find(
    //         (item: NzSafeAny) => item.functionCode === reportCode,
    //       );

    //       if (!getItem) return [];
    //       try {
    //         const reportFilter = getItem['reportFilter'];
    //         const jsonParam = reportFilter ? JSON.parse(reportFilter) : {};
    //         return jsonParam;
    //       } catch (error) {
    //         return {};
    //       }
    //     }),
    //     catchError(() => of({})),
    //   );
  }

  widgetOptions = computed(() => {
    return this.layoutOptions()?.widget_options ?? {};
  });

  getSubText(title: string) {
    const options = this.widgetOptions().empty;
    // const headingText = options?.text ?? 'No ' + title + ' data yet';
    const headingText = 'No information yet';
    const subText = options?.sub_text ?? 'Add new';

    return {
      headingText,
      subDescriptionText: subText,
    };
  }

  dataPermission: NzSafeAny = undefined;
  // #endregion
  ngAfterViewInit() {
    this.handleActionByParam();
    if (this.tableScrollHeightOption() || this.expandFilter()) return;
    this.setupResizeObserver();
  }

  checkDataWithChildAction(d: NzSafeAny) {
    const childrenActions = this.childrenActions();
    if (!d) return;
    if (childrenActions && childrenActions.length > 0) {
      const data = d.data.map((item: NzSafeAny) => {
        const faceCode = (item as Data)['faceCode'];

        if (faceCode)
          item.actions = childrenActions.find(
            (action) => action.faceCode === faceCode,
          )?.actions;
        return item;
      });
      d.data = data;
      return d;
    }

    return d;
  }

  isDidSetOriginTotal = false;

  setDataWithChildAction() {
    const d = this.checkDataWithChildAction(this.dataPermission);
    let data = [];
    // in case response only have array, no data or total infos
    if (Array.isArray(d)) {
      data = d;
      this.total.set(d.length);
    } else {
      data = d.data;
      this.total.set(d.total);
    }

    if (!this.isDidSetOriginTotal) {
      this.isDidSetOriginTotal = true;
      const total = Array.isArray(d) ? d.length : d.total;
      this.originTotal.set(total);
    }

    // use _id to check condition of row action instead of id, because some data don't have id to check
    data = data.map((item: NzSafeAny) =>
      this.createRecordCompositeKey(item, this.recordCompositeKey()),
    );
    this.data.set(data);
    // share data between layouts in the same route for handle page header buttons condition
    this.layoutDataService?.update({ data: d.data });
  }

  formValueStorage = computed(() => {
    const storage = this.layoutOptions()?.form_value_storage ?? [];
    if (storage.length <= 0) return;
    const dialogType = this.dialogType();
    return storage.find((s) => s.formType === dialogType);
  });

  recordCompositeKey = computed(
    () => this.layoutOptions()?.record_composite_key ?? { name: '_id' },
  );
  recordCompositeKeyName = computed(() => this.recordCompositeKey().name);
  createRecordCompositeKey(item: Data, config: RecordCompositeKey) {
    let compositeKey = config.keys_mapping
      ?.map((key) => item[key])
      .filter((value) => !isNil(value))
      .join(config.join_by ?? '');
    if (!compositeKey) {
      compositeKey = uniqueId();
    }
    item[config.name] = compositeKey;
    return item;
  }

  showRowAction(item: Data, action: LayoutButton) {
    return (
      (this.actionOneCondition()?.[item[this.recordCompositeKeyName()]]?.[
        action.id
      ] ??
        true) &&
      this.checkPermission(action.id, item)
    );
  }

  disabledRowAction(item: Data, action: LayoutButton) {
    return (
      this.actionOneDisabled()?.[item[this.recordCompositeKeyName()]]?.[
        action.id
      ] ?? false
    );
  }

  filterRowActionChildrenWithPermission(
    row: Data,
    rowActions: RowActionsData[],
  ) {
    return rowActions.filter((item) => this.checkPermission(item.id, row));
  }

  /**
   * Batch-generate avatar links for each row in the table data.
   * Updates each row's avatarLink property asynchronously as the link becomes available.
   */
  private avatarLinkCache = new Map<string, Promise<string>>();

  private generateAvatarLinksForRows() {
    // Clear cache before each batch to avoid memory leaks and ensure fresh data
    this.avatarLinkCache.clear();
    const rows = this.data();
    if (!Array.isArray(rows)) return;
    const faceCodeValue =
      typeof this.faceCode() === 'string' ? this.faceCode() : '';
    rows.forEach((row, idx) => {
      const avatarFile =
        typeof row['avatarFile'] === 'string' && row['avatarFile']
          ? row['avatarFile']
          : undefined;
      if (typeof avatarFile === 'string' && avatarFile.length > 0) {
        let linkPromise = this.avatarLinkCache.get(avatarFile);
        if (!linkPromise) {
          linkPromise = this._service
            .generateAvatarLink(avatarFile, faceCodeValue || '')
            .catch((err) => {
              this.avatarLinkCache.delete(avatarFile);
              // Optionally log error or show a default avatar
              return '';
            });
          this.avatarLinkCache.set(avatarFile, linkPromise);
        }
        linkPromise.then((avatarLink) => {
          const updatedRows = [...this.data()];
          updatedRows[idx] = { ...row, avatarLink };
          this.data.set(updatedRows);
        });
      }
    });
  }
}

enum PREVIEW_TYPE {
  TABLE = 'table',
  CV = 'cv',
  TAB_TABLE = 'tab-table',
  FORM = 'form',
  TABLE_EXPAND = 'table-expand',
}

function mappingConfig(
  array: FieldGroupConfig[],
  variables: Record<string, NzSafeAny> = {},
  variablesMapping: Record<string, string> = {},
  obj: Record<
    string,
    {
      type: string;
      label: string;
      format: string;
      options?: { value: NzSafeAny; label: string }[];
    }
  > = {},
) {
  for (const field of array) {
    const item = field as NzSafeAny;
    if (item.name) {
      let options = item?.['options'];
      let format = item.setting?.format ?? item?.format;
      if (item.type === 'radio' && item.radio) {
        options = item.radio;
      }

      if (item.type === 'number') {
        format = item.number?.format;
      }

      const variablesMappingKey = variablesMapping[item.name];
      if (variablesMappingKey) {
        options = variables[variablesMappingKey];
      }

      obj[item.name] = {
        type: item.type,
        label: item.label ?? '',
        format,
        options,
      };
    }

    if (item?.fields) {
      mappingConfig(
        item.fields as FieldGroupConfig[],
        variables,
        variablesMapping,
        obj,
      );
    }
  }

  return obj;
}
