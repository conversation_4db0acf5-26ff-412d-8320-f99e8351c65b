import { Component, inject, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { IconComponent } from '@hrdx/hrdx-design';
import { BffService } from '@hrdx-fe/shared';
import { HttpClient } from '@angular/common/http';
@Component({
  selector: 'app-import-employee-title',
  standalone: true,
  imports: [CommonModule, NzIconModule, IconComponent],
  template: `
    <div class="modal-title">
      <span>Import file</span>
      <a
        (click)="downloadTemplate()"
        *ngIf="checkPermission('export-template')"
      >
        <hrdx-icon icon="icon-download-simple"></hrdx-icon>
        Export template
      </a>
    </div>
  `,
  styleUrl: './import-employee-title.component.less',
})
export class ImportEmployeeTitleComponent {
  http = inject(HttpClient);

  service = inject(BffService);

  @Input({ required: true }) checkPermission!: (permission: string) => boolean;

  downloadTemplate(): void {
    this.service
      .exportReport('/api/delete-employee-infos/download-template')
      .subscribe((res) => {});
  }
}
