<ng-container>
  <nz-layout>
    <nz-content class="content">
      <!-- TODO: create another layout for expand filter if needed -->
      <h3 class="matching-title" *ngIf="expandFilter()">
        Matching {{ pageHeader().title }}
      </h3>
      <div class="tool-table">
        <div class="left">
          <div style="width: 300px" *ngIf="showTableSearch()">
            <nz-input-group [nzPrefix]="suffixIconSearch">
              <input
                type="text"
                nz-input
                placeholder="Search"
                class="search-input"
                [ngModel]="searchValue()"
                (ngModelChange)="onSearch($event)"
              />
            </nz-input-group>
            <ng-template #suffixIconSearch>
              <span class="search-icon">
                <hrdx-icon [icon]="'icon-magnifying-glass'"></hrdx-icon>
              </span>
            </ng-template>
          </div>
          <hrdx-badge
            [status]="4"
            [count]="filterCount()"
            *ngIf="!expandFilter()"
          >
            <hrdx-button
              [type]="'tertiary'"
              [onlyIcon]="true"
              icon="icon-funnel-simple"
              (clicked)="filterClickOne(filterValue())"
              *ngIf="showTableFilter()"
            />
          </hrdx-badge>

          <hrdx-button
            [nzDropdownMenu]="groupMenu"
            [nzTrigger]="'click'"
            [nzPlacement]="'bottomLeft'"
            nz-dropdown
            [title]="groupInfo().title"
            [isLeftIcon]="true"
            [leftIcon]="groupInfo().icon"
            [type]="groupInfo().type"
            [isRightIcon]="groupLabel.length > 0"
            [rightIcon]="'x'"
            (clickedRightIcon)="removeGroupByKey($event)"
            *ngIf="showTableGroup()"
          />
          <nz-dropdown-menu #groupMenu="nzDropdownMenu">
            <ul nz-menu class="list-dropdown-group">
              <nz-radio-group
                [(ngModel)]="selectedGroupKey"
                (ngModelChange)="groupItem($event)"
              >
                @for (h of allHeaders(); track $index) {
                  <li class="list-radio">
                    <label nz-radio [nzValue]="h?.code">
                      {{ h?.title }}
                    </label>
                  </li>
                }
              </nz-radio-group>
            </ul>
          </nz-dropdown-menu>
        </div>
        <div class="right">
          @for (tool of toolTable(); track tool.id) {
            <hrdx-button
              [type]="tool.type ?? 'tertiary'"
              [onlyIcon]="true"
              [icon]="tool.icon"
              (clicked)="onToolTableClick(tool.id)"
              *ngIf="checkPermission(tool.id)"
            >
            </hrdx-button>
          }
          <!-- <hrdx-button [type]="'tertiary'" [nzDropdownMenu]="menu" [nzTrigger]="'click'"
                        [nzPlacement]="'bottomLeft'" nz-dropdown [onlyIcon]="true" [icon]="'gear'">
                    </hrdx-button> -->
          <nz-dropdown-menu #menu="nzDropdownMenu">
            <ul
              nz-menu
              style="
                max-height: 500px;
                overflow: auto;
                display: flex;
                flex-direction: column;
              "
            >
              @for (h of allHeaders(); track $index) {
                <li
                  style="
                    width: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    padding: 8px 16px;
                  "
                >
                  <span>{{ h.title }}</span>
                  <nz-switch
                    class="switch"
                    [ngModel]="!isOff(h.code)"
                    (ngModelChange)="switchChange(h.code)"
                  ></nz-switch>
                </li>
              }
            </ul>
          </nz-dropdown-menu>
        </div>
      </div>

      <div *ngIf="isValidFilterValue() && !expandFilter()">
        <hrdx-data-render
          [filterLst]="filterDataRenderValue()"
          [filterConfig]="filterConfigMapping()"
          (removedFilterItem)="removedFilterItem($event)"
          #filterDataRender
        />
      </div>
      <span class="filter-result-messages" *ngIf="showFilterResultsMessage()">
        {{ total() ?? 0 }} record(s) was found
      </span>
      <hrdx-new-table
        [data]="data()"
        [total]="total()"
        [loading]="loading()"
        [pageIndex]="pageIndex()"
        [pageSize]="pageSize()"
        (selectedItemChange)="listOfSelectedItems.set($event)"
        (pageSizeChange)="onPageSizeChange($event)"
        (pageIndexChange)="onPageIndexChange($event)"
        [showCheckbox]="showTableCheckbox()"
        [showCreateDataTable]="showCreateDataButton()"
        [class.group-table]="groupedData?.length"
      >
        <hrdx-thead>
          @for (column of headers(); track $index) {
            <hrdx-th
              [width]="column.options?.tabular?.column_width"
              [fixedLeft]="column.pinned"
              [align]="column.options?.tabular?.align ?? 'left'"
            >
              {{ column.title }}
            </hrdx-th>
          }
        </hrdx-thead>
        <ng-container *ngIf="groupedData?.length; else defaultCase">
          @for (data of groupedData; track data.key) {
            <hrdx-tbody [hiddenAction]="true">
              <hrdx-td [colSpan]="headers().length" className="group-header">
                {{ groupLabel }}: {{ data.key }}
              </hrdx-td>
            </hrdx-tbody>
            @for (row of data.items; track $index) {
              <hrdx-tbody (clickRow)="handleClickRow(row)">
                @for (column of headers(); track column.code) {
                  <hrdx-td>
                    <hrdx-display
                      [type]="column?.display_type?.key || 'Label'"
                      [value]="row[column.code]"
                      [title]="column.title"
                      [href]="column.href"
                    ></hrdx-display>
                  </hrdx-td>
                }
                <ng-container row-actions *ngIf="!hideRowAction()">
                  @if (actionOne()) {
                    @for (action of actionOne(); track action.id) {
                      <hrdx-button
                        [type]="action.type"
                        [icon]="action.icon"
                        [onlyIcon]="true"
                        [size]="'small'"
                        (clicked)="onActionOneClick(row, action.id, $event)"
                      />
                    }
                  } @else {
                    <!-- <hrdx-button [type]="'ghost-gray'" [size]="'small'" [onlyIcon]="true" [icon]="'eye'"
                                (clicked)="viewClickOne(row.id, row, $event)" />
                            <hrdx-button [type]="'ghost-gray'" [size]="'small'" [onlyIcon]="true" [icon]="'pencil'"
                                (clicked)="editClickOne(row.id, row, $event)" />
                            <hrdx-button [type]="'ghost-gray'" [size]="'small'" [onlyIcon]="true"
                                [icon]="'lock-keyhole'" />
                            <hrdx-button [type]="'ghost-gray'" [size]="'small'" [onlyIcon]="true" [icon]="'trash'"
                                (clicked)="deleteClickOne(row, $event)" /> -->
                  }
                </ng-container>
              </hrdx-tbody>
            } @empty {}
          } @empty {}
        </ng-container>
        <ng-template #defaultCase>
          @for (row of data(); track $index) {
            <hrdx-tbody
              (clickRow)="handleClickRow(row)"
              [disabled]="row['disabled']"
            >
              @for (column of headers(); track $index) {
                <hrdx-td>
                  <hrdx-display
                    [type]="column?.display_type?.key || 'Label'"
                    [value]="row[column.code]"
                    [title]="column.title"
                    (changeValue)="onChangeValueCell($event, row, column.code)"
                    [href]="column.href"
                  ></hrdx-display>
                </hrdx-td>
              }
              <ng-container row-actions *ngIf="!hideRowAction()">
                @if (actionOne()) {
                  @for (action of actionOne(); track action.id) {
                    <hrdx-button
                      [type]="action.type"
                      [icon]="action.icon"
                      [onlyIcon]="true"
                      [size]="'small'"
                      (clicked)="onActionOneClick(row, action.id, $event)"
                      *ngIf="
                        actionOneCondition()?.[row.id]?.[action.id] ?? true
                      "
                    />
                  }
                } @else {
                  <hrdx-button
                    [type]="'ghost-gray'"
                    [size]="'small'"
                    [onlyIcon]="true"
                    [icon]="'icon-trash'"
                    (clicked)="deleteClickOne(row, $event)"
                    title="Delete"
                    *ngIf="row['status']?.value !== 'Y'"
                  />
                }
              </ng-container>
            </hrdx-tbody>
          }
        </ng-template>

        <ng-container selected-actions>
          @if (actionsMany()) {
            @for (action of actionsMany(); track action.id) {
              <hrdx-button
                [type]="action.type"
                [title]="action.title"
                [leftIcon]="action.icon"
                [isLeftIcon]="true"
                [size]="'xsmall'"
                (clicked)="onActionsManyClick(action.id)"
              />
            }
          } @else {
            <hrdx-button
              [type]="'secondary'"
              [size]="'xsmall'"
              [leftIcon]="'icon-play'"
              [isLeftIcon]="true"
              [title]="'Run'"
              (clicked)="runDeleteClickMany()"
              *ngIf="!hiddenActionsMany() && checkPermission('delete')"
            />
            &nbsp;
            <hrdx-button
              [type]="'secondary'"
              [size]="'xsmall'"
              [leftIcon]="'icon-trash'"
              [isLeftIcon]="true"
              [title]="'Remove Row'"
              (clicked)="deleteClickMany()"
              *ngIf="!hiddenActionsMany()"
            />
          }
        </ng-container>
      </hrdx-new-table>
      <!-- {{ data() | json }} -->
    </nz-content>
    <nz-footer *ngIf="pageFooterOptions()?.visible">
      <ng-container [ngTemplateOutlet]="footer"></ng-container>
    </nz-footer>
  </nz-layout>
</ng-container>

<ng-template #footer>
  <!-- TODO: should move to hrdx-design component -->
  <div class="footer-wrapper" *ngIf="pageFooterButtons()?.length">
    <div class="footer-btns">
      @for (button of pageFooterButtons(); track button.id) {
        <hrdx-button
          [title]="button.title ?? ''"
          [type]="button.type"
          (clicked)="pageFooterButtonClicked(button.id)"
          [size]="button.size"
          [leftIcon]="button.leftIcon"
          [isLeftIcon]="button.isLeftIcon"
        >
        </hrdx-button>
      }
    </div>
  </div>
</ng-template>

<ng-template #modalTitle>
  <app-import-employee-title
    [checkPermission]="checkPermission"
  ></app-import-employee-title>
</ng-template>

<lib-layout-dialog
  [dialogVisible]="dialogVisible()"
  (dialogVisibleChange)="onDialogVisibleChange($event)"
  [config]="dialogConfig()"
  [dialogType]="dialogType()"
  [id]="dialogValue()?.[fsKey()]"
  [url]="''"
  [customDetailBackendUrl]="''"
  [addOnValue]="addOnValue()"
  [value]="dialogValue()"
  [title]="dialogTitle()"
  [showSaveAddButton]="showDialogSaveAddBtn()"
  [showDeleteButton]="showDeleteButton()"
  [footerButtonsCustom]="footerButtonsCustom()"
  (submitValue)="dialogSubmit($event)"
  (clickedModalButton)="clickedModalButton($event)"
  [showFooter]="true"
  [isViewDetailConfig]="false"
  [showSubmitButton]="true"
  [checkPermission]="checkPermission"
  [disabledActionLst]="disabledActionLst()"
  [maskClosable]="false"
  [isPopup]="isPopup()"
  [dataLayout]="dataLayout()"
  [showAvatarInfo]="!!dataLayout()?.['dataProfile']"
  [isNewDynamicForm]="true"
  [faceCode]="faceCode()"
  #layoutDialog
  [centered]="true"
  *ngIf="dialogVisible()"
>
</lib-layout-dialog>
