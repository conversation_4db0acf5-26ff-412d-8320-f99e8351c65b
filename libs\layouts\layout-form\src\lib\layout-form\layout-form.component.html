<nz-layout class="layout-form">
  <nz-content class="content" [ngStyle]="customStyleContent()">
    <ng-container [ngTemplateOutlet]="content"></ng-container>
  </nz-content>
</nz-layout>

<ng-template #content>
  <div class="form-wrapper" [ngStyle]="customStyleFormWrapper()">
    @if (!loading()) {
      <dynamic-form
        [config]="formConfig().fields ?? []"
        [sources]="formConfig()?.sources ?? {}"
        [variables]="formConfig()?.variables ?? {}"
        [formValue]="valueData()"
        [readOnly]="formConfig()?.footer ? true : false"
        [ppxClass]="'ppxm-style'"
        [extend]="{
          formType: 'proceed',
          layoutData: layoutData(),
          defaultValue: valueData(),
        }"
        [hideFooter]="true"
        [reload]="resetForm()"
        [_mode]="formMode()"
        [footer]="formConfig()?.footer"
        (reloadFormValue)="refreshData.set(!refreshData())"
        [authAction]="AuthActions.Create"
        [checkPermissionActionFn]="this.checkPermission"
        #formObj
      ></dynamic-form>
    } @else {
      <ng-container [ngTemplateOutlet]="skeleton"></ng-container>
    }
  </div>
  <div class="footer-wrapper" *ngIf="showPageFooter()">
    @if (checkPermission('create') && pageFooterButtons().length > 0) {
      <div class="footer-btns">
        @for (button of pageFooterButtons(); track button.id) {
          <hrdx-button
            [title]="button.title ?? ''"
            [type]="button.type"
            (clicked)="pageFooterButtonClicked(button.id)"
            [size]="button.size"
            [leftIcon]="button.leftIcon"
            [isLeftIcon]="button.isLeftIcon"
            [isLoading]="
              (button.id === 'proceed' || button.id === 'save') && isLoading()
            "
          >
          </hrdx-button>
        }
      </div>
    }
    @if (showCredit()) {
      <hrdx-credit-footer
        [footerConfig]="footerConfig()"
        [value]="valueData()"
      />
    }
  </div>
</ng-template>

<lib-layout-dialog
  [(dialogVisible)]="dialogVisible"
  [config]="functionSpec().form_config"
  [value]="dialogFormValue()"
  [loadAfterOpen]="dialogType() !== 'view'"
  [showSaveAddButton]="showDialogFormSaveAddBtn()"
  [noNeedConfirm]="noNeedConfirm()"
  [showAvatarInfo]="true"
  [maskClosable]="false"
  [url]="''"
  [title]="functionSpec().form_config?.title ?? functionSpec().title"
  (submitValue)="onSubmit($event)"
  [dialogType]="'create'"
  [dataLayout]="_dataLayout()"
  [checkPermission]="checkPermission"
  [disabledActionLst]="disabledActionLst()"
  [isNewDynamicForm]="isNewDynamicForm()"
  *ngIf="dialogVisible"
></lib-layout-dialog>

<ng-template #skeleton>
  <div class="skeleton-container">
    <nz-skeleton [nzActive]="true"></nz-skeleton>
    <nz-skeleton [nzActive]="true"></nz-skeleton>
    <nz-skeleton [nzActive]="true"></nz-skeleton>
    <nz-skeleton [nzActive]="true"></nz-skeleton>
  </div>
</ng-template>
