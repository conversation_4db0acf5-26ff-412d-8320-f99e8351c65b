import { CommonModule } from '@angular/common';
import {
  AfterViewInit,
  Component,
  computed,
  effect,
  EventEmitter,
  inject,
  OnDestroy,
  OnInit,
  Output,
  signal,
  ViewChild,
} from '@angular/core';
import { ActivatedRoute, Data, Router } from '@angular/router';
import { DynamicFormService, FormComponent } from '@hrdx-fe/dynamic-features';
import {
  AuthActions,
  BffService,
  FilterService,
  LayoutCommon,
  LayoutCommonComponent,
  LayoutStore,
  mappingUrl,
  MasterdataService,
  MenuItem,
  UtilService,
} from '@hrdx-fe/shared';
import {
  ButtonComponent,
  ButtonSchema,
  CreditFooterComponent,
  ModalComponent,
  PageHeader,
  ToastMessageComponent,
} from '@hrdx/hrdx-design';
import { capitalize, isArray, isEmpty, isNil } from 'lodash';
import { NzSafeAny } from 'ng-zorro-antd/core/types';
import { NzLayoutModule } from 'ng-zorro-antd/layout';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { catchError, lastValueFrom, of, switchMap, tap } from 'rxjs';
import { LayoutFormStore } from './layout-form.store';
import { QueryFilter } from '@nestjsx/crud-request';
import { NzSpinModule } from 'ng-zorro-antd/spin';
// eslint-disable-next-line @nx/enforce-module-boundaries
import { LayoutDialogComponent } from '@hrdx-fe/layout-simple-table';
import { NzSkeletonModule } from 'ng-zorro-antd/skeleton';

//TODO: should define another place instead of here
type Modal = {
  title: string;
  type?: string;
  content?: string;
  onConfirm?: FormAction;
};
type Toast = {
  title: string;
  type: string;
  content?: string;
  _content?: string;
};
type FormAction = {
  id: string;
  type: 'modal' | 'navigate' | 'confirm' | 'toast' | 'default';
  modal?: Modal;
  toast?: Toast;
  link?: string;
  paramsUrl?: NzSafeAny;
};

//TODO: should define another place instead of here
function getValue(data: NzSafeAny, path: (string | number)[]): NzSafeAny {
  if (!data) {
    return undefined;
  }
  if (path.length < 1) return undefined;
  if (path.length === 1) {
    const tmp = path[0];
    if (typeof tmp === 'string' && new RegExp(/\([a-z0-9]*\)/, 'g').test(tmp)) {
      if (isArray(data)) {
        return data.map((it) => it[tmp.replace('(', '').replace(')', '')]);
      }
    }
    return data[tmp];
  } else {
    const thisPath = path.shift();
    if (!thisPath) return undefined;
    const tmp = thisPath;
    if (typeof tmp === 'string' && new RegExp(/\([a-z0-9]*\)/, 'g').test(tmp)) {
      if (isArray(data)) {
        return getValue(
          data.map((it) => it[tmp.replace('(', '').replace(')', '')]),
          path,
        );
      }
    }
    return getValue(data[thisPath], path);
  }
}

@Component({
  selector: 'lib-layout-form',
  standalone: true,
  imports: [
    CommonModule,
    NzLayoutModule,
    FormComponent,
    ButtonComponent,
    NzModalModule,
    LayoutDialogComponent,
    NzSkeletonModule,
    NzSpinModule,
    CreditFooterComponent,
  ],
  templateUrl: './layout-form.component.html',
  styleUrl: './layout-form.component.less',
  providers: [LayoutFormStore, ModalComponent, ToastMessageComponent],
})
export class LayoutFormComponent
  extends LayoutCommonComponent
  implements LayoutCommon, AfterViewInit, OnInit, OnDestroy
{
  router = inject(Router);
  readonly defaultFooterButtons: (ButtonSchema & { id: string })[] = [
    {
      id: 'cancel',
      type: 'secondary',
      size: 'default' as ButtonSchema['size'],
      state: 'default' as ButtonSchema['state'],
      title: 'Cancel',
    },
    {
      id: 'save',
      type: 'primary',
      size: 'default' as ButtonSchema['size'],
      state: 'default' as ButtonSchema['state'],
      title: 'Save',
    },
  ];
  #store = inject(LayoutFormStore);
  #layoutStore = inject(LayoutStore);
  metadataService = inject(MasterdataService);
  filterService = inject(FilterService);
  service = inject(BffService);
  filterValue = signal<NzSafeAny>(null);
  dynamicService = inject(DynamicFormService);
  isLoading = signal(false);
  AuthActions = AuthActions;
  @Output() submitDialog = new EventEmitter();
  utilService = inject(UtilService);
  constructor(private route: ActivatedRoute) {
    super();
  }

  dialogType = signal<'view' | 'edit' | 'create' | 'proceed'>('proceed');
  capitalize = capitalize;

  effectFunctionSpec = effect(
    () => this.#store.setCurrentFunctionSpec(this.functionSpec()),
    { allowSignalWrites: true },
  );

  currentModule = this.#layoutStore.currentModuleId;

  @ViewChild('formObj') dynamicForm?: FormComponent;
  modalComponent = inject(ModalComponent);
  toast = inject(ToastMessageComponent);

  resetForm = signal(false);
  layoutData = signal(this.layoutDataService.getData());

  formMode = computed(() => {
    return this.functionSpec()?.form_config?._mode;
  });

  formConfig = computed(() => {
    const fs = this.functionSpec();
    return fs.create_form ?? fs?.form_config ?? {};
  });

  formType = computed(() => {
    return isEmpty(this.valueData()) ? 'create' : 'edit';
  });

  layoutOptions = computed(() => {
    return this.functionSpec().layout_options;
  });

  pageHeaderOptions = computed(() => {
    return this.layoutOptions()?.page_header_options;
  });

  pageFooterOptions = computed(() => {
    return this.layoutOptions()?.page_footer_options;
  });

  showCredit = computed(() => {
    return this.pageFooterOptions()?.show_credit ?? true;
  });

  showPageFooter = computed(() => {
    return this.pageFooterOptions()?.visible ?? true;
  });

  footerConfig = computed(() => {
    return this.formConfig()?.footer;
  });

  backendUrl = computed(() => {
    return this.url();
  });

  showDialogFormSaveAddBtn = computed(() => {
    return (
      this.functionSpec()?.layout_options?.show_dialog_form_save_add_button ??
      false
    );
  });

  noNeedConfirm = computed(() => {
    return this.functionSpec()?.layout_options?.no_need_confirm ?? false;
  });

  customStyleContent = computed(() => {
    return this.layoutOptions()?.customStyleContent ?? {};
  });
  customStyleFormWrapper = computed(() => {
    return this.layoutOptions()?.customStyleFormWrapper ?? {};
  });

  pageHeader = computed<PageHeader>(() => {
    const fs = this.functionSpec();
    const module = this.currentModule();
    const breadcrumb = [
      module,
      (fs.menu_item?.parent as MenuItem)?.title ?? '',
      fs.menu_item?.title ?? '',
    ].map((title) => ({ title }));

    const buttons = (fs.layout_options?.header_buttons ?? []).map((btn) => ({
      id: btn.id,
      type: btn.type,
      size: 'default' as ButtonSchema['size'],
      state: 'default' as ButtonSchema['state'],
      title: btn.title,
      leftIcon: btn.icon,
      isLeftIcon: btn.icon ? true : false,
    }));

    return {
      title: fs.title ?? '',
      breadcrumb: breadcrumb,
      buttons: buttons,
    };
  });

  // config for new dynamic form
  isNewDynamicForm = computed(() => {
    const layoutOptions = this.functionSpec()?.layout_options;
    return (layoutOptions as NzSafeAny)?.is_new_dynamic_form ?? false;
  });

  showModalConfirmCancel = computed(() => {
    const layoutOptions = this.functionSpec()?.layout_options;
    return (layoutOptions as NzSafeAny)?.show_modal_confirm_cancel ?? false;
  });

  override pageHeaderButtonClicked(id: string) {
    const formValue = this.dynamicForm?.value ?? {};
    switch (id) {
      case 'cancel': {
        this.modalComponent.showDialog({
          nzTitle: 'Please confirm',
          nzContent: 'You have unsaved changes. All your changes will be lost.',
          nzWrapClassName: 'popup popup-confirm',
          nzIconType: 'icons:warning',
          nzOkText: 'Confirm',
          nzCancelText: 'Cancel',
          nzOnOk: () => {
            this.resetForm.update((prev) => !prev);
          },
        });

        break;
      }
      case 'reset': {
        this.onReset();

        break;
      }
      case 'save': {
        if (!this.dynamicForm?.valid) {
          this.handleNotiRequire();
          return;
        }
        if (this.backendUrl()) {
          this.handleOnSave(formValue);
          break;
        }
        this.toast.showToast('success', '', 'Saved Successfully');
        break;
      }
      case 'apply': {
        if (!this.dynamicForm?.valid) {
          this.handleNotiRequire();
          return;
        }
        if (this.backendUrl()) {
          this.handleOnSave(formValue, 'apply');
          break;
        }
        this.toast.showToast('success', '', 'Saved Successfully');
        break;
      }
      case 'proceed': {
        if (!this.dynamicForm?.valid) {
          this.handleNotiRequire();
          return;
        }
        this.handleProceed(formValue);
        break;
      }
    }
  }

  pageFooterButtons = computed<(ButtonSchema & { id: string })[]>(() => {
    const buttons = this.functionSpec()?.layout_options?.footer_buttons;
    if (!buttons) return this.defaultFooterButtons;
    return buttons.map((btn) => ({
      id: btn.id,
      type: btn.type,
      size: 'default' as ButtonSchema['size'],
      state: 'default' as ButtonSchema['state'],
      title: btn.title,
      leftIcon: btn.icon,
      isLeftIcon: btn.icon ? true : false,
    }));
  });

  pageFooterButtonClicked(id: string) {
    const formValue = this.dynamicForm?.value ?? {};
    switch (id) {
      case 'cancel': {
        this.onCancelForm();
        break;
      }
      case 'save': {
        if (!this.dynamicForm?.valid) {
          this.handleNotiRequire();
          return;
        }
        if (this.backendUrl()) {
          this.handleOnSave(formValue);
          break;
        }
        this.toast.showToast('success', '', 'Saved Successfully');
        break;
      }
      case 'proceed': {
        if (!this.dynamicForm?.valid) {
          this.handleNotiRequire();
          return;
        }
        this.handleProceed(formValue);
        break;
      }
    }
  }

  handleNotiRequire() {
    this.dynamicForm?.setFormTouched();
    // this.toast.showToast(
    //   'error',
    //   'Error',
    //   'Required fields have not been filled in completely (fields marked with *) or the format of the fields not correctly!',
    // );
    return;
  }

  handleProceed(formValue: NzSafeAny) {
    this.onProceed(formValue);
  }

  valueData = signal<Data | null | undefined>(null);
  refreshData = signal(false);
  loading = signal(false);
  dataEffect = effect(
    () => {
      this.refresh();
      this.refreshData(); // do not remove this line
      const url = this.backendUrl();
      if (!url) {
        this.valueData.set(this.functionSpec()?.mock_data?.[0] ?? {});
        return;
      }

      of(url)
        .pipe(
          tap(() => this.loading.set(true)),
          switchMap((url) =>
            this.service.getObject(
              url,
              this.filterQuery(),
              this.faceCode() ?? undefined,
            ),
          ),
          catchError((err) => {
            this.loading.set(false);
            this.toast.showToast('error', 'Error', err?.error?.message ?? err);
            return of(null);
          }),
          tap((data) => {
            this.valueData.set(data);
            this.resetForm.update((val) => !val);
          }),
          tap(() => this.loading.set(false)),
        )
        .subscribe();
    },
    { allowSignalWrites: true },
  );

  async handleOnSave(
    value: NzSafeAny,
    formType?: string,
    url?: string,
    callback?: (success: boolean) => void,
  ) {
    url = mappingUrl(url ?? this.backendUrl() ?? '', value);
    if (!url) return;
    formType = formType ?? this.formType();
    switch (formType) {
      case 'create': {
        const isActionWarning = await this.utilService.transformRedirectTo(
          value,
          this.functionSpec()?.form_config?._isActionWarning,
        );
        if (isActionWarning) {
          this.onConfirm('warning', undefined, (success: boolean) => {
            success ? this.onCreate(url, value, callback) : callback?.(false);
            return;
          });
        } else {
          this.onCreate(url, value, callback);
        }
        break;
      }

      case 'edit': {
        this.onEdit(url, value);
        break;
      }

      case 'apply': {
        this.onApply(url, value);
        break;
      }
    }
  }

  onCreate(
    url: string,
    value: NzSafeAny,
    callback?: (success: boolean) => void,
  ) {
    this.isLoading.set(true);
    if (this.functionSpec().layout_options?.is_upload_file) {
      this.service
        .createFormData(url, value, this.faceCode() ?? undefined)
        .subscribe({
          next: () => {
            callback?.(true);
            this.isLoading.set(false);
            this.handleSuccess();
          },
          error: (err) => {
            callback?.(false);
            this.isLoading.set(false);
            this.handleError(err.error?.message || err?.message);
          },
        });
    } else {
      this.service
        .createItem(url, value, this.faceCode() ?? undefined)
        .subscribe({
          next: () => {
            callback?.(true);
            this.isLoading.set(false);
            this.handleSuccess();
          },
          error: (err) => {
            callback?.(false);
            this.isLoading.set(false);
            this.handleError(err.error?.message);
          },
        });
    }
  }

  onEdit(url: string, value: NzSafeAny) {
    this.isLoading.set(true);
    this.service
      .update(url, value, undefined, this.faceCode() ?? undefined)
      .subscribe({
        next: () => {
          this.handleSuccess();
        },
        error: (err) => {
          this.handleError(err);
        },
      });
  }

  //TODO - action for General settings
  onApply(url: string, value: NzSafeAny) {
    this.#layoutStore.setLoading(true);
    this.service
      .updateItem(
        url,
        this.valueData()?.['id'],
        value,
        undefined,
        this.faceCode() ?? undefined,
      )
      .subscribe({
        next: () => {
          this.#layoutStore.setLoading(false);
          this.refreshData.update((e) => !e);
          this.toast.showToast('success', 'Success', 'Record saved');
        },
        error: (err) => {
          this.#layoutStore.setLoading(false);
          this.handleError(err);
        },
      });
  }

  //TODO - action for General settings
  handleReset() {
    const url = 'api/general-system-settings/get-default-setting';
    this.#layoutStore.setLoading(true);
    this.service
      .getObject(url, undefined, this.faceCode() ?? undefined)
      .subscribe({
        next: (data) => {
          this.#layoutStore.setLoading(false);
          this.valueData.update((prev) => ({
            ...data,
            id: prev?.['id'] ?? data?.id,
          }));
          this.resetForm.update((val) => !val);
        },
        error: (err) => {
          this.#layoutStore.setLoading(false);
          this.handleError(err);
        },
      });
  }

  handleSuccess() {
    this.isLoading.set(false);
    if (
      this.functionSpec()?.form_config?.actions &&
      this.functionSpec()?.form_config?.actions.length > 0
    ) {
      this.onConfirm('success');
    } else {
      this.toast.showToast('success', 'Success', 'Record saved');
      this.dialogVisible = false;
      this.resetForm.update((val) => !val);
    }
  }

  handleError(errorMessage: string) {
    this.isLoading.set(false);
    if (
      this.functionSpec()?.form_config?.actions &&
      this.functionSpec()?.form_config?.actions.length > 0
    ) {
      this.onConfirm('error', errorMessage);
    } else {
      this.toast.showToast('error', 'Error', errorMessage);
    }
  }

  onSubmit(e: {
    type: string;
    value: NzSafeAny;
    callback?: (success: boolean) => void;
  }) {
    const url = this.functionSpec()?.form_config?.backendUrl || this.url();
    if (url) {
      this.handleOnSave(e?.value, e?.type, url, e.callback);
    } else {
      this.onConfirm(undefined);
    }
  }

  dialogVisible = false;
  dialogFormValue = signal<NzSafeAny>({});
  openDialog() {
    this.dialogVisible = true;
  }

  createItem() {
    this.openDialog();
  }
  async onProceed(formValue: NzSafeAny) {
    let isError = false;
    this.isLoading.set(true);
    this.dialogFormValue.set({ ...formValue });
    const requestsOnProceed =
      this.functionSpec()?.create_form?.requestsOnProceed;

    await this.handleRequestsOnProceed(
      formValue,
      requestsOnProceed,
      (err) => (isError = err),
    );

    if (isError) {
      this.isLoading.set(false);
      return;
    }

    let isActionProceed =
      this.functionSpec()?.create_form?.isActionProceed || false;
    const _isActionProceed = await this.utilService.transformRedirectTo(
      this.dialogFormValue(),
      this.functionSpec()?.create_form?._isActionProceed,
    );
    isActionProceed = _isActionProceed;

    const isActionToastError = await this.utilService.transformRedirectTo(
      this.dialogFormValue(),
      this.functionSpec()?.create_form?._isActionToastError,
    );
    const isActionModalWarning = await this.utilService.transformRedirectTo(
      this.dialogFormValue(),
      this.functionSpec()?.create_form?._isActionModalWarning,
    );
    this.filterValue.set(formValue);
    if (isActionToastError) {
      this.onConfirmProceed('error', 'toast');
      this.isLoading.set(false);
      return;
    }
    if (isActionModalWarning) {
      this.onConfirmProceed('warning', 'modal', (res: boolean) => {
        if (res) {
          if (isActionProceed) {
            this.onConfirmProceed('success', 'modal');
          } else {
            this.createItem();
          }
        }
      });
      this.isLoading.set(false);
      return;
    }
    if (isActionProceed) {
      this.onConfirmProceed('success', 'modal');
    } else {
      this.createItem();
    }
    this.isLoading.set(false);
    //   this.dialogFormValue.set(structuredClone(this.dynamicForm?.value ?? {}));
    //   this.createItem();
    //   this.isLoading.set(false);
  }

  async handleRequestsOnProceed(
    formValue: NzSafeAny,
    sourceProceed: NzSafeAny,
    onError: (err: boolean) => boolean,
  ) {
    const sourcesMapping = sourceProceed.sourcesMapping ?? [];
    const sources = sourceProceed.sources ?? {};

    const promises = sourcesMapping.map(async (item: NzSafeAny) => {
      const sourceData = sources[item.key];
      const enabled = await this.transformValue(item.enabled, {
        variables: this.dynamicForm?.variablesSource,
        formType: 'proceed',
        formData: formValue,
      });

      if (sourceData && enabled) {
        return lastValueFrom(
          this.dynamicService.getValueFromApi(
            sourceData,
            { ...formValue, _formType: 'proceed' },
            {
              onError: (err: NzSafeAny) => {
                this.toast.showToast('error', '', err.error?.message);
                onError(true);
              },
            },
            this.faceCode(),
          ),
        )
          .then((data: NzSafeAny) => {
            if (data) {
              this.dialogFormValue.set({
                ...this.dialogFormValue(),
                ...data,
              });
            }
          })
          .catch((err: NzSafeAny) => {
            this.toast.showToast('error', '', err.error?.message);
          });
      }
    });

    await Promise.all(promises);
    return;
  }

  async transformValue(transform: string, value: Record<string, NzSafeAny>) {
    return await this.dynamicService.getJsonataExpression({})(transform, value);
  }

  filterQuery = computed(() => {
    const res =
      this.functionSpec()
        ?.create_form?.filterMapping?.map((f: any) => {
          return {
            field: f.field,
            operator: f.operator,
            value: getValue(this.filterValue(), f.valueField.split('.')),
          } as QueryFilter;
        })
        .filter(
          (f: any) =>
            (!isArray(f.value) && !isNil(f.value) && f.value !== '') ||
            (isArray(f.value) && f.value.length),
        ) ?? [];

    if (this.defaultFilter().length > 0) {
      res.push(...this.defaultFilter());
    }

    return res;
  });

  _dataLayout = computed<Record<string, NzSafeAny> | null>(() => {
    return {
      dataProfile: { ...this.profileCard(), avatarLink: this.avatarLink() },
    };
  });

  profileCard = computed<NzSafeAny>(() => {
    const data: NzSafeAny = this.dialogFormValue();
    return {
      name: data?.fullName,
      company: data?.companyName,
      email: data?.email,
    };
  });

  avatarFile = computed(() => {
    return this.dialogFormValue()?.avatarFile;
  });

  avatarLink = signal<string>('');
  avatarLinkEffect = effect(async () => {
    if (this.avatarFile()) {
      const url = await this.service.generateAvatarLink(
        this.avatarFile(),
        this.faceCode() as string,
      );
      this.avatarLink.set(url);
    }
  });

  onCancel() {
    this.valueData.set(null);
    this.layoutData.set(null as NzSafeAny);
    this.resetForm.update((prev) => !prev);
  }

  onConfirm(
    id?: string,
    errorMessage?: string,
    callback?: (success: boolean) => void,
  ) {
    const saveAction = this.getAction(id!);
    if (!saveAction) {
      if (id === 'success') {
        this.dialogVisible = false;
        this.toast.showToast('success', 'Success', 'Record saved');
        this.resetForm.update((val) => !val);
      } else {
        this.toast.showToast('error', 'Error', errorMessage || 'Record failed');
      }
      return;
    }
    this.doAction(saveAction, callback);
  }

  onConfirmProceed(
    id?: string,
    type?: string,
    callback?: (res: boolean) => void,
  ) {
    const saveAction = this.getActionProceed(id!, type!);
    if (!saveAction) return;
    this.doAction(saveAction, callback);
  }

  async doAction(action: FormAction, callback?: (res: boolean) => void) {
    const modalInfo: NzSafeAny = {
      class: {
        success: 'popup-success',
        warning: 'popup-confirm',
      },
      icon: {
        success: 'icons:success',
        warning: 'icons:warning',
      },
    };
    if (!action) return;
    switch (action.type) {
      case 'modal': {
        const modal = action.modal;
        if (!modal) return;
        this.modalComponent.showDialog(
          {
            nzTitle: modal?.title ?? '',
            nzContent: modal?.content ?? '',
            nzWrapClassName: `popup ${modalInfo.class[action.id]}`,
            nzIconType: modalInfo.icon[action.id],
            nzOkText: 'Confirm',
            nzCancelText: 'Cancel',
            nzOnOk: () => {
              if (action.id === 'warning') {
                if (modal.onConfirm?.type === 'confirm') {
                  callback?.(true);
                }
                return;
              }
              if (modal.onConfirm?.type === 'navigate') {
                this.handleNavigateAfterConfirm(modal);
              }
              if (modal.onConfirm?.type === 'confirm') {
                callback?.(true);
              }
            },
            nzOnCancel: () => {
              if (action.id === 'success') {
                this.dialogVisible = false;
                this.resetForm.update((val) => !val);
              }
              if (action.id === 'warning') {
                callback?.(false);
              }
              if (modal.onConfirm?.type === 'confirm') {
                callback?.(false);
              }
            },
          },
          modal.type as 'success' | 'warning' | 'info' | 'error',
        );
        break;
      }
      case 'toast': {
        const toast = action.toast;
        if (!toast) return;
        const content =
          (await this.getDinamicContentAction(toast._content)) ??
          toast.content ??
          '';
        this.toast.showToast(toast.type, toast.title, content);
        break;
      }
      case 'navigate': {
        this.router.navigate([action.link ?? '']);
        break;
      }
    }
  }

  async handleNavigateAfterConfirm(modal: Modal) {
    const paramsUrl =
      (await this.utilService.transformRedirectTo(
        this.dialogFormValue(),
        modal.onConfirm?.paramsUrl,
      )) || {};
    this.router.navigate(
      [mappingUrl(modal.onConfirm?.link ?? '', this.dynamicForm?.value)],
      {
        queryParams: paramsUrl,
        queryParamsHandling: 'merge',
      },
    );
    this.resetForm.update((val) => !val);
  }

  async getDinamicContentAction(contentDynamic: NzSafeAny) {
    const formValue = this.dynamicForm?.value ?? {};

    const content = await this.utilService.transformRedirectTo(
      formValue,
      contentDynamic,
    );
    return content;
  }

  getAction(id: string) {
    const actions: FormAction[] =
      this.functionSpec()?.form_config?.actions ?? [];
    return actions.find((action) => action.id == id) as FormAction | undefined;
  }

  getActionProceed(id: string, type: string) {
    const actions: FormAction[] =
      this.functionSpec()?.create_form?.actions ?? [];
    return actions.find((action) => action.id == id && action.type == type) as
      | FormAction
      | undefined;
  }

  onCancelForm() {
    if (this.showModalConfirmCancel()) {
      this.modalComponent.showDialog({
        nzTitle: 'Please confirm',
        nzContent: 'You have unsaved changes. All your changes will be lost.',
        nzWrapClassName: 'popup popup-confirm',
        nzIconType: 'icons:warning',
        nzOkText: 'Confirm',
        nzCancelText: 'Cancel',
        nzOnOk: () => {
          this.onCancel();
        },
      });
    } else {
      this.onCancel();
    }
  }

  onReset() {
    // this.onCancel();
    // setTimeout(() => {
    //   const formValue = this.dynamicForm?.value ?? {};
    //   this.handleOnSave(formValue, 'apply');
    // }, 100);
    this.handleReset();
  }

  ngOnInit(): void {
    // get layout data
    this.getLayoutData();
  }

  ngAfterViewInit(): void {
    const queryParams: Record<string, NzSafeAny> =
      this.route.snapshot.queryParams;
    if (isEmpty(queryParams)) return;
    this.valueData.set(queryParams);
    this.resetForm.update((prev) => !prev);
  }

  // a method to get data from the layout data service
  getLayoutData() {
    // get layout options
    const layoutOptions = this.layoutOptions() as NzSafeAny;
    // if layout options has layout_data_service then get data from the layout data service
    if (layoutOptions?.layout_data_service) {
      const layoutData = this.layoutDataService.getData();
      // if has data then get that data using the key which is the value of data_key in layout_data_service
      if (layoutData) {
        // get the data key
        const dataKey = layoutOptions?.layout_data_service?.data_key;
        // get the data mapping
        const key_mapping = layoutOptions?.layout_data_service?.key_mapping;

        // layout data object
        const layoutDataObj = layoutData[dataKey];
        if (dataKey && key_mapping && layoutDataObj) {
          // Create a new object with only mapped keys
          const mappedData = Object.entries(key_mapping).reduce(
            (acc, [sourceKey, targetKey]) => {
              if (
                Object.prototype.hasOwnProperty.call(
                  layoutDataObj,
                  targetKey as string,
                )
              ) {
                acc[sourceKey] =
                  layoutDataObj[targetKey as keyof typeof layoutDataObj];
              }
              return acc;
            },
            {} as Record<string, unknown>,
          );
          this.valueData.set(mappedData);
        }
      }
    }
    return;
  }

  ngOnDestroy() {
    this.layoutDataService.clearData();
  }
}
