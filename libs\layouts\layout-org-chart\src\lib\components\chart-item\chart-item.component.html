<lib-up-level *ngIf="upLevel()" [data]="item()" />
<div>
  <!-- data()?.positionCode -->
  <ng-container *ngIf="item()?.content?.type === 'org-chart-position'">
    <!-- <lib-edit /> -->
    <lib-chart-position [data]="item()" />
  </ng-container>
  <ng-container *ngIf="item()?.content?.type === 'org-chart-object'">
    <!-- <lib-edit /> -->
    <lib-chart-object [data]="item()" />
  </ng-container>
  <ng-container *ngIf="item()?.content?.type === 'org-chart-user-card'">
    <!-- <lib-edit /> -->
    <lib-user-card [data]="item()" />
  </ng-container>
  <ng-container *ngIf="item()?.content?.type === 'org-chart-model'">
    <lib-model [data]="item()" />
  </ng-container>
  <ng-container *ngIf="item()?.content?.type === 'org-group-user'">
    <lib-group-user-cards [data]="item()" />
  </ng-container>
</div>
