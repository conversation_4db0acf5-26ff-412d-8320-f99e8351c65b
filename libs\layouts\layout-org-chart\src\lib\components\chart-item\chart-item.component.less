@import '../../../../../../hrdx-design/src/themes/tokens.less';
:host {
  display: block;
  cursor: pointer;
  z-index: 9;
  
  &.dragging {
    cursor: move;
  }
  
  &.row {
    width: 220px;
    min-height: 284px;
    box-shadow: 0px 4px 6px 0px #00000014;
    display: flex;
    flex-direction: column;
    // padding: @spacing-3;
    background-color: @color-base-neutral-white;
  }
}
.title {
  font-weight: @font-weight-bold;
  font-size: @font-size-base;
}
footer {
  margin-top: auto;
  display: grid;
  grid-template-columns: 50% 50%;
  aside {
    padding: @spacing-2;
    text-align: center;
    span {
      white-space: pre-wrap;
      color: @color-primary;
    }
  }
}
.avatar {
  border-radius: 50%;
  height: 50px;
  width: 50px;
}
