import { Component, inject, input, OnInit, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ChartPositionComponent } from './chart-position/chart-position.component';
import { ChartObjectComponent } from './chart-object/chart-object.component';
import { UserCardComponent } from './user-card/user-card.component';
import { ModelComponent } from './model/model.component';
import { UpLevelComponent } from './up-level/up-level.component';
import { ConfigService } from '../../services/config/config.service';
import { ItemServicesService } from './services/item-services.service';
import { PanService } from '../../services/pan/pan.service';
import { NzSafeAny } from 'ng-zorro-antd/core/types';
import { GroupUserCardsComponent } from './group-user-cards/group-user-cards.component';

@Component({
  selector: 'lib-chart-item',
  standalone: true,
  imports: [
    CommonModule,
    ChartObjectComponent,
    ChartPositionComponent,
    UserCardComponent,
    ModelComponent,
    UpLevelComponent,
    GroupUserCardsComponent,
  ],
  providers: [ItemServicesService],
  templateUrl: './chart-item.component.html',
  styleUrls: ['./chart-item.component.less'],
  host: {
    '[style.position]': "'absolute'",
    '[style.left]': 'data().shape.x +"px"',
    '[style.top]': 'data().shape.y+"px"',
    '[class.row]': 'data()?.type === "raw"',
    '[class.dragging]': 'isDragging()',
    '[style.width]': "data().shape.width+'px'",
    '[style.height]': "data().shape.height+'px'",
  },
})
export class ChartItemComponent implements OnInit {
  data = input<NzSafeAny>();
  position = input<{
    left: number;
    top: number;
  }>({
    left: 0,
    top: 0,
  });
  showDetail = signal(false);
  upLevel = input<boolean>(false);
  config: NzSafeAny;
  layoutconfigService = inject(ConfigService);
  panService = inject(PanService);
  itemService = inject(ItemServicesService);
  item = signal<NzSafeAny>(null);
  isDragging = signal<boolean>(false);
  
  ngOnInit(): void {
    this.layoutconfigService.currentFs.subscribe(
      (data) => (this.config = data),
    );
    this.panService.currentDragging.subscribe(
      (dragging) => this.isDragging.set(dragging)
    );
    this.itemService.changeItem(this.data());
    this.itemService.currentItem.subscribe((value) => {
      this.item.set(value);
    });
  }
}
