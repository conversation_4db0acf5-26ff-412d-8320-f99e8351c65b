import {
  ACTIONS,
  RoleBasedAccessControl,
} from './../../../../../services/RBAC/index';
import { Component, computed, inject, input, OnInit, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { IconComponent } from '@hrdx/hrdx-design';
import { NzSafeAny } from 'ng-zorro-antd/core/types';
import { ConfigService } from '../../../../../services/config/config.service';
import { Params, Router } from '@angular/router';
import { ItemServicesService } from '../../../services/item-services.service';
@Component({
  selector: 'lib-add-child-object',
  standalone: true,
  imports: [CommonModule, NzDropDownModule, IconComponent, NzDropDownModule],
  templateUrl: './add-child-object.component.html',
  styleUrl: './add-child-object.component.less',
})
export class AddChildObjectComponent implements OnInit {
  constructor(
    private layoutconfigService: ConfigService,
    private router: Router,
    private accessControl: RoleBasedAccessControl,
  ) {}
  hasPermission = this.accessControl.hasPermission(ACTIONS['ADD_CHILD_OBJECT']);
  baseUrl = window.location.origin;
  queryParams: Params = {
    openForm: true,
    name: 'create',
  };
  data = input<NzSafeAny>('');
  dataAddChild = input<NzSafeAny>('');
  path = input<NzSafeAny>('');
  // - Nếu organizationType của object đang thao tác = Group thì chỉ hiển thị Company
  // - Nếu organizationType của object đang thao tác = Company và đang chọn select là Legal Structure thì chỉ hiển thị Legal Entity
  // - Nếu organizationType của object đang thao tác = Legal Entity thì chỉ hiển thị Legal Entity và Department
  // - Nếu organizationType của object đang thao tác = Department thì chỉ hiển thị Department
  // - Nếu organizationType của object đang thao tác = Company và đang chọn select là Management Structure thì chỉ hiển thị Business Unit
  // - Nếu organizationType của object đang thao tác = Business Unit thì chỉ hiển thị Business Unit và Divison
  // - Nếu organizationType của object đang thao tác = Divison thì chỉ hiển thị Divison
  groupOptions = [
    {
      label: 'Company',
      value: 'Company',
    },
  ];
  companyOptions = [
    {
      label: 'Legal Entity',
      value: 'LegalEntity',
    },
  ];
  legalEntityOptions = [
    {
      label: 'Legal Entity',
      value: 'LegalEntity',
    },
    {
      label: 'Department',
      value: 'Department',
    },
  ];
  departmentOptions = [
    {
      label: 'Department',
      value: 'Department',
    },
  ];
  businessUnitOptions = [
    {
      label: 'Business Unit',
      value: 'BusinessUnit',
    },
    {
      label: 'Division',
      value: 'Division',
    },
  ];
  divisionOptions = [
    {
      label: 'Division',
      value: 'Division',
    },
  ];
  value = '';
  options = computed(() => {
    switch (this.data().type) {
      case 'Group':
        this.value = 'Company';
        return this.groupOptions;
      case 'Company':
        if (this.structureType() === '2') {
          this.value = 'LegalEntity';
          return [
            {
              label: 'Legal Entity',
              value: 'LegalEntity',
            },
          ];
        }
        if (this.structureType() === '1') {
          this.value = 'BusinessUnit';
          return [
            {
              label: 'Business Unit',
              value: 'BusinessUnit',
            },
          ];
        }
        return this.companyOptions;
      case 'LegalEntity':
        this.value = 'LegalEntity';
        return this.legalEntityOptions;
      case 'Department':
        this.value = 'Department';
        return this.departmentOptions;
      case 'BusinessUnit':
        this.value = 'BusinessUnit';
        return this.businessUnitOptions;
      case 'Division':
        this.value = 'Division';
        return this.divisionOptions;
      default:
        return [];
    }
  });

  // ref to item service
  itemService = inject(ItemServicesService);

  addChild() {
    const path = this.path()(this.value);
    const newPath = this.router
      .createUrlTree([path], { queryParams: this.queryParams })
      .toString();
    // Construct the full URL
    const fullUrl = `${this.baseUrl}${newPath}`;
    localStorage.setItem('orgChartParent', JSON.stringify(this.data()));
    let addChildData = this.dataAddChild();
    if (!addChildData) {
      // no data add child --> get from item service
      addChildData = this.itemService.getObjectOrganization();
    }
    addChildData.type = this.data().type;
    addChildData.code = this.data().code;
    localStorage.setItem('orgChartParentAdd', JSON.stringify(addChildData));
    // Open the new URL in a new tab or window
    window.open(fullUrl, '_blank');
  }
  structureType = signal('2');
  ngOnInit(): void {
    this.layoutconfigService.currentStructureType.subscribe((data) =>
      this.structureType.set(data),
    );
  }
}
