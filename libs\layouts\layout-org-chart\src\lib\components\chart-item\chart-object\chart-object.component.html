<header [style.--header-color]="data()?.color">
  <h5>
    <hrdx-tooltip [title]="data()?.shortName || ''">
      {{ data()?.shortName }}
    </hrdx-tooltip>
  </h5>
  <ul>
    <li>
      <hrdx-icon icon="icon-users-three-bold" size="base"></hrdx-icon>
      <span>{{ organizationData()?.employeeCount ?? 0 }}</span>
    </li>
    <li (click)="focusObject()" class="pointer">
      <hrdx-icon icon="icon-tree-structure-bold" size="base"></hrdx-icon>
      <span>{{ data()?.level }}</span>
    </li>
    <li>
      <button
        [class.upside-down]="expandedEmployee"
        (click)="toggleExpandedEmployee()"
      >
        <hrdx-icon icon="icon-caret-down-bold" size="small"></hrdx-icon>
      </button>
    </li>
  </ul>
</header>

<main
  [class.expand]="expandedEmployee"
  [class.oneline]="
    displayEmployees()?.length === 0 &&
    colorsShort.includes(data()?.color ?? '')
  "
>
  <h6 (click)="openObjectDetails()">
    {{ data()?.longName }}
  </h6>
  <p (click)="openObjectDetails()">
    {{ data()?.location }}
  </p>
  <hrdx-loading *ngIf="loading()" />
  <div
    class="employee"
    *ngFor="let employee of displayEmployees()"
    (click)="$event.stopPropagation(); viewUserDetails(employee)"
  >
    <hrdx-avatar
      [type]="employee.avatarFile ? 'image' : 'text'"
      [shape]="'circle'"
      [size]="'medium'"
      [imgSrc]="
        employee.avatarFile
          ? employee.avatarLink
          : ''
      "
      [imgAlt]="employee.fullName"
      [text]="employee.fullName?.[0] ?? ''"
      [avatarElement]="
        employee?.managerType === 'HeadOf' ? 'icon-ic-star-action' : undefined
      "
    ></hrdx-avatar>
    <aside>
      <p>
        <hrdx-tooltip [title]="employee.fullName + ' ' + employee.account">
          <span class="ellipsis">
            {{ employee.fullName }} ({{ employee.account }})
          </span>
        </hrdx-tooltip>
      </p>
      <p>
        <span class="jobTitle">
          <hrdx-tooltip
            [title]="employee.jobName + ' ' + '(' + employee.jobCode + ')'"
          >
            <span class="ellipsis">
              {{ employee.jobName }}
            </span>
          </hrdx-tooltip>
        </span>
        <!-- <span class="positionTitle"> Position Title </span> -->
      </p>
    </aside>
  </div>
  <hrdx-avatar-group
    *ngIf="avatarGroup().length > 0"
    [avatars]="avatarGroup()"
    [size]="'medium'"
    [maxAvatarsDisplay]="1"
    (click)="openObjectDetails()"
  ></hrdx-avatar-group>
</main>
<footer>
  <span (click)="toggleExpandedUnit()" *ngIf="data()?.countChild">
    {{ data()?.countChild }}
    <span [class.upside-down]="!expandedUnit">
      <hrdx-icon icon="icon-caret-down-bold" size="small"></hrdx-icon>
    </span>
  </span>
  <span *ngIf="!data()?.countChild"> </span>
</footer>

<!-- user details -->
<ng-template #userDetails>
  <lib-user-details
    [fromPosition]="true"
    *ngIf="userSelected()"
    [data]="userSelected()"
    [employeeId]="userSelected()?.employeeId"
    [positionCode]="userSelected()?.positionCode"
  />
</ng-template>
<ng-template #userDetailsFooter>
  <lib-user-detail-action [employeeId]="userSelected()?.employeeId" />
</ng-template>

<!-- object details -->
<ng-template #objectDetails>
  <lib-object-details
    [objectDetailsRef]="objectDetails"
    [footerRef]="objectDetailsFooter"
    [objectDetails]="organizationData()"
    [employees]="employees() ?? []"
  />
</ng-template>
<ng-template #objectDetailsFooter>
  <footer class="alignLeft"><lib-action [data]="data()" [dataAddChild]="dataAddChild()" /></footer>
</ng-template>
<ng-template #objectDetailsTitleTitle>
  <span class="title"
    >{{ data()?.shortName
    }}<span class="longName">({{ data()?.longName }})</span>
  </span>
</ng-template>
