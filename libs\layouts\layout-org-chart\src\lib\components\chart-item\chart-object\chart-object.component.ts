import { ItemServicesService } from './../services/item-services.service';
import {
  Component,
  computed,
  effect,
  inject,
  input,
  OnInit,
  signal,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  AvatarComponent,
  AvatarGroup,
  AvatarGroupComponent,
  IconComponent,
  LoadingComponent,
  ToastMessageComponent,
  TooltipComponent,
} from '@hrdx/hrdx-design';
import { chartObject } from './chart-object.model';
import { ObjectDetailsComponent } from './object-details/object-details.component';
import { ActionComponent } from './action/action.component';
import { BffService } from '@hrdx-fe/shared';
import { ActivatedRoute } from '@angular/router';
import { ConfigService } from '../../../services/config/config.service';
import { catchError, of, switchMap, tap } from 'rxjs';
import { UserDetailsComponent } from '../user-details/user-details.component';
import { AvatarService } from '../../../services/avatar/avatar.service';
import { NzSafeAny } from 'ng-zorro-antd/core/types';
import { UserDetailActionComponent } from '../user-details/user-detail-action/user-detail-action.component';
import { QueryFilter } from '@nestjsx/crud-request';
import { OrgChartDrawerService } from '../../../services/org-chart-drawer/org-chart-drawer.service';
import { colorsShort } from '../../../services/org-chart/org-chart.service';
@Component({
  selector: 'lib-chart-object',
  standalone: true,
  imports: [
    CommonModule,
    IconComponent,
    AvatarComponent,
    ObjectDetailsComponent,
    ActionComponent,
    AvatarGroupComponent,
    UserDetailsComponent,
    UserDetailActionComponent,
    LoadingComponent,
    TooltipComponent,
  ],
  templateUrl: './chart-object.component.html',
  styleUrl: './chart-object.component.less',
  host: {
    '[style.height]': '!expandedEmployee ? "92px" : "auto"',
    '[class.dashed]': 'data()?.connectionType === "dashed"',
    '[class.search]': 'organization === this.data()?.code',
  },
})
export class ChartObjectComponent implements OnInit {
  constructor(
    private route: ActivatedRoute,
    private layoutconfigService: ConfigService,
    private orgChartDrawerService: OrgChartDrawerService,
    private ItemServicesService: ItemServicesService,
  ) {}
  organization = this.route.snapshot.queryParams['organization'];
  colorsShort = colorsShort;
  data = input<chartObject>();
  expandedEmployee = true;
  tree: NzSafeAny[] = [];
  searchByEffectiveDate =
    this.route.snapshot.queryParams?.['effectiveDate'] ?? new Date();
  toggleExpandedEmployee() {
    if (this.expandedEmployee) {
      this.layoutconfigService.changeTree(
        this.tree.map((item) =>
          item.id === this.data()?.id
            ? {
                ...item,
                expandedEmployee: !this.expandedEmployee,
                shape: { ...item.shape, height: 92 },
              }
            : item,
        ),
        false,
      );
    } else {
      this.layoutconfigService.changeTree(
        this.tree.map((item) =>
          item.id === this.data()?.id
            ? {
                ...item,
                expandedEmployee: !this.expandedEmployee,
                shape: { ...item.shape, height: 292 },
              }
            : item,
        ),
        false,
      );
    }
  }
  expandedUnit = false;
  structureType = signal<string>('2');
  organizationDisplay = signal<string>('maximize');
  ngOnInit(): void {
    this.expandedEmployee = this.data()?.expandedEmployee ?? true;
    this.expandedUnit = (this.data()?.childs?.length ?? 0) <= 0;
    this.layoutconfigService.currentTree.subscribe((data) => {
      this.tree = data;
    });
    this.layoutconfigService.currentStructureType.subscribe((data) =>
      this.structureType.set(data),
    );
    this.layoutconfigService.currentOrganizationDisplay.subscribe((data) =>
      this.organizationDisplay.set(data),
    );
  }
  @ViewChild('objectDetailsFooter') objectDetailsFooter!: TemplateRef<''>;
  @ViewChild('objectDetails') objectDetails!: TemplateRef<''>;

  @ViewChild('userDetailsFooter') userDetailsFooter!: TemplateRef<''>;
  @ViewChild('userDetails') userDetails!: TemplateRef<''>;
  @ViewChild('objectDetailsTitleTitle')
  objectDetailsTitleTitle!: TemplateRef<''>;

  _service = inject(BffService);
  avatarService = inject(AvatarService);
  toast = inject(ToastMessageComponent);

  toggleExpandedUnit() {
    // set toggle node expansion
    this.layoutconfigService.setLastAction('nodeClick');
    // this.layoutconfigService.toggleNodeExpansion(this.data()?.id ?? '');
    this.layoutconfigService.toggleNodeExpansionByAncestryPath([this.data()?.id ?? '', ...this.data()?.ancestryPath ?? []]);

    if (this.expandedUnit) {
      let params: { [key: string]: string } = {};
      this.route.queryParams.subscribe((p: { [key: string]: string }) => {
        params = p; // Replace 'param1' with your parameter name
      });

      const filter: QueryFilter[] = [
        {
          field: 'effectiveDate',
          operator: '$eq',
          value: new Date(this.searchByEffectiveDate).getTime(),
        },
        {
          field: 'currentLevel',
          operator: '$eq',
          value: this.data()?.level ?? 0,
        },
      ];
      const url =
        '/api/trees/organization/' +
        params['structureType'] +
        '/' +
        this.data()?.id +
        '/child/' +
        this.data()?.type;
      this._service
        .getPaginate(url, 1, 10000, filter)
        .pipe(
          catchError((err) => {
            this.toast.showToast('error', 'Error', err?.error?.message ?? err);
            return of([]);
          }),
        )
        .subscribe((d: NzSafeAny) => {
          if (d.length !== 0) {
            const removeMatrixID = d.map((item: NzSafeAny) => {
              const res = { ...item };
              if (item?.directPositionId !== this.data()?.id) {
                res.directPositionId = '';
                res.matrixPositionIds = item?.matrixPositionIds?.filter(
                  (matrixId: string) => this.data()?.id === matrixId,
                );
              } else {
                res.matrixPositionIds = [];
              }
              return res;
            });
            this.layoutconfigService.addChild(removeMatrixID, this.data()?.id);
            this.layoutconfigService.changeRemovedElementWatcher(this.data());
            if (this.organizationDisplay() === 'minimize') {
              this.layoutconfigService.minimizeAll();
            } else {
              this.layoutconfigService.maximizeAll();
            }
          }
        });
    } else {
      this.layoutconfigService.removeChild(this.data()?.id ?? '');
      this.expandedUnit = !this.expandedUnit;
    }
  }
  modalVisible = false;

  employees = signal<NzSafeAny | null>(null);
  totalEmployees = signal<number>(0);
  loading = signal<boolean>(false);
  setEmployees = effect(
    () => {
      const localData = this.ItemServicesService.getObjectUser();
      if (localData) {
        this.employees.set(localData);
        this.totalEmployees.set(localData?.length ?? 0);
        return;
      }
      const id = this.data()?.id;
      const type = this.data()?.type;
      if (!id || type === 'Group') return;
      const url = `/api/trees/organization/${id}/users`;
      of(url)
        .pipe(
          tap(() => this.loading.set(true)),
          switchMap((url) =>
            this._service
              .getList(url, 1, 99, [
                {
                  field: 'organizationType',
                  operator: '$eq',
                  value: type,
                },
                {
                  field: 'effectiveDate',
                  operator: '$eq',
                  value: new Date(this.searchByEffectiveDate).getTime(),
                },
              ])
              .pipe(
                switchMap((d) => {
                  // Use AvatarService to generate avatar links for employees
                  return this.avatarService.generateAvatarLinks(d, {
                    faceCode: '', // You can get this from component input or service
                    avatarFileProperty: 'avatarFile',
                    avatarLinkProperty: 'avatarLink'
                  });
                }),
                tap((employeesWithAvatars) => {
                  this.employees.set(employeesWithAvatars);
                  this.totalEmployees.set(employeesWithAvatars?.length ?? 0);
                  this.ItemServicesService.setObjectUser(employeesWithAvatars);
                }),
              ),
          ),
          catchError(() => {
            this.getEmployeesFaild = true;
            return of([]);
          }),
          tap(() => this.loading.set(false)),
        )
        .subscribe();
    },
    { allowSignalWrites: true },
  );

  organizationData = signal<NzSafeAny | null>(null);
  dataAddChild = signal<NzSafeAny | null>(null);
  setOrganizationData = effect(
    () => {
      const localData = this.ItemServicesService.getObjectOrganization();
      if (localData) {
        this.organizationData.set(localData);
        return;
      }
      const id = this.data()?.id;
      if (!id) return;
      const type = this.data()?.type;
      const url = `/api/trees/organization/detail/${id}`;
      of(url)
        .pipe(
          tap(() => this.loading.set(true)),
          switchMap((url) =>
            this._service
              .getObject(url, [
                { field: 'organizationType', operator: '$eq', value: type },
                {
                  field: 'effectiveDate',
                  operator: '$eq',
                  value: new Date(this.searchByEffectiveDate).getTime(),
                },
              ])
              .pipe(
                tap((d) => {
                  this.dataAddChild.set(d);
                  this.organizationData.set({ ...this.data(), ...d });
                  this.ItemServicesService.setObjectOrganization({
                    ...this.data(),
                    ...d,
                  });
                }),
              ),
          ),
          catchError(() => {
            return of([]);
          }),
          tap(() => this.loading.set(false)),
        )
        .subscribe();
    },
    { allowSignalWrites: true },
  );

  openObjectDetails() {
    this.orgChartDrawerService.changeDrawerVisible(true);
    this.orgChartDrawerService.changeDrawerConfig({
      title: this.objectDetailsTitleTitle,
      width: 'xsmall',
      body: this.objectDetails,
      footer: this.objectDetailsFooter,
    });
  }

  getEmployeesFaild = false;
  displayEmployees = computed(() => {
    const employees = this.employees();
    if (!employees) {
      return [];
    }
    if (employees.length <= 3) return employees;
    return employees.slice(0, 2);
  });

  avatarGroup = computed<AvatarGroup['avatars']>(() => {
    const employees = this.employees();
    if (!employees || employees.length <= 3) return [];
    return employees.slice(2)?.map((employee: NzSafeAny) => ({
      type: employee.avatarFile ? 'image' : 'text',
      shape: 'circle',
      text: employee.fullName?.[0] ?? '',
      imgSrc: employee.avatarFile
        ? '/api/personals/QUANGNV/basic-infomation/avatar/' +
          employee.avatarFile
        : '',
    }));
  });

  userSelected = signal<NzSafeAny>(null);
  viewUserDetails(data: NzSafeAny) {
    this.orgChartDrawerService.changeDrawerVisible(true);
    this.userSelected.set(data);
    this.orgChartDrawerService.changeDrawerConfig({
      title: 'Details',
      width: 'xsmall',
      body: this.userDetails,
      footer: this.userDetailsFooter,
    });
  }
  focusObject() {
    let params: { [key: string]: string } = {};
    this.route.queryParams.subscribe((p: { [key: string]: string }) => {
      params = p; // Replace 'param1' with your parameter name
    });

    const filter: QueryFilter[] = [
      {
        field: 'effectiveDate',
        operator: '$eq',
        value: new Date(this.searchByEffectiveDate),
      },
    ];
    const url =
      '/api/trees/organization/' +
      params['structureType'] +
      '/' +
      this.data()?.code +
      '/' +
      this.data()?.type;
    this._service
      .getPaginate(url, 1, 10000, filter)
      .pipe(
        catchError((err) => {
          this.toast.showToast('error', 'Error', err?.error?.message ?? err);
          return of([]);
        }),
      )
      .subscribe((data: NzSafeAny) => {
        this.layoutconfigService.changeTree(data);
      });
  }
}
