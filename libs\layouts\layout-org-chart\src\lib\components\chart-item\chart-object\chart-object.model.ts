import { z } from 'zod';

const chartObjectSchema = z
  .object({
    id: z.string(),
    parentId: z.null(),
    shortName: z.string(),
    longName: z.string(),
    effectiveDate: z.number(),
    code: z.string(),
    location: z.string(),
    type: z.string(),
    level: z.number(),
    countChild: z.number(),
    color: z.string(),
    content: z.object({
      type: z.literal('org-chart-object'),
    }),
    childs: z.array(z.unknown()),
    shape: z.object({
      width: z.number(),
      height: z.number(),
      coverWidth: z.number(),
      coverHeight: z.number(),
      x: z.number(),
      y: z.number(),
    }),
    expandedEmployee: z.boolean().optional(),
    ancestryPath: z.array(z.string()).optional(),
  })
  .optional();

type chartObject = z.infer<typeof chartObjectSchema>;

export { chartObject, chartObjectSchema };
