<hrdx-collapse
  header="Object Details"
  [border]="false"
  [expand]="true"
  [horizontalPadding]="'4px'"
  [verticalPadding]="'8px'"
>
  <main class="table">
    <div class="tr" *ngIf="showLocation()">
      <div class="td">Location/Site</div>
      <div class="td">
        <div
          [style]="{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
          }"
        >
          {{ objectDetails()?.location ?? '--' }}
          <lib-location
            class="location"
            [data]="objectDetails()"
            *ngIf="objectDetails()?.location"
          />
        </div>
      </div>
    </div>
    <div class="tr" *ngIf="showLocation()">
      <div class="td">Country</div>
      <div class="td">{{ objectDetails()?.locationCountry ?? '--' }}</div>
    </div>
    <div class="tr">
      <div class="td">Employees</div>
      <div class="td">{{ objectDetails()?.employeeCount ?? 0 }}</div>
    </div>
    <div class="tr">
      <div class="td">Created Date</div>
      <div class="td">
        <hrdx-display
          type="DD/MM/yyyy"
          [value]="objectDetails()?.effectiveDate"
        ></hrdx-display>
      </div>
    </div>
    <div class="tr">
      <div class="td">Term of Office</div>
      <div class="td" *ngIf="objectDetails()?.effectiveDate">
        {{ getDuration(objectDetails()?.effectiveDate) }}
      </div>
    </div>
  </main>
</hrdx-collapse>
<hr />

<hrdx-collapse
  header="Management Personel"
  [border]="false"
  [expand]="true"
  [horizontalPadding]="'4px'"
  [verticalPadding]="'8px'"
>
  <main class="management-personel">
    <div
      class="personel"
      *ngFor="let employee of employeesSort(); let i = index"
    >
      <ng-container
        *ngIf="
          i === 0 || employee.managerType !== employeesSort()[i - 1].managerType
        "
      >
        @if (employee.managerType === 'HeadOf') {
          <p class="employee-type">
            <ng-container [ngSwitch]="objectDetails()?.type">
              <ng-container *ngSwitchCase="'Department'"
                >Head of Department</ng-container
              >
              <ng-container *ngSwitchCase="'BusinessUnit'"
                >Head of Business Unit</ng-container
              >
              <ng-container *ngSwitchCase="'Company'"
                >Head of Company</ng-container
              >
              <ng-container *ngSwitchCase="'Division'"
                >Head of Division</ng-container
              >
              <ng-container *ngSwitchCase="'LegalEntity'"
                >Head of Legal Entity</ng-container
              >

              <ng-container *ngSwitchDefault>Unknown Manager Type</ng-container>
            </ng-container>
          </p>
        } @else {
          <p class="employee-type">Deputy Manager</p>
        }
      </ng-container>
      <div>
        <aside>
          <hrdx-avatar
            [type]="employee.avatarFile ? 'image' : 'text'"
            [shape]="'circle'"
            [size]="'default'"
            [imgSrc]="
              employee.avatarFile
                ? employee.avatarLink
                : ''
            "
            [imgAlt]="employee.fullName"
            [text]="employee.fullName?.[0] ?? ''"
          ></hrdx-avatar>
        </aside>
        <aside>
          <span class="name">
            {{ employee.fullName }} ({{ employee?.account }})
          </span>
          <!-- <div>
            <span class="flex-item">
              <hrdx-icon icon="icon-calendar-blank-bold" />
              <hrdx-display
                type="DD/MM/yyyy"
                [value]="employee.effectiveDate"
                [props]="{
                  tooltip: false,
                }"
              ></hrdx-display>
            </span>
            <span class="flex-item">
              <hrdx-icon icon="icon-clock-counter-clockwise-bold" />
              <span>{{ getDuration(employee?.effectiveDate) }}</span>
            </span>
          </div> -->
        </aside>
        <hrdx-button
          (clicked)="viewUserDetails(employee)"
          type="link"
          [onlyIcon]="true"
          icon="icon-article-bold"
        />
      </div>
    </div>
  </main>
</hrdx-collapse>
<ng-template #userDetails>
  <lib-user-details
    [employeeId]="userSelected()?.employeeId"
    [positionCode]="userSelected()?.positionCode"
  />
</ng-template>
<ng-template #userDetailsFooter>
  <footer class="alignLeft">
    <lib-user-detail-action [employeeId]="userSelected()?.employeeId"/>
  </footer>
</ng-template>
