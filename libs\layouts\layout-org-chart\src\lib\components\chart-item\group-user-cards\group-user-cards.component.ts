import { Component, computed, input, HostBinding } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NzSafeAny } from 'ng-zorro-antd/core/types';
import { ItemComponent } from './item/item.component';

@Component({
  selector: 'lib-group-user-cards',
  standalone: true,
  imports: [CommonModule, ItemComponent],
  templateUrl: './group-user-cards.component.html',
  styleUrl: './group-user-cards.component.less',
})
export class GroupUserCardsComponent {
  data = input<NzSafeAny>();
  groupChilds = computed(() => {
    const groupChilds = this.data().groupChilds;
    groupChilds.sort((a: NzSafeAny, b: NzSafeAny) =>
      a?.fullName?.localeCompare(b.fullName),
    );
    return groupChilds;
  });

  // Calculate number of columns based on groupChilds length
  private columnCount = computed(() => {
    const length = this.groupChilds().length;
    if (length > 50) return Math.ceil(length / 10); // 6+ columns for very large groups
    if (length > 40) return 5;
    if (length > 30) return 4;
    if (length > 20) return 3;
    return 2; // Default 2 columns
  });

  // Use HostBinding to set CSS custom property for dynamic columns
  @HostBinding('style.--columns')
  get gridColumns(): string {
    return `repeat(${this.columnCount()}, auto)`;
  }
}
