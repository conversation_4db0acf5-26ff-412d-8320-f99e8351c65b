import { Component, inject, input, OnInit, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { BffService } from '@hrdx-fe/shared';
import { ToastMessageComponent } from '@hrdx/hrdx-design';
import { ConfigService } from '../../../services/config/config.service';
import { ActivatedRoute } from '@angular/router';
import { QueryFilter } from '@nestjsx/crud-request';
import { NzSafeAny } from 'ng-zorro-antd/core/types';
import { UserUplevelComponent } from './userUplevel/userUplevel.component';

@Component({
  selector: 'lib-up-level',
  standalone: true,
  imports: [CommonModule, UserUplevelComponent],
  templateUrl: './up-level.component.html',
  styleUrl: './up-level.component.less',
})
export class UpLevelComponent implements OnInit {
  toast = inject(ToastMessageComponent);
  config: NzSafeAny;
  data = input<NzSafeAny>('');
  _service = inject(BffService);
  layoutconfigService = inject(ConfigService);
  constructor(private route: ActivatedRoute) {}
  chartType = 'org-chart-user-card';
  focusMode = signal(false);
  organizationDisplay = signal('maximize');
  searchByEffectiveDate =
    this.route.snapshot.queryParams?.['effectiveDate'] ?? new Date();
  showInactive = this.route.snapshot.queryParams?.['showInactive'] === 'Y';
  ngOnInit(): void {
    this.layoutconfigService.currentFs.subscribe(
      (data) => (this.config = data),
    );
    this.layoutconfigService.currentChartType.subscribe(
      (data) => (this.chartType = data),
    );
    this.layoutconfigService.currentFocusMode.subscribe((data) =>
      this.focusMode.set(data),
    );
    this.layoutconfigService.currentOrganizationDisplay.subscribe((data) =>
      this.organizationDisplay.set(data),
    );
  }
  goUp() {
    this.layoutconfigService.changeLoading(true);
    this.layoutconfigService.changeFocusMode(false);
    let urlUpLevels =
      'api/personals/' + this.data().positionCode + '/up-levels';
    let params: { [key: string]: string } = {};
    this.route.queryParams.subscribe((p: { [key: string]: string }) => {
      params = p; // Replace 'param1' with your parameter name
    });
    const filter: QueryFilter[] = [
      {
        field: 'effectiveDate',
        operator: '$eq',
        value: new Date(this.searchByEffectiveDate).getTime(),
      },
      {
        field: 'currentLevel',
        operator: '$eq',
        value: this.data()?.level ?? 0,
      },
      {
        field: 'showInactive',
        operator: '$eq',
        value: this.showInactive,
      },
    ];
    switch (this.chartType) {
      case 'org-chart-position':
        urlUpLevels =
          '/api/positions/' + this.data().positionCode + '/parent-management';
        break;
      case 'org-chart-user-card':
        urlUpLevels = 'api/personals/' + this.data().employeeId + '/up-levels';
        break;
      case 'org-chart-object':
        urlUpLevels =
          '/api/trees/organization/' +
          params['structureType'] +
          '/' +
          this.data().id +
          '/parent/' +
          this.data().type;

        break;
    }
    this._service.getPaginate(urlUpLevels, 1, 10000, filter).subscribe(
      (res: NzSafeAny) => {
        if (this.chartType === 'org-chart-object') {
          this.layoutconfigService.addParent(res[0]);
          const url =
            '/api/trees/organization/' +
            params['structureType'] +
            '/' +
            res[0]?.id +
            '/child/' +
            res[0]?.type;
          filter[1].value = res[0]?.level ?? 0;
          this._service.getPaginate(url, 1, 10000, filter).subscribe(
            (childs: NzSafeAny) => {
              // Add the old root node's ID to expandedNodeIds before changing the tree
              const oldRootId = this.data()?.id;
              if (oldRootId) {
                // this.layoutconfigService.expandNode(oldRootId);
                this.layoutconfigService.expandNodeByAncestryPath([res[0]?.id]);

                // set the selected node
                this.layoutconfigService.setSelectedNode(this.data());
              }
              this.layoutconfigService.setLastAction('upOneLevel');


              this.removeMatrixID(res.id);
              this.layoutconfigService.addChild(
                this.removeUnnecessaryConnections(
                  res,
                  this.removeCurentNode(childs, this.data()?.id),
                ),
                res[0]?.id,
              );
              if (this.organizationDisplay() === 'minimize') {
                this.layoutconfigService.minimizeAll();
              } else {
                this.layoutconfigService.maximizeAll();
              }
            },
            (err) => {
              this.toast.showToast(
                'error',
                'Error',
                err?.error?.message ?? err,
              );
              this.layoutconfigService.changeLoading(false);
            },
          );
        } else if (this.chartType === 'org-chart-position') {
          this.removeMatrixID(res.id);
          this.layoutconfigService.addParent(res);
          const url = `/api/positions/${res?.id}/child-management`;
          this._service.getPaginate(url, 1, 10000, filter).subscribe(
            (childs: NzSafeAny) => {
              this.layoutconfigService.addChild(
                this.removeUnnecessaryConnections(
                  res,
                  childs.filter(
                    (item: NzSafeAny) => item?.id !== this.data()?.id,
                  ),
                ),
                res?.id,
              );
            },
            (err) => {
              this.toast.showToast(
                'error',
                'Error',
                err?.error?.message ?? err,
              );
              this.layoutconfigService.changeLoading(false);
            },
          );
        } else {
          this.removeMatrixID(res.id);
          this.layoutconfigService.addParent(res);
        }
        this.layoutconfigService.changeLoading(false);
      },
      (err) => {
        this.toast.showToast('error', 'Error', err?.error?.message ?? err);
        this.layoutconfigService.changeLoading(false);
      },
    );
    0.0;
  }
  removeMatrixID(parentId?: string) {
    if (parentId) {
      const removeMatrixID = {
        ...this.data(),
        matrixPositionIds: this.data()?.matrixPositionIds?.filter(
          (id: string) => id === parentId,
        ),
      };
      this.layoutconfigService.changeNode(removeMatrixID);
      return;
    }
    const removeMatrixID = { ...this.data(), matrixPositionIds: [] };
    this.layoutconfigService.changeNode(removeMatrixID);
  }
  removeUnnecessaryConnections(parent: NzSafeAny, childs: NzSafeAny) {
    const parentID = parent?.id;
    childs = childs.map((item: NzSafeAny) => {
      if (
        item.directPositionId !== parentID &&
        item.matrixPositionIds?.length
      ) {
        item.directPositionId = '';
        item.matrixPositionIds = item?.matrixPositionIds?.filter(
          (id: string) => id === parentID,
        );
      }
      if (
        item.directPositionId === parentID &&
        item.matrixPositionIds?.length
      ) {
        item.matrixPositionIds = [];
      }
      return { ...item };
    });
    return childs;
  }
  removeCurentNode(childs: NzSafeAny, id: string) {
    childs = childs.filter((item: NzSafeAny) => item?.id !== id);
    return childs;
  }
}
