import { replace } from 'lodash';
import { ItemServicesService } from './../../services/item-services.service';
import {
  Component,
  OnInit,
  signal,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { UserComponent } from './user/user.component';
import { BffService } from '@hrdx-fe/shared';
import { QueryFilter } from '@nestjsx/crud-request';
import { ConfigService } from '../../../../services/config/config.service';
import { NzSafeAny } from 'ng-zorro-antd/core/types';
import { LoadingComponent, ToastMessageComponent } from '@hrdx/hrdx-design';
import { ActivatedRoute } from '@angular/router';
import { catchError, of, take } from 'rxjs';
import { AvatarService } from '../../../../services/avatar/avatar.service';

@Component({
  selector: 'lib-user-uplevel',
  standalone: true,
  imports: [CommonModule, NzDropDownModule, UserComponent, LoadingComponent],
  templateUrl: './userUplevel.component.html',
  styleUrl: './userUplevel.component.less',
})
export class UserUplevelComponent implements OnInit {
  constructor(
    private _service: BffService,
    private layoutconfigService: ConfigService,
    private toast: ToastMessageComponent,
    private route: ActivatedRoute,
    private itemServices: ItemServicesService,
    private avatarService: AvatarService
  ) {}
  data = signal<NzSafeAny>({});
  loading = false;
  userOptions = signal<NzSafeAny>([]);
  tree: NzSafeAny = [];
  loadUserOptions() {
    this.loading = true;
    // const id = replace(this.data().id, /-/g, '');
    const urlUpLevels = 'api/personals/' + this.data().id + '/up-levels';
    const searchByEffectiveDate =
      this.route.snapshot.queryParams?.['effectiveDate'] ?? new Date();
    const showInactive =
      this.route.snapshot.queryParams?.['showInactive'] === 'Y';
    const filter: QueryFilter[] = [
      {
        field: 'effectiveDate',
        operator: '$eq',
        value: new Date(searchByEffectiveDate).getTime(),
      },
      {
        field: 'currentLevel',
        operator: '$eq',
        value: this.data()?.level ?? 0,
      },
      {
        field: 'showInactive',
        operator: '$eq',
        value: showInactive,
      },
      {
        field: 'employeeRecordNumber',
        operator: '$eq',
        value: this.data()?.employeeRecordNumber,
      },
    ];
    this._service.getPaginate(urlUpLevels, 1, 10000, filter).subscribe(
        async (res: NzSafeAny) => {
          if (Array.isArray(res)) {
            // generate avatar link
            const usersWithAvatars = await this.avatarService.generateAvatarLinks(Array.isArray(res) ? res : [], {
              avatarFileProperty: 'avatarFile',
              avatarLinkProperty: 'avatarLink'
            });

            // check if res is an array, if not, return an empty array
            this.userOptions.set(usersWithAvatars);
          } else {
            this.userOptions.set([]);
          }
          this.loading = false;
      },
      (err) => {
        this.toast.showToast('error', 'Error', err?.error?.message ?? err);
        // set empty array to userOptions
        this.userOptions.set([]);
        this.loading = false;
      },
    );
    0.0;
  }
  @ViewChild('userOne') userOne!: TemplateRef<NzSafeAny>;
  getChartUplevel(
    id: string,
    positionCode: string,
    employeeRecordNumber: number,
  ) {
    const urlTree = '/api/personals/' + id + '/build-org-chart';
    const filter: QueryFilter[] = [];
    if (positionCode)
      filter.push({
        field: 'positionCode',
        operator: '$eq',
        value: positionCode,
      });
    if (employeeRecordNumber === 0 || employeeRecordNumber)
      filter.push({
        field: 'employeeRecordNumber',
        operator: '$eq',
        value: employeeRecordNumber,
      });
    this._service
      .getListTree(urlTree, filter)
      .pipe(
        // tap(() => this.loading.set(true)),
        catchError((err) => {
          this.toast.showToast('error', 'Error', err?.error?.message ?? err);
          return of([]);
        }),
        // tap(() => this.loading.set(false)),
      )
      .subscribe((res: NzSafeAny) => {
        const childs =
          res?.childs
            ?.map((item: NzSafeAny) => {
              // if reportToPosition is the same as the parent positionCode, then it is a directPosition
              if (
                !!item.reportToPosition &&
                !!res.item.positionCode &&
                !item.directPositionId &&
                item.reportToPosition === res.item.positionCode
              ) {
                item.directPositionId = res.item.id;
                return item;
              }
              // if the directPositionId is not the same as the parent id and the matrixPositionIds does not include the parent id, then add the parent id to the matrixPosition
              if (
                item.directPositionId !== res.item.id
              ) {
                item.matrixPositionIds.push(res.item.id);
                item.directPositionId=''
                return item;
              }
              return item;
            })
            // ?.filter(
            //   (node: NzSafeAny) =>
            //     !this.tree.some(
            //       (treeNode: NzSafeAny) => treeNode.id === node.id,
            //     ),
            // ) ?? [];
        this.layoutconfigService.addChild(childs, id);
      });
  }
  /**
   * Moves the org chart view up one level to the selected parent node.
   *
   * Sets lastAction to 'upOneLevel' via ConfigService to inform downstream effects
   * (such as chart rendering logic) that this action was an up-level navigation.
   * This allows the chart to distinguish between up-level actions and other actions
   * (like node clicks or filtering) and update the UI accordingly.
   */
  UpOneLevel(uplevel: NzSafeAny) {
    // Add the old root node's ID to expandedNodeIds before changing the tree
    const oldRootId = this.data()?.id;
    if (oldRootId) {
      // this.layoutconfigService.expandNode(oldRootId);
      this.layoutconfigService.expandNodeByAncestryPath([uplevel.id]);
    }
    this.layoutconfigService.setLastAction('upOneLevel');
    const removeMatrixID = {
      ...this.data(),
      matrixPositionIds: [],
      directPositionId: uplevel.id,
    };
    this.layoutconfigService.changeNodeByID(removeMatrixID);
    this.layoutconfigService.addParent(uplevel);
    this.getChartUplevel(
      uplevel.id,
      uplevel.positionCode,
      uplevel.employeeRecordNumber,
    );

    // set the selected node
    this.layoutconfigService.setSelectedNode(uplevel);
  }
  ngOnInit(): void {
    this.itemServices.currentItem.subscribe((data) => {
      this.data.set(data);
    });
    this.layoutconfigService.currentTree.subscribe((data) => {
      this.tree = data;
    });
    this.loadUserOptions();
  }
}
