<aside>
  <hrdx-avatar
    [type]="avatarImage"
    [text]="data().fullName"
    [shape]="avatarShape"
    [size]="40"
    [imgSrc]="
      data().avatarFile
        ? data().avatarLink
        : ''
    "
    imgAlt="'user avatar'"
  ></hrdx-avatar>
</aside>
<aside>
  <h6>
    {{ data().fullName }}<span>({{ data().email.split('@')[0] }})</span>
  </h6>
  <p>
    <hrdx-icon icon="user-circle" />
    <span>{{ data().jobName }}</span>
  </p>
  <p class="positionTitle">
    <hrdx-icon icon="suitcase-simple" />
    <span>{{ departmentInfo(data()) }}</span>
  </p>
</aside>
