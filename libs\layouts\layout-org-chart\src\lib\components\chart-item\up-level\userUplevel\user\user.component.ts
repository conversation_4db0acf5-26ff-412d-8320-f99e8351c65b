import {
  Component,
  input,
  OnInit,
  signal,
  output,
  computed,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  AvatarComponent,
  AvatarShape,
  AvatarType,
  IconComponent,
} from '@hrdx/hrdx-design';
import { ItemServicesService } from '../../../services/item-services.service';
import { NzSafeAny } from 'ng-zorro-antd/core/types';
import { isEmpty } from 'lodash';

@Component({
  selector: 'lib-user',
  standalone: true,
  imports: [CommonModule, AvatarComponent, IconComponent],
  templateUrl: './user.component.html',
  styleUrl: './user.component.less',
  host: {
    '(click)': 'onHostClick()',
  },
})
export class UserComponent implements OnInit {
  data = input({
    id: '7260117026424176640',
    fullName: 'V1 Nguyễn Văn',
    employeeId: '00005048',
    email: '',
    positionCode: '00001253',
    positionName: 'V',
    companyCode: '66',
    companyName: 'FPT Software JSC',
    companyShortName: 'FSOFT',
    legalEntityCode: '6601000000',
    legalEntityName: 'Cty TNHH Phần mềm FPT',
    businessUnitCode: '',
    businessUnitName: '',
    jobCode: '00001126',
    jobName: 'CB Hành chính',
    departmentCode: '6601010000',
    departmentName: 'Ban Nhân sự FSOFT',
    departmentShortName: 'FHO HRM',
    avatarFile: null,
    avatarFileName: null,
    seniorityDate: null,
    directPositionId: '',
    directPositionName: '',
    matrixManager: [],
    employeeRecordNumber: 0,
    totalParent: 0,
    totalChild: 5,
    numberChildDirect: 1,
    numberChildMatrix: 0,
    totalChildMatrix: 0,
    totalChildDirect: 5,
    avatarLink: '',
  });

  avatarImage = AvatarType.Image;
  avatarShape = AvatarShape.Circle;
  currentItem = signal<NzSafeAny>({});
  constructor(private itemService: ItemServicesService) {}
  upleve = output();
  onHostClick() {
    this.upleve.emit();
  }
  ngOnInit(): void {
    this.itemService.currentItem.subscribe((data) => {
      this.currentItem.set(data);
    });
  }
  departmentInfo = (data: NzSafeAny) => {
    const department = isEmpty(data?.departmentShortName)
      ? data?.departmentName
      : data?.departmentShortName;
    const company = isEmpty(data?.companyShortName)
      ? data?.companyName
      : data?.companyShortName;

    return [company, department].filter((item) => !isEmpty(item)).join(' - ');
  };
}
