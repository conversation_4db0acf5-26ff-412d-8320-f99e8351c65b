<header
  (click)="
    (data()?.numberChildDirect || data()?.numberChildMatrix) && dropdown()
  "
>
  <hrdx-avatar
    [type]="data()?.avatarFile ? avatarImage : avatarText"
    [text]="data()?.fullName?.slice(0, 1)"
    [shape]="avatarShape"
    [size]="avatarSize"
    [imgSrc]="
      data()?.avatarFile
        ? data()?.avatarLink
        : ''
    "
    imgAlt="'user avatar'"
  ></hrdx-avatar>
</header>

<main
  (click)="
    (data()?.numberChildDirect || data()?.numberChildMatrix) && dropdown()
  "
>
  <hrdx-tooltip
    title="{{
      data()?.fullName +  (data()?.email ? ' ' + '(' + data()?.email?.split('@')?.[0] + ')' : '')

    }}"
  >
    <h5>
      {{ data()?.fullName }}
      <span *ngIf="data()?.email">
        ({{ data()?.email?.split('@')?.[0] }})
      </span>
    </h5>
  </hrdx-tooltip>
  <p class="positionTitle">
    <hrdx-icon icon="user-circle" />
    <span class="color-text-secondary">{{ data()?.jobName }} </span>
  </p>
  <p class="positionTitle">
    <hrdx-icon icon="suitcase-simple" />
    <span class="color-text-secondary">
      <hrdx-popover
        [arrow]="popoverConfig.arrow"
        [position]="popoverConfig.position"
        [content]="departmentInfoPopover"
        [trigger]="popoverConfig.trigger"
      >
        <span>{{ departmentInfo() }}</span>
      </hrdx-popover>
    </span>
    <!-- - -->
    <!-- <span>{{ data().department }}</span> -->
  </p>
  <p class="Direct-Matrix">
    <span *ngIf="data()?.numberChildDirect" class="text-primary"
      >{{ data()?.numberChildDirect }}/{{
        data()?.totalChildDirect
      }}
      Direct</span
    ><hrdx-icon
      *ngIf="data()?.numberChildDirect && data()?.numberChildMatrix"
      icon="dot-bold"
    /><span *ngIf="data()?.numberChildMatrix" class="text-primary"
      >{{ data()?.numberChildMatrix }} Matrix</span
    >
  </p>
</main>

<footer
  (click)="
    (data()?.numberChildDirect || data()?.numberChildMatrix) && dropdown()
  "
>
  <hrdx-button
    (clicked)="$event.stopPropagation(); viewUserDetails()"
    type="link"
    [onlyIcon]="true"
    icon="icon-article-bold"
  />
</footer>

<svg>
  <rect
    x="1"
    y="1"
    width="316"
    height="100"
    class="line"
    [attr.stroke-width]="
      employeeId === this.data()?.employeeId
        ? scale < 1
          ? 2 / scale
          : 2
        : scale < 1
          ? 1 / scale
          : 1
    "
    [attr.stroke-dasharray]="data()?.connectionType === 'dashed' ? '5,5' : 0"
    fill="none"
  />
</svg>

<ng-template #userDetails>
  <lib-user-details
    [data]="data()"
    (viewOrgChart)="viewOrgChart()"
    [employeeId]="data()?.employeeId"
    [positionCode]="data()?.positionCode"
  />
</ng-template>
<ng-template #userDetailsFooter>
  <lib-user-detail-action [employeeId]="data()?.employeeId ?? ''" />
</ng-template>

<ng-template #departmentInfoPopover>
  <ul class="user-card__popover-content">
    <li>Company: {{ companyName() }}</li>
    <li>Department: {{ departmentName() }}</li>
  </ul>
</ng-template>
