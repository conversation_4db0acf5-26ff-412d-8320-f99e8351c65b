import { PanService } from './../../../services/pan/pan.service';
import {
  Component,
  computed,
  inject,
  input,
  OnInit,
  ViewChild,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  AvatarComponent,
  AvatarShape,
  AvatarSize,
  AvatarType,
  IconComponent,
  ButtonComponent,
  PopoverComponent,
  PopoverPosition,
  PopoverTrigger,
  TooltipComponent,
} from '@hrdx/hrdx-design';
import { UserCard } from './user-card.model';
import { UserDetailsComponent } from '../user-details/user-details.component';

import { catchError, of, switchMap, tap } from 'rxjs';
import { ConfigService } from '../../../services/config/config.service';
import { BffService } from '@hrdx-fe/shared';
import { UserDetailActionComponent } from '../user-details/user-detail-action/user-detail-action.component';
import { ApiService } from '../../../services/api/api.service';
import { NzSafeAny } from 'ng-zorro-antd/core/types';
import { ItemServicesService } from '../services/item-services.service';
import { isEmpty } from 'lodash';
import { QueryFilter } from '@nestjsx/crud-request';
import { OrgChartDrawerService } from '../../../services/org-chart-drawer/org-chart-drawer.service';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'lib-user-card',
  standalone: true,
  imports: [
    CommonModule,
    AvatarComponent,
    IconComponent,
    UserDetailsComponent,
    ButtonComponent,
    UserDetailActionComponent,
    PopoverComponent,
    TooltipComponent,
  ],
  templateUrl: './user-card.component.html',
  styleUrl: './user-card.component.less',
  host: {
    '[class.dashed]': 'data()?.connectionType === "dashed"',
    '[class.search]': 'employeeId === this.data()?.employeeId',
  },
})
export class UserCardComponent implements OnInit {
  constructor(
    private apiService: ApiService,
    private itemService: ItemServicesService,
    private orgChartDrawerService: OrgChartDrawerService,
    private route: ActivatedRoute,
    private panService: PanService,
  ) {}
  avatarShape = AvatarShape.Circle;
  avatarImage = AvatarType.Image;
  avatarText = AvatarType.Text;
  avatarSize = AvatarSize.Default;
  data = input<UserCard>();
  tree: NzSafeAny = [];
  readonly popoverConfig = {
    arrow: true,
    position: PopoverPosition.BottomCenter,
    trigger: PopoverTrigger.Hover,
  };

  employeeId = this.route.snapshot.queryParams['employeeId'];
  scale = 1;
  nodeIndex = 0;
  ngOnInit(): void {
    this.layoutconfigService.currentTree.subscribe(
      (data) => (this.tree = data),
    );
    this.panService.currentScale.subscribe((data) => (this.scale = data));
    this.layoutconfigService.currentNodeIndex.subscribe(
      (data) => (this.nodeIndex = data),
    );
  }
  _service = inject(BffService);
  layoutconfigService = inject(ConfigService);
  loadChild() {
    this.layoutconfigService.changeLoading(true);
    if (!this.data()?.employeeId) {
      return;
    }
    const url = `/api/personals/${this.data()?.employeeId}/build-org-chart`;
    const filter: QueryFilter[] = [];
    if (this.data()?.positionCode)
      filter.push({
        field: 'positionCode',
        operator: '$eq',
        value: this.data()?.positionCode,
      });
    if (
      this.data()?.employeeRecordNumber === 0 ||
      this.data()?.employeeRecordNumber
    )
      filter.push({
        field: 'employeeRecordNumber',
        operator: '$eq',
        value: this.data()?.employeeRecordNumber,
      });
    of(url)
      .pipe(
        switchMap((url) =>
          this._service.getListTree(url, filter).pipe(
            tap((d: NzSafeAny) => {
              const childs = d.childs.map((item: NzSafeAny) => {
                if (
                  !item.directPositionId &&
                  !!item.reportToPosition &&
                  !!d.item.positionCode &&
                  item.reportToPosition === d.item.positionCode
                ) {
                  item.directPositionId = d.item.id;
                  return item;
                }
                if (
                  item.directPositionId !== d.item.id &&
                  !item.matrixPositionIds.includes(d.item.id)
                ) {
                  item.matrixPositionIds.push(d.item.id);
                  return item;
                }
                return item;
              });
              this.layoutconfigService.changeRemovedElementWatcher(this.data());
              this.layoutconfigService.addChild(childs, this.data()?.id, this.data()?.ancestryPath??[]);
              this.layoutconfigService.changeNode({
                ...this.data(),
                nodeIndex: this.nodeIndex + 1,
              });
              this.layoutconfigService.changeNodeIndex();
            }),
          ),
        ),
        catchError(() => {
          this.layoutconfigService.changeLoading(false);
          return of([]);
        }),
      )
      .subscribe(() => {
        this.layoutconfigService.changeLoading(false);
      });
  }
  /**
   * Toggles the expansion/collapse of this node's children in the org chart.
   *
   * Sets lastAction to 'nodeClick' via ConfigService to inform downstream effects
   * (such as chart rendering logic) that this action was a user node toggle.
   * This allows the chart to distinguish between node clicks and other actions
   * (like up one level or filtering) and update the UI accordingly.
   */
  dropdown() {
    this.layoutconfigService.setLastAction('nodeClick');
   
    this.layoutconfigService.toggleNodeExpansionByAncestryPath([this.data()?.id ?? '', ...this.data()?.ancestryPath ?? []]);
    if (Number(this.data()?.childs?.length) > 0) {
      // If node has children, collapse it (remove children from tree)
      // remove all nodes whose ancestryPath includes the given id
      this.layoutconfigService.removeChildsByAncestryPath([this.data()?.id ?? '', ...this.data()?.ancestryPath ?? []]);
      this.layoutconfigService.changeRemovedElementWatcher(this.data());

      // find the direct parent node of the current node and set it as the selected node
      const parent = this.tree.find((node: NzSafeAny) => JSON.stringify(this.data()?.ancestryPath) === JSON.stringify([node.id ?? '', ...node?.ancestryPath ?? []]));
      if (parent) {
        this.layoutconfigService.setSelectedNode(parent);
      }
    } else {
      // set the selected node
      this.layoutconfigService.setSelectedNode(this.data());
      // If node doesn't have children loaded yet, load them
      this.loadChild();
    }
  }

  viewOrgChart() {
    this.apiService.orgChartTreeSearchById(
      this.data()?.employeeId ?? '',
      this.data()?.positionCode,
    );
  }

  departmentInfo = computed(() => {
    const data = this.data();
    const department = isEmpty(data?.departmentShortName)
      ? data?.departmentName
      : data?.departmentShortName;
    const company = isEmpty(data?.companyShortName)
      ? data?.companyName
      : data?.companyShortName;

    return [company, department].filter((item) => !isEmpty(item)).join(' - ');
  });

  departmentName = computed(() => {
    return this.buildInfoText('departmentName', 'departmentShortName');
  });

  companyName = computed(() => {
    return this.buildInfoText('companyName', 'companyShortName');
  });

  buildInfoText(mainKey: keyof UserCard, subKey: keyof UserCard) {
    const data = this.data();
    let text = data?.[mainKey] ?? '';

    const subText = data?.[subKey];
    if (subText) {
      text += ` (${subText})`;
    }

    if (!text) return '--';
    return text;
  }

  updateUserDetails(newdata: NzSafeAny) {
    this.itemService.changeItem({ ...this.data(), userDetails: newdata });
  }
  @ViewChild('userDetails') userDetails: NzSafeAny;
  @ViewChild('userDetailsFooter') userDetailsFooter: NzSafeAny;
  viewUserDetails() {
    this.orgChartDrawerService.changeDrawerVisible(true);
    this.orgChartDrawerService.changeDrawerConfig({
      title: 'Details',
      width: 'xsmall',
      body: this.userDetails,
      footer: this.userDetailsFooter,
    });
  }
}
