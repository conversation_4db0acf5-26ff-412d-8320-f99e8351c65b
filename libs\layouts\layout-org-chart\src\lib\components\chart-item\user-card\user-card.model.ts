import { z } from 'zod';

const userCardSchema = z.object({
  employeeRecordNumber: z.number(),
  fullName: z.string(),
  jobName: z.string(),
  avatarFile: z.string(),
  departmentName: z.string(),
  companyName: z.string(),
  matrixPositionIds: z.array(z.string()),
  direct: z.string(),
  employeeId: z.string(),
  positionCode: z.string(),
  totalChild: z.number(),
  companyShortName: z.string(),
  departmentShortName: z.string(),
  id: z.string(),
  childs: z.array(
    z.object({
      directPositionId: z.string(),
      matrixManager: z.array(z.string()).optional(),
    }),
  ),
  totalChildDirect: z.number().optional(),
  totalChildMatrix: z.number().optional(),
  userDetails: z
    .object({
      avatarFile: z.string(),
    })
    .optional(),
  numberChildDirect: z.number().optional(),
  numberChildMatrix: z.number().optional(),

  email: z.string().optional(),
  positionName: z.string().optional(),
  companyCode: z.string().optional(),
  legalEntityCode: z.string().optional(),
  legalEntityName: z.string().optional(),
  divisionCode: z.string().optional(),
  divisionName: z.string().optional(),
  businessUnitCode: z.string().optional(),
  businessUnitName: z.string().optional(),
  jobCode: z.string().optional(),
  regionCode: z.string().optional(),
  regionName: z.string().optional(),
  employeeLevelCode: z.string().nullable().optional(),
  employeeLevelName: z.string().nullable().optional(),
  locationCode: z.string().optional(),
  locationName: z.string().optional(),
  departmentCode: z.string().optional(),
  avatarFileName: z.string().optional(),
  seniorityDate: z.string().nullable().optional(),
  reportToPositionName: z.string().optional(),
  reportToPosition: z.string().optional(),
  directPositionName: z.string().optional(),
  totalParent: z.number().optional(),
  matrixManager: z.array(z.string()).optional(),
  directPositionId: z.string().optional(),
  connectionType: z.string().optional(),
  ancestryPath: z.array(z.string()).optional(),
  avatarLink: z.string().optional(),
});

type UserCard = z.infer<typeof userCardSchema>;

export { UserCard, userCardSchema };
