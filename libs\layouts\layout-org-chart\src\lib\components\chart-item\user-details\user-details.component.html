<hrdx-loading *ngIf="loading()" />
<div *ngIf="!loading()">
  <header>
    <hrdx-avatar
      [type]="apiData()?.avatarFile ? avatarType.Image : avatarType.Text"
      [text]="apiData()?.fullName?.[0]"
      [shape]="avatarShape.Circle"
      [size]="120"
      [imgSrc]="
        apiData()?.avatarFile
          ? apiData()?.avatarLink
          : ''
      "
      [imgAlt]="apiData()?.seniorityDate"
    ></hrdx-avatar>
    <div>
      <h4>
        {{ apiData()?.fullName }}
        <span *ngIf="apiData()?.email">
          ({{ apiData()?.email.split('@')[0] }})
        </span>
      </h4>
      <!-- <p class="seniority">
        {{ apiData()?.seniorityDate }}
      </p> -->
      <hrdx-button
        type="primary"
        [isLeftIcon]="true"
        leftIcon="icon-tree-structure"
        title="Org Chart"
        (clicked)="
          redirectOrgChart(
            employeeId(),
            data()?.positionCode,
            data()?.recordNumber
          )
        "
      />
    </div>
  </header>
  <main>
    <section>
      <p>
        <hrdx-icon name="icon-envelope-simple-bold" />
        <a>
          {{ apiData()?.email }}
        </a>
      </p>
      <p>
        <hrdx-icon icon="icon-suitcase-simple-bold" />
        <span>
          {{ apiData()?.departmentName }} ({{ apiData()?.departmentCode }})
        </span>
      </p>
      <p>
        <hrdx-icon icon="icon-user-circle-bold" />
        <span> {{ apiData()?.jobName }} </span>
      </p>
    </section>
    <dl>
      <dt>Direct Manager</dt>
      <dd
        [class.pointer]="quickActionPermission"
        (click)="
          quickActionPermission
            ? !fromPosition()
              ? apiService.orgChartTreeSearchById(apiData()?.directPositionId)
              : redirectOrgChart(
                  apiData()?.directPositionId,
                  apiData()?.reportToPosition,
                  apiData()?.employeeRecordNumber
                )
            : null
        "
      >
        <hrdx-icon
          icon="icon-user-circle-gear-bold"
          *ngIf="apiData()?.directPositionName"
        />
        {{ apiData()?.directPositionName }}
        <span *ngIf="apiData().directPositionEmail"
          >({{ apiData().directPositionEmail.split('@')[0] }})</span
        >
      </dd>
      <dt>Matrix Manager</dt>
      <dd
        [class.pointer]="quickActionPermission"
        *ngFor="let manager of apiData()?.matrixManager"
        (click)="
          quickActionPermission
            ? !fromPosition()
              ? apiService.orgChartTreeSearchById(manager.matrixManager)
              : redirectOrgChart(manager.matrixManager)
            : null
        "
      >
        <hrdx-icon
          icon="icon-identification-badge-bold"
          *ngIf="manager.matrixManagerName"
        />{{ manager.matrixManagerName }}
        <span *ngIf="manager.matrixManagerEmail"
          >({{ manager.matrixManagerEmail.split('@')[0] }})</span
        >
      </dd>
    </dl>
  </main>
</div>
