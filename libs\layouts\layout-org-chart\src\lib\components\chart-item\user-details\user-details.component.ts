import {
  ACTIONS,
  RoleBasedAccessControl,
} from './../../../services/RBAC/index';
import {
  Component,
  inject,
  input,
  OnChanges,
  output,
  signal,
  SimpleChanges,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  AvatarComponent,
  AvatarShape,
  AvatarType,
  ButtonComponent,
  IconComponent,
  LoadingComponent,
  ToastMessageComponent,
} from '@hrdx/hrdx-design';
import { catchError, of, switchMap, tap } from 'rxjs';
import { BffService } from '@hrdx-fe/shared';
import { ActivatedRoute } from '@angular/router';
import { NzSafeAny } from 'ng-zorro-antd/core/types';
import { ApiService } from '../../../services/api/api.service';
import { OrgChartDrawerService } from '../../../services/org-chart-drawer/org-chart-drawer.service';
import { ConfigService } from '../../../services/config/config.service';
import { QueryFilter } from '@nestjsx/crud-request';
import { ItemServicesService } from '../services/item-services.service';
import { Router } from '@angular/router';
import { AvatarService } from '../../../services/avatar/avatar.service';

@Component({
  selector: 'lib-user-details',
  standalone: true,
  imports: [
    CommonModule,
    ButtonComponent,
    AvatarComponent,
    LoadingComponent,
    IconComponent,
  ],
  templateUrl: './user-details.component.html',
  styleUrl: './user-details.component.less',
})
export class UserDetailsComponent implements OnChanges {
  readonly avatarType = AvatarType;
  readonly avatarShape = AvatarShape;

  constructor(
    private layoutconfigService: ConfigService,
    private route: ActivatedRoute,
    private orgChartDrawerService: OrgChartDrawerService,
    private toast: ToastMessageComponent,
    private _service: BffService,
    private roleBasedAccessControl: RoleBasedAccessControl,
    private ItemServices: ItemServicesService,
    private router: Router,
    private avatarService: AvatarService,
  ) {}
  apiService = inject(ApiService);
  quickActionPermission = this.roleBasedAccessControl.hasPermission(
    ACTIONS['QUICK_ACTION'],
  );
  data = input<NzSafeAny>({});
  fromPosition = input<boolean>(false);
  employeeId = input<NzSafeAny>({});
  positionCode = input<NzSafeAny>({});
  loading = signal<boolean>(false);
  apiData = signal<NzSafeAny>(null);
  searchByEffectiveDate =
    this.route.snapshot.queryParams?.['effectiveDate'] ?? new Date();
  getData() {
    if (!this.employeeId()) return;
    const localData = this.ItemServices.getObjectUser();
    if (localData) {
      this.apiData.update((prev) => ({
        ...prev,
        ...this.data(),
        childs: localData,
      }));
    }
    this.getDataByIDAndPositionCode();
  }
  getDataByIDAndPositionCode() {

    const url = '/api/personals/' + this.employeeId() + '/org-chart';
    const filter: QueryFilter[] = [];
    if (this.positionCode())
      filter.push({
        field: 'positionCode',
        operator: '$eq',
        value: this.positionCode(),
      });
    if (
      this.data()?.employeeRecordNumber === 0 ||
      this.data()?.employeeRecordNumber
    ) {
      filter.push({
        field: 'employeeRecordNumber',
        operator: '$eq',
        value: this.data()?.employeeRecordNumber,
      });
    }
    debugger;
    const ObjectOrganizationData = this.ItemServices.getObjectOrganization();
    if (ObjectOrganizationData?.type) {
      filter.push({
        field: 'organizationType',
        operator: '$eq',
        value: ObjectOrganizationData?.type,
      });
    }
    if (ObjectOrganizationData?.id) {
      filter.push({
        field: 'organizationId',
        operator: '$eq',
        value: ObjectOrganizationData?.id,
      });
    }
    if (this.searchByEffectiveDate) {
      filter.push({
        field: 'effectiveDate',
        operator: '$eq',
        value: new Date(this.searchByEffectiveDate).getTime(),
      });
    }

    of(url)
      .pipe(
        tap(() => this.loading.set(true)),
        switchMap((url) =>
          this._service.getObject(url, filter).pipe(
            tap((d: NzSafeAny) => {
              // if avatarFile is not null, generate avatar link
              if (d.item?.avatarFile) {
                this.avatarService.generateAvatarLinks(d.item?.avatarFile).then((avatarLink) => {
                  d.item.avatarLink = avatarLink;
                });
              }
              this.apiData.update((prev) => ({
                ...prev,
                ...this.data(),
                ...d.item,
                childs: d.childs,
              }));
              this.ItemServices.setObjectUser(d.item);
            }),
          ),
        ),
        catchError((err) => {
          if (err?.error?.message) {
            this.toast.showToast('error', 'Error', err?.error?.message);
          }
          return of(null);
        }),
        tap(() => this.loading.set(false)),
      )
      .subscribe();
  }
  ngOnChanges(changes: SimpleChanges): void {
    if (changes?.['employeeId'] || changes?.['positionCode']) {
      if (
        changes['employeeId'].previousValue !==
          changes['employeeId'].currentValue ||
        changes['positionCode'].previousValue !==
          changes['positionCode'].currentValue
      ) {
        this.getData();
      }
    }
  }
  viewOrgChart = output();
  redirectOrgChart(
    employeeId: string,
    positionCode?: string,
    employeeRecordNumber?: string | number,
  ) {
    this.orgChartDrawerService.changeDrawerVisible(false);
    let orgType = '';
    this.layoutconfigService.currentChartType.subscribe((data) => {
      orgType = data;
    });
    if (orgType === 'org-chart-user-card') {
      this.apiService.orgChartTreeSearchById(
        employeeId,
        positionCode,
        employeeRecordNumber,
      );
    } else {
      this.layoutconfigService.changeChartType('org-chart-user-card');
    }
    this.router.navigate([], {
      queryParams: {
        employeeId: employeeId,
        positionCode: positionCode,
        employeeRecordNumber: employeeRecordNumber,
      },
      queryParamsHandling: 'merge',
    });
  }
}
