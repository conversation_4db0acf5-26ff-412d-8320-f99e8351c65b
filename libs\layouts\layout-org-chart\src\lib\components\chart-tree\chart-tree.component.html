<div
  id="chartExport"
  [ngStyle]="{
    width: chartWidth() + 'px',
    height: rootTree()?.shape?.coverHeight + startDrawY * 2,
  }"
>
  <lib-chart-item
    *ngFor="let data of chartData()"
    [upLevel]="
      data?.shape?.y === startDrawY &&
      ((chartType === 'org-chart-position' && data?.directPositionId) ||
        (chartType === 'org-chart-object' && data?.level > 1) ||
        (chartType === 'org-chart-user-card' && data?.totalParent > 0))
    "
    [data]="data"
  ></lib-chart-item>
  <lib-chart-line
    [connection]="connection()"
    [size]="svgSize()"
  ></lib-chart-line>
</div>
