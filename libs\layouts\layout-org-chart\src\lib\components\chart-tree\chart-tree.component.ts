import { ConfigService } from './../../services/config/config.service';
import { ChartLineComponent } from './../chart-line/chart-line.component';
import {
  Component,
  computed,
  effect,
  inject,
  input,
  OnInit,
  signal,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { ChartItemComponent } from '../chart-item/chart-item.component';
import { OrgChartService } from '../../services/org-chart/org-chart.service';
import { ActivatedRoute } from '@angular/router';
import { NzSafeAny } from 'ng-zorro-antd/core/types';
import { PanService } from '../../services/pan/pan.service';
import {
  ConnectionService,
  Connection,
} from '../../services/connection/connection.service';
import { AvatarService } from '../../services/avatar/avatar.service';

function ancestryPathEqual(a?: string[], b?: string[]): boolean {
  if (!Array.isArray(a) || !Array.isArray(b)) return false;
  if (a.length !== b.length) return false;
  return a.every((val, idx) => val === b[idx]);
}

function findNodeByIdAndAncestryPath(tree: any, id: string, ancestryPath?: string[]): any | null {
  if (!tree) return null;

  // in case of same id but selected node does not have ancestryPath, then return the node
  if (tree.id === id && !ancestryPath) return tree;

  if (tree.id === id && ancestryPathEqual(tree.ancestryPath, ancestryPath)) return tree;

  // Check for children arrays (childs, groupChilds, etc.)
  const childrenArrays = ['childs', 'groupChilds'];
  for (const key of childrenArrays) {
    if (Array.isArray(tree[key])) {
      for (const child of tree[key]) {
        const found = findNodeByIdAndAncestryPath(child, id, ancestryPath);
        if (found) return found;
      }
    }
  }
  return null;
}

@Component({
  selector: 'lib-chart-tree',
  standalone: true,
  imports: [CommonModule, ChartItemComponent, ChartLineComponent],
  providers: [ConnectionService],
  templateUrl: './chart-tree.component.html',
  styleUrl: './chart-tree.component.less',
})
export class ChartTreeComponent implements OnInit {
  constructor(
    private route: ActivatedRoute,
    private panService: PanService,
    private connectionService: ConnectionService,
  ) {}
  data = input<NzSafeAny>();
  config: NzSafeAny;
  chartService = inject(OrgChartService);
  layoutconfigService = inject(ConfigService);
  chartType = 'org-chart-user-card';
  treeSetting = signal('all');
  orgChartFilter = signal(false);
  structureType = signal('2');
  startDrawX = 0;
  startDrawY = 0;
  connection = signal<Connection[]>([]);
  lastAction: 'nodeClick' | 'upOneLevel' | 'filter' | 'other' = 'other';
  expandedNodeIds: Set<string> = new Set();
  expandedNodeAncestryPaths: Set<string> = new Set();
  ngOnInit(): void {
    this.layoutconfigService.currentFs.subscribe(
      (data) => (this.config = data),
    );
    this.layoutconfigService.currentChartType.subscribe(
      (data) => (this.chartType = data),
    );
    // treeSetting
    this.layoutconfigService.currentTreeSetting.subscribe((data) =>
      this.treeSetting.set(data),
    );
    // orgChartFilter
    this.layoutconfigService.currentFocusMode.subscribe((data) =>
     {
      this.orgChartFilter.set(data)}
    );
    this.layoutconfigService.currentStructureType.subscribe((data) =>
      this.structureType.set(data),
    );
    this.connectionService.currentConnection.subscribe((data) =>
      this.connection.set(data),
    );
    this.startDrawX = this.chartService.startDrawX;
    this.startDrawY = this.chartService.startDrawY;
    this.layoutconfigService.currentLastAction.subscribe(
      (action) => (this.lastAction = action)
    );
    this.layoutconfigService.currentExpandedNodeIds.subscribe(
      (ids: Set<string>) => (this.expandedNodeIds = ids)
    );
    this.layoutconfigService.currentExpandedAncestryPaths.subscribe(  
      (ancestryPaths: Set<string>) => (this.expandedNodeAncestryPaths = ancestryPaths)
    );
  }
  chartWidth = computed(() => {
    return this.chartService.chartWidth();
  });
  chartData = signal<NzSafeAny>(null);
  /**
   * Effect to update the chart data whenever dependencies change.
   *
   * Uses lastAction (from ConfigService) to determine how to render the org chart:
   * - 'nodeClick': show the full subtree from the clicked node
   * - 'upOneLevel': show only the root and its direct children
   * - 'filter' or 'other': may trigger other rendering behaviors
   *
   * This allows the chart to distinguish between user actions and update the UI accordingly.
   */
  updateChartData = effect(
    () => {
      if (!this.data()) return this.chartData.set(null);
      const directKey = this.config?.layout_options?.orgChartOptions?.directKey;
      const indirectKey =
        this.config?.layout_options?.orgChartOptions?.indirectKey;
      const addType = this.chartService.addType(
        this.data(),
        this.chartType ?? 'org-chart-position',
      );
      let onlyShowRootChildren = true;
      if (this.lastAction === 'nodeClick') {
        onlyShowRootChildren = false;
      } else if (this.lastAction === 'upOneLevel') {
        onlyShowRootChildren = false;
      }
      const tree = this.chartService.buildTreeV2(addType, directKey, indirectKey, onlyShowRootChildren, this.expandedNodeAncestryPaths);
      const res = [];
      const drawPoint = {
        maxX: 0,
      };
      for (let i = 0; i < tree.length; i++) {
        if (i !== 0) {
          drawPoint.maxX +=
            this.chartService.getTreeSize(
              this.chartService.drawArrayFromTree(
                tree[i - 1],
                50,
                100,
                this.structureType(),
              ),
            ).maxX + 100;
        }

        switch (this.chartType) {
          case 'org-chart-user-card': {
            const orgChartFilter = this.orgChartFilter()
              ? this.chartService.focusTree(
                  tree[i],
                  this.route.snapshot.queryParams['employeeId'],
                )
              : tree[i];
            const filterTree = this.chartService.filterTree(
              orgChartFilter,
              this.treeSetting(),
              directKey,
              indirectKey,
            );
            res.push(
              ...this.chartService.drawArrayFromTree(
                filterTree,
                50,
                100,
                this.structureType(),
              ),
            );
            if (this.orgChartFilter()) {
              this.panService.isReCenter(true);
            }
            break;
          }
          case 'org-chart-position': {
            const filterTree = this.chartService.filterTree(
              tree[i],
              this.treeSetting(),
              directKey,
              indirectKey,
            );
            res.push(
              ...this.chartService.drawArrayFromTree(
                filterTree,
                drawPoint.maxX,
                100,
                this.structureType(),
              ),
            );
            break;
          }
          // Add more cases as needed
          default:
            res.push(
              ...this.chartService.drawArrayFromTree(
                tree[i],
                50,
                100,
                this.structureType(),
              ),
            );
            break;
        }
      }
      
      // Generate avatar links before setting chartData
      this.generateAvatarLinksForChart(res).then((resWithAvatars: NzSafeAny[]) => {
        this.chartData.set(resWithAvatars);
        this.connectionService.calcConnection(resWithAvatars);

        // Center on the selected node after redraw
        const selectedNode = this.layoutconfigService.getSelectedNode();
        let nodeInChart = null;
        if (selectedNode) {
          // if the chart type is org-chart-user-card, then find the node in the chart
          if (this.chartType === 'org-chart-user-card') {
            // if single item in resWithAvatars, then center on it
            if (resWithAvatars.length > 1) {
              for (const root of resWithAvatars) {
                nodeInChart = findNodeByIdAndAncestryPath(root, selectedNode.id, selectedNode.ancestryPath);
                if (nodeInChart) break;
              }
              if (nodeInChart) {
                this.panService.centerOnNode(nodeInChart);
              }
            } 
          } else if (this.chartType === 'org-chart-object') {
            // find the node in resWithAvatars
            nodeInChart = resWithAvatars.find((item: NzSafeAny) => item.id === selectedNode.id);
            if (nodeInChart) {
              this.panService.centerOnNode(nodeInChart);
            }
          }

          // clear the selected node
          this.layoutconfigService.setSelectedNode(null);
        }

        const root = this.chartData()?.find(
          (item: NzSafeAny) => item.shape?.y === this.startDrawY,
        );
        this.layoutconfigService.recenterAfterRemoveChild(this.cover.root, resWithAvatars);
        if (root) {
          this.panService.chartSizeChange(
            root.shape.coverWidth + this.startDrawX * 2,
            root.shape.coverHeight + this.startDrawY * 2,
          );
        }
      });
    },
    {
      allowSignalWrites: true,
    },
  );
  cover: {
    width: number;
    height: number;
    root: NzSafeAny;
  } = {
    width: 0,
    height: 0,
    root: null,
  };
  rootTree = computed(() => {
    if (!this.chartData()) return null;
    const root = this.chartData()?.find(
      (item: NzSafeAny) => item.shape?.y === this.startDrawY,
    );
    this.cover = {
      width: root?.shape?.coverWidth,
      height: root?.shape?.coverHeight,
      root: root,
    };
    return root;
  });

  // Calculate SVG size based on actual connection coordinates
  svgSize = computed(() => {
    const connections = this.connection();
    const rootTree = this.rootTree();
    
    let maxY = rootTree?.shape?.coverHeight + this.startDrawY * 2 || 0;
    let maxX = rootTree?.shape?.coverWidth + this.startDrawX * 2 || 0;
    
    // Check all connection coordinates to find the actual bounds needed
    connections.forEach(conn => {
      const startY = conn.start.top + conn.start.height;
      const endY = conn.end.top + conn.end.height;
      const startX = conn.start.left + conn.start.width;
      const endX = conn.end.left + conn.end.width;
      
      maxY = Math.max(maxY, startY, endY);
      maxX = Math.max(maxX, startX, endX);
    });
    
    return {
      width: maxX + 50, // Add some padding
      height: maxY + 50
    };
  });

  avatarService = inject(AvatarService);
  fCode = input<string | null>(null);

  /**
   * Generate avatar links for chart data using the reusable AvatarService
   * This prevents infinite loops by generating all avatars first, then setting data once
   */
  private async generateAvatarLinksForChart(chartItems: NzSafeAny[]): Promise<NzSafeAny[]> {
    const faceCodeValue = typeof this.fCode() === 'string' ? this.fCode() : '';
    
    return this.avatarService.generateAvatarLinks(chartItems, {
      faceCode: faceCodeValue || '',
      avatarFileProperty: 'avatarFile',
      avatarLinkProperty: 'avatarLink'
    });
  }
}
