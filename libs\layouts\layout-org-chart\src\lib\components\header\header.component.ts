import { Component, OnInit, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  BreadCrumbModule,
  TabComponent,
  TabsComponent,
  TabsType,
} from '@hrdx/hrdx-design';
import { ConfigService } from '../../services/config/config.service';
import { ACTIONS, RoleBasedAccessControl } from '../../services/RBAC';
import { Router } from '@angular/router';
import { ChildrenActionPermission, ActionPermission } from '@hrdx-fe/shared';

@Component({
  selector: 'lib-header',
  standalone: true,
  imports: [CommonModule, BreadCrumbModule, TabsComponent, TabComponent],
  templateUrl: './header.component.html',
  styleUrl: './header.component.less',
})
export class HeaderComponent implements OnInit {
  @Input() childrenActions: ChildrenActionPermission[] = [];
  horizontal = TabsType.Horizontal;
  visibleTabs: { id: string; title: string }[] = [];
  tabConfig = [
    { id: 'org-chart-user-card', title: 'Org Chart', match: (child: ChildrenActionPermission) => child.functionCode === 'FO_034' },
    // hide theo JIRA FPT_PEOPLEX_2024_PM-63274
    // { id: 'org-chart-position', title: 'Position Hierarchy', match: (child: ChildrenActionPermission) => child.functionCode === 'FO_035' },
    { id: 'org-chart-object', title: 'Organization Hierarchy', match: (child: ChildrenActionPermission) => child.functionCode === 'FO_031' },
  ];
  constructor(
    private layoutconfigService: ConfigService,
    private accessControl: RoleBasedAccessControl,
    private router: Router,
  ) {}
  currentTab = 0;
  structureConfigurePermission = false;

  onTabChange(event: string) {
    this.layoutconfigService.changeChartType(event);
    this.layoutconfigService.changeTree([]);
    this.router.navigate([], {
      queryParams: {
        tab: event,
        employeeId: null,
        positionCode: null,
        // organization: null,
        // organizationType: null,
        // structureType: null,
      },
      queryParamsHandling: 'merge',
    });
  }

  ngOnInit(): void {
    console.log('[HeaderComponent] childrenActions permission data:', this.childrenActions);
    // Filter visible tabs based on childrenActions and active 'Read' permission
    this.visibleTabs = this.tabConfig.filter(tab =>
      this.childrenActions.some(child =>
        tab.match(child) && child.actions.some((a: ActionPermission) => a.code === 'Read' && a.isActive)
      )
    );
    this.layoutconfigService.currentChartType.subscribe((chartType) => {
      const idx = this.visibleTabs.findIndex(tab => tab.id === chartType);
      this.currentTab = idx !== -1 ? idx : 0;
    });
    this.accessControl.currentDisablePermission.subscribe((disable) => {
      if (disable) {
        this.structureConfigurePermission = this.accessControl.hasPermission(
          ACTIONS['EDIT'],
        );
      }
    });
  }
}
