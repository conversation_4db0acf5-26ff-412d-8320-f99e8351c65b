import { CommonModule, DOCUMENT } from '@angular/common';
import {
  AfterViewInit,
  Component,
  computed,
  ElementRef,
  HostListener,
  inject,
  OnInit,
  signal,
  ViewChild,
} from '@angular/core';
import { PanService } from '../../services/pan/pan.service';

@Component({
  selector: 'lib-pan',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './pan.component.html',
  styleUrl: './pan.component.less',
})
export class PanComponent implements OnInit, AfterViewInit {
  coordinateAxis = {
    x: 0,
    y: 0,
    prevX: 0,
    prevY: 0,
  };
  @ViewChild('pan') pan!: ElementRef;

  isPanning = false;
  private document = inject(DOCUMENT);
  private dragThreshold = 5; // pixels - minimum movement to consider it a drag
  private totalDragDistance = 0;
  
  mousedown(event: MouseEvent) {
    event.preventDefault();
    this.isPanning = true;
    this.totalDragDistance = 0; // Reset drag distance
    this.panService.setDragging(true); // Set dragging state
    this.coordinateAxis = {
      x: event.clientX,
      y: event.clientY,
      prevX: this.translateX(),
      prevY: this.translateY(),
    };
    this.document.body.style.userSelect = 'none';
  }
  
  @HostListener('window:mouseup')
  mouseup() {
    if (this.isPanning && this.totalDragDistance > this.dragThreshold) {
      // If we dragged significantly, prevent any click events that might fire
      this.preventClicksTemporarily();
    }
    this.isPanning = false;
    this.panService.setDragging(false); // Clear dragging state
    this.document.body.style.userSelect = 'auto';
  }
  
  private preventClicksTemporarily() {
    // Add a temporary click blocker to the document
    const clickBlocker = (event: Event) => {
      event.stopPropagation();
      event.preventDefault();
    };
    
    this.document.addEventListener('click', clickBlocker, true);
    
    // Remove the click blocker after a short delay
    setTimeout(() => {
      this.document.removeEventListener('click', clickBlocker, true);
    }, 10);
  }

  scale = signal(3);
  translateX = signal(0);
  translateY = signal(0);
  containerStyle = computed(
    () =>
      `translateX(${this.translateX()}px)  translateY(${this.translateY()}px) scale(${this.scale()})`,
  );
  
  @HostListener('window:mousemove', ['$event'])
  handlePanning(event: MouseEvent) {
    if (!this.isPanning) return;
    const dx = event.clientX - this.coordinateAxis.x;
    const dy = event.clientY - this.coordinateAxis.y;
    
    // Track total drag distance (distance from initial click position)
    this.totalDragDistance = Math.sqrt(dx * dx + dy * dy);
    
    this.translateX.set(this.coordinateAxis.prevX + dx);
    this.translateY.set(this.coordinateAxis.prevY + dy);
    this.panService.translateChange(this.coordinateAxis.prevX + dx, this.coordinateAxis.prevY + dy);
  }

  expand = false;
  constructor(private panService: PanService) {}

  ngOnInit() {
    this.panService.currentExpand.subscribe((data) => (this.expand = data));
    this.panService.currentScale.subscribe((data) => {
      this.scale.set(data);
    });
    this.panService.currentTranslate.subscribe((data) => {
      this.translateX.set(data.x);
      this.translateY.set(data.y);
    });
  }
  ngAfterViewInit(): void {
    this.panService.panSizeChange(
      this.pan.nativeElement.clientWidth,
      this.pan.nativeElement.clientHeight,
    );
  }
  handleDoubleClick() {
    this.panService.zoomIn(this.scale());
  }
  @HostListener('wheel', ['$event'])
  onMouseWheel(event: WheelEvent) {
    event.preventDefault();
    if (event.ctrlKey) {
      if (event.deltaY < 0) {
        this.panService.zoomIn(this.scale());
      } else {
        this.panService.zoomOut(this.scale());
      }
    }
  }
}
