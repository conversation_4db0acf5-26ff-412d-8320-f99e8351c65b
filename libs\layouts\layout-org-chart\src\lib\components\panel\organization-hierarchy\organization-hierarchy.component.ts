import { Component, effect, inject, OnInit, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  ToastMessageComponent,
  LoadingComponent,
  SelectComponent,
} from '@hrdx/hrdx-design';
import { FormsModule } from '@angular/forms';
import { ConfigService } from '../../../services/config/config.service';
import { BffService } from '@hrdx-fe/shared';
import { catchError, of, switchMap, tap } from 'rxjs';
import { SearchByManagementStructureComponent } from './search-by-management-structure/search-by-management-structure.component';
import { EffectiveDateComponent } from '../effective-date/effective-date.component';
import { ActivatedRoute, Router } from '@angular/router';
import { QueryFilter } from '@nestjsx/crud-request';
import { NzSafeAny } from 'ng-zorro-antd/core/types';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { PanService } from '../../../services/pan/pan.service';

@Component({
  selector: 'lib-organization-hierarchy',
  standalone: true,
  imports: [
    CommonModule,
    EffectiveDateComponent,
    FormsModule,
    SearchByManagementStructureComponent,
    LoadingComponent,
    SelectComponent,
    NzSelectModule,
  ],
  templateUrl: './organization-hierarchy.component.html',
  styleUrl: './organization-hierarchy.component.less',
})
export class OrganizationHierarchyComponent implements OnInit {
  constructor(
    private router: Router,
    private layoutconfigService: ConfigService,
    private panService: PanService,
    private route: ActivatedRoute,
  ) {}
  organizationDisplay = signal('maximize');
  structureType = '2';
  filter_config: {
    value: string;
    label: string;
  }[] = [];
  config: NzSafeAny;
  chartType = signal<string>('org-chart-user-card');
  searchByEffectiveDate = signal(new Date());
  toast = inject(ToastMessageComponent);
  searchByOrganization: NzSafeAny = '';
  loading = signal(true);
  effectiveDate: NzSafeAny = new Date();
  _service = inject(BffService);
  pushStructureName(key: string) {
    debugger
    this.router.navigate([], {
      queryParams: {
        structureName: this.filter_config?.find(
          (x: { value: string }) => x.value === key,
        )?.label,
      },
      queryParamsHandling: 'merge',
    });
  }
  dataEffect = effect(
    () => {
      const url = '/api/trees/structure-type';
      of(url)
        .pipe(
          // tap(() => (this.loading.set(false))),
          switchMap((url) => this._service.getListTree(url)),
          catchError((err) => {
            this.toast.showToast('error', 'Error', err?.error?.message ?? err);
            return of(null);
          }),
          tap(() => this.loading.set(false)),
        )
        .subscribe((d: NzSafeAny) => {
          this.filter_config = d.map((data: NzSafeAny) => ({
            value: data.code,
            label: data.name,
          }));
          this.pushStructureName(this.structureType);
        });
    },
    { allowSignalWrites: true },
  );

  search() {
    if (!this.searchByOrganization?.code) return;
    this.layoutconfigService.clearLocalStorage();
    this.layoutconfigService.changeLoading(true);
    const filter: QueryFilter[] = [
      {
        field: 'effectiveDate',
        operator: '$eq',
        value: this.searchByEffectiveDate().getTime(),
      },
    ];
    const urlTree =
      '/api/trees/organization/' +
      this.structureType +
      '/' +
      this.searchByOrganization.code +
      '/' +
      this.searchByOrganization.organizationType;
    this._service
      .getPaginate(urlTree, 1, 10000, filter)
      .pipe(
        catchError((err) => {
          this.toast.showToast('error', 'Error', err?.error?.message ?? err);
          return of([]);
        }),
      )
      .subscribe((res) => {
        this.layoutconfigService.changeTree(res);
        this.layoutconfigService.changeLoading(false);
        if (this.organizationDisplay() === 'minimize') {
          this.layoutconfigService.minimizeAll();
        } else {
          this.layoutconfigService.maximizeAll();
        }
        this.router.navigate([], {
          queryParams: {
            structureType: this.structureType,
            organization: this.searchByOrganization?.code,
            organizationType: this.searchByOrganization?.organizationType,
          },
          queryParamsHandling: 'merge',
        });
      });
  }
  onChange($event: string) {
    this.structureType = $event;
    this.searchByOrganization = null;
    this.layoutconfigService.changeTree([]);
    this.layoutconfigService.changeStructureType($event);
    this.searchByEffectiveDate.set(new Date());
    this.router.navigate([], {
      queryParams: {
        effectiveDate: this.searchByEffectiveDate().getTime(),
      },
      queryParamsHandling: 'merge',
    });
    this.pushStructureName($event);
  }
  ngOnInit() {
    this.panService.setScale(0.6);
    this.layoutconfigService.currentFs.subscribe(
      (data) => (this.config = data),
    );
    this.layoutconfigService.currentChartType.subscribe((data) =>
      this.chartType.set(data),
    );
    this.layoutconfigService.currentStructureType.subscribe(
      (data) => (this.structureType = data),
    );
    this.layoutconfigService.currentOrganizationDisplay.subscribe((data) =>
      this.organizationDisplay.set(data),
    );
    const organization = this.route.snapshot.queryParams?.['organization'];
    const structureType = this.route.snapshot.queryParams?.['structureType'];
    const organizationType =
      this.route.snapshot.queryParams?.['organizationType'];
    if (organization) {
      this.layoutconfigService.changeStructureType(structureType);
      this.searchByOrganization = {
        code: organization,
        organizationType: organizationType,
      };
      this.search();
    }
  }
}
