import {
  Component,
  inject,
  input,
  OnChanges,
  OnInit,
  output,
  signal,
  SimpleChanges,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { debounceTime, Subject, tap } from 'rxjs';
import { ConfigService } from '../../../../services/config/config.service';
import { BffService } from '@hrdx-fe/shared';
import {
  IconComponent,
  LoadingComponent,
  TooltipComponent,
  TooltipPosition,
  TooltipTrigger,
} from '@hrdx/hrdx-design';
import { NzAutocompleteModule } from 'ng-zorro-antd/auto-complete';
import { FormsModule } from '@angular/forms';
import { QueryFilter } from '@nestjsx/crud-request';
import { NzSafeAny } from 'ng-zorro-antd/core/types';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'lib-search-by-management-structure',
  standalone: true,
  imports: [
    CommonModule,
    IconComponent,
    NzAutocompleteModule,
    FormsModule,
    LoadingComponent,
    TooltipComponent,
  ],
  templateUrl: './search-by-management-structure.component.html',
  styleUrl: './search-by-management-structure.component.less',
})
export class SearchByManagementStructureComponent implements OnInit, OnChanges {
  code = input<string>();
  readonly tooltipConfig = {
    trigger: TooltipTrigger.Hover,
    position: TooltipPosition.TopCenter,
    arrow: true,
  };
  effectiveDate = input<Date>(new Date());
  inputValue?: string;
  private inputSubject: Subject<string> = new Subject<string>();
  positionAPI = signal<string>('');
  constructor(
    private layoutconfigService: ConfigService,
    private route: ActivatedRoute,
  ) {
    this.inputSubject.pipe(debounceTime(300)).subscribe((value) => {
      this.handleInputChange(value);
    });
  }
  afterFirstInit = false;

  _service = inject(BffService);
  loading = signal<boolean>(false);
  options: NzSafeAny[] = [];
  url = signal<string>('');
  filter = signal<QueryFilter[]>([]);
  searchKey = signal<string>('');
  pageIndex = signal<number>(1);
  pageSize = signal<number>(10);
  pageCount = signal<number>(0);

  onInput(event: Event): void {
    const value = (event.target as HTMLInputElement).value;
    this.inputSubject.next(value);
  }
  handleInputChange(value: string): void {
    const url = '/api/trees/organization/' + this.code();
    const filter: QueryFilter[] = [
      {
        field: 'effectiveDate',
        operator: '$eq',
        value: new Date(this.effectiveDate()).getTime(),
      },
      {
        field: 'OrderBy',
        operator: '$eq',
        value: 'Type',
        // value: 'OrganizationType,NA_LongName',
      },
      {
        field: 'page',
        operator: '$eq',
        value: this.pageIndex(),
      },
    ];
    this.url.set(url);
    this.filter.set(filter);
    this.searchKey.set(value);
    this.options = [];
    this.pageIndex.set(1);
    this.pageSize.set(10);
    this.loading.set(true);
    this.loadContent(this.pageIndex(), this.pageSize());
  }
  loadContent(index: number, pageSize: number): void {
    this._service
      .getPaginate(this.url(), index, pageSize, this.filter(), this.searchKey())
      .pipe(tap(() => this.loading.set(false)))
      .subscribe((d: NzSafeAny) => {
        this.options = this.searchKey() ? [...this.options, ...d.data] : [];
        this.pageCount.set(d.pageCount);
        if (this.afterFirstInit) {
          this.inputValue = this.options[0].shortName;
          this.onChange(this.options[0].id);
          this.afterFirstInit = false;
        }
      });
  }
  displayName(fullName: string, key: string) {
    const regex = new RegExp(key, 'gi');
    return key
      ? fullName.replace(regex, (match) => `<b>${match}</b>`)
      : fullName;
  }
  onHover(index: NzSafeAny): void {
    if (
      index > this.pageSize() * this.pageIndex() - 1 &&
      !this.loading() &&
      this.pageIndex() < this.pageCount()
    ) {
      this.pageIndex.update((i) => i + 1);
      this.filter.update((f) => [
        ...f,
        {
          field: 'page',
          operator: '$eq',
          value: this.pageIndex(),
        },
      ]);
      this.loadContent(this.pageIndex(), this.pageSize());
    }
  }
  organizationChange = output<{
    id: string;
    shortName: string;
    longName: string;
    organizationType: string;
    code: string;
  }>();
  onChange(value: string): void {
    for (let i = 0; i < this.options.length; i++) {
      if (this.options[i].id === value) {
        this.inputValue = value;
        this.organizationChange.emit(this.options[i]);
      }
    }
  }
  ngOnInit() {
    this.layoutconfigService.currentFs.subscribe((data) =>
      this.positionAPI.set(data?.filter_config?.position?.api),
    );
    const organization = this.route.snapshot.queryParams?.['organization'];
    if (organization) {
      this.inputSubject.next(organization);
      this.afterFirstInit = true;
    }
  }
  ngOnChanges(changes: SimpleChanges): void {
    let shouldReset = false;
    if (changes?.['code'] && !changes?.['code']?.firstChange) {
      shouldReset = true;
    }
    if (changes?.['effectiveDate'] && !changes?.['effectiveDate']?.firstChange) {
      shouldReset = true;
    }
    if (shouldReset) {
      this.inputValue = '';
      this.options = [];
      this.handleInputChange(this.searchKey());
    }
  }
}
