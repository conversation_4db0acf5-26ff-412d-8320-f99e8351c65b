<hrdx-avatar
  [type]="avatarIMG"
  [text]="data.name"
  [shape]="avatarShape"
  [size]="avatarSize"
  [imgSrc]="
    data.avatarFile
      ? data.avatarLink
      : ''
  "
  [imgAlt]=""
></hrdx-avatar>
<div>
  <h6>
    {{ data.name }} ({{ data.employeeId }})
    <ng-container
      *ngIf="data.employeeRecordNumber === 0 || data.employeeRecordNumber"
    >
      - {{ data.employeeRecordNumber }}
    </ng-container>
  </h6>
  <ul>
    <li>
      <hrdx-icon icon="envelope-simple"></hrdx-icon>
      <hrdx-tooltip [title]="data.email">
        {{ data.email }}
      </hrdx-tooltip>
    </li>
    <li>
      <hrdx-icon icon="dot-bold"></hrdx-icon>
    </li>
    <li>
      <hrdx-icon icon="user-circle"></hrdx-icon>
      <hrdx-tooltip [title]="data.jobName">
        {{ data.jobName }}
      </hrdx-tooltip>
    </li>
    <li>
      <hrdx-icon icon="dot-bold"></hrdx-icon>
    </li>
    <li>
      <hrdx-icon icon="suitcase-simple"></hrdx-icon>
      <hrdx-tooltip [title]="data.departmentName">
        {{ data.departmentName }}
      </hrdx-tooltip>
    </li>
  </ul>
</div>
