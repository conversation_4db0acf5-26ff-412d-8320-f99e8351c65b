import { Component, OnInit, output, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NzAutocompleteModule } from 'ng-zorro-antd/auto-complete';
import {
  IconComponent,
  LoadingComponent,
  ToastMessageComponent,
} from '@hrdx/hrdx-design';
import { PeopleItemComponent } from './people-item/people-item.component';
import { catchError, debounceTime, of, Subject, switchMap, tap } from 'rxjs';
import { BffService } from '@hrdx-fe/shared';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { NzSafeAny } from 'ng-zorro-antd/core/types';
import { AvatarService } from '../../../services/avatar/avatar.service';

@Component({
  selector: 'lib-search-by-people',
  standalone: true,
  imports: [
    CommonModule,
    NzAutocompleteModule,
    IconComponent,
    PeopleItemComponent,
    FormsModule,
    LoadingComponent,
  ],
  templateUrl: './search-by-people.component.html',
  styleUrl: './search-by-people.component.less',
})
export class SearchByPeopleComponent implements OnInit {
  private inputSubject: Subject<string> = new Subject<string>();
  options: NzSafeAny[] = [];

  constructor(
    private router: Router,
    private _service: BffService,
    private toast: ToastMessageComponent,
    private route: ActivatedRoute,
    private avatarService: AvatarService,
  ) {
    this.inputSubject.pipe(debounceTime(500)).subscribe((value) => {
      // if value is empty, either trim or not, skip the request
      if (value === null || value === undefined || value.trim() === '') {
        this.options = [];
        return;
      }
      this.handleInputChange(value);
    });
  }
  loading = signal<boolean>(false);
  inputValue = '';

  onInput(event: Event): void {
    const value = (event.target as HTMLInputElement).value;
    this.inputSubject.next(value);
  }
  afterFirstInit = false;
  handleInputChange(value: string): void {
    // set loading
    this.loading.set(true);
    const url = 'api/personals';
    this._service
      .getPaginate(url, 1, 25, [
        {
          field: 'hrStatus',
          operator: '$eq',
          value: 'A',
        },
        {
          field: 'ignorequeryfilter',
          operator: '$eq',
          value: true,
        },
        {
          field: 'effectiveDate',
          operator: '$eq',
          value: new Date(),
        },
        {
          field: 'jobDataEffectiveDateFrom',
          operator: '$lte',
          value: new Date(),
        },
      ], value)
      .pipe(
        catchError((err) => {
          this.toast.showToast('error', 'Error', err?.error?.message ?? err);
          return of({ data: [] }); // Return proper structure for consistency
        }),
        switchMap((d: NzSafeAny) => {
          // Generate avatar links for the people data
          if (d.data && Array.isArray(d.data)) {
            return this.avatarService.generateAvatarLinks(d.data, {
              faceCode: '', // You can add faceCode input if needed
              avatarFileProperty: 'avatarFile',
              avatarLinkProperty: 'avatarLink'
            }).then((dataWithAvatars) => ({ ...d, data: dataWithAvatars }));
          }
          return of(d);
        }),
        tap(() => this.loading.set(false)),
      )
      .subscribe((d: NzSafeAny) => {
        this.options = value ? d.data : [];
        if (this.afterFirstInit) {
          this.inputValue = this.options[0].name;
          this.onChange(this.options[0]);
          this.afterFirstInit = false;
        }
      });
  }
  peopleChange = output<string>();
  onChange(value: NzSafeAny): void {
    for (let i = 0; i < this.options?.length; i++) {
      if (this.options[i].employeeId === value?.employeeId) {
        this.peopleChange.emit(value);
        this.router.navigate([], {
          queryParams: {
            employeeId: value.employeeId,
            positionCode: value.positionCode,
          },
          queryParamsHandling: 'merge',
        });
        return;
      }
    }
  }
  ngOnInit(): void {
    const employeeId = this.route.snapshot.queryParams?.['employeeId'];
    if (employeeId) {
      this.afterFirstInit = true;
      this.inputSubject.next(employeeId);
    }
  }
}
