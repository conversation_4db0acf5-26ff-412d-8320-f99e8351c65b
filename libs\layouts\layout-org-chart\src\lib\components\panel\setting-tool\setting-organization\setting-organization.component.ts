import { Component, inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ButtonComponent } from '@hrdx/hrdx-design';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { ConfigService } from '../../../../services/config/config.service';
import { NzSafeAny } from 'ng-zorro-antd/core/types';

@Component({
  selector: 'lib-setting-organization',
  standalone: true,
  imports: [CommonModule, ButtonComponent, NzDropDownModule],
  templateUrl: './setting-organization.component.html',
  styleUrl: './setting-organization.component.less',
})
export class SettingOrganizationComponent implements OnInit {
  displayOptions = [
    {
      label: 'Minimize All',
      name: 'minimize',
      icon: 'icon-arrows-in-simple-bold',
    },
    {
      label: 'Maximize All',
      name: 'maximize',
      icon: 'icon-arrows-out-simple-bold',
    },
  ];
  display = 'maximize';
  structureType = '2';
  tree: NzSafeAny[] = [];
  constructor(private layoutconfigService: ConfigService) {}
  onDisplayChange(value: string) {
    this.display = value;
    this.layoutconfigService.changeOrganizationDisplay(value);
    if (value === 'minimize') {
      this.layoutconfigService.minimizeAll();
    } else {
      this.layoutconfigService.maximizeAll();
    }
  }
  ngOnInit(): void {
    this.layoutconfigService.currentTree.subscribe(
      (data) => (this.tree = data),
    );
    this.layoutconfigService.currentStructureType.subscribe(
      (data) => (this.structureType = data),
    );
  }
}
