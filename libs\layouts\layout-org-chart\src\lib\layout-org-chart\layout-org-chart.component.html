<!-- <h1>{{ 'HELLO' | translate }}</h1>
<p>{{ 'WELCOME' | translate }}</p> -->
<nz-header>
  <lib-header [childrenActions]="childrenActions()" />
</nz-header>
<nz-layout>
  <nz-content>
    <nz-layout [class.fullscreen]="expand">
      <nz-header>
        <lib-panel></lib-panel>
      </nz-header>
      <nz-content class="main">
        <ng-container *ngIf="loading()">
          <div class="loading">
            <hrdx-loading />
          </div>
        </ng-container>
        <lib-pan>
          <div class="container">
            <ng-container *ngIf="!loading()">
              <lib-chart-tree [data]="data()" [fCode]="faceCode()"> </lib-chart-tree>
            </ng-container>
          </div>
        </lib-pan>
      </nz-content>
    </nz-layout>
  </nz-content>
</nz-layout>
<lib-org-chart-drawer />
