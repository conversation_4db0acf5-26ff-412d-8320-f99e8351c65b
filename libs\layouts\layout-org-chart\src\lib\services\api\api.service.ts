import { inject, Injectable } from '@angular/core';
import { ConfigService } from '../config/config.service';
import { ToastMessageComponent } from '@hrdx/hrdx-design';
import { BffService } from '@hrdx-fe/shared';
import { catchError, of } from 'rxjs';
import { QueryFilter } from '@nestjsx/crud-request';
import { NzSafeAny } from 'ng-zorro-antd/core/types';
import { ActivatedRoute } from '@angular/router';

@Injectable()
export class ApiService {
  layoutconfigService = inject(ConfigService);
  route = inject(ActivatedRoute);
  toast = inject(ToastMessageComponent);

  _service = inject(BffService);
  orgChartTreeSearchById(
    id: string,
    positionCode?: string,
    employeeRecordNumber?: string | number,
  ) {
    this.layoutconfigService.changeLoading(true);
    this.layoutconfigService.changeFocusMode(false);
    const urlTree = '/api/personals/' + id + '/build-org-chart';
    const filter: QueryFilter[] = [];
    if (positionCode)
      filter.push({
        field: 'positionCode',
        operator: '$eq',
        value: positionCode,
      });
    if (employeeRecordNumber === 0 || employeeRecordNumber)
      filter.push({
        field: 'employeeRecordNumber',
        operator: '$eq',
        value: employeeRecordNumber,
      });

    this._service
      .getListTree(urlTree, filter)
      .pipe(
        // tap(() => this.loading.set(true)),
        catchError((err) => {
          this.toast.showToast('error', 'Error', err?.error?.message ?? err);
          return of([]);
        }),
        // tap(() => this.loading.set(false)),
      )
      .subscribe((res: NzSafeAny) => {
        // Process child relationships for potential future use, but don't add them immediately
        const childs =
          res?.childs?.map((item: NzSafeAny) => {
            // if reportToPosition is the same as the parent positionCode, then it is a directPosition
            if (
              !!item.reportToPosition &&
              !!res.item.positionCode &&
              !item.directPositionId &&
              item.reportToPosition === res.item.positionCode
            ) {
              item.directPositionId = res.item.id;
              return item;
            }
            // if the directPositionId is not the same as the parent id and the matrixPositionIds does not include the parent id, then add the parent id to the matrixPosition
            if (
              item.directPositionId !== res.item.id &&
              !item.matrixPositionIds.includes(res.item.id)
            ) {
              item.matrixPositionIds.push(res.item.id);
              return item;
            }
            return item;
          }) ?? [];
        
        // On search, only show the root item initially. Children will be loaded when user expands.
        // Set childs property on root item to indicate children are available for expansion
        const rootItem = { ...res.item, childs: childs.length > 0 ? [] : undefined };
        this.layoutconfigService.changeTree([rootItem]);
        
        this.layoutconfigService.changeLoading(false);
      });
  }
  positionHierarchySearchByPositionCode(positionCode: string) {
    this.layoutconfigService.changeLoading(true);
    const effectiveDate =
      this.route.snapshot.queryParams?.['effectiveDate'] ?? new Date();
    const showInactive =
      this.route.snapshot.queryParams?.['showInactive'] === 'Y';
    const urlTree = '/api/positions/' + positionCode + '/tree';
    const filter: QueryFilter[] = [
      {
        field: 'effectiveDate',
        operator: '$eq',
        value: new Date(effectiveDate).getTime(),
      },
      { field: 'showInactive', operator: '$eq', value: showInactive },
    ];
    this._service
      .getPaginate(urlTree, 1, 10000, filter)
      .pipe(
        catchError((err) => {
          this.toast.showToast('error', 'Error', err?.error?.message ?? err);
          return of([]);
        }),
      )
      .subscribe((res) => {
        const lstTree: NzSafeAny = res;
        for (let index = 0; index < lstTree?.length; index++) {
          if (lstTree[index].positionCode === positionCode) {
            lstTree[index].isRoot = true;
          }
        }
        // remove unnecessary connections
        const root = lstTree.find(
          (item: NzSafeAny) => item.positionCode === positionCode,
        );
        lstTree.map((item: NzSafeAny) => {
          item.matrixPositionIds = item?.matrixPositionIds?.filter(
            (id: string) => id === root.id,
          );
          if (item.id !== root.id) {
            item.directPositionId =
              item.directPositionId !== root.id ? null : item.directPositionId;
          }
        });

        this.layoutconfigService.changeTree(lstTree);
        this.layoutconfigService.changeLoading(false);
      });
  }
}
