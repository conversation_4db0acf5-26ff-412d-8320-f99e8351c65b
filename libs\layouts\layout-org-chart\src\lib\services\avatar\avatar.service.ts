import { inject, Injectable } from '@angular/core';
import { BffService } from '@hrdx-fe/shared';
import { NzSafeAny } from 'ng-zorro-antd/core/types';

export interface AvatarOptions {
  faceCode?: string;
  avatarFileProperty?: string;
  avatarLinkProperty?: string;
}

@Injectable({
  providedIn: 'root',
})
export class AvatarService {
  private _service = inject(BffService);
  private avatarLinkCache = new Map<string, Promise<string>>();

  /**
   * Generate avatar links for an array of objects
   * @param items - Array of objects that may contain avatar files
   * @param options - Configuration options for avatar generation
   * @returns Promise resolving to the same array with avatar links added
   */
  async generateAvatarLinks<T extends Record<string, NzSafeAny>>(
    items: T[],
    options: AvatarOptions = {}
  ): Promise<T[]> {
    if (!Array.isArray(items)) return items;

    const {
      faceCode = '',
      avatarFileProperty = 'avatarFile',
      avatarLinkProperty = 'avatarLink'
    } = options;

    // Create array of promises for all avatar generations
    const avatarPromises = items.map(async (item) => {
      const avatarFile = typeof item[avatarFileProperty] === 'string' && item[avatarFileProperty]
        ? item[avatarFileProperty]
        : undefined;

      if (typeof avatarFile === 'string' && avatarFile.length > 0) {
        try {
          let linkPromise = this.avatarLinkCache.get(avatarFile);
          if (!linkPromise) {
            linkPromise = this._service.generateAvatarLink(avatarFile, faceCode);
            this.avatarLinkCache.set(avatarFile, linkPromise);
          }
          const avatarLink = await linkPromise;
          return { ...item, [avatarLinkProperty]: avatarLink };
        } catch (err) {
          this.avatarLinkCache.delete(avatarFile);
          return item; // Return original item if avatar generation fails
        }
      }
      return item; // Return original item if no avatarFile
    });

    // Wait for all avatar generations to complete
    return Promise.all(avatarPromises);
  }

  /**
   * Clear the avatar cache
   */
  clearCache(): void {
    this.avatarLinkCache.clear();
  }

  /**
   * Remove a specific avatar from cache
   * @param avatarFile - The avatar file to remove from cache
   */
  removeCachedAvatar(avatarFile: string): void {
    this.avatarLinkCache.delete(avatarFile);
  }

  /**
   * Check if an avatar is cached
   * @param avatarFile - The avatar file to check
   * @returns boolean indicating if the avatar is cached
   */
  isCached(avatarFile: string): boolean {
    return this.avatarLinkCache.has(avatarFile);
  }
} 