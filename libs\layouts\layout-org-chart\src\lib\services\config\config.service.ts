import { BehaviorSubject } from 'rxjs';

import { inject, Injectable } from '@angular/core';
import { OrgChartService } from '../org-chart/org-chart.service';
import { PanService } from '../pan/pan.service';
import { NzSafeAny } from 'ng-zorro-antd/core/types';

export const KEY_CODE_PREFIX = 'node-';
@Injectable({
  providedIn: 'root',
})
export class ConfigService {
  private fs = new BehaviorSubject<NzSafeAny>({});
  chartService = inject(OrgChartService);
  panService = inject(PanService);
  currentFs = this.fs.asObservable();
  changeFS(fs: NzSafeAny) {
    this.fs.next(fs);
  }

  private chartType = new BehaviorSubject<string>('org-chart-user-card');
  currentChartType = this.chartType.asObservable();
  changeChartType(chartType: string) {
    this.treeSetting.next('all');
    this.chartType.next(chartType);
  }

  private organizationDisplay = new BehaviorSubject<string>('maximize');
  currentOrganizationDisplay = this.organizationDisplay.asObservable();
  changeOrganizationDisplay(display: string) {
    this.organizationDisplay.next(display);
  }

  private removedElementWatcher = new BehaviorSubject<NzSafeAny>(null);
  currentRemovedElementWatcher = this.removedElementWatcher.asObservable();
  changeRemovedElementWatcher(removeElement: NzSafeAny) {
    this.removedElementWatcher.next(removeElement);
  }

  getHeight(item: NzSafeAny) {
    //this height should return correctly height of the node when it have emp list.

    // if (colorsShort.includes(item.color)) {
    //   return 222;
    // }
    return 292;
  }

  minimizeAll() {
    let tree: NzSafeAny = [];
    this.currentTree.subscribe((data) => (tree = data));
    this.changeTree(
      tree.map((item: NzSafeAny) => ({
        ...item,
        expandedEmployee: false,
        shape: { ...item.shape, height: 92 },
      })),
      false,
    );
  }
  maximizeAll() {
    let tree: NzSafeAny = [];
    this.currentTree.subscribe((data) => (tree = data));
    this.changeTree(
      tree.map((item: NzSafeAny) => ({
        ...item,
        expandedEmployee: true,
        shape: { ...item.shape, height: this.getHeight(item) },
      })),
      false,
    );
  }

  private structureType = new BehaviorSubject<string>('2');
  currentStructureType = this.structureType.asObservable();
  changeStructureType(structureType: string) {
    this.structureType.next(structureType);
  }

  private tree = new BehaviorSubject<NzSafeAny>([]);
  currentTree = this.tree.asObservable();
  changeTree(tree: NzSafeAny, reCenter = true, parentKey?: string) {
    // if last action is upOneLevel, then update the ancestryPath of the tree
    if (this.lastAction.getValue() === 'upOneLevel' && parentKey) {
      this.updateAncestryPathsOnUpLevel(tree, parentKey);

      // Filter out duplicate nodes with same id and ancestryPath
      tree = this.filterDuplicatesByIdAndAncestryPath(tree);
    }

    this.tree.next(tree);
    if (reCenter) {
      this.panService.isReCenter(true);
    } else {
      this.panService.isReCenter(false);
    }
  }

  /**
   * Filters out duplicate nodes based on id and ancestryPath.
   * Keeps the first occurrence of each unique combination.
   * @param nodes Array of nodes to filter
   * @param existingTree Optional existing tree to check against for duplicates
   * @returns Filtered array with unique id + ancestryPath combinations
   */
  private filterDuplicatesByIdAndAncestryPath(
    nodes: NzSafeAny[], 
    existingTree?: NzSafeAny[]
  ): NzSafeAny[] {
    if (!nodes || nodes.length === 0) {
      return [];
    }

    const seen = new Set<string>();
    
    // If existing tree is provided, add existing combinations to the seen set
    if (existingTree && existingTree.length > 0) {
      existingTree.forEach(item => {
        const key = this.createIdAncestryPathKey(item.id, item.ancestryPath);
        seen.add(key);
      });
    }

    return nodes.filter(item => {
      const key = this.createIdAncestryPathKey(item.id, item.ancestryPath);
      
      if (seen.has(key)) {
        return false; // Duplicate found, filter out
      }
      
      seen.add(key);
      return true; // Unique combination, keep it
    });
  }

  /**
   * Creates a unique key from id and ancestryPath for duplicate detection
   * @param id Node id
   * @param ancestryPath Array of ancestor ids
   * @returns Unique string key
   */
  private createIdAncestryPathKey(id: string, ancestryPath?: string[]): string {
    const pathString = Array.isArray(ancestryPath) 
      ? ancestryPath.join('/') 
      : '';
    return `${id}|${pathString}`;
  }

  addChild(node: NzSafeAny, parentKey?: string, ancestryPath?: string[]) {
    let tree: NzSafeAny = [];
    this.currentTree.subscribe((data) => (tree = data));

    // if last action is not upOneLevel, then update ancestryPath of the filteredNode
    if (this.lastAction.getValue() !== 'upOneLevel' && parentKey) {
      this.updateFilteredNodeAncestryPath(node, parentKey, tree, ancestryPath);
    }

    // filter out duplicate nodes based on id and ancestryPath
    const filteredNode = this.filterDuplicateNodes(node, tree);

    if (parentKey) {
      filteredNode.map((item: NzSafeAny) => {
        if (item.directPositionId !== parentKey.split('-')[0]) {
          item.directPositionId = '';
        } else {
          item.directPositionId = parentKey;
        }
        item.matrixPositionIds =
          item.matrixPositionIds?.filter(
            (matrixId: string) => matrixId === parentKey,
          ) ?? [];
        return item;
      });
    }
    // same Id but different directPositionId
    filteredNode.map((item: NzSafeAny) => {
      const existingNode = tree.find(
        (treeNode: NzSafeAny) => treeNode.id === item.id && JSON.stringify(treeNode.ancestryPath) === JSON.stringify(item.ancestryPath),
      );
      if (existingNode) {
        if (existingNode.directPositionId !== item.directPositionId) {
          item.id = `${item.id}-${item.directPositionId ?? item.matrixPositionIds?.[0]}`;
        } else if (
          existingNode.matrixPositionIds?.length &&
          existingNode.matrixPositionIds[0] !== item.matrixPositionIds[0]
        ) {
          item.id = `${item.id}-${item.directPositionId}`;
        }
      }
    });

    this.changeTree([...tree, ...filteredNode], false, parentKey);
  }

  addParent(node: NzSafeAny) {
    let tree: NzSafeAny = [];
    node.isRoot = true;
    this.currentTree.subscribe((data) => (tree = data));
    for (let i = 0; i < tree.length; i++) {
      tree[i].isRoot = false;
    }
    this.changeTree([node, ...tree]);
  }

  changeNode(node: NzSafeAny) {
    let tree: NzSafeAny = [];
    this.currentTree.subscribe((data) => (tree = data));
    const index = tree.findIndex(
      (item: NzSafeAny) =>
        item.id === node.id && item.directPositionId === node.directPositionId,
    );
    tree[index] = node;
    this.changeTree(tree, false);
  }
  changeNodeByID(node: NzSafeAny) {
    let tree: NzSafeAny = [];
    this.currentTree.subscribe((data) => (tree = data));
    const index = tree.findIndex((item: NzSafeAny) => item.id === node.id);
    tree[index] = node;
    this.changeTree(tree, false);
  }

  removeChild(id: string) {
    let tree: NzSafeAny = this.tree.getValue();
    const blackList = [id];
    while (blackList.length) {
      tree = tree.filter((item: NzSafeAny) => {
        if (item.directPositionId === blackList[0]) {
          blackList.push(item.id);
        }
        return item.directPositionId !== blackList[0];
      });
      blackList.shift();
    }
    const nodeRemove = tree.find((node: NzSafeAny) => node.id === id);
    this.removedElementWatcher.next(nodeRemove);
    this.changeTree(tree, false);
  }

  removeChildsByParentId(id: string) {
    let tree: NzSafeAny = [];
    this.currentTree.subscribe((data) => (tree = data));
    const blackList = [id];
    while (blackList.length) {
      tree = tree.filter((item: NzSafeAny) => {
        if (
          item.directPositionId === blackList[0] ||
          item.matrixPositionIds?.includes(blackList[0])
        ) {
          blackList.push(item.id);
        }
        return (
          item.directPositionId !== blackList[0] &&
          !item.matrixPositionIds?.includes(blackList[0])
        );
      });
      blackList.shift();
    }
    this.changeTree(tree, false);
  }

  /**
   * Remove all nodes whose ancestryPath includes the given id
   * @param ancestryPath 
   */
  removeChildsByAncestryPath(ancestryPath: string[]) {
    let tree: NzSafeAny = [];
    this.currentTree.subscribe((data) => (tree = data));
    // Remove all nodes whose ancestryPath includes the given id
    tree = tree.filter((item: NzSafeAny) => {
      // ancestryPath is expected to be an array of strings
      return JSON.stringify(item.ancestryPath) !== JSON.stringify(ancestryPath);
    });
    this.changeTree(tree, false);
  }

  private loading = new BehaviorSubject<boolean>(false);
  curentLoading = this.loading.asObservable();
  changeLoading(loading: boolean) {
    this.loading.next(loading);
  }

  private treeSetting = new BehaviorSubject<'all' | 'direct' | 'matrix'>('all');
  currentTreeSetting = this.treeSetting.asObservable();
  changeTreeSetting(setting: NzSafeAny) {
    this.treeSetting.next(setting);
  }

  private focusMode = new BehaviorSubject<boolean>(false);
  currentFocusMode = this.focusMode.asObservable();
  changeFocusMode(focusMode: boolean) {
    this.focusMode.next(focusMode);
  }

  clearLocalStorage() {
    const keys = Object.keys(localStorage);
    keys.forEach((key) => {
      if (key.startsWith(KEY_CODE_PREFIX)) {
        localStorage.removeItem(key);
      }
    });
  }

  private ownerAccount = new BehaviorSubject<NzSafeAny>(null);
  currentOwnerAccount = this.ownerAccount.asObservable();
  changeOwnerAccount(data: NzSafeAny) {
    this.ownerAccount.next(data);
  }
  findNodeById(
    id: string,
    tree: {
      id: string;
      childs?: NzSafeAny[];
    },
  ): NzSafeAny | null {
    if (tree?.id === id) {
      return tree;
    }
    if (!tree?.childs) return null;
    for (const node of tree.childs) {
      if (node?.id === id) {
        return node;
      }
      if (node.childs) {
        const found = this.findNodeById(id, node);
        if (found) {
          return found;
        }
      }
    }
    return null;
  }

  recenterAfterRemoveChild(prevChartData: NzSafeAny, chartData: NzSafeAny) {
    let removedElementWatcher: NzSafeAny = null;
    this.removedElementWatcher.subscribe((data) => {
      removedElementWatcher = data;
    });
    let translateX = 0;
    let translateY = 0;
    this.panService.currentTranslate.subscribe((data) => {
      translateX = data.x;
      translateY = data.y;
    });
    let scale = 1;
    this.panService.currentScale.subscribe((data) => (scale = data));
    if (removedElementWatcher) {
      const nodeRemove = chartData.find(
        (node: NzSafeAny) => node.id === removedElementWatcher.id,
      );
      const nodeRemovePrev = this.findNodeById(
        removedElementWatcher.id,
        prevChartData,
      );
      if (!nodeRemove || !nodeRemovePrev) return;
      const dx = (nodeRemove.shape.x - nodeRemovePrev.shape.x) * scale;
      this.panService.translateChange(translateX - dx, translateY);
      this.changeRemovedElementWatcher(null);
      return;
    }
    if (!prevChartData) {
      let pansize = {
        width: 0,
        height: 0,
      };
      this.panService.currentPanSize.subscribe((data) => {
        pansize = data;
      });
      const dx = chartData[chartData.length - 1]?.shape.x * scale;
      this.panService.translateChange((pansize.width / 2) * scale - dx, 0);
      return;
    }
  }

  private nodeIndex = new BehaviorSubject<number>(0);
  currentNodeIndex = this.nodeIndex.asObservable();
  changeNodeIndex() {
    this.nodeIndex.next(this.nodeIndex.getValue() + 1);
  }

  /**
   * Tracks the last user action that affects the org chart rendering.
   * Possible values: 'nodeClick', 'upOneLevel', 'filter', 'other'.
   * Used by chart components to distinguish between user interactions and update the UI accordingly.
   */
  private lastAction = new BehaviorSubject<'nodeClick' | 'upOneLevel' | 'filter' | 'other'>('other');
  currentLastAction = this.lastAction.asObservable();
  /**
   * Sets the lastAction value to indicate the most recent user action.
   * This informs downstream chart components/effects how to render the org chart.
   * @param action The user action type ('nodeClick', 'upOneLevel', 'filter', or 'other')
   */
  setLastAction(action: 'nodeClick' | 'upOneLevel' | 'filter' | 'other') {
    this.lastAction.next(action);
  }

  /**
   * Tracks the set of currently expanded node IDs in the org chart.
   * Each node can be expanded/collapsed independently.
   */
  private expandedNodeIds = new BehaviorSubject<Set<string>>(new Set());
  currentExpandedNodeIds = this.expandedNodeIds.asObservable();

  /**
   * Add a node ID to the expanded set (expand the node)
   */
  expandNode(nodeId: string) {
    const newSet = new Set(this.expandedNodeIds.getValue());
    newSet.add(nodeId);
    this.expandedNodeIds.next(newSet);
  }

  /**
   * Remove a node ID from the expanded set (collapse the node)
   */
  collapseNode(nodeId: string) {
    const newSet = new Set(this.expandedNodeIds.getValue());
    newSet.delete(nodeId);
    this.expandedNodeIds.next(newSet);
  }

  /**
   * Toggle a node's expanded/collapsed state
   */
  toggleNodeExpansion(nodeId: string) {
    const newSet = new Set(this.expandedNodeIds.getValue());
    if (newSet.has(nodeId)) {
      newSet.delete(nodeId);
    } else {
      newSet.add(nodeId);
    }
    this.expandedNodeIds.next(newSet);
  }

  /**
   * Collapse all nodes
   */
  collapseAllNodes() {
    this.expandedNodeIds.next(new Set());
  }

  /**
   * Helper to get node by id, supporting ids in the form nodeid-parentId
   */
  private getNodeById(id: string, nodeMap: Map<string, NzSafeAny>): NzSafeAny | undefined {
    let node = nodeMap.get(id);
    if (!node && id.includes('-')) {
      node = nodeMap.get(id.split('-')[0]);
    }
    return node;
  }

  findAncestorsToRoot(startId: string, tree: NzSafeAny[]): NzSafeAny[] {
    const nodeMap = new Map(tree.map(n => [n.id, n]));
    const ancestors: NzSafeAny[] = [];
    let currentNode = this.getNodeById(startId, nodeMap);
    while (currentNode) {
      // 1. If isRoot, stop
      if (currentNode.isRoot) break;

      let parentNode: NzSafeAny | undefined = undefined;

      // 2. Check directPositionId
      if (currentNode.directPositionId) {
        parentNode = this.getNodeById(currentNode.directPositionId, nodeMap);
        if (parentNode) {
          ancestors.push(parentNode);
          currentNode = parentNode;
          continue;
        }
      }

      // 3. Check matrixPositionIds
      if (Array.isArray(currentNode.matrixPositionIds) && currentNode.matrixPositionIds.length > 0) {
        let foundMatrixParent = false;
        for (const matrixId of currentNode.matrixPositionIds) {
          const matrixParent = this.getNodeById(matrixId, nodeMap);
          if (matrixParent) {
            ancestors.push(matrixParent);
            currentNode = matrixParent;
            foundMatrixParent = true;
            break;
          }
        }
        if (foundMatrixParent) continue;
      }

      // 4. No parent found, stop
      break;
    }
    return ancestors;
  }

  updateAncestryPathsOnUpLevel(flatNodes: NzSafeAny[], newRootId: string) {
    if (!flatNodes.some(node => node.ancestryPath && node.ancestryPath.length)) {
      // First time: build ancestryPath by traversing parent relationships
      for (const node of flatNodes) {
        if (node.id === newRootId) {
          node.ancestryPath = [];
        } else {
          node.ancestryPath = this.findAncestorsToRoot(node.id, flatNodes).map(n => n.id).filter(id => id !== node.id);
        }
      }
    } else {
      // Subsequent times: append new root id to the end of ancestryPath, but skip the root node itself
      for (const node of flatNodes) {
        if (node.id === newRootId) {
          node.ancestryPath = [];
        } else {
          node.ancestryPath = [...(node.ancestryPath || []), newRootId];
        }
      }
    }
  }

  /**
   * Updates ancestryPath for each node in filteredNode based on parentKey and the current tree.
   */
  private updateFilteredNodeAncestryPath(filteredNode: NzSafeAny[], parentKey: string, tree: NzSafeAny[], ancestryPath?: string[]) {
    const nodeMap = new Map((tree as NzSafeAny[]).map((n: NzSafeAny) => [n.id, n]));
    // Updated getNodeById to support ancestryPath
    const getNodeById = (id: string, ancestryPath?: string[]) => {
      let node: NzSafeAny | undefined;
      if (ancestryPath) {
        // Find node with both id and ancestryPath match
        node = (tree as NzSafeAny[]).find(
          (n: NzSafeAny) => n.id === id && Array.isArray(n.ancestryPath) && Array.isArray(ancestryPath) && n.ancestryPath.join('/') === ancestryPath.join('/')
        );
      }
      if (!node) {
        node = nodeMap.get(id);
      }
      if (!node && id.includes('-')) node = nodeMap.get(id.split('-')[0]);
      return node;
    };
    const parentNode = getNodeById(parentKey, ancestryPath) as NzSafeAny;

    filteredNode.forEach((item: NzSafeAny) => {
      if (parentNode) {
        item.ancestryPath = [parentKey, ...(parentNode.ancestryPath || [])];
      } else {
        item.ancestryPath = [parentKey];
      }
    });
  }

  /**
   * Tracks the set of currently expanded node ancestry paths in the org chart.
   * Each node can be expanded/collapsed independently by its ancestryPath.
   * ancestryPath is serialized as a string (joined by '/')
   */
  private expandedAncestryPaths = new BehaviorSubject<Set<string>>(new Set());
  currentExpandedAncestryPaths = this.expandedAncestryPaths.asObservable();

  /**
   * Add an ancestryPath to the expanded set (expand the node by ancestryPath)
   */
  expandNodeByAncestryPath(ancestryPath: string[]) {
    const key = ancestryPath.join('/');
    const newSet = new Set(this.expandedAncestryPaths.getValue());
    newSet.add(key);
    this.expandedAncestryPaths.next(newSet);
  }

  /**
   * Remove an ancestryPath from the expanded set (collapse the node by ancestryPath)
   */
  collapseNodeByAncestryPath(ancestryPath: string[]) {
    const key = ancestryPath.join('/');
    const newSet = new Set(this.expandedAncestryPaths.getValue());
    newSet.delete(key);
    this.expandedAncestryPaths.next(newSet);
  }

  /**
   * Toggle a node's expanded/collapsed state by ancestryPath
   */
  toggleNodeExpansionByAncestryPath(ancestryPath: string[]) {
    const key = ancestryPath.join('/');
    const newSet = new Set(this.expandedAncestryPaths.getValue());
    if (newSet.has(key)) {
      newSet.delete(key);
    } else {
      newSet.add(key);
    }
    this.expandedAncestryPaths.next(newSet);
  }

  /**
   * Collapse all nodes by ancestryPath
   */
  collapseAllNodesByAncestryPath() {
    this.expandedAncestryPaths.next(new Set());
  }

  /**
   * Sets the expanded ancestry paths to the provided array of path strings.
   * @param paths Array of ancestry path strings (e.g., ['id1/id2', ...])
   */
  setExpandedAncestryPaths(paths: string[]) {
    this.expandedAncestryPaths.next(new Set(paths));
  }

  /**
   * Public method to filter out duplicate nodes based on id and ancestryPath.
   * This can be used by other components to ensure unique tree nodes.
   * @param nodes Array of nodes to filter
   * @param existingTree Optional existing tree to check against for duplicates
   * @returns Filtered array with unique id + ancestryPath combinations
   */
  public filterDuplicateNodes(
    nodes: NzSafeAny[], 
    existingTree?: NzSafeAny[]
  ): NzSafeAny[] {
    return this.filterDuplicatesByIdAndAncestryPath(nodes, existingTree);
  }

  private selectedNode = new BehaviorSubject<NzSafeAny | null>(null);
  currentSelectedNode = this.selectedNode.asObservable();
  setSelectedNode(node: NzSafeAny) {
    this.selectedNode.next(node);
  }
  getSelectedNode(): NzSafeAny | null {
    return this.selectedNode.getValue();
  }
}
