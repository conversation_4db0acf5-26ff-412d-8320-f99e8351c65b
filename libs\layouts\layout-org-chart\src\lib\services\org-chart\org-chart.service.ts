import { Injectable, signal } from '@angular/core';
import { NzSafeAny } from 'ng-zorro-antd/core/types';
import compute_tree_coordinate from './compute_tree_coordinate';
export const colorsShort = ['#f8b484', '#ffdc64', '#acd48c'];
import { padding_top, padding_left } from './compute_tree_coordinate';
import { flatMap, maxBy, minBy } from 'lodash';
@Injectable({
  providedIn: 'root',
})
export class OrgChartService {
  distanceX = 6;
  distanceY = 28;
  startDrawX = padding_left;
  startDrawY = padding_top;

  // Helper method to calculate dynamic columns (matches GroupUserCardsComponent logic)
  private calculateColumns(length: number): number {
    if (length > 50) return Math.ceil(length / 10); // 6+ columns for very large groups
    if (length > 40) return 5;
    if (length > 30) return 4;
    if (length > 20) return 3;
    return 2; // Default 2 columns
  }

  calcSize(tree: NzSafeAny, structureType: string) {
    if (tree?.content?.type === 'org-group-user') {
      const groupLength = tree?.groupChilds?.length || 0;
      const columns = this.calculateColumns(groupLength);
      const columnWidth = 320; // Base width per column (640px / 2 columns = 320px)
      const rowHeight = 104; // Height per row
      const gapBetweenColumns = 1; // Small gap between columns
      
      const dynamicWidth = (columns * columnWidth) + ((columns - 1) * gapBetweenColumns);
      const dynamicHeight = Math.ceil(groupLength / columns) * rowHeight;

      return {
        ...tree,
        shape: {
          ...tree.shape,
          width: dynamicWidth,
          height: dynamicHeight,
          coverWidth: dynamicWidth,
          coverHeight: dynamicHeight,
        },
      };
    }
    if (!tree?.childs?.length) {
      return {
        ...tree,
        shape: {
          ...tree.shape,
          coverWidth: Number(tree.shape.width),
          coverHeight: Number(tree.shape.height),
        },
      };
    } else {
      let coverWidth = 0,
        coverHeight = 0;
      for (let i = 0; i < tree?.childs.length; i++) {
        tree.childs[i] = this.calcSize(tree.childs[i], structureType);
        if (
          tree?.childs[i]?.childs?.length === 0 &&
          tree?.childs[i - 1]?.shape?.coverWidth / 2 >
            Number(tree?.childs[i - 1]?.shape?.width) / 2 + this.distanceX
        ) {
          coverWidth +=
            Number(tree.childs[i]?.shape.coverWidth) +
            -Number(tree.childs[i - 1]?.shape.width) / 2 +
            this.distanceX;
        } else {
          coverWidth +=
            Number(tree.childs[i].shape.coverWidth) +
            (i > 0 ? this.distanceX : 0);
        }
        coverHeight = Math.max(
          coverHeight,
          Number(tree.childs[i].shape.coverHeight) + this.distanceY,
        );
      }
      coverHeight += Number(tree.shape.height) + this.distanceY;
      return {
        ...tree,
        shape: { ...tree.shape, coverWidth, coverHeight },
      };
    }
  }
  calcLocation(
    tree: NzSafeAny,
    startX: number,
    startY: number,
    structureType: string,
  ) {
    let x = startX;
    if (tree?.childs?.length) {
      for (let i = 0; i < tree?.childs.length; i++) {
        tree.childs[i] = this.calcLocation(
          tree?.childs[i],
          x,
          startY + Number(tree?.shape.height) + this.distanceY,
          structureType,
        );
        if (
          tree?.childs[i + 1]?.childs?.length === 0 &&
          colorsShort.includes(tree.childs[i].color) ===
            colorsShort.includes(tree?.childs[i + 1]?.color)
        ) {
          x +=
            tree.childs[i]?.shape.coverWidth / 2 +
            Number(tree.childs[i]?.shape.width) / 2 +
            this.distanceX;
          continue;
        }
        x += tree.childs[i].shape.coverWidth + this.distanceX;
      }
    }
    return {
      ...tree,
      shape: {
        ...tree?.shape,
        x: tree?.shape.coverWidth / 2 + startX - Number(tree?.shape.width) / 2,
        y: startY,
      },
    };
  }
  treeToArray: (tree: NzSafeAny) => NzSafeAny[] = (tree: NzSafeAny) => {
    const arr = [];
    if (tree?.childs?.length) {
      for (let i = 0; i < tree?.childs.length; i++) {
        arr.push(...this.treeToArray(tree.childs[i]));
      }
    }
    arr.push(tree);
    return [...arr];
  };
  addDefaultShape(data: NzSafeAny, structureType?: string) {
    if (data?.childs?.length) {
      data.childs = data.childs.map((child: NzSafeAny) =>
        this.addDefaultShape(child, structureType),
      );
    }
    const shape = {
      width: 200,
      height: 200,
    };
    if (data?.content?.type === 'org-chart-object') {
      shape.width = data?.shape?.width ?? 320;
      if (data?.shape?.height) {
        shape.height = data?.shape?.height;
      } else if (colorsShort.includes(data.color)) {
        shape.height = 222;
      } else {
        shape.height = 292;
      }
    }
    if (data?.content?.type === 'org-chart-position') {
      shape.width = data?.shape?.width ?? 200;
      shape.height = data?.shape?.height ?? 288;
    }
    if (data?.content?.type === 'org-chart-user-card') {
      shape.width = data?.shape?.width ?? 320;
      shape.height = data?.shape?.height ?? 104;
    }
    if (data?.content?.type === 'org-chart-model') {
      shape.width = data?.shape?.width ?? 400;
      shape.height = data?.shape?.height ?? 250;
    }
    if (data?.content?.type === 'org-group-user') {
      shape.width = data?.shape?.width ?? 640;
      shape.height = Math.ceil(data?.groupChilds?.length / 2) * 104;
    }
    return {
      ...data,
      shape,
    };
  }
  buildTree(
    items: {
      [key: string]: NzSafeAny;
    }[],
    directKey: string,
    indirectKey?: string,
  ) {
    // Filter out nodes with empty directPositionId
    const filteredItems = items; // .filter(item => item['directPositionId']);
    // Use filteredItems instead of items for the rest of the function
    let tree: NzSafeAny = [];
    const lookup: NzSafeAny = {};
    if (!filteredItems) {
      return null;
    }

    // First, create a lookup table where each id points to its corresponding item
    filteredItems.forEach((item) => {
      lookup[item['id']] = { ...item, childs: [] };
      tree.push(lookup[item['id']]);
    });

    // Then, iterate through the items again to build the tree
    filteredItems.forEach((item) => {
      if (lookup[item[directKey]]) {
        lookup[item[directKey]]?.childs?.push({
          ...lookup[item['id']],
          connectionType: 'solid',
        });
        const remove = tree.filter((i: NzSafeAny) => {
          return i[directKey] !== lookup[item[directKey]].id;
        });
        tree = remove;
      }
    });
    if (indirectKey) {
      filteredItems.forEach((item) => {
        if (!item[indirectKey]) return;
        const indirectKeys = item[indirectKey];
        indirectKeys.forEach((key: string) => {
          if (lookup[key]) {
            lookup[key]?.childs?.push({
              ...lookup[item['id']],
              connectionType: 'dashed',
            });
            const remove = tree.filter((i: NzSafeAny) => {
              return !i[indirectKey]?.includes(lookup[key].id);
            });
            tree = remove;
          }
        });
      });
    }
    return tree;
  }
  addType(data: NzSafeAny, type = 'org-chart-position') {
    return data?.map((item: NzSafeAny) => ({
      ...item,
      content: { type: item?.content?.type ?? type },
    }));
  }

  sortTreeByName(tree: NzSafeAny) {
    if (!tree?.childs?.length) {
      return tree;
    }
    tree.childs = tree.childs.sort((a: NzSafeAny, b: NzSafeAny) => {
      // if (a.nodeIndex || b.nodeIndex) {
      //   return b.nodeIndex - a.nodeIndex;
      // }
      if (a.connectionType === 'solid' && b.connectionType === 'dashed')
        return -1;
      if (a.connectionType === 'dashed' && b.connectionType === 'solid')
        return 1;
      if (a.content.type === 'org-chart-object')
        return a?.shortName?.localeCompare(b?.shortName);
      if (a.content.type === 'org-chart-position')
        return a?.longName?.default?.localeCompare(b?.longName?.default);
      if (
        a.content.type === 'org-chart-user-card' &&
        b.content.type === 'org-chart-user-card'
      ) {
        return a?.fullName?.localeCompare(b?.fullName);
      }
      if (
        a.content.type === 'org-group-user' &&
        b.content.type === 'org-group-user'
      ) {
        if (a.connectionType === 'solid' && b.connectionType === 'dashed')
          return -1;
        if (a.connectionType === 'dashed' && b.connectionType === 'solid')
          return 1;
        // return a?.connectionType === 'solid' ? -1 : 1;
      }
      if (a.content.type === 'org-group-user') {
        a.groupChilds.sort((a: NzSafeAny, b: NzSafeAny) =>
          a.fullName.localeCompare(b.fullName),
        );
        if (b?.fullName?.localeCompare(a.groupChilds[0])) return -1;
        if (b?.fullName?.localeCompare(b.groupChilds[b.groupChilds.length - 1]))
          return 1;
      }
      if (b.content.type === 'org-group-user') {
        b.groupChilds.sort((a: NzSafeAny, b: NzSafeAny) =>
          a.fullName.localeCompare(b.fullName),
        );
        if (a?.fullName?.localeCompare(b.groupChilds[0])) return 1;
        if (a?.fullName?.localeCompare(b.groupChilds[b.groupChilds.length - 1]))
          return -1;
      }
      return 0;
    });
    for (let i = 0; i < tree?.childs.length; i++) {
      tree.childs[i] = this.sortTreeByName(tree.childs[i]);
    }
    return tree;
  }
  sortTreeByChildsNumber(tree: NzSafeAny) {
    if (!tree?.childs?.length) {
      return tree;
    }
    tree.childs = tree.childs.sort((a: NzSafeAny, b: NzSafeAny) => {
      if (a.content.type === 'org-chart-object')
        return b.countChild - a.countChild;
      if (a.content.type === 'org-chart-position')
        return b.totalChild - a.totalChild;
      if (a.content.type === 'org-chart-user-card')
        return b.totalChild - a.totalChild;
      if (a.content.type === 'org-group-user') return 1;
      if (b.content.type === 'org-group-user') return -1;
      return b.childs.length - a.childs.length;
    });
    for (let i = 0; i < tree?.childs.length; i++) {
      tree.childs[i] = this.sortTreeByChildsNumber(tree.childs[i]);
    }
    return tree;
  }

  //calculate chart width from chart data
  getAccurateChartWidth = (chartData: ChartNode[], padding = 16): number => {
    const collectBounds = (nodes: ChartNode[]): [number, number][] => {
      return flatMap(nodes, (node) => {
        const bounds: [number, number][] = [];

        // If node has shape and x + width
        if (node.shape?.x !== undefined) {
          const x = node.shape.x;
          const width = node.shape.width ?? 0;
          bounds.push([x, x + width]);
        }

        // Recurse into groupChilds and childs
        if (node.groupChilds) {
          bounds.push(...collectBounds(node.groupChilds));
        }
        if (node.childs) {
          bounds.push(...collectBounds(node.childs));
        }

        return bounds;
      });
    };

    const allBounds = collectBounds(chartData);
    const minX = minBy(allBounds, (b) => b[0])?.[0] ?? 0;
    const maxX = maxBy(allBounds, (b) => b[1])?.[1] ?? 0;

    return maxX - minX + padding * 2;
  };

  chartWidth = signal<number>(0);
  drawArrayFromTree(
    data: NzSafeAny,
    startX = this.startDrawX,
    startY = this.startDrawY,
    structureType?: string,
  ) {
    let _tree = structuredClone(data);
    if (data?.content?.type === 'org-chart-user-card') {
      _tree = this.buildGroupTree(_tree);
    }
    const dataShape = this.addDefaultShape(_tree, structureType);
    const sortTree = this.sortTreeByName(dataShape);
    const treeSize = this.calcSize(sortTree, structureType ?? '2');
    // const TreeCode = this.calcLocation(
    //   treeSize,
    //   startX,
    //   startY,
    //   structureType ?? '1',
    // );
    const array = this.treeToArray(treeSize);
    // gọi anh tuna35 để nhờ anh giải thích thuật toán nhé
    compute_tree_coordinate(array[array.length - 1]);
    
    // Fix: Rebuild the flat array after coordinate computation to get updated coordinates
    const updatedArray = this.treeToArray(array[array.length - 1]);
    
    //set chart width when draw the tree.
    this.chartWidth.set(this.getAccurateChartWidth(structuredClone(updatedArray)));
    return updatedArray;
  }
  getTreeSize(tree: NzSafeAny) {
    let maxX = 50;
    let maxY = 100;
    for (let i = 0; i < tree.length; i++) {
      maxX = Math.max(maxX, tree[i].shape.x + tree[i].shape.width);
      maxY = Math.max(maxY, tree[i].shape.y + tree[i].shape.height);
    }
    return { maxX, maxY };
  }
  filterTree(
    tree: NzSafeAny,
    type: string,
    directKey: string,
    indirectKey: string,
  ): NzSafeAny {
    if (!tree?.childs?.length || type === 'all') {
      return tree;
    }
    if (type === 'direct') {
      for (let i = tree.childs.length - 1; i >= 0; i--) {
        if (tree.childs[i][directKey] === tree.id) {
          tree.childs[i] = this.filterTree(
            tree.childs[i],
            type,
            directKey,
            indirectKey,
          );
        } else tree.childs.splice(i, 1);
      }
    }
    if (type === 'matrix') {
      for (let i = tree.childs.length - 1; i >= 0; i--) {
        if (tree.childs[i]?.[indirectKey]?.includes(tree.id)) {
          tree.childs[i] = this.filterTree(
            tree.childs[i],
            type,
            directKey,
            indirectKey,
          );
        } else {
          tree.childs.splice(i, 1);
        }
      }
    }
    return tree;
  }
  focusTree(tree: NzSafeAny, id: string): NzSafeAny {
    // Kiểm tra input
    if (!tree || !id) return null;

    // Tìm thấy node cần focus - giữ nguyên cấu trúc con của nó
    if (tree.id === id) {
        return tree; // Không xóa childs nữa mà giữ nguyên tree
    }

    // Không có children
    if (!tree.childs?.length) {
        return null;
    }

    // Tìm trong children
    for (const child of tree.childs) {
        const focusedChild = this.focusTree(child, id);
        if (focusedChild) {
            return {
                ...tree,
                childs: [focusedChild] // Chỉ giữ lại đường dẫn đến node focus
            };
        }
    }

    return null;
}
  groupChilds(tree: NzSafeAny, type: string): NzSafeAny {
    const groupedChilds = [];
    if (type === 'directPositionId') {
      for (let i = tree?.childs?.length - 1; i >= 0; i--) {
        if (
          !tree.childs[i]?.childs?.length &&
          tree.childs[i]?.directPositionId === tree.id
        ) {
          groupedChilds.push(tree.childs[i]);
        }
      }
    }
    if (type === 'matrixPositionIds') {
      for (let i = tree?.childs?.length - 1; i >= 0; i--) {
        if (
          !tree.childs[i]?.childs?.length &&
          tree.childs[i]?.matrixPositionIds?.includes(tree.id)
        ) {
          groupedChilds.push(tree.childs[i]);
        }
      }
    }
    return groupedChilds;
  }
  buildGroupTree(tree: NzSafeAny) {
    tree.childs = tree.childs.filter(
      (child: NzSafeAny) => child.content?.type !== 'org-group-user',
    );
    const groupChildsDirect = this.groupChilds(tree, 'directPositionId');
    const groupChildsMatrix = this.groupChilds(tree, 'matrixPositionIds');
    for (let i = tree?.childs?.length - 1; i >= 0; i--) {
      if (tree.childs[i]?.childs?.length) {
        tree.childs[i] = this.buildGroupTree(tree.childs[i]);
      }
    }
    if (groupChildsDirect.length > 1) {
      tree.childs.unshift({
        id: 'group',
        directPositionId: tree.id,
        groupChilds: groupChildsDirect,
        content: { type: 'org-group-user' },
        connectionType: 'solid',
      });
      tree.childs = tree.childs.filter(
        (child: NzSafeAny) =>
          !groupChildsDirect.some(
            (groupChild: NzSafeAny) => groupChild.id === child.id,
          ),
      );
    }
    if (groupChildsMatrix.length > 1) {
      tree.childs.unshift({
        id: 'group',
        matrixPositionIds: [tree.id],
        groupChilds: groupChildsMatrix,
        content: { type: 'org-group-user' },
        connectionType: 'dashed',
      });
      tree.childs = tree.childs.filter(
        (child: NzSafeAny) =>
          !groupChildsMatrix.some(
            (groupChild: NzSafeAny) => groupChild.id === child.id,
          ),
      );
    }
    return tree;
  }
  /**
   * New buildTreeV2: builds tree recursively from flat items array.
   * - Finds root (isRoot: true)
   * - For each node, children are those whose directKey equals node.id (solid) or indirectKey includes node.id (dashed)
   * - Adds connectionType
   * - Adds debug logs
   * - If expandedNodeIds is provided, only expand nodes whose IDs are in the set
   */
  buildTreeV2(
    items: { [key: string]: NzSafeAny }[],
    directKey: string,
    indirectKey?: string,
    onlyShowRootChildren: boolean = false,
    expandedNodePaths: Set<string> = new Set()
  ) {
    if (!items?.length) {
      return [];
    }
    let root = items.find(item => item['isRoot']);
    if (!root) {
      if (items.length === 1) {
        root = items[0];
        return [{ ...root, childs: [] }];
      } else {
        // Find all ids in the items list
        const allIds = new Set(items.map(item => item['id']));
        root = items.find(item => {
          // Check if directKey is in allIds
          const hasDirect = item[directKey] && allIds.has(item[directKey]);
          // Check if any indirectKey value is in allIds
          let hasIndirect = false;
          if (indirectKey && Array.isArray(item[indirectKey])) {
            hasIndirect = item[indirectKey].some((id: string) => allIds.has(id));
          }
          // Root if neither directKey nor any indirectKey value is in allIds
          return !hasDirect && !hasIndirect;
        });
        if (!root) return [];
      }
    }

    // Helper function to create a unique key for visited tracking (consistent with ConfigService)
    const createVisitedKey = (nodeId: string, ancestryPath: string[] = []): string => {
      const pathString = Array.isArray(ancestryPath) ? ancestryPath.join('/') : '';
      return `${nodeId}|${pathString}`;
    };

    // Helper function to safely compare ancestry paths
    const ancestryPathsEqual = (path1: string[] = [], path2: string[] = []): boolean => {
      if (path1.length !== path2.length) return false;
      return path1.every((val, index) => val === path2[index]);
    };

    // Recursive: for each node, recursively find and attach its direct and indirect children
    const buildRecursiveChildren = (node: NzSafeAny, visited = new Set(), parentIsRoot = false): NzSafeAny => {
      const visitedKey = createVisitedKey(node['id'], node['ancestryPath']);
      if (visited.has(visitedKey)) {
        return null;
      }
      visited.add(visitedKey);

      // Find direct children
      const directChildren = items.filter(item => {
        if (item[directKey] !== node['id'] || item['id'] === node['id']) {
          return false;
        }
        
        // Expected ancestry path for this child should be [node.id, ...node.ancestryPath]
        const expectedAncestryPath = [node['id'], ...(node['ancestryPath'] ?? [])];
        return ancestryPathsEqual(item['ancestryPath'], expectedAncestryPath);
      }).map(child => ({
        ...child,
        connectionType: 'solid',
      }));

      // Find indirect children (if indirectKey is provided)
      let indirectChildren: NzSafeAny[] = [];
      if (indirectKey) {
        indirectChildren = items.filter(item => {
          const indirectVal = item[indirectKey];
          if (!indirectVal || item['id'] === node['id']) return false;
          
          if (Array.isArray(indirectVal) && indirectVal.includes(node['id'])) {
            // Expected ancestry path for this child should be [node.id, ...node.ancestryPath]
            const expectedAncestryPath = [node['id'], ...(node['ancestryPath'] ?? [])];
            return ancestryPathsEqual(item['ancestryPath'], expectedAncestryPath);
          }
          return false;
        }).map(child => ({
          ...child,
          connectionType: 'dashed',
        }));
      }

      const allChildren = [...directChildren, ...indirectChildren];

      // Don't expand children if:
      // 1. onlyShowRootChildren is true and this node is a direct or indirect child of root
      // 2. expandedNodeIds is provided and this node's ID is not in the 
      const currentPath = [node['id'], ...(node['ancestryPath'] ?? [])].join('/');
      const isExpanded = Array.from(expandedNodePaths).some(
        path => currentPath === path || currentPath.startsWith(path + '/')
      );
      const shouldCollapseChildren =
        (onlyShowRootChildren && parentIsRoot) ||
        (!isExpanded && node['id'] !== root['id']);

      if (shouldCollapseChildren) {
        return {
          ...node,
          childs: []
        };
      }

      // Otherwise, process children recursively with the SAME visited set
      const processedChildren = allChildren
        .map((child: NzSafeAny) => buildRecursiveChildren(
          child,
          visited, // ✅ FIXED: Pass the same visited set instead of creating new one
          onlyShowRootChildren && node['id'] === root['id']
        ))
        .filter(Boolean);
      
      return {
        ...node,
        childs: processedChildren
      };
    };

    // Recursively build the tree from the root
    return [buildRecursiveChildren(root, new Set(), false)];
  }
}

interface ChartNode {
  id?: string;
  shape?: Shape;
  groupChilds?: ChartNode[];
  childs?: ChartNode[];
  [key: string]: NzSafeAny;
}

interface Shape {
  x: number;
  width?: number;
  coverWidth?: number;
}
