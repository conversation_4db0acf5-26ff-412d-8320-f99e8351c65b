import { BehaviorSubject } from 'rxjs';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
@Injectable()
export class PanService {
  private expand = new BehaviorSubject<boolean>(false);
  currentExpand = this.expand.asObservable();
  changeExpand(isExpand: boolean) {
    this.expand.next(isExpand);
  }

  private isDragging = new BehaviorSubject<boolean>(false);
  currentDragging = this.isDragging.asObservable();
  setDragging(dragging: boolean) {
    this.isDragging.next(dragging);
  }

  private scale = new BehaviorSubject<number>(1);
  currentScale = this.scale.asObservable();

  setScale(value: number) {
    this.scale.next(value);
  }

  // slideChange(value: number) {
  //   this.scale.next(value / 50);
  // }
  zoomIn(prev: number) {
    if (prev * 1.2 > 2) {
      this.scale.next(2);
    } else {
      this.scale.next(prev + 0.1);
    }
  }
  zoomOut(prev: number) {
    if (prev * 0.83 < 0.5) {
      this.scale.next(0.5);
    } else {
      this.scale.next(prev - 0.1);
    }
  }

  private translate = new BehaviorSubject<{
    x: number;
    y: number;
  }>({
    x: 0,
    y: 0,
  });
  currentTranslate = this.translate.asObservable();
  translateChange(x: number, y: number) {
    this.translate.next({ x, y });
  }
  private panSize = new BehaviorSubject<{
    width: number;
    height: number;
  }>({
    width: 0,
    height: 0,
  });
  currentPanSize = this.panSize.asObservable();
  panSizeChange(width: number, height: number) {
    this.panSize.next({ width, height });
  }
  private chartSize = new BehaviorSubject<{
    width: number;
    height: number;
  }>({
    width: 0,
    height: 0,
  });
  currentChartSize = this.chartSize.asObservable();
  chartSizeChange(width: number, height: number) {
    this.chartSize.next({ width, height });
    if (this.isCenter.getValue()) {
      this.reCenter();
    }
  }
  isCenter = new BehaviorSubject<boolean>(false);
  isReCenter(isCenter: boolean) {
    this.isCenter.next(isCenter);
  }
  
  /**
   * Centers a specific node (current selected/active node) horizontally and positions it 50px from top
   * @param node - The node to center (should have shape property with x, y, width, height)
   */
  centerOnNode(node: any) {
    if (!node || !node.shape) {
      console.warn('centerOnNode: Invalid node or missing shape property');
      return;
    }
    
    const panSize = this.panSize.getValue();
    const currentScale = this.scale.getValue();
    
    // Calculate the target node's center position in original coordinates
    const targetCenterX = (node.shape.x + (node.shape.width / 2)) * currentScale;
    const targetTopY = node.shape.y * currentScale;
    
    // Calculate desired position
    const desiredX = panSize.width / 2;  // center horizontally
    const desiredY = 50;  // 50px from top
    
    // Calculate translation needed to position the target node correctly
    this.translate.next({
      x: desiredX - targetCenterX,
      y: desiredY - targetTopY
    });
  }
  
  reCenter() {
    const panSize = this.panSize.getValue();
    const chartSize = this.chartSize.getValue();
    this.translate.next({
      x: panSize.width / 2 - chartSize.width / 2,
      y:
        panSize.height < chartSize.height
          ? 0
          : panSize.height / 2 - chartSize.height / 2,
    });
  }
}
