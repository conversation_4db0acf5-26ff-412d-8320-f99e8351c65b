@if (open()) {
  <hrdx-modal
    [isVisible]="dialogVisible()"
    [title]="nzModalTitle"
    [footer]="_showFooter() ? footer : null"
    (visibleChange)="dialogVisibleChange.emit(!dialogVisible())"
    (canceled)="onCancel()"
    [wrapClassName]="'dialog' + (open() ? ' standard-popup' : '')"
    [bodyModalStyle]="bodyModalStyle()"
    [size]="size()"
    [maskClosable]="maskClosable()"
    (afterOpen)="onAfterOpen()"
    [centered]="centered()"
  >
    <div
      [ngClass]="['dialog-content']"
      *ngIf="dialogVisible"
      [class.overview]="realConfig()?.overview || realConfig()?.overviewGroup"
    >
      <ng-container [ngTemplateOutlet]="content"></ng-container>
      <lib-overview
        *ngIf="realConfig()?.overview"
        [value]="this.overViewValue"
        [variables]="this.valueDef"
        [config]="realConfig()?.overview"
        [messages]="noDataMessagesOverview"
        [mode]="_formMode()"
      />
      <lib-overview-group
        *ngIf="realConfig()?.overviewGroup"
        [value]="this.overViewValueGroup"
        [variables]="this.valueDef"
        [config]="realConfig()?.overviewGroup"
        [messages]="noDataMessagesOverview"
      />
    </div>
  </hrdx-modal>
} @else {
  <hrdx-drawer
    [visible]="dialogVisible()"
    [title]="nzModalTitle"
    [footer]="_showFooter() ? footer : undefined"
    (visibleChange)="dialogVisibleChange.emit(!dialogVisible())"
    (closed)="onCancel()"
    [wrapClassName]="'dialog'"
    [paddingLess]="isPaddingLess()"
    [width]="size()"
    [top]="setDrawerFixed()"
    [maskTransparent]="dialogType() === 'view' ? true : false"
    [maskClosable]="dialogType() === 'edit' ? maskClosable() : true"
  >
    <div [ngClass]="['dialog-content']" *ngIf="dialogVisible">
      <ng-container [ngTemplateOutlet]="content"></ng-container>
    </div>
  </hrdx-drawer>
}

<ng-template #nzModalTitle>
  <div class="modal-header">
    <span class="modal-title">
      {{ formTitle() }}
    </span>
    @if (!settingConfig() && showAvatarInfo()) {
      <div class="avatar-info">
        <hrdx-avatar
          [type]="avatarType.Image"
          [shape]="avatarShape.Circle"
          [size]="avatarSize.Medium"
          [imgSrc]="dataLayout()?.['dataProfile']?.['avatarLink']"
          [imgAlt]="dataLayout()?.['dataProfile']?.['name']"
          [text]="dataLayout()?.['dataProfile']?.['name']"
        ></hrdx-avatar>
        <span class="avatar-info__name">
          {{ dataLayout()?.['dataProfile']?.['name'] }}</span
        >
      </div>
    } @else {
      <div style="width: 40px; height: 40px"></div>
    }
    <lib-filter-setting
      [fields]="filterSettingsFields() ?? []"
      (filterListChange)="filterListChange($event)"
      *ngIf="settingConfig()"
      [ngStyle]="{ marginRight: '16px' }"
    />
  </div>
</ng-template>
<ng-template #content>
  @if (!loading() && !reLoadingDetail() && !loadingDetailSchedule()) {
    <div class="content-wrapper">
      <lib-toast
        [(visible)]="showToastApplyTempValue"
        [duration]="10000"
        *ngIf="showToastApplyTempValue"
      >
        <span>
          The information entered last time is not complete. Click
          <a (click)="applyTempValue()">Here</a> to continue using.
        </span>
      </lib-toast>
      <dynamic-form
        [config]="realConfig()?.fields ?? []"
        [sources]="realConfig()?.sources ?? {}"
        [variables]="realConfig()?.variables ?? {}"
        [formValue]="_value()"
        [ppxClass]="'ppxm-style'"
        [ngClass]="!open() ? ' form-detail' : ''"
        [readOnly]="['view', 'viewSchedule'].includes(_dialogType())"
        [extend]="{
          formType: _dialogType(),
          isDuplicate: dialogType() === 'duplicate',
          disabledFields: mappingDisabledFields(),
          defaultValue: defaultFormValue(),
          addOnValue: addOnValue(),
          params: params(),
          subForms: realConfig()?.subForms ?? {},
          user: user(),
          permission: permissionForForm(),
          responseError: responseError(),
          accountPermissions: accountPermissions(),
          extraData: extraDataSchedule(),
          faceCode: faceCode(),
        }"
        [footer]="realConfig()?.footer"
        [reset]="reset()"
        [reload]="reset()"
        [_mode]="realConfig()?._mode"
        (valueChanges)="onFormValueChanges($event.value)"
        [isNewDynamicForm]="isNewDynamicForm()"
        [faceCode]="faceCode()"
        [checkValueEmpty]="true"
        [authAction]="getAuthActionByFormType()"
        #formObj
      ></dynamic-form>

      <div
        class="dialog-content__footer"
        *ngIf="!realConfig()?.footer && showDialogFooter()"
      >
        <ng-container
          [ngTemplateOutlet]="contentFooter"
          [ngTemplateOutletContext]="{ value: _value() }"
        ></ng-container>
      </div>
    </div>
    @if (
      isViewDetailConfig() && dataViewDetailConfig() && dialogType() === 'view'
    ) {
      <hrdx-view-detail-config
        [details]="dataViewDetailConfig()?.details"
        [settings]="dataViewDetailConfig()?.settings"
      >
      </hrdx-view-detail-config>
    }
  } @else {
    <div class="loading">
      <hrdx-loading></hrdx-loading>
    </div>
  }
</ng-template>

<ng-template #contentFooter let-value="value">
  @if (realConfig()?.footer) {
    <hrdx-credit-footer [footerConfig]="realConfig()?.footer" [value]="value" />
  }
</ng-template>

<ng-template #footer>
  @switch (dialogFooter()?.type) {
    @case ('step') {
      <div class="dialog--footer" *ngIf="getStepFooter() as footer">
        <div class="left-btns">
          <hrdx-button
            [type]="'link'"
            [title]="'Clear all'"
            [size]="'default'"
            (clicked)="resetForm()"
            *ngIf="footer.showClearAll"
          ></hrdx-button>
        </div>
        <div class="list-btn">
          <hrdx-button
            type="tertiary"
            [title]="currentStep() <= 0 ? 'Cancel' : 'Back'"
            [size]="'default'"
            (clicked)="currentStep() <= 0 ? onCancel() : onNextStep(-1)"
          ></hrdx-button>
          <hrdx-button
            type="secondary"
            [title]="secondary.title"
            [size]="'default'"
            [isLoading]="actionClicked() === secondary?.id && processing()"
            [disabled]="actionClicked() !== secondary?.id && processing()"
            *ngIf="
              currentStep() === maxStep() &&
              footer.buttons?.secondary as secondary
            "
            (clicked)="onActionClick(secondary)"
          ></hrdx-button>
          <hrdx-button
            type="primary"
            [title]="currentStep() < maxStep() ? 'Next' : primary.title"
            [size]="'default'"
            [disabled]="
              isNextStepDisabled() ||
              (actionClicked() !== primary?.id && processing())
            "
            [isLoading]="actionClicked() === primary?.id && processing()"
            (clicked)="
              currentStep() < maxStep() ? onNextStep(1) : onActionClick(primary)
            "
            *ngIf="footer.buttons?.primary as primary"
          ></hrdx-button>
        </div>
      </div>
    }
    @case ('buttons') {
      <div class="dialog--footer" *ngIf="getButtonsFooter() as footer">
        <div class="left-btns"></div>
        <div class="list-btn">
          @for (btn of footer.buttons; track btn.id) {
            <hrdx-button
              [type]="btn.type"
              [title]="btn.title"
              [size]="'default'"
              [disabled]="actionClicked() !== btn.id && processing()"
              [isLoading]="actionClicked() === btn.id && processing()"
              (clicked)="onActionClick(btn)"
              *ngIf="
                footerElementsDisplayState()[btn.id] && validAction(btn.id)
              "
            ></hrdx-button>
          }
        </div>
      </div>
    }
    @default {
      @if (footerButtonsCustom().length > 0 && dialogType() !== 'filter') {
        <div class="dialog--footer__custom">
          @for (btn of footerButtonsCustom(); track btn.id) {
            <hrdx-button
              [type]="btn.type"
              [title]="btn.title"
              [icon]="btn.icon"
              (click)="onClickModalButton(btn)"
              [size]="'default'"
              [isLoading]="
                btn.id === 'generateReport'
                  ? isSubmitBtnLoading()
                  : actionClicked() === btn.id && processing()
              "
              *ngIf="checkActionFooterPermission(btn)"
            />
          }
        </div>
      } @else {
        <div class="dialog--footer">
          <div class="reset-btn">
            <!-- define reset button -->
            <hrdx-button
              [type]="'link'"
              [title]="button.label"
              [size]="'default'"
              (clicked)="onReset()"
              [disabled]="shouldDisabledReset()"
              *ngIf="buttons()['reset'] as button"
            ></hrdx-button>

            <hrdx-button
              [type]="'link'"
              [title]="hyperlinkInForm()?.title ?? 'Maintain Bank Details '"
              [size]="'default'"
              (clicked)="handleHyperlinkClicked()"
              *ngIf="hyperlinkInForm()"
            ></hrdx-button>
          </div>
          <div class="list-btn">
            <!-- define cancel and  ok button  -->
            <hrdx-button
              [type]="'tertiary'"
              [title]="button.label"
              [size]="'default'"
              (clicked)="onCancel()"
              *ngIf="buttons()['cancel'] as button"
            ></hrdx-button>
            <hrdx-button
              type="primary"
              [title]="'Block'"
              [size]="'default'"
              (clicked)="onBlock()"
              *ngIf="showDeleteButton()"
            ></hrdx-button>
            <hrdx-button
              type="tertiary"
              [title]="button.label"
              [size]="'default'"
              (clicked)="onDelete()"
              [disabled]="button.disabled || loadingFooterAction()"
              [isLoading]="loadingFooterAction()"
              *ngIf="
                !showDeleteButton() &&
                !hideDeleteButton() &&
                checkPermissionActionDetail('delete') as button
              "
            ></hrdx-button>
            <hrdx-button
              type="secondary"
              [title]="button.label"
              [size]="'default'"
              [disabled]="button.disabled"
              (clicked)="onDuplicate()"
              *ngIf="
                !showDeleteButton() &&
                checkPermissionActionDetail('duplicate') as button
              "
            ></hrdx-button>
            <hrdx-button
              type="primary"
              [title]="button.label"
              [size]="'default'"
              [disabled]="button.disabled || loadingFooterAction()"
              [isLoading]="loadingFooterAction()"
              (clicked)="onEdit()"
              *ngIf="
                !showDeleteButton() &&
                checkPermissionActionDetail('edit') as button
              "
            ></hrdx-button>

            <hrdx-button
              type="secondary"
              [title]="button.label"
              (clicked)="onSaveAndAddNew()"
              [size]="'default'"
              [isLoading]="saveAndAddNewProcessing()"
              [disabled]="processing()"
              *ngIf="
                showSaveAddButton() &&
                checkPermissionActionDetail('saveAndAddNew') as button
              "
            ></hrdx-button>
            <hrdx-button
              [type]="okButtonType()"
              [title]="button.label"
              [size]="'default'"
              (clicked)="onOk()"
              [isLoading]="processing()"
              [disabled]="saveAndAddNewProcessing()"
              *ngIf="buttons()['ok'] as button"
            ></hrdx-button>
            <hrdx-button
              [type]="'primary'"
              [title]="button.label"
              [size]="'default'"
              (clicked)="onSubmit()"
              [isLoading]="submitProcessing()"
              *ngIf="showSubmitButton() && buttons()['submit'] as button"
            ></hrdx-button>
            <!-- [disabled]="!dynamicForm?.valid" -->
          </div>
        </div>
      }
    }
  }
</ng-template>

<ng-template #contentCancel let-ref="modalRef">
  <hrdx-modal-action [type]="'warning'"></hrdx-modal-action>
  <!-- <span
    >You have unsaved changes. Do you want to save changes before
    continuing?</span
  > -->
  <div class="modal-footer-action">
    <hrdx-button
      [type]="'tertiary'"
      [title]="'Back'"
      [size]="'default'"
      (clicked)="ref.destroy()"
    />
    <hrdx-button
      [type]="'tertiary'"
      [title]="'Don\'t save'"
      [size]="'default'"
      (clicked)="
        this.dialogVisibleChange.emit(false);
        this.canceled.emit(true);
        storeValueByKey && clearTempValue(storeValueByKey);
        ref.destroy()
      "
    />
    <hrdx-button
      [type]="'primary'"
      (clicked)="onOk(); ref.destroy()"
      [size]="'default'"
      [title]="'Save'"
    />
  </div>
</ng-template>
