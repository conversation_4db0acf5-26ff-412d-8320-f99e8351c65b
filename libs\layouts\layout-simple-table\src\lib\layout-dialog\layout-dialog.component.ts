import { CommonModule } from '@angular/common';
import {
  Component,
  computed,
  effect,
  inject,
  Input,
  input,
  output,
  signal,
  TemplateRef,
  ViewChild,
  OnDestroy,
  untracked,
  model,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import {
  DynamicFormService,
  FieldConfig,
  FormComponent,
} from '@hrdx-fe/dynamic-features';
// eslint-disable-next-line @nx/enforce-module-boundaries
import {
  ActionPermission,
  AuthActions,
  BffService,
  Data,
  DialogFooter,
  FormConfig,
  LayoutDataService,
  LayoutStore,
  LocalStorageService,
  mappingUrl,
  UserStore,
  UtilService,
  WidgetOptions,
  ValueStorageConfig,
  AccountPermission,
  MasterdataService,
  ChildrenActionPermission,
} from '@hrdx-fe/shared';
import {
  AvatarComponent,
  AvatarShape,
  AvatarSize,
  AvatarType,
  ButtonComponent,
  CreditFooterComponent,
  DrawerComponent,
  LoadingComponent,
  ModalActionComponent,
  ModalComponent,
  ModalFooterButtons,
  ModalSize,
  ToastMessageComponent,
  ViewDetailConfigComponent,
} from '@hrdx/hrdx-design';
import { NzSafeAny } from 'ng-zorro-antd/core/types';
import { catchError, EMPTY, takeUntil, finalize } from 'rxjs';
import { Subject } from 'rxjs';

import {
  OverviewComponent,
  OverviewGroupComponent,
  ToastComponent,
} from '../component';
import { FilterSettingComponent } from 'libs/layouts/layout-dynamic/src/lib/layout-table/filter-setting/filter-setting.component';
import * as moment from 'moment';

import { UtilsService } from '@hrdx-fe/dynamic-features';
import { isNil, memoize } from 'lodash';

export type DialogType =
  | 'create'
  | 'edit'
  | 'proceed'
  | 'proceedCustom'
  | 'filter'
  | 'view'
  | 'duplicate'
  | 'toDuplicate'
  | 'viewSchedule';

@Component({
  selector: 'lib-layout-dialog',
  standalone: true,
  imports: [
    CommonModule,
    ModalComponent,
    ButtonComponent,
    FormComponent,
    FormsModule,
    ButtonComponent,
    DrawerComponent,
    LoadingComponent,
    ModalActionComponent,
    OverviewComponent,
    OverviewGroupComponent,
    ViewDetailConfigComponent,
    AvatarComponent,
    FilterSettingComponent,
    CreditFooterComponent,
    ToastComponent,
  ],
  providers: [ToastMessageComponent, ModalComponent, UtilsService],
  templateUrl: './layout-dialog.component.html',
  styleUrl: './layout-dialog.component.less',
  // changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LayoutDialogComponent implements OnDestroy {
  private readonly destroy$ = new Subject<void>();

  avatarType = AvatarType;
  avatarShape = AvatarShape;
  avatarSize = AvatarSize;

  title = input<string>();
  dialogVisible = input<boolean>(false);
  dialogVisibleChange = output<boolean>();
  toast = inject(ToastMessageComponent);
  modal = inject(ModalComponent);
  dynamicService = inject(DynamicFormService);
  url = input.required<string | null>();
  customDetailBackendUrl = input<string | null>();
  addOnValue = input<{ [k: string]: unknown }>({});
  params = input<any>({});
  childrenActionPermission = input<ChildrenActionPermission[]>([]);
  extraDataSchedule = input<any>(undefined);
  dataLayout = input<Record<string, NzSafeAny> | null>(null);
  showDialogFooter = input<boolean>(false);
  showSaveAddButton = input<boolean>(false);
  showDeleteButton = input<boolean>(false);
  footerButtonsCustom = input<ModalFooterButtons[]>([]);
  isPopup = input<boolean | undefined>(undefined);
  showFooter = input<boolean | null>(true);
  isSubmitBtnLoading = input<boolean>(false);
  centered = input<boolean>(true);
  processing = signal<boolean>(false);
  isViewDetailConfig = input<boolean>(false);
  dataViewDetailConfig = input<NzSafeAny>({});
  noNeedConfirm = input<boolean>(false);
  loadingDetailSchedule = input<boolean>(false);
  hyperlinkInForm = input<WidgetOptions['hyperlinkInForm'] | undefined>(
    undefined,
  );
  canceled = output<boolean>();
  permissionForForm = input<Record<string, NzSafeAny> | null>(null);

  accountPermissions = input<AccountPermission>(undefined);
  @Input({ required: true }) checkPermission!: (
    permission: string,
    row?: NzSafeAny,
    accessType?: string,
  ) => boolean;
  // this is for disabled actions base permission;
  disabledActionLst = input.required<string[]>();
  // this is for disabled actions base condition from layout;
  disabledActions = input<string[]>([]);
  showAvatarInfo = input(false);
  showSubmitButton = input<boolean>(false);
  #userStore = inject(UserStore);
  private refresh = signal(false);

  faceCode = input<string | null>(null);
  customValueBeforeEdit = input<string | undefined>(undefined);

  // set maskClosable
  maskClosable = input<boolean>(true);
  loadingFooterAction = input(false);

  user = computed(() => this.#userStore.user());
  dialogFooter = input<DialogFooter<any>>();

  permissionKey = input<NzSafeAny>(undefined);
  @Input() checkPermissionDetail!: (
    permission: string,
    accessType?: string,
  ) => boolean;
  actionDetailLst = input<ActionPermission[]>([]);
  authAction = input<string | undefined>(undefined);
  showDuplicateButton = input(true);
  isCheckPermissionWithAccessType = input(false);

  private readonly utils = inject(UtilsService);

  // ----------------------------------------
  // define primary buttons here
  @ViewChild('formObj') dynamicForm?: FormComponent;
  @ViewChild('contentCancel') contentCancel!: TemplateRef<''>;

  okButtonType = computed(() => {
    if (this._dialogType() === 'filter' || !this.showSubmitButton()) {
      return 'primary';
    }

    return 'secondary';
  });

  checkPermissionActionDetail = (actionId: string) => {
    const button = (
      this.buttons() as Record<string, { label: string; disabled: boolean }>
    )[actionId];
    if (isNil(button)) return false;

    const accessType = this.isCheckPermissionWithAccessType()
      ? this.defaultFormValue()?.['accessType']
      : null;
    if (!this.checkPermission(actionId, undefined, accessType)) return false;

    if (
      !isNil(this.permissionKey()) &&
      !this.checkPermissionDetail(actionId, accessType)
    ) {
      return false;
    } else return button;
  };

  showDuplicateBtn = computed(() => {
    return !isNil(this.permissionKey()) && this.showDuplicateButton();
  });

  defaultValidateMessage = computed(() => {
    const defaultMessage = this.config()?.defaultValidateError;
    if (typeof defaultMessage === 'string') return defaultMessage;
    return (
      defaultMessage?.[this.dialogType()] ??
      'Please check the format of the fields'
    );
  });

  _formMode = signal<string | null>(null);
  setFormMode = effect(
    async () => {
      const _mode = this.realConfig()?._mode;
      if (!_mode) return;
      const mode = await this.dynamicService.getJsonataExpression({})(
        _mode?.transform ?? '',
        { extend: { formType: this.dialogType() } },
      );
      if (typeof mode === 'string') {
        this._formMode.set(mode);
      } else {
        this._formMode.set(mode?.name);
      }
    },
    { allowSignalWrites: true },
  );

  getStepFooter() {
    return this.dialogFooter() as DialogFooter<'step'>;
  }

  getButtonsFooter() {
    return this.dialogFooter() as DialogFooter<'buttons'>;
  }

  isPaddingLess = computed(() => {
    return ['step', 'mark-scroll'].includes(this._formMode() ?? '');
  });

  isOverflow = computed(() => {
    switch (this._formMode()) {
      case 'mark-scroll':
      case 'step':
        return 'hidden';
      default:
        return 'auto';
    }
  });

  bodyModalStyle = computed(() => {
    return {
      padding: this.isPaddingLess() ? 0 : '',
      overflow: this.isOverflow(),
    };
  });

  #layoutStore = inject(LayoutStore);

  actionCode = effect(
    () => {
      const formType = this.dialogType();

      const authAction = this.authAction();

      switch (formType) {
        case 'create':
        case 'proceed':
        case 'proceedCustom':
        case 'duplicate':
          this.#layoutStore.setCurrentAuthAction(
            authAction ?? AuthActions.Create,
          );
          break;
        case 'edit':
          this.#layoutStore.setCurrentAuthAction(
            authAction ?? AuthActions.Update,
          );
          break;
        default:
          this.#layoutStore.setCurrentAuthAction(AuthActions.Read);
          break;
      }
    },
    { allowSignalWrites: true },
  );

  formTitle = signal<string>('');

  formTitleEffect = effect(
    async () => {
      let getFormTitle =
        this.config()?.formTitle?.[this.dialogType()] ?? `${this.title()}`;
      const transformFormTitle = this.config()?._formTitle?.[this.dialogType()];
      if (transformFormTitle) {
        getFormTitle = await this.dynamicService.getJsonataExpression({})(
          transformFormTitle ?? '',
          this.value(),
        );
      }
      this.formTitle.set(getFormTitle);
    },
    { allowSignalWrites: true },
  );

  deleteButtonCondition = computed<string>(() => {
    return this.config()?.deleteButtonCondition || '';
  });

  hideDeleteButton = signal<boolean>(false);

  hideDeleteButtonEffect = effect(
    async () => {
      if (!this.deleteButtonCondition()) return;
      const deleteButtonCondition =
        await this.dynamicService.getJsonataExpression({})(
          this.deleteButtonCondition() ?? '',
          this.value(),
        );
      this.hideDeleteButton.set(deleteButtonCondition);
    },
    { allowSignalWrites: true },
  );

  normalizeObject(obj: { [key: string]: NzSafeAny }): string {
    const normalizedObj = Object.keys(obj)
      .sort()
      .reduce(
        (acc, key) => {
          acc[key] = obj[key];
          return acc;
        },
        {} as { [key: string]: NzSafeAny },
      );

    return JSON.stringify(normalizedObj);
  }

  findDuplicates(
    array: Array<{ [key: string]: NzSafeAny }>,
  ): Array<{ [key: string]: NzSafeAny }> {
    const seen = new Set<string>();
    const duplicates = new Set<string>();

    array.forEach((obj) => {
      const normalizedString = this.normalizeObject(obj);

      if (seen.has(normalizedString)) {
        duplicates.add(normalizedString);
      } else {
        seen.add(normalizedString);
      }
    });

    return Array.from(duplicates).map((str) => JSON.parse(str));
  }

  hasDuplicateCriterias(): boolean {
    const userGroupConfigCriterias =
      this.dynamicForm?.value.userGroupConfigCriterias;

    return userGroupConfigCriterias
      ? this.findDuplicates(userGroupConfigCriterias).length > 0
      : false;
  }

  submitValue = output<{
    value: NzSafeAny;
    type:
      | DialogType
      | 'toDelete'
      | 'toEdit'
      | 'saveAndAddNew'
      | 'toSubmit'
      | 'toDuplicate';
    callback?: (status: boolean) => void;
    overrideValue?: NzSafeAny;
    authAction?: AuthActions;
  }>();

  hyperlinkClicked = output<{ callback?: (status: boolean) => void }>();

  handleHyperlinkClicked = () => {
    this.hyperlinkClicked.emit({
      callback: (status: boolean) => {
        if (status) {
          this.onCancel();
        }
      },
    });
  };

  reset = signal(false);

  // check if use new dynamic form
  isNewDynamicForm = input<boolean>(false);
  metaDataService = inject(MasterdataService);

  resetForm = () => {
    this.reset.update((prev) => !prev);
  };

  onReset = () => {
    this.value.set(null);
    this.resetForm();
  };
  onCancel = () => {
    // TODO: should check touched state of form, but field translation is not update touched state when user edit
    // this.dialogType() === 'edit' && this.dynamicForm?.formGroup?.touched
    const checkTypes = [
      'create',
      'edit',
      'duplicate',
      'proceed',
      'proceedCustom',
    ];
    const isDirty = this.dynamicForm?.dirty;
    if (
      checkTypes.includes(this.dialogType()) &&
      !this.noNeedConfirm() &&
      isDirty
    ) {
      this.modal.showDialog({
        // nzTitle: 'Unsaved change',
        nzContent: this.contentCancel,
        nzWrapClassName: 'popup popup-confirm hide-footer-btns custom',
        // nzIconType: 'icons:info',
        nzOkText: null,
        nzWidth: '400px',
        nzClosable: false,
        nzCentered: true,
        nzAutofocus: null,
      });
    } else {
      this.dialogVisibleChange.emit(false);
    }
  };

  layoutDataService = inject(LayoutDataService);

  responseError = signal<NzSafeAny>(null);

  preCheckSubmit(sourceData: NzSafeAny) {
    if (!sourceData) return;
    return new Promise((resolve) => {
      this.dynamicService
        .getValueFromApi(
          sourceData,
          { ...this.dynamicForm?.value, _params: this.params() },
          undefined,
          this.faceCode(),
        )
        .subscribe((val: NzSafeAny) => {
          this.responseError.set({
            ...val,
            errorValue: this.dynamicForm?.value,
          });

          setTimeout(() => {
            resolve(true);
          });
        });
    });
  }

  onOk = async (callback?: (status: boolean) => void) => {
    await this.checkFormSourceLoading(); // check nếu có source đang call api thì phải chờ call xong
    const notSubmitIfNoChange = this.config()?.not_submit_if_no_change;
    const isDirty = this.dynamicForm?.dirty;
    if (!isDirty && notSubmitIfNoChange) {
      this.toast.showToast('warning', '', 'There is no change in this record');
      this.dialogVisibleChange.emit(false);
      return;
    }
    if (this.hasDuplicateCriterias()) {
      // this.dynamicForm?.markAllAsTouched();
      this.dynamicForm?.setFormTouched();
      this.dynamicForm?.scrollToFirstInvalidControl();
      return;
    }

    if (!this.dynamicForm?.valid) {
      // this.dynamicForm?.markAllAsTouched();
      this.dynamicForm?.setFormTouched();
      this.dynamicForm?.scrollToFirstInvalidControl();
      return;
    }

    this.responseError.set(null);
    if (this.config().preCheckSubmit) {
      this.processing.set(true);
      await this.preCheckSubmit(this.config().preCheckSubmit?.source);
      this.processing.set(false);
      if (!this.dynamicForm?.valid) {
        this.dynamicForm?.setFormTouched();
        this.dynamicForm?.scrollToFirstInvalidControl();
        return;
      }
    }

    const handleOk = async (submitData?: NzSafeAny) => {
      this.processing.set(true);
      if (!submitData) {
        // In new dynamic form, use formValue to preserve all step data
        submitData =
          this.isNewDynamicForm() === true && this._formMode() === 'step'
            ? { ...this.dynamicForm?.formValue, ...this.dynamicForm?.value }
            : this.dynamicForm?.value;
      }

      if (this._dialogType() === 'filter') {
        submitData = this.getFilterFormValue(submitData);
      }

      this.submitValue.emit({
        value: submitData,
        type: this.dialogType(),
        // don't close dialog until action is success, wait for callback to confirm
        callback: async (status: boolean, responseData?: NzSafeAny) => {
          callback?.(status);
          this.processing.set(false);
          if (status) {
            this.reset.update((val) => !val);
            this.storeValueByKey && this.clearTempValue(this.storeValueByKey);
            this.dialogVisibleChange.emit(false);
          }
        },
      });
    };

    const nestedConfirmOnSubmit = this.config()?.nestedConfirmOnSubmit;
    if (nestedConfirmOnSubmit) {
      this.handleSubmitWithNestedConfirm(nestedConfirmOnSubmit, handleOk);
    } else {
      this.handleSubmitWithConfirm(handleOk);
    }
  };

  onGenerateReport = (id: string) => {
    if (!this.dynamicForm?.valid) {
      // this.dynamicForm?.markAllAsTouched();
      this.dynamicForm?.setFormTouched();
      this.dynamicForm?.scrollToFirstInvalidControl();
      return;
    }

    this.clickedModalButton.emit({
      id: id,
      value: this.dynamicForm?.value,
      config: this.config(),
    });
  };

  checkFormSourceLoading = async () => {
    return new Promise((resolve) => {
      if (!this.dynamicForm?.isSourceLoading) {
        resolve(true);
      } else {
        this.processing.set(true);
        const interval = setInterval(() => {
          if (!this.dynamicForm?.isSourceLoading) {
            clearInterval(interval);
            resolve(true);
            this.processing.set(false);
          }
        }, 10); // Check every 10ms
      }
    });
  };

  handleSubmitWithConfirm = async (handleOk: (value: NzSafeAny) => void) => {
    // In new dynamic form, use formValue to preserve all step data
    const submitData =
      this.isNewDynamicForm() === true && this._formMode() === 'step'
        ? { ...this.dynamicForm?.formValue, ...this.dynamicForm?.value }
        : this.dynamicForm?.value;

    const confirmSettings =
      this.config()?.confirmOnSubmit?.[this._dialogType()];
    if (!confirmSettings) {
      handleOk(submitData);
      return;
    }

    let {
      title = 'Confirm to save',
      content = 'Are you sure you want to save this information?',
    } = confirmSettings as {
      title: string;
      content: string;
    };

    const confirmObj = confirmSettings as {
      transform: string;
      value?: 'formValue' | 'compareValue';
      type?: 'success' | 'warning' | 'info' | 'error';
    };

    if (confirmObj.transform) {
      await this.checkFormSourceLoading(); // check nếu có source đang call api thì phải chờ call xong
      const transformValue =
        confirmObj.value === 'compareValue'
          ? { prevValue: this._value(), currentValue: this.dynamicForm?.value }
          : (this.dynamicForm?.value ?? {});

      const res = (await this.dynamicService.getJsonataExpression({})(
        confirmObj.transform,
        transformValue,
      )) as NzSafeAny;

      if (!res) {
        handleOk(submitData);
        return;
      }
      title = res.title;
      content = res.content;
    }

    this.modal.showDialog(
      {
        nzTitle: title,
        nzContent: content,
        nzWrapClassName: 'popup popup-confirm',
        nzOkText: 'Confirm',
        nzCancelText: 'Cancel',
        nzOnOk: () => handleOk(submitData),
        nzCentered: true,
        nzAutofocus: null,
      },
      confirmObj?.type ?? 'warning',
    );
  };

  handleSubmitWithNestedConfirm = async (
    confirmSettings: any,
    handleOk: (value: NzSafeAny) => void,
  ) => {
    const submitData =
      this.isNewDynamicForm() === true && this._formMode() === 'step'
        ? { ...this.dynamicForm?.formValue, ...this.dynamicForm?.value }
        : this.dynamicForm?.value;

    if (!confirmSettings) {
      handleOk(submitData);
      return;
    }

    let transformSubmitData = submitData;

    const processConfirm = async (
      settings: any,
      onConfirm: (value: NzSafeAny) => void,
    ) => {
      let {
        title = 'Confirm to save',
        content = 'Are you sure you want to save this information?',
        contents = [],
        dialogOptions = null,
      } = settings as {
        title: string;
        content: string;
        contents?: string[];
        dialogOptions?: NzSafeAny;
      };

      const confirmObj = settings as {
        transform: string;
        type?: 'success' | 'warning' | 'info' | 'error';
        transformValueAfterConfirm?: string;
        listMessage?: {
          title: string;
          contents: string[];
          _contents?: string;
        };
        continuteWithCancel?: boolean;
        skipConfirm?: boolean;
      };

      if (confirmObj.transform) {
        await this.checkFormSourceLoading();
        const transformValue = {
          prevValue: this._value(),
          currentValue: this.dynamicForm?.value,
          variables: this.dynamicForm?.variablesSource,
          formType: this._dialogType(),
        };

        const res = (await this.dynamicService.getJsonataExpression({})(
          confirmObj.transform,
          transformValue,
        )) as NzSafeAny;
        if (!res) {
          if (settings.nestedSetting) {
            processConfirm(settings.nestedSetting, onConfirm);
          } else {
            onConfirm(transformSubmitData);
          }

          return;
        }
        title = res.title;
        content = res.content;
        contents = res.contents;
        dialogOptions = res.dialogOptions;
      }

      const listMessage = confirmObj.listMessage;
      if (listMessage?.contents?.length) {
        contents = listMessage?.contents;
      }

      if (listMessage?.title) {
        title = listMessage?.title;
      }

      if (listMessage?._contents) {
        const transformValue = {
          prevValue: this._value(),
          currentValue: this.dynamicForm?.value,
          variables: this.dynamicForm?.variablesSource,
          formType: this._dialogType(),
        };
        contents = (await this.dynamicService.getJsonataExpression({})(
          listMessage?._contents,
          transformValue,
        )) as NzSafeAny[];
      }
      const showNextConfirmation = (index: number) => {
        if (index < (contents?.length ?? 0)) {
          this.modal.showDialog(
            {
              nzTitle: title,
              nzContent: contents?.[index],
              nzWrapClassName: 'popup popup-confirm',
              nzOkText: 'Confirm',
              nzCancelText: 'Cancel',
              nzOnOk: () => showNextConfirmation(index + 1),
              nzCentered: true,
              nzAutofocus: null,
            },
            confirmObj?.type ?? 'warning',
          );
        } else {
          onConfirm(transformSubmitData);
        }
      };

      if (contents?.length) {
        showNextConfirmation(0);
      } else {
        this.modal.showDialog(
          {
            nzTitle: title,
            nzContent: content,
            nzWrapClassName: 'popup popup-confirm',
            nzOkText: 'Confirm',
            nzCancelText: 'Cancel',
            nzOnOk: async () => {
              if (confirmObj.skipConfirm) return;
              if (confirmObj?.transformValueAfterConfirm) {
                transformSubmitData =
                  (await this.dynamicService.getJsonataExpression({})(
                    confirmObj.transformValueAfterConfirm,
                    {
                      formValue: transformSubmitData,
                      variables: this.dynamicForm?.variablesSource,
                      formType: this._dialogType(),
                    },
                  )) as NzSafeAny;
              }

              if (settings.nestedSetting) {
                processConfirm(settings.nestedSetting, onConfirm);
              } else {
                onConfirm(transformSubmitData);
              }
            },
            nzOnCancel: () => {
              if (settings.continuteWithCancel) {
                if (settings.nestedSetting) {
                  processConfirm(settings.nestedSetting, onConfirm);
                } else {
                  onConfirm(transformSubmitData);
                }
              }
            },
            nzCentered: true,
            nzAutofocus: null,
            ...dialogOptions,
          },
          confirmObj?.type ?? 'warning',
        );
      }
    };

    processConfirm(confirmSettings, handleOk);
  };

  _service = inject(BffService);

  // Create new record, keep dialog show and reset form to user continue create another record
  saveAndAddNewProcessing = signal(false);
  onSaveAndAddNew = () => {
    if (!this.dynamicForm?.valid) {
      // this.dynamicForm?.markAllAsTouched();
      this.dynamicForm?.setFormTouched();
      this.dynamicForm?.scrollToFirstInvalidControl();
      return;
    }
    this.saveAndAddNewProcessing.set(true);
    this.submitValue.emit({
      value: this.dynamicForm?.value,
      type: 'saveAndAddNew',
      callback: (status: boolean) => {
        this.saveAndAddNewProcessing.set(false);
        if (status) {
          this.dynamicForm?.setFormUnTouched();
          this.dynamicForm?.reloadVariables();
          this.reset.update((val) => !val);
        }
      },
    });
  };

  submitProcessing = signal(false);
  onSubmit() {
    if (!this.dynamicForm?.valid) {
      // this.toast.showToast('error', 'Error', this.defaultValidateMessage());
      // this.dynamicForm?.markAllAsTouched();
      this.dynamicForm?.setFormTouched();
      this.dynamicForm?.scrollToFirstInvalidControl();
      return;
    }

    this.submitProcessing.set(true);
    this.submitValue.emit({
      value: this.dynamicForm?.value,
      type: 'toSubmit',
      callback: (status: boolean) => {
        this.submitProcessing.set(false);
        if (status) {
          this.reset.update((val) => !val);
          this.dialogVisibleChange.emit(false);
        }
      },
    });
  }

  onDuplicate = () => {
    this.submitValue.emit({
      value: this.value(),
      type: 'toDuplicate',
      overrideValue: {
        id: null,
        code: null,
        effectiveDate: new Date(),
      },
    });
  };

  onEdit = (options?: { authAction: AuthActions }) => {
    this.submitValue.emit({
      value: this.value(),
      type: 'toEdit',
      authAction: options?.authAction,
    });
  };

  onBlock = () => {
    console.log('Blocked');
  };

  onDelete = () => {
    this.submitValue.emit({
      value: this.value(),
      type: 'toDelete',
    });
  };

  clickedModalButton = output<any>();

  _showFooter = computed(() => {
    if (
      this.loading() ||
      this.reLoadingDetail() ||
      this.loadingDetailSchedule()
    )
      return false;
    if (this.dialogFooter()?.type !== 'buttons') return this.showFooter();
    const stateObj = this.footerElementsDisplayState();
    return Object.keys(stateObj).some((key) => stateObj[key] !== false);
  });
  footerElementsDisplayState = signal<Record<string, boolean>>({});
  footerElementsDisplayEffect = effect(
    async () => {
      if (this.dialogFooter()?.type !== 'buttons') return;
      const { buttons } = this.dialogFooter() as DialogFooter<'buttons'>;
      const data = this._value();
      const stateObj = untracked(() =>
        structuredClone(this.footerElementsDisplayState() ?? {}),
      );
      await Promise.all(
        buttons.map(async (btn) => {
          if (!btn._condition) {
            stateObj[btn.id] = true;
            return;
          }
          const display = await this.dynamicService.getJsonataExpression({})(
            btn._condition,
            data,
          );
          stateObj[btn.id] = !!display;
          return;
        }),
      );

      this.footerElementsDisplayState.set(stateObj);
    },
    { allowSignalWrites: true },
  );

  actionClicked = signal<string | null>(null);
  onActionClick(btn: any) {
    if (btn.action === 'toEdit') {
      this.onEdit({ authAction: btn?.authAction });
      return;
    }
    if (btn.action === 'toDelete') {
      this.onDelete();
      return;
    }
    if (btn.action === 'cancel') {
      this.onCancel();
      return;
    }
    if (!this.dynamicForm?.valid) {
      this.dynamicForm?.setFormTouched();
      this.dynamicForm?.scrollToFirstInvalidControl();

      return;
    }
    this.actionClicked.set(btn?.id);
    this.processing.set(true);
    this.submitValue.emit({
      value: this.dynamicForm?.value,
      type: btn?.action as DialogType,
      callback: (success: boolean) => {
        this.processing.set(false);
        if (!success) return;
        this.dialogVisibleChange.emit(false);
      },
    });
  }

  onClickModalButton(btn: NzSafeAny) {
    const { id, backendUrl } = btn;
    this.actionClicked.set(id);
    switch (id) {
      case 'cancel':
        this.onCancel();
        break;
      case 'back':
        this.onCancel();
        break;
      case 'save':
        this.onOk();
        break;
      case 'saveAndDownload': {
        this.onOk((status: boolean) => {
          if (!status) return;
          this.submitValue.emit({
            value: this.dynamicForm?.value,
            type: 'download' as DialogType,
          });
        });
        break;
      }
      case 'save-draft':
        break;
      case 'generateReport':
        this.onGenerateReport(id);

        break;
      //handle case lock and unlock in dialog for PR
      case 'lock':
      case 'unlock':
      case 'edit-custom':
        this.clickedModalButton.emit({
          id: id,
          value: this._value(),
        });
        break;
      case 'note':
        this.clickedModalButton.emit({
          id: id,
          backendUrl: backendUrl,
          value: this._value(),
        });
        break;
      default:
        this.clickedModalButton.emit({
          id: id,
        });
        break;
    }
  }

  // end define primary buttons

  // ----------------------------------------

  // setting config function here
  settingConfig = computed(() => this.dialogType() === 'filter');
  disabledFields = signal<string[]>([]);
  mappingDisabledFields = computed(() =>
    this.disabledFields().map((f) => ({ name: f })),
  );
  realConfig = computed(() => {
    if (!this.settingConfig()) return this.config();
    const tmp = this.utils.cloneObject(this.config());
    if (!tmp?.fields) return undefined;
    tmp.fields = tmp.fields.map((f) => {
      if ((f as FieldConfig).unvisible) return f;
      return {
        ...f,
        _condition: {
          transform: `$not($exists($.extend.disabledFields[name = '${f.name}']))`,
        },
      };
    });
    return tmp;
  });

  filterSettingsFields = computed(() =>
    this.config()?.fields?.filter((f) => !(f as FieldConfig).unvisible),
  );
  // end setting config function

  formatDate(date: string | Date | undefined) {
    return moment(date).format('DD/MM/yyyy HH:mm:ss');
  }
  // ----------------------------------------

  // config dynamic form here
  config = input.required<
    FormConfig & {
      deleteButtonCondition?: string;
      _formTitle?: any;
      not_submit_if_no_change?: boolean;
    }
  >();
  value = model<Data | null | undefined>(null); // initial value
  id = input<string | null | undefined>(null);
  loadAfterOpen = input<boolean>(false);
  valueData = signal<Data | null | undefined>(null);
  service = inject(BffService);
  loading = signal(true);
  reLoadingDetail = signal(false);

  overrideValue = input<Data | null | undefined>(null);
  refreshData = () => {
    this.refresh.update((prev) => !prev);
  };

  utilService = inject(UtilService);

  dataEffect = effect(
    () => {
      if (this.loadAfterOpen()) return;
      if (this.dialogType() === 'viewSchedule') {
        this.loading.set(false);
        return;
      }

      this.refresh();
      const url = this.url();
      const id = this.id();
      if (!url || !id) {
        this.loading.set(false);
        return;
      }

      this.loading.set(true);
      this.reset.set(true);

      let customUrl = '';
      if (this.customDetailBackendUrl()) {
        customUrl = mappingUrl(
          this.customDetailBackendUrl() || '',
          this.value() || {},
        );
      }

      this.service
        .getItem(url, id, customUrl)
        .pipe(
          takeUntil(this.destroy$),
          catchError((err) => {
            this.toast.showToast('error', 'Error', err?.error?.message ?? err);
            this.dialogVisibleChange.emit(false);
            return EMPTY;
          }),
          finalize(() => {
            this.loading.set(false);
          }),
        )
        .subscribe({
          next: async (data) => {
            if (this.overrideValue() && this.dialogType() === 'duplicate') {
              data = { ...data, ...this.overrideValue() } as Data;
            }
            if (this.customValueBeforeEdit()) {
              data = await this.utilService.transformRedirectTo(
                data,
                this.customValueBeforeEdit(),
              );
            }
            this.valueData.set(data);
            this.reset.set(false);
          },
          error: () => {
            this.reset.set(false);
          },
        });
    },
    { allowSignalWrites: true },
  );
  _value = computed(() => {
    if (
      this.url() &&
      ['edit', 'view', 'duplicate', 'new'].includes(this.dialogType())
    )
      return this.valueData();
    return this.memorizedClone(this.value());
  });

  // memorize value
  private memorizedClone = memoize(
    (value: Data | null | undefined) => this.utils.cloneObject(value),
    // Use id for cache key if available to avoid stringifying entire object
    (value: Data | null | undefined) => JSON.stringify(value),
  );

  dialogType = input.required<DialogType>();
  // end config dynamic form

  changeLoadingDialog(loading: boolean) {
    this.reLoadingDetail.set(loading ?? false);
  }
  // ----------------------------------------

  // condition for footer buttons

  buttons = computed(() => {
    switch (this._dialogType()) {
      case 'view': {
        return {
          delete: {
            label: 'Delete',
            disabled: this.isDisabledAction('delete'),
          },
          duplicate: this.showDuplicateBtn()
            ? {
                label: 'Duplicate',
                disabled: this.isDisabledAction('duplicate'),
              }
            : undefined,
          edit: { label: 'Edit', disabled: this.isDisabledAction('edit') },
        };
      }
      case 'create': {
        return {
          cancel: { label: 'Cancel' },
          ok: { label: 'Save' },
          saveAndAddNew: { label: 'Save & Add New' },
          submit: { label: 'Submit' },
        };
      }
      case 'edit':
        return {
          cancel: { label: 'Cancel' },
          ok: { label: 'Save' },
          submit: { label: 'Submit' },
        };
      case 'proceedCustom':
      case 'proceed':
        return { cancel: { label: 'Cancel' }, ok: { label: 'Save' } };
      case 'viewSchedule':
        return {
          cancel: { label: 'Cancel' },
        };
      case 'filter':
        return {
          reset: { label: 'Clear all' },
          cancel: { label: 'Cancel' },
          ok: { label: 'Apply' },
        };
      default:
        return {};
    }
  });

  isDisabledAction(id: string) {
    return this.disabledActions()?.includes(id) ?? false;
  }

  _dialogType = computed(() => {
    if (this.dialogType() === 'duplicate') return 'create';
    return this.dialogType();
  });

  open = signal<boolean>(false);
  _isOpen = effect(
    () => {
      if (this.isPopup() === null || this.isPopup() === undefined) {
        switch (this.dialogType()) {
          case 'create':
          case 'edit':
          case 'duplicate':
          case 'proceed':
          case 'proceedCustom':
            this.open.set(true);
            break;

          default:
            this.open.set(false);
            break;
        }
      } else {
        if (this.utils.isBoolean(this.isPopup())) {
          this.open.set(false);
        } else if (
          [
            'edit',
            'view',
            'duplicate',
            'new',
            'proceed',
            'proceedCustom',
          ].includes(this.dialogType())
        ) {
          this.open.set(true);
        } else {
          this.open.set(false);
        }
      }
    },
    { allowSignalWrites: true },
  );

  onAfterOpen() {
    if (this.loadAfterOpen()) {
      this.loading.set(false);
    }
  }

  setDrawerFixed() {
    if (this.dialogType() === 'filter') {
      return false;
    }
    return true;
  }

  get valueDef() {
    return this.dynamicForm?.valueDef;
  }
  get overViewValue() {
    const dependentField = this.realConfig()?.overview?.dependentField;
    if (dependentField !== undefined) {
      const value = this.dynamicForm?.value[dependentField];
      return this.isValidObjectValue(value) ? value : null;
    }
    return null;
  }
  get overViewValueGroup() {
    const value: NzSafeAny = [];
    this.realConfig()?.overviewGroup?.forEach((group) => {
      const dependentField = group?.dependentField;
      if (dependentField !== undefined) {
        value.push(this.dynamicForm?.value[dependentField]);
      }
    });
    return value;
  }

  private isValidObjectValue(value: Record<string, NzSafeAny>): boolean {
    if (this.utils.isEmpty(value)) return false;
    return Object.keys(value).some((key) => {
      const v = value[key];
      if (this.utils.isNil(v)) return false;
      if (v instanceof Date) return true;
      if (typeof v === 'object') return this.isValidObjectValue(v);
      if (typeof v === 'string') return v !== '';
      return true;
    });
  }

  get noDataMessagesOverview() {
    const dependentField = this.realConfig()?.overview?.noDataMessages;
    if (dependentField !== undefined) {
      return this.dynamicForm?.value[dependentField];
    }
  }
  // Handle size of popup and drawer by dialogType
  size = signal<ModalSize>('small');
  _size = effect(
    () => {
      const formSize = this.config()?.formSize;
      if (formSize) {
        this.size.set((formSize[this._dialogType()] ?? 'small') as ModalSize);
      } else if (
        this.realConfig()?.overview ||
        this.realConfig()?.overviewGroup
      ) {
        this.size.set('largex');
      } else if (
        this._formMode() === 'mark-scroll' ||
        this._formMode() === 'step'
      ) {
        this.size.set('middle');
      } else {
        this.size.set('small');
      }
    },
    { allowSignalWrites: true },
  );

  shouldDisabledReset = signal(false);
  onFormValueChanges(value: Record<string, unknown>) {
    if (this.storeValueByKey && this.dynamicForm?.dirty) {
      this.setTempValue(this.storeValueByKey, value);
    }
    this.formValue.set(value);
  }

  formValue = signal<any>(null);
  filterFormValueChangesEffect = effect(
    () => {
      if (this._dialogType() !== 'filter') return;
      const value = this.getFilterFormValue(this.formValue());
      if (this.isValidObjectValue(value)) {
        this.shouldDisabledReset.set(false);
      } else {
        this.shouldDisabledReset.set(true);
      }
    },
    { allowSignalWrites: true },
  );

  private getFilterFormValue(value: any) {
    const cloneValue = structuredClone(value);
    const disabledFields = this.mappingDisabledFields() ?? [];
    for (const field of disabledFields) {
      delete cloneValue[field.name];
    }
    return cloneValue;
  }

  filterListChange(data: NzSafeAny[]) {
    this.disabledFields.set(data);
  }

  checkActionFooterPermission(btn: ModalFooterButtons) {
    const condition =
      !btn.conditionValue ||
      btn.conditionValue === this._value()?.[btn.conditionByKey ?? ''];
    if (!this.open()) {
      const accessType = this.isCheckPermissionWithAccessType()
        ? this._value()?.['accessType']
        : null;
      return this.checkPermission(btn.id, undefined, accessType) && condition;
    }
    return condition;
  }

  ngOnDestroy() {
    // clear memorized value
    this.memorizedClone.cache.clear?.();

    this.destroy$.next();
    this.destroy$.complete();
    this.#layoutStore.setCurrentAuthAction(AuthActions.Read);
  }

  onNextStep(steps = 1) {
    this.dynamicForm?.nextStep(steps);
  }

  currentStep() {
    return this.dynamicForm?.currentStep ?? 0;
  }

  maxStep() {
    return (this.dynamicForm?.formSteps?.length ?? 1) - 1;
  }

  isNextStepDisabled() {
    return this.dynamicForm?.isNextStepDisabled();
  }

  // have to clone deep _value to avoid change data in form
  defaultFormValue = computed(() => structuredClone(this._value()));

  // for save temp value
  localStorageService = inject(LocalStorageService);
  valueStorageConfig = input<ValueStorageConfig>();
  static VALUE_STORAGE_KEY = 'valueStorage';
  storeValueByKey: string | null = null;
  private tempValue: unknown = null;
  showToastApplyTempValue = false;

  storeTempValueCheckEffect = effect(() => {
    const config = this.valueStorageConfig();
    if (!config) return;
    this.tempValue = this.getTempValue(config.key);
    this.storeValueByKey = config.key;
    if (!this.tempValue || !this.isValidObjectValue(this.tempValue)) return;
    this.showToastApplyTempValue = true;
  });

  applyTempValue() {
    this.value.set(this.tempValue as Data);
    this.resetForm();
    this.showToastApplyTempValue = false;
  }

  getTempValue(key: string) {
    const storageObj = this.localStorageService.getItem<
      Record<string, unknown>
    >(LayoutDialogComponent.VALUE_STORAGE_KEY);
    return storageObj?.[key] ?? null;
  }

  setTempValue(key: string, value: unknown) {
    let storageObj =
      this.localStorageService.getItem<Record<string, unknown>>(
        LayoutDialogComponent.VALUE_STORAGE_KEY,
      ) ?? {};
    storageObj = { ...storageObj, [key]: value };
    this.localStorageService.setItem(
      LayoutDialogComponent.VALUE_STORAGE_KEY,
      storageObj,
    );
  }

  validAction = (actionId: string) => {
    const accessType = this.isCheckPermissionWithAccessType()
      ? this.defaultFormValue()?.['accessType']
      : null;
    if (!this.checkPermission(actionId, undefined, accessType)) return false;

    if (
      !isNil(this.permissionKey()) &&
      !this.checkPermissionDetail(actionId, accessType)
    ) {
      return false;
    } else return true;
  };

  clearTempValue(key: string) {
    const storageObj = this.localStorageService.getItem<
      Record<string, unknown>
    >(LayoutDialogComponent.VALUE_STORAGE_KEY);
    if (!storageObj) return;
    delete storageObj[key];
    this.localStorageService.setItem(
      LayoutDialogComponent.VALUE_STORAGE_KEY,
      storageObj,
    );
  }

  AuthActions = AuthActions;

  getAuthActionByFormType = computed(() => {
    const authAction = this.authAction();
    if (authAction) {
      return authAction;
    }
    const formType = this.dialogType();
    switch (formType) {
      case 'create':
      case 'proceed':
      case 'proceedCustom':
      case 'duplicate':
        return AuthActions.Create;
      case 'edit':
        return AuthActions.Update;
      default:
        return AuthActions.Read;
    }
  });
}
