@if (widgetHistoryType() === 'table') {
  <hrdx-modal
    [isVisible]="dialogVisible"
    [title]="titleTemplate"
    [footer]="footer"
    (visibleChange)="onVisibleChange(!dialogVisible)"
    (canceled)="onCancel()"
    [wrapClassName]="'dialog-history'"
    [bodyModalStyle]="{
      overflow: 'auto',
    }"
    [size]="size()"
    [maskClosable]="true"
    [centered]="true"
  >
    <ng-container
      [ngTemplateOutlet]="content"
      *ngIf="dialogVisible"
    ></ng-container>
  </hrdx-modal>
} @else {
  <hrdx-drawer
    [visible]="dialogVisible"
    [title]="titleTemplate"
    [footer]="widgetHistoryType() === 'table' ? footer : undefined"
    [maskTransparent]="widgetHistoryType() !== 'table'"
    (closed)="onCancel()"
    [width]="size()"
    [paddingLess]="true"
    (visibleChanged)="onVisibleChange($event)"
    [wrapClassName]="'dialog-history'"
  >
    <ng-container
      [ngTemplateOutlet]="content"
      *ngIf="dialogVisible"
    ></ng-container>
  </hrdx-drawer>
}

<ng-template #titleTemplate>
  <div class="title--container">
    <div class="title__lablel">
      <span> {{ _title() | async }}</span>
    </div>
    @if (showAvatarInfo()) {
      <div
        class="title__avatar"
        [ngClass]="{ 'table-type': widgetHistoryType() === 'table' }"
      >
        <hrdx-avatar
          [type]="avatarType.Image"
          [shape]="avatarShape.Circle"
          [size]="avatarSize.Medium"
          [imgSrc]="dataLayout()?.['dataProfile']?.['avatarLink']"
          [imgAlt]="dataLayout()?.['dataProfile']?.['name']"
          [text]="dataLayout()?.['dataProfile']?.['name']"
        ></hrdx-avatar>
        <span class="title__avatar-name">
          {{ dataLayout()?.['dataProfile']?.['name'] }}</span
        >
      </div>
    }
    <!-- @else {
      <div style="height: 32px; padding: 10px 16px"></div>
    } -->
  </div>
</ng-template>

<ng-template #content>
  <!-- {{ typeDialog() }} -->
  <!-- {{ formObj.value | json }} -->

  <ng-container
    [ngTemplateOutlet]="filterContent"
    *ngIf="checkHasFilter()"
  ></ng-container>

  <div
    class="history-dialog__content"
    [ngClass]="getClassDialog__content()"
    [style.maxHeight]="checkHasFilter() ? calcHeightFilterForm() : undefined"
  >
    <div class="left-side" *ngIf="widgetHistoryType() !== 'table'">
      <div
        class="left-side-header"
        *ngIf="sidebarHeaderOptions()?.visible ?? true"
      >
        <ng-container [ngTemplateOutlet]="sidebarHeader"></ng-container>
      </div>
      <div
        class="left-side-main"
        [style.maxHeight]="checkHasFilter() ? 'none' : undefined"
      >
        @if (!loading()) {
          <ng-container [ngTemplateOutlet]="sidebarContent"></ng-container>
        } @else {
          <ng-container [ngTemplateOutlet]="skeleton"></ng-container>
        }
      </div>
    </div>
    <div class="right-side">
      <ng-container
        [ngTemplateOutlet]="filterContent"
        *ngIf="isFilterRight()"
      ></ng-container>
      <div
        class="right-side-inner {{
          paddingTab() === true ? 'have-padding' : 'no-padding'
        }}"
        [style.maxHeight]="isFilterRight() ? calcHeightFilterForm() : undefined"
        [ngClass]="{
          'group-array': values()?.['_historyGroupArray'].length > 0,
        }"
      >
        @if (!loading()) {
          @if (
            values()?.['_historyGroupArray'].length > 0 && isCollapsContainer
          ) {
            <div class="right-side-content">
              <!-- <div class="label">{{ values()?.['_historyTitle'] }}</div> -->
              @for (value of values()?.['_historyGroupArray']; track $index) {
                <hrdx-collapse
                  [expand]="true"
                  [header]="collapseTitle() | async"
                  [headerButtons]="collapseHeaderButtons"
                  [showArrow]="showArrowCollaps()"
                  [disabled]="disabledEventCollaps()"
                  (headerButtonClicked)="
                    onCollapseHeaderButtonClicked($event, $index)
                  "
                >
                  <dynamic-form
                    [config]="config()?.fields ?? []"
                    [sources]="config()?.sources ?? {}"
                    [variables]="config()?.variables ?? {}"
                    [formValue]="value"
                    [readOnly]="true"
                    [ppxClass]="'ppxm-style'"
                    [extend]="{ formType: 'view' }"
                    [reload]="reloadHistory()"
                    [_mode]="formMode()"
                    [isNewDynamicForm]="isNewDynamicForm()"
                    [faceCode]="faceCode()"
                    [authAction]="AuthActions.History"
                    #formObj
                  ></dynamic-form>
                  <div class="right-side-content-footer">
                    <ng-container
                      [ngTemplateOutlet]="historyContentFooter"
                      [ngTemplateOutletContext]="{ value: value }"
                    ></ng-container>
                  </div>
                </hrdx-collapse>
              }
            </div>
          } @else {
            <ng-container [ngTemplateOutlet]="mainContent"></ng-container>
          }
        } @else {
          <ng-container [ngTemplateOutlet]="skeleton"></ng-container>
        }
      </div>
    </div>
  </div>
</ng-template>

<ng-template #skeleton>
  <div class="skeleton-container">
    <nz-skeleton [nzActive]="true"></nz-skeleton>
    <nz-skeleton [nzActive]="true"></nz-skeleton>
    <nz-skeleton [nzActive]="true"></nz-skeleton>
    <nz-skeleton [nzActive]="true"></nz-skeleton>
  </div>
</ng-template>

<ng-template #footer>
  <div class="dialog--footer" *ngIf="widgetHistoryType() === 'table'">
    <div class="left-btns"></div>
    <div class="right-btns list-btn">
      <hrdx-button
        [type]="'tertiary'"
        [title]="'Cancel'"
        [size]="'default'"
        (clicked)="onCancel()"
      />
      <hrdx-button
        [type]="'primary'"
        [size]="'default'"
        [title]="'Edit'"
        (clicked)="onEdit()"
        *ngIf="
          checkPermission('delete') ||
          checkPermission('edit') ||
          checkPermission('create')
        "
      />
    </div>
  </div>
</ng-template>
<hrdx-modal
  [size]="'xsmall'"
  [isVisible]="effectiveDatePopup"
  (canceled)="cancelEffectiveDatePopup()"
  [title]="'Enter Effective Date'"
  [footer]="dialogFooter"
  *ngIf="effectiveDatePopup"
>
  <ng-container>
    <dynamic-form
      [config]="fields"
      [reload]="resetEffectiveDateForm()"
      #effectiveDateForm
    ></dynamic-form>
  </ng-container>
</hrdx-modal>

<ng-template #dialogFooter>
  <div class="dialog--footer">
    <hrdx-button
      [title]="'Cancel'"
      [type]="'tertiary'"
      (clicked)="cancelEffectiveDatePopup()"
      [size]="'default'"
    />
    <hrdx-button
      [type]="'primary'"
      [title]="'Proceed'"
      (clicked)="addItem()"
      [disabled]="!effectiveDateForm?.valid"
      [size]="'default'"
    />
  </div>
</ng-template>

<ng-template #historyContentFooter let-value="value">
  <hrdx-credit-footer [footerConfig]="footerConfig()" [value]="value" />
</ng-template>

<ng-template #sidebarHeader>
  <span>History</span>
  <hrdx-button
    [type]="'link'"
    [size]="'xsmall'"
    (clicked)="openEffectiveDatePopup()"
    [title]="'Insert New Record'"
    [isLeftIcon]="true"
    [leftIcon]="'plus'"
    [isLoading]="loadingInsertNew()"
    [disabled]="loadingInsertNew()"
    *ngIf="_showInsertNewRecord() | async"
  />
</ng-template>

<ng-template #sidebarContent>
  <div class="left-side-search-bar" *ngIf="showSearchBar()">
    <hrdx-input
      [field]="InputFieldType.IconLeft"
      [placeHolder]="'Search'"
      [icon]="'magnifying-glass'"
    />
  </div>

  <div class="left-side-filter-bar" *ngIf="isFilterInLeftSidebar()">
    <ng-container [ngTemplateOutlet]="filterContent"></ng-container>
  </div>
  <div
    class="left-side-content"
    *ngFor="let c of dataFilter(); index as idx; trackBy: trackByIdx"
    (click)="selectCard(idx)"
  >
    <div
      class="left-side-card"
      [ngClass]="{
        active: idx === selectedIdx(),
      }"
    >
      <div class="left-side-card-label">{{ c['_historyTitle'] }}</div>
      <div
        [ngClass]="
          c['_historyDescription'] === 'Active'
            ? 'left-side-card-description-active'
            : c['_historyDescription'] === 'Inactive'
              ? 'left-side-card-description-inactive'
              : 'left-side-card-description'
        "
      >
        <span *ngIf="!isLayoutWidget() && !isNotViewBageLeftSidebar()"
          >&#x2022;</span
        >
        {{ c['_historyDescription'] }}
      </div>
    </div>
  </div>
</ng-template>

<ng-template #mainContent>
  @if (dataFilter().length > 0) {
    <!-- <div
      class="right-side-header"
      *ngIf="(widgetOptions()?.header ?? true) && !isLayoutWidget()"
    >
      <div class="right-side-header-title">
        {{ widgetHeaderOptions()?.title ?? generateDate(selectedIdx()) }}
      </div>
      <div class="right-side-header-widget">
        <hrdx-button
          [type]="'ghost-gray'"
          [size]="'small'"
          [title]="'Edit'"
          (clicked)="onEdit()"
          [onlyIcon]="true"
          [icon]="'pen'"
          class="right-side-header-widget-button"
          *ngIf="canDoAction('edit')"
        />
        <hrdx-button
          [type]="'ghost-gray'"
          [size]="'small'"
          [title]="'Duplicate'"
          (clicked)="onDuplicate()"
          [onlyIcon]="true"
          [icon]="'clone'"
          class="right-side-header-widget-button"
          *ngIf="
            (widgetHeaderOptions()?.duplicate ?? true) &&
            canDoAction('duplicate')
          "
        />
        <hrdx-button
          [type]="'ghost-gray'"
          [size]="'small'"
          [title]="'Delete'"
          (clicked)="onDelete()"
          [onlyIcon]="true"
          [icon]="'trash-can'"
          class="right-side-header-widget-button"
          *ngIf="canDoAction('delete')"
        />
      </div>
    </div>
    <nz-divider class="right-side-divider" *ngIf="!isLayoutWidget()" /> -->

    <ng-container *ngIf="!loading()">
      <div
        class="right-side-content"
        *ngIf="!isCollapsContainer"
        [ngClass]="{ 'widget-table': widgetHistoryType() === 'table' }"
      >
        @if (groupDataByKey()) {
          <dynamic-form
            [config]="config()?.fields ?? []"
            [sources]="config()?.sources ?? {}"
            [variables]="config()?.variables ?? {}"
            [formValue]="value()"
            [readOnly]="true"
            [ppxClass]="'ppxm-style'"
            [extend]="{
              formType: 'view',
              defaultValue: value(),
              permission: permissionForForm(),
            }"
            [_mode]="config()?._mode"
            [reload]="reloadHistory()"
            [faceCode]="faceCode()"
            [authAction]="AuthActions.History"
            #formObj
          ></dynamic-form>
        } @else {
          <hrdx-tabs [selectedIndex]="selectedIdx()" [showTabBar]="false">
            <hrdx-tab>
              <ng-container *ngFor="let item of [currentFormValue()]">
                <dynamic-form
                  [config]="config()?.fields ?? []"
                  [sources]="config()?.sources ?? {}"
                  [variables]="config()?.variables ?? {}"
                  [formValue]="item"
                  [readOnly]="true"
                  [ppxClass]="'ppxm-style'"
                  [extend]="{
                    formType: 'view',
                    defaultValue: item,
                    permission: permissionForForm(),
                  }"
                  [_mode]="config()?._mode"
                  #formObj
                  *ngIf="item"
                  [isNewDynamicForm]="isNewDynamicForm()"
                  [faceCode]="faceCode()"
                  [authAction]="AuthActions.History"
                ></dynamic-form>
              </ng-container>
            </hrdx-tab>
          </hrdx-tabs>
        }

        <div
          class="right-side-content-footer"
          *ngIf="widgetHistoryType() !== 'table'"
          [ngClass]="{ 'is-navigation_contract': showNavigateToContactBtn() }"
        >
          <hrdx-button
            [type]="'link'"
            [title]="'Contract Data'"
            [size]="'default'"
            [isLeftIcon]="true"
            [leftIcon]="'icon-arrow-bend-down-left-bold'"
            (clicked)="onNavigateToContract()"
            *ngIf="showNavigateToContactBtn()"
          />
          <ng-container
            [ngTemplateOutlet]="historyContentFooter"
            [ngTemplateOutletContext]="{ value: value() }"
          ></ng-container>
        </div>
      </div>
      <div
        class="footer-action--container"
        *ngIf="
          (widgetOptions()?.header ?? true) && widgetHistoryType() !== 'table'
        "
      >
        <ng-container [ngTemplateOutlet]="historyActionFooter"></ng-container>
      </div>
    </ng-container>
  } @else {
    <ng-container [ngTemplateOutlet]="empty"></ng-container>
  }
</ng-template>

<ng-template #empty>
  <div class="empty">
    <hrdx-illustrations
      [size]="illustrationConfig.size"
      [type]="illustrationConfig.type"
      [subAction]="false"
      [subText]="{
        headingText: 'No data',
        subDescriptionText: '',
      }"
    ></hrdx-illustrations>
  </div>
</ng-template>

<ng-template #filterContent>
  <div
    class="history-dialog__filter-form"
    [ngClass]="{ 'widget-table': widgetHistoryType() === 'table' }"
    [ngStyle]="styleFilterForm()"
    *ngIf="filterConfig()?.fields"
    #filterForm
  >
    <dynamic-form
      [config]="filterConfig()?.fields ?? []"
      [sources]="filterConfig()?.sources ?? {}"
      [variables]="filterConfig()?.variables ?? {}"
      [formValue]="defaultFilterValue() ?? {}"
      [extend]="{
        formType: 'filter',
        addOnValue: addOnValue(),
        params: params(),
        refresh: resetFilterForm(),
      }"
      [readOnly]="false"
      [ppxClass]="'ppxm-style'"
      (valueChanges)="formFilterChange($event)"
      [faceCode]="faceCode()"
      [authAction]="AuthActions.History"
      #filterFormObj
    ></dynamic-form>
    <!-- <div class="history-dialog__filter-footer" *ngIf="isLayoutWidget()">
      <span class="history-dialog__filter-divider"></span>
    </div> -->
  </div>
</ng-template>

<ng-template #historyActionFooter>
  <div class="right-side-footer">
    <hrdx-button
      [type]="'ghost-color'"
      [size]="'default'"
      class="right-side-header-widget-button"
      *ngIf="loadingFooterAction()"
      [isLoading]="true"
    />
    @for (action of _actions() | async; track action.id) {
      <hrdx-button
        [type]="'tertiary'"
        [size]="'default'"
        [title]="action.title ?? action.id"
        (clicked)="onActionClick(action.id)"
        [onlyIcon]="true"
        [icon]="action.icon"
        class="right-side-header-widget-button"
        *ngIf="canDoAction(action.id)"
        [disabled]="loadingFooterAction()"
      />
    }
  </div>
</ng-template>
