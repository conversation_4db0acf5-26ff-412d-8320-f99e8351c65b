import { CommonModule } from '@angular/common';
import {
  Component,
  computed,
  effect,
  EventEmitter,
  inject,
  input,
  Input,
  OnChanges,
  OnDestroy,
  Output,
  signal,
  SimpleChanges,
  ViewChild,
  OnInit,
  ElementRef,
} from '@angular/core';
import {
  DynamicFormService,
  FormComponent,
  FormFieldsConfig,
  SourceField,
  UtilsService,
} from '@hrdx-fe/dynamic-features';
// eslint-disable-next-line @nx/enforce-module-boundaries
import {
  ActionPermission,
  AuthActions,
  BffService,
  Data,
  FilterService,
  FormConfig,
  FunctionSpec,
  getValue,
  HistoryDialogOptions,
  LayoutButton,
  LayoutStore,
  MasterdataService,
  UtilService,
  WidgetHeaderOptions,
} from '@hrdx-fe/shared';
import {
  AvatarComponent,
  AvatarShape,
  AvatarSize,
  AvatarType,
  ButtonComponent,
  CollapseComponent,
  CollapseHeaderButton,
  CreditFooterComponent,
  DrawerComponent,
  IllustrationsComponent,
  IllustrationsSize,
  IllustrationsType,
  InputComponent,
  InputFieldType,
  LoadingComponent,
  ModalComponent,
  TabsModule,
  ToastMessageComponent,
} from '@hrdx/hrdx-design';
import { QueryFilter } from '@nestjsx/crud-request';
import { minBy, maxBy, isNil } from 'lodash';
import * as moment from 'moment';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzOverlayModule } from 'ng-zorro-antd/core/overlay';
import { NzSafeAny } from 'ng-zorro-antd/core/types';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzMessageModule, NzMessageService } from 'ng-zorro-antd/message';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { Subject, catchError, of, takeUntil } from 'rxjs';
import { LayoutTableStore } from '../layout-table.store';
import { NzSkeletonModule } from 'ng-zorro-antd/skeleton';

type filterOperator = '$eq' | '$inRange' | '$in' | '$fuzzy' | '$dateInRange';
type EffectiveDateValidation = 'sameDate' | 'greaterThanLastest';

@Component({
  selector: 'lib-layout-history',
  standalone: true,
  // changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    CommonModule,
    FormComponent,
    NzModalModule,
    NzOverlayModule,
    NzButtonModule,
    NzIconModule,
    NzDividerModule,
    InputComponent,
    ButtonComponent,
    ModalComponent,
    CollapseComponent,
    NzMessageModule,
    IllustrationsComponent,
    AvatarComponent,
    NzSkeletonModule,
    TabsModule,
    DrawerComponent,
    CreditFooterComponent,
  ],
  providers: [ToastMessageComponent, UtilsService],
  templateUrl: './layout-history.component.html',
  styleUrl: './layout-history.component.less',
})
export class LayoutHistoryComponent implements OnChanges, OnInit, OnDestroy {
  static DEFAULT_ACTIONS: LayoutButton[] = [
    {
      title: 'Delete',
      icon: 'icon-trash-bold',
      id: 'delete',
      type: 'tertiary',
    },
    {
      title: 'Duplicate',
      icon: 'icon-copy-bold',
      id: 'duplicate',
      type: 'tertiary',
    },
    {
      title: 'Edit',
      icon: 'icon-pencil-simple-bold',
      id: 'edit',
      type: 'tertiary',
    },
  ];
  private readonly destroy$ = new Subject<void>();
  private readonly utilsService = inject(UtilsService);

  avatarType = AvatarType;
  avatarShape = AvatarShape;
  avatarSize = AvatarSize;

  AuthActions = AuthActions;

  readonly illustrationConfig = {
    size: IllustrationsSize.Small,
    type: IllustrationsType.Empty,
  };
  @Input() dialogVisible = false;
  @Output() dialogVisibleChange = new EventEmitter();
  config = input<
    | (FormConfig & {
        historyTitle: string;
        historyDescription: string;
        historyGroup: string;
        historyHeaderTitle?: string;
        historyStatusName?: string;
        historyCodeName?: string;
        historyCloneInactive?: boolean;
        effectiveDateValidations?: EffectiveDateValidation[];
        customDataItemToInsertNew?: string;
        customTypeEmitInsertNew?: string;
        customDataHistory?: string;
        isFilterRight?: boolean;
        customDataSelectedItem?: string;
        isFilterInLeftSidebar?: boolean;
        isNotViewBageLeftSidebar?: boolean;
        paddingTab?: boolean;
        styleFilterForm?: Record<string, NzSafeAny>;
      })
    | undefined
  >(undefined);
  @Input() okLabel = 'Lưu';
  @Input() cancelBtn = true;
  @Input() resetBtn = false;
  url = input.required<string | null>();
  @Input() isCollapsContainer = false;
  @Output() submitDialog = new EventEmitter();
  @Output() navigateEmit = new EventEmitter();
  @Input({ required: true }) checkPermission!: (
    permission: string,
    row?: NzSafeAny,
    accessType?: string,
  ) => boolean;
  @Input() checkPermissionDetail!: (
    permission: string,
    accessType?: string,
  ) => boolean;
  actionDetailLst = input<ActionPermission[]>([]);
  disabledActionLst = input.required<string[]>();
  dynamicService = inject(DynamicFormService);
  InputFieldType = InputFieldType;
  filterService = inject(FilterService);
  filterValue = signal<any>(undefined);
  filterConfig = input<{
    fields: FormFieldsConfig[];
    filterMapping?: {
      valueField: string;
      field: string;
      operator: filterOperator;
    }[];
    sources: Record<string, NzSafeAny>;
    variables: Record<string, NzSafeAny>;
  }>();
  typeDialog = input<'create' | 'edit'>('create');
  data = signal<Data[]>([]);
  _data = signal<Data[]>([]);
  showInsertNewRecord = input(true);
  skipInsertNewProceed = input(false);
  showNavigateToContactBtn = input(false);
  showSearchBar = input(false);
  isCustomInsertNewProceed = input(false);
  widgetHeaderOptions = input<WidgetHeaderOptions>();
  functionsSpec = inject(LayoutTableStore).currentFunctionSpec;
  metaDataService = inject(MasterdataService);
  popupService = inject(NzMessageService);
  toast = inject(ToastMessageComponent);
  service = inject(BffService);
  selectedData = input<Data | null>();
  selectedHistoryId = input<string | null>();
  widgetHistoryType = input<string | null>();
  loading = signal(false);
  refreshData = signal<boolean>(false);
  filterMethod = input<'api' | 'manual'>('api');
  addOnValue = input<{ [k: string]: string }>({});
  options = input<HistoryDialogOptions>({});
  functionSpec = input<FunctionSpec>();
  showAvatarInfo = input(false);
  dataLayout = input<Record<string, NzSafeAny> | null>(null);
  params = input<any>({});
  isLayoutWidget = input(false);
  defaultFilterValue = input<NzSafeAny>();
  groupDataByKey = input<string>('');
  dataInput = input<NzSafeAny[]>([], {
    alias: 'data',
  });
  actions = input<LayoutButton[] | null>(null);
  permissionForForm = input<Record<string, NzSafeAny> | null>(null);
  resetFilterForm = signal(false);
  isCheckPermissionWithAccessType = input(false);

  faceCode = input<string | null>(null);
  loadingFooterAction = input(false);

  layoutOptions = computed(() => {
    return this.functionSpec()?.layout_options;
  });

  // check if use new dynamic form
  isNewDynamicForm = input<boolean>(false);

  ngOnInit() {
    if (this.defaultFilterValue()) {
      // this.filterValue.set(this.defaultFilterValue());
      this.formFilterChange({ value: this.defaultFilterValue() });
    }
  }

  @ViewChild('filterForm') filterFormRef!: ElementRef;

  isFilterRight = computed(() => {
    return this.config()?.isFilterRight ?? false;
  });

  paddingTab = computed(() => {
    return this.layoutOptions()?.paddingTab ?? true;
  });

  isFilterInLeftSidebar = computed(() => {
    return this.config()?.isFilterInLeftSidebar ?? false;
  });

  isNotViewBageLeftSidebar = computed(() => {
    return this.config()?.isNotViewBageLeftSidebar ?? false;
  });

  calcHeightFilterForm() {
    if (this.filterFormRef) {
      const height = this.filterFormRef.nativeElement.offsetHeight;
      return `calc(100vh - ${height}px)`;
    }
    return null;
  }

  _showInsertNewRecord = computed(async () => {
    const insertTransform = this.sidebarHeaderOptions()?.insertRecord;
    let isShowInsertNewRecord = true;
    if (this.utilsService.isNil(insertTransform)) {
      isShowInsertNewRecord = this.showInsertNewRecord();
    } else {
      if (typeof insertTransform === 'boolean') {
        isShowInsertNewRecord = insertTransform;
      } else {
        isShowInsertNewRecord = (await this.transformValue(
          insertTransform.transform,
          this.selectedData(),
        )) as boolean;
      }
    }

    return isShowInsertNewRecord && this.checkPermission('create');
  });

  private transformValue(transform: string, data: NzSafeAny) {
    return this.dynamicService.getJsonataExpression({})(transform, data);
  }

  getClassDialog__content = computed(() => {
    const classNameArray = [];
    if (this.isLayoutWidget() || this.isNotViewBageLeftSidebar()) {
      classNameArray.push('is_layout_widget');
    }
    if (this.widgetHistoryType() === 'table') {
      classNameArray.push('is_table');
    }
    if (this.isFilterRight()) {
      classNameArray.push('is_filter_right');
    }

    const className = classNameArray.join(' ');
    return className;
  });

  onNavigateToContract() {
    this.navigateEmit.emit({
      type: 'navigateToContract',
      callback: (stt?: boolean) => {
        if (stt) {
          this.onCancel();
        }
      },
    });
  }
  // filterQuery = computed(() => {
  //   const getFilterMapping =  this.functionSpec()?.layout_options?.historyFilterMapping;
  //   const res =
  //     getFilterMapping
  //       ?.map((f: any) => {
  //         return {
  //           field: f.field,
  //           operator: f.operator,
  //           value:  this.selectedData()?.[f.valueField],
  //         } as QueryFilter;
  //       })
  //       .filter(
  //         (f: any) =>
  //           (!isArray(f.value) && !isNil(f.value) && f.value !== '') ||
  //           (isArray(f.value) && f.value.length),
  //       ) ?? [];
  //   return res;
  // });

  filterQuery = signal<QueryFilter[]>([]);

  filterQueryEffect = effect(
    () => {
      if (this.isLayoutWidget()) return;
      const dataFilterQuery =
        this.functionSpec()
          ?.layout_options?.historyFilterMapping?.map((f: any) => {
            return {
              field: f.field,
              operator: f.operator,
              value: this.selectedData()?.[f.valueField],
            } as QueryFilter;
          })
          .filter(
            (f: any) =>
              (!Array.isArray(f.value) &&
                !this.utilsService.isNil(f.value) &&
                f.value !== '') ||
              (Array.isArray(f.value) && f.value.length),
          ) ?? [];
      this.filterQuery.set(dataFilterQuery);
    },
    { allowSignalWrites: true },
  );
  #layoutStore = inject(LayoutStore);
  refreshHistoryData = () => {
    this.#layoutStore.setCurrentAuthAction(AuthActions.Read);
    this.refreshData.update((prev) => !prev);
  };

  refreshHistoryFilter = () => {
    this.resetFilterForm.update((prev) => !prev);
  };

  selectedItemCode = computed(() => this.selectedData()?.['code']);

  private loadHistoryData(url: string, code: string | undefined) {
    this.loading.set(true);

    return this.service
      .getHistories(
        url,
        {
          id: code,
          filter: this.filterQuery(),
        },
        this.faceCode() as string,
      )
      .pipe(
        takeUntil(this.destroy$),
        catchError((err) => {
          this.popupService.error(err.error.message);
          return of([] as Data[]);
        }),
      );
  }
  latestRequestTime = 0;

  dataEffect = effect(
    () => {
      const fs = this.functionsSpec();
      const code = this.selectedItemCode();
      const url = this.url();
      this.refreshData();

      if (!url) {
        this.data.set(
          this.metaDataService.generateMockData(fs.local_fields, fs.mock_data),
        );
        return;
      }

      const requestTime = Date.now(); // Ghi lại thời điểm gọi API
      this.latestRequestTime = Math.max(this.latestRequestTime, requestTime);

      this.loadHistoryData(url, code).subscribe(async (d) => {
        if (requestTime < this.latestRequestTime) return;
        this.loading.set(false);

        let dataHistory = d;
        const customDataHistory = this.config()?.customDataHistory;

        if (customDataHistory) {
          dataHistory = await this.utilService.transformArrayList(
            d,
            customDataHistory,
          );
        }
        this.data.set(dataHistory);
      });
    },
    { allowSignalWrites: true },
  );

  dataFilter = computed(() => {
    let dataFiltered = [];
    if (
      !Array.isArray(this.filterConfig()?.filterMapping) ||
      this.filterMethod() !== 'manual'
    ) {
      dataFiltered = this.data();
    } else {
      dataFiltered = this.filterService.filterDataByQuery(
        this.data(),
        this.filterConfig()?.filterMapping,
        this.filterValue(),
      );
    }

    return dataFiltered;
  });

  selectedIdx = signal(0);

  selectedIdxEffect = effect(
    () => {
      let selectedIdx = 0;
      const selectedId = this.selectedHistoryId();

      if (selectedId) {
        selectedIdx = this.dataFilter().findIndex((it) => it.id === selectedId);
      }

      if (selectedIdx < 0) {
        selectedIdx = 0;
      }
      this.resetFormsRendered(selectedIdx);
    },
    { allowSignalWrites: true },
  );

  reloadHistory = signal(false);
  resetEffectiveDateForm = signal(false);
  formMode = input<SourceField>();
  showArrowCollaps = input(false);
  disabledEventCollaps = input(false);
  value = computed(() => {
    if (this.groupDataByKey()) {
      return { [this.groupDataByKey()]: this.dataFilter() };
    }
    return this.dataFilter()[this.selectedIdx()];
  });
  values = computed(() => {
    return this.dataFilter()[this.selectedIdx()];
  });

  @ViewChild('effectiveDateForm') effectiveDateForm?: FormComponent;
  menuItemName = input.required<string | undefined>();

  title = computed<string>(() => {
    return `${this.menuItemName()}`;
  });

  styleFilterForm = computed(() => {
    return this.config()?.styleFilterForm;
  });

  _title = computed(async () => {
    const historyHeaderTitle = this.config()?.historyHeaderTitle;
    if (!historyHeaderTitle) return this.title();
    return await this.dynamicService.getJsonataExpression({})(
      historyHeaderTitle,
      this.selectedData(),
    );
  });

  collapseHeaderButtons: CollapseHeaderButton[] = [
    {
      id: 'edit',
      icon: 'pencil',
      title: 'edit',
    },
    {
      id: 'delete',
      icon: 'trash-can',
      title: 'delete',
    },
  ];

  onCollapseHeaderButtonClicked(id: string, index: number) {
    if (id === 'edit') {
      this.onEdit(index);
    } else if (id === 'delete') {
      this.onDelete(index);
    }
  }

  // isNil = isNil;
  footerConfig = computed(() => {
    return this.config()?.footer;
  });

  historyStatusName = computed(() => {
    return this.config()?.historyStatusName ?? 'status';
  });

  historyCloneInactive = computed(() => {
    return this.config()?.historyCloneInactive ?? false;
  });

  historyCodeName = computed(() => {
    return this.config()?.historyCodeName ?? 'code';
  });

  sidebarOptions = computed(() => {
    return this.options()?.sidebar;
  });

  contentOptions = computed(() => {
    return this.options()?.content;
  });

  sidebarHeaderOptions = computed(() => {
    return this.sidebarOptions()?.header ?? {};
  });

  widgetOptions = computed(() => {
    return this.contentOptions()?.widget ?? {};
  });

  effectiveDateValidations = computed(
    () => this.config()?.effectiveDateValidations,
  );

  setData = effect(async () => {
    // if (this.groupDataByKey()) return;
    await Promise.all(
      this.data().map(async (item, index) => {
        const historyTitle = this.config()?.historyTitle;
        const historyDescription = this.config()?.historyDescription;
        item['_historyGroupArray'] = item.histories ?? [];
        // if (historyGroup) {
        //   item['_historyGroup'] =
        //     await this.dynamicService.getJsonataExpression({})(
        //       `$.${historyGroup}`,
        //       item,
        //     );
        // }

        //   item['_historyGroupArray'] = filter(
        //     this.data(),
        //     (_item) => _item[historyGroup] === item[historyGroup],
        //   );
        // } else {
        //   item['_historyGroupArray'] = [item];
        // }

        if (!historyTitle) {
          item['_historyTitle'] = moment(item.effectiveDate).format(
            'DD/MM/YYYY',
          );
        } else
          item['_historyTitle'] =
            await this.dynamicService.getJsonataExpression({})(historyTitle, {
              ...item,
              itemIndex: index,
              _isFirtItem: index === this.data().length - 1,
            });

        if (!historyDescription) {
          item['_historyDescription'] = item[this.historyStatusName()]
            ? 'Active'
            : 'Inactive';
        } else
          item['_historyDescription'] =
            await this.dynamicService.getJsonataExpression({})(
              historyDescription,
              {
                ...item,
                itemIndex: index,
                _isFirtItem: index === this.data().length - 1,
              },
            );

        return item;
      }),
    );
  });

  @ViewChild('formObj') dynamicForm?: FormComponent;
  ngOnChanges(changes: SimpleChanges): void {
    if (changes['menuItemName']) {
      this.selectedIdx.set(0);
    }
  }

  onVisibleChange(value: boolean | undefined) {
    this.dialogVisibleChange.emit(value);
  }

  onCancel() {
    this.dialogVisible = false;
    this.dialogVisibleChange.emit(false);
  }

  onSubmit() {
    this.submitDialog.emit({
      type: this.typeDialog(),
      value: this.dynamicForm?.value,
    });
    this.onCancel();
  }

  reset = false;
  onReset() {
    this.reset = !this.reset;
  }
  selectCard(idx: number) {
    this.selectedIdx.set(idx);
    this.reloadHistory.set(!this.reloadHistory());
    this.formsRenderedByindex.update((prev) => ({ ...prev, [idx]: true }));
  }

  sortedData = computed(() => {
    const data = this.data();
    return [...data].sort(
      (a, b) =>
        moment(new Date(b.effectiveDate ?? ''))?.unix() -
        moment(new Date(a.effectiveDate ?? ''))?.unix(),
    );
  });

  sortedDates = computed(() => {
    return this.sortedData()
      .map((item) => moment(item.effectiveDate).format('yyyy-MM-DD'))
      .filter((date) => !this.utilsService.isNil(date));
  });

  formatDate(date: string | Date | undefined) {
    return moment(date).format('DD/MM/yyyy HH:mm:ss');
  }

  addItem() {
    const data = this.sortedData();
    const targetDate = moment(
      this.effectiveDateForm?.value.effectiveDate,
    ).format('yyyy-MM-DD');

    const validations = this.effectiveDateValidations() ?? ['sameDate'];
    const dates = this.sortedDates();

    // if fail validations
    if (!this.validateEffectiveDate(targetDate, dates, validations)) return;

    // if pass validations
    const prev = this.getItemByEffectiveDate(targetDate, data);
    this.cancelEffectiveDatePopup();
    // this.onCancel();
    const item = this.utilsService.cloneObject(prev);

    this.navigateEmit.emit({
      type: 'proceed',
      item: {
        ...item,
        ...this.effectiveDateForm?.value,
      },
    });

    this.resetEffectiveDateForm.update((e) => !e);
  }

  validateEffectiveDate(
    effectiveDate: string | Date,
    sortedDates: (string | Date)[],
    validations: EffectiveDateValidation[],
  ) {
    const lastestDate = moment(sortedDates[0]).format('yyyy-MM-DD');
    return validations.every((validation) => {
      switch (validation) {
        case 'sameDate': {
          return sortedDates.every((date) =>
            this.checkValidate(
              effectiveDate,
              date,
              'The effective date cannot be same as the previous record',
              this.isSameDate,
            ),
          );
        }
        case 'greaterThanLastest': {
          return this.checkValidate(
            effectiveDate,
            lastestDate,
            'The effective date must be greater than latest effective date',
            this.isLargerDate,
          );
        }
        default:
          return true;
      }
    });
  }

  checkValidate(
    date1: string | Date,
    date2: string | Date,
    errorMessage: string,
    fn: (date1: string | Date, date2: string | Date) => boolean,
  ) {
    const valid = !fn(date1, date2);

    if (valid) return true;

    this.toast.showToast('error', 'Error', errorMessage);
    return false;
  }

  isSameDate(date1: string | Date, date2: string | Date) {
    return moment(date1).diff(moment(date2), 'd', true) === 0;
  }

  isLargerDate(date1: string | Date, date2: string | Date) {
    return moment(date1).diff(moment(date2), 'd', true) < 0;
  }

  getItemByEffectiveDate(targetDate: string, data: Data[]) {
    // Filter items with effectiveDate before or equal to targetDate
    const filteredItems = data.filter((item: Data) =>
      moment(item.effectiveDate).isSameOrBefore(targetDate),
    );

    if (filteredItems.length > 0) {
      // Get item with max effectiveDate from filtered items
      return maxBy(filteredItems, (item: Data) =>
        moment(item.effectiveDate).toDate(),
      );
    }

    // If no matching items found, get item with earliest effectiveDate
    return minBy(data, (item: Data) => moment(item.effectiveDate).toDate());
  }

  generateDate(idx: number) {
    return moment(this.data()[idx]?.['effectiveDate']).format('DD/MM/YYYY');
    // const currMm = moment(new Date()).subtract(idx * 5, 'days');
    // return cloneDeep(currMm).subtract(5, 'days').format('DD/MM/YYYY');
  }

  onEdit(index?: number) {
    this.navigateEmit.emit({
      type: 'edit',
      item: this.groupDataByKey()
        ? { [this.groupDataByKey()]: this.dataFilter() }
        : index
          ? this.dataFilter()[this.selectedIdx()]?.histories?.[index]
          : this.dataFilter()[this.selectedIdx()],
    });
    // this.onCancel();
  }

  onActionClick(id: string) {
    switch (id) {
      case 'duplicate':
        this.onDuplicate();
        break;
      case 'delete':
        this.onDelete();
        break;
      case 'edit':
        this.onEdit();
        break;
      default: {
        this.navigateEmit.emit({
          type: id,
          item: this.dataFilter()[this.selectedIdx()],
          cb: (success: boolean) => {
            if (success) {
              this.refreshData.update((e) => !e);
            }
          },
        });
      }
    }
  }

  onDelete(index?: number) {
    this.navigateEmit.emit({
      type: 'delete',
      item: index
        ? this.dataFilter()[this.selectedIdx()]?.histories?.[index]
        : this.dataFilter()[this.selectedIdx()],
      cb: (success: boolean) => {
        if (success) {
          this.dataFilter().length === 1
            ? this.dialogVisibleChange.emit(false)
            : this.refreshData.update((e) => !e);
        }
      },
    });
  }

  onDuplicate() {
    this.navigateEmit.emit({
      type: 'duplicate',
      item: {
        ...this.data()[this.selectedIdx()],
        // code: null,
        // effectiveDate: new Date(),
      },
      overrideValue: {
        id: null,
        code: null,
        effectiveDate: new Date(),
      },
    });

    // this.onCancel();
  }

  // TODO: Implement the following methods to handle the effective date popup by configuring the form fields
  fields = [
    {
      type: 'dateRange',
      label: 'Effective Date',
      name: 'effectiveDate',
      mode: 'date-picker',
      setting: {
        format: 'dd/MM/yyyy',
        type: 'date',
      },

      validators: [
        {
          type: 'required',
        },
      ],
      value: new Date(),
    },
  ] as FormFieldsConfig[];
  effectiveDatePopup = false;

  utilService = inject(UtilService);

  loadingInsertNew = signal(false);

  async openEffectiveDatePopup() {
    const customTypeEmit = this.config()?.customTypeEmitInsertNew;
    if (this.isLayoutWidget()) {
      let itemToNewInsert = {
        ...(this.dataFilter()[this.selectedIdx()] ?? {}),
        effectiveDate: new Date(),
      };

      const customDataItemToInsertNew =
        this.config()?.customDataItemToInsertNew;

      if (customDataItemToInsertNew) {
        itemToNewInsert = await this.utilService.transformArrayList(
          this.dataFilter(),
          customDataItemToInsertNew,
        );
      }

      const customDataSelectedItem = this.config()?.customDataSelectedItem;

      if (customDataSelectedItem) {
        const dataOriginal = {
          selectedItem: this.dataFilter()[this.selectedIdx()],
          nextItem: this.dataFilter()[this.selectedIdx() - 1],
          prevItem: this.dataFilter()[this.selectedIdx() + 1],
          dataAll: this.dataFilter(),
        };
        itemToNewInsert = await this.utilService.transformRedirectTo(
          dataOriginal,
          customDataSelectedItem,
        );
      }

      if (customTypeEmit === 'proceed') {
        this.loadingInsertNew.set(true);
      }

      this.navigateEmit.emit({
        type: customTypeEmit ?? 'create',
        item: itemToNewInsert,
        value: customTypeEmit ? itemToNewInsert : null,
        callback: (status: boolean) => {
          this.loadingInsertNew.set(false);
        },
      });
      // prevent close the drawer
      // this.onCancel();
    } else {
      if (this.isCustomInsertNewProceed()) {
        this.navigateEmit.emit({
          type: 'insertNew',
          item: {
            ...this.dataFilter()[this.selectedIdx()],
          },
        });
        this.onCancel();
      } else if (this.skipInsertNewProceed()) {
        const itemToNewInsert = {
          ...(this.dataFilter()[this.selectedIdx()] ?? {}),
        };
        this.navigateEmit.emit({
          type: customTypeEmit ?? 'create',
          item: itemToNewInsert,
          value: itemToNewInsert,
        });
        this.onCancel();
      } else {
        this.effectiveDatePopup = true;
      }
    }
  }
  cancelEffectiveDatePopup() {
    this.resetEffectiveDateForm.update((e) => !e);
    this.effectiveDatePopup = false;
  }

  collapseTitle = computed(async () => {
    // console.log(this.values());

    if (!this.values()?.['startDate']) return this.values()?.['_historyTitle'];
    return await this.dynamicService.getJsonataExpression({})(
      `'Start date ' & $DateFormat($.startDate, 'DD/MM/YYYY')`,
      this.values(),
    );
  });

  formFilterChange(event: { value: any }) {
    this.navigateEmit.emit({
      type: 'changeFilter',
      value: event.value,
    });
    if (this.filterMethod() === 'manual') {
      this.filterValue.set(event.value);
      this.reloadHistory.set(!this.reloadHistory());
      this.resetFormsRendered();
    } else if (
      this.isLayoutWidget() &&
      event.value &&
      !this.utilsService.isEmpty(event.value)
    ) {
      const getFilterMapping =
        this.functionSpec()?.filter_config?.filterMapping;

      const res =
        getFilterMapping
          ?.map((f: any) => {
            return {
              field: f.field,
              operator: f.operator,
              value: getValue(event.value, f.valueField.split('.')),
            } as QueryFilter;
          })
          .filter(
            (f: any) =>
              (!Array.isArray(f.value) &&
                !this.utilsService.isNil(f.value) &&
                f.value !== '') ||
              (Array.isArray(f.value) && f.value.length),
          ) ?? [];

      if (this.utilsService.deepEqual(res, this.filterQuery())) return;

      this.selectedIdx.set(0);

      this.filterQuery.set(res);
      // this.filterValue.set(event.value);
    }
  }

  _actions = computed(async () => {
    let actions = this.actions() ?? LayoutHistoryComponent.DEFAULT_ACTIONS;
    actions = actions.filter((action) => this.canDoAction(action.id));
    const expressionFunc = this.dynamicService.getJsonataExpression({});
    const value = this.value();
    const displayRes = await Promise.all(
      actions.map(async (action) => {
        if (action.condition_func) {
          return expressionFunc(action.condition_func, value);
        } else {
          const actionId = action.id as keyof WidgetHeaderOptions;
          const transformKey = `_${actionId}` as keyof WidgetHeaderOptions;
          if (this.widgetHeaderOptions()?.[transformKey]) {
            return expressionFunc(
              this.widgetHeaderOptions()?.[transformKey] as string,
              value,
            );
          }
          return this.widgetHeaderOptions()?.[actionId] ?? true;
        }
      }),
    );
    return actions.filter((action, idx) => displayRes[idx]);
  });

  canDoAction(name: string) {
    const permissionKey = this.functionSpec()?.permission_key;
    const accessType = this.isCheckPermissionWithAccessType()
      ? this.dataFilter()[this.selectedIdx()]?.accessType
      : null;

    const isCheckPermissionOrigin =
      isNil(this.checkPermissionDetail) || isNil(permissionKey);

    if (
      isCheckPermissionOrigin &&
      !this.checkPermission(name, undefined, accessType)
    ) {
      return false;
    }
    if (
      !this.checkPermissionDetail?.(name, accessType) &&
      !isCheckPermissionOrigin
    ) {
      return false;
    }
    if (name === 'duplicate') {
      return (
        (this.widgetHeaderOptions()?.duplicate ?? true) &&
        !this.isLayoutWidget()
      );
    }
    return true;
  }

  formsRenderedByindex = signal<Record<number, boolean>>({ 0: true });
  resetFormsRendered(idx = 0) {
    this.selectedIdx.set(idx);
    this.formsRenderedByindex.set({ [idx]: true });
  }

  // Handle size of popup and drawer by dialogType
  size = signal<'small' | 'xsmall' | 'middle' | 'large' | 'largex'>('small');
  _size = effect(
    () => {
      const formSize = this.config()?.formSize;
      if (formSize) {
        const formSize = this.config()?.formSize;
        const getValueByDialogType = formSize ? formSize['history'] : 'middle';
        switch (getValueByDialogType) {
          case 'small':
          case 'xsmall':
          case 'middle':
          case 'large':
          case 'largex':
            this.size.set(getValueByDialogType);
            break;
          default:
            this.size.set('middle');
            break;
        }
      } else {
        this.size.set('middle');
      }
    },
    { allowSignalWrites: true },
  );

  trackByIdx(index: number): number {
    return index;
  }

  currentFormValue = computed(() => {
    return this.dataFilter()[this.selectedIdx()];
  });

  checkHasFilter = computed(
    () =>
      this.filterConfig()?.fields &&
      !this.isFilterRight() &&
      !this.isFilterInLeftSidebar(),
  );

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
