<hrdx-modal
  [isVisible]="dialogVisible()"
  [title]="title()"
  [footer]="footer"
  (canceled)="onCancel()"
  [maskClosable]="false"
  [size]="_dialogSize()"
  (changedVisible)="dialogVisibleChange.emit($event)"
  [wrapClassName]="'modal-dialog'"
>
  <div
    [ngClass]="['dialog-content']"
    *ngIf="dialogVisible"
    [class.overview]="config()?.overview || config()?.overviewGroup"
  >
    <ng-container
      [ngTemplateOutlet]="content"
      *ngIf="dialogVisible()"
    ></ng-container>
    <lib-overview
      *ngIf="config()?.overview"
      [value]="this.overViewValue"
      [variables]="this.valueDef"
      [config]="config()?.overview"
    />
    <lib-overview-group
      *ngIf="config()?.overviewGroup"
      [value]="this.overViewValueGroup"
      [variables]="this.valueDef"
      [config]="config()?.overviewGroup"
    />
  </div>
</hrdx-modal>

<ng-template #content>
  <div class="content">
    <dynamic-form
      [config]="config()?.fields ?? []"
      [sources]="config()?.sources ?? {}"
      [variables]="config()?.variables ?? {}"
      [formValue]="value()"
      [ppxClass]="'ppxm-style'"
      [readOnly]="dialogType() === 'view'"
      [extend]="_extendData()"
      [reload]="resetDialog()"
      [reset]="resetDialog()"
      [authAction]="_extendData().authAction"
      #formObj
    ></dynamic-form>
  </div>
</ng-template>

<ng-template #footer>
  <div class="dialog--footer">
    <hrdx-button
      [type]="'tertiary'"
      [title]="'Cancel'"
      [size]="'default'"
      [disabled]="isLoading()"
      (clicked)="onCancel()"
    ></hrdx-button>
    @for (btn of actionBtnFooter(); track btn.id) {
      <hrdx-button
        [type]="btn.type"
        [title]="btn.title"
        [size]="'default'"
        (clicked)="onSubmitAction(btn.id)"
        [disabled]="btnClickedId() !== btn.id && isLoading()"
        [isLoading]="btnClickedId() === btn.id && isLoading()"
      ></hrdx-button>
    } @empty {
      <hrdx-button
        [type]="'primary'"
        [title]="'Confirm'"
        [size]="'default'"
        (clicked)="onSubmitAction('submit')"
        [isLoading]="isLoading()"
      ></hrdx-button>
    }
  </div>
</ng-template>
