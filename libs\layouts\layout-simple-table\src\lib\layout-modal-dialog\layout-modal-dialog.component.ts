import { CommonModule } from '@angular/common';
import {
  Component,
  computed,
  effect,
  inject,
  input,
  model,
  output,
  signal,
  ViewChild,
} from '@angular/core';
import { FormComponent } from '@hrdx-fe/dynamic-features';
import { AuthActions, FormConfig } from '@hrdx-fe/shared';
import { ButtonComponent, ModalComponent, ModalSize } from '@hrdx/hrdx-design';
import { NzSafeAny } from 'ng-zorro-antd/core/types';
import { OverviewComponent } from '../component/overview/overview.component';
import { OverviewGroupComponent } from '../component/overviewGroup/overviewGroup.component';

export type LayoutModalExtendData = {
  authAction?: AuthActions;
  formType?: string;
}

@Component({
  selector: 'lib-layout-modal-dialog',
  standalone: true,
  imports: [
    CommonModule,
    ModalComponent,
    FormComponent,
    ButtonComponent,
    OverviewComponent,
    OverviewGroupComponent,
  ],
  providers: [ModalComponent],
  templateUrl: './layout-modal-dialog.component.html',
  styleUrl: './layout-modal-dialog.component.less',
})
export class LayoutModalDialogComponent {
  title = input<string>('');
  dialogVisible = input<boolean>(false);
  dialogVisibleChange = output<boolean>();
  config = input.required<FormConfig>();
  value = input<NzSafeAny | null | undefined>(null);
  dialogType = input.required<string>();
  dialogSize = input<ModalSize>('middle');
  isLoading = model<boolean>(false);
  modalComponent = inject(ModalComponent);
  resetDialog = input<boolean>(false);
  extendData = input<LayoutModalExtendData | null>(null);

  submitValue = output<{
    value: NzSafeAny;
    type: string;
    callback?: (success: boolean) => void;
  }>();

  @ViewChild('formObj') dynamicForm?: FormComponent;

  _extendData = computed(() => {
    const inputData = this.extendData() ?? {};
    return {
      formType: this.dialogType(),
      disabledFields: [],
      defaultValue: this.value(),
      ...inputData,
    };
  });

  reset = signal(false);
  onReset = () => {
    this.reset.set(true);
  };

  onCancel = () => {
    this.dialogVisibleChange.emit(false);
  };

  onSubmit = () => {
    this.submitValue.emit({
      value: this.dynamicForm?.value,
      type: this.dialogType(),
    });
    this.dialogVisibleChange.emit(false);
  };

  actionBtnFooter = input<NzSafeAny[]>([
    {
      id: 'run',
      label: 'Run',
      type: 'primary',
    },
  ]);

  btnClickedId = signal<string | null>(null);
  onSubmitAction = (id: string) => {
    if (!this.dynamicForm?.valid) {
      this.dynamicForm?.setFormTouched();
      return;
    }
    this.btnClickedId.set(id);
    this.isLoading.set(true);
    this.submitValue.emit({
      value: this.dynamicForm?.value,
      type: id ?? this.dialogType(),
      callback: (success: boolean) => {
        this.isLoading.set(false);
        if (!success) return;
        this.dialogVisibleChange.emit(false);
      },
    });
    // this.dialogVisibleChange.emit(false);
  };

  get disabledBtn() {
    const form = this.dynamicForm?.form;
    if (!form) return false;
    return !form.valid;
  }

  get overViewValue() {
    const dependentField = this.config()?.overview?.dependentField;
    if (dependentField !== undefined) {
      return this.dynamicForm?.value[dependentField];
    }
    return '';
  }
  get overViewValueGroup() {
    const value: NzSafeAny = [];
    this.config()?.overviewGroup?.forEach((group) => {
      const dependentField = group?.dependentField;
      if (dependentField !== undefined) {
        value.push(this.dynamicForm?.value[dependentField]);
      }
    });
    return value;
  }

  get valueDef() {
    return this.dynamicForm?.valueDef;
  }

  _dialogSize = signal<ModalSize>('middle');

  _dialogSizeEffect = effect(
    () => {
      if (this.config()?.overview) {
        this._dialogSize.set('largex');
      } else {
        this._dialogSize.set(this.dialogSize());
      }
    },
    { allowSignalWrites: true },
  );
}
