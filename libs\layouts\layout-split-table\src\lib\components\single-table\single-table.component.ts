import {
  AfterViewInit,
  Component,
  computed,
  effect,
  ElementRef,
  inject,
  input,
  model,
  OnChanges,
  OnDestroy,
  output,
  signal,
  SimpleChanges,
  viewChild,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import {
  BadgeComponent,
  ButtonComponent,
  DataRenderComponent,
  DisplayComponent,
  InputComponent,
  ModalComponent,
  NewTableComponent,
  TbodyComponent,
  TdComponent,
  ThComponent,
  TheadComponent,
  ToastMessageComponent,
} from '@hrdx/hrdx-design';
import {
  AuthActions,
  BffService,
  Data,
  debouncedSignal,
  FilterService,
  FunctionSpec,
  getValue,
  LayoutButton,
  LayoutDataService,
} from '@hrdx-fe/shared';
import { NzInputModule } from 'ng-zorro-antd/input';
import { filter, isArray, isEmpty, isEqual, isNil } from 'lodash';
import { LayoutDialogComponent } from '@hrdx-fe/layout-simple-table';
import { NzSafeAny } from 'ng-zorro-antd/core/types';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { NzSwitchModule } from 'ng-zorro-antd/switch';
import { catchError, map, of, Subject, switchMap, takeUntil, tap } from 'rxjs';
import { QueryFilter } from '@nestjsx/crud-request';
import { AdjustDisplayComponent } from 'libs/layouts/layout-dynamic/src/lib/layout-table/adjust-display/adjust-display.component';
import {
  createResizeObserverObservable,
  evaluateExpression$,
  isValidFilterValue,
  mappingConfig,
} from '../../helper';
import { FilterDialogConfig } from '../../models';
import { SearchInputConfig } from '../../const';
import { Router } from '@angular/router';

@Component({
  selector: 'lib-single-table',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    DisplayComponent,
    ButtonComponent,
    NewTableComponent,
    TheadComponent,
    ThComponent,
    TbodyComponent,
    TdComponent,
    NzInputModule,
    NzIconModule,
    NzDropDownModule,
    FormsModule,
    NzSwitchModule,
    AdjustDisplayComponent,
    InputComponent,
    BadgeComponent,
    DataRenderComponent,
    LayoutDialogComponent,
  ],
  providers: [ToastMessageComponent, ModalComponent],
  templateUrl: './single-table.component.html',
  styleUrl: './single-table.component.less',
})
export class SingleTableComponent
  implements OnChanges, AfterViewInit, OnDestroy
{
  readonly searchInputConfig = SearchInputConfig;
  filterService = inject(FilterService);
  title = input<string>('');
  data = input<NzSafeAny[]>();
  headers = input.required<NzSafeAny[]>();
  filterConfig = input<FunctionSpec['filter_config']>();
  filterDialogConfig = input<FilterDialogConfig>();
  showFilterBar = input(false);
  toolTable = input<LayoutButton[]>();
  showTableCheckbox = input<boolean>(true);
  defaultFilterQuery = input<(QueryFilter & { condition?: string })[]>();
  allowLoadData = input<boolean>(false);
  actionsMany = input<LayoutButton[]>();
  isRightTable = input(false);

  headersVisible = signal<string[]>([]);

  loading = signal(false);
  pageIndex = signal<number>(1);
  pageSize = signal<number>(25);
  listOfSelectedItems = model<Data[]>([]);

  bffService = inject(BffService);
  toast = inject(ToastMessageComponent);
  modal = inject(ModalComponent);
  layoutDataService = inject(LayoutDataService);
  url = input('');
  refreshData = input<boolean>();
  conditionRequired = signal(true);
  content = viewChild<ElementRef>('content');
  tableHeight = signal<number | null>(null);
  dataChange = output<Data[]>();
  _headers = signal<NzSafeAny[]>([]);
  height = input<number | null>(null);
  router = inject(Router);
  elementsRef = viewChild<ElementRef<HTMLElement>>('elements');
  searchKeyword = signal('');
  debouncedSearchValue = debouncedSignal(this.searchKeyword, 400);
  adjustDisplayRef = viewChild<AdjustDisplayComponent>('adjustDisplay');

  private readonly destroy$ = new Subject<void>();

  collapsedSkeletons = computed(() => new Array(20).fill(null));

  constructor() {
    effect(() => this._headers.set(structuredClone(this.headers())), {
      allowSignalWrites: true,
    });

    effect(
      () => {
        const height = this.height();
        if (!height) return;
        this.setTableHeight(height);
      },
      { allowSignalWrites: true },
    );
  }

  setTableHeight(contentHeight: number) {
    const elementsRef = this.elementsRef()?.nativeElement;
    const gap = 12;
    const adjustHeightUnit = 2;
    if (!elementsRef) return;
    this.tableHeight.set(
      contentHeight - elementsRef.offsetHeight - gap - adjustHeightUnit,
    );
  }

  ngAfterViewInit() {
    const elements = this.elementsRef()?.nativeElement;
    if (!elements) return;
    const resize$ = createResizeObserverObservable(elements).pipe(
      takeUntil(this.destroy$),
      // debounceTime(200), // Debounce to limit the number of events
      map((entries) => entries[0]?.contentRect), // Extract contentRect for size info
      // distinctUntilChanged((prev, curr) => prev?.height === curr?.height),
    );

    resize$.subscribe((contentRect) => {
      const height = this.height();
      if (!height) return;
      this.setTableHeight(height);
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next(); // Emit to signal teardown
    this.destroy$.complete(); // Complete the destroy observable
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['data']) {
      this.listOfSelectedItems.set([]);
    }

    if (
      changes['filterValue'] &&
      !isEqual(
        changes['filterValue'].currentValue,
        changes['filterValue'].previousValue,
      )
    ) {
      this.privateFilterValue.set({});
    }
  }

  total = computed(() => {
    if (this.isLoadDataFromAPI()) {
      return this.totalData() ?? 0;
    } else {
      return this.data()?.length ?? 0;
    }
  });

  dialogVisible = signal(false);

  openDialog() {
    this.dialogVisible.set(true);
  }

  closeDialog() {
    this.dialogVisible.set(false);
  }

  dataFilter = computed(() => {
    const filterMapping = this.filterConfig().filterMapping;
    if (!isArray(filterMapping)) return this.data();

    return this.filterService.filterDataByQuery(
      this.data() ?? [],
      filterMapping,
      this.privateFilterValue(),
    );
  });

  realData = computed(() => {
    if (this.isLoadDataFromAPI()) {
      return this.apiData();
    } else {
      return this.dataFilter();
    }
  });

  onToolTableClick(tool: LayoutButton) {
    switch (tool?.id) {
      case 'import': {
        this.router.navigate([tool.href ?? '']);
        this.layoutDataService.updateData({
          cm_002: tool?.paramsRedirect,
        });
        break;
      }
      case 'export': {
        this.handleExport();
        break;
      }
      default: {
        if (tool?.href) {
          this.router.navigate([tool.href]);
          break;
        }
      }
    }
  }

  handleExport = async (selectedIds?: string[]) => {
    const url = this.url();
    if (!url) return;
    this.toast.showTemplate();
    let filterQuery = await this.getFilterQuery();
    let pageIndex = 1;
    let pageSize = this.total();
    if (selectedIds && selectedIds.length > 0) {
      filterQuery = [
        ...filterQuery,
        { field: 'id', operator: '$in', value: selectedIds },
      ];
      pageIndex = 1;
      pageSize = selectedIds.length;
    }
    this.bffService
      .exportFileByGet(
        url,
        pageIndex,
        pageSize,
        filterQuery,
        this.searchValue(),
        this.sortOrder(),
        null,
        {
          authAction: AuthActions.ExportGrid,
        },
      )
      .subscribe({
        next: (data) => {
          this.toast.removeToast();
        },
        error: async (err) => {
          this.toast.removeToast();
          const errorMessage = await this.bffService.getErrorMessage(err);
          this.toast.showToast('error', 'Error', errorMessage);
        },
      });
  };

  isLoadDataFromAPI = computed(() => {
    return this.url() ? true : false;
  });

  privateFilterValue = signal<NzSafeAny>({});
  _filterValue = computed(() => ({
    ...(this.filterValue() ?? {}),
    ...(this.privateFilterValue() ?? {}),
  }));

  filterQuery = computed(() => {
    const filterValue = this._filterValue();
    const filterMapping = this.filterConfig()?.filterMapping;

    const res =
      filterMapping
        ?.map((f: any) => {
          if (!f.valueField) return null;
          return {
            field: f.field,
            operator: f.operator,
            value: getValue(filterValue, f.valueField.split('.')),
          } as QueryFilter;
        })
        .filter(
          (f: NzSafeAny) =>
            f &&
            ((!isArray(f.value) && !isNil(f.value) && f.value !== '') ||
              (isArray(f.value) && f.value.length)),
        ) ?? [];
    return res;
  });

  getDefaultFilterQuery = async (context: Record<string, any>) => {
    const filterQuery = this.defaultFilterQuery() ?? [];
    if (filterQuery.length <= 0) return [];
    const res = await Promise.all(
      filterQuery.map(async (f: any) => {
        if (!f.condition) return f;
        const conditionRes = await evaluateExpression$(f.condition, context);
        if (conditionRes) {
          return f;
        }
        return null;
      }),
    );
    return res.filter((f) => !isNil(f));
  };

  private getFilterQuery = async () => {
    const filterQuery = this.filterQuery() ?? [];
    const searchValue = this._searchValue();
    const defaultFilterQuery = await this.getDefaultFilterQuery({
      searchValue,
      filterQuery,
    });
    return [...filterQuery, ...defaultFilterQuery];
  };

  _searchValue = computed(() => {
    if (this.showFilterBar()) return this.debouncedSearchValue();
    return this.searchValue();
  });
  filterValue = input<NzSafeAny>({});
  searchValue = input('');
  apiData = signal<NzSafeAny[]>([]);
  totalData = signal(0);
  dataEffect = effect(
    async () => {
      this.refreshData();
      if (!this.allowLoadData()) return;
      const url = this.url();
      if (!url) return;
      const searchValue = this._searchValue();
      const pageIndex = this.pageIndex();
      const pageSize = this.pageSize();
      const sortOrder = this.sortOrder();
      const filterQuery = await this.getFilterQuery();

      of(url)
        .pipe(
          tap(() => this.loading.set(true)),
          switchMap((url) =>
            this.bffService.getPaginate(
              url,
              pageIndex,
              pageSize,
              filterQuery,
              searchValue,
              sortOrder,
            ),
          ),
          catchError((err) => {
            this.toast.showToast('error', 'Error', err?.error?.message ?? err);
            return of({ data: [], total: 0 });
          }),
          tap(() => this.loading.set(false)),
        )
        .subscribe((d) => {
          // in case response only have array, no data or total infos
          if (Array.isArray(d) && !d.data) {
            this.apiData.set(d);
            this.totalData.set(d.length);
            this.dataChange.emit(d);
          } else {
            this.apiData.set(d.data);
            this.totalData.set(d.total);
            this.dataChange.emit(d.data);
          }
        });
    },
    { allowSignalWrites: true },
  );

  filterOptionsLoadFromVariables = computed(() => {
    const fields = this.filterConfig()?.fields;
    if (!fields) return {};
    return fields.reduce((acc: Record<string, string>, field: NzSafeAny) => {
      const _radioTransform = field?._radio?.transform;
      if (_radioTransform) {
        acc[field.name] = _radioTransform.split('.').at(-1);
      }
      return acc;
    }, {});
  });

  layoutDialog = viewChild<LayoutDialogComponent>('layoutDialog');
  layoutDialogVariables = signal<Record<string, NzSafeAny>>({});
  updateLayoutDialogVariables() {
    if (isEmpty(this.filterOptionsLoadFromVariables())) return;
    const dialogRef = this.layoutDialog();
    if (!dialogRef) return;
    const variables = dialogRef.dynamicForm?.variablesSource;
    this.layoutDialogVariables.set(variables ?? {});
  }

  isValidFilterValue = computed(() => {
    if (!this.showFilterBar()) return false;
    const filterValue = this.privateFilterValue();
    return isValidFilterValue(filterValue);
  });

  filterConfigMapping = computed(() => {
    const fields = this.filterConfig()?.fields;
    if (!fields) return {};
    const variables = this.layoutDialogVariables();
    const variablesMapping = this.filterOptionsLoadFromVariables();
    return mappingConfig(fields, variables, variablesMapping);
  });

  removedFilterItem = (event: NzSafeAny) => {
    this.privateFilterValue.set(event);
  };

  onDialogSubmit(event: { type: string; value: any }) {
    switch (event.type) {
      case 'filter': {
        this.updateLayoutDialogVariables();
        this.privateFilterValue.set(event?.value ?? {});
        this.closeDialog();
      }
    }
  }

  filterDataRender = viewChild<DataRenderComponent>('filterDataRender');
  filterCount = computed(() => {
    const validFilterList =
      this.filterDataRender()?.validFilterList() ??
      ({} as Record<string, NzSafeAny>);
    return Object.keys(validFilterList).length;
  });

  clearSearch() {
    this.searchKeyword.set('');
    this.pageIndex.set(1);
  }

  isFiltering = computed(() => this.isValidFilterValue());

  collapsed = input(false);
  collapsedChange = output<boolean>();
  collapse() {
    this.collapsedChange.emit(!this.collapsed());
  }

  onActionsManyClick(action: string) {
    switch (action) {
      case 'export': {
        const ids = (this.listOfSelectedItems() ?? []).map((item) => item.id);
        this.handleExport(ids);
      }
    }
  }

  sortOrder = signal<Record<string, string | null>>({});
  sortOrderChange(e: string | null, key: string) {
    this.sortOrder.set({ [key]: e });
  }

  headerOrderChange(newlist: NzSafeAny) {
    this._headers.set(newlist);
    this.adjustDisplayRef()?.updateOrder(newlist);
  }
}
