<div class="split-table-wrapper">
  <ng-container
    [ngTemplateOutlet]="tableTemplate"
    [ngTemplateOutletContext]="{
      tableData: dataTable1(),
      idx: 0,
      listChecked: listCheckedTable1,
      search: searchValue(),
      filterValue: filterValue(),
      url: leftTableUrl(),
      config: config,
    }"
    *ngIf="tableConfig(0) as config"
  ></ng-container>
  <div class="control-btns" *ngIf="controlBtnsVisible()">
    @for (btn of controlBtns(); track btn.id) {
      <hrdx-button
        (clicked)="onControlBtnClick(btn.id)"
        [icon]="btn.icon"
        [onlyIcon]="true"
        [disabled]="isDisabled(btn.id)"
        type="tertiary"
      ></hrdx-button>
    }
  </div>
  <ng-container
    [ngTemplateOutlet]="tableTemplate"
    [ngTemplateOutletContext]="{
      tableData: dataTable2(),
      idx: 1,
      listChecked: listCheckedTable2,
      search: searchValue(),
      filterValue: filterValue(),
      url: rightTableUrl(),
      config: config,
    }"
    *ngIf="tableConfig(1) as config"
  ></ng-container>
</div>

<ng-template
  #tableTemplate
  let-tableData="tableData"
  let-tableIdx="idx"
  let-listChecked="listChecked"
  let-filterValue="filterValue"
  let-search="search"
  let-url="url"
  let-config="config"
>
  <div
    class="table-wrapper"
    [class.first]="tableIdx === 0"
    [class.second]="tableIdx === 1"
    [class.collapsed]="collapsedState()[tableIdx]"
  >
    <lib-single-table
      [data]="tableData"
      [headers]="headers()"
      [title]="config?.name ?? ''"
      [(listOfSelectedItems)]="listChecked!"
      [filterConfig]="filterConfig()"
      [toolTable]="getActions(config?.tool_table ?? [])"
      [actionsMany]="getActions(config?.actions_many ?? [])"
      [showTableCheckbox]="config?.show_table_checkbox ?? true"
      [url]="url ?? ''"
      [filterValue]="filterValue"
      [searchValue]="search"
      [refreshData]="refreshData()"
      [defaultFilterQuery]="config?.default_filter_query"
      [allowLoadData]="allowLoadData() ?? true"
      (dataChange)="onDataTableChange(tableIdx, $event)"
      [height]="tableHeight()"
      [showFilterBar]="config?.show_filter"
      [filterDialogConfig]="filterDialogConfig()"
      [collapsed]="collapsedState()[tableIdx]"
      [isRightTable]="tableIdx === 1"
      (collapsedChange)="onCollapsedTableChange(tableIdx, $event)"
    ></lib-single-table>
  </div>
</ng-template>
