@import '../../../../../../hrdx-design/src/themes/tokens.less';

.split-table-wrapper {
  display: flex;
  height: 100%;
  --control-btns-width: @size-80;
  --table-collapsed-width: @size-32;
  --table-default-width: calc(50% - var(--control-btns-width) / 2);
  --table-full-width: calc(100% - var(--control-btns-width) - var(--table-collapsed-width));

  .control-btns {
    width: @size-80;
    display: flex;
    gap: @size-10;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  &:not(:has(.control-btns)) {
    --table-default-width: calc(50% - var(--control-btns-width) / 8);
    --table-full-width: calc(100% - var(--control-btns-width) / 4 - var(--table-collapsed-width));
    gap: calc(var(--control-btns-width) / 4);
  }

  &:has(.table-wrapper.first.collapsed) {
    .table-wrapper.second {
      width: var(--table-full-width);
    }
  }

  &:has(.table-wrapper.second.collapsed) {
    .table-wrapper.first {
      width: var(--table-full-width);
    }
  }

  .table-wrapper {
    width: var(--table-default-width);
    transition: all 0.4s;

    &.collapsed {
      width: var(--table-collapsed-width);
    }
  }
}
