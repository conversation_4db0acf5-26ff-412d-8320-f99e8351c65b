import {
  Component,
  computed,
  effect,
  inject,
  input,
  output,
  signal,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { ButtonComponent, ToastMessageComponent } from '@hrdx/hrdx-design';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { FormsModule } from '@angular/forms';
import { BffService, Data, LayoutButton, mappingUrl, TransferTable } from '@hrdx-fe/shared';
import { DynamicFormService } from '@hrdx-fe/dynamic-features';
import { SingleTableComponent } from '../single-table/single-table.component';
import { NzSafeAny } from 'ng-zorro-antd/core/types';
import { of, switchMap } from 'rxjs';
import { cloneDeep } from 'lodash';
import { ControlBtn, FilterDialogConfig } from '../../models';

@Component({
  selector: 'lib-split-table',
  standalone: true,
  imports: [
    CommonModule,
    ButtonComponent,
    NzTableModule,
    NzDropDownModule,
    FormsModule,
    SingleTableComponent,
  ],
  providers: [ToastMessageComponent],
  templateUrl: './split-table.component.html',
  styleUrl: './split-table.component.less',
})
export class SplitTableComponent {
  headers = input.required<unknown[]>();
  filterConfig = input<unknown>();
  filterDialogConfig = input<FilterDialogConfig>();
  tablesConfig = input<TransferTable[]>([]);
  url = input<string>();
  allowLoadData = input<boolean>();
  parentData = input<NzSafeAny>();

  dynamicService = inject(DynamicFormService);
  bffService = inject(BffService);
  toast = inject(ToastMessageComponent);

  searchValue = input<string>();
  filterValue = input<NzSafeAny>();
  refresh = input(false);

  conditionRequired = input(false);
  refreshData = signal<boolean>(false);
  disabledBtns = input<string[] | null>([]);
  tableHeight = input<number | null>(null);
  checkActionPermission = input<(action: string) => boolean>();

  getActions = (btns: LayoutButton[]) => {
    const checkFn = this.checkActionPermission();
    return btns?.filter((item) => checkFn?.(item.id) ?? true);
  }

  controlBtnsVisible = computed(() => this.checkActionPermission()?.('edit') ?? true);

  controlBtns = signal<ControlBtn[]>([
    {
      id: 'moveSelectedToRight',
      title: 'Move selected to right',
      icon: 'angle-right',
      disabled: false,
    },
    {
      id: 'moveSelectedToLeft',
      title: 'Move selected to left',
      icon: 'angle-left',
      disabled: false,
    },
    {
      id: 'moveAllToRight',
      title: 'Move all to right',
      icon: 'angles-right',
      disabled: false,
    },
    {
      id: 'moveAllToLeft',
      title: 'Move all to left',
      icon: 'angles-left',
      disabled: false,
    },
  ]);

  isDisabledMoveToLeft = computed(() => {
    return this.listCheckedTable2().length <= 0;
  });

  isDisabledMoveToRight = computed(() => {
    return this.listCheckedTable1().length <= 0;
  });

  isDisabledMoveAllToRight = computed(() => {
    return this.dataTable1().length <= 0;
  });

  isDisabledMoveAllToLeft = computed(() => {
    return this.dataTable2().length <= 0;
  });

  transferFieldsTable1 = computed(() => {
    const config = this.tablesConfig();
    return config?.[0].transfer_fields;
  });

  transferFieldsTable2 = computed(() => {
    const config = this.tablesConfig();
    return config?.[1].transfer_fields;
  });

  isDisabled(buttonId: string) {
    const disabledBtns = this.disabledBtns() ?? [];
    if (disabledBtns.includes(buttonId)) return true;
    switch (buttonId) {
      case 'moveAllToRight':
        return this.isDisabledMoveAllToRight();
      case 'moveAllToLeft':
        return this.isDisabledMoveAllToLeft();
      case 'moveSelectedToRight':
        return this.isDisabledMoveToRight();
      case 'moveSelectedToLeft':
        return this.isDisabledMoveToLeft();
      default:
        return false;
    }
  }

  listCheckedTable1 = signal<Data[]>([]);
  listCheckedTable2 = signal<Data[]>([]);

  dataTable1 = signal<NzSafeAny[]>([]);
  dataTable2 = signal<NzSafeAny[]>([]);

  moveItemsEmit = output<{ type: string; items: NzSafeAny[] }>();

  expressionFunc!: (transform: string, data: unknown) => Promise<unknown>;

  constructor() {
    effect(
      () => {
        this.refresh();
        this.refreshData.update((prev) => !prev);
      },
      { allowSignalWrites: true },
    );
  }

  tableConfig(tableIdx: number) {
    return this.tablesConfig()?.[tableIdx];
  }

  leftTable = computed(() => this.tablesConfig()?.[0]);
  rightTable = computed(() => this.tablesConfig()?.[1]);

  async filterTableData(data: NzSafeAny[], transform: string) {
    if (!this.expressionFunc) {
      this.expressionFunc = await this.dynamicService.getJsonataExpression({});
    }

    const res = await Promise.all(
      data.map(async (item) => await this.expressionFunc(transform, item)),
    );

    return data.filter((_, idx) => res[idx]);
  }

  updateList(ids: string[], updateData: NzSafeAny) {
    const url = this.url();
    if (!url) return;
    of(undefined)
      .pipe(
        switchMap(() => {
          return this.bffService.updateList(url, {
            ids,
            ...cloneDeep(updateData),
          });
        }),
      )
      .subscribe({
        next: () => {
          this.toast.showToast('success', 'Success', 'Saved Successfully');
          this.refreshData.update((prev) => !prev);
        },
        error: (err) => {
          this.toast.showToast('error', 'Error', err.error?.message);
        },
      });
  }

  onControlBtnClick(type: string) {
    const items = this.getItems(type);
    if (this.url()) {
      const ids = items.map((item) => item.id);
      const updateData = this.getTransferFields(type);
      this.updateList(ids, updateData);
    } else {
      this.moveItemsEmit.emit({ type, items });
    }
  }

  getItems(type: string) {
    switch (type) {
      case 'moveSelectedToRight': {
        return this.listCheckedTable1();
      }

      case 'moveSelectedToLeft': {
        return this.listCheckedTable2();
      }

      case 'moveAllToRight': {
        return this.dataTable1();
      }

      case 'moveAllToLeft': {
        return this.dataTable2();
      }
    }
    return [];
  }

  onDataTableChange(idx: number, data: Data[]) {
    if (idx === 0) {
      this.dataTable1.set(data);
    } else {
      this.dataTable2.set(data);
    }
  }

  getTransferFields(type: string) {
    switch (type) {
      case 'moveSelectedToRight':
      case 'moveAllToRight': {
        return this.transferFieldsTable2();
      }
      case 'moveSelectedToLeft':
      case 'moveAllToLeft': {
        return this.transferFieldsTable1();
      }
    }
    return {};
  }

  leftTableUrl = computed(() => {
    return this.getUrlTableByIndex(0);
  });

  rightTableUrl = computed(() => {
    return this.getUrlTableByIndex(1);
  });

  getUrlTableByIndex(index: number) {
    const customUrl = this.tablesConfig()?.[index]?.custom_get_list_api?.url;
    if (customUrl) return mappingUrl(customUrl, this.parentData() ?? {});
    return this.url();
  }

  collapsedState = signal<boolean[]>([false, false]);

  onCollapsedTableChange(index: number, collapsed: boolean) {
    const nextTableIdx = index === 0 ? 1 : 0;
    this.collapsedState.update((prev) => {
      const prevState = [...prev];
      prevState[index] = collapsed;
      if (collapsed && prevState[nextTableIdx]) {
        prevState[nextTableIdx] = false;
      }
      return prevState;
    });
  }
}
