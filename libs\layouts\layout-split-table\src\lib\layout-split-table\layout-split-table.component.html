<nz-layout class="layout-transfer-table">
  <nz-content class="content flex-cols" #content>
    <div class="flex-cols elements" #elements>
      <ng-container
        [ngTemplateOutlet]="expandFilterTemplate"
        *ngIf="expandFilter()"
      ></ng-container>
      <div class="tool-table" *ngIf="showToolTable()">
        <div class="left">
          <hrdx-input
            [(value)]="searchValue"
            [field]="searchInputConfig.field"
            [icon]="searchInputConfig.icon"
            [placeHolder]="searchInputConfig.placeholder"
            [disabledInput]="!allowLoadData()"
            class="search-input"
            *ngIf="showTableFilter()"
          ></hrdx-input>

          <hrdx-badge [status]="4" [count]="filterCount()">
            <hrdx-button
              [type]="filterCount() > 0 ? 'secondary' : 'tertiary'"
              [onlyIcon]="true"
              icon="icon-funnel-simple-bold"
              (clicked)="filterItem()"
              [disabled]="!allowLoadData()"
              [title]="'Filters'"
            />
          </hrdx-badge>
        </div>

        <div class="right">
          @for (tool of toolTable(); track tool) {
            <hrdx-button
              [type]="tool.type ?? 'tertiary'"
              [onlyIcon]="true"
              [icon]="tool.icon"
              (clicked)="onToolTableClick(tool.id)"
              [disabled]="tool.disabled"
            >
            </hrdx-button>
          }
        </div>
      </div>
      <ng-container *ngIf="isValidFilterValue()">
        <hrdx-data-render
          [filterConfig]="filterConfigMapping()"
          [filterLst]="filterLst()"
          (removedFilterItem)="removedFilterItem($event)"
          #filterDataRender
        />
      </ng-container>
    </div>

    <div class="split-table" *ngIf="(transferTableConfig() ?? []).length >= 2">
      <lib-split-table
        [headers]="headers()"
        [tablesConfig]="transferTableConfig()!"
        [filterConfig]="filterConfig()"
        [searchValue]="debouncedSearchValue()"
        [filterValue]="filterValue()"
        [allowLoadData]="allowLoadData()"
        [url]="backendUrl()"
        [parentData]="parent()"
        [disabledBtns]="disabledTransferBtns() | async"
        [tableHeight]="leftSpace()"
        [filterDialogConfig]="filterDialogConfig()"
        [refresh]="refresh()"
        [checkActionPermission]="checkPermission"
      ></lib-split-table>
    </div>
  </nz-content>
</nz-layout>

<lib-layout-dialog
  [(dialogVisible)]="filterDialog"
  (dialogVisibleChange)="filterDialog = $event"
  [config]="filterConfig()"
  [dialogType]="'filter'"
  [value]="filterLst()"
  [title]="'Filter'"
  (submitValue)="onDialogSubmit($event)"
  [checkPermission]="checkPermission"
  [disabledActionLst]="disabledActionLst()"
  [url]="''"
  [addOnValue]="filterAddOnValue()"
  *ngIf="filterDialog"
  #layoutDialog
>
</lib-layout-dialog>

<ng-template #expandFilterTemplate>
  <div class="expand-filter-wrapper" *ngIf="expandFilter()">
    <div class="form-wrapper">
      <dynamic-form
        [config]="functionSpec().create_form?.fields ?? []"
        [sources]="functionSpec().create_form?.sources ?? {}"
        [variables]="functionSpec().create_form?.variables ?? {}"
        [formValue]="{}"
        [readOnly]="false"
        [ppxClass]="'ppxm-style'"
        #filterFormObj
      ></dynamic-form>
    </div>
    <div class="actions">
      <hrdx-button
        [type]="'primary'"
        [title]="'Search'"
        (clicked)="onFilter()"
        [disabled]="!filterFormObj?.valid"
        [size]="'default'"
      />
    </div>
  </div>
</ng-template>
