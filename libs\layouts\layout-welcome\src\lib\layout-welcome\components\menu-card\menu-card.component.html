<div class="menu-card">
  <div class="menu-card__header">
    <!-- TODO: replace with hrdx-icon when default icon is ready to use -->
    <span
      class="menu-card__header-icon"
      nz-icon
      nzType="icons:{{ getIconMenu(menu().icon) }}"
      nzTheme="outline"
      *ngIf="menu().icon"
    ></span>
    <span class="menu-card__header-title">{{ menu().title }}</span>
  </div>
  <div class="menu-card__content">
    <ul *ngIf="(submenus() ?? []).length > 0; else listEmpty">
      @for (submenu of submenus(); track $index) {
        <li *ngIf="submenu">
          <a [routerLink]="submenu.route">
            <hrdx-icon
              [name]="'angle-right'"
              [size]="18"
              [classes]="'color-icon-primary'"
            ></hrdx-icon>
            {{ submenu.title }}
          </a>
        </li>
      }
    </ul>

    <hrdx-button
      type="link"
      [title]="expanded() ? 'See less' : 'See more'"
      size="small"
      *ngIf="displayShowMore()"
      (clicked)="toggleExpanded()"
    ></hrdx-button>
  </div>
</div>

<ng-template #listEmpty>
  <div class="list-empty">
    <hrdx-illustrations
      [type]="illustrationSetting.type"
      [size]="illustrationSetting.size"
      [isSubDescription]="illustrationSetting.isSubDescription"
      [subAction]="illustrationSetting.subAction"
      [subText]="getIllustrationSubText()"
    ></hrdx-illustrations>
  </div>
</ng-template>
