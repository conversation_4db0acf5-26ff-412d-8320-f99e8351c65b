@import '../../../../../../../hrdx-design/src/themes/tokens.less';

.version-card {
  border: 1px solid @color-border-secondary;
  border-radius: @size-12;


  &__header {
    background-color: @color-bg-surface-hover;
    padding: @size-12;
    border-radius: @size-8;
    display: flex;
    text-overflow: ellipsis;
    gap: @size-8;


    &-title {
      font-weight: @font-weight-semibold;
      color: @color-text-primary;
      font-size: @font-size-medium;
      line-height: @font-line-height-medium;
    }
  }

  &__content {
    transition: height 0.3s ease-in-out;
    padding: 0 @spacing-2;

    .version-card__content-version {
      color: @color-text-primary;
      font-size: @font-size-base;
      font-weight: @font-weight-regular;
      display: flex;
      margin-bottom: 0;
      padding-top: @spacing-2;
    }
  }
}