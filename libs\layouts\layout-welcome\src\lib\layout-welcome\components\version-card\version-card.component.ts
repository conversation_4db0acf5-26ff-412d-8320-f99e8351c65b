import { Component, input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { RouterModule } from '@angular/router';

@Component({
  selector: 'lib-version-card',
  standalone: true,
  imports: [CommonModule, NzIconModule, RouterModule],
  templateUrl: './version-card.component.html',
  styleUrl: './version-card.component.less',
})
export class VersionCardComponent {
  data = input.required<any>();
}
