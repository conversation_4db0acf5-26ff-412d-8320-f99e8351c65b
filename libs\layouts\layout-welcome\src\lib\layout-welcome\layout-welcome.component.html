<nz-layout class="layout-welcome">
  <section class="page-banner-wrapper">
    <lib-page-banner
      [title]="bannerTitle"
      [descriptions]="bannerDescriptions"
    ></lib-page-banner>
  </section>

  <nz-content>
    <lib-module-card [name]="moduleName()">
      <div class="menus">
        @for (menu of menus(); track menu.id) {
          @if (menu.children && menu.children !== undefined) {
            <lib-menu-card
              [maxSubmenusDisplay]="5"
              [menu]="menu"
            ></lib-menu-card>
          }
        }
      </div>
    </lib-module-card>
    <div *ngIf="moduleId() === 'ADC'" class="version-info">
      <lib-module-card [name]="'Version Info'">
        <div class="menus">
          @for (item of versionList(); track item.moduleId) {
            <lib-version-card [data]="item"></lib-version-card>
          }
        </div>
      </lib-module-card>
    </div>
  </nz-content>
</nz-layout>
