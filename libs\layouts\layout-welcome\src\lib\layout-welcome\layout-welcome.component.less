@import '../../../../../hrdx-design/src/themes/tokens.less';

.layout-welcome {
  height: 100%;
  display: flex;
  flex-direction: column;
  width: 100%;
  overflow-y: auto;
  background-color: @color-background;
  padding: @spacing-5;
  gap: @spacing-5;

  nz-content {
    .menus {
      display: flex;
      gap: @size-16;
      flex-wrap: wrap;
      margin-top: @size-16;

      lib-menu-card {
        width: @size-1 * 283;
      }

      lib-version-card {
        width: @size-1 * 283;
      }

      // height: 90%;
      // overflow-y: auto;
      // height: fit-content;
    }

    .version-info {
      margin-top: @spacing-5;
      margin-bottom: @spacing-5;

      .version-item {
        width: 50%;

      }
    }
  }


}