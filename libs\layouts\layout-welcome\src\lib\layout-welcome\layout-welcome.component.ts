import { CommonModule } from '@angular/common';
import { Component, computed, effect, inject, signal } from '@angular/core';
import {
  BffService,
  LayoutStore,
  ModuleStore,
  UserStore,
} from '@hrdx-fe/shared';
import { NzLayoutModule } from 'ng-zorro-antd/layout';
import { MenuCardComponent } from './components/menu-card/menu-card.component';
import { ModuleCardComponent } from './components/module-card/module-card.component';
import { PageBannerComponent } from './components/page-banner/page-banner.component';
import { catchError, firstValueFrom, of, tap } from 'rxjs';
import { VersionCardComponent } from './components/version-card/version-card.component';

@Component({
  selector: 'lib-layout-welcome',
  standalone: true,
  imports: [
    CommonModule,
    NzLayoutModule,
    PageBannerComponent,
    ModuleCardComponent,
    MenuCardComponent,
    VersionCardComponent,
  ],
  templateUrl: './layout-welcome.component.html',
  styleUrl: './layout-welcome.component.less',
})
export class LayoutWelcomeComponent {
  #layoutStore = inject(LayoutStore);
  #moduleStore = inject(ModuleStore);
  #userStore = inject(UserStore);

  bannerTitle = 'Hi, Nguyen 👋';
  bannerDescriptions =
    "Welcome to the Admin Dashboard. Let's get started managing your site. Find insights that improve employee recruitment, satisfaction and retention.";

  menus = this.#layoutStore.menus;
  moduleId = this.#layoutStore.currentModuleId;
  modules = this.#moduleStore.modules;

  service = inject(BffService);

  constructor() {
    // effect to update banner title
    effect(() => {
      const fullName = this.#userStore.user()?.fullName;
      const userName = this.#userStore.user()?.userName;
      this.updateBannerTitle(fullName ?? userName ?? '');
    });
  }

  updateBannerTitle(name: string) {
    this.bannerTitle = `Hi, ${name} 👋`;
  }

  moduleName = computed(() => {
    const subText = 'Main Features';
    const moduleId = this.moduleId();
    const module = this.modules()?.find((module) => module.id === moduleId);
    return (module?.name ?? moduleId) + ' ' + subText;
  });

  getVersion(url: string) {
    //FIXME - change if api ignore auth functionCode
    return fetch(url)
      .then((res) => res.text())
      .catch(() => null);
    // return firstValueFrom(
    //   this.service
    //     .Get<string | null>(url, undefined, { responseType: 'text' })
    //     .pipe(catchError(() => of(null))),
    // );
  }

  versionList = signal<
    {
      moduleId: string;
      version: string | null;
      name: string;
    }[]
  >([]);

  versionListEffect = effect(async () => {
    if (this.moduleId() === 'ADC') {
      const listUrl = [
        {
          url: '/api/pr-version',
          moduleId: 'PR',
          name: 'CorePR',
        },
        {
          url: '/api/hrm-version',
          moduleId: 'HR',
          name: 'CoreHR',
        },
        {
          url: '/api/ts-version',
          moduleId: 'TS',
          name: 'TimeSheets',
        },
        {
          url: '/api/com-version',
          moduleId: 'ADC',
          name: 'Common',
        },
        {
          url: '/api/report-version',
          moduleId: 'GE',
          name: 'Reporting',
        },
        {
          url: '/api/ins-version',
          moduleId: 'INS',
          name: 'Insurance',
        },
      ];

      const results = await Promise.all(
        listUrl.map(async (item) => {
          const version = await this.getVersion(item.url);
          return {
            moduleId: item.moduleId,
            name: item.name,
            version,
          };
        }),
      );

      this.versionList.set(results);
    }
  });
}
