<!-- <hrdx-container>
  <div [ngClass]="['widgets-layout', layoutColumns >= 2 ? 'columns-2' : '']"> -->
@if (widgetsTmp(); as widget) {
  <ng-container
    [ngTemplateOutlet]="widgetContent"
    [ngTemplateOutletContext]="{
      widget: widget,
    }"
  ></ng-container>
}

<ng-template #widgetContent let-widget="widget">
  <div class="layout-widget">
    <!-- <hrdx-widget
      [type]="widget.type"
      [title]="widget.title"
      [column]="widget.column ?? 1"
      (headerButtonClicked)="pageHeaderButtonClicked($event)"
      [icon]="widget.icon"
      [headerButtons]="widget.headerButtons"
    > -->
    <div
      class="filter-container"
      *ngIf="isHasFilterInline() && !isEmptyContentOrigin()"
    >
      <dynamic-form
        [config]="functionSpec().filter_config?.fields ?? []"
        [sources]="functionSpec().filter_config?.sources ?? {}"
        [variables]="functionSpec().filter_config?.variables ?? {}"
        [formValue]="filterValue()"
        [extend]="{ params: params(), refresh: resetFilterForm() }"
        [readOnly]="false"
        [ppxClass]="'ppxm-style'"
        (valueChanges)="onFilterChange($event)"
        [reload]="reloadFilterForm()"
        [isNewDynamicForm]="isNewDynamicForm()"
        [faceCode]="faceCode()"
        #filterFormObj
      ></dynamic-form>
    </div>
    @switch (widget.type) {
      @case ('descriptions') {
        @if (loading()) {
          <div class="skeleton">
            <nz-skeleton [nzActive]="true"></nz-skeleton>
            <nz-skeleton [nzActive]="true"></nz-skeleton>
          </div>
        } @else if (!isEmptyContent()) {
          <div class="main-content" #content>
            @switch (widgetContentType()) {
              @case ('readonly-form') {
                <dynamic-form
                  [config]="functionSpec()?.form_config?.fields ?? []"
                  [sources]="{}"
                  [variables]="{}"
                  [formValue]="data()"
                  [extend]="{
                    formType: 'viewMore',
                  }"
                  [readOnly]="true"
                  [ppxClass]="'ppxm-style'"
                  #readonlyFormObj
                  *ngIf="data()"
                ></dynamic-form>
              }
              @case ('table-custom') {
                <ng-container
                  [ngTemplateOutlet]="tableCustom"
                  [ngTemplateOutletContext]="{
                    data: tableDataFiltered(),
                    headers: tableHeaders(),
                  }"
                ></ng-container>
              }
              @default {
                <hrdx-descriptions
                  [fields]="data() ?? undefined"
                  [titles]="widget.headers"
                  [columns]="widget.column ?? 1"
                ></hrdx-descriptions>
              }
            }
          </div>
        } @else {
          <hrdx-illustrations
            class="layout-widget-illustrations-custom"
            [size]="IllustrationsSize.Small"
            [type]="IllustrationsType.Empty"
            [subAction]="false"
            [subTextAction]="
              (widgetEmptyOptions()?.show_add_information ?? true) &&
              checkPermission('create') &&
              isEmptyContentOrigin()
            "
            (subTextClicked)="
              widgetEmptyOptions()?.is_proceed ? proceedItem() : createItem()
            "
            [subText]="getSubText(widget.title)"
          ></hrdx-illustrations>
        }
      }
      @case ('table') {
        <div class="tool-table" *ngIf="showTableFilter() && !expandFilter()">
          <div style="width: 300px">
            <nz-input-group [nzPrefix]="suffixIconSearch">
              <input type="text" nz-input placeholder="Search" />
            </nz-input-group>
            <ng-template #suffixIconSearch>
              <span nz-icon nzType="icons:magnifying-glass"></span>
            </ng-template>
          </div>
          <hrdx-button
            [title]="'Filter'"
            (clicked)="filterItem()"
            [isLeftIcon]="true"
            [leftIcon]="'filter'"
          />
        </div>
        <div class="expand-filter" *ngIf="expandFilter()">
          <ng-container
            [ngTemplateOutlet]="expandFitlerTemplate"
          ></ng-container>
        </div>

        <div class="table-wrapper">
          <hrdx-table
            [dataSource]="widget.data || []"
            [columns]="headerTable()"
            [pagination]="false"
            (viewItem)="viewItem($event)"
          ></hrdx-table>
        </div>
      }
      @default {
        <nz-empty></nz-empty>
      }
    }
    <!-- </hrdx-widget> -->
  </div>
</ng-template>

<ng-template #expandFitlerTemplate>
  <dynamic-form
    [config]="functionSpec().filter_config?.fields ?? []"
    [sources]="{}"
    [variables]="{}"
    [formValue]="{}"
    [readOnly]="false"
    [ppxClass]="'ppxm-style'"
    [reload]="resetExpandFilterForm()"
    [faceCode]="faceCode()"
    #filterFormObj
  ></dynamic-form>
  <div class="actions">
    <hrdx-button
      [type]="'secondary'"
      [title]="'Clear'"
      (clicked)="onResetExpandFilterForm()"
    />
    <hrdx-button
      [type]="'primary'"
      [title]="'Search'"
      (clicked)="getFilterLst(filterFormObj)"
    />
  </div>
</ng-template>

<!-- action create/edit -->
<lib-layout-dialog
  [dialogVisible]="dialogVisible()"
  (dialogVisibleChange)="dialogVisible.set($event)"
  [config]="functionSpec().form_config"
  [dialogType]="dialogType()"
  [loadAfterOpen]="dialogType() !== 'view'"
  [url]="''"
  [params]="customParams()"
  [noNeedConfirm]="noNeedConfirm()"
  [value]="selectedItem()"
  [title]="
    dialogType() !== 'view'
      ? capitalize(dialogType()) + ' ' + functionSpec().title
      : functionSpec().title
  "
  [showSaveAddButton]="showDialogFormSaveAddBtn()"
  (submitValue)="dialogSubmit($event)"
  [maskClosable]="false"
  [checkPermission]="checkPermission"
  [disabledActionLst]="disabledActionLst()"
  [dataLayout]="dataLayout()"
  [showAvatarInfo]="dialogType() !== 'view'"
  *ngIf="dialogVisible()"
  (canceled)="onCancelDialog()"
  [showDialogFooter]="dialogType() === 'view'"
  [centered]="true"
  [isNewDynamicForm]="isNewDynamicForm()"
  [hyperlinkInForm]="hyperlinkInForm()"
  (hyperlinkClicked)="onHyperlinkClicked($event)"
  [faceCode]="faceCode()"
  [childrenActionPermission]="childrenActions()"
  [permissionForForm]="permissionForForm()"
  #layoutDialog
>
</lib-layout-dialog>
<!-- action view -->
<lib-layout-dialog
  [dialogVisible]="dialogVisibleView()"
  (dialogVisibleChange)="dialogVisibleView.set($event)"
  [config]="functionSpec().form_config"
  [dialogType]="'view'"
  [loadAfterOpen]="dialogType() !== 'view'"
  [url]="null"
  [params]="customParams()"
  [noNeedConfirm]="noNeedConfirm()"
  [value]="selectedItem()"
  [title]="
    dialogType() !== 'view'
      ? capitalize(dialogType()) + ' ' + functionSpec().title
      : functionSpec().title
  "
  [showSaveAddButton]="showDialogFormSaveAddBtn()"
  (submitValue)="dialogSubmit($event)"
  [maskClosable]="false"
  [checkPermission]="checkPermission"
  [disabledActionLst]="disabledActionLst()"
  [dataLayout]="dataLayout()"
  [showAvatarInfo]="dialogType() !== 'view'"
  *ngIf="dialogVisibleView()"
  (canceled)="onCancelDialog()"
  [showDialogFooter]="dialogType() === 'view'"
  [centered]="true"
  [isNewDynamicForm]="isNewDynamicForm()"
  [faceCode]="faceCode()"
  [childrenActionPermission]="childrenActions()"
  [permissionForForm]="permissionForForm()"
  [loadingFooterAction]="loadingDelete()"
  #detailDialog
>
</lib-layout-dialog>

<!-- proceed form -->

<lib-layout-dialog
  [(dialogVisible)]="dialogVisibleProceed"
  (dialogVisibleChange)="dialogVisibleProceed.set($event)"
  [config]="formConfig()"
  [dialogType]="'proceed'"
  [params]="customParams()"
  [value]="selectedItem()"
  [title]="
    dialogType() !== 'view'
      ? capitalize(dialogType()) + ' ' + functionSpec().title
      : functionSpec().title
  "
  (submitValue)="navigateItem($event)"
  [showSaveAddButton]="false"
  [url]="''"
  [checkPermission]="checkPermission"
  [disabledActionLst]="disabledActionLst()"
  *ngIf="dialogVisibleProceed()"
  [footerButtonsCustom]="proceedFooterButtonsCustom"
  [dataLayout]="dataLayout()"
  [childrenActionPermission]="childrenActions()"
  [showAvatarInfo]="false"
  [noNeedConfirm]="true"
  [centered]="true"
  [faceCode]="faceCode()"
  [permissionForForm]="permissionForForm()"
></lib-layout-dialog>

<lib-layout-history
  [(dialogVisible)]="dialogInfoVisible"
  (dialogVisibleChange)="dialogInfoVisible = $event"
  [config]="functionSpec()?.form_config"
  [menuItemName]="functionSpec()?.title"
  [cancelBtn]="false"
  [url]="url()"
  [data]="[data()]"
  [selectedData]="selectedItem()"
  (navigateEmit)="navigateItem($event, 'history')"
  [showInsertNewRecord]="showHistoryInsertBtn()"
  [showSearchBar]="showHistorySearchBar()"
  [isCollapsContainer]="isCollapsContainer()"
  [showArrowCollaps]="showArrowCollaps()"
  [disabledEventCollaps]="disabledEventCollaps()"
  [widgetHeaderOptions]="historyWidgetHeaderOptions()"
  [defaultFilterValue]="defaultFilterValue() ?? {}"
  [selectedHistoryId]="selectedId()"
  [filterConfig]="functionSpec().filter_config"
  [filterMethod]="historyFilterMethod()"
  [options]="historyDialogOptions()"
  [functionSpec]="functionSpec()"
  [checkPermission]="checkPermission"
  [disabledActionLst]="disabledActionLst()"
  [params]="customParams()"
  [isLayoutWidget]="true"
  [dataLayout]="dataLayout()"
  [showAvatarInfo]="true"
  [skipInsertNewProceed]="true"
  [widgetHistoryType]="widgetHistoryType()"
  [groupDataByKey]="groupDataHistoryByKey() ?? ''"
  [showNavigateToContactBtn]="showNavigateToContactBtn()"
  [isNewDynamicForm]="isNewDynamicForm()"
  [faceCode]="faceCode()"
  [permissionForForm]="permissionForForm()"
  [isCheckPermissionWithAccessType]="isCheckPermissionWithAccessType()"
  [loadingFooterAction]="loadingDelete()"
  #layoutHistory
  *ngIf="dialogInfoVisible"
></lib-layout-history>

<ng-template #contentDeletedItem let-ref="modalRef">
  <span
    >Are you sure you want to delete this record ? All the data associated with
    it will be deleted</span
  >
  <div class="modal-footer-action">
    <hrdx-button
      [type]="'tertiary'"
      [title]="'Cancel'"
      (clicked)="ref.destroy()"
    />
    <hrdx-button
      [type]="'primary'"
      [destruction]="true"
      (clicked)="handleOK(); ref.destroy()"
      [title]="'Confirm'"
    />
  </div>
</ng-template>

<ng-template #tableCustom let-data="data" let-headers="headers">
  <div class="table">
    @for (item of data; track $index) {
      <div class="tr">
        @for (column of headers; track $index) {
          <div class="td">
            <hrdx-display
              [type]="column?.display_type?.key || 'Label'"
              [value]="item[column.code]"
              [title]="column.title"
              [href]="column.href"
            ></hrdx-display>
          </div>
        }
      </div>
    }
  </div>
</ng-template>

<!-- xử lý view show more cho màn hình Jobdata -->
<hrdx-drawer
  *ngIf="showMoreContentType() === 'widget-drawer' && viewShowMore()"
  [title]="functionSpec().title"
  [visible]="viewShowMore()"
  [footer]="
    handleCheckPermission('delete') || handleCheckPermission('edit')
      ? drawerFooter
      : undefined
  "
  (visibleChanged)="$event ? viewShowMore.set(true) : viewShowMore.set(false)"
  [wrapClassName]="'dialog-drawer-widget'"
  (closed)="viewShowMore.set(false)"
  [width]="functionSpec().form_config?.formSize?.['view'] ?? 'large'"
  [paddingLess]="true"
>
  <div class="layout-widget view-more-wraper">
    <div
      class="filter-container"
      *ngIf="
        functionSpec()?.filter_config && filterType() === 'widget' && data()
      "
    >
      <dynamic-form
        [config]="functionSpec().filter_config?.fields ?? []"
        [sources]="functionSpec().filter_config?.sources ?? {}"
        [variables]="functionSpec().filter_config?.variables ?? {}"
        [formValue]="filterValue()"
        [extend]="{
          formType: 'filter',
          params: params(),
          refresh: resetFilterForm(),
        }"
        [readOnly]="false"
        [ppxClass]="'ppxm-style'"
        (valueChanges)="onFilterChange($event, true)"
        [isNewDynamicForm]="isNewDynamicForm()"
        [faceCode]="faceCode()"
        [authAction]="AuthActions.Read"
        #filterFormObjView
      ></dynamic-form>
    </div>
    @if (loading()) {
      <div class="skeleton">
        <nz-skeleton [nzActive]="true"></nz-skeleton>
        <nz-skeleton [nzActive]="true"></nz-skeleton>
      </div>
    } @else {
      <div class="main-content view-more" #content>
        <dynamic-form
          [config]="functionSpec()?.form_config?.fields ?? []"
          [sources]="{}"
          [variables]="{}"
          [extend]="{
            formType: 'viewMore',
            permission: permissionForForm(),
            defaultValue: data(),
          }"
          [formValue]="data()"
          [readOnly]="true"
          [ppxClass]="'ppxm-style'"
          [isNewDynamicForm]="isNewDynamicForm()"
          [faceCode]="faceCode()"
          [authAction]="AuthActions.Read"
          #readonlyFormObj
          *ngIf="data()"
        ></dynamic-form>
        <div
          class="footer-content"
          [ngClass]="{ 'is-navigation_contract': showNavigateToContactBtn() }"
        >
          <hrdx-button
            [type]="'link'"
            [title]="'Contract Data'"
            [size]="'default'"
            [isLeftIcon]="true"
            [leftIcon]="'icon-arrow-bend-down-left-bold'"
            (clicked)="onNavigateToContract(true)"
            *ngIf="showNavigateToContactBtn()"
          />
          <lib-last-updated
            [updatedBy]="data()?.updatedBy"
            [updatedAt]="data()?.updatedAt"
          />
        </div>
      </div>
    }
  </div>
</hrdx-drawer>

<ng-template #drawerFooter>
  <div class="dialog--footer">
    <hrdx-button
      [type]="'ghost-color'"
      [size]="'default'"
      *ngIf="loadingDelete()"
      [isLoading]="true"
    />
    <hrdx-button
      type="secondary"
      [title]="'Delete'"
      [size]="'default'"
      (clicked)="deleteClickOne(data()?.id || '', data())"
      *ngIf="handleCheckPermission('delete')"
      [disabled]="loadingDelete()"
    ></hrdx-button>
    <hrdx-button
      type="primary"
      [title]="'Edit'"
      [size]="'default'"
      (clicked)="editItem(data())"
      *ngIf="handleCheckPermission('edit')"
      [disabled]="loadingDelete()"
    ></hrdx-button>
  </div>
</ng-template>
