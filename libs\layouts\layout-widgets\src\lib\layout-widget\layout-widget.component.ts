import { CommonModule } from '@angular/common';
import {
  Component,
  computed,
  effect,
  inject,
  OnD<PERSON>roy,
  OnInit,
  signal,
  TemplateRef,
  viewChild,
  ViewChild,
} from '@angular/core';
import {
  ButtonComponent,
  DescriptionsComponent,
  DisplayComponent,
  DrawerComponent,
  IllustrationsComponent,
  IllustrationsSize,
  IllustrationsType,
  ModalComponent,
  ModalFooterButtons,
  TableComponent,
  ToastMessageComponent,
  WidgetType,
} from '@hrdx/hrdx-design';
// todo: just for demo fix later

import { Router, Routes } from '@angular/router';
import {
  DynamicFormService,
  FormComponent,
  Source,
  SourceField,
} from '@hrdx-fe/dynamic-features';
// eslint-disable-next-line @nx/enforce-module-boundaries
import {
  LayoutDialogComponent,
  LayoutHistoryComponent,
} from '@hrdx-fe/layout-simple-table';
// eslint-disable-next-line @nx/enforce-module-boundaries
import {
  ApiConfig,
  AuthActions,
  BasicInfoFunctionCode,
  BffService,
  Data,
  debouncedSignal,
  FunctionSpec,
  JobdataFunctionCode,
  JobdataPermissionCodes,
  LayoutCommon,
  LayoutCommonComponent,
  LayoutStore,
  mappingUrl,
  MasterdataService,
  OIR_MENU_IDS,
  PrecheckDeleteApiConfig,
  UtilService,
} from '@hrdx-fe/shared';
import { QueryFilter } from '@nestjsx/crud-request';
import { capitalize, every, isArray, isEmpty, isEqual, isNil } from 'lodash';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzSafeAny } from 'ng-zorro-antd/core/types';
import { NzEmptyModule } from 'ng-zorro-antd/empty';
import { NzFlexModule } from 'ng-zorro-antd/flex';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzModalModule, NzModalService } from 'ng-zorro-antd/modal';
import { NzSkeletonModule } from 'ng-zorro-antd/skeleton';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import {
  catchError,
  firstValueFrom,
  of,
  Subscription,
  switchMap,
  tap,
} from 'rxjs';
import { LastUpdatedComponent } from '../component/last-updated/last-updated.component';

type FormAction = {
  title: string;
  type?: string;
  content?: string;
  onConfirm?: FormAction;
  link?: string;
  _valueData?: SourceField;
};

export type DialogType =
  | 'create'
  | 'edit'
  | 'proceed'
  | 'proceedCustom'
  | 'filter'
  | 'view'
  | 'duplicate'
  | 'viewSchedule';

type HandleProceedConfigType = {
  proceedRedirect?: {
    _enabledRedirect?: string;
    _redirectTo?: string;
    _paramsRedirect?: string;
    redirectTo?: string;
  };
  receiveResponse: {
    type?: 'action' | 'open-form' | 'request';
    _type?: SourceField;
    actionConfig?: FormAction;
    _valueData?: SourceField;
    sourceRequest?: Source;
  };
};

@Component({
  selector: 'lib-layout-widget',
  standalone: true,
  imports: [
    CommonModule,
    NzCardModule,
    NzFlexModule,
    NzButtonModule,
    NzIconModule,
    NzInputModule,
    NzEmptyModule,
    DescriptionsComponent,
    FormComponent,
    ButtonComponent,
    IllustrationsComponent,
    TableComponent,
    NzModalModule,
    NzSpinModule,
    DisplayComponent,
    NzSkeletonModule,
    LayoutDialogComponent,
    LayoutHistoryComponent,
    DrawerComponent,
    LastUpdatedComponent,
  ],
  providers: [ModalComponent, ToastMessageComponent],
  templateUrl: './layout-widget.component.html',
  styleUrl: './layout-widget.component.less',
})
export class LayoutWidgetComponent
  extends LayoutCommonComponent
  implements LayoutCommon, OnInit, OnDestroy
{
  metaDataService = inject(MasterdataService);
  _service = inject(BffService);
  IllustrationsType = IllustrationsType;
  IllustrationsSize = IllustrationsSize;

  modalComponent = inject(ModalComponent);

  @ViewChild('contentDeletedItem') contentDeletedItem!: TemplateRef<''>;

  constructor() {
    super();
    effect(
      () => {
        if (this.isHasFilterInline()) {
          this.childData()?.set(this.dataOriginFoFilterInline());
        } else {
          const data = this.isGetList() ? this.tableData() : this.data();
          this.childData()?.set(data);
        }
      },
      { allowSignalWrites: true },
    );
  }

  AuthActions = AuthActions;
  dialogActionVisible = false;
  dialogInfoVisible = false;
  metadataService = inject(MasterdataService);
  effectFunctionSpec = effect(
    () => {
      // TODO: set function spec to store
      return;
    },
    { allowSignalWrites: true },
  );
  layoutColumns = 2;
  data = signal<Data | null>(null);
  tableData = signal<Data[] | null>(null);
  tableDataFiltered = signal<Data[] | null>(null);
  loading = signal<boolean>(false);
  refreshData = signal(false);
  layoutHistory = viewChild(LayoutHistoryComponent);
  layoutDetail = viewChild(LayoutDialogComponent);
  detailDialog = viewChild<LayoutDialogComponent>('detailDialog');
  layoutDialog = viewChild<LayoutDialogComponent>('layoutDialog');

  filterQuery = signal<QueryFilter[]>([]);

  debouncedFilterQuery = debouncedSignal(this.filterQuery, 500);

  private layoutDataSubscription!: Subscription;
  private subscriptionLayoutEvent!: Subscription;

  ngOnInit() {
    this.subscriptionLayoutEvent =
      this.layoutDataService.layoutEventEmiter$.subscribe(
        async (event: NzSafeAny) => {
          const conditionRefreshConfig =
            this.widgetOptions()?.condition_refresh_config;
          let checkConditionRefresh = true;
          if (conditionRefreshConfig) {
            checkConditionRefresh = await this.transformValue(
              conditionRefreshConfig,
              { currentData: this.data(), receiptData: event.extraData },
            );
          }
          if (
            event.key === 'refreshData' &&
            event.value === this.functionSpec().id &&
            checkConditionRefresh
          ) {
            this.refreshData.update((e) => !e);
            this.resetFilterForm.update((prev) => !prev);
            this.reloadFilterForm.update((prev) => !prev);
          }
        },
      );

    this.layoutDataSubscription = this.layoutDataService
      .getDataSubject()
      .subscribe((data) => {
        if (this.functionSpec()?.id) {
          const filterFromData = data[`${this.functionSpec().id}`];
          if (filterFromData) {
            this.onFilterChange({ value: filterFromData }, true);
            this.resetFilterForm.update((prev) => !prev);
          }
        }
      });
  }

  ngOnDestroy() {
    if (this.layoutDataSubscription) {
      this.layoutDataSubscription.unsubscribe();
    }

    if (this.subscriptionLayoutEvent) {
      this.subscriptionLayoutEvent.unsubscribe();
    }
  }

  filterDataTableCustom = computed(() => {
    return this.functionSpec()?.layout_options?.filter_data_tableCustom ?? '';
  });

  noNeedConfirm = computed(() => {
    return this.functionSpec()?.layout_options?.no_need_confirm ?? false;
  });

  historyFilterMethod = computed(
    () => this.layoutOptions()?.filter_history_method ?? 'api',
  );

  historyDialogOptions = computed(() => {
    return this.layoutOptions()?.history_dialog_options ?? {};
  });

  showHistorySearchBar = computed(() => {
    return (
      this.functionSpec()?.layout_options?.show_history_search_bar ?? false
    );
  });

  showArrowCollaps = computed(() => {
    return (
      this.functionSpec().layout_options?.collaps_options?.show_arrow_collaps ??
      false
    );
  });
  disabledEventCollaps = computed(() => {
    return (
      this.functionSpec().layout_options?.collaps_options
        ?.disabled_event_collaps ?? false
    );
  });
  historyWidgetHeaderOptions = computed(() => {
    return this.layoutOptions()?.history_widget_header_options;
  });

  customValueBeforeEdit = computed(() => {
    return this.layoutOptions()?.custom_value_before_edit;
  });

  isCheckPermissionWithAccessType = computed(() => {
    return this.layoutOptions()?.is_check_permission_with_accessType ?? false;
  });

  tableDataEffect = effect(
    async () => {
      const filterConditionTransform = this.filterDataTableCustom();
      if (filterConditionTransform) {
        const data = this.tableData() as Data[];
        const dataFilter = await this.utilService.transformArrayList(
          data,
          filterConditionTransform,
        );
        this.tableDataFiltered.set(dataFilter);
      } else {
        this.tableDataFiltered.set(this.tableData());
      }
    },
    { allowSignalWrites: true },
  );

  customDataTransform = computed(() => {
    return this.widgetOptions()?.custom_data_transform;
  });

  hyperlinkInForm = computed(() => {
    return this.widgetOptions()?.hyperlinkInForm;
  });

  isHasFilterInline = computed(() => {
    return this.functionSpec()?.filter_config && this.filterType() === 'widget';
  });

  dataOriginFoFilterInline = signal<Data | null>(null);

  latestRequestTime = 0;

  dataEffect = effect(
    () => {
      this.refreshData(); // don't remove this line
      const fs = this.functionSpec();
      const url = this.url();
      if (!url)
        return this.data.set(
          this.metaDataService.generateMockData(
            fs.local_fields,
            fs.mock_data,
          )[0],
        );
      else {
        let requestTime = 0;
        // this.layoutDetail()?.changeLoadingDialog(true);
        of(url)
          .pipe(
            tap(() => this.loading.set(true)),
            tap(() => {
              requestTime = Date.now(); // Ghi lại thời điểm gọi API
              this.latestRequestTime = Math.max(
                this.latestRequestTime,
                requestTime,
              );
            }),
            switchMap((url) => {
              if (this.isGetList() || this.customDataTransform()) {
                return this._service.getList(
                  url,
                  undefined,
                  undefined,
                  undefined,
                  this.faceCode() as string,
                );
              } else {
                return this._service.getObject(
                  url,
                  this.filterQuery(),
                  this.faceCode() as string,
                );
              }
            }),

            catchError((err) => {
              if (!this.isHasFilterInline()) {
                this.toast.showToast(
                  'error',
                  'Error',
                  err?.error?.message ?? err,
                );
              }

              this.loading.set(false);
              return of(null);
            }),
          )
          .subscribe(async (d) => {
            if (requestTime < this.latestRequestTime) return;

            if (this.isGetList() && isArray(d)) {
              this.tableData.set(d);
            } else {
              if (this.customDataTransform()) {
                const data = d as Data[];
                const dataTransform = await this.utilService.transformArrayList(
                  data,
                  this.customDataTransform(),
                );
                this.data.set(dataTransform as Data);
              } else {
                this.data.set(d as Data);
              }
            }
            this.loading.set(false);
            if (this.dialogVisibleView()) {
              this.selectedItem.set(this.data());
              this.detailDialog()?.refreshData();
            }
            // this.data.set(d as Data);
          });

        if (this.isHasFilterInline()) {
          of(url)
            .pipe(
              switchMap((url) => {
                return this._service.getObject(
                  url,
                  undefined,
                  this.faceCode() as string,
                );
              }),

              catchError((err) => {
                this.toast.showToast(
                  'error',
                  'Error',
                  err?.error?.message ?? err,
                );
                return of(null);
              }),
            )
            .subscribe(async (d) => {
              if (d) {
                this.dataOriginFoFilterInline.set(d as Data);
              } else {
                this.dataOriginFoFilterInline.set(null);
                this.filterValue.set(null);
              }
            });
        }
      }
    },
    { allowSignalWrites: true },
  );

  layoutDetailEffect = effect(
    () => {
      this.layoutDetail()?.changeLoadingDialog(this.loading());
    },
    { allowSignalWrites: true },
  );

  dataWidget = computed(() => {
    const groupDataConfig = this.functionSpec()?.form_config?.historyGroup;

    // if (groupDataConfig) return uniqBy(this.data(), groupDataConfig);
    return this.data();
  });

  showHistoryCancelButton = computed(() => {
    return (
      this.functionSpec()?.layout_options?.show_history_cancel_button ?? false
    );
  });

  tableHeaders = computed(() => this.functionSpec()?.local_fields);
  headers = computed(() => {
    const fields = this.functionSpec()?.local_fields ?? [];
    const groups: Record<string, typeof fields> = {};
    fields.forEach((field) => {
      if (field.group) {
        if (!groups[field.group]) {
          groups[field.group] = [];
        }
        groups[field.group].push(field);
      }
    });
    const newFields = fields
      .map((field) => {
        if (Object.keys(groups).includes(field.code)) {
          return { ...field, children: groups[field.code] };
        }
        if (field.group) return null;
        return field;
      })
      .filter((field) => field);
    return newFields;
  });

  // config for new dynamic form
  isNewDynamicForm = computed(() => {
    const layoutOptions = this.functionSpec()?.layout_options;
    return (layoutOptions as any)?.is_new_dynamic_form ?? false;
  });

  transformData(fields: FunctionSpec['local_fields']) {
    return (
      fields?.map((item) => ({
        key: item.code,
        title: item.title,
        fixed: item.pinned,
        type: item.display_type.key,
        dataIndex: item.code,
        width: item.options?.tabular?.column_width
          ? item.options.tabular.column_width + 'rem'
          : 'fit-content',
      })) || []
    );
  }

  headerTable = computed(() => {
    const fields = this.functionSpec()?.local_fields ?? [];
    let columns = [];
    columns = this.transformData(fields) as [];
    return columns;
  });

  widgetIcon = computed(() => {
    return this.functionSpec()?.layout_options?.icon ?? '';
  });

  widgetHistoryType = computed(() => {
    return this.functionSpec()?.layout_options?.widget_history_type || '';
  });

  widgetHeaderButtons = computed(() => {
    return this.functionSpec()?.layout_options?.widget_header_buttons ?? [];
  });

  filterType = computed(() => {
    return this.functionSpec()?.layout_options?.filterType ?? '';
  });

  customParams = computed(() => {
    return {
      ...this.params(),
      ...this.filterValue(),
      data: this.data(),
    };
  });

  showDialogFormSaveAddBtn = computed(() => {
    return (
      this.functionSpec()?.layout_options?.show_dialog_form_save_add_button ??
      false
    );
  });

  formMode = computed(() => {
    return this.functionSpec()?.form_config?._mode;
  });

  showHistoryInsertBtn = computed(() => {
    return (
      this.functionSpec()?.layout_options?.show_history_insert_button ?? true
    );
  });

  showNavigateToContactBtn = computed(() => {
    return (
      this.functionSpec()?.layout_options?.show_navigate_to_contact_btn ?? false
    );
  });

  layoutOptions = computed(() => this.functionSpec().layout_options);

  customMessageOnSubmit = computed(() => {
    return this.layoutOptions()?.custom_message_on_submit;
  });

  widgetContentType = computed(() => {
    return this.layoutOptions()?.widget_content_type ?? 'descriptions';
  });

  widgetOptions = computed(() => {
    return this.layoutOptions()?.widget_options ?? {};
  });

  widgetEmptyOptions = computed(() => {
    return this.widgetOptions()?.empty;
  });

  showMoreContentType = computed(() => {
    return this.widgetOptions()?.show_more_content_type ?? 'dialog';
  });

  isCollapsContainer = computed(() => {
    return (
      this.functionSpec().layout_options?.collaps_options
        ?.history_collaps_container ?? false
    );
  });

  isUpdateArray = computed(() => {
    return this.layoutOptions()?.is_update_array ?? false;
  });

  isGetList = computed(() => {
    return this.widgetContentType() === 'table-custom';
  });

  // use when need to group data by key in layout history
  groupDataHistoryByKey = computed(() => {
    return this.layoutOptions()?.group_data_history_by_key;
  });

  isCopyDataInsertNew = computed(() => {
    return this.layoutOptions()?.is_copy_data_insert_new ?? true;
  });

  capitalize = capitalize;

  dynamicService = inject(DynamicFormService);
  widgetsTmp = signal<NzSafeAny>({});
  widgetsTmpEffect = effect(
    async () => {
      const data = this.dataWidget();
      const headers = this.headers();

      const newHeaders = await Promise.all(
        headers.map(async (d) => {
          const expressionFunc = this.dynamicService.getJsonataExpression({});
          const condition = await expressionFunc(
            d?.extra_config?.['condition'] || '',
            data,
          );
          return { ...d, condition };
        }),
      );

      const headersFiltered = newHeaders.filter((d) => d.condition !== false);

      // const title = this.menuItemName()[this.menuItemName().length - 1] || '';
      const title = this.functionSpec().title || '';
      const icon = this.widgetIcon();
      const headerButtons = this.widgetHeaderButtons().filter((btn) => {
        if (btn.id === 'history' && !data) return false;
        return true;
      });
      const filterType = this.filterType();
      const column = this.functionSpec()?.layout_options?.n_cols ?? 1;

      const isArrayData = this.functionSpec()?.layout === 'layout-widget-table';

      const res = {
        title,
        headers: headersFiltered.map((d) => d),
        column,
        icon,
        headerButtons,
        filterType,
        type: isArrayData ? WidgetType.Table : WidgetType.Descriptions,
        data: isArrayData ? data : [data],
      };
      this.widgetsTmp.set(res);
    },
    {
      allowSignalWrites: true,
    },
  );

  modalService = inject(NzModalService);
  toast = inject(ToastMessageComponent);
  router = inject(Router);

  selectedItem = signal<any>({});
  prevItem = signal<any>({});
  selectedId = computed(() => {
    // return this.data()?.id;
    // console.log('id', this.selectedItem()?.id);

    return this.selectedItem()?.id;
  });

  refreshDataWidget(type?: string) {
    if (this.widgetOptions()?.refreshHeader) {
      //refresh data select ERN for layout Employee profile
      this.layoutDataService.layoutEventEmit({
        key: 'refreshERN',
      });
    }

    //refresh data for block Employment Seniority and Contract
    if (this.widgetOptions()?.refeshDependent) {
      this.widgetOptions()?.refeshDependent?.forEach((item) => {
        this.layoutDataService.layoutEventEmit({
          key: 'refreshData',
          value: item,
        });
      });
    }

    const refreshProfileIfDelete =
      this.widgetOptions()?.refreshProfileIfDelete && type === 'delete';

    //refresh employee profile data
    if (this.widgetOptions()?.refreshProfile || refreshProfileIfDelete) {
      this.layoutDataService.layoutEventEmit({
        key: 'refreshProfile',
      });
    }
  }

  async transformValue(transform: string, value: Record<string, NzSafeAny>) {
    return await this.dynamicService.getJsonataExpression({})(transform, value);
  }

  async handleSubRequest(submitData: NzSafeAny, responseData?: NzSafeAny) {
    const requestAfterSubmitResponse =
      this.functionSpec().form_config?.requestAfterSubmitResponse;
    if (requestAfterSubmitResponse) {
      const sourcesMapping = requestAfterSubmitResponse.sourcesMapping ?? [];
      const sources = requestAfterSubmitResponse.sources ?? {};

      const dynamicForm = this.layoutDialog()?.dynamicForm;

      const results = await Promise.all(
        sourcesMapping.map(async (item: NzSafeAny) => {
          const sourceData = sources[item.key];
          const enabled = await this.transformValue(item.enabled, {
            variables: dynamicForm?.variablesSource,
            formType: this.dialogType(),
            formData: submitData,
            params: this.params(),
          });
          return new Promise((resolve) => {
            if (sourceData && enabled) {
              this.dynamicService
                .getValueFromApi(
                  sourceData,
                  {
                    ...submitData,
                    _params: this.params(),
                    _responseData: responseData,
                    _formType: this.dialogType(),
                  },
                  undefined,
                  this.faceCode(),
                )
                .subscribe({
                  next: (val: NzSafeAny) => {
                    resolve(val);
                  },
                  error: (err) => {
                    resolve(err);
                  },
                });
            } else {
              resolve(null);
            }
          });
        }),
      );

      results.forEach((dataItem: NzSafeAny) => {
        if (dataItem?.messageResult?.length > 0) {
          dataItem?.messageResult.forEach((item: NzSafeAny) => {
            if (item.success === 'Y') {
              this.toast.showToast('success', '', item.message);
            } else {
              this.modalComponent.showDialog(
                {
                  nzTitle: 'Warning',
                  nzContent: item.message,
                  nzWrapClassName: 'popup popup-confirm',
                  nzIconType: 'icons:warning',
                  nzOkText: 'OK',
                  nzCancelText: null,
                  nzWidth: '400px',
                  nzClosable: false,
                  nzCentered: true,
                  nzClassName: 'popup-got-it',
                  nzMaskStyle: {
                    backgroundColor: 'transparent',
                  },
                },
                'warning',
              );
            }
          });
        } else if (dataItem?.error) {
          this.toast.showToast('error', 'Error', dataItem?.error?.message);
        }
      });
    }
  }

  isObjectDeepEmpty = (obj: Record<string, NzSafeAny>) => {
    // Kiểm tra nếu object rỗng hoặc tất cả các giá trị của nó là `undefined`
    return isEmpty(obj) || every(obj, (value) => isNil(value));
  };

  async handleShowMessageOnSubmit(
    res: NzSafeAny,
    type: 'create' | 'update',
    responseType: 'error' | 'success',
  ) {
    const customMessageOnSubmitConfig = this.customMessageOnSubmit();
    let message =
      customMessageOnSubmitConfig?.[type]?.[responseType]?.message ??
      res?.message ??
      'Record saved';

    if (customMessageOnSubmitConfig?.[type]?.[responseType]?._message) {
      const newMessage = await this.transformValue(
        customMessageOnSubmitConfig?.[type]?.[responseType]?._message,
        {
          variables: this.layoutDialog()?.dynamicForm?.variablesSource,
          formType: this.dialogType(),
          formData: this.data(),
          params: this.params(),
        },
      );
      if (newMessage) {
        message = newMessage;
      }
    }

    let customMessageOnSubmitType =
      customMessageOnSubmitConfig?.[type]?.[responseType]?.type;

    if (customMessageOnSubmitConfig?.[type]?.[responseType]?._type) {
      const newType = await this.transformValue(
        customMessageOnSubmitConfig?.[type]?.[responseType]?._type,
        {
          variables: this.layoutDialog()?.dynamicForm?.variablesSource,
          formType: this.dialogType(),
          formData: this.data(),
          params: this.params(),
        },
      );
      if (newType) {
        customMessageOnSubmitType = newType;
      }
    }

    if (customMessageOnSubmitType === 'dialog') {
      this.modalComponent.showDialog(
        {
          nzTitle: 'Record saved',
          nzContent: message,
          nzWrapClassName: 'popup popup-confirm',
          nzOkText: 'OK',
          nzCancelText: null,
          nzWidth: '400px',
          nzClosable: false,
          nzCentered: true,
          nzClassName: 'popup-got-it',
        },
        responseType,
      );
    } else {
      this.toast.showToast(responseType, '', message);
    }
  }

  async dialogSubmit(e: {
    type: string;
    value: NzSafeAny;
    callback?: (success: boolean, error?: NzSafeAny) => void;
  }) {
    const url = this.url();

    let value = e.value;
    const form_value_transform =
      this.functionSpec().form_config?.form_value_transform;
    if (form_value_transform) {
      value = await this.utilService.transformRedirectTo(
        e.value,
        form_value_transform,
      );
    }

    const requestAfterSubmitResponse =
      this.functionSpec().form_config?.requestAfterSubmitResponse;

    switch (e.type) {
      case 'create':
      case 'saveAndAddNew':
        if (this.isObjectDeepEmpty({ ...value })) {
          this.toast.showToast('warning', '', 'The record is empty');
          e.callback?.(true);
        } else if (url) {
          of(undefined)
            .pipe(
              switchMap(() => {
                if (this.functionSpec().layout_options?.is_upload_file)
                  return this._service.createFormData(
                    url,
                    value,
                    this.faceCode() as string,
                  );
                return this._service.createItem(
                  url,
                  value,
                  this.faceCode() as string,
                );
              }),
            )
            .subscribe({
              next: async (val: NzSafeAny) => {
                if (requestAfterSubmitResponse) {
                  await this.handleSubRequest(value, val);
                }

                this.handleShowMessageOnSubmit(val, 'create', 'success');

                if (this.dialogInfoVisible) {
                  this.layoutHistory()?.refreshHistoryData();
                  this.layoutHistory()?.refreshHistoryFilter();
                } else if (
                  val?.id &&
                  this.startFlow() === 'history' &&
                  e.type === 'create'
                ) {
                  this.openDetailInfo({
                    ...value,
                    ...val,
                    id: this.selectedId(),
                  });
                }
                this.refreshData.update((e) => !e);
                this.resetFilterForm.update((prev) => !prev);

                e.callback?.(true, val);
                this.refreshDataWidget();
              },
              error: (err) => {
                // const message =
                //   this.customMessageOnSubmit()?.create?.error?.message ??
                //   err.error?.message;

                // this.toast.showToast('error', 'Error', message);
                this.handleShowMessageOnSubmit(err.error, 'create', 'error');
                e.callback?.(false, err.error);
              },
            });
        } else {
          this.toast.showToast('success', '', 'Record saved');
          e.callback?.(true);
        }
        break;
      case 'edit': {
        if (this.isObjectDeepEmpty({ ...value })) {
          this.toast.showToast('warning', '', 'The record is empty');
          e.callback?.(true);
        } else if (url) {
          of(undefined)
            .pipe(
              switchMap(() => {
                if (this.functionSpec().layout_options?.is_upload_file) {
                  return this._service.updateFormData(
                    url,
                    this.selectedId(),
                    value,
                    this.faceCode() as string,
                  );
                  // TODO: use only for custom case (email and phone), should find a better way
                } else if (this.isUpdateArray() && this.prevItem()) {
                  const prevValue = this.prevItem()?.['commands'] || [];
                  const currentValue = value?.['commands'] || [];
                  const deletedItems = prevValue.filter(
                    (item: NzSafeAny) =>
                      currentValue.find(
                        (item2: NzSafeAny) => item2.id === item.id,
                      ) === undefined,
                  );
                  const formData = {
                    commands: [
                      ...deletedItems.map((item: NzSafeAny) => ({
                        ...item,
                        isDeleted: true,
                      })),
                      ...currentValue.map((item: NzSafeAny) => ({
                        ...item,
                        isDeleted: false,
                      })),
                    ],
                  };
                  return this._service.update(
                    url,
                    formData,
                    undefined,
                    this.faceCode() as string,
                  );
                } else {
                  return this._service.updateItem(
                    url,
                    this.selectedId(),
                    value,
                    undefined,
                    this.faceCode() as string,
                  );
                }
              }),
            )
            .subscribe({
              next: async (val: NzSafeAny) => {
                if (requestAfterSubmitResponse) {
                  await this.handleSubRequest(value, val);
                }

                this.handleShowMessageOnSubmit(val, 'update', 'success');

                if (this.dialogInfoVisible) {
                  this.layoutHistory()?.refreshHistoryData();
                  this.layoutHistory()?.refreshHistoryFilter();
                } else if (this.startFlow() === 'history') {
                  this.openDetailInfo({
                    ...value,
                    ...val,
                    id: this.selectedId(),
                  });
                }

                this.refreshData.update((e) => !e);
                this.resetFilterForm.update((prev) => !prev);

                this.refreshDataWidget();

                e.callback?.(true, val);
              },
              error: (err) => {
                this.handleShowMessageOnSubmit(err.error, 'update', 'error');
                e.callback?.(false, err.error);
              },
            });
        } else {
          this.toast.showToast('success', '', 'Record saved');
          e.callback?.(true);
        }
        break;
      }

      case 'toEdit': {
        this.dialogType.set('edit');
        this.openDialog();
        break;
      }
      case 'toDelete': {
        const id = this.selectedId();
        if (id) {
          this.deleteClickOne(e.value.id, undefined, e.callback);
        }
        break;
      }
    }
  }

  dialogVisible = signal(false);
  dialogVisibleView = signal(false);
  dialogVisibleProceed = signal(false);
  openDialog() {
    this.dialogVisible.set(true);
  }
  openDialogProceed() {
    this.dialogVisibleProceed.set(true);
  }
  openDialogView() {
    this.dialogVisibleView.set(true);
  }

  proceedFooterButtonsCustom: ModalFooterButtons[] = [
    { id: 'cancel', title: 'Cancel', type: 'tertiary' },
    { id: 'save', title: 'Proceed', type: 'primary' },
  ];

  afterCloseDialogFormOpenHistory = false;
  dialogType = signal<'view' | 'edit' | 'create' | 'proceed'>('create');

  #layoutStore = inject(LayoutStore);

  navigateItem(
    e: {
      type:
        | DialogType
        | 'toDelete'
        | 'toEdit'
        | 'saveAndAddNew'
        | 'toSubmit'
        | 'delete'
        | 'changeFilter'
        | 'toDuplicate'
        | 'navigateToContract';
      item?: any;
      value?: any;
      callback?: (success: boolean) => void;
      cb?: (success: boolean) => void;
    },
    type?: string,
  ) {
    // console.log(e, 'event');
    if (type === 'history') {
      this.startFlow.set('history');
    }
    switch (e.type) {
      case 'create':
        this.createItem(this.isCopyDataInsertNew() ? e.item : undefined);
        break;
      case 'edit':
        this.editItem(e.item);
        break;
      case 'delete':
        this.deleteClickOne(e.item.id, e.item, e.callback || e.cb);
        break;
      case 'proceed':
        this.onProceed(e.value, e.callback);
        break;
      case 'changeFilter':
        this.onChangeFilterHistory(e.value);
        break;
      case 'navigateToContract':
        this.onNavigateToContract(false, e.callback);
        break;
    }
  }

  saveFilterHistory = signal<Record<string, unknown> | null>(null);
  onChangeFilterHistory(value: any) {
    this.saveFilterHistory.set(value);
  }

  precheckDeleteApi = computed(() => this.layoutOptions()?.precheck_delete_api);

  getApiValueByKey<K extends keyof ApiConfig>(
    config: ApiConfig,
    key: K,
    extendValue: Record<string, NzSafeAny>,
  ) {
    const transformKey = `_${key}` as K;
    const transformObj = config[transformKey] as { transform: string };
    if (transformObj?.transform)
      return this.transformValue(transformObj.transform, extendValue);
    return config[key];
  }

  getCustomUrl(config: ApiConfig, extendValue: Record<string, NzSafeAny>) {
    return this.getApiValueByKey(config, 'url', extendValue);
  }

  async precheckDelete(
    config: PrecheckDeleteApiConfig,
    value: Record<string, NzSafeAny>,
  ) {
    if (config?.source) {
      return firstValueFrom(
        this.dynamicService
          .getValueFromApi(config?.source, value, undefined, this.faceCode())
          .pipe(
            catchError((err) => {
              this.toast.showToast('error', 'Error', err.error?.message);
              return of(null);
            }),
          ),
      );
    } else {
      const precheckApiUrl = await this.getCustomUrl(config, value);
      if (!precheckApiUrl) return null;
      return firstValueFrom(
        this._service.Get(precheckApiUrl).pipe(
          catchError((err) => {
            this.toast.showToast('error', 'Error', err.error?.message);
            return of(null);
          }),
        ),
      );
    }
  }

  showMessageDeleteError(
    message: string,
    status: number,
    cb?: (status: boolean) => void,
  ) {
    const serverErrorList = [
      500, 501, 502, 503, 504, 505, 506, 507, 508, 510, 511,
    ];
    if (!serverErrorList.includes(status)) {
      this.modalComponent.showDialog(
        {
          nzTitle: 'Cannot Delete',
          nzContent: message,
          nzWrapClassName: 'popup popup-confirm',
          nzIconType: 'icons:warning',
          nzOkText: 'Got it',
          nzCancelText: null,
          nzWidth: '400px',
          nzClosable: false,
          nzCentered: true,
          nzClassName: 'popup-got-it',
          nzOnOk: () => {
            cb?.(false);
          },
        },
        'warning',
      );
    } else {
      this.toast.showToast('error', 'Error', message);
      cb?.(false);
    }
  }

  loadingDelete = signal(false);

  async deleteClickOne(
    id: string,
    value?: NzSafeAny,
    callback?: (success: boolean) => void,
  ) {
    const url = this.url();
    let title = 'Delete record?';
    let content =
      'This will permanently delete the record and all associated data. Delete anyway?';

    // for precheck delete
    const precheckDeleteConfig = this.precheckDeleteApi();
    if (precheckDeleteConfig) {
      // this.#layoutStore.setLoading(true);
      this.loadingDelete.set(true);
      const precheckRes = await this.precheckDelete(
        precheckDeleteConfig,
        value,
      );
      // this.#layoutStore.setLoading(false);
      this.loadingDelete.set(false);

      if (isNil(precheckRes)) {
        callback?.(false);
        return;
      }

      const confirmConfig = precheckDeleteConfig.confirm;
      const extendValue = { precheckRes, record: value };
      if (confirmConfig) {
        const newTitle = confirmConfig?._title
          ? await this.transformValue(
              confirmConfig?._title?.transform,
              extendValue,
            )
          : confirmConfig.title;
        const newContent = confirmConfig?._content
          ? await this.transformValue(
              confirmConfig?._content?.transform,
              extendValue,
            )
          : confirmConfig.content;

        if (!isNil(newTitle)) title = newTitle;
        if (!isNil(newContent)) content = newContent;
      }
    }

    this.modalComponent.showDialog({
      nzTitle: title,
      nzContent: content,
      nzWrapClassName: 'popup popup-error',
      nzIconType: 'icons:trash-bold',
      nzOkText: 'Delete',
      nzCancelText: 'Cancel',
      nzCentered: true,
      nzOnOk: () => {
        if (url) {
          this.#layoutStore.setLoading(true);
          this._service
            .deleteItem(url, id, undefined, this.faceCode() as string)
            .subscribe({
              next: () => {
                this.#layoutStore.setLoading(false);
                this.refreshData();
                this.toast.showToast('success', '', 'Record deleted');
                this.refreshData.update((e) => !e);
                this.resetFilterForm.update((prev) => !prev);
                this.startFlow.set(null);
                this.dialogVisible.set(false);
                this.selectedItem.set(null);
                this.viewShowMore.set(false);
                this.layoutHistory()?.refreshHistoryData();
                this.layoutHistory()?.refreshHistoryFilter();
                this.refreshDataWidget('delete');
                callback?.(true);
                return;
              },
              error: (err) => {
                this.#layoutStore.setLoading(false);
                this.showMessageDeleteError(
                  err.error?.message,
                  err.status,
                  callback,
                );
                // this.toast.showToast('error', 'Error', err.error?.message);
                this.startFlow.set(null);
                // callback?.(false);
              },
            });
        } else {
          callback?.(false);
          this.toast.showToast('success', '', 'Record deleted');
        }
      },
    });

    return;
  }

  formConfig = computed(() => {
    if (this.dialogType() === 'proceed') {
      if (
        this.functionSpec()?.create_form &&
        !isEmpty(this.functionSpec()?.create_form)
      ) {
        return this.functionSpec()?.create_form;
      }
    }
    return this.functionSpec()?.form_config;
  });

  checkValues(
    valueData: { [key: string]: boolean | string },
    triggerFields: any[],
  ): boolean {
    for (const trigger of triggerFields) {
      if (valueData[trigger.name] !== trigger.value) {
        return false;
      }
    }
    return true;
  }

  utilService = inject(UtilService);

  handleProceedConfig = computed(
    () =>
      (this.functionSpec()?.create_form?.handleProceedConfig ??
        {}) as HandleProceedConfigType,
  );

  async onProceed(value: any, callback?: (success: boolean) => void) {
    const handleProceedConfig = this.handleProceedConfig();
    const proceedRedirect = handleProceedConfig.proceedRedirect;
    let enabledRedirect;

    if (proceedRedirect) {
      enabledRedirect = await this.utilService.transformRedirectTo(
        value,
        proceedRedirect._enabledRedirect,
      );
    }

    if (enabledRedirect) {
      callback?.(true);
      if (proceedRedirect?._redirectTo) {
        const redirectTo = await this.utilService.transformRedirectTo(
          value,
          proceedRedirect?._redirectTo,
        );
        const paramsRedirect = await this.utilService.transformRedirectTo(
          value,
          proceedRedirect?._paramsRedirect,
        );
        let flag = true;
        (redirectTo as string)
          .split('/')
          .filter((path) => path)
          .reduce((acc: Routes | undefined, path) => {
            if (!flag) return undefined;
            if (!path) {
              flag = false;
              return undefined;
            }
            const route = acc?.find((r) => r.path === path);
            if (route) {
              if (route.children) {
                return route.children;
              } else {
                return undefined;
              }
            } else {
              flag = false;
              return undefined;
            }
          }, this.router.config);
        if (!flag) {
          this.router.navigate(['/no-permission']);
          return;
        }
        if (redirectTo) {
          this.router.navigate([redirectTo]);
          this.layoutDataService.updateData({
            ...paramsRedirect,
          });
          return;
        }
      }

      if (proceedRedirect?.redirectTo) {
        this.router.navigate([proceedRedirect.redirectTo]);
        return;
      }
    } else {
      let backendUrl = this.functionSpec()?.create_form?.backendUrl;
      if (backendUrl) {
        backendUrl = mappingUrl(backendUrl, value);

        let filterObject = this.functionSpec()?.create_form?.filterObject;
        if (filterObject) {
          filterObject = await this.utilService.transformRedirectTo(
            value,
            filterObject,
          );
        }

        this.handleProceed(backendUrl, filterObject, value, callback);
      } else {
        callback?.(true);
        this.dialogVisibleProceed.set(false);
        this.createItem(value);
      }
    }
  }

  handleProceed(
    backendUrl: string,
    filterObject?: NzSafeAny,
    value?: NzSafeAny,
    callback?: (success: boolean) => void,
  ) {
    const handleProceedConfig = this.handleProceedConfig();
    const receiveResponse = handleProceedConfig.receiveResponse;

    this._service
      .getObject(backendUrl, filterObject, this.faceCode() as string)
      .subscribe({
        next: async (data: any) => {
          if (data) {
            let receiveResponseType = receiveResponse.type;
            if (receiveResponse._type) {
              receiveResponseType = await this.utilService.transformRedirectTo(
                {
                  responseData: data,
                  formData: value,
                  selectedData: this.data(),
                },
                receiveResponse._type.transform,
              );
            }

            switch (receiveResponseType) {
              case 'action': {
                callback?.(true);
                const action = receiveResponse.actionConfig;
                if (action) {
                  this.doAction(action, value, data);
                }

                break;
              }
              case 'open-form': {
                let newData = { ...value, ...(data ?? {}) };
                if (receiveResponse._valueData) {
                  newData = await this.utilService.transformRedirectTo(
                    {
                      responseData: data,
                      formData: value,
                      selectedData: this.data(),
                    },
                    receiveResponse._valueData.transform,
                  );
                }
                callback?.(true);
                this.dialogVisibleProceed.set(false);
                this.createItem(newData);
                break;
              }
              case 'request': {
                const sourceData = receiveResponse.sourceRequest;
                if (sourceData) {
                  this.dynamicService
                    .getValueFromApi(
                      sourceData,
                      {
                        responseData: data,
                        formData: value,
                        selectedData: this.data(),
                        _params: this.params(),
                        _formType: this.dialogType(),
                      },
                      undefined,
                      this.faceCode(),
                    )
                    .subscribe({
                      next: async (val: NzSafeAny) => {
                        let newData = { ...value, ...(data ?? {}) };
                        if (receiveResponse._valueData) {
                          newData = await this.utilService.transformRedirectTo(
                            {
                              responseData: data,
                              formData: value,
                              selectedData: this.data(),
                              currentResponseData: val,
                            },
                            receiveResponse._valueData.transform,
                          );
                        }
                        callback?.(true);
                        this.dialogVisibleProceed.set(false);
                        this.createItem(newData);
                      },
                      error: (err) => {
                        callback?.(false);
                        this.toast.showToast(
                          'error',
                          'Error',
                          err.error?.message,
                        );
                      },
                    });
                } else {
                  const newData = { ...value, ...(data ?? {}) };
                  callback?.(true);
                  this.dialogVisibleProceed.set(false);
                  this.createItem(newData);
                }

                break;
              }
              default: {
                const newData = { ...value, ...(data ?? {}) };
                callback?.(true);
                this.dialogVisibleProceed.set(false);
                this.createItem(newData);
                break;
              }
            }
            // console.log('data proceed', data);
          } else {
            callback?.(true);
            this.dialogVisibleProceed.set(false);
            this.createItem();
          }
        },
        error: (err: { error: { message: string } }) => {
          callback?.(false);
          this.toast.showToast('error', 'Error', err.error?.message);
        },
      });
  }

  doAction(action: FormAction, formValue: any, responseData?: any) {
    this.modalComponent.showDialog({
      nzTitle: action?.title ?? '',
      nzContent: action?.content ?? '',
      nzWrapClassName: 'popup popup-confirm',
      nzIconType: 'icons:warning',
      nzOkText: 'Confirm',
      nzCancelText: 'Cancel',
      nzOnOk: async () => {
        if (action.onConfirm?.type === 'navigate') {
          const valueDataConfig = action.onConfirm._valueData;
          const valueData = await this.utilService.transformRedirectTo(
            { responseData, formData: formValue, selectedData: this.data() },
            valueDataConfig?.transform,
          );
          this.layoutDataService.updateData(valueData);
          this.router.navigate([
            mappingUrl(action.onConfirm?.link ?? '', formValue),
          ]);
        }
      },
      nzOnCancel: () => {
        this.onCancelDialog();
      },
    });
  }
  createItem(value?: any, e?: Event) {
    e?.stopPropagation();
    this.selectedItem.set(value ?? undefined);
    this.dialogType.set('create');
    this.openDialog();
  }
  proceedItem(e?: Event) {
    e?.stopPropagation();

    this.selectedItem.set(undefined);
    this.dialogType.set('proceed');
    this.openDialogProceed();
  }
  async editItem(item: any, e?: Event) {
    e?.stopPropagation();
    if (this.customValueBeforeEdit()) {
      const newItem = await this.utilService.transformRedirectTo(
        item,
        this.customValueBeforeEdit(),
      );
      this.selectedItem.set(newItem);
    } else {
      this.selectedItem.set(item);
    }

    if (this.isUpdateArray()) {
      this.prevItem.set(structuredClone(item));
    }
    this.dialogType.set('edit');
    this.openDialog();
  }

  deleteItem(index: number, e?: Event) {
    e?.stopPropagation();
    this.modalComponent.showDialog({
      nzTitle: 'Delete record',
      nzContent: this.contentDeletedItem,
      nzWrapClassName: 'popup popup-confirm hide-footer-btns',
      nzIconType: 'icons:trash-bold',
      nzOkText: null,
    });
  }

  handleOK() {
    this.toast.showToast('info', '', 'Record has been deleted');
    this.dialogVisible.set(false);
    this.dialogVisibleProceed.set(false);
    this.dialogInfoVisible = false;
  }

  startFlow = signal<'history' | null>(null);

  openDetailInfo(item: any) {
    if (!item.id) {
      this.saveFilterHistory.set(null);
    }
    this.selectedItem.set(item);
    this.dialogInfoVisible = true;
    this.startFlow.set(null);
  }

  override pageHeaderButtonClicked(id: string) {
    switch (id) {
      case 'history': {
        this.openDetailInfo({});
        break;
      }
      case 'edit': {
        this.editItem(this.data());
        break;
      }
      case 'proceed': {
        this.startFlow.set(null);
        this.proceedItem();
        break;
      }
      case 'create': {
        this.startFlow.set(null);
        this.createItem();
        break;
      }
    }
  }

  onCancelDialog() {
    if (this.startFlow() === 'history') {
      this.openDetailInfo(this.selectedItem());
    }
  }

  // onAfterCloseDialogForm() {
  //   this.dialogInfoVisible = this.afterCloseDialogFormOpenHistory;
  //   this.afterCloseDialogFormOpenHistory = false;
  // }

  getSubText(title: string) {
    const options = this.widgetEmptyOptions();
    // const headingText = options?.text ?? 'No ' + title + ' data yet';
    const headingText = 'No information yet';
    const subText = options?.sub_text ?? 'Add new';

    return {
      headingText,
      subDescriptionText: subText,
    };
  }

  viewShowMore = signal(false);
  viewItem(item: any) {
    this.startFlow.set(null);
    if (this.showMoreContentType() === 'widget-drawer') {
      this.viewShowMore.set(true);
    } else {
      this.selectedItem.set(item);
      this.dialogType.set('view');
      this.openDialogView();
    }
  }

  showTableFilter = computed(() => {
    return this.functionSpec()?.layout_options?.widget_table_filter ?? false;
  });

  expandFilter = computed(() => {
    return this.functionSpec()?.layout_options?.expand_filter ?? false;
  });
  filterDialog = false;

  filterItem() {
    this.filterDialog = true;
  }

  resetExpandFilterForm = signal(false);
  filterLst = signal<any>(undefined);

  onResetExpandFilterForm() {
    this.resetExpandFilterForm.update((prev) => !prev);
    this.filterLst.set({});
  }

  getFilterLst(event: { value: any }) {
    this.filterLst.set(event.value);
  }

  filterValue = signal<Record<string, unknown> | null>(null);
  resetFilterForm = signal(false);
  reloadFilterForm = signal(false);

  defaultFilterValue = computed(() => {
    if (this.saveFilterHistory()) {
      return this.saveFilterHistory();
    }
    return this.filterValue();
  });

  async onFilterChange(value: NzSafeAny, isReload = false) {
    const filterVal = value?.value ?? {};

    const checkValueFilterEmpty =
      isEmpty(value?.value) || every(value?.value, (v) => v === undefined);
    if (checkValueFilterEmpty || isEqual(this.filterValue(), filterVal)) return;

    this.setFilterQuery(filterVal);
    this.filterValue.set(filterVal);
    if (isReload) {
      this.reloadFilterForm.update((prev) => !prev);
    }
    const updateHeaderProfile =
      this.functionSpec()?.filter_config?.update_header_profile;
    if (updateHeaderProfile) {
      const valueUpdate = await this.transformValue(
        updateHeaderProfile,
        filterVal,
      );
      this.layoutDataService.layoutEventEmit({
        key: 'selectERN',
        value: valueUpdate,
      });
    }
  }

  setFilterQuery(filterValue: Record<string, NzSafeAny>) {
    const newFilterQuery = this.buildFilterQuery(
      this.functionSpec()?.filter_config.filterMapping,
      filterValue,
    );

    if (isEqual(newFilterQuery, this.filterQuery())) return;
    this.filterQuery.set(newFilterQuery);
  }

  buildFilterQuery(filterMapping?: NzSafeAny[], formValue?: NzSafeAny) {
    return (
      filterMapping
        ?.map((f: any) => {
          return {
            field: f.field,
            operator: f.operator,
            value: getValue(formValue, f.valueField.split('.')),
          } as QueryFilter;
        })
        .filter(
          (f: any) =>
            (!isArray(f.value) && !isNil(f.value) && f.value !== '') ||
            (isArray(f.value) && f.value.length),
        ) ?? []
    );
  }

  isEmptyContent = computed(() => {
    if (this.isGetList()) {
      return isNil(this.tableData()) || isEmpty(this.tableData());
    } else {
      return isNil(this.data()) || isEmpty(this.data());
    }
  });

  isEmptyContentOrigin = computed(() => {
    return (
      isNil(this.dataOriginFoFilterInline()) ||
      isEmpty(this.dataOriginFoFilterInline())
    );
  });

  onNavigateToContract(
    isViewMore?: boolean,
    callback?: (success: boolean) => void,
  ) {
    const element = document.getElementById('HR.FS.FR.045');
    if (element) {
      callback?.(true);
      if (isViewMore) {
        this.viewShowMore.set(false);
      }
      element.scrollIntoView({ behavior: 'smooth', block: 'start' });
    } else {
      callback?.(false);
      this.modalComponent.showDialog(
        {
          nzTitle: 'Access Denied',
          nzContent: `You don't have permission to access this resource.`,
          nzWrapClassName: 'popup popup-confirm',
          nzIconType: 'icons:warning',
          nzOkText: 'OK',
          nzCancelText: null,
          nzWidth: '400px',
          nzClosable: false,
          nzCentered: true,
          nzClassName: 'popup-got-it',
        },
        'warning',
      );
    }

    this.layoutDataService.updateData({
      'HR.FS.FR.045': this.defaultFilterValue(),
    });
  }

  permissionForForm = computed(() => {
    const basePermissions = {
      isCreate: this.checkPermission(JobdataPermissionCodes.CREATE),
      isEdit: this.checkPermission(JobdataPermissionCodes.EDIT),
      isDelete: this.checkPermission(JobdataPermissionCodes.DELETE),
    };

    const currentModule = this.#layoutStore.currentModuleId();

    const addOirJobdataList = OIR_MENU_IDS.filter((id) =>
      this.metaDataService.getMenuByFunctionSpecId(id, currentModule),
    );

    const childrenPermissions = this.childrenActions();

    const findActiveAction = (functionCode: string, actionCode = 'Create') =>
      childrenPermissions
        ?.find((item) => item.functionCode === functionCode)
        ?.actions?.find((action) => action.code === actionCode)?.isActive ??
      false;

    const findActivePermissions = (functionCode: string) =>
      childrenPermissions
        ?.find((item) => item.functionCode === functionCode)
        ?.actions?.reduce(
          (curr, action) => ({ ...curr, [action.code]: action.isActive }),
          {},
        );

    return {
      ...basePermissions,
      isAddActionJobdata: this.checkPermission(JobdataPermissionCodes.CREATE),
      isAddERNJobdata: findActiveAction(JobdataFunctionCode.ADD_ERN),
      isAddOIRJobdata: addOirJobdataList.length > 0,
      ClassificationOfTermination: findActivePermissions(
        JobdataFunctionCode.ADD_CLASSIFY_TER,
      ),
      SocialName: findActivePermissions(BasicInfoFunctionCode.SOCIAL_NAME),
      SpecialName: findActivePermissions(BasicInfoFunctionCode.SPECIAL_NAME),
      addOirJobdataList,
    };
  });

  onHyperlinkClicked(e: { callback?: (success: boolean) => void }) {
    if (this.hyperlinkInForm()?.type === 'scrollToBlock') {
      const route = this.hyperlinkInForm()?.route as string;
      const element = document.getElementById(route);
      if (element) {
        e?.callback?.(true);
        element.scrollIntoView({ behavior: 'smooth', block: 'start' });
      } else {
        e?.callback?.(false);
        this.modalComponent.showDialog(
          {
            nzTitle: 'Access Denied',
            nzContent: `You don't have permission to access this resource.`,
            nzWrapClassName: 'popup popup-confirm',
            nzIconType: 'icons:warning',
            nzOkText: 'OK',
            nzCancelText: null,
            nzWidth: '400px',
            nzClosable: false,
            nzCentered: true,
            nzClassName: 'popup-got-it',
          },
          'warning',
        );
      }
    }
  }

  handleCheckPermission(id: string) {
    const accessType = this.isCheckPermissionWithAccessType()
      ? this.data()?.['accessType']
      : undefined;
    return this.checkPermission(id, undefined, accessType);
  }
}

function getValue(data: NzSafeAny, path: (string | number)[]): NzSafeAny {
  if (!data) {
    return undefined;
  }
  if (path.length < 1) return undefined;
  if (path.length === 1) {
    const tmp = path[0];
    if (typeof tmp === 'string' && new RegExp(/\([a-z0-9]*\)/, 'g').test(tmp)) {
      if (isArray(data)) {
        return data.map((it) => it[tmp.replace('(', '').replace(')', '')]);
      }
    }
    return data[tmp];
  } else {
    const thisPath = path.shift();
    if (!thisPath) return undefined;
    const tmp = thisPath;
    if (typeof tmp === 'string' && new RegExp(/\([a-z0-9]*\)/, 'g').test(tmp)) {
      if (isArray(data)) {
        return getValue(
          data.map((it) => it[tmp.replace('(', '').replace(')', '')]),
          path,
        );
      }
    }
    return getValue(data[thisPath], path);
  }
}
