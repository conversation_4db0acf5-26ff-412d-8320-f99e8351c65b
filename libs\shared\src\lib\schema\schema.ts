import { BehaviorSubject, fromEventPattern } from 'rxjs';
import {
  Component,
  computed,
  EffectRef,
  inject,
  input,
  InputSignal,
  WritableSignal,
} from '@angular/core';
import {
  FormFieldsConfig,
  Source,
  SourceField,
} from '@hrdx-fe/dynamic-features';
import { PageHeader } from '@hrdx/hrdx-design';
import { QueryFilter } from '@nestjsx/crud-request';
import { NzSafeAny } from 'ng-zorro-antd/core/types';
import {
  AuthService,
  Data,
  FunctionSpec,
  LayoutOptionsType,
} from '../services';
import { LayoutDataService } from '../services/layout-data.service';
import {
  ACTION_TYPE,
  isActionActive,
  isActionNeedReAuth,
} from '../services/utils';
import {
  ActionPermission,
  ChildrenActionPermission,
} from './../services/masterdata.service';
import { AccountPermission } from '../stores';

export type HeaderStyle = PageHeader['headerStyle'];

@Component({
  template: ``,
})
export class LayoutCommonComponent<LOT extends LayoutOptionsType = 'default'> {
  functionSpec = input.required<FunctionSpec<LOT>>();
  headerStyle = input<HeaderStyle>('master');
  headerVisible = input<boolean>(true);
  layoutDataService = inject(LayoutDataService);
  params = input<{ [k: string]: string }>({});
  parent = input<Data | null>(null);
  defaultFilter = input<QueryFilter[]>([]);
  url = input<string | null>(null);
  dataLayout = input<Record<string, NzSafeAny> | null>(null);
  childData = input<WritableSignal<NzSafeAny>>();
  disabledActionLst = input<string[]>([]);
  reAuthActionLst = input<string[]>([]);
  refresh = input<boolean>(false);
  // pass optional options from parent to child layout
  options = input<Partial<FunctionSpec['layout_options']>>();
  faceCode = input<string | null>(null);
  childrenActions = input<ChildrenActionPermission[]>([]);
  accountPermissions = input<AccountPermission>(undefined);
  leftHeight = input<number | null>(null);
  eventSubjects = input<Record<string, BehaviorSubject<any>>>();
  parentLevel = input(-1);
  private authenService = inject(AuthService);

  currentLevel = computed(() => (this.parentLevel() ?? -1) + 1);
  isRootLayout = computed(() => this.currentLevel() === 0);

  getActionsByItem = (item?: Data) => {
    if (!item?.['faceCode']) return null;
    return item['actions'] as ActionPermission[];
  };

  checkReAuthentication = async (permission: string, item?: Data) => {
    const actionsByItem = this.getActionsByItem(item);
    if (
      actionsByItem &&
      isActionNeedReAuth(permission as ACTION_TYPE, actionsByItem)
    ) {
      return await this.authenService.checkReAuthenticationByPopup();
    }
    if (!this.reAuthActionLst()?.includes(permission)) return true;
    return await this.authenService.checkReAuthenticationByPopup();
  };

  checkPermission = (
    permission: string,
    rowItem?: Data,
    accessType?: string,
  ): boolean => {
    const actionsByItem = this.getActionsByItem(rowItem);
    if (permission !== 'add-category' && actionsByItem) {
      return isActionActive(permission as ACTION_TYPE, actionsByItem);
    }
    if (this.disabledActionLst().includes(permission)) {
      return false;
    }
    if (
      accessType === 'Read' &&
      ['edit', 'create', 'delete'].includes(permission)
    ) {
      return false;
    }
    return true;
  };

  pageHeaderButtonClicked(id: string): void {
    return;
  }

  // A function to create a ResizeObserver Observable
  static createResizeObserverObservable(element: HTMLElement) {
    return fromEventPattern<ResizeObserverEntry[]>(
      // Start observing: setup the ResizeObserver
      (handler) => {
        const resizeObserver = new ResizeObserver((entries) =>
          handler(entries),
        );
        resizeObserver.observe(element);
        return resizeObserver;
      },
      // Stop observing: disconnect the ResizeObserver
      (resizeObserver) => (resizeObserver as NzSafeAny)?.disconnect(),
    );
  }
}

export interface LayoutCommon<LOT extends LayoutOptionsType = 'default'> {
  headerStyle: InputSignal<HeaderStyle>;
  functionSpec: InputSignal<FunctionSpec<LOT>>;
  effectFunctionSpec: EffectRef;
  headerVisible: InputSignal<boolean>;
  url: InputSignal<string | null>;
  parent: InputSignal<Data | null>;
  pageHeaderButtonClicked: (id: string) => void;
  disabledActionLst: InputSignal<string[]>;
  childrenActions: InputSignal<ChildrenActionPermission[]>;
}

export interface NestedConfirmOnSubmit {
  name: string;
  transform: string;
  transformValueAfterConfirm?: string;
  nestedSetting: NestedConfirmOnSubmit;
  type?: 'success' | 'warning' | 'info' | 'error';
  listMessage?: {
    title: string;
    contents: string[];
    _contents?: string;
  };
}

export type FormConfig = {
  fields?: FormFieldsConfig[];
  sources?: { [k: string]: Source };
  variables?: { [k: string]: SourceField };
  subForms?: { [k: string]: FormConfig };
  _mode?: SourceField;
  footer?: {
    create: string;
    update: string;
    createdBy: string;
    createdOn: string;
    updatedBy: string;
    updatedOn: string;
  };
  formTitle?: Record<string, string>;
  _formTitle?: Record<string, string>;
  formSize?: Record<string, string>;
  overview?: {
    dependentField: string;
    title: string;
    data: {
      key: string;
      label: string;
      value: string;
    }[];
    sources: { [k: string]: Source };
    noDataMessages: string;
  };
  overviewGroup?: {
    dependentField: string;
    title: string;
    data: {
      key: string;
      label: string;
      value: string;
    }[];
    sources: { [k: string]: Source };
    noDataMessages: string;
  }[];
  defaultValidateError?: string | Record<string, string>;
  confirmOnSubmit?: {
    [k: string]:
      | { transform: string; value?: 'compareValue' | 'formValue' }
      | { title: string; content: string };
  };

  nestedConfirmOnSubmit?: NestedConfirmOnSubmit;
  requestAfterSubmitResponse?: {
    sourcesMapping: {
      key: string;
      enabled: string;
    }[];
    sources: { [k: string]: Source };
  };
  preCheckSubmit?: {
    source: Source;
  };
};
