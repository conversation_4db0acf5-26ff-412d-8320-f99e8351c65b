import { inject, Inject, Injectable } from '@angular/core';
import {
  MSAL_GUARD_CONFIG,
  MsalGuardConfiguration,
  MsalService,
} from '@azure/msal-angular';
import {
  AccountInfo,
  AuthenticationResult,
  BrowserAuthError,
  BrowserAuthErrorCodes,
  EndSessionPopupRequest,
  EndSessionRequest,
  PopupRequest,
  RedirectRequest,
} from '@azure/msal-browser';
import { firstValueFrom, map, Observable, of } from 'rxjs';
import { DeltaTimeService } from './delta-time.service';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Router } from '@angular/router';
import {
  NzNotificationDataOptions,
  NzNotificationService,
} from 'ng-zorro-antd/notification';

export type TRedirectRequest = Partial<RedirectRequest>;
export type TPopupRequest = Partial<PopupRequest>;
export type TAuthenticationResult = AuthenticationResult;
export type TEndSessionPopupRequest = Partial<EndSessionPopupRequest>;
export type TEndSessionRequest = Partial<EndSessionRequest>;
export type TAccountInfo = AccountInfo;

type AuthSessionInfo = {
  expiredTime: number;
  sessionTimeout: number;
};

@Injectable({
  providedIn: 'root',
})
export class AuthService {
  static SESSION_EXPIRED_TIME_KEY = 'sessionExpiredTime';
  // TODO: should define into .env file
  static DELTA_TIME_TO_FORCE_RE_AUTHENTICATION = 5 * 60;
  static AUTH_SESSION_URL = '/api/auth-sessions';
  static INIT_AUTH_SESSION_ROUTE = '/auth/init-session';
  static POST_LOGOUT_REDIRECT_ROUTE = '/auth/post-logout';

  private reAuthEnabled?: boolean;
  private httpClient = inject(HttpClient);
  private router = inject(Router);
  private isPopupOpen = false;
  notification = inject(NzNotificationService);

  constructor(
    @Inject(MSAL_GUARD_CONFIG) private msalGuardConfig: MsalGuardConfiguration,
    private msalService: MsalService,
    private deltaTimeService: DeltaTimeService,
  ) {
    // TODO: should read config from .env file
    this.reAuthEnabled = true;
  }

  get authReqConfig() {
    return this.msalGuardConfig.authRequest ?? {};
  }

  get activeAccount() {
    return this.msalService.instance.getActiveAccount();
  }

  setSessionExpiredTime(expiredTime: number) {
    this.deltaTimeService.setTimeByKey(
      AuthService.SESSION_EXPIRED_TIME_KEY,
      expiredTime,
    );
  }

  shouldForceReAuthentication() {
    if (!this.reAuthEnabled) return false;
    return this.deltaTimeService.isKeyExpired(
      AuthService.SESSION_EXPIRED_TIME_KEY,
    );
  }

  reAuthenticationByPopup = async () => {
    if (this.isPopupOpen) {
      this.showToastMessage(
        'info',
        '',
        'Login popup may be behind your current window. Please check all open windows.',
      );
    }
    this.isPopupOpen = true;
    const response = await firstValueFrom(this.loginPopup({ prompt: 'login' }));
    await this.createAuthSession();
    this.setActiveAccount(response.account);
    this.isPopupOpen = false;
  };

  checkReAuthenticationByPopup = async () => {
    if (!this.shouldForceReAuthentication()) return true;
    try {
      const authSessionInfo = await firstValueFrom(this.getAuthSessionInfo());

      if (
        authSessionInfo.expiredTime &&
        !this.deltaTimeService.isTimeExpired(authSessionInfo.expiredTime)
      ) {
        this.setSessionExpiredTime(authSessionInfo.expiredTime);
        return true;
      }
      await this.reAuthenticationByPopup();
      return true;
    } catch (error: any) {
      if (
        error instanceof BrowserAuthError &&
        [BrowserAuthErrorCodes.userCancelled].includes(error.errorCode)
      ) {
        this.isPopupOpen = false;
      }
      console.error(
        'There is an error when check re-authentication: ',
        error,
      );

      return false;
    }
  };

  createAuthSession = async (): Promise<{
    success: boolean;
    expiredTime?: number;
    error?: HttpErrorResponse;
  }> => {
    try {
      const initSessionRes = await firstValueFrom(this.initAuthSession());
      this.setSessionExpiredTime(initSessionRes.expiredTime);
      return {
        success: true,
        expiredTime: initSessionRes.expiredTime,
      };
    } catch (error) {
      console.error('There is an error when init auth session: ', error);
      return {
        success: false,
        error: error as HttpErrorResponse,
      };
    }
  };

  initAuthSession() {
    return this.httpClient.post<AuthSessionInfo>(
      AuthService.AUTH_SESSION_URL,
      {},
    );
  }

  clearAuthSession() {
    return this.httpClient.patch(AuthService.AUTH_SESSION_URL, {});
  }

  getAuthSessionInfo() {
    return this.httpClient.get<AuthSessionInfo>(AuthService.AUTH_SESSION_URL);
  }

  private combineAuthRequest<T>(request?: T) {
    const authReqConfig = this.authReqConfig;
    return (request = { ...authReqConfig, ...(request ?? {}) } as T);
  }

  setActiveAccount(account: TAuthenticationResult['account']) {
    this.msalService.instance.setActiveAccount(account);
  }

  loginRedirect(request?: TRedirectRequest) {
    request = this.combineAuthRequest(request);
    return this.msalService.loginRedirect(request as RedirectRequest);
  }

  loginPopup(request?: TPopupRequest): Observable<TAuthenticationResult> {
    request = this.combineAuthRequest(request);
    return this.msalService
      .loginPopup(request as PopupRequest)
      .pipe(map((response) => response as TAuthenticationResult));
  }

  logoutPopup(request?: TEndSessionPopupRequest) {
    request = this.combineAuthRequest(request);
    return this.msalService.logoutPopup(request);
  }

  async logoutRedirect(request?: TEndSessionRequest) {
    try {
      // TODO: should use postLogoutRedirectUri to redirect to a page that will clear auth session, not clear auth session before user logout
      // request = this.combineAuthRequest({postLogoutRedirectUri: AuthService.POST_LOGOUT_REDIRECT_ROUTE, ...request});

      // first await to clear auth session then logout redirect
      await firstValueFrom(this.clearAuthSession());
      this.deltaTimeService.clearTimeByKey(
        AuthService.SESSION_EXPIRED_TIME_KEY,
      );
      request = this.combineAuthRequest(request);
      return this.msalService.logoutRedirect(request);
    } catch (error) {
      if (error instanceof HttpErrorResponse) {
        console.error(error);
      }
    }
    return of(null);
  }

  reAuthByLoginRedirect() {
    const currentRoute = this.router.url;
    return this.loginRedirect({
      prompt: 'login',
      redirectStartPage: `${AuthService.INIT_AUTH_SESSION_ROUTE}?redirectUrl=${currentRoute}`,
    });
  }

  private showToastMessage(
    type: 'error' | 'info' | 'success',
    title: string,
    message: string,
    options?: NzNotificationDataOptions & { size: 'default' | 'lg' },
  ) {
    const classes = [`toast-message-${type}`];
    classes.push(`toast-message__size-${options?.size ?? 'default'}`);
    if (!title || title.length === 0) {
      classes.push('no-title');
    }

    this.notification.create(type, title, message, {
      nzPlacement: 'topRight',
      nzClass: classes.join(' '),
      ...options,
    });
  }
}
