import {
  HttpClient,
  HttpErrorResponse,
  HttpHandler,
  HttpInterceptor,
  HttpRequest,
} from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { DynamicFormService } from '@hrdx-fe/dynamic-features';
import {
  QueryFilter,
  QuerySortOperator,
  RequestQueryBuilder,
} from '@nestjsx/crud-request';
import * as b64 from 'b64-to-blob';
import * as Handlebars from 'handlebars';
import {
  isArray,
  isDate,
  isNil,
  isObject,
  isString,
  mapValues,
  toString,
  trim,
} from 'lodash';
import * as moment from 'moment';
import { NzSafeAny } from 'ng-zorro-antd/core/types';
import {
  catchError,
  combineLatest,
  firstValueFrom,
  forkJoin,
  map,
  Observable,
  of,
  switchMap,
  tap,
} from 'rxjs';
import { ConfigStore, LayoutStore } from '../stores';
import { MasterdataService } from './masterdata.service';
import * as _ from 'lodash';
export type Data = {
  id: string;
  code?: string;
  effectiveDate?: Date | string;
  updatedBy?: unknown;
  updatedAt?: Date | string;
  createdBy?: unknown;
  createdAt?: Date | string;
  histories?: Data[];
} & { [key: string]: any };

export type PaginateData = {
  data: Data[];
  count: number;
  total: number;
};

export function mappingUrl(
  url: string,
  params: Record<string, string>,
  variables?: any,
  options?: CompileOptions,
) {
  return Handlebars.compile(
    url,
    options,
  )(variables).replace(/\/:([^/]+)/g, (_, key) => '/' + params[key]);
  // return url;
}

export enum AuthActions {
  Read = 'Read',
  Create = 'Create',
  Update = 'Update',
  Delete = 'Delete',
  GenerateReport = 'GenerateReport',
  ExportGrid = 'ExportGrid',
  Schedule = 'Schedule',
  Calculation = 'Calculation',
  History = 'History',
  Approve = 'Approve',
  Lock = 'Lock',
  Unlock = 'Unlock',
}

export function isValidAuthAction(
  action: string | AuthActions,
): action is AuthActions {
  const isValidAuthAction = Object.values(AuthActions).includes(
    action as AuthActions,
  );
  if (!isValidAuthAction)
    console.error(
      `Invalid auth action: ${action}. Valid actions are: ${Object.values(AuthActions).join(', ')}`,
    );
  return isValidAuthAction;
}

export type ServiceFunctionOptions = {
  onError?: (err: any) => void;
  authAction?: AuthActions;
  faceCode?: string;
};

export type ServiceFunction = <T = NzSafeAny>(
  url: string,
  data: NzSafeAny,
  faceCode?: string,
  options?: ServiceFunctionOptions,
) => Observable<T>;

export enum DeleteErrorCode {
  BusinessLogic = 'NghiepVu',
  Permission = '4543',
}

// error code not error status
export enum ErrorCode {
  NoEmployeesWhenReCalculatePayroll = '2027',
}

export enum ActionCodeProgressLayout {
  Calculation = 'Calculation',
  Note = 'Note',
  Lock = 'Lock',
  Unlock = 'Unlock',
}

@Injectable()
export class FCInterceptor implements HttpInterceptor {
  #layoutStore = inject(LayoutStore);
  intercept(req: HttpRequest<any>, next: HttpHandler) {
    const faceCode = this.#layoutStore.currentFaceCode() ?? '';

    const authAction =
      this.#layoutStore.currentAuthAction() ?? AuthActions.Read;

    const newHeaders = req.headers
      .set(
        'Ppx-function-code',
        req.headers.get('Ppx-function-code') ?? faceCode,
      )
      .set('ppx-action-code', req.headers.get('ppx-action-code') ?? authAction);

    const authReq = req.clone({ headers: newHeaders });

    return next.handle(authReq);
  }
}

@Injectable({
  providedIn: 'root',
})
export class BffService {
  _service = inject(DynamicFormService);
  http = inject(HttpClient);
  masterdataService = inject(MasterdataService);
  config = inject(ConfigStore);
  host = '';

  setDataHeaders(faceCode?: string, authAction?: string) {
    const headers: any = {};
    if (faceCode) {
      headers['Ppx-function-code'] = faceCode;
    }
    if (authAction) {
      headers['ppx-action-code'] = authAction;
    }
    return headers;
  }

  generateAvatarLink = async (avatarFile: string, faceCode?: string) => {
    if (avatarFile) {
      const link = await firstValueFrom(
        this.http
          .get(`/api/hr-files/${avatarFile}`, {
            headers: this.setDataHeaders(faceCode, AuthActions.Read),
            responseType: 'blob',
            observe: 'response',
          })
          .pipe(
            switchMap((res: any) => {
              if (res?.body instanceof Blob) {
                const url = URL.createObjectURL(res.body);
                return of(url);
              }
              return of('');
            }),
            catchError(() => of('')),
          ),
      );
      return link;
    }
    return '';
  };

  getList(
    url: string,
    page = 0,
    pageSize = 0,
    filter: QueryFilter[] = [],
    faceCode?: string,
  ) {
    const qb = RequestQueryBuilder.create();
    qb.setPage(page);
    qb.setLimit(pageSize);
    // qb.setFilter(filter);
    const newFilter = this.transferFilterToSearch([...filter]);
    qb.search({ $and: newFilter });

    const queryString = qb.query();

    return of(undefined).pipe(
      switchMap(() =>
        this.http
          .get<
            { data: Data[] } | Data[]
          >(`${this.host}${url}?${queryString}`, { headers: this.setDataHeaders(faceCode, AuthActions.Read) })
          .pipe(
            map((d) => {
              if (isArray(d)) {
                return d;
              } else {
                return (d.data as Data[]).map((it, i) => ({ ...it, index: i }));
              }
            }),
          ),
      ),
    );
  }

  getListTree(url: string, filter: QueryFilter[] = [], faceCode?: string) {
    let apiUrl = `${this.host}${url}`;
    if (filter && filter.length > 0) {
      const qb = RequestQueryBuilder.create();
      qb.setFilter(filter);
      const queryString = qb.query();
      apiUrl = `${this.host}${url}?${queryString}`;
    }

    return this.http
      .get<
        Data | Data[]
      >(apiUrl, { headers: this.setDataHeaders(faceCode, AuthActions.Read) })
      .pipe(map((d) => d));
  }

  getObject(url: string, filter?: QueryFilter[], faceCode?: string) {
    let apiUrl = `${this.host}${url}`;
    if (filter && filter.length > 0) {
      const qb = RequestQueryBuilder.create();
      qb.setFilter(filter);
      const queryString = qb.query();
      apiUrl = `${this.host}${url}?${queryString}`;
    }

    return this.http
      .get<
        Data | Data[]
      >(apiUrl, { headers: this.setDataHeaders(faceCode, AuthActions.Read) })
      .pipe(
        map((d) => {
          if (isArray((d as NzSafeAny)?.data)) {
            return (d as NzSafeAny)?.data[0] as Data;
          } else {
            return isArray(d) ? d[0] : d;
          }
        }),
      );
  }

  getObjectByQuery(
    url: string,
    filter: QueryFilter[] = [],
    qs?: string,
    faceCode?: string,
    options?: ServiceFunctionOptions,
  ) {
    const qb = RequestQueryBuilder.create();
    qb.setFilter(filter);
    const queryString = qs ? qs : qb.query();

    return this.http
      .get<
        Data | Data[] | NzSafeAny[]
      >(`${this.host}${url}?${queryString}`, { headers: this.setDataHeaders(faceCode, options?.authAction ?? AuthActions.Read) })
      .pipe(map((d) => (isArray(d) ? d[0] : d)));
  }

  getStreamByQuery(
    url: string,
    queryString?: string,
    faceCode?: string,
    options?: ServiceFunctionOptions,
  ) {
    return this.http.get(`${this.host}${url}?${queryString}`, {
      headers: this.setDataHeaders(
        faceCode,
        options?.authAction ?? AuthActions.Read,
      ),
      responseType: 'blob',
    });
  }

  exportReport(
    url: string,
    filter: QueryFilter[] = [],
    qs?: string,
    fileName?: string,
    method?: string,
    fileType?: number,
    faceCode?: string,
  ) {
    const qb = RequestQueryBuilder.create();
    // qb.setFilter(filter);
    const newFilter = this.transferFilterToSearch([...filter]);
    qb.search({ $and: newFilter });
    const queryString = qs ? qs : qb.query();

    return method === 'POST'
      ? this.http
          .post(
            `${this.host}${url}?${queryString}`,
            {},
            {
              headers: this.setDataHeaders(faceCode),
              responseType: 'blob',
              observe: 'response',
            },
          )
          .pipe(
            catchError((err) => {
              throw err;
            }),
            tap((res: NzSafeAny) => {
              this.getFilenameFromHeader(res, fileName, fileType);
            }),
          )
      : this.http
          .get(`${this.host}${url}?${queryString}`, {
            responseType: 'blob',
            observe: 'response',
          })
          .pipe(
            tap((res: NzSafeAny) => {
              this.getFilenameFromHeader(res, fileName, fileType);
            }),
          );
  }

  exportReportForPOST(
    url: string,
    body: NzSafeAny,
    fileName?: string,
    fileType?: number,
    faceCode?: string,
  ) {
    return this.http
      .post(`${this.host}${url}`, body, {
        headers: this.setDataHeaders(faceCode),
        responseType: 'blob',
        observe: 'response',
      })
      .pipe(
        catchError((err) => {
          throw err;
        }),
        tap((res: NzSafeAny) => {
          this.getFilenameFromHeader(res, fileName, fileType);
        }),
      );
  }

  tryConvertToTimeStamp(value: string): string | '' {
    if (!value || value.trim() === '') {
      return value;
    }

    const dateRegex = /^(\d{2})\/(\d{2})\/(\d{4})$/;
    const match = value.match(dateRegex);

    if (match) {
      const currentDate = moment(value, 'DD/MM/YYYY').toDate();
      const startOfDay = new Date(
        Date.UTC(
          currentDate?.getFullYear(),
          currentDate?.getMonth(),
          currentDate?.getDate(),
          0,
          0,
          0,
        ),
      );
      return Math.floor(startOfDay.getTime() / 1000) + '';
    }
    return value;
  }

  getPaginate(
    url: string,
    page = 0,
    pageSize = 0,
    filter: QueryFilter[] = [],
    search = '',
    sortOrder: Record<string, string | null> = {},
    isSearchDate = false,
    faceCode?: string,
  ) {
    const qb = RequestQueryBuilder.create();

    const filterSearch = search
      ? [
          {
            field: 'search',
            operator: '$eq',
            value: isSearchDate
              ? this.tryConvertToTimeStamp(trim(search).replace(/\t/g, ''))
              : trim(search).replace(/\t/g, ''),
          } as QueryFilter,
        ]
      : [];
    qb.setPage(page);
    qb.setLimit(pageSize);
    const newFilter = this.transferFilterToSearch([...filter, ...filterSearch]);
    // qb.setFilter([...filter, ...filterSearch]);
    qb.search({ $and: newFilter });

    const sortQuery = Object.keys(sortOrder)
      .filter((key) => sortOrder[key])
      .map((key) => ({
        field: key,
        order:
          sortOrder[key] === 'ascend' ? 'ASC' : ('DESC' as QuerySortOperator),
      }));
    qb.sortBy(sortQuery);
    const queryString = qb.query();

    // console.log('url', url);

    return of(undefined).pipe(
      switchMap(() =>
        this.http.get<{
          data: Data[];
          count: number;
          total: number;
        }>(`${this.host}${url}?${queryString}`, {
          headers: this.setDataHeaders(faceCode, AuthActions.Read),
        }),
      ),
      switchMap((d) => {
        const dataLength = d.data?.length ?? 0;
        if (dataLength > 0 && this.config.menu().getActionByRow) {
          return combineLatest({
            data: forkJoin(
              d.data.map((item) => {
                if (item['faceCode']) {
                  return combineLatest({
                    actions: this.masterdataService.getPermissionActionById(
                      item['faceCode'],
                    ),
                    item: of(item),
                  }).pipe(
                    map(({ actions, item }) => {
                      return { ...item, actions };
                    }),
                  );
                }
                return of(item);
              }),
            ),
            d: of(d),
          }).pipe(
            map(({ data, d }) => {
              return { data, count: d.count, total: d.total };
            }),
          );
        } else return of(d);
      }),
      catchError((err) => {
        console.error(err);
        return of({ data: [], count: 0, total: 0 });
      }),
    );
  }

  getItemCustom(
    url: string,
    filter: QueryFilter[] = [],
    faceCode?: string,
    options?: ServiceFunctionOptions,
  ) {
    const qb = RequestQueryBuilder.create();
    const newFilter = this.transferFilterToSearch([...filter]);
    qb.search({ $and: newFilter });
    const queryString = qb.query();

    return this.http.get<NzSafeAny[] | NzSafeAny>(`${url}?${queryString}`, {
      headers: this.setDataHeaders(faceCode,options?.authAction),
    });
  }

  getItem(url: string, id: string, customUrl?: string, faceCode?: string) {
    if (customUrl)
      return this.http.get<Data>(customUrl, {
        headers: this.setDataHeaders(faceCode),
      });
    return this.http.get<Data>(`${url}/${id}`, {
      headers: this.setDataHeaders(faceCode),
    });
  }

  private appendToFormData(
    formData: FormData,
    key: string,
    value: NzSafeAny,
    parentKey?: string,
    isInArray?: boolean,
  ) {
    if (value?.FieldValue === 'file') {
      const blob = b64.default(value?.data.split(',')[1], value?.type);
      formData.append(key, blob, encodeURIComponent(value?.name));
    } else if (isArray(value)) {
      value.forEach((v: NzSafeAny, index: number) => {
        if (v?.FieldValue === 'file') {
          this.appendToFormData(formData, `${key}`, v, parentKey);
        } else {
          this.appendToFormData(
            formData,
            `${key}[${index}]`,
            v,
            parentKey,
            true,
          );
        }
      });
    } else if (isObject(value)) {
      Object.keys(value).forEach((subKey) => {
        this.appendToFormData(
          formData,
          `${key}.${subKey}`,
          (value as Record<string, NzSafeAny>)[subKey],
          parentKey,
        );
      });
    } else {
      const stringValue =
        typeof value === 'string' && isInArray
          ? value
          : JSON.stringify(value + '');
      !isNil(value) &&
        formData.append(parentKey ? `${parentKey}.${key}` : key, stringValue);
    }
  }

  createFormData(
    url: string,
    body: any,
    faceCode?: string,
    options?: ServiceFunctionOptions,
  ) {
    const formData = new FormData();
    body = this.transformValues(body);
    Object.keys(body).forEach((key) => {
      if (!isNil(body[key])) this.appendToFormData(formData, key, body[key]);
    });
    return this.http.post(url + '/upload', formData, {
      headers: this.setDataHeaders(
        faceCode,
        options?.authAction ?? AuthActions.Create,
      ),
    });
  }

  uploadAvatar(url: string, file: File, faceCode?: string) {
    const formData = new FormData();
    // const blob = b64.default((file as any)?.data.split(',')[1], file?.type);
    // formData.append('file', blob, file?.name);
    formData.append('file', file);

    return this.http.post(url + '/avatar', formData, {
      headers: this.setDataHeaders(faceCode),
    });
  }

  deleteAvatar(url: string, faceCode?: string) {
    return this.http.delete(url + '/avatar', {
      headers: this.setDataHeaders(faceCode),
    });
  }

  importData(url: string, body: any, faceCode?: string) {
    const formData = new FormData();
    Object.keys(body).forEach((key) => {
      if (isArray(body[key])) {
        let check = false;
        body[key].forEach((v: any) => {
          if (v?.FieldValue === 'file') {
            check = true;
            const blob = b64.default(v?.data.split(',')[1], v?.type);
            formData.append(key, blob, encodeURIComponent(v?.name));
            return;
          }
        });
        if (!check) formData.append(key, JSON.stringify(body[key]));

        return;
      }
      if (body[key]?.FieldValue === 'file') {
        const blob = b64.default(
          body[key]?.data.split(',')[1],
          body[key]?.type,
        );
        formData.append(key, blob, encodeURIComponent(body[key]?.name));
        return;
      }

      if (!isNil(body[key])) formData.append(key, JSON.stringify(body[key]));
    });
    return this.http.post(url + '/import', formData, {
      headers: this.setDataHeaders(faceCode),
    });
  }
  #layoutStore = inject(LayoutStore);
  createItem(
    url: string,
    body: NzSafeAny,
    faceCode?: string,
    options?: ServiceFunctionOptions,
  ) {
    body = this.transformValues(body);
    return this.http.post<Data | NzSafeAny>(url, body, {
      headers: this.setDataHeaders(
        faceCode,
        options?.authAction ?? AuthActions.Create,
      ),
    });
  }

  updateFormData(
    url: string,
    id: string | null | undefined,
    body: any,
    faceCode?: string,
  ) {
    if (!id) return of(undefined);
    body = this.transformValues(body);
    const formData = new FormData();
    Object.keys(body).forEach((key) => {
      if (!isNil(body[key])) this.appendToFormData(formData, key, body[key]);
    });
    return this.http.post(url + `/${id}/upload`, formData, {
      headers: this.setDataHeaders(faceCode, AuthActions.Update),
    });
  }
  updateItem(
    url: string,
    id: string | null | undefined,
    body: any,
    urlCustom?: string,
    faceCode?: string,
    options?: ServiceFunctionOptions,
  ): Observable<any | undefined> {
    if (!id) return of(undefined);
    return this.http.patch(
      urlCustom ?? `${url}/${id}`,
      this.transformValues(body),
      {
        headers: this.setDataHeaders(
          faceCode,
          options?.authAction ?? AuthActions.Update,
        ),
      },
    );
  }

  deactiveItem(
    url: string,
    id: string | null | undefined,
    body?: any,
    urlCustom?: string,
    faceCode?: string,
  ): Observable<any | undefined> {
    if (!id) return of(undefined);
    return this.http.patch(
      urlCustom ?? `${url}/${id}/deactive`,
      this.transformValues(body),
      {
        headers: this.setDataHeaders(faceCode, AuthActions.Update),
      },
    );
  }

  updateList(
    url: string,
    body: any,
    faceCode?: string,
    options?: ServiceFunctionOptions,
  ) {
    console.log(options, 'optionsoptionsoptions');
    return this.http.patch(`${url}`, body, {
      headers: this.setDataHeaders(
        faceCode,
        options?.authAction ?? AuthActions.Update,
      ),
    });
  }
  update(
    url: string,
    body: any,
    filter?: QueryFilter[],
    faceCode?: string,
  ): Observable<any | undefined> {
    const qb = RequestQueryBuilder.create();
    qb.setFilter(filter ?? []);
    const queryString = qb.query();
    const customUrl = filter ? `${url}?${queryString}` : url;
    return this.http.patch(customUrl, body, {
      headers: this.setDataHeaders(faceCode, AuthActions.Update),
    });
  }

  deleteItem(url: string, id: string, customUrl?: string, faceCode?: string) {
    return this.http.delete(customUrl ?? `${url}/${id}`, {
      headers: this.setDataHeaders(faceCode, AuthActions.Delete),
    });
  }

  deleteItems(url: string, ids: NzSafeAny[], faceCode?: string) {
    if (!isArray(ids)) return of([]);
    return forkJoin(
      ids.map((item) =>
        this.deleteItem(url, item?.id || item, item?.customUrl, faceCode).pipe(
          map((data) => ({ status: 'success', value: data, statusCode: 200 })),
          catchError((err) =>
            of({ status: 'error', value: err.error, statusCode: err.status }),
          ),
        ),
      ),
    );
  }

  deleteMultiItems(
    url: string,
    ids: NzSafeAny[],
    customBodyDelete?: any,
    faceCode?: string,
  ) {
    if (!isArray(ids)) return of([]);
    const body = customBodyDelete ?? { ids };
    return this.http.delete(`${url}/multidelete`, {
      headers: this.setDataHeaders(faceCode, AuthActions.Delete),
      body: body,
    });
  }
  transferFilterToSearch(filter: QueryFilter[]): any {
    return filter.map((it) => {
      if (it.operator === '$and' || it.operator === '$or') {
        return {
          [it.operator]: this.transferFilterToSearch(it.value as QueryFilter[]),
        };
      }
      return {
        [it.field]: {
          [it.operator]: it.value,
        },
      };
    });
  }

  getHistories(
    url: string,
    req: {
      id?: string;
      filter?: QueryFilter[];
      search?: string;
    },
    faceCode?: string,
  ) {
    // return this.getList(`${url}`);
    const id = req.id;
    const filter = req.filter ?? [];
    const search = req.search ?? '';
    const qb = RequestQueryBuilder.create();
    const filterSearch = search
      ? [{ field: 'search', operator: '$eq', value: search } as QueryFilter]
      : [];

    const newFilter = this.transferFilterToSearch([...filter, ...filterSearch]);
    qb.search({ $and: newFilter });
    const queryString = qb.query();
    const options = {
      headers: this.setDataHeaders(faceCode, AuthActions.History),
    };
    if (!id)
      return this.http
        .get<
          { data: Data[] } | Data[]
        >(`${url}/histories?${queryString}`, options)
        .pipe(
          map((d) => {
            if (isArray((d as { data: Data[] })?.data)) {
              return (d as { data: Data[] }).data.map((it, i) => ({ ...it }));
            } else {
              return d as Data[];
            }
          }),
        );
    return this.http
      .get<
        { data: Data[] } | Data[]
      >(`${url}/${id}/histories${queryString ? '?' + queryString : ''}`, options)
      .pipe(
        map((d) =>
          isArray(d)
            ? ((d as Data[]) ?? [])
            : d.data.map((it, i) => ({ ...it })),
        ),
      );
  }

  lockItem<T>(
    url: string,
    id: string,
    body: T,
    customUrl?: string,
    method?: string,
    faceCode?: string,
    options?: ServiceFunctionOptions,
  ): Observable<any | undefined> {
    if (!id) return of(undefined);
    const headers = this.setDataHeaders(
      faceCode,
      options?.authAction ?? AuthActions.Lock,
    );
    if (customUrl) {
      return method === 'POST'
        ? this.http.post(customUrl, body, {
            headers,
          })
        : this.http.patch(customUrl, body, {
            headers,
          });
    }
    return this.http.patch(`${url}/${id}/lock`, body, {
      headers,
    });
  }

  exportFile(url: string, body: any, returnFile = true, faceCode?: string) {
    return this.http
      .post(`${url}/export`, body, {
        headers: this.setDataHeaders(faceCode),
        responseType: 'blob',
      })
      .pipe(
        tap((res) => {
          if (returnFile) {
            const url = window.URL.createObjectURL(res);
            const a = document.createElement('a');
            document.body.appendChild(a);
            a.href = url;
            a.click();
            window.URL.revokeObjectURL(url);
          }
        }),
      );
  }

  exportFileByGet(
    url: string,
    page = 0,
    pageSize = 0,
    filter: QueryFilter[] = [],
    search = '',
    sortOrder: Record<string, string | null> = {},
    body: NzSafeAny = undefined,
    options?: ServiceFunctionOptions,
  ) {
    const qb = RequestQueryBuilder.create();
    const filterSearch = search
      ? [{ field: 'search', operator: '$eq', value: search } as QueryFilter]
      : [];
    qb.setPage(page);
    qb.setLimit(pageSize);
    const newFilter = this.transferFilterToSearch([...filter, ...filterSearch]);
    qb.search({ $and: newFilter });

    // qb.setFilter([...filter, ...filterSearch]);
    const sortQuery = Object.keys(sortOrder)
      .filter((key) => sortOrder[key])
      .map((key) => ({
        field: key,
        order:
          sortOrder[key] === 'ascend' ? 'ASC' : ('DESC' as QuerySortOperator),
      }));
    qb.sortBy(sortQuery);
    const queryString = qb.query();
    if (!isNil(body)) {
      return this.http
        .post(`${this.host}${url}/export?${queryString}`, body, {
          responseType: 'blob',
          observe: 'response',
          headers: this.setDataHeaders(options?.faceCode, options?.authAction),
        })
        .pipe(
          tap((res) => {
            this.getFilenameFromHeader(res);
          }),
        );
    } else
      return this.http
        .get(`${this.host}${url}/export?${queryString}`, {
          responseType: 'blob',
          observe: 'response',
          headers: this.setDataHeaders(options?.faceCode, options?.authAction),
        })
        .pipe(
          tap((res) => {
            this.getFilenameFromHeader(res);
          }),
        );
  }

  getFile(
    url: string,
    method = 'GET',
    body?: any,
    options?: ServiceFunctionOptions,
  ) {
    let observable: Observable<any> = of(null);
    method = method.toUpperCase();
    const reqOptions = {
      responseType: 'blob',
      observe: 'response',
      headers: this.setDataHeaders(options?.faceCode, options?.authAction),
    } as any;
    switch (method) {
      case 'GET': {
        observable = this.http.get(url, reqOptions);
        break;
      }
      case 'POST': {
        this.http.post(url, body, reqOptions);
        break;
      }
    }

    return observable.pipe(
      catchError(async (err) => {
        const onError = options?.onError;
        if (!onError) return;
        let errorMessage = err?.message;
        if (err?.error instanceof Blob && !errorMessage) {
          const jsonData = JSON.parse(await err.error.text());
          errorMessage = jsonData?.message;
        }

        onError(errorMessage);
      }),
      tap((res) => {
        this.getFilenameFromHeader(res);
      }),
    );
  }

  getTemplate(
    url: string,
    fileName?: string,
    queryString?: string,
    faceCode?: string,
  ) {
    const fullUrl = queryString
      ? `${url}/template?${queryString}`
      : `${url}/template`;
    return this.http
      .get(fullUrl, {
        headers: this.setDataHeaders(faceCode),
        responseType: 'blob',
        observe: 'response',
      })
      .pipe(
        tap((res: NzSafeAny) => {
          // const url = window.URL.createObjectURL(res);
          // const a = document.createElement('a');
          // document.body.appendChild(a);
          this.getFilenameFromHeader(res, fileName);
        }),
      );
  }

  getFilenameFromHeader(
    res: NzSafeAny,
    defaultFileName?: string,
    type?: number,
  ) {
    //Get filename from Content-Disposition header
    const header = res.headers.get('Content-Disposition');
    // Check both `filename*=` and `filename=` in order of priority
    const filenameStarRegex = /filename\*=(([‘"]).*?\2|[^;\n]*)/;
    const filenameRegex = /filename[^;=\n]*=(([‘"]).*?\2|[^;\n]*)/;
    let filename;

    let matches;

    // Prioritize `filename*=`
    if ((matches = filenameStarRegex.exec(header)) !== null) {
      let extractedFilename = matches[1].trim();

      // Check if `filename*=` has the format `UTF-8''encoded-filename` and decode
      if (extractedFilename.startsWith("UTF-8''")) {
        extractedFilename = decodeURIComponent(
          extractedFilename.replace("UTF-8''", ''),
        );
      }

      filename = extractedFilename;
    } else if ((matches = filenameRegex.exec(header)) !== null) {
      // If `filename*=` is not found, check for `filename=`
      filename = matches[1].trim().replace(/['"]/g, ''); // Remove surrounding quotes if present
    }

    if (isNil(filename)) {
      filename = defaultFileName + this.getFileType(type) || 'report.xlsx';
    }

    const url = window.URL.createObjectURL(res.body);
    const a = document.createElement('a');
    document.body.appendChild(a);
    a.href = url;
    a.download = filename ?? 'file.xlsx';
    a.click();
    window.URL.revokeObjectURL(url);
    a.remove();
  }

  getFileType(type?: number) {
    switch (type) {
      case FileType.EXCEL:
        return '.xlsx';
      case FileType.CSV:
        return '.csv';
      case FileType.PDF:
        return '.pdf';
      case FileType.WORD:
        return '.docx';
      default:
        return '.xlsx';
    }
  }

  getErrorMessage = async (err: HttpErrorResponse) => {
    let errorMessage = err?.message;
    if (err?.error instanceof Blob) {
      const jsonData = JSON.parse(await err.error.text());
      errorMessage = jsonData?.message;
    }
    return errorMessage;
  };

  getFileValidate(
    key: string | null | undefined,
    options?: ServiceFunctionOptions,
  ) {
    if (!key) return of(undefined);
    return this.http
      .get(`api/files/${key}/validate`, {
        responseType: 'blob',
        observe: 'response',
        headers: this.setDataHeaders(options?.faceCode, options?.authAction),
      })
      .pipe(
        tap((res: NzSafeAny) => {
          this.getFilenameFromHeader(res, key);
        }),
      );
  }

  Post = <T>(
    url: string,
    body: Record<string, NzSafeAny>,
    faceCode?: string,
    options?: ServiceFunctionOptions,
  ) => {
    return this.http.post(`${this.host}${url}`, this.transformValues(body), {
      headers: this.setDataHeaders(faceCode, options?.authAction),
    }) as Observable<T>;
  };

  Patch = <T>(
    url: string,
    body: Record<string, NzSafeAny>,
    faceCode?: string,
    options?: ServiceFunctionOptions,
  ) => {
    return this.http.patch(url, this.transformValues(body), {
      headers: this.setDataHeaders(faceCode, options?.authAction),
    }) as Observable<T>;
  };

  Put = <T>(
    url: string,
    body: Record<string, NzSafeAny>,
    faceCode?: string,
  ) => {
    return this.http.put(url, this.transformValues(body), {
      headers: this.setDataHeaders(faceCode),
    }) as Observable<T>;
  };

  Get = <T>(
    url: string,
    faceCode?: string,
    options?: ServiceFunctionOptions,
  ) => {
    return this.http.get(url, {
      headers: this.setDataHeaders(faceCode, options?.authAction),
      ...options,
    }) as Observable<T>;
  };

  private transformValues<
    T extends Record<string, unknown> | Record<string, unknown>[] | unknown,
  >(value: T): T {
    if (Array.isArray(value)) {
      return value.map((item) => this.transformValues(item)) as T;
    } else if (isDate(value)) {
      return value.toISOString() as T;
    } else if (isObject(value)) {
      const res = mapValues(value, (v) => this.transformValues(v)) as T;
      return res;
    } else {
      if (isString(value)) {
        return trim(toString(value)) as T;
      } else {
        return value;
      }
    }
  }
}

enum FileType {
  EXCEL = 0,
  CSV = 1,
  PDF = 2,
  WORD = 3,
}

function getAuthAction(authActions: NzSafeAny[], action: string) {
  const authAction = _.get(_.find(authActions, { code: action }), 'code') ?? '';

  return authAction;
}
