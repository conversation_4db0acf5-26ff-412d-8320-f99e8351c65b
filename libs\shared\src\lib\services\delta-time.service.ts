import { LocalStorageService } from './local-storage.service';
import { inject, Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class DeltaTimeService {
  private localStorageService = inject(LocalStorageService);
  // constructor() {}

  private convertTime(time: string | Date | number) {
    if (typeof time === 'string') {
      return new Date(time).valueOf();
    } else if (time instanceof Date) {
      return time.valueOf();
    }
    return Math.floor(time / 1000);
  }

  setTimeByKey(key: string, time: number) {
    this.localStorageService.setItem(key, time);
  }

  clearTimeByKey(key: string) {
    this.localStorageService.removeItem(key);
  }

  getTimeByKey(key: string) {
    return +(this.localStorageService.getItem<number>(key) ?? 0);
  }

  private diffTime(startTime: number, endTime: number) {
    return endTime - startTime;
  }

  isKeyExpired(key: string) {
    const timeByKey = this.getTimeByKey(key);
    if (!timeByKey) return true;
    return this.isTimeExpired(timeByKey);
  }

  isTimeExpired(time: number) {
    const now = this.convertTime(Date.now());
    const diff = this.diffTime(time, now);
    return diff >= 0;
  }
}
