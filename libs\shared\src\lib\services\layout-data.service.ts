import { Injectable, signal } from '@angular/core';
import { NzSafeAny } from 'ng-zorro-antd/core/types';
import { Observable, Subject } from 'rxjs';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class LayoutDataService {
  data = signal<Record<string, NzSafeAny>>({});
  private _layoutEventEmiter = new Subject();
  public layoutEventEmiter$ = this._layoutEventEmiter.asObservable();

  public layoutEventEmit(rec: {
    key: string;
    value?: NzSafeAny;
    extraData?: NzSafeAny;
  }) {
    this._layoutEventEmiter.next(rec);
  }

  public update(data: Record<string, NzSafeAny>) {
    this.data.update((val) => ({ ...val, ...data }));
  }

  public clear() {
    this.data.set({});
  }

  private dataSubject = new BehaviorSubject<Record<string, NzSafeAny>>({});
  public data$: Observable<Record<string, NzSafeAny>> =
    this.dataSubject.asObservable();

  public updateData(data: Record<string, NzSafeAny>) {
    this.dataSubject.next({ ...this.dataSubject.value, ...data });
  }

  public clearData() {
    this.dataSubject.next({});
  }

  public getData(): Record<string, NzSafeAny> {
    return this.dataSubject.value;
  }

  public getDataSubject(): BehaviorSubject<Record<string, NzSafeAny>> {
    return this.dataSubject;
  }
}
