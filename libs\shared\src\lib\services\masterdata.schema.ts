import { Source, SourceField } from '@hrdx-fe/dynamic-features';
import { ButtonSchema, TabbarMenu, Widget } from '@hrdx/hrdx-design';
import { ComparisonOperator, QueryFilter } from '@nestjsx/crud-request';
import { NzSafeAny } from 'ng-zorro-antd/core/types';
import { AuthActions } from './bff.service';

type RowActionsData = {
  icon: string;
  title: string;
  id: string;
  children?: RowActionsData[];
  group?: string;
  condition_func?: string;
  _disabled?: string;
  type: ButtonSchema['type'];
};

type TransferTable = {
  name: string;
  group_by: string; // for mock data
  display_filter?: boolean;
  transfer_fields?: Record<string, any>;
  tool_table?: LayoutButton[];
  show_table_checkbox?: boolean;
  default_filter_query?: QueryFilter[];
  custom_get_list_api?: ApiConfig;
  show_filter?: boolean;
  actions_many?: LayoutButton[];
};

type TableAction = {
  id: string;
  icon?: string;
  title: string;
  type: 'navigate' | 'form';
  link?: string;
};

type TabItem = {
  id: string;
  title: string;
  url?: string;
  //TODO: for mock data, remove when backend ready
  data?: any[];
  filter?: {
    filterValue: Record<string, any>;
    filterMapping: { field: string; operator: string; valueField: string }[];
  };
  pick_local_fields?: string[];
};

type LayoutButton = {
  id: string;
  type: ButtonSchema['type'];
  title: string;
  icon: string;
  backendUrl?: string;
  disabled?: boolean;
  _disabled?: SourceField;
  href?: string;
  paramsRedirect?: any;
  _condition?: string;
  confirm?: {
    content?: string;
    title?: string;
    icon?: string;
    okText?: string;
    cancelText?: string;
  };
  condition_func?: string;
};

type PickField = {
  [key: string]: boolean | PickField;
};

type ActionManyHandler = {
  action: 'edit';
  pick_field?: PickField;
  form_config?: any;
  url?: string;
  title?: string;
  confirm?: {
    title?: string;
    content?: string;
    icon?: string;
    type?: string;
  };
  custom_body?: string;
  api_config?: ApiConfig;
};

type RowActionsHandler = Record<
  string,
  {
    action: 'edit';
    update_fields?: {
      [key: string]: NzSafeAny;
    };
    confirm?: {
      title?: string;
      content?: string;
    };
    _update_fields?: string;
    backendUrl?: string;
    _backendUrl?: string;
    apiConfig?: ApiConfig;
    keyValue?: {
      [key: string]: NzSafeAny;
    };
    method?: 'POST' | 'PUT' | 'PATCH';
    form?: {
      title?: string;
      config?: any;
    };
  }
>;

type WidgetHeaderOptions = {
  title?: string;
  duplicate?: boolean;
  edit?: boolean;
  delete?: boolean;
  visible?: boolean; // default true;
  _delete?: string;
  _edit?: string;
  _duplicate?: string;
};

type HistoryDialogOptions = {
  sidebar?: {
    header?: {
      visible?: boolean; // default true
      insertRecord?: boolean | { transform: string };
    };
  };
  content?: {
    widget?: {
      header?: WidgetHeaderOptions;
    };
  };
};

type FilterHistoryMethod = 'manual' | 'api';

type WidgetOptions = {
  empty?: {
    show_add_information?: boolean;
    is_proceed?: boolean;
    text?: string;
    sub_text?: string;
  };
  custom_data_transform?: string;
  show_more_type?: 'expand' | 'open-dialog' | 'hidden'; // default open-dialog
  show_more_content_type?: 'dialog' | 'widget-drawer';
  is_full_height?: boolean;
  refreshHeader?: boolean;
  refreshProfile?: boolean;
  refreshProfileIfDelete?: boolean;
  refeshDependent?: string[];
  hyperlinkInForm?: {
    type: 'navigation' | 'scrollToBlock';
    route: string;
    title: string;
  };
  condition_refresh_config?: string;
};

enum EApiMethod {
  GET = 'GET',
  POST = 'POST',
  PATCH = 'PATCH',
  PUT = 'PUT',
  DELETE = 'DELETE',
}

type TApiMethod = keyof typeof EApiMethod;

type ApiConfig = {
  url: string;
  method: TApiMethod;
  _url?: SourceField;
  _method?: SourceField;
  queryFilter?: QueryFilter[];
  _queryFilter?: SourceField;
  transform?: string; // use this to transform all config for api;
  messages?: {
    success?: string;
    error?: string;
  };
  body?: any;
  _body?: SourceField;
  isGetFile?: boolean;
};

type Toast = {
  type?: 'info' | 'warning' | 'success' | 'error'; // default info
  title?: string;
  text: string;
  icon?: string; // default icon info
};

type PrecheckDeleteApiConfig = ApiConfig & {
  confirm?: {
    title?: string;
    content?: string;
    _title?: SourceField;
    _content?: SourceField;
  };
  source?: Source;
};

type OptionalBooleanMap<T extends string> = {
  [K in T]?: boolean;
};

type ContextDataType = 'parent_data' | 'selected_items';

type ContextDataOptions = OptionalBooleanMap<ContextDataType>;

type DefaultLayoutOptions = {
  show_history_detail_function?: boolean;
  is_upload_file?: boolean;
  link_redirect?: string;
  show_modal?: boolean;
  title_proceed?: string;
  is_border?: boolean;
  is_reload_form?: boolean;
  is_show_length_pagination?: boolean;
  is_export_grid?: boolean;
  paddingTab?: boolean;
  header_buttons?: {
    id: string;
    type: ButtonSchema['type'];
    title: string;
    icon: string;
    href?: string;
    size?: string;
  }[];
  footer_buttons?: {
    id: string;
    type: ButtonSchema['type'];
    title: string;
    icon: string;
  }[];
  icon: string;
  widget_header_buttons?: Widget['headerButtons'];
  filterType: string;
  link_get_template_redirect?: string;
  n_cols: number;
  row_actions?: RowActionsData[];
  show_detail_history?: boolean;
  hide_detail_history_after_save?: boolean;
  show_dialog_form_save_add_button?: boolean;
  show_dialog_form_delete_button?: boolean;
  show_dialog_duplicate_button?: boolean; // default true
  form_mode: Record<string, string>;
  transfer_table?: TransferTable[];
  show_table_header_action?: boolean;
  show_history_insert_button?: boolean;
  show_history_search_bar?: boolean;
  show_table_filter?: boolean;
  show_table_group?: boolean;
  show_table_checkbox?: boolean;
  checkNoteBackendUrl?: string;
  checkEmployeeBackendUrl?: string;
  show_table_expandable_row?: boolean;
  show_create_data_table?: boolean;
  nested_row_type?: string;
  transform_group_row_of_nested_row?: string;
  has_addition_button_in_nested_row?: boolean;
  max_level_of_nested_row?: number;
  show_actions_delete?: boolean;
  label_of_addition_button_in_nested_row?: string;
  expand_filter?: boolean;
  no_need_confirm?: boolean;
  is_dynamic_config_table?: boolean;
  show_detail_drawer?: boolean;
  custom_title?: SourceField;
  view_detail_by_config?: {
    is_show: boolean;
    show_view_detail_by_key: string;
    value_to_show: string[];
  };
  export_report_method?: string;
  table_actions?: TableAction[];
  historyFilterMapping?: QueryFilter[];
  disabled_tab_key?: {
    key: string;
    tabs: string[];
  };
  is_load_pdf?: boolean;
  show_actions_many?: boolean;
  hide_action_row?: boolean;
  collaps_options: {
    history_collaps_container?: boolean;
    show_arrow_collaps: boolean;
    disabled_event_collaps: boolean;
  };
  tabs_title?: string[];
  link?: string;
  step_actions?: Record<
    string,
    {
      id: string;
      type: ButtonSchema['type'];
      title: string;
      icon: string;
    }[]
  >;
  //TODO: any is not a good type here
  tabs: NzSafeAny[];
  cvData: NzSafeAny[];
  toolTable?: {
    filter: boolean;
    operations: boolean;
    lock: boolean;
    export: boolean;
    adjustDisplay: boolean;
    synthetize: Record<string, any>;
    folderDisplay: boolean;
  };
  action_click_row?: {
    actionId: string;
    params: string;
  };
  folderDisplayForm?: NzSafeAny;
  previewForm?: NzSafeAny;
  modal_footer_buttons?: {
    id: string;
    type: ButtonSchema['type'];
    title: string;
    icon: string;
    conditionByKey?: string;
    conditionValue?: string;
  }[];
  edit_modal_footer_buttons?: {
    id: string;
    type: ButtonSchema['type'];
    title: string;
    icon: string;
    conditionByKey?: string;
    conditionValue?: string;
  }[];
  layout_detail_modal_footer_buttons?: {
    id: string;
    type: ButtonSchema['type'];
    title: string;
    icon: string;
    condition_func?: string;
  }[];
  table_search_placeholder: string;
  widget_table_filter: boolean;
  tabset?: TabItem[];
  previewTableTitle: string;
  titlePreviewPopup: string;
  typePreviewPopup: string;
  previewTabTable?: NzSafeAny[];
  previewTableCol?: NzSafeAny[];
  previewTableData?: NzSafeAny[];
  scrollValue: { x: string; y: string };
  actions_many?: LayoutButton[];
  actions_many_handler?: Record<string, ActionManyHandler>;
  layout_drag_drop?: Record<string, any>;
  export_dropdown?: NzSafeAny[];
  table_buttons?: LayoutButton[];
  row_actions_handler?: RowActionsHandler;
  tab_calculation_status?: string;
  widget_content_type?: 'descriptions' | 'readonly-form' | 'table-custom';
  disabled_click_row?: boolean;
  page_header_options?: {
    breadcrumb?: boolean; // default true,
    title?: boolean; // default true,
    buttons?: boolean; // default true,
    visible?: boolean; // default true,
    back_btn?: boolean; // default false,
  };
  page_footer_options: {
    visible?: boolean; // default
    show_credit?: boolean;
  };
  parent_path?: string;
  orgChartOptions?: {
    mode?: string;
    directKey: string;
    indirectKey: string;
  };
  tool_table: LayoutButton[];
  tool_table_context_data_options?: ContextDataOptions;
  key_set_tab_count?: NzSafeAny;
  drawer_view?: boolean;
  header_buttons_condition?: Record<
    string,
    {
      _disabled?: string;
      _unvisible?: string;
    }
  >;
  fsdIdProgressingPath?: string;
  show_table_search?: boolean;
  view_after_updated?: boolean;
  view_after_created?: boolean;
  view_history_after_created?: boolean;
  view_history_after_updated?: boolean;
  history_widget_header_options?: WidgetHeaderOptions;
  is_group_edit?: boolean;
  is_popup?: boolean;
  is_update_array?: boolean;
  custom_delete_backend_url?: string;
  custom_detail_backend_url?: string;
  group_data_history_by_key?: string;
  filter_history_method?: FilterHistoryMethod;
  duplicate_value_transform?: {
    fields: string[];
    transform: string;
  };
  show_employee_record_number?: boolean;
  show_dialog_footer: boolean | Record<string, boolean | Transform>;
  row_data_combine?: string[];
  row_type?: 'expand' | 'default';
  custom_history_backend_url?: string;
  support_search_date?: boolean;
  tabset_menu?: TabbarMenu[];
  show_history_cancel_button?: boolean;
  precondition_filter?: {
    custom_path?: SourceField;
    is_auto_filter?: boolean;
    is_auto_sync_filter?: boolean;
    auto_filter_take?: number;
    sync_filter_fields?: string[];
    form_settings?: {
      submit_button?: {
        type?: 'primary' | 'secondary';
        title?: string;
      };
      size?: 'auto-fit' | 'default'; // default is 300px
      border?: boolean;
      padding?: string;
    };
  };
  widget_options?: WidgetOptions;
  is_copy_data_insert_new?: boolean;
  children_full_width?: string[];
  delete_multi_items?: boolean;
  apply_delete_multi_items_to_delete_one?: boolean; // default false
  is_custom_insert_new_proceed?: boolean;
  filter_data_tableCustom?: string;
  show_history_filter_form?: boolean; // default false
  history_dialog_options?: HistoryDialogOptions;
  custom_get_list_api?: ApiConfig;
  show_tool_table?: boolean;
  show_table_pagination?: boolean;
  table_toasts?: Toast[];
  custom_update_api?: ApiConfig;
  custom_create_api?: ApiConfig;
  custom_view_detail_api?: ApiConfig;
  show_dialog_submit_button?: boolean;
  custom_submit_api?: ApiConfig;
  table_scroll_height?: string | number | 'auto';
  empty_state_style?: 'illustration' | 'default';
  precheck_delete_api?: PrecheckDeleteApiConfig;
  expand_create_form?: {
    submit_button_title?: string;
    form_size: 'auto-fit' | 'default'; // default is 300px
  };
  custom_delete_body?: string;
  skip_insert_new_proceed_history?: boolean;
  widget_history_type?: 'table' | 'form';
  show_navigate_to_contact_btn?: boolean;
  is_layout_widget?: boolean;
  employee_record_number_api_url?: string;
  ern_transfrom_data?: string;
  defaultTabsetIndex?: number;
  page_header_description?: string;
  skip_create_form?: boolean;
  custom_export_api?: ApiConfig & {
    selected_items_query_config?: {
      field: string;
      operator: ComparisonOperator;
      valueField: string;
    };
  };
  table?: {
    defaultCollapse?: boolean;
  };
  customStyleTabset?: boolean;
  progressing_info?: Record<string, NzSafeAny>;
  customStyleContent?: Record<string, NzSafeAny>;
  customStyleFormWrapper?: Record<string, NzSafeAny>;
  custom_rowId_for_view_detail?: string;
  hide_precondition_filter_if_empty?: boolean;
  apply_custom_delete_url_to_delete_in_history?: boolean;
  custom_value_before_edit?: string;
  profile_image_fsId?: string;
  data_type_values?: Record<string, NzSafeAny>;
  form_value_storage?: (ValueStorageConfig & { formType: string })[];
  is_layout_detail?: boolean;
  custom_message_on_submit?: {
    create: ViewMessageOnSubmits;
    update: ViewMessageOnSubmits;
  };
  schedule_config?: {
    api?: {
      url: string;
      method?: string;
      bodyTransform?: string;
    };
    calendarSettings?: {
      viewType?: 'month' | 'week' | 'period' | 'year';
    };
  };
  is_layout_profile?: boolean;
  ignore_check_accessType?: string[]; //[HR.FS.FR.009]
  is_check_permission_with_accessType?: boolean;
  get_children_action_permission?: boolean;
};

type ViewMessageOnSubmit = {
  message?: string;
  _message?: string;
  type?: 'toast' | 'dialog';
  _type?: string;
};

type ViewMessageOnSubmits = {
  success?: ViewMessageOnSubmit;
  error?: ViewMessageOnSubmit;
};

type ValueStorageConfig = {
  key: string;
};

type DialogFooter<T extends DialogFooterType> = {
  type: T;
  for: string[];
} & DialogFooterMapping[T];

type DialogFooterMapping = {
  step: DialogStepFooter;
  buttons: DialogButtonsFooter;
};

type DialogStepFooter = {
  showClearAll?: boolean;
  buttons: StepButtons;
};

type DialogButtonsFooter = {
  buttons: (LayoutButton & { action: string; authAction?: AuthActions })[];
};

type DialogFooterType = keyof DialogFooterMapping;

type StepButtons = {
  secondary?: {
    title: string;
    id: string;
    action: string;
  };
  primary?: {
    title: string;
    id: string;
    action: string;
  };
};

type TableLayoutOptions = DefaultLayoutOptions & {
  mapping_row_actions_state?: boolean;
  reset_page_index_after_do_action: Record<string, boolean>;
  export_all?: {
    type: 'base_total' | 'base_query';
  };
  dialog_footer?: DialogFooter<any>[];
  dialog_actions?: DialogAction[];
  show_filter_results_message?: boolean;
  data_group?: {
    group_api: {
      url: string;
    };
    group_details_api: {
      url: string;
    };
  };
  record_composite_key?: RecordCompositeKey;
  mapping_one_actions?: {
    history?: boolean;
    detail?: boolean;
  };
  initial_dialog_value?: {
    valueTransform: string;
    fromAction: string;
  }[];
  pre_check_calculate: string;
  store_selected_items?: boolean | { keyToCheck: string };
  precheck_action?: { action: string; api?: ApiConfig; expression: string }[];
  pre_check_action_click_config?: {
    api?: ApiConfig;
    expression: string;
  };
};

type RecordCompositeKey = {
  name: string;
  keys_mapping?: string[];
  join_by?: string;
};

type DialogAction = {
  id: string;
  type: 'api';
  config: ApiConfig;
  confirm?: {
    content: string;
    title: string;
  };
};

type TabsetLayoutOptions = DefaultLayoutOptions & {
  show_details_form?: boolean; // default false
  show_create_form?: boolean; // default false
  child_event_subjects?: { type: 'lock'; action: 'refresh' }[];
  default_selected_tab_index?: number;
};

type LayoutOptionsMapping = {
  default: TableLayoutOptions & TransformType<TableLayoutOptions>;
  'tabset-custom': TabsetCustomLayoutOptions;
  'transfer-table': TransferTableLayoutOptions;
  tabset: TabsetLayoutOptions;
};

type LayoutOptionsType = keyof LayoutOptionsMapping;

// for tabset custom layout options
type TabConfigMapping = {
  table: TableTab;
  form: FormTab;
};

type TabCommonConfig = {
  type: TabConfigType;
  id: string;
  title: string;
};

type TableTab = TabCommonConfig & {
  type: 'table';
  local_fields: FunctionSpec['local_fields'];
  form_config?: FunctionSpec['form_config'];
  filter_config?: FunctionSpec['filter_config'];
  pick_local_fields?: string[];
  tools?: {
    search?: boolean;
    filter?: boolean;
    export?: boolean;
    viewSettings?: boolean;
    addRecord?: {
      url: string;
      local_fields: LocalField[];
      title?: string;
    };
  };
  row_actions: {
    delete?: boolean;
  };
  action_many_form_config?: FunctionSpec['form_config'];
  action_many_update_field?: string;
  show_checkbox?: boolean;
};

type FormTab = TabCommonConfig & {
  type: 'form';
  form_config: FunctionSpec['form_config'];
  form_type: 'create' | 'edit';
};

type TabConfigType = keyof TabConfigMapping;
type TabsetCustomLayoutOptions = {
  tabset?: TabConfigMapping[TabConfigType][];
  custom_get_list_api?: ApiConfig;
  custom_update_api?: ApiConfig;
  distinct_record_by?: string[];
};

type Transform = {
  transform: string;
};

type TransformType<T> = {
  [Property in keyof T as `_${string & Property}`]?: Transform;
};

type TransferTableLayoutOptions = DefaultLayoutOptions & {
  _disabled_transfer_btns?: Transform;
};

type LocalField = {
  code: string;
  title: string;
  description: string;
  pinned: boolean;
  data_type: {
    key: string;
  };
  display_type: {
    key: string;
  };
  options?: {
    tabular?: {
      column_width?: number;
      align?: 'left' | 'center' | 'right';
    };
  };
  group?: string;
  href?: string;
  show_sort?: boolean;
  combine_keys?: string[];
  extra_config?: Record<string, NzSafeAny>;
};

type FunctionSpec<LOT extends LayoutOptionsType = 'default'> = {
  id?: string;
  title?: string;
  module?: string;
  menu_item?: MenuItem;
  layout?: string;
  backend_url?: string;
  key_detail?: string;
  detail_function_spec?: string;
  inherited_default_detail?: boolean;
  local_fields?: LocalField[];
  default_filter?: { name: string; operator: string; fieldValue: string }[];
  local_buttons?: {
    code: string;
    name: string;
    description: string;
    reference: {
      key: string;
    };
    trigger: {
      key: string;
    };
    note: string;
    group: string;
  }[];
  children?: FunctionSpec[];

  layout_options?: LayoutOptionsMapping[LOT];
  permission_key?: Record<string, string>[];

  //TODO: any is not a good type here
  mock_data?: any[];
  form_config?: any;
  // filter_config?: Record<string, any> & {
  //   filterMapping?: { field: string; operator: string; valueField: string }[];
  // };
  filter_config?: any;
  screen_design?: string;
  create_form?: any;
  payment_config?: any;
  multi_typing_config?: NzSafeAny;
  pre_condition_config?: NzSafeAny;
};

type Module = {
  id: string;
  name: string;
  icon: string;
};

type MenuItem = {
  id: string;
  title: string;
  icon: string;
  children: MenuItem[];
  sort: number;
  module: string | Module;
  parent: MenuItem | string;
  function_specs: string[] | FunctionSpec[];
  status: string;
  params: number;
  route_name: string;
};

type RawMenu = {
  id: string;
  title: string;
  icon: string;
  children: string[];
  menuChildren: RawMenu[];
  sort: number;
  module: string | Module;
  fsId: string;
  parent: MenuItem | string;
  function_specs: string[] | FunctionSpec[];
  route: string[];
  status: string;
  params: number;
  route_name: string;
};

type Schema = {
  function_specs: FunctionSpec[];
  modules: Module[];
  menu_items: MenuItem[];
};

interface Progressing {
  id: number;
  title: string;
  subTitle: string;
  code: string;
  status: 'progressing' | 'pending' | 'completed' | 'failed' | 'deprecated';
  progress: number;
  type: 'CALCULATION' | 'IDENTITY';
  createdAt?: number;
}

export type {
  FilterHistoryMethod,
  FunctionSpec,
  LayoutButton,
  MenuItem,
  Module,
  PickField,
  RawMenu,
  RowActionsData,
  Schema,
  TableAction,
  TransferTable,
  WidgetHeaderOptions,
  HistoryDialogOptions,
  ApiConfig,
  Toast,
  PrecheckDeleteApiConfig,
  Progressing,
  RowActionsHandler,
  LayoutOptionsType,
  TableTab,
  FormTab,
  TApiMethod,
  LocalField,
  TransformType,
  WidgetOptions,
  DialogFooter,
  DialogAction,
  DialogFooterType,
  ActionManyHandler,
  ValueStorageConfig,
  RecordCompositeKey,
  ContextDataOptions,
};
