import { inject, Injectable } from '@angular/core';
import {
  createDirectus,
  readItem,
  readItems,
  rest,
  withToken,
} from '@directus/sdk';
import { Menu, SidebarMenu } from '@hrdx/hrdx-design';
import {
  BehaviorSubject,
  catchError,
  combineLatest,
  filter,
  from,
  map,
  mergeMap,
  of,
  tap,
  toArray,
} from 'rxjs';

import { HttpClient } from '@angular/common/http';
import { Router } from '@angular/router';
import { unflatten } from 'flat';
import { isArray } from 'lodash';
import { NzSafeAny } from 'ng-zorro-antd/core/types';
import {
  AccountPermission,
  ConfigStore,
  ModuleStore,
  UserState,
  UserStore,
} from '../stores/layout.store';
import { FunctionSpec, MenuItem, RawMenu, Schema } from './masterdata.schema';
import { generateMockField } from './utils/generate-mock';
import { UtilService } from './util.service';
import { QueryFilter, RequestQueryBuilder } from '@nestjsx/crud-request';
function PRAMS_MAPPING(params: number) {
  switch (params) {
    case 0:
      return '';
    case 1:
      return ':id1';
    case 2:
      return ':id1/:id2';
    default:
      return ':id1/:id2';
  }
}

export type MenuType = {
  id: string;
  title: string;
  icon: string;
  status: string;
  module: string;
  route: string;
  parent: string;
  fsId: string;
  fsdFE?: string;
  sort: number;
  faceCode?: string;
  children: MenuType[] | null;
};

export type ActionPermission = {
  id: string;
  name: string;
  code: string;
  isActive: boolean;
  is2FA: boolean;
};
export type ChildrenActionPermission = {
  actions: ActionPermission[];
  faceCode: string;
  functionCode: string;
  functionId: number;
  fsdFE: string;
  name: string;
};

export type AuthActionsObj = {
  id: string;
  code: string;
};

export enum ResponseStatusCode {
  UNAUTHORIZED = 401,
}

export type ActionLog = {
  userId?: string;
  action: 'Login' | 'Logout';
  actionDate?: string | Date;
  timeZone?: string;
  locationName?: string;
  browserName?: string;
  ipAddress?: string;
  note?: string;
};

export function getValue(
  data: NzSafeAny,
  path: (string | number)[],
): NzSafeAny {
  if (!data) {
    return undefined;
  }
  if (path.length < 1) return undefined;
  const tmp = path.shift() ?? '';
  if (typeof tmp === 'string' && new RegExp(/\([a-z0-9]*\)/, 'g').test(tmp)) {
    const key = tmp.replace('(', '').replace(')', '');
    if (isArray(data)) {
      return data.map((it) => it[key]);
    } else {
      return data[key];
    }
  }
  if (path.length <= 0) {
    return data[tmp];
  } else {
    return getValue(data[tmp], path);
  }
}

function ROUTE_NAME_MAPPING(routeName: string, fs: string) {
  if (routeName) {
    return routeName;
  }
  if (fs) return fs;
  return '';
}
@Injectable({
  providedIn: 'root',
})
export class MasterdataService {
  readonly #TOKEN = '-FZjvzXkC6A9-M3i-ftj74ssCswwSESH';
  menusByModuleId = new BehaviorSubject<{ [key: string]: SidebarMenu[] }>({});
  #userStore = inject(UserStore);
  utilService = inject(UtilService);
  router = inject(Router);
  directus = createDirectus<Schema>('https://peoplex-tools.paas.ttgt.vn').with(
    rest(),
  );
  getConfig() {
    return this.#configStore;
  }

  reverseChildMenu(menu: Menu | undefined, id: string): Menu[] | undefined {
    if (!menu) return [];
    if (menu.fsdFE === id) {
      return menu.children;
    }
    if (menu.children) {
      for (const c of menu.children) {
        const res = this.reverseChildMenu(c, id);
        if (isArray(res) && res.length > 0) {
          return res;
        }
      }
    }
    return [];
  }
  getChildrenMenuByFunctionSpecId(id: string, moduleId: string) {
    const menus = this.menusByModuleId.value;
    for (const m of menus[moduleId] ?? []) {
      const res = this.reverseChildMenu(m, id);
      if (isArray(res) && res.length > 0) {
        return res;
      }
    }
    return [];
  }

  reverseMenu(menu: Menu | undefined, id: string): Menu | undefined {
    if (!menu) return undefined;
    if (menu.fsdFE === id) {
      return menu;
    }
    if (menu.children) {
      for (const c of menu.children) {
        const res = this.reverseMenu(c, id);
        if (res) {
          return res;
        }
      }
    }
    return undefined;
  }
  getMenuByFunctionSpecId(id: string, moduleId: string) {
    const menus = this.menusByModuleId.value;
    for (const m of menus[moduleId] ?? []) {
      const res = this.reverseMenu(m, id);
      if (res) {
        return res;
      }
    }
    return undefined;
  }

  getMenusByParent(parent: string) {
    return from(
      this.directus.request<MenuItem[]>(
        withToken(
          this.#TOKEN,
          readItems('menu_items', {
            fields: [
              'id',
              'title',
              'icon',
              'children',
              'function_specs',
              {
                children: ['id', 'title', 'icon', 'function_specs', 'status'],
              },
              'status',
            ],
            filter: {
              parent: parent,
            },
          }),
        ),
      ),
    ).pipe(
      mergeMap((r) => r),
      filter((r) => {
        if (location.hostname === 'mngwebuat-ppx.fpt.com') {
          return r.status === 'published';
        } else return r.status !== 'archived';
      }),
      map((r) => {
        return {
          id: r.id,
          title: r.title,
          icon: r.icon,
          children: r.children
            .filter((c) => {
              if (location.hostname === 'mngwebuat-ppx.fpt.com') {
                return c.status === 'published';
              } else return c.status !== 'archived';
            })
            .map((c) => ({
              id: c.id,
              title: c.title,
              icon: c.icon,
              sort: c.sort,
              moduleId: c.function_specs[0],
              route: [
                '/',
                r.module,
                r.id,
                ...c.function_specs.map((f) => f),
              ].join('/'),
            })),
        } as SidebarMenu;
      }),
      toArray(),
    );
  }
  http = inject(HttpClient);
  #moduleStore = inject(ModuleStore);
  #configStore = inject(ConfigStore);

  setConfig(config: any) {
    this.#configStore.setConfig(config);
  }
  private getAllMenus() {
    if (this.#configStore.menu().loadFromBackend)
      return this.http.get<MenuType[]>('/api/menus/permissions').pipe(
        catchError((err) => {
          console.error('error', err);
          this.router.navigate(['/not-found']);
          return of([] as MenuType[]);
        }),
        tap((r) => {
          this.#moduleStore.setModules(
            r.map((m) => ({
              id: m.id,
              name: m.title,
              icon: '',
            })),
          );
        }),
        map((r) => {
          return r.reduce(
            (acc, cur) =>
              cur.children
                ? [
                    ...acc,
                    ...cur.children.map((c) => {
                      c.route = c.route ? c.route : `${c.module}`;
                      return c;
                    }),
                  ]
                : acc,
            [] as MenuType[],
          );
        }),
        // map((r) => {
        //   const newMenu = {
        //     id: 'fakeMenu',
        //     title: 'SYS_016 TEST',
        //     status: 'published',
        //     module: 'SYS',
        //     route: '/SYS/SYS.FS.FR.13_01_clone',
        //     parent: '19',
        //     sort: 37,
        //     fsId: 'SYS_016',
        //     fsdFE: 'SYS.FS.FR.13_01_clone',
        //     faceCode: 'ee8927d946384d2d8c91',
        //   } as MenuType;

        //   r[17]?.children?.push(newMenu);

        //   return r;
        // }),
        // tap((r) => console.log(r)),
        // map((r) => {
        //   r[18].children![0].route = '/SYS/SYS.FR.06_01_check';
        //   r[18].children![0].fsdFE = 'SYS.FR.06_01_check';
        //   return r;
        // }),
        tap((r) => console.log(r)),
      );
    return from(
      this.directus.request<RawMenu[]>(
        withToken(
          this.#TOKEN,
          readItems('menu_items', {
            fields: [
              'id',
              'title',
              'icon',
              'params',
              'children',
              'function_specs',
              'children',
              'status',
              'module',
              'sort',
              'parent',
              'route_name',
            ],
            filter: {
              // module: { _nnull: true },
              status:
                location.hostname === 'mngwebuat-ppx.fpt.com'
                  ? {
                      _eq: 'published',
                    }
                  : { _neq: 'archived' },
            },
            limit: 1000,
          }),
        ),
      ),
    ).pipe(
      mergeMap((r) => r),
      toArray(),
      map((r) => {
        return r.sort((a, b) => {
          if (a.parent === null && b.parent === null) return 0;
          if (a.parent === null) return -1;
          if (b.parent === null) return 1;

          if (a.children.includes(b.id)) {
            return -1;
          }
          if (b.children.includes(a.id)) {
            return 1;
          }
          if (a.children.length > 0 && b.children.length === 0) {
            return -1;
          }
          if (b.children.length > 0 && a.children.length === 0) {
            return 1;
          }
          return 0;
        });
      }),
      map((r) => {
        const res: RawMenu[] = [];
        r.forEach((m) => {
          // check
          if (res.some((rm) => this.checkChild(rm, m))) {
            return;
          } else {
            res.push({
              ...m,
              route: [
                '/',
                m.module as string,
                ROUTE_NAME_MAPPING(m.route_name, m.function_specs[0] as string),
                PRAMS_MAPPING(m.params),
              ].filter((r) => r),
              fsId: m.function_specs[0] as string,
            });
          }
        });
        return res.map((m) => {
          return {
            ...m,
            children: m.menuChildren,
          };
        });
        // return res as any as MenuItem[];
      }),
      map((m) => this.transformMenus(m)),
      map((m) => m.filter((r) => r.parent === null)),
      tap((m) => console.log(m)),
    );
  }

  transformMenus(menus: any[]): MenuType[] {
    return menus.map((m) => ({
      id: m.id,
      title: m.title,
      icon: m.icon,
      status: m.status,
      module: m.module,
      route:
        m.route.length > 2
          ? '/' + m.route.slice(1).join('/')
          : m.route.slice(1).join('/'),
      parent: m.parent,
      fsId: m.fsId,
      fsdFE: m.fsId,
      sort: m.sort,
      children: m.children ? this.transformMenus(m.children) : null,
    }));
  }

  checkChild(parent: RawMenu, child: RawMenu) {
    if (!parent) {
      console.error('parent is null');
      return false;
    }
    const idx = parent.children
      ? parent.children.findIndex((c) => c === child.id)
      : -1;
    if (idx !== -1) {
      if (!parent.menuChildren) {
        parent.menuChildren = [];
      }
      child.route = [
        ...(parent.route as string[]),
        ROUTE_NAME_MAPPING(child.route_name, child.function_specs[0] as string),
        PRAMS_MAPPING(child.params),
      ].filter((r) => r);
      child.fsId = child.function_specs[0] as string;
      parent.menuChildren[idx] = child;
      return true;
    }
    for (const c of parent.menuChildren ?? []) {
      if (this.checkChild(c, child)) {
        return true;
      }
    }
    return false;
  }

  // private _getMenusByModuleId(moduleId: string, parent?: string) {
  //   return from(
  //     this.directus.request<MenuItem[]>(
  //       withToken(
  //         this.#TOKEN,
  //         readItems('menu_items', {
  //           fields: [
  //             'id',
  //             'title',
  //             'icon',
  //             'children',
  //             'function_specs',
  //             {
  //               children: [
  //                 'id',
  //                 'title',
  //                 'icon',
  //                 'sort',
  //                 'function_specs',
  //                 'status',
  //               ],
  //             },
  //             'status',
  //           ],
  //           filter: {
  //             module: moduleId,
  //             parent: parent ? parent : { _null: true },
  //           },
  //         }),
  //       ),
  //     ),
  //   ).pipe(
  //     mergeMap((r) => r),
  //     filter((r) => r.status != 'archived'),
  //     map((r) => {
  //       return {
  //         moduleId: moduleId,
  //         id: r.id,
  //         title: r.title,
  //         icon: r.icon,
  //         children: r.children
  //           .filter((c) => c.status !== 'archived')
  //           .map((c) => ({
  //             id: c.id,
  //             title: c.title,
  //             icon: c.icon,
  //             sort: c.sort,
  //             moduleId: moduleId,
  //             fsId: c.function_specs[0],
  //             route: ['/', moduleId, ...c.function_specs.map((f) => f)],
  //           })),
  //       } as SidebarMenu;
  //     }),
  //     toArray(),
  //   );
  // }
  private mappingSidebarMenu(menu: MenuType): SidebarMenu {
    return {
      id: menu.id,
      title: menu.title,
      icon: menu.icon,
      moduleId: menu.module as string,
      route: menu.fsId ? menu.route : undefined,
      parent: menu.parent as string,
      fsId: menu.fsId,
      fsdFE: menu.fsdFE,
      faceCode: menu.faceCode,
      sort: menu.sort,
      children: menu.children?.map((c: MenuType) => this.mappingSidebarMenu(c)),
    } as SidebarMenu;
  }
  initModule() {
    return this.getAllMenus().pipe(
      map((r) => {
        return r.reduce(
          (acc, cur) => {
            if (typeof cur.module === 'string') {
              const menu = this.mappingSidebarMenu(cur);

              const idx = acc.findIndex((m) => m.key === cur.module);
              if (idx === -1) {
                acc.push({ key: cur.module, value: [menu] });
              } else {
                acc[idx].value.push(menu);
              }
            }
            return acc;
          },
          [] as { key: string; value: SidebarMenu[] }[],
        );
      }),
      tap((r) => {
        const menus = r.reduce(
          (acc, cur) => {
            acc[cur.key] = cur.value;
            return acc;
          },
          {} as { [key: string]: SidebarMenu[] },
        );
        this.menusByModuleId.next(menus);
      }),
      // switchMap((r) => this.getUserByMe().pipe(map(() => r))),
    );
  }

  async getUserByMe() {
    const _something = await this.utilService.getDecryptedCookie('ppx');
    // if (_something) {
    //   const _user = JSON.parse(_something);
    //   this.#userStore.setUser((_user as UserState) ?? undefined);
    //   return of(_user as UserState);
    // }

    return this.http.get<UserState>('/api/me').pipe(
      catchError((err) => {
        console.error('error', err);
        if (err.status === ResponseStatusCode.UNAUTHORIZED) {
          // const url = this.router.url;
          this.router.navigate(['/auth/unauthorized'], {
            queryParams: { code: 401, message: err?.error?.message ?? '' },
          });
        } else {
          if (_something) {
            const _user = JSON.parse(_something);
            return of(_user as UserState);
          }
          this.router.navigate(['/not-found']);
        }
        return of(null);
      }),
      tap(async (user) => {
        this.#userStore.setUser(user ?? undefined);
        await this.utilService.setEncryptedCookie(
          'ppx',
          JSON.stringify(this.#userStore.user()),
          24,
        );
      }),
    );
  }

  logAction(actionData: ActionLog) {
    return this.http.post<ActionLog>('/api/action-logs', actionData).pipe(
      catchError((err) => {
        console.error('log actions error', err);
        if (err.status === ResponseStatusCode.UNAUTHORIZED) {
          this.router.navigate(['/auth/unauthorized'], {
            queryParams: { code: 401, message: err?.error?.message ?? '' },
          });
        }
        return of(null);
      }),
      tap((log) => {
        if (log) {
          console.log('log action success', log);
        }
      }),
    );
  }

  getMenusByModuleId(moduleId: string) {
    return this.menusByModuleId.pipe(map((r) => r[moduleId] ?? []));
  }

  // k v
  collectionMapping: { [key: string]: any } = {
    function_specs: {
      fields: [
        '*',
        {
          children: ['*'],
        },
        {
          menu_item: ['*', { parent: ['title'] }],
        },
      ],
      filter: {
        // status: 'published',
      },
    },
  };

  getOne<T>(collection: keyof Schema, id: string) {
    const config = this.collectionMapping[collection];
    return from(
      this.directus.request<T>(
        withToken(
          this.#TOKEN,
          readItem(collection, id, {
            fields: config.fields,
          }),
        ),
      ),
    ).pipe(
      map((r) => {
        return unflatten(r, { delimiter: '__' }) as T;
      }),
    );
  }
  reversePath(menu: Menu | undefined, id: string): ReversePath[] {
    if (!menu) return [];
    if (menu.fsdFE === id) {
      return [
        {
          title: menu.title ?? '',
          route: menu.route,
        },
      ];
    }
    if (menu.children) {
      for (const c of menu.children) {
        const res = this.reversePath(c, id);
        if (res.length > 0) {
          return [
            {
              title: menu.title ?? '',
              route: menu.route,
              children: menu.children?.map((subMenu) => ({
                title: subMenu.title,
                route: subMenu.route,
              })),
            },
            ...res,
          ];
        }
      }
    }
    return [];
  }

  private customModuleName(moduleId: string): string {
    const moduleNames: { [key in ModuleId]?: ModuleName } = {
      [ModuleId.GE]: ModuleName.GE,
      [ModuleId.HR]: ModuleName.HR,
    };
    return moduleNames[moduleId as ModuleId] ?? moduleId;
  }

  getPathByFunctionSpecId(id: string, moduleId: string): ReversePath[] {
    const menus = this.menusByModuleId.value;
    for (const m of menus[moduleId] ?? []) {
      const res = this.reversePath(m, id);
      if (res.length > 0) {
        const path = [
          { title: this.customModuleName(moduleId), route: moduleId },
          ...res,
        ];
        return path;
      }
    }
    return [];
  }
  getFunctionSpecById(id: string) {
    // const moduleId = this.layoutStore.currentModuleId();
    const moduleId = id.split('.')[0].toLocaleLowerCase();
    return this.http.get<FunctionSpec>(`/api/metadata/${moduleId}/${id}`).pipe(
      map((r) => {
        return unflatten(r, { delimiter: '__' }) as FunctionSpec;
      }),
      // switchMap((res) => {
      //   const children =
      //     res?.children && res.children.length > 0
      //       ? forkJoin(
      //           res.children.map((c) => {
      //             return this.http
      //               .get<FunctionSpec>(`/api/metadata/${moduleId}/${c}`)
      //               .pipe(
      //                 map((r) => {
      //                   return unflatten(r, {
      //                     delimiter: '__',
      //                   }) as FunctionSpec;
      //                 }),
      //               );
      //           }),
      //         )
      //       : of([]);
      //   return combineLatest({
      //     raw: of(res),
      //     children: children,
      //   }).pipe(
      //     map(({ raw, children }) => {
      //       return { ...raw, children: children } as FunctionSpec;
      //     }),
      //     catchError((err) => {
      //       return of({} as FunctionSpec);
      //     }),
      //   );
      //   // return of(res);
      // }),
      catchError((err) => {
        return of({} as FunctionSpec);
      }),
    );
  }

  getPermissionActionForDetail(id: string, filter: QueryFilter[] = []) {
    const qb = RequestQueryBuilder.create();
    qb.search({
      $and: filter.map((it) => ({
        [it.field]: {
          [it.operator]: it.value,
        },
      })),
    });
    const queryString = qb.query();

    return combineLatest({
      actions: this.http.get<ActionPermission[]>(
        `/api/actions-sys/detail-actions/${id}?${queryString}`,
        {
          headers: { 'Ppx-function-code': id },
        },
      ),
      authActions: this.http
        .get<{
          data: AuthActionsObj[];
        }>('/api/actions-sys', { headers: { 'Ppx-function-code': id } })
        .pipe(
          map((response) => response.data),
          catchError(() => of([] as AuthActionsObj[])),
        ),
      children: this.http
        .get<
          ChildrenActionPermission[]
        >(`/api/actions-sys/function-actions/${id}?${queryString}`, { headers: { 'Ppx-function-code': id } })
        .pipe(catchError(() => of([] as ChildrenActionPermission[]))),
      accountPermissions: this.http
        .get<AccountPermission>('/api/account-permissions', {
          headers: { 'Ppx-function-code': id },
        })
        .pipe(catchError(() => of(undefined as AccountPermission))),
    });
  }

  getChidrenFunction(id: string, filter: QueryFilter[] = []) {
    const qb = RequestQueryBuilder.create();
    qb.search({
      $and: filter.map((it) => ({
        [it.field]: {
          [it.operator]: it.value,
        },
      })),
    });
    const queryString = qb.query();
    return this.http
      .get<
        ChildrenActionPermission[]
      >(`/api/actions-sys/function-actions/${id}?${queryString}`, { headers: { 'Ppx-function-code': id } })
      .pipe(catchError(() => of([] as ChildrenActionPermission[])));
  }

  getPermissionActionForEmployeeProfile(
    id: string,
    filter: QueryFilter[] = [],
    empId: string,
  ) {
    const qb = RequestQueryBuilder.create();
    qb.search({
      $and: filter.map((it) => ({
        [it.field]: {
          [it.operator]: it.value,
        },
      })),
    });
    const queryString = qb.query();

    return combineLatest({
      actions: this.http.get<ActionPermission[]>(
        `/api/actions-sys/detail-actions/${id}?${queryString}`,
        {
          headers: { 'Ppx-function-code': id },
        },
      ),
      authActions: this.http
        .get<{
          data: AuthActionsObj[];
        }>('/api/actions-sys', { headers: { 'Ppx-function-code': id } })
        .pipe(
          map((response) => response.data),
          catchError(() => of([] as AuthActionsObj[])),
        ),
      children: this.http
        .get<
          ChildrenActionPermission[]
        >(`/api/actions-sys/function-actions/${id}?${queryString}`, { headers: { 'Ppx-function-code': id } })
        .pipe(catchError(() => of([] as ChildrenActionPermission[]))),
      accountPermissions: this.http
        .get<AccountPermission>('/api/account-permissions', {
          headers: { 'Ppx-function-code': id },
        })
        .pipe(catchError(() => of(undefined as AccountPermission))),
      accessType: this.http
        .get<string>(`/api/personals/${empId}/get-access-type`, {
          headers: { 'Ppx-function-code': id },
        })
        .pipe(
          catchError(() => of(undefined as any)),
          map((data: { accessType: string }) => data?.accessType),
        ),
    });
  }

  getPermissionActionById(id: string) {
    return combineLatest({
      actions: this.http.get<ActionPermission[]>(`/api/menus/${id}/actions`, {
        headers: { 'Ppx-function-code': id },
      }),
      authActions: this.http
        .get<{
          data: AuthActionsObj[];
        }>('/api/actions-sys', { headers: { 'Ppx-function-code': id } })
        .pipe(
          map((response) => response.data),
          catchError(() => of([] as AuthActionsObj[])),
        ),
      children: this.http
        .get<
          ChildrenActionPermission[]
        >(`/api/menus/${id}/children`, { headers: { 'Ppx-function-code': id } })
        .pipe(catchError(() => of([] as ChildrenActionPermission[]))),
      accountPermissions: this.http
        .get<AccountPermission>('/api/account-permissions', {
          headers: { 'Ppx-function-code': id },
        })
        .pipe(catchError(() => of(undefined as AccountPermission))),
    });
  }

  generateMockData(fields: FunctionSpec['local_fields'], data: any[] = []) {
    return data.map((d, i) => {
      for (const field of fields ?? []) {
        if (d[field.code] === undefined) {
          d[field.code] = generateMockField(field.data_type?.key ?? 'default');
        }
      }

      return { ...d, id: i, index: i };
    });
  }
}

interface ReversePath {
  title?: string;
  route?: string;
  children?: ReversePath[];
}

enum ModuleId {
  GE = 'GE',
  ADC = 'ADC',
  FO = 'FO',
  HR = 'HR',
  INS = 'INS',
  PIT = 'PIT',
  PR = 'PR',
  SYS = 'SYS',
  TS = 'TS',
}

//define more when needed
enum ModuleName {
  GE = 'General',
  HR = 'HRM',
}

export type StrictBooleanKeys<T> = {
  [K in keyof T]: T[K] extends boolean ? K : never;
}[keyof T];
