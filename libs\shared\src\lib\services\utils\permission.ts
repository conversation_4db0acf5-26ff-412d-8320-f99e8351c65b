import { flatMap, isNil } from 'lodash';
import { ActionPermission, StrictBooleanKeys } from '../masterdata.service';

export type ACTION_TYPE =
  | 'create'
  | 'edit'
  | 'edit-custom'
  | 'delete'
  | 'view'
  | 'export'
  | 'import'
  | 'history'
  | 'calculate'
  | 'integrate'
  | 'duplicate'
  | 'multi-typing'
  | 'lock'
  | 'unlock'
  | 'synthesize'
  | 'addperson'
  | 'export-template'
  | 'export-data'
  | 'proceed'
  | 'new-schedule'
  | 'generate-report'
  | 'generateReport'
  | 'add-category'
  | 'reCalculate'
  | 'folder'
  | 'add-action-jobdata'
  | 'add-ern-jobdata'
  | 'add-oir-jobdata'
  | 'add-classify-ter-jobdata'
  | 'deactive'
  | 'reset'
  | 'apply'
  | 'cancel-schedule'
  | 'run-schedule'
  | 'download-schedule'
  | 'edit-schedule'
  | 'note'
  | 'rehire'
  | 'addJob'
  | 'hire';

export const allPermissionBE = [
  'Read',
  'Create',
  'Update',
  'Delete',
  'Import',
  'ExportGrid',
  'ExportTemplate',
  'GenerateReport',
  'Calculation',
  'History',
  'Lock',
  'Unlock',
  'Schedule',
  'Categoryas',
  'CreateAction',
  'CreateERN',
  'CreateOIR',
  'ClassifyTermination',
];

export function getPermissionMapping(permissionBE: string): ACTION_TYPE[] {
  switch (permissionBE) {
    case 'Create':
      return [
        'create',
        'duplicate',
        'addperson',
        'proceed',
        'rehire',
        'addJob',
        'hire',
      ];
    case 'Update':
      return [
        'edit',
        'multi-typing',
        'edit-custom',
        'deactive',
        'apply',
        'reset',
        'cancel-schedule',
        'note',
      ];
    case 'Delete':
      return ['delete'];
    case 'Import':
      return ['import'];
    case 'ExportGrid':
      return ['export'];
    case 'ExportTemplate':
      return ['export-template', 'export-data'];
    case 'GenerateReport':
      return ['generate-report', 'generateReport', 'run-schedule'];
    case 'Calculation':
      return ['calculate', 'synthesize', 'reCalculate', 'integrate'];
    case 'History':
      return ['history'];
    case 'Lock':
      return ['lock'];
    case 'Unlock':
      return ['unlock'];
    case 'Schedule':
      return ['new-schedule', 'edit-schedule'];
    case 'Categoryas':
      return ['add-category', 'folder'];
    case 'CreateAction':
      return ['add-action-jobdata'];
    case 'CreateERN':
      return ['add-ern-jobdata'];
    case 'CreateOIR':
      return ['add-oir-jobdata'];
    case 'ClassifyTermination':
      return ['add-classify-ter-jobdata'];
    case 'Read':
      return ['view', 'download-schedule'];
    default:
      return [];
  }
}

export function flatMapPermissions(permissionsBE: string[]) {
  return flatMap<ACTION_TYPE>(
    permissionsBE.map((pa) => getPermissionMapping(pa)),
  ).filter((pa) => !isNil(pa));
}

export function getReAuthPermission(permissionAction: ActionPermission[]) {
  const reAuthPermissionBE = allPermissionBE.filter(
    (pa) => permissionAction?.find((p) => p.code === pa)?.is2FA,
  );

  return flatMapPermissions(reAuthPermissionBE);
}

export const checkActionByBooleanKey = (
  action: ACTION_TYPE,
  permissionAction: ActionPermission[],
  booleanKey: StrictBooleanKeys<ActionPermission>,
) => {
  const permissionFound = permissionAction.find((p) =>
    getPermissionMapping(p.code).includes(action),
  );
  return !!permissionFound?.[booleanKey];
};

export function isActionNeedReAuth(
  action: ACTION_TYPE,
  permissionAction: ActionPermission[],
) {
  return checkActionByBooleanKey(action, permissionAction, 'is2FA');
}

export function isActionActive(
  action: ACTION_TYPE,
  permissionAction: ActionPermission[],
) {
  return checkActionByBooleanKey(action, permissionAction, 'isActive');
}

export function getDisabledPermission(permissionAction: ActionPermission[]) {
  const disabledPermissionBE = allPermissionBE.filter(
    (pa) => !permissionAction?.find((p) => p.code === pa)?.isActive,
  );

  const tmp = flatMapPermissions(disabledPermissionBE);

  // if (
  //   permissionAction?.filter((p) =>
  //     ['Read', 'Update', 'Delete', 'Create'].includes(p.name),
  //   ).length < 4
  // ) {
  //   tmp.push('folder');
  // }
  return tmp;
}

export function overwritePermissionAction(
  permissionAction: ActionPermission[],
  accessType?: string,
) {
  if (accessType === 'Read') {
    return permissionAction.map((item: any) => {
      if (['Update', 'Create', 'Delete'].includes(item.code)) {
        return {
          ...item,
          isActive: false,
        };
      }
      return item;
    });
  }
  return permissionAction;
}

export enum JobdataFunctionCode {
  ADD_ERN = 'HR_050;HR_035;HR_036;HR_037;HR_038;HR_040;HR_041;HR_042;HR_043;HR_044;HR_046;HR_039,1',
  ADD_CLASSIFY_TER = 'HR_050;HR_035;HR_036;HR_037;HR_038;HR_040;HR_041;HR_042;HR_043;HR_044;HR_046;HR_039,2',
}

export const OIR_MENU_IDS = [
  'HR.FS.FR.046_01',
  'HR.FS.FR.046_02',
  'HR.FS.FR.046_03',
];

export enum JobdataPermissionCodes {
  CREATE = 'create',
  EDIT = 'edit',
  DELETE = 'delete',
  ADD_ACTION_JOBDATA = 'add-action-jobdata',
  ADD_ERN_JOBDATA = 'add-ern-jobdata',
  ADD_OIR_JOBDATA = 'add-oir-jobdata',
  ADD_CLASSIFY_TER_JOBDATA = 'add-classify-ter-jobdata',
}

export enum BasicInfoFunctionCode {
  SOCIAL_NAME = 'HR_001,1',
  SPECIAL_NAME = 'HR_001,2',
}
