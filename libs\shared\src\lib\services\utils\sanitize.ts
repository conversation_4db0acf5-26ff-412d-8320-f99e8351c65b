// Recursive sanitizer for dangerous input in JSON objects/arrays
// Removes <script> tags, event handler attributes, dangerous URLs, and base64-encoded scripts

const DANGEROUS_HTML_REGEX = /<\s*script.*?>[\s\S]*?<\s*\/\s*script\s*>/gi;
const DANGEROUS_EVENT_ATTR_REGEX = /<[^>]+\s(on\w+)\s*=\s*(['"]).*?\2[^>]*>/gi;
const DANGEROUS_URL_REGEX = /((javascript|data):[^\s)"'>]+)/gi;
const DANGEROUS_BASE64_REGEX = /data:text\/html;base64,[a-z0-9+/=]+/gi;

function sanitizeString(str: string): string {
  let sanitized = str;
  // Remove <script> tags
  sanitized = sanitized.replace(DANGEROUS_HTML_REGEX, '');
  // Remove event handler attributes (e.g., onmouseover)
  sanitized = sanitized.replace(DANGEROUS_EVENT_ATTR_REGEX, (match) => {
    // Remove the event attribute only, keep the rest of the tag
    return match.replace(/\s(on\w+)\s*=\s*(['"]).*?\2/, '');
  });
  // Remove dangerous URLs in markdown/HTML
  sanitized = sanitized.replace(DANGEROUS_URL_REGEX, '#');
  // Remove base64-encoded scripts
  sanitized = sanitized.replace(DANGEROUS_BASE64_REGEX, '#');
  // Remove unicode-encoded <script> (e.g., \u0061lert)
  sanitized = sanitized.replace(/<script.*?>[\s\S]*?<\/script>/gi, '');
  return sanitized;
}

export function sanitizeDangerousInput(obj: any): any {
  if (Array.isArray(obj)) {
    return obj.map(sanitizeDangerousInput);
  } else if (obj && typeof obj === 'object') {
    return Object.entries(obj).reduce((acc, [k, v]) => {
      acc[k] = sanitizeDangerousInput(v);
      return acc;
    }, {} as any);
  } else if (typeof obj === 'string') {
    return sanitizeString(obj);
  }
  return obj;
} 