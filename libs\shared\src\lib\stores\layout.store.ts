import { inject } from '@angular/core';
import { HeaderMenuItem, SidebarMenu } from '@hrdx/hrdx-design';
import {
  patchState,
  signalStore,
  withMethods,
  withState,
  WritableStateSource,
} from '@ngrx/signals';
import { rxMethod } from '@ngrx/signals/rxjs-interop';
import { isBoolean } from 'lodash';
import { combineLatest, mergeMap, of, pipe, tap } from 'rxjs';
import { AuthActionsObj, MasterdataService } from '../services';

export type PermissionKey = { name: string; defaultName: string };

type LayoutState = {
  menus: SidebarMenu[];
  currentModuleId: HeaderMenuItem['id'];
  currentFaceCode: string | undefined;
  currentFunctionCode: string | undefined;
  authActions: AuthActionsObj[] | undefined;
  currentAuthAction: string | undefined;
  loading: boolean;
  // use for case details page
  detailData: any;
  isLayoutDetail: boolean;
  permissionKeys: PermissionKey[];
};

const initialState: LayoutState = {
  menus: [],
  currentModuleId: '',
  currentFunctionCode: undefined,
  currentFaceCode: undefined,
  authActions: undefined,
  currentAuthAction: undefined,
  loading: false,
  detailData: undefined,
  isLayoutDetail: false,
  permissionKeys: [],
};

const updatePartialState = <
  K extends keyof LayoutState,
  T extends LayoutState[K],
>(
  stores: WritableStateSource<LayoutState>,
  key: K,
) =>
  rxMethod<T>(
    pipe(
      tap((value) => {
        patchState(stores, {
          [key]: value,
        });
      }),
    ),
  );

export const LayoutStore = signalStore(
  { providedIn: 'root' },
  withState(initialState),
  withMethods((stores, masterdataService = inject(MasterdataService)) => ({
    selectModule: rxMethod<string>(
      pipe(
        mergeMap((moduleId) =>
          combineLatest({
            menus: masterdataService.getMenusByModuleId(moduleId),
            moduleId: of(moduleId),
          }),
        ),
        tap(({ menus, moduleId }) => {
          patchState(stores, {
            menus,
            currentModuleId: moduleId,
          });
        }),
      ),
    ),
    setFaceCode: updatePartialState(stores, 'currentFaceCode'),
    setAuthActions: updatePartialState(stores, 'authActions'),
    setCurrentAuthAction: updatePartialState(stores, 'currentAuthAction'),
    setLoading: updatePartialState(stores, 'loading'),
    setDetailData: updatePartialState(stores, 'detailData'),
    setLayoutDetail: updatePartialState(stores, 'isLayoutDetail'),
    setPermissionKeys: updatePartialState(stores, 'permissionKeys'),
  })),
);

type ModuleState = {
  modules: HeaderMenuItem[];
};

const initialModuleState: ModuleState = {
  modules: [
    {
      id: 'SYS',
      name: 'System',
      icon: 'hard-drives-bold',
      // disabled: true,
    },
    {
      id: 'TS',
      name: 'TMS',
      icon: 'calendar-check-bold',
      // disabled: true,
    },
    {
      id: 'PR',
      name: 'PRM',
      icon: 'currency-circle-dollar-bold',
    },
    {
      id: 'FO',
      name: 'Foundation',
      icon: 'stack-simple-bold',
    },
    {
      id: 'HR',
      name: 'HRM',
      icon: 'stack-simple-bold',
    },
    {
      id: 'PIT',
      name: 'PIT',
      icon: 'scroll-bold',
      // disabled: true,
    },
    {
      id: 'COM',
      name: 'Common',
      icon: 'scroll-bold',
      // disabled: true,
    },
    {
      id: 'ELN',
      name: 'E-Learning',
      icon: 'scroll-bold',
      // disabled: true,
    },
    {
      id: 'OB',
      name: 'Onboarding',
      icon: 'airplane-tilt-bold',
      disabled: true,
    },
    {
      id: 'General',
      name: 'General',
      icon: 'general-thumb',
      // disabled: true,
    },
    {
      id: 'INS',
      name: 'Insurance',
      icon: 'general-thumb',
      // disabled: true,
    },
  ],
};

export const ModuleStore = signalStore(
  { providedIn: 'root' },
  withState(initialModuleState),
  withMethods((stores) => ({
    setModules: rxMethod<HeaderMenuItem[]>(
      pipe(
        tap((modules) => {
          patchState(stores, {
            modules,
          });
        }),
      ),
    ),
  })),
);

export type UserState =
  | {
      avatarFile?: string;
      userId?: string;
      userName?: string;
      email?: string;
      firstName?: string;
      lastName?: string;
      fullName?: string;
      companyCode?: string;
      requestingLanguageCode?: string;
      employeeCode?: string;
      departmentCode?: string;
      divisionCode?: string;
      businessTitleCode?: string;
      legalEntityCode?: string;
    }
  | undefined;

export type AccountPermission =
  | {
      permissionType: number;
      permissionTypeName: string;
    }
  | undefined;

const initialUserState: {
  user: UserState;
} = {
  user: undefined,
};
export const UserStore = signalStore(
  { providedIn: 'root' },
  withState(initialUserState),
  withMethods((stores) => ({
    setUser: rxMethod<UserState>(
      pipe(
        tap((user) => {
          patchState(stores, { user });
        }),
      ),
    ),
  })),
);

type ConfigState = {
  menu: {
    loadFromBackend: boolean;
    getActionByRow: boolean;
  };
  auth: {
    authority: string | undefined;
    clientId: string | undefined;
    secureRoutes: string[];
  };
};

const initialConfigState: ConfigState = {
  menu: {
    loadFromBackend: false,
    getActionByRow: false,
  },
  auth: {
    authority: undefined,
    clientId: undefined,
    secureRoutes: [],
  },
};

export const ConfigStore = signalStore(
  { providedIn: 'root' },
  withState(initialConfigState),
  withMethods((stores) => ({
    setConfig: rxMethod<ConfigState>(
      pipe(
        tap((config) => {
          patchState(stores, {
            menu: {
              loadFromBackend: isBoolean(config.menu.loadFromBackend)
                ? config.menu.loadFromBackend
                : config.menu.loadFromBackend === 'true'
                  ? true
                  : false,
              getActionByRow: isBoolean(config.menu.getActionByRow)
                ? config.menu.getActionByRow
                : config.menu.getActionByRow === 'true'
                  ? true
                  : false,
            },
            auth: {
              authority: config.auth.authority,
              clientId: config.auth.clientId,
              secureRoutes: config.auth.secureRoutes,
            },
          });
        }),
      ),
    ),
  })),
);
