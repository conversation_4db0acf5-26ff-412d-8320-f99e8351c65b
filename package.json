{"name": "@hrdx-fe/source", "version": "0.0.0", "license": "MIT", "scripts": {"start:bff": "npx nx run-many --target=serve --projects=hrdx-bff --parallel=1"}, "private": true, "dependencies": {"@angular/animations": "18.2.5", "@angular/cdk": "18.2.4", "@angular/common": "18.2.5", "@angular/compiler": "18.2.5", "@angular/core": "18.2.5", "@angular/forms": "18.2.5", "@angular/platform-browser": "18.2.5", "@angular/platform-browser-dynamic": "18.2.5", "@angular/router": "18.2.5", "@azure/msal-angular": "^3.0.23", "@azure/msal-browser": "^3.21.0", "@ctrl/tinycolor": "^4.1.0", "@directus/sdk": "^16.1.0", "@evanion/nestjs-correlation-id": "1.1.0", "@faker-js/faker": "^9.0.1", "@metinseylan/nestjs-opentelemetry": "3.0.0", "@nestjs/axios": "2.0.0", "@nestjs/cache-manager": "2.2.2", "@nestjs/common": "9.4.3", "@nestjs/config": "3.0.0", "@nestjs/core": "9.4.3", "@nestjs/passport": "9.0.3", "@nestjs/platform-express": "9.4.3", "@nestjs/terminus": "9.2.2", "@nestjsx/crud": "5.0.0-alpha.3", "@nestjsx/crud-request": "^5.0.0-alpha.3", "@ngrx/signals": "^18.0.0-rc.0", "@ngx-translate/core": "^16.0.4", "@ngx-translate/http-loader": "^16.0.1", "@nx/angular": "19.6.6", "@nx/nest": "19.6.6", "@nx/plugin": "19.6.6", "@opentelemetry/exporter-jaeger": "1.20.0", "@opentelemetry/exporter-otlp-proto": "0.26.0", "@opentelemetry/instrumentation-express": "0.34.1", "@opentelemetry/instrumentation-http": "0.47.0", "@opentelemetry/instrumentation-nestjs-core": "0.33.4", "@opentelemetry/sdk-trace-base": "1.21.0", "@peoplex/url-join": "*", "@smc/nestcloud-boot": "1.5.4", "@smc/nestcloud-common": "1.5.4", "@smc/nestcloud-config": "1.5.4", "@smc/nestcloud-consul": "1.5.4", "@smc/nestcloud-service": "1.5.4", "@types/file-saver": "^2.0.7", "@types/js-yaml": "^4.0.9", "angular-file-saver": "^1.1.3", "axios": "1.8.4", "b64-to-blob": "^1.2.19", "blueimp-md5": "^2.19.0", "cache-manager": "^5.7.6", "class-transformer": "^0.5.1", "class-validator": "0.14.0", "file-saver": "^2.0.5", "flat": "^6.0.1", "handlebars": "^4.7.8", "helmet": "^7.1.0", "html2canvas": "^1.4.1", "html2canvas-pro": "^1.5.8", "js-yaml": "^4.1.0", "jsonata": "^2.0.5", "jspdf": "^3.0.1", "lodash": "^4.17.21", "moment": "^2.30.1", "nestjs-form-data": "^1.9.91", "ng-zorro-antd": "18.1.1", "ng2-pdfjs-viewer": "^18.0.0", "ngx-quill": "^26.0.1", "ngxtension": "^3.4.0", "passport": "^0.6.0", "quill": "^2.0.2", "rc-picker": "^4.6.0", "react": "^18.3.1", "react-dom": "^18.3.1", "reflect-metadata": "^0.1.13", "remove-vietnamese-accents": "^1.0.7", "rxjs": "~7.8.1", "storybook": "8.4.7", "tslib": "^2.6.3", "zod": "^3.23.8", "zone.js": "~0.14.7"}, "overrides": {"@metinseylan/nestjs-opentelemetry": {"@opentelemetry/resource-detector-gcp": {"semver": "7.5.2"}, "@opentelemetry/auto-instrumentations-node": "0.38.0"}, "glob": "9.3.5", "@nestjs/core": {"path-to-regexp": "3.3.0"}, "@nestjs/platform-express": {"express": {".": "4.20.0", "serve-static": {".": "1.16.1", "send": "0.19.0"}, "send": "0.19.0"}, "body-parser": "1.20.3"}, "express": {"path-to-regexp": "0.1.12", "cookie": "0.7.1"}, "serve-static": {"send": "0.19.0"}, "@angular-devkit/build-angular": {"vite": "5.4.19", "@vitejs/plugin-basic-ssl": {"vite": "5.4.19"}}, "@nx/playwright": {"vite": "5.4.19", "vitest": "3.0.5", "@vitest/mocker": {"vite": "5.4.19"}, "vite-node": {"vite": "5.4.19"}}, "@vitejs/plugin-basic-ssl": {"vite": "5.4.19"}, "vite": "5.4.19", "vitest": "3.0.5", "@swc/cli": {"@mole-inc/bin-wrapper": {"bin-check": {"execa": {".": "5.1.1"}}}}, "koa": "2.16.1", "@babel/runtime": "7.26.10", "@babel/runtime-corejs3": "7.26.10", "undici": "5.28.5", "dompurify": "3.2.6"}, "devDependencies": {"@angular-devkit/architect": "0.1802.4", "@angular-devkit/build-angular": "18.2.12", "@angular-devkit/core": "18.2.4", "@angular-devkit/schematics": "18.2.4", "@angular-eslint/eslint-plugin": "^18.0.1", "@angular-eslint/eslint-plugin-template": "^18.0.1", "@angular-eslint/template-parser": "^18.0.1", "@angular/cli": "~18.2.0", "@angular/compiler-cli": "18.2.5", "@angular/language-service": "18.2.5", "@babel/core": "7.26.10", "@babel/helpers": "7.26.10", "@babel/runtime": "7.26.10", "@chromatic-com/storybook": "^1.9.0", "@nestjs/schematics": "^9.1.1", "@nestjs/testing": "^9.1.1", "@nx-tools/container-metadata": "^6.0.1", "@nx-tools/nx-container": "^6.0.1", "@nx/devkit": "19.6.6", "@nx/eslint": "19.6.6", "@nx/eslint-plugin": "19.6.6", "@nx/jest": "19.6.6", "@nx/js": "19.6.6", "@nx/node": "19.6.6", "@nx/playwright": "19.6.6", "@nx/storybook": "19.6.6", "@nx/web": "19.6.6", "@nx/webpack": "19.6.6", "@nx/workspace": "19.6.6", "@playwright/test": "^1.36.0", "@schematics/angular": "18.2.4", "@storybook/addon-essentials": "8.3.1", "@storybook/addon-interactions": "8.3.1", "@storybook/angular": "8.3.1", "@storybook/core-server": "8.3.1", "@storybook/test": "^8.3.1", "@storybook/test-runner": "^0.18.2", "@swc-node/register": "~1.9.2", "@swc/cli": "~0.3.14", "@swc/core": "~1.5.27", "@swc/helpers": "~0.5.11", "@types/jest": "29.5.13", "@types/node": "20.14.2", "@typescript-eslint/eslint-plugin": "7.18.0", "@typescript-eslint/parser": "7.18.0", "@typescript-eslint/utils": "7.18.0", "autoprefixer": "^10.4.19", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-playwright": "^1.6.2", "http-proxy-middleware": "^3.0.3", "jest": "29.7.0", "jest-environment-jsdom": "29.7.0", "jest-environment-node": "^29.4.1", "jest-preset-angular": "~14.1.0", "jsonc-eslint-parser": "^2.4.0", "ng-packagr": "18.2.1", "nx": "19.6.6", "postcss": "^8.4.38", "postcss-url": "~10.1.3", "prettier": "^3.3.1", "tailwindcss": "^3.4.4", "ts-jest": "^29.1.4", "ts-node": "10.9.2", "typescript": "5.5.4", "webpack-cli": "^5.1.4"}}